<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>AdminLTE 3 | Buttons</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="../../plugins/fontawesome-free/css/all.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="../../dist/css/adminlte.min.css">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
  <nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
      <li class="nav-item">
        <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
      </li>
      <li class="nav-item d-none d-sm-inline-block">
        <a href="../../index3.html" class="nav-link">Home</a>
      </li>
      <li class="nav-item d-none d-sm-inline-block">
        <a href="#" class="nav-link">Contact</a>
      </li>
    </ul>

    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">
      <!-- Navbar Search -->
      <li class="nav-item">
        <a class="nav-link" data-widget="navbar-search" href="#" role="button">
          <i class="fas fa-search"></i>
        </a>
        <div class="navbar-search-block">
          <form class="form-inline">
            <div class="input-group input-group-sm">
              <input class="form-control form-control-navbar" type="search" placeholder="Search" aria-label="Search">
              <div class="input-group-append">
                <button class="btn btn-navbar" type="submit">
                  <i class="fas fa-search"></i>
                </button>
                <button class="btn btn-navbar" type="button" data-widget="navbar-search">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </form>
        </div>
      </li>

      <!-- Messages Dropdown Menu -->
      <li class="nav-item dropdown">
        <a class="nav-link" data-toggle="dropdown" href="#">
          <i class="far fa-comments"></i>
          <span class="badge badge-danger navbar-badge">3</span>
        </a>
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
          <a href="#" class="dropdown-item">
            <!-- Message Start -->
            <div class="media">
              <img src="../../dist/img/user1-128x128.jpg" alt="User Avatar" class="img-size-50 mr-3 img-circle">
              <div class="media-body">
                <h3 class="dropdown-item-title">
                  Brad Diesel
                  <span class="float-right text-sm text-danger"><i class="fas fa-star"></i></span>
                </h3>
                <p class="text-sm">Call me whenever you can...</p>
                <p class="text-sm text-muted"><i class="far fa-clock mr-1"></i> 4 Hours Ago</p>
              </div>
            </div>
            <!-- Message End -->
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <!-- Message Start -->
            <div class="media">
              <img src="../../dist/img/user8-128x128.jpg" alt="User Avatar" class="img-size-50 img-circle mr-3">
              <div class="media-body">
                <h3 class="dropdown-item-title">
                  John Pierce
                  <span class="float-right text-sm text-muted"><i class="fas fa-star"></i></span>
                </h3>
                <p class="text-sm">I got your message bro</p>
                <p class="text-sm text-muted"><i class="far fa-clock mr-1"></i> 4 Hours Ago</p>
              </div>
            </div>
            <!-- Message End -->
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <!-- Message Start -->
            <div class="media">
              <img src="../../dist/img/user3-128x128.jpg" alt="User Avatar" class="img-size-50 img-circle mr-3">
              <div class="media-body">
                <h3 class="dropdown-item-title">
                  Nora Silvester
                  <span class="float-right text-sm text-warning"><i class="fas fa-star"></i></span>
                </h3>
                <p class="text-sm">The subject goes here</p>
                <p class="text-sm text-muted"><i class="far fa-clock mr-1"></i> 4 Hours Ago</p>
              </div>
            </div>
            <!-- Message End -->
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item dropdown-footer">See All Messages</a>
        </div>
      </li>
      <!-- Notifications Dropdown Menu -->
      <li class="nav-item dropdown">
        <a class="nav-link" data-toggle="dropdown" href="#">
          <i class="far fa-bell"></i>
          <span class="badge badge-warning navbar-badge">15</span>
        </a>
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
          <span class="dropdown-item dropdown-header">15 Notifications</span>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <i class="fas fa-envelope mr-2"></i> 4 new messages
            <span class="float-right text-muted text-sm">3 mins</span>
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <i class="fas fa-users mr-2"></i> 8 friend requests
            <span class="float-right text-muted text-sm">12 hours</span>
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item">
            <i class="fas fa-file mr-2"></i> 3 new reports
            <span class="float-right text-muted text-sm">2 days</span>
          </a>
          <div class="dropdown-divider"></div>
          <a href="#" class="dropdown-item dropdown-footer">See All Notifications</a>
        </div>
      </li>
      <li class="nav-item">
        <a class="nav-link" data-widget="fullscreen" href="#" role="button">
          <i class="fas fa-expand-arrows-alt"></i>
        </a>
      </li>
      <li class="nav-item">
        <a class="nav-link" data-widget="control-sidebar" data-slide="true" href="#" role="button">
          <i class="fas fa-th-large"></i>
        </a>
      </li>
    </ul>
  </nav>
  <!-- /.navbar -->

  <!-- Main Sidebar Container -->
  <aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <a href="../../index3.html" class="brand-link">
      <img src="../../dist/img/AdminLTELogo.png" alt="AdminLTE Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
      <span class="brand-text font-weight-light">AdminLTE 3</span>
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
      <!-- Sidebar user (optional) -->
      <div class="user-panel mt-3 pb-3 mb-3 d-flex">
        <div class="image">
          <img src="../../dist/img/user2-160x160.jpg" class="img-circle elevation-2" alt="User Image">
        </div>
        <div class="info">
          <a href="#" class="d-block">Alexander Pierce</a>
        </div>
      </div>

      <!-- SidebarSearch Form -->
      <div class="form-inline">
        <div class="input-group" data-widget="sidebar-search">
          <input class="form-control form-control-sidebar" type="search" placeholder="Search" aria-label="Search">
          <div class="input-group-append">
            <button class="btn btn-sidebar">
              <i class="fas fa-search fa-fw"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- Sidebar Menu -->
      <nav class="mt-2">
        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
          <!-- Add icons to the links using the .nav-icon class
               with font-awesome or any other icon font library -->
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon fas fa-tachometer-alt"></i>
              <p>
                Dashboard
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="../../index.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Dashboard v1</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../../index2.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Dashboard v2</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../../index3.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Dashboard v3</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="../widgets.html" class="nav-link">
              <i class="nav-icon fas fa-th"></i>
              <p>
                Widgets
                <span class="right badge badge-danger">New</span>
              </p>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon fas fa-copy"></i>
              <p>
                Layout Options
                <i class="fas fa-angle-left right"></i>
                <span class="badge badge-info right">6</span>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="../layout/top-nav.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Top Navigation</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../layout/top-nav-sidebar.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Top Navigation + Sidebar</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../layout/boxed.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Boxed</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../layout/fixed-sidebar.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Fixed Sidebar</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../layout/fixed-sidebar-custom.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Fixed Sidebar <small>+ Custom Area</small></p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../layout/fixed-topnav.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Fixed Navbar</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../layout/fixed-footer.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Fixed Footer</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../layout/collapsed-sidebar.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Collapsed Sidebar</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon fas fa-chart-pie"></i>
              <p>
                Charts
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="../charts/chartjs.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>ChartJS</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../charts/flot.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Flot</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../charts/inline.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Inline</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../charts/uplot.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>uPlot</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item menu-open">
            <a href="#" class="nav-link active">
              <i class="nav-icon fas fa-tree"></i>
              <p>
                UI Elements
                <i class="fas fa-angle-left right"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="../UI/general.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>General</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../UI/icons.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Icons</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../UI/buttons.html" class="nav-link active">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Buttons</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../UI/sliders.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Sliders</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../UI/modals.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Modals & Alerts</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../UI/navbar.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Navbar & Tabs</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../UI/timeline.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Timeline</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../UI/ribbons.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Ribbons</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon fas fa-edit"></i>
              <p>
                Forms
                <i class="fas fa-angle-left right"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="../forms/general.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>General Elements</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../forms/advanced.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Advanced Elements</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../forms/editors.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Editors</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../forms/validation.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Validation</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon fas fa-table"></i>
              <p>
                Tables
                <i class="fas fa-angle-left right"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="../tables/simple.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Simple Tables</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../tables/data.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>DataTables</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../tables/jsgrid.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>jsGrid</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-header">EXAMPLES</li>
          <li class="nav-item">
            <a href="../calendar.html" class="nav-link">
              <i class="nav-icon far fa-calendar-alt"></i>
              <p>
                Calendar
                <span class="badge badge-info right">2</span>
              </p>
            </a>
          </li>
          <li class="nav-item">
            <a href="../gallery.html" class="nav-link">
              <i class="nav-icon far fa-image"></i>
              <p>
                Gallery
              </p>
            </a>
          </li>
          <li class="nav-item">
            <a href="../kanban.html" class="nav-link">
              <i class="nav-icon fas fa-columns"></i>
              <p>
                Kanban Board
              </p>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon far fa-envelope"></i>
              <p>
                Mailbox
                <i class="fas fa-angle-left right"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="../mailbox/mailbox.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Inbox</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../mailbox/compose.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Compose</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../mailbox/read-mail.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Read</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon fas fa-book"></i>
              <p>
                Pages
                <i class="fas fa-angle-left right"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="../examples/invoice.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Invoice</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/profile.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Profile</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/e-commerce.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>E-commerce</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/projects.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Projects</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/project-add.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Project Add</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/project-edit.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Project Edit</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/project-detail.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Project Detail</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/contacts.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Contacts</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/faq.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>FAQ</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/contact-us.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Contact us</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon far fa-plus-square"></i>
              <p>
                Extras
                <i class="fas fa-angle-left right"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="#" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>
                    Login & Register v1
                    <i class="fas fa-angle-left right"></i>
                  </p>
                </a>
                <ul class="nav nav-treeview">
                  <li class="nav-item">
                    <a href="../examples/login.html" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Login v1</p>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a href="../examples/register.html" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Register v1</p>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a href="../examples/forgot-password.html" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Forgot Password v1</p>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a href="../examples/recover-password.html" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Recover Password v1</p>
                    </a>
                  </li>
                </ul>
              </li>
              <li class="nav-item">
                <a href="#" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>
                    Login & Register v2
                    <i class="fas fa-angle-left right"></i>
                  </p>
                </a>
                <ul class="nav nav-treeview">
                  <li class="nav-item">
                    <a href="../examples/login-v2.html" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Login v2</p>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a href="../examples/register-v2.html" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Register v2</p>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a href="../examples/forgot-password-v2.html" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Forgot Password v2</p>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a href="../examples/recover-password-v2.html" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Recover Password v2</p>
                    </a>
                  </li>
                </ul>
              </li>
              <li class="nav-item">
                <a href="../examples/lockscreen.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Lockscreen</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/legacy-user-menu.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Legacy User Menu</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/language-menu.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Language Menu</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/404.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Error 404</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/500.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Error 500</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/pace.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Pace</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../examples/blank.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Blank Page</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../../starter.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Starter Page</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon fas fa-search"></i>
              <p>
                Search
                <i class="fas fa-angle-left right"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="../search/simple.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Simple Search</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="../search/enhanced.html" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Enhanced</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-header">MISCELLANEOUS</li>
          <li class="nav-item">
            <a href="../../iframe.html" class="nav-link">
              <i class="nav-icon fas fa-ellipsis-h"></i>
              <p>Tabbed IFrame Plugin</p>
            </a>
          </li>
          <li class="nav-item">
            <a href="https://adminlte.io/docs/3.1/" class="nav-link">
              <i class="nav-icon fas fa-file"></i>
              <p>Documentation</p>
            </a>
          </li>
          <li class="nav-header">MULTI LEVEL EXAMPLE</li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="fas fa-circle nav-icon"></i>
              <p>Level 1</p>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon fas fa-circle"></i>
              <p>
                Level 1
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <a href="#" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Level 2</p>
                </a>
              </li>
              <li class="nav-item">
                <a href="#" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>
                    Level 2
                    <i class="right fas fa-angle-left"></i>
                  </p>
                </a>
                <ul class="nav nav-treeview">
                  <li class="nav-item">
                    <a href="#" class="nav-link">
                      <i class="far fa-dot-circle nav-icon"></i>
                      <p>Level 3</p>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a href="#" class="nav-link">
                      <i class="far fa-dot-circle nav-icon"></i>
                      <p>Level 3</p>
                    </a>
                  </li>
                  <li class="nav-item">
                    <a href="#" class="nav-link">
                      <i class="far fa-dot-circle nav-icon"></i>
                      <p>Level 3</p>
                    </a>
                  </li>
                </ul>
              </li>
              <li class="nav-item">
                <a href="#" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Level 2</p>
                </a>
              </li>
            </ul>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="fas fa-circle nav-icon"></i>
              <p>Level 1</p>
            </a>
          </li>
          <li class="nav-header">LABELS</li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon far fa-circle text-danger"></i>
              <p class="text">Important</p>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon far fa-circle text-warning"></i>
              <p>Warning</p>
            </a>
          </li>
          <li class="nav-item">
            <a href="#" class="nav-link">
              <i class="nav-icon far fa-circle text-info"></i>
              <p>Informational</p>
            </a>
          </li>
        </ul>
      </nav>
      <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
  </aside>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Buttons</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="#">Home</a></li>
              <li class="breadcrumb-item active">Buttons</li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-12">
            <div class="card card-primary card-outline">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-edit"></i>
                  Buttons
                </h3>
              </div>
              <div class="card-body pad table-responsive">
                <p>Various types of buttons. Using the base class <code>.btn</code></p>
                <table class="table table-bordered text-center">
                  <tr>
                    <th>Normal</th>
                    <th>Large <code>.btn-lg</code></th>
                    <th>Small <code>.btn-sm</code></th>
                    <th>Extra Small <code>.btn-xs</code></th>
                    <th>Flat <code>.btn-flat</code></th>
                    <th>Disabled <code>.disabled</code></th>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-default">Default</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-default btn-lg">Default</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-default btn-sm">Default</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-default btn-xs">Default</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-default btn-flat">Default</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-default disabled">Default</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-primary">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-primary btn-lg">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-primary btn-sm">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-primary btn-xs">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-primary btn-flat">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-primary disabled">Primary</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-secondary">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-secondary btn-lg">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-secondary btn-sm">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-secondary btn-xs">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-secondary btn-flat">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-secondary disabled">Secondary</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-success">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-success btn-lg">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-success btn-sm">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-success btn-xs">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-success btn-flat">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-success disabled">Success</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-info">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-info btn-lg">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-info btn-sm">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-info btn-xs">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-info btn-flat">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-info disabled">Info</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-danger">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-danger btn-lg">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-danger btn-sm">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-danger btn-xs">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-danger btn-flat">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-danger disabled">Danger</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-warning">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-warning btn-lg">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-warning btn-sm">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-warning btn-xs">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-warning btn-flat">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-warning disabled">Warning</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-light">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-light btn-lg">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-light btn-sm">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-light btn-xs">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-light btn-flat">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-light disabled">Light</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-dark">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-dark btn-lg">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-dark btn-sm">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-dark btn-xs">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-dark btn-flat">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-dark disabled">Dark</button>
                    </td>
                  </tr>
                </table>
              </div>
              <!-- /.card -->
            </div>
          </div>
          <!-- /.col -->
        </div>
        <!-- ./row -->

        <div class="row">
          <div class="col-md-12">
            <div class="card card-primary card-outline">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-edit"></i>
                  Outline Buttons
                </h3>
              </div>
              <div class="card-body pad table-responsive">
                <p>Various types of buttons. Using the base class <code>.btn</code></p>
                <table class="table table-bordered text-center">
                  <tr>
                    <th>Normal</th>
                    <th>Large <code>.btn-lg</code></th>
                    <th>Small <code>.btn-sm</code></th>
                    <th>Extra Small <code>.btn-xs</code></th>
                    <th>Flat <code>.btn-flat</code></th>
                    <th>Disabled <code>.disabled</code></th>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-primary">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-primary btn-lg">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-primary btn-sm">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-primary btn-xs">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-primary btn-flat">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-primary disabled">Primary</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-secondary">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-secondary btn-lg">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-secondary btn-sm">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-secondary btn-xs">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-secondary btn-flat">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-secondary disabled">Secondary</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-success">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-success btn-lg">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-success btn-sm">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-success btn-xs">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-success btn-flat">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-success disabled">Success</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-info">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-info btn-lg">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-info btn-sm">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-info btn-xs">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-info btn-flat">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-info disabled">Info</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-danger">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-danger btn-lg">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-danger btn-sm">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-danger btn-xs">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-danger btn-flat">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-danger disabled">Danger</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-warning">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-warning btn-lg">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-warning btn-sm">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-warning btn-xs">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-warning btn-flat">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-warning disabled">Warning</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-light">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-light btn-lg">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-light btn-sm">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-light btn-xs">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-light btn-flat">Light</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-light disabled">Light</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-dark">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-dark btn-lg">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-dark btn-sm">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-dark btn-xs">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-dark btn-flat">Dark</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block btn-outline-dark disabled">Dark</button>
                    </td>
                  </tr>
                </table>
              </div>
              <!-- /.card -->
            </div>
          </div>
          <!-- /.col -->
        </div>
        <!-- ./row -->

        <div class="row">
          <div class="col-md-12">
            <div class="card card-primary card-outline">
              <div class="card-header">
                <h3 class="card-title">
                  <i class="fas fa-edit"></i>
                  Gradient Buttons (bg-gradient-*)
                </h3>
              </div>
              <div class="card-body pad table-responsive">
                <p>Various types of buttons. Using the base class <code>.btn</code></p>
                <table class="table table-bordered text-center">
                  <tr>
                    <th>Normal</th>
                    <th>Large <code>.btn-lg</code></th>
                    <th>Small <code>.btn-sm</code></th>
                    <th>Extra Small <code>.btn-xs</code></th>
                    <th>Flat <code>.btn-flat</code></th>
                    <th>Disabled <code>.disabled</code></th>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-primary">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-primary btn-lg">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-primary btn-sm">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-primary btn-xs">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-primary btn-flat">Primary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-primary disabled">Primary</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-secondary">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-secondary btn-lg">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-secondary btn-sm">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-secondary btn-xs">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-secondary btn-flat">Secondary</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-secondary disabled">Secondary</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-success">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-success btn-lg">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-success btn-sm">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-success btn-xs">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-success btn-flat">Success</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-success disabled">Success</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-info">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-info btn-lg">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-info btn-sm">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-info btn-xs">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-info btn-flat">Info</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-info disabled">Info</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-danger">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-danger btn-lg">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-danger btn-sm">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-danger btn-xs">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-danger btn-flat">Danger</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-danger disabled">Danger</button>
                    </td>
                  </tr>
                  <tr>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-warning">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-warning btn-lg">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-warning btn-sm">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-warning btn-xs">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-warning btn-flat">Warning</button>
                    </td>
                    <td>
                      <button type="button" class="btn btn-block bg-gradient-warning disabled">Warning</button>
                    </td>
                  </tr>
                </table>
              </div>
              <!-- /.card -->
            </div>
          </div>
          <!-- /.col -->
        </div>
          <!-- ./row -->
        <div class="row">
          <div class="col-md-6">
            <!-- Buttons with Icons -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Buttons with Icons</h3>
              </div>
              <div class="card-body row">
                <div class="col-md-6">
                  <button type="button" class="btn btn-primary btn-block"><i class="fa fa-bell"></i> .btn-block</button>
                  <button type="button" class="btn btn-info btn-block btn-flat"><i class="fa fa-bell"></i> .btn-block .btn-flat</button>
                  <button type="button" class="btn btn-danger btn-block btn-sm"><i class="fa fa-bell"></i> .btn-block .btn-sm</button>
                </div>
                <div class="col-md-6">
                  <button type="button" class="btn btn-outline-primary btn-block"><i class="fa fa-bell"></i> .btn-block</button>
                  <button type="button" class="btn btn-outline-info btn-block btn-flat"><i class="fa fa-book"></i> .btn-block .btn-flat</button>
                  <button type="button" class="btn btn-outline-danger btn-block btn-sm"><i class="fa fa-book"></i> .btn-block .btn-sm</button>
                </div>
              </div>
            </div>
            <!-- /.card -->

            <!-- Block buttons -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Block Buttons</h3>
              </div>
              <div class="card-body">
                <button type="button" class="btn btn-default btn-block">.btn-block</button>
                <button type="button" class="btn btn-default btn-block btn-flat">.btn-block .btn-flat</button>
                <button type="button" class="btn btn-default btn-block btn-sm">.btn-block .btn-sm</button>
              </div>
            </div>
            <!-- /.card -->

            <!-- Horizontal grouping -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Horizontal Button Group</h3>
              </div>
              <div class="card-body table-responsive pad">
                <p>
                  Horizontal button groups are easy to create with bootstrap. Just add your buttons
                  inside <code>&lt;div class="btn-group"&gt;&lt;/div&gt;</code>
                </p>

                <table class="table table-bordered">
                  <tr>
                    <th>Button</th>
                    <th>Icons</th>
                    <th>Flat</th>
                    <th>Dropdown</th>
                  </tr>
                  <!-- Default -->
                  <tr>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-default">Left</button>
                        <button type="button" class="btn btn-default">Middle</button>
                        <button type="button" class="btn btn-default">Right</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-default">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-default">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-default">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-default btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-default btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-default btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-default">1</button>
                        <button type="button" class="btn btn-default">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-default dropdown-toggle dropdown-icon" data-toggle="dropdown">
                          </button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="#">Dropdown link</a>
                            <a class="dropdown-item" href="#">Dropdown link</a>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- ./default -->
                  <!-- Info -->
                  <tr>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-info">Left</button>
                        <button type="button" class="btn btn-info">Middle</button>
                        <button type="button" class="btn btn-info">Right</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-info">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-info">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-info">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-info btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-info btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-info btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-info">1</button>
                        <button type="button" class="btn btn-info">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-info dropdown-toggle dropdown-icon" data-toggle="dropdown">
                          </button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="#">Dropdown link</a>
                            <a class="dropdown-item" href="#">Dropdown link</a>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- /. info -->
                  <!-- /.danger -->
                  <tr>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-danger">Left</button>
                        <button type="button" class="btn btn-danger">Middle</button>
                        <button type="button" class="btn btn-danger">Right</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-danger">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-danger">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-danger">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-danger btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-danger">1</button>
                        <button type="button" class="btn btn-danger">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-danger dropdown-toggle dropdown-icon" data-toggle="dropdown">
                          </button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="#">Dropdown link</a>
                            <a class="dropdown-item" href="#">Dropdown link</a>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- /.danger -->
                  <!-- warning -->
                  <tr>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-warning">Left</button>
                        <button type="button" class="btn btn-warning">Middle</button>
                        <button type="button" class="btn btn-warning">Right</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-warning">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-warning">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-warning">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-warning btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-warning btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-warning btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-warning">1</button>
                        <button type="button" class="btn btn-warning">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-warning dropdown-toggle dropdown-icon" data-toggle="dropdown">
                          </button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="#">Dropdown link</a>
                            <a class="dropdown-item" href="#">Dropdown link</a>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- /.warning -->
                  <!-- success -->
                  <tr>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-success">Left</button>
                        <button type="button" class="btn btn-success">Middle</button>
                        <button type="button" class="btn btn-success">Right</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-success">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-success">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-success">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-success btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-success btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-success btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group">
                        <button type="button" class="btn btn-success">1</button>
                        <button type="button" class="btn btn-success">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-success dropdown-toggle dropdown-icon" data-toggle="dropdown">
                          </button>
                          <div class="dropdown-menu">
                            <a class="dropdown-item" href="#">Dropdown link</a>
                            <a class="dropdown-item" href="#">Dropdown link</a>
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- /.success -->
                </table>
              </div>
            </div>
            <!-- /.card -->

            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Appended Buttons</h3>
              </div>
              <div class="card-body">
                <strong>With dropdown</strong>
                <div class="input-group mb-3">
                  <div class="input-group-prepend">
                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                      Action
                    </button>
                    <div class="dropdown-menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <!-- /btn-group -->
                  <input type="text" class="form-control">
                </div>
                <!-- /input-group -->
                <strong>Normal</strong>
                <div class="input-group mb-3">
                  <div class="input-group-prepend">
                    <button type="button" class="btn btn-danger">Action</button>
                  </div>
                  <!-- /btn-group -->
                  <input type="text" class="form-control">
                </div>
                <!-- /input-group -->
                <strong>Flat</strong>
                <div class="input-group mb-3">
                  <input type="text" class="form-control rounded-0">
                  <span class="input-group-append">
                    <button type="button" class="btn btn-info btn-flat">Go!</button>
                  </span>
                </div>
                <!-- /input-group -->
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
            <!-- split buttons box -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Split buttons</h3>
              </div>
              <div class="card-body">
                <!-- Split button -->
                <p class="mb-1">Normal split buttons:</p>

                <div class="margin">
                  <div class="btn-group">
                    <button type="button" class="btn btn-default">Action</button>
                    <button type="button" class="btn btn-default dropdown-toggle dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-info">Action</button>
                    <button type="button" class="btn btn-info dropdown-toggle dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-danger">Action</button>
                    <button type="button" class="btn btn-danger dropdown-toggle dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-success">Action</button>
                    <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-warning">Action</button>
                    <button type="button" class="btn btn-warning dropdown-toggle dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                </div>

                <!-- flat split buttons -->
                <p class="mt-3 mb-1">Flat split buttons:</p>

                <div class="margin">
                  <div class="btn-group">
                    <button type="button" class="btn btn-default btn-flat">Action</button>
                    <button type="button" class="btn btn-default btn-flat dropdown-toggle dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-info btn-flat">Action</button>
                    <button type="button" class="btn btn-info btn-flat dropdown-toggle dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-danger btn-flat">Action</button>
                    <button type="button" class="btn btn-danger btn-flat dropdown-toggle dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-success btn-flat">Action</button>
                    <button type="button" class="btn btn-success btn-flat dropdown-toggle dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-warning btn-flat">Action</button>
                    <button type="button" class="btn btn-warning btn-flat dropdown-toggle dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                </div>

                <!-- Split button -->
                <p class="mt-3 mb-1">Hoverable split buttons:</p>
                <div class="margin">
                  <div class="btn-group">
                    <button type="button" class="btn btn-default">Action</button>
                    <button type="button" class="btn btn-default dropdown-toggle dropdown-hover dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-info">Action</button>
                    <button type="button" class="btn btn-info dropdown-toggle dropdown-hover dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-danger">Action</button>
                    <button type="button" class="btn btn-danger dropdown-toggle dropdown-hover dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-success">Action</button>
                    <button type="button" class="btn btn-success dropdown-toggle dropdown-hover dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                  <div class="btn-group">
                    <button type="button" class="btn btn-warning">Action</button>
                    <button type="button" class="btn btn-warning dropdown-toggle dropdown-hover dropdown-icon" data-toggle="dropdown">
                      <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu" role="menu">
                      <a class="dropdown-item" href="#">Action</a>
                      <a class="dropdown-item" href="#">Another action</a>
                      <a class="dropdown-item" href="#">Something else here</a>
                      <div class="dropdown-divider"></div>
                      <a class="dropdown-item" href="#">Separated link</a>
                    </div>
                  </div>
                </div>

              </div>
              <!-- /.card-body -->
            </div>
            <!-- end split buttons box -->
          </div>
          <!-- /.col -->
          <div class="col-md-6">
            <!-- Application buttons -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Application Buttons</h3>
              </div>
              <div class="card-body">
                <p>Add the classes <code>.btn.btn-app</code> to an <code>&lt;a></code> tag to achieve the following:</p>
                <a class="btn btn-app">
                  <i class="fas fa-edit"></i> Edit
                </a>
                <a class="btn btn-app">
                  <i class="fas fa-play"></i> Play
                </a>
                <a class="btn btn-app">
                  <i class="fas fa-pause"></i> Pause
                </a>
                <a class="btn btn-app">
                  <i class="fas fa-save"></i> Save
                </a>
                <a class="btn btn-app">
                  <span class="badge bg-warning">3</span>
                  <i class="fas fa-bullhorn"></i> Notifications
                </a>
                <a class="btn btn-app">
                  <span class="badge bg-success">300</span>
                  <i class="fas fa-barcode"></i> Products
                </a>
                <a class="btn btn-app">
                  <span class="badge bg-purple">891</span>
                  <i class="fas fa-users"></i> Users
                </a>
                <a class="btn btn-app">
                  <span class="badge bg-teal">67</span>
                  <i class="fas fa-inbox"></i> Orders
                </a>
                <a class="btn btn-app">
                  <span class="badge bg-info">12</span>
                  <i class="fas fa-envelope"></i> Inbox
                </a>
                <a class="btn btn-app">
                  <span class="badge bg-danger">531</span>
                  <i class="fas fa-heart"></i> Likes
                </a>

                <p>Application Buttons with Custom Colors</p>
                <a class="btn btn-app bg-secondary">
                  <span class="badge bg-success">300</span>
                  <i class="fas fa-barcode"></i> Products
                </a>
                <a class="btn btn-app bg-success">
                  <span class="badge bg-purple">891</span>
                  <i class="fas fa-users"></i> Users
                </a>
                <a class="btn btn-app bg-danger">
                  <span class="badge bg-teal">67</span>
                  <i class="fas fa-inbox"></i> Orders
                </a>
                <a class="btn btn-app bg-warning">
                  <span class="badge bg-info">12</span>
                  <i class="fas fa-envelope"></i> Inbox
                </a>
                <a class="btn btn-app bg-info">
                  <span class="badge bg-danger">531</span>
                  <i class="fas fa-heart"></i> Likes
                </a>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->

            <!-- Vertical grouping -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Vertical Button Group</h3>
              </div>
              <div class="card-body table-responsive pad">

                <p>
                  Vertical button groups are easy to create with bootstrap. Just add your buttons
                  inside <code>&lt;div class="btn-group-vertical"&gt;&lt;/div&gt;</code>
                </p>

                <table class="table table-bordered">
                  <tr>
                    <th>Button</th>
                    <th>Icons</th>
                    <th>Flat</th>
                    <th>Dropdown</th>
                  </tr>
                  <!-- Default -->
                  <tr>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-default">Top</button>
                        <button type="button" class="btn btn-default">Middle</button>
                        <button type="button" class="btn btn-default">Bottom</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-default">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-default">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-default">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-default btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-default btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-default btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-default">1</button>
                        <button type="button" class="btn btn-default">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                          </button>
                          <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                          </ul>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- ./default -->
                  <!-- Info -->
                  <tr>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-info">Top</button>
                        <button type="button" class="btn btn-info">Middle</button>
                        <button type="button" class="btn btn-info">Bottom</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-info">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-info">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-info">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-info btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-info btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-info btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-info">1</button>
                        <button type="button" class="btn btn-info">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown">
                          </button>
                          <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                          </ul>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- /. info -->
                  <!-- /.danger -->
                  <tr>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-danger">Top</button>
                        <button type="button" class="btn btn-danger">Middle</button>
                        <button type="button" class="btn btn-danger">Bottom</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-danger">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-danger">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-danger">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-danger btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-danger btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-danger">1</button>
                        <button type="button" class="btn btn-danger">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-danger dropdown-toggle" data-toggle="dropdown">
                          </button>
                          <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                          </ul>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- /.danger -->
                  <!-- warning -->
                  <tr>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-warning">Top</button>
                        <button type="button" class="btn btn-warning">Middle</button>
                        <button type="button" class="btn btn-warning">Bottom</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-warning">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-warning">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-warning">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-warning btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-warning btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-warning btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-warning">1</button>
                        <button type="button" class="btn btn-warning">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown">
                          </button>
                          <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                          </ul>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- /.warning -->
                  <!-- success -->
                  <tr>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-success">Top</button>
                        <button type="button" class="btn btn-success">Middle</button>
                        <button type="button" class="btn btn-success">Bottom</button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-success">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-success">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-success">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-success btn-flat">
                          <i class="fas fa-align-left"></i>
                        </button>
                        <button type="button" class="btn btn-success btn-flat">
                          <i class="fas fa-align-center"></i>
                        </button>
                        <button type="button" class="btn btn-success btn-flat">
                          <i class="fas fa-align-right"></i>
                        </button>
                      </div>
                    </td>
                    <td>
                      <div class="btn-group-vertical">
                        <button type="button" class="btn btn-success">1</button>
                        <button type="button" class="btn btn-success">2</button>

                        <div class="btn-group">
                          <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
                          </button>
                          <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                            <li><a class="dropdown-item" href="#">Dropdown link</a></li>
                          </ul>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <!-- /.success -->
                </table>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->

            <!-- Radio Buttons -->
            <div class="card">
              <div class="card-header">
                <h3 class="card-title">Radio Button Group</h3>
              </div>
              <div class="card-body table-responsive pad">
                <p class="mb-1">Radio Button Group with <code>.btn-secondary</code></p>
                <div class="btn-group btn-group-toggle" data-toggle="buttons">
                  <label class="btn btn-secondary active">
                    <input type="radio" name="options" id="option_a1" autocomplete="off" checked> Active
                  </label>
                  <label class="btn btn-secondary">
                    <input type="radio" name="options" id="option_a2" autocomplete="off"> Radio
                  </label>
                  <label class="btn btn-secondary">
                    <input type="radio" name="options" id="option_a3" autocomplete="off"> Radio
                  </label>
                </div>

                <p class="mt-3 mb-1">Radio Button Group with <code>.bg-olive</code></p>
                <div class="btn-group btn-group-toggle" data-toggle="buttons">
                  <label class="btn bg-olive active">
                    <input type="radio" name="options" id="option_b1" autocomplete="off" checked> Active
                  </label>
                  <label class="btn bg-olive">
                    <input type="radio" name="options" id="option_b2" autocomplete="off"> Radio
                  </label>
                  <label class="btn bg-olive">
                    <input type="radio" name="options" id="option_b3" autocomplete="off"> Radio
                  </label>
                </div>
              </div>
              <!-- /.card-body -->
            </div>
            <!-- /.card -->
          </div>
          <!-- /.col -->
        </div>
        <!-- /. row -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->
  <footer class="main-footer">
    <div class="float-right d-none d-sm-block">
      <b>Version</b> 3.2.0
    </div>
    <strong>Copyright &copy; 2014-2021 <a href="https://adminlte.io">AdminLTE.io</a>.</strong> All rights reserved.
  </footer>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="../../plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap 4 -->
<script src="../../plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="../../dist/js/adminlte.min.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="../../dist/js/demo.js"></script>
</body>
</html>
