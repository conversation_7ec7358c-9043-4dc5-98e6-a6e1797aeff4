body {
    font-family: 'Open Sans', serif;
    padding-top: 54px;
    color: var(--color-text-primary);
  }
  
  /* @media (min-width: 992px) {
    body {
      padding-top: 0;
      padding-left: 17rem;
    }
  } */

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Saira Extra Condensed', serif;
    font-weight: 700;
    text-transform: uppercase;
    color: var(--color-text-primary);
  }
  
  h1 {
    font-size: 6rem;
    line-height: 5.5rem;
  }
  
  h2 {
    font-size: 3.5rem;
  }
  
  .subheading {
    text-transform: uppercase;
    font-weight: 500;
    font-family: 'Saira Extra Condensed', serif;
    font-size: 1.35rem;
  }
  
  .list-social-icons a {
    color: var(--color-primary);
  }
  
  .list-social-icons a:hover {
    color: var(--color-primary-hover);
  }
  
  .list-social-icons a .fa-lg {
    font-size: 1.75rem;
  }
  
  .list-icons {
    font-size: 3rem;
  }
  
  .list-icons .list-inline-item i:hover {
    color: var(--color-primary-hover);
  }
  
  #sideNav .navbar-nav .nav-item .nav-link {
    font-weight: 600;
    text-transform: uppercase;
  }
  
  @media (min-width: 992px) {
    #sideNav {
      text-align: center;
      position: fixed;
      top: 0;
      left: 0;
      display: flex;
      flex-direction: column;
      width: 17rem;
      height: 100vh;
    }
    #sideNav .navbar-brand {
      display: flex;
      margin: auto auto 0;
      padding: 0.5rem;
    }
    #sideNav .navbar-brand .img-profile {
      max-width: 10rem;
      max-height: 10rem;
      border: 0.5rem solid rgba(255, 255, 255, 0.2);
    }
    #sideNav .navbar-collapse {
      display: flex;
      align-items: flex-start;
      flex-grow: 0;
      width: 100%;
      margin-bottom: auto;
    }
    #sideNav .navbar-collapse .navbar-nav {
      flex-direction: column;
      width: 100%;
    }
    #sideNav .navbar-collapse .navbar-nav .nav-item {
      display: block;
    }
    #sideNav .navbar-collapse .navbar-nav .nav-item .nav-link {
      display: block;
    }
  }
  
  section.resume-section {
    border-bottom: 1px solid var(--color);
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }
  
  section.resume-section .resume-item .resume-date {
    min-width: none;
  }
  
  @media (min-width: 768px) {
    section.resume-section {
      min-height: 100vh;
    }
    section.resume-section .resume-item .resume-date {
      min-width: 18rem;
    }
  }
  
  @media (min-width: 992px) {
    section.resume-section {
      padding-top: 3rem !important;
      padding-bottom: 3rem !important;
    }
  }
  
  .bg-primary {
    background-color: var(--color-primary) !important;
  }
  
  .text-primary {
    color: var(--color-secondary) !important;
  }
  
  a {
    color: var(--color-secondary);
  }
  
  a:hover, a:focus, a:active {
    color: var(--color-primary-hover);
  }
  
  .vue-typer .custom.char {
    color: var(--color-text-primary);
  }

.card-body, .card-title {
    color: #868e96
}

  /* COLOR STYLES */

  :root {
    --color: #868e96;
    --color-primary: #478547;
    --color-secondary: #3b833b;
    --color-primary-hover: #276827;
    --color-text-primary: #343a40;
    --color-text-secondary: #e2e2e2; 
    --color-text-third: #343a40;
    --bg: #f3f5f4;
    --bg-secondary: #fff;
    --border-color: #ddd;
  }
  
  .dark-mode {
    
    --color: #ebf4f1;
    --color-primary: #0a0d0f;
    --color-secondary: #000000;
    --color-primary-hover: #020405;
    --color-text-primary: #ffffff;
    --color-text-secondary: #e2e2e2; 
    --bg: #081927;
    --bg-secondary: #071521;
    --border-color: #0d2538;
  }
  
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-seriff;
    background-color: var(--bg);
    color: var(--color);
    transition: background-color .3s;
  }
  a {
    color: var(--color-primary)
  }