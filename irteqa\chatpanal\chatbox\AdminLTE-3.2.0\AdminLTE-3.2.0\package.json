{"name": "admin-lte", "description": "Responsive open source admin dashboard and control panel.", "version": "3.2.0", "license": "MIT", "author": "Colorlib <https://colorlib.com>", "main": "dist/js/adminlte.min.js", "scripts": {"bundlewatch": "bundlewatch --config .bundlewatch.config.json", "css": "npm-run-all css-compile css-prefix css-minify", "css-splits": "npm-run-all css-compile-splits css-prefix-splits css-minify-splits", "css-all": "npm-run-all --parallel css css-splits", "css-compile-bash": "node-sass --importer node_modules/node-sass-package-importer/dist/cli.js --output-style expanded --source-map true --source-map-contents true --precision 6 ", "css-compile": "npm run css-compile-bash -- build/scss/adminlte.scss dist/css/adminlte.css", "css-compile-splits": "npm run css-compile-bash -- build/scss/parts -o dist/css/alt/", "css-prefix": "postcss --config build/config/postcss.config.js --replace \"dist/css/*.css\" \"!dist/css/*.min.css\"", "css-prefix-splits": "postcss --config build/config/postcss.config.js --replace \"dist/css/alt/*.css\" \"!dist/css/alt/*.min.css\"", "css-minify-bash": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output ", "css-minify": "npm run css-minify-bash -- dist/css/ --batch --batch-suffix \".min\" \"dist/css/*.css\" \"!dist/css/*.min.css\"", "css-minify-splits": "npm run css-minify-bash -- dist/css/alt/ --batch --batch-suffix \".min\" \"dist/css/alt/*.css\" \"!dist/css/alt/*.min.css\"", "css-lint": "stylelint \"build/scss/**/*.scss\" --cache --cache-location .cache/.stylelintcache", "compile": "npm-run-all --parallel js css-all", "dev": "npm-run-all --parallel watch sync", "docs": "npm-run-all docs-prepare docs-compile", "docs-lint": "node build/npm/vnu-jar.js", "docs-compile": "cd docs/ && bundle exec jekyll build -d ../docs_html", "docs-serve": "npm-run-all compile docs-prepare && cd docs/ && bundle exec jekyll serve", "docs-prepare": "node build/npm/DocsPublish.js -v", "lockfile-lint": "lockfile-lint --allowed-hosts npm --allowed-schemes https: --empty-hostname false --type npm --path package-lock.json", "postinstall": "npm run plugins", "js": "npm-run-all js-compile js-minify", "js-compile": "rollup --config build/config/rollup.config.js --sourcemap", "js-minify": "terser --compress typeofs=false --mangle --comments \"/^!/\" --source-map \"content=dist/js/adminlte.js.map,includeSources,url=adminlte.min.js.map\" --output dist/js/adminlte.min.js dist/js/adminlte.js", "js-lint": "eslint --cache --cache-location .cache/.eslintcache --report-unused-disable-directives .", "lint": "npm-run-all --continue-on-error --parallel css-lint js-lint lockfile-lint", "production": "npm-run-all --parallel compile plugins", "prepare-release": "npm-run-all production docs", "test": "npm-run-all lint production", "plugins": "node build/npm/Publish.js -v", "sync": "browser-sync start --server --files *.html pages/ dist/", "watch": "concurrently \"npm run watch-css\" \"npm run watch-js\"", "watch-css": "nodemon --legacy-watch --watch build/scss -e scss -x \"npm-run-all css-lint css\"", "watch-js": "nodemon --legacy-watch --watch build/js -e js -x \"npm-run-all js-lint js\""}, "keywords": ["css", "sass", "responsive", "admin", "template", "theme", "framework", "control-panel", "dashboard"], "homepage": "https://adminlte.io", "style": "dist/css/adminlte.css", "sass": "build/scss/adminlte.scss", "repository": {"type": "git", "url": "git://github.com/ColorlibHQ/AdminLTE.git"}, "bugs": {"url": "https://github.com/ColorlibHQ/AdminLTE/issues"}, "dependencies": {"@fortawesome/fontawesome-free": "^5.15.4", "@lgaitan/pace-progress": "^1.0.7", "@sweetalert2/theme-bootstrap-4": "^5.0.8", "@ttskch/select2-bootstrap4-theme": "^1.5.2", "bootstrap": "^4.6.1", "bootstrap-colorpicker": "^3.4.0", "bootstrap-slider": "^11.0.2", "bootstrap-switch": "3.3.4", "bootstrap4-duallistbox": "^4.0.2", "bs-custom-file-input": "^1.3.4", "bs-stepper": "^1.7.0", "chart.js": "^2.9.4", "codemirror": "^5.65.1", "datatables.net": "^1.11.4", "datatables.net-autofill-bs4": "^2.3.9", "datatables.net-bs4": "^1.11.4", "datatables.net-buttons-bs4": "^2.2.2", "datatables.net-colreorder-bs4": "^1.5.5", "datatables.net-fixedcolumns-bs4": "^4.0.1", "datatables.net-fixedheader-bs4": "^3.2.1", "datatables.net-keytable-bs4": "^2.6.4", "datatables.net-responsive-bs4": "^2.2.9", "datatables.net-rowgroup-bs4": "^1.1.4", "datatables.net-rowreorder-bs4": "^1.2.8", "datatables.net-scroller-bs4": "^2.0.5", "datatables.net-searchbuilder-bs4": "^1.3.1", "datatables.net-searchpanes-bs4": "^1.4.0", "datatables.net-select-bs4": "^1.3.4", "daterangepicker": "^3.1.0", "dropzone": "^5.9.3", "ekko-lightbox": "^5.3.0", "fastclick": "^1.0.6", "filterizr": "^2.2.4", "flag-icon-css": "^4.1.7", "flot": "^4.2.2", "fs-extra": "^10.0.0", "fullcalendar": "^5.10.1", "icheck-bootstrap": "^3.0.1", "inputmask": "^5.0.7", "ion-rangeslider": "^2.3.1", "jquery": "^3.6.0", "jquery-knob-chif": "^1.2.13", "jquery-mapael": "^2.2.0", "jquery-mousewheel": "^3.1.13", "jquery-ui-dist": "^1.13.0", "jquery-validation": "^1.19.3", "jqvmap-novulnerability": "^1.5.1", "jsgrid": "^1.5.3", "jszip": "^3.7.1", "moment": "^2.29.1", "overlayscrollbars": "^1.13.1", "pdfmake": "^0.2.4", "popper.js": "^1.16.1", "raphael": "^2.3.0", "select2": "^4.0.13", "sparklines": "^1.3.0", "summernote": "^0.8.20", "sweetalert2": "^11.4.0", "tempusdominus-bootstrap-4": "^5.39.0", "toastr": "^2.1.4", "uplot": "^1.6.18"}, "devDependencies": {"@babel/core": "^7.17.0", "@babel/preset-env": "^7.16.11", "@rollup/plugin-babel": "^5.3.0", "autoprefixer": "^10.4.2", "browser-sync": "^2.27.7", "bundlewatch": "^0.3.3", "clean-css-cli": "^5.5.2", "concurrently": "^7.0.0", "eslint": "^8.8.0", "eslint-config-xo": "^0.39.0", "eslint-plugin-compat": "^4.0.2", "eslint-plugin-import": "^2.25.4", "eslint-plugin-unicorn": "^40.1.0", "lockfile-lint": "^4.6.2", "node-sass": "^7.0.1", "node-sass-package-importer": "^5.3.2", "nodemon": "^2.0.15", "npm-run-all": "^4.1.5", "postcss": "^8.4.6", "postcss-cli": "^9.1.0", "rollup": "^2.67.0", "stylelint": "^13.13.1", "stylelint-config-twbs-bootstrap": "^2.2.4", "terser": "^5.10.0", "vnu-jar": "^21.10.12"}}