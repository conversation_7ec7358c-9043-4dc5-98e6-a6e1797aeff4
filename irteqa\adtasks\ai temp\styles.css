body {
    font-family: 'Arial', sans-serif;
    display: flex;
    margin: 0;
    padding: 0;
}

.sidebar {
    width: 250px;
    background-color: #2c3e50;
    color: #ecf0f1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 20px;
}

.sidebar .profile img {
    border-radius: 50%;
    width: 80px;
    height: 80px;
}

.sidebar nav ul {
    list-style-type: none;
    padding: 0;
}

.sidebar nav ul li {
    margin: 20px 0;
}

.sidebar nav ul li a {
    color: #ecf0f1;
    text-decoration: none;
    font-size: 18px;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

header {
    background-color: #ecf0f1;
    padding: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content input[type="search"] {
    padding: 5px;
    font-size: 16px;
}

.header-content .icons .icon {
    margin-left: 15px;
    font-size: 20px;
    cursor: pointer;
}

.dashboard {
    padding: 20px;
}

.cards {
    display: flex;
    justify-content: space-between;
}

.card {
    background-color: #3498db;
    color: #ecf0f1;
    padding: 20px;
    border-radius: 8px;
    width: 23%;
    text-align: center;
}

.card-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.progress {
    width: 100px;
    height: 100px;
    background-color: #ecf0f1;
    border-radius: 50%;
    position: relative;
    margin-bottom: 10px;
}

.progress-bar {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #2ecc71;
    clip: rect(0, 50px, 100px, 0);
    transform: rotate(0deg);
    position: absolute;
    top: 0;
    left: 0;
}

.charts {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
}

.chart {
    background-color: #ecf0f1;
    border-radius: 8px;
    padding: 20px;
    width: 48%;
}
