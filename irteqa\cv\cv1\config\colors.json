{
    "__schema": "colors.json"
  , "__parent": "./copy.json"
  , "colors": {
        "N":      "[0m"

      , "Black":  "[0;1;30m"
      , "Red":    "[0;1;31m"
      , "Green":  "[0;1;32m"
      , "Yellow": "[0;1;33m"
      , "<PERSON>": "[0;1;35m"
      , "Cyan":   "[0;1;36m"
      , "White":  "[0;1;37m"
      , "Gray":   "[0;1;90m"
      , "Orange": "[38;5;178m"
      , "Gold":   "[38;5;178m"

      , "B":    "[0;1;30m"
      , "R":    "[0;1;31m"
      , "G":    "[0;1;32m"
      , "Y":    "[0;1;33m"
      , "Blue": "[0;1;34m"
      , "P":    "[0;1;35m"
      , "C":    "[0;1;36m"
      , "W":    "[0;1;37m"
      , "Gr":   "[0;1;90m"
      , "O":    "[38;5;178m"

      , "BB":    "[1;1;30m"
      , "BR":    "[1;1;31m"
      , "BG":    "[1;1;32m"
      , "BY":    "[1;1;33m"
      , "BBlue": "[1;1;34m"
      , "BP":    "[1;1;35m"
      , "BC":    "[1;1;36m"
      , "BW":    "[1;1;37m"
      , "BGray": "[1;1;90m"

      , "OnB":    "[40m"
      , "OnR":    "[41m"
      , "OnG":    "[42m"
      , "OnY":    "[43m"
      , "OnBlue": "[44m"
      , "OnP":    "[45m"
      , "OnC":    "[46m"
      , "OnW":    "[47m"
      , "OnGray": "[90m"
    }
}
