<?php
/**
 * إعدادات قاعدة البيانات الموحدة
 * جميع الجداول في قاعدة بيانات واحدة مع الاعتماد على user_id للفصل
 */

// إعدادات قاعدة البيانات الموحدة
define('DB_HOST', 'localhost');
define('DB_NAME', 'u193708811_system_main');
define('DB_USER', 'sales01');
define('DB_PASS', 'dNz35nd5@');
define('DB_PORT', 3306);

// متغير الاتصال العام
$unified_db = null;

// دالة الاتصال بقاعدة البيانات الموحدة
function getUnifiedDB() {
    global $unified_db;
    
    // إذا كان الاتصال موجود ويعمل، أرجعه
    if ($unified_db && !$unified_db->connect_error) {
        return $unified_db;
    }
    
    // محاولة إنشاء اتصال جديد
    try {
        $unified_db = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME, DB_PORT);
        $unified_db->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
        $unified_db->set_charset("utf8mb4");
        
        if ($unified_db->connect_error) {
            throw new Exception("خطأ في الاتصال: " . $unified_db->connect_error);
        }
        
        return $unified_db;
    } catch (Exception $e) {
        error_log("خطأ في الاتصال بقاعدة البيانات الموحدة: " . $e->getMessage());
        return null;
    }
}

// دالة للتوافق مع النظام القديم
function getCurrentUserDB() {
    return getUnifiedDB();
}

// دالة للتوافق مع النظام القديم
function getOperationsDB() {
    return getUnifiedDB();
}

// دالة للحصول على اتصال قاعدة البيانات الرئيسية (نفس الموحدة)
function getMainDB() {
    return getUnifiedDB();
}

// دالة لإنشاء جميع الجداول في قاعدة البيانات الموحدة
function createUnifiedTables() {
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }
    
    // جدول المستخدمين
    $users_sql = "CREATE TABLE IF NOT EXISTS `users` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `email` varchar(100) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) DEFAULT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `status` enum('active','inactive','suspended') DEFAULT 'active',
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_username` (`username`),
        KEY `idx_email` (`email`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المدراء
    $admins_sql = "CREATE TABLE IF NOT EXISTS `admins` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `username` varchar(50) NOT NULL UNIQUE,
        `email` varchar(100) NOT NULL UNIQUE,
        `password` varchar(255) NOT NULL,
        `full_name` varchar(100) DEFAULT NULL,
        `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
        `permissions` text DEFAULT NULL,
        `status` enum('active','inactive') DEFAULT 'active',
        `last_login` timestamp NULL DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_username` (`username`),
        KEY `idx_email` (`email`),
        KEY `idx_role` (`role`),
        KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول سجل النشاطات
    $activity_log_sql = "CREATE TABLE IF NOT EXISTS `activity_log` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) DEFAULT NULL,
        `user_type` enum('user','admin') DEFAULT 'user',
        `action` varchar(100) NOT NULL,
        `table_name` varchar(50) DEFAULT NULL,
        `record_id` int(11) DEFAULT NULL,
        `old_data` text DEFAULT NULL,
        `new_data` text DEFAULT NULL,
        `description` text DEFAULT NULL,
        `ip_address` varchar(45) DEFAULT NULL,
        `user_agent` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_user_type` (`user_type`),
        KEY `idx_action` (`action`),
        KEY `idx_table_name` (`table_name`),
        KEY `idx_created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول العملاء (بدون بادئة)
    $customers_sql = "CREATE TABLE IF NOT EXISTS `customers` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `name` varchar(255) NOT NULL,
        `phone` varchar(20) DEFAULT NULL,
        `email` varchar(255) DEFAULT NULL,
        `tax_number` varchar(50) DEFAULT NULL,
        `address` text DEFAULT NULL,
        `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_name` (`name`),
        KEY `idx_email` (`email`),
        KEY `idx_phone` (`phone`),
        KEY `idx_customer_type` (`customer_type`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المنتجات (بدون بادئة)
    $products_sql = "CREATE TABLE IF NOT EXISTS `products` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `name` varchar(255) NOT NULL,
        `description` text DEFAULT NULL,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
        `category` varchar(100) DEFAULT NULL,
        `stock_quantity` decimal(10,2) DEFAULT 0.00,
        `unit` varchar(50) DEFAULT 'قطعة',
        `barcode` varchar(100) DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_name` (`name`),
        KEY `idx_category` (`category`),
        KEY `idx_barcode` (`barcode`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المبيعات (بدون بادئة)
    $sales_sql = "CREATE TABLE IF NOT EXISTS `sales` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `invoice_number` varchar(50) NOT NULL,
        `date` date NOT NULL,
        `customer_id` int(11) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
        `notes` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        UNIQUE KEY `idx_invoice_number` (`invoice_number`),
        KEY `idx_customer_id` (`customer_id`),
        KEY `idx_date` (`date`),
        KEY `idx_payment_status` (`payment_status`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول المشتريات (بدون بادئة)
    $purchases_sql = "CREATE TABLE IF NOT EXISTS `purchases` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `invoice_number` varchar(50) NOT NULL,
        `date` date NOT NULL,
        `customer_id` int(11) DEFAULT NULL,
        `supplier_name` varchar(255) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
        `notes` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        UNIQUE KEY `idx_invoice_number` (`invoice_number`),
        KEY `idx_customer_id` (`customer_id`),
        KEY `idx_date` (`date`),
        KEY `idx_payment_status` (`payment_status`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول عناصر المبيعات (بدون بادئة)
    $sale_items_sql = "CREATE TABLE IF NOT EXISTS `sale_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `sale_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `product_name` varchar(255) NOT NULL DEFAULT '',
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_sale_id` (`sale_id`),
        KEY `idx_product_id` (`product_id`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // جدول عناصر المشتريات (بدون بادئة)
    $purchase_items_sql = "CREATE TABLE IF NOT EXISTS `purchase_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `user_id` int(11) NOT NULL,
        `purchase_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `product_name` varchar(255) NOT NULL DEFAULT '',
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `idx_user_id` (`user_id`),
        KEY `idx_purchase_id` (`purchase_id`),
        KEY `idx_product_id` (`product_id`),
        FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`purchase_id`) REFERENCES `purchases`(`id`) ON DELETE CASCADE,
        FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    // تنفيذ إنشاء الجداول
    $tables = [
        'users' => $users_sql,
        'admins' => $admins_sql,
        'activity_log' => $activity_log_sql,
        'customers' => $customers_sql,
        'products' => $products_sql,
        'sales' => $sales_sql,
        'purchases' => $purchases_sql,
        'sale_items' => $sale_items_sql,
        'purchase_items' => $purchase_items_sql
    ];
    
    foreach ($tables as $table_name => $sql) {
        if (!$db->query($sql)) {
            error_log("خطأ في إنشاء جدول $table_name: " . $db->error);
            return false;
        }
    }
    
    return true;
}

// دالة للتوافق - إرجاع اسم الجدول بدون بادئة
function getUserTableName($table_name, $username = null) {
    // في النظام الموحد، نستخدم أسماء الجداول مباشرة بدون بادئة
    return $table_name;
}

// دالة للتوافق - التحقق من وجود الجدول
function userTableExists($table, $username = null) {
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }
    
    $result = $db->query("SHOW TABLES LIKE '$table'");
    return ($result && $result->num_rows > 0);
}

// دالة لإدراج البيانات مع user_id تلقائياً
function insertWithUserId($table, $data, $username = null) {
    if (!$username && isset($_SESSION['username'])) {
        $username = $_SESSION['username'];
    }
    
    if (!$username || !isset($_SESSION['user_id'])) {
        return false;
    }
    
    // إضافة user_id للبيانات
    $data['user_id'] = $_SESSION['user_id'];
    
    // بناء استعلام الإدراج
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }
    
    $columns = array_keys($data);
    $placeholders = array_fill(0, count($data), '?');
    $values = array_values($data);
    
    $sql = "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $placeholders) . ")";
    
    $stmt = $db->prepare($sql);
    if (!$stmt) {
        return false;
    }
    
    // تحديد أنواع البيانات
    $types = str_repeat('s', count($values));
    $stmt->bind_param($types, ...$values);
    
    $result = $stmt->execute();
    $insert_id = $result ? $db->insert_id : false;
    $stmt->close();
    
    return $insert_id;
}

// دالة لتحديث البيانات مع فلترة user_id تلقائياً
function updateWithUserId($table, $data, $where, $username = null) {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    $db = getUnifiedDB();
    if (!$db) {
        return false;
    }
    
    // بناء استعلام التحديث مع فلترة user_id
    $set_clauses = [];
    $values = [];
    foreach ($data as $column => $value) {
        $set_clauses[] = "`$column` = ?";
        $values[] = $value;
    }
    
    $where_clause = $where . " AND `user_id` = ?";
    $values[] = $_SESSION['user_id'];
    
    $sql = "UPDATE `$table` SET " . implode(', ', $set_clauses) . " WHERE $where_clause";
    
    $stmt = $db->prepare($sql);
    if (!$stmt) {
        return false;
    }
    
    $types = str_repeat('s', count($values));
    $stmt->bind_param($types, ...$values);
    
    $result = $stmt->execute();
    $affected_rows = $result ? $stmt->affected_rows : 0;
    $stmt->close();
    
    return $affected_rows;
}

// إنشاء الجداول تلقائياً عند تحميل الملف
createUnifiedTables();
?>
