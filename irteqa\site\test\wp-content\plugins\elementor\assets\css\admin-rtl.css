/*! elementor - v3.11.5 - 14-03-2023 */
.elementor-button {
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  font-weight: 500;
  text-transform: uppercase;
  outline: none;
  border: none;
  border-radius: 3px;
  transition-property: background, color, box-shadow, opacity;
  transition-duration: 0.3s;
}
.elementor-button:hover {
  border: none;
}
.elementor-button:not([disabled]) {
  cursor: pointer;
}
.elementor-button:not(.elementor-button-state) .elementor-state-icon {
  display: none;
}
.elementor-button.elementor-button-success {
  color: #fff;
}
.elementor-button.elementor-button-success[disabled] {
  background-color: #c2cbd2;
}
.elementor-button.elementor-button-success:not([disabled]) {
  background-color: #39b54a;
}
.elementor-button.elementor-button-success:not([disabled]):hover {
  opacity: 0.85;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.12), 0 2px 2px rgba(0, 0, 0, 0.2);
}
.elementor-button.elementor-button-success:not([disabled]):active {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.19), 0 3px 3px rgba(0, 0, 0, 0.1);
}
.elementor-button.elementor-button-brand {
  color: #ffffff;
}
.elementor-button.elementor-button-brand[disabled] {
  background-color: #c2cbd2;
}
.elementor-button.elementor-button-brand:not([disabled]) {
  background-color: #93003c;
}
.elementor-button.elementor-button-brand:not([disabled]):hover {
  opacity: 0.85;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.12), 0 2px 2px rgba(0, 0, 0, 0.2);
}
.elementor-button.elementor-button-brand:not([disabled]):active {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.19), 0 3px 3px rgba(0, 0, 0, 0.1);
}
.elementor-button.elementor-button-warning {
  background-color: #a4afb7;
  color: #fff;
}
.elementor-button.elementor-button-warning[disabled] {
  background-color: #c2cbd2;
}
.elementor-button.elementor-button-warning:not([disabled]):hover {
  background-color: #b01b1b;
  opacity: 0.85;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.12), 0 2px 2px rgba(0, 0, 0, 0.2);
}
.elementor-button.elementor-button-warning:not([disabled]):active {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.19), 0 3px 3px rgba(0, 0, 0, 0.1);
}
.elementor-button.elementor-button-danger {
  background-color: #d72b3f;
  color: #fff;
}
.elementor-button.elementor-button-danger[disabled] {
  background-color: #c2cbd2;
}
.elementor-button.elementor-button-danger:not([disabled]):hover {
  opacity: 0.85;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.12), 0 2px 2px rgba(0, 0, 0, 0.2);
}
.elementor-button.elementor-button-danger:not([disabled]):active {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.19), 0 3px 3px rgba(0, 0, 0, 0.1);
}
.elementor-button.elementor-edit-template {
  display: inline-block;
  margin-top: 15px;
  color: #fff;
}
.elementor-button.elementor-button-default {
  background-color: #a4afb7;
  color: #fff;
  font-size: 11px;
  padding: 7px 21px;
}
.elementor-button.elementor-button-default:hover {
  background-color: #6d7882;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.12), 0 2px 2px rgba(0, 0, 0, 0.2);
}
.elementor-button.elementor-button-default:active {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.19), 0 3px 3px rgba(0, 0, 0, 0.1);
}
.elementor-button.elementor-button-default:visited {
  color: #fff;
}
.elementor-button.elementor-button-go-pro {
  background-color: #93003c;
}
.elementor-button i {
  margin-left: 10px;
}

#adminmenu #toplevel_page_elementor div.wp-menu-image:before {
  content: "\e813";
  font-family: eicons;
  font-size: 18px;
  margin-top: 1px;
}
#adminmenu #toplevel_page_elementor a[href="admin.php?page=go_elementor_pro"] {
  font-weight: 700;
}
#adminmenu #toplevel_page_elementor a[href="admin.php?page=go_elementor_pro"]:hover {
  color: #e0005b;
}
#adminmenu #toplevel_page_elementor .dashicons.dashicons-star-filled {
  height: auto;
}
#adminmenu #menu-posts-elementor_library .wp-menu-image:before {
  content: "\e8ff";
  font-family: eicons;
  font-size: 18px;
}

#e-admin-menu__kit-library {
  color: #5cb85c;
}

body.admin-color-fresh #adminmenu #toplevel_page_elementor a[href="admin.php?page=go_elementor_pro"] {
  color: #c60051;
}

.elementor-plugins-gopro {
  color: #93003c;
  text-shadow: 1px 1px 1px #eee;
  font-weight: bold;
}

#elementor-switch-mode {
  margin: 15px 0;
}

#elementor-switch-mode-button,
#elementor-editor-button {
  outline: none;
  cursor: pointer;
}
#elementor-switch-mode-button i,
#elementor-editor-button i {
  margin-left: 3px;
  font-size: 125%;
  font-style: normal;
}

body.elementor-editor-active .elementor-switch-mode-off {
  display: none;
}
body.elementor-editor-active #elementor-switch-mode-button {
  background-color: #f7f7f7;
  color: #555;
  border-color: #ccc;
  box-shadow: 0 1px 0 #ccc !important;
  text-shadow: unset;
}
body.elementor-editor-active #elementor-switch-mode-button:hover {
  background-color: #e9e9e9;
}
body.elementor-editor-active #elementor-switch-mode-button:active {
  box-shadow: inset 0 1px 0 #ccc;
  transform: translateY(1px);
}
body.elementor-editor-active #postdivrich {
  display: none !important;
}
body.elementor-editor-active .editor-block-list__layout,
body.elementor-editor-active .block-editor-block-list__layout {
  display: none;
}
body.elementor-editor-inactive .elementor-switch-mode-on {
  display: none;
}
body.elementor-editor-inactive #elementor-editor {
  display: none;
}

body.elementor-editor-active .editor-block-list__layout {
  display: none;
}
body.elementor-editor-active .edit-post-layout__content .edit-post-visual-editor {
  flex-basis: auto;
}
body.elementor-editor-active #elementor-editor {
  margin-bottom: 50px;
}
body.elementor-editor-active .edit-post-text-editor__body .editor-post-text-editor {
  display: none;
}
body .block-editor #elementor-switch-mode {
  margin: 0 15px;
}
body .block-editor #elementor-switch-mode .button {
  margin: 2px;
  height: 33px;
  font-size: 13px;
  line-height: 1;
}
body .block-editor #elementor-switch-mode .button i {
  padding-left: 5px;
}

.elementor-button {
  font-size: 13px;
  text-decoration: none;
  padding: 15px 40px;
}

#elementor-editor {
  height: 300px;
  width: 100%;
  transition: all 0.5s ease;
}
#elementor-editor .elementor-loader-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
#elementor-editor .elementor-loader {
  border-radius: 50%;
  padding: 40px;
  height: 150px;
  width: 150px;
  background-color: rgba(255, 255, 255, 0.9);
  box-sizing: border-box;
  box-shadow: 2px 2px 20px 4px rgba(0, 0, 0, 0.02);
}
#elementor-editor .elementor-loader-boxes {
  height: 100%;
  width: 100%;
  position: relative;
}
#elementor-editor .elementor-loader-box {
  position: absolute;
  background-color: #d5dadf;
  animation: load 1.8s linear infinite;
}
#elementor-editor .elementor-loader-box:nth-of-type(1) {
  width: 20%;
  height: 100%;
  left: 0;
  top: 0;
}
#elementor-editor .elementor-loader-box:not(:nth-of-type(1)) {
  right: 0;
  height: 20%;
  width: 60%;
}
#elementor-editor .elementor-loader-box:nth-of-type(2) {
  top: 0;
  animation-delay: calc( 1.8s / 4 * -1 );
}
#elementor-editor .elementor-loader-box:nth-of-type(3) {
  top: 40%;
  animation-delay: calc( 1.8s / 4 * -2 );
}
#elementor-editor .elementor-loader-box:nth-of-type(4) {
  bottom: 0;
  animation-delay: calc( 1.8s / 4 * -3 );
}
#elementor-editor .elementor-loading-title {
  color: #a4afb7;
  text-align: center;
  text-transform: uppercase;
  margin-top: 30px;
  letter-spacing: 7px;
  text-indent: 7px;
  font-size: 10px;
  width: 100%;
}

#elementor-go-to-edit-page-link {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #DDD;
  background-color: #F7F7F7;
  text-decoration: none;
  position: relative;
  font-family: Sans-serif;
}
#elementor-go-to-edit-page-link:hover {
  background-color: #ffffff;
}
#elementor-go-to-edit-page-link:focus {
  box-shadow: none;
}
#elementor-go-to-edit-page-link.elementor-animate #elementor-editor-button {
  display: none;
}
#elementor-go-to-edit-page-link:not(.elementor-animate) .elementor-loader-wrapper {
  display: none;
}

.elementor-button-spinner:before {
  font: normal 20px/0.5 dashicons;
  speak: none;
  display: inline-block;
  padding: 0;
  top: 8px;
  right: -4px;
  position: relative;
  vertical-align: top;
  content: "\f463";
}
.elementor-button-spinner.loading:before {
  animation: rotation 1s infinite linear;
}
.elementor-button-spinner.success:before {
  content: "\f147";
  color: #46b450;
}

.elementor-blank_state {
  padding: 5em 0;
  margin: auto;
  max-width: 520px;
  text-align: center;
  color: #6d7882;
  font-family: Roboto, sans-serif;
}
.elementor-blank_state i {
  font-size: 50px;
  color: #a4afb7;
}
.elementor-blank_state h3 {
  font-size: 32px;
  font-weight: 300;
  color: inherit;
  margin: 40px 0 10px;
  line-height: 1.2;
}
.elementor-blank_state p {
  font-size: 16px;
  font-weight: normal;
  color: #a4afb7;
  margin-bottom: 40px;
}
.elementor-blank_state .elementor-button {
  display: inline-block;
}

#available-widgets [class*=elementor-template] .widget-title:before {
  content: "\e813";
  font-family: eicons;
  font-size: 17px;
}

.elementor-settings-form-page {
  padding-top: 30px;
}
.elementor-settings-form-page:not(.elementor-active) {
  display: none;
}

._elementor_settings_update_time {
  display: none;
}

#confirm_fa_migration_admin_modal .dialog-confirm-ok {
  color: #6d7882;
}

body.post-type-attachment table.media .column-title .media-icon img[src$=".svg"] {
  width: 100%;
}

.e-major-update-warning {
  margin-bottom: 5px;
  max-width: 1000px;
  display: flex;
}
.e-major-update-warning__separator {
  margin: 15px -12px;
}
.e-major-update-warning__icon {
  font-size: 17px;
  margin-left: 9px;
  margin-right: 2px;
}
.e-major-update-warning__title {
  font-weight: 600;
  margin-bottom: 10px;
}
.e-major-update-warning + p {
  display: none;
}

.notice-success .e-major-update-warning__separator {
  border: 1px solid #46b450;
}
.notice-success .e-major-update-warning__icon {
  color: #79ba49;
}

.notice-warning .e-major-update-warning__separator {
  border: 1px solid #ffb900;
}
.notice-warning .e-major-update-warning__icon {
  color: #f56e28;
}

.plugins table.e-compatibility-update-table tr {
  background: transparent;
}
.plugins table.e-compatibility-update-table tr th {
  font-weight: 600;
}
.plugins table.e-compatibility-update-table tr th, .plugins table.e-compatibility-update-table tr td {
  min-width: 250px;
  font-size: 13px;
  background: transparent;
  box-shadow: none;
  border: none;
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 15px;
  padding-right: 0;
}

:root {
  --e-focus-color: rgba(0, 115, 170, .4);
  --e-context-primary-color: #0073aa;
  --e-context-primary-color-dark: #005177;
  --e-context-primary-tint-4: rgba(0, 115, 170, 0.4);
  --e-context-primary-tint-1: rgba(0, 115, 170, 0.04);
  --e-context-success-color: #39b54a;
  --e-context-success-color-dark: #2d8e3a;
  --e-context-success-tint-4: rgba(57, 181, 74, 0.4);
  --e-context-success-tint-1: rgba(57, 181, 74, 0.04);
  --e-context-info-color: #71d7f7;
  --e-context-info-color-dark: #41c9f4;
  --e-context-info-tint-4: rgba(113, 215, 247, 0.4);
  --e-context-info-tint-1: rgba(113, 215, 247, 0.04);
  --e-context-warning-color: #fcb92c;
  --e-context-warning-color-dark: #f2a503;
  --e-context-warning-tint-4: rgba(252, 185, 44, 0.4);
  --e-context-warning-tint-1: rgba(252, 185, 44, 0.04);
  --e-context-error-color: #d72b3f;
  --e-context-error-color-dark: #ae2131;
  --e-context-error-tint-4: rgba(215, 43, 63, 0.4);
  --e-context-error-tint-1: rgba(215, 43, 63, 0.04);
  --e-context-cta-color: #93003c;
  --e-context-cta-color-dark: #600027;
  --e-context-cta-tint-4: rgba(147, 0, 60, 0.4);
  --e-context-cta-tint-1: rgba(147, 0, 60, 0.04);
}

.e-getting-started {
  max-width: 900px;
  padding: 2.5em 0;
  margin: auto;
  text-align: center;
}
.e-getting-started__header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
}
.e-getting-started__header .e-logo-wrapper {
  font-size: 10px;
  margin-left: 10px;
}
.e-getting-started__title {
  padding: 0 15px;
  font-weight: 600;
  text-transform: uppercase;
  display: flex;
  align-items: center;
}
.e-getting-started__skip {
  border-right: 1px solid #eee;
  font-size: 16px;
  color: inherit;
}
.e-getting-started__skip i {
  padding: 15px;
}
.e-getting-started__content {
  padding: 50px;
}
.e-getting-started__content h2 {
  font-size: 2em;
  margin-top: 0;
}
.e-getting-started__content--narrow {
  max-width: 500px;
  margin: auto;
}
.e-getting-started__video {
  margin: 40px 0 60px;
}
.e-getting-started__video iframe {
  box-shadow: 10px 10px 20px rgba(0, 0, 0, 0.15);
}
.e-getting-started__actions .button-primary {
  margin-left: 20px;
}

:root {
  --e-button-padding-y: 0.4375rem;
  --e-button-padding-x: 0.75rem;
  --e-button-font-size: 0.8125rem;
  --e-button-font-weight: 500;
  --e-button-line-height: 0.9375rem;
  --e-button-border-radius: 3px;
  --e-button-context-color: var(--e-context-primary-color);
  --e-button-context-color-dark: var(--e-context-primary-color-dark);
  --e-button-context-tint: var(--e-context-primary-tint-1);
}

.e-button {
  display: inline-block;
  font-weight: var(--e-button-font-weight);
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: #ffffff;
  border: 0;
  text-decoration: none;
  background: var(--e-button-context-color);
  padding: var(--e-button-padding-y) var(--e-button-padding-x);
  font-size: var(--e-button-font-size);
  line-height: var(--e-button-line-height);
  border-radius: var(--e-button-border-radius);
  transition: background-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.e-button:active, .e-button:hover, .e-button:focus {
  color: #ffffff;
  text-decoration: none;
  background: var(--e-button-context-color-dark);
}
.e-button:focus, .e-button.focus {
  outline: 0;
  box-shadow: 0 0 0 2px var(--e-focus-color);
}
.e-button.disabled, .e-button:disabled {
  opacity: 0.5;
  box-shadow: none;
}
.e-button:not(:disabled):not(.disabled) {
  cursor: pointer;
}
.e-button:not(:disabled):not(.disabled):active:focus, .e-button:not(:disabled):not(.disabled).active:focus {
  box-shadow: 0 0 0 2px var(--e-focus-color);
}
.e-button--primary {
  --e-button-context-color: var(--e-context-primary-color);
  --e-button-context-color-dark: var(--e-context-primary-color-dark);
  --e-button-context-tint: var(--e-context-primary-tint-1);
  --e-focus-color: var(--e-context-primary-tint-4);
}
.e-button--success {
  --e-button-context-color: var(--e-context-success-color);
  --e-button-context-color-dark: var(--e-context-success-color-dark);
  --e-button-context-tint: var(--e-context-success-tint-1);
  --e-focus-color: var(--e-context-success-tint-4);
}
.e-button--info {
  --e-button-context-color: var(--e-context-info-color);
  --e-button-context-color-dark: var(--e-context-info-color-dark);
  --e-button-context-tint: var(--e-context-info-tint-1);
  --e-focus-color: var(--e-context-info-tint-4);
}
.e-button--warning {
  --e-button-context-color: var(--e-context-warning-color);
  --e-button-context-color-dark: var(--e-context-warning-color-dark);
  --e-button-context-tint: var(--e-context-warning-tint-1);
  --e-focus-color: var(--e-context-warning-tint-4);
}
.e-button--error {
  --e-button-context-color: var(--e-context-error-color);
  --e-button-context-color-dark: var(--e-context-error-color-dark);
  --e-button-context-tint: var(--e-context-error-tint-1);
  --e-focus-color: var(--e-context-error-tint-4);
}
.e-button--cta {
  --e-button-context-color: var(--e-context-cta-color);
  --e-button-context-color-dark: var(--e-context-cta-color-dark);
  --e-button-context-tint: var(--e-context-cta-tint-1);
  --e-focus-color: var(--e-context-cta-tint-4);
}
.e-button.e-button--outline {
  color: var(--e-button-context-color);
  background: none;
  border: 1px solid currentColor;
}
.e-button.e-button--outline:hover, .e-button.e-button--outline:focus {
  color: var(--e-button-context-color-dark);
  background: var(--e-button-context-tint);
}
.e-button.e-button--outline.disabled, .e-button.e-button--outline:disabled {
  color: var(--e-button-context-color-dark);
  background: #818a91;
}
.e-button > i {
  line-height: inherit;
  height: var(--e-button-line-height);
  width: -moz-min-content;
  width: min-content;
}
.e-button > * + * {
  -webkit-margin-start: 0.5ch;
          margin-inline-start: 0.5ch;
}
.e-button--link {
  color: var(--e-button-context-color);
  background-color: transparent;
}
.e-button--link:hover, .e-button--link:focus {
  color: var(--e-button-context-color-dark);
  background: var(--e-button-context-tint);
}
.e-button--link:disabled, .e-button--link.disabled {
  color: #818a91;
}

a.e-button.disabled,
fieldset:disabled a.e-button {
  pointer-events: none;
}

:root {
  --e-notice-bg: #fff;
  --e-notice-border-color: #ccd0d4;
  --e-notice-context-color: #93003c;
  --e-notice-context-tint: var(--e-context-cta-tint-1);
  --e-notice-box-shadow: 0 1px 4px rgba(0,0,0,.15);
  --e-notice-dismiss-color: #6d7882;
}

.e-notice {
  position: relative;
  display: flex;
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  background: var(--e-notice-bg);
  border: 1px solid var(--e-notice-border-color);
  border-inline-start-width: 4px;
  box-shadow: var(--e-notice-box-shadow);
  margin: 5px 20px 5px 2px;
}
.e-notice.notice {
  padding: 0;
}
.e-notice::before {
  display: block;
  content: "";
  position: absolute;
  right: -4px;
  top: -1px;
  bottom: -1px;
  width: 4px;
  background-color: var(--e-notice-context-color);
}
.e-notice--primary {
  --e-notice-context-color: var(--e-context-primary-color);
  --e-notice-context-color-dark: var(--e-context-primary-color-dark);
  --e-notice-context-tint: var(--e-context-primary-tint-1);
}
.e-notice--success {
  --e-notice-context-color: var(--e-context-success-color);
  --e-notice-context-color-dark: var(--e-context-success-color-dark);
  --e-notice-context-tint: var(--e-context-success-tint-1);
}
.e-notice--info {
  --e-notice-context-color: var(--e-context-info-color);
  --e-notice-context-color-dark: var(--e-context-info-color-dark);
  --e-notice-context-tint: var(--e-context-info-tint-1);
}
.e-notice--warning {
  --e-notice-context-color: var(--e-context-warning-color);
  --e-notice-context-color-dark: var(--e-context-warning-color-dark);
  --e-notice-context-tint: var(--e-context-warning-tint-1);
}
.e-notice--error {
  --e-notice-context-color: var(--e-context-error-color);
  --e-notice-context-color-dark: var(--e-context-error-color-dark);
  --e-notice-context-tint: var(--e-context-error-tint-1);
}
.e-notice--cta {
  --e-notice-context-color: var(--e-context-cta-color);
  --e-notice-context-color-dark: var(--e-context-cta-color-dark);
  --e-notice-context-tint: var(--e-context-cta-tint-1);
}
.e-notice--extended {
  --e-notice-is-extended: 1;
}
.e-notice--dismissible {
  padding-right: 38px;
}
.e-notice__aside {
  overflow: hidden;
  background-color: var(--e-notice-context-tint);
  width: calc(var(--e-notice-is-extended, 0) * 50px);
  text-align: center;
  padding-top: 15px;
  flex-grow: 0;
  flex-shrink: 0;
}
.e-notice__icon-wrapper {
  display: inline-block;
  font-size: 0.625rem;
  max-height: 1.5rem;
  width: 1.5rem;
  line-height: 1.5rem;
  border-radius: 100px;
  background: var(--e-notice-context-color);
  color: #fff;
  text-shadow: 0 0 3px var(--e-notice-context-color-dark), 0 0 1px var(--e-notice-context-color-dark), 0 0 1px var(--e-notice-context-color-dark);
}
.e-notice__content {
  padding: 20px;
}
.e-notice__actions {
  display: flex;
}
.e-notice__actions > * + * {
  -webkit-margin-start: 8px;
          margin-inline-start: 8px;
}
.e-notice__dismiss {
  width: 20px;
  height: 20px;
  line-height: 20px;
  font-size: 0.8125rem;
  text-align: center;
  background: none;
  display: block;
  position: absolute;
  top: 0;
  inset-inline-end: 1px;
  border: none;
  margin: 0;
  padding: 9px;
  cursor: pointer;
  font-style: normal;
}
.e-notice__dismiss:before {
  font-family: eicons;
  display: inline-block;
  content: "\e87f";
  color: var(--e-notice-dismiss-color);
  width: 20px;
  border-radius: 20px;
  speak: none;
  text-align: center;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.e-notice__dismiss:hover:before, .e-notice__dismiss:active:before, .e-notice__dismiss:focus:before {
  font-weight: bold;
}
.e-notice__dismiss:focus:before {
  color: #fff;
  background: var(--e-notice-dismiss-color);
  outline: none;
}
.e-notice__dismiss:focus {
  outline: none;
}
.e-notice p {
  line-height: 1.2;
  padding: 0;
  margin: 0;
}
.e-notice p + .e-notice__actions {
  margin-top: 1rem;
}
.e-notice h3 {
  font-size: 1.0625rem;
  line-height: 1.2;
  margin: 0;
}
.e-notice h3 + p {
  margin-top: 8px;
}

/*= Elementor Admin Alert
---------------------------------------*/
.elementor-admin-alert {
  padding: 15px;
  border-left: 5px solid transparent;
  position: relative;
  font-size: 12px;
  line-height: 1.5;
  text-align: right;
}
.elementor-admin-alert a {
  color: inherit;
}
.elementor-admin-alert.elementor-alert-info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bcdff1;
}
.elementor-admin-alert.elementor-alert-success {
  color: #3c763d;
  background-color: #dff0d8;
  border-color: #cae6be;
}
.elementor-admin-alert.elementor-alert-warning {
  color: #8a6d3b;
  background-color: #fcf8e3;
  border-color: #f9f0c3;
}
.elementor-admin-alert.elementor-alert-danger {
  color: #a94442;
  background-color: #f2dede;
  border-color: #e8c4c4;
}

/*= Elementor System Info
---------------------------------------*/
#elementor-system-info {
  padding: 15px;
}
#elementor-system-info .elementor-system-info-section {
  margin-bottom: 10px;
}
#elementor-system-info .elementor-system-info-section > .elementor-system-info-report-name {
  padding-left: 10px;
  border-bottom: 1px solid #e1e1e1;
}
#elementor-system-info .elementor-system-info-section .widefat {
  white-space: pre;
}
#elementor-system-info .elementor-system-info-section .elementor-log-entries {
  white-space: pre-wrap;
}
#elementor-system-info .elementor-system-info-section:not(.elementor-system-info-log) tbody td:first-child {
  width: 300px;
}
#elementor-system-info .elementor-system-info-report-name {
  text-transform: uppercase;
  font-size: 14px;
  margin: 0;
  line-height: 2;
}
#elementor-system-info .elementor-system-info-report-row {
  overflow: hidden;
  padding: 5px 0;
}
#elementor-system-info .elementor-system-info-report-row > * {
  float: left;
}
#elementor-system-info .elementor-system-info-report-field,
#elementor-system-info .elementor-system-info-field-recommendation {
  padding-left: 10px;
  color: #7F7F7F;
}
#elementor-system-info .elementor-system-info-report-fields {
  padding-left: 20px;
}
#elementor-system-info .elementor-system-info-plugin-name {
  color: #000;
}
#elementor-system-info .elementor-system-info-plugin-properties {
  padding: 10px;
}
#elementor-system-info #elementor-system-info-raw-code {
  width: 100%;
  height: 200px;
}
#elementor-system-info #elementor-system-info-raw-code-label {
  padding: 5px;
  display: block;
}
#elementor-system-info .elementor-warning td:first-child {
  border-right: 3px solid #fcb92c;
}
#elementor-system-info a.box-title-tool {
  font-size: 80%;
  margin-right: 15px;
  color: #818a91;
}
#elementor-system-info a.box-title-tool:hover {
  text-decoration: underline;
}
#elementor-system-info #elementor-usage-recalc {
  font-size: 12px;
  color: #ffffff;
  background-color: #a4afb7;
  padding: 4px 18px 5px 18px;
  border-radius: 3px;
}

@keyframes elementor-rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(359deg);
  }
}
#elementor-deactivate-feedback-dialog-wrapper {
  display: none;
}

#elementor-deactivate-feedback-modal .dialog-widget-content {
  width: 550px;
}
#elementor-deactivate-feedback-modal .dialog-header {
  padding: 18px 15px;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  text-align: right;
}
#elementor-deactivate-feedback-modal .dialog-message {
  padding: 30px 30px 0;
  text-align: right;
}
#elementor-deactivate-feedback-modal .elementor-deactivate-feedback-dialog-input {
  float: right;
  margin: 0 0 0 15px;
  box-shadow: none;
}
#elementor-deactivate-feedback-modal .elementor-deactivate-feedback-dialog-input:not(:checked) ~ .elementor-feedback-text {
  display: none;
}
#elementor-deactivate-feedback-modal .elementor-deactivate-feedback-dialog-label {
  display: block;
  font-size: 13px;
  color: #6d7882;
}
#elementor-deactivate-feedback-modal .elementor-feedback-text {
  margin: 10px 30px 0 0;
  padding: 5px;
  font-size: 13px;
  box-shadow: none;
  background-color: #fff;
  width: 92%;
}
#elementor-deactivate-feedback-modal .dialog-buttons-wrapper {
  border-top: none;
  text-align: right;
  padding: 20px 30px 30px;
  overflow: hidden;
}
#elementor-deactivate-feedback-modal .dialog-submit {
  background-color: #93003c;
  border-radius: 3px;
  color: #fff;
  line-height: 1;
  padding: 12px 20px;
  font-size: 13px;
  width: 180px;
  height: 38px;
}
#elementor-deactivate-feedback-modal .dialog-submit.elementor-loading:before {
  display: inline-block;
  content: "\f463";
  font: 18px dashicons;
  animation: elementor-rotation 2s infinite linear;
}
#elementor-deactivate-feedback-modal .dialog-skip {
  font-size: 12px;
  color: #a4afb7;
  background: none;
  float: left;
  width: auto;
}
#elementor-deactivate-feedback-modal[data-feedback-selected=elementor_pro] .elementor-feedback-text {
  color: #b01b1b;
  padding: 0;
}
#elementor-deactivate-feedback-modal[data-feedback-selected=elementor_pro] .dialog-submit {
  display: none;
}

#elementor-deactivate-feedback-dialog-header i {
  color: #93003c;
  font-size: 19px;
}
#elementor-deactivate-feedback-dialog-header-title {
  font-size: 15px;
  text-transform: uppercase;
  font-weight: bold;
  padding-right: 5px;
}
#elementor-deactivate-feedback-dialog-form-caption {
  font-weight: bold;
  font-size: 15px;
  color: #495157;
  line-height: 1.4;
}
#elementor-deactivate-feedback-dialog-form-body {
  padding-top: 30px;
}

.elementor-deactivate-feedback-dialog-input-wrapper {
  line-height: 1;
  overflow: hidden;
  margin-bottom: 15px;
}

#elementor-hidden-area {
  display: none;
}

#elementor-import-template-trigger {
  cursor: pointer;
}

#elementor-import-template-area {
  display: none;
  margin: 50px 0 30px;
  text-align: center;
}

#elementor-import-template-form {
  display: inline-block;
  margin-top: 30px;
  padding: 30px 50px;
  background-color: #FFFFFF;
  border: 1px solid #e5e5e5;
}

#elementor-import-template-title {
  font-size: 18px;
  color: #555d66;
}

.form-table:not(.elementor-maintenance-mode-is-enabled) .elementor-default-hide {
  display: none;
}

.elementor-maintenance-mode-error {
  color: red;
  line-height: 1.6;
  display: none;
}

#tab-replace_url.elementor-active ~ p.submit,
#tab-fontawesome4_migration.elementor-active ~ p.submit,
#tab-import-export-kit.elementor-active ~ p.submit {
  display: none;
}

#elementor_replace_url > div {
  max-width: 800px;
}
#elementor_replace_url > div input {
  margin-bottom: 6px;
}

#elementor_rollback > div,
#elementor_rollback_pro > div {
  display: flex;
}
#elementor_rollback > div input,
#elementor_rollback > div select,
#elementor_rollback_pro > div input,
#elementor_rollback_pro > div select {
  margin-left: 6px;
}

.tab-import-export-kit__wrapper {
  margin: 40px 0;
  max-width: 700px;
}
.tab-import-export-kit__container {
  background-color: white;
  font-size: 16px;
  max-width: 700px;
  padding: 30px;
}
.tab-import-export-kit__container:not(:first-child) {
  margin-top: 5px;
}
.tab-import-export-kit__container p {
  color: #A4AFB7;
  font-size: 16px;
  margin: 20px 0 25px;
}
.tab-import-export-kit__info {
  font-size: 14px;
}
.tab-import-export-kit__container a:not(.elementor-button), .tab-import-export-kit__info a {
  color: #58d0f5;
  text-decoration: underline;
}
.tab-import-export-kit__box {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.tab-import-export-kit__box h2 {
  color: #6D7882;
  font-size: 28px;
  font-weight: normal;
  line-height: 1;
  margin: 0;
}
.tab-import-export-kit__box .elementor-button.elementor-button-success {
  font-weight: bold;
  padding: 8px 16px;
  text-transform: initial;
}

#dashboard-widgets .e-dashboard-widget h3.e-heading {
  font-weight: 600;
  margin-bottom: 13px;
}
#dashboard-widgets .e-dashboard-widget .e-divider_bottom {
  border-bottom: 1px solid #eee;
  margin: 0 -12px;
  padding: 6px 12px;
}
#dashboard-widgets .e-dashboard-widget .e-divider_top {
  border-top: 1px solid #eee;
  margin: 0 -12px;
  padding: 6px 12px;
}
#dashboard-widgets .e-dashboard-widget .e-quick-actions-wrap .e-divider_top,
#dashboard-widgets .e-dashboard-widget .e-news-feed-wrap .e-divider_top {
  padding-top: 18px;
  margin-top: 18px;
}

.e-dashboard-widget .dashicons {
  color: #606a73;
}
.e-dashboard-widget ul.e-action-list li {
  margin-top: 14px;
}
.e-dashboard-widget ul.e-action-list li a {
  margin-right: 5px;
}

#e-dashboard-overview .dashicons {
  vertical-align: middle;
  font-size: 17px;
}
#e-dashboard-overview .e-overview__header {
  display: table;
  width: 100%;
  box-shadow: 0 5px 8px rgba(0, 0, 0, 0.05);
  margin: 0 -12px 8px;
  padding: 0 12px 12px;
}
#e-dashboard-overview .e-overview__logo, #e-dashboard-overview .e-overview__versions, #e-dashboard-overview .e-overview__create {
  display: table-cell;
  vertical-align: middle;
}
#e-dashboard-overview .e-overview__logo {
  width: 30px;
}
#e-dashboard-overview .e-overview__versions {
  padding: 0 10px;
  font-size: 0.9em;
  line-height: 1.5;
}
#e-dashboard-overview .e-overview__version {
  display: block;
}
#e-dashboard-overview .e-overview__create {
  text-align: left;
}
#e-dashboard-overview .e-overview__feed {
  font-size: 14px;
  font-weight: 500;
}
#e-dashboard-overview .e-overview__post {
  margin-top: 10px;
}
#e-dashboard-overview .e-overview__post-link {
  display: inline-block;
}
#e-dashboard-overview .e-overview__badge {
  background: #39b54a;
  color: white;
  font-size: 0.75em;
  padding: 3px 6px;
  border-radius: 3px;
  text-transform: uppercase;
}
#e-dashboard-overview .e-overview__post-description {
  margin: 0 0 1.5em;
}
#e-dashboard-overview .e-overview__recently-edited li {
  color: #72777c;
}
#e-dashboard-overview .e-overview__footer.e-divider_top {
  padding-top: 12px;
  padding-bottom: 0;
}
#e-dashboard-overview .e-overview__footer ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}
#e-dashboard-overview .e-overview__footer ul li {
  padding: 0 10px;
  margin: 0;
  border-right: 1px solid #ddd;
}
#e-dashboard-overview .e-overview__footer ul li:first-child {
  padding-right: 0;
  border: none;
}
#e-dashboard-overview .e-overview__go-pro a {
  color: #93003c;
  font-weight: 500;
}

.post-type-elementor_library #elementor-template-library-tabs-wrapper {
  padding-top: 2em;
  margin-bottom: 2em;
}
.post-type-elementor_library th#taxonomy-elementor_library_category {
  width: 110px;
}

#elementor-new-template-modal .dialog-message {
  max-height: 70vh;
}
#elementor-new-template-modal .e-hidden {
  display: none !important;
}
#elementor-new-template-dialog-content {
  display: flex;
  align-items: center;
  height: 100%;
  text-align: right;
  color: #6d7882;
}
@media (max-width: 1439px) {
  #elementor-new-template-dialog-content {
    padding: 0 50px;
  }
}
@media (min-width: 1440px) {
  #elementor-new-template-dialog-content {
    padding: 0 120px;
  }
}
#elementor-new-template__description {
  width: 35%;
  max-width: 300px;
  padding-left: 100px;
}
#elementor-new-template__description__title {
  font-size: 30px;
  color: #556068;
}
#elementor-new-template__description__title span {
  font-weight: bold;
}
#elementor-new-template__description__content {
  font-size: 16px;
  padding: 30px 0;
}
#elementor-new-template__take_a_tour {
  display: flex;
  align-items: center;
  font-size: 15px;
}
#elementor-new-template__take_a_tour i {
  color: #93003c;
  font-size: 30px;
}
#elementor-new-template__take_a_tour a {
  color: #6d7882;
  padding-right: 10px;
  text-decoration: none;
  font-weight: 500;
}
#elementor-new-template__form {
  flex-grow: 1;
  max-width: 440px;
  padding: 55px;
  background-color: #fff;
  border-radius: 3px;
  box-shadow: 0 2px 30px 0 rgba(0, 0, 0, 0.08);
}
#elementor-new-template__form__title {
  font-size: 23px;
  color: #556068;
}
#elementor-new-template__form__template-type.elementor-form-field__select {
  max-width: initial;
}
#elementor-new-template__form__template-type-badge {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  border-radius: 2px;
  background-color: #f1f3f5;
  padding: 4px;
  font-size: 8px;
  font-weight: 500;
  line-height: 1;
  text-transform: uppercase;
  top: 50%;
  inset-inline-end: 28px;
  transform: translateY(-50%);
}
#elementor-new-template__form .elementor-form-field__label {
  display: block;
  margin: 25px 0 7px;
  font-size: 14px;
  line-height: 1;
}
#elementor-new-template__form .elementor-form-field input,
#elementor-new-template__form .elementor-form-field select {
  width: 100%;
  height: 50px;
  padding: 10px;
  font-size: 14px;
  box-shadow: none;
  border-radius: 3px;
  background: none;
  color: #495157;
  border: 1px solid;
  outline: none;
}
#elementor-new-template__form .elementor-form-field input:not(:focus),
#elementor-new-template__form .elementor-form-field select:not(:focus) {
  border-color: #d5dadf;
}
#elementor-new-template__form .elementor-form-field input:focus,
#elementor-new-template__form .elementor-form-field select:focus {
  border-color: #a4afb7;
}
#elementor-new-template__form .elementor-form-field__select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  cursor: pointer;
}
#elementor-new-template__form .elementor-form-field__select__wrapper {
  position: relative;
}
#elementor-new-template__form .elementor-form-field__select__wrapper:after {
  font-family: eicons;
  content: "\e8ad";
  position: absolute;
  top: 50%;
  left: 10px;
  transform: translateY(-50%);
}
#elementor-new-template__form__submit, #elementor-new-template__form__lock_button {
  display: block;
  width: 100%;
  height: 50px;
  margin-top: 24px;
  box-sizing: border-box;
  text-align: center;
  text-transform: none;
}

@media (max-width: 1024px) {
  #elementor-new-template__description {
    max-width: 250px;
    padding-left: 30px;
  }
}
@media (max-width: 767px) {
  #elementor-new-template__description {
    display: none;
  }
}
#elementor-role-manager {
  max-width: 500px;
  margin-top: 50px;
}
#elementor-role-manager h3 {
  color: #6d7882;
  font-weight: normal;
  font-size: 22px;
}
#elementor-role-manager .elementor-settings-form-page {
  padding: 0;
}
#elementor-role-manager .elementor-role-row {
  background: #ffffff;
  color: #6d7882;
  margin-bottom: 2px;
}
#elementor-role-manager .elementor-role-row .elementor-role-label {
  display: flex;
  padding: 15px 20px;
  font-weight: 500;
  cursor: pointer;
}
#elementor-role-manager .elementor-role-row .elementor-role-label span.elementor-role-name {
  padding-left: 20px;
}
#elementor-role-manager .elementor-role-row .elementor-role-label span.elementor-role-toggle {
  text-align: left;
  flex-grow: 1;
}
#elementor-role-manager .elementor-role-row .elementor-role-label span.elementor-role-excluded-indicator {
  color: #a4afb7;
}
#elementor-role-manager .elementor-role-row .elementor-role-controls {
  background-color: #f7f7f7;
  padding: 20px 20px 5px;
}
#elementor-role-manager .elementor-role-row .elementor-role-controls > div {
  margin-bottom: 15px;
}
#elementor-role-manager .elementor-role-row .elementor-role-controls .elementor-role-go-pro {
  display: flex;
}
#elementor-role-manager .elementor-role-row .elementor-role-controls .elementor-role-go-pro .elementor-role-go-pro__desc {
  font-weight: 500;
  font-style: italic;
}
#elementor-role-manager .elementor-role-row .elementor-role-controls .elementor-role-go-pro .elementor-role-go-pro__link {
  text-align: left;
  flex-grow: 1;
}

#elementor-beta-tester-modal .elementor-templates-modal__header__items-area {
  color: #c2cbd2;
  cursor: pointer;
}
#elementor-beta-tester-modal .elementor-templates-modal__header__items-area:hover .elementor-beta-tester-do-not-show-again,
#elementor-beta-tester-modal .elementor-templates-modal__header__items-area:hover .elementor-templates-modal__header__item > i {
  color: #6d7882;
}
#elementor-beta-tester-modal .elementor-templates-modal__header__items-area .elementor-templates-modal__header__close {
  border: none;
}
#elementor-beta-tester-modal .elementor-templates-modal__header__items-area .elementor-beta-tester-do-not-show-again {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 12px;
  transition: all 0.3s;
}
#elementor-beta-tester-modal .dialog-lightbox-widget-content {
  max-width: 500px;
  height: initial;
}
#elementor-beta-tester-modal .dialog-lightbox-message {
  padding: 40px;
  height: 300px;
  background-color: #fff;
}

#elementor-beta-tester-form__caption {
  font-weight: bold;
  font-size: 20px;
  color: #495157;
}
#elementor-beta-tester-form__description {
  font-size: 15px;
  color: #6d7882;
  margin-top: 10px;
}
#elementor-beta-tester-form__input-wrapper {
  display: flex;
  margin-top: 30px;
}
#elementor-beta-tester-form__input-wrapper .elementor-button {
  border-radius: 3px 0 0 3px;
}
#elementor-beta-tester-form__email {
  flex-grow: 1;
  border: #d5dadf 1px solid;
  border-left: 0;
  border-radius: 0 3px 3px 0;
  margin: 0;
  padding: 10px;
  height: 50px;
}
#elementor-beta-tester-form__terms {
  margin-top: 40px;
  font-size: 11px;
  color: #a4afb7;
}

.e-experiment__title {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}
.e-experiment__title__indicator {
  position: absolute;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  margin-top: 2px;
}
.e-experiment__title__indicator--active {
  background: #39b54a;
}
.e-experiment__title__label {
  margin-right: 24px;
}
.e-experiment__title__tag {
  background: #0085ba;
  color: #ffffff;
  font-size: 0.8em;
  padding: 3px 6px;
  line-height: 1;
  border-radius: 3px;
  font-weight: 600;
  margin-top: 5px;
  margin-right: 24px;
}
.e-experiment__table-title {
  margin: 30px 0;
}
.e-experiment__dependency, .e-experiment__status {
  margin-top: 4px;
  font-size: 0.9em;
  line-height: 18px;
  font-weight: bold;
  font-style: italic;
}
.e-experiment__button.button {
  margin: 18px 0 22px 14px;
}
.e-experiment__dependency {
  color: #21759b;
}
.e-experiment__dependency__title {
  font-weight: inherit;
}

#tab-experiments .form-table tr {
  border-bottom: 1px solid #dcdcde;
}
#tab-experiments .form-table tr:last-child {
  border-bottom: none;
}
#tab-experiments .form-table tr .description {
  font-size: 0.9em;
  margin: 10px 0;
  max-width: 820px;
}

.e-landing-pages-empty .elementor-blank_state {
  padding: 5em 0 2em 0;
}
.e-landing-pages-empty .e-trashed-items {
  text-align: center;
}
/*# sourceMappingURL=admin-rtl.css.map */