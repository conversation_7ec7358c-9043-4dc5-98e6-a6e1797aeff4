.center-align-text {
    margin-left: auto;
    margin-right: auto;
    display: block;
    width: max-content;
    text-align: center;
}


.preview {
    user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    -o-user-select: none;
}

.preview>* {
    text-align: justify !important;
    line-height: 1.2 !important;
}

.preview>small {
    text-decoration: none !important;
    color: #808080 !important;
}

.preview>.sub-color {
    color: #808080 !important;
}

.preview>h4 {
    margin-top: 1.5em !important;
    margin-bottom: 0.5em !important;
}

/* .preview>body {
    size: 7in 9.25in !important;
    margin: 27mm 16mm 27mm 16mm !important;
} */

.preview-box {
    text-align: justify;
    border: solid 1px;
    padding: 3px;
    font-size: 12pt !important;
    width: 21cm;
    font-size: 10pt !important
}

.preview {
    font-size: 10pt
}

.preview-box h2 {
    font-size: 14pt !important;
}

.accordion-header {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline;
    font-size: 1rem;
}

.accordion {
    text-align: justify
}

.navbar-collapse {
    flex-grow: 0;
}


.skills2 {
    background-color: lightgray;
    padding: 10px 15px;
    margin: 5px;
    border-radius: 5px;
    width: max-content;
}


li:before {
    content: "\2014\a0\a0";
}

li {
    list-style: none !important;
}

.pr-2 {
    padding-right: 5dp !important;
}

h4 {
    font-size: 14pt !important;
}

.headding {
    font-size: 12pt;
    margin-bottom: 5px;
}