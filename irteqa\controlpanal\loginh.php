<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>login h</title>
</head>
<!--
<body onload = "sctn();">
<script type="text/javascript">
function sctn(){
    const xhttp = new XMLHttpRequest();
    xhttp.onload = function(){
        document.getElementById("sctn").innerHTML = this.responseText;
    }
    xhttp.open("GET","logint.php");
    xhttp.send();
}

setInterval(function(){
    sctn();
}, 1);
</script>
<section id="sctn">
        
</section> 
</body>
-->
</html>