<?php
/**
 * ملف لإنشاء جدول الإعدادات وإدراج القيم الافتراضية
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

$results = [];
$errors = [];

// إنشاء جدول الإعدادات
try {
    $create_settings_table = "
    CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT NOT NULL,
        setting_type ENUM('text', 'number', 'boolean', 'email', 'url', 'textarea') DEFAULT 'text',
        category VARCHAR(50) DEFAULT 'general',
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    
    if ($db->query($create_settings_table)) {
        $results[] = "تم إنشاء جدول الإعدادات بنجاح";
    } else {
        $errors[] = "فشل في إنشاء جدول الإعدادات: " . $db->error;
    }
} catch (Exception $e) {
    $errors[] = "خطأ في إنشاء جدول الإعدادات: " . $e->getMessage();
}

// إدراج الإعدادات الافتراضية
if (empty($errors)) {
    $default_settings = [
        // إعدادات الشركة
        ['company_name', 'شركة المبيعات والمشتريات', 'text', 'company', 'اسم الشركة'],
        ['company_address', 'الرياض، المملكة العربية السعودية', 'textarea', 'company', 'عنوان الشركة'],
        ['company_phone', '+966501234567', 'text', 'company', 'هاتف الشركة'],
        ['company_email', '<EMAIL>', 'email', 'company', 'البريد الإلكتروني للشركة'],
        ['company_tax_number', '123456789012345', 'text', 'company', 'الرقم الضريبي للشركة'],
        ['company_website', 'https://www.company.com', 'url', 'company', 'موقع الشركة الإلكتروني'],
        
        // إعدادات النظام العامة
        ['default_language', 'ar', 'text', 'general', 'اللغة الافتراضية للنظام'],
        ['currency_symbol', 'ر.س', 'text', 'general', 'رمز العملة'],
        ['currency_code', 'SAR', 'text', 'general', 'كود العملة'],
        ['decimal_places', '2', 'number', 'general', 'عدد الخانات العشرية'],
        ['date_format', 'd/m/Y', 'text', 'general', 'تنسيق التاريخ'],
        ['time_format', 'H:i', 'text', 'general', 'تنسيق الوقت'],
        ['timezone', 'Asia/Riyadh', 'text', 'general', 'المنطقة الزمنية'],
        
        // إعدادات الضريبة
        ['tax_enabled', '1', 'boolean', 'tax', 'تفعيل نظام الضريبة'],
        ['default_tax_rate', '15', 'number', 'tax', 'نسبة الضريبة الافتراضية'],
        ['tax_number_required', '1', 'boolean', 'tax', 'إجبارية الرقم الضريبي'],
        ['tax_inclusive_pricing', '0', 'boolean', 'tax', 'الأسعار شاملة الضريبة'],
        
        // إعدادات الفواتير
        ['invoice_prefix', 'INV-', 'text', 'invoice', 'بادئة رقم الفاتورة'],
        ['invoice_number_length', '6', 'number', 'invoice', 'طول رقم الفاتورة'],
        ['invoice_footer', 'شكراً لتعاملكم معنا', 'textarea', 'invoice', 'تذييل الفاتورة'],
        ['auto_invoice_number', '1', 'boolean', 'invoice', 'ترقيم الفواتير تلقائياً'],
        
        // إعدادات النظام
        ['items_per_page', '10', 'number', 'system', 'عدد العناصر في الصفحة'],
        ['backup_enabled', '1', 'boolean', 'system', 'تفعيل النسخ الاحتياطي'],
        ['maintenance_mode', '0', 'boolean', 'system', 'وضع الصيانة'],
        ['debug_mode', '0', 'boolean', 'system', 'وضع التطوير'],
        
        // إعدادات الأمان
        ['session_timeout', '3600', 'number', 'security', 'مهلة انتهاء الجلسة (بالثواني)'],
        ['password_min_length', '6', 'number', 'security', 'الحد الأدنى لطول كلمة المرور'],
        ['login_attempts', '5', 'number', 'security', 'عدد محاولات تسجيل الدخول المسموحة'],
        ['account_lockout_time', '900', 'number', 'security', 'مدة قفل الحساب (بالثواني)'],
    ];
    
    $insert_count = 0;
    $update_count = 0;
    
    foreach ($default_settings as $setting) {
        list($key, $value, $type, $category, $description) = $setting;
        
        try {
            // التحقق من وجود الإعداد
            $check_stmt = $db->prepare("SELECT id FROM settings WHERE setting_key = ?");
            $check_stmt->bind_param("s", $key);
            $check_stmt->execute();
            $result = $check_stmt->get_result();
            
            if ($result->num_rows > 0) {
                // تحديث الإعداد الموجود
                $update_stmt = $db->prepare("UPDATE settings SET setting_value = ?, setting_type = ?, category = ?, description = ? WHERE setting_key = ?");
                $update_stmt->bind_param("sssss", $value, $type, $category, $description, $key);
                if ($update_stmt->execute()) {
                    $update_count++;
                }
                $update_stmt->close();
            } else {
                // إدراج إعداد جديد
                $insert_stmt = $db->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, category, description) VALUES (?, ?, ?, ?, ?)");
                $insert_stmt->bind_param("sssss", $key, $value, $type, $category, $description);
                if ($insert_stmt->execute()) {
                    $insert_count++;
                }
                $insert_stmt->close();
            }
            $check_stmt->close();
            
        } catch (Exception $e) {
            $errors[] = "خطأ في معالجة الإعداد '$key': " . $e->getMessage();
        }
    }
    
    if ($insert_count > 0) {
        $results[] = "تم إدراج $insert_count إعداد جديد";
    }
    if ($update_count > 0) {
        $results[] = "تم تحديث $update_count إعداد موجود";
    }
}

// عرض النتائج
if (!empty($results)) {
    $_SESSION['success'] = implode('<br>', $results);
}
if (!empty($errors)) {
    $_SESSION['error'] = implode('<br>', $errors);
}

displayMessages();
?>

<div class="container mt-4">
    <h2>إنشاء جدول الإعدادات</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات</h5>
        <p>هذه الأداة تقوم بإنشاء جدول الإعدادات وإدراج القيم الافتراضية للنظام.</p>
    </div>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-cogs"></i>
                حالة جدول الإعدادات
            </h5>
        </div>
        <div class="card-body">
            <?php
            // فحص حالة الجدول
            try {
                $check_table = $db->query("SHOW TABLES LIKE 'settings'");
                $table_exists = ($check_table && $check_table->num_rows > 0);
                
                if ($table_exists) {
                    $count_result = $db->query("SELECT COUNT(*) as count FROM settings");
                    $settings_count = $count_result->fetch_assoc()['count'];
                    
                    echo '<div class="alert alert-success">';
                    echo '<i class="fas fa-check-circle"></i> ';
                    echo "جدول الإعدادات موجود ويحتوي على $settings_count إعداد";
                    echo '</div>';
                    
                    // عرض الإعدادات حسب الفئة
                    $categories_result = $db->query("SELECT DISTINCT category FROM settings ORDER BY category");
                    while ($cat_row = $categories_result->fetch_assoc()) {
                        $category = $cat_row['category'];
                        $cat_settings = $db->query("SELECT setting_key, setting_value, description FROM settings WHERE category = '$category' ORDER BY setting_key");
                        
                        echo "<h6 class='mt-3'>فئة: $category</h6>";
                        echo '<div class="table-responsive">';
                        echo '<table class="table table-sm table-striped">';
                        echo '<thead><tr><th>المفتاح</th><th>القيمة</th><th>الوصف</th></tr></thead>';
                        echo '<tbody>';
                        
                        while ($setting_row = $cat_settings->fetch_assoc()) {
                            echo '<tr>';
                            echo '<td><code>' . htmlspecialchars($setting_row['setting_key']) . '</code></td>';
                            echo '<td>' . htmlspecialchars($setting_row['setting_value']) . '</td>';
                            echo '<td>' . htmlspecialchars($setting_row['description']) . '</td>';
                            echo '</tr>';
                        }
                        
                        echo '</tbody></table></div>';
                    }
                } else {
                    echo '<div class="alert alert-warning">';
                    echo '<i class="fas fa-exclamation-triangle"></i> ';
                    echo 'جدول الإعدادات غير موجود';
                    echo '</div>';
                }
            } catch (Exception $e) {
                echo '<div class="alert alert-danger">';
                echo '<i class="fas fa-times-circle"></i> ';
                echo 'خطأ في فحص الجدول: ' . htmlspecialchars($e->getMessage());
                echo '</div>';
            }
            ?>
        </div>
    </div>
    
    <div class="card mt-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">الإجراءات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>إدارة الإعدادات:</h6>
                    <div class="d-grid gap-2">
                        <a href="settings.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-cogs"></i> صفحة الإعدادات
                        </a>
                        <a href="create_settings_table.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-redo"></i> إعادة إنشاء الجدول
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>العودة:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="test_all_fixes.php" class="btn btn-info btn-sm">
                            <i class="fas fa-clipboard-check"></i> الاختبار الشامل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
