/*Stats*/
.fabo-chart {
    border-right: 1px;
    margin-bottom: 24px; }
.fabo-chart::after {
    content: " ";
    display: table;
    clear: both; }
.fabo-chart .fabo-point {
    height:345px;
    display: inline-block;
    float: left;
    box-sizing: border-box;
    padding-left: 0px;
    ovefabolow: hidden; }
.fabo-chart .fabo-point .fabo-point-inner {
    height: 100%;
    position: relative;
    ovefabolow: hidden;
    background: #f4f6f7; }
.fabo-chart .fabo-point
.fabo-value-text {
    width: 100%;
    text-align: center;
    z-index: 100;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 18px; }
.fabo-chart .fabo-point
.fabo-value-label {
    width: 100%;
    text-align: center;
    z-index: 100;
    color: #95a5b3;
    font-size: 12px;
    font-weight: 700;
    position: absolute;
    left: 0;
    right: 0;
    top: 18px; }
	.fabo-value-label {
    color: #00C6D7!important;
}

.fabo-chart .fabo-point .fabo-value {
    box-sizing: content-box;
    width: 0;
    height: 100px;
    border-top: 0 solid transparent;
    border-right: 0 solid #7b82ff;
    border-bottom: 0 solid transparent;
    border-left: 0px solid #7b82ff;
    transition: height 200ms;
    position: absolute;
    bottom: 0;
    -webkit-animation: chart-height 200ms;
    animation: chart-height 200ms; }
	.fabo-value-label {
    color: #fff!important;
}
.fabo-value-label {
    color: #00C6D7!important;
}
.fabo-chart .fabo-point .fabo-value.hide {
    display: none;
    height: 0 !important; }
.fabo-chart .fabo-point .fabo-value.hide-border {
    border-top-width: 0 !important; }

@-webkit-keyframes chart-height {
    0% {
        height: 0; } }

@keyframes chart-height {
    0% {
        height: 0; } }

/*# sourceMappingURL=chart.css.map */
@media (max-width:1024px){
    .fabo-chart .fabo-point .fabo-value-text {
		width: 100%;
		text-align: center;
		z-index: 100;
		font-size: 15px;
	}
	.fabo-chart .fabo-point .fabo-value-label {
      font-size:10px;
	}
}