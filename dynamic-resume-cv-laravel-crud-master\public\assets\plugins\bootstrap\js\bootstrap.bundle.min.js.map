{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/@popperjs/core/lib/enums.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../node_modules/@popperjs/core/lib/utils/math.js", "../../node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../node_modules/@popperjs/core/lib/utils/within.js", "../../node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../node_modules/@popperjs/core/lib/utils/getVariation.js", "../../node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../node_modules/@popperjs/core/lib/modifiers/flip.js", "../../node_modules/@popperjs/core/lib/modifiers/hide.js", "../../node_modules/@popperjs/core/lib/modifiers/offset.js", "../../node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../node_modules/@popperjs/core/lib/createPopper.js", "../../node_modules/@popperjs/core/lib/utils/debounce.js", "../../node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../node_modules/@popperjs/core/lib/popper-lite.js", "../../node_modules/@popperjs/core/lib/popper.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["TRANSITION_END", "getSelector", "element", "selector", "getAttribute", "hrefAttribute", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "document", "querySelector", "getElementFromSelector", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "object", "j<PERSON>y", "nodeType", "getElement", "length", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "Object", "values", "find", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParameters", "originalTypeEvent", "delegationFunction", "delegation", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "call", "this", "handlers", "previousFunction", "replace", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "keys", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "key", "defineProperty", "get", "preventDefault", "elementMap", "Map", "Data", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "normalizeData", "value", "toString", "JSON", "parse", "decodeURIComponent", "_unused", "normalizeDataKey", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "prototype", "match", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "static", "getInstance", "VERSION", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "getOrCreateInstance", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "endCallback", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLID", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "clearTimeout", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "eventName", "_orderToDirection", "isCycling", "directionalClassName", "orderClassName", "_isAnimated", "SELECTOR_ACTIVE", "clearInterval", "carousel", "slideIndex", "carousels", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "style", "scrollSize", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "selectorElements", "top", "bottom", "right", "left", "auto", "basePlacements", "start", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "getNodeName", "nodeName", "getWindow", "node", "ownerDocument", "defaultView", "isHTMLElement", "HTMLElement", "isShadowRoot", "applyStyles$1", "enabled", "phase", "_ref", "state", "elements", "for<PERSON>ach", "styles", "assign", "effect", "_ref2", "initialStyles", "position", "options", "strategy", "margin", "arrow", "hasOwnProperty", "attribute", "requires", "getBasePlacement", "round", "includeScale", "rect", "scaleX", "scaleY", "offsetWidth", "width", "height", "x", "y", "getLayoutRect", "clientRect", "offsetLeft", "offsetTop", "rootNode", "isSameNode", "host", "isTableElement", "getDocumentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getOffsetParent", "isFirefox", "userAgent", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "getContainingBlock", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "arrow$1", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "len", "padding", "rects", "toPaddingObject", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "offset", "axisProp", "centerOffset", "_options$element", "requiresIfExists", "getVariation", "unsetSides", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasY", "sideX", "sideY", "win", "heightProp", "widthProp", "visualViewport", "_Object$assign", "commonStyles", "_ref4", "dpr", "devicePixelRatio", "roundOffsetsByDPR", "computeStyles$1", "_ref5", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "passive", "eventListeners", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "update", "hash", "getOppositePlacement", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getWindowScrollBarX", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "getScrollParent", "listScrollParents", "_element$ownerDocumen", "isBody", "updatedList", "rectToClientRect", "getClientRectFromMixedType", "clippingParent", "html", "getViewportRect", "clientTop", "clientLeft", "getInnerBoundingClientRect", "winScroll", "scrollWidth", "scrollHeight", "getDocumentRect", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "mainClippingParents", "clipperElement", "getClippingParents", "firstClippingParent", "clippingRect", "accRect", "getClippingRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "flip$1", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "oppositePlacement", "getExpandedFallbackPlacements", "referenceRect", "checksMap", "makeFallbackChecks", "firstFittingPlacement", "i", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "every", "check", "_loop", "_i", "fittingPlacement", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide$1", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "offset$1", "_options$offset", "invertDistance", "skidding", "distance", "distanceAndSkiddingToXY", "_data$state$placement", "popperOffsets$1", "preventOverflow$1", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "_offsetModifierState$", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMax", "preventedOffset", "_offsetModifierState$2", "_mainSide", "_altSide", "_offset", "_len", "_min", "_max", "isOriginSide", "_offsetModifierValue", "_tetherMin", "_tetherMax", "_preventedOffset", "v", "withinMaxClamp", "getCompositeRect", "elementOrVirtualElement", "isOffsetParentAnElement", "offsetParentIsScaled", "isElementScaled", "modifiers", "visited", "result", "modifier", "dep", "depModifier", "DEFAULT_OPTIONS", "areValidElements", "arguments", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "pending", "orderedModifiers", "effectCleanupFns", "isDestroyed", "setOptions", "setOptionsAction", "cleanupModifierEffects", "merged", "orderModifiers", "current", "existing", "m", "_ref3$options", "cleanupFn", "forceUpdate", "_state$elements", "_state$orderedModifie", "_state$orderedModifie2", "Promise", "resolve", "then", "destroy", "onFirstUpdate", "createPopper", "computeStyles", "applyStyles", "flip", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "display", "popperConfig", "autoClose", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "_selectMenuItem", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "dataApiKeydownHandler", "clearMenus", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "innerWidth", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "setProperty", "actualValue", "removeProperty", "callBack", "sel", "EVENT_MOUSEDOWN", "className", "rootElement", "clickCallback", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "TAB_NAV_BACKWARD", "trapElement", "autofocus", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "shift<PERSON>ey", "EVENT_HIDDEN", "EVENT_SHOW", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "modalBody", "_triggerBackdropTransition", "currentTarget", "_resetAdjustments", "isModalOverflowing", "initialOverflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "EVENT_HIDE_PREVENTED", "<PERSON><PERSON><PERSON>", "blur", "uriAttributes", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "allowedAttributeList", "attributeName", "nodeValue", "attributeRegex", "regex", "DefaultAllowlist", "area", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "extraClass", "template", "content", "sanitize", "sanitizeFn", "allowList", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "innerHTML", "_maybeSanitize", "text", "entries", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "sanitizeHtml", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "title", "delay", "container", "customClass", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "click", "_isWithActiveTrigger", "_enter", "_leave", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "_getTipElement", "previousHoverState", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "isShown", "_getDelegateConfig", "attachment", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitle", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "Popover", "_getContent", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "rootMargin", "smoothScroll", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "scrollTo", "threshold", "_getR<PERSON><PERSON><PERSON><PERSON>", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "id", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "_activateParents", "listGroup", "item", "activeNodes", "spy", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "SELECTOR_INNER_ELEM", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;0OAOA,MAEMA,EAAiB,gBAuBjBC,EAAcC,IAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAIE,EAAgBH,EAAQE,aAAa,QAMzC,IAAKC,IAAmBA,EAAcC,SAAS,OAASD,EAAcE,WAAW,KAC/E,OAAO,KAILF,EAAcC,SAAS,OAASD,EAAcE,WAAW,OAC3DF,EAAiB,IAAGA,EAAcG,MAAM,KAAK,MAG/CL,EAAWE,GAAmC,MAAlBA,EAAwBA,EAAcI,OAAS,KAG7E,OAAON,GAGHO,EAAyBR,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKQ,SAASC,cAAcT,GAAYA,EAGrC,MAGHU,EAAyBX,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWQ,SAASC,cAAcT,GAAY,MA0BjDW,EAAuBZ,IAC3BA,EAAQa,cAAc,IAAIC,MAAMhB,KAG5BiB,EAAYC,MACXA,GAA4B,iBAAXA,UAIO,IAAlBA,EAAOC,SAChBD,EAASA,EAAO,SAGgB,IAApBA,EAAOE,UAGjBC,EAAaH,GAEbD,EAAUC,GACLA,EAAOC,OAASD,EAAO,GAAKA,EAGf,iBAAXA,GAAuBA,EAAOI,OAAS,EACzCX,SAASC,cAAcM,GAGzB,KAGHK,EAAYrB,IAChB,IAAKe,EAAUf,IAAgD,IAApCA,EAAQsB,iBAAiBF,OAClD,OAAO,EAGT,MAAMG,EAAgF,YAA7DC,iBAAiBxB,GAASyB,iBAAiB,cAE9DC,EAAgB1B,EAAQ2B,QAAQ,uBAEtC,IAAKD,EACH,OAAOH,EAGT,GAAIG,IAAkB1B,EAAS,CAC7B,MAAM4B,EAAU5B,EAAQ2B,QAAQ,WAChC,GAAIC,GAAWA,EAAQC,aAAeH,EACpC,OAAO,EAGT,GAAgB,OAAZE,EACF,OAAO,EAIX,OAAOL,GAGHO,EAAa9B,IACZA,GAAWA,EAAQkB,WAAaa,KAAKC,gBAItChC,EAAQiC,UAAUC,SAAS,mBAIC,IAArBlC,EAAQmC,SACVnC,EAAQmC,SAGVnC,EAAQoC,aAAa,aAAoD,UAArCpC,EAAQE,aAAa,aAG5DmC,EAAiBrC,IACrB,IAAKS,SAAS6B,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBvC,EAAQwC,YAA4B,CAC7C,MAAMC,EAAOzC,EAAQwC,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIzC,aAAmB0C,WACd1C,EAIJA,EAAQ6B,WAINQ,EAAerC,EAAQ6B,YAHrB,MAMLc,EAAO,OAUPC,EAAS5C,IACbA,EAAQ6C,cAGJC,EAAY,IACZC,OAAOC,SAAWvC,SAASwC,KAAKb,aAAa,qBACxCW,OAAOC,OAGT,KAGHE,EAA4B,GAmB5BC,EAAQ,IAAuC,QAAjC1C,SAAS6B,gBAAgBc,IAEvCC,EAAqBC,IAnBAC,IAAAA,EAAAA,EAoBN,KACjB,MAAMC,EAAIV,IAEV,GAAIU,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,mBA7BQ,YAAxBpD,SAASuD,YAENd,EAA0B9B,QAC7BX,SAASwD,iBAAiB,oBAAoB,KAC5C,IAAK,MAAMV,KAAYL,EACrBK,OAKNL,EAA0BgB,KAAKX,IAE/BA,KAuBEY,EAAUZ,IACU,mBAAbA,GACTA,KAIEa,EAAyB,CAACb,EAAUc,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAH,EAAQZ,GAIV,MACMgB,EA/LiCvE,CAAAA,IACvC,IAAKA,EACH,OAAO,EAIT,IAAIwE,mBAAEA,EAAFC,gBAAsBA,GAAoB1B,OAAOvB,iBAAiBxB,GAEtE,MAAM0E,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBlE,MAAM,KAAK,GACnDmE,EAAkBA,EAAgBnE,MAAM,KAAK,GAnFf,KAqFtBqE,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GAkLgBK,CAAiCT,GADlC,EAGxB,IAAIU,GAAS,EAEb,MAAMC,EAAU,EAAGC,OAAAA,MACbA,IAAWZ,IAIfU,GAAS,EACTV,EAAkBa,oBAAoBpF,EAAgBkF,GACtDb,EAAQZ,KAGVc,EAAkBJ,iBAAiBnE,EAAgBkF,GACnDG,YAAW,KACJJ,GACHnE,EAAqByD,KAEtBE,IAYCa,EAAuB,CAACC,EAAMC,EAAeC,EAAeC,KAChE,MAAMC,EAAaJ,EAAKjE,OACxB,IAAIsE,EAAQL,EAAKM,QAAQL,GAIzB,OAAe,IAAXI,GACMH,GAAiBC,EAAiBH,EAAKI,EAAa,GAAKJ,EAAK,IAGxEK,GAASH,EAAgB,GAAK,EAE1BC,IACFE,GAASA,EAAQD,GAAcA,GAG1BJ,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIJ,EAAOD,EAAa,OC1SjDM,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WAOF,SAASC,EAAYzG,EAAS0G,GAC5B,OAAQA,GAAQ,GAAEA,MAAQP,OAAiBnG,EAAQmG,UAAYA,IAGjE,SAASQ,EAAS3G,GAChB,MAAM0G,EAAMD,EAAYzG,GAKxB,OAHAA,EAAQmG,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,GAqCvB,SAASE,EAAYC,EAAQ7B,EAAS8B,EAAqB,MACzD,OAAOC,OAAOC,OAAOH,GAClBI,MAAKC,GAASA,EAAMC,kBAAoBnC,GAAWkC,EAAMJ,qBAAuBA,IAGrF,SAASM,EAAoBC,EAAmBrC,EAASsC,GACvD,MAAMC,EAAgC,iBAAZvC,EACpBmC,EAAkBI,EAAaD,EAAqBtC,EAC1D,IAAIwC,EAAYC,EAAaJ,GAM7B,OAJKd,EAAamB,IAAIF,KACpBA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAW3H,EAASqH,EAAmBrC,EAASsC,EAAoBM,GAC3E,GAAiC,iBAAtBP,IAAmCrH,EAC5C,OAUF,GAPKgF,IACHA,EAAUsC,EACVA,EAAqB,MAKnBD,KAAqBjB,EAAc,CACrC,MAAMyB,EAAejE,GACZ,SAAUsD,GACf,IAAKA,EAAMY,eAAkBZ,EAAMY,gBAAkBZ,EAAMa,iBAAmBb,EAAMa,eAAe7F,SAASgF,EAAMY,eAChH,OAAOlE,EAAGoE,KAAKC,KAAMf,IAKvBI,EACFA,EAAqBO,EAAaP,GAElCtC,EAAU6C,EAAa7C,GAI3B,MAAOuC,EAAYJ,EAAiBK,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAC3FT,EAASF,EAAS3G,GAClBkI,EAAWrB,EAAOW,KAAeX,EAAOW,GAAa,IACrDW,EAAmBvB,EAAYsB,EAAUf,EAAiBI,EAAavC,EAAU,MAEvF,GAAImD,EAGF,YAFAA,EAAiBP,OAASO,EAAiBP,QAAUA,GAKvD,MAAMlB,EAAMD,EAAYU,EAAiBE,EAAkBe,QAAQrC,EAAgB,KAC7EnC,EAAK2D,EA/Eb,SAAoCvH,EAASC,EAAU2D,GACrD,OAAO,SAASoB,EAAQkC,GACtB,MAAMmB,EAAcrI,EAAQsI,iBAAiBrI,GAE7C,IAAK,IAAIgF,OAAEA,GAAWiC,EAAOjC,GAAUA,IAAWgD,KAAMhD,EAASA,EAAOpD,WACtE,IAAK,MAAM0G,KAAcF,EACvB,GAAIE,IAAetD,EAUnB,OANAiC,EAAMa,eAAiB9C,EAEnBD,EAAQ4C,QACVY,EAAaC,IAAIzI,EAASkH,EAAMwB,KAAMzI,EAAU2D,GAG3CA,EAAG+E,MAAM1D,EAAQ,CAACiC,KAgE7B0B,CAA2B5I,EAASgF,EAASsC,GA5FjD,SAA0BtH,EAAS4D,GACjC,OAAO,SAASoB,EAAQkC,GAOtB,OANAA,EAAMa,eAAiB/H,EAEnBgF,EAAQ4C,QACVY,EAAaC,IAAIzI,EAASkH,EAAMwB,KAAM9E,GAGjCA,EAAG+E,MAAM3I,EAAS,CAACkH,KAqF1B2B,CAAiB7I,EAASgF,GAE5BpB,EAAGkD,mBAAqBS,EAAavC,EAAU,KAC/CpB,EAAGuD,gBAAkBA,EACrBvD,EAAGgE,OAASA,EACZhE,EAAGuC,SAAWO,EACdwB,EAASxB,GAAO9C,EAEhB5D,EAAQiE,iBAAiBuD,EAAW5D,EAAI2D,GAG1C,SAASuB,EAAc9I,EAAS6G,EAAQW,EAAWxC,EAAS8B,GAC1D,MAAMlD,EAAKgD,EAAYC,EAAOW,GAAYxC,EAAS8B,GAE9ClD,IAIL5D,EAAQkF,oBAAoBsC,EAAW5D,EAAImF,QAAQjC,WAC5CD,EAAOW,GAAW5D,EAAGuC,WAG9B,SAAS6C,EAAyBhJ,EAAS6G,EAAQW,EAAWyB,GAC5D,MAAMC,EAAoBrC,EAAOW,IAAc,GAE/C,IAAK,MAAM2B,KAAcpC,OAAOqC,KAAKF,GACnC,GAAIC,EAAW/I,SAAS6I,GAAY,CAClC,MAAM/B,EAAQgC,EAAkBC,GAChCL,EAAc9I,EAAS6G,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,qBAK7E,SAASW,EAAaP,GAGpB,OADAA,EAAQA,EAAMkB,QAAQpC,EAAgB,IAC/BI,EAAac,IAAUA,EAGhC,MAAMsB,EAAe,CACnBa,GAAGrJ,EAASkH,EAAOlC,EAASsC,GAC1BK,EAAW3H,EAASkH,EAAOlC,EAASsC,GAAoB,IAG1DgC,IAAItJ,EAASkH,EAAOlC,EAASsC,GAC3BK,EAAW3H,EAASkH,EAAOlC,EAASsC,GAAoB,IAG1DmB,IAAIzI,EAASqH,EAAmBrC,EAASsC,GACvC,GAAiC,iBAAtBD,IAAmCrH,EAC5C,OAGF,MAAOuH,EAAYJ,EAAiBK,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAC3FiC,EAAc/B,IAAcH,EAC5BR,EAASF,EAAS3G,GAClBwJ,EAAcnC,EAAkBhH,WAAW,KAEjD,QAA+B,IAApB8G,EAAiC,CAE1C,IAAKN,IAAWA,EAAOW,GACrB,OAIF,YADAsB,EAAc9I,EAAS6G,EAAQW,EAAWL,EAAiBI,EAAavC,EAAU,MAIpF,GAAIwE,EACF,IAAK,MAAMC,KAAgB1C,OAAOqC,KAAKvC,GACrCmC,EAAyBhJ,EAAS6G,EAAQ4C,EAAcpC,EAAkBqC,MAAM,IAIpF,MAAMR,EAAoBrC,EAAOW,IAAc,GAC/C,IAAK,MAAMmC,KAAe5C,OAAOqC,KAAKF,GAAoB,CACxD,MAAMC,EAAaQ,EAAYvB,QAAQnC,EAAe,IAEtD,IAAKsD,GAAelC,EAAkBjH,SAAS+I,GAAa,CAC1D,MAAMjC,EAAQgC,EAAkBS,GAChCb,EAAc9I,EAAS6G,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,uBAK7E8C,QAAQ5J,EAASkH,EAAO2C,GACtB,GAAqB,iBAAV3C,IAAuBlH,EAChC,OAAO,KAGT,MAAMwD,EAAIV,IAIV,IAAIgH,EAAc,KACdC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EALH/C,IADFO,EAAaP,IAQZ1D,IACjBsG,EAActG,EAAE1C,MAAMoG,EAAO2C,GAE7BrG,EAAExD,GAAS4J,QAAQE,GACnBC,GAAWD,EAAYI,uBACvBF,GAAkBF,EAAYK,gCAC9BF,EAAmBH,EAAYM,sBAGjC,MAAMC,EAAM,IAAIvJ,MAAMoG,EAAO,CAAE6C,QAAAA,EAASO,YAAY,IAGpD,QAAoB,IAATT,EACT,IAAK,MAAMU,KAAOxD,OAAOqC,KAAKS,GAC5B9C,OAAOyD,eAAeH,EAAKE,EAAK,CAC9BE,IAAG,IACMZ,EAAKU,KAkBpB,OAZIN,GACFI,EAAIK,iBAGFV,GACFhK,EAAQa,cAAcwJ,GAGpBA,EAAIJ,kBAAoBH,GAC1BA,EAAYY,iBAGPL,IChTLM,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAI9K,EAASuK,EAAKQ,GACXJ,EAAWjD,IAAI1H,IAClB2K,EAAWG,IAAI9K,EAAS,IAAI4K,KAG9B,MAAMI,EAAcL,EAAWF,IAAIzK,GAI9BgL,EAAYtD,IAAI6C,IAA6B,IAArBS,EAAYC,KAMzCD,EAAYF,IAAIP,EAAKQ,GAJnBG,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKL,EAAY5B,QAAQ,QAOhIqB,IAAG,CAACzK,EAASuK,IACPI,EAAWjD,IAAI1H,IACV2K,EAAWF,IAAIzK,GAASyK,IAAIF,IAG9B,KAGTe,OAAOtL,EAASuK,GACd,IAAKI,EAAWjD,IAAI1H,GAClB,OAGF,MAAMgL,EAAcL,EAAWF,IAAIzK,GAEnCgL,EAAYO,OAAOhB,GAGM,IAArBS,EAAYC,MACdN,EAAWY,OAAOvL,KC5CxB,SAASwL,EAAcC,GACrB,GAAc,SAAVA,EACF,OAAO,EAGT,GAAc,UAAVA,EACF,OAAO,EAGT,GAAIA,IAAU9G,OAAO8G,GAAOC,WAC1B,OAAO/G,OAAO8G,GAGhB,GAAc,KAAVA,GAA0B,SAAVA,EAClB,OAAO,KAGT,GAAqB,iBAAVA,EACT,OAAOA,EAGT,IACE,OAAOE,KAAKC,MAAMC,mBAAmBJ,IACrC,MAAMK,GACN,OAAOL,GAIX,SAASM,EAAiBxB,GACxB,OAAOA,EAAInC,QAAQ,UAAU4D,GAAQ,IAAGA,EAAIC,kBAG9C,MAAMC,EAAc,CAClBC,iBAAiBnM,EAASuK,EAAKkB,GAC7BzL,EAAQoM,aAAc,WAAUL,EAAiBxB,KAAQkB,IAG3DY,oBAAoBrM,EAASuK,GAC3BvK,EAAQsM,gBAAiB,WAAUP,EAAiBxB,OAGtDgC,kBAAkBvM,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMwM,EAAa,GACbC,EAAS1F,OAAOqC,KAAKpJ,EAAQ0M,SAASC,QAAOpC,GAAOA,EAAIlK,WAAW,QAAUkK,EAAIlK,WAAW,cAElG,IAAK,MAAMkK,KAAOkC,EAAQ,CACxB,IAAIG,EAAUrC,EAAInC,QAAQ,MAAO,IACjCwE,EAAUA,EAAQC,OAAO,GAAGZ,cAAgBW,EAAQlD,MAAM,EAAGkD,EAAQxL,QACrEoL,EAAWI,GAAWpB,EAAcxL,EAAQ0M,QAAQnC,IAGtD,OAAOiC,GAGTM,iBAAgB,CAAC9M,EAASuK,IACjBiB,EAAcxL,EAAQE,aAAc,WAAU6L,EAAiBxB,QCpD1E,MAAMwC,EAEOC,qBACT,MAAO,GAGEC,yBACT,MAAO,GAGEvJ,kBACT,MAAM,IAAIwJ,MAAM,uEAGlBC,WAAWC,GAIT,OAHAA,EAASnF,KAAKoF,gBAAgBD,GAC9BA,EAASnF,KAAKqF,kBAAkBF,GAChCnF,KAAKsF,iBAAiBH,GACfA,EAGTE,kBAAkBF,GAChB,OAAOA,EAGTC,gBAAgBD,EAAQpN,GACtB,MAAMwN,EAAazM,EAAUf,GAAWkM,EAAYY,iBAAiB9M,EAAS,UAAY,GAE1F,MAAO,IACFiI,KAAKwF,YAAYT,WACM,iBAAfQ,EAA0BA,EAAa,MAC9CzM,EAAUf,GAAWkM,EAAYK,kBAAkBvM,GAAW,MAC5C,iBAAXoN,EAAsBA,EAAS,IAI9CG,iBAAiBH,EAAQM,EAAczF,KAAKwF,YAAYR,aACtD,IAAK,MAAMU,KAAY5G,OAAOqC,KAAKsE,GAAc,CAC/C,MAAME,EAAgBF,EAAYC,GAC5BlC,EAAQ2B,EAAOO,GACfE,EAAY9M,EAAU0K,GAAS,UJzCrCzK,OADSA,EI0C+CyK,GJxClD,GAAEzK,IAGL+F,OAAO+G,UAAUpC,SAAS1D,KAAKhH,GAAQ+M,MAAM,eAAe,GAAG9B,cIuClE,IAAK,IAAI+B,OAAOJ,GAAeK,KAAKJ,GAClC,MAAM,IAAIK,UACP,GAAEjG,KAAKwF,YAAY/J,KAAKyK,0BAA0BR,qBAA4BE,yBAAiCD,OJ9C3G5M,IAAAA,GKUf,MAAMoN,UAAsBrB,EAC1BU,YAAYzN,EAASoN,GACnBiB,SAEArO,EAAUmB,EAAWnB,MAKrBiI,KAAKqG,SAAWtO,EAChBiI,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAE/BvC,EAAKC,IAAI7C,KAAKqG,SAAUrG,KAAKwF,YAAYe,SAAUvG,OAIrDwG,UACE5D,EAAKS,OAAOrD,KAAKqG,SAAUrG,KAAKwF,YAAYe,UAC5ChG,EAAaC,IAAIR,KAAKqG,SAAUrG,KAAKwF,YAAYiB,WAEjD,IAAK,MAAMC,KAAgB5H,OAAO6H,oBAAoB3G,MACpDA,KAAK0G,GAAgB,KAIzBE,eAAetL,EAAUvD,EAAS8O,GAAa,GAC7C1K,EAAuBb,EAAUvD,EAAS8O,GAG5C3B,WAAWC,GAIT,OAHAA,EAASnF,KAAKoF,gBAAgBD,EAAQnF,KAAKqG,UAC3ClB,EAASnF,KAAKqF,kBAAkBF,GAChCnF,KAAKsF,iBAAiBH,GACfA,EAIS2B,mBAAC/O,GACjB,OAAO6K,EAAKJ,IAAItJ,EAAWnB,GAAUiI,KAAKuG,UAGlBO,2BAAC/O,EAASoN,EAAS,IAC3C,OAAOnF,KAAK+G,YAAYhP,IAAY,IAAIiI,KAAKjI,EAA2B,iBAAXoN,EAAsBA,EAAS,MAGnF6B,qBACT,MApDY,cAuDHT,sBACT,MAAQ,MAAKvG,KAAKvE,OAGTgL,uBACT,MAAQ,IAAGzG,KAAKuG,WAGFO,iBAACtL,GACf,MAAQ,GAAEA,IAAOwE,KAAKyG,aCtE1B,MAAMQ,EAAuB,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAc,gBAAeF,EAAUT,YACvCjL,EAAO0L,EAAUzL,KAEvB8E,EAAaa,GAAG5I,SAAU4O,EAAa,qBAAoB5L,OAAU,SAAUyD,GAK7E,GAJI,CAAC,IAAK,QAAQ9G,SAAS6H,KAAKqH,UAC9BpI,EAAMwD,iBAGJ5I,EAAWmG,MACb,OAGF,MAAMhD,EAAStE,EAAuBsH,OAASA,KAAKtG,QAAS,IAAG8B,KAC/C0L,EAAUI,oBAAoBtK,GAGtCmK,SCEb,MAAMI,UAAcpB,EAEP1K,kBACT,MAhBS,QAoBX+L,QAGE,GAFmBjH,EAAaoB,QAAQ3B,KAAKqG,SAjB5B,kBAmBFrE,iBACb,OAGFhC,KAAKqG,SAASrM,UAAUqJ,OApBJ,QAsBpB,MAAMwD,EAAa7G,KAAKqG,SAASrM,UAAUC,SAvBvB,QAwBpB+F,KAAK4G,gBAAe,IAAM5G,KAAKyH,mBAAmBzH,KAAKqG,SAAUQ,GAInEY,kBACEzH,KAAKqG,SAAShD,SACd9C,EAAaoB,QAAQ3B,KAAKqG,SA/BR,mBAgClBrG,KAAKwG,UAIeM,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOJ,EAAMD,oBAAoBtH,MAEvC,GAAsB,iBAAXmF,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQnF,WASnBiH,EAAqBM,EAAO,SAM5BnM,EAAmBmM,GCrEnB,MAMMM,EAAuB,4BAO7B,MAAMC,UAAe3B,EAER1K,kBACT,MAhBS,SAoBXsM,SAEE/H,KAAKqG,SAASlC,aAAa,eAAgBnE,KAAKqG,SAASrM,UAAU+N,OAjB7C,WAqBFjB,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOG,EAAOR,oBAAoBtH,MAEzB,WAAXmF,GACFwC,EAAKxC,SAUb5E,EAAaa,GAAG5I,SAlCc,2BAkCkBqP,GAAsB5I,IACpEA,EAAMwD,iBAEN,MAAMuF,EAAS/I,EAAMjC,OAAOtD,QAAQmO,GACvBC,EAAOR,oBAAoBU,GAEnCD,YAOP3M,EAAmB0M,GCxDnB,MAAMG,EAAiB,CACrBjJ,KAAI,CAAChH,EAAUD,EAAUS,SAAS6B,kBACzB,GAAG6N,UAAUC,QAAQtC,UAAUxF,iBAAiBN,KAAKhI,EAASC,IAGvEoQ,QAAO,CAACpQ,EAAUD,EAAUS,SAAS6B,kBAC5B8N,QAAQtC,UAAUpN,cAAcsH,KAAKhI,EAASC,GAGvDqQ,SAAQ,CAACtQ,EAASC,IACT,GAAGkQ,UAAUnQ,EAAQsQ,UAAU3D,QAAO4D,GAASA,EAAMC,QAAQvQ,KAGtEwQ,QAAQzQ,EAASC,GACf,MAAMwQ,EAAU,GAChB,IAAIC,EAAW1Q,EAAQ6B,WAAWF,QAAQ1B,GAE1C,KAAOyQ,GACLD,EAAQvM,KAAKwM,GACbA,EAAWA,EAAS7O,WAAWF,QAAQ1B,GAGzC,OAAOwQ,GAGTE,KAAK3Q,EAASC,GACZ,IAAI2Q,EAAW5Q,EAAQ6Q,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQvQ,GACnB,MAAO,CAAC2Q,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAK9Q,EAASC,GACZ,IAAI6Q,EAAO9Q,EAAQ+Q,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQvQ,GACf,MAAO,CAAC6Q,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,IAGTC,kBAAkBhR,GAChB,MAAMiR,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAC,KAAIjR,GAAa,GAAEA,2BAAiCkR,KAAK,KAE3D,OAAOlJ,KAAKhB,KAAKgK,EAAYjR,GAAS2M,QAAOyE,IAAOtP,EAAWsP,IAAO/P,EAAU+P,OCnD9EpE,EAAU,CACdqE,aAAc,KACdC,cAAe,KACfC,YAAa,MAGTtE,EAAc,CAClBoE,aAAc,kBACdC,cAAe,kBACfC,YAAa,mBAOf,MAAMC,UAAczE,EAClBU,YAAYzN,EAASoN,GACnBiB,QACApG,KAAKqG,SAAWtO,EAEXA,GAAYwR,EAAMC,gBAIvBxJ,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAC/BnF,KAAKyJ,QAAU,EACfzJ,KAAK0J,sBAAwB5I,QAAQhG,OAAO6O,cAC5C3J,KAAK4J,eAII7E,qBACT,OAAOA,EAGEC,yBACT,OAAOA,EAGEvJ,kBACT,MArDS,QAyDX+K,UACEjG,EAAaC,IAAIR,KAAKqG,SAzDR,aA6DhBwD,OAAO5K,GACAe,KAAK0J,sBAMN1J,KAAK8J,wBAAwB7K,KAC/Be,KAAKyJ,QAAUxK,EAAM8K,SANrB/J,KAAKyJ,QAAUxK,EAAM+K,QAAQ,GAAGD,QAUpCE,KAAKhL,GACCe,KAAK8J,wBAAwB7K,KAC/Be,KAAKyJ,QAAUxK,EAAM8K,QAAU/J,KAAKyJ,SAGtCzJ,KAAKkK,eACLhO,EAAQ8D,KAAKsG,QAAQgD,aAGvBa,MAAMlL,GACJe,KAAKyJ,QAAUxK,EAAM+K,SAAW/K,EAAM+K,QAAQ7Q,OAAS,EACrD,EACA8F,EAAM+K,QAAQ,GAAGD,QAAU/J,KAAKyJ,QAGpCS,eACE,MAAME,EAAYzM,KAAK0M,IAAIrK,KAAKyJ,SAEhC,GAAIW,GAlFgB,GAmFlB,OAGF,MAAME,EAAYF,EAAYpK,KAAKyJ,QAEnCzJ,KAAKyJ,QAAU,EAEVa,GAILpO,EAAQoO,EAAY,EAAItK,KAAKsG,QAAQ+C,cAAgBrJ,KAAKsG,QAAQ8C,cAGpEQ,cACM5J,KAAK0J,uBACPnJ,EAAaa,GAAGpB,KAAKqG,SAxGA,wBAwG6BpH,GAASe,KAAK6J,OAAO5K,KACvEsB,EAAaa,GAAGpB,KAAKqG,SAxGF,sBAwG6BpH,GAASe,KAAKiK,KAAKhL,KAEnEe,KAAKqG,SAASrM,UAAUuQ,IAvGG,mBAyG3BhK,EAAaa,GAAGpB,KAAKqG,SAhHD,uBAgH6BpH,GAASe,KAAK6J,OAAO5K,KACtEsB,EAAaa,GAAGpB,KAAKqG,SAhHF,sBAgH6BpH,GAASe,KAAKmK,MAAMlL,KACpEsB,EAAaa,GAAGpB,KAAKqG,SAhHH,qBAgH6BpH,GAASe,KAAKiK,KAAKhL,MAItE6K,wBAAwB7K,GACtB,OAAOe,KAAK0J,wBAjHS,QAiHiBzK,EAAMuL,aAlHrB,UAkHyDvL,EAAMuL,aAItE1D,qBAChB,MAAO,iBAAkBtO,SAAS6B,iBAAmBoQ,UAAUC,eAAiB,GCnHpF,MASMC,EAAa,OACbC,EAAa,OACbC,EAAiB,OACjBC,EAAkB,QAGlBC,GAAc,mBAQdC,GAAsB,WACtBC,GAAoB,SAepBC,GAAmB,CACvBC,UAAkBL,EAClBM,WAAmBP,GAGf9F,GAAU,CACdsG,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGF1G,GAAc,CAClBqG,SAAU,mBACVC,SAAU,UACVE,KAAM,mBACND,MAAO,mBACPE,MAAO,UACPC,KAAM,WAOR,MAAMC,WAAiBxF,EACrBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAK4L,UAAY,KACjB5L,KAAK6L,eAAiB,KACtB7L,KAAK8L,YAAa,EAClB9L,KAAK+L,aAAe,KACpB/L,KAAKgM,aAAe,KAEpBhM,KAAKiM,mBAAqBhE,EAAeG,QAzCjB,uBAyC8CpI,KAAKqG,UAC3ErG,KAAKkM,qBAEDlM,KAAKsG,QAAQkF,OAASR,IACxBhL,KAAKmM,QAKEpH,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA9FS,WAkGXoN,OACE7I,KAAKoM,OAAOzB,GAGd0B,mBAIO7T,SAAS8T,QAAUlT,EAAU4G,KAAKqG,WACrCrG,KAAK6I,OAITH,OACE1I,KAAKoM,OAAOxB,GAGdW,QACMvL,KAAK8L,YACPnT,EAAqBqH,KAAKqG,UAG5BrG,KAAKuM,iBAGPJ,QACEnM,KAAKuM,iBACLvM,KAAKwM,kBAELxM,KAAK4L,UAAYa,aAAY,IAAMzM,KAAKqM,mBAAmBrM,KAAKsG,QAAQ+E,UAG1EqB,oBACO1M,KAAKsG,QAAQkF,OAIdxL,KAAK8L,WACPvL,EAAac,IAAIrB,KAAKqG,SAAU0E,IAAY,IAAM/K,KAAKmM,UAIzDnM,KAAKmM,SAGPQ,GAAGlP,GACD,MAAMmP,EAAQ5M,KAAK6M,YACnB,GAAIpP,EAAQmP,EAAMzT,OAAS,GAAKsE,EAAQ,EACtC,OAGF,GAAIuC,KAAK8L,WAEP,YADAvL,EAAac,IAAIrB,KAAKqG,SAAU0E,IAAY,IAAM/K,KAAK2M,GAAGlP,KAI5D,MAAMqP,EAAc9M,KAAK+M,cAAc/M,KAAKgN,cAC5C,GAAIF,IAAgBrP,EAClB,OAGF,MAAMwP,EAAQxP,EAAQqP,EAAcnC,EAAaC,EAEjD5K,KAAKoM,OAAOa,EAAOL,EAAMnP,IAG3B+I,UACMxG,KAAKgM,cACPhM,KAAKgM,aAAaxF,UAGpBJ,MAAMI,UAIRnB,kBAAkBF,GAEhB,OADAA,EAAO+H,gBAAkB/H,EAAOkG,SACzBlG,EAGT+G,qBACMlM,KAAKsG,QAAQgF,UACf/K,EAAaa,GAAGpB,KAAKqG,SApKJ,uBAoK6BpH,GAASe,KAAKmN,SAASlO,KAG5C,UAAvBe,KAAKsG,QAAQiF,QACfhL,EAAaa,GAAGpB,KAAKqG,SAvKD,0BAuK6B,IAAMrG,KAAKuL,UAC5DhL,EAAaa,GAAGpB,KAAKqG,SAvKD,0BAuK6B,IAAMrG,KAAK0M,uBAG1D1M,KAAKsG,QAAQmF,OAASlC,EAAMC,eAC9BxJ,KAAKoN,0BAITA,0BACE,IAAK,MAAMC,KAAOpF,EAAejJ,KAhKX,qBAgKmCgB,KAAKqG,UAC5D9F,EAAaa,GAAGiM,EAhLI,yBAgLmBpO,GAASA,EAAMwD,mBAGxD,MAqBM6K,EAAc,CAClBlE,aAAc,IAAMpJ,KAAKoM,OAAOpM,KAAKuN,kBAAkB1C,IACvDxB,cAAe,IAAMrJ,KAAKoM,OAAOpM,KAAKuN,kBAAkBzC,IACxDxB,YAxBkB,KACS,UAAvBtJ,KAAKsG,QAAQiF,QAYjBvL,KAAKuL,QACDvL,KAAK+L,cACPyB,aAAaxN,KAAK+L,cAGpB/L,KAAK+L,aAAe7O,YAAW,IAAM8C,KAAK0M,qBAjNjB,IAiN+D1M,KAAKsG,QAAQ+E,aASvGrL,KAAKgM,aAAe,IAAIzC,EAAMvJ,KAAKqG,SAAUiH,GAG/CH,SAASlO,GACP,GAAI,kBAAkB+G,KAAK/G,EAAMjC,OAAOqK,SACtC,OAGF,MAAMiD,EAAYY,GAAiBjM,EAAMqD,KACrCgI,IACFrL,EAAMwD,iBACNzC,KAAKoM,OAAOpM,KAAKuN,kBAAkBjD,KAIvCyC,cAAchV,GACZ,OAAOiI,KAAK6M,YAAYnP,QAAQ3F,GAGlC0V,2BAA2BhQ,GACzB,IAAKuC,KAAKiM,mBACR,OAGF,MAAMyB,EAAkBzF,EAAeG,QA1NnB,UA0N4CpI,KAAKiM,oBAErEyB,EAAgB1T,UAAUqJ,OAAO4H,IACjCyC,EAAgBrJ,gBAAgB,gBAEhC,MAAMsJ,EAAqB1F,EAAeG,QAAS,sBAAqB3K,MAAWuC,KAAKiM,oBAEpF0B,IACFA,EAAmB3T,UAAUuQ,IAAIU,IACjC0C,EAAmBxJ,aAAa,eAAgB,SAIpDqI,kBACE,MAAMzU,EAAUiI,KAAK6L,gBAAkB7L,KAAKgN,aAE5C,IAAKjV,EACH,OAGF,MAAM6V,EAAkBlR,OAAOmR,SAAS9V,EAAQE,aAAa,oBAAqB,IAElF+H,KAAKsG,QAAQ+E,SAAWuC,GAAmB5N,KAAKsG,QAAQ4G,gBAG1Dd,OAAOa,EAAOlV,EAAU,MACtB,GAAIiI,KAAK8L,WACP,OAGF,MAAMzO,EAAgB2C,KAAKgN,aACrBc,EAASb,IAAUtC,EACnBoD,EAAchW,GAAWoF,EAAqB6C,KAAK6M,YAAaxP,EAAeyQ,EAAQ9N,KAAKsG,QAAQoF,MAE1G,GAAIqC,IAAgB1Q,EAClB,OAGF,MAAM2Q,EAAmBhO,KAAK+M,cAAcgB,GAEtCE,EAAeC,GACZ3N,EAAaoB,QAAQ3B,KAAKqG,SAAU6H,EAAW,CACpDrO,cAAekO,EACfzD,UAAWtK,KAAKmO,kBAAkBlB,GAClC7J,KAAMpD,KAAK+M,cAAc1P,GACzBsP,GAAIqB,IAMR,GAFmBC,EA5RF,qBA8RFjM,iBACb,OAGF,IAAK3E,IAAkB0Q,EAGrB,OAGF,MAAMK,EAAYtN,QAAQd,KAAK4L,WAC/B5L,KAAKuL,QAELvL,KAAK8L,YAAa,EAElB9L,KAAKyN,2BAA2BO,GAChChO,KAAK6L,eAAiBkC,EAEtB,MAAMM,EAAuBP,EAnSR,sBADF,oBAqSbQ,EAAiBR,EAnSH,qBACA,qBAoSpBC,EAAY/T,UAAUuQ,IAAI+D,GAE1B3T,EAAOoT,GAEP1Q,EAAcrD,UAAUuQ,IAAI8D,GAC5BN,EAAY/T,UAAUuQ,IAAI8D,GAa1BrO,KAAK4G,gBAXoB,KACvBmH,EAAY/T,UAAUqJ,OAAOgL,EAAsBC,GACnDP,EAAY/T,UAAUuQ,IAAIU,IAE1B5N,EAAcrD,UAAUqJ,OAAO4H,GAAmBqD,EAAgBD,GAElErO,KAAK8L,YAAa,EAElBmC,EAAalD,MAGuB1N,EAAe2C,KAAKuO,eAEtDH,GACFpO,KAAKmM,QAIToC,cACE,OAAOvO,KAAKqG,SAASrM,UAAUC,SAlUV,SAqUvB+S,aACE,OAAO/E,EAAeG,QA9TGoG,wBA8T2BxO,KAAKqG,UAG3DwG,YACE,OAAO5E,EAAejJ,KAnUJ,iBAmUwBgB,KAAKqG,UAGjDkG,iBACMvM,KAAK4L,YACP6C,cAAczO,KAAK4L,WACnB5L,KAAK4L,UAAY,MAIrB2B,kBAAkBjD,GAChB,OAAIpP,IACKoP,IAAcO,EAAiBD,EAAaD,EAG9CL,IAAcO,EAAiBF,EAAaC,EAGrDuD,kBAAkBlB,GAChB,OAAI/R,IACK+R,IAAUrC,EAAaC,EAAiBC,EAG1CmC,IAAUrC,EAAaE,EAAkBD,EAI5B/D,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOgE,GAASrE,oBAAoBtH,KAAMmF,GAEhD,GAAsB,iBAAXA,GAKX,GAAsB,iBAAXA,EAAqB,CAC9B,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,WATLwC,EAAKgF,GAAGxH,OAmBhB5E,EAAaa,GAAG5I,SAjYc,6BAeF,uCAkXyC,SAAUyG,GAC7E,MAAMjC,EAAStE,EAAuBsH,MAEtC,IAAKhD,IAAWA,EAAOhD,UAAUC,SAAS+Q,IACxC,OAGF/L,EAAMwD,iBAEN,MAAMiM,EAAW/C,GAASrE,oBAAoBtK,GACxC2R,EAAa3O,KAAK/H,aAAa,oBAErC,OAAI0W,GACFD,EAAS/B,GAAGgC,QACZD,EAAShC,qBAIyC,SAAhDzI,EAAYY,iBAAiB7E,KAAM,UACrC0O,EAAS7F,YACT6F,EAAShC,sBAIXgC,EAAShG,YACTgG,EAAShC,wBAGXnM,EAAaa,GAAGtG,OA9Za,6BA8ZgB,KAC3C,MAAM8T,EAAY3G,EAAejJ,KA9YR,6BAgZzB,IAAK,MAAM0P,KAAYE,EACrBjD,GAASrE,oBAAoBoH,MAQjCtT,EAAmBuQ,IClcnB,MAWMkD,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aASxBlH,GAAuB,8BAEvB9C,GAAU,CACdgD,QAAQ,EACRiH,OAAQ,MAGJhK,GAAc,CAClB+C,OAAQ,UACRiH,OAAQ,kBAOV,MAAMC,WAAiB9I,EACrBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAKkP,kBAAmB,EACxBlP,KAAKmP,cAAgB,GAErB,MAAMC,EAAanH,EAAejJ,KAAK6I,IAEvC,IAAK,MAAMwH,KAAQD,EAAY,CAC7B,MAAMpX,EAAWO,EAAuB8W,GAClCC,EAAgBrH,EAAejJ,KAAKhH,GACvC0M,QAAO6K,GAAgBA,IAAiBvP,KAAKqG,WAE/B,OAAbrO,GAAqBsX,EAAcnW,QACrC6G,KAAKmP,cAAclT,KAAKoT,GAI5BrP,KAAKwP,sBAEAxP,KAAKsG,QAAQ0I,QAChBhP,KAAKyP,0BAA0BzP,KAAKmP,cAAenP,KAAK0P,YAGtD1P,KAAKsG,QAAQyB,QACf/H,KAAK+H,SAKEhD,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA9ES,WAkFXsM,SACM/H,KAAK0P,WACP1P,KAAK2P,OAEL3P,KAAK4P,OAITA,OACE,GAAI5P,KAAKkP,kBAAoBlP,KAAK0P,WAChC,OAGF,IAAIG,EAAiB,GASrB,GANI7P,KAAKsG,QAAQ0I,SACfa,EAAiB7P,KAAK8P,uBA9EH,wCA+EhBpL,QAAO3M,GAAWA,IAAYiI,KAAKqG,WACnC4C,KAAIlR,GAAWkX,GAAS3H,oBAAoBvP,EAAS,CAAEgQ,QAAQ,OAGhE8H,EAAe1W,QAAU0W,EAAe,GAAGX,iBAC7C,OAIF,GADmB3O,EAAaoB,QAAQ3B,KAAKqG,SAvG7B,oBAwGDrE,iBACb,OAGF,IAAK,MAAM+N,KAAkBF,EAC3BE,EAAeJ,OAGjB,MAAMK,EAAYhQ,KAAKiQ,gBAEvBjQ,KAAKqG,SAASrM,UAAUqJ,OAAOyL,IAC/B9O,KAAKqG,SAASrM,UAAUuQ,IAAIwE,IAE5B/O,KAAKqG,SAAS6J,MAAMF,GAAa,EAEjChQ,KAAKyP,0BAA0BzP,KAAKmP,eAAe,GACnDnP,KAAKkP,kBAAmB,EAExB,MAYMiB,EAAc,SADSH,EAAU,GAAG9J,cAAgB8J,EAAUvO,MAAM,KAG1EzB,KAAK4G,gBAdY,KACf5G,KAAKkP,kBAAmB,EAExBlP,KAAKqG,SAASrM,UAAUqJ,OAAO0L,IAC/B/O,KAAKqG,SAASrM,UAAUuQ,IAAIuE,GAAqBD,IAEjD7O,KAAKqG,SAAS6J,MAAMF,GAAa,GAEjCzP,EAAaoB,QAAQ3B,KAAKqG,SAjIX,uBAuIarG,KAAKqG,UAAU,GAC7CrG,KAAKqG,SAAS6J,MAAMF,GAAc,GAAEhQ,KAAKqG,SAAS8J,OAGpDR,OACE,GAAI3P,KAAKkP,mBAAqBlP,KAAK0P,WACjC,OAIF,GADmBnP,EAAaoB,QAAQ3B,KAAKqG,SA/I7B,oBAgJDrE,iBACb,OAGF,MAAMgO,EAAYhQ,KAAKiQ,gBAEvBjQ,KAAKqG,SAAS6J,MAAMF,GAAc,GAAEhQ,KAAKqG,SAAS+J,wBAAwBJ,OAE1ErV,EAAOqF,KAAKqG,UAEZrG,KAAKqG,SAASrM,UAAUuQ,IAAIwE,IAC5B/O,KAAKqG,SAASrM,UAAUqJ,OAAOyL,GAAqBD,IAEpD,IAAK,MAAMlN,KAAW3B,KAAKmP,cAAe,CACxC,MAAMpX,EAAUW,EAAuBiJ,GAEnC5J,IAAYiI,KAAK0P,SAAS3X,IAC5BiI,KAAKyP,0BAA0B,CAAC9N,IAAU,GAI9C3B,KAAKkP,kBAAmB,EASxBlP,KAAKqG,SAAS6J,MAAMF,GAAa,GAEjChQ,KAAK4G,gBATY,KACf5G,KAAKkP,kBAAmB,EACxBlP,KAAKqG,SAASrM,UAAUqJ,OAAO0L,IAC/B/O,KAAKqG,SAASrM,UAAUuQ,IAAIuE,IAC5BvO,EAAaoB,QAAQ3B,KAAKqG,SA1KV,wBA+KYrG,KAAKqG,UAAU,GAG/CqJ,SAAS3X,EAAUiI,KAAKqG,UACtB,OAAOtO,EAAQiC,UAAUC,SAAS4U,IAIpCxJ,kBAAkBF,GAGhB,OAFAA,EAAO4C,OAASjH,QAAQqE,EAAO4C,QAC/B5C,EAAO6J,OAAS9V,EAAWiM,EAAO6J,QAC3B7J,EAGT8K,gBACE,OAAOjQ,KAAKqG,SAASrM,UAAUC,SAtLL,uBAEhB,QACC,SAsLbuV,sBACE,IAAKxP,KAAKsG,QAAQ0I,OAChB,OAGF,MAAM3G,EAAWrI,KAAK8P,uBAAuBjI,IAE7C,IAAK,MAAM9P,KAAWsQ,EAAU,CAC9B,MAAMgI,EAAW3X,EAAuBX,GAEpCsY,GACFrQ,KAAKyP,0BAA0B,CAAC1X,GAAUiI,KAAK0P,SAASW,KAK9DP,uBAAuB9X,GACrB,MAAMqQ,EAAWJ,EAAejJ,KA3MA,6BA2MiCgB,KAAKsG,QAAQ0I,QAE9E,OAAO/G,EAAejJ,KAAKhH,EAAUgI,KAAKsG,QAAQ0I,QAAQtK,QAAO3M,IAAYsQ,EAASlQ,SAASJ,KAGjG0X,0BAA0Ba,EAAcC,GACtC,GAAKD,EAAanX,OAIlB,IAAK,MAAMpB,KAAWuY,EACpBvY,EAAQiC,UAAU+N,OAvNK,aAuNyBwI,GAChDxY,EAAQoM,aAAa,gBAAiBoM,GAKpBzJ,uBAAC3B,GACrB,MAAMmB,EAAU,GAKhB,MAJsB,iBAAXnB,GAAuB,YAAYa,KAAKb,KACjDmB,EAAQyB,QAAS,GAGZ/H,KAAK0H,MAAK,WACf,MAAMC,EAAOsH,GAAS3H,oBAAoBtH,KAAMsG,GAEhD,GAAsB,iBAAXnB,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UAUb5E,EAAaa,GAAG5I,SA1Pc,6BA0PkBqP,IAAsB,SAAU5I,IAEjD,MAAzBA,EAAMjC,OAAOqK,SAAoBpI,EAAMa,gBAAmD,MAAjCb,EAAMa,eAAeuH,UAChFpI,EAAMwD,iBAGR,MAAMzK,EAAWO,EAAuByH,MAClCwQ,EAAmBvI,EAAejJ,KAAKhH,GAE7C,IAAK,MAAMD,KAAWyY,EACpBvB,GAAS3H,oBAAoBvP,EAAS,CAAEgQ,QAAQ,IAASA,YAQ7D3M,EAAmB6T,IC3SZ,IAAIwB,GAAM,MACNC,GAAS,SACTC,GAAQ,QACRC,GAAO,OACPC,GAAO,OACPC,GAAiB,CAACL,GAAKC,GAAQC,GAAOC,IACtCG,GAAQ,QACRC,GAAM,MACNC,GAAkB,kBAClBC,GAAW,WACXC,GAAS,SACTC,GAAY,YACZC,GAAmCP,GAAeQ,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAIrJ,OAAO,CAACsJ,EAAY,IAAMT,GAAOS,EAAY,IAAMR,OAC7D,IACQS,GAA0B,GAAGvJ,OAAO4I,GAAgB,CAACD,KAAOS,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAIrJ,OAAO,CAACsJ,EAAWA,EAAY,IAAMT,GAAOS,EAAY,IAAMR,OACxE,IAEQU,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAa,aACbC,GAAO,OACPC,GAAY,YAEZC,GAAc,cACdC,GAAQ,QACRC,GAAa,aACbC,GAAiB,CAACT,GAAYC,GAAMC,GAAWC,GAAYC,GAAMC,GAAWC,GAAaC,GAAOC,IC9B5F,SAASE,GAAYra,GAClC,OAAOA,GAAWA,EAAQsa,UAAY,IAAIrO,cAAgB,KCD7C,SAASsO,GAAUC,GAChC,GAAY,MAARA,EACF,OAAOzX,OAGT,GAAwB,oBAApByX,EAAK9O,WAAkC,CACzC,IAAI+O,EAAgBD,EAAKC,cACzB,OAAOA,GAAgBA,EAAcC,aAAwB3X,OAG/D,OAAOyX,ECRT,SAASzZ,GAAUyZ,GAEjB,OAAOA,aADUD,GAAUC,GAAMpK,SACIoK,aAAgBpK,QAGvD,SAASuK,GAAcH,GAErB,OAAOA,aADUD,GAAUC,GAAMI,aACIJ,aAAgBI,YAGvD,SAASC,GAAaL,GAEpB,MAA0B,oBAAf9X,aAKJ8X,aADUD,GAAUC,GAAM9X,YACI8X,aAAgB9X,YCyDvD,MAAAoY,GAAe,CACbrX,KAAM,cACNsX,SAAS,EACTC,MAAO,QACPpX,GA5EF,SAAqBqX,GACnB,IAAIC,EAAQD,EAAKC,MACjBnU,OAAOqC,KAAK8R,EAAMC,UAAUC,SAAQ,SAAU3X,GAC5C,IAAI0U,EAAQ+C,EAAMG,OAAO5X,IAAS,GAC9B+I,EAAa0O,EAAM1O,WAAW/I,IAAS,GACvCzD,EAAUkb,EAAMC,SAAS1X,GAExBkX,GAAc3a,IAAaqa,GAAYra,KAO5C+G,OAAOuU,OAAOtb,EAAQmY,MAAOA,GAC7BpR,OAAOqC,KAAKoD,GAAY4O,SAAQ,SAAU3X,GACxC,IAAIgI,EAAQe,EAAW/I,IAET,IAAVgI,EACFzL,EAAQsM,gBAAgB7I,GAExBzD,EAAQoM,aAAa3I,GAAgB,IAAVgI,EAAiB,GAAKA,WAwDvD8P,OAlDF,SAAgBC,GACd,IAAIN,EAAQM,EAAMN,MACdO,EAAgB,CAClBrC,OAAQ,CACNsC,SAAUR,EAAMS,QAAQC,SACxB/C,KAAM,IACNH,IAAK,IACLmD,OAAQ,KAEVC,MAAO,CACLJ,SAAU,YAEZrC,UAAW,IASb,OAPAtS,OAAOuU,OAAOJ,EAAMC,SAAS/B,OAAOjB,MAAOsD,EAAcrC,QACzD8B,EAAMG,OAASI,EAEXP,EAAMC,SAASW,OACjB/U,OAAOuU,OAAOJ,EAAMC,SAASW,MAAM3D,MAAOsD,EAAcK,OAGnD,WACL/U,OAAOqC,KAAK8R,EAAMC,UAAUC,SAAQ,SAAU3X,GAC5C,IAAIzD,EAAUkb,EAAMC,SAAS1X,GACzB+I,EAAa0O,EAAM1O,WAAW/I,IAAS,GAGvC0U,EAFkBpR,OAAOqC,KAAK8R,EAAMG,OAAOU,eAAetY,GAAQyX,EAAMG,OAAO5X,GAAQgY,EAAchY,IAE7E8V,QAAO,SAAUpB,EAAOxK,GAElD,OADAwK,EAAMxK,GAAY,GACXwK,IACN,IAEEwC,GAAc3a,IAAaqa,GAAYra,KAI5C+G,OAAOuU,OAAOtb,EAAQmY,MAAOA,GAC7BpR,OAAOqC,KAAKoD,GAAY4O,SAAQ,SAAUY,GACxChc,EAAQsM,gBAAgB0P,YAa9BC,SAAU,CAAC,kBCjFE,SAASC,GAAiBzC,GACvC,OAAOA,EAAUnZ,MAAM,KAAK,GCFvB,IAAIuF,GAAMD,KAAKC,IACXC,GAAMF,KAAKE,IACXqW,GAAQvW,KAAKuW,MCAT,SAAS9D,GAAsBrY,EAASoc,QAChC,IAAjBA,IACFA,GAAe,GAGjB,IAAIC,EAAOrc,EAAQqY,wBACfiE,EAAS,EACTC,EAAS,EAEb,GAAI5B,GAAc3a,IAAYoc,EAAc,CAC1C,IAAIvZ,EAAe7C,EAAQ6C,aACvB2Z,EAAcxc,EAAQwc,YAGtBA,EAAc,IAChBF,EAASH,GAAME,EAAKI,OAASD,GAAe,GAG1C3Z,EAAe,IACjB0Z,EAASJ,GAAME,EAAKK,QAAU7Z,GAAgB,GAIlD,MAAO,CACL4Z,MAAOJ,EAAKI,MAAQH,EACpBI,OAAQL,EAAKK,OAASH,EACtB7D,IAAK2D,EAAK3D,IAAM6D,EAChB3D,MAAOyD,EAAKzD,MAAQ0D,EACpB3D,OAAQ0D,EAAK1D,OAAS4D,EACtB1D,KAAMwD,EAAKxD,KAAOyD,EAClBK,EAAGN,EAAKxD,KAAOyD,EACfM,EAAGP,EAAK3D,IAAM6D,GC9BH,SAASM,GAAc7c,GACpC,IAAI8c,EAAazE,GAAsBrY,GAGnCyc,EAAQzc,EAAQwc,YAChBE,EAAS1c,EAAQ6C,aAUrB,OARI+C,KAAK0M,IAAIwK,EAAWL,MAAQA,IAAU,IACxCA,EAAQK,EAAWL,OAGjB7W,KAAK0M,IAAIwK,EAAWJ,OAASA,IAAW,IAC1CA,EAASI,EAAWJ,QAGf,CACLC,EAAG3c,EAAQ+c,WACXH,EAAG5c,EAAQgd,UACXP,MAAOA,EACPC,OAAQA,GCrBG,SAASxa,GAAS+U,EAAQ1G,GACvC,IAAI0M,EAAW1M,EAAM/N,aAAe+N,EAAM/N,cAE1C,GAAIyU,EAAO/U,SAASqO,GAClB,OAAO,EAEJ,GAAI0M,GAAYpC,GAAaoC,GAAW,CACzC,IAAInM,EAAOP,EAEX,EAAG,CACD,GAAIO,GAAQmG,EAAOiG,WAAWpM,GAC5B,OAAO,EAITA,EAAOA,EAAKjP,YAAciP,EAAKqM,WACxBrM,GAIb,OAAO,ECpBM,SAAStP,GAAiBxB,GACvC,OAAOua,GAAUva,GAASwB,iBAAiBxB,GCD9B,SAASod,GAAepd,GACrC,MAAO,CAAC,QAAS,KAAM,MAAM2F,QAAQ0U,GAAYra,KAAa,ECDjD,SAASqd,GAAmBrd,GAEzC,QAASe,GAAUf,GAAWA,EAAQya,cACtCza,EAAQS,WAAasC,OAAOtC,UAAU6B,gBCDzB,SAASgb,GAActd,GACpC,MAA6B,SAAzBqa,GAAYra,GACPA,EAMPA,EAAQud,cACRvd,EAAQ6B,aACRgZ,GAAa7a,GAAWA,EAAQmd,KAAO,OAEvCE,GAAmBrd,GCRvB,SAASwd,GAAoBxd,GAC3B,OAAK2a,GAAc3a,IACoB,UAAvCwB,GAAiBxB,GAAS0b,SAInB1b,EAAQyd,aAHN,KA4CI,SAASC,GAAgB1d,GAItC,IAHA,IAAI+C,EAASwX,GAAUva,GACnByd,EAAeD,GAAoBxd,GAEhCyd,GAAgBL,GAAeK,IAA6D,WAA5Cjc,GAAiBic,GAAc/B,UACpF+B,EAAeD,GAAoBC,GAGrC,OAAIA,IAA+C,SAA9BpD,GAAYoD,IAA0D,SAA9BpD,GAAYoD,IAAwE,WAA5Cjc,GAAiBic,GAAc/B,UAC3H3Y,EAGF0a,GAhDT,SAA4Bzd,GAC1B,IAAI2d,GAAsE,IAA1DjL,UAAUkL,UAAU3R,cAActG,QAAQ,WAG1D,IAFuD,IAA5C+M,UAAUkL,UAAUjY,QAAQ,YAE3BgV,GAAc3a,IAII,UAFXwB,GAAiBxB,GAEnB0b,SACb,OAAO,KAIX,IAAImC,EAAcP,GAActd,GAMhC,IAJI6a,GAAagD,KACfA,EAAcA,EAAYV,MAGrBxC,GAAckD,IAAgB,CAAC,OAAQ,QAAQlY,QAAQ0U,GAAYwD,IAAgB,GAAG,CAC3F,IAAIC,EAAMtc,GAAiBqc,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAiF,IAA1D,CAAC,YAAa,eAAetY,QAAQmY,EAAII,aAAsBP,GAAgC,WAAnBG,EAAII,YAA2BP,GAAaG,EAAInR,QAAyB,SAAfmR,EAAInR,OACjO,OAAOkR,EAEPA,EAAcA,EAAYhc,WAI9B,OAAO,KAiBgBsc,CAAmBne,IAAY+C,EClEzC,SAASqb,GAAyB3E,GAC/C,MAAO,CAAC,MAAO,UAAU9T,QAAQ8T,IAAc,EAAI,IAAM,ICApD,SAAS4E,GAAOvY,EAAK2F,EAAO5F,GACjC,OAAOyY,GAAQxY,EAAKyY,GAAQ9S,EAAO5F,ICDtB,SAAS2Y,GAAmBC,GACzC,OAAO1X,OAAOuU,OAAO,GCDd,CACL5C,IAAK,EACLE,MAAO,EACPD,OAAQ,EACRE,KAAM,GDHuC4F,GEFlC,SAASC,GAAgBjT,EAAOrC,GAC7C,OAAOA,EAAKmQ,QAAO,SAAUoF,EAASpU,GAEpC,OADAoU,EAAQpU,GAAOkB,EACRkT,IACN,ICwFL,MAAAC,GAAe,CACbnb,KAAM,QACNsX,SAAS,EACTC,MAAO,OACPpX,GA9EF,SAAeqX,GACb,IAAI4D,EAEA3D,EAAQD,EAAKC,MACbzX,EAAOwX,EAAKxX,KACZkY,EAAUV,EAAKU,QACfmD,EAAe5D,EAAMC,SAASW,MAC9BiD,EAAgB7D,EAAM8D,cAAcD,cACpCE,EAAgB/C,GAAiBhB,EAAMzB,WACvCyF,EAAOd,GAAyBa,GAEhCE,EADa,CAACtG,GAAMD,IAAOjT,QAAQsZ,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIN,EAxBgB,SAAyBW,EAASlE,GAItD,OAAOsD,GAAsC,iBAH7CY,EAA6B,mBAAZA,EAAyBA,EAAQrY,OAAOuU,OAAO,GAAIJ,EAAMmE,MAAO,CAC/E5F,UAAWyB,EAAMzB,aACb2F,GACkDA,EAAUV,GAAgBU,EAASrG,KAoBvEuG,CAAgB3D,EAAQyD,QAASlE,GACjDqE,EAAY1C,GAAciC,GAC1BU,EAAmB,MAATN,EAAexG,GAAMG,GAC/B4G,EAAmB,MAATP,EAAevG,GAASC,GAClC8G,EAAUxE,EAAMmE,MAAMhG,UAAU8F,GAAOjE,EAAMmE,MAAMhG,UAAU6F,GAAQH,EAAcG,GAAQhE,EAAMmE,MAAMjG,OAAO+F,GAC9GQ,EAAYZ,EAAcG,GAAQhE,EAAMmE,MAAMhG,UAAU6F,GACxDU,EAAoBlC,GAAgBoB,GACpCe,EAAaD,EAA6B,MAATV,EAAeU,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9C7Z,EAAM2Y,EAAce,GACpB3Z,EAAMga,EAAaN,EAAUJ,GAAOV,EAAcgB,GAClDQ,EAASJ,EAAa,EAAIN,EAAUJ,GAAO,EAAIa,EAC/CE,EAAS7B,GAAOvY,EAAKma,EAAQpa,GAE7Bsa,EAAWjB,EACfhE,EAAM8D,cAAcvb,KAASob,EAAwB,IAA0BsB,GAAYD,EAAQrB,EAAsBuB,aAAeF,EAASD,EAAQpB,KA6CzJtD,OA1CF,SAAgBC,GACd,IAAIN,EAAQM,EAAMN,MAEdmF,EADU7E,EAAMG,QACW3b,QAC3B8e,OAAoC,IAArBuB,EAA8B,sBAAwBA,EAErD,MAAhBvB,IAKwB,iBAAjBA,IACTA,EAAe5D,EAAMC,SAAS/B,OAAO1Y,cAAcoe,MAahD5c,GAASgZ,EAAMC,SAAS/B,OAAQ0F,KAQrC5D,EAAMC,SAASW,MAAQgD,IAUvB7C,SAAU,CAAC,iBACXqE,iBAAkB,CAAC,oBCnGN,SAASC,GAAa9G,GACnC,OAAOA,EAAUnZ,MAAM,KAAK,GCQ9B,IAAIkgB,GAAa,CACf9H,IAAK,OACLE,MAAO,OACPD,OAAQ,OACRE,KAAM,QAgBD,SAAS4H,GAAYjF,GAC1B,IAAIkF,EAEAtH,EAASoC,EAAMpC,OACfuH,EAAanF,EAAMmF,WACnBlH,EAAY+B,EAAM/B,UAClBmH,EAAYpF,EAAMoF,UAClBC,EAAUrF,EAAMqF,QAChBnF,EAAWF,EAAME,SACjBoF,EAAkBtF,EAAMsF,gBACxBC,EAAWvF,EAAMuF,SACjBC,EAAexF,EAAMwF,aACrBC,EAAUzF,EAAMyF,QAChBC,EAAaL,EAAQlE,EACrBA,OAAmB,IAAfuE,EAAwB,EAAIA,EAChCC,EAAaN,EAAQjE,EACrBA,OAAmB,IAAfuE,EAAwB,EAAIA,EAEhCC,EAAgC,mBAAjBJ,EAA8BA,EAAa,CAC5DrE,EAAGA,EACHC,EAAGA,IACA,CACHD,EAAGA,EACHC,EAAGA,GAGLD,EAAIyE,EAAMzE,EACVC,EAAIwE,EAAMxE,EACV,IAAIyE,EAAOR,EAAQ9E,eAAe,KAC9BuF,EAAOT,EAAQ9E,eAAe,KAC9BwF,EAAQ1I,GACR2I,EAAQ9I,GACR+I,EAAM1e,OAEV,GAAIge,EAAU,CACZ,IAAItD,EAAeC,GAAgBtE,GAC/BsI,EAAa,eACbC,EAAY,cAEZlE,IAAiBlD,GAAUnB,IAGmB,WAA5C5X,GAFJic,EAAeJ,GAAmBjE,IAECsC,UAAsC,aAAbA,IAC1DgG,EAAa,eACbC,EAAY,gBAOZlI,IAAcf,KAAQe,IAAcZ,IAAQY,IAAcb,KAAUgI,IAAc3H,MACpFuI,EAAQ7I,GAGRiE,IAFcqE,GAAWxD,IAAiBgE,GAAOA,EAAIG,eAAiBH,EAAIG,eAAelF,OACzFe,EAAaiE,IACEf,EAAWjE,OAC1BE,GAAKkE,EAAkB,GAAK,GAG1BrH,IAAcZ,KAASY,IAAcf,IAAOe,IAAcd,IAAWiI,IAAc3H,MACrFsI,EAAQ3I,GAGR+D,IAFcsE,GAAWxD,IAAiBgE,GAAOA,EAAIG,eAAiBH,EAAIG,eAAenF,MACzFgB,EAAakE,IACEhB,EAAWlE,MAC1BE,GAAKmE,EAAkB,GAAK,GAIhC,IAgBMe,EAhBFC,EAAe/a,OAAOuU,OAAO,CAC/BI,SAAUA,GACTqF,GAAYP,IAEXuB,GAAyB,IAAjBf,EAnFd,SAA2B/F,GACzB,IAAI0B,EAAI1B,EAAK0B,EACTC,EAAI3B,EAAK2B,EAEToF,EADMjf,OACIkf,kBAAoB,EAClC,MAAO,CACLtF,EAAGR,GAAMQ,EAAIqF,GAAOA,GAAO,EAC3BpF,EAAGT,GAAMS,EAAIoF,GAAOA,GAAO,GA4EOE,CAAkB,CACpDvF,EAAGA,EACHC,EAAGA,IACA,CACHD,EAAGA,EACHC,EAAGA,GAML,OAHAD,EAAIoF,EAAMpF,EACVC,EAAImF,EAAMnF,EAENkE,EAGK/Z,OAAOuU,OAAO,GAAIwG,IAAeD,EAAiB,IAAmBL,GAASF,EAAO,IAAM,GAAIO,EAAeN,GAASF,EAAO,IAAM,GAAIQ,EAAe9D,WAAa0D,EAAIQ,kBAAoB,IAAM,EAAI,aAAetF,EAAI,OAASC,EAAI,MAAQ,eAAiBD,EAAI,OAASC,EAAI,SAAUiF,IAG5R9a,OAAOuU,OAAO,GAAIwG,IAAepB,EAAkB,IAAoBc,GAASF,EAAO1E,EAAI,KAAO,GAAI8D,EAAgBa,GAASF,EAAO1E,EAAI,KAAO,GAAI+D,EAAgB3C,UAAY,GAAI2C,IAwD9L,MAAAyB,GAAe,CACb1e,KAAM,gBACNsX,SAAS,EACTC,MAAO,cACPpX,GAzDF,SAAuBwe,GACrB,IAAIlH,EAAQkH,EAAMlH,MACdS,EAAUyG,EAAMzG,QAChB0G,EAAwB1G,EAAQmF,gBAChCA,OAA4C,IAA1BuB,GAA0CA,EAC5DC,EAAoB3G,EAAQoF,SAC5BA,OAAiC,IAAtBuB,GAAsCA,EACjDC,EAAwB5G,EAAQqF,aAChCA,OAAyC,IAA1BuB,GAA0CA,EAYzDT,EAAe,CACjBrI,UAAWyC,GAAiBhB,EAAMzB,WAClCmH,UAAWL,GAAarF,EAAMzB,WAC9BL,OAAQ8B,EAAMC,SAAS/B,OACvBuH,WAAYzF,EAAMmE,MAAMjG,OACxB0H,gBAAiBA,EACjBG,QAAoC,UAA3B/F,EAAMS,QAAQC,UAGgB,MAArCV,EAAM8D,cAAcD,gBACtB7D,EAAMG,OAAOjC,OAASrS,OAAOuU,OAAO,GAAIJ,EAAMG,OAAOjC,OAAQqH,GAAY1Z,OAAOuU,OAAO,GAAIwG,EAAc,CACvGjB,QAAS3F,EAAM8D,cAAcD,cAC7BrD,SAAUR,EAAMS,QAAQC,SACxBmF,SAAUA,EACVC,aAAcA,OAIe,MAA7B9F,EAAM8D,cAAclD,QACtBZ,EAAMG,OAAOS,MAAQ/U,OAAOuU,OAAO,GAAIJ,EAAMG,OAAOS,MAAO2E,GAAY1Z,OAAOuU,OAAO,GAAIwG,EAAc,CACrGjB,QAAS3F,EAAM8D,cAAclD,MAC7BJ,SAAU,WACVqF,UAAU,EACVC,aAAcA,OAIlB9F,EAAM1O,WAAW4M,OAASrS,OAAOuU,OAAO,GAAIJ,EAAM1O,WAAW4M,OAAQ,CACnE,wBAAyB8B,EAAMzB,aAUjC7J,KAAM,ICjLR,IAAI4S,GAAU,CACZA,SAAS,GAsCX,MAAAC,GAAe,CACbhf,KAAM,iBACNsX,SAAS,EACTC,MAAO,QACPpX,GAAI,aACJ2X,OAxCF,SAAgBN,GACd,IAAIC,EAAQD,EAAKC,MACbnQ,EAAWkQ,EAAKlQ,SAChB4Q,EAAUV,EAAKU,QACf+G,EAAkB/G,EAAQgH,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkBjH,EAAQkH,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7C7f,EAASwX,GAAUW,EAAMC,SAAS/B,QAClC0J,EAAgB,GAAG3S,OAAO+K,EAAM4H,cAAczJ,UAAW6B,EAAM4H,cAAc1J,QAYjF,OAVIuJ,GACFG,EAAc1H,SAAQ,SAAU2H,GAC9BA,EAAa9e,iBAAiB,SAAU8G,EAASiY,OAAQR,OAIzDK,GACF9f,EAAOkB,iBAAiB,SAAU8G,EAASiY,OAAQR,IAG9C,WACDG,GACFG,EAAc1H,SAAQ,SAAU2H,GAC9BA,EAAa7d,oBAAoB,SAAU6F,EAASiY,OAAQR,OAI5DK,GACF9f,EAAOmC,oBAAoB,SAAU6F,EAASiY,OAAQR,MAY1D5S,KAAM,IC/CR,IAAIqT,GAAO,CACTpK,KAAM,QACND,MAAO,OACPD,OAAQ,MACRD,IAAK,UAEQ,SAASwK,GAAqBzJ,GAC3C,OAAOA,EAAUrR,QAAQ,0BAA0B,SAAU+a,GAC3D,OAAOF,GAAKE,MCRhB,IAAIF,GAAO,CACTjK,MAAO,MACPC,IAAK,SAEQ,SAASmK,GAA8B3J,GACpD,OAAOA,EAAUrR,QAAQ,cAAc,SAAU+a,GAC/C,OAAOF,GAAKE,MCLD,SAASE,GAAgB7I,GACtC,IAAIiH,EAAMlH,GAAUC,GAGpB,MAAO,CACL8I,WAHe7B,EAAI8B,YAInBC,UAHc/B,EAAIgC,aCDP,SAASC,GAAoB1jB,GAQ1C,OAAOqY,GAAsBgF,GAAmBrd,IAAU6Y,KAAOwK,GAAgBrjB,GAASsjB,WCV7E,SAASK,GAAe3jB,GAErC,IAAI4jB,EAAoBpiB,GAAiBxB,GACrC6jB,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6B9V,KAAK4V,EAAWE,EAAYD,GCJnD,SAASE,GAAgBxJ,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAa7U,QAAQ0U,GAAYG,KAAU,EAEvDA,EAAKC,cAAcxX,KAGxB0X,GAAcH,IAASmJ,GAAenJ,GACjCA,EAGFwJ,GAAgB1G,GAAc9C,ICHxB,SAASyJ,GAAkBjkB,EAASqF,GACjD,IAAI6e,OAES,IAAT7e,IACFA,EAAO,IAGT,IAAI0d,EAAeiB,GAAgBhkB,GAC/BmkB,EAASpB,KAAqE,OAAlDmB,EAAwBlkB,EAAQya,oBAAyB,EAASyJ,EAAsBjhB,MACpHwe,EAAMlH,GAAUwI,GAChB9d,EAASkf,EAAS,CAAC1C,GAAKtR,OAAOsR,EAAIG,gBAAkB,GAAI+B,GAAeZ,GAAgBA,EAAe,IAAMA,EAC7GqB,EAAc/e,EAAK8K,OAAOlL,GAC9B,OAAOkf,EAASC,EAChBA,EAAYjU,OAAO8T,GAAkB3G,GAAcrY,KCxBtC,SAASof,GAAiBhI,GACvC,OAAOtV,OAAOuU,OAAO,GAAIe,EAAM,CAC7BxD,KAAMwD,EAAKM,EACXjE,IAAK2D,EAAKO,EACVhE,MAAOyD,EAAKM,EAAIN,EAAKI,MACrB9D,OAAQ0D,EAAKO,EAAIP,EAAKK,SCuB1B,SAAS4H,GAA2BtkB,EAASukB,GAC3C,OAAOA,IAAmBpL,GAAWkL,GC1BxB,SAAyBrkB,GACtC,IAAIyhB,EAAMlH,GAAUva,GAChBwkB,EAAOnH,GAAmBrd,GAC1B4hB,EAAiBH,EAAIG,eACrBnF,EAAQ+H,EAAKzE,YACbrD,EAAS8H,EAAK1E,aACdnD,EAAI,EACJC,EAAI,EAuBR,OAjBIgF,IACFnF,EAAQmF,EAAenF,MACvBC,EAASkF,EAAelF,OASnB,iCAAiCzO,KAAKyE,UAAUkL,aACnDjB,EAAIiF,EAAe7E,WACnBH,EAAIgF,EAAe5E,YAIhB,CACLP,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EAAI+G,GAAoB1jB,GAC3B4c,EAAGA,GDRiD6H,CAAgBzkB,IAAYe,GAAUwjB,GAd9F,SAAoCvkB,GAClC,IAAIqc,EAAOhE,GAAsBrY,GASjC,OARAqc,EAAK3D,IAAM2D,EAAK3D,IAAM1Y,EAAQ0kB,UAC9BrI,EAAKxD,KAAOwD,EAAKxD,KAAO7Y,EAAQ2kB,WAChCtI,EAAK1D,OAAS0D,EAAK3D,IAAM1Y,EAAQ8f,aACjCzD,EAAKzD,MAAQyD,EAAKxD,KAAO7Y,EAAQ+f,YACjC1D,EAAKI,MAAQzc,EAAQ+f,YACrB1D,EAAKK,OAAS1c,EAAQ8f,aACtBzD,EAAKM,EAAIN,EAAKxD,KACdwD,EAAKO,EAAIP,EAAK3D,IACP2D,EAIuGuI,CAA2BL,GAAkBF,GEtB9I,SAAyBrkB,GACtC,IAAIkkB,EAEAM,EAAOnH,GAAmBrd,GAC1B6kB,EAAYxB,GAAgBrjB,GAC5BiD,EAA0D,OAAlDihB,EAAwBlkB,EAAQya,oBAAyB,EAASyJ,EAAsBjhB,KAChGwZ,EAAQ5W,GAAI2e,EAAKM,YAAaN,EAAKzE,YAAa9c,EAAOA,EAAK6hB,YAAc,EAAG7hB,EAAOA,EAAK8c,YAAc,GACvGrD,EAAS7W,GAAI2e,EAAKO,aAAcP,EAAK1E,aAAc7c,EAAOA,EAAK8hB,aAAe,EAAG9hB,EAAOA,EAAK6c,aAAe,GAC5GnD,GAAKkI,EAAUvB,WAAaI,GAAoB1jB,GAChD4c,GAAKiI,EAAUrB,UAMnB,MAJiD,QAA7ChiB,GAAiByB,GAAQuhB,GAAMjS,YACjCoK,GAAK9W,GAAI2e,EAAKzE,YAAa9c,EAAOA,EAAK8c,YAAc,GAAKtD,GAGrD,CACLA,MAAOA,EACPC,OAAQA,EACRC,EAAGA,EACHC,EAAGA,GFGuKoI,CAAgB3H,GAAmBrd,KGzBlM,SAASilB,GAAehK,GACrC,IAOI4F,EAPAxH,EAAY4B,EAAK5B,UACjBrZ,EAAUib,EAAKjb,QACfyZ,EAAYwB,EAAKxB,UACjBwF,EAAgBxF,EAAYyC,GAAiBzC,GAAa,KAC1DmH,EAAYnH,EAAY8G,GAAa9G,GAAa,KAClDyL,EAAU7L,EAAUsD,EAAItD,EAAUoD,MAAQ,EAAIzc,EAAQyc,MAAQ,EAC9D0I,EAAU9L,EAAUuD,EAAIvD,EAAUqD,OAAS,EAAI1c,EAAQ0c,OAAS,EAGpE,OAAQuC,GACN,KAAKvG,GACHmI,EAAU,CACRlE,EAAGuI,EACHtI,EAAGvD,EAAUuD,EAAI5c,EAAQ0c,QAE3B,MAEF,KAAK/D,GACHkI,EAAU,CACRlE,EAAGuI,EACHtI,EAAGvD,EAAUuD,EAAIvD,EAAUqD,QAE7B,MAEF,KAAK9D,GACHiI,EAAU,CACRlE,EAAGtD,EAAUsD,EAAItD,EAAUoD,MAC3BG,EAAGuI,GAEL,MAEF,KAAKtM,GACHgI,EAAU,CACRlE,EAAGtD,EAAUsD,EAAI3c,EAAQyc,MACzBG,EAAGuI,GAEL,MAEF,QACEtE,EAAU,CACRlE,EAAGtD,EAAUsD,EACbC,EAAGvD,EAAUuD,GAInB,IAAIwI,EAAWnG,EAAgBb,GAAyBa,GAAiB,KAEzE,GAAgB,MAAZmG,EAAkB,CACpB,IAAIjG,EAAmB,MAAbiG,EAAmB,SAAW,QAExC,OAAQxE,GACN,KAAK5H,GACH6H,EAAQuE,GAAYvE,EAAQuE,IAAa/L,EAAU8F,GAAO,EAAInf,EAAQmf,GAAO,GAC7E,MAEF,KAAKlG,GACH4H,EAAQuE,GAAYvE,EAAQuE,IAAa/L,EAAU8F,GAAO,EAAInf,EAAQmf,GAAO,IAOnF,OAAO0B,EC1DM,SAASwE,GAAenK,EAAOS,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAI2J,EAAW3J,EACX4J,EAAqBD,EAAS7L,UAC9BA,OAAmC,IAAvB8L,EAAgCrK,EAAMzB,UAAY8L,EAC9DC,EAAoBF,EAASG,SAC7BA,OAAiC,IAAtBD,EAA+BtM,GAAkBsM,EAC5DE,EAAwBJ,EAASK,aACjCA,OAAyC,IAA1BD,EAAmCvM,GAAWuM,EAC7DE,EAAwBN,EAASO,eACjCA,OAA2C,IAA1BD,EAAmCxM,GAASwM,EAC7DE,EAAuBR,EAASS,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBV,EAASlG,QAC5BA,OAA+B,IAArB4G,EAA8B,EAAIA,EAC5CvH,EAAgBD,GAAsC,iBAAZY,EAAuBA,EAAUV,GAAgBU,EAASrG,KACpGkN,EAAaJ,IAAmBzM,GAASC,GAAYD,GACrDuH,EAAazF,EAAMmE,MAAMjG,OACzBpZ,EAAUkb,EAAMC,SAAS4K,EAAcE,EAAaJ,GACpDK,EJoBS,SAAyBlmB,EAASylB,EAAUE,GACzD,IAAIQ,EAAmC,oBAAbV,EAlB5B,SAA4BzlB,GAC1B,IAAIkZ,EAAkB+K,GAAkB3G,GAActd,IAElDomB,EADoB,CAAC,WAAY,SAASzgB,QAAQnE,GAAiBxB,GAAS0b,WAAa,GACnDf,GAAc3a,GAAW0d,GAAgB1d,GAAWA,EAE9F,OAAKe,GAAUqlB,GAKRlN,EAAgBvM,QAAO,SAAU4X,GACtC,OAAOxjB,GAAUwjB,IAAmBriB,GAASqiB,EAAgB6B,IAAmD,SAAhC/L,GAAYkK,MALrF,GAYkD8B,CAAmBrmB,GAAW,GAAGmQ,OAAOsV,GAC/FvM,EAAkB,GAAG/I,OAAOgW,EAAqB,CAACR,IAClDW,EAAsBpN,EAAgB,GACtCqN,EAAerN,EAAgBK,QAAO,SAAUiN,EAASjC,GAC3D,IAAIlI,EAAOiI,GAA2BtkB,EAASukB,GAK/C,OAJAiC,EAAQ9N,IAAM7S,GAAIwW,EAAK3D,IAAK8N,EAAQ9N,KACpC8N,EAAQ5N,MAAQ9S,GAAIuW,EAAKzD,MAAO4N,EAAQ5N,OACxC4N,EAAQ7N,OAAS7S,GAAIuW,EAAK1D,OAAQ6N,EAAQ7N,QAC1C6N,EAAQ3N,KAAOhT,GAAIwW,EAAKxD,KAAM2N,EAAQ3N,MAC/B2N,IACNlC,GAA2BtkB,EAASsmB,IAKvC,OAJAC,EAAa9J,MAAQ8J,EAAa3N,MAAQ2N,EAAa1N,KACvD0N,EAAa7J,OAAS6J,EAAa5N,OAAS4N,EAAa7N,IACzD6N,EAAa5J,EAAI4J,EAAa1N,KAC9B0N,EAAa3J,EAAI2J,EAAa7N,IACvB6N,EIpCkBE,CAAgB1lB,GAAUf,GAAWA,EAAUA,EAAQ0mB,gBAAkBrJ,GAAmBnC,EAAMC,SAAS/B,QAASqM,EAAUE,GACnJgB,EAAsBtO,GAAsB6C,EAAMC,SAAS9B,WAC3D0F,EAAgBkG,GAAe,CACjC5L,UAAWsN,EACX3mB,QAAS2gB,EACT/E,SAAU,WACVnC,UAAWA,IAETmN,EAAmBvC,GAAiBtd,OAAOuU,OAAO,GAAIqF,EAAY5B,IAClE8H,EAAoBhB,IAAmBzM,GAASwN,EAAmBD,EAGnEG,EAAkB,CACpBpO,IAAKwN,EAAmBxN,IAAMmO,EAAkBnO,IAAM+F,EAAc/F,IACpEC,OAAQkO,EAAkBlO,OAASuN,EAAmBvN,OAAS8F,EAAc9F,OAC7EE,KAAMqN,EAAmBrN,KAAOgO,EAAkBhO,KAAO4F,EAAc5F,KACvED,MAAOiO,EAAkBjO,MAAQsN,EAAmBtN,MAAQ6F,EAAc7F,OAExEmO,EAAa7L,EAAM8D,cAAckB,OAErC,GAAI2F,IAAmBzM,IAAU2N,EAAY,CAC3C,IAAI7G,EAAS6G,EAAWtN,GACxB1S,OAAOqC,KAAK0d,GAAiB1L,SAAQ,SAAU7Q,GAC7C,IAAIyc,EAAW,CAACpO,GAAOD,IAAQhT,QAAQ4E,IAAQ,EAAI,GAAK,EACpD2U,EAAO,CAACxG,GAAKC,IAAQhT,QAAQ4E,IAAQ,EAAI,IAAM,IACnDuc,EAAgBvc,IAAQ2V,EAAOhB,GAAQ8H,KAI3C,OAAOF,ECzDM,SAASG,GAAqB/L,EAAOS,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAI2J,EAAW3J,EACXlC,EAAY6L,EAAS7L,UACrBgM,EAAWH,EAASG,SACpBE,EAAeL,EAASK,aACxBvG,EAAUkG,EAASlG,QACnB8H,EAAiB5B,EAAS4B,eAC1BC,EAAwB7B,EAAS8B,sBACjCA,OAAkD,IAA1BD,EAAmCE,GAAgBF,EAC3EvG,EAAYL,GAAa9G,GACzBC,EAAakH,EAAYsG,EAAiB5N,GAAsBA,GAAoB3M,QAAO,SAAU8M,GACvG,OAAO8G,GAAa9G,KAAemH,KAChC7H,GACDuO,EAAoB5N,EAAW/M,QAAO,SAAU8M,GAClD,OAAO2N,EAAsBzhB,QAAQ8T,IAAc,KAGpB,IAA7B6N,EAAkBlmB,SACpBkmB,EAAoB5N,GAQtB,IAAI6N,EAAYD,EAAkB/N,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAa4L,GAAenK,EAAO,CACrCzB,UAAWA,EACXgM,SAAUA,EACVE,aAAcA,EACdvG,QAASA,IACRlD,GAAiBzC,IACbD,IACN,IACH,OAAOzS,OAAOqC,KAAKme,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,MC6FpC,MAAAC,GAAe,CACblkB,KAAM,OACNsX,SAAS,EACTC,MAAO,OACPpX,GA5HF,SAAcqX,GACZ,IAAIC,EAAQD,EAAKC,MACbS,EAAUV,EAAKU,QACflY,EAAOwX,EAAKxX,KAEhB,IAAIyX,EAAM8D,cAAcvb,GAAMmkB,MAA9B,CAoCA,IAhCA,IAAIC,EAAoBlM,EAAQyJ,SAC5B0C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBpM,EAAQqM,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8BvM,EAAQwM,mBACtC/I,EAAUzD,EAAQyD,QAClBqG,EAAW9J,EAAQ8J,SACnBE,EAAehK,EAAQgK,aACvBI,EAAcpK,EAAQoK,YACtBqC,EAAwBzM,EAAQuL,eAChCA,OAA2C,IAA1BkB,GAA0CA,EAC3DhB,EAAwBzL,EAAQyL,sBAChCiB,EAAqBnN,EAAMS,QAAQlC,UACnCwF,EAAgB/C,GAAiBmM,GAEjCF,EAAqBD,IADHjJ,IAAkBoJ,GACqCnB,EAjC/E,SAAuCzN,GACrC,GAAIyC,GAAiBzC,KAAeX,GAClC,MAAO,GAGT,IAAIwP,EAAoBpF,GAAqBzJ,GAC7C,MAAO,CAAC2J,GAA8B3J,GAAY6O,EAAmBlF,GAA8BkF,IA2BwCC,CAA8BF,GAA3E,CAACnF,GAAqBmF,KAChH3O,EAAa,CAAC2O,GAAoBlY,OAAOgY,GAAoB5O,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAIrJ,OAAO+L,GAAiBzC,KAAeX,GAAOmO,GAAqB/L,EAAO,CACnFzB,UAAWA,EACXgM,SAAUA,EACVE,aAAcA,EACdvG,QAASA,EACT8H,eAAgBA,EAChBE,sBAAuBA,IACpB3N,KACJ,IACC+O,EAAgBtN,EAAMmE,MAAMhG,UAC5BsH,EAAazF,EAAMmE,MAAMjG,OACzBqP,EAAY,IAAI7d,IAChB8d,GAAqB,EACrBC,EAAwBjP,EAAW,GAE9BkP,EAAI,EAAGA,EAAIlP,EAAWtY,OAAQwnB,IAAK,CAC1C,IAAInP,EAAYC,EAAWkP,GAEvBC,EAAiB3M,GAAiBzC,GAElCqP,EAAmBvI,GAAa9G,KAAeT,GAC/C+P,EAAa,CAACrQ,GAAKC,IAAQhT,QAAQkjB,IAAmB,EACtD1J,EAAM4J,EAAa,QAAU,SAC7BlF,EAAWwB,GAAenK,EAAO,CACnCzB,UAAWA,EACXgM,SAAUA,EACVE,aAAcA,EACdI,YAAaA,EACb3G,QAASA,IAEP4J,EAAoBD,EAAaD,EAAmBlQ,GAAQC,GAAOiQ,EAAmBnQ,GAASD,GAE/F8P,EAAcrJ,GAAOwB,EAAWxB,KAClC6J,EAAoB9F,GAAqB8F,IAG3C,IAAIC,EAAmB/F,GAAqB8F,GACxCE,EAAS,GAUb,GARIpB,GACFoB,EAAOhlB,KAAK2f,EAASgF,IAAmB,GAGtCZ,GACFiB,EAAOhlB,KAAK2f,EAASmF,IAAsB,EAAGnF,EAASoF,IAAqB,GAG1EC,EAAOC,OAAM,SAAUC,GACzB,OAAOA,KACL,CACFT,EAAwBlP,EACxBiP,GAAqB,EACrB,MAGFD,EAAU3d,IAAI2O,EAAWyP,GAG3B,GAAIR,EAqBF,IAnBA,IAEIW,EAAQ,SAAeC,GACzB,IAAIC,EAAmB7P,EAAWzS,MAAK,SAAUwS,GAC/C,IAAIyP,EAAST,EAAUhe,IAAIgP,GAE3B,GAAIyP,EACF,OAAOA,EAAOxf,MAAM,EAAG4f,GAAIH,OAAM,SAAUC,GACzC,OAAOA,QAKb,GAAIG,EAEF,OADAZ,EAAwBY,EACjB,SAIFD,EAnBYpC,EAAiB,EAAI,EAmBZoC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpCpO,EAAMzB,YAAckP,IACtBzN,EAAM8D,cAAcvb,GAAMmkB,OAAQ,EAClC1M,EAAMzB,UAAYkP,EAClBzN,EAAMsO,OAAQ,KAUhBlJ,iBAAkB,CAAC,UACnB1Q,KAAM,CACJgY,OAAO,IC7IX,SAAS6B,GAAe5F,EAAUxH,EAAMqN,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjB/M,EAAG,EACHC,EAAG,IAIA,CACLlE,IAAKmL,EAASnL,IAAM2D,EAAKK,OAASgN,EAAiB9M,EACnDhE,MAAOiL,EAASjL,MAAQyD,EAAKI,MAAQiN,EAAiB/M,EACtDhE,OAAQkL,EAASlL,OAAS0D,EAAKK,OAASgN,EAAiB9M,EACzD/D,KAAMgL,EAAShL,KAAOwD,EAAKI,MAAQiN,EAAiB/M,GAIxD,SAASgN,GAAsB9F,GAC7B,MAAO,CAACnL,GAAKE,GAAOD,GAAQE,IAAM+Q,MAAK,SAAUC,GAC/C,OAAOhG,EAASgG,IAAS,KAiC7B,MAAAC,GAAe,CACbrmB,KAAM,OACNsX,SAAS,EACTC,MAAO,OACPsF,iBAAkB,CAAC,mBACnB1c,GAlCF,SAAcqX,GACZ,IAAIC,EAAQD,EAAKC,MACbzX,EAAOwX,EAAKxX,KACZ+kB,EAAgBtN,EAAMmE,MAAMhG,UAC5BsH,EAAazF,EAAMmE,MAAMjG,OACzBsQ,EAAmBxO,EAAM8D,cAAc+K,gBACvCC,EAAoB3E,GAAenK,EAAO,CAC5C2K,eAAgB,cAEdoE,EAAoB5E,GAAenK,EAAO,CAC5C6K,aAAa,IAEXmE,EAA2BT,GAAeO,EAAmBxB,GAC7D2B,EAAsBV,GAAeQ,EAAmBtJ,EAAY+I,GACpEU,EAAoBT,GAAsBO,GAC1CG,EAAmBV,GAAsBQ,GAC7CjP,EAAM8D,cAAcvb,GAAQ,CAC1BymB,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpBnP,EAAM1O,WAAW4M,OAASrS,OAAOuU,OAAO,GAAIJ,EAAM1O,WAAW4M,OAAQ,CACnE,+BAAgCgR,EAChC,sBAAuBC,MCF3BC,GAAe,CACb7mB,KAAM,SACNsX,SAAS,EACTC,MAAO,OACPiB,SAAU,CAAC,iBACXrY,GA5BF,SAAgB4X,GACd,IAAIN,EAAQM,EAAMN,MACdS,EAAUH,EAAMG,QAChBlY,EAAO+X,EAAM/X,KACb8mB,EAAkB5O,EAAQuE,OAC1BA,OAA6B,IAApBqK,EAA6B,CAAC,EAAG,GAAKA,EAC/C3a,EAAO8J,GAAWH,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GA5BD,SAAiCA,EAAW4F,EAAOa,GACxD,IAAIjB,EAAgB/C,GAAiBzC,GACjC+Q,EAAiB,CAAC3R,GAAMH,IAAK/S,QAAQsZ,IAAkB,GAAK,EAAI,EAEhEhE,EAAyB,mBAAXiF,EAAwBA,EAAOnZ,OAAOuU,OAAO,GAAI+D,EAAO,CACxE5F,UAAWA,KACPyG,EACFuK,EAAWxP,EAAK,GAChByP,EAAWzP,EAAK,GAIpB,OAFAwP,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAAC3R,GAAMD,IAAOjT,QAAQsZ,IAAkB,EAAI,CACjDtC,EAAG+N,EACH9N,EAAG6N,GACD,CACF9N,EAAG8N,EACH7N,EAAG8N,GAWcC,CAAwBlR,EAAWyB,EAAMmE,MAAOa,GAC1D1G,IACN,IACCoR,EAAwBhb,EAAKsL,EAAMzB,WACnCkD,EAAIiO,EAAsBjO,EAC1BC,EAAIgO,EAAsBhO,EAEW,MAArC1B,EAAM8D,cAAcD,gBACtB7D,EAAM8D,cAAcD,cAAcpC,GAAKA,EACvCzB,EAAM8D,cAAcD,cAAcnC,GAAKA,GAGzC1B,EAAM8D,cAAcvb,GAAQmM,ICzB9Bib,GAAe,CACbpnB,KAAM,gBACNsX,SAAS,EACTC,MAAO,OACPpX,GApBF,SAAuBqX,GACrB,IAAIC,EAAQD,EAAKC,MACbzX,EAAOwX,EAAKxX,KAKhByX,EAAM8D,cAAcvb,GAAQwhB,GAAe,CACzC5L,UAAW6B,EAAMmE,MAAMhG,UACvBrZ,QAASkb,EAAMmE,MAAMjG,OACrBwC,SAAU,WACVnC,UAAWyB,EAAMzB,aAUnB7J,KAAM,ICgHRkb,GAAe,CACbrnB,KAAM,kBACNsX,SAAS,EACTC,MAAO,OACPpX,GA/HF,SAAyBqX,GACvB,IAAIC,EAAQD,EAAKC,MACbS,EAAUV,EAAKU,QACflY,EAAOwX,EAAKxX,KACZokB,EAAoBlM,EAAQyJ,SAC5B0C,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmBpM,EAAQqM,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrDtC,EAAW9J,EAAQ8J,SACnBE,EAAehK,EAAQgK,aACvBI,EAAcpK,EAAQoK,YACtB3G,EAAUzD,EAAQyD,QAClB2L,EAAkBpP,EAAQqP,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBtP,EAAQuP,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtDpH,EAAWwB,GAAenK,EAAO,CACnCuK,SAAUA,EACVE,aAAcA,EACdvG,QAASA,EACT2G,YAAaA,IAEX9G,EAAgB/C,GAAiBhB,EAAMzB,WACvCmH,EAAYL,GAAarF,EAAMzB,WAC/B0R,GAAmBvK,EACnBwE,EAAWhH,GAAyBa,GACpC+I,ECrCY,MDqCS5C,ECrCH,IAAM,IDsCxBrG,EAAgB7D,EAAM8D,cAAcD,cACpCyJ,EAAgBtN,EAAMmE,MAAMhG,UAC5BsH,EAAazF,EAAMmE,MAAMjG,OACzBgS,EAA4C,mBAAjBF,EAA8BA,EAAankB,OAAOuU,OAAO,GAAIJ,EAAMmE,MAAO,CACvG5F,UAAWyB,EAAMzB,aACbyR,EACFG,EAA2D,iBAAtBD,EAAiC,CACxEhG,SAAUgG,EACVpD,QAASoD,GACPrkB,OAAOuU,OAAO,CAChB8J,SAAU,EACV4C,QAAS,GACRoD,GACCE,EAAsBpQ,EAAM8D,cAAckB,OAAShF,EAAM8D,cAAckB,OAAOhF,EAAMzB,WAAa,KACjG7J,EAAO,CACT+M,EAAG,EACHC,EAAG,GAGL,GAAKmC,EAAL,CAIA,GAAI+I,EAAe,CACjB,IAAIyD,EAEAC,EAAwB,MAAbpG,EAAmB1M,GAAMG,GACpC4S,EAAuB,MAAbrG,EAAmBzM,GAASC,GACtCuG,EAAmB,MAAbiG,EAAmB,SAAW,QACpClF,EAASnB,EAAcqG,GACvBtf,EAAMoa,EAAS2D,EAAS2H,GACxB3lB,EAAMqa,EAAS2D,EAAS4H,GACxBC,EAAWV,GAAUrK,EAAWxB,GAAO,EAAI,EAC3CwM,EAAS/K,IAAc5H,GAAQwP,EAAcrJ,GAAOwB,EAAWxB,GAC/DyM,EAAShL,IAAc5H,IAAS2H,EAAWxB,IAAQqJ,EAAcrJ,GAGjEL,EAAe5D,EAAMC,SAASW,MAC9ByD,EAAYyL,GAAUlM,EAAejC,GAAciC,GAAgB,CACrErC,MAAO,EACPC,OAAQ,GAENmP,EAAqB3Q,EAAM8D,cAAc,oBAAsB9D,EAAM8D,cAAc,oBAAoBI,QxBhFtG,CACL1G,IAAK,EACLE,MAAO,EACPD,OAAQ,EACRE,KAAM,GwB6EFiT,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAW3N,GAAO,EAAGmK,EAAcrJ,GAAMI,EAAUJ,IACnD8M,EAAYd,EAAkB3C,EAAcrJ,GAAO,EAAIuM,EAAWM,EAAWF,EAAkBT,EAA4BjG,SAAWuG,EAASK,EAAWF,EAAkBT,EAA4BjG,SACxM8G,EAAYf,GAAmB3C,EAAcrJ,GAAO,EAAIuM,EAAWM,EAAWD,EAAkBV,EAA4BjG,SAAWwG,EAASI,EAAWD,EAAkBV,EAA4BjG,SACzMxF,EAAoB1E,EAAMC,SAASW,OAAS4B,GAAgBxC,EAAMC,SAASW,OAC3EqQ,EAAevM,EAAiC,MAAbwF,EAAmBxF,EAAkB8E,WAAa,EAAI9E,EAAkB+E,YAAc,EAAI,EAC7HyH,EAAwH,OAAjGb,EAA+C,MAAvBD,OAA8B,EAASA,EAAoBlG,IAAqBmG,EAAwB,EAEvJc,EAAYnM,EAASgM,EAAYE,EACjCE,EAAkBjO,GAAO2M,EAASzM,GAAQzY,EAF9Boa,EAAS+L,EAAYG,EAAsBD,GAEKrmB,EAAKoa,EAAQ8K,EAAS1M,GAAQzY,EAAKwmB,GAAaxmB,GAChHkZ,EAAcqG,GAAYkH,EAC1B1c,EAAKwV,GAAYkH,EAAkBpM,EAGrC,GAAI+H,EAAc,CAChB,IAAIsE,EAEAC,EAAyB,MAAbpH,EAAmB1M,GAAMG,GAErC4T,GAAwB,MAAbrH,EAAmBzM,GAASC,GAEvC8T,GAAU3N,EAAciJ,GAExB2E,GAAmB,MAAZ3E,EAAkB,SAAW,QAEpC4E,GAAOF,GAAU7I,EAAS2I,GAE1BK,GAAOH,GAAU7I,EAAS4I,IAE1BK,IAAuD,IAAxC,CAACpU,GAAKG,IAAMlT,QAAQsZ,GAEnC8N,GAAyH,OAAjGR,EAAgD,MAAvBjB,OAA8B,EAASA,EAAoBtD,IAAoBuE,EAAyB,EAEzJS,GAAaF,GAAeF,GAAOF,GAAUlE,EAAcmE,IAAQhM,EAAWgM,IAAQI,GAAuB1B,EAA4BrD,QAEzIiF,GAAaH,GAAeJ,GAAUlE,EAAcmE,IAAQhM,EAAWgM,IAAQI,GAAuB1B,EAA4BrD,QAAU6E,GAE5IK,GAAmBlC,GAAU8B,G1BzH9B,SAAwBhnB,EAAK2F,EAAO5F,GACzC,IAAIsnB,EAAI9O,GAAOvY,EAAK2F,EAAO5F,GAC3B,OAAOsnB,EAAItnB,EAAMA,EAAMsnB,E0BuH2BC,CAAeJ,GAAYN,GAASO,IAAc5O,GAAO2M,EAASgC,GAAaJ,GAAMF,GAAS1B,EAASiC,GAAaJ,IAEpK9N,EAAciJ,GAAWkF,GACzBtd,EAAKoY,GAAWkF,GAAmBR,GAGrCxR,EAAM8D,cAAcvb,GAAQmM,IAS5B0Q,iBAAkB,CAAC,WE1HN,SAAS+M,GAAiBC,EAAyB7P,EAAcwD,QAC9D,IAAZA,IACFA,GAAU,GAGZ,ICnBoCzG,ECJOxa,EFuBvCutB,EAA0B5S,GAAc8C,GACxC+P,EAAuB7S,GAAc8C,IAf3C,SAAyBzd,GACvB,IAAIqc,EAAOrc,EAAQqY,wBACfiE,EAASH,GAAME,EAAKI,OAASzc,EAAQwc,aAAe,EACpDD,EAASJ,GAAME,EAAKK,QAAU1c,EAAQ6C,cAAgB,EAC1D,OAAkB,IAAXyZ,GAA2B,IAAXC,EAWmCkR,CAAgBhQ,GACtEnb,EAAkB+a,GAAmBI,GACrCpB,EAAOhE,GAAsBiV,EAAyBE,GACtD7K,EAAS,CACXW,WAAY,EACZE,UAAW,GAET3C,EAAU,CACZlE,EAAG,EACHC,EAAG,GAkBL,OAfI2Q,IAA4BA,IAA4BtM,MACxB,SAA9B5G,GAAYoD,IAChBkG,GAAerhB,MACbqgB,GCnCgCnI,EDmCTiD,KClCdlD,GAAUC,IAAUG,GAAcH,GCJxC,CACL8I,YAFyCtjB,EDQbwa,GCNR8I,WACpBE,UAAWxjB,EAAQwjB,WDGZH,GAAgB7I,IDoCnBG,GAAc8C,KAChBoD,EAAUxI,GAAsBoF,GAAc,IACtCd,GAAKc,EAAakH,WAC1B9D,EAAQjE,GAAKa,EAAaiH,WACjBpiB,IACTue,EAAQlE,EAAI+G,GAAoBphB,KAI7B,CACLqa,EAAGN,EAAKxD,KAAO8J,EAAOW,WAAazC,EAAQlE,EAC3CC,EAAGP,EAAK3D,IAAMiK,EAAOa,UAAY3C,EAAQjE,EACzCH,MAAOJ,EAAKI,MACZC,OAAQL,EAAKK,QGrDjB,SAASxH,GAAMwY,GACb,IAAIxc,EAAM,IAAItG,IACV+iB,EAAU,IAAInnB,IACdonB,EAAS,GAKb,SAASpG,EAAKqG,GACZF,EAAQnb,IAAIqb,EAASpqB,MACN,GAAG0M,OAAO0d,EAAS5R,UAAY,GAAI4R,EAASvN,kBAAoB,IACtElF,SAAQ,SAAU0S,GACzB,IAAKH,EAAQjmB,IAAIomB,GAAM,CACrB,IAAIC,EAAc7c,EAAIzG,IAAIqjB,GAEtBC,GACFvG,EAAKuG,OAIXH,EAAO1pB,KAAK2pB,GASd,OAzBAH,EAAUtS,SAAQ,SAAUyS,GAC1B3c,EAAIpG,IAAI+iB,EAASpqB,KAAMoqB,MAkBzBH,EAAUtS,SAAQ,SAAUyS,GACrBF,EAAQjmB,IAAImmB,EAASpqB,OAExB+jB,EAAKqG,MAGFD,ECfT,IAAII,GAAkB,CACpBvU,UAAW,SACXiU,UAAW,GACX9R,SAAU,YAGZ,SAASqS,KACP,IAAK,IAAItB,EAAOuB,UAAU9sB,OAAQyI,EAAO,IAAIuB,MAAMuhB,GAAOwB,EAAO,EAAGA,EAAOxB,EAAMwB,IAC/EtkB,EAAKskB,GAAQD,UAAUC,GAGzB,OAAQtkB,EAAK+f,MAAK,SAAU5pB,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQqY,0BAIhC,SAAS+V,GAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCT,GAAkBS,EAC3E,OAAO,SAAsBpV,EAAWD,EAAQuC,QAC9B,IAAZA,IACFA,EAAU+S,GAGZ,IC/C6B9qB,EAC3B+qB,ED8CEzT,EAAQ,CACVzB,UAAW,SACXmV,iBAAkB,GAClBjT,QAAS5U,OAAOuU,OAAO,GAAI0S,GAAiBU,GAC5C1P,cAAe,GACf7D,SAAU,CACR9B,UAAWA,EACXD,OAAQA,GAEV5M,WAAY,GACZ6O,OAAQ,IAENwT,EAAmB,GACnBC,GAAc,EACd/jB,EAAW,CACbmQ,MAAOA,EACP6T,WAAY,SAAoBC,GAC9B,IAAIrT,EAAsC,mBAArBqT,EAAkCA,EAAiB9T,EAAMS,SAAWqT,EACzFC,IACA/T,EAAMS,QAAU5U,OAAOuU,OAAO,GAAIoT,EAAgBxT,EAAMS,QAASA,GACjET,EAAM4H,cAAgB,CACpBzJ,UAAWtY,GAAUsY,GAAa4K,GAAkB5K,GAAaA,EAAUqN,eAAiBzC,GAAkB5K,EAAUqN,gBAAkB,GAC1ItN,OAAQ6K,GAAkB7K,IAI5B,IEzE4BsU,EAC9BwB,EFwEMN,EDvCG,SAAwBlB,GAErC,IAAIkB,EAAmB1Z,GAAMwY,GAE7B,OAAOtT,GAAeb,QAAO,SAAUC,EAAKwB,GAC1C,OAAOxB,EAAIrJ,OAAOye,EAAiBjiB,QAAO,SAAUkhB,GAClD,OAAOA,EAAS7S,QAAUA,QAE3B,IC+B0BmU,EEzEKzB,EFyEsB,GAAGvd,OAAOqe,EAAkBtT,EAAMS,QAAQ+R,WExE9FwB,EAASxB,EAAUnU,QAAO,SAAU2V,EAAQE,GAC9C,IAAIC,EAAWH,EAAOE,EAAQ3rB,MAK9B,OAJAyrB,EAAOE,EAAQ3rB,MAAQ4rB,EAAWtoB,OAAOuU,OAAO,GAAI+T,EAAUD,EAAS,CACrEzT,QAAS5U,OAAOuU,OAAO,GAAI+T,EAAS1T,QAASyT,EAAQzT,SACrD/L,KAAM7I,OAAOuU,OAAO,GAAI+T,EAASzf,KAAMwf,EAAQxf,QAC5Cwf,EACEF,IACN,IAEInoB,OAAOqC,KAAK8lB,GAAQhe,KAAI,SAAU3G,GACvC,OAAO2kB,EAAO3kB,QFuGV,OAvCA2Q,EAAM0T,iBAAmBA,EAAiBjiB,QAAO,SAAU2iB,GACzD,OAAOA,EAAEvU,WAqJbG,EAAM0T,iBAAiBxT,SAAQ,SAAUgG,GACvC,IAAI3d,EAAO2d,EAAM3d,KACb8rB,EAAgBnO,EAAMzF,QACtBA,OAA4B,IAAlB4T,EAA2B,GAAKA,EAC1ChU,EAAS6F,EAAM7F,OAEnB,GAAsB,mBAAXA,EAAuB,CAChC,IAAIiU,EAAYjU,EAAO,CACrBL,MAAOA,EACPzX,KAAMA,EACNsH,SAAUA,EACV4Q,QAASA,IAKXkT,EAAiB3qB,KAAKsrB,GAFT,kBA7HRzkB,EAASiY,UAOlByM,YAAa,WACX,IAAIX,EAAJ,CAIA,IAAIY,EAAkBxU,EAAMC,SACxB9B,EAAYqW,EAAgBrW,UAC5BD,EAASsW,EAAgBtW,OAG7B,GAAK6U,GAAiB5U,EAAWD,GAAjC,CASA8B,EAAMmE,MAAQ,CACZhG,UAAWgU,GAAiBhU,EAAWqE,GAAgBtE,GAAoC,UAA3B8B,EAAMS,QAAQC,UAC9ExC,OAAQyD,GAAczD,IAOxB8B,EAAMsO,OAAQ,EACdtO,EAAMzB,UAAYyB,EAAMS,QAAQlC,UAKhCyB,EAAM0T,iBAAiBxT,SAAQ,SAAUyS,GACvC,OAAO3S,EAAM8D,cAAc6O,EAASpqB,MAAQsD,OAAOuU,OAAO,GAAIuS,EAASje,SAIzE,IAAK,IAAIlK,EAAQ,EAAGA,EAAQwV,EAAM0T,iBAAiBxtB,OAAQsE,IAUzD,IAAoB,IAAhBwV,EAAMsO,MAAV,CAMA,IAAImG,EAAwBzU,EAAM0T,iBAAiBlpB,GAC/C9B,EAAK+rB,EAAsB/rB,GAC3BgsB,EAAyBD,EAAsBhU,QAC/C2J,OAAsC,IAA3BsK,EAAoC,GAAKA,EACpDnsB,EAAOksB,EAAsBlsB,KAEf,mBAAPG,IACTsX,EAAQtX,EAAG,CACTsX,MAAOA,EACPS,QAAS2J,EACT7hB,KAAMA,EACNsH,SAAUA,KACNmQ,QAjBNA,EAAMsO,OAAQ,EACd9jB,GAAS,KAsBfsd,QClM2Bpf,EDkMV,WACf,OAAO,IAAIisB,SAAQ,SAAUC,GAC3B/kB,EAAS0kB,cACTK,EAAQ5U,OCnMT,WAUL,OATKyT,IACHA,EAAU,IAAIkB,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBpB,OAAU9e,EACVigB,EAAQlsB,YAKP+qB,ID4LLqB,QAAS,WACPf,IACAH,GAAc,IAIlB,IAAKb,GAAiB5U,EAAWD,GAK/B,OAAOrO,EAmCT,SAASkkB,IACPJ,EAAiBzT,SAAQ,SAAUxX,GACjC,OAAOA,OAETirB,EAAmB,GAGrB,OAvCA9jB,EAASgkB,WAAWpT,GAASoU,MAAK,SAAU7U,IACrC4T,GAAenT,EAAQsU,eAC1BtU,EAAQsU,cAAc/U,MAqCnBnQ,GAGJ,IAAImlB,GAA4B9B,KG1PnC8B,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAAC/L,GAAgB1D,GAAeoR,GAAeC,MCMlEF,GAA4B9B,GAAgB,CAC9CI,iBAFqB,CAAC/L,GAAgB1D,GAAeoR,GAAeC,GAAalQ,GAAQmQ,GAAMtG,GAAiBjO,GAAOlE,qmBCiBnHlU,GAAO,WAOP4sB,GAAe,UACfC,GAAiB,YAOjBC,GAAwB,6BACxBC,GAA0B,+BAG1B3Z,GAAkB,OAOlBhH,GAAuB,4DACvB4gB,GAA8B,GAAE5gB,UAChC6gB,GAAgB,iBAKhBC,GAAgBztB,IAAU,UAAY,YACtC0tB,GAAmB1tB,IAAU,YAAc,UAC3C2tB,GAAmB3tB,IAAU,aAAe,eAC5C4tB,GAAsB5tB,IAAU,eAAiB,aACjD6tB,GAAkB7tB,IAAU,aAAe,cAC3C8tB,GAAiB9tB,IAAU,cAAgB,aAI3C6J,GAAU,CACdkT,OAAQ,CAAC,EAAG,GACZuF,SAAU,kBACVpM,UAAW,SACX6X,QAAS,UACTC,aAAc,KACdC,WAAW,GAGPnkB,GAAc,CAClBiT,OAAQ,0BACRuF,SAAU,mBACVpM,UAAW,0BACX6X,QAAS,SACTC,aAAc,yBACdC,UAAW,oBAOb,MAAMC,WAAiBjjB,EACrBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAKqpB,QAAU,KACfrpB,KAAKspB,QAAUtpB,KAAKqG,SAASzM,WAC7BoG,KAAKupB,MAAQthB,EAAeG,QAAQsgB,GAAe1oB,KAAKspB,SACxDtpB,KAAKwpB,UAAYxpB,KAAKypB,gBAIb1kB,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,OAAOA,GAITsM,SACE,OAAO/H,KAAK0P,WAAa1P,KAAK2P,OAAS3P,KAAK4P,OAG9CA,OACE,GAAI/V,EAAWmG,KAAKqG,WAAarG,KAAK0P,WACpC,OAGF,MAAM7P,EAAgB,CACpBA,cAAeG,KAAKqG,UAKtB,IAFkB9F,EAAaoB,QAAQ3B,KAAKqG,SAxF5B,mBAwFkDxG,GAEpDmC,iBAAd,CAUA,GANAhC,KAAK0pB,gBAMD,iBAAkBlxB,SAAS6B,kBAAoB2F,KAAKspB,QAAQ5vB,QAnFxC,eAoFtB,IAAK,MAAM3B,IAAW,GAAGmQ,UAAU1P,SAASwC,KAAKqN,UAC/C9H,EAAaa,GAAGrJ,EAAS,YAAa2C,GAI1CsF,KAAKqG,SAASsjB,QACd3pB,KAAKqG,SAASlC,aAAa,iBAAiB,GAE5CnE,KAAKupB,MAAMvvB,UAAUuQ,IAAIsE,IACzB7O,KAAKqG,SAASrM,UAAUuQ,IAAIsE,IAC5BtO,EAAaoB,QAAQ3B,KAAKqG,SA9GT,oBA8GgCxG,IAGnD8P,OACE,GAAI9V,EAAWmG,KAAKqG,YAAcrG,KAAK0P,WACrC,OAGF,MAAM7P,EAAgB,CACpBA,cAAeG,KAAKqG,UAGtBrG,KAAK4pB,cAAc/pB,GAGrB2G,UACMxG,KAAKqpB,SACPrpB,KAAKqpB,QAAQtB,UAGf3hB,MAAMI,UAGRuU,SACE/a,KAAKwpB,UAAYxpB,KAAKypB,gBAClBzpB,KAAKqpB,SACPrpB,KAAKqpB,QAAQtO,SAKjB6O,cAAc/pB,GAEZ,IADkBU,EAAaoB,QAAQ3B,KAAKqG,SAjJ5B,mBAiJkDxG,GACpDmC,iBAAd,CAMA,GAAI,iBAAkBxJ,SAAS6B,gBAC7B,IAAK,MAAMtC,IAAW,GAAGmQ,UAAU1P,SAASwC,KAAKqN,UAC/C9H,EAAaC,IAAIzI,EAAS,YAAa2C,GAIvCsF,KAAKqpB,SACPrpB,KAAKqpB,QAAQtB,UAGf/nB,KAAKupB,MAAMvvB,UAAUqJ,OAAOwL,IAC5B7O,KAAKqG,SAASrM,UAAUqJ,OAAOwL,IAC/B7O,KAAKqG,SAASlC,aAAa,gBAAiB,SAC5CF,EAAYG,oBAAoBpE,KAAKupB,MAAO,UAC5ChpB,EAAaoB,QAAQ3B,KAAKqG,SArKR,qBAqKgCxG,IAGpDqF,WAAWC,GAGT,GAAgC,iBAFhCA,EAASiB,MAAMlB,WAAWC,IAERiM,YAA2BtY,EAAUqM,EAAOiM,YACV,mBAA3CjM,EAAOiM,UAAUhB,sBAGxB,MAAM,IAAInK,UAAW,GAAExK,GAAKyK,+GAG9B,OAAOf,EAGTukB,gBACE,QAAsB,IAAXG,GACT,MAAM,IAAI5jB,UAAU,gEAGtB,IAAI6jB,EAAmB9pB,KAAKqG,SAEG,WAA3BrG,KAAKsG,QAAQ8K,UACf0Y,EAAmB9pB,KAAKspB,QACfxwB,EAAUkH,KAAKsG,QAAQ8K,WAChC0Y,EAAmB5wB,EAAW8G,KAAKsG,QAAQ8K,WACA,iBAA3BpR,KAAKsG,QAAQ8K,YAC7B0Y,EAAmB9pB,KAAKsG,QAAQ8K,WAGlC,MAAM8X,EAAelpB,KAAK+pB,mBAC1B/pB,KAAKqpB,QAAUQ,GAAoBC,EAAkB9pB,KAAKupB,MAAOL,GAGnExZ,WACE,OAAO1P,KAAKupB,MAAMvvB,UAAUC,SAAS4U,IAGvCmb,gBACE,MAAMC,EAAiBjqB,KAAKspB,QAE5B,GAAIW,EAAejwB,UAAUC,SAtMN,WAuMrB,OAAO8uB,GAGT,GAAIkB,EAAejwB,UAAUC,SAzMJ,aA0MvB,OAAO+uB,GAGT,GAAIiB,EAAejwB,UAAUC,SA5MA,iBA6M3B,MA7LsB,MAgMxB,GAAIgwB,EAAejwB,UAAUC,SA/ME,mBAgN7B,MAhMyB,SAoM3B,MAAMiwB,EAAkF,QAA1E3wB,iBAAiByG,KAAKupB,OAAO/vB,iBAAiB,iBAAiBlB,OAE7E,OAAI2xB,EAAejwB,UAAUC,SA1NP,UA2NbiwB,EAAQtB,GAAmBD,GAG7BuB,EAAQpB,GAAsBD,GAGvCY,gBACE,OAAkD,OAA3CzpB,KAAKqG,SAAS3M,QAzND,WA4NtBywB,aACE,MAAMlS,OAAEA,GAAWjY,KAAKsG,QAExB,MAAsB,iBAAX2R,EACFA,EAAO5f,MAAM,KAAK4Q,KAAIzF,GAAS9G,OAAOmR,SAASrK,EAAO,MAGzC,mBAAXyU,EACFmS,GAAcnS,EAAOmS,EAAYpqB,KAAKqG,UAGxC4R,EAGT8R,mBACE,MAAMM,EAAwB,CAC5B7Y,UAAWxR,KAAKgqB,gBAChBvE,UAAW,CAAC,CACVjqB,KAAM,kBACNkY,QAAS,CACP8J,SAAUxd,KAAKsG,QAAQkX,WAG3B,CACEhiB,KAAM,SACNkY,QAAS,CACPuE,OAAQjY,KAAKmqB,iBAcnB,OARInqB,KAAKwpB,WAAsC,WAAzBxpB,KAAKsG,QAAQ2iB,WACjChlB,EAAYC,iBAAiBlE,KAAKupB,MAAO,SAAU,UACnDc,EAAsB5E,UAAY,CAAC,CACjCjqB,KAAM,cACNsX,SAAS,KAIN,IACFuX,KACsC,mBAA9BrqB,KAAKsG,QAAQ4iB,aAA8BlpB,KAAKsG,QAAQ4iB,aAAamB,GAAyBrqB,KAAKsG,QAAQ4iB,cAI1HoB,iBAAgBhoB,IAAEA,EAAFtF,OAAOA,IACrB,MAAM4P,EAAQ3E,EAAejJ,KAzQF,8DAyQ+BgB,KAAKupB,OAAO7kB,QAAO3M,GAAWqB,EAAUrB,KAE7F6U,EAAMzT,QAMXgE,EAAqByP,EAAO5P,EAAQsF,IAAQgmB,IAAiB1b,EAAMzU,SAAS6E,IAAS2sB,QAIjE7iB,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOyhB,GAAS9hB,oBAAoBtH,KAAMmF,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,SAIQ2B,kBAAC7H,GAChB,GA5TuB,IA4TnBA,EAAM+I,QAAiD,UAAf/I,EAAMwB,MA/TtC,QA+T0DxB,EAAMqD,IAC1E,OAGF,MAAMioB,EAActiB,EAAejJ,KAAKypB,IAExC,IAAK,MAAM1gB,KAAUwiB,EAAa,CAChC,MAAMC,EAAUpB,GAASriB,YAAYgB,GACrC,IAAKyiB,IAAyC,IAA9BA,EAAQlkB,QAAQ6iB,UAC9B,SAGF,MAAMsB,EAAexrB,EAAMwrB,eACrBC,EAAeD,EAAatyB,SAASqyB,EAAQjB,OACnD,GACEkB,EAAatyB,SAASqyB,EAAQnkB,WACC,WAA9BmkB,EAAQlkB,QAAQ6iB,YAA2BuB,GACb,YAA9BF,EAAQlkB,QAAQ6iB,WAA2BuB,EAE5C,SAIF,GAAIF,EAAQjB,MAAMtvB,SAASgF,EAAMjC,UAA4B,UAAfiC,EAAMwB,MAtV1C,QAsV8DxB,EAAMqD,KAAoB,qCAAqC0D,KAAK/G,EAAMjC,OAAOqK,UACvJ,SAGF,MAAMxH,EAAgB,CAAEA,cAAe2qB,EAAQnkB,UAE5B,UAAfpH,EAAMwB,OACRZ,EAAcuH,WAAanI,GAG7BurB,EAAQZ,cAAc/pB,IAIEiH,6BAAC7H,GAI3B,MAAM0rB,EAAU,kBAAkB3kB,KAAK/G,EAAMjC,OAAOqK,SAC9CujB,EA1WS,WA0WO3rB,EAAMqD,IACtBuoB,EAAkB,CAACxC,GAAcC,IAAgBnwB,SAAS8G,EAAMqD,KAEtE,IAAKuoB,IAAoBD,EACvB,OAGF,GAAID,IAAYC,EACd,OAGF3rB,EAAMwD,iBAEN,MAAMqoB,EAAkB7iB,EAAeG,QAAQP,GAAsB5I,EAAMa,eAAelG,YACpFkJ,EAAWsmB,GAAS9hB,oBAAoBwjB,GAE9C,GAAID,EAIF,OAHA5rB,EAAM8rB,kBACNjoB,EAAS8M,YACT9M,EAASwnB,gBAAgBrrB,GAIvB6D,EAAS4M,aACXzQ,EAAM8rB,kBACNjoB,EAAS6M,OACTmb,EAAgBnB,UAStBppB,EAAaa,GAAG5I,SAAUgwB,GAAwB3gB,GAAsBuhB,GAAS4B,uBACjFzqB,EAAaa,GAAG5I,SAAUgwB,GAAwBE,GAAeU,GAAS4B,uBAC1EzqB,EAAaa,GAAG5I,SAAU+vB,GAAsBa,GAAS6B,YACzD1qB,EAAaa,GAAG5I,SApYc,6BAoYkB4wB,GAAS6B,YACzD1qB,EAAaa,GAAG5I,SAAU+vB,GAAsB1gB,IAAsB,SAAU5I,GAC9EA,EAAMwD,iBACN2mB,GAAS9hB,oBAAoBtH,MAAM+H,YAOrC3M,EAAmBguB,IC3anB,MAAM8B,GAAyB,oDACzBC,GAA0B,cAC1BC,GAAmB,gBACnBC,GAAkB,eAMxB,MAAMC,GACJ9lB,cACExF,KAAKqG,SAAW7N,SAASwC,KAI3BuwB,WAEE,MAAMC,EAAgBhzB,SAAS6B,gBAAgByd,YAC/C,OAAOna,KAAK0M,IAAIvP,OAAO2wB,WAAaD,GAGtC7b,OACE,MAAM6E,EAAQxU,KAAKurB,WACnBvrB,KAAK0rB,mBAEL1rB,KAAK2rB,sBAAsB3rB,KAAKqG,SAAU+kB,IAAkBQ,GAAmBA,EAAkBpX,IAEjGxU,KAAK2rB,sBAAsBT,GAAwBE,IAAkBQ,GAAmBA,EAAkBpX,IAC1GxU,KAAK2rB,sBAAsBR,GAAyBE,IAAiBO,GAAmBA,EAAkBpX,IAG5G+M,QACEvhB,KAAK6rB,wBAAwB7rB,KAAKqG,SAAU,YAC5CrG,KAAK6rB,wBAAwB7rB,KAAKqG,SAAU+kB,IAC5CprB,KAAK6rB,wBAAwBX,GAAwBE,IACrDprB,KAAK6rB,wBAAwBV,GAAyBE,IAGxDS,gBACE,OAAO9rB,KAAKurB,WAAa,EAI3BG,mBACE1rB,KAAK+rB,sBAAsB/rB,KAAKqG,SAAU,YAC1CrG,KAAKqG,SAAS6J,MAAM0L,SAAW,SAGjC+P,sBAAsB3zB,EAAUg0B,EAAe1wB,GAC7C,MAAM2wB,EAAiBjsB,KAAKurB,WAW5BvrB,KAAKksB,2BAA2Bl0B,GAVHD,IAC3B,GAAIA,IAAYiI,KAAKqG,UAAYvL,OAAO2wB,WAAa1zB,EAAQ+f,YAAcmU,EACzE,OAGFjsB,KAAK+rB,sBAAsBh0B,EAASi0B,GACpC,MAAMJ,EAAkB9wB,OAAOvB,iBAAiBxB,GAASyB,iBAAiBwyB,GAC1Ej0B,EAAQmY,MAAMic,YAAYH,EAAgB,GAAE1wB,EAASoB,OAAOC,WAAWivB,YAM3EG,sBAAsBh0B,EAASi0B,GAC7B,MAAMI,EAAcr0B,EAAQmY,MAAM1W,iBAAiBwyB,GAC/CI,GACFnoB,EAAYC,iBAAiBnM,EAASi0B,EAAeI,GAIzDP,wBAAwB7zB,EAAUg0B,GAahChsB,KAAKksB,2BAA2Bl0B,GAZHD,IAC3B,MAAMyL,EAAQS,EAAYY,iBAAiB9M,EAASi0B,GAEtC,OAAVxoB,GAKJS,EAAYG,oBAAoBrM,EAASi0B,GACzCj0B,EAAQmY,MAAMic,YAAYH,EAAexoB,IALvCzL,EAAQmY,MAAMmc,eAAeL,MAWnCE,2BAA2Bl0B,EAAUs0B,GACnC,GAAIxzB,EAAUd,GACZs0B,EAASt0B,QAIX,IAAK,MAAMu0B,KAAOtkB,EAAejJ,KAAKhH,EAAUgI,KAAKqG,UACnDimB,EAASC,IC7Ff,MAEM1d,GAAkB,OAClB2d,GAAmB,wBAEnBznB,GAAU,CACd0nB,UAAW,iBACXrzB,WAAW,EACXyN,YAAY,EACZ6lB,YAAa,OACbC,cAAe,MAGX3nB,GAAc,CAClBynB,UAAW,SACXrzB,UAAW,UACXyN,WAAY,UACZ6lB,YAAa,mBACbC,cAAe,mBAOjB,MAAMC,WAAiB9nB,EACrBU,YAAYL,GACViB,QACApG,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAC/BnF,KAAK6sB,aAAc,EACnB7sB,KAAKqG,SAAW,KAIPtB,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA3CS,WA+CXmU,KAAKtU,GACH,IAAK0E,KAAKsG,QAAQlN,UAEhB,YADA8C,EAAQZ,GAIV0E,KAAK8sB,UAEL,MAAM/0B,EAAUiI,KAAK+sB,cACjB/sB,KAAKsG,QAAQO,YACflM,EAAO5C,GAGTA,EAAQiC,UAAUuQ,IAAIsE,IAEtB7O,KAAKgtB,mBAAkB,KACrB9wB,EAAQZ,MAIZqU,KAAKrU,GACE0E,KAAKsG,QAAQlN,WAKlB4G,KAAK+sB,cAAc/yB,UAAUqJ,OAAOwL,IAEpC7O,KAAKgtB,mBAAkB,KACrBhtB,KAAKwG,UACLtK,EAAQZ,OARRY,EAAQZ,GAYZkL,UACOxG,KAAK6sB,cAIVtsB,EAAaC,IAAIR,KAAKqG,SAAUmmB,IAEhCxsB,KAAKqG,SAAShD,SACdrD,KAAK6sB,aAAc,GAIrBE,cACE,IAAK/sB,KAAKqG,SAAU,CAClB,MAAM4mB,EAAWz0B,SAAS00B,cAAc,OACxCD,EAASR,UAAYzsB,KAAKsG,QAAQmmB,UAC9BzsB,KAAKsG,QAAQO,YACfomB,EAASjzB,UAAUuQ,IAjGH,QAoGlBvK,KAAKqG,SAAW4mB,EAGlB,OAAOjtB,KAAKqG,SAGdhB,kBAAkBF,GAGhB,OADAA,EAAOunB,YAAcxzB,EAAWiM,EAAOunB,aAChCvnB,EAGT2nB,UACE,GAAI9sB,KAAK6sB,YACP,OAGF,MAAM90B,EAAUiI,KAAK+sB,cACrB/sB,KAAKsG,QAAQomB,YAAYS,OAAOp1B,GAEhCwI,EAAaa,GAAGrJ,EAASy0B,IAAiB,KACxCtwB,EAAQ8D,KAAKsG,QAAQqmB,kBAGvB3sB,KAAK6sB,aAAc,EAGrBG,kBAAkB1xB,GAChBa,EAAuBb,EAAU0E,KAAK+sB,cAAe/sB,KAAKsG,QAAQO,aCjItE,MAEMJ,GAAa,gBAMb2mB,GAAmB,WAEnBroB,GAAU,CACdsoB,YAAa,KACbC,WAAW,GAGPtoB,GAAc,CAClBqoB,YAAa,UACbC,UAAW,WAOb,MAAMC,WAAkBzoB,EACtBU,YAAYL,GACViB,QACApG,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAC/BnF,KAAKwtB,WAAY,EACjBxtB,KAAKytB,qBAAuB,KAInB1oB,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA1CS,YA8CXiyB,WACM1tB,KAAKwtB,YAILxtB,KAAKsG,QAAQgnB,WACfttB,KAAKsG,QAAQ+mB,YAAY1D,QAG3BppB,EAAaC,IAAIhI,SAAUiO,IAC3BlG,EAAaa,GAAG5I,SArDG,wBAqDsByG,GAASe,KAAK2tB,eAAe1uB,KACtEsB,EAAaa,GAAG5I,SArDO,4BAqDsByG,GAASe,KAAK4tB,eAAe3uB,KAE1Ee,KAAKwtB,WAAY,GAGnBK,aACO7tB,KAAKwtB,YAIVxtB,KAAKwtB,WAAY,EACjBjtB,EAAaC,IAAIhI,SAAUiO,KAI7BknB,eAAe1uB,GACb,MAAMouB,YAAEA,GAAgBrtB,KAAKsG,QAE7B,GAAIrH,EAAMjC,SAAWxE,UAAYyG,EAAMjC,SAAWqwB,GAAeA,EAAYpzB,SAASgF,EAAMjC,QAC1F,OAGF,MAAMkW,EAAWjL,EAAec,kBAAkBskB,GAE1B,IAApBna,EAAS/Z,OACXk0B,EAAY1D,QACH3pB,KAAKytB,uBAAyBL,GACvCla,EAASA,EAAS/Z,OAAS,GAAGwwB,QAE9BzW,EAAS,GAAGyW,QAIhBiE,eAAe3uB,GApFD,QAqFRA,EAAMqD,MAIVtC,KAAKytB,qBAAuBxuB,EAAM6uB,SAAWV,GAxFzB,YCFxB,MAQMW,GAAgB,kBAChBC,GAAc,gBAOdC,GAAkB,aAElBpf,GAAkB,OAClBqf,GAAoB,eAOpBnpB,GAAU,CACdkoB,UAAU,EACV3hB,UAAU,EACVqe,OAAO,GAGH3kB,GAAc,CAClBioB,SAAU,mBACV3hB,SAAU,UACVqe,MAAO,WAOT,MAAMwE,WAAchoB,EAClBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAKouB,QAAUnmB,EAAeG,QAxBV,gBAwBmCpI,KAAKqG,UAC5DrG,KAAKquB,UAAYruB,KAAKsuB,sBACtBtuB,KAAKuuB,WAAavuB,KAAKwuB,uBACvBxuB,KAAK0P,UAAW,EAChB1P,KAAKkP,kBAAmB,EACxBlP,KAAKyuB,WAAa,IAAInD,GAEtBtrB,KAAKkM,qBAIInH,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAlES,QAsEXsM,OAAOlI,GACL,OAAOG,KAAK0P,SAAW1P,KAAK2P,OAAS3P,KAAK4P,KAAK/P,GAGjD+P,KAAK/P,GACCG,KAAK0P,UAAY1P,KAAKkP,kBAIR3O,EAAaoB,QAAQ3B,KAAKqG,SAAU2nB,GAAY,CAChEnuB,cAAAA,IAGYmC,mBAIdhC,KAAK0P,UAAW,EAChB1P,KAAKkP,kBAAmB,EAExBlP,KAAKyuB,WAAW9e,OAEhBnX,SAASwC,KAAKhB,UAAUuQ,IAAI0jB,IAE5BjuB,KAAK0uB,gBAEL1uB,KAAKquB,UAAUze,MAAK,IAAM5P,KAAK2uB,aAAa9uB,MAG9C8P,OACO3P,KAAK0P,WAAY1P,KAAKkP,mBAIT3O,EAAaoB,QAAQ3B,KAAKqG,SAlG5B,iBAoGFrE,mBAIdhC,KAAK0P,UAAW,EAChB1P,KAAKkP,kBAAmB,EACxBlP,KAAKuuB,WAAWV,aAEhB7tB,KAAKqG,SAASrM,UAAUqJ,OAAOwL,IAE/B7O,KAAK4G,gBAAe,IAAM5G,KAAK4uB,cAAc5uB,KAAKqG,SAAUrG,KAAKuO,iBAGnE/H,UACE,IAAK,MAAMqoB,IAAe,CAAC/zB,OAAQkF,KAAKouB,SACtC7tB,EAAaC,IAAIquB,EAvHJ,aA0Hf7uB,KAAKquB,UAAU7nB,UACfxG,KAAKuuB,WAAWV,aAChBznB,MAAMI,UAGRsoB,eACE9uB,KAAK0uB,gBAIPJ,sBACE,OAAO,IAAI1B,GAAS,CAClBxzB,UAAW0H,QAAQd,KAAKsG,QAAQ2mB,UAChCpmB,WAAY7G,KAAKuO,gBAIrBigB,uBACE,OAAO,IAAIjB,GAAU,CACnBF,YAAartB,KAAKqG,WAItBsoB,aAAa9uB,GAENrH,SAASwC,KAAKf,SAAS+F,KAAKqG,WAC/B7N,SAASwC,KAAKmyB,OAAOntB,KAAKqG,UAG5BrG,KAAKqG,SAAS6J,MAAM+Y,QAAU,QAC9BjpB,KAAKqG,SAAShC,gBAAgB,eAC9BrE,KAAKqG,SAASlC,aAAa,cAAc,GACzCnE,KAAKqG,SAASlC,aAAa,OAAQ,UACnCnE,KAAKqG,SAASkV,UAAY,EAE1B,MAAMwT,EAAY9mB,EAAeG,QAxIT,cAwIsCpI,KAAKouB,SAC/DW,IACFA,EAAUxT,UAAY,GAGxB5gB,EAAOqF,KAAKqG,UAEZrG,KAAKqG,SAASrM,UAAUuQ,IAAIsE,IAa5B7O,KAAK4G,gBAXsB,KACrB5G,KAAKsG,QAAQqjB,OACf3pB,KAAKuuB,WAAWb,WAGlB1tB,KAAKkP,kBAAmB,EACxB3O,EAAaoB,QAAQ3B,KAAKqG,SApKX,iBAoKkC,CAC/CxG,cAAAA,MAIoCG,KAAKouB,QAASpuB,KAAKuO,eAG7DrC,qBACE3L,EAAaa,GAAGpB,KAAKqG,SA1KM,4BA0K2BpH,IACpD,GApLa,WAoLTA,EAAMqD,IAIV,OAAItC,KAAKsG,QAAQgF,UACfrM,EAAMwD,sBACNzC,KAAK2P,aAIP3P,KAAKgvB,gCAGPzuB,EAAaa,GAAGtG,OA1LE,mBA0LoB,KAChCkF,KAAK0P,WAAa1P,KAAKkP,kBACzBlP,KAAK0uB,mBAITnuB,EAAaa,GAAGpB,KAAKqG,SA/LI,0BA+L2BpH,IAC9CA,EAAMjC,SAAWiC,EAAMgwB,gBAIG,WAA1BjvB,KAAKsG,QAAQ2mB,SAKbjtB,KAAKsG,QAAQ2mB,UACfjtB,KAAK2P,OALL3P,KAAKgvB,iCAUXJ,aACE5uB,KAAKqG,SAAS6J,MAAM+Y,QAAU,OAC9BjpB,KAAKqG,SAASlC,aAAa,eAAe,GAC1CnE,KAAKqG,SAAShC,gBAAgB,cAC9BrE,KAAKqG,SAAShC,gBAAgB,QAC9BrE,KAAKkP,kBAAmB,EAExBlP,KAAKquB,UAAU1e,MAAK,KAClBnX,SAASwC,KAAKhB,UAAUqJ,OAAO4qB,IAC/BjuB,KAAKkvB,oBACLlvB,KAAKyuB,WAAWlN,QAChBhhB,EAAaoB,QAAQ3B,KAAKqG,SAAU0nB,OAIxCxf,cACE,OAAOvO,KAAKqG,SAASrM,UAAUC,SA1NX,QA6NtB+0B,6BAEE,GADkBzuB,EAAaoB,QAAQ3B,KAAKqG,SAxOlB,0BAyOZrE,iBACZ,OAGF,MAAMmtB,EAAqBnvB,KAAKqG,SAASyW,aAAetkB,SAAS6B,gBAAgBwd,aAC3EuX,EAAmBpvB,KAAKqG,SAAS6J,MAAM4L,UAEpB,WAArBsT,GAAiCpvB,KAAKqG,SAASrM,UAAUC,SAASi0B,MAIjEiB,IACHnvB,KAAKqG,SAAS6J,MAAM4L,UAAY,UAGlC9b,KAAKqG,SAASrM,UAAUuQ,IAAI2jB,IAC5BluB,KAAK4G,gBAAe,KAClB5G,KAAKqG,SAASrM,UAAUqJ,OAAO6qB,IAC/BluB,KAAK4G,gBAAe,KAClB5G,KAAKqG,SAAS6J,MAAM4L,UAAYsT,IAC/BpvB,KAAKouB,WACPpuB,KAAKouB,SAERpuB,KAAKqG,SAASsjB,SAOhB+E,gBACE,MAAMS,EAAqBnvB,KAAKqG,SAASyW,aAAetkB,SAAS6B,gBAAgBwd,aAC3EoU,EAAiBjsB,KAAKyuB,WAAWlD,WACjC8D,EAAoBpD,EAAiB,EAE3C,GAAIoD,IAAsBF,EAAoB,CAC5C,MAAMzpB,EAAWxK,IAAU,cAAgB,eAC3C8E,KAAKqG,SAAS6J,MAAMxK,GAAa,GAAEumB,MAGrC,IAAKoD,GAAqBF,EAAoB,CAC5C,MAAMzpB,EAAWxK,IAAU,eAAiB,cAC5C8E,KAAKqG,SAAS6J,MAAMxK,GAAa,GAAEumB,OAIvCiD,oBACElvB,KAAKqG,SAAS6J,MAAMof,YAAc,GAClCtvB,KAAKqG,SAAS6J,MAAMqf,aAAe,GAIfzoB,uBAAC3B,EAAQtF,GAC7B,OAAOG,KAAK0H,MAAK,WACf,MAAMC,EAAOwmB,GAAM7mB,oBAAoBtH,KAAMmF,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQtF,QASnBU,EAAaa,GAAG5I,SA3Sc,0BAUD,4BAiSyC,SAAUyG,GAC9E,MAAMjC,EAAStE,EAAuBsH,MAElC,CAAC,IAAK,QAAQ7H,SAAS6H,KAAKqH,UAC9BpI,EAAMwD,iBAGRlC,EAAac,IAAIrE,EAAQgxB,IAAYwB,IAC/BA,EAAUxtB,kBAKdzB,EAAac,IAAIrE,EAAQ+wB,IAAc,KACjC30B,EAAU4G,OACZA,KAAK2pB,cAMX,MAAM8F,EAAcxnB,EAAeG,QAzTf,eA0ThBqnB,GACFtB,GAAMpnB,YAAY0oB,GAAa9f,OAGpBwe,GAAM7mB,oBAAoBtK,GAElC+K,OAAO/H,SAGdiH,EAAqBknB,IAMrB/yB,EAAmB+yB,ICzVnB,MAOMtf,GAAkB,OAClB6gB,GAAqB,UACrBC,GAAoB,SAEpBC,GAAgB,kBAKhBC,GAAwB,6BACxB9B,GAAgB,sBAOhBhpB,GAAU,CACdkoB,UAAU,EACV3hB,UAAU,EACVoP,QAAQ,GAGJ1V,GAAc,CAClBioB,SAAU,mBACV3hB,SAAU,UACVoP,OAAQ,WAOV,MAAMoV,WAAkB3pB,EACtBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAK0P,UAAW,EAChB1P,KAAKquB,UAAYruB,KAAKsuB,sBACtBtuB,KAAKuuB,WAAavuB,KAAKwuB,uBACvBxuB,KAAKkM,qBAIInH,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA5DS,YAgEXsM,OAAOlI,GACL,OAAOG,KAAK0P,SAAW1P,KAAK2P,OAAS3P,KAAK4P,KAAK/P,GAGjD+P,KAAK/P,GACCG,KAAK0P,UAISnP,EAAaoB,QAAQ3B,KAAKqG,SA5D5B,oBA4DkD,CAAExG,cAAAA,IAEtDmC,mBAIdhC,KAAK0P,UAAW,EAChB1P,KAAKquB,UAAUze,OAEV5P,KAAKsG,QAAQoU,SAChB,IAAI4Q,IAAkB3b,OAGxB3P,KAAKqG,SAASlC,aAAa,cAAc,GACzCnE,KAAKqG,SAASlC,aAAa,OAAQ,UACnCnE,KAAKqG,SAASrM,UAAUuQ,IAAImlB,IAY5B1vB,KAAK4G,gBAVoB,KAClB5G,KAAKsG,QAAQoU,QAChB1a,KAAKuuB,WAAWb,WAGlB1tB,KAAKqG,SAASrM,UAAUuQ,IAAIsE,IAC5B7O,KAAKqG,SAASrM,UAAUqJ,OAAOqsB,IAC/BnvB,EAAaoB,QAAQ3B,KAAKqG,SAnFX,qBAmFkC,CAAExG,cAAAA,MAGfG,KAAKqG,UAAU,IAGvDsJ,OACO3P,KAAK0P,WAIQnP,EAAaoB,QAAQ3B,KAAKqG,SA7F5B,qBA+FFrE,mBAIdhC,KAAKuuB,WAAWV,aAChB7tB,KAAKqG,SAAS0pB,OACd/vB,KAAK0P,UAAW,EAChB1P,KAAKqG,SAASrM,UAAUuQ,IAAIolB,IAC5B3vB,KAAKquB,UAAU1e,OAcf3P,KAAK4G,gBAZoB,KACvB5G,KAAKqG,SAASrM,UAAUqJ,OAAOwL,GAAiB8gB,IAChD3vB,KAAKqG,SAAShC,gBAAgB,cAC9BrE,KAAKqG,SAAShC,gBAAgB,QAEzBrE,KAAKsG,QAAQoU,SAChB,IAAI4Q,IAAkB/J,QAGxBhhB,EAAaoB,QAAQ3B,KAAKqG,SAAU0nB,MAGA/tB,KAAKqG,UAAU,KAGvDG,UACExG,KAAKquB,UAAU7nB,UACfxG,KAAKuuB,WAAWV,aAChBznB,MAAMI,UAIR8nB,sBACE,MAUMl1B,EAAY0H,QAAQd,KAAKsG,QAAQ2mB,UAEvC,OAAO,IAAIL,GAAS,CAClBH,UAlJsB,qBAmJtBrzB,UAAAA,EACAyN,YAAY,EACZ6lB,YAAa1sB,KAAKqG,SAASzM,WAC3B+yB,cAAevzB,EAjBK,KACU,WAA1B4G,KAAKsG,QAAQ2mB,SAKjBjtB,KAAK2P,OAJHpP,EAAaoB,QAAQ3B,KAAKqG,SAAUwpB,KAeK,OAI/CrB,uBACE,OAAO,IAAIjB,GAAU,CACnBF,YAAartB,KAAKqG,WAItB6F,qBACE3L,EAAaa,GAAGpB,KAAKqG,SAvJM,gCAuJ2BpH,IAtKvC,WAuKTA,EAAMqD,MAILtC,KAAKsG,QAAQgF,SAKlBtL,KAAK2P,OAJHpP,EAAaoB,QAAQ3B,KAAKqG,SAAUwpB,QASpB/oB,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOmoB,GAAUxoB,oBAAoBtH,KAAMmF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQnF,WASnBO,EAAaa,GAAG5I,SA5Lc,8BAGD,gCAyLyC,SAAUyG,GAC9E,MAAMjC,EAAStE,EAAuBsH,MAMtC,GAJI,CAAC,IAAK,QAAQ7H,SAAS6H,KAAKqH,UAC9BpI,EAAMwD,iBAGJ5I,EAAWmG,MACb,OAGFO,EAAac,IAAIrE,EAAQ+wB,IAAc,KAEjC30B,EAAU4G,OACZA,KAAK2pB,WAKT,MAAM8F,EAAcxnB,EAAeG,QAAQwnB,IACvCH,GAAeA,IAAgBzyB,GACjC8yB,GAAU/oB,YAAY0oB,GAAa9f,OAGxBmgB,GAAUxoB,oBAAoBtK,GACtC+K,OAAO/H,SAGdO,EAAaa,GAAGtG,OAvOa,8BAuOgB,KAC3C,IAAK,MAAM9C,KAAYiQ,EAAejJ,KAAK4wB,IACzCE,GAAUxoB,oBAAoBtP,GAAU4X,UAI5CrP,EAAaa,GAAGtG,OA/NM,uBA+NgB,KACpC,IAAK,MAAM/C,KAAWkQ,EAAejJ,KAAK,gDACG,UAAvCzF,iBAAiBxB,GAAS0b,UAC5Bqc,GAAUxoB,oBAAoBvP,GAAS4X,UAK7C1I,EAAqB6oB,IAMrB10B,EAAmB00B,ICjRnB,MAAME,GAAgB,IAAIzxB,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUI0xB,GAAmB,iEAOnBC,GAAmB,qIAEnBC,GAAmB,CAACpc,EAAWqc,KACnC,MAAMC,EAAgBtc,EAAU1B,SAASrO,cAEzC,OAAIosB,EAAqBj4B,SAASk4B,IAC5BL,GAAcvwB,IAAI4wB,IACbvvB,QAAQmvB,GAAiBjqB,KAAK+N,EAAUuc,YAAcJ,GAAiBlqB,KAAK+N,EAAUuc,YAO1FF,EAAqB1rB,QAAO6rB,GAAkBA,aAA0BxqB,SAC5E4b,MAAK6O,GAASA,EAAMxqB,KAAKqqB,MAGjBI,GAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAlCP,kBAmC7BjR,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BkR,KAAM,GACNjR,EAAG,GACHkR,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJ3Q,EAAG,GACHtT,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDkkB,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IC/DAntB,GAAU,CACdotB,WAAY,GACZC,SAAU,cACVC,QAAS,GACT9V,MAAM,EACN+V,UAAU,EACVC,WAAY,KACZC,UAAW/B,IAGPzrB,GAAc,CAClBmtB,WAAY,oBACZC,SAAU,SACVC,QAAS,SACT9V,KAAM,UACN+V,SAAU,UACVC,WAAY,kBACZC,UAAW,UAGPC,GAAqB,CACzBz6B,SAAU,mBACV06B,MAAO,kCAOT,MAAMC,WAAwB7tB,EAC5BU,YAAYL,GACViB,QACApG,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAItBJ,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA/CS,kBAmDXm3B,aACE,OAAO9zB,OAAOC,OAAOiB,KAAKsG,QAAQ+rB,SAC/BppB,KAAI9D,GAAUnF,KAAK6yB,yBAAyB1tB,KAC5CT,OAAO5D,SAGZgyB,aACE,OAAO9yB,KAAK4yB,aAAaz5B,OAAS,EAGpC45B,cAAcV,GAGZ,OAFAryB,KAAKgzB,cAAcX,GACnBryB,KAAKsG,QAAQ+rB,QAAU,IAAKryB,KAAKsG,QAAQ+rB,WAAYA,GAC9CryB,KAGTizB,SACE,MAAMC,EAAkB16B,SAAS00B,cAAc,OAC/CgG,EAAgBC,UAAYnzB,KAAKozB,eAAepzB,KAAKsG,QAAQ8rB,UAE7D,IAAK,MAAOp6B,EAAUq7B,KAASv0B,OAAOw0B,QAAQtzB,KAAKsG,QAAQ+rB,SACzDryB,KAAKuzB,YAAYL,EAAiBG,EAAMr7B,GAG1C,MAAMo6B,EAAWc,EAAgB7qB,SAAS,GACpC8pB,EAAanyB,KAAK6yB,yBAAyB7yB,KAAKsG,QAAQ6rB,YAM9D,OAJIA,GACFC,EAASp4B,UAAUuQ,OAAO4nB,EAAW95B,MAAM,MAGtC+5B,EAIT9sB,iBAAiBH,GACfiB,MAAMd,iBAAiBH,GACvBnF,KAAKgzB,cAAc7tB,EAAOktB,SAG5BW,cAAcQ,GACZ,IAAK,MAAOx7B,EAAUq6B,KAAYvzB,OAAOw0B,QAAQE,GAC/CptB,MAAMd,iBAAiB,CAAEtN,SAAAA,EAAU06B,MAAOL,GAAWI,IAIzDc,YAAYnB,EAAUC,EAASr6B,GAC7B,MAAMy7B,EAAkBxrB,EAAeG,QAAQpQ,EAAUo6B,GAEpDqB,KAILpB,EAAUryB,KAAK6yB,yBAAyBR,IAOpCv5B,EAAUu5B,GACZryB,KAAK0zB,sBAAsBx6B,EAAWm5B,GAAUoB,GAI9CzzB,KAAKsG,QAAQiW,KACfkX,EAAgBN,UAAYnzB,KAAKozB,eAAef,GAIlDoB,EAAgBE,YAActB,EAd5BoB,EAAgBpwB,UAiBpB+vB,eAAeI,GACb,OAAOxzB,KAAKsG,QAAQgsB,SDzDjB,SAAsBsB,EAAYpB,EAAWqB,GAClD,IAAKD,EAAWz6B,OACd,OAAOy6B,EAGT,GAAIC,GAAgD,mBAArBA,EAC7B,OAAOA,EAAiBD,GAG1B,MACME,GADY,IAAIh5B,OAAOi5B,WACKC,gBAAgBJ,EAAY,aACxD1gB,EAAW,GAAGhL,UAAU4rB,EAAgB94B,KAAKqF,iBAAiB,MAEpE,IAAK,MAAMtI,KAAWmb,EAAU,CAC9B,MAAM+gB,EAAcl8B,EAAQsa,SAASrO,cAErC,IAAKlF,OAAOqC,KAAKqxB,GAAWr6B,SAAS87B,GAAc,CACjDl8B,EAAQsL,SAER,SAGF,MAAM6wB,EAAgB,GAAGhsB,UAAUnQ,EAAQwM,YACrC4vB,EAAoB,GAAGjsB,OAAOsqB,EAAU,MAAQ,GAAIA,EAAUyB,IAAgB,IAEpF,IAAK,MAAMlgB,KAAamgB,EACjB/D,GAAiBpc,EAAWogB,IAC/Bp8B,EAAQsM,gBAAgB0P,EAAU1B,UAKxC,OAAOyhB,EAAgB94B,KAAKm4B,UCyBKiB,CAAaZ,EAAKxzB,KAAKsG,QAAQksB,UAAWxyB,KAAKsG,QAAQisB,YAAciB,EAGtGX,yBAAyBW,GACvB,MAAsB,mBAARA,EAAqBA,EAAIxzB,MAAQwzB,EAGjDE,sBAAsB37B,EAAS07B,GAC7B,GAAIzzB,KAAKsG,QAAQiW,KAGf,OAFAkX,EAAgBN,UAAY,QAC5BM,EAAgBtG,OAAOp1B,GAIzB07B,EAAgBE,YAAc57B,EAAQ47B,aCxI1C,MACMU,GAAwB,IAAI91B,IAAI,CAAC,WAAY,YAAa,eAE1D+1B,GAAkB,OAElBzlB,GAAkB,OAGlB0lB,GAAkB,SAElBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAehBC,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO55B,IAAU,OAAS,QAC1B65B,OAAQ,SACRC,KAAM95B,IAAU,QAAU,QAGtB6J,GAAU,CACdkwB,WAAW,EACX7C,SAAU,+GAIVzwB,QAAS,cACTuzB,MAAO,GACPC,MAAO,EACP5Y,MAAM,EACNvkB,UAAU,EACVwZ,UAAW,MACXyG,OAAQ,CAAC,EAAG,GACZmd,WAAW,EACXlV,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C1C,SAAU,kBACV6X,YAAa,GACb/C,UAAU,EACVC,WAAY,KACZC,UAAW/B,GACXvH,aAAc,MAGVlkB,GAAc,CAClBiwB,UAAW,UACX7C,SAAU,SACV8C,MAAO,4BACPvzB,QAAS,SACTwzB,MAAO,kBACP5Y,KAAM,UACNvkB,SAAU,mBACVwZ,UAAW,oBACXyG,OAAQ,0BACRmd,UAAW,2BACXlV,mBAAoB,QACpB1C,SAAU,mBACV6X,YAAa,oBACb/C,SAAU,UACVC,WAAY,kBACZC,UAAW,SACXtJ,aAAc,0BAOhB,MAAMoM,WAAgBnvB,EACpBX,YAAYzN,EAASoN,GACnB,QAAsB,IAAX0kB,GACT,MAAM,IAAI5jB,UAAU,+DAGtBG,MAAMrO,EAASoN,GAGfnF,KAAKu1B,YAAa,EAClBv1B,KAAKw1B,SAAW,EAChBx1B,KAAKy1B,YAAa,EAClBz1B,KAAK01B,eAAiB,GACtB11B,KAAKqpB,QAAU,KACfrpB,KAAK21B,iBAAmB,KAGxB31B,KAAK41B,IAAM,KAEX51B,KAAK61B,gBAII9wB,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAnHS,UAuHXq6B,SACE91B,KAAKu1B,YAAa,EAGpBQ,UACE/1B,KAAKu1B,YAAa,EAGpBS,gBACEh2B,KAAKu1B,YAAcv1B,KAAKu1B,WAG1BxtB,OAAO9I,GACL,GAAKe,KAAKu1B,WAAV,CAIA,GAAIt2B,EAAO,CACT,MAAMurB,EAAUxqB,KAAKi2B,6BAA6Bh3B,GAUlD,OARAurB,EAAQkL,eAAeQ,OAAS1L,EAAQkL,eAAeQ,WAEnD1L,EAAQ2L,uBACV3L,EAAQ4L,SAER5L,EAAQ6L,UAMRr2B,KAAK0P,WACP1P,KAAKq2B,SAIPr2B,KAAKo2B,UAGP5vB,UACEgH,aAAaxN,KAAKw1B,UAElBj1B,EAAaC,IAAIR,KAAKqG,SAAS3M,QAAQ66B,IAAiBC,GAAkBx0B,KAAKs2B,mBAE3Et2B,KAAK41B,KACP51B,KAAK41B,IAAIvyB,SAGXrD,KAAKu2B,iBACLnwB,MAAMI,UAGRoJ,OACE,GAAoC,SAAhC5P,KAAKqG,SAAS6J,MAAM+Y,QACtB,MAAM,IAAIhkB,MAAM,uCAGlB,IAAMjF,KAAKw2B,mBAAoBx2B,KAAKu1B,WAClC,OAGF,MAAM/F,EAAYjvB,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UAjKxD,SAmKTuoB,GADar8B,EAAe4F,KAAKqG,WACLrG,KAAKqG,SAASmM,cAAcnY,iBAAiBJ,SAAS+F,KAAKqG,UAE7F,GAAImpB,EAAUxtB,mBAAqBy0B,EACjC,OAGF,MAAMb,EAAM51B,KAAK02B,iBAEjB12B,KAAKqG,SAASlC,aAAa,mBAAoByxB,EAAI39B,aAAa,OAEhE,MAAMm9B,UAAEA,GAAcp1B,KAAKsG,QAmB3B,GAjBKtG,KAAKqG,SAASmM,cAAcnY,gBAAgBJ,SAAS+F,KAAK41B,OAC7DR,EAAUjI,OAAOyI,GACjBr1B,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UA/KpC,cAkLflO,KAAKqpB,QACPrpB,KAAKqpB,QAAQtO,SAEb/a,KAAK0pB,cAAckM,GAGrBA,EAAI57B,UAAUuQ,IAAIsE,IAMd,iBAAkBrW,SAAS6B,gBAC7B,IAAK,MAAMtC,IAAW,GAAGmQ,UAAU1P,SAASwC,KAAKqN,UAC/C9H,EAAaa,GAAGrJ,EAAS,YAAa2C,GAe1CsF,KAAK4G,gBAXY,KACf,MAAM+vB,EAAqB32B,KAAKy1B,WAEhCz1B,KAAKy1B,YAAa,EAClBl1B,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UAzMvC,UA2MVyoB,GACF32B,KAAKq2B,WAIqBr2B,KAAK41B,IAAK51B,KAAKuO,eAG/CoB,OACE,IAAK3P,KAAK0P,WACR,OAIF,GADkBnP,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UA3NxD,SA4NDlM,iBACZ,OAGF,MAAM4zB,EAAM51B,KAAK02B,iBAKjB,GAJAd,EAAI57B,UAAUqJ,OAAOwL,IAIjB,iBAAkBrW,SAAS6B,gBAC7B,IAAK,MAAMtC,IAAW,GAAGmQ,UAAU1P,SAASwC,KAAKqN,UAC/C9H,EAAaC,IAAIzI,EAAS,YAAa2C,GAI3CsF,KAAK01B,eAAL,OAAqC,EACrC11B,KAAK01B,eAAL,OAAqC,EACrC11B,KAAK01B,eAAL,OAAqC,EACrC11B,KAAKy1B,YAAa,EAiBlBz1B,KAAK4G,gBAfY,KACX5G,KAAKm2B,yBAIJn2B,KAAKy1B,YACRG,EAAIvyB,SAGNrD,KAAKqG,SAAShC,gBAAgB,oBAC9B9D,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UAzPtC,WA2PflO,KAAKu2B,oBAGuBv2B,KAAK41B,IAAK51B,KAAKuO,eAG/CwM,SACM/a,KAAKqpB,SACPrpB,KAAKqpB,QAAQtO,SAKjByb,iBACE,OAAO11B,QAAQd,KAAK42B,aAGtBF,iBAKE,OAJK12B,KAAK41B,MACR51B,KAAK41B,IAAM51B,KAAK62B,kBAAkB72B,KAAK82B,2BAGlC92B,KAAK41B,IAGdiB,kBAAkBxE,GAChB,MAAMuD,EAAM51B,KAAK+2B,oBAAoB1E,GAASY,SAG9C,IAAK2C,EACH,OAAO,KAGTA,EAAI57B,UAAUqJ,OAAOixB,GAAiBzlB,IAEtC+mB,EAAI57B,UAAUuQ,IAAK,MAAKvK,KAAKwF,YAAY/J,aAEzC,MAAMu7B,E1E7SKC,CAAAA,IACb,GACEA,GAAUt5B,KAAKu5B,MAnBH,IAmBSv5B,KAAKw5B,gBACnB3+B,SAAS4+B,eAAeH,IAEjC,OAAOA,G0EwSSI,CAAOr3B,KAAKwF,YAAY/J,MAAMgI,WAQ5C,OANAmyB,EAAIzxB,aAAa,KAAM6yB,GAEnBh3B,KAAKuO,eACPqnB,EAAI57B,UAAUuQ,IAAI+pB,IAGbsB,EAGT0B,WAAWjF,GACT,IAAIkF,GAAU,EACVv3B,KAAK41B,MACP2B,EAAUv3B,KAAK0P,WACf1P,KAAK41B,IAAIvyB,SACTrD,KAAK41B,IAAM,MAGb51B,KAAKu2B,iBACLv2B,KAAK41B,IAAM51B,KAAK62B,kBAAkBxE,GAE9BkF,GACFv3B,KAAK4P,OAITmnB,oBAAoB1E,GAalB,OAZIryB,KAAK21B,iBACP31B,KAAK21B,iBAAiB5C,cAAcV,GAEpCryB,KAAK21B,iBAAmB,IAAIhD,GAAgB,IACvC3yB,KAAKsG,QAGR+rB,QAAAA,EACAF,WAAYnyB,KAAK6yB,yBAAyB7yB,KAAKsG,QAAQ+uB,eAIpDr1B,KAAK21B,iBAGdmB,yBACE,MAAO,CACL,iBAA0B92B,KAAK42B,aAInCA,YACE,OAAO52B,KAAKsG,QAAQ4uB,MAItBe,6BAA6Bh3B,GAC3B,OAAOe,KAAKwF,YAAY8B,oBAAoBrI,EAAMa,eAAgBE,KAAKw3B,sBAGzEjpB,cACE,OAAOvO,KAAKsG,QAAQ2uB,WAAcj1B,KAAK41B,KAAO51B,KAAK41B,IAAI57B,UAAUC,SAASq6B,IAG5E5kB,WACE,OAAO1P,KAAK41B,KAAO51B,KAAK41B,IAAI57B,UAAUC,SAAS4U,IAGjD6a,cAAckM,GACZ,MAAMpkB,EAA8C,mBAA3BxR,KAAKsG,QAAQkL,UACpCxR,KAAKsG,QAAQkL,UAAUzR,KAAKC,KAAM41B,EAAK51B,KAAKqG,UAC5CrG,KAAKsG,QAAQkL,UACTimB,EAAa9C,GAAcnjB,EAAUtL,eAC3ClG,KAAKqpB,QAAUQ,GAAoB7pB,KAAKqG,SAAUuvB,EAAK51B,KAAK+pB,iBAAiB0N,IAG/EtN,aACE,MAAMlS,OAAEA,GAAWjY,KAAKsG,QAExB,MAAsB,iBAAX2R,EACFA,EAAO5f,MAAM,KAAK4Q,KAAIzF,GAAS9G,OAAOmR,SAASrK,EAAO,MAGzC,mBAAXyU,EACFmS,GAAcnS,EAAOmS,EAAYpqB,KAAKqG,UAGxC4R,EAGT4a,yBAAyBW,GACvB,MAAsB,mBAARA,EAAqBA,EAAIzzB,KAAKC,KAAKqG,UAAYmtB,EAG/DzJ,iBAAiB0N,GACf,MAAMpN,EAAwB,CAC5B7Y,UAAWimB,EACXhS,UAAW,CACT,CACEjqB,KAAM,OACNkY,QAAS,CACPwM,mBAAoBlgB,KAAKsG,QAAQ4Z,qBAGrC,CACE1kB,KAAM,SACNkY,QAAS,CACPuE,OAAQjY,KAAKmqB,eAGjB,CACE3uB,KAAM,kBACNkY,QAAS,CACP8J,SAAUxd,KAAKsG,QAAQkX,WAG3B,CACEhiB,KAAM,QACNkY,QAAS,CACP3b,QAAU,IAAGiI,KAAKwF,YAAY/J,eAGlC,CACED,KAAM,kBACNsX,SAAS,EACTC,MAAO,aACPpX,GAAIgM,IAGF3H,KAAK02B,iBAAiBvyB,aAAa,wBAAyBwD,EAAKsL,MAAMzB,eAM/E,MAAO,IACF6Y,KACsC,mBAA9BrqB,KAAKsG,QAAQ4iB,aAA8BlpB,KAAKsG,QAAQ4iB,aAAamB,GAAyBrqB,KAAKsG,QAAQ4iB,cAI1H2M,gBACE,MAAM6B,EAAW13B,KAAKsG,QAAQ3E,QAAQtJ,MAAM,KAE5C,IAAK,MAAMsJ,KAAW+1B,EACpB,GAAgB,UAAZ/1B,EACFpB,EAAaa,GAAGpB,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UA5apC,SA4a4DlO,KAAKsG,QAAQtO,UAAUiH,GAASe,KAAK+H,OAAO9I,UAC/G,GApbU,WAobN0C,EAA4B,CACrC,MAAMg2B,EAAUh2B,IAAY8yB,GAC1Bz0B,KAAKwF,YAAY0I,UA5aF,cA6aflO,KAAKwF,YAAY0I,UA/aL,WAgbR0pB,EAAWj2B,IAAY8yB,GAC3Bz0B,KAAKwF,YAAY0I,UA9aF,cA+aflO,KAAKwF,YAAY0I,UAjbJ,YAmbf3N,EAAaa,GAAGpB,KAAKqG,SAAUsxB,EAAS33B,KAAKsG,QAAQtO,UAAUiH,IAC7D,MAAMurB,EAAUxqB,KAAKi2B,6BAA6Bh3B,GAClDurB,EAAQkL,eAA8B,YAAfz2B,EAAMwB,KAAqBi0B,GAAgBD,KAAiB,EACnFjK,EAAQ4L,YAEV71B,EAAaa,GAAGpB,KAAKqG,SAAUuxB,EAAU53B,KAAKsG,QAAQtO,UAAUiH,IAC9D,MAAMurB,EAAUxqB,KAAKi2B,6BAA6Bh3B,GAClDurB,EAAQkL,eAA8B,aAAfz2B,EAAMwB,KAAsBi0B,GAAgBD,IACjEjK,EAAQnkB,SAASpM,SAASgF,EAAMY,eAElC2qB,EAAQ6L,YAKdr2B,KAAKs2B,kBAAoB,KACnBt2B,KAAKqG,UACPrG,KAAK2P,QAITpP,EAAaa,GAAGpB,KAAKqG,SAAS3M,QAAQ66B,IAAiBC,GAAkBx0B,KAAKs2B,mBAE1Et2B,KAAKsG,QAAQtO,SACfgI,KAAKsG,QAAU,IACVtG,KAAKsG,QACR3E,QAAS,SACT3J,SAAU,IAGZgI,KAAK63B,YAITA,YACE,MAAM3C,EAAQl1B,KAAKsG,QAAQwxB,cAEtB5C,IAIAl1B,KAAKqG,SAASpO,aAAa,eAAkB+H,KAAKqG,SAASstB,aAC9D3zB,KAAKqG,SAASlC,aAAa,aAAc+wB,GAG3Cl1B,KAAKqG,SAAShC,gBAAgB,UAGhC+xB,SACMp2B,KAAK0P,YAAc1P,KAAKy1B,WAC1Bz1B,KAAKy1B,YAAa,GAIpBz1B,KAAKy1B,YAAa,EAElBz1B,KAAK+3B,aAAY,KACX/3B,KAAKy1B,YACPz1B,KAAK4P,SAEN5P,KAAKsG,QAAQ6uB,MAAMvlB,OAGxBymB,SACMr2B,KAAKm2B,yBAITn2B,KAAKy1B,YAAa,EAElBz1B,KAAK+3B,aAAY,KACV/3B,KAAKy1B,YACRz1B,KAAK2P,SAEN3P,KAAKsG,QAAQ6uB,MAAMxlB,OAGxBooB,YAAYh7B,EAASi7B,GACnBxqB,aAAaxN,KAAKw1B,UAClBx1B,KAAKw1B,SAAWt4B,WAAWH,EAASi7B,GAGtC7B,uBACE,OAAOr3B,OAAOC,OAAOiB,KAAK01B,gBAAgBv9B,UAAS,GAGrD+M,WAAWC,GACT,MAAM8yB,EAAiBh0B,EAAYK,kBAAkBtE,KAAKqG,UAE1D,IAAK,MAAM6xB,KAAiBp5B,OAAOqC,KAAK82B,GAClC5D,GAAsB50B,IAAIy4B,WACrBD,EAAeC,GAW1B,OAPA/yB,EAAS,IACJ8yB,KACmB,iBAAX9yB,GAAuBA,EAASA,EAAS,IAEtDA,EAASnF,KAAKoF,gBAAgBD,GAC9BA,EAASnF,KAAKqF,kBAAkBF,GAChCnF,KAAKsF,iBAAiBH,GACfA,EAGTE,kBAAkBF,GAoBhB,OAnBAA,EAAOiwB,WAAiC,IAArBjwB,EAAOiwB,UAAsB58B,SAASwC,KAAO9B,EAAWiM,EAAOiwB,WAEtD,iBAAjBjwB,EAAOgwB,QAChBhwB,EAAOgwB,MAAQ,CACbvlB,KAAMzK,EAAOgwB,MACbxlB,KAAMxK,EAAOgwB,QAIjBhwB,EAAO2yB,cAAgB93B,KAAKqG,SAASpO,aAAa,UAAY,GAC9DkN,EAAO+vB,MAAQl1B,KAAK6yB,yBAAyB1tB,EAAO+vB,QAAU/vB,EAAO2yB,cACzC,iBAAjB3yB,EAAO+vB,QAChB/vB,EAAO+vB,MAAQ/vB,EAAO+vB,MAAMzxB,YAGA,iBAAnB0B,EAAOktB,UAChBltB,EAAOktB,QAAUltB,EAAOktB,QAAQ5uB,YAG3B0B,EAGTqyB,qBACE,MAAMryB,EAAS,GAEf,IAAK,MAAM7C,KAAOtC,KAAKsG,QACjBtG,KAAKwF,YAAYT,QAAQzC,KAAStC,KAAKsG,QAAQhE,KACjD6C,EAAO7C,GAAOtC,KAAKsG,QAAQhE,IAO/B,OAAO6C,EAGToxB,iBACMv2B,KAAKqpB,UACPrpB,KAAKqpB,QAAQtB,UACb/nB,KAAKqpB,QAAU,MAKGviB,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAO2tB,GAAQhuB,oBAAoBtH,KAAMmF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UASX/J,EAAmBk6B,IC5nBnB,MAKMvwB,GAAU,IACXuwB,GAAQvwB,QACXyM,UAAW,QACXyG,OAAQ,CAAC,EAAG,GACZtW,QAAS,QACT0wB,QAAS,GACTD,SAAU,+IAONptB,GAAc,IACfswB,GAAQtwB,YACXqtB,QAAS,kCAOX,MAAM8F,WAAgB7C,GAETvwB,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAtCS,UA0CX+6B,iBACE,OAAOx2B,KAAK42B,aAAe52B,KAAKo4B,cAIlCtB,yBACE,MAAO,CACL,kBAAkB92B,KAAK42B,YACvB,gBAAoB52B,KAAKo4B,eAI7BA,cACE,OAAOp4B,KAAK6yB,yBAAyB7yB,KAAKsG,QAAQ+rB,SAI9BvrB,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOwwB,GAAQ7wB,oBAAoBtH,KAAMmF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UASX/J,EAAmB+8B,IC9EnB,MAMME,GAAe,qBAIfptB,GAAoB,SAGpBqtB,GAAwB,SASxBvzB,GAAU,CACdkT,OAAQ,KACRsgB,WAAY,eACZC,cAAc,EACdx7B,OAAQ,MAGJgI,GAAc,CAClBiT,OAAQ,gBACRsgB,WAAY,SACZC,aAAc,UACdx7B,OAAQ,WAOV,MAAMy7B,WAAkBtyB,EACtBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAGfnF,KAAK04B,aAAe,IAAI/1B,IACxB3C,KAAK24B,oBAAsB,IAAIh2B,IAC/B3C,KAAK44B,aAA6D,YAA9Cr/B,iBAAiByG,KAAKqG,UAAUyV,UAA0B,KAAO9b,KAAKqG,SAC1FrG,KAAK64B,cAAgB,KACrB74B,KAAK84B,UAAY,KACjB94B,KAAK+4B,oBAAsB,CACzBC,gBAAiB,EACjBC,gBAAiB,GAEnBj5B,KAAKk5B,UAIIn0B,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAnES,YAuEXy9B,UACEl5B,KAAKm5B,mCACLn5B,KAAKo5B,2BAEDp5B,KAAK84B,UACP94B,KAAK84B,UAAUO,aAEfr5B,KAAK84B,UAAY94B,KAAKs5B,kBAGxB,IAAK,MAAMC,KAAWv5B,KAAK24B,oBAAoB55B,SAC7CiB,KAAK84B,UAAUU,QAAQD,GAI3B/yB,UACExG,KAAK84B,UAAUO,aACfjzB,MAAMI,UAIRnB,kBAAkBF,GAIhB,OAFAA,EAAOnI,OAAS9D,EAAWiM,EAAOnI,SAAWxE,SAASwC,KAE/CmK,EAGTi0B,2BACOp5B,KAAKsG,QAAQkyB,eAKlBj4B,EAAaC,IAAIR,KAAKsG,QAAQtJ,OAAQq7B,IAEtC93B,EAAaa,GAAGpB,KAAKsG,QAAQtJ,OAAQq7B,GAAaC,IAAuBr5B,IACvE,MAAMw6B,EAAoBz5B,KAAK24B,oBAAoBn2B,IAAIvD,EAAMjC,OAAOge,MACpE,GAAIye,EAAmB,CACrBx6B,EAAMwD,iBACN,MAAMjI,EAAOwF,KAAK44B,cAAgB99B,OAC5B2Z,EAASglB,EAAkB1kB,UAAY/U,KAAKqG,SAAS0O,UAC3D,GAAIva,EAAKk/B,SAEP,YADAl/B,EAAKk/B,SAAS,CAAEjpB,IAAKgE,IAKvBja,EAAK+gB,UAAY9G,OAKvB6kB,kBACE,MAAM5lB,EAAU,CACdlZ,KAAMwF,KAAK44B,aACXe,UAAW,CAAC,GAAK,GAAK,GACtBpB,WAAYv4B,KAAK45B,kBAGnB,OAAO,IAAIC,sBAAqBvG,GAAWtzB,KAAK85B,kBAAkBxG,IAAU5f,GAI9EomB,kBAAkBxG,GAChB,MAAMyG,EAAgBrH,GAAS1yB,KAAK04B,aAAal2B,IAAK,IAAGkwB,EAAM11B,OAAOg9B,MAChEtM,EAAWgF,IACf1yB,KAAK+4B,oBAAoBC,gBAAkBtG,EAAM11B,OAAO+X,UACxD/U,KAAKi6B,SAASF,EAAcrH,KAGxBuG,GAAmBj5B,KAAK44B,cAAgBpgC,SAAS6B,iBAAiBkhB,UAClE2e,EAAkBjB,GAAmBj5B,KAAK+4B,oBAAoBE,gBACpEj5B,KAAK+4B,oBAAoBE,gBAAkBA,EAE3C,IAAK,MAAMvG,KAASY,EAAS,CAC3B,IAAKZ,EAAMyH,eAAgB,CACzBn6B,KAAK64B,cAAgB,KACrB74B,KAAKo6B,kBAAkBL,EAAcrH,IAErC,SAGF,MAAM2H,EAA2B3H,EAAM11B,OAAO+X,WAAa/U,KAAK+4B,oBAAoBC,gBAEpF,GAAIkB,GAAmBG,GAGrB,GAFA3M,EAASgF,IAEJuG,EACH,YAOCiB,GAAoBG,GACvB3M,EAASgF,IAMfkH,iBACE,OAAO55B,KAAKsG,QAAQ2R,OAAU,GAAEjY,KAAKsG,QAAQ2R,oBAAsBjY,KAAKsG,QAAQiyB,WAGlFY,mCACEn5B,KAAK04B,aAAe,IAAI/1B,IACxB3C,KAAK24B,oBAAsB,IAAIh2B,IAE/B,MAAM23B,EAAcryB,EAAejJ,KAAKs5B,GAAuBt4B,KAAKsG,QAAQtJ,QAE5E,IAAK,MAAMu9B,KAAUD,EAAa,CAEhC,IAAKC,EAAOvf,MAAQnhB,EAAW0gC,GAC7B,SAGF,MAAMd,EAAoBxxB,EAAeG,QAAQmyB,EAAOvf,KAAMhb,KAAKqG,UAG/DjN,EAAUqgC,KACZz5B,KAAK04B,aAAa71B,IAAI03B,EAAOvf,KAAMuf,GACnCv6B,KAAK24B,oBAAoB91B,IAAI03B,EAAOvf,KAAMye,KAKhDQ,SAASj9B,GACHgD,KAAK64B,gBAAkB77B,IAI3BgD,KAAKo6B,kBAAkBp6B,KAAKsG,QAAQtJ,QACpCgD,KAAK64B,cAAgB77B,EACrBA,EAAOhD,UAAUuQ,IAAIU,IACrBjL,KAAKw6B,iBAAiBx9B,GAEtBuD,EAAaoB,QAAQ3B,KAAKqG,SA7MN,wBA6MgC,CAAExG,cAAe7C,KAGvEw9B,iBAAiBx9B,GAEf,GAAIA,EAAOhD,UAAUC,SA9MQ,iBA+M3BgO,EAAeG,QApMY,mBAoMsBpL,EAAOtD,QArMpC,cAsMjBM,UAAUuQ,IAAIU,SAInB,IAAK,MAAMwvB,KAAaxyB,EAAeO,QAAQxL,EA/MnB,qBAkN1B,IAAK,MAAM09B,KAAQzyB,EAAeS,KAAK+xB,EA9MhB,sDA+MrBC,EAAK1gC,UAAUuQ,IAAIU,IAKzBmvB,kBAAkBprB,GAChBA,EAAOhV,UAAUqJ,OAAO4H,IAExB,MAAM0vB,EAAc1yB,EAAejJ,KAAM,gBAAgDgQ,GACzF,IAAK,MAAMuD,KAAQooB,EACjBpoB,EAAKvY,UAAUqJ,OAAO4H,IAKJnE,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAO8wB,GAAUnxB,oBAAoBtH,KAAMmF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UASX5E,EAAaa,GAAGtG,OA9Pa,8BA8PgB,KAC3C,IAAK,MAAM8/B,KAAO3yB,EAAejJ,KA1PT,0BA2PtBy5B,GAAUnxB,oBAAoBszB,MAQlCx/B,EAAmBq9B,IC/QnB,MAYMoC,GAAiB,YACjBC,GAAkB,aAClBzS,GAAe,UACfC,GAAiB,YAEjBrd,GAAoB,SACpBqpB,GAAkB,OAClBzlB,GAAkB,OAWlBhH,GAAuB,2EACvBkzB,GAAuB,gHAAqBlzB,KAQlD,MAAMmzB,WAAY70B,EAChBX,YAAYzN,GACVqO,MAAMrO,GACNiI,KAAKspB,QAAUtpB,KAAKqG,SAAS3M,QAfN,uCAiBlBsG,KAAKspB,UAOVtpB,KAAKi7B,sBAAsBj7B,KAAKspB,QAAStpB,KAAKk7B,gBAE9C36B,EAAaa,GAAGpB,KAAKqG,SA5CF,kBA4C2BpH,GAASe,KAAKmN,SAASlO,MAI5DxD,kBACT,MA1DS,MA8DXmU,OACE,MAAMurB,EAAYn7B,KAAKqG,SACvB,GAAIrG,KAAKo7B,cAAcD,GACrB,OAIF,MAAME,EAASr7B,KAAKs7B,iBAEdC,EAAYF,EAChB96B,EAAaoB,QAAQ05B,EApEP,cAoE2B,CAAEx7B,cAAes7B,IAC1D,KAEgB56B,EAAaoB,QAAQw5B,EArEvB,cAqE8C,CAAEt7B,cAAew7B,IAEjEr5B,kBAAqBu5B,GAAaA,EAAUv5B,mBAI1DhC,KAAKw7B,YAAYH,EAAQF,GACzBn7B,KAAKy7B,UAAUN,EAAWE,IAI5BI,UAAU1jC,EAAS2jC,GACjB,IAAK3jC,EACH,OAGFA,EAAQiC,UAAUuQ,IAAIU,IAEtBjL,KAAKy7B,UAAU/iC,EAAuBX,IAEtC,MAAM8O,EAAa9O,EAAQiC,UAAUC,SAASq6B,IAmB9Ct0B,KAAK4G,gBAlBY,KACXC,GACF9O,EAAQiC,UAAUuQ,IAAIsE,IAGa,QAAjC9W,EAAQE,aAAa,UAIzBF,EAAQ4xB,QACR5xB,EAAQsM,gBAAgB,YACxBtM,EAAQoM,aAAa,iBAAiB,GACtCnE,KAAK27B,gBAAgB5jC,GAAS,GAC9BwI,EAAaoB,QAAQ5J,EAtGN,eAsG4B,CACzC8H,cAAe67B,OAIW3jC,EAAS8O,GAGzC20B,YAAYzjC,EAAS2jC,GACnB,IAAK3jC,EACH,OAGFA,EAAQiC,UAAUqJ,OAAO4H,IACzBlT,EAAQg4B,OAER/vB,KAAKw7B,YAAY9iC,EAAuBX,IAExC,MAAM8O,EAAa9O,EAAQiC,UAAUC,SAASq6B,IAgB9Ct0B,KAAK4G,gBAfY,KACXC,GACF9O,EAAQiC,UAAUqJ,OAAOwL,IAGU,QAAjC9W,EAAQE,aAAa,UAIzBF,EAAQoM,aAAa,iBAAiB,GACtCpM,EAAQoM,aAAa,WAAY,MACjCnE,KAAK27B,gBAAgB5jC,GAAS,GAC9BwI,EAAaoB,QAAQ5J,EAvIL,gBAuI4B,CAAE8H,cAAe67B,OAGjC3jC,EAAS8O,GAGzCsG,SAASlO,GACP,IAAM,CAAC47B,GAAgBC,GAAiBzS,GAAcC,IAAgBnwB,SAAS8G,EAAMqD,KACnF,OAGFrD,EAAM8rB,kBACN9rB,EAAMwD,iBACN,MAAMqL,EAAS,CAACgtB,GAAiBxS,IAAgBnwB,SAAS8G,EAAMqD,KAC1Ds5B,EAAoBz+B,EAAqB6C,KAAKk7B,eAAex2B,QAAO3M,IAAY8B,EAAW9B,KAAWkH,EAAMjC,OAAQ8Q,GAAQ,GAE9H8tB,GACFZ,GAAI1zB,oBAAoBs0B,GAAmBhsB,OAI/CsrB,eACE,OAAOjzB,EAAejJ,KAAK+7B,GAAqB/6B,KAAKspB,SAGvDgS,iBACE,OAAOt7B,KAAKk7B,eAAel8B,MAAKsJ,GAAStI,KAAKo7B,cAAc9yB,MAAW,KAGzE2yB,sBAAsBjsB,EAAQ3G,GAC5BrI,KAAK67B,yBAAyB7sB,EAAQ,OAAQ,WAE9C,IAAK,MAAM1G,KAASD,EAClBrI,KAAK87B,6BAA6BxzB,GAItCwzB,6BAA6BxzB,GAC3BA,EAAQtI,KAAK+7B,iBAAiBzzB,GAC9B,MAAM0zB,EAAWh8B,KAAKo7B,cAAc9yB,GAC9B2zB,EAAYj8B,KAAKk8B,iBAAiB5zB,GACxCA,EAAMnE,aAAa,gBAAiB63B,GAEhCC,IAAc3zB,GAChBtI,KAAK67B,yBAAyBI,EAAW,OAAQ,gBAG9CD,GACH1zB,EAAMnE,aAAa,WAAY,MAGjCnE,KAAK67B,yBAAyBvzB,EAAO,OAAQ,OAG7CtI,KAAKm8B,mCAAmC7zB,GAG1C6zB,mCAAmC7zB,GACjC,MAAMtL,EAAStE,EAAuB4P,GAEjCtL,IAILgD,KAAK67B,yBAAyB7+B,EAAQ,OAAQ,YAE1CsL,EAAM0xB,IACRh6B,KAAK67B,yBAAyB7+B,EAAQ,kBAAoB,IAAGsL,EAAM0xB,OAIvE2B,gBAAgB5jC,EAASqkC,GACvB,MAAMH,EAAYj8B,KAAKk8B,iBAAiBnkC,GACxC,IAAKkkC,EAAUjiC,UAAUC,SAjMN,YAkMjB,OAGF,MAAM8N,EAAS,CAAC/P,EAAUy0B,KACxB,MAAM10B,EAAUkQ,EAAeG,QAAQpQ,EAAUikC,GAC7ClkC,GACFA,EAAQiC,UAAU+N,OAAO0kB,EAAW2P,IAIxCr0B,EA1M6B,mBA0MIkD,IACjClD,EA1M2B,iBA0MI8G,IAC/B9G,EA1M2B,iBA0MIkD,IAC/BgxB,EAAU93B,aAAa,gBAAiBi4B,GAG1CP,yBAAyB9jC,EAASgc,EAAWvQ,GACtCzL,EAAQoC,aAAa4Z,IACxBhc,EAAQoM,aAAa4P,EAAWvQ,GAIpC43B,cAAc/rB,GACZ,OAAOA,EAAKrV,UAAUC,SAASgR,IAIjC8wB,iBAAiB1sB,GACf,OAAOA,EAAK9G,QAAQwyB,IAAuB1rB,EAAOpH,EAAeG,QAAQ2yB,GAAqB1rB,GAIhG6sB,iBAAiB7sB,GACf,OAAOA,EAAK3V,QA3NO,gCA2NoB2V,EAInBvI,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOqzB,GAAI1zB,oBAAoBtH,MAErC,GAAsB,iBAAXmF,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UASX5E,EAAaa,GAAG5I,SAxQc,eAwQkBqP,IAAsB,SAAU5I,GAC1E,CAAC,IAAK,QAAQ9G,SAAS6H,KAAKqH,UAC9BpI,EAAMwD,iBAGJ5I,EAAWmG,OAIfg7B,GAAI1zB,oBAAoBtH,MAAM4P,UAMhCrP,EAAaa,GAAGtG,OArRa,eAqRgB,KAC3C,IAAK,MAAM/C,KAAWkQ,EAAejJ,KA/PF,iGAgQjCg8B,GAAI1zB,oBAAoBvP,MAO5BqD,EAAmB4/B,ICxSnB,MAcMqB,GAAkB,OAClBxtB,GAAkB,OAClB6gB,GAAqB,UAErB1qB,GAAc,CAClBiwB,UAAW,UACXqH,SAAU,UACVnH,MAAO,UAGHpwB,GAAU,CACdkwB,WAAW,EACXqH,UAAU,EACVnH,MAAO,KAOT,MAAMoH,WAAcp2B,EAClBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAKw1B,SAAW,KAChBx1B,KAAKw8B,sBAAuB,EAC5Bx8B,KAAKy8B,yBAA0B,EAC/Bz8B,KAAK61B,gBAII9wB,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAtDS,QA0DXmU,OACoBrP,EAAaoB,QAAQ3B,KAAKqG,SAjD5B,iBAmDFrE,mBAIdhC,KAAK08B,gBAED18B,KAAKsG,QAAQ2uB,WACfj1B,KAAKqG,SAASrM,UAAUuQ,IAvDN,QAiEpBvK,KAAKqG,SAASrM,UAAUqJ,OAAOg5B,IAC/B1hC,EAAOqF,KAAKqG,UACZrG,KAAKqG,SAASrM,UAAUuQ,IAAIsE,GAAiB6gB,IAE7C1vB,KAAK4G,gBAXY,KACf5G,KAAKqG,SAASrM,UAAUqJ,OAAOqsB,IAC/BnvB,EAAaoB,QAAQ3B,KAAKqG,SA9DX,kBAgEfrG,KAAK28B,uBAOuB38B,KAAKqG,SAAUrG,KAAKsG,QAAQ2uB,YAG5DtlB,OACO3P,KAAKu3B,YAIQh3B,EAAaoB,QAAQ3B,KAAKqG,SAlF5B,iBAoFFrE,mBAUdhC,KAAKqG,SAASrM,UAAUuQ,IAAImlB,IAC5B1vB,KAAK4G,gBAPY,KACf5G,KAAKqG,SAASrM,UAAUuQ,IAAI8xB,IAC5Br8B,KAAKqG,SAASrM,UAAUqJ,OAAOqsB,GAAoB7gB,IACnDtO,EAAaoB,QAAQ3B,KAAKqG,SA1FV,qBA8FYrG,KAAKqG,SAAUrG,KAAKsG,QAAQ2uB,aAG5DzuB,UACExG,KAAK08B,gBAED18B,KAAKu3B,WACPv3B,KAAKqG,SAASrM,UAAUqJ,OAAOwL,IAGjCzI,MAAMI,UAGR+wB,UACE,OAAOv3B,KAAKqG,SAASrM,UAAUC,SAAS4U,IAK1C8tB,qBACO38B,KAAKsG,QAAQg2B,WAIdt8B,KAAKw8B,sBAAwBx8B,KAAKy8B,0BAItCz8B,KAAKw1B,SAAWt4B,YAAW,KACzB8C,KAAK2P,SACJ3P,KAAKsG,QAAQ6uB,SAGlByH,eAAe39B,EAAO49B,GACpB,OAAQ59B,EAAMwB,MACZ,IAAK,YACL,IAAK,WACHT,KAAKw8B,qBAAuBK,EAC5B,MACF,IAAK,UACL,IAAK,WACH78B,KAAKy8B,wBAA0BI,EAMnC,GAAIA,EAEF,YADA78B,KAAK08B,gBAIP,MAAM3uB,EAAc9O,EAAMY,cACtBG,KAAKqG,WAAa0H,GAAe/N,KAAKqG,SAASpM,SAAS8T,IAI5D/N,KAAK28B,qBAGP9G,gBACEt1B,EAAaa,GAAGpB,KAAKqG,SAhKA,sBAgK2BpH,GAASe,KAAK48B,eAAe39B,GAAO,KACpFsB,EAAaa,GAAGpB,KAAKqG,SAhKD,qBAgK2BpH,GAASe,KAAK48B,eAAe39B,GAAO,KACnFsB,EAAaa,GAAGpB,KAAKqG,SAhKF,oBAgK2BpH,GAASe,KAAK48B,eAAe39B,GAAO,KAClFsB,EAAaa,GAAGpB,KAAKqG,SAhKD,qBAgK2BpH,GAASe,KAAK48B,eAAe39B,GAAO,KAGrFy9B,gBACElvB,aAAaxN,KAAKw1B,UAClBx1B,KAAKw1B,SAAW,KAII1uB,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAO40B,GAAMj1B,oBAAoBtH,KAAMmF,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQnF,kBAUrBiH,EAAqBs1B,IAMrBnhC,EAAmBmhC,ICrMJ,CACbh1B,MAAAA,EACAO,OAAAA,EACA6D,SAAAA,GACAsD,SAAAA,GACAma,SAAAA,GACA+E,MAAAA,GACA2B,UAAAA,GACAqI,QAAAA,GACAM,UAAAA,GACAuC,IAAAA,GACAuB,MAAAA,GACAjH,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(object)\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getElementFromSelector,\n  getjQuery,\n  getNextActiveElement,\n  getSelectorFromElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        event.delegateTarget = target\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.originalHandler === handler && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFunction : handler\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFunction\n    delegationFunction = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFunction) {\n      delegationFunction = wrapFunction(delegationFunction)\n    } else {\n      handler = wrapFunction(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFunction) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const handlerKey of Object.keys(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    for (const keyHandlers of Object.keys(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = new Event(event, { bubbles, cancelable: true })\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      for (const key of Object.keys(args)) {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      }\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isElement, toType } from './index'\nimport Manipulator from '../dom/manipulator'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const property of Object.keys(configTypes)) {\n      const expectedTypes = configTypes[property]\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport { executeAfterTransition, getElement } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Config from './util/config'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.2.0-beta1'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible } from '../util/index'\n\n/**\n * Constants\n */\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Config from './config'\nimport EventHandler from '../dom/event-handler'\nimport { execute } from './index'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  leftCallback: null,\n  rightCallback: null,\n  endCallback: null\n}\n\nconst DefaultType = {\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)',\n  endCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport Swipe from './util/swipe'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  ride: '(boolean|string)',\n  pause: '(string|boolean)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // todo: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  getSelectorFromElement,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  toggle: true,\n  parent: null\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(null|element)'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  for (const element of selectorElements) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "import { isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nexport default function getBoundingClientRect(element, includeScale) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  var rect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (isHTMLElement(element) && includeScale) {\n    var offsetHeight = element.offsetHeight;\n    var offsetWidth = element.offsetWidth; // Do not attempt to divide by 0, otherwise we get `Infinity` as scale\n    // Fallback to 1 in case both values are `0`\n\n    if (offsetWidth > 0) {\n      scaleX = round(rect.width) / offsetWidth || 1;\n    }\n\n    if (offsetHeight > 0) {\n      scaleY = round(rect.height) / offsetHeight || 1;\n    }\n  }\n\n  return {\n    width: rect.width / scaleX,\n    height: rect.height / scaleY,\n    top: rect.top / scaleY,\n    right: rect.right / scaleX,\n    bottom: rect.bottom / scaleY,\n    left: rect.left / scaleX,\n    x: rect.left / scaleX,\n    y: rect.top / scaleY\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') !== -1;\n  var isIE = navigator.userAgent.indexOf('Trident') !== -1;\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (process.env.NODE_ENV !== \"production\") {\n    if (!isHTMLElement(arrowElement)) {\n      console.error(['Popper: \"arrow\" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: \"arrow\" modifier\\'s `element` must be a child of the popper', 'element.'].join(' '));\n    }\n\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref) {\n  var x = _ref.x,\n      y = _ref.y;\n  var win = window;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n\n  if (process.env.NODE_ENV !== \"production\") {\n    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';\n\n    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {\n      return transitionProperty.indexOf(property) >= 0;\n    })) {\n      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: \"transform\", \"top\", \"right\", \"bottom\", \"left\".', '\\n\\n', 'Disable the \"computeStyles\" modifier\\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\\n\\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));\n    }\n  }\n\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element) {\n  var rect = getBoundingClientRect(element);\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nexport default function getViewportRect(element) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0; // NB: This isn't supported on iOS <= 12. If the keyboard is open, the popper\n  // can be obscured underneath it.\n  // Also, `html.clientHeight` adds the bottom bar height in Safari iOS, even\n  // if it isn't open, so if this isn't available, the popper will be detected\n  // to overflow the bottom of the screen too early.\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height; // Uses Layout Viewport (like Chrome; Safari does not currently)\n    // In Chrome, it returns a value very close to 0 (+/-) but contains rounding\n    // errors due to floating point numbers, so we need to check precision.\n    // Safari returns a number <= 0, usually < -1 when pinch-zoomed\n    // Feature detection fails in mobile emulation mode in Chrome.\n    // Math.abs(win.innerWidth / visualViewport.scale - visualViewport.width) <\n    // 0.001\n    // Fallback here: \"Not Safari\" userAgent\n\n    if (!/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, \"auto\" cannot be used to allow \"bottom-start\".', 'Use \"auto-start\" instead.'].join(' '));\n    }\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport getComputedStyle from \"./dom-utils/getComputedStyle.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport validateModifiers from \"./utils/validateModifiers.js\";\nimport uniqueBy from \"./utils/uniqueBy.js\";\nimport getBasePlacement from \"./utils/getBasePlacement.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nimport { auto } from \"./enums.js\";\nvar INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';\nvar INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        }); // Validate the provided modifiers so that the consumer will get warned\n        // if one of the modifiers is invalid for any reason\n\n        if (process.env.NODE_ENV !== \"production\") {\n          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {\n            var name = _ref.name;\n            return name;\n          });\n          validateModifiers(modifiers);\n\n          if (getBasePlacement(state.options.placement) === auto) {\n            var flipModifier = state.orderedModifiers.find(function (_ref2) {\n              var name = _ref2.name;\n              return name === 'flip';\n            });\n\n            if (!flipModifier) {\n              console.error(['Popper: \"auto\" placements require the \"flip\" modifier be', 'present and enabled to work.'].join(' '));\n            }\n          }\n\n          var _getComputedStyle = getComputedStyle(popper),\n              marginTop = _getComputedStyle.marginTop,\n              marginRight = _getComputedStyle.marginRight,\n              marginBottom = _getComputedStyle.marginBottom,\n              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can\n          // cause bugs with positioning, so we'll warn the consumer\n\n\n          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {\n            return parseFloat(margin);\n          })) {\n            console.warn(['Popper: CSS \"margin\" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));\n          }\n        }\n\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          if (process.env.NODE_ENV !== \"production\") {\n            console.error(INVALID_ELEMENT_ERROR);\n          }\n\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n        var __debug_loops__ = 0;\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (process.env.NODE_ENV !== \"production\") {\n            __debug_loops__ += 1;\n\n            if (__debug_loops__ > 100) {\n              console.error(INFINITE_LOOP_ERROR);\n              break;\n            }\n          }\n\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      if (process.env.NODE_ENV !== \"production\") {\n        console.error(INVALID_ELEMENT_ERROR);\n      }\n\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref3) {\n        var name = _ref3.name,\n            _ref3$options = _ref3.options,\n            options = _ref3$options === void 0 ? {} : _ref3$options,\n            effect = _ref3.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    this._menu = SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // todo:v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    const getToggleButton = SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode)\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow } from './index'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  className: 'string',\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  trapElement: null, // The element to trap focus inside of\n  autofocus: true\n}\n\nconst DefaultType = {\n  trapElement: 'element',\n  autofocus: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, isRTL, isVisible, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    for (const htmlElement of [window, this._dialog]) {\n      EventHandler.off(htmlElement, EVENT_KEY)\n    }\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        event.preventDefault()\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (event.target !== event.currentTarget) { // click is inside modal-dialog\n        return\n      }\n\n      if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n        return\n      }\n\n      if (this._config.backdrop) {\n        this.hide()\n      }\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (!this._config.keyboard) {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer'\nimport { getElement, isElement } from '../util/index'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  extraClass: '',\n  template: '<div></div>',\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist\n}\n\nconst DefaultType = {\n  extraClass: '(string|function)',\n  template: 'string',\n  content: 'object',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object'\n}\n\nconst DefaultContentType = {\n  selector: '(string|element)',\n  entry: '(string|element|function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg(this) : arg\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport { defineJQueryPlugin, findShadowRoot, getElement, getUID, isRTL, noop } from './util/index'\nimport { DefaultAllowlist } from './util/sanitizer'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\nimport TemplateFactory from './util/template-factory'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = false\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter()\n      } else {\n        context._leave()\n      }\n\n      return\n    }\n\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._createPopper(tip)\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      const previousHoverState = this._isHovered\n\n      this._isHovered = false\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (previousHoverState) {\n        this._leave()\n      }\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = false\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        tip.remove()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n\n      this._disposePopper()\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // todo: remove this check on v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // todo: on v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    let isShown = false\n    if (this.tip) {\n      isShown = this._isShown()\n      this.tip.remove()\n      this.tip = null\n    }\n\n    this._disposePopper()\n    this.tip = this._createTipElement(content)\n\n    if (isShown) {\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._config.title\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg.call(this._element) : arg\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._config.originalTitle\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    config.originalTitle = this._element.getAttribute('title') || ''\n    config.title = this._resolvePossibleFunction(config.title) || config.originalTitle\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const key in this._config) {\n      if (this.constructor.Default[key] !== this._config[key]) {\n        config[key] = this._config[key]\n      }\n    }\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Tooltip from './tooltip'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElement, isDisabled, isVisible } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: [0.1, 0.5, 1],\n      rootMargin: this._getRootMargin()\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n  _getRootMargin() {\n    return this._config.offset ? `${this._config.offset}px 0px -30%` : this._config.rootMargin\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(anchor.hash, this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(anchor.hash, anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, getNextActiveElement, isDisabled } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst SELECTOR_DROPDOWN_ITEM = '.dropdown-item'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = ':not(.dropdown-toggle)'\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // todo:v6: could be only `tab`\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // todo: should Throw exception on v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    const complete = () => {\n      if (isAnimated) { // todo: maybe is redundant\n        element.classList.add(CLASS_NAME_SHOW)\n      }\n\n      if (element.getAttribute('role') !== 'tab') {\n        return\n      }\n\n      element.focus()\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, isAnimated)\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    const complete = () => {\n      if (isAnimated) { // todo maybe is redundant\n        element.classList.remove(CLASS_NAME_SHOW)\n      }\n\n      if (element.getAttribute('role') !== 'tab') {\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, isAnimated)\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n    const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n    const nextActiveElement = getNextActiveElement(this._getChildren().filter(element => !isDisabled(element)), event.target, isNext, true)\n\n    if (nextActiveElement) {\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `#${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    toggle(SELECTOR_DROPDOWN_ITEM, CLASS_NAME_ACTIVE)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  <PERSON><PERSON>,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}