<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح مشاكل قاعدة البيانات - salessystem_v2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            min-height: 100vh;
        }
        .fix-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 30px auto;
            max-width: 1000px;
            overflow: hidden;
        }
        .fix-header {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .fix-content {
            padding: 40px;
        }
        .fix-section {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <?php
    require_once __DIR__.'/config/init.php';
    redirectIfNotLoggedIn();

    $username = $_SESSION['username'];
    $user_id = $_SESSION['user_id'];
    $db = getCurrentUserDB();

    $issues_found = [];
    $fixes_applied = [];
    $errors = [];

    // فحص الجداول المطلوبة
    $required_tables = ['customers', 'products', 'sales', 'sale_items', 'purchases', 'purchase_items'];
    $missing_tables = [];

    foreach ($required_tables as $table_type) {
        $table_name = getUserTableName($table_type, $username);
        $check_table = $db->query("SHOW TABLES LIKE '$table_name'");
        
        if (!$check_table || $check_table->num_rows == 0) {
            $missing_tables[] = $table_name;
            $issues_found[] = "جدول مفقود: $table_name";
        }
    }

    // إصلاح الجداول المفقودة إذا تم الطلب
    if (isset($_POST['fix_tables']) && !empty($missing_tables)) {
        // إعادة التوجيه لإنشاء الجداول
        header("Location: create_missing_tables.php?redirect=" . urlencode($_SERVER['PHP_SELF']));
        exit();
    }

    // فحص البيانات
    $data_issues = [];
    
    // فحص جدول العملاء
    $customers_table = getUserTableName('customers', $username);
    if ($db->query("SHOW TABLES LIKE '$customers_table'")->num_rows > 0) {
        $customers_without_user_id = $db->query("SELECT COUNT(*) as count FROM `$customers_table` WHERE user_id IS NULL OR user_id = 0")->fetch_assoc()['count'];
        if ($customers_without_user_id > 0) {
            $data_issues[] = "يوجد $customers_without_user_id عميل بدون user_id صحيح";
        }
    }

    // فحص جدول المنتجات
    $products_table = getUserTableName('products', $username);
    if ($db->query("SHOW TABLES LIKE '$products_table'")->num_rows > 0) {
        $products_without_user_id = $db->query("SELECT COUNT(*) as count FROM `$products_table` WHERE user_id IS NULL OR user_id = 0")->fetch_assoc()['count'];
        if ($products_without_user_id > 0) {
            $data_issues[] = "يوجد $products_without_user_id منتج بدون user_id صحيح";
        }
    }

    // إصلاح البيانات إذا تم الطلب
    if (isset($_POST['fix_data'])) {
        try {
            // إصلاح العملاء
            if ($db->query("SHOW TABLES LIKE '$customers_table'")->num_rows > 0) {
                $db->query("UPDATE `$customers_table` SET user_id = $user_id WHERE user_id IS NULL OR user_id = 0");
                $fixes_applied[] = "تم إصلاح user_id للعملاء";
            }

            // إصلاح المنتجات
            if ($db->query("SHOW TABLES LIKE '$products_table'")->num_rows > 0) {
                $db->query("UPDATE `$products_table` SET user_id = $user_id WHERE user_id IS NULL OR user_id = 0");
                $fixes_applied[] = "تم إصلاح user_id للمنتجات";
            }

            // إصلاح المبيعات
            $sales_table = getUserTableName('sales', $username);
            if ($db->query("SHOW TABLES LIKE '$sales_table'")->num_rows > 0) {
                $db->query("UPDATE `$sales_table` SET user_id = $user_id WHERE user_id IS NULL OR user_id = 0");
                $fixes_applied[] = "تم إصلاح user_id للمبيعات";
            }

            // إصلاح المشتريات
            $purchases_table = getUserTableName('purchases', $username);
            if ($db->query("SHOW TABLES LIKE '$purchases_table'")->num_rows > 0) {
                $db->query("UPDATE `$purchases_table` SET user_id = $user_id WHERE user_id IS NULL OR user_id = 0");
                $fixes_applied[] = "تم إصلاح user_id للمشتريات";
            }

        } catch (Exception $e) {
            $errors[] = "خطأ في إصلاح البيانات: " . $e->getMessage();
        }
    }
    ?>
    
    <div class="container">
        <div class="fix-container">
            <div class="fix-header">
                <h1><i class="fas fa-tools"></i> إصلاح مشاكل قاعدة البيانات</h1>
                <p class="mb-0">فحص وإصلاح المشاكل في قاعدة البيانات</p>
            </div>
            
            <div class="fix-content">
                <?php if (!empty($fixes_applied)): ?>
                <div class="alert alert-success">
                    <h5><i class="fas fa-check-circle"></i> تم تطبيق الإصلاحات التالية:</h5>
                    <ul class="mb-0">
                        <?php foreach ($fixes_applied as $fix): ?>
                        <li><?php echo $fix; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <h5><i class="fas fa-exclamation-triangle"></i> حدثت أخطاء:</h5>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <!-- فحص الجداول -->
                <div class="fix-section">
                    <h3><i class="fas fa-database"></i> فحص الجداول</h3>
                    
                    <?php if (empty($missing_tables)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> جميع الجداول المطلوبة موجودة
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> جداول مفقودة:</h5>
                        <ul class="mb-3">
                            <?php foreach ($missing_tables as $table): ?>
                            <li><?php echo $table; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="fix_tables" class="btn btn-warning">
                                <i class="fas fa-plus"></i> إنشاء الجداول المفقودة
                            </button>
                        </form>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- فحص البيانات -->
                <div class="fix-section">
                    <h3><i class="fas fa-data"></i> فحص البيانات</h3>
                    
                    <?php if (empty($data_issues)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> جميع البيانات سليمة
                    </div>
                    <?php else: ?>
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> مشاكل في البيانات:</h5>
                        <ul class="mb-3">
                            <?php foreach ($data_issues as $issue): ?>
                            <li><?php echo $issue; ?></li>
                            <?php endforeach; ?>
                        </ul>
                        
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="fix_data" class="btn btn-warning">
                                <i class="fas fa-wrench"></i> إصلاح البيانات
                            </button>
                        </form>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- معلومات النظام -->
                <div class="fix-section">
                    <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <th>اسم المستخدم:</th>
                                    <td><?php echo htmlspecialchars($username); ?></td>
                                </tr>
                                <tr>
                                    <th>معرف المستخدم:</th>
                                    <td><?php echo $user_id; ?></td>
                                </tr>
                                <tr>
                                    <th>قاعدة البيانات:</th>
                                    <td><?php echo $db->query("SELECT DATABASE()")->fetch_row()[0]; ?></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>الجداول المطلوبة:</h6>
                            <ul class="list-group list-group-flush">
                                <?php foreach ($required_tables as $table_type): ?>
                                <?php 
                                $table_name = getUserTableName($table_type, $username);
                                $exists = $db->query("SHOW TABLES LIKE '$table_name'")->num_rows > 0;
                                ?>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <?php echo $table_name; ?>
                                    <?php if ($exists): ?>
                                    <span class="badge bg-success rounded-pill">موجود</span>
                                    <?php else: ?>
                                    <span class="badge bg-danger rounded-pill">مفقود</span>
                                    <?php endif; ?>
                                </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="text-center mt-4">
                    <a href="index.php" class="btn btn-success btn-lg me-3">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                    <a href="test_system.php" class="btn btn-info btn-lg me-3">
                        <i class="fas fa-chart-line"></i> تقرير النظام
                    </a>
                    <button onclick="location.reload()" class="btn btn-secondary btn-lg">
                        <i class="fas fa-sync-alt"></i> إعادة فحص
                    </button>
                </div>

                <!-- معلومات إضافية -->
                <div class="mt-4 text-center text-muted">
                    <hr>
                    <p class="mb-0">
                        <small>
                            أداة إصلاح مشاكل قاعدة البيانات - salessystem_v2 | 
                            تاريخ الفحص: <?php echo date('Y-m-d H:i:s'); ?>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
