<?php

class BrowserDetection {
    public function getBrowser($user_agent) {
        // اكتشاف المتصفح
        // يمكنك استخدام UserAgentParser أو مكتبات مماثلة للكشف عن المتصفح
        // يتم استخدام هنا سلسلة نصية بسيطة لأغراض التوضيح فقط
        return "مثال متصفح";
    }

    public function getOperatingSystem($user_agent) {
        // اكتشاف نظام التشغيل
        // يمكنك استخدام UserAgentParser أو مكتبات مماثلة للكشف عن نظام التشغيل
        // يتم استخدام هنا سلسلة نصية بسيطة لأغراض التوضيح فقط
        return "مثال نظام التشغيل";
    }
}
?>