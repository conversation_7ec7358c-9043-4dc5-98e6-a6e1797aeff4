<?php
/**
 * فاحص التقدم للفحص المطول
 * يتم استدعاؤه بشكل دوري من JavaScript للحصول على تحديثات التقدم
 */

// نظام فحص التقدم - متاح للجميع بدون تسجيل دخول
// تم إزالة متطلب تسجيل الدخول لسهولة الوصول

// تعيين header للاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// معالجة الأخطاء
set_error_handler(function($severity, $message, $file, $line) {
    if ($severity === E_ERROR || $severity === E_PARSE) {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في الخادم']);
        exit;
    }
});

set_exception_handler(function($exception) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في معالجة الطلب']);
    exit;
});

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة']);
    exit;
}

$action = $_GET['action'] ?? '';

switch ($action) {
    case 'get_progress':
        getProgress();
        break;
    case 'stop_test':
        stopTest();
        break;
    default:
        http_response_code(400);
        echo json_encode(['error' => 'إجراء غير صحيح']);
        exit;
}

/**
 * الحصول على تقدم الاختبار
 */
function getProgress() {
    // البحث عن أي ملف تقدم موجود
    $progressFiles = glob(sys_get_temp_dir() . '/bcrypt_progress_*.json');
    $progressFile = !empty($progressFiles) ? $progressFiles[0] : sys_get_temp_dir() . '/bcrypt_progress_general.json';
    
    if (!file_exists($progressFile)) {
        echo json_encode([
            'status' => 'not_started',
            'message' => 'لم يبدأ الاختبار بعد'
        ]);
        return;
    }
    
    $progressData = json_decode(file_get_contents($progressFile), true);
    
    if (!$progressData) {
        echo json_encode([
            'status' => 'error',
            'message' => 'خطأ في قراءة ملف التقدم'
        ]);
        return;
    }
    
    // فحص إذا كان الاختبار متوقف (لم يتم تحديث الملف لأكثر من 30 ثانية)
    if (time() - $progressData['timestamp'] > 30) {
        $progressData['status'] = 'stalled';
        $progressData['message'] = 'الاختبار متوقف أو معلق';
    } else {
        $progressData['status'] = 'running';
    }
    
    // إضافة معلومات إضافية
    $progressData['memory_usage_mb'] = round($progressData['memory_usage'] / 1024 / 1024, 2);
    $progressData['peak_memory_mb'] = round($progressData['peak_memory'] / 1024 / 1024, 2);
    
    // حساب السرعة
    if ($progressData['time_elapsed'] > 0) {
        $progressData['attempts_per_second'] = round($progressData['attempts'] / $progressData['time_elapsed'], 2);
    } else {
        $progressData['attempts_per_second'] = 0;
    }
    
    // تقدير الوقت المتبقي
    if ($progressData['attempts_per_second'] > 0 && $progressData['max_attempts'] > 0) {
        $remainingAttempts = $progressData['max_attempts'] - $progressData['attempts'];
        $estimatedTimeRemaining = round($remainingAttempts / $progressData['attempts_per_second']);
        $progressData['estimated_time_remaining'] = $estimatedTimeRemaining;
        $progressData['estimated_time_remaining_formatted'] = formatTime($estimatedTimeRemaining);
    } else {
        $progressData['estimated_time_remaining'] = null;
        $progressData['estimated_time_remaining_formatted'] = 'غير محدد';
    }
    
    echo json_encode($progressData);
}

/**
 * إيقاف الاختبار
 */
function stopTest() {
    $stopFile = sys_get_temp_dir() . '/bcrypt_stop_general.flag';
    file_put_contents($stopFile, time());
    
    echo json_encode([
        'status' => 'stop_requested',
        'message' => 'تم طلب إيقاف الاختبار'
    ]);
}

/**
 * تنسيق الوقت
 */
function formatTime($seconds) {
    if ($seconds < 60) {
        return $seconds . 's';
    } elseif ($seconds < 3600) {
        $minutes = floor($seconds / 60);
        $remainingSeconds = $seconds % 60;
        return $minutes . 'm ' . $remainingSeconds . 's';
    } elseif ($seconds < 86400) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        return $hours . 'h ' . $minutes . 'm';
    } else {
        $days = floor($seconds / 86400);
        $hours = floor(($seconds % 86400) / 3600);
        return $days . 'd ' . $hours . 'h';
    }
}
?>
