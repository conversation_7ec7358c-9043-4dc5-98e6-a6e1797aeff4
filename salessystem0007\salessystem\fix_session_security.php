<?php
/**
 * أداة إصلاح أمان الجلسات
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// معالجة طلب الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_session_security'])) {
    $fixes_applied = [];
    $errors = [];
    
    try {
        // 1. إنشاء ملف .htaccess لإعدادات PHP
        $htaccess_content = "
# إعدادات أمان الجلسات
php_value session.cookie_httponly 1
php_value session.cookie_secure 1
php_value session.use_strict_mode 1
php_value session.cookie_samesite Strict
php_value session.gc_maxlifetime 3600
php_value session.gc_probability 1
php_value session.gc_divisor 100

# إعدادات أمان إضافية
php_value expose_php Off
php_value display_errors Off
php_value log_errors On

# منع الوصول للملفات الحساسة
<Files \"config.php\">
    Order allow,deny
    Deny from all
</Files>

<Files \"*.log\">
    Order allow,deny
    Deny from all
</Files>

# Security Headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection \"1; mode=block\"
    Header always set Strict-Transport-Security \"max-age=31536000; includeSubDomains\"
    Header always set Content-Security-Policy \"default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:\"
    Header always set Referrer-Policy \"strict-origin-when-cross-origin\"
</IfModule>
";
        
        $htaccess_file = __DIR__ . '/.htaccess';
        if (file_put_contents($htaccess_file, $htaccess_content)) {
            $fixes_applied[] = "تم إنشاء ملف .htaccess مع إعدادات الأمان";
        } else {
            $errors[] = "فشل في إنشاء ملف .htaccess";
        }
        
        // 2. تحديث ملف init.php لإعدادات الجلسة
        $init_file = __DIR__ . '/config/init.php';
        if (file_exists($init_file)) {
            $init_content = file_get_contents($init_file);
            
            // إضافة إعدادات الجلسة الآمنة
            $session_config = "
// إعدادات أمان الجلسة
if (session_status() == PHP_SESSION_NONE) {
    // إعدادات الجلسة الآمنة
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
    ini_set('session.gc_maxlifetime', 3600);
    ini_set('session.name', 'SECURE_SESSID');
    
    // بدء الجلسة
    session_start();
    
    // تجديد معرف الجلسة دورياً
    if (!isset(\$_SESSION['last_regeneration'])) {
        \$_SESSION['last_regeneration'] = time();
    } elseif (time() - \$_SESSION['last_regeneration'] > 300) {
        session_regenerate_id(true);
        \$_SESSION['last_regeneration'] = time();
    }
    
    // فحص IP address للحماية من session hijacking
    if (!isset(\$_SESSION['user_ip'])) {
        \$_SESSION['user_ip'] = \$_SERVER['REMOTE_ADDR'];
    } elseif (\$_SESSION['user_ip'] !== \$_SERVER['REMOTE_ADDR']) {
        session_destroy();
        header('Location: login.php?error=session_hijack');
        exit();
    }
    
    // فحص User Agent للحماية من session hijacking
    if (!isset(\$_SESSION['user_agent'])) {
        \$_SESSION['user_agent'] = \$_SERVER['HTTP_USER_AGENT'];
    } elseif (\$_SESSION['user_agent'] !== \$_SERVER['HTTP_USER_AGENT']) {
        session_destroy();
        header('Location: login.php?error=session_hijack');
        exit();
    }
}
";
            
            // إضافة الكود إذا لم يكن موجوداً
            if (strpos($init_content, 'session.cookie_httponly') === false) {
                $init_content = str_replace('<?php', '<?php' . $session_config, $init_content);
                
                if (file_put_contents($init_file, $init_content)) {
                    $fixes_applied[] = "تم تحديث ملف init.php مع إعدادات الجلسة الآمنة";
                } else {
                    $errors[] = "فشل في تحديث ملف init.php";
                }
            } else {
                $fixes_applied[] = "إعدادات الجلسة موجودة بالفعل في init.php";
            }
        }
        
        // 3. إنشاء ملف session helper
        $session_helper_content = '<?php
/**
 * مساعد أمان الجلسات
 */

class SessionSecurity {
    
    /**
     * بدء جلسة آمنة
     */
    public static function startSecureSession() {
        if (session_status() == PHP_SESSION_NONE) {
            // إعدادات الجلسة الآمنة
            ini_set("session.cookie_httponly", 1);
            ini_set("session.cookie_secure", 1);
            ini_set("session.use_strict_mode", 1);
            ini_set("session.cookie_samesite", "Strict");
            ini_set("session.gc_maxlifetime", 3600);
            ini_set("session.name", "SECURE_SESSID");
            
            session_start();
            
            // تجديد معرف الجلسة
            self::regenerateSessionId();
            
            // فحص أمان الجلسة
            self::validateSession();
        }
    }
    
    /**
     * تجديد معرف الجلسة دورياً
     */
    public static function regenerateSessionId() {
        if (!isset($_SESSION["last_regeneration"])) {
            $_SESSION["last_regeneration"] = time();
        } elseif (time() - $_SESSION["last_regeneration"] > 300) {
            session_regenerate_id(true);
            $_SESSION["last_regeneration"] = time();
        }
    }
    
    /**
     * التحقق من صحة الجلسة
     */
    public static function validateSession() {
        // فحص IP address
        if (!isset($_SESSION["user_ip"])) {
            $_SESSION["user_ip"] = $_SERVER["REMOTE_ADDR"];
        } elseif ($_SESSION["user_ip"] !== $_SERVER["REMOTE_ADDR"]) {
            self::destroySession();
            header("Location: login.php?error=session_hijack");
            exit();
        }
        
        // فحص User Agent
        if (!isset($_SESSION["user_agent"])) {
            $_SESSION["user_agent"] = $_SERVER["HTTP_USER_AGENT"];
        } elseif ($_SESSION["user_agent"] !== $_SERVER["HTTP_USER_AGENT"]) {
            self::destroySession();
            header("Location: login.php?error=session_hijack");
            exit();
        }
        
        // فحص انتهاء صلاحية الجلسة
        if (isset($_SESSION["last_activity"]) && (time() - $_SESSION["last_activity"] > 3600)) {
            self::destroySession();
            header("Location: login.php?error=session_expired");
            exit();
        }
        
        $_SESSION["last_activity"] = time();
    }
    
    /**
     * تدمير الجلسة بشكل آمن
     */
    public static function destroySession() {
        $_SESSION = array();
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), "", time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
    }
    
    /**
     * إنشاء CSRF token
     */
    public static function generateCSRFToken() {
        if (!isset($_SESSION["csrf_token"])) {
            $_SESSION["csrf_token"] = bin2hex(random_bytes(32));
        }
        return $_SESSION["csrf_token"];
    }
    
    /**
     * التحقق من CSRF token
     */
    public static function validateCSRFToken($token) {
        return isset($_SESSION["csrf_token"]) && hash_equals($_SESSION["csrf_token"], $token);
    }
    
    /**
     * تسجيل محاولة دخول مشبوهة
     */
    public static function logSuspiciousActivity($activity) {
        $log_entry = date("Y-m-d H:i:s") . " - " . $_SERVER["REMOTE_ADDR"] . " - " . $activity . PHP_EOL;
        file_put_contents(__DIR__ . "/../logs/security.log", $log_entry, FILE_APPEND | LOCK_EX);
    }
}
';
        
        $session_helper_file = __DIR__ . '/includes/session_security.php';
        if (file_put_contents($session_helper_file, $session_helper_content)) {
            $fixes_applied[] = "تم إنشاء ملف session_security.php";
        } else {
            $errors[] = "فشل في إنشاء ملف session_security.php";
        }
        
        // 4. إنشاء مجلد logs إذا لم يكن موجوداً
        $logs_dir = __DIR__ . '/logs';
        if (!is_dir($logs_dir)) {
            if (mkdir($logs_dir, 0755, true)) {
                $fixes_applied[] = "تم إنشاء مجلد logs";
                
                // إنشاء ملف .htaccess لحماية مجلد logs
                $logs_htaccess = $logs_dir . '/.htaccess';
                file_put_contents($logs_htaccess, "Order allow,deny\nDeny from all");
            } else {
                $errors[] = "فشل في إنشاء مجلد logs";
            }
        }
        
        // 5. تحديث ملف functions.php لاستخدام الجلسة الآمنة
        $functions_file = __DIR__ . '/includes/functions.php';
        if (file_exists($functions_file)) {
            $functions_content = file_get_contents($functions_file);
            
            // إضافة require للـ session security
            $require_session = "require_once __DIR__ . '/session_security.php';";
            
            if (strpos($functions_content, 'session_security.php') === false) {
                $functions_content = str_replace('<?php', '<?php' . PHP_EOL . $require_session . PHP_EOL, $functions_content);
                
                if (file_put_contents($functions_file, $functions_content)) {
                    $fixes_applied[] = "تم تحديث ملف functions.php";
                } else {
                    $errors[] = "فشل في تحديث ملف functions.php";
                }
            }
        }
        
        if (!empty($fixes_applied)) {
            $_SESSION['success'] = "تم تطبيق الإصلاحات الأمنية: " . implode(', ', $fixes_applied);
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء تطبيق الإصلاحات: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt"></i>
                        إصلاح أمان الجلسات
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> حول إصلاح أمان الجلسات</h6>
                        <p>هذه الأداة ستقوم بتطبيق الإصلاحات التالية لتحسين أمان الجلسات:</p>
                        <ul>
                            <li>تفعيل <code>session.cookie_httponly</code></li>
                            <li>تفعيل <code>session.cookie_secure</code></li>
                            <li>تفعيل <code>session.use_strict_mode</code></li>
                            <li>إضافة <code>SameSite=Strict</code> للكوكيز</li>
                            <li>تجديد معرف الجلسة دورياً</li>
                            <li>فحص IP address و User Agent</li>
                            <li>إضافة CSRF protection</li>
                            <li>تسجيل الأنشطة المشبوهة</li>
                        </ul>
                    </div>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من تطبيق إصلاحات أمان الجلسات؟')">
                        <div class="text-center">
                            <button type="submit" name="fix_session_security" class="btn btn-primary btn-lg">
                                <i class="fas fa-shield-alt"></i> تطبيق إصلاحات أمان الجلسات
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4">
                        <h6>الفوائد المتوقعة:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> منع session hijacking</li>
                                    <li><i class="fas fa-check text-success"></i> حماية من XSS attacks</li>
                                    <li><i class="fas fa-check text-success"></i> منع CSRF attacks</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> تشفير أقوى للجلسات</li>
                                    <li><i class="fas fa-check text-success"></i> تسجيل الأنشطة المشبوهة</li>
                                    <li><i class="fas fa-check text-success"></i> انتهاء صلاحية تلقائي</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
