-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.5.1
-- http://www.phpmyadmin.net
--
-- Host: 127.0.0.1
-- Generation Time: Jun 07, 2018 at 06:11 PM
-- Server version: 10.1.16-MariaDB
-- PHP Version: 5.6.24

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `itemdb`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin`
--

CREATE TABLE `admin` (
  `admin_id` int(12) UNSIGNED NOT NULL,
  `email` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `firstname` varchar(50) NOT NULL,
  `lastname` varchar(50) NOT NULL,
  `username` varchar(50) DEFAULT NULL,
  `contact_no` varchar(20) DEFAULT NULL,
  `access_level` tinyint(1) NOT NULL,
  `image` varchar(200) DEFAULT NULL,
  `status` tinyint(1) NOT NULL,
  `registered_at` int(11) NOT NULL,
  `verification_code` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `admin`
--

INSERT INTO `admin` (`admin_id`, `email`, `password`, `firstname`, `lastname`, `username`, `contact_no`, `access_level`, `image`, `status`, `registered_at`, `verification_code`) VALUES
(1, '<EMAIL>', '$2y$10$wso1ckAZl0BbRgDIKF.dyepxLiXX4ctvqdx1D3Mwgm.zTRqD6Dd0a', 'Veo Thadeus', 'Calimlim', 'veocalimlim', NULL, 1, '41950298320471c17f6fdea86cfd4ee6.jpg', 1, 1511079552, '97bbe0a44892225f83bd27a7703633cbf3dc8bbdbb025dce3f33c9e03101'),
(2, '<EMAIL>', '$2y$10$dIdroXz22sY4A7PGfJSqTuS3GKBCFGZVj9ULr76NfMrUGPbky.wt.', 'Chris John', 'Agarap', 'seej', '09173673233', 0, '673154b038411e25cabec9c3e15634c4.jpg', 1, 1512515685, 'c794c8ed8feb2ec1ec9896daf0f21cf4fb65fad3f46b5ab7ae6beca5dd5e'),
(3, '<EMAIL>', '$2y$10$Y7Mgo/S446efiPTPlytgGeJ2wHFRy9u.16HYpptGkrRZgwWlipXwm', 'VVilliam', 'Suarez', 'vvilliam', NULL, 1, 'default-user.png', 1, 1511351673, '440ed919305a1bfc73dd80847daf3da345c28c7a6b48e9d1e86094ff0756'),
(4, '<EMAIL>', '$2y$10$yhlwfq3oKSXOb0wEYRDIBevyUllq0myh6mu.8x2.6epqpCHZI2NAq', 'Will To Live', 'Suarez', NULL, NULL, 1, 'default-user.png', 1, 1511351800, 'bddb02650433b0dcdb53f69e34ca6e5e1987f22a8e2436bfe919be44596f'),
(5, '<EMAIL>', '$2y$10$BCu7XVGLJSdzsFGXawJideUjxSUOZKq.zG.c0fuXyvvwJt7ATYziC', '[removed]alert&amp;#40;&#039;jdgashdsa&#039;&amp;#', '&quot;Heelo', NULL, '&lt;a href = &quot;f', 1, 'default-user.png', 0, **********, '9a8560739eedc59ba06a0c9a3142e7756aa81ec1e070ebb61423c059232f'),
(6, '<EMAIL>', '$2y$10$2clbf2bdqTRSFR7Al25Wde1Voa2SaYViHg/mAJgPUQ/87asf5SSim', 'Safsaf', 'Dgdsg', NULL, '09758869191', 1, 'default-user.png', 0, **********, '0b8b09deeefa962c11701f0f1f3ecbf12822d15d8b16dc237465e43ff4af'),
(7, '<EMAIL>', '$2y$10$hUnXKRNKDTze7U8oZVgs8.B6CQWTqW/reiKgxTGPPQED/M.rPAvS6', 'Asdfghj', 'Asdfghjk', 'lkbhjgf', '45565', 1, 'default-user.png', 0, **********, '2b27306d81fbe7f1d91dc3320fe03247cb8274606f28c4ac33dcda14ccaf'),
(8, '<EMAIL>', '$2y$10$4QpRxezIavzw4A29q.z/.enTj0ArWiQDpg2IRau2.OUKQTcVbJn6G', 'Ewrtyui', 'Ertyui', 'kjbhghcf', '6262959', 1, 'default-user.png', 0, **********, '720a2688732ad8836d03c0558749fece1bb33d32973e53feec9acd07c3f3'),
(9, '<EMAIL>', '$2y$10$axX7n6cjulzRnf10ScSc.uzSMSUQapEu8tZ2aqtWFNf6FOXPQG.qu', 'Sdfgh', 'Sdfg', 'erty', '616161', 1, 'default-user.png', 0, **********, 'cabf63b908fd5fcdf7d698a7652df2bf37031ec7a42b7b960696b09ddeec'),
(10, '<EMAIL>', '$2y$10$P0Ydy2q/gJsgtFl7C.EaKu5esjil3vEB5KM3VeJZfAPpj7COVC6F2', 'Jgjb', 'Jbjb', 'kjgjb', 'bjb', 1, 'default-user.png', 0, **********, 'd98a73a485c9716ca76b9efff25d34ac38fe36ab89f7012bfc9de0d4b73b'),
(11, '<EMAIL>', '$2y$10$J8PJPpumtkBodjsb62WNl.zEmSBygri/Fqc6Lex.9v2buY.dYDB/O', 'Asdasds', 'Asdasd', 'asdas', '2312313', 1, 'default-user.png', 0, **********, '1d1e9b85a80a1da0b7e2533d4add00db5afc87f66b8c49c32e2d7c019bbd'),
(12, '<EMAIL>', '$2y$10$tX7JzNbV9jXZaJAKo5spjeZlt63ZXRRMucsK9WVyT8r9KV6vkh2/G', 'Dasd', 'Asdada', 'dasdasd', '312313', 1, 'default-user.png', 0, **********, '03087d4f6195b5c931651d722ba17dcab918f97c376ee58b929b11b49c15'),
(13, '<EMAIL>', '$2y$10$8SXsa8skb8uLlh5o.SWXvu8oj0oFE6QoJJunHwxlNXEcn4Bqg8LmC', 'Adasdsa', 'Asdasda', 'qasda', '788686', 1, 'default-user.png', 0, **********, '3eb1ef3ba2b6126624a5a3d2d46e84307374fe3af77b373d62cf3d19fa39'),
(14, '<EMAIL>', '$2y$10$bxYPzQ2TipD0Ve1clwyf8O5letVQdxPRxSX871JAXwDmq61BNuufe', 'Dasdada', 'Asdad', 'asdaddas', '213131', 1, 'default-user.png', 0, **********, 'ac8ab4c09980512604b4c40d536cb79cc2ccee7a76a7918a24994fcc730b'),
(15, '<EMAIL>', '$2y$10$NPLOnRaVBu1MD4P6YfRp1uGpm3aXGXSRJeKM1pqzVJu2IKuytAN42', 'Ghjkl;', 'Ghjkl', 'flash', '6464', 1, 'd4215f18783d5697910e0889a0dec672.png', 0, **********, '2924a55bf031248604327c6fda747f9a47ffa8454707e9234e8c676b9c2e'),
(16, '<EMAIL>', '$2y$10$P/UlzZC.Hn.dQkJOaJNg4.wprio5pCaQLVHy8/EYndHltacQuMaf6', 'Gfhjkl', 'Gfhjkl', 'spidey', '65161', 1, 'default-user.png', 0, 1528333719, 'caef3737aae8e68c51ffb9b0fb6c52e740ff355216d04b9005355711d833');

-- --------------------------------------------------------

--
-- Table structure for table `audit_trail`
--

CREATE TABLE `audit_trail` (
  `at_id` int(12) UNSIGNED NOT NULL,
  `customer_name` varchar(100) NOT NULL,
  `item_name` varchar(100) NOT NULL,
  `at_detail` varchar(50) DEFAULT NULL,
  `at_date` int(11) NOT NULL,
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1',
  `customer_id` int(12) UNSIGNED DEFAULT NULL,
  `order_id` int(12) UNSIGNED DEFAULT NULL,
  `product_id` int(12) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `audit_trail`
--

INSERT INTO `audit_trail` (`at_id`, `customer_name`, `item_name`, `at_detail`, `at_date`, `status`, `customer_id`, `order_id`, `product_id`) VALUES
(70, 'tabingi_mukha_ko', 'iPhone 7', 'Purchase', 1516764686, 1, 1, 17, 75),
(71, 'tabingi_mukha_ko', 'HP Stream 11.6" Celeron Laptop', 'Purchase', 1516764739, 1, 1, 18, 71),
(72, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Purchase', 1516764792, 1, 1, 19, 84),
(73, 'tabingi_mukha_ko', 'ZenFone 3 Max ZC520TL', 'Purchase', 1516764856, 1, 1, 20, 80),
(74, 'tabingi_mukha_ko', 'Samsung J7', 'Purchase', 1516764905, 1, 1, 21, 78),
(95, 'tabingi_mukha_ko', 'iPhone 8', 'Purchase', 1516764945, 1, 1, 22, 74),
(96, 'tabingi_mukha_ko', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1516766105, 1, 1, 23, 70),
(97, 'tabingi_mukha_ko', 'HP Laptop - 15z touch optional', 'Purchase', 1516766176, 1, 1, 24, 67),
(98, 'tabingi_mukha_ko', 'Samsung Galaxy Tab A 7.0', 'Purchase', 1516766176, 1, 1, 24, 82),
(99, 'RO1517055802', 'iPhone 8', 'Purchase', 1517056594, 1, 8, 27, 74),
(100, 'RO1517055802', 'iPhone 7', 'Purchase', 1517056595, 1, 8, 27, 75),
(101, 'RO1517055802', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1517056638, 1, 8, 28, 70),
(102, 'RO1517055802', 'HP Laptop - 15z touch optional', 'Purchase', 1517056639, 1, 8, 28, 67),
(103, 'RO1517055802', 'HP Stream 11.6" Celeron Laptop', 'Purchase', 1517056639, 1, 8, 28, 71),
(104, 'RO1517055802', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1517056640, 1, 8, 28, 72),
(105, 'EA1517057098', 'HP Laptop - 15z touch optional', 'Purchase', 1517057098, 1, 10, 30, 67),
(106, 'EA1517057098', 'Apple iPad Pro', 'Purchase', 1517057098, 1, 10, 30, 73),
(107, 'tabingi_mukha_ko', 'Samsung Galaxy Tab A 7.0', 'Purchase', 1517057440, 1, 1, 31, 82),
(108, 'tabingi_mukha_ko', 'Apple iPad Pro', 'Purchase', 1517057441, 1, 1, 31, 73),
(109, 'EA1517057098', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1517057516, 1, 10, 32, 70),
(110, 'SJ1517060191', 'iPhone 8', 'Purchase', 1517060192, 1, 11, 33, 74),
(111, 'tabingi_mukha_ko', 'iPhone 8', 'Purchase', 1517366748, 1, 1, 34, 74),
(112, 'tabingi_mukha_ko', 'Samsung Galaxy Tab A 7.0', 'Purchase', 1517408267, 1, 1, 36, 82),
(113, 'tabingi_mukha_ko', 'iPhone 8', 'Purchase', 1517408267, 1, 1, 36, 74),
(114, 'tabingi_mukha_ko', 'iPhone 7', 'Purchase', 1517408267, 1, 1, 36, 75),
(115, 'tabingi_mukha_ko', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1517409833, 1, 1, 38, 72),
(116, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Purchase', 1517409833, 1, 1, 38, 84),
(117, 'tabingi_mukha_ko', 'Apple iPad Pro', 'Purchase', 1517546454, 1, 1, 39, 73),
(118, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Purchase', 1517611034, 1, 1, 40, 84),
(119, 'SM1517056867', 'ZenFone 3 Max ZC520TL', 'Purchase', 1517984665, 1, 9, 41, 80),
(120, 'SM1517056867', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1517984665, 1, 9, 41, 72),
(121, 'SM1517056867', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1517984665, 1, 9, 41, 70),
(122, 'SM1517056867', 'iPhone 7', 'Purchase', 1517984753, 1, 9, 42, 75),
(123, 'SM1517056867', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1517984753, 1, 9, 42, 72),
(124, 'SM1517056867', 'Samsung Galaxy C5', 'Purchase', 1517984820, 1, 9, 43, 77),
(125, 'SM1517056867', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1517984820, 1, 9, 43, 72),
(126, 'SM1517056867', 'iPhone 7', 'Purchase', 1517984999, 1, 9, 44, 75),
(127, 'SM1517056867', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1517985000, 1, 9, 44, 72),
(128, 'SM1517056867', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1517985001, 1, 9, 44, 70),
(129, 'SM1517056867', 'Samsung Galaxy C5', 'Purchase', 1517985066, 1, 9, 45, 77),
(130, 'SM1517056867', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1517985067, 1, 8, 45, 70),
(131, 'SM1517056867', 'Samsung Galaxy C5', 'Purchase', 1517985096, 1, 9, 46, 77),
(132, 'SM1517056867', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1517985097, 1, 9, 46, 72),
(133, 'SM1517056867', 'Samsung Galaxy C5', 'Purchase', 1517985122, 1, 9, 47, 77),
(134, 'SM1517056867', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1517985123, 1, 8, 47, 70),
(135, 'SM1517056867', 'ZenFone 3 Max ZC520TL', 'Purchase', 1517985194, 1, 9, 48, 80),
(136, 'SM1517056867', 'Samsung Galaxy C5', 'Purchase', 1517985194, 1, 9, 48, 77),
(137, 'SM1517056867', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1517985194, 1, 9, 48, 72),
(138, 'SM1517056867', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1517985195, 1, 9, 48, 70),
(139, 'SM1517056867', 'Samsung Galaxy C5', 'Purchase', 1517985335, 1, 9, 49, 77),
(140, 'SM1517056867', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1517985336, 1, 9, 49, 72),
(141, 'SM1517056867', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1517985336, 1, 9, 49, 70),
(142, 'SM1517056867', 'ZenFone 3 Max ZC520TL', 'Purchase', 1518317662, 1, 9, 50, 80),
(143, 'SM1517056867', 'iPhone X', 'Purchase', 1518317662, 1, 9, 50, 85),
(144, 'SM1517056867', 'Apple iPad Mini 2', 'Purchase', 1518317663, 1, 9, 50, 84),
(145, 'tabingi_mukha_ko', 'iPhone 8', 'Purchase', 1518319218, 1, 1, 51, 74),
(146, 'tabingi_mukha_ko', 'iPhone 7', 'Purchase', 1518319219, 1, 1, 51, 75),
(147, 'tabingi_mukha_ko', 'iPhone 8', 'Purchase', 1518319284, 1, 1, 52, 74),
(148, 'tabingi_mukha_ko', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Purchase', 1518319284, 1, 1, 52, 68),
(149, 'tabingi_mukha_ko', 'iPhone 7', 'Purchase', 1518319285, 1, 1, 52, 75),
(150, 'tabingi_mukha_ko', 'Samsung Galaxy C5', 'Purchase', 1518347219, 1, 1, 53, 77),
(151, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Purchase', 1518347219, 1, 1, 53, 84),
(152, 'tabingi_mukha_ko', 'Samsung J7', 'Purchase', 1518347305, 1, 1, 54, 78),
(153, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Purchase', 1518347305, 1, 1, 54, 84),
(154, 'tabingi_mukha_ko', 'ACER ASPIRE ES1 332 BLACK', 'Purchase', 1518347373, 1, 1, 55, 72),
(155, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Purchase', 1518347374, 1, 1, 55, 84),
(156, 'tabingi_mukha_ko', 'iPhone 7', 'Purchase', 1518347662, 1, 1, 56, 75),
(157, 'tabingi_mukha_ko', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1518347663, 1, 1, 56, 70),
(158, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Purchase', 1518373703, 1, 1, 57, 84),
(159, 'tabingi_mukha_ko', 'Samsung Galaxy C5', 'Purchase', 1518373703, 1, 1, 57, 77),
(160, 'tabingi_mukha_ko', 'Apple iPad Pro', 'Purchase', **********, 1, 1, 58, 73),
(161, 'tabingi_mukha_ko', 'iPhone 7', 'Purchase', **********, 1, 1, 58, 75),
(162, 'tabingi_mukha_ko', 'iPhone X', 'Purchase', **********, 1, 1, 58, 85),
(163, 'tabingi_mukha_ko', 'iPhone 8', 'Purchase', **********, 1, 1, 58, 74),
(164, '**********', 'Apple iPad Pro', 'Purchase', 1518975053, 1, 12, 59, 73),
(165, '**********', 'Lenovo - 15.6', 'Purchase', **********, 1, 13, 60, 68),
(170, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Search', 1519012732, 0, 1, NULL, 84),
(171, 'tabingi_mukha_ko', 'Apple iPad Pro', 'Search', 1519012732, 0, 1, NULL, 73),
(172, '**********', 'Samsung J7', 'Purchase', **********, 1, 14, 62, 78),
(173, 'tabingi_mukha_ko', 'Samsung Galaxy C5', 'Purchase', 1519273633, 1, 1, 63, 77),
(174, 'AA**********', 'iPhone 7', 'Purchase', 1519421268, 1, 18, 64, 75),
(175, 'tabingi_mukha_ko', 'Apple 12W USB Power Adapter', 'Search', 1519954985, 1, 1, NULL, 95),
(176, 'tabingi_mukha_ko', 'Apple 30-pin to USB Cable', 'Search', 1519954985, 1, 1, NULL, 94),
(177, 'tabingi_mukha_ko', 'Apple Airpods', 'Search', 1519954985, 1, 1, NULL, 93),
(178, 'tabingi_mukha_ko', 'Apple Earphones', 'Search', 1519954985, 1, 1, NULL, 90),
(179, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Search', 1519954985, 1, 1, NULL, 84),
(180, 'tabingi_mukha_ko', 'Apple iPad Pro', 'Search', 1519954985, 1, 1, NULL, 73),
(181, 'tabingi_mukha_ko', 'Apple Lightning Digital AV Adapter', 'Search', 1519954985, 1, 1, NULL, 96),
(182, 'tabingi_mukha_ko', 'Apple Lightning to 30-pin Adapter', 'Search', 1519954985, 1, 1, NULL, 97),
(183, 'tabingi_mukha_ko', 'Apple Lightning to 30-pin Adapter (0.2m)', 'Search', 1519954985, 1, 1, NULL, 98),
(184, 'tabingi_mukha_ko', 'Apple 12W USB Power Adapter', 'Search', 1519956800, 1, 1, NULL, 95),
(185, 'tabingi_mukha_ko', 'Apple 30-pin to USB Cable', 'Search', 1519956800, 1, 1, NULL, 94),
(186, 'tabingi_mukha_ko', 'Apple Airpods', 'Search', 1519956800, 1, 1, NULL, 93),
(187, 'tabingi_mukha_ko', 'Apple Earphones', 'Search', 1519956800, 1, 1, NULL, 90),
(188, 'tabingi_mukha_ko', 'Apple iPad Mini 2', 'Search', 1519956801, 1, 1, NULL, 84),
(189, 'tabingi_mukha_ko', 'Apple iPad Pro', 'Search', 1519956801, 1, 1, NULL, 73),
(190, 'tabingi_mukha_ko', 'Apple Lightning Digital AV Adapter', 'Search', 1519956801, 1, 1, NULL, 96),
(191, 'tabingi_mukha_ko', 'Apple Lightning to 30-pin Adapter', 'Search', 1519956801, 1, 1, NULL, 97),
(192, 'tabingi_mukha_ko', 'Apple Lightning to 30-pin Adapter (0.2m)', 'Search', 1519956801, 1, 1, NULL, 98),
(193, 'tabingi_mukha_ko', 'ASUS Laptop X556UQ-NH71', 'Search', 1519958095, 1, 1, NULL, 70),
(194, 'RB1515560608', 'Samsung Galaxy C5', 'Search', 1519959641, 1, 4, NULL, 77),
(195, 'RB1515560608', 'Samsung Galaxy Tab A 7.0', 'Search', 1519959641, 1, 4, NULL, 82),
(196, 'RB1515560608', 'Samsung J7', 'Search', 1519959642, 1, 4, NULL, 78),
(197, 'RB1515560608', 'Samsung S4', 'Search', 1519959642, 1, 4, NULL, 79),
(198, 'RB1515560608', 'HP Laptop - 15z touch optional', 'Search', 1519959652, 1, 4, NULL, 67),
(199, 'RB1515560608', 'HP Stream 11.6" Celeron Laptop', 'Search', 1519959652, 1, 4, NULL, 71),
(200, 'RB1515560608', 'Samsung Galaxy C5', 'Search', 1519959659, 1, 4, NULL, 77),
(201, 'RB1515560608', 'Samsung Galaxy Tab A 7.0', 'Search', 1519959659, 1, 4, NULL, 82),
(202, 'RB1515560608', 'Samsung J7', 'Search', 1519959659, 1, 4, NULL, 78),
(203, 'RB1515560608', 'Samsung S4', 'Search', 1519959659, 1, 4, NULL, 79),
(204, 'RB1515560608', 'iPhone 8', 'Search', 1519976457, 1, 4, NULL, 74),
(205, 'RB1515560608', 'iPhone 8', 'Search', 1519976480, 1, 4, NULL, 74),
(206, 'RB1515560608', 'Apple iPad Mini 2', 'Search', 1519976491, 1, 4, NULL, 84),
(207, 'RB1515560608', 'ACER ASPIRE ES1 332 BLACK', 'Search', 1520003583, 1, 4, NULL, 72),
(208, 'RB1515560608', 'ASUS Laptop X556UQ-NH71', 'Search', 1520003583, 1, 4, NULL, 70),
(209, 'RB1515560608', 'Avantree - Walrus - Waterproof Case with Earphone Jack', 'Search', 1520003583, 1, 4, NULL, 105),
(210, 'RB1515560608', 'iPhone 7', 'Product Rating', 1520091289, 1, 4, NULL, 75),
(211, 'RB1515560608', 'iPhone X', 'Product Rating', 1520091289, 1, 4, NULL, 85),
(212, 'RB1515560608', 'Aukey PA-T11 6-port Charging Station with Quick Charge 3.0', 'Product Rating', 1520094512, 1, 4, NULL, 99),
(213, 'RB1515560608', 'iPad Air 2', 'Product Rating', 1520094908, 1, 4, NULL, 81),
(214, 'RB1515560608', 'Aukey PA-U32 Mini Dual Port Wall Charger with Foldable Plug', 'Product Rating', **********, 1, 4, NULL, 100),
(215, 'RB1515560608', 'Apple Lightning Digital AV Adapter', 'Product Rating', **********, 1, 4, NULL, 96),
(216, 'RB1515560608', 'Apple Airpods', 'Product Rating', **********, 1, 4, NULL, 93),
(217, 'RB1515560608', 'iPhone 8', 'Product Rating', **********, 1, 4, NULL, 74),
(218, 'RO1517055802', 'iPhone X', 'Product Rating', **********, 1, 8, NULL, 85),
(219, 'RO1517055802', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Product Rating', **********, 1, 8, NULL, 101),
(220, 'RO1517055802', 'iPhone 7', 'Product Rating', **********, 1, 8, NULL, 75),
(221, 'SM1517056867', 'iPhone 7', 'Product Rating', **********, 1, 9, NULL, 75),
(222, 'SM1517056867', 'iPhone X', 'Product Rating', **********, 1, 9, NULL, 85),
(223, 'SM1517056867', 'iPhone 8', 'Product Rating', **********, 1, 9, NULL, 74),
(224, 'RO1517055802', 'iPad Air 2', 'Viewed', **********, 1, 8, NULL, 81),
(225, 'RO1517055802', 'Apple Airpods', 'Viewed', **********, 1, 8, NULL, 93),
(226, 'RO1517055802', 'Apple 12W USB Power Adapter', 'Viewed', **********, 1, 8, NULL, 95),
(227, 'RO1517055802', 'Aukey PA-U32 Mini Dual Port Wall Charger with Foldable Plug', 'Viewed', **********, 1, 8, NULL, 100),
(228, 'RO1517055802', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Viewed', **********, 1, 8, NULL, 68),
(229, 'RO1517055802', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Viewed', **********, 1, 8, NULL, 68),
(230, 'RO1517055802', 'Apple Airpods', 'Viewed', **********, 1, 8, NULL, 93),
(231, 'RO1517055802', 'iPad Air 2', 'Viewed', **********, 1, 8, NULL, 81),
(232, 'RB1515560608', 'Apple iPad Mini 2', 'Viewed', **********, 1, 4, NULL, 84),
(233, 'RB1515560608', 'AUKEY PB-N41 POCKET 5000MAH POWER BANK ?1,17900 ?1,179.00', 'Viewed', **********, 1, 4, NULL, 102),
(234, 'RB1515560608', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Viewed', **********, 1, 4, NULL, 68),
(235, 'RB1515560608', 'HP Laptop - 15z touch optional', 'Viewed', **********, 1, 4, NULL, 67),
(236, 'RB1515560608', 'Samsung Galaxy C5', 'Viewed', **********, 1, 4, NULL, 77),
(237, 'RB1515560608', 'iPhone 7', 'Viewed', **********, 1, 4, NULL, 75),
(240, 'johndoe', 'iPhone 8', 'Viewed', **********, 1, 1, NULL, 74),
(241, 'johndoe', 'iPhone 8', 'Viewed', **********, 1, 1, NULL, 74),
(242, 'johndoe', 'ASUS Laptop X556UQ-NH71', 'Viewed', **********, 1, 1, NULL, 70),
(243, 'johndoe', 'iPhone 8', 'Viewed', **********, 1, 1, NULL, 74),
(244, 'RB1515560608', 'ASUS Laptop X556UQ-NH71', 'Viewed', **********, 1, 4, NULL, 70),
(245, 'RB1515560608', 'Samsung S4', 'Viewed', **********, 1, 4, NULL, 79),
(246, 'RB1515560608', 'iPhone 8', 'Viewed', **********, 1, 4, NULL, 74),
(247, 'RB1515560608', 'Samsung Galaxy Tab A 7.0', 'Viewed', **********, 1, 4, NULL, 82),
(248, 'RB1515560608', 'Aukey PB-T10 20,000mah Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, 4, NULL, 104),
(249, 'SJ1517060191', 'Apple iPad Pro', 'Viewed', **********, 1, 11, NULL, 73),
(250, 'SJ1517060191', 'Aukey PB-T10 20,000mah Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, 11, NULL, 104),
(251, 'SJ1517060191', 'Aukey PA-U32 Mini Dual Port Wall Charger with Foldable Plug', 'Viewed', **********, 1, 11, NULL, 100),
(252, 'SJ1517060191', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 11, NULL, 80),
(253, 'SJ1517060191', 'iPhone 8', 'Viewed', **********, 1, 11, NULL, 74),
(254, 'SJ1517060191', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 11, NULL, 80),
(255, 'SR1520271710', 'Apple 30-pin to USB Cable', 'Purchase', **********, 1, 19, 65, 94),
(256, 'SR1520271710', 'Apple Airpods', 'Purchase', **********, 1, 19, 65, 93),
(257, 'SR1520271710', 'iPhone 8', 'Viewed', **********, 1, 19, NULL, 74),
(258, 'SR1520271710', 'iPhone X', 'Viewed', **********, 1, 19, NULL, 85),
(259, 'SR1520271710', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 19, NULL, 80),
(260, 'RB1515560608', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 4, NULL, 80),
(261, 'RO1517055802', 'iPhone X', 'Viewed', **********, 1, 8, NULL, 85),
(262, 'SJ1517060191', 'iPhone X', 'Viewed', 1520272510, 1, 11, NULL, 85),
(263, 'johndoe', 'iPhone X', 'Viewed', 1520272559, 1, 1, NULL, 85),
(264, 'johndoe', 'Aukey PA-T11 6-port Charging Station with Quick Charge 3.0', 'Viewed', 1520272559, 1, 1, NULL, 99),
(265, 'SJ1517060191', 'iPhone X', 'Viewed', 1520272634, 1, 11, NULL, 85),
(266, 'SJ1517060191', 'Apple iPad Pro', 'Viewed', 1520272704, 1, 11, NULL, 73),
(267, 'SJ1517060191', 'iPhone 8', 'Viewed', 1520272744, 1, 11, NULL, 74),
(268, 'SJ1517060191', 'Apple Earphones', 'Viewed', 1520272744, 1, 11, NULL, 90),
(269, 'SJ1517060191', 'Apple iPad Mini 2', 'Viewed', 1520272744, 1, 11, NULL, 84),
(270, 'SJ1517060191', 'iPhone X', 'Viewed', 1520272776, 1, 11, NULL, 85),
(271, 'SJ1517060191', 'Apple Airpods', 'Viewed', 1520272823, 1, 11, NULL, 93),
(272, 'RB1515560608', 'Dell Inspiron 3467', 'Viewed', 1520299093, 1, 4, NULL, 89),
(273, 'RO1517055802', 'Dell Inspiron 3467', 'Viewed', 1520299155, 1, 8, NULL, 89),
(274, 'johndoe', 'ZenFone 3 Max ZC520TL', 'Viewed', 1520299213, 1, 1, NULL, 80),
(275, 'johndoe', 'iPhone 8', 'Viewed', 1520299213, 1, 1, NULL, 74),
(276, 'SJ1517060191', 'iPhone 8', 'Viewed', 1520300887, 1, 11, NULL, 74),
(277, 'SJ1517060191', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Viewed', 1520300887, 1, 11, NULL, 68),
(278, 'SJ1517060191', 'Apple 30-pin to USB Cable', 'Viewed', 1520300887, 1, 11, NULL, 94),
(279, 'johndoe', 'ZenFone 3 Max ZC520TL', 'Viewed', 1520301267, 1, 1, NULL, 80),
(280, 'SM1517056867', 'iPhone X', 'Search', 1520301295, 1, 9, NULL, 85),
(281, 'SM1517056867', 'Apple 12W USB Power Adapter', 'Search', 1520301311, 1, 9, NULL, 95),
(282, 'SM1517056867', 'Apple 30-pin to USB Cable', 'Search', 1520301312, 1, 9, NULL, 94),
(283, 'SM1517056867', 'Apple Airpods', 'Search', 1520301312, 1, 9, NULL, 93),
(284, 'SM1517056867', 'Apple Earphones', 'Search', 1520301312, 1, 9, NULL, 90),
(285, 'SM1517056867', 'Apple iPad Mini 2', 'Search', 1520301312, 1, 9, NULL, 84),
(286, 'SM1517056867', 'Apple iPad Pro', 'Search', 1520301312, 1, 9, NULL, 73),
(287, 'SM1517056867', 'Apple Lightning Digital AV Adapter', 'Search', 1520301312, 1, 9, NULL, 96),
(288, 'SM1517056867', 'Apple Lightning to 30-pin Adapter', 'Search', 1520301312, 1, 9, NULL, 97),
(289, 'SM1517056867', 'Apple Lightning to 30-pin Adapter (0.2m)', 'Search', 1520301312, 1, 9, NULL, 98),
(290, 'SM1517056867', 'iPhone 7', 'Search', 1520301335, 1, 9, NULL, 75),
(291, 'SM1517056867', 'iPhone X', 'Viewed', 1520301341, 1, 9, NULL, 85),
(292, 'SM1517056867', 'Apple iPad Mini 2', 'Viewed', 1520301342, 1, 9, NULL, 84),
(293, 'SM1517056867', 'iPhone 7', 'Viewed', 1520301342, 1, 9, NULL, 75),
(294, 'SM1517056867', 'iPhone X', 'Search', 1520301352, 1, 9, NULL, 85),
(295, 'SM1517056867', 'iPhone 7', 'Search', 1520301362, 1, 9, NULL, 75),
(296, 'SM1517056867', 'Apple 12W USB Power Adapter', 'Search', 1520301367, 1, 9, NULL, 95),
(297, 'SM1517056867', 'Apple 30-pin to USB Cable', 'Search', 1520301367, 1, 9, NULL, 94),
(298, 'SM1517056867', 'Apple Airpods', 'Search', 1520301367, 1, 9, NULL, 93),
(299, 'SM1517056867', 'Apple Earphones', 'Search', 1520301367, 1, 9, NULL, 90),
(300, 'SM1517056867', 'Apple iPad Mini 2', 'Search', 1520301367, 1, 9, NULL, 84),
(301, 'SM1517056867', 'Apple iPad Pro', 'Search', 1520301367, 1, 9, NULL, 73),
(302, 'SM1517056867', 'Apple Lightning Digital AV Adapter', 'Search', 1520301367, 1, 9, NULL, 96),
(303, 'SM1517056867', 'Apple Lightning to 30-pin Adapter', 'Search', 1520301367, 1, 9, NULL, 97),
(304, 'SM1517056867', 'Apple Lightning to 30-pin Adapter (0.2m)', 'Search', 1520301367, 1, 9, NULL, 98),
(305, 'SM1517056867', 'iPhone X', 'Viewed', 1520301371, 1, 9, NULL, 85),
(306, 'SM1517056867', 'Dell Inspiron 3467', 'Viewed', 1520301424, 1, 9, NULL, 89),
(307, 'SM1517056867', 'Aukey PA-T11 6-port Charging Station with Quick Charge 3.0', 'Viewed', 1520301424, 1, 9, NULL, 99),
(308, 'SM1517056867', 'HP Laptop - 15z touch optional', 'Viewed', 1520301424, 1, 9, NULL, 67),
(309, 'SM1517056867', 'Aukey PA-T11 6-port Charging Station with Quick Charge 3.0', 'Viewed', **********, 1, 9, NULL, 99),
(310, 'SM1517056867', 'HP Laptop - 15z touch optional', 'Viewed', **********, 1, 9, NULL, 67),
(311, 'SM1517056867', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 9, NULL, 80),
(312, 'johndoe', 'iPhone 8', 'Viewed', **********, 1, 1, NULL, 74),
(313, 'johndoe', 'HP Laptop - 15z touch optional', 'Viewed', **********, 1, 1, NULL, 67),
(314, 'johndoe', 'Romoss Solo 5 Powerbank', 'Viewed', **********, 1, 1, NULL, 91),
(315, 'johndoe', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 1, NULL, 80),
(316, 'johndoe', 'ASUS Laptop X556UQ-NH71', 'Viewed', **********, 1, 1, NULL, 70),
(317, 'SJ1517060191', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 11, NULL, 80),
(318, 'SJ1517060191', 'Aukey PA-T11 6-port Charging Station with Quick Charge 3.0', 'Viewed', **********, 1, 11, NULL, 99),
(319, 'RB1515560608', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 4, NULL, 80),
(320, 'johndoe', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 1, NULL, 80),
(321, 'johndoe', 'Apple Lightning to 30-pin Adapter (0.2m)', 'Viewed', **********, 1, 1, NULL, 98),
(322, 'RO1517055802', 'iPhone X', 'Viewed', **********, 1, 8, NULL, 85),
(323, 'RB1515560608', 'Apple 12W USB Power Adapter', 'Search', **********, 1, 4, NULL, 95),
(324, 'RB1515560608', 'Apple 30-pin to USB Cable', 'Search', **********, 1, 4, NULL, 94),
(325, 'RB1515560608', 'Apple Airpods', 'Search', **********, 1, 4, NULL, 93),
(326, 'RB1515560608', 'Apple Earphones', 'Search', **********, 1, 4, NULL, 90),
(327, 'RB1515560608', 'Apple iPad Mini 2', 'Search', **********, 1, 4, NULL, 84),
(328, 'RB1515560608', 'Apple iPad Pro', 'Search', **********, 1, 4, NULL, 73),
(329, 'RB1515560608', 'Apple Lightning Digital AV Adapter', 'Search', **********, 1, 4, NULL, 96),
(330, 'RB1515560608', 'Apple Lightning to 30-pin Adapter', 'Search', **********, 1, 4, NULL, 97),
(331, 'RB1515560608', 'Apple Lightning to 30-pin Adapter (0.2m)', 'Search', **********, 1, 4, NULL, 98),
(332, 'RB1515560608', 'Apple Airpods', 'Viewed', 1522406158, 1, 4, NULL, 93),
(333, 'RB1515560608', 'Apple Earphones', 'Purchase', **********, 1, 4, 66, 90),
(334, 'RO1517055802', 'Apple 12W USB Power Adapter', 'Search', 1522469808, 1, 8, NULL, 95),
(335, 'RO1517055802', 'Apple 30-pin to USB Cable', 'Search', 1522469808, 1, 8, NULL, 94),
(336, 'RO1517055802', 'Apple Airpods', 'Search', 1522469808, 1, 8, NULL, 93),
(337, 'RO1517055802', 'Apple Earphones', 'Search', 1522469808, 1, 8, NULL, 90),
(338, 'RO1517055802', 'Apple iPad Mini 2', 'Search', 1522469808, 1, 8, NULL, 84),
(339, 'RO1517055802', 'Apple iPad Pro', 'Search', 1522469808, 1, 8, NULL, 73),
(340, 'RO1517055802', 'Apple Lightning Digital AV Adapter', 'Search', 1522469809, 1, 8, NULL, 96),
(341, 'RO1517055802', 'Apple Lightning to 30-pin Adapter', 'Search', 1522469809, 1, 8, NULL, 97),
(342, 'RO1517055802', 'Apple Lightning to 30-pin Adapter (0.2m)', 'Search', 1522469809, 1, 8, NULL, 98),
(343, 'RO1517055802', 'Apple 12W USB Power Adapter', 'Purchase', **********, 1, 8, 67, 95),
(344, 'RO1517055802', 'Samsung Galaxy C5', 'Search', 1522470144, 1, 8, NULL, 77),
(345, 'RO1517055802', 'Samsung Galaxy Tab A 7.0', 'Search', 1522470144, 1, 8, NULL, 82),
(346, 'RO1517055802', 'Samsung J7', 'Search', 1522470144, 1, 8, NULL, 78),
(347, 'RO1517055802', 'Samsung S4', 'Search', 1522470144, 1, 8, NULL, 79),
(348, 'RO1517055802', 'Apple 12W USB Power Adapter', 'Viewed', 1522470713, 1, 8, NULL, 95),
(349, 'RO1517055802', 'Apple Earphones', 'Viewed', 1522470714, 1, 8, NULL, 90),
(350, 'SR1520271710', 'ASUS Laptop X556UQ-NH71', 'Search', 1522470722, 1, 19, NULL, 70),
(351, 'AL1515664193', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Search', 1522471649, 1, 7, NULL, 68),
(352, 'AL1515664193', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Viewed', 1522471734, 1, 7, NULL, 68),
(353, 'RO1517055802', 'ASUS Laptop X556UQ-NH71', 'Search', 1522471743, 1, 8, NULL, 70),
(354, 'RO1517055802', 'iPhone X', 'Search', 1522471743, 1, 8, NULL, 85),
(355, 'RO1517055802', 'Samsung Galaxy C5', 'Search', 1522471743, 1, 8, NULL, 77),
(356, 'RO1517055802', 'Samsung Galaxy Tab A 7.0', 'Search', 1522471743, 1, 8, NULL, 82),
(357, 'RO1517055802', 'ZenFone 3 Max ZC520TL', 'Search', 1522471743, 1, 8, NULL, 80),
(358, 'RO1517055802', 'Apple 12W USB Power Adapter', 'Search', 1522730463, 1, 8, NULL, 95),
(359, 'RO1517055802', 'Apple 30-pin to USB Cable', 'Search', 1522730463, 1, 8, NULL, 94),
(360, 'RO1517055802', 'Apple Airpods', 'Search', 1522730463, 1, 8, NULL, 93),
(361, 'RO1517055802', 'Apple Earphones', 'Search', 1522730463, 1, 8, NULL, 90),
(362, 'RO1517055802', 'Apple iPad Mini 2', 'Search', 1522730463, 1, 8, NULL, 84),
(363, 'RO1517055802', 'Apple iPad Pro', 'Search', 1522730463, 1, 8, NULL, 73),
(364, 'RO1517055802', 'Apple Lightning Digital AV Adapter', 'Search', 1522730463, 1, 8, NULL, 96),
(365, 'RO1517055802', 'Apple Lightning to 30-pin Adapter', 'Search', 1522730463, 1, 8, NULL, 97),
(366, 'RO1517055802', 'Apple Lightning to 30-pin Adapter (0.2m)', 'Search', 1522730464, 1, 8, NULL, 98),
(367, 'RO1517055802', 'Apple Earphones', 'Purchase', **********, 1, 8, 68, 90),
(368, 'RO1517055802', 'Apple Lightning Digital AV Adapter', 'Purchase', **********, 1, 8, 68, 96),
(369, 'RO1517055802', 'ASUS Laptop X556UQ-NH71', 'Search', 1522730956, 1, 8, NULL, 70),
(370, 'RO1517055802', 'ASUS Laptop X556UQ-NH71', 'Purchase', 1522730979, 1, 8, 69, 70),
(371, 'RO1517055802', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1522762324, 1, 8, NULL, 70),
(373, 'Guest', 'iPhone 7', 'Viewed', 1523597463, 1, NULL, NULL, 75),
(374, 'Guest', 'iPhone 7', 'Viewed', 1523598136, 1, NULL, NULL, 75),
(375, 'Guest', 'iPhone 7', 'Viewed', 1523598171, 1, NULL, NULL, 75),
(376, 'Guest', 'iPhone 7', 'Viewed', 1523598192, 1, NULL, NULL, 75),
(377, 'Guest', 'iPhone 7', 'Viewed', 1523598214, 1, NULL, NULL, 75),
(378, 'Guest', 'iPhone 7', 'Viewed', 1523598232, 1, NULL, NULL, 75),
(379, 'Guest', 'iPhone 7', 'Viewed', 1523598296, 1, NULL, NULL, 75),
(380, 'Guest', 'iPhone 7', 'Viewed', 1523598297, 1, NULL, NULL, 75),
(381, 'Guest', 'iPhone 7', 'Viewed', 1523598298, 1, NULL, NULL, 75),
(382, 'Guest', 'iPhone 7', 'Viewed', 1523598341, 1, NULL, NULL, 75),
(383, 'Guest', 'iPhone 7', 'Viewed', 1523598418, 1, NULL, NULL, 75),
(384, 'Guest', 'iPhone 7', 'Viewed', 1523598446, 1, NULL, NULL, 75),
(385, 'Guest', 'iPhone 7', 'Viewed', 1523598621, 1, NULL, NULL, 75),
(386, 'Guest', 'iPhone 7', 'Viewed', 1523598642, 1, NULL, NULL, 75),
(387, 'Guest', 'iPhone 7', 'Viewed', 1523598653, 1, NULL, NULL, 75),
(388, 'Guest', 'iPhone 7', 'Viewed', 1523598667, 1, NULL, NULL, 75),
(389, 'Guest', 'iPhone 7', 'Viewed', 1523599590, 1, NULL, NULL, 75),
(390, 'Guest', 'iPhone 7', 'Viewed', 1523599686, 1, NULL, NULL, 75),
(391, 'Guest', 'iPhone 7', 'Viewed', 1523599700, 1, NULL, NULL, 75),
(392, 'Guest', 'iPhone 7', 'Viewed', 1523599776, 1, NULL, NULL, 75),
(393, 'Guest', 'iPhone 7', 'Viewed', 1523599804, 1, NULL, NULL, 75),
(394, 'Guest', 'iPhone 7', 'Viewed', 1523599814, 1, NULL, NULL, 75),
(395, 'Guest', 'iPhone 7', 'Viewed', 1523599861, 1, NULL, NULL, 75),
(396, 'Guest', 'iPhone 7', 'Viewed', 1523599885, 1, NULL, NULL, 75),
(397, 'Guest', 'iPhone 7', 'Viewed', 1523599926, 1, NULL, NULL, 75),
(398, 'Guest', 'iPhone 7', 'Viewed', 1523600049, 1, NULL, NULL, 75),
(399, 'Guest', 'iPhone 7', 'Viewed', 1523600088, 1, NULL, NULL, 75),
(400, 'Guest', 'iPhone 7', 'Viewed', 1523600103, 1, NULL, NULL, 75),
(401, 'Guest', 'iPhone 7', 'Viewed', 1523600116, 1, NULL, NULL, 75),
(402, 'Guest', 'iPhone 7', 'Viewed', 1523600134, 1, NULL, NULL, 75),
(403, 'Guest', 'iPhone 7', 'Viewed', 1523600150, 1, NULL, NULL, 75),
(404, 'Guest', 'iPhone 7', 'Viewed', 1523600162, 1, NULL, NULL, 75),
(405, 'Guest', 'iPhone 7', 'Viewed', 1523600189, 1, NULL, NULL, 75),
(406, 'Guest', 'iPhone 7', 'Viewed', 1523600200, 1, NULL, NULL, 75),
(407, 'Guest', 'iPhone 8', 'Viewed', 1523600204, 1, NULL, NULL, 74),
(408, 'Guest', 'iPhone 8', 'Viewed', 1523600232, 1, NULL, NULL, 74),
(409, 'Guest', 'iPhone 8', 'Viewed', 1523600275, 1, NULL, NULL, 74),
(410, 'Guest', 'iPhone 8', 'Viewed', 1523600276, 1, NULL, NULL, 74),
(411, 'Guest', 'iPhone 8', 'Viewed', 1523600378, 1, NULL, NULL, 74),
(412, 'Guest', 'iPhone 8', 'Viewed', 1523600652, 1, NULL, NULL, 74),
(413, 'Guest', 'iPhone 8', 'Viewed', 1523600665, 1, NULL, NULL, 74),
(414, 'Guest', 'Acer Chromebook CB3-131-C4NW N2840', 'Viewed', 1523600691, 1, NULL, NULL, 92),
(415, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523600696, 1, NULL, NULL, 72),
(416, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523600730, 1, NULL, NULL, 72),
(417, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523600824, 1, NULL, NULL, 72),
(418, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523600866, 1, NULL, NULL, 72),
(419, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523600972, 1, NULL, NULL, 72),
(420, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523600986, 1, NULL, NULL, 72),
(421, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601039, 1, NULL, NULL, 72),
(422, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601071, 1, NULL, NULL, 72),
(423, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601079, 1, NULL, NULL, 72),
(424, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601094, 1, NULL, NULL, 72),
(425, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601212, 1, NULL, NULL, 72),
(426, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601214, 1, NULL, NULL, 72),
(427, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601350, 1, NULL, NULL, 72),
(428, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601352, 1, NULL, NULL, 72),
(429, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601353, 1, NULL, NULL, 72),
(430, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601354, 1, NULL, NULL, 72),
(431, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601364, 1, NULL, NULL, 72),
(432, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601365, 1, NULL, NULL, 72),
(433, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601366, 1, NULL, NULL, 72),
(434, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601367, 1, NULL, NULL, 72),
(435, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601368, 1, NULL, NULL, 72),
(436, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601368, 1, NULL, NULL, 72),
(437, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601369, 1, NULL, NULL, 72),
(438, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601370, 1, NULL, NULL, 72),
(439, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601416, 1, NULL, NULL, 72),
(440, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601417, 1, NULL, NULL, 72),
(441, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601418, 1, NULL, NULL, 72),
(442, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601451, 1, NULL, NULL, 72),
(443, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601453, 1, NULL, NULL, 72),
(444, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601454, 1, NULL, NULL, 72),
(445, 'Guest', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', 1523601455, 1, NULL, NULL, 72),
(446, 'Guest', 'HP Stream 11.6" Celeron Laptop', 'Viewed', 1523601456, 1, NULL, NULL, 71),
(447, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601461, 1, NULL, NULL, 70),
(448, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601467, 1, NULL, NULL, 70),
(449, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601468, 1, NULL, NULL, 70),
(450, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601514, 1, NULL, NULL, 70),
(451, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601516, 1, NULL, NULL, 70),
(452, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601517, 1, NULL, NULL, 70),
(453, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601519, 1, NULL, NULL, 70),
(454, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601522, 1, NULL, NULL, 70),
(455, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601523, 1, NULL, NULL, 70),
(456, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601524, 1, NULL, NULL, 70),
(457, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601525, 1, NULL, NULL, 70),
(458, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601526, 1, NULL, NULL, 70),
(459, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601527, 1, NULL, NULL, 70),
(460, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601528, 1, NULL, NULL, 70),
(461, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601529, 1, NULL, NULL, 70),
(462, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', 1523601530, 1, NULL, NULL, 70),
(463, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601541, 1, NULL, NULL, 67),
(464, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601576, 1, NULL, NULL, 67),
(465, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601644, 1, NULL, NULL, 67),
(466, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601646, 1, NULL, NULL, 67),
(467, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601647, 1, NULL, NULL, 67),
(468, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601648, 1, NULL, NULL, 67),
(469, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601650, 1, NULL, NULL, 67),
(470, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601651, 1, NULL, NULL, 67),
(471, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601652, 1, NULL, NULL, 67),
(472, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601653, 1, NULL, NULL, 67),
(473, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601654, 1, NULL, NULL, 67),
(474, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601655, 1, NULL, NULL, 67),
(475, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', 1523601656, 1, NULL, NULL, 67),
(476, 'Guest', 'Apple iPad Pro', 'Viewed', 1523601663, 1, NULL, NULL, 73),
(477, 'Guest', 'iPhone 8', 'Viewed', 1523602106, 1, NULL, NULL, 74),
(478, 'Guest', 'Apple 30-pin to USB Cable', 'Viewed', 1523602113, 1, NULL, NULL, 94),
(479, 'Guest', 'Apple Airpods', 'Viewed', 1523602137, 1, NULL, NULL, 93),
(480, 'Guest', 'iPhone 7', 'Viewed', 1523602598, 1, NULL, NULL, 75),
(481, 'Guest', 'iPhone 7', 'Viewed', 1523602605, 1, NULL, NULL, 75),
(482, 'Guest', 'iPhone 7', 'Viewed', 1523602607, 1, NULL, NULL, 75),
(483, 'Guest', 'iPhone 7', 'Viewed', 1523602608, 1, NULL, NULL, 75),
(484, 'Guest', 'iPhone 7', 'Viewed', 1523602611, 1, NULL, NULL, 75),
(485, 'Guest', 'iPhone 7', 'Viewed', 1523602616, 1, NULL, NULL, 75),
(486, 'Guest', 'iPhone 7', 'Viewed', 1523602619, 1, NULL, NULL, 75),
(487, 'Guest', 'iPhone 7', 'Viewed', 1523602620, 1, NULL, NULL, 75),
(488, 'Guest', 'iPhone 7', 'Viewed', 1523602622, 1, NULL, NULL, 75),
(489, 'Guest', 'iPhone 7', 'Viewed', 1523602623, 1, NULL, NULL, 75),
(490, 'Guest', 'iPhone 7', 'Viewed', 1523602625, 1, NULL, NULL, 75),
(491, 'Guest', 'iPhone 7', 'Viewed', 1523602626, 1, NULL, NULL, 75),
(492, 'Guest', 'iPhone 7', 'Viewed', 1523602705, 1, NULL, NULL, 75),
(493, 'Guest', 'iPhone 7', 'Viewed', 1523602708, 1, NULL, NULL, 75),
(494, 'Guest', 'iPhone 7', 'Viewed', 1523602732, 1, NULL, NULL, 75),
(495, 'Guest', 'iPhone 7', 'Viewed', 1523602744, 1, NULL, NULL, 75),
(496, 'Guest', 'iPhone 7', 'Viewed', 1523602746, 1, NULL, NULL, 75),
(497, 'Guest', 'iPhone 7', 'Viewed', 1523602747, 1, NULL, NULL, 75),
(498, 'Guest', 'iPhone 7', 'Viewed', 1523602749, 1, NULL, NULL, 75),
(499, 'Guest', 'iPhone 7', 'Viewed', 1523602750, 1, NULL, NULL, 75),
(500, 'Guest', 'iPhone 7', 'Viewed', 1523602751, 1, NULL, NULL, 75),
(501, 'Guest', 'iPhone 7', 'Viewed', 1523602751, 1, NULL, NULL, 75),
(502, 'Guest', 'iPhone 7', 'Viewed', 1523602753, 1, NULL, NULL, 75),
(503, 'Guest', 'iPhone 7', 'Viewed', 1523602754, 1, NULL, NULL, 75),
(504, 'Guest', 'iPhone 7', 'Viewed', 1523602813, 1, NULL, NULL, 75),
(505, 'Guest', 'iPhone 7', 'Viewed', 1523602952, 1, NULL, NULL, 75),
(506, 'Guest', 'iPhone 7', 'Viewed', 1523603001, 1, NULL, NULL, 75),
(507, 'Guest', 'iPhone 7', 'Viewed', 1523603005, 1, NULL, NULL, 75),
(508, 'Guest', 'iPhone 7', 'Viewed', 1523603051, 1, NULL, NULL, 75),
(509, 'Guest', 'iPhone 7', 'Viewed', 1523603055, 1, NULL, NULL, 75),
(510, 'Guest', 'iPhone 7', 'Viewed', 1523603300, 1, NULL, NULL, 75),
(511, 'Guest', 'iPhone 7', 'Viewed', 1523603338, 1, NULL, NULL, 75),
(512, 'Guest', 'iPhone 7', 'Viewed', 1523603437, 1, NULL, NULL, 75),
(513, 'Guest', 'iPhone 7', 'Search', 1523603976, 1, NULL, NULL, 75),
(514, 'Guest', 'iPhone 8', 'Search', 1523603976, 1, NULL, NULL, 74),
(515, 'Guest', 'iPhone X', 'Search', 1523603976, 1, NULL, NULL, 85),
(516, 'Guest', 'iPad Air 2', 'Viewed', 1523604200, 1, NULL, NULL, 81),
(517, 'Guest', 'Apple 30-pin to USB Cable', 'Viewed', 1523604209, 1, NULL, NULL, 94),
(518, 'Guest', 'iPhone 8', 'Viewed', 1523604217, 1, NULL, NULL, 74),
(519, 'Guest', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Viewed', 1523604739, 1, NULL, NULL, 68),
(520, 'Guest', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Viewed', **********, 1, NULL, NULL, 68),
(521, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(522, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(523, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(524, 'Guest', 'Apple 30-pin to USB Cable', 'Viewed', **********, 1, NULL, NULL, 94),
(525, 'Guest', 'Apple Earphones', 'Viewed', **********, 1, NULL, NULL, 90),
(526, 'Guest', 'Aukey PB-N42 Pocket 10,000mah Power Bank', 'Viewed', **********, 1, NULL, NULL, 103),
(527, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(528, 'Guest', 'AUKEY PB-N41 POCKET 5000MAH POWER BANK ?1,17900 ?1,179.00', 'Viewed', **********, 1, NULL, NULL, 102),
(529, 'Guest', 'AUKEY PB-N41 POCKET 5000MAH POWER BANK ?1,17900 ?1,179.00', 'Viewed', **********, 1, NULL, NULL, 102),
(530, 'Guest', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, NULL, NULL, 101),
(531, 'Guest', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, NULL, NULL, 101),
(532, 'Guest', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, NULL, NULL, 101),
(533, 'Guest', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, NULL, NULL, 101),
(534, 'Guest', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, NULL, NULL, 101),
(535, 'Guest', 'AUKEY PB-N41 POCKET 5000MAH POWER BANK ?1,17900 ?1,179.00', 'Viewed', **********, 1, NULL, NULL, 102),
(536, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(537, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(538, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(539, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(540, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(541, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(542, 'Guest', 'iPhone 7', 'Viewed', **********, 1, NULL, NULL, 75),
(543, 'RB1515560608', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, 4, NULL, 101),
(544, 'RB1515560608', 'iPhone 7', 'Viewed', **********, 1, 4, NULL, 75),
(545, 'RB1515560608', 'ASUS Laptop X556UQ-NH71', 'Viewed', **********, 1, 4, NULL, 70),
(546, 'RB1515560608', 'iPhone 8', 'Viewed', **********, 1, 4, NULL, 74),
(547, 'RB1515560608', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 4, NULL, 80),
(548, 'RB1515560608', 'Apple Earphones', 'Viewed', **********, 1, 4, NULL, 90),
(549, 'RB1515560608', 'ACER ASPIRE ES1 332 BLACK', 'Viewed', **********, 1, 4, NULL, 72),
(550, 'RB1515560608', 'HP Stream 11.6" Celeron Laptop', 'Viewed', **********, 1, 4, NULL, 71),
(551, 'Guest', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, NULL, NULL, 101),
(552, 'Guest', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, NULL, NULL, 101),
(553, 'Guest', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, NULL, NULL, 101),
(554, 'Guest', 'iPhone 8', 'Viewed', **********, 1, NULL, NULL, 74),
(555, 'johndoe', 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Viewed', **********, 1, 1, NULL, 101),
(556, 'johndoe', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, 1, NULL, 80),
(557, 'johndoe', 'Apple iPad Mini 2', 'Viewed', **********, 1, 1, NULL, 84),
(558, 'Guest', 'Dell Inspiron 3467', 'Viewed', **********, 1, NULL, NULL, 89),
(559, 'Guest', 'ASUS Laptop X556UQ-NH71', 'Viewed', **********, 1, NULL, NULL, 70),
(560, 'Guest', 'HP Stream 11.6" Celeron Laptop', 'Viewed', **********, 1, NULL, NULL, 71),
(561, 'Guest', 'HP Laptop - 15z touch optional', 'Viewed', **********, 1, NULL, NULL, 67),
(562, 'Guest', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, NULL, NULL, 80),
(563, 'Guest', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, NULL, NULL, 80),
(564, 'Guest', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, NULL, NULL, 80),
(565, 'Guest', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, NULL, NULL, 80),
(566, 'Guest', 'ZenFone 3 Max ZC520TL', 'Viewed', **********, 1, NULL, NULL, 80),
(567, 'Guest', 'ZenFone 3 Max ZC520TL', 'Viewed', 1527154975, 1, NULL, NULL, 80),
(568, 'Guest', 'ZenFone 3 Max ZC520TL', 'Viewed', 1527154976, 1, NULL, NULL, 80),
(569, 'Guest', 'ZenFone 3 Max ZC520TL', 'Viewed', 1527154976, 1, NULL, NULL, 80),
(570, 'Guest', 'ZenFone 3 Max ZC520TL', 'Viewed', 1527154977, 1, NULL, NULL, 80),
(571, 'Guest', 'iPhone 8', 'Viewed', 1527154978, 1, NULL, NULL, 74),
(572, 'Guest', 'iPhone 8', 'Viewed', **********, 1, NULL, NULL, 74),
(573, 'Guest', 'iPhone 8', 'Viewed', **********, 1, NULL, NULL, 74),
(574, 'Guest', 'iPhone 8', 'Viewed', **********, 1, NULL, NULL, 74),
(575, 'Guest', 'iPhone 8', 'Viewed', **********, 1, NULL, NULL, 74),
(576, 'Guest', 'iPhone 8', 'Viewed', **********, 1, NULL, NULL, 74),
(577, 'Guest', 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Viewed', **********, 1, NULL, NULL, 68),
(578, 'RO1517055802', 'Romoss Solo 5 Powerbank', 'Purchase', **********, 0, 8, 70, 91),
(579, 'RO1517055802', 'Aukey PB-T10 20,000mah Power Bank with Quick Charge 3.0', 'Purchase', **********, 0, 8, 70, 104);

-- --------------------------------------------------------

--
-- Table structure for table `brand`
--

CREATE TABLE `brand` (
  `brand_id` int(12) UNSIGNED NOT NULL,
  `brand_name` varchar(40) NOT NULL,
  `category_id` int(12) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `brand`
--

INSERT INTO `brand` (`brand_id`, `brand_name`, `category_id`, `status`) VALUES
(601, 'apple', 702, 1),
(602, 'asus', 703, 1),
(603, 'samsung', 702, 1),
(604, 'lenovo', 703, 1),
(605, 'acer', 703, 1),
(606, 'dell', 703, 1),
(607, 'hp', 703, 1),
(608, 'sony', 702, 1),
(609, 'romoss', 701, 1),
(610, 'aukey', 701, 1),
(613, 'avantree', 701, 1),
(614, 'oppo', 702, 1),
(615, 'acer', 703, 0),
(616, 'asus', 702, 1),
(617, 'apple', 704, 1),
(618, 'apple', 705, 1),
(619, 'samsung', 705, 1),
(620, 'aukey', 704, 1);

-- --------------------------------------------------------

--
-- Table structure for table `category`
--

CREATE TABLE `category` (
  `category_id` int(12) UNSIGNED NOT NULL,
  `category` varchar(40) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `category`
--

INSERT INTO `category` (`category_id`, `category`, `status`) VALUES
(701, 'accessories', 1),
(702, 'smartphones', 1),
(703, 'laptops', 1),
(704, 'chargers', 1),
(705, 'tablets', 1),
(706, 'featured', 0);

-- --------------------------------------------------------

--
-- Table structure for table `content`
--

CREATE TABLE `content` (
  `content_id` int(11) UNSIGNED NOT NULL,
  `image_1` varchar(50) NOT NULL,
  `image_2` varchar(50) NOT NULL,
  `image_3` varchar(50) NOT NULL,
  `color_1` varchar(10) NOT NULL,
  `color_2` varchar(10) NOT NULL,
  `color_3` varchar(10) NOT NULL,
  `company_logo` varchar(50) NOT NULL,
  `customer_color1` varchar(10) NOT NULL,
  `logo_icon` varchar(50) NOT NULL,
  `announcementh1` varchar(250) NOT NULL,
  `announcementh2` varchar(250) NOT NULL,
  `announcementh3` varchar(250) NOT NULL,
  `announcement1` text NOT NULL,
  `announcement2` text NOT NULL,
  `announcement3` text NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `content`
--

INSERT INTO `content` (`content_id`, `image_1`, `image_2`, `image_3`, `color_1`, `color_2`, `color_3`, `company_logo`, `customer_color1`, `logo_icon`, `announcementh1`, `announcementh2`, `announcementh3`, `announcement1`, `announcement2`, `announcement3`) VALUES
(1, 'af4f6e258b8d91332fe75934553b0d15.jpg', '13ed53eb944126d6b7a03a2c653ed0cb.jpg', 'bc7d9c8ae9b71ab923d8ab6b9d77d38f.jpg', '#555555', ' ', ' ', '75e9fce4143c96cccaad6b70fbb2c14f.png', '#555555', '63db1bd61df45d2f5672a89ee8122017.png', 'SUMMER1000', 'Best prices', 'We love our customers', '1000 pesos off every time you use this code! Only applicable for purchases 5000 pesos or above. (Promo runs until April 23 2018)', 'We offer only the best prices available for our customers here at TECHNOHOLICS!', 'We are known to provide best possible service ever.');

-- --------------------------------------------------------

--
-- Table structure for table `customer`
--

CREATE TABLE `customer` (
  `customer_id` int(12) UNSIGNED NOT NULL,
  `email` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `firstname` varchar(50) NOT NULL,
  `lastname` varchar(50) NOT NULL,
  `username` varchar(50) DEFAULT NULL,
  `product_preference` varchar(255) DEFAULT NULL,
  `preference_basis` varchar(20) DEFAULT NULL,
  `gender` varchar(6) NOT NULL,
  `age` tinyint(3) UNSIGNED NOT NULL,
  `birthdate` int(11) UNSIGNED NOT NULL,
  `a_range` varchar(10) NOT NULL,
  `complete_address` varchar(50) NOT NULL,
  `province` varchar(50) NOT NULL,
  `city_municipality` varchar(50) NOT NULL,
  `barangay` varchar(50) NOT NULL,
  `zip_code` varchar(4) NOT NULL,
  `contact_no` varchar(20) DEFAULT NULL,
  `image` varchar(200) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `registered_at` int(11) NOT NULL,
  `verification_code` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `customer`
--

INSERT INTO `customer` (`customer_id`, `email`, `password`, `firstname`, `lastname`, `username`, `product_preference`, `preference_basis`, `gender`, `age`, `birthdate`, `a_range`, `complete_address`, `province`, `city_municipality`, `barangay`, `zip_code`, `contact_no`, `image`, `status`, `is_verified`, `registered_at`, `verification_code`) VALUES
(1, '<EMAIL>', '$2y$10$vjAHPMTIlA105nVl9M6dJ.rKYfHn.ngx0cS4LW/0/fXqTlmTVf39C', 'John', 'Doe', 'johndoe', 'Apple 12W USB Power Adapter, Apple Lightning Digital AV Adapter, Apple 30-pin to USB Cable, Apple Airpods, Apple Earphones, Apple iPad Pro, Apple Lightning to 30-pin Adapter, Apple Lightning to 30-pin Adapter (0.2m)', 'Searched products', 'Male', 20, 877612980, '13-20', '1101 Tilted Bldg., Maceda St.', 'Metro Manila', 'Titled Place', 'Brgy. Tilted', '1069', '09166629866', 'tilted.jpg', 1, 1, 1511318551, '8372ff6856c18e05066a8ae3db7136212966ad18ec09ad3c875f94c4eb70'),
(3, '<EMAIL>', 'customer', 'Kervin', 'Rollan', 'KR1515550574', NULL, NULL, 'Female', 22, 793594620, '31-40', 'Suntrust building', 'Mindoro', 'Quezon City', 'Bahay Toro', '1231', '09123355982', 'default-user.png', 0, 0, 0, ''),
(4, '<EMAIL>', '$2y$10$rEkMAgjR/DuJJngX54ysM.gHX8mtTp7VHUeStwYOGLnLXAMminUvq', 'Rex', 'Baldonado', 'RB1515560608', 'HP Stream 11.6" Celeron Laptop, Avantree - Walrus - Waterproof Case with Earphone Jack, ACER ASPIRE ES1 332 BLACK, Samsung J7, Apple 12W USB Power Adapter, Apple iPad Pro, Apple Earphones, Apple Lightning Digital AV Adapter', 'Searched products', 'Male', 19, 914363820, '13-20', 'Paredes St. #11', 'None', 'Quezon City', 'Brgy. FEU', '123', '09123355272', 'p4few9dvj4jk3k2j99hjknk32.jpg', 1, 1, 0, '1da7957d86c646d930d37624fe550ba9564f07917786865c8df0e38c1940'),
(5, '<EMAIL>', '$2y$10$N2PDAht1XXaRlMU4Dn3RH.0x.Q89yun8icgYoUzeoXL50FkflUAiO', 'Ederson', 'Villegas', 'EV1515640022', NULL, NULL, 'Male', 21, 842956020, '21-30', 'Lower Nawasa #71', 'Quezon Province', 'Quezon City', 'CommonWealth', '1661', '09123350598', 'default-user.png', 1, 0, 0, '5f7449161e5d2373aebeb5f9e66c93f1a2680a6adaa7539d96f91d19e143'),
(6, '<EMAIL>', '$2y$10$738OkUDjTuth6wx4AC7X4O88UTyfnoE0nlnOtr7XVS00rf8Bhmcce', 'Arjhomel', 'Jimenez', 'AJ1515640308', NULL, NULL, 'Male', 23, 767924820, '21-30', 'FEU Instititue #69', 'Quezon Province', 'Quezon City', 'Toro', '1661', '09123355980', 'default-user.png', 1, 0, 0, '78b4bcfffb6180db327ce2948036f10d5cb1e9c6f0c6b9920bc2afee998d'),
(7, '<EMAIL>', '$2y$10$AkkJ6LNbadLa7/vQs0bbluOdWKlIRABHNKfQqsRtzlqjGGBIEZwr.', 'Andrew', 'Leona', 'AL1515664193', NULL, NULL, 'Female', 18, 918174420, '13-20', 'adasda', 'zdads', 'asdadasd', 'dasdada', '1661', '09414321302', 'al35sfsfas1498113071.jpg', 1, 1, 0, 'd13eb2e652de51b878cc93eca890e42c55cfc033fc181ee5cbfe112d8952'),
(8, '<EMAIL>', '$2y$10$DhJfkyxUOz5dRiOuUJ1rdOnmifM6xNa12chgvQpIFAzNDifd6mVR6', 'Aj', 'Brooks', 'RO1517055802', 'Apple Airpods, Lenovo - 15.6" Laptop - AMD A6-Series', 'Viewed Products', 'Female', 30, 543103020, '21-30', '4509 Nutter St.', 'Missouri', 'St. Louis', 'Belton', '6401', '09491399640', 'default-user.png', 1, 1, 0, '873dec17c7c8d6821a8091d77df5589ccf8593c8246351ddc16aaf8118d5'),
(9, '<EMAIL>', '$2y$10$u8UwRvakhaDd9pwpor059u5Zrox0xzrFifH299aYwtbUUua9XWx4q', 'Shawn', 'Michaels', 'SM1517056867', 'Aukey PA-T11 6-port Charging Station with Quick Charge 3.0, HP Laptop - 15z touch optional', 'Viewed Products', 'Male', 47, 17445420, '41-50', '2264 Sundown Lane', 'Texas', 'San Antonio', 'Connect', '7874', '09123655995', 'default-user.png', 1, 1, 1517065455, '4148763f386fdd4d11b550fbffe2fe1148bae1dfb9c0b936ee111a5f1830'),
(10, '<EMAIL>', '$2y$10$5U/tUafvIvXTku85icxISugVSw2.oPZKiKSonCH9gEuUWyJZGDl/S', 'Enzo', 'Amore', 'EA1517057098', NULL, NULL, 'Male', 31, 534418020, '31-40', '1403 Sawft', 'Metro Manila', 'Manila', 'Sampaloc', '6202', '09123655995', 'default-user.png', 1, 1, 1517064533, '7d9e8162cd5f3cd553eb37c9f4a27e2801897d9f28aa53fd4eb9771b8e64'),
(11, '<EMAIL>', '$2y$10$b3n5GwD0yp7IY.zxkM2WS.s9z8qg6h8OttuBcp4gkDNInmzYwG6W.', 'Samoa', 'Joe', 'SJ1517060191', NULL, NULL, 'Male', 38, 290510820, '31-40', '1122 Coquina', 'Metro Manila', 'Manila', 'Sampaloc', '6202', '09755229463', 'sjqu82udjck9diwjmdc998wuje8whxiwbdk92err4r9f8ujdc0s9dci.jpg', 1, 1, 1517060191, '39efa0769e3de26402fe15515174819df9ce09f135da05db44f595bbc7f9'),
(12, '', '$2y$10$VGurwXocuFfy3KLTGbFD5ent.XbIuSkn2dLB26DVCnYpiChr3Xiwe', '', '', '**********', NULL, NULL, '', 0, 0, '', '', '', '', '', '', '', 'default-user.png', 0, 0, **********, '2ba09ef44400703e221219d7e1c93f57fbea974b5f8df343e98d8e479890'),
(13, '', '$2y$10$ph2MYbM2eTG05NXtXVW0meJizZwLStMPXpFrWx1wpL8lBBeawC6nG', '', '', '**********', NULL, NULL, '', 0, 0, '', '', '', '', '', '', '', 'default-user.png', 0, 0, **********, '84f412404f5d70c1d46a147adf826a2ccf80cee51140a0033872a1f0f9a6'),
(14, '', '$2y$10$/WkbDBGhM39spsTFNnl37ezy17bmOYOj5ARE18b5yHhgwVbsK8JUW', '', '', '**********', NULL, NULL, '', 0, 0, '', '', '', '', '', '', '', 'default-user.png', 0, 0, **********, 'c9bf68beeddb082aaf8c6d1db8eaf8e14989bf6b9c5386c1b1b10b121698'),
(16, '<EMAIL>', '5fa0328a573f93df69b06a5f55ccf5ef0523e8aa', 'Calimlim', 'Veo', NULL, NULL, NULL, '', 0, 0, '', '#11 Lazaro Soriano Street', '', '', '', '', NULL, NULL, 0, 0, 1519101587, 'jftAzJiTncOsDxG'),
(17, '<EMAIL>', 'd15c0e69e7668915585d728524ad1c8026d591ec', 'Leona', 'Andrew', NULL, NULL, NULL, '', 0, 0, '', '15 Posooy', '', '', '', '', NULL, NULL, 0, 0, 1519102090, 'zQTSEJgrv79ABhM'),
(18, '<EMAIL>', '$2y$10$Qf.4SYvZu5nIBYvbslFVqeHHPVY4KzzEqxDGn4akuIgyfF1IShHj2', 'Ass', 'Asaas', 'AA**********', NULL, NULL, '', 0, 0, '', 'As', 'Ass"', 'Asas', 'Asa', '1212', '121', 'default-user.png', 0, 0, **********, '1f14ef1589825fc0c4138319dee5b891199618d3905a9ef42e021cf8b27c'),
(19, '<EMAIL>', '$2y$10$YdxZSReNp379NCpcOy2coe.xjHkXrIw4bjcQWF0LHh/1Gg0cURzrC', 'Seth', 'Rollins', 'SR1520271710', NULL, NULL, 'Male', 19, 902959200, '13-20', 'Jnkjnjk', '', 'Kjnn', '', '545', '09123655995', 'default-user.png', 1, 1, 1520271710, 'd8cca071736dd581bf959f2feac6193f796edfad61f94131fb435e6455ab');

-- --------------------------------------------------------

--
-- Table structure for table `feedback`
--

CREATE TABLE `feedback` (
  `feedback_id` int(11) UNSIGNED NOT NULL,
  `customer_id` int(12) UNSIGNED DEFAULT NULL,
  `product_id` int(12) UNSIGNED DEFAULT NULL,
  `feedback` varchar(200) NOT NULL,
  `rating` float(2,1) UNSIGNED NOT NULL,
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1',
  `added_at` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `feedback`
--

INSERT INTO `feedback` (`feedback_id`, `customer_id`, `product_id`, `feedback`, `rating`, `status`, `added_at`) VALUES
(16, 1, 70, 'again', 1.0, 1, 1519137007),
(17, 1, 72, 'Ganda!', 4.0, 1, 1519137049),
(18, 1, 100, 'its okay', 3.0, 1, 1519957985),
(19, 4, 74, 'ang mahal naman masyado', 2.0, 1, **********),
(20, 4, 84, '3', 3.0, 1, 1520094865),
(21, 4, 70, 'sakto lang', 3.0, 1, 1520003600),
(22, 4, 93, 'ganda', 5.0, 1, 1520095522),
(23, 4, 90, 'Great quality!', 5.0, 1, 1520087838),
(24, 4, 95, 'Is this even legit?', 3.0, 1, 1520087865),
(25, 4, 67, 'this product is good', 4.0, 1, 1520087927),
(26, 4, 71, 'cute ng kulay', 4.0, 1, 1520088552),
(27, 4, 85, 'ano ba yan, overpriced!', 1.0, 1, 1520089878),
(28, 4, 75, 'legit price', 5.0, 1, 1520089814),
(29, 4, 72, 'cooool', 4.0, 1, 1520094979),
(30, 4, 99, 'sdfg', 5.0, 1, 1520095001),
(31, 4, 77, 'qwerty', 2.0, 1, 1520094592),
(32, 4, 78, 'aaaaaaaaaaaaaaaaaaaaaa', 2.0, 1, 1520094849),
(33, 4, 79, 'as', 3.0, 1, 1520094761),
(34, 4, 94, 'asd', 2.0, 1, 1520094804),
(35, 4, 81, 'asd', 2.0, 1, 1520094900),
(36, 4, 100, 'asdf', 2.0, 1, 1520095046),
(37, 8, 81, 'fghvjbklm,', 3.0, 1, 1520095292),
(38, 8, 82, 'cute', 4.0, 1, 1520095318),
(39, 8, 90, 'fcghvjklm;', 3.0, 1, 1520095432),
(40, 4, 96, 'bakit ganun', 3.0, 1, 1520095508),
(41, 8, 85, 'Good service, keep it up!', 4.0, 1, 1520095644),
(42, 8, 101, 'This is a great product!', 5.0, 1, **********),
(43, 9, 75, 'Great service! ', 4.0, 1, **********),
(44, 9, 85, 'asdfgh', 3.0, 1, **********),
(45, 9, 74, 'poor quality was delivered to me', 1.0, 1, **********);

-- --------------------------------------------------------

--
-- Table structure for table `forecast`
--

CREATE TABLE `forecast` (
  `forecast_id` int(12) NOT NULL,
  `date_forecasted` int(11) NOT NULL,
  `forecasted_income` float(8,2) NOT NULL,
  `added_at` int(11) NOT NULL,
  `updated_at` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `forecast`
--

INSERT INTO `forecast` (`forecast_id`, `date_forecasted`, `forecasted_income`, `added_at`, `updated_at`) VALUES
(10, 1525125600, 465021.34, 1523213098, **********),
(11, 1527804000, 69442.67, 1523213129, **********),
(12, 1530396000, 16029.67, 1523213151, 1528287379),
(13, 1533074400, 5343.22, 1523213456, 1528287458),
(14, 1535752800, 125943.52, 1523258546, 1527944129),
(16, 1538344800, 49105.47, 1523259837, 1527947176),
(28, 1541026800, 60130.74, 1523261514, 1528287397),
(29, 1543618800, 78393.24, 1523261522, 1528288034),
(30, 1514761200, 94282.33, 1523261531, 1527947209),
(31, 1517439600, 159641.67, 1523261540, 1527947215),
(32, 1519858800, 516230.34, 1523261547, 1528288138),
(33, 1522533600, 531177.69, 1523261554, 1528288092),
(34, 1490997600, 25293.33, 1528287047, 1528287280),
(35, 1498860000, 27990.00, 1528287068, 0),
(36, 1509490800, 34726.00, 1528287076, 0),
(37, 1493589600, 17796.67, 1528287239, 0),
(38, 1512082800, 60849.67, 1528287250, 0);

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `order_id` int(12) UNSIGNED NOT NULL,
  `order_quantity` int(8) NOT NULL,
  `total_price` float(8,2) UNSIGNED NOT NULL,
  `order_detail` varchar(200) DEFAULT NULL,
  `payment_method` varchar(50) NOT NULL,
  `transaction_date` int(11) NOT NULL,
  `delivery_date` int(11) NOT NULL,
  `shipping_address` varchar(200) NOT NULL,
  `process_status` tinyint(1) UNSIGNED DEFAULT '1',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1',
  `admin_id` int(12) UNSIGNED DEFAULT NULL,
  `customer_id` int(12) UNSIGNED NOT NULL,
  `shipper_id` int(12) UNSIGNED DEFAULT '904'
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`order_id`, `order_quantity`, `total_price`, `order_detail`, `payment_method`, `transaction_date`, `delivery_date`, `shipping_address`, `process_status`, `status`, `admin_id`, `customer_id`, `shipper_id`) VALUES
(2, 3, 100997.00, NULL, 'COD', 1515550574, 1515550574, 'Suntrust building', 2, 0, NULL, 3, 903),
(3, 3, 100997.00, NULL, 'COD', 1515560608, 1515819808, 'Paredes St. #11', 3, 0, NULL, 4, 904),
(4, 5, 166551.00, NULL, 'COD', 1515640022, 1515899222, 'Lower Nawasa #71', 2, 0, NULL, 5, 904),
(5, 5, 149995.00, NULL, 'paypal', 1515640308, 1515899508, 'FEU Instititue #69', 3, 0, NULL, 6, 903),
(6, 6, 197994.00, NULL, 'paypal', 1515664193, 1515923393, 'adasda', 3, 0, NULL, 7, 903),
(8, 2, 24998.00, 'CANCELLED', 'visa_mastercard', 1516336528, 1516595728, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 0, NULL, 1, 903),
(9, 4, 65996.00, NULL, 'visa_mastercard', 1516336719, 1516595919, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 0, NULL, 1, 904),
(10, 2, 898.00, NULL, 'visa_mastercard', 1516336896, 1516596096, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 0, NULL, 1, 903),
(11, 1, 8999.00, NULL, 'COD', 1516407895, 1516667095, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 2, 0, 2, 1, 903),
(12, 2, 17998.00, NULL, 'COD', 1516408908, 1516668108, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 2, 0, 1, 1, 903),
(13, 2, 17998.00, NULL, 'COD', 1516408934, 1516668134, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 1, 0, 2, 1, 904),
(14, 3, 33848.00, NULL, 'paypal', 1516409136, 1516668336, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 1, 0, NULL, 1, 904),
(15, 1, 9999.00, NULL, 'COD', 1516450877, 1516710077, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 1, 0, NULL, 1, 904),
(16, 1, 499.00, NULL, 'visa_mastercard', 1516466530, 1516725730, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 0, 1, 1, 903),
(17, 1, 35490.00, NULL, 'COD', 1484288745, 1484323452, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(18, 1, 25400.00, NULL, 'visa_mastercard', 1487658345, 1487744745, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(19, 1, 14990.00, NULL, 'paypal', 1490509545, 1490675362, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(20, 2, 13000.00, NULL, 'paypal', 1492322084, 1492386884, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(21, 2, 24980.00, NULL, 'COD', 1494042884, 1494129284, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(22, 1, 45990.00, NULL, 'visa_mastercard', 1497169446, 1497236084, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(23, 2, 83998.00, NULL, 'COD', 1500985446, 1501125846, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(24, 2, 38599.00, NULL, 'COD', 1503845093, 1503897873, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(25, 2, 15099.00, NULL, 'COD', 1505694735, 1505781135, '4509 Nutter St.', 3, 1, 2, 8, 904),
(26, 2, 50480.00, NULL, 'COD', 1508995935, 1509255135, '4509 Nutter St., Belton, St. Louis, Missouri', 3, 1, 2, 8, 904),
(27, 3, 116970.00, NULL, 'COD', 1509892335, 1510014735, '4509 Nutter St., Belton, St. Louis, Missouri', 3, 1, 2, 8, 904),
(28, 4, 115397.00, NULL, 'paypal', 1513902735, 1514003535, '4509 Nutter St., Belton, St. Louis, Missouri', 3, 1, 2, 8, 904),
(29, 1, 39990.00, NULL, 'visa_mastercard', 1517056867, 1517316067, '2264 Sundown Lane', 3, 1, 1, 9, 904),
(30, 2, 70989.00, NULL, 'visa_mastercard', 1517057098, 1517316298, '1403 Sawft', 3, 1, 2, 10, 904),
(31, 2, 47590.00, NULL, 'paypal', 1517057440, 1517316640, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(32, 1, 41999.00, NULL, 'COD', 1517057516, 1517316716, '1403 Sawft, Sampaloc, Manila, Metro Manila', 3, 1, 2, 10, 904),
(33, 1, 45990.00, NULL, 'paypal', 1517060192, 1517319392, '1122 Coquina', 3, 1, 2, 11, 904),
(34, 1, 46060.00, NULL, 'COD', 1517366748, 1517625948, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 1, 0, 2, 1, 904),
(36, 3, 89150.00, NULL, 'visa_mastercard', 1517408267, 1517667467, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(37, 0, 70.00, NULL, 'visa_mastercard', 1517409790, 1517668990, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 1, 0, NULL, 1, 904),
(38, 3, 47049.00, NULL, 'visa_mastercard', 1517409833, 1517669033, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 903),
(39, 1, 40060.00, NULL, 'visa_mastercard', 1517546454, 1517805654, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(40, 1, 15060.00, NULL, 'visa_mastercard', 1517611033, 1517870233, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(41, 3, 65568.00, NULL, 'COD', 1517984665, 1518243865, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(42, 2, 52559.00, NULL, 'paypal', 1517984753, 1518243953, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(43, 2, 29069.00, NULL, 'visa_mastercard', 1517984820, 1518244020, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(44, 3, 94558.00, NULL, 'paypal', 1517984999, 1518244199, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(45, 2, 54069.00, NULL, 'visa_mastercard', 1517985066, 1518244266, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(46, 2, 29069.00, NULL, 'visa_mastercard', 1517985096, 1518244296, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(47, 2, 54069.00, NULL, 'COD', 1517985122, 1518244322, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(48, 4, 77568.00, NULL, 'visa_mastercard', 1517985194, 1518244394, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(49, 3, 71068.00, NULL, 'COD', 1517985335, 1518244535, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(50, 3, 86550.00, NULL, '', 1518317662, 1518576862, '2264 Sundown Lane, Connect, San Antonio, Texas', 3, 1, 2, 9, 904),
(51, 2, 81550.00, NULL, 'visa_mastercard', 1518319218, 1518537600, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(52, 3, 98290.00, NULL, 'visa_mastercard', 1518319284, 1518537600, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 903),
(53, 3, 42050.00, NULL, 'COD', 1518347219, 1518606419, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(54, 2, 27550.00, NULL, 'COD', 1518347304, 1518606504, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(55, 4, 64048.00, NULL, 'visa_mastercard', 1518347373, 1518606573, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(56, 2, 77559.00, NULL, 'visa_mastercard', 1518347662, 1518606862, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 0, 2, 1, 904),
(57, 3, 39060.00, NULL, 'paypal', **********, **********, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(58, 5, 251520.00, NULL, '', **********, **********, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(59, 1, 39990.00, NULL, 'paypal', **********, **********, '', 3, 1, 2, 12, 904),
(60, 1, 16740.00, NULL, 'COD', **********, **********, '', 3, 1, 2, 13, 904),
(61, 1, 21289.00, NULL, 'bank_dep', **********, **********, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(62, 1, 12490.00, NULL, 'COD', **********, **********, '', 3, 1, 2, 14, 904),
(63, 2, 24080.00, NULL, 'visa_mastercard', **********, **********, '1101 Tilted Bldg., Maceda St., Brgy. Tilted, Titled Place, Metro Manila', 3, 1, 2, 1, 904),
(64, 1, 35490.00, NULL, 'visa_mastercard', **********, **********, 'As', 3, 1, 2, 18, 904),
(65, 2, 10160.00, NULL, 'bank_dep', **********, **********, 'Jnkjnjk,,Kjnn,', 3, 1, 2, 19, 904),
(66, 1, 1080.00, NULL, 'bank_dep', **********, **********, 'Paredes St. #11, Brgy. FEU, Quezon City, None', 3, 1, 2, 4, 904),
(67, 1, 1170.00, NULL, 'bank_dep', **********, **********, '4509 Nutter St., Belton, St. Louis, Missouri', 3, 1, 2, 8, 904),
(68, 2, 3770.00, NULL, 'bank_dep', **********, **********, '4509 Nutter St., Belton, St. Louis, Missouri', 3, 1, 2, 8, 904),
(69, 1, 42069.00, NULL, 'bank_dep', **********, **********, '4509 Nutter St., Belton, St. Louis, Missouri', 3, 1, 2, 8, 904),
(70, 2, 3928.00, NULL, 'bank_dep', **********, **********, '4509 Nutter St., Belton, St. Louis, Missouri', 3, 1, 2, 8, 904);

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `orderitems_id` int(12) UNSIGNED NOT NULL,
  `product_name` varchar(250) NOT NULL,
  `product_price` float(8,2) UNSIGNED NOT NULL,
  `product_subtotal` float(8,2) UNSIGNED NOT NULL,
  `quantity` int(5) UNSIGNED DEFAULT NULL,
  `product_image1` varchar(250) NOT NULL,
  `order_id` int(12) UNSIGNED NOT NULL,
  `product_id` int(12) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`orderitems_id`, `product_name`, `product_price`, `product_subtotal`, `quantity`, `product_image1`, `order_id`, `product_id`) VALUES
(1, 'iPhone 7', 35490.00, 0.00, 1, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 17, 75),
(2, 'HP Stream 11.6" Celeron Laptop', 25400.00, 0.00, 1, '79894dd530010a23f536f5607aea5c4d.jpg', 18, 71),
(3, 'Apple iPad Mini 2', 14990.00, 0.00, 1, 'a861ace37697ac911798e89073e8d3d7.jpg', 19, 84),
(4, 'ZenFone 3 Max ZC520TL', 6500.00, 0.00, 2, 'b15969f923e4c8b71914df733addb5e3.jpg', 20, 80),
(5, 'Samsung J7', 12490.00, 0.00, 2, 'ae4c7195898417e688fa5c8bda95dbbc.jpg', 21, 78),
(6, 'iPhone 8', 45990.00, 0.00, 1, '02173be3ece0a965ceda82c1e9f2670d.jpeg', 22, 74),
(7, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 2, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 23, 70),
(8, 'HP Laptop - 15z touch optional', 30999.00, 0.00, 1, '9e6b7401be8e1d07334e7400394fdabd.png', 24, 67),
(9, 'Samsung Galaxy Tab A 7.0', 7600.00, 0.00, 1, '521efa1534dc8c29a1876c338415fd15.jpg', 24, 82),
(10, 'Samsung S4', 7499.00, 0.00, 1, '12c8ad001f256b55c3a2fb55e427c052.jpg', 25, 79),
(11, 'Apple iPad Mini 2', 14990.00, 0.00, 1, 'a861ace37697ac911798e89073e8d3d7.jpg', 26, 84),
(12, 'iPhone 8', 45990.00, 0.00, 1, '02173be3ece0a965ceda82c1e9f2670d.jpeg', 27, 74),
(13, 'iPhone 7', 35490.00, 0.00, 2, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 27, 75),
(14, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 28, 70),
(15, 'HP Laptop - 15z touch optional', 30999.00, 0.00, 1, '9e6b7401be8e1d07334e7400394fdabd.png', 28, 67),
(16, 'HP Stream 11.6" Celeron Laptop', 25400.00, 0.00, 1, '79894dd530010a23f536f5607aea5c4d.jpg', 28, 71),
(17, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 1, 'd6a65f8a81af237e472b33ce66751cba.jpg', 28, 72),
(18, 'Apple iPad Pro', 39990.00, 0.00, 1, '84d1099989505d1e9b7740e1c4a5c7b1.png', 29, 73),
(19, 'HP Laptop - 15z touch optional', 30999.00, 0.00, 1, '9e6b7401be8e1d07334e7400394fdabd.png', 30, 67),
(20, 'Apple iPad Pro', 39990.00, 0.00, 1, '84d1099989505d1e9b7740e1c4a5c7b1.png', 30, 73),
(21, 'Samsung Galaxy Tab A 7.0', 7600.00, 0.00, 1, '521efa1534dc8c29a1876c338415fd15.jpg', 31, 82),
(22, 'Apple iPad Pro', 39990.00, 0.00, 1, '84d1099989505d1e9b7740e1c4a5c7b1.png', 31, 73),
(23, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 32, 70),
(24, 'iPhone 8', 45990.00, 0.00, 1, '02173be3ece0a965ceda82c1e9f2670d.jpeg', 33, 74),
(26, 'Samsung Galaxy Tab A 7.0', 7600.00, 0.00, 1, '521efa1534dc8c29a1876c338415fd15.jpg', 36, 82),
(27, 'iPhone 8', 45990.00, 0.00, 1, '02173be3ece0a965ceda82c1e9f2670d.jpeg', 36, 74),
(28, 'iPhone 7', 35490.00, 0.00, 1, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 36, 75),
(29, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 1, 'd6a65f8a81af237e472b33ce66751cba.jpg', 38, 72),
(30, 'Apple iPad Mini 2', 14990.00, 0.00, 3, 'a861ace37697ac911798e89073e8d3d7.jpg', 38, 84),
(31, 'Apple iPad Pro', 39990.00, 0.00, 1, '84d1099989505d1e9b7740e1c4a5c7b1.png', 39, 73),
(32, 'Apple iPad Mini 2', 14990.00, 0.00, 1, 'a861ace37697ac911798e89073e8d3d7.jpg', 40, 84),
(33, 'ZenFone 3 Max ZC520TL', 6500.00, 0.00, 1, 'b15969f923e4c8b71914df733addb5e3.jpg', 41, 80),
(34, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 1, 'd6a65f8a81af237e472b33ce66751cba.jpg', 41, 72),
(35, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 41, 70),
(36, 'iPhone 7', 35490.00, 0.00, 1, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 42, 75),
(37, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 1, 'd6a65f8a81af237e472b33ce66751cba.jpg', 42, 72),
(38, 'Samsung Galaxy C5', 12000.00, 0.00, 1, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 43, 77),
(39, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 1, 'd6a65f8a81af237e472b33ce66751cba.jpg', 43, 72),
(40, 'iPhone 7', 35490.00, 0.00, 1, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 44, 75),
(41, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 1, 'd6a65f8a81af237e472b33ce66751cba.jpg', 44, 72),
(42, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 44, 70),
(43, 'Samsung Galaxy C5', 12000.00, 0.00, 1, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 45, 77),
(44, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 45, 70),
(45, 'Samsung Galaxy C5', 12000.00, 0.00, 1, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 46, 77),
(46, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 1, 'd6a65f8a81af237e472b33ce66751cba.jpg', 46, 72),
(47, 'Samsung Galaxy C5', 12000.00, 0.00, 1, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 47, 77),
(48, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 47, 70),
(49, 'ZenFone 3 Max ZC520TL', 6500.00, 0.00, 1, 'b15969f923e4c8b71914df733addb5e3.jpg', 48, 80),
(50, 'Samsung Galaxy C5', 12000.00, 0.00, 1, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 48, 77),
(51, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 1, 'd6a65f8a81af237e472b33ce66751cba.jpg', 48, 72),
(52, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 48, 70),
(53, 'Samsung Galaxy C5', 12000.00, 0.00, 1, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 49, 77),
(54, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 1, 'd6a65f8a81af237e472b33ce66751cba.jpg', 49, 72),
(55, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 49, 70),
(56, 'ZenFone 3 Max ZC520TL', 6500.00, 0.00, 1, 'b15969f923e4c8b71914df733addb5e3.jpg', 50, 80),
(57, 'iPhone X', 64990.00, 0.00, 1, '3b9fed4e98127f41c60e63b271e9757c.jpg', 50, 85),
(58, 'Apple iPad Mini 2', 14990.00, 0.00, 1, 'a861ace37697ac911798e89073e8d3d7.jpg', 50, 84),
(59, 'iPhone 8', 45990.00, 0.00, 1, '02173be3ece0a965ceda82c1e9f2670d.jpeg', 51, 74),
(60, 'iPhone 7', 35490.00, 0.00, 1, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 51, 75),
(61, 'iPhone 8', 45990.00, 0.00, 1, '02173be3ece0a965ceda82c1e9f2670d.jpeg', 52, 74),
(62, 'Lenovo - 15.6" Laptop - AMD A6-Series', 16740.00, 0.00, 1, 'ead536687dbbfd1c0d2b5d9981d7d6b1.jpg', 52, 68),
(63, 'iPhone 7', 35490.00, 0.00, 1, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 52, 75),
(64, 'Samsung Galaxy C5', 12000.00, 0.00, 1, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 53, 77),
(65, 'Apple iPad Mini 2', 14990.00, 0.00, 2, 'a861ace37697ac911798e89073e8d3d7.jpg', 53, 84),
(66, 'Samsung J7', 12490.00, 0.00, 1, 'ae4c7195898417e688fa5c8bda95dbbc.jpg', 54, 78),
(67, 'Apple iPad Mini 2', 14990.00, 0.00, 1, 'a861ace37697ac911798e89073e8d3d7.jpg', 54, 84),
(68, 'ACER ASPIRE ES1 332 BLACK', 16999.00, 0.00, 2, 'd6a65f8a81af237e472b33ce66751cba.jpg', 55, 72),
(69, 'Apple iPad Mini 2', 14990.00, 0.00, 2, 'a861ace37697ac911798e89073e8d3d7.jpg', 55, 84),
(70, 'iPhone 7', 35490.00, 0.00, 1, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 56, 75),
(71, 'ASUS Laptop X556UQ-NH71', 41999.00, 0.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 56, 70),
(72, 'Apple iPad Mini 2', 14990.00, 0.00, 1, 'a861ace37697ac911798e89073e8d3d7.jpg', 57, 84),
(73, 'Samsung Galaxy C5', 12000.00, 0.00, 2, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 57, 77),
(74, 'Apple iPad Pro', 39990.00, 39990.00, 1, '84d1099989505d1e9b7740e1c4a5c7b1.png', 58, 73),
(75, 'iPhone 7', 35490.00, 35490.00, 1, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 58, 75),
(76, 'iPhone X', 64990.00, 129980.00, 2, '3b9fed4e98127f41c60e63b271e9757c.jpg', 58, 85),
(77, 'iPhone 8', 45990.00, 45990.00, 1, '02173be3ece0a965ceda82c1e9f2670d.jpeg', 58, 74),
(78, 'Apple iPad Pro', 39990.00, 0.00, 1, '84d1099989505d1e9b7740e1c4a5c7b1.png', 59, 73),
(79, 'Lenovo - 15.6', 16740.00, 0.00, 1, 'ead536687dbbfd1c0d2b5d9981d7d6b1.jpg', 60, 68),
(80, 'iPad Air 2', 20499.00, 0.00, 1, '68c0ba45fb7ac086d49debd62252d560.jpg', 61, 81),
(81, 'Samsung J7', 12490.00, 0.00, 1, 'ae4c7195898417e688fa5c8bda95dbbc.jpg', 62, 78),
(82, 'Samsung Galaxy C5', 12000.00, 24000.00, 2, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 63, 77),
(83, 'iPhone 7', 35490.00, 35490.00, 1, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', 64, 75),
(84, 'Apple 30-pin to USB Cable', 1090.00, 1090.00, 1, '61a525c779fa964263cb7fdfb21fa40f.jpeg', 65, 94),
(85, 'Apple Airpods', 8990.00, 8990.00, 1, 'e1e2df3fe8a93d3f2762e994201f2bd1.jpeg', 65, 93),
(86, 'Apple Earphones', 1000.00, 1000.00, 1, 'bfbca836ca30d59d90cef52ca1086bdb.jpg', 66, 90),
(87, 'Apple 12W USB Power Adapter', 1090.00, 1090.00, 1, 'ccf40c256279bc39aa0beb9c0a7c30e1.jpeg', 67, 95),
(88, 'Apple Earphones', 1000.00, 1000.00, 1, 'bfbca836ca30d59d90cef52ca1086bdb.jpg', 68, 90),
(89, 'Apple Lightning Digital AV Adapter', 2690.00, 2690.00, 1, '1bcbf452f40c3084abca62b0142d9887.jpeg', 68, 96),
(90, 'ASUS Laptop X556UQ-NH71', 41999.00, 41999.00, 1, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', 69, 70),
(91, 'Romoss Solo 5 Powerbank', 699.00, 699.00, 1, 'd37643e4352910366914a894b0f3d301.jpg', 70, 91),
(92, 'Aukey PB-T10 20,000mah Power Bank with Quick Charge 3.0', 3149.00, 3149.00, 1, '59e6b67237f7f642e6d4ec509458fc27.jpg', 70, 104);

-- --------------------------------------------------------

--
-- Table structure for table `order_status`
--

CREATE TABLE `order_status` (
  `orderstatus_id` int(11) NOT NULL,
  `description_status` varchar(250) NOT NULL,
  `customer_id` int(12) UNSIGNED NOT NULL,
  `order_id` int(12) UNSIGNED NOT NULL,
  `transaction_date` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `order_status`
--

INSERT INTO `order_status` (`orderstatus_id`, `description_status`, `customer_id`, `order_id`, `transaction_date`) VALUES
(5, 'Your item(s) is being packed and ready for shipment at our merchant''s warehouse.', 1, 61, **********),
(6, 'Thank you for shopping with Technoholics! your order has arrived at your location.', 1, 58, **********),
(7, 'Changed delivery date of order #58 to 2018-02-21', 1, 58, **********),
(8, 'Your item(s) is being packed and ready for shipment at our merchant''s warehouse.', 1, 63, 1519273633),
(9, 'Your item(s) is being packed and ready for shipment at our merchant''s warehouse.', 18, 64, 1519421268),
(10, 'Thank you for shopping with Technoholics! your order has arrived at your location.', 12, 59, 1519959531),
(11, 'Changed delivery date of order #59 to 2018-02-22', 12, 59, 1519959531),
(12, 'Your order is being verified please check track my order page for more updates.', 8, 70, **********),
(13, 'Your order has been cancelled.', 8, 70, 1528296830),
(14, 'Your order has been cancelled.', 8, 70, 1528297085),
(15, 'Your order has been cancelled.', 8, 70, 1528297535),
(16, 'Your order has been confirmed, and is now waiting to be verified.', 8, 70, 1528298008),
(17, 'Your order has been confirmed, and is now waiting to be verified.', 8, 70, 1528298281),
(18, 'Your order has been confirmed, and is now waiting to be verified.', 8, 70, 1528298423),
(19, 'Your order has been cancelled.', 8, 70, 1528298456),
(20, 'Your order has been confirmed, and is now waiting to be verified.', 8, 70, 1528298538),
(21, 'Your order has been verified and is now being shipped to your address.', 8, 70, 1528298720),
(22, 'Your order has been confirmed, and is now waiting to be verified.', 8, 70, 1528298760),
(23, 'Thank you for shopping with Technoholics! Questions? Email <NAME_EMAIL>.', 8, 70, **********);

-- --------------------------------------------------------

--
-- Table structure for table `payment`
--

CREATE TABLE `payment` (
  `payment_id` int(12) UNSIGNED NOT NULL,
  `payment_detail` varchar(200) NOT NULL,
  `payment_date` int(11) NOT NULL,
  `customer_id` int(12) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `payment`
--

INSERT INTO `payment` (`payment_id`, `payment_detail`, `payment_date`, `customer_id`) VALUES
(1, 'bank_dep', **********, 19),
(2, 'bank_dep', **********, 4),
(3, 'bank_dep', **********, 8),
(4, 'bank_dep', **********, 8),
(5, 'bank_dep', **********, 8),
(6, 'bank_dep', **********, 8);

-- --------------------------------------------------------

--
-- Table structure for table `product`
--

CREATE TABLE `product` (
  `product_id` int(12) UNSIGNED NOT NULL,
  `product_name` varchar(250) NOT NULL,
  `product_desc` text NOT NULL,
  `product_brand` varchar(40) NOT NULL,
  `product_category` varchar(40) NOT NULL,
  `product_price` float(8,2) UNSIGNED NOT NULL,
  `product_discount` int(3) NOT NULL DEFAULT '0',
  `product_quantity` int(5) UNSIGNED NOT NULL,
  `product_rating` float(2,1) UNSIGNED NOT NULL DEFAULT '0.0',
  `no_of_views` int(8) UNSIGNED NOT NULL DEFAULT '0',
  `times_searched` int(8) UNSIGNED NOT NULL DEFAULT '0',
  `times_bought` int(8) UNSIGNED NOT NULL DEFAULT '0',
  `is_featured` tinyint(1) UNSIGNED NOT NULL DEFAULT '0',
  `product_image1` varchar(250) NOT NULL,
  `product_image2` varchar(250) DEFAULT NULL,
  `product_image3` varchar(250) DEFAULT NULL,
  `product_image4` varchar(250) DEFAULT NULL,
  `added_at` int(11) NOT NULL,
  `updated_at` int(11) NOT NULL,
  `batch_number` varchar(7) NOT NULL,
  `status` int(1) UNSIGNED NOT NULL DEFAULT '1',
  `supplier_id` int(12) UNSIGNED NOT NULL,
  `admin_id` int(12) UNSIGNED DEFAULT NULL,
  `category_id` int(12) UNSIGNED NOT NULL,
  `brand_id` int(12) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `product`
--

INSERT INTO `product` (`product_id`, `product_name`, `product_desc`, `product_brand`, `product_category`, `product_price`, `product_discount`, `product_quantity`, `product_rating`, `no_of_views`, `times_searched`, `times_bought`, `is_featured`, `product_image1`, `product_image2`, `product_image3`, `product_image4`, `added_at`, `updated_at`, `batch_number`, `status`, `supplier_id`, `admin_id`, `category_id`, `brand_id`) VALUES
(60, 'Zenfone Go', 'zenfone go RED', 'asus', 'smartphones', 5000.00, 0, 5, 0.0, 0, 0, 0, 0, 'df5422b7165f0348dc28ef36c88eebf9.jpg', NULL, NULL, NULL, 1516603039, 0, 'Jan2218', 0, 801, 2, 702, 602),
(67, 'HP Laptop - 15z touch optional', 'Windows 10 Home 64\r\nAMD Dual-Core A9 APU\r\n8 GB memory; 1 TB HDD storage\r\nAMD Radeon™ R5 Graphics\r\n15.6&quot; diagonal HD display', 'hp', 'laptops', 30999.00, 0, 2, 4.0, 17, 11, 1, 0, '9e6b7401be8e1d07334e7400394fdabd.png', 'ac86eedeaf428f04d9e958e9faa4cb00.png', '12f38c5acd95fb986363cf395c58778e.png', 'a1be94f222d1e4bae7eb25d59576da80.png', 1516630134, 0, 'Jan2218', 1, 802, 2, 703, 607),
(68, 'Lenovo - 15.6" Laptop - AMD A6-Series', 'Lenovo 110-15ACL Laptop: Enjoy productivity anywhere with this 15.6-inch Lenovo Ideapad laptop. Its 500GB of storage holds plenty of large applications and documents, and its built-in optical drive lets you read and write digital files. The quad-core', 'lenovo', 'laptops', 16740.00, 0, 3, 0.0, 20, 2, 6, 0, 'ead536687dbbfd1c0d2b5d9981d7d6b1.jpg', '5873a2b88c69b11909671a0a8ee275d9.jpg', 'c3ff1872430ea7831fca017e3b8adf34.jpg', 'b96024033337b0287a2bbec47790cdbd.jpg', 1516630278, 0, 'Jan2218', 1, 802, 2, 703, 604),
(69, 'HP Spectre x360 Laptop - 15&quot; Touch', 'Windows 10 Home 64 with Windows Ink\r\n7th Generation Intel® Core™ i7 processor\r\n16 GB memory; 512 GB SSD storage\r\nNVIDIA® GeForce® 940MX (2 GB GDDR5 dedicated)\r\n15.6&quot; diagonal UHD UWVA eDP touch', 'hp', 'laptops', 76999.00, 0, 6, 0.0, 0, 0, 3, 0, 'hp_spectre_1.png', 'be31cdd4e3bb9bb79a6c8bcc9c010e7e.png', '1ddad77d384ec29bb7d5abd7f3dc2d30.png', '01ce373678f64afd794f2f50cc177080.png', 1516630462, 0, 'Jan2218', 0, 802, 2, 703, 607),
(70, 'ASUS Laptop X556UQ-NH71', 'Powerful & efficient Intel­ Core i7-7500U 2.7GHz (Turbo up to 3.5GHz) Processor\r\nNVIDIA GeForce 940MX graphics; 8GB DDR4 RAM\r\n512GB 2.5" SSD; Dual-layer DVD-RW drive; Ergonomic chiclet keyboard with number pad\r\nLightning-fast dual band 802.1', 'asus', 'laptops', 41999.00, 0, 19, 2.0, 51, 24, 8, 0, 'c819ab0df1bea8bcce14a7a9e92ddb4d.jpg', '9e491142d4cfff5c14aa1e5234119e63.jpg', 'b60d87056520114f38abfe862c46d0b2.jpg', '5c3b37519b2342fe4f96c52514bcd2aa.jpg', 1516630595, 1518012371, 'Jan2218', 1, 803, 2, 703, 602),
(71, 'HP Stream 11.6" Celeron Laptop', 'Operating System: Windows 10\r\nCPU (Model + Speed): Intel Celeron N3050 1.6GHz Processor\r\nRAM and Hard Disk Drive (HDD): 2GB RAM and 32GB eMMC\r\nGraphics: Intel HD Graphics', 'hp', 'laptops', 25400.00, 0, 6, 4.0, 10, 0, 0, 0, '79894dd530010a23f536f5607aea5c4d.jpg', '3bd42edc91a9b6fe4196385ef62f71e5.jpg', 'bed28d817d167c710b112dad0b6b3e53.jpg', '05355179c0c154fe427c92221be8faa2.jpg', 1516631606, 0, 'Jan2218', 1, 802, 2, 703, 607),
(72, 'ACER ASPIRE ES1 332 BLACK', 'Acer Aspire ES1-332-C8FS - Midnight Black is a 13.3" notebook powered by Intel Celeron N3450 processor with 2MB of cache, with Intel HD Graphics, equipped with 64-bit Windows 10 Operating System. It runs at 1.6 GHz with 4Gb of RAM and 1600 MHz o', 'acer', 'laptops', 16999.00, 10, 13, 4.0, 196, 0, 13, 0, 'd6a65f8a81af237e472b33ce66751cba.jpg', NULL, NULL, NULL, 1516632423, 1528298968, 'Jan2218', 1, 803, 2, 703, 605),
(73, 'Apple iPad Pro', 'Apple iPad Pro 12.9-inch Wi-Fi Gold 64GB', 'apple', 'Tablets', 39990.00, 0, 14, 0.0, 30, 3, 3, 1, '84d1099989505d1e9b7740e1c4a5c7b1.png', NULL, NULL, NULL, 1516633112, 1520103056, 'Jan2218', 1, 804, 2, 705, 601),
(74, 'iPhone 8', '4.7-inch Retina HD display with True Tone\r\nAll-glass and aluminum design, water and dust resistant\r\n12MP camera with 4K video up to 60 fps\r\n7MP FaceTime HD camera with Retina Flash for stunning selfies\r\nTouch ID for secure authentication\r\nA11 Bionic,', 'apple', 'smartphones', 45990.00, 0, 29, 1.5, 158, 35, 9, 1, '02173be3ece0a965ceda82c1e9f2670d.jpeg', 'fa8856ad7b6c82a27bd4ed8748a439f2.jpeg', '974e53a95a78044fa8f25d006f886ba3.jpeg', '4963604fd06a9194402af2169a4e0296.jpeg', 1516633254, 0, 'Jan2218', 1, 802, 2, 702, 601),
(75, 'iPhone 7', '4.7-inch Retina HD display\r\nWater and dust resistant\r\n12MP camera with 4K video at 30 fps\r\n7MP FaceTime HD camera with Retina Flash for stunning selfies\r\nTouch ID for secure authentication and Apple Pay\r\nA10 Fusion chip', 'apple', 'smartphones', 35490.00, 0, 39, 4.5, 124, 27, 7, 0, 'ed84b2b347955d92d5b4bbe2363d1655.jpeg', '06106fb33115444fe453086282b08316.jpeg', 'b352cc068d5e3a0b29b8aa3f6de42743.jpeg', '8c4f3fe446ea85d6e3aa62674f045bd0.jpg', 1516633371, 1518010916, 'Jan2218', 1, 802, 2, 702, 601),
(76, 'Zenfone Go ZB551KL 16GBv', 'Asus Zenfone Go ZB551KL 16GB', 'asus', 'smartphones', 5500.00, 0, 5, 0.0, 0, 19, 0, 0, 'asusgo_1.png', 'd0d78385f749c69e62277e222b66f6e3.jpg', 'c72bb64c9794e1fb723abc7f2fbb0a1f.jpg', '1e9eec4383cf747450bc6fd4c6ddd4cc.jpg', 1516633645, 0, 'Jan2218', 0, 802, 2, 702, 602),
(77, 'Samsung Galaxy C5', 'The Samsung Galaxy C5 is powered by 1.2GHz octa-core Qualcomm Snapdragon 617 processor and it comes with 4GB of RAM. The phone packs 32GB of internal storage that can be expanded up to 128GB via a microSD card. As far as the cameras are concerned, th', 'samsung', 'smartphones', 12000.00, 0, 5, 2.0, 14, 22, 13, 0, '55afb1383d9e6b8c006a2aef0d4d5d23.jpg', 'fa2173369c1fa8de2dbd289040b852f6.jpg', '1a173e733694212c1cd6b85e2a04c639.jpg', '87af405bf109748db10f4166ad12f380.jpg', 1516633900, 1518010993, 'Jan2218', 1, 801, 2, 702, 603),
(78, 'Samsung J7', 'Samsung Galaxy J7 Pro - Black is a Dual SIM smartphone with a 5.5-inch display with a resolution of 1080 x 1920 pixels. It operates on Android Nougat and is powered by a 3600mAh non-removable Li-Ion battery. Under the hood, the Samsung Galaxy J7 Pro ', 'samsung', 'smartphones', 12490.00, 0, 15, 2.0, 16, 21, 7, 0, 'ae4c7195898417e688fa5c8bda95dbbc.jpg', NULL, NULL, NULL, 1516635326, 1518010958, 'Jan2218', 1, 804, 2, 702, 603),
(79, 'Samsung S4', 'Samsung Galaxy S4', 'samsung', 'smartphones', 7499.00, 0, 5, 3.0, 13, 20, 0, 0, '12c8ad001f256b55c3a2fb55e427c052.jpg', 'a6a5a5a1294509f7cf64f3a45715feda.jpg', 'a57fd558037ba542c10f485346e8810b.jpg', '2809f8c7c80a56b66b94a9ce124a67a8.jpg', 1516635770, 0, 'Jan2218', 1, 802, 2, 702, 603),
(80, 'ZenFone 3 Max ZC520TL', 'ZenFone 3 Max is the 5.2-inch smartphone eliminates battery life worries, with enough power to get you through a full work day, and even beyond! With its high-capacity 4100mAh cell ZenFone 3 Max just keeps on going, with standby that lasts up to 30 d', 'asus', 'smartphones', 6500.00, 0, 0, 0.0, 37, 26, 3, 1, 'b15969f923e4c8b71914df733addb5e3.jpg', NULL, NULL, NULL, 1516671804, 1520103554, 'Jan2318', 1, 802, 1, 702, 602),
(81, 'iPad Air 2', 'Wi-Fi (802.11a/b/g/n/ac)\r\nBluetooth 4.0 technology \r\n9.7-inch Retina display\r\n8-megapixel iSight camera\r\nFaceTime HD camera\r\n1080p HD video recording\r\nA8X chip with 64-bit architecture\r\nM8 motion coprocessor\r\n10-hour battery life', 'apple', 'tablets', 20499.00, 0, 16, 2.5, 31, 15, 1, 0, '68c0ba45fb7ac086d49debd62252d560.jpg', '4759bd8282d9f7c3c009bb6e4256d7a4.jpg', '1dcadd5720ee74875cf5d76f36db601f.jpg', '4fea15de7ed62ebd6b6cc1eb2fc1762c.jpg', 1516671979, 1518010333, 'Jan2318', 1, 803, 2, 705, 601),
(82, 'Samsung Galaxy Tab A 7.0', 'The 2016 model is 7 inches display, 8GB of storage, runs Android 5.1 Lollipop with 1.3/1.5 GHz Quad Core processor.', 'samsung', 'tablets', 7600.00, 0, 2, 4.0, 9, 10, 0, 0, '521efa1534dc8c29a1876c338415fd15.jpg', 'bd579cbe335bc870ab81ac790729f923.jpg', '07974bc8bc7d380b4f5909cceccd26cb.jpg', '091837347f7bf125c39d50f2b0fa3da2.jpg', 1516672123, 0, 'Jan2318', 1, 802, 2, 705, 603),
(84, 'Apple iPad Mini 2', 'Apple iPad Mini 2 16GB', 'apple', 'tablets', 14990.00, 0, 5, 3.0, 33, 14, 19, 0, 'a861ace37697ac911798e89073e8d3d7.jpg', NULL, NULL, NULL, 1516672956, 1518012665, 'Jan2318', 1, 804, 2, 705, 601),
(85, 'iPhone X', 'Apple iPhone X 64GB', 'apple', 'smartphones', 64990.00, 0, 21, 2.7, 234, 56, 3, 1, '3b9fed4e98127f41c60e63b271e9757c.jpg', '9ea9b32beb5608e88d304d6026a559ad.jpg', 'dbe2b9c704ec31e472791aff91b492e2.png', 'c429537233261c42399c9a8638871960.png', 1516673339, 1518010892, 'Jan2318', 1, 804, 2, 702, 601),
(86, 'gaisfgjkbasfk', 'asfasf', 'dell', 'laptops', 5515.00, 0, 13, 0.0, 0, 0, 0, 0, '77e2bcebb93c1966540ac8e1c64f6b83.jpg', NULL, NULL, NULL, 1517206925, 0, 'Jan2918', 1, 801, 2, 703, 606),
(87, 'sfsafsa', 'cxbxcbxc', 'asus', 'accessories', 4242.00, 0, 23, 0.0, 0, 0, 0, 0, '', NULL, NULL, NULL, 1518010726, 0, 'Feb0718', 0, 802, 2, 701, 602),
(89, 'Dell Inspiron 3467', 'Processor Type Intel Core i5\r\nDisplay Size 14.0\r\nGraphics Card AMD\r\nGraphics Memory 2GB\r\nHard Drive Capacity 500GB\r\nModel 3467-I57200U\r\nOperating System Windows 10\r\nProcessor Type Dual-core\r\nSystem Memory 4GB', 'dell', 'laptops', 32990.00, 0, 5, 0.0, 11, 0, 0, 1, 'ef00595ea3331a022ec3da5475c0ca85.jpg', NULL, NULL, NULL, **********, **********, 'Feb2118', 1, 801, 1, 703, 606),
(90, 'Apple Earphones', 'Frequency Response: 5Hz to 21kHz\r\n\r\nDrivers: Custom two-way balanced armature (woofer and tweeter in each earpiece)\r\n\r\nWeight: 0.4 ounces (10.2 grams)\r\nCable Length: 42 in./106 cm; 13 in./330 mm to earpiece\r\nGeneral\r\nWith Remote and Mic', 'apple', 'accessories', 1000.00, 0, 8, 4.0, 21, 0, 2, 0, 'bfbca836ca30d59d90cef52ca1086bdb.jpg', NULL, NULL, NULL, **********, 0, 'Feb2118', 1, 804, 2, 701, 601),
(91, 'Romoss Solo 5 Powerbank', '10000 mAh\r\nDual Output', 'romoss', 'accessories', 699.00, 0, 8, 0.0, 3, 1, 1, 0, 'd37643e4352910366914a894b0f3d301.jpg', '0d75bb08772ed1914e6f353c0791aca9.jpg', NULL, NULL, **********, 0, 'Feb2118', 1, 803, 2, 701, 609),
(92, 'Acer Chromebook CB3-131-C4NW N2840', 'Intel Celeron N2840 2.16 GHz\r\n2 GB DDR3L SDRAM\r\n16 GB Internal Storage\r\n11.6-Inch HD IPS Screen, Intel HD Graphics\r\nPatterned Aluminum Cover, Google Chrome, Up to 9-hour battery life', 'acer', 'laptops', 9999.00, 0, 26, 0.0, 1, 0, 0, 0, '67425ea9c71222f35eff5c17a93c7b53.png', NULL, NULL, NULL, **********, **********, 'Feb2118', 1, 802, 2, 703, 605),
(93, 'Apple Airpods', 'WIRELESS. EFFORTLESS. MAGICAL.\r\nAirPods will forever change the way you use headphones. Whenever you pull your AirPods out of the charging case, they instantly turn on and connect to your iPhone, Apple Watch, iPad or Mac.(1) Audio automatically plays as soon as you put them in your ears and pauses when you take them out. To adjust the volume, change the song, make a call, or even get directions, just double-tap to activate Siri.\r\n\r\nDriven by the custom Apple W1 chip, AirPods use optical sensors and a motion accelerometer to detect when they’re in your ears. Whether you’re using both AirPods or just one, the W1 chip automatically routes the audio and engages the microphone. And when you’re on a call or talking to Siri, an additional accelerometer works with beamforming microphones to filter out background noise and focus on the sound of your voice. Because the ultralow-power W1 chip manages battery life so well, AirPods deliver an industry-leading 5 hours of listening time on one charge.(2) And they’re made to keep up with you, thanks to a charging case that holds multiple additional charges for more than 24 hours of listening time.(3) Need a quick charge? Just 15 minutes in the case gives you 3 hours of listening time.(4)', 'apple', 'accessories', 8990.00, 0, 4, 5.0, 30, 0, 1, 0, 'e1e2df3fe8a93d3f2762e994201f2bd1.jpeg', '16065d9c13262c81d93b66f2f4167bd6.jpeg', NULL, NULL, 1519813594, 0, 'Feb2818', 1, 802, 2, 701, 601),
(94, 'Apple 30-pin to USB Cable', 'This USB 2.0 cable connects your iPod, iPhone, or iPad — directly or through a dock — to your computer&#039;s USB port for efficient syncing and charging or to the Apple USB Power Adaptor for convenient charging from a wall outlet.', 'apple', 'accessories', 1090.00, 0, 4, 2.0, 8, 0, 1, 0, '61a525c779fa964263cb7fdfb21fa40f.jpeg', '1c1bde07d0b35713f2b77c0ad7871fc3.jpeg', NULL, NULL, 1519814332, 0, 'Feb2818', 1, 802, 2, 701, 601),
(95, 'Apple 12W USB Power Adapter', 'Use this compact and convenient USB-based power adapter to charge your iPhone, iPad or iPod with Lightning connector at home, on the road or whenever it’s not connected to a computer. You can connect the adapter directly to your device via the Lightning connector.\r\n\r\nFeaturing a compact design, this power adapter offers fast, efficient charging.', 'apple', 'chargers', 1090.00, 0, 4, 3.0, 13, 0, 1, 0, 'ccf40c256279bc39aa0beb9c0a7c30e1.jpeg', 'f4ecf7a96dd15d9a62cc3ef71156e052.jpeg', NULL, NULL, 1519814439, 0, 'Feb2818', 1, 802, 2, 704, 601),
(96, 'Apple Lightning Digital AV Adapter', 'Use the Lightning Digital AV Adapter with your iPhone, iPad or iPod with Lightning connector. The Lightning Digital AV Adapter supports mirroring of what is displayed on your device screen — including apps, presentations, websites, slideshows and more — to your HDMI-equipped TV, display, projector or other compatible display in up to 1080p HD.\r\n\r\nIt also outputs video content — movies, TV shows, captured video — to your big screen in up to 1080p HD. Simply attach the Lightning Digital AV Adapter to the Lightning connector on your device and then to your TV or projector via an HDMI cable (sold separately).', 'apple', 'accessories', 2690.00, 0, 4, 3.0, 4, 0, 1, 0, '1bcbf452f40c3084abca62b0142d9887.jpeg', '4695012671208ba39251b4a8226cd53c.jpeg', '4695012671208ba39251b4a8226cd53c.jpeg', NULL, 1519814757, 0, 'Feb2818', 1, 802, 2, 701, 601),
(97, 'Apple Lightning to 30-pin Adapter', 'This adapter lets you connect devices with a Lightning connector to many of your 30-pin accessories.* Supports analog audio output and USB audio, as well as syncing and charging. Video output not supported.\r\n\r\n\r\n*Some 30-pin accessories are not supported.', 'apple', 'accessories', 1590.00, 0, 5, 0.0, 1, 0, 0, 0, '89ddb8ebbfd0b46777101bd12b567360.jpeg', '2a64dbf6966369f694a6db94cd9a9b93.jpeg', NULL, NULL, 1519815016, 0, 'Feb2818', 1, 802, 2, 701, 601),
(98, 'Apple Lightning to 30-pin Adapter (0.2m)', 'This 0.2-metre cabled adapter lets you connect devices with a Lightning connector to many of your 30-pin accessories.*\r\n\r\n \r\n\r\n*Some 30-pin accessories are not supported. This cable is 8 inches/0.2 m long. It supports analog and USB audio output. It does not support video output.', 'apple', 'accessories', 2290.00, 0, 5, 0.0, 3, 0, 0, 0, '473b8e508653d7e976adfec7f1c56fa1.jpeg', '972e0b14a904b02c258d0c499bb00c65.jpeg', '8759f4cb27ff92b46b9fe65372f11397.jpeg', NULL, **********, 0, 'Feb2818', 1, 802, 2, 701, 601),
(99, 'Aukey PA-T11 6-port Charging Station with Quick Charge 3.0', 'Charge compatible devices up to 4 times faster with Qualcomm Quick Charge 3.0 Technology\r\nBuilt-in safeguards protect your devices against excessive current, overheating, and overcharging\r\n\r\nQuick Charge supports multi-voltage output. You can use the product to charge any USB powered device, but charging speeds will be limited by original device manufacture’s specifications (ex: you can charge an iPhone, but cannot Quick Charge an iPhone).\r\n\r\nSpecifications\r\n• Model Number: PA-T11\r\n• Technology: Quick Charge 3.0, AiPower\r\n• Input: 100-240V\r\n• Output (AiPower): 5V 2.4A per port \r\n• Output (Quick Charge 3.0): 3.6V-6.5V 3A | 6.5V-9V 2A | 9V-12V 1.5A\r\n• Weight: 207g / 7.3oz', 'aukey', 'chargers', 2199.00, 0, 5, 5.0, 15, 0, 0, 0, '50fc4e6ec6e0617932887503e0befbf8.jpg', '8f9a18d5d9227e07c606d42e499a5d4b.jpg', '4e4ee777aeaf8afcc96c4896b3d4ed88.jpg', NULL, **********, 0, 'Feb2818', 1, 802, 2, 704, 610),
(100, 'Aukey PA-U32 Mini Dual Port Wall Charger with Foldable Plug', 'AiPower Adaptive Charging Technology provides the safest maximum recharge rate for all USB powered devices\r\nAdaptively charges all 5V USB powered devices including Android and Apple devices at up to 2.4A\r\n\r\nWhat is AiPower Adaptive Charging Technology?\r\nAiPower supports healthier battery function and the fastest USB charging speeds. With up to 2.4A of dedicated adaptive charging output per Ai USB port, this intelligent charging technology adjusts to match the unique charging needs of all your USB powered devices.\r\n\r\nSpecifications\r\n• Model Number: PA-U32 \r\n• Technology: AiPower\r\n• Input: 5V 2.4A\r\n• Output: 5V 4.8A\r\n• Total Power: 12W\r\n• Weight: 34g / 1.2oz', 'aukey', 'chargers', 799.00, 0, 5, 2.5, 13, 0, 0, 0, '1879156f76a76e188c6a60e6a30d9ad7.jpg', '93bdf48d8cfb750977b91ac4a2d40223.jpg', NULL, NULL, **********, 0, 'Feb2818', 1, 802, 2, 704, 610),
(101, 'Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0', 'Quick Charge 3.0 - Charge compatible devices up to 4 times faster than conventional charging\r\n• 10050mAh Portable Battery - Fully charge your iPhone 6s 7.5 times or a Galaxy S7 4.4 times, or an iPad Air 1.5 times\r\n• Adaptively charges all 5V USB powered devices including Android and Apple devices at up to 2.4A\r\n• Built-in safeguards protect your devices against excessive current, overheating, and overcharging\r\n• Package Contents: AUKEY 10050mAh Power Bank, Micro USB Cable, User Manual, 18 Month Warranty Card\r\nQualcomm Quick Charge 3.0 Engineered to refuel devices up to 4x faster than conventional charging. Powered by INOV (Intelligent Negotiation for Optimum Voltage) Technology for fine-tuned power output and more optimized charging cycles. Up to 45% more efficient than Quick Charge 2.0 &amp; compatible with a full range of USB connector types, from A to C.\r\n\r\nAiPower Adaptive Charging Technology \r\nTuned to support healthier battery function and faster USB charging speeds, AiPower intelligently adjusts power output to match the unique charging needs of all your USB powered gear. Advanced circuitry and built in safeguards protect your devices against excessive current, overheating, and over charging.Universal Compatibility Keep your devices charged on the go with dual USB charging ports. Equipped with built-in flashlight and LED battery indicator. Designed to work with all popular USB powered devices from iPhones to Android phones, tablets, photography gear, Bluetooth speakers, headphones and more. Whatever USB powered gear you&#039;ve got, we&#039;ve got you covered.\r\n\r\n18 Month Warranty Whether it&#039;s your first AUKEY purchase or you&#039;re back for more, rest assured that we&#039;re in this together: All AUKEY products are backed by our 18 Month Product Warranty.\r\n\r\nSpecifications \r\nCapacity: 10050mAh   \r\nOutput 1 (Quick Charge 3.0): 3.6-6.5V 3A, 6.5V-9V 2A, 9V-12V 1.5A \r\nOutput 2 (AiPower): 5V 2.4A \r\n\r\nBattery Indicator Light  \r\n4 LEDs = 75 - 100%  \r\n3 LEDs = 50 - 75%  2 LEDs = 25 - 50% 1 LED = 0 - 25% \r\nWhat&#039;s in the box:\r\n1 x AUKEY PB-AT10 10050mAh Power Bank\r\n1 x 30cm microUSB charging cable\r\n1 x User manual', 'aukey', 'accessories', 1579.00, 0, 5, 5.0, 4, 0, 0, 0, '915301411c43b4f4b2376694789f3e12.jpg', '60505c54ceb27de785979b4477d8ceed.jpg', NULL, NULL, **********, 0, 'Feb2818', 1, 802, 2, 701, 610),
(102, 'AUKEY PB-N41 POCKET 5000MAH POWER BANK ?1,17900 ?1,179.00', 'Pocket 5000mAh Power Bank\r\n \r\nSpecifications\r\n• Model Number: PB-N41\r\n• Technology: AiPower\r\n• Capacity: 5000mAh \r\n• Input: 5V 2.1A \r\n• Output: 5V 2.1A\r\n• Weight: 118g / 4.2oz', 'aukey', 'accessories', 1179.00, 0, 5, 0.0, 11, 0, 0, 0, '3cd00a81b38e86ae2c02cdf43a69c1d7.jpg', '8a01fdd8a6fbfc4cf695d89b6cf6c4ac.jpg', NULL, NULL, **********, 0, 'Feb2818', 1, 802, 2, 701, 610),
(103, 'Aukey PB-N42 Pocket 10,000mah Power Bank', 'Pocket 10000mAh Power Bank\r\n• Fit 3 charges for your iPhone 7 in the back pocket of your jeans - 10000mAh of backup USB charging power, plus a flashlight just in case\r\n• AiPower Adaptive Charging Technology - Get the safest maximum recharge rate for all of your USB-powered devices\r\n• Combined 3.1A output from dual USB ports - Simultaneously recharge your smartphone and tablet at max speed\r\n\r\nSpecifications\r\n• Model Number: PB-N42\r\n• Technology: AiPower\r\n• Capacity: 10000mAh\r\n• Input: 5V 2.1A\r\n• USB Output 1 | 2: 5V 1A ? 5V 2.1A\r\n• Weight: 226g / 8oz', 'aukey', 'accessories', 2349.00, 0, 5, 0.0, 0, 0, 0, 0, '756076269eb42ef620451ca2423fb602.jpg', '139ccc4f1a65e19119f4d030188c283e.jpg', NULL, NULL, **********, 0, 'Feb2818', 1, 802, 2, 701, 610),
(104, 'Aukey PB-T10 20,000mah Power Bank with Quick Charge 3.0', '20000mAh Power Bank with Quick Charge 3.0\r\n\r\n• Quick Charge 3.0 - Charge compatible devices up to 4 times faster than conventional charging\r\n• Adaptively charges all 5V USB powered devices including Android and Apple devices at up to 2.4A\r\n• Built-in safeguards protect your devices against excessive current, overheating, and overcharging\r\n\r\nSpecifications\r\n• Model Number: PB-T10\r\n• Technology: Quick Charge 3.0 &amp; AiPower\r\n• Capacity: 20000mAh\r\n• Input: 5V 2A\r\n• Output (Quick Charge 3.0): 3.6V-6.5V 3A | 6.5V-9V 2A | 9V-12V 1.5A\r\n• Output (AiPower): 5V 2.4A\r\n• Weight: 388g / 13.7oz', 'aukey', 'accessories', 3149.00, 0, 8, 0.0, 3, 0, 1, 0, '59e6b67237f7f642e6d4ec509458fc27.jpg', '5b13d4268baee4bbd50277adf0bee8c9.jpg', NULL, NULL, **********, 0, 'Feb2818', 1, 802, 2, 701, 610),
(105, 'Avantree - Walrus - Waterproof Case with Earphone Jack', 'IPX8 waterproof. Fits the iPhone 5/s 6/s, 6/s Plus, 7, 7 Plus Galaxy Note 5, 4, 3, 2, Galaxy S8, S7, S6, S5, etc.\r\n\r\nThe Avantree Walrus waterproof case designed for smart phones up to 6.5 inch. This waterproof case enables you to enjoy music under water. Transparent window allows you to take photos while inside the protector. It&#039;s waterproof up to 10 meters (2 to 3 hours). Perfect for boating, camping, canoeing, fishing, hiking, kayaking, kite-boarding, mountain-biking, off-roading, surfing, swimming, windsurfing and all kinds of general beach &amp; water fun! \r\n\r\nWarning: Use at your own risk! Since this case offers limited protection to your phone, use the case in conditions that don&#039;t exceed the maximum depth. There are a number of factors beyond our control, including environmental conditions and normal wear and tear, that can affect waterproofing.To test waterproofing, put tissue paper or paper towel inside the case and immerse into water to check for leaks before every use.', 'acer', 'accessories', 790.00, 0, 5, 0.0, 1, 0, 0, 0, 'default-product.jpg', NULL, NULL, NULL, **********, 0, 'Feb2818', 1, 802, 2, 701, 605),
(106, 'sdfg', 'dfdgfhjh', 'acer', 'accessories', 243.00, 0, 434, 0.0, 1, 0, 0, 0, 'default-product.jpg', NULL, NULL, NULL, 1520103359, 1520103390, 'Mar0418', 0, 802, 1, 701, 605),
(107, 'asfdgf', 'asfdgfgjhjk', 'aukey', 'accessories', 51.00, 0, 2, 0.0, 0, 0, 0, 0, 'default-product.jpg', NULL, NULL, NULL, 1528299470, **********, 'Jun0618', 0, 801, 2, 701, 610),
(108, 'ghvjknlm', 'gfchvjnml;', 'apple', 'chargers', 6.00, 0, 5, 0.0, 0, 0, 0, 0, 'default-product.jpg', NULL, NULL, NULL, 1528299652, 0, 'Jun0618', 0, 801, 2, 704, 617);

-- --------------------------------------------------------

--
-- Table structure for table `promo`
--

CREATE TABLE `promo` (
  `promo_id` int(12) NOT NULL,
  `promo_code` varchar(10) NOT NULL,
  `promo_discount` float(8,2) NOT NULL,
  `promo_condition` float(8,2) NOT NULL,
  `promo_end` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `promo`
--

INSERT INTO `promo` (`promo_id`, `promo_code`, `promo_discount`, `promo_condition`, `promo_end`, `status`) VALUES
(1, 'Summer1000', 1000.00, 5000.00, 1524412800, 1);

-- --------------------------------------------------------

--
-- Table structure for table `sales`
--

CREATE TABLE `sales` (
  `sales_id` int(12) UNSIGNED NOT NULL,
  `sales_detail` varchar(200) DEFAULT NULL,
  `items_sold` int(3) UNSIGNED NOT NULL DEFAULT '0',
  `income` float(8,2) NOT NULL,
  `sales_date` int(11) NOT NULL,
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1',
  `admin_id` int(12) UNSIGNED DEFAULT NULL,
  `order_id` int(12) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `sales`
--

INSERT INTO `sales` (`sales_id`, `sales_detail`, `items_sold`, `income`, `sales_date`, `status`, `admin_id`, `order_id`) VALUES
(21, 'In this transaction, 1 item was bought and 35,490.00 is earned.', 1, 35490.00, 1484323200, 1, 2, 17),
(22, 'In this transaction, 1 item was bought and 25,400.00 is earned.', 1, 25400.00, 1487819684, 1, 2, 18),
(23, 'In this transaction, 1 item was bought and 14,990.00 is earned.', 1, 14990.00, 1490858397, 0, 2, 19),
(24, 'In this transaction, 2 items were bought and 13,000.00 is earned.', 2, 13000.00, 1492388024, 1, 2, 20),
(25, 'In this transaction, 2 items were bought and 24,980.00 is earned.', 2, 24980.00, 1494136484, 1, 2, 21),
(26, 'In this transaction, 1 item was bought and 45,990.00 is earned.', 1, 45990.00, 1497246884, 1, 2, 22),
(27, 'In this transaction, 2 items were bought and 83,998.00 is earned.', 2, 83998.00, 1501136646, 1, 2, 23),
(28, 'In this transaction, 2 items were bought and 38,599.00 is earned.', 2, 38599.00, 1503915768, 1, 2, 24),
(29, 'In this transaction, 2 items were bought and 15,099.00 is earned.', 2, 15099.00, 1505784735, 1, 2, 25),
(30, 'In this transaction, 2 items were bought and 50,480.00 is earned.', 2, 50480.00, 1509262335, 1, 2, 26),
(31, 'In this transaction, 3 items were bought and 116,970.00 is earned.', 3, 116970.00, 1510021935, 1, 2, 27),
(32, 'In this transaction, 4 items were bought and 115,397.00 is earned.', 4, 115397.00, 1514021535, 1, 2, 28),
(33, 'In this transaction, 1 item was bought and 39,990.00 is earned.', 1, 39990.00, 1517153982, 1, 1, 29),
(34, 'In this transaction, 2 items were bought and 70,989.00 is earned.', 2, 70989.00, 1517400669, 1, 2, 30),
(35, 'In this transaction, 2 items were bought and 47,590.00 is earned.', 2, 47590.00, 1517400675, 1, 2, 31),
(36, 'In this transaction, 1 item was bought and 41,999.00 is earned.', 1, 41999.00, 1517400685, 1, 2, 32),
(37, 'In this transaction, 1 item was bought and 45,990.00 is earned.', 1, 45990.00, 1517400689, 1, 2, 33),
(38, 'In this transaction, 3 items were bought and 47,049.00 is earned.', 3, 47049.00, 1517610735, 1, 2, 38),
(39, 'In this transaction, 1 item was bought and 15,060.00 is earned.', 1, 15060.00, 1517985368, 1, 2, 40),
(40, 'In this transaction, 3 items were bought and 65,568.00 is earned.', 3, 65568.00, 1518318110, 1, 2, 41),
(41, 'In this transaction, 2 items were bought and 52,559.00 is earned.', 2, 52559.00, 1518318136, 1, 2, 42),
(42, 'In this transaction, 2 items were bought and 29,069.00 is earned.', 2, 29069.00, 1518318142, 1, 2, 43),
(43, 'In this transaction, 3 items were bought and 94,558.00 is earned.', 3, 94558.00, 1518318148, 1, 2, 44),
(44, 'In this transaction, 2 items were bought and 54,069.00 is earned.', 2, 54069.00, 1518318153, 1, 2, 45),
(45, 'In this transaction, 2 item/s were bought and 29,069.00 is earned.', 2, 29069.00, 1518318157, 1, 2, 46),
(46, 'In this transaction, 2 items were bought and 54,069.00 is earned.', 2, 54069.00, 1518318164, 1, 2, 47),
(47, 'In this transaction, 4 items were bought and 77,568.00 is earned.', 4, 77568.00, 1518318170, 1, 2, 48),
(48, 'In this transaction, 3 items were bought and 71,068.00 is earned.', 3, 71068.00, 1518318176, 1, 2, 49),
(49, 'In this transaction, 3 items were bought and 86,550.00 is earned.', 3, 86550.00, 1518337512, 1, 2, 50),
(50, 'In this transaction, 3 items were bought and 42,050.00 is earned.', 3, 42050.00, 1518347232, 0, 2, 53),
(51, 'In this transaction, 3 items were bought and 42,050.00 is earned.', 3, 42050.00, 1518347232, 0, 2, 53),
(52, 'In this transaction, 2 items were bought and 27,550.00 is earned.', 2, 27550.00, 1518347315, 0, 2, 54),
(53, 'In this transaction, 4 items were bought and 64,048.00 is earned.', 4, 64048.00, 1518347381, 0, 2, 55),
(54, 'In this transaction, 2 items were bought and 77,559.00 is earned.', 2, 77559.00, 1518347674, 0, 2, 56),
(55, 'In this transaction, 1 item was bought and 40,060.00 is earned.', 1, 40060.00, 1518361556, 1, 2, 39),
(56, 'In this transaction, 3 items were bought and 39,060.00 is earned.', 3, 39060.00, 1518373843, 1, 2, 57),
(57, 'In this transaction, 2 items were bought and 81,550.00 is earned.', 2, 81550.00, 1518959839, 1, 2, 51),
(58, 'In this transaction, 3 items were bought and 98,290.00 is earned.', 3, 98290.00, 1518959851, 1, 2, 52),
(59, 'In this transaction, 5 item/s were bought and 251,520.00 is earned.', 5, 251520.00, **********, 1, 2, 58),
(60, 'In this transaction, 1 item was bought and 39,990.00 is earned.', 1, 39990.00, 1519959532, 1, 2, 59),
(61, 'In this transaction, 1 item was bought and 16,740.00 is earned.', 1, 16740.00, 1522337127, 1, 2, 60),
(62, 'In this transaction, 1 item was bought and 21,289.00 is earned.', 1, 21289.00, 1522337382, 1, 2, 61),
(63, 'In this transaction, 1 item was bought and 12,490.00 is earned.', 1, 12490.00, 1522379025, 1, 2, 62),
(65, 'No items were purchased this day.', 0, 0.00, 1522380916, 0, NULL, NULL),
(66, 'In this transaction, 2 items were bought and 24,080.00 is earned.', 2, 24080.00, 1522472822, 1, 2, 63),
(67, 'In this transaction, 1 item was bought and 35,490.00 is earned.', 1, 35490.00, 1522472842, 1, 2, 64),
(68, 'In this transaction, 2 items were bought and 10,160.00 is earned.', 2, 10160.00, 1522472865, 1, 2, 65),
(69, 'No items were purchased this day.', 0, 0.00, 1522673618, 1, NULL, NULL),
(71, 'No items were purchased this day.', 0, 0.00, 1522533600, 1, NULL, NULL),
(72, 'In this transaction, 1 item was bought and 1,080.00 is earned.', 1, 1080.00, 1522730095, 1, 2, 66),
(73, 'In this transaction, 1 item was bought and 1,170.00 is earned.', 1, 1170.00, 1522730110, 1, 2, 67),
(78, 'In this transaction, 2 items were bought and 3,770.00 is earned.', 2, 3770.00, 1522730500, 1, 2, 68),
(80, 'In this transaction, 1 item was bought and 42,069.00 is earned.', 1, 42069.00, 1522731708, 1, 2, 69),
(81, 'No items were purchased this day.', 0, 0.00, 1522814336, 1, NULL, NULL),
(82, 'No items were purchased this day.', 0, 0.00, 1522930648, 1, NULL, NULL),
(83, 'No items were purchased this day.', 0, 0.00, 1522979549, 1, NULL, NULL),
(84, 'No items were purchased this day.', 0, 0.00, 1523201627, 1, NULL, NULL),
(85, 'No items were purchased this day.', 0, 0.00, 1523244274, 1, NULL, NULL),
(86, 'No items were purchased this day.', 0, 0.00, 1523332205, 1, NULL, NULL),
(87, 'No items were purchased this day.', 0, 0.00, 1523418400, 1, NULL, NULL),
(88, 'No items were purchased this day.', 0, 0.00, 1523517520, 1, NULL, NULL),
(89, 'No items were purchased this day.', 0, 0.00, 1523590331, 1, NULL, NULL),
(90, 'No items were purchased this day.', 0, 0.00, 1523788439, 1, NULL, NULL),
(91, 'No items were purchased this day.', 0, 0.00, 1523809724, 1, NULL, NULL),
(92, 'No items were purchased this day.', 0, 0.00, 1525151276, 1, NULL, NULL),
(93, 'No items were purchased this day.', 0, 0.00, 1527154776, 1, NULL, NULL),
(94, 'No items were purchased this day.', 0, 0.00, 1527339673, 1, NULL, NULL),
(95, 'No items were purchased this day.', 0, 0.00, 1527630415, 1, NULL, NULL),
(96, 'No items were purchased this day.', 0, 0.00, 1527939527, 1, NULL, NULL),
(97, 'No items were purchased this day.', 0, 0.00, 1525212000, 1, NULL, NULL),
(98, 'No items were purchased this day.', 0, 0.00, 1525298400, 1, NULL, NULL),
(99, 'No items were purchased this day.', 0, 0.00, 1525384800, 1, NULL, NULL),
(100, 'No items were purchased this day.', 0, 0.00, 1525471200, 0, NULL, NULL),
(101, 'No items were purchased this day.', 0, 0.00, 1525471200, 0, NULL, NULL),
(102, 'No items were purchased this day.', 0, 0.00, 1525471200, 1, NULL, NULL),
(103, 'No items were purchased this day.', 0, 0.00, 1525557600, 1, NULL, NULL),
(104, 'No items were purchased this day.', 0, 0.00, 1525644000, 1, NULL, NULL),
(105, 'No items were purchased this day.', 0, 0.00, 1525730400, 1, NULL, NULL),
(106, 'No items were purchased this day.', 0, 0.00, 1525730400, 0, NULL, NULL),
(107, 'No items were purchased this day.', 0, 0.00, 1525816800, 1, NULL, NULL),
(108, 'No items were purchased this day.', 0, 0.00, 1525816800, 0, NULL, NULL),
(109, 'No items were purchased this day.', 0, 0.00, 1525903200, 1, NULL, NULL),
(110, 'No items were purchased this day.', 0, 0.00, 1525989600, 1, NULL, NULL),
(111, 'No items were purchased this day.', 0, 0.00, 1526076000, 1, NULL, NULL),
(112, 'No items were purchased this day.', 0, 0.00, 1526162400, 1, NULL, NULL),
(113, 'No items were purchased this day.', 0, 0.00, 1526248800, 1, NULL, NULL),
(114, 'No items were purchased this day.', 0, 0.00, 1526335200, 1, NULL, NULL),
(115, 'No items were purchased this day.', 0, 0.00, 1526421600, 0, NULL, NULL),
(116, 'No items were purchased this day.', 0, 0.00, 1526421600, 1, NULL, NULL),
(117, 'No items were purchased this day.', 0, 0.00, 1526508000, 1, NULL, NULL),
(118, 'No items were purchased this day.', 0, 0.00, 1526594400, 1, NULL, NULL),
(119, 'No items were purchased this day.', 0, 0.00, 1526680800, 1, NULL, NULL),
(120, 'No items were purchased this day.', 0, 0.00, 1526767200, 0, NULL, NULL),
(121, 'No items were purchased this day.', 0, 0.00, 1526767200, 1, NULL, NULL),
(122, 'No items were purchased this day.', 0, 0.00, 1526853600, 0, NULL, NULL),
(123, 'No items were purchased this day.', 0, 0.00, 1526853600, 1, NULL, NULL),
(124, 'No items were purchased this day.', 0, 0.00, 1526940000, 1, NULL, NULL),
(125, 'No items were purchased this day.', 0, 0.00, 1527026400, 1, NULL, NULL),
(126, 'No items were purchased this day.', 0, 0.00, 1527199200, 1, NULL, NULL),
(127, 'No items were purchased this day.', 0, 0.00, 1527372000, 1, NULL, NULL),
(128, 'No items were purchased this day.', 0, 0.00, 1527458400, 1, NULL, NULL),
(129, 'No items were purchased this day.', 0, 0.00, 1527544800, 1, NULL, NULL),
(130, 'No items were purchased this day.', 0, 0.00, 1527717600, 1, NULL, NULL),
(131, 'No items were purchased this day.', 0, 0.00, 1527804000, 1, NULL, NULL),
(132, 'No items were purchased this day.', 0, 0.00, 1523916000, 1, NULL, NULL),
(133, 'No items were purchased this day.', 0, 0.00, 1524002400, 1, NULL, NULL),
(134, 'No items were purchased this day.', 0, 0.00, 1524088800, 1, NULL, NULL),
(135, 'No items were purchased this day.', 0, 0.00, 1524175200, 1, NULL, NULL),
(136, 'No items were purchased this day.', 0, 0.00, 1524261600, 1, NULL, NULL),
(137, 'No items were purchased this day.', 0, 0.00, 1524348000, 1, NULL, NULL),
(138, 'No items were purchased this day.', 0, 0.00, 1524434400, 1, NULL, NULL),
(139, 'No items were purchased this day.', 0, 0.00, 1524520800, 1, NULL, NULL),
(140, 'No items were purchased this day.', 0, 0.00, 1524607200, 1, NULL, NULL),
(141, 'No items were purchased this day.', 0, 0.00, 1524693600, 1, NULL, NULL),
(142, 'No items were purchased this day.', 0, 0.00, 1524780000, 1, NULL, NULL),
(143, 'No items were purchased this day.', 0, 0.00, 1524866400, 1, NULL, NULL),
(144, 'No items were purchased this day.', 0, 0.00, 1524952800, 1, NULL, NULL),
(145, 'No items were purchased this day.', 0, 0.00, 1525039200, 1, NULL, NULL),
(146, 'No items were purchased this day.', 0, 0.00, 1523656800, 1, NULL, NULL),
(147, 'No items were purchased this day.', 0, 0.00, 1523052000, 1, NULL, NULL),
(148, 'No items were purchased this day.', 0, 0.00, 1528128530, 1, NULL, NULL),
(149, 'In this transaction, 2 items were bought and 3,928.00 is earned.', 2, 3928.00, 1528298813, 1, 2, 70),
(150, 'No items were purchased this day.', 0, 0.00, 1528333048, 1, NULL, NULL),
(151, 'No items were purchased this day.', 0, 0.00, 1528387577, 1, NULL, NULL),
(152, 'No items were purchased this day.', 0, 0.00, 1527976800, 1, NULL, NULL),
(153, 'No items were purchased this day.', 0, 0.00, 1528063200, 1, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `shipper`
--

CREATE TABLE `shipper` (
  `shipper_id` int(12) UNSIGNED NOT NULL,
  `shipper_name` varchar(50) NOT NULL,
  `shipper_price` float(8,2) UNSIGNED NOT NULL,
  `contact_no` varchar(20) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `shipper`
--

INSERT INTO `shipper` (`shipper_id`, `shipper_name`, `shipper_price`, `contact_no`, `status`) VALUES
(903, 'Raviga', 70.00, '09173673233', 0),
(904, 'Raviga', 80.00, '09987881213', 1),
(905, 'asas', 0.00, '121221', 0);

-- --------------------------------------------------------

--
-- Table structure for table `supplier`
--

CREATE TABLE `supplier` (
  `supplier_id` int(12) UNSIGNED NOT NULL,
  `company_name` varchar(50) NOT NULL,
  `contact_no` varchar(20) NOT NULL,
  `address` varchar(200) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `supplier`
--

INSERT INTO `supplier` (`supplier_id`, `company_name`, `contact_no`, `address`, `status`) VALUES
(801, 'Octagon', '09505959054', 'SM North', 1),
(802, 'Abenson', '09123355692', 'SM North', 1),
(803, 'PC Express', '09086694541', 'SM Manila', 1),
(804, 'Silicon Valley', '09125854680', 'SM Manila', 1);

-- --------------------------------------------------------

--
-- Table structure for table `user_log`
--

CREATE TABLE `user_log` (
  `log_id` int(11) UNSIGNED NOT NULL,
  `user_type` varchar(30) NOT NULL,
  `username` varchar(100) NOT NULL,
  `date` varchar(100) NOT NULL,
  `action` varchar(100) NOT NULL,
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT '1',
  `customer_id` int(12) UNSIGNED DEFAULT NULL,
  `admin_id` int(12) UNSIGNED DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `user_log`
--

INSERT INTO `user_log` (`log_id`, `user_type`, `username`, `date`, `action`, `status`, `customer_id`, `admin_id`) VALUES
(4, '0', 'seej', '1515383600', 'Logged out.', 1, NULL, 1),
(5, '1', 'vvilliam', '1515383878', 'Logged in.', 1, NULL, 3),
(6, '1', 'vvilliam', '1515384191', 'Logged out.', 1, NULL, 3),
(7, '2', 'tabingi_mukha_ko', '1515384202', 'Logged in.', 1, 1, NULL),
(8, '2', 'tabingi_mukha_ko', '1515384205', 'Logged out.', 1, 1, NULL),
(9, '0', 'seej', '1515395733', 'Logged in.', 1, NULL, 1),
(10, '0', 'seej', '1515462813', 'Logged in.', 1, NULL, 1),
(11, '0', 'seej', '1515468435', 'Added product: Asus Laptop Charger 19V', 1, NULL, 1),
(12, '0', 'seej', '1515468998', 'Added product: Universal Charger', 1, NULL, 1),
(13, '0', 'seej', '1515468998', 'Added product: Universal Charger', 1, NULL, 1),
(14, '0', 'seej', '1515469096', 'Added product: Samsung Convoy U640 Cell Phone Charger', 1, NULL, 1),
(15, '0', 'seej', '1515469132', 'Added product: ASUS Charger for Zenfone2/T100CHI', 1, NULL, 1),
(16, '0', 'seej', '1515469178', 'Added product: Cell Phone Home / Travel Charger for SamsungGravity 3', 1, NULL, 1),
(17, '0', 'seej', '1515473320', 'Added product: ChargeAll-Universal-Cell-Phone-Charger', 1, NULL, 1),
(18, '0', 'seej', '1515473368', 'Added product: Motorola Motorola SPN5185B Cell Phone Travel Charger', 1, NULL, 1),
(19, '0', 'seej', '1515473738', 'Added product: Apple 85W MagSafe 2 Power Adapter for MacBook Pro', 1, NULL, 1),
(20, '0', 'seej', '1515473771', 'Added product: Genuine Apple A1399 USB Mains Wall Charger Adaptor', 1, NULL, 1),
(21, '0', 'seej', '1515473794', 'Added product: Genuine Apple Macbook Charger 60W Magsafe Power Adapter', 1, NULL, 1),
(22, '0', 'seej', '1515481989', 'Logged in.', 1, NULL, 1),
(23, '0', 'seej', '1515482070', 'Added product: HP Laptop - 15z touch optional', 1, NULL, 1),
(24, '0', 'seej', '1515482113', 'Added product: Lenovo - 15.6" Laptop - AMD A6-Series - 4GB Memory - 500GB Hard', 1, NULL, 1),
(25, '0', 'seej', '1515482236', 'Added product: HP 15 Core i3 6th Gen - (4 GB/1 TB HDD/Windows', 1, NULL, 1),
(26, '0', 'seej', '1515482537', 'Added product: HP Spectre x360 Laptop - 15" Touch', 1, NULL, 1),
(27, '0', 'seej', '1515490556', 'Logged in.', 1, NULL, 1),
(28, '0', 'seej', '1515491282', 'Added product: Acer Aspire F 15', 1, NULL, 1),
(29, '0', 'seej', '1515491327', 'Added product: ASUS Laptop X556UQ-NH71 Intel Core i7 7th Gen 7500U (2.70 GHz)', 1, NULL, 1),
(30, '0', 'seej', '1515491364', 'Added product: HP Stream 11.6" Celeron Laptop', 1, NULL, 1),
(31, '0', 'seej', '1515491407', 'Added product: ACER ASPIRE ES1 332 BLACK', 1, NULL, 1),
(32, '0', 'seej', '1515491467', 'Added product: Razer Blade 14 RZ09 Gaming Laptop', 1, NULL, 1),
(33, '0', 'seej', '1515491493', 'Added product: HP OMEN Gaming Laptop - 15"', 1, NULL, 1),
(34, '0', 'seej', '1515491524', 'Added product: Handy Grip Phone Strap', 1, NULL, 1),
(35, '0', 'seej', '1515491549', 'Added product: Wallet Card Holder Monogram', 1, NULL, 1),
(36, '0', 'seej', '1515491570', 'Added product: Cellphone Handy Tripod', 1, NULL, 1),
(37, '0', 'seej', '1515491589', 'Added product: Pop Socket', 1, NULL, 1),
(38, '0', 'seej', '1515491589', 'Added product: Pop Socket', 1, NULL, 1),
(39, '0', 'seej', '1515491605', 'Added product: iPhone X Silicone Case', 1, NULL, 1),
(40, '0', 'seej', '1515491644', 'Added product: Selfie Stick', 1, NULL, 1),
(41, '0', 'seej', '1515491664', 'Added product: Zilu CM001 Universal Car Phone Mount', 1, NULL, 1),
(42, '0', 'seej', '1515491686', 'Added product: Pokémon Folio Wallet iPhone 6 Case', 1, NULL, 1),
(43, '0', 'seej', '1515491710', 'Added product: Cell Phone Pocket Protectors', 1, NULL, 1),
(44, '0', 'seej', '1515491730', 'Added product: Sports Armband for iPhone', 1, NULL, 1),
(45, '0', 'seej', '1515491772', 'Added product: iPhone 6 Plus 16GB', 1, NULL, 1),
(46, '0', 'seej', '1515491807', 'Added product: Samsung Note 7', 1, NULL, 1),
(47, '0', 'seej', '1515492166', 'Added product: Hoffco Celltronix Braided Heavy Duty Cell Phone Charging Cable', 1, NULL, 1),
(48, '0', 'seej', '1515492210', 'Added product: Voltaic Amp Portable Solar Charger', 1, NULL, 1),
(49, '0', 'seej', '1515492233', 'Added product: Silicone Phone Wallet Stand', 1, NULL, 1),
(50, '0', 'seej', '1515492269', 'Added product: Dual Layer Armor Defender Shockproof Protective Hard Case With Stand', 1, NULL, 1),
(51, '0', 'seej', '1515492308', 'Added product: LG V30 LTE Advanced', 1, NULL, 1),
(52, '0', 'seej', '1515492333', 'Added product: LG Leon 4G LTE H345', 1, NULL, 1),
(53, '0', 'seej', '1515492351', 'Added product: Samsung Galaxy J7 J700M, 16GB, Dual SIM LTE, Factory Unlocked', 1, NULL, 1),
(54, '0', 'seej', '1515492377', 'Added product: Galaxy Grand Prime', 1, NULL, 1),
(55, '0', 'seej', '1515492405', 'Added product: Samsung Galaxy Ace Dual-Sim', 1, NULL, 1),
(56, '0', 'seej', '1515492441', 'Added product: HUAWEI Mate 9 Pro', 1, NULL, 1),
(57, '0', 'seej', '1515492461', 'Added product: HUAWEI P9', 1, NULL, 1),
(58, '0', 'seej', '1515492480', 'Added product: OPPO R9s', 1, NULL, 1),
(59, '0', 'seej', '1515492499', 'Added product: OPPO R9s Plus- Rose Gold', 1, NULL, 1),
(60, '0', 'seej', '**********', 'Added product: Lenovo IdeaPad 300 Series', 1, NULL, 1),
(61, '0', 'seej', '**********', 'Added product: HP Flyer Red 15.6" 15-f272wm Laptop PC', 1, NULL, 1),
(62, '0', 'seej', '**********', 'Logged in.', 1, NULL, 1),
(67, '0', 'seej', '**********', 'Logged out.', 1, NULL, 1),
(68, '0', 'seej', '**********', 'Logged in.', 1, NULL, 1),
(69, '0', 'seej', '**********', 'Logged in.', 1, NULL, 1),
(70, '0', 'seej', '**********', 'Added account: &quot;Heelo, [removed]alert&amp;#40;&#039;jdgashdsa&#039;&amp;#41;;[removed]', 1, NULL, 1),
(71, '0', 'seej', '**********', 'Logged out.', 1, NULL, 1),
(72, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(73, '2', 'tabingi_mukha_ko', '**********', 'Logged out.', 1, 1, NULL),
(74, '2', 'RB1515560608', '**********', 'Logged in.', 1, 4, NULL),
(75, '2', 'RB1515560608', '**********', 'Logged out.', 1, 4, NULL),
(76, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(77, '2', 'tabingi_mukha_ko', '**********', 'Logged out.', 1, 1, NULL),
(78, '0', 'seej', '**********', 'Logged in.', 1, NULL, 1),
(79, '0', 'seej', '**********', 'Logged out.', 1, NULL, 1),
(80, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 2),
(81, '1', 'veocalimlim', '**********', 'Added product: Test', 1, NULL, 2),
(82, '1', 'veocalimlim', '**********', 'Added product: adsad', 1, NULL, 2),
(83, '1', 'veocalimlim', '**********', 'Added product: dsada', 1, NULL, 2),
(84, '1', 'veocalimlim', '1516068512', 'Added product: sadad', 1, NULL, 2),
(85, '1', 'veocalimlim', '1516070400', 'Added product: sadad', 1, NULL, 2),
(86, '1', 'veocalimlim', '1516070443', 'Added product: sadad', 1, NULL, 2),
(87, '1', 'veocalimlim', '1516070586', 'Added product: sadad', 1, NULL, 2),
(88, '1', 'veocalimlim', '1516070748', 'Added product: sadad', 1, NULL, 2),
(89, '1', 'veocalimlim', '1516070843', 'Added product: sadad', 1, NULL, 2),
(90, '1', 'veocalimlim', '1516070917', 'Added product: sadad', 1, NULL, 2),
(91, '1', 'veocalimlim', '1516071252', 'Added product: sadad', 1, NULL, 2),
(92, '1', 'veocalimlim', '1516073760', 'Added product: asdadas', 1, NULL, 2),
(93, '1', 'veocalimlim', '1516077569', 'Added product: asdadas', 1, NULL, 2),
(94, '1', 'veocalimlim', '1516077588', 'Added product: asdadas', 1, NULL, 2),
(95, '1', 'veocalimlim', '1516078261', 'Added product: asdadas', 1, NULL, 2),
(96, '1', 'veocalimlim', '1516078278', 'Added product: asdadas', 1, NULL, 2),
(97, '1', 'veocalimlim', '1516078314', 'Added product: asdadas', 1, NULL, 2),
(98, '1', 'veocalimlim', '1516078347', 'Added product: asdadas', 1, NULL, 2),
(99, '1', 'veocalimlim', '1516079709', 'Added product: sadsad', 1, NULL, 2),
(100, '1', 'veocalimlim', '1516079912', 'Added product: adsadsa', 1, NULL, 2),
(101, '1', 'veocalimlim', '1516084664', 'Added product: dasdsad', 1, NULL, 2),
(102, '1', 'veocalimlim', '1516086165', 'Added product: Product Test1', 1, NULL, 2),
(103, '1', 'veocalimlim', '1516086332', 'Added product: William', 1, NULL, 2),
(104, '1', 'veocalimlim', '1516086532', 'Added product: William', 1, NULL, 2),
(105, '', 'seej', '1516093746', 'Logged out.', 1, NULL, 1),
(106, '1', 'veocalimlim', '1516153546', 'Logged in.', 1, NULL, 2),
(107, '1', 'veocalimlim', '1516154837', 'Added product: asdad', 1, NULL, 2),
(108, '1', 'veocalimlim', '1516154949', 'Added product: asdad', 1, NULL, 2),
(109, '1', 'veocalimlim', '1516155354', 'Added product: asdad', 1, NULL, 2),
(110, '1', 'veocalimlim', '1516155384', 'Added product: asdad', 1, NULL, 2),
(111, '1', 'veocalimlim', '1516155438', 'Added product: asdad', 1, NULL, 2),
(112, '1', 'veocalimlim', '1516155731', 'Added product: asdad', 1, NULL, 2),
(113, '1', 'veocalimlim', '1516162345', 'Logged out.', 1, NULL, 2),
(114, '1', 'veocalimlim', '1516162398', 'Logged in.', 1, NULL, 2),
(115, '1', 'veocalimlim', '1516200995', 'Logged in.', 1, NULL, 2),
(116, '1', 'veocalimlim', '1516234916', 'Logged in.', 1, NULL, 2),
(117, '1', 'veocalimlim', '1516244619', 'Logged in.', 1, NULL, 2),
(119, '1', 'veocalimlim', '1516279839', 'Logged in.', 1, NULL, 2),
(120, '1', 'veocalimlim', '1516287495', 'Logged out.', 1, NULL, 2),
(121, '0', 'seej', '1516327686', 'Logged in.', 1, NULL, 1),
(122, '0', 'seej', '1516332322', 'Logged in.', 1, NULL, 1),
(123, '2', 'tabingi_mukha_ko', '1516335640', 'Logged in.', 1, 1, NULL),
(124, '1', 'veocalimlim', '1516350332', 'Logged in.', 1, NULL, 2),
(125, '1', 'veocalimlim', '1516350438', 'Added product: jnjknj', 1, NULL, 2),
(126, '0', 'seej', '**********', 'Logged in.', 1, NULL, 1),
(127, '0', 'seej', '**********', 'Logged out.', 1, NULL, 1),
(128, '1', 'veocalimlim', '**********', 'Added product: kjgkj', 1, NULL, 2),
(129, '0', 'seej', '**********', 'Logged in.', 1, NULL, 1),
(130, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(131, '0', 'seej', '**********', 'Logged in.', 1, NULL, 1),
(132, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(133, '0', 'seej', '**********', 'Deleted account #5', 1, NULL, 1),
(134, '0', 'seej', '**********', 'Reactivated account #5', 1, NULL, 1),
(136, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 2),
(137, '1', 'veocalimlim', '**********', 'Edited order #12', 1, NULL, 2),
(138, '1', 'veocalimlim', '**********', 'Edited order #13', 1, NULL, 2),
(139, '1', 'veocalimlim', '**********', 'Logged out.', 1, NULL, 2),
(140, '0', 'seej', '**********', 'Cancelled order #11', 1, NULL, 1),
(141, '0', 'seej', '**********', 'Deleted account #5', 1, NULL, 2),
(142, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(143, '0', 'seej', '**********', 'Edited order #16', 1, NULL, 2),
(144, '0', 'seej', '**********', 'Edited order #13', 1, NULL, 2),
(145, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 1),
(146, '1', 'veocalimlim', '**********', 'Edited order #16', 1, NULL, 1),
(147, '1', 'veocalimlim', '**********', 'Edited order #16''s status to ''delivered''.', 1, NULL, 1),
(148, '1', 'veocalimlim', '**********', 'Logged out.', 1, NULL, 1),
(149, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(150, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 1),
(151, '1', 'veocalimlim', '**********', 'Added product: Product1', 1, NULL, 1),
(152, '1', 'veocalimlim', '**********', 'Logged out.', 1, NULL, 1),
(153, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 1),
(154, '1', 'veocalimlim', '**********', 'Added product: Product1', 1, NULL, 1),
(155, '0', 'seej', '1516596092', 'Logged in.', 1, NULL, 2),
(156, '0', 'seej', '1516603039', 'Added product: Zenfone Go', 1, NULL, 2),
(157, '0', 'seej', '1516606599', 'Added product: asfas', 1, NULL, 2),
(158, '0', 'seej', '1516606698', 'Added product: hjbk', 1, NULL, 2),
(159, '0', 'seej', '1516609097', 'Logged out.', 1, NULL, 2),
(160, '0', 'seej', '1516609229', 'Logged in.', 1, NULL, 2),
(161, '0', 'seej', '1516609536', 'Deleted product #61', 1, NULL, 2),
(162, '0', 'seej', '1516609537', 'Deleted product #62', 1, NULL, 2),
(163, '0', 'seej', '1516609539', 'Deleted product #60', 1, NULL, 2),
(164, '0', 'seej', '1516611042', 'Added product: iPhone X', 1, NULL, 2),
(165, '0', 'seej', '1516611297', 'Added product: jhvjkbk', 1, NULL, 2),
(166, '0', 'seej', '1516611372', 'Deleted product #64', 1, NULL, 2),
(167, '0', 'seej', '1516611374', 'Deleted product #63', 1, NULL, 2),
(168, '0', 'seej', '1516611406', 'Added product: iPhone X', 1, NULL, 2),
(169, '0', 'seej', '1516611555', 'Deleted product #65', 1, NULL, 2),
(170, '0', 'seej', '1516611869', 'Added product: iPhone X', 1, NULL, 2),
(171, '0', 'seej', '1516611899', 'Logged out.', 1, NULL, 2),
(172, '0', 'seej', '1516629346', 'Logged out.', 1, NULL, 2),
(173, '0', 'seej', '1516629359', 'Logged in.', 1, NULL, 2),
(174, '0', 'seej', '1516630134', 'Added product: HP Laptop - 15z touch optional', 1, NULL, 2),
(175, '0', 'seej', '1516630278', 'Added product: Lenovo - 15.6" Laptop - AMD A6-Series', 1, NULL, 2),
(176, '0', 'seej', '1516630462', 'Added product: HP Spectre x360 Laptop - 15" Touch', 1, NULL, 2),
(177, '0', 'seej', '1516630595', 'Added product: ASUS Laptop X556UQ-NH71', 1, NULL, 2),
(178, '0', 'seej', '1516631484', 'Deleted product #66', 1, NULL, 2),
(179, '0', 'seej', '1516631493', 'Deleted product #69', 1, NULL, 2),
(180, '0', 'seej', '1516631606', 'Added product: HP Stream 11.6" Celeron Laptop', 1, NULL, 2),
(181, '0', 'seej', '1516632423', 'Added product: ACER ASPIRE ES1 332 BLACK', 1, NULL, 2),
(182, '0', 'seej', '1516633112', 'Added product: Apple iPad Pro', 1, NULL, 2),
(183, '0', 'seej', '1516633254', 'Added product: iPhone 8', 1, NULL, 2),
(184, '0', 'seej', '1516633371', 'Added product: iPhone 7', 1, NULL, 2),
(185, '0', 'seej', '1516633645', 'Added product: Zenfone Go ZB551KL 16GBv', 1, NULL, 2),
(186, '0', 'seej', '1516633900', 'Added product: Samsung Galaxy C5', 1, NULL, 2),
(187, '0', 'seej', '1516635326', 'Added product: Samsung J7', 1, NULL, 2),
(188, '0', 'seej', '1516635770', 'Added product: Samsung S4', 1, NULL, 2),
(189, '0', 'seej', '1516671606', 'Logged in.', 1, NULL, 2),
(190, '0', 'seej', '1516671804', 'Added product: ZenFone 3 Max ZC520TL', 1, NULL, 2),
(191, '0', 'seej', '1516671979', 'Added product: Apple Air 2', 1, NULL, 2),
(192, '0', 'seej', '1516672123', 'Added product: Samsung Galaxy Tab A 7.0', 1, NULL, 2),
(193, '0', 'seej', '1516672340', 'Added product: APPLE IPAD MINI 2', 1, NULL, 2),
(194, '0', 'seej', '1516672371', 'Deleted product #83', 1, NULL, 2),
(195, '0', 'seej', '1516672422', 'Deleted product #76', 1, NULL, 2),
(196, '0', 'seej', '1516672956', 'Added product: Apple iPad Mini 2', 1, NULL, 2),
(197, '0', 'seej', '1516672988', 'Restored product #63', 1, NULL, 2),
(198, '0', 'seej', '1516673001', 'Deleted product #63', 1, NULL, 2),
(199, '0', 'seej', '1516673339', 'Added product: Apple iPhone X', 1, NULL, 2),
(200, '0', 'seej', '1516673976', 'Added product: zenfone go', 1, NULL, 2),
(201, '0', 'seej', '1516674109', 'Added product: asdada', 1, NULL, 2),
(202, '0', 'seej', '1516674163', 'Added product: 1231', 1, NULL, 2),
(203, '2', 'tabingi_mukha_ko', '1516764609', 'Logged in.', 1, 1, NULL),
(204, '0', 'seej', '1516799981', 'Logged in.', 1, NULL, 2),
(205, '0', 'seej', '1516801396', 'Logged out.', 1, NULL, 2),
(206, '0', 'seej', '1516801415', 'Logged in.', 1, NULL, 2),
(207, '0', 'seej', '1516801651', 'Edited order #17', 1, NULL, 2),
(208, '0', 'seej', '1516801651', 'Edited order #17''s status to ''delivered''.', 1, NULL, 2),
(209, '0', 'seej', '1516801676', 'Edited order #18', 1, NULL, 2),
(210, '0', 'seej', '1516972068', 'Logged in.', 1, NULL, 2),
(211, '0', 'seej', '1516972448', 'Logged in.', 1, NULL, 2),
(212, '0', 'seej', '1516972499', 'Edited order #18', 1, NULL, 2),
(213, '0', 'seej', '1516972499', 'Edited order #18''s status to ''delivered''.', 1, NULL, 2),
(214, '0', 'seej', '1516972545', 'Logged out.', 1, NULL, 2),
(215, '0', 'seej', '1516972654', 'Logged in.', 1, NULL, 2),
(216, '0', 'seej', '1516974784', 'Logged out.', 1, NULL, 2),
(217, '0', 'seej', '1517055192', 'Logged in.', 1, NULL, 2),
(218, '0', 'seej', '1517055387', 'Logged out.', 1, NULL, 2),
(219, '0', 'seej', '1517056169', 'Logged in.', 1, NULL, 2),
(220, '2', 'RO1517055802', '1517056335', 'Logged in.', 1, 8, NULL),
(221, '0', 'seej', '1517056660', 'Edited order #19', 1, NULL, 2),
(222, '0', 'seej', '1517056661', 'Edited order #19''s status to ''delivered''.', 1, NULL, 2),
(223, '0', 'seej', '1517056669', 'Edited order #20', 1, NULL, 2),
(224, '0', 'seej', '1517056669', 'Edited order #20''s status to ''delivered''.', 1, NULL, 2),
(225, '0', 'seej', '1517056678', 'Edited order #21', 1, NULL, 2),
(226, '0', 'seej', '1517056679', 'Edited order #21''s status to ''delivered''.', 1, NULL, 2),
(227, '2', 'RO1517055802', '1517056691', 'Logged out.', 1, 8, NULL),
(228, '0', 'seej', '1517057190', 'Edited order #22', 1, NULL, 2),
(229, '0', 'seej', '1517057190', 'Edited order #22''s status to ''delivered''.', 1, NULL, 2),
(230, '0', 'seej', '1517057195', 'Edited order #23', 1, NULL, 2),
(231, '0', 'seej', '1517057195', 'Edited order #23''s status to ''delivered''.', 1, NULL, 2),
(232, '0', 'seej', '1517057200', 'Edited order #24', 1, NULL, 2),
(233, '0', 'seej', '1517057200', 'Edited order #24''s status to ''delivered''.', 1, NULL, 2),
(234, '2', 'tabingi_mukha_ko', '1517057421', 'Logged in.', 1, 1, NULL),
(235, '2', 'tabingi_mukha_ko', '1517057470', 'Logged out.', 1, 1, NULL),
(236, '2', 'EA1517057098', '1517057496', 'Logged in.', 1, 10, NULL),
(237, '2', 'EA1517057098', '1517057902', 'Logged out.', 1, 10, NULL),
(238, '0', 'seej', '1517060655', 'Edited order #25', 1, NULL, 2),
(239, '0', 'seej', '1517060663', 'Edited order #26', 1, NULL, 2),
(240, '0', 'seej', '1517060667', 'Edited order #27', 1, NULL, 2),
(241, '0', 'seej', '1517060672', 'Edited order #25', 1, NULL, 2),
(242, '0', 'seej', '1517060672', 'Edited order #25''s status to ''delivered''.', 1, NULL, 2),
(243, '0', 'seej', '1517063215', 'Logged out.', 1, NULL, 2),
(244, '0', 'seej', '1517063222', 'Logged in.', 1, NULL, 2),
(245, '0', 'seej', '1517065554', 'Edited order #26', 1, NULL, 2),
(246, '0', 'seej', '1517065555', 'Edited order #26''s status to ''delivered''.', 1, NULL, 2),
(247, '0', 'seej', '1517065645', 'Edited order #28', 1, NULL, 2),
(248, '0', 'seej', '1517065651', 'Edited order #27', 1, NULL, 2),
(249, '0', 'seej', '1517065651', 'Edited order #27''s status to ''delivered''.', 1, NULL, 2),
(250, '0', 'seej', '1517065758', 'Edited order #29', 1, NULL, 2),
(251, '0', 'seej', '1517065772', 'Edited order #28', 1, NULL, 2),
(252, '0', 'seej', '1517065772', 'Edited order #28''s status to ''delivered''.', 1, NULL, 2),
(253, '0', 'seej', '1517096593', 'Logged in.', 1, NULL, 2),
(254, '0', 'seej', '1517100259', 'Logged out.', 1, NULL, 2),
(255, '0', 'seej', '1517114943', 'Logged in.', 1, NULL, 2),
(256, '0', 'seej', '1517116249', 'Logged out.', 1, NULL, 2),
(257, '0', 'seej', '1517116270', 'Logged in.', 1, NULL, 2),
(258, '0', 'seej', '1517116273', 'Logged out.', 1, NULL, 2),
(259, '2', 'tabingi_mukha_ko', '1517116281', 'Logged in.', 1, 1, NULL),
(260, '2', 'tabingi_mukha_ko', '1517116348', 'Logged out.', 1, 1, NULL),
(261, '0', 'seej', '1517116353', 'Logged in.', 1, NULL, 2),
(262, '0', 'seej', '1517116924', 'Logged out.', 1, NULL, 2),
(263, '0', 'seej', '1517146972', 'Logged in.', 1, NULL, 2),
(264, '1', 'veocalimlim', '1517151854', 'Logged in.', 1, NULL, 1),
(265, '0', 'seej', '1517152207', 'Logged in.', 1, NULL, 2),
(266, '0', 'seej', '1517152239', 'Logged in.', 1, NULL, 2),
(267, '1', 'veocalimlim', '1517152688', 'Logged in.', 1, NULL, 1),
(268, '1', 'veocalimlim', '1517153974', 'Logged in.', 1, NULL, 1),
(269, '1', 'veocalimlim', '1517153982', 'Edited order #29', 1, NULL, 1),
(270, '1', 'veocalimlim', '1517153982', 'Edited order #29''s status to ''delivered''.', 1, NULL, 1),
(271, '1', 'veocalimlim', '1517161517', 'Logged out.', 1, NULL, 1),
(272, '0', 'seej', '1517161524', 'Logged in.', 1, NULL, 2),
(273, '0', 'seej', '1517161577', 'Logged out.', 1, NULL, 2),
(274, '0', 'seej', '1517202257', 'Logged in.', 1, NULL, 2),
(275, '2', 'tabingi_mukha_ko', '1517203004', 'Logged in.', 1, 1, NULL),
(276, '2', 'tabingi_mukha_ko', '1517203036', 'Commented on product Apple Air 2 and rate it 4', 1, 1, NULL),
(277, '2', 'tabingi_mukha_ko', '1517203090', 'Commented on product Apple Air 2 and rate it 5', 1, 1, NULL),
(278, '2', 'tabingi_mukha_ko', '1517203104', 'Commented on product Apple Air 2 and rate it 2', 1, 1, NULL),
(279, '2', 'tabingi_mukha_ko', '1517203171', 'Logged out.', 1, 1, NULL),
(280, '0', 'seej', '1517206925', 'Added product: gaisfgjkbasfk', 1, NULL, 2),
(281, '0', 'seej', '1517206945', 'Deleted product #86', 1, NULL, 2),
(282, '0', 'seej', '1517207060', 'Restored product #86', 1, NULL, 2),
(283, '0', 'seej', '1517207097', 'Deleted product #86', 1, NULL, 2),
(284, '1', 'veocalimlim', '1517217161', 'Logged in.', 1, NULL, 1),
(285, '0', 'seej', '1517365837', 'Logged in.', 1, NULL, 2),
(286, '0', 'seej', '1517365981', 'Logged in.', 1, NULL, 2),
(287, '2', 'tabingi_mukha_ko', '1517366727', 'Logged in.', 1, 1, NULL),
(288, '2', 'tabingi_mukha_ko', '1517368454', 'Logged out.', 1, 1, NULL),
(289, '1', 'veocalimlim', '1517368469', 'Logged in.', 1, NULL, 1),
(290, '1', 'veocalimlim', '1517368527', 'Logged out.', 1, NULL, 1),
(291, '0', 'seej', '1517369395', 'Logged out.', 1, NULL, 2),
(292, '0', 'seej', '1517383543', 'Logged in.', 1, NULL, 2),
(293, '0', 'seej', '1517383660', 'Cancelled order #35', 1, NULL, 2),
(295, '1', 'veocalimlim', '1517384620', 'Logged in.', 1, NULL, 1),
(296, '1', 'veocalimlim', '1517385863', 'Logged out.', 1, NULL, 1),
(297, '0', 'seej', '1517385908', 'Logged in.', 1, NULL, 2),
(299, '0', 'seej', '1517386170', 'Logged in.', 1, NULL, 2),
(301, '0', 'seej', '1517386655', 'Logged in.', 1, NULL, 2),
(302, '0', 'seej', '1517400106', 'Logged in.', 1, NULL, 2),
(303, '0', 'seej', '1517400669', 'Edited order #30', 1, NULL, 2),
(304, '0', 'seej', '1517400669', 'Edited order #30''s status to ''delivered''.', 1, NULL, 2),
(305, '0', 'seej', '1517400675', 'Edited order #31', 1, NULL, 2),
(306, '0', 'seej', '1517400675', 'Edited order #31''s status to ''delivered''.', 1, NULL, 2),
(307, '0', 'seej', '1517400682', 'Edited order #32', 1, NULL, 2),
(308, '0', 'seej', '1517400685', 'Edited order #32', 1, NULL, 2),
(309, '0', 'seej', '1517400685', 'Edited order #32''s status to ''delivered''.', 1, NULL, 2),
(310, '0', 'seej', '1517400689', 'Edited order #33', 1, NULL, 2),
(311, '0', 'seej', '1517400689', 'Edited order #33''s status to ''delivered''.', 1, NULL, 2),
(312, '0', 'seej', '1517405129', 'Edited order #34', 1, NULL, 2),
(313, '0', 'seej', '1517405298', 'Edited order #34', 1, NULL, 2),
(314, '0', 'seej', '1517405566', 'Edited order #34', 1, NULL, 2),
(315, '0', 'seej', '1517405786', 'Edited order #34', 1, NULL, 2),
(316, '0', 'seej', '1517405935', 'Edited order #34', 1, NULL, 2),
(318, '0', 'seej', '1517406417', 'Edited order #34', 1, NULL, 2),
(319, '0', 'seej', '1517406417', 'Edited order #34''s status to ''delivered''.', 1, NULL, 2),
(320, '0', 'seej', '1517406515', 'Edited order #34', 1, NULL, 2),
(321, '0', 'seej', '1517406515', 'Edited order #34''s status to ''delivered''.', 1, NULL, 2),
(322, '0', 'seej', '1517406602', 'Edited order #34', 1, NULL, 2),
(323, '0', 'seej', '1517406778', 'Edited order #34', 1, NULL, 2),
(324, '0', 'seej', '1517406898', 'Edited order #34', 1, NULL, 2),
(325, '0', 'seej', '1517406966', 'Edited order #34', 1, NULL, 2),
(326, '0', 'seej', '1517408173', 'Edited order #34', 1, NULL, 2),
(327, '0', 'seej', '1517408192', 'Edited order #34', 1, NULL, 2),
(328, '0', 'seej', '1517408200', 'Edited order #34', 1, NULL, 2),
(329, '0', 'seej', '1517408214', 'Edited order #34', 1, NULL, 2),
(330, '2', 'tabingi_mukha_ko', '1517408246', 'Logged in.', 1, 1, NULL),
(331, '0', 'seej', '1517408286', 'Edited order #36', 1, NULL, 2),
(332, '0', 'seej', '1517408371', 'Edited order #36', 1, NULL, 2),
(333, '0', 'seej', '1517408609', 'Edited order #36', 1, NULL, 2),
(334, '0', 'seej', '1517408617', 'Edited order #36', 1, NULL, 2),
(335, '0', 'seej', '1517408663', 'Edited order #36', 1, NULL, 2),
(336, '0', 'seej', '1517408669', 'Edited order #36', 1, NULL, 2),
(337, '0', 'seej', '1517408683', 'Edited order #36', 1, NULL, 2),
(338, '0', 'seej', '1517408777', 'Edited order #36', 1, NULL, 2),
(339, '0', 'seej', '1517408794', 'Edited order #36', 1, NULL, 2),
(340, '0', 'seej', '1517408823', 'Edited order #36', 1, NULL, 2),
(341, '0', 'seej', '1517408984', 'Edited order #36', 1, NULL, 2),
(342, '0', 'seej', '1517409066', 'Edited order #36', 1, NULL, 2),
(343, '0', 'seej', '1517409338', 'Edited order #36', 1, NULL, 2),
(344, '0', 'seej', '1517409407', 'Edited order #36', 1, NULL, 2),
(345, '0', 'seej', '1517409523', 'Edited order #36', 1, NULL, 2),
(346, '0', 'seej', '1517409531', 'Edited order #36', 1, NULL, 2),
(347, '0', 'seej', '1517409746', 'Edited order #36', 1, NULL, 2),
(348, '0', 'seej', '1517410052', 'Edited order #38', 1, NULL, 2),
(349, '0', 'seej', '1517410317', 'Edited order #38', 1, NULL, 2),
(350, '0', 'seej', '1517410334', 'Edited order #38', 1, NULL, 2),
(351, '0', 'seej', '1517410459', 'Edited order #38', 1, NULL, 2),
(352, '0', 'seej', '1517410563', 'Edited order #38', 1, NULL, 2),
(353, '0', 'seej', '1517410586', 'Edited order #38', 1, NULL, 2),
(354, '0', 'seej', '1517410601', 'Edited order #38', 1, NULL, 2),
(355, '0', 'seej', '1517410673', 'Edited order #38', 1, NULL, 2),
(356, '0', 'seej', '1517410717', 'Edited order #38', 1, NULL, 2),
(357, '0', 'seej', '1517410810', 'Edited order #38', 1, NULL, 2),
(358, '0', 'seej', '1517410820', 'Edited order #38', 1, NULL, 2),
(359, '0', 'seej', '1517410964', 'Edited order #38', 1, NULL, 2),
(360, '0', 'seej', '1517411052', 'Edited order #38', 1, NULL, 2),
(361, '0', 'seej', '1517411143', 'Edited order #38', 1, NULL, 2),
(362, '0', 'seej', '1517411150', 'Edited order #38', 1, NULL, 2),
(363, '0', 'seej', '1517411209', 'Edited order #38', 1, NULL, 2),
(364, '0', 'seej', '1517411243', 'Edited order #38', 1, NULL, 2),
(365, '0', 'seej', '1517411249', 'Edited order #38', 1, NULL, 2),
(366, '0', 'seej', '1517411355', 'Edited order #38', 1, NULL, 2),
(367, '0', 'seej', '1517411386', 'Edited order #38', 1, NULL, 2),
(368, '0', 'seej', '1517411395', 'Edited order #38', 1, NULL, 2),
(369, '0', 'seej', '1517411398', 'Edited order #38', 1, NULL, 2),
(370, '0', 'seej', '1517411413', 'Edited order #38', 1, NULL, 2),
(371, '0', 'seej', '1517411418', 'Edited order #38', 1, NULL, 2),
(372, '0', 'seej', '1517411437', 'Edited order #38', 1, NULL, 2),
(373, '0', 'seej', '1517411543', 'Edited order #38', 1, NULL, 2),
(374, '0', 'seej', '1517411563', 'Edited order #38', 1, NULL, 2),
(375, '0', 'seej', '1517411576', 'Edited order #38', 1, NULL, 2),
(376, '0', 'seej', '1517411592', 'Edited order #38', 1, NULL, 2),
(377, '0', 'seej', '1517411594', 'Edited order #38', 1, NULL, 2),
(378, '0', 'seej', '1517411595', 'Edited order #38', 1, NULL, 2),
(379, '0', 'seej', '1517411596', 'Edited order #38', 1, NULL, 2),
(380, '0', 'seej', '1517411597', 'Edited order #38', 1, NULL, 2),
(381, '0', 'seej', '1517411598', 'Edited order #38', 1, NULL, 2),
(382, '0', 'seej', '1517411598', 'Edited order #38', 1, NULL, 2),
(383, '0', 'seej', '1517411599', 'Edited order #38', 1, NULL, 2),
(384, '0', 'seej', '1517411615', 'Edited order #38', 1, NULL, 2),
(385, '0', 'seej', '1517411748', 'Edited order #38', 1, NULL, 2),
(386, '0', 'seej', '1517411751', 'Edited order #38', 1, NULL, 2),
(387, '0', 'seej', '1517411753', 'Edited order #38', 1, NULL, 2),
(388, '0', 'seej', '1517411757', 'Edited order #38', 1, NULL, 2),
(389, '0', 'seej', '1517411760', 'Edited order #38', 1, NULL, 2),
(390, '0', 'seej', '1517411877', 'Edited order #38', 1, NULL, 2),
(391, '0', 'seej', '1517411884', 'Edited order #38', 1, NULL, 2),
(392, '0', 'seej', '1517412025', 'Edited order #38', 1, NULL, 2),
(393, '0', 'seej', '1517412029', 'Edited order #38', 1, NULL, 2),
(394, '0', 'seej', '1517412031', 'Edited order #38', 1, NULL, 2),
(395, '0', 'seej', '1517412035', 'Edited order #38', 1, NULL, 2),
(396, '0', 'seej', '1517412071', 'Edited order #38', 1, NULL, 2),
(397, '0', 'seej', '1517412091', 'Edited order #38', 1, NULL, 2),
(398, '0', 'seej', '1517412175', 'Edited order #38', 1, NULL, 2),
(399, '0', 'seej', '1517412179', 'Edited order #38', 1, NULL, 2),
(400, '0', 'seej', '1517412197', 'Edited order #38', 1, NULL, 2),
(401, '0', 'seej', '1517412225', 'Edited order #38', 1, NULL, 2),
(402, '0', 'seej', '1517412253', 'Edited order #38', 1, NULL, 2),
(403, '0', 'seej', '1517412263', 'Edited order #38', 1, NULL, 2),
(404, '0', 'seej', '1517412280', 'Edited order #38', 1, NULL, 2),
(405, '0', 'seej', '1517412394', 'Edited order #38', 1, NULL, 2),
(406, '0', 'seej', '1517412406', 'Edited order #38', 1, NULL, 2),
(407, '0', 'seej', '1517412431', 'Edited order #38', 1, NULL, 2),
(408, '0', 'seej', '1517412445', 'Edited order #38', 1, NULL, 2),
(409, '0', 'seej', '1517412485', 'Edited order #38', 1, NULL, 2),
(410, '0', 'seej', '1517412542', 'Edited order #38', 1, NULL, 2),
(411, '0', 'seej', '1517412557', 'Edited order #38', 1, NULL, 2),
(412, '0', 'seej', '1517412559', 'Edited order #38', 1, NULL, 2),
(413, '0', 'seej', '1517412560', 'Edited order #38', 1, NULL, 2),
(414, '0', 'seej', '1517412607', 'Edited order #38', 1, NULL, 2),
(415, '0', 'seej', '1517412609', 'Edited order #38', 1, NULL, 2),
(416, '0', 'seej', '1517412612', 'Edited order #38', 1, NULL, 2),
(417, '0', 'seej', '1517412613', 'Edited order #38', 1, NULL, 2),
(418, '0', 'seej', '1517412614', 'Edited order #38', 1, NULL, 2),
(419, '0', 'seej', '1517412616', 'Edited order #38', 1, NULL, 2),
(420, '0', 'seej', '1517412617', 'Edited order #38', 1, NULL, 2),
(421, '0', 'seej', '1517412623', 'Edited order #38', 1, NULL, 2),
(422, '0', 'seej', '1517412626', 'Edited order #38', 1, NULL, 2),
(423, '0', 'seej', '1517412655', 'Edited order #38', 1, NULL, 2),
(424, '0', 'seej', '1517412658', 'Edited order #38', 1, NULL, 2),
(425, '0', 'seej', '1517412659', 'Edited order #38', 1, NULL, 2),
(426, '0', 'seej', '1517412660', 'Edited order #38', 1, NULL, 2),
(427, '0', 'seej', '1517412661', 'Edited order #38', 1, NULL, 2),
(428, '0', 'seej', '1517412662', 'Edited order #38', 1, NULL, 2),
(429, '0', 'seej', '1517412663', 'Edited order #38', 1, NULL, 2),
(430, '0', 'seej', '1517412664', 'Edited order #38', 1, NULL, 2),
(431, '0', 'seej', '1517412664', 'Edited order #38', 1, NULL, 2),
(432, '0', 'seej', '1517412665', 'Edited order #38', 1, NULL, 2),
(433, '0', 'seej', '1517412666', 'Edited order #38', 1, NULL, 2),
(434, '0', 'seej', '1517412667', 'Edited order #38', 1, NULL, 2),
(435, '0', 'seej', '1517412670', 'Edited order #38', 1, NULL, 2),
(436, '0', 'seej', '1517412675', 'Edited order #38', 1, NULL, 2),
(437, '0', 'seej', '1517412676', 'Edited order #38', 1, NULL, 2),
(438, '0', 'seej', '1517412677', 'Edited order #38', 1, NULL, 2),
(439, '0', 'seej', '1517412678', 'Edited order #38', 1, NULL, 2),
(440, '0', 'seej', '1517412691', 'Edited order #38', 1, NULL, 2),
(441, '0', 'seej', '1517412725', 'Edited order #38', 1, NULL, 2),
(442, '0', 'seej', '1517412728', 'Edited order #38', 1, NULL, 2),
(443, '0', 'seej', '1517412729', 'Edited order #38', 1, NULL, 2),
(444, '0', 'seej', '1517412730', 'Edited order #38', 1, NULL, 2),
(445, '0', 'seej', '1517412731', 'Edited order #38', 1, NULL, 2),
(446, '0', 'seej', '1517412732', 'Edited order #38', 1, NULL, 2),
(447, '0', 'seej', '1517412734', 'Edited order #38', 1, NULL, 2),
(448, '0', 'seej', '1517412735', 'Edited order #38', 1, NULL, 2),
(449, '0', 'seej', '1517412736', 'Edited order #38', 1, NULL, 2),
(450, '0', 'seej', '1517412743', 'Edited order #38', 1, NULL, 2),
(451, '0', 'seej', '1517412744', 'Edited order #38', 1, NULL, 2),
(452, '0', 'seej', '1517412745', 'Edited order #38', 1, NULL, 2),
(453, '0', 'seej', '1517412748', 'Edited order #38', 1, NULL, 2),
(454, '0', 'seej', '1517412749', 'Edited order #38', 1, NULL, 2),
(455, '0', 'seej', '1517412750', 'Edited order #38', 1, NULL, 2),
(456, '0', 'seej', '1517412751', 'Edited order #38', 1, NULL, 2),
(457, '0', 'seej', '1517412753', 'Edited order #38', 1, NULL, 2),
(458, '0', 'seej', '1517412754', 'Edited order #38', 1, NULL, 2),
(459, '0', 'seej', '1517412757', 'Edited order #38', 1, NULL, 2),
(460, '0', 'seej', '1517412758', 'Edited order #38', 1, NULL, 2),
(461, '0', 'seej', '1517412759', 'Edited order #38', 1, NULL, 2),
(462, '0', 'seej', '1517412760', 'Edited order #38', 1, NULL, 2),
(463, '0', 'seej', '1517412762', 'Edited order #38', 1, NULL, 2),
(464, '0', 'seej', '1517412845', 'Edited order #38', 1, NULL, 2),
(465, '0', 'seej', '1517412850', 'Edited order #38', 1, NULL, 2),
(466, '0', 'seej', '1517412851', 'Edited order #38', 1, NULL, 2),
(467, '0', 'seej', '1517412852', 'Edited order #38', 1, NULL, 2),
(468, '0', 'seej', '1517412854', 'Edited order #38', 1, NULL, 2),
(469, '0', 'seej', '1517412856', 'Edited order #38', 1, NULL, 2),
(470, '0', 'seej', '1517412858', 'Edited order #38', 1, NULL, 2),
(471, '0', 'seej', '1517412859', 'Edited order #38', 1, NULL, 2),
(472, '0', 'seej', '1517412860', 'Edited order #38', 1, NULL, 2),
(473, '0', 'seej', '1517413399', 'Edited order #38', 1, NULL, 2),
(474, '0', 'seej', '1517414791', 'Logged out.', 1, NULL, 2),
(475, '0', 'seej', '1517454281', 'Logged in.', 1, NULL, 2),
(476, '0', 'seej', '1517454572', 'Edited order #38', 1, NULL, 2),
(477, '0', 'seej', '1517454723', 'Edited order #38', 1, NULL, 2),
(478, '0', 'seej', '1517454727', 'Edited order #38', 1, NULL, 2),
(479, '0', 'seej', '1517454728', 'Edited order #38', 1, NULL, 2),
(480, '0', 'seej', '1517454730', 'Edited order #38', 1, NULL, 2),
(481, '0', 'seej', '1517455315', 'Edited order #38', 1, NULL, 2),
(482, '0', 'seej', '1517455486', 'Edited order #38', 1, NULL, 2),
(483, '1', 'veocalimlim', '1517475847', 'Logged in.', 1, NULL, 1),
(484, '1', 'veocalimlim', '1517475937', 'Edited order #38', 1, NULL, 1),
(485, '1', 'veocalimlim', '1517475961', 'Edited order #38', 1, NULL, 1),
(486, '1', 'veocalimlim', '1517476026', 'Edited order #38', 1, NULL, 1),
(487, '1', 'veocalimlim', '1517476036', 'Edited order #38', 1, NULL, 1),
(488, '1', 'veocalimlim', '1517476065', 'Edited order #38', 1, NULL, 1),
(489, '1', 'veocalimlim', '1517476077', 'Edited order #38', 1, NULL, 1),
(490, '1', 'veocalimlim', '1517476079', 'Edited order #38', 1, NULL, 1),
(491, '1', 'veocalimlim', '1517476083', 'Edited order #38', 1, NULL, 1),
(492, '1', 'veocalimlim', '1517476592', 'Edited order #38', 1, NULL, 1),
(493, '1', 'veocalimlim', '1517476610', 'Edited order #38', 1, NULL, 1),
(494, '1', 'veocalimlim', '1517476667', 'Edited order #38', 1, NULL, 1),
(495, '1', 'veocalimlim', '1517476724', 'Edited order #38', 1, NULL, 1),
(496, '1', 'veocalimlim', '1517476724', 'Edited order #38', 1, NULL, 1),
(497, '1', 'veocalimlim', '1517476728', 'Edited order #38', 1, NULL, 1),
(498, '1', 'veocalimlim', '1517476785', 'Edited order #38', 1, NULL, 1),
(499, '1', 'veocalimlim', '1517476788', 'Edited order #38', 1, NULL, 1),
(500, '1', 'veocalimlim', '1517476791', 'Edited order #38', 1, NULL, 1),
(501, '1', 'veocalimlim', '1517476808', 'Edited order #38', 1, NULL, 1),
(502, '1', 'veocalimlim', '1517476952', 'Edited order #38', 1, NULL, 1),
(503, '1', 'veocalimlim', '1517476968', 'Edited order #38', 1, NULL, 1),
(504, '1', 'veocalimlim', '1517476991', 'Edited order #38', 1, NULL, 1),
(505, '1', 'veocalimlim', '1517477042', 'Edited order #38', 1, NULL, 1),
(506, '1', 'veocalimlim', '1517477170', 'Edited order #38', 1, NULL, 1),
(507, '1', 'veocalimlim', '1517477277', 'Edited order #38', 1, NULL, 1),
(508, '1', 'veocalimlim', '1517477284', 'Edited order #38', 1, NULL, 1),
(509, '0', 'seej', '1517477854', 'Logged in.', 1, NULL, 2),
(510, '0', 'seej', '1517477871', 'Logged out.', 1, NULL, 2),
(511, '1', 'veocalimlim', '1517477883', 'Logged out.', 1, NULL, 1),
(512, '0', 'seej', '1517478751', 'Logged in.', 1, NULL, 2),
(513, '2', 'tabingi_mukha_ko', '1517480500', 'Logged in.', 1, 1, NULL),
(514, '0', 'seej', '1517480912', 'Logged out.', 1, NULL, 2),
(515, '2', 'tabingi_mukha_ko', '1517480918', 'Logged out.', 1, 1, NULL),
(516, '0', 'seej', '1517482016', 'Logged in.', 1, NULL, 2),
(517, '0', 'seej', '1517484219', 'Logged out.', 1, NULL, 2),
(518, '0', 'seej', '1517493305', 'Logged in.', 1, NULL, 2),
(519, '1', 'veocalimlim', '1517493351', 'Logged in.', 1, NULL, 1),
(520, '1', 'veocalimlim', '1517493910', 'Logged in.', 1, NULL, 1),
(521, '1', 'veocalimlim', '1517494669', 'Logged in.', 1, NULL, 1),
(522, '1', 'veocalimlim', '1517498196', 'Deleted Sales #34', 1, NULL, 1),
(523, '1', 'veocalimlim', '1517498209', 'Deleted Sales #37', 1, NULL, 1),
(524, '0', 'seej', '1517534152', 'Logged in.', 1, NULL, 2),
(525, '1', 'veocalimlim', '1517538506', 'Logged in.', 1, NULL, 1),
(526, '1', 'veocalimlim', '1517538555', 'Logged in.', 1, NULL, 1),
(527, '1', 'veocalimlim', '1517538764', 'Logged in.', 1, NULL, 1),
(528, '0', 'seej', '1517542886', 'Logged in.', 1, NULL, 2),
(529, '1', 'veocalimlim', '1517543398', 'Logged in.', 1, NULL, 1),
(530, '1', 'veocalimlim', '1517544982', 'Logged out.', 1, NULL, 1),
(531, '0', 'seej', '1517544987', 'Logged in.', 1, NULL, 2),
(532, '2', 'tabingi_mukha_ko', '1517546439', 'Logged in.', 1, 1, NULL),
(533, '2', 'tabingi_mukha_ko', '1517546676', 'Logged out.', 1, 1, NULL),
(534, '0', 'seej', '1517577754', 'Logged in.', 1, NULL, 2),
(535, '0', 'seej', '1517582405', 'Logged in.', 1, NULL, 2),
(536, '0', 'seej', '1517607975', 'Logged in.', 1, NULL, 2),
(537, '2', 'tabingi_mukha_ko', '1517610509', 'Logged in.', 1, 1, NULL),
(538, '0', 'seej', '1517610735', 'Edited order #38', 1, NULL, 2),
(539, '0', 'seej', '1517610735', 'Edited order #38''s status to ''delivered''.', 1, NULL, 2),
(540, '1', 'veocalimlim', '1517612602', 'Logged in.', 1, NULL, 1),
(541, '0', 'seej', '1517612751', 'Logged in.', 1, NULL, 2),
(542, '1', 'veocalimlim', '1517613377', 'Logged in.', 1, NULL, 1),
(543, '1', 'veocalimlim', '1517615416', 'Logged in.', 1, NULL, 1),
(544, '1', 'veocalimlim', '1517615509', 'Logged in.', 1, NULL, 1),
(545, '0', 'seej', '1517616036', 'Logged in.', 1, NULL, 2),
(546, '2', 'tabingi_mukha_ko', '1517616068', 'Logged out.', 1, 1, NULL),
(547, '1', 'veocalimlim', '1517616085', 'Logged in.', 1, NULL, 1),
(548, '0', 'seej', '1517616397', 'Logged in.', 1, NULL, 2),
(549, '0', 'seej', '1517616652', 'Logged in.', 1, NULL, 2),
(550, '0', 'seej', '1517617747', 'Logged in.', 1, NULL, 2),
(551, '1', 'veocalimlim', '1517618090', 'Logged in.', 1, NULL, 1),
(552, '0', 'seej', '1517618788', 'Logged in.', 1, NULL, 2),
(553, '0', 'seej', '1517620279', 'Logged in.', 1, NULL, 2),
(554, '1', 'veocalimlim', '1517620471', 'Logged in.', 1, NULL, 1),
(555, '1', 'veocalimlim', '1517620517', 'Logged out.', 1, NULL, 1),
(556, '0', 'seej', '1517669677', 'Logged in.', 1, NULL, 2),
(557, '0', 'seej', '1517675381', 'Logged out.', 1, NULL, 2),
(558, '2', 'tabingi_mukha_ko', '1517675417', 'Logged in.', 1, 1, NULL),
(559, '2', 'tabingi_mukha_ko', '1517675457', 'Logged out.', 1, 1, NULL),
(560, '2', 'tabingi_mukha_ko', '1517675749', 'Logged in.', 1, 1, NULL),
(561, '2', 'tabingi_mukha_ko', '1517675754', 'Logged out.', 1, 1, NULL),
(562, '1', 'veocalimlim', '1517675760', 'Logged in.', 1, NULL, 1),
(563, '1', 'veocalimlim', '1517676263', 'Logged out.', 1, NULL, 1),
(564, '0', 'seej', '1517715628', 'Logged in.', 1, NULL, 2),
(565, '0', 'seej', '1517715729', 'Logged out.', 1, NULL, 2),
(566, '0', 'seej', '1517715813', 'Logged in.', 1, NULL, 2),
(567, '0', 'seej', '1517716539', 'Logged in.', 1, NULL, 2),
(568, '0', 'seej', '1517726008', 'Logged in.', 1, NULL, 2),
(569, '0', 'seej', '1517751274', 'Logged in.', 1, NULL, 2),
(570, '0', 'seej', '1517751970', 'Edited product #78', 1, NULL, 2),
(571, '0', 'seej', '1517751984', 'Edited product #78', 1, NULL, 2),
(572, '0', 'seej', '1517758885', 'Edited product #85', 1, NULL, 2),
(573, '0', 'seej', '1517803109', 'Logged in.', 1, NULL, 2),
(574, '0', 'seej', '1517812076', 'Logged in.', 1, NULL, 2),
(575, '0', 'seej', '1517812094', 'Logged out.', 1, NULL, 2),
(576, '1', 'veocalimlim', '1517826433', 'Logged in.', 1, NULL, 1),
(577, '0', 'seej', '1517841174', 'Logged in.', 1, NULL, 2),
(578, '0', 'seej', '1517879722', 'Logged in.', 1, NULL, 2),
(579, '0', 'seej', '1517977026', 'Logged in.', 1, NULL, 2),
(580, '2', 'SM1517056867', '1517984543', 'Logged in.', 1, 9, NULL),
(581, '0', 'seej', '1517984893', 'Edited product #72', 1, NULL, 2),
(582, '0', 'seej', '1517984960', 'Edited product #75', 1, NULL, 2),
(583, '0', 'seej', '1517985302', 'Edited product #77', 1, NULL, 2),
(584, '0', 'seej', '1517985368', 'Edited order #40', 1, NULL, 2),
(585, '0', 'seej', '1517985369', 'Edited order #40''s status to ''delivered''.', 1, NULL, 2),
(586, '0', 'seej', '1517986546', 'Logged out.', 1, NULL, 2),
(587, '1', 'veocalimlim', '1517993557', 'Logged in.', 1, NULL, 1),
(588, '0', 'seej', '1518005513', 'Logged in.', 1, NULL, 2),
(589, '0', 'seej', '1518005633', 'Logged out.', 1, NULL, 2),
(590, '0', 'seej', '1518005808', 'Logged in.', 1, NULL, 2),
(591, '0', 'seej', '1518006502', 'Edited product #72', 1, NULL, 2),
(592, '0', 'seej', '1518010333', 'Edited product #81', 1, NULL, 2),
(593, '0', 'seej', '1518010503', 'Edited product #84', 1, NULL, 2),
(594, '0', 'seej', '1518010726', 'Added product: sfsafsa', 1, NULL, 2),
(595, '0', 'seej', '1518010742', 'Deleted product #87', 1, NULL, 2),
(596, '0', 'seej', '1518010811', 'Added product: safsaf', 1, NULL, 2),
(597, '0', 'seej', '1518010860', 'Deleted product #88', 1, NULL, 2),
(598, '0', 'seej', '1518010892', 'Edited product #85', 1, NULL, 2),
(599, '0', 'seej', '1518010916', 'Edited product #75', 1, NULL, 2),
(600, '0', 'seej', '1518010958', 'Edited product #78', 1, NULL, 2),
(601, '0', 'seej', '1518010993', 'Edited product #77', 1, NULL, 2),
(602, '0', 'seej', '1518012371', 'Edited product #70', 1, NULL, 2),
(603, '0', 'seej', '1518012524', 'Restored product #86', 1, NULL, 2),
(604, '0', 'seej', '1518012634', 'Deleted product #86', 1, NULL, 2),
(605, '0', 'seej', '1518012665', 'Edited product #84', 1, NULL, 2),
(606, '0', 'seej', '1518012856', 'Restored product #86', 1, NULL, 2),
(607, '0', 'seej', '1518012871', 'Deleted product #86', 1, NULL, 2),
(608, '0', 'seej', '1518013028', 'Restored product #86', 1, NULL, 2),
(609, '0', 'seej', '**********', 'Restored product #86', 1, NULL, 2),
(610, '0', 'seej', '**********', 'Restored product #86', 1, NULL, 2),
(611, '0', 'seej', '**********', 'Deleted product #86', 1, NULL, 2),
(612, '0', 'seej', '**********', 'Restored product #87', 1, NULL, 2),
(613, '0', 'seej', '**********', 'Deleted product #87', 1, NULL, 2),
(614, '0', 'seej', '**********', 'Deleted product #72', 1, NULL, 2),
(615, '0', 'seej', '**********', 'Restored product #72', 1, NULL, 2),
(616, '0', 'seej', '**********', 'Added account: dgdsg, safsaf', 1, NULL, NULL),
(617, '0', 'seej', '**********', 'Deleted account #6', 1, NULL, 2),
(618, '0', 'seej', '**********', 'Added account: asdfghjk, asdfghj', 1, NULL, NULL),
(619, '0', 'seej', '**********', 'Added account: ertyui, ewrtyui', 1, NULL, NULL),
(620, '0', 'seej', '**********', 'Deleted account #7', 1, NULL, 2),
(621, '0', 'seej', '**********', 'Deleted account #8', 1, NULL, 2),
(622, '0', 'seej', '**********', 'Added account: sdfg, sdfgh', 1, NULL, NULL),
(623, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(624, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(625, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 1),
(626, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(627, '0', 'seej', '**********', 'Deleted account #9', 1, NULL, 2),
(628, '0', 'seej', '**********', 'Deleted account #1', 1, NULL, 2),
(629, '0', 'seej', '**********', 'Added account: jbjb, jgjb', 1, NULL, NULL),
(630, '0', 'seej', '**********', 'Added account: asdasd, asdasds', 1, NULL, NULL),
(631, '0', 'seej', '**********', 'Added account: asdada, dasd', 1, NULL, NULL),
(632, '0', 'seej', '**********', 'Added account: asdasda, adasdsa', 1, NULL, NULL),
(633, '0', 'seej', '**********', 'Added account: asdad, dasdada', 1, NULL, NULL),
(634, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(635, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(636, '2', 'tabingi_mukha_ko', '**********', 'Logged out.', 1, 1, NULL),
(637, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(638, '0', 'seej', '**********', 'Edited order #41', 1, NULL, 2),
(639, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(640, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(641, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(642, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(643, '2', 'tabingi_mukha_ko', '**********', 'Logged out.', 1, 1, NULL),
(644, '0', 'seej', '********06', 'Logged in.', 1, NULL, 2),
(645, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(646, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(647, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(648, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(649, '0', 'seej', '1518143255', 'Logged out.', 1, NULL, 2),
(650, '1', 'veocalimlim', '1518143310', 'Logged in.', 1, NULL, 1),
(651, '0', 'seej', '1518149049', 'Logged in.', 1, NULL, 2),
(652, '0', 'seej', '1518149132', 'Logged in.', 1, NULL, 2),
(653, '0', 'seej', '1518183025', 'Logged in.', 1, NULL, 2),
(654, '0', 'seej', '1518193319', 'Logged out.', 1, NULL, 2),
(655, '0', 'seej', '1518193342', 'Logged in.', 1, NULL, 2),
(656, '0', 'seej', '1518193451', 'Logged in.', 1, NULL, 2),
(657, '0', 'seej', '1518315382', 'Logged in.', 1, NULL, 2),
(658, '0', 'seej', '1518317035', 'Logged out.', 1, NULL, 2),
(659, '2', 'SM1517056867', '1518317040', 'Logged in.', 1, 9, NULL),
(660, '2', 'SM1517056867', '1518317667', 'Logged out.', 1, 9, NULL),
(661, '0', 'seej', '1518317673', 'Logged in.', 1, NULL, 2),
(662, '0', 'seej', '1518318109', 'Edited order #41', 1, NULL, 2),
(663, '0', 'seej', '1518318111', 'Edited order #41''s status to ''delivered''.', 1, NULL, 2),
(664, '0', 'seej', '1518318129', 'Edited order #50', 1, NULL, 2),
(665, '0', 'seej', '1518318135', 'Edited order #42', 1, NULL, 2),
(666, '0', 'seej', '1518318136', 'Edited order #42''s status to ''delivered''.', 1, NULL, 2),
(667, '0', 'seej', '1518318141', 'Edited order #43', 1, NULL, 2),
(668, '0', 'seej', '1518318142', 'Edited order #43''s status to ''delivered''.', 1, NULL, 2),
(669, '0', 'seej', '1518318147', 'Edited order #44', 1, NULL, 2),
(670, '0', 'seej', '1518318148', 'Edited order #44''s status to ''delivered''.', 1, NULL, 2),
(671, '0', 'seej', '1518318153', 'Edited order #45', 1, NULL, 2),
(672, '0', 'seej', '1518318153', 'Edited order #45''s status to ''delivered''.', 1, NULL, 2),
(673, '0', 'seej', '1518318157', 'Edited order #46', 1, NULL, 2),
(674, '0', 'seej', '1518318157', 'Edited order #46''s status to ''delivered''.', 1, NULL, 2),
(675, '0', 'seej', '1518318164', 'Edited order #47', 1, NULL, 2),
(676, '0', 'seej', '1518318164', 'Edited order #47''s status to ''delivered''.', 1, NULL, 2),
(677, '0', 'seej', '1518318170', 'Edited order #48', 1, NULL, 2),
(678, '0', 'seej', '1518318171', 'Edited order #48''s status to ''delivered''.', 1, NULL, 2),
(679, '0', 'seej', '1518318175', 'Edited order #49', 1, NULL, 2),
(680, '0', 'seej', '1518318176', 'Edited order #49''s status to ''delivered''.', 1, NULL, 2),
(681, '0', 'seej', '1518319143', 'Logged out.', 1, NULL, 2),
(682, '2', 'tabingi_mukha_ko', '1518319152', 'Logged in.', 1, 1, NULL),
(683, '0', 'seej', '1518319230', 'Logged in.', 1, NULL, 2),
(684, '2', 'tabingi_mukha_ko', '1518319759', 'Logged out.', 1, 1, NULL),
(685, '0', 'seej', '1518319764', 'Logged in.', 1, NULL, 2),
(686, '0', 'seej', '1518329830', 'Logged in.', 1, NULL, 2),
(687, '0', 'seej', '1518337512', 'Edited order #50', 1, NULL, 2),
(688, '0', 'seej', '1518337513', 'Edited order #50''s status to ''delivered''.', 1, NULL, 2),
(689, '0', 'seej', '1518340157', 'Logged in.', 1, NULL, 2),
(690, '0', 'seej', '1518345803', 'Logged in.', 1, NULL, 2),
(691, '0', 'seej', '1518347159', 'Logged out.', 1, NULL, 2),
(692, '2', 'tabingi_mukha_ko', '1518347168', 'Logged in.', 1, 1, NULL),
(693, '0', 'seej', '1518347231', 'Edited order #53', 1, NULL, 2),
(694, '0', 'seej', '1518347232', 'Edited order #53''s status to ''delivered''.', 1, NULL, 2),
(695, '0', 'seej', '1518347232', 'Edited order #53', 1, NULL, 2),
(696, '0', 'seej', '1518347232', 'Edited order #53''s status to ''delivered''.', 1, NULL, 2),
(697, '0', 'seej', '1518347314', 'Edited order #54', 1, NULL, 2),
(698, '0', 'seej', '1518347315', 'Edited order #54''s status to ''delivered''.', 1, NULL, 2),
(699, '0', 'seej', '1518347381', 'Edited order #55', 1, NULL, 2),
(700, '0', 'seej', '1518347381', 'Edited order #55''s status to ''delivered''.', 1, NULL, 2),
(701, '2', 'tabingi_mukha_ko', '1518347428', 'Logged out.', 1, 1, NULL),
(702, '0', 'seej', '1518347511', 'Deleted Sales #53', 1, NULL, 2),
(703, '0', 'seej', '1518347524', 'Deleted Sales #52', 1, NULL, 2),
(704, '0', 'seej', '1518347546', 'Deleted Sales #51', 1, NULL, 2),
(705, '0', 'seej', '1518347550', 'Deleted Sales #50', 1, NULL, 2),
(706, '2', 'tabingi_mukha_ko', '1518347635', 'Logged in.', 1, 1, NULL),
(707, '0', 'seej', '1518347674', 'Edited order #56', 1, NULL, 2),
(708, '0', 'seej', '1518347674', 'Edited order #56''s status to ''delivered''.', 1, NULL, 2),
(709, '0', 'seej', '1518347758', 'Deleted Sales #54', 1, NULL, 2),
(710, '0', 'seej', '1518359910', 'Logged in.', 1, NULL, 2),
(713, '1', 'veocalimlim', '1518359952', 'Logged in.', 1, NULL, 1),
(714, '0', 'seej', '1518360339', 'Logged in.', 1, NULL, 2),
(715, '1', 'veocalimlim', '1518360495', 'Logged in.', 1, NULL, 1),
(716, '0', 'seej', '1518361046', 'Logged in.', 1, NULL, 2),
(717, '0', 'seej', '1518361479', 'Edited order #52', 1, NULL, 2),
(718, '0', 'seej', '1518361556', 'Edited order #39', 1, NULL, 2),
(719, '0', 'seej', '1518361556', 'Edited order #39''s status to ''delivered''.', 1, NULL, 2),
(720, '0', 'seej', '1518363392', 'Logged in.', 1, NULL, 2),
(721, '1', 'veocalimlim', '1518363583', 'Logged in.', 1, NULL, 1),
(722, '0', 'seej', '1518363973', 'Logged in.', 1, NULL, 2),
(723, '0', 'seej', '1518364408', 'Logged in.', 1, NULL, 2),
(724, '1', 'veocalimlim', '1518364918', 'Logged in.', 1, NULL, 1),
(725, '0', 'seej', '1518365854', 'Logged in.', 1, NULL, 2),
(726, '1', 'veocalimlim', '1518365932', 'Logged in.', 1, NULL, 1),
(727, '1', 'veocalimlim', '1518373502', 'Logged in.', 1, NULL, 1),
(728, '2', 'tabingi_mukha_ko', '1518373662', 'Logged in.', 1, 1, NULL),
(729, '0', 'seej', '1518373843', 'Edited order #57', 1, NULL, 2),
(730, '0', 'seej', '1518373843', 'Edited order #57''s status to ''delivered''.', 1, NULL, 2),
(731, '2', 'tabingi_mukha_ko', '1518378965', 'Logged out.', 1, 1, NULL),
(732, '2', 'tabingi_mukha_ko', '1518378975', 'Logged in.', 1, 1, NULL),
(733, '2', 'tabingi_mukha_ko', '1518378981', 'Logged out.', 1, 1, NULL),
(734, '2', 'tabingi_mukha_ko', '1518379602', 'Logged in.', 1, 1, NULL),
(735, '2', 'tabingi_mukha_ko', '1518379610', 'Logged out.', 1, 1, NULL),
(736, '2', 'SM1517056867', '1518379656', 'Logged in.', 1, 9, NULL),
(737, '2', 'SM1517056867', '1518379659', 'Logged out.', 1, 9, NULL);
INSERT INTO `user_log` (`log_id`, `user_type`, `username`, `date`, `action`, `status`, `customer_id`, `admin_id`) VALUES
(738, '2', 'tabingi_mukha_ko', '1518422636', 'Logged in.', 1, 1, NULL),
(739, '2', 'tabingi_mukha_ko', '1518422662', 'Logged out.', 1, 1, NULL),
(740, '2', 'tabingi_mukha_ko', '1518422746', 'Logged in.', 1, 1, NULL),
(741, '2', 'tabingi_mukha_ko', '1518422753', 'Logged out.', 1, 1, NULL),
(742, '0', 'seej', '1518422784', 'Logged in.', 1, NULL, 2),
(743, '0', 'seej', '1518436371', 'Logged in.', 1, NULL, 2),
(744, '0', 'seej', '1518436831', 'Logged out.', 1, NULL, 2),
(745, '1', 'veocalimlim', '1518436838', 'Logged in.', 1, NULL, 1),
(746, '0', 'seej', '1518436914', 'Logged in.', 1, NULL, 2),
(747, '0', 'seej', '1518436943', 'Logged in.', 1, NULL, 2),
(748, '0', 'seej', '1518437901', 'Logged in.', 1, NULL, 2),
(749, '0', 'seej', '1518437939', 'Logged out.', 1, NULL, 2),
(750, '2', 'RB1515560608', '1518438013', 'Logged in.', 1, 4, NULL),
(751, '2', 'RB1515560608', '1518438016', 'Logged out.', 1, 4, NULL),
(752, '0', 'seej', '1518438024', 'Logged in.', 1, NULL, 2),
(753, '0', 'seej', '1518438485', 'Logged out.', 1, NULL, 2),
(754, '0', 'seej', '1518438637', 'Logged in.', 1, NULL, 2),
(755, '0', 'seej', '1518468136', 'Logged in.', 1, NULL, 2),
(756, '1', 'veocalimlim', '1518469400', 'Logged in.', 1, NULL, 1),
(757, '1', 'veocalimlim', '1518470287', 'Logged out.', 1, NULL, 1),
(758, '0', 'seej', '1518520882', 'Logged in.', 1, NULL, 2),
(759, '0', 'seej', '1518520921', 'Logged out.', 1, NULL, 2),
(760, '0', 'seej', '1518521737', 'Logged in.', 1, NULL, 2),
(761, '0', 'seej', '1518523956', 'Logged out.', 1, NULL, 2),
(762, '0', 'seej', '1518524956', 'Logged in.', 1, NULL, 2),
(763, '2', 'RO1517055802', '1518525856', 'Logged in.', 1, 8, NULL),
(764, '2', 'RO1517055802', '1518525927', 'Logged out.', 1, 8, NULL),
(765, '0', 'seej', '1518526457', 'Logged in.', 1, NULL, 2),
(766, '0', 'seej', '1518527602', 'Logged out.', 1, NULL, 2),
(767, '0', 'seej', '1518527608', 'Logged in.', 1, NULL, 2),
(768, '2', 'tabingi_mukha_ko', '1518527903', 'Logged in.', 1, 1, NULL),
(769, '0', 'seej', '1518528399', 'Edited order #52', 1, NULL, 2),
(770, '0', 'seej', '1518528426', 'Edited order #51', 1, NULL, 2),
(771, '0', 'seej', '1518528465', 'Edited order #52', 1, NULL, 2),
(772, '2', 'tabingi_mukha_ko', '1518594399', 'Logged in.', 1, 1, NULL),
(773, '2', 'tabingi_mukha_ko', '1518595724', 'Logged out.', 1, 1, NULL),
(774, '0', 'seej', '1518595886', 'Logged in.', 1, NULL, 2),
(775, '2', 'EA1517057098', '1518596854', 'Logged in.', 1, 10, NULL),
(776, '2', 'EA1517057098', '1518600906', 'Commented on product iPhone 8 and rated it 5', 1, 10, NULL),
(777, '2', 'EA1517057098', '1518605150', 'Logged out.', 1, 10, NULL),
(778, '2', 'RB1515560608', '1518608142', 'Logged in.', 1, 4, NULL),
(779, '2', 'RB1515560608', '1518608732', 'Logged out.', 1, 4, NULL),
(780, '2', 'AL1515664193', '1518608756', 'Logged in.', 1, 7, NULL),
(781, '2', 'tabingi_mukha_ko', '1518627031', 'Logged in.', 1, 1, NULL),
(782, '2', 'tabingi_mukha_ko', '1518631351', 'Logged out.', 1, 1, NULL),
(783, '1', 'veocalimlim', '1518631358', 'Logged in.', 1, NULL, 1),
(784, '1', 'veocalimlim', '1518632301', 'Logged out.', 1, NULL, 1),
(785, '0', 'seej', '1518632307', 'Logged in.', 1, NULL, 2),
(786, '2', 'tabingi_mukha_ko', '1518782711', 'Logged in.', 1, 1, NULL),
(787, '2', 'tabingi_mukha_ko', '1518782766', 'Commented on product iPhone 7 and rated it 5', 1, 1, NULL),
(788, '2', 'tabingi_mukha_ko', '1518783576', 'Logged out.', 1, 1, NULL),
(789, '2', 'tabingi_mukha_ko', '1518837717', 'Logged in.', 1, 1, NULL),
(790, '2', 'tabingi_mukha_ko', '1518841243', 'Logged in.', 1, 1, NULL),
(791, '2', 'tabingi_mukha_ko', '1518841257', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(792, '2', 'tabingi_mukha_ko', '1518841275', 'Logged out.', 1, 1, NULL),
(793, '2', 'tabingi_mukha_ko', '1518841753', 'Logged in.', 1, 1, NULL),
(794, '2', 'tabingi_mukha_ko', '1518841857', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 1', 1, 1, NULL),
(795, '2', 'tabingi_mukha_ko', '1518841988', 'Logged out.', 1, 1, NULL),
(796, '1', 'veocalimlim', '1518841993', 'Logged in.', 1, NULL, 1),
(797, '1', 'veocalimlim', '1518842026', 'Logged out.', 1, NULL, 1),
(798, '2', 'tabingi_mukha_ko', '1518845982', 'Logged in.', 1, 1, NULL),
(799, '1', 'veocalimlim', '1518847851', 'Logged in.', 1, NULL, 1),
(800, '1', 'veocalimlim', '1518847899', 'Edited order #52', 1, NULL, 1),
(801, '0', 'seej', '1518928818', 'Cancelled order #6', 1, NULL, 2),
(802, '1', 'veocalimlim', '1518958384', 'Logged out.', 1, NULL, 1),
(803, '2', 'tabingi_mukha_ko', '1518958401', 'Logged in.', 1, 1, NULL),
(804, '0', 'seej', '1518959709', 'Logged in.', 1, NULL, 2),
(805, '0', 'seej', '1518959793', 'Edited order #58', 1, NULL, 2),
(806, '0', 'seej', '1518959793', 'Changed delivery date of order #58 to 2018-02-21', 1, NULL, 2),
(807, '0', 'seej', '1518959838', 'Edited order #51', 1, NULL, 2),
(808, '0', 'seej', '1518959838', 'Changed delivery date of order #51 to 2018-02-14', 1, NULL, 2),
(809, '0', 'seej', '1518959839', 'Edited order #51''s status to ''delivered''.', 1, NULL, 2),
(810, '0', 'seej', '1518959851', 'Edited order #52', 1, NULL, 2),
(811, '0', 'seej', '1518959851', 'Changed delivery date of order #52 to 2018-02-14', 1, NULL, 2),
(812, '0', 'seej', '1518959851', 'Edited order #52''s status to ''delivered''.', 1, NULL, 2),
(813, '0', 'seej', '1518960121', 'Logged in.', 1, NULL, 2),
(814, '0', 'seej', '1518960167', 'Logged in.', 1, NULL, 2),
(815, '2', 'tabingi_mukha_ko', '1518960962', 'Logged in.', 1, 1, NULL),
(816, '0', 'seej', '1518961349', 'Logged in.', 1, NULL, 2),
(817, '0', 'seej', '1518961357', 'Logged out.', 1, NULL, 2),
(818, '2', 'tabingi_mukha_ko', '1518961365', 'Logged in.', 1, 1, NULL),
(819, '2', 'tabingi_mukha_ko', '1518961574', 'Logged in.', 1, 1, NULL),
(820, '2', 'SM1517056867', '1518970487', 'Commented on product iPhone X and rated it 5', 1, 9, NULL),
(821, '2', 'SM1517056867', '1518970496', 'Logged out.', 1, 9, NULL),
(822, '2', 'RO1517055802', '1518970504', 'Logged in.', 1, 8, NULL),
(823, '2', 'RO1517055802', '1518970570', 'Commented on product iPhone X and rated it 4', 1, 8, NULL),
(824, '2', 'RO1517055802', '1518970594', 'Commented on product iPhone X and rated it 2', 1, 8, NULL),
(825, '2', 'RO1517055802', '1518970652', 'Logged out.', 1, 8, NULL),
(826, '2', 'tabingi_mukha_ko', '1518970809', 'Logged in.', 1, 1, NULL),
(827, '2', 'tabingi_mukha_ko', '1518970827', 'Commented on product iPhone X and rated it 1', 1, 1, NULL),
(828, '2', 'tabingi_mukha_ko', '1518973056', 'Commented on product iPhone X and rated it 1', 1, 1, NULL),
(829, '2', 'tabingi_mukha_ko', '**********', 'Commented on product iPhone X and rated it 2', 1, 1, NULL),
(830, '2', 'tabingi_mukha_ko', '**********', 'Commented on product iPhone X and rated it 1', 1, 1, NULL),
(831, '2', 'tabingi_mukha_ko', '**********', 'Commented on product iPhone X and rated it 2', 1, 1, NULL),
(832, '2', 'tabingi_mukha_ko', '**********', 'Commented on product iPhone X and rated it 4', 1, 1, NULL),
(833, '2', 'tabingi_mukha_ko', '**********', 'Logged out.', 1, 1, NULL),
(834, '0', 'seej', '**********', 'Deleted account #12', 1, NULL, 2),
(835, '0', 'seej', '**********', 'Deleted account #13', 1, NULL, 2),
(836, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(837, '0', 'seej', '**********', 'Edited order #61', 1, NULL, 2),
(838, '0', 'seej', '**********', 'Cancelled order #3', 1, NULL, 2),
(839, '0', 'seej', '**********', 'Cancelled order #2', 1, NULL, 2),
(840, '2', 'tabingi_mukha_ko', '**********', 'Commented on product iPhone 8 and rated it 2', 1, 1, NULL),
(841, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(842, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(843, '2', 'tabingi_mukha_ko', '**********', 'Commented on product Apple iPad Mini 2 and rated it 3', 1, 1, NULL),
(844, '2', 'tabingi_mukha_ko', '**********', 'Commented on product Apple iPad Pro and rated it 1', 1, 1, NULL),
(845, '2', 'tabingi_mukha_ko', '**********', 'Logged out.', 1, 1, NULL),
(846, '0', 'seej', '**********', 'Deleted account #14', 1, NULL, 2),
(847, '0', 'seej', '**********', 'Cancelled order #12', 1, NULL, 2),
(848, '0', 'seej', '**********', 'Deleted account #14', 1, NULL, 2),
(849, '0', 'seej', '**********', 'Deleted account #13', 1, NULL, 2),
(850, '0', 'seej', '**********', 'Deleted account #12', 1, NULL, 2),
(851, '0', 'seej', '**********', 'Deleted account #11', 1, NULL, 2),
(852, '0', 'seej', '**********', 'Deleted account #10', 1, NULL, 2),
(853, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 1),
(854, '1', 'veocalimlim', '**********', 'Cancelled order #11', 1, NULL, 1),
(855, '1', 'veocalimlim', '**********', 'Cancelled order #10', 1, NULL, 1),
(856, '1', 'veocalimlim', '**********', 'Cancelled order #6', 1, NULL, 1),
(857, '1', 'veocalimlim', '**********', 'Logged out.', 1, NULL, 1),
(858, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(859, '2', 'tabingi_mukha_ko', '**********', 'Updated his/her profile.', 1, 1, NULL),
(860, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 1),
(861, '1', 'veocalimlim', '**********', 'Logged out.', 1, NULL, 1),
(862, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 1),
(863, '0', 'seej', '**********', 'Edited order #58', 1, NULL, 2),
(864, '0', 'seej', '**********', 'Changed delivery date of order #58 to 2018-02-21', 1, NULL, 2),
(865, '0', 'seej', '**********', 'Edited order #58''s status to ''delivered''.', 1, NULL, 2),
(866, '0', 'seej', '1519089259', 'Logged in.', 1, NULL, 2),
(867, '1', 'Calimlim Veo', '1519101590', 'Signed up.', 1, 16, NULL),
(868, '1', 'Leona Andrew', '1519102094', 'Signed up.', 1, 17, NULL),
(869, '2', 'tabingi_mukha_ko', '1519102455', 'Logged in.', 1, 1, NULL),
(870, '2', 'tabingi_mukha_ko', '1519125230', 'Logged in.', 1, 1, NULL),
(871, '2', 'tabingi_mukha_ko', '1519127884', 'Logged in.', 1, 1, NULL),
(872, '2', 'tabingi_mukha_ko', '1519129117', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(873, '2', 'tabingi_mukha_ko', '1519129130', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(874, '2', 'tabingi_mukha_ko', '1519129204', 'Commented on product iPhone 7 and rated it 5', 1, 1, NULL),
(875, '2', 'tabingi_mukha_ko', '1519129211', 'Commented on product iPhone 7 and rated it 3', 1, 1, NULL),
(876, '2', 'tabingi_mukha_ko', '1519129223', 'Commented on product Samsung Galaxy Tab A 7.0 and rated it 4', 1, 1, NULL),
(877, '2', 'tabingi_mukha_ko', '1519129236', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(878, '2', 'tabingi_mukha_ko', '1519129293', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(879, '2', 'tabingi_mukha_ko', '1519130515', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(880, '2', 'tabingi_mukha_ko', '1519130874', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 0', 1, 1, NULL),
(881, '2', 'tabingi_mukha_ko', '1519130884', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(882, '2', 'tabingi_mukha_ko', '1519131140', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 2', 1, 1, NULL),
(883, '2', 'tabingi_mukha_ko', '1519131881', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 3', 1, 1, NULL),
(884, '2', 'tabingi_mukha_ko', '1519131967', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(885, '2', 'tabingi_mukha_ko', '1519132019', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(886, '2', 'tabingi_mukha_ko', '1519132079', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 3', 1, 1, NULL),
(887, '2', 'tabingi_mukha_ko', '1519132184', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 3', 1, 1, NULL),
(888, '2', 'tabingi_mukha_ko', '1519132368', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(889, '2', 'tabingi_mukha_ko', '1519132370', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(890, '2', 'tabingi_mukha_ko', '1519132371', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(891, '2', 'tabingi_mukha_ko', '1519132371', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(892, '2', 'tabingi_mukha_ko', '1519132371', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(893, '2', 'tabingi_mukha_ko', '1519132646', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(894, '2', 'tabingi_mukha_ko', '1519132658', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(895, '2', 'tabingi_mukha_ko', '1519132659', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(896, '2', 'tabingi_mukha_ko', '1519132660', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(897, '2', 'tabingi_mukha_ko', '1519132707', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 3', 1, 1, NULL),
(898, '2', 'tabingi_mukha_ko', '1519132717', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(899, '2', 'tabingi_mukha_ko', '1519132729', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(900, '2', 'tabingi_mukha_ko', '1519132736', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 1', 1, 1, NULL),
(901, '2', 'tabingi_mukha_ko', '1519132743', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 1', 1, 1, NULL),
(902, '2', 'tabingi_mukha_ko', '1519132754', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(903, '2', 'tabingi_mukha_ko', '1519132783', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(904, '2', 'tabingi_mukha_ko', '1519132806', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 1', 1, 1, NULL),
(905, '2', 'tabingi_mukha_ko', '1519132844', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(906, '2', 'tabingi_mukha_ko', '1519136088', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 3', 1, 1, NULL),
(907, '2', 'tabingi_mukha_ko', '1519136151', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(908, '2', 'tabingi_mukha_ko', '1519136158', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(909, '2', 'tabingi_mukha_ko', '1519136169', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 1', 1, 1, NULL),
(910, '2', 'tabingi_mukha_ko', '1519136183', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 1', 1, 1, NULL),
(911, '2', 'tabingi_mukha_ko', '1519136190', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(912, '2', 'tabingi_mukha_ko', '1519136331', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(913, '2', 'tabingi_mukha_ko', '1519136428', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(914, '2', 'tabingi_mukha_ko', '1519136435', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 1', 1, 1, NULL),
(915, '2', 'tabingi_mukha_ko', '1519136455', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 2', 1, 1, NULL),
(916, '2', 'tabingi_mukha_ko', '1519136464', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 5', 1, 1, NULL),
(917, '2', 'tabingi_mukha_ko', '1519136476', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 3', 1, 1, NULL),
(918, '2', 'tabingi_mukha_ko', '1519136502', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 1', 1, 1, NULL),
(919, '2', 'tabingi_mukha_ko', '1519136555', 'Commented on product ASUS Laptop X556UQ-NH71 and rated it 5', 1, 1, NULL),
(920, '2', 'tabingi_mukha_ko', '1519136939', 'Commented on product ASUS Laptop X556UQ-NH71 and rated it 5', 1, 1, NULL),
(921, '2', 'tabingi_mukha_ko', '1519137000', 'Commented on product ASUS Laptop X556UQ-NH71 and rated it 5', 1, 1, NULL),
(922, '2', 'tabingi_mukha_ko', '1519137007', 'Commented on product ASUS Laptop X556UQ-NH71 and rated it 1', 1, 1, NULL),
(923, '2', 'tabingi_mukha_ko', '1519137049', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 1, NULL),
(924, '2', 'tabingi_mukha_ko', '**********', 'Logged out.', 1, 1, NULL),
(925, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(926, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(927, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(928, '0', 'seej', '**********', 'Added product: Dell Inspiron 3467', 1, NULL, 2),
(929, '0', 'seej', '**********', 'Added product: Apple Earphones', 1, NULL, 2),
(930, '0', 'seej', '**********', 'Added product: Romoss Solo 5 Powerbank', 1, NULL, 2),
(931, '0', 'seej', '**********', 'Added product: Acer Chromebook CB3-131-C4NW N2840', 1, NULL, 2),
(932, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(933, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(934, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(935, '2', 'tabingi_mukha_ko', '**********', 'Logged in.', 1, 1, NULL),
(936, '2', 'tabingi_mukha_ko', '**********', 'Logged out.', 1, 1, NULL),
(937, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(938, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(939, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(940, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(941, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(942, '1', 'veocalimlim', '**********', 'Logged in.', 1, NULL, 1),
(943, '1', 'veocalimlim', '**********', 'Logged out.', 1, NULL, 1),
(944, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(945, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(946, '1', 'veocalimlim', '1519272987', 'Logged in.', 1, NULL, 1),
(947, '1', 'veocalimlim', '1519273047', 'Logged out.', 1, NULL, 1),
(948, '2', 'tabingi_mukha_ko', '1519273060', 'Logged in.', 1, 1, NULL),
(949, '2', 'tabingi_mukha_ko', '1519273214', 'Logged out.', 1, 1, NULL),
(950, '2', 'tabingi_mukha_ko', '1519273345', 'Logged in.', 1, 1, NULL),
(951, '2', 'tabingi_mukha_ko', '1519420998', 'Logged out.', 1, 1, NULL),
(952, '0', 'seej', '1519421280', 'Logged in.', 1, NULL, 2),
(953, '0', 'seej', '1519797754', 'Logged in.', 1, NULL, 2),
(954, '0', 'seej', '1519813594', 'Added product: APPLE AIRPODS', 1, NULL, 2),
(955, '0', 'seej', '1519814332', 'Added product: APPLE 30-PIN TO USB CABLE', 1, NULL, 2),
(956, '0', 'seej', '1519814439', 'Added product: APPLE 12W USB POWER ADAPTER', 1, NULL, 2),
(957, '0', 'seej', '1519814757', 'Added product: APPLE LIGHTNING DIGITAL AV ADAPTER', 1, NULL, 2),
(958, '0', 'seej', '1519815016', 'Added product: APPLE LIGHTNING TO 30-PIN ADAPTER', 1, NULL, 2),
(959, '0', 'seej', '**********', 'Added product: APPLE LIGHTNING TO 30-PIN ADAPTER (0.2M)', 1, NULL, 2),
(960, '0', 'seej', '**********', 'Added product: AUKEY PA-T11 6-PORT CHARGING STATION WITH QUICK CHARGE 3.0', 1, NULL, 2),
(961, '0', 'seej', '**********', 'Added product: AUKEY PA-U32 MINI DUAL PORT WALL CHARGER WITH FOLDABLE PLUG', 1, NULL, 2),
(962, '0', 'seej', '**********', 'Added product: AUKEY PB-AT10 10050MAH POWERALL POWER BANK WITH QUICK CHARGE 3.0', 1, NULL, 2),
(963, '0', 'seej', '**********', 'Added product: AUKEY PB-N41 POCKET 5000MAH POWER BANK ?1,17900 ?1,179.00', 1, NULL, 2),
(964, '0', 'seej', '**********', 'Added product: AUKEY PB-N42 POCKET 10000MAH POWER BANK', 1, NULL, 2),
(965, '0', 'seej', '**********', 'Added product: AUKEY PB-T10 20000MAH POWER BANK WITH QUICK CHARGE 3.0', 1, NULL, 2),
(966, '0', 'seej', '**********', 'Added product: AVANTREE - WALRUS - WATERPROOF CASE WITH EARPHONE JACK', 1, NULL, 2),
(967, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(968, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(969, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(970, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(971, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(972, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(973, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(974, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(975, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(976, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(977, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(978, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(979, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(980, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(981, '0', 'seej', '1519889226', 'Logged in.', 1, NULL, 2),
(982, '0', 'seej', '1519889839', 'Logged out.', 1, NULL, 2),
(983, '0', 'seej', '1519890055', 'Logged in.', 1, NULL, 2),
(984, '0', 'seej', '1519890200', 'Logged in.', 1, NULL, 2),
(985, '0', 'seej', '1519892272', 'Logged in.', 1, NULL, 2),
(986, '0', 'seej', '1519892450', 'Logged out.', 1, NULL, 2),
(987, '1', 'veocalimlim', '1519892654', 'Logged in.', 1, NULL, 1),
(988, '1', 'veocalimlim', '1519892667', 'Logged out.', 1, NULL, 1),
(989, '0', 'seej', '1519892714', 'Logged in.', 1, NULL, 2),
(990, '0', 'seej', '1519892724', 'Logged out.', 1, NULL, 2),
(991, '2', 'tabingi_mukha_ko', '1519895337', 'Logged in.', 1, 1, NULL),
(992, '2', 'tabingi_mukha_ko', '1519895528', 'Logged out.', 1, 1, NULL),
(993, '0', 'seej', '1519895535', 'Logged in.', 1, NULL, 2),
(994, '2', 'tabingi_mukha_ko', '1519895816', 'Logged in.', 1, 1, NULL),
(995, '0', 'seej', '1519899511', 'Logged in.', 1, NULL, 2),
(996, '0', 'seej', '1519899570', 'Logged out.', 1, NULL, 2),
(997, '2', 'tabingi_mukha_ko', '1519899578', 'Logged in.', 1, 1, NULL),
(998, '2', 'tabingi_mukha_ko', '1519899773', 'Logged out.', 1, 1, NULL),
(999, '0', 'seej', '1519899780', 'Logged in.', 1, NULL, 2),
(1000, '0', 'seej', '1519900555', 'Logged in.', 1, NULL, 2),
(1001, '0', 'seej', '1519900571', 'Logged out.', 1, NULL, 2),
(1002, '2', 'tabingi_mukha_ko', '1519900577', 'Logged in.', 1, 1, NULL),
(1003, '1', 'veocalimlim', '1519900676', 'Logged in.', 1, NULL, 1),
(1004, '0', 'seej', '1519953361', 'Logged in.', 1, NULL, 2),
(1005, '2', 'tabingi_mukha_ko', '1519954976', 'Logged in.', 1, 1, NULL),
(1006, '2', 'tabingi_mukha_ko', '1519957985', 'Commented on product Aukey PA-U32 Mini Dual Port Wall Charger with Foldable Plug and rated it 3', 1, 1, NULL),
(1007, '0', 'seej', '1519959531', 'Edited order #59', 1, NULL, 2),
(1008, '0', 'seej', '1519959531', 'Changed delivery date of order #59 to 2018-02-22', 1, NULL, 2),
(1009, '0', 'seej', '1519959532', 'Edited order #59''s status to ''delivered''.', 1, NULL, 2),
(1010, '2', 'tabingi_mukha_ko', '1519959625', 'Logged out.', 1, 1, NULL),
(1011, '2', 'RB1515560608', '1519959631', 'Logged in.', 1, 4, NULL),
(1012, '0', 'seej', '1519976260', 'Logged in.', 1, NULL, 2),
(1013, '0', 'seej', '1519976295', 'Logged out.', 1, NULL, 2),
(1014, '0', 'seej', '1519976328', 'Logged in.', 1, NULL, 2),
(1015, '0', 'seej', '1519976389', 'Logged out.', 1, NULL, 2),
(1016, '2', 'RB1515560608', '1519976450', 'Logged in.', 1, 4, NULL),
(1017, '2', 'RB1515560608', '1519976468', 'Commented on product iPhone 8 and rated it 5', 1, 4, NULL),
(1018, '2', 'RB1515560608', '1519976499', 'Commented on product Apple iPad Mini 2 and rated it 2', 1, 4, NULL),
(1019, '0', 'seej', '1519976729', 'Logged in.', 1, NULL, 2),
(1020, '2', 'RB1515560608', '1520003563', 'Logged out.', 1, 4, NULL),
(1021, '2', 'RB1515560608', '1520003575', 'Logged in.', 1, 4, NULL),
(1022, '2', 'RB1515560608', '1520003601', 'Commented on product ASUS Laptop X556UQ-NH71 and rated it 3', 1, 4, NULL),
(1023, '2', 'RB1515560608', '1520003696', 'Commented on product iPhone 8 and rated it 4', 1, 4, NULL),
(1024, '2', 'RB1515560608', '1520005253', 'Commented on product Apple Airpods and rated it 1', 1, 4, NULL),
(1025, '0', 'seej', '1520009891', 'Logged in.', 1, NULL, 2),
(1026, '0', 'seej', '1520011660', 'Logged in.', 1, NULL, 2),
(1027, '0', 'seej', '1520087283', 'Logged in.', 1, NULL, 2),
(1028, '2', 'RB1515560608', '1520087818', 'Logged in.', 1, 4, NULL),
(1029, '2', 'RB1515560608', '1520087838', 'Commented on product Apple Earphones and rated it 5', 1, 4, NULL),
(1030, '2', 'RB1515560608', '1520087865', 'Commented on product Apple 12W USB Power Adapter and rated it 3', 1, 4, NULL),
(1031, '2', 'RB1515560608', '1520087928', 'Commented on product HP Laptop - 15z touch optional and rated it 4', 1, 4, NULL),
(1032, '2', 'RB1515560608', '1520088447', 'Commented on product HP Stream 11.6 and rated it 4', 1, 4, NULL),
(1033, '2', 'RB1515560608', '1520088552', 'Commented on product HP Stream 11.6 and rated it 4', 1, 4, NULL),
(1034, '2', 'RB1515560608', '1520088961', 'Logged out.', 1, 4, NULL),
(1035, '2', 'RB1515560608', '1520088971', 'Logged in.', 1, 4, NULL),
(1036, '2', 'RB1515560608', '1520088985', 'Commented on product iPhone X and rated it 4', 1, 4, NULL),
(1037, '2', 'RB1515560608', '1520089193', 'Commented on product iPhone 8 and rated it 3', 1, 4, NULL),
(1038, '2', 'RB1515560608', '1520089212', 'Commented on product iPhone X and rated it 2', 1, 4, NULL),
(1039, '2', 'RB1515560608', '1520089254', 'Logged out.', 1, 4, NULL),
(1040, '2', 'RB1515560608', '1520089262', 'Logged in.', 1, 4, NULL),
(1041, '2', 'RB1515560608', '1520089814', 'Commented on product iPhone 7 and rated it 5', 1, 4, NULL),
(1042, '2', 'RB1515560608', '1520089878', 'Commented on product iPhone X and rated it 1', 1, 4, NULL),
(1043, '2', 'RB1515560608', '1520091289', 'Logged out.', 1, 4, NULL),
(1044, '2', 'RB1515560608', '1520093745', 'Logged in.', 1, 4, NULL),
(1045, '2', 'RB1515560608', '1520094451', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 2', 1, 4, NULL),
(1046, '2', 'RB1515560608', '1520094482', 'Commented on product Aukey PA-T11 6-port Charging Station with Quick Charge 3.0 and rated it 2', 1, 4, NULL),
(1047, '2', 'RB1515560608', '1520094512', 'Logged out.', 1, 4, NULL),
(1048, '2', 'RB1515560608', '1520094537', 'Logged in.', 1, 4, NULL),
(1049, '2', 'RB1515560608', '1520094575', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 4, NULL),
(1050, '2', 'RB1515560608', '1520094592', 'Commented on product Samsung Galaxy C5 and rated it 2', 1, 4, NULL),
(1051, '2', 'RB1515560608', '1520094728', 'Commented on product Samsung J7 and rated it 3', 1, 4, NULL),
(1052, '2', 'RB1515560608', '1520094762', 'Commented on product Samsung S4 and rated it 3', 1, 4, NULL),
(1053, '2', 'RB1515560608', '1520094804', 'Commented on product Apple 30-pin to USB Cable and rated it 2', 1, 4, NULL),
(1054, '2', 'RB1515560608', '1520094849', 'Commented on product Samsung J7 and rated it 2', 1, 4, NULL),
(1055, '2', 'RB1515560608', '1520094865', 'Commented on product Apple iPad Mini 2 and rated it 3', 1, 4, NULL),
(1056, '2', 'RB1515560608', '1520094900', 'Commented on product iPad Air 2 and rated it 2', 1, 4, NULL),
(1057, '2', 'RB1515560608', '1520094908', 'Logged out.', 1, 4, NULL),
(1058, '2', 'RB1515560608', '1520094932', 'Logged in.', 1, 4, NULL),
(1059, '2', 'RB1515560608', '1520094979', 'Commented on product ACER ASPIRE ES1 332 BLACK and rated it 4', 1, 4, NULL),
(1060, '2', 'RB1515560608', '1520095001', 'Commented on product Aukey PA-T11 6-port Charging Station with Quick Charge 3.0 and rated it 5', 1, 4, NULL),
(1061, '2', 'RB1515560608', '1520095046', 'Commented on product Aukey PA-U32 Mini Dual Port Wall Charger with Foldable Plug and rated it 2', 1, 4, NULL),
(1062, '2', 'RB1515560608', '**********', 'Logged out.', 1, 4, NULL),
(1063, '0', 'seej', '1520095137', 'Logged in.', 1, NULL, 2),
(1064, '2', 'RO1517055802', '1520095279', 'Logged in.', 1, 8, NULL),
(1065, '2', 'RO1517055802', '1520095292', 'Commented on product iPad Air 2 and rated it 3', 1, 8, NULL),
(1066, '2', 'RO1517055802', '1520095318', 'Commented on product Samsung Galaxy Tab A 7.0 and rated it 4', 1, 8, NULL),
(1067, '2', 'RO1517055802', '1520095432', 'Commented on product Apple Earphones and rated it 3', 1, 8, NULL),
(1068, '2', 'RO1517055802', '1520095489', 'Logged out.', 1, 8, NULL),
(1069, '2', 'RB1515560608', '1520095497', 'Logged in.', 1, 4, NULL),
(1070, '2', 'RB1515560608', '1520095508', 'Commented on product Apple Lightning Digital AV Adapter and rated it 3', 1, 4, NULL),
(1071, '2', 'RB1515560608', '1520095522', 'Commented on product Apple Airpods and rated it 5', 1, 4, NULL),
(1072, '2', 'RB1515560608', '**********', 'Commented on product iPhone 8 and rated it 2', 1, 4, NULL),
(1073, '2', 'RB1515560608', '**********', 'Logged out.', 1, 4, NULL),
(1074, '2', 'RO1517055802', '**********', 'Logged in.', 1, 8, NULL),
(1075, '2', 'RO1517055802', '**********', 'Commented on product iPhone X and rated it 4', 1, 8, NULL),
(1076, '2', 'RO1517055802', '**********', 'Commented on product Aukey PB-AT10 10050mah Powerall Power Bank with Quick Charge 3.0 and rated it 5', 1, 8, NULL),
(1077, '2', 'RO1517055802', '**********', 'Logged out.', 1, 8, NULL),
(1078, '2', 'RO1517055802', '**********', 'Logged in.', 1, 8, NULL),
(1079, '2', 'RO1517055802', '**********', 'Logged out.', 1, 8, NULL),
(1080, '2', 'SM1517056867', '**********', 'Logged in.', 1, 9, NULL),
(1081, '2', 'SM1517056867', '**********', 'Commented on product iPhone 7 and rated it 4', 1, 9, NULL),
(1082, '2', 'SM1517056867', '**********', 'Commented on product iPhone 7 and rated it 4', 1, 9, NULL),
(1083, '2', 'SM1517056867', '**********', 'Commented on product iPhone X and rated it 4', 1, 9, NULL),
(1084, '2', 'SM1517056867', '**********', 'Commented on product iPhone X and rated it 3', 1, 9, NULL),
(1085, '2', 'SM1517056867', '**********', 'Commented on product iPhone X and rated it 3', 1, 9, NULL),
(1086, '2', 'SM1517056867', '**********', 'Commented on product iPhone 8 and rated it 1', 1, 9, NULL),
(1087, '2', 'SM1517056867', '**********', 'Commented on product iPhone 8 and rated it 1', 1, 9, NULL),
(1088, '2', 'SM1517056867', '**********', 'Logged out.', 1, 9, NULL),
(1089, '2', 'RO1517055802', '1520096285', 'Logged in.', 1, 8, NULL),
(1090, '0', 'seej', '1520096920', 'Logged out.', 1, NULL, 2),
(1091, '1', 'veocalimlim', '1520096952', 'Logged in.', 1, NULL, 1),
(1092, '1', 'veocalimlim', '1520096959', 'Logged out.', 1, NULL, 1),
(1093, '0', 'seej', '1520096971', 'Logged in.', 1, NULL, 2),
(1094, '0', 'seej', '1520096989', 'Logged out.', 1, NULL, 2),
(1095, '1', 'veocalimlim', '1520097044', 'Logged in.', 1, NULL, 1),
(1096, '2', 'RO1517055802', '**********', 'Logged out.', 1, 8, NULL),
(1097, '2', 'RO1517055802', '1520099497', 'Logged in.', 1, 8, NULL),
(1098, '2', 'RO1517055802', '**********', 'Logged out.', 1, 8, NULL),
(1099, '2', 'RB1515560608', '1520100115', 'Logged in.', 1, 4, NULL),
(1100, '2', 'RB1515560608', '1520101216', 'Logged out.', 1, 4, NULL),
(1101, '1', 'veocalimlim', '1520101708', 'Edited product #72', 1, NULL, 1),
(1102, '0', 'seej', '1520102329', 'Logged in.', 1, NULL, 2),
(1103, '0', 'seej', '1520102519', 'Logged in.', 1, NULL, 2),
(1104, '0', 'seej', '1520103056', 'Edited product #73', 1, NULL, 2),
(1105, '0', 'seej', '1520103065', 'Logged out.', 1, NULL, 2),
(1106, '1', 'veocalimlim', '1520103359', 'Added product: sdfg', 1, NULL, 1),
(1107, '1', 'veocalimlim', '1520103390', 'Edited product #106', 1, NULL, 1),
(1108, '1', 'veocalimlim', '1520103399', 'Deleted product #106', 1, NULL, 1),
(1109, '1', 'veocalimlim', '1520103554', 'Edited product #80', 1, NULL, 1),
(1110, '1', 'veocalimlim', '**********', 'Edited product #89', 1, NULL, 1),
(1111, '1', 'veocalimlim', '1520105034', 'Updated his/her profile picture', 1, NULL, 1),
(1112, '1', 'veocalimlim', '1520105112', 'Updated his/her profile picture', 1, NULL, 1),
(1113, '1', 'veocalimlim', '1520105126', 'Logged out.', 1, NULL, 1),
(1114, '0', 'seej', '1520105131', 'Logged in.', 1, NULL, 2),
(1115, '0', 'seej', '1520105325', 'Updated his/her profile picture', 1, NULL, 2),
(1116, '0', 'seej', '1520105553', 'Updated his/her profile picture', 1, NULL, 2),
(1117, '0', 'seej', '1520105785', 'Updated his/her profile picture', 1, NULL, 2),
(1118, '2', 'RO1517055802', '1520105861', 'Logged in.', 1, 8, NULL),
(1119, '0', 'seej', '1520135875', 'Logged in.', 1, NULL, 2),
(1120, '0', 'seej', '1520136396', 'Logged in.', 1, NULL, 2),
(1121, '0', 'seej', '1520174122', 'Logged in.', 1, NULL, 2),
(1122, '0', 'seej', '1520174501', 'Logged out.', 1, NULL, 2),
(1123, '2', 'RO1517055802', '1520174510', 'Logged in.', 1, 8, NULL),
(1124, '0', 'seej', '1520175328', 'Logged in.', 1, NULL, 2),
(1125, '2', 'RO1517055802', '**********', 'Logged out.', 1, 8, NULL),
(1126, '2', 'RB1515560608', '1520175593', 'Logged in.', 1, 4, NULL),
(1127, '0', 'seej', '1520176587', 'Logged out.', 1, NULL, 2),
(1128, '0', 'seej', '1520179133', 'Logged in.', 1, NULL, 2),
(1129, '2', 'RB1515560608', '**********', 'Logged out.', 1, 4, NULL),
(1130, '0', 'seej', '1520179314', 'Logged in.', 1, NULL, 2),
(1131, '0', 'seej', '1520179560', 'Logged out.', 1, NULL, 2),
(1132, '0', 'seej', '1520179569', 'Logged in.', 1, NULL, 2),
(1133, '0', 'seej', '1520179594', 'Logged in.', 1, NULL, 2),
(1134, '2', 'RB1515560608', '1520180107', 'Logged in.', 1, 4, NULL),
(1135, '2', 'RB1515560608', '**********', 'Logged out.', 1, 4, NULL),
(1136, '2', 'RB1515560608', '1520184031', 'Logged out.', 1, 4, NULL),
(1137, '2', 'RB1515560608', '1520184095', 'Logged out.', 1, 4, NULL),
(1138, '2', 'RB1515560608', '1520184113', 'Logged out.', 1, 4, NULL),
(1139, '2', 'RB1515560608', '1520184158', 'Logged in.', 1, 4, NULL),
(1140, '2', 'RB1515560608', '1520184248', 'Logged out.', 1, 4, NULL),
(1141, '2', 'RB1515560608', '1520184261', 'Logged in.', 1, 4, NULL),
(1142, '2', 'RB1515560608', '1520184288', 'Logged out.', 1, 4, NULL),
(1143, '2', 'RO1517055802', '1520184336', 'Logged in.', 1, 8, NULL),
(1144, '2', 'RO1517055802', '1520185073', 'Logged out.', 1, 8, NULL),
(1145, '2', 'RB1515560608', '1520185089', 'Logged in.', 1, 4, NULL),
(1146, '2', 'RB1515560608', '1520185143', 'Logged out.', 1, 4, NULL),
(1147, '2', 'RB1515560608', '1520185167', 'Logged in.', 1, 4, NULL),
(1148, '2', 'RB1515560608', '1520185426', 'Logged out.', 1, 4, NULL),
(1149, '2', 'johndoe', '1520185461', 'Logged in.', 1, 1, NULL),
(1150, '2', 'johndoe', '1520185509', 'Logged out.', 1, 1, NULL),
(1151, '2', 'johndoe', '1520185515', 'Logged in.', 1, 1, NULL),
(1152, '2', 'johndoe', '1520185556', 'Logged out.', 1, 1, NULL),
(1153, '2', 'RO1517055802', '1520185570', 'Logged in.', 1, 8, NULL),
(1154, '2', 'RO1517055802', '1520185615', 'Logged out.', 1, 8, NULL),
(1155, '2', 'RB1515560608', '1520185622', 'Logged in.', 1, 4, NULL),
(1156, '2', 'johndoe', '1520188639', 'Logged in.', 1, 1, NULL),
(1157, '2', 'johndoe', '1520188831', 'Logged out.', 1, 1, NULL),
(1158, '2', 'johndoe', '1520188842', 'Logged in.', 1, 1, NULL),
(1159, '2', 'johndoe', '1520188849', 'Logged out.', 1, 1, NULL),
(1160, '2', 'RB1515560608', '1520188880', 'Logged in.', 1, 4, NULL),
(1161, '2', 'RB1515560608', '1520188891', 'Logged out.', 1, 4, NULL),
(1162, '2', 'RB1515560608', '1520188936', 'Logged in.', 1, 4, NULL),
(1163, '2', 'RB1515560608', '1520188980', 'Logged out.', 1, 4, NULL),
(1164, '0', 'seej', '1520188999', 'Logged out.', 1, NULL, 2),
(1165, '2', 'RB1515560608', '1520189014', 'Logged in.', 1, 4, NULL),
(1166, '0', 'seej', '1520189079', 'Logged in.', 1, NULL, 2),
(1167, '2', 'RB1515560608', '1520191220', 'Logged out.', 1, 4, NULL),
(1168, '2', 'RO1517055802', '1520191234', 'Logged in.', 1, 8, NULL),
(1169, '2', 'RO1517055802', '1520191271', 'Logged out.', 1, 8, NULL),
(1170, '2', 'johndoe', '1520191286', 'Logged in.', 1, 1, NULL),
(1171, '2', 'johndoe', '**********', 'Logged out.', 1, 1, NULL),
(1172, '2', 'johndoe', '1520193380', 'Logged in.', 1, 1, NULL),
(1173, '2', 'johndoe', '**********', 'Logged out.', 1, 1, NULL),
(1174, '2', 'johndoe', '1520193602', 'Logged in.', 1, 1, NULL),
(1175, '2', 'johndoe', '**********', 'Logged out.', 1, 1, NULL),
(1176, '2', 'RB1515560608', '1520193664', 'Logged in.', 1, 4, NULL),
(1177, '2', 'RB1515560608', '**********', 'Logged out.', 1, 4, NULL),
(1178, '2', 'SM1517056867', '1520193910', 'Logged in.', 1, 9, NULL),
(1179, '2', 'SJ1517060191', '1520195148', 'Logged in.', 1, 11, NULL),
(1180, '2', 'SJ1517060191', '**********', 'Logged out.', 1, 11, NULL),
(1181, '2', 'SJ1517060191', '1520196578', 'Logged in.', 1, 11, NULL),
(1182, '2', 'SJ1517060191', '**********', 'Logged out.', 1, 11, NULL),
(1183, '2', 'SJ1517060191', '1520196870', 'Logged in.', 1, 11, NULL),
(1184, '2', 'SJ1517060191', '1520196947', 'Logged in.', 1, 11, NULL),
(1185, '2', 'SJ1517060191', '1520197093', 'Logged in.', 1, 11, NULL),
(1186, '2', 'SJ1517060191', '1520197150', 'Logged in.', 1, 11, NULL),
(1187, '2', 'SJ1517060191', '**********', 'Logged out.', 1, 11, NULL),
(1188, '0', 'seej', '1520200294', 'Logged in.', 1, NULL, 2),
(1189, '0', 'seej', '1520200843', 'Logged in.', 1, NULL, 2),
(1190, '0', 'seej', '1520201688', 'Logged in.', 1, NULL, 2),
(1191, '0', 'seej', '1520219418', 'Logged in.', 1, NULL, 2),
(1192, '2', 'SJ1517060191', '1520219442', 'Logged in.', 1, 11, NULL),
(1193, '2', 'SJ1517060191', '**********', 'Logged out.', 1, 11, NULL),
(1194, '2', 'RB1515560608', '1520219493', 'Logged in.', 1, 4, NULL),
(1195, '0', 'seej', '1520219894', 'Logged in.', 1, NULL, 2),
(1196, '0', 'seej', '1520220058', 'Logged in.', 1, NULL, 2),
(1197, '0', 'seej', '1520271529', 'Logged in.', 1, NULL, 2),
(1198, '0', 'seej', '1520271552', 'Logged out.', 1, NULL, 2),
(1199, '1', 'SR1520271710', '**********', 'Signed up.', 1, 19, NULL),
(1200, '2', 'SR1520271710', '1520271816', 'Logged in.', 1, 19, NULL),
(1201, '0', 'seej', '1520271876', 'Logged in.', 1, NULL, 2),
(1202, '2', 'SR1520271710', '1520272230', 'Logged out.', 1, 19, NULL),
(1203, '2', 'SR1520271710', '1520272239', 'Logged in.', 1, 19, NULL),
(1204, '2', 'SR1520271710', '**********', 'Logged out.', 1, 19, NULL),
(1205, '2', 'SR1520271710', '1520272298', 'Logged in.', 1, 19, NULL),
(1206, '2', 'SR1520271710', '**********', 'Logged out.', 1, 19, NULL),
(1207, '2', 'SR1520271710', '1520272322', 'Logged in.', 1, 19, NULL),
(1208, '2', 'SR1520271710', '**********', 'Logged out.', 1, 19, NULL),
(1209, '2', 'RB1515560608', '1520272371', 'Logged in.', 1, 4, NULL),
(1210, '2', 'RB1515560608', '**********', 'Logged out.', 1, 4, NULL),
(1211, '2', 'RO1517055802', '1520272391', 'Logged in.', 1, 8, NULL),
(1212, '2', 'RO1517055802', '**********', 'Logged out.', 1, 8, NULL),
(1213, '2', 'SJ1517060191', '1520272469', 'Logged in.', 1, 11, NULL),
(1214, '2', 'SJ1517060191', '1520272510', 'Logged out.', 1, 11, NULL),
(1215, '2', 'johndoe', '1520272538', 'Logged in.', 1, 1, NULL),
(1216, '2', 'johndoe', '1520272559', 'Logged out.', 1, 1, NULL),
(1217, '2', 'SJ1517060191', '1520272566', 'Logged in.', 1, 11, NULL),
(1218, '2', 'SJ1517060191', '1520272634', 'Logged out.', 1, 11, NULL),
(1219, '2', 'SJ1517060191', '1520272688', 'Logged in.', 1, 11, NULL),
(1220, '2', 'SJ1517060191', '1520272704', 'Logged out.', 1, 11, NULL),
(1221, '2', 'SJ1517060191', '1520272709', 'Logged in.', 1, 11, NULL),
(1222, '2', 'SJ1517060191', '1520272744', 'Logged out.', 1, 11, NULL),
(1223, '2', 'SJ1517060191', '1520272749', 'Logged in.', 1, 11, NULL),
(1224, '2', 'SJ1517060191', '1520272776', 'Logged out.', 1, 11, NULL),
(1225, '2', 'SJ1517060191', '1520272808', 'Logged in.', 1, 11, NULL),
(1226, '2', 'SJ1517060191', '1520272823', 'Logged out.', 1, 11, NULL),
(1227, '2', 'SJ1517060191', '1520272833', 'Logged in.', 1, 11, NULL),
(1228, '1', 'veocalimlim', '1520277266', 'Logged in.', 1, NULL, 1),
(1229, '0', 'seej', '1520298866', 'Logged in.', 1, NULL, 2),
(1230, '0', 'seej', '1520299026', 'Logged out.', 1, NULL, 2),
(1231, '2', 'RB1515560608', '1520299047', 'Logged in.', 1, 4, NULL),
(1232, '0', 'seej', '1520299061', 'Logged in.', 1, NULL, 2),
(1233, '2', 'RB1515560608', '1520299093', 'Logged out.', 1, 4, NULL),
(1234, '2', 'RO1517055802', '1520299102', 'Logged in.', 1, 8, NULL),
(1235, '2', 'RO1517055802', '1520299155', 'Logged out.', 1, 8, NULL),
(1236, '2', 'johndoe', '1520299185', 'Logged in.', 1, 1, NULL),
(1237, '2', 'johndoe', '1520299213', 'Logged out.', 1, 1, NULL),
(1238, '2', 'SJ1517060191', '1520299319', 'Logged in.', 1, 11, NULL),
(1239, '2', 'SJ1517060191', '1520299451', 'Logged in.', 1, 11, NULL),
(1240, '2', 'SJ1517060191', '1520299660', 'Logged in.', 1, 11, NULL),
(1241, '2', 'SJ1517060191', '1520300887', 'Logged out.', 1, 11, NULL),
(1242, '2', 'johndoe', '1520301096', 'Logged in.', 1, 1, NULL),
(1243, '2', 'johndoe', '1520301267', 'Logged out.', 1, 1, NULL),
(1244, '2', 'SM1517056867', '1520301279', 'Logged in.', 1, 9, NULL),
(1245, '2', 'SM1517056867', '1520301341', 'Logged out.', 1, 9, NULL),
(1246, '2', 'SM1517056867', '1520301346', 'Logged in.', 1, 9, NULL),
(1247, '2', 'SM1517056867', '1520301371', 'Logged out.', 1, 9, NULL),
(1248, '2', 'SM1517056867', '1520301377', 'Logged in.', 1, 9, NULL),
(1249, '2', 'SM1517056867', '1520301424', 'Logged out.', 1, 9, NULL),
(1250, '2', 'SM1517056867', '1520301430', 'Logged in.', 1, 9, NULL),
(1251, '2', 'SM1517056867', '**********', 'Logged out.', 1, 9, NULL),
(1252, '2', 'SM1517056867', '1520301458', 'Logged in.', 1, 9, NULL),
(1253, '2', 'SM1517056867', '**********', 'Logged out.', 1, 9, NULL),
(1254, '0', 'seej', '1520325495', 'Logged in.', 1, NULL, 2),
(1255, '0', 'seej', '1520325560', 'Logged in.', 1, NULL, 2),
(1256, '0', 'seej', '1520325608', 'Logged in.', 1, NULL, 2),
(1257, '0', 'seej', '1520325653', 'Logged in.', 1, NULL, 2),
(1258, '0', 'seej', '1520325798', 'Logged in.', 1, NULL, 2),
(1259, '0', 'seej', '1520325865', 'Logged out.', 1, NULL, 2),
(1260, '2', 'johndoe', '1520330147', 'Logged in.', 1, 1, NULL),
(1261, '2', 'johndoe', '**********', 'Logged out.', 1, 1, NULL),
(1262, '0', 'seej', '1520330294', 'Logged in.', 1, NULL, 2),
(1263, '0', 'seej', '1520332045', 'Logged out.', 1, NULL, 2),
(1264, '2', 'johndoe', '1520332101', 'Logged in.', 1, 1, NULL),
(1265, '0', 'seej', '1520332146', 'Logged in.', 1, NULL, 2),
(1266, '2', 'johndoe', '**********', 'Logged out.', 1, 1, NULL),
(1267, '2', 'SJ1517060191', '1520332187', 'Logged in.', 1, 11, NULL),
(1268, '2', 'SJ1517060191', '**********', 'Logged out.', 1, 11, NULL),
(1269, '2', 'RB1515560608', '1520332212', 'Logged in.', 1, 4, NULL),
(1270, '2', 'RB1515560608', '**********', 'Logged out.', 1, 4, NULL),
(1271, '2', 'johndoe', '1520332562', 'Logged in.', 1, 1, NULL),
(1272, '2', 'johndoe', '**********', 'Logged out.', 1, 1, NULL),
(1273, '2', 'RO1517055802', '1520332614', 'Logged in.', 1, 8, NULL),
(1274, '2', 'RO1517055802', '**********', 'Logged out.', 1, 8, NULL),
(1275, '1', 'veocalimlim', '1521982311', 'Logged in.', 1, NULL, 1),
(1276, '1', 'veocalimlim', '1522321873', 'Logged in.', 1, NULL, 1),
(1277, '1', 'veocalimlim', '1522327098', 'Logged in.', 1, NULL, 1),
(1278, '0', 'seej', '1522337126', 'Edited order #60', 1, NULL, 2),
(1279, '0', 'seej', '1522337127', 'Edited order #60''s status to ''delivered''.', 1, NULL, 2),
(1280, '2', 'johndoe', '1522337309', 'Logged in.', 1, 1, NULL),
(1281, '2', 'johndoe', '1522337348', 'Logged out.', 1, 1, NULL),
(1282, '1', 'veocalimlim', '1522337357', 'Logged in.', 1, NULL, 1),
(1283, '0', 'seej', '1522337381', 'Edited order #61', 1, NULL, 2),
(1284, '0', 'seej', '1522337383', 'Edited order #61''s status to ''delivered''.', 1, NULL, 2),
(1285, '0', 'seej', '1522338166', 'Logged out.', 1, NULL, 2),
(1286, '0', 'seej', '1522378958', 'Logged in.', 1, NULL, 2),
(1287, '0', 'seej', '1522379024', 'Edited order #62', 1, NULL, 2),
(1288, '0', 'seej', '1522379025', 'Edited order #62''s status to ''delivered''.', 1, NULL, 2),
(1289, '0', 'seej', '1522380418', 'Edited order #46', 1, NULL, 2),
(1290, '0', 'seej', '1522380419', 'Edited order #46''s status to ''delivered''.', 1, NULL, 2),
(1291, '2', 'RB1515560608', '********15', 'Logged in.', 1, 4, NULL),
(1292, '0', 'seej', '1522406155', 'Logged out.', 1, NULL, 2),
(1293, '2', 'RB1515560608', '1522406158', 'Logged out.', 1, 4, NULL),
(1294, '0', 'seej', '1522406751', 'Logged in.', 1, NULL, 2),
(1295, '2', 'RB1515560608', '1522410909', 'Logged in.', 1, 4, NULL),
(1296, '0', 'seej', '1522416888', 'Logged in.', 1, NULL, 2),
(1297, '0', 'seej', '1522469125', 'Logged in.', 1, NULL, 2),
(1298, '2', 'AL1515664193', '1522469737', 'Logged in.', 1, 7, NULL),
(1299, '2', 'AL1515664193', '1522469751', 'Logged out.', 1, 7, NULL),
(1300, '2', 'SJ1517060191', '1522469756', 'Logged in.', 1, 11, NULL),
(1301, '2', 'SJ1517060191', '1522469762', 'Logged out.', 1, 11, NULL),
(1302, '2', 'SR1520271710', '1522469770', 'Logged in.', 1, 19, NULL),
(1303, '2', 'SR1520271710', '1522469777', 'Logged out.', 1, 19, NULL),
(1304, '2', 'RO1517055802', '1522469782', 'Logged in.', 1, 8, NULL),
(1305, '2', 'RO1517055802', '1522470713', 'Logged out.', 1, 8, NULL),
(1306, '2', 'SR1520271710', '1522470718', 'Logged in.', 1, 19, NULL),
(1307, '2', 'SR1520271710', '1522471143', 'Logged out.', 1, 19, NULL),
(1308, '2', 'AL1515664193', '1522471150', 'Logged in.', 1, 7, NULL),
(1309, '2', 'AL1515664193', '1522471734', 'Logged out.', 1, 7, NULL),
(1310, '2', 'RO1517055802', '1522471738', 'Logged in.', 1, 8, NULL),
(1311, '2', 'RO1517055802', '1522471779', 'Logged out.', 1, 8, NULL),
(1312, '0', 'seej', '1522472821', 'Edited order #63', 1, NULL, 2),
(1313, '0', 'seej', '1522472822', 'Edited order #63''s status to ''delivered''.', 1, NULL, 2),
(1314, '0', 'seej', '1522472842', 'Edited order #64', 1, NULL, 2),
(1315, '0', 'seej', '1522472842', 'Edited order #64''s status to ''delivered''.', 1, NULL, 2),
(1316, '0', 'seej', '1522472864', 'Edited order #65', 1, NULL, 2),
(1317, '0', 'seej', '1522472865', 'Edited order #65''s status to ''delivered''.', 1, NULL, 2),
(1318, '0', 'seej', '1522500800', 'Logged in.', 1, NULL, 2),
(1319, '0', 'seej', '1522673639', 'Logged in.', 1, NULL, 2),
(1320, '0', 'seej', '1522729614', 'Logged in.', 1, NULL, 2),
(1321, '0', 'seej', '1522730094', 'Edited order #66', 1, NULL, 2),
(1322, '0', 'seej', '1522730095', 'Edited order #66''s status to ''delivered''.', 1, NULL, 2),
(1323, '0', 'seej', '1522730109', 'Edited order #67', 1, NULL, 2),
(1324, '0', 'seej', '1522730110', 'Edited order #67''s status to ''delivered''.', 1, NULL, 2),
(1325, '0', 'seej', '1522730439', 'Logged in.', 1, NULL, 2),
(1326, '0', 'seej', '1522730447', 'Logged out.', 1, NULL, 2),
(1327, '2', 'RO1517055802', '1522730455', 'Logged in.', 1, 8, NULL),
(1328, '0', 'seej', '1522730500', 'Edited order #68', 1, NULL, 2),
(1329, '0', 'seej', '1522730500', 'Edited order #68''s status to ''delivered''.', 1, NULL, 2),
(1330, '0', 'seej', '1522731674', 'Edited order #69', 1, NULL, 2),
(1331, '0', 'seej', '1522731708', 'Edited order #69''s status to ''delivered''.', 1, NULL, 2),
(1332, '2', 'RO1517055802', '1522762324', 'Logged out.', 1, 8, NULL),
(1333, '1', 'veocalimlim', '1522762345', 'Logged in.', 1, NULL, 1),
(1334, '1', 'veocalimlim', '1522762374', 'Logged out.', 1, NULL, 1),
(1335, '0', 'seej', '1522814396', 'Logged in.', 1, NULL, 2),
(1336, '1', 'veocalimlim', '1522979563', 'Logged in.', 1, NULL, 1),
(1337, '1', 'veocalimlim', '1522989476', 'Logged in.', 1, NULL, 1),
(1338, '1', 'veocalimlim', '1523201635', 'Logged in.', 1, NULL, 1),
(1339, '1', 'veocalimlim', '1523208737', 'Forecasted sales income for May 18', 1, NULL, 1),
(1340, '1', 'veocalimlim', '1523208856', 'Forecasted sales income for May 18', 1, NULL, 1),
(1341, '1', 'veocalimlim', '1523208947', 'Forecasted sales income for May 18', 1, NULL, 1),
(1342, '1', 'veocalimlim', '1523209239', 'Forecasted sales income for May 18', 1, NULL, 1),
(1343, '1', 'veocalimlim', '1523209277', 'Forecasted sales income for May 18', 1, NULL, 1),
(1344, '1', 'veocalimlim', '1523209357', 'Forecasted sales income for May 18', 1, NULL, 1),
(1345, '1', 'veocalimlim', '1523210871', 'Forecasted sales income for May 18', 1, NULL, 1),
(1346, '1', 'veocalimlim', '1523210902', 'Forecasted sales income for May 18', 1, NULL, 1),
(1347, '1', 'veocalimlim', '1523210927', 'Forecasted sales income for Jun 18', 1, NULL, 1),
(1348, '1', 'veocalimlim', '1523213049', 'Forecasted sales income for Jun 18', 1, NULL, 1),
(1349, '1', 'veocalimlim', '1523213098', 'Forecasted sales income for May 18', 1, NULL, 1),
(1350, '1', 'veocalimlim', '1523213129', 'Forecasted sales income for Jun 18', 1, NULL, 1),
(1351, '1', 'veocalimlim', '1523213151', 'Forecasted sales income for Jul 18', 1, NULL, 1),
(1352, '1', 'veocalimlim', '1523213456', 'Forecasted sales income for Aug 18', 1, NULL, 1),
(1353, '1', 'veocalimlim', '1523244298', 'Logged in.', 1, NULL, 1),
(1354, '1', 'veocalimlim', '1523258546', 'Forecasted sales income for Sep 18', 1, NULL, 1),
(1355, '1', 'veocalimlim', '1523259350', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1356, '1', 'veocalimlim', '1523259837', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1357, '1', 'veocalimlim', '1523259979', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1358, '1', 'veocalimlim', '1523260306', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1359, '1', 'veocalimlim', '1523260597', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1360, '1', 'veocalimlim', '1523260604', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1361, '1', 'veocalimlim', '1523260866', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1362, '1', 'veocalimlim', '1523260876', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1363, '1', 'veocalimlim', '1523261005', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1364, '1', 'veocalimlim', '1523261173', 'Forecasted sales income for Oct 18', 1, NULL, 1),
(1365, '1', 'veocalimlim', '1523261514', 'Forecasted sales income for Nov 18', 1, NULL, 1),
(1366, '1', 'veocalimlim', '1523261522', 'Forecasted sales income for Dec 18', 1, NULL, 1),
(1367, '1', 'veocalimlim', '1523261531', 'Forecasted sales income for Jan 18', 1, NULL, 1),
(1368, '1', 'veocalimlim', '1523261540', 'Forecasted sales income for Feb 18', 1, NULL, 1),
(1369, '1', 'veocalimlim', '1523261547', 'Forecasted sales income for Mar 18', 1, NULL, 1),
(1370, '1', 'veocalimlim', '1523261554', 'Forecasted sales income for Apr 18', 1, NULL, 1),
(1371, '1', 'veocalimlim', '1523261611', 'Forecasted sales income for May 18', 1, NULL, 1),
(1372, '1', 'veocalimlim', '1523265997', 'Forecasted sales income for Dec 18', 1, NULL, 1),
(1373, '1', 'veocalimlim', '1523273850', 'Logged in.', 1, NULL, 1),
(1374, '1', 'veocalimlim', '1523333393', 'Logged in.', 1, NULL, 1),
(1375, '1', 'veocalimlim', '1523335276', 'Edited product #72', 1, NULL, 1),
(1376, '1', 'veocalimlim', '1523418451', 'Logged in.', 1, NULL, 1),
(1377, '1', 'veocalimlim', '1523438054', 'Logged out.', 1, NULL, 1),
(1378, '2', 'johndoe', '1523438062', 'Logged in.', 1, 1, NULL),
(1379, '2', 'johndoe', '1523438263', 'Logged out.', 1, 1, NULL),
(1380, '1', 'veocalimlim', '1523438269', 'Logged in.', 1, NULL, 1),
(1381, '1', 'veocalimlim', '1523504124', 'Logged in.', 1, NULL, 1),
(1382, '2', 'johndoe', '1523519106', 'Logged in.', 1, 1, NULL),
(1383, '2', 'johndoe', '1523524956', 'Logged in.', 1, 1, NULL),
(1384, '2', 'johndoe', '1523525593', 'Logged in.', 1, 1, NULL),
(1385, '2', 'johndoe', '1523525915', 'Logged in.', 1, 1, NULL),
(1386, '1', 'veocalimlim', '1523590345', 'Logged in.', 1, NULL, 1);
INSERT INTO `user_log` (`log_id`, `user_type`, `username`, `date`, `action`, `status`, `customer_id`, `admin_id`) VALUES
(1387, '1', 'veocalimlim', '1523591193', 'Edited product #72', 1, NULL, 1),
(1388, '1', 'veocalimlim', '1523596645', 'Logged in.', 1, NULL, 1),
(1389, '1', 'veocalimlim', '1523616679', 'Logged in.', 1, NULL, 1),
(1390, '1', 'veocalimlim', '1523616698', 'Logged out.', 1, NULL, 1),
(1391, '2', 'johndoe', '1523616705', 'Logged in.', 1, 1, NULL),
(1392, '2', 'johndoe', '1523616720', 'Logged out.', 1, 1, NULL),
(1393, '2', 'RB1515560608', '1523616744', 'Logged in.', 1, 4, NULL),
(1394, '2', 'RB1515560608', '**********', 'Logged out.', 1, 4, NULL),
(1395, '2', 'johndoe', '1523617963', 'Logged in.', 1, 1, NULL),
(1396, '0', 'seej', '1523618092', 'Logged in.', 1, NULL, 2),
(1397, '2', 'johndoe', '**********', 'Logged out.', 1, 1, NULL),
(1398, '2', 'RO1517055802', '1523618179', 'Logged in.', 1, 8, NULL),
(1399, '2', 'RO1517055802', '1523618189', 'Logged out.', 1, 8, NULL),
(1400, '2', 'SM1517056867', '1523618202', 'Logged in.', 1, 9, NULL),
(1401, '2', 'SM1517056867', '1523618210', 'Logged out.', 1, 9, NULL),
(1402, '2', 'johndoe', '1523618220', 'Logged in.', 1, 1, NULL),
(1403, '1', 'veocalimlim', '1523789561', 'Logged in.', 1, NULL, 1),
(1404, '1', 'veocalimlim', '1523789978', 'Logged out.', 1, NULL, 1),
(1405, '0', 'seej', '1523789985', 'Logged in.', 1, NULL, 2),
(1406, '1', 'veocalimlim', '1523803973', 'Logged in.', 1, NULL, 1),
(1407, '1', 'veocalimlim', '1523804067', 'Forecasted sales income for Mar 18', 1, NULL, 1),
(1408, '1', 'veocalimlim', '1523804157', 'Logged out.', 1, NULL, 1),
(1409, '0', 'seej', '1523840795', 'Logged in.', 1, NULL, 2),
(1410, '0', 'seej', '1523852854', 'Logged in.', 1, NULL, 2),
(1411, '0', 'seej', '1523857062', 'Forecasted sales income for Feb 18', 1, NULL, 2),
(1412, '0', 'seej', '1523858816', 'Logged out.', 1, NULL, 2),
(1413, '1', 'veocalimlim', '1523858864', 'Logged in.', 1, NULL, 1),
(1414, '1', 'veocalimlim', '1523858897', 'Logged out.', 1, NULL, 1),
(1415, '0', 'seej', '1525151408', 'Logged in.', 1, NULL, 2),
(1416, '0', 'seej', '1527154791', 'Logged in.', 1, NULL, 2),
(1417, '0', 'seej', '1527154874', 'Logged out.', 1, NULL, 2),
(1418, '0', 'seej', '1527154879', 'Logged in.', 1, NULL, 2),
(1419, '0', 'seej', '1527173078', 'Logged out.', 1, NULL, 2),
(1420, '0', 'seej', '1527173110', 'Logged in.', 1, NULL, 2),
(1421, '0', 'seej', '1527173166', 'Logged in.', 1, NULL, 2),
(1422, '0', 'seej', '1527339853', 'Logged in.', 1, NULL, 2),
(1423, '0', 'seej', '1527633049', 'Logged in.', 1, NULL, 2),
(1424, '0', 'seej', '1527939577', 'Logged in.', 1, NULL, 2),
(1425, '0', 'seej', '1527939885', 'Deleted Sales #101', 1, NULL, 2),
(1426, '0', 'seej', '1527939893', 'Deleted Sales #100', 1, NULL, 2),
(1427, '0', 'seej', '1527939920', 'Deleted Sales #108', 1, NULL, 2),
(1428, '0', 'seej', '1527939939', 'Deleted Sales #106', 1, NULL, 2),
(1429, '0', 'seej', '1527940007', 'Deleted Sales #115', 1, NULL, 2),
(1430, '0', 'seej', '1527940054', 'Deleted Sales #120', 1, NULL, 2),
(1431, '0', 'seej', '1527940068', 'Deleted Sales #122', 1, NULL, 2),
(1432, '0', 'seej', '1527940399', 'Forecasted sales income for Jan 18', 1, NULL, 2),
(1433, '0', 'seej', '1527940416', 'Forecasted sales income for Sep 18', 1, NULL, 2),
(1434, '0', 'seej', '1527940739', 'Forecasted sales income for Jul 18', 1, NULL, 2),
(1435, '0', 'seej', '1527940750', 'Forecasted sales income for Jun 18', 1, NULL, 2),
(1436, '0', 'seej', '1527942866', 'Forecasted sales income for Apr 18', 1, NULL, 2),
(1437, '0', 'seej', '1527943020', 'Logged out.', 1, NULL, 2),
(1438, '0', 'seej', '1527943031', 'Logged in.', 1, NULL, 2),
(1439, '0', 'seej', '1527943061', 'Forecasted sales income for Feb 18', 1, NULL, 2),
(1440, '0', 'seej', '1527943134', 'Logged out.', 1, NULL, 2),
(1441, '0', 'seej', '1527943142', 'Logged in.', 1, NULL, 2),
(1442, '0', 'seej', '1527943153', 'Forecasted sales income for Mar 18', 1, NULL, 2),
(1443, '0', 'seej', '1527943180', 'Logged out.', 1, NULL, 2),
(1444, '0', 'seej', '1527943186', 'Logged in.', 1, NULL, 2),
(1445, '0', 'seej', '1527943193', 'Forecasted sales income for May 18', 1, NULL, 2),
(1446, '0', 'seej', '1527943740', 'Forecasted sales income for May 18', 1, NULL, 2),
(1447, '0', 'seej', '1527944106', 'Logged out.', 1, NULL, 2),
(1448, '0', 'seej', '1527944112', 'Logged in.', 1, NULL, 2),
(1449, '0', 'seej', '1527944121', 'Forecasted sales income for May 18', 1, NULL, 2),
(1450, '0', 'seej', '1527944129', 'Forecasted sales income for Sep 18', 1, NULL, 2),
(1451, '0', 'seej', '1527944202', 'Logged out.', 1, NULL, 2),
(1452, '0', 'seej', '1527944207', 'Logged in.', 1, NULL, 2),
(1453, '0', 'seej', '1527944216', 'Forecasted sales income for Jul 18', 1, NULL, 2),
(1454, '0', 'seej', '1527944237', 'Logged out.', 1, NULL, 2),
(1455, '0', 'seej', '1527944243', 'Logged in.', 1, NULL, 2),
(1456, '0', 'seej', '1527944250', 'Forecasted sales income for Jul 18', 1, NULL, 2),
(1457, '0', 'seej', '1527944310', 'Forecasted sales income for Dec 18', 1, NULL, 2),
(1458, '0', 'seej', '1527944330', 'Forecasted sales income for Aug 18', 1, NULL, 2),
(1459, '0', 'seej', '1527944501', 'Forecasted sales income for Jan 18', 1, NULL, 2),
(1460, '0', 'seej', '1527944509', 'Forecasted sales income for Feb 18', 1, NULL, 2),
(1461, '0', 'seej', '1527944861', 'Logged out.', 1, NULL, 2),
(1462, '0', 'seej', '1527944871', 'Logged in.', 1, NULL, 2),
(1463, '0', 'seej', '1527945393', 'Forecasted sales income for May 18', 1, NULL, 2),
(1464, '0', 'seej', '1527947176', 'Forecasted sales income for Oct 18', 1, NULL, 2),
(1465, '0', 'seej', '1527947209', 'Forecasted sales income for Jan 18', 1, NULL, 2),
(1466, '0', 'seej', '1527947215', 'Forecasted sales income for Feb 18', 1, NULL, 2),
(1467, '0', 'seej', '1527947221', 'Forecasted sales income for Mar 18', 1, NULL, 2),
(1468, '0', 'seej', '1528128563', 'Logged in.', 1, NULL, 2),
(1469, '0', 'seej', '1528286887', 'Logged in.', 1, NULL, 2),
(1470, '0', 'seej', '1528287047', 'Forecasted sales income for Apr 17', 1, NULL, 2),
(1471, '0', 'seej', '1528287068', 'Forecasted sales income for Jul 17', 1, NULL, 2),
(1472, '0', 'seej', '1528287076', 'Forecasted sales income for Nov 17', 1, NULL, 2),
(1473, '0', 'seej', '1528287239', 'Forecasted sales income for May 17', 1, NULL, 2),
(1474, '0', 'seej', '1528287250', 'Forecasted sales income for Dec 17', 1, NULL, 2),
(1475, '0', 'seej', '1528287280', 'Forecasted sales income for Apr 17', 1, NULL, 2),
(1476, '0', 'seej', '1528287343', 'Forecasted sales income for Jun 18', 1, NULL, 2),
(1477, '0', 'seej', '1528287373', 'Forecasted sales income for Jun 18', 1, NULL, 2),
(1478, '0', 'seej', '1528287379', 'Forecasted sales income for Jul 18', 1, NULL, 2),
(1479, '0', 'seej', '1528287387', 'Forecasted sales income for Aug 18', 1, NULL, 2),
(1480, '0', 'seej', '1528287397', 'Forecasted sales income for Nov 18', 1, NULL, 2),
(1481, '0', 'seej', '1528287404', 'Forecasted sales income for Mar 18', 1, NULL, 2),
(1482, '0', 'seej', '1528287410', 'Forecasted sales income for Apr 18', 1, NULL, 2),
(1483, '0', 'seej', '1528287447', 'Forecasted sales income for Apr 18', 1, NULL, 2),
(1484, '0', 'seej', '1528287458', 'Forecasted sales income for Aug 18', 1, NULL, 2),
(1485, '0', 'seej', '1528287466', 'Forecasted sales income for Dec 18', 1, NULL, 2),
(1486, '0', 'seej', '1528288034', 'Forecasted sales income for Dec 18', 1, NULL, 2),
(1487, '0', 'seej', '1528288092', 'Forecasted sales income for Apr 18', 1, NULL, 2),
(1488, '0', 'seej', '1528288138', 'Forecasted sales income for Mar 18', 1, NULL, 2),
(1489, '2', 'RO1517055802', '1528296691', 'Logged in.', 1, 8, NULL),
(1490, '0', 'seej', '1528296830', 'Cancelled order #70', 1, NULL, 2),
(1491, '0', 'seej', '1528297085', 'Cancelled order #70', 1, NULL, 2),
(1492, '0', 'seej', '1528297535', 'Cancelled order #70', 1, NULL, 2),
(1493, '0', 'seej', '1528298008', 'Edited order #70', 1, NULL, 2),
(1494, '0', 'seej', '1528298267', 'Logged in.', 1, NULL, 2),
(1495, '0', 'seej', '1528298281', 'Edited order #70', 1, NULL, 2),
(1496, '0', 'seej', '1528298422', 'Edited order #70', 1, NULL, 2),
(1497, '0', 'seej', '1528298456', 'Cancelled order #70', 1, NULL, 2),
(1498, '0', 'seej', '1528298538', 'Edited order #70', 1, NULL, 2),
(1499, '0', 'seej', '1528298720', 'Edited order #70', 1, NULL, 2),
(1500, '0', 'seej', '1528298760', 'Edited order #70', 1, NULL, 2),
(1501, '0', 'seej', '**********', 'Edited order #70', 1, NULL, 2),
(1502, '0', 'seej', '1528298813', 'Edited order #70''s status to ''delivered''.', 1, NULL, 2),
(1503, '0', 'seej', '1528298917', 'Deleted Sales #149', 1, NULL, 2),
(1504, '0', 'seej', '1528298968', 'Edited product #72', 1, NULL, 2),
(1505, '0', 'seej', '1528298975', 'Deleted product #72', 1, NULL, 2),
(1506, '0', 'seej', '1528298984', 'Restored product #72', 1, NULL, 2),
(1507, '0', 'seej', '**********', 'Edited product #92', 1, NULL, 2),
(1508, '0', 'seej', '1528299470', 'Added product: asfdgf', 1, NULL, 2),
(1509, '0', 'seej', '1528299652', 'Added product: ghvjknlm', 1, NULL, 2),
(1510, '0', 'seej', '1528299746', 'Deleted product #107', 1, NULL, 2),
(1511, '0', 'seej', '1528299760', 'Deleted product #108', 1, NULL, 2),
(1512, '0', 'seej', '1528299826', 'Restored product #107', 1, NULL, 2),
(1513, '0', 'seej', '1528299913', 'Restored product #86', 1, NULL, 2),
(1514, '0', 'seej', '**********', 'Edited product #107', 1, NULL, 2),
(1515, '0', 'seej', '**********', 'Deleted product #107', 1, NULL, 2),
(1516, '0', 'seej', '**********', 'Logged out.', 1, NULL, 2),
(1517, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2),
(1518, '0', 'seej', '**********', 'Forecasted sales income for Jun 18', 1, NULL, 2),
(1519, '0', 'seej', '**********', 'Forecasted sales income for May 18', 1, NULL, 2),
(1520, '0', 'seej', '**********', 'Added account: ghjkl, ghjkl;', 1, NULL, NULL),
(1521, '0', 'seej', '**********', 'Added account: gfhjkl, gfhjkl', 1, NULL, NULL),
(1522, '0', 'seej', '**********', 'Deleted account #', 1, NULL, 2),
(1523, '0', 'seej', '**********', 'Deleted account #', 1, NULL, 2),
(1524, '0', 'seej', '**********', 'Reactivated account #15', 1, NULL, 2),
(1525, '0', 'seej', '**********', 'Deleted account #', 1, NULL, 2),
(1526, '0', 'seej', '**********', 'Reactivated account #15', 1, NULL, 2),
(1527, '0', 'seej', '**********', 'Deleted account #', 1, NULL, 2),
(1528, '0', 'seej', '**********', 'Logged in.', 1, NULL, 2);

-- --------------------------------------------------------

--
-- Table structure for table `wishlist`
--

CREATE TABLE `wishlist` (
  `wishlist_id` int(12) UNSIGNED NOT NULL,
  `customer_id` int(12) UNSIGNED NOT NULL,
  `product_id` int(12) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `wishlist`
--

INSERT INTO `wishlist` (`wishlist_id`, `customer_id`, `product_id`) VALUES
(8, 1, 78),
(9, 1, 77),
(10, 1, 72),
(11, 1, 75),
(12, 1, 85);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin`
--
ALTER TABLE `admin`
  ADD PRIMARY KEY (`admin_id`);

--
-- Indexes for table `audit_trail`
--
ALTER TABLE `audit_trail`
  ADD PRIMARY KEY (`at_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `audit_trail_ibfk_3` (`product_id`);

--
-- Indexes for table `brand`
--
ALTER TABLE `brand`
  ADD PRIMARY KEY (`brand_id`);

--
-- Indexes for table `category`
--
ALTER TABLE `category`
  ADD PRIMARY KEY (`category_id`);

--
-- Indexes for table `content`
--
ALTER TABLE `content`
  ADD PRIMARY KEY (`content_id`);

--
-- Indexes for table `customer`
--
ALTER TABLE `customer`
  ADD PRIMARY KEY (`customer_id`);

--
-- Indexes for table `feedback`
--
ALTER TABLE `feedback`
  ADD PRIMARY KEY (`feedback_id`),
  ADD KEY `product_id` (`product_id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `forecast`
--
ALTER TABLE `forecast`
  ADD PRIMARY KEY (`forecast_id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`order_id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `shipper_id` (`shipper_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`orderitems_id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `product_id` (`product_id`);

--
-- Indexes for table `order_status`
--
ALTER TABLE `order_status`
  ADD PRIMARY KEY (`orderstatus_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `order_id` (`order_id`);

--
-- Indexes for table `payment`
--
ALTER TABLE `payment`
  ADD PRIMARY KEY (`payment_id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `product`
--
ALTER TABLE `product`
  ADD PRIMARY KEY (`product_id`),
  ADD KEY `supplier_id` (`supplier_id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `brand_id` (`brand_id`);

--
-- Indexes for table `promo`
--
ALTER TABLE `promo`
  ADD PRIMARY KEY (`promo_id`);

--
-- Indexes for table `sales`
--
ALTER TABLE `sales`
  ADD PRIMARY KEY (`sales_id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `order_id` (`order_id`);

--
-- Indexes for table `shipper`
--
ALTER TABLE `shipper`
  ADD PRIMARY KEY (`shipper_id`);

--
-- Indexes for table `supplier`
--
ALTER TABLE `supplier`
  ADD PRIMARY KEY (`supplier_id`);

--
-- Indexes for table `user_log`
--
ALTER TABLE `user_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `admin_id` (`admin_id`);

--
-- Indexes for table `wishlist`
--
ALTER TABLE `wishlist`
  ADD PRIMARY KEY (`wishlist_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `product_id` (`product_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin`
--
ALTER TABLE `admin`
  MODIFY `admin_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;
--
-- AUTO_INCREMENT for table `audit_trail`
--
ALTER TABLE `audit_trail`
  MODIFY `at_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=580;
--
-- AUTO_INCREMENT for table `brand`
--
ALTER TABLE `brand`
  MODIFY `brand_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=621;
--
-- AUTO_INCREMENT for table `category`
--
ALTER TABLE `category`
  MODIFY `category_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=707;
--
-- AUTO_INCREMENT for table `content`
--
ALTER TABLE `content`
  MODIFY `content_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
--
-- AUTO_INCREMENT for table `customer`
--
ALTER TABLE `customer`
  MODIFY `customer_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;
--
-- AUTO_INCREMENT for table `feedback`
--
ALTER TABLE `feedback`
  MODIFY `feedback_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=46;
--
-- AUTO_INCREMENT for table `forecast`
--
ALTER TABLE `forecast`
  MODIFY `forecast_id` int(12) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;
--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `order_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=71;
--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `orderitems_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=93;
--
-- AUTO_INCREMENT for table `order_status`
--
ALTER TABLE `order_status`
  MODIFY `orderstatus_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;
--
-- AUTO_INCREMENT for table `payment`
--
ALTER TABLE `payment`
  MODIFY `payment_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;
--
-- AUTO_INCREMENT for table `product`
--
ALTER TABLE `product`
  MODIFY `product_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=109;
--
-- AUTO_INCREMENT for table `promo`
--
ALTER TABLE `promo`
  MODIFY `promo_id` int(12) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;
--
-- AUTO_INCREMENT for table `sales`
--
ALTER TABLE `sales`
  MODIFY `sales_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=154;
--
-- AUTO_INCREMENT for table `shipper`
--
ALTER TABLE `shipper`
  MODIFY `shipper_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=906;
--
-- AUTO_INCREMENT for table `supplier`
--
ALTER TABLE `supplier`
  MODIFY `supplier_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=805;
--
-- AUTO_INCREMENT for table `user_log`
--
ALTER TABLE `user_log`
  MODIFY `log_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1529;
--
-- AUTO_INCREMENT for table `wishlist`
--
ALTER TABLE `wishlist`
  MODIFY `wishlist_id` int(12) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;
--
-- Constraints for dumped tables
--

--
-- Constraints for table `audit_trail`
--
ALTER TABLE `audit_trail`
  ADD CONSTRAINT `audit_trail_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`customer_id`),
  ADD CONSTRAINT `audit_trail_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`),
  ADD CONSTRAINT `audit_trail_ibfk_3` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`);

--
-- Constraints for table `feedback`
--
ALTER TABLE `feedback`
  ADD CONSTRAINT `feedback_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`customer_id`),
  ADD CONSTRAINT `feedback_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`);

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`admin_id`),
  ADD CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`customer_id`),
  ADD CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`shipper_id`) REFERENCES `shipper` (`shipper_id`);

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`),
  ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`);

--
-- Constraints for table `order_status`
--
ALTER TABLE `order_status`
  ADD CONSTRAINT `order_status_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`customer_id`),
  ADD CONSTRAINT `order_status_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`);

--
-- Constraints for table `payment`
--
ALTER TABLE `payment`
  ADD CONSTRAINT `payment_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`customer_id`);

--
-- Constraints for table `product`
--
ALTER TABLE `product`
  ADD CONSTRAINT `product_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`admin_id`),
  ADD CONSTRAINT `product_ibfk_3` FOREIGN KEY (`supplier_id`) REFERENCES `supplier` (`supplier_id`),
  ADD CONSTRAINT `product_ibfk_4` FOREIGN KEY (`category_id`) REFERENCES `category` (`category_id`),
  ADD CONSTRAINT `product_ibfk_5` FOREIGN KEY (`brand_id`) REFERENCES `brand` (`brand_id`);

--
-- Constraints for table `sales`
--
ALTER TABLE `sales`
  ADD CONSTRAINT `sales_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`admin_id`),
  ADD CONSTRAINT `sales_ibfk_2` FOREIGN KEY (`order_id`) REFERENCES `orders` (`order_id`);

--
-- Constraints for table `user_log`
--
ALTER TABLE `user_log`
  ADD CONSTRAINT `user_log_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`customer_id`),
  ADD CONSTRAINT `user_log_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`admin_id`);

--
-- Constraints for table `wishlist`
--
ALTER TABLE `wishlist`
  ADD CONSTRAINT `wishlist_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer` (`customer_id`),
  ADD CONSTRAINT `wishlist_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`);

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
