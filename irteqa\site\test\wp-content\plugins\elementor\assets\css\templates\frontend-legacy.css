.elementor-bc-flex-widget .elementor-section-content-top > .elementor-container > .elementor-row > .elementor-column > .elementor-column-wrap {
  align-items: flex-start;
}
.elementor-bc-flex-widget .elementor-section-content-middle > .elementor-container > .elementor-row > .elementor-column > .elementor-column-wrap {
  align-items: center;
}
.elementor-bc-flex-widget .elementor-section-content-bottom > .elementor-container > .elementor-row > .elementor-column > .elementor-column-wrap {
  align-items: flex-end;
}

.elementor-column-gap-narrow > .elementor-row > .elementor-column > .elementor-element-populated > .elementor-widget-wrap {
  padding: 5px;
}
.elementor-column-gap-default > .elementor-row > .elementor-column > .elementor-element-populated > .elementor-widget-wrap {
  padding: 10px;
}
.elementor-column-gap-extended > .elementor-row > .elementor-column > .elementor-element-populated > .elementor-widget-wrap {
  padding: 15px;
}
.elementor-column-gap-wide > .elementor-row > .elementor-column > .elementor-element-populated > .elementor-widget-wrap {
  padding: 20px;
}
.elementor-column-gap-wider > .elementor-row > .elementor-column > .elementor-element-populated > .elementor-widget-wrap {
  padding: 30px;
}

@media (min-width: ELEMENTOR_SCREEN_WIDESCREEN_MIN) {
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(1) {
    order: 10;
  }
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(2) {
    order: 9;
  }
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(3) {
    order: 8;
  }
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(4) {
    order: 7;
  }
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(5) {
    order: 6;
  }
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(6) {
    order: 5;
  }
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(7) {
    order: 4;
  }
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(8) {
    order: 3;
  }
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(9) {
    order: 2;
  }
  .elementor-reverse-widescreen > .elementor-container > .elementor-row > :nth-child(10) {
    order: 1;
  }
}
@media (min-width: ELEMENTOR_SCREEN_TABLET_NEXT) and (max-width: ELEMENTOR_SCREEN_LAPTOP_MAX) {
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(1) {
    order: 10;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(2) {
    order: 9;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(3) {
    order: 8;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(4) {
    order: 7;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(5) {
    order: 6;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(6) {
    order: 5;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(7) {
    order: 4;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(8) {
    order: 3;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(9) {
    order: 2;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(10) {
    order: 1;
  }
}
@media (min-width: ELEMENTOR_SCREEN_TABLET_EXTRA_NEXT) and (max-width: ELEMENTOR_SCREEN_LAPTOP_MAX) {
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(1) {
    order: 10;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(2) {
    order: 9;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(3) {
    order: 8;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(4) {
    order: 7;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(5) {
    order: 6;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(6) {
    order: 5;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(7) {
    order: 4;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(8) {
    order: 3;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(9) {
    order: 2;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(10) {
    order: 1;
  }
}
@media (min-width: ELEMENTOR_SCREEN_TABLET_NEXT) and (max-width: ELEMENTOR_SCREEN_TABLET_EXTRA_MAX) {
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(1) {
    order: initial;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(2) {
    order: initial;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(3) {
    order: initial;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(4) {
    order: initial;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(5) {
    order: initial;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(6) {
    order: initial;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(7) {
    order: initial;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(8) {
    order: initial;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(9) {
    order: initial;
  }
  .elementor-reverse-laptop > .elementor-container > .elementor-row > :nth-child(10) {
    order: initial;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(1) {
    order: 10;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(2) {
    order: 9;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(3) {
    order: 8;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(4) {
    order: 7;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(5) {
    order: 6;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(6) {
    order: 5;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(7) {
    order: 4;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(8) {
    order: 3;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(9) {
    order: 2;
  }
  .elementor-reverse-tablet_extra > .elementor-container > .elementor-row > :nth-child(10) {
    order: 1;
  }
}
@media (min-width: ELEMENTOR_SCREEN_MOBILE_NEXT) and (max-width: ELEMENTOR_SCREEN_TABLET_MAX) {
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(1) {
    order: 10;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(2) {
    order: 9;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(3) {
    order: 8;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(4) {
    order: 7;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(5) {
    order: 6;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(6) {
    order: 5;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(7) {
    order: 4;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(8) {
    order: 3;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(9) {
    order: 2;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(10) {
    order: 1;
  }
}
@media (min-width: ELEMENTOR_SCREEN_MOBILE_EXTRA_NEXT) and (max-width: ELEMENTOR_SCREEN_TABLET_MAX) {
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(1) {
    order: 10;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(2) {
    order: 9;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(3) {
    order: 8;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(4) {
    order: 7;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(5) {
    order: 6;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(6) {
    order: 5;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(7) {
    order: 4;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(8) {
    order: 3;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(9) {
    order: 2;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(10) {
    order: 1;
  }
}
@media (min-width: ELEMENTOR_SCREEN_MOBILE_NEXT) and (max-width: ELEMENTOR_SCREEN_MOBILE_EXTRA_MAX) {
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(1) {
    order: initial;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(2) {
    order: initial;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(3) {
    order: initial;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(4) {
    order: initial;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(5) {
    order: initial;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(6) {
    order: initial;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(7) {
    order: initial;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(8) {
    order: initial;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(9) {
    order: initial;
  }
  .elementor-reverse-tablet > .elementor-container > .elementor-row > :nth-child(10) {
    order: initial;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(1) {
    order: 10;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(2) {
    order: 9;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(3) {
    order: 8;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(4) {
    order: 7;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(5) {
    order: 6;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(6) {
    order: 5;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(7) {
    order: 4;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(8) {
    order: 3;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(9) {
    order: 2;
  }
  .elementor-reverse-mobile_extra > .elementor-container > .elementor-row > :nth-child(10) {
    order: 1;
  }
}
@media (max-width: ELEMENTOR_SCREEN_MOBILE_MAX) {
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(1) {
    order: 10;
  }
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(2) {
    order: 9;
  }
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(3) {
    order: 8;
  }
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(4) {
    order: 7;
  }
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(5) {
    order: 6;
  }
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(6) {
    order: 5;
  }
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(7) {
    order: 4;
  }
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(8) {
    order: 3;
  }
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(9) {
    order: 2;
  }
  .elementor-reverse-mobile > .elementor-container > .elementor-row > :nth-child(10) {
    order: 1;
  }
  .elementor-column {
    width: 100%;
  }
}

/*# sourceMappingURL=frontend-legacy.css.map */