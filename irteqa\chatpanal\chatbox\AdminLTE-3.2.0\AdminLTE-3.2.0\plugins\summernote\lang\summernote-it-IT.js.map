{"version": 3, "file": "lang/summernote-it-IT.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,oBADF;AAEJC,QAAAA,MAAM,EAAE,kBAFJ;AAGJC,QAAAA,SAAS,EAAE,oBAHP;AAIJC,QAAAA,KAAK,EAAE,oCAJH;AAKJC,QAAAA,MAAM,EAAE,8BALJ;AAMJC,QAAAA,IAAI,EAAE,eANF;AAOJC,QAAAA,aAAa,EAAE,eAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,UADF;AAELC,QAAAA,MAAM,EAAE,oBAFH;AAGLC,QAAAA,UAAU,EAAE,sBAHP;AAILC,QAAAA,UAAU,EAAE,qBAJP;AAKLC,QAAAA,aAAa,EAAE,qBALV;AAMLC,QAAAA,SAAS,EAAE,sBANN;AAOLC,QAAAA,UAAU,EAAE,oBAPP;AAQLC,QAAAA,SAAS,EAAE,uBARN;AASLC,QAAAA,YAAY,EAAE,gBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,kBAXX;AAYLC,QAAAA,SAAS,EAAE,aAZN;AAaLC,QAAAA,aAAa,EAAE,2BAbV;AAcLC,QAAAA,SAAS,EAAE,oBAdN;AAeLC,QAAAA,eAAe,EAAE,sBAfZ;AAgBLC,QAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,6BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,oBAlBA;AAmBLC,QAAAA,MAAM,EAAE,kBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,0BAFN;AAGLpB,QAAAA,MAAM,EAAE,iBAHH;AAILgB,QAAAA,GAAG,EAAE,eAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,cADF;AAEJtB,QAAAA,MAAM,EAAE,wBAFJ;AAGJuB,QAAAA,MAAM,EAAE,sBAHJ;AAIJC,QAAAA,IAAI,EAAE,uBAJF;AAKJC,QAAAA,aAAa,EAAE,wBALX;AAMJT,QAAAA,GAAG,EAAE,sBAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,IAFE;AAGLC,QAAAA,UAAU,EAAE,WAHP;AAILC,QAAAA,GAAG,EAAE,QAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,qBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,0BAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,WADF;AAETC,QAAAA,OAAO,EAAE,kCAFA;AAGTC,QAAAA,MAAM,EAAE,+BAHC;AAITC,QAAAA,IAAI,EAAE,oBAJG;AAKTC,QAAAA,MAAM,EAAE,QALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,0BADH;AAELC,QAAAA,IAAI,EAAE,cAFD;AAGLC,QAAAA,UAAU,EAAE,kBAHP;AAILC,QAAAA,UAAU,EAAE,QAJP;AAKLC,QAAAA,WAAW,EAAE,aALR;AAMLC,QAAAA,cAAc,EAAE,aANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,yBADH;AAERC,QAAAA,KAAK,EAAE,QAFC;AAGRC,QAAAA,cAAc,EAAE,qBAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,yBALb;AAMRC,QAAAA,aAAa,EAAE,OANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,kBADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,yBAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,kBANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,uBART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,gBAdX;AAeJ,+BAAuB,uBAfnB;AAgBJ,6BAAqB,qBAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,sCApBR;AAqBJ,oBAAY,sCArBR;AAsBJ,oBAAY,sCAtBR;AAuBJ,oBAAY,sCAvBR;AAwBJ,oBAAY,sCAxBR;AAyBJ,oBAAY,sCAzBR;AA0BJ,gCAAwB,wBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,oBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-it-IT.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'it-IT': {\n      font: {\n        bold: 'Testo in grassetto',\n        italic: 'Testo in corsivo',\n        underline: 'Testo sottolineato',\n        clear: 'Elimina la formattazione del testo',\n        height: 'Altezza della linea di testo',\n        name: 'Famiglia Font',\n        strikethrough: 'Testo barrato',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Dimensione del carattere',\n      },\n      image: {\n        image: 'Immagine',\n        insert: 'Inserisci Immagine',\n        resizeFull: 'Dimensioni originali',\n        resizeHalf: 'Ridimensiona al 50%',\n        resizeQuarter: 'Ridimensiona al 25%',\n        floatLeft: 'Posiziona a sinistra',\n        floatRight: 'Posiziona a destra',\n        floatNone: 'Nessun posizionamento',\n        shapeRounded: 'Shape: Rounded',\n        shapeCircle: 'Shape: Circle',\n        shapeThumbnail: 'Shape: Thumbnail',\n        shapeNone: 'Shape: None',\n        dragImageHere: 'Trascina qui un\\'immagine',\n        dropImage: 'Drop image or Text',\n        selectFromFiles: 'Scegli dai Documenti',\n        maximumFileSize: 'Maximum file size',\n        maximumFileSizeError: 'Maximum file size exceeded.',\n        url: 'URL dell\\'immagine',\n        remove: 'Rimuovi immagine',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Collegamento ad un Video',\n        insert: 'Inserisci Video',\n        url: 'URL del Video',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion o Youku)',\n      },\n      link: {\n        link: 'Collegamento',\n        insert: 'Inserisci Collegamento',\n        unlink: 'Elimina collegamento',\n        edit: 'Modifica collegamento',\n        textToDisplay: 'Testo del collegamento',\n        url: 'URL del collegamento',\n        openInNewWindow: 'Apri in una nuova finestra',\n      },\n      table: {\n        table: 'Tabella',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Inserisce una linea di separazione',\n      },\n      style: {\n        style: 'Stili',\n        p: 'pe',\n        blockquote: 'Citazione',\n        pre: 'Codice',\n        h1: 'Titolo 1',\n        h2: 'Titolo 2',\n        h3: 'Titolo 3',\n        h4: 'Titolo 4',\n        h5: 'Titolo 5',\n        h6: 'Titolo 6',\n      },\n      lists: {\n        unordered: 'Elenco non ordinato',\n        ordered: 'Elenco ordinato',\n      },\n      options: {\n        help: 'Aiuto',\n        fullscreen: 'Modalità a tutto schermo',\n        codeview: 'Visualizza codice',\n      },\n      paragraph: {\n        paragraph: 'Paragrafo',\n        outdent: 'Diminuisce il livello di rientro',\n        indent: 'Aumenta il livello di rientro',\n        left: 'Allinea a sinistra',\n        center: 'Centra',\n        right: 'Allinea a destra',\n        justify: 'Giustifica (allinea a destra e sinistra)',\n      },\n      color: {\n        recent: 'Ultimo colore utilizzato',\n        more: 'Altri colori',\n        background: 'Colore di sfondo',\n        foreground: 'Colore',\n        transparent: 'Trasparente',\n        setTransparent: 'Trasparente',\n        reset: 'Reimposta',\n        resetToDefault: 'Reimposta i colori',\n      },\n      shortcut: {\n        shortcuts: 'Scorciatoie da tastiera',\n        close: 'Chiudi',\n        textFormatting: 'Formattazione testo',\n        action: 'Azioni',\n        paragraphFormatting: 'Formattazione paragrafo',\n        documentStyle: 'Stili',\n        extraKeys: 'Extra keys',\n      },\n      help: {\n        'insertParagraph': 'Insert Paragraph',\n        'undo': 'Undoes the last command',\n        'redo': 'Redoes the last command',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Set a bold style',\n        'italic': 'Set a italic style',\n        'underline': 'Set a underline style',\n        'strikethrough': 'Set a strikethrough style',\n        'removeFormat': 'Clean a style',\n        'justifyLeft': 'Set left align',\n        'justifyCenter': 'Set center align',\n        'justifyRight': 'Set right align',\n        'justifyFull': 'Set full align',\n        'insertUnorderedList': 'Toggle unordered list',\n        'insertOrderedList': 'Toggle ordered list',\n        'outdent': 'Outdent on current paragraph',\n        'indent': 'Indent on current paragraph',\n        'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n        'formatH1': 'Change current block\\'s format as H1',\n        'formatH2': 'Change current block\\'s format as H2',\n        'formatH3': 'Change current block\\'s format as H3',\n        'formatH4': 'Change current block\\'s format as H4',\n        'formatH5': 'Change current block\\'s format as H5',\n        'formatH6': 'Change current block\\'s format as H6',\n        'insertHorizontalRule': 'Insert horizontal rule',\n        'linkDialog.show': 'Show Link Dialog',\n      },\n      history: {\n        undo: 'Annulla',\n        redo: 'Ripristina',\n      },\n      specialChar: {\n        specialChar: 'SPECIAL CHARACTERS',\n        select: 'Select Special characters',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}