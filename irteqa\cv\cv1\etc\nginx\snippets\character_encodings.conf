# ----------------------------------------------------------------------
# | Character encodings                                                |
# ----------------------------------------------------------------------

# Serve all resources labeled as `text/html` or `text/plain` with the media type
# `charset` parameter set to `UTF-8`.
# https://nginx.org/en/docs/http/ngx_http_charset_module.html#charset

charset utf-8;

# - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

# Update charset_types to match updated mime.types.
# `text/html` is always included by charset module.
# Default: text/html text/xml text/plain text/vnd.wap.wml application/javascript application/rss+xml
#
# https://nginx.org/en/docs/http/ngx_http_charset_module.html#charset_types

charset_types
  text/xml
  text/css
  text/plain
  text/vnd.wap.wml
  text/javascript
  text/markdown
  text/calendar
  text/x-component
  text/vcard
  text/cache-manifest
  text/vtt
  application/json
  application/manifest+json;
