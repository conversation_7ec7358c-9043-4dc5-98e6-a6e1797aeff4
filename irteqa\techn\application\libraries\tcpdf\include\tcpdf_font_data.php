<?php
//============================================================+
// File name   : tcpdf_font_data.php
// Version     : 1.0.001
// Begin       : 2008-01-01
// Last Update : 2013-04-01
// Author      : <PERSON>.com LTD - www.tecnick.com - <EMAIL>
// License     : GNU-LGPL v3 (http://www.gnu.org/copyleft/lesser.html)
// -------------------------------------------------------------------
// Copyright (C) 2008-2013 Nicola <PERSON> - <PERSON>.com LTD
//
// This file is part of TCPDF software library.
//
// TCPDF is free software: you can redistribute it and/or modify it
// under the terms of the GNU Lesser General Public License as
// published by the Free Software Foundation, either version 3 of the
// License, or (at your option) any later version.
//
// TCPDF is distributed in the hope that it will be useful, but
// WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
// See the GNU Lesser General Public License for more details.
//
// You should have received a copy of the GNU Lesser General Public License
// along with TCPDF.  If not, see <http://www.gnu.org/licenses/>.
//
// See LICENSE.TXT file for more information.
// -------------------------------------------------------------------
//
// Description : Unicode data and encoding maps for TCPDF.
//
//============================================================+

/**
 * @file
 * Unicode data and encoding maps for TCPDF.
 * <AUTHOR> Asuni
 * @package com.tecnick.tcpdf
 */

/**
 * @class TCPDF_FONT_DATA
 * Unicode data and encoding maps for TCPDF.
 * @package com.tecnick.tcpdf
 * @version 1.0.001
 * <AUTHOR> Asuni - <EMAIL>
 */
class TCPDF_FONT_DATA {

/**
 * Unicode code for Left-to-Right Mark.
 * @public
 */
public static $uni_LRM = 8206;

/**
 * Unicode code for Right-to-Left Mark.
 * @public
 */
public static $uni_RLM = 8207;

/**
 * Unicode code for Left-to-Right Embedding.
 * @public
 */
public static $uni_LRE = 8234;

/**
 * Unicode code for Right-to-Left Embedding.
 * @public
 */
public static $uni_RLE = 8235;

/**
 * Unicode code for Pop Directional Format.
 * @public
 */
public static $uni_PDF = 8236;

/**
 * Unicode code for Left-to-Right Override.
 * @public
 */
public static $uni_LRO = 8237;

/**
 * Unicode code for Right-to-Left Override.
 * @public
 */
public static $uni_RLO = 8238;

/**
 * Pattern to test RTL (Righ-To-Left) strings using regular expressions.
 * @public
 */
public static $uni_RE_PATTERN_RTL = "/(
	  \xD6\xBE                                             # R
	| \xD7[\x80\x83\x86\x90-\xAA\xB0-\xB4]                 # R
	| \xDF[\x80-\xAA\xB4\xB5\xBA]                          # R
	| \xE2\x80\x8F                                         # R
	| \xEF\xAC[\x9D\x9F\xA0-\xA8\xAA-\xB6\xB8-\xBC\xBE]    # R
	| \xEF\xAD[\x80\x81\x83\x84\x86-\x8F]                  # R
	| \xF0\x90\xA0[\x80-\x85\x88\x8A-\xB5\xB7\xB8\xBC\xBF] # R
	| \xF0\x90\xA4[\x80-\x99]                              # R
	| \xF0\x90\xA8[\x80\x90-\x93\x95-\x97\x99-\xB3]        # R
	| \xF0\x90\xA9[\x80-\x87\x90-\x98]                     # R
	| \xE2\x80[\xAB\xAE]                                   # RLE & RLO
	)/x";

/**
 * Pattern to test Arabic strings using regular expressions. Source: http://www.w3.org/International/questions/qa-forms-utf-8
 * @public
 */
public static $uni_RE_PATTERN_ARABIC = "/(
		  \xD8[\x80-\x83\x8B\x8D\x9B\x9E\x9F\xA1-\xBA]  # AL
		| \xD9[\x80-\x8A\xAD-\xAF\xB1-\xBF]             # AL
		| \xDA[\x80-\xBF]                               # AL
		| \xDB[\x80-\x95\x9D\xA5\xA6\xAE\xAF\xBA-\xBF]  # AL
		| \xDC[\x80-\x8D\x90\x92-\xAF]                  # AL
		| \xDD[\x8D-\xAD]                               # AL
		| \xDE[\x80-\xA5\xB1]                           # AL
		| \xEF\xAD[\x90-\xBF]                           # AL
		| \xEF\xAE[\x80-\xB1]                           # AL
		| \xEF\xAF[\x93-\xBF]                           # AL
		| \xEF[\xB0-\xB3][\x80-\xBF]                    # AL
		| \xEF\xB4[\x80-\xBD]                           # AL
		| \xEF\xB5[\x90-\xBF]                           # AL
		| \xEF\xB6[\x80-\x8F\x92-\xBF]                  # AL
		| \xEF\xB7[\x80-\x87\xB0-\xBC]                  # AL
		| \xEF\xB9[\xB0-\xB4\xB6-\xBF]                  # AL
		| \xEF\xBA[\x80-\xBF]                           # AL
		| \xEF\xBB[\x80-\xBC]                           # AL
		| \xD9[\xA0-\xA9\xAB\xAC]                       # AN
		)/x";

/**
 * Array of Unicode types.
 * @public
 */
public static $uni_type = array(
0=>'BN',
1=>'BN',
2=>'BN',
3=>'BN',
4=>'BN',
5=>'BN',
6=>'BN',
7=>'BN',
8=>'BN',
9=>'S',
10=>'B',
11=>'S',
12=>'WS',
13=>'B',
14=>'BN',
15=>'BN',
16=>'BN',
17=>'BN',
18=>'BN',
19=>'BN',
20=>'BN',
21=>'BN',
22=>'BN',
23=>'BN',
24=>'BN',
25=>'BN',
26=>'BN',
27=>'BN',
28=>'B',
29=>'B',
30=>'B',
31=>'S',
32=>'WS',
33=>'ON',
34=>'ON',
35=>'ET',
36=>'ET',
37=>'ET',
38=>'ON',
39=>'ON',
40=>'ON',
41=>'ON',
42=>'ON',
43=>'ES',
44=>'CS',
45=>'ES',
46=>'CS',
47=>'CS',
48=>'EN',
49=>'EN',
50=>'EN',
51=>'EN',
52=>'EN',
53=>'EN',
54=>'EN',
55=>'EN',
56=>'EN',
57=>'EN',
58=>'CS',
59=>'ON',
60=>'ON',
61=>'ON',
62=>'ON',
63=>'ON',
64=>'ON',
65=>'L',
66=>'L',
67=>'L',
68=>'L',
69=>'L',
70=>'L',
71=>'L',
72=>'L',
73=>'L',
74=>'L',
75=>'L',
76=>'L',
77=>'L',
78=>'L',
79=>'L',
80=>'L',
81=>'L',
82=>'L',
83=>'L',
84=>'L',
85=>'L',
86=>'L',
87=>'L',
88=>'L',
89=>'L',
90=>'L',
91=>'ON',
92=>'ON',
93=>'ON',
94=>'ON',
95=>'ON',
96=>'ON',
97=>'L',
98=>'L',
99=>'L',
100=>'L',
101=>'L',
102=>'L',
103=>'L',
104=>'L',
105=>'L',
106=>'L',
107=>'L',
108=>'L',
109=>'L',
110=>'L',
111=>'L',
112=>'L',
113=>'L',
114=>'L',
115=>'L',
116=>'L',
117=>'L',
118=>'L',
119=>'L',
120=>'L',
121=>'L',
122=>'L',
123=>'ON',
124=>'ON',
125=>'ON',
126=>'ON',
127=>'BN',
128=>'BN',
129=>'BN',
130=>'BN',
131=>'BN',
132=>'BN',
133=>'B',
134=>'BN',
135=>'BN',
136=>'BN',
137=>'BN',
138=>'BN',
139=>'BN',
140=>'BN',
141=>'BN',
142=>'BN',
143=>'BN',
144=>'BN',
145=>'BN',
146=>'BN',
147=>'BN',
148=>'BN',
149=>'BN',
150=>'BN',
151=>'BN',
152=>'BN',
153=>'BN',
154=>'BN',
155=>'BN',
156=>'BN',
157=>'BN',
158=>'BN',
159=>'BN',
160=>'CS',
161=>'ON',
162=>'ET',
163=>'ET',
164=>'ET',
165=>'ET',
166=>'ON',
167=>'ON',
168=>'ON',
169=>'ON',
170=>'L',
171=>'ON',
172=>'ON',
173=>'BN',
174=>'ON',
175=>'ON',
176=>'ET',
177=>'ET',
178=>'EN',
179=>'EN',
180=>'ON',
181=>'L',
182=>'ON',
183=>'ON',
184=>'ON',
185=>'EN',
186=>'L',
187=>'ON',
188=>'ON',
189=>'ON',
190=>'ON',
191=>'ON',
192=>'L',
193=>'L',
194=>'L',
195=>'L',
196=>'L',
197=>'L',
198=>'L',
199=>'L',
200=>'L',
201=>'L',
202=>'L',
203=>'L',
204=>'L',
205=>'L',
206=>'L',
207=>'L',
208=>'L',
209=>'L',
210=>'L',
211=>'L',
212=>'L',
213=>'L',
214=>'L',
215=>'ON',
216=>'L',
217=>'L',
218=>'L',
219=>'L',
220=>'L',
221=>'L',
222=>'L',
223=>'L',
224=>'L',
225=>'L',
226=>'L',
227=>'L',
228=>'L',
229=>'L',
230=>'L',
231=>'L',
232=>'L',
233=>'L',
234=>'L',
235=>'L',
236=>'L',
237=>'L',
238=>'L',
239=>'L',
240=>'L',
241=>'L',
242=>'L',
243=>'L',
244=>'L',
245=>'L',
246=>'L',
247=>'ON',
248=>'L',
249=>'L',
250=>'L',
251=>'L',
252=>'L',
253=>'L',
254=>'L',
255=>'L',
256=>'L',
257=>'L',
258=>'L',
259=>'L',
260=>'L',
261=>'L',
262=>'L',
263=>'L',
264=>'L',
265=>'L',
266=>'L',
267=>'L',
268=>'L',
269=>'L',
270=>'L',
271=>'L',
272=>'L',
273=>'L',
274=>'L',
275=>'L',
276=>'L',
277=>'L',
278=>'L',
279=>'L',
280=>'L',
281=>'L',
282=>'L',
283=>'L',
284=>'L',
285=>'L',
286=>'L',
287=>'L',
288=>'L',
289=>'L',
290=>'L',
291=>'L',
292=>'L',
293=>'L',
294=>'L',
295=>'L',
296=>'L',
297=>'L',
298=>'L',
299=>'L',
300=>'L',
301=>'L',
302=>'L',
303=>'L',
304=>'L',
305=>'L',
306=>'L',
307=>'L',
308=>'L',
309=>'L',
310=>'L',
311=>'L',
312=>'L',
313=>'L',
314=>'L',
315=>'L',
316=>'L',
317=>'L',
318=>'L',
319=>'L',
320=>'L',
321=>'L',
322=>'L',
323=>'L',
324=>'L',
325=>'L',
326=>'L',
327=>'L',
328=>'L',
329=>'L',
330=>'L',
331=>'L',
332=>'L',
333=>'L',
334=>'L',
335=>'L',
336=>'L',
337=>'L',
338=>'L',
339=>'L',
340=>'L',
341=>'L',
342=>'L',
343=>'L',
344=>'L',
345=>'L',
346=>'L',
347=>'L',
348=>'L',
349=>'L',
350=>'L',
351=>'L',
352=>'L',
353=>'L',
354=>'L',
355=>'L',
356=>'L',
357=>'L',
358=>'L',
359=>'L',
360=>'L',
361=>'L',
362=>'L',
363=>'L',
364=>'L',
365=>'L',
366=>'L',
367=>'L',
368=>'L',
369=>'L',
370=>'L',
371=>'L',
372=>'L',
373=>'L',
374=>'L',
375=>'L',
376=>'L',
377=>'L',
378=>'L',
379=>'L',
380=>'L',
381=>'L',
382=>'L',
383=>'L',
384=>'L',
385=>'L',
386=>'L',
387=>'L',
388=>'L',
389=>'L',
390=>'L',
391=>'L',
392=>'L',
393=>'L',
394=>'L',
395=>'L',
396=>'L',
397=>'L',
398=>'L',
399=>'L',
400=>'L',
401=>'L',
402=>'L',
403=>'L',
404=>'L',
405=>'L',
406=>'L',
407=>'L',
408=>'L',
409=>'L',
410=>'L',
411=>'L',
412=>'L',
413=>'L',
414=>'L',
415=>'L',
416=>'L',
417=>'L',
418=>'L',
419=>'L',
420=>'L',
421=>'L',
422=>'L',
423=>'L',
424=>'L',
425=>'L',
426=>'L',
427=>'L',
428=>'L',
429=>'L',
430=>'L',
431=>'L',
432=>'L',
433=>'L',
434=>'L',
435=>'L',
436=>'L',
437=>'L',
438=>'L',
439=>'L',
440=>'L',
441=>'L',
442=>'L',
443=>'L',
444=>'L',
445=>'L',
446=>'L',
447=>'L',
448=>'L',
449=>'L',
450=>'L',
451=>'L',
452=>'L',
453=>'L',
454=>'L',
455=>'L',
456=>'L',
457=>'L',
458=>'L',
459=>'L',
460=>'L',
461=>'L',
462=>'L',
463=>'L',
464=>'L',
465=>'L',
466=>'L',
467=>'L',
468=>'L',
469=>'L',
470=>'L',
471=>'L',
472=>'L',
473=>'L',
474=>'L',
475=>'L',
476=>'L',
477=>'L',
478=>'L',
479=>'L',
480=>'L',
481=>'L',
482=>'L',
483=>'L',
484=>'L',
485=>'L',
486=>'L',
487=>'L',
488=>'L',
489=>'L',
490=>'L',
491=>'L',
492=>'L',
493=>'L',
494=>'L',
495=>'L',
496=>'L',
497=>'L',
498=>'L',
499=>'L',
500=>'L',
501=>'L',
502=>'L',
503=>'L',
504=>'L',
505=>'L',
506=>'L',
507=>'L',
508=>'L',
509=>'L',
510=>'L',
511=>'L',
512=>'L',
513=>'L',
514=>'L',
515=>'L',
516=>'L',
517=>'L',
518=>'L',
519=>'L',
520=>'L',
521=>'L',
522=>'L',
523=>'L',
524=>'L',
525=>'L',
526=>'L',
527=>'L',
528=>'L',
529=>'L',
530=>'L',
531=>'L',
532=>'L',
533=>'L',
534=>'L',
535=>'L',
536=>'L',
537=>'L',
538=>'L',
539=>'L',
540=>'L',
541=>'L',
542=>'L',
543=>'L',
544=>'L',
545=>'L',
546=>'L',
547=>'L',
548=>'L',
549=>'L',
550=>'L',
551=>'L',
552=>'L',
553=>'L',
554=>'L',
555=>'L',
556=>'L',
557=>'L',
558=>'L',
559=>'L',
560=>'L',
561=>'L',
562=>'L',
563=>'L',
564=>'L',
565=>'L',
566=>'L',
567=>'L',
568=>'L',
569=>'L',
570=>'L',
571=>'L',
572=>'L',
573=>'L',
574=>'L',
575=>'L',
576=>'L',
577=>'L',
578=>'L',
579=>'L',
580=>'L',
581=>'L',
582=>'L',
583=>'L',
584=>'L',
585=>'L',
586=>'L',
587=>'L',
588=>'L',
589=>'L',
590=>'L',
591=>'L',
592=>'L',
593=>'L',
594=>'L',
595=>'L',
596=>'L',
597=>'L',
598=>'L',
599=>'L',
600=>'L',
601=>'L',
602=>'L',
603=>'L',
604=>'L',
605=>'L',
606=>'L',
607=>'L',
608=>'L',
609=>'L',
610=>'L',
611=>'L',
612=>'L',
613=>'L',
614=>'L',
615=>'L',
616=>'L',
617=>'L',
618=>'L',
619=>'L',
620=>'L',
621=>'L',
622=>'L',
623=>'L',
624=>'L',
625=>'L',
626=>'L',
627=>'L',
628=>'L',
629=>'L',
630=>'L',
631=>'L',
632=>'L',
633=>'L',
634=>'L',
635=>'L',
636=>'L',
637=>'L',
638=>'L',
639=>'L',
640=>'L',
641=>'L',
642=>'L',
643=>'L',
644=>'L',
645=>'L',
646=>'L',
647=>'L',
648=>'L',
649=>'L',
650=>'L',
651=>'L',
652=>'L',
653=>'L',
654=>'L',
655=>'L',
656=>'L',
657=>'L',
658=>'L',
659=>'L',
660=>'L',
661=>'L',
662=>'L',
663=>'L',
664=>'L',
665=>'L',
666=>'L',
667=>'L',
668=>'L',
669=>'L',
670=>'L',
671=>'L',
672=>'L',
673=>'L',
674=>'L',
675=>'L',
676=>'L',
677=>'L',
678=>'L',
679=>'L',
680=>'L',
681=>'L',
682=>'L',
683=>'L',
684=>'L',
685=>'L',
686=>'L',
687=>'L',
688=>'L',
689=>'L',
690=>'L',
691=>'L',
692=>'L',
693=>'L',
694=>'L',
695=>'L',
696=>'L',
697=>'ON',
698=>'ON',
699=>'L',
700=>'L',
701=>'L',
702=>'L',
703=>'L',
704=>'L',
705=>'L',
706=>'ON',
707=>'ON',
708=>'ON',
709=>'ON',
710=>'ON',
711=>'ON',
712=>'ON',
713=>'ON',
714=>'ON',
715=>'ON',
716=>'ON',
717=>'ON',
718=>'ON',
719=>'ON',
720=>'L',
721=>'L',
722=>'ON',
723=>'ON',
724=>'ON',
725=>'ON',
726=>'ON',
727=>'ON',
728=>'ON',
729=>'ON',
730=>'ON',
731=>'ON',
732=>'ON',
733=>'ON',
734=>'ON',
735=>'ON',
736=>'L',
737=>'L',
738=>'L',
739=>'L',
740=>'L',
741=>'ON',
742=>'ON',
743=>'ON',
744=>'ON',
745=>'ON',
746=>'ON',
747=>'ON',
748=>'ON',
749=>'ON',
750=>'L',
751=>'ON',
752=>'ON',
753=>'ON',
754=>'ON',
755=>'ON',
756=>'ON',
757=>'ON',
758=>'ON',
759=>'ON',
760=>'ON',
761=>'ON',
762=>'ON',
763=>'ON',
764=>'ON',
765=>'ON',
766=>'ON',
767=>'ON',
768=>'NSM',
769=>'NSM',
770=>'NSM',
771=>'NSM',
772=>'NSM',
773=>'NSM',
774=>'NSM',
775=>'NSM',
776=>'NSM',
777=>'NSM',
778=>'NSM',
779=>'NSM',
780=>'NSM',
781=>'NSM',
782=>'NSM',
783=>'NSM',
784=>'NSM',
785=>'NSM',
786=>'NSM',
787=>'NSM',
788=>'NSM',
789=>'NSM',
790=>'NSM',
791=>'NSM',
792=>'NSM',
793=>'NSM',
794=>'NSM',
795=>'NSM',
796=>'NSM',
797=>'NSM',
798=>'NSM',
799=>'NSM',
800=>'NSM',
801=>'NSM',
802=>'NSM',
803=>'NSM',
804=>'NSM',
805=>'NSM',
806=>'NSM',
807=>'NSM',
808=>'NSM',
809=>'NSM',
810=>'NSM',
811=>'NSM',
812=>'NSM',
813=>'NSM',
814=>'NSM',
815=>'NSM',
816=>'NSM',
817=>'NSM',
818=>'NSM',
819=>'NSM',
820=>'NSM',
821=>'NSM',
822=>'NSM',
823=>'NSM',
824=>'NSM',
825=>'NSM',
826=>'NSM',
827=>'NSM',
828=>'NSM',
829=>'NSM',
830=>'NSM',
831=>'NSM',
832=>'NSM',
833=>'NSM',
834=>'NSM',
835=>'NSM',
836=>'NSM',
837=>'NSM',
838=>'NSM',
839=>'NSM',
840=>'NSM',
841=>'NSM',
842=>'NSM',
843=>'NSM',
844=>'NSM',
845=>'NSM',
846=>'NSM',
847=>'NSM',
848=>'NSM',
849=>'NSM',
850=>'NSM',
851=>'NSM',
852=>'NSM',
853=>'NSM',
854=>'NSM',
855=>'NSM',
856=>'NSM',
857=>'NSM',
858=>'NSM',
859=>'NSM',
860=>'NSM',
861=>'NSM',
862=>'NSM',
863=>'NSM',
864=>'NSM',
865=>'NSM',
866=>'NSM',
867=>'NSM',
868=>'NSM',
869=>'NSM',
870=>'NSM',
871=>'NSM',
872=>'NSM',
873=>'NSM',
874=>'NSM',
875=>'NSM',
876=>'NSM',
877=>'NSM',
878=>'NSM',
879=>'NSM',
884=>'ON',
885=>'ON',
890=>'L',
891=>'L',
892=>'L',
893=>'L',
894=>'ON',
900=>'ON',
901=>'ON',
902=>'L',
903=>'ON',
904=>'L',
905=>'L',
906=>'L',
908=>'L',
910=>'L',
911=>'L',
912=>'L',
913=>'L',
914=>'L',
915=>'L',
916=>'L',
917=>'L',
918=>'L',
919=>'L',
920=>'L',
921=>'L',
922=>'L',
923=>'L',
924=>'L',
925=>'L',
926=>'L',
927=>'L',
928=>'L',
929=>'L',
931=>'L',
932=>'L',
933=>'L',
934=>'L',
935=>'L',
936=>'L',
937=>'L',
938=>'L',
939=>'L',
940=>'L',
941=>'L',
942=>'L',
943=>'L',
944=>'L',
945=>'L',
946=>'L',
947=>'L',
948=>'L',
949=>'L',
950=>'L',
951=>'L',
952=>'L',
953=>'L',
954=>'L',
955=>'L',
956=>'L',
957=>'L',
958=>'L',
959=>'L',
960=>'L',
961=>'L',
962=>'L',
963=>'L',
964=>'L',
965=>'L',
966=>'L',
967=>'L',
968=>'L',
969=>'L',
970=>'L',
971=>'L',
972=>'L',
973=>'L',
974=>'L',
976=>'L',
977=>'L',
978=>'L',
979=>'L',
980=>'L',
981=>'L',
982=>'L',
983=>'L',
984=>'L',
985=>'L',
986=>'L',
987=>'L',
988=>'L',
989=>'L',
990=>'L',
991=>'L',
992=>'L',
993=>'L',
994=>'L',
995=>'L',
996=>'L',
997=>'L',
998=>'L',
999=>'L',
1000=>'L',
1001=>'L',
1002=>'L',
1003=>'L',
1004=>'L',
1005=>'L',
1006=>'L',
1007=>'L',
1008=>'L',
1009=>'L',
1010=>'L',
1011=>'L',
1012=>'L',
1013=>'L',
1014=>'ON',
1015=>'L',
1016=>'L',
1017=>'L',
1018=>'L',
1019=>'L',
1020=>'L',
1021=>'L',
1022=>'L',
1023=>'L',
1024=>'L',
1025=>'L',
1026=>'L',
1027=>'L',
1028=>'L',
1029=>'L',
1030=>'L',
1031=>'L',
1032=>'L',
1033=>'L',
1034=>'L',
1035=>'L',
1036=>'L',
1037=>'L',
1038=>'L',
1039=>'L',
1040=>'L',
1041=>'L',
1042=>'L',
1043=>'L',
1044=>'L',
1045=>'L',
1046=>'L',
1047=>'L',
1048=>'L',
1049=>'L',
1050=>'L',
1051=>'L',
1052=>'L',
1053=>'L',
1054=>'L',
1055=>'L',
1056=>'L',
1057=>'L',
1058=>'L',
1059=>'L',
1060=>'L',
1061=>'L',
1062=>'L',
1063=>'L',
1064=>'L',
1065=>'L',
1066=>'L',
1067=>'L',
1068=>'L',
1069=>'L',
1070=>'L',
1071=>'L',
1072=>'L',
1073=>'L',
1074=>'L',
1075=>'L',
1076=>'L',
1077=>'L',
1078=>'L',
1079=>'L',
1080=>'L',
1081=>'L',
1082=>'L',
1083=>'L',
1084=>'L',
1085=>'L',
1086=>'L',
1087=>'L',
1088=>'L',
1089=>'L',
1090=>'L',
1091=>'L',
1092=>'L',
1093=>'L',
1094=>'L',
1095=>'L',
1096=>'L',
1097=>'L',
1098=>'L',
1099=>'L',
1100=>'L',
1101=>'L',
1102=>'L',
1103=>'L',
1104=>'L',
1105=>'L',
1106=>'L',
1107=>'L',
1108=>'L',
1109=>'L',
1110=>'L',
1111=>'L',
1112=>'L',
1113=>'L',
1114=>'L',
1115=>'L',
1116=>'L',
1117=>'L',
1118=>'L',
1119=>'L',
1120=>'L',
1121=>'L',
1122=>'L',
1123=>'L',
1124=>'L',
1125=>'L',
1126=>'L',
1127=>'L',
1128=>'L',
1129=>'L',
1130=>'L',
1131=>'L',
1132=>'L',
1133=>'L',
1134=>'L',
1135=>'L',
1136=>'L',
1137=>'L',
1138=>'L',
1139=>'L',
1140=>'L',
1141=>'L',
1142=>'L',
1143=>'L',
1144=>'L',
1145=>'L',
1146=>'L',
1147=>'L',
1148=>'L',
1149=>'L',
1150=>'L',
1151=>'L',
1152=>'L',
1153=>'L',
1154=>'L',
1155=>'NSM',
1156=>'NSM',
1157=>'NSM',
1158=>'NSM',
1160=>'NSM',
1161=>'NSM',
1162=>'L',
1163=>'L',
1164=>'L',
1165=>'L',
1166=>'L',
1167=>'L',
1168=>'L',
1169=>'L',
1170=>'L',
1171=>'L',
1172=>'L',
1173=>'L',
1174=>'L',
1175=>'L',
1176=>'L',
1177=>'L',
1178=>'L',
1179=>'L',
1180=>'L',
1181=>'L',
1182=>'L',
1183=>'L',
1184=>'L',
1185=>'L',
1186=>'L',
1187=>'L',
1188=>'L',
1189=>'L',
1190=>'L',
1191=>'L',
1192=>'L',
1193=>'L',
1194=>'L',
1195=>'L',
1196=>'L',
1197=>'L',
1198=>'L',
1199=>'L',
1200=>'L',
1201=>'L',
1202=>'L',
1203=>'L',
1204=>'L',
1205=>'L',
1206=>'L',
1207=>'L',
1208=>'L',
1209=>'L',
1210=>'L',
1211=>'L',
1212=>'L',
1213=>'L',
1214=>'L',
1215=>'L',
1216=>'L',
1217=>'L',
1218=>'L',
1219=>'L',
1220=>'L',
1221=>'L',
1222=>'L',
1223=>'L',
1224=>'L',
1225=>'L',
1226=>'L',
1227=>'L',
1228=>'L',
1229=>'L',
1230=>'L',
1231=>'L',
1232=>'L',
1233=>'L',
1234=>'L',
1235=>'L',
1236=>'L',
1237=>'L',
1238=>'L',
1239=>'L',
1240=>'L',
1241=>'L',
1242=>'L',
1243=>'L',
1244=>'L',
1245=>'L',
1246=>'L',
1247=>'L',
1248=>'L',
1249=>'L',
1250=>'L',
1251=>'L',
1252=>'L',
1253=>'L',
1254=>'L',
1255=>'L',
1256=>'L',
1257=>'L',
1258=>'L',
1259=>'L',
1260=>'L',
1261=>'L',
1262=>'L',
1263=>'L',
1264=>'L',
1265=>'L',
1266=>'L',
1267=>'L',
1268=>'L',
1269=>'L',
1270=>'L',
1271=>'L',
1272=>'L',
1273=>'L',
1274=>'L',
1275=>'L',
1276=>'L',
1277=>'L',
1278=>'L',
1279=>'L',
1280=>'L',
1281=>'L',
1282=>'L',
1283=>'L',
1284=>'L',
1285=>'L',
1286=>'L',
1287=>'L',
1288=>'L',
1289=>'L',
1290=>'L',
1291=>'L',
1292=>'L',
1293=>'L',
1294=>'L',
1295=>'L',
1296=>'L',
1297=>'L',
1298=>'L',
1299=>'L',
1329=>'L',
1330=>'L',
1331=>'L',
1332=>'L',
1333=>'L',
1334=>'L',
1335=>'L',
1336=>'L',
1337=>'L',
1338=>'L',
1339=>'L',
1340=>'L',
1341=>'L',
1342=>'L',
1343=>'L',
1344=>'L',
1345=>'L',
1346=>'L',
1347=>'L',
1348=>'L',
1349=>'L',
1350=>'L',
1351=>'L',
1352=>'L',
1353=>'L',
1354=>'L',
1355=>'L',
1356=>'L',
1357=>'L',
1358=>'L',
1359=>'L',
1360=>'L',
1361=>'L',
1362=>'L',
1363=>'L',
1364=>'L',
1365=>'L',
1366=>'L',
1369=>'L',
1370=>'L',
1371=>'L',
1372=>'L',
1373=>'L',
1374=>'L',
1375=>'L',
1377=>'L',
1378=>'L',
1379=>'L',
1380=>'L',
1381=>'L',
1382=>'L',
1383=>'L',
1384=>'L',
1385=>'L',
1386=>'L',
1387=>'L',
1388=>'L',
1389=>'L',
1390=>'L',
1391=>'L',
1392=>'L',
1393=>'L',
1394=>'L',
1395=>'L',
1396=>'L',
1397=>'L',
1398=>'L',
1399=>'L',
1400=>'L',
1401=>'L',
1402=>'L',
1403=>'L',
1404=>'L',
1405=>'L',
1406=>'L',
1407=>'L',
1408=>'L',
1409=>'L',
1410=>'L',
1411=>'L',
1412=>'L',
1413=>'L',
1414=>'L',
1415=>'L',
1417=>'L',
1418=>'ON',
1425=>'NSM',
1426=>'NSM',
1427=>'NSM',
1428=>'NSM',
1429=>'NSM',
1430=>'NSM',
1431=>'NSM',
1432=>'NSM',
1433=>'NSM',
1434=>'NSM',
1435=>'NSM',
1436=>'NSM',
1437=>'NSM',
1438=>'NSM',
1439=>'NSM',
1440=>'NSM',
1441=>'NSM',
1442=>'NSM',
1443=>'NSM',
1444=>'NSM',
1445=>'NSM',
1446=>'NSM',
1447=>'NSM',
1448=>'NSM',
1449=>'NSM',
1450=>'NSM',
1451=>'NSM',
1452=>'NSM',
1453=>'NSM',
1454=>'NSM',
1455=>'NSM',
1456=>'NSM',
1457=>'NSM',
1458=>'NSM',
1459=>'NSM',
1460=>'NSM',
1461=>'NSM',
1462=>'NSM',
1463=>'NSM',
1464=>'NSM',
1465=>'NSM',
1466=>'NSM',
1467=>'NSM',
1468=>'NSM',
1469=>'NSM',
1470=>'R',
1471=>'NSM',
1472=>'R',
1473=>'NSM',
1474=>'NSM',
1475=>'R',
1476=>'NSM',
1477=>'NSM',
1478=>'R',
1479=>'NSM',
1488=>'R',
1489=>'R',
1490=>'R',
1491=>'R',
1492=>'R',
1493=>'R',
1494=>'R',
1495=>'R',
1496=>'R',
1497=>'R',
1498=>'R',
1499=>'R',
1500=>'R',
1501=>'R',
1502=>'R',
1503=>'R',
1504=>'R',
1505=>'R',
1506=>'R',
1507=>'R',
1508=>'R',
1509=>'R',
1510=>'R',
1511=>'R',
1512=>'R',
1513=>'R',
1514=>'R',
1520=>'R',
1521=>'R',
1522=>'R',
1523=>'R',
1524=>'R',
1536=>'AL',
1537=>'AL',
1538=>'AL',
1539=>'AL',
1547=>'AL',
1548=>'CS',
1549=>'AL',
1550=>'ON',
1551=>'ON',
1552=>'NSM',
1553=>'NSM',
1554=>'NSM',
1555=>'NSM',
1556=>'NSM',
1557=>'NSM',
1563=>'AL',
1566=>'AL',
1567=>'AL',
1569=>'AL',
1570=>'AL',
1571=>'AL',
1572=>'AL',
1573=>'AL',
1574=>'AL',
1575=>'AL',
1576=>'AL',
1577=>'AL',
1578=>'AL',
1579=>'AL',
1580=>'AL',
1581=>'AL',
1582=>'AL',
1583=>'AL',
1584=>'AL',
1585=>'AL',
1586=>'AL',
1587=>'AL',
1588=>'AL',
1589=>'AL',
1590=>'AL',
1591=>'AL',
1592=>'AL',
1593=>'AL',
1594=>'AL',
1600=>'AL',
1601=>'AL',
1602=>'AL',
1603=>'AL',
1604=>'AL',
1605=>'AL',
1606=>'AL',
1607=>'AL',
1608=>'AL',
1609=>'AL',
1610=>'AL',
1611=>'NSM',
1612=>'NSM',
1613=>'NSM',
1614=>'NSM',
1615=>'NSM',
1616=>'NSM',
1617=>'NSM',
1618=>'NSM',
1619=>'NSM',
1620=>'NSM',
1621=>'NSM',
1622=>'NSM',
1623=>'NSM',
1624=>'NSM',
1625=>'NSM',
1626=>'NSM',
1627=>'NSM',
1628=>'NSM',
1629=>'NSM',
1630=>'NSM',
1632=>'AN',
1633=>'AN',
1634=>'AN',
1635=>'AN',
1636=>'AN',
1637=>'AN',
1638=>'AN',
1639=>'AN',
1640=>'AN',
1641=>'AN',
1642=>'ET',
1643=>'AN',
1644=>'AN',
1645=>'AL',
1646=>'AL',
1647=>'AL',
1648=>'NSM',
1649=>'AL',
1650=>'AL',
1651=>'AL',
1652=>'AL',
1653=>'AL',
1654=>'AL',
1655=>'AL',
1656=>'AL',
1657=>'AL',
1658=>'AL',
1659=>'AL',
1660=>'AL',
1661=>'AL',
1662=>'AL',
1663=>'AL',
1664=>'AL',
1665=>'AL',
1666=>'AL',
1667=>'AL',
1668=>'AL',
1669=>'AL',
1670=>'AL',
1671=>'AL',
1672=>'AL',
1673=>'AL',
1674=>'AL',
1675=>'AL',
1676=>'AL',
1677=>'AL',
1678=>'AL',
1679=>'AL',
1680=>'AL',
1681=>'AL',
1682=>'AL',
1683=>'AL',
1684=>'AL',
1685=>'AL',
1686=>'AL',
1687=>'AL',
1688=>'AL',
1689=>'AL',
1690=>'AL',
1691=>'AL',
1692=>'AL',
1693=>'AL',
1694=>'AL',
1695=>'AL',
1696=>'AL',
1697=>'AL',
1698=>'AL',
1699=>'AL',
1700=>'AL',
1701=>'AL',
1702=>'AL',
1703=>'AL',
1704=>'AL',
1705=>'AL',
1706=>'AL',
1707=>'AL',
1708=>'AL',
1709=>'AL',
1710=>'AL',
1711=>'AL',
1712=>'AL',
1713=>'AL',
1714=>'AL',
1715=>'AL',
1716=>'AL',
1717=>'AL',
1718=>'AL',
1719=>'AL',
1720=>'AL',
1721=>'AL',
1722=>'AL',
1723=>'AL',
1724=>'AL',
1725=>'AL',
1726=>'AL',
1727=>'AL',
1728=>'AL',
1729=>'AL',
1730=>'AL',
1731=>'AL',
1732=>'AL',
1733=>'AL',
1734=>'AL',
1735=>'AL',
1736=>'AL',
1737=>'AL',
1738=>'AL',
1739=>'AL',
1740=>'AL',
1741=>'AL',
1742=>'AL',
1743=>'AL',
1744=>'AL',
1745=>'AL',
1746=>'AL',
1747=>'AL',
1748=>'AL',
1749=>'AL',
1750=>'NSM',
1751=>'NSM',
1752=>'NSM',
1753=>'NSM',
1754=>'NSM',
1755=>'NSM',
1756=>'NSM',
1757=>'AL',
1758=>'NSM',
1759=>'NSM',
1760=>'NSM',
1761=>'NSM',
1762=>'NSM',
1763=>'NSM',
1764=>'NSM',
1765=>'AL',
1766=>'AL',
1767=>'NSM',
1768=>'NSM',
1769=>'ON',
1770=>'NSM',
1771=>'NSM',
1772=>'NSM',
1773=>'NSM',
1774=>'AL',
1775=>'AL',
1776=>'EN',
1777=>'EN',
1778=>'EN',
1779=>'EN',
1780=>'EN',
1781=>'EN',
1782=>'EN',
1783=>'EN',
1784=>'EN',
1785=>'EN',
1786=>'AL',
1787=>'AL',
1788=>'AL',
1789=>'AL',
1790=>'AL',
1791=>'AL',
1792=>'AL',
1793=>'AL',
1794=>'AL',
1795=>'AL',
1796=>'AL',
1797=>'AL',
1798=>'AL',
1799=>'AL',
1800=>'AL',
1801=>'AL',
1802=>'AL',
1803=>'AL',
1804=>'AL',
1805=>'AL',
1807=>'BN',
1808=>'AL',
1809=>'NSM',
1810=>'AL',
1811=>'AL',
1812=>'AL',
1813=>'AL',
1814=>'AL',
1815=>'AL',
1816=>'AL',
1817=>'AL',
1818=>'AL',
1819=>'AL',
1820=>'AL',
1821=>'AL',
1822=>'AL',
1823=>'AL',
1824=>'AL',
1825=>'AL',
1826=>'AL',
1827=>'AL',
1828=>'AL',
1829=>'AL',
1830=>'AL',
1831=>'AL',
1832=>'AL',
1833=>'AL',
1834=>'AL',
1835=>'AL',
1836=>'AL',
1837=>'AL',
1838=>'AL',
1839=>'AL',
1840=>'NSM',
1841=>'NSM',
1842=>'NSM',
1843=>'NSM',
1844=>'NSM',
1845=>'NSM',
1846=>'NSM',
1847=>'NSM',
1848=>'NSM',
1849=>'NSM',
1850=>'NSM',
1851=>'NSM',
1852=>'NSM',
1853=>'NSM',
1854=>'NSM',
1855=>'NSM',
1856=>'NSM',
1857=>'NSM',
1858=>'NSM',
1859=>'NSM',
1860=>'NSM',
1861=>'NSM',
1862=>'NSM',
1863=>'NSM',
1864=>'NSM',
1865=>'NSM',
1866=>'NSM',
1869=>'AL',
1870=>'AL',
1871=>'AL',
1872=>'AL',
1873=>'AL',
1874=>'AL',
1875=>'AL',
1876=>'AL',
1877=>'AL',
1878=>'AL',
1879=>'AL',
1880=>'AL',
1881=>'AL',
1882=>'AL',
1883=>'AL',
1884=>'AL',
1885=>'AL',
1886=>'AL',
1887=>'AL',
1888=>'AL',
1889=>'AL',
1890=>'AL',
1891=>'AL',
1892=>'AL',
1893=>'AL',
1894=>'AL',
1895=>'AL',
1896=>'AL',
1897=>'AL',
1898=>'AL',
1899=>'AL',
1900=>'AL',
1901=>'AL',
1920=>'AL',
1921=>'AL',
1922=>'AL',
1923=>'AL',
1924=>'AL',
1925=>'AL',
1926=>'AL',
1927=>'AL',
1928=>'AL',
1929=>'AL',
1930=>'AL',
1931=>'AL',
1932=>'AL',
1933=>'AL',
1934=>'AL',
1935=>'AL',
1936=>'AL',
1937=>'AL',
1938=>'AL',
1939=>'AL',
1940=>'AL',
1941=>'AL',
1942=>'AL',
1943=>'AL',
1944=>'AL',
1945=>'AL',
1946=>'AL',
1947=>'AL',
1948=>'AL',
1949=>'AL',
1950=>'AL',
1951=>'AL',
1952=>'AL',
1953=>'AL',
1954=>'AL',
1955=>'AL',
1956=>'AL',
1957=>'AL',
1958=>'NSM',
1959=>'NSM',
1960=>'NSM',
1961=>'NSM',
1962=>'NSM',
1963=>'NSM',
1964=>'NSM',
1965=>'NSM',
1966=>'NSM',
1967=>'NSM',
1968=>'NSM',
1969=>'AL',
1984=>'R',
1985=>'R',
1986=>'R',
1987=>'R',
1988=>'R',
1989=>'R',
1990=>'R',
1991=>'R',
1992=>'R',
1993=>'R',
1994=>'R',
1995=>'R',
1996=>'R',
1997=>'R',
1998=>'R',
1999=>'R',
2000=>'R',
2001=>'R',
2002=>'R',
2003=>'R',
2004=>'R',
2005=>'R',
2006=>'R',
2007=>'R',
2008=>'R',
2009=>'R',
2010=>'R',
2011=>'R',
2012=>'R',
2013=>'R',
2014=>'R',
2015=>'R',
2016=>'R',
2017=>'R',
2018=>'R',
2019=>'R',
2020=>'R',
2021=>'R',
2022=>'R',
2023=>'R',
2024=>'R',
2025=>'R',
2026=>'R',
2027=>'NSM',
2028=>'NSM',
2029=>'NSM',
2030=>'NSM',
2031=>'NSM',
2032=>'NSM',
2033=>'NSM',
2034=>'NSM',
2035=>'NSM',
2036=>'R',
2037=>'R',
2038=>'ON',
2039=>'ON',
2040=>'ON',
2041=>'ON',
2042=>'R',
2305=>'NSM',
2306=>'NSM',
2307=>'L',
2308=>'L',
2309=>'L',
2310=>'L',
2311=>'L',
2312=>'L',
2313=>'L',
2314=>'L',
2315=>'L',
2316=>'L',
2317=>'L',
2318=>'L',
2319=>'L',
2320=>'L',
2321=>'L',
2322=>'L',
2323=>'L',
2324=>'L',
2325=>'L',
2326=>'L',
2327=>'L',
2328=>'L',
2329=>'L',
2330=>'L',
2331=>'L',
2332=>'L',
2333=>'L',
2334=>'L',
2335=>'L',
2336=>'L',
2337=>'L',
2338=>'L',
2339=>'L',
2340=>'L',
2341=>'L',
2342=>'L',
2343=>'L',
2344=>'L',
2345=>'L',
2346=>'L',
2347=>'L',
2348=>'L',
2349=>'L',
2350=>'L',
2351=>'L',
2352=>'L',
2353=>'L',
2354=>'L',
2355=>'L',
2356=>'L',
2357=>'L',
2358=>'L',
2359=>'L',
2360=>'L',
2361=>'L',
2364=>'NSM',
2365=>'L',
2366=>'L',
2367=>'L',
2368=>'L',
2369=>'NSM',
2370=>'NSM',
2371=>'NSM',
2372=>'NSM',
2373=>'NSM',
2374=>'NSM',
2375=>'NSM',
2376=>'NSM',
2377=>'L',
2378=>'L',
2379=>'L',
2380=>'L',
2381=>'NSM',
2384=>'L',
2385=>'NSM',
2386=>'NSM',
2387=>'NSM',
2388=>'NSM',
2392=>'L',
2393=>'L',
2394=>'L',
2395=>'L',
2396=>'L',
2397=>'L',
2398=>'L',
2399=>'L',
2400=>'L',
2401=>'L',
2402=>'NSM',
2403=>'NSM',
2404=>'L',
2405=>'L',
2406=>'L',
2407=>'L',
2408=>'L',
2409=>'L',
2410=>'L',
2411=>'L',
2412=>'L',
2413=>'L',
2414=>'L',
2415=>'L',
2416=>'L',
2427=>'L',
2428=>'L',
2429=>'L',
2430=>'L',
2431=>'L',
2433=>'NSM',
2434=>'L',
2435=>'L',
2437=>'L',
2438=>'L',
2439=>'L',
2440=>'L',
2441=>'L',
2442=>'L',
2443=>'L',
2444=>'L',
2447=>'L',
2448=>'L',
2451=>'L',
2452=>'L',
2453=>'L',
2454=>'L',
2455=>'L',
2456=>'L',
2457=>'L',
2458=>'L',
2459=>'L',
2460=>'L',
2461=>'L',
2462=>'L',
2463=>'L',
2464=>'L',
2465=>'L',
2466=>'L',
2467=>'L',
2468=>'L',
2469=>'L',
2470=>'L',
2471=>'L',
2472=>'L',
2474=>'L',
2475=>'L',
2476=>'L',
2477=>'L',
2478=>'L',
2479=>'L',
2480=>'L',
2482=>'L',
2486=>'L',
2487=>'L',
2488=>'L',
2489=>'L',
2492=>'NSM',
2493=>'L',
2494=>'L',
2495=>'L',
2496=>'L',
2497=>'NSM',
2498=>'NSM',
2499=>'NSM',
2500=>'NSM',
2503=>'L',
2504=>'L',
2507=>'L',
2508=>'L',
2509=>'NSM',
2510=>'L',
2519=>'L',
2524=>'L',
2525=>'L',
2527=>'L',
2528=>'L',
2529=>'L',
2530=>'NSM',
2531=>'NSM',
2534=>'L',
2535=>'L',
2536=>'L',
2537=>'L',
2538=>'L',
2539=>'L',
2540=>'L',
2541=>'L',
2542=>'L',
2543=>'L',
2544=>'L',
2545=>'L',
2546=>'ET',
2547=>'ET',
2548=>'L',
2549=>'L',
2550=>'L',
2551=>'L',
2552=>'L',
2553=>'L',
2554=>'L',
2561=>'NSM',
2562=>'NSM',
2563=>'L',
2565=>'L',
2566=>'L',
2567=>'L',
2568=>'L',
2569=>'L',
2570=>'L',
2575=>'L',
2576=>'L',
2579=>'L',
2580=>'L',
2581=>'L',
2582=>'L',
2583=>'L',
2584=>'L',
2585=>'L',
2586=>'L',
2587=>'L',
2588=>'L',
2589=>'L',
2590=>'L',
2591=>'L',
2592=>'L',
2593=>'L',
2594=>'L',
2595=>'L',
2596=>'L',
2597=>'L',
2598=>'L',
2599=>'L',
2600=>'L',
2602=>'L',
2603=>'L',
2604=>'L',
2605=>'L',
2606=>'L',
2607=>'L',
2608=>'L',
2610=>'L',
2611=>'L',
2613=>'L',
2614=>'L',
2616=>'L',
2617=>'L',
2620=>'NSM',
2622=>'L',
2623=>'L',
2624=>'L',
2625=>'NSM',
2626=>'NSM',
2631=>'NSM',
2632=>'NSM',
2635=>'NSM',
2636=>'NSM',
2637=>'NSM',
2649=>'L',
2650=>'L',
2651=>'L',
2652=>'L',
2654=>'L',
2662=>'L',
2663=>'L',
2664=>'L',
2665=>'L',
2666=>'L',
2667=>'L',
2668=>'L',
2669=>'L',
2670=>'L',
2671=>'L',
2672=>'NSM',
2673=>'NSM',
2674=>'L',
2675=>'L',
2676=>'L',
2689=>'NSM',
2690=>'NSM',
2691=>'L',
2693=>'L',
2694=>'L',
2695=>'L',
2696=>'L',
2697=>'L',
2698=>'L',
2699=>'L',
2700=>'L',
2701=>'L',
2703=>'L',
2704=>'L',
2705=>'L',
2707=>'L',
2708=>'L',
2709=>'L',
2710=>'L',
2711=>'L',
2712=>'L',
2713=>'L',
2714=>'L',
2715=>'L',
2716=>'L',
2717=>'L',
2718=>'L',
2719=>'L',
2720=>'L',
2721=>'L',
2722=>'L',
2723=>'L',
2724=>'L',
2725=>'L',
2726=>'L',
2727=>'L',
2728=>'L',
2730=>'L',
2731=>'L',
2732=>'L',
2733=>'L',
2734=>'L',
2735=>'L',
2736=>'L',
2738=>'L',
2739=>'L',
2741=>'L',
2742=>'L',
2743=>'L',
2744=>'L',
2745=>'L',
2748=>'NSM',
2749=>'L',
2750=>'L',
2751=>'L',
2752=>'L',
2753=>'NSM',
2754=>'NSM',
2755=>'NSM',
2756=>'NSM',
2757=>'NSM',
2759=>'NSM',
2760=>'NSM',
2761=>'L',
2763=>'L',
2764=>'L',
2765=>'NSM',
2768=>'L',
2784=>'L',
2785=>'L',
2786=>'NSM',
2787=>'NSM',
2790=>'L',
2791=>'L',
2792=>'L',
2793=>'L',
2794=>'L',
2795=>'L',
2796=>'L',
2797=>'L',
2798=>'L',
2799=>'L',
2801=>'ET',
2817=>'NSM',
2818=>'L',
2819=>'L',
2821=>'L',
2822=>'L',
2823=>'L',
2824=>'L',
2825=>'L',
2826=>'L',
2827=>'L',
2828=>'L',
2831=>'L',
2832=>'L',
2835=>'L',
2836=>'L',
2837=>'L',
2838=>'L',
2839=>'L',
2840=>'L',
2841=>'L',
2842=>'L',
2843=>'L',
2844=>'L',
2845=>'L',
2846=>'L',
2847=>'L',
2848=>'L',
2849=>'L',
2850=>'L',
2851=>'L',
2852=>'L',
2853=>'L',
2854=>'L',
2855=>'L',
2856=>'L',
2858=>'L',
2859=>'L',
2860=>'L',
2861=>'L',
2862=>'L',
2863=>'L',
2864=>'L',
2866=>'L',
2867=>'L',
2869=>'L',
2870=>'L',
2871=>'L',
2872=>'L',
2873=>'L',
2876=>'NSM',
2877=>'L',
2878=>'L',
2879=>'NSM',
2880=>'L',
2881=>'NSM',
2882=>'NSM',
2883=>'NSM',
2887=>'L',
2888=>'L',
2891=>'L',
2892=>'L',
2893=>'NSM',
2902=>'NSM',
2903=>'L',
2908=>'L',
2909=>'L',
2911=>'L',
2912=>'L',
2913=>'L',
2918=>'L',
2919=>'L',
2920=>'L',
2921=>'L',
2922=>'L',
2923=>'L',
2924=>'L',
2925=>'L',
2926=>'L',
2927=>'L',
2928=>'L',
2929=>'L',
2946=>'NSM',
2947=>'L',
2949=>'L',
2950=>'L',
2951=>'L',
2952=>'L',
2953=>'L',
2954=>'L',
2958=>'L',
2959=>'L',
2960=>'L',
2962=>'L',
2963=>'L',
2964=>'L',
2965=>'L',
2969=>'L',
2970=>'L',
2972=>'L',
2974=>'L',
2975=>'L',
2979=>'L',
2980=>'L',
2984=>'L',
2985=>'L',
2986=>'L',
2990=>'L',
2991=>'L',
2992=>'L',
2993=>'L',
2994=>'L',
2995=>'L',
2996=>'L',
2997=>'L',
2998=>'L',
2999=>'L',
3000=>'L',
3001=>'L',
3006=>'L',
3007=>'L',
3008=>'NSM',
3009=>'L',
3010=>'L',
3014=>'L',
3015=>'L',
3016=>'L',
3018=>'L',
3019=>'L',
3020=>'L',
3021=>'NSM',
3031=>'L',
3046=>'L',
3047=>'L',
3048=>'L',
3049=>'L',
3050=>'L',
3051=>'L',
3052=>'L',
3053=>'L',
3054=>'L',
3055=>'L',
3056=>'L',
3057=>'L',
3058=>'L',
3059=>'ON',
3060=>'ON',
3061=>'ON',
3062=>'ON',
3063=>'ON',
3064=>'ON',
3065=>'ET',
3066=>'ON',
3073=>'L',
3074=>'L',
3075=>'L',
3077=>'L',
3078=>'L',
3079=>'L',
3080=>'L',
3081=>'L',
3082=>'L',
3083=>'L',
3084=>'L',
3086=>'L',
3087=>'L',
3088=>'L',
3090=>'L',
3091=>'L',
3092=>'L',
3093=>'L',
3094=>'L',
3095=>'L',
3096=>'L',
3097=>'L',
3098=>'L',
3099=>'L',
3100=>'L',
3101=>'L',
3102=>'L',
3103=>'L',
3104=>'L',
3105=>'L',
3106=>'L',
3107=>'L',
3108=>'L',
3109=>'L',
3110=>'L',
3111=>'L',
3112=>'L',
3114=>'L',
3115=>'L',
3116=>'L',
3117=>'L',
3118=>'L',
3119=>'L',
3120=>'L',
3121=>'L',
3122=>'L',
3123=>'L',
3125=>'L',
3126=>'L',
3127=>'L',
3128=>'L',
3129=>'L',
3134=>'NSM',
3135=>'NSM',
3136=>'NSM',
3137=>'L',
3138=>'L',
3139=>'L',
3140=>'L',
3142=>'NSM',
3143=>'NSM',
3144=>'NSM',
3146=>'NSM',
3147=>'NSM',
3148=>'NSM',
3149=>'NSM',
3157=>'NSM',
3158=>'NSM',
3168=>'L',
3169=>'L',
3174=>'L',
3175=>'L',
3176=>'L',
3177=>'L',
3178=>'L',
3179=>'L',
3180=>'L',
3181=>'L',
3182=>'L',
3183=>'L',
3202=>'L',
3203=>'L',
3205=>'L',
3206=>'L',
3207=>'L',
3208=>'L',
3209=>'L',
3210=>'L',
3211=>'L',
3212=>'L',
3214=>'L',
3215=>'L',
3216=>'L',
3218=>'L',
3219=>'L',
3220=>'L',
3221=>'L',
3222=>'L',
3223=>'L',
3224=>'L',
3225=>'L',
3226=>'L',
3227=>'L',
3228=>'L',
3229=>'L',
3230=>'L',
3231=>'L',
3232=>'L',
3233=>'L',
3234=>'L',
3235=>'L',
3236=>'L',
3237=>'L',
3238=>'L',
3239=>'L',
3240=>'L',
3242=>'L',
3243=>'L',
3244=>'L',
3245=>'L',
3246=>'L',
3247=>'L',
3248=>'L',
3249=>'L',
3250=>'L',
3251=>'L',
3253=>'L',
3254=>'L',
3255=>'L',
3256=>'L',
3257=>'L',
3260=>'NSM',
3261=>'L',
3262=>'L',
3263=>'L',
3264=>'L',
3265=>'L',
3266=>'L',
3267=>'L',
3268=>'L',
3270=>'L',
3271=>'L',
3272=>'L',
3274=>'L',
3275=>'L',
3276=>'NSM',
3277=>'NSM',
3285=>'L',
3286=>'L',
3294=>'L',
3296=>'L',
3297=>'L',
3298=>'NSM',
3299=>'NSM',
3302=>'L',
3303=>'L',
3304=>'L',
3305=>'L',
3306=>'L',
3307=>'L',
3308=>'L',
3309=>'L',
3310=>'L',
3311=>'L',
3313=>'ON',
3314=>'ON',
3330=>'L',
3331=>'L',
3333=>'L',
3334=>'L',
3335=>'L',
3336=>'L',
3337=>'L',
3338=>'L',
3339=>'L',
3340=>'L',
3342=>'L',
3343=>'L',
3344=>'L',
3346=>'L',
3347=>'L',
3348=>'L',
3349=>'L',
3350=>'L',
3351=>'L',
3352=>'L',
3353=>'L',
3354=>'L',
3355=>'L',
3356=>'L',
3357=>'L',
3358=>'L',
3359=>'L',
3360=>'L',
3361=>'L',
3362=>'L',
3363=>'L',
3364=>'L',
3365=>'L',
3366=>'L',
3367=>'L',
3368=>'L',
3370=>'L',
3371=>'L',
3372=>'L',
3373=>'L',
3374=>'L',
3375=>'L',
3376=>'L',
3377=>'L',
3378=>'L',
3379=>'L',
3380=>'L',
3381=>'L',
3382=>'L',
3383=>'L',
3384=>'L',
3385=>'L',
3390=>'L',
3391=>'L',
3392=>'L',
3393=>'NSM',
3394=>'NSM',
3395=>'NSM',
3398=>'L',
3399=>'L',
3400=>'L',
3402=>'L',
3403=>'L',
3404=>'L',
3405=>'NSM',
3415=>'L',
3424=>'L',
3425=>'L',
3430=>'L',
3431=>'L',
3432=>'L',
3433=>'L',
3434=>'L',
3435=>'L',
3436=>'L',
3437=>'L',
3438=>'L',
3439=>'L',
3458=>'L',
3459=>'L',
3461=>'L',
3462=>'L',
3463=>'L',
3464=>'L',
3465=>'L',
3466=>'L',
3467=>'L',
3468=>'L',
3469=>'L',
3470=>'L',
3471=>'L',
3472=>'L',
3473=>'L',
3474=>'L',
3475=>'L',
3476=>'L',
3477=>'L',
3478=>'L',
3482=>'L',
3483=>'L',
3484=>'L',
3485=>'L',
3486=>'L',
3487=>'L',
3488=>'L',
3489=>'L',
3490=>'L',
3491=>'L',
3492=>'L',
3493=>'L',
3494=>'L',
3495=>'L',
3496=>'L',
3497=>'L',
3498=>'L',
3499=>'L',
3500=>'L',
3501=>'L',
3502=>'L',
3503=>'L',
3504=>'L',
3505=>'L',
3507=>'L',
3508=>'L',
3509=>'L',
3510=>'L',
3511=>'L',
3512=>'L',
3513=>'L',
3514=>'L',
3515=>'L',
3517=>'L',
3520=>'L',
3521=>'L',
3522=>'L',
3523=>'L',
3524=>'L',
3525=>'L',
3526=>'L',
3530=>'NSM',
3535=>'L',
3536=>'L',
3537=>'L',
3538=>'NSM',
3539=>'NSM',
3540=>'NSM',
3542=>'NSM',
3544=>'L',
3545=>'L',
3546=>'L',
3547=>'L',
3548=>'L',
3549=>'L',
3550=>'L',
3551=>'L',
3570=>'L',
3571=>'L',
3572=>'L',
3585=>'L',
3586=>'L',
3587=>'L',
3588=>'L',
3589=>'L',
3590=>'L',
3591=>'L',
3592=>'L',
3593=>'L',
3594=>'L',
3595=>'L',
3596=>'L',
3597=>'L',
3598=>'L',
3599=>'L',
3600=>'L',
3601=>'L',
3602=>'L',
3603=>'L',
3604=>'L',
3605=>'L',
3606=>'L',
3607=>'L',
3608=>'L',
3609=>'L',
3610=>'L',
3611=>'L',
3612=>'L',
3613=>'L',
3614=>'L',
3615=>'L',
3616=>'L',
3617=>'L',
3618=>'L',
3619=>'L',
3620=>'L',
3621=>'L',
3622=>'L',
3623=>'L',
3624=>'L',
3625=>'L',
3626=>'L',
3627=>'L',
3628=>'L',
3629=>'L',
3630=>'L',
3631=>'L',
3632=>'L',
3633=>'NSM',
3634=>'L',
3635=>'L',
3636=>'NSM',
3637=>'NSM',
3638=>'NSM',
3639=>'NSM',
3640=>'NSM',
3641=>'NSM',
3642=>'NSM',
3647=>'ET',
3648=>'L',
3649=>'L',
3650=>'L',
3651=>'L',
3652=>'L',
3653=>'L',
3654=>'L',
3655=>'NSM',
3656=>'NSM',
3657=>'NSM',
3658=>'NSM',
3659=>'NSM',
3660=>'NSM',
3661=>'NSM',
3662=>'NSM',
3663=>'L',
3664=>'L',
3665=>'L',
3666=>'L',
3667=>'L',
3668=>'L',
3669=>'L',
3670=>'L',
3671=>'L',
3672=>'L',
3673=>'L',
3674=>'L',
3675=>'L',
3713=>'L',
3714=>'L',
3716=>'L',
3719=>'L',
3720=>'L',
3722=>'L',
3725=>'L',
3732=>'L',
3733=>'L',
3734=>'L',
3735=>'L',
3737=>'L',
3738=>'L',
3739=>'L',
3740=>'L',
3741=>'L',
3742=>'L',
3743=>'L',
3745=>'L',
3746=>'L',
3747=>'L',
3749=>'L',
3751=>'L',
3754=>'L',
3755=>'L',
3757=>'L',
3758=>'L',
3759=>'L',
3760=>'L',
3761=>'NSM',
3762=>'L',
3763=>'L',
3764=>'NSM',
3765=>'NSM',
3766=>'NSM',
3767=>'NSM',
3768=>'NSM',
3769=>'NSM',
3771=>'NSM',
3772=>'NSM',
3773=>'L',
3776=>'L',
3777=>'L',
3778=>'L',
3779=>'L',
3780=>'L',
3782=>'L',
3784=>'NSM',
3785=>'NSM',
3786=>'NSM',
3787=>'NSM',
3788=>'NSM',
3789=>'NSM',
3792=>'L',
3793=>'L',
3794=>'L',
3795=>'L',
3796=>'L',
3797=>'L',
3798=>'L',
3799=>'L',
3800=>'L',
3801=>'L',
3804=>'L',
3805=>'L',
3840=>'L',
3841=>'L',
3842=>'L',
3843=>'L',
3844=>'L',
3845=>'L',
3846=>'L',
3847=>'L',
3848=>'L',
3849=>'L',
3850=>'L',
3851=>'L',
3852=>'L',
3853=>'L',
3854=>'L',
3855=>'L',
3856=>'L',
3857=>'L',
3858=>'L',
3859=>'L',
3860=>'L',
3861=>'L',
3862=>'L',
3863=>'L',
3864=>'NSM',
3865=>'NSM',
3866=>'L',
3867=>'L',
3868=>'L',
3869=>'L',
3870=>'L',
3871=>'L',
3872=>'L',
3873=>'L',
3874=>'L',
3875=>'L',
3876=>'L',
3877=>'L',
3878=>'L',
3879=>'L',
3880=>'L',
3881=>'L',
3882=>'L',
3883=>'L',
3884=>'L',
3885=>'L',
3886=>'L',
3887=>'L',
3888=>'L',
3889=>'L',
3890=>'L',
3891=>'L',
3892=>'L',
3893=>'NSM',
3894=>'L',
3895=>'NSM',
3896=>'L',
3897=>'NSM',
3898=>'ON',
3899=>'ON',
3900=>'ON',
3901=>'ON',
3902=>'L',
3903=>'L',
3904=>'L',
3905=>'L',
3906=>'L',
3907=>'L',
3908=>'L',
3909=>'L',
3910=>'L',
3911=>'L',
3913=>'L',
3914=>'L',
3915=>'L',
3916=>'L',
3917=>'L',
3918=>'L',
3919=>'L',
3920=>'L',
3921=>'L',
3922=>'L',
3923=>'L',
3924=>'L',
3925=>'L',
3926=>'L',
3927=>'L',
3928=>'L',
3929=>'L',
3930=>'L',
3931=>'L',
3932=>'L',
3933=>'L',
3934=>'L',
3935=>'L',
3936=>'L',
3937=>'L',
3938=>'L',
3939=>'L',
3940=>'L',
3941=>'L',
3942=>'L',
3943=>'L',
3944=>'L',
3945=>'L',
3946=>'L',
3953=>'NSM',
3954=>'NSM',
3955=>'NSM',
3956=>'NSM',
3957=>'NSM',
3958=>'NSM',
3959=>'NSM',
3960=>'NSM',
3961=>'NSM',
3962=>'NSM',
3963=>'NSM',
3964=>'NSM',
3965=>'NSM',
3966=>'NSM',
3967=>'L',
3968=>'NSM',
3969=>'NSM',
3970=>'NSM',
3971=>'NSM',
3972=>'NSM',
3973=>'L',
3974=>'NSM',
3975=>'NSM',
3976=>'L',
3977=>'L',
3978=>'L',
3979=>'L',
3984=>'NSM',
3985=>'NSM',
3986=>'NSM',
3987=>'NSM',
3988=>'NSM',
3989=>'NSM',
3990=>'NSM',
3991=>'NSM',
3993=>'NSM',
3994=>'NSM',
3995=>'NSM',
3996=>'NSM',
3997=>'NSM',
3998=>'NSM',
3999=>'NSM',
4000=>'NSM',
4001=>'NSM',
4002=>'NSM',
4003=>'NSM',
4004=>'NSM',
4005=>'NSM',
4006=>'NSM',
4007=>'NSM',
4008=>'NSM',
4009=>'NSM',
4010=>'NSM',
4011=>'NSM',
4012=>'NSM',
4013=>'NSM',
4014=>'NSM',
4015=>'NSM',
4016=>'NSM',
4017=>'NSM',
4018=>'NSM',
4019=>'NSM',
4020=>'NSM',
4021=>'NSM',
4022=>'NSM',
4023=>'NSM',
4024=>'NSM',
4025=>'NSM',
4026=>'NSM',
4027=>'NSM',
4028=>'NSM',
4030=>'L',
4031=>'L',
4032=>'L',
4033=>'L',
4034=>'L',
4035=>'L',
4036=>'L',
4037=>'L',
4038=>'NSM',
4039=>'L',
4040=>'L',
4041=>'L',
4042=>'L',
4043=>'L',
4044=>'L',
4047=>'L',
4048=>'L',
4049=>'L',
4096=>'L',
4097=>'L',
4098=>'L',
4099=>'L',
4100=>'L',
4101=>'L',
4102=>'L',
4103=>'L',
4104=>'L',
4105=>'L',
4106=>'L',
4107=>'L',
4108=>'L',
4109=>'L',
4110=>'L',
4111=>'L',
4112=>'L',
4113=>'L',
4114=>'L',
4115=>'L',
4116=>'L',
4117=>'L',
4118=>'L',
4119=>'L',
4120=>'L',
4121=>'L',
4122=>'L',
4123=>'L',
4124=>'L',
4125=>'L',
4126=>'L',
4127=>'L',
4128=>'L',
4129=>'L',
4131=>'L',
4132=>'L',
4133=>'L',
4134=>'L',
4135=>'L',
4137=>'L',
4138=>'L',
4140=>'L',
4141=>'NSM',
4142=>'NSM',
4143=>'NSM',
4144=>'NSM',
4145=>'L',
4146=>'NSM',
4150=>'NSM',
4151=>'NSM',
4152=>'L',
4153=>'NSM',
4160=>'L',
4161=>'L',
4162=>'L',
4163=>'L',
4164=>'L',
4165=>'L',
4166=>'L',
4167=>'L',
4168=>'L',
4169=>'L',
4170=>'L',
4171=>'L',
4172=>'L',
4173=>'L',
4174=>'L',
4175=>'L',
4176=>'L',
4177=>'L',
4178=>'L',
4179=>'L',
4180=>'L',
4181=>'L',
4182=>'L',
4183=>'L',
4184=>'NSM',
4185=>'NSM',
4256=>'L',
4257=>'L',
4258=>'L',
4259=>'L',
4260=>'L',
4261=>'L',
4262=>'L',
4263=>'L',
4264=>'L',
4265=>'L',
4266=>'L',
4267=>'L',
4268=>'L',
4269=>'L',
4270=>'L',
4271=>'L',
4272=>'L',
4273=>'L',
4274=>'L',
4275=>'L',
4276=>'L',
4277=>'L',
4278=>'L',
4279=>'L',
4280=>'L',
4281=>'L',
4282=>'L',
4283=>'L',
4284=>'L',
4285=>'L',
4286=>'L',
4287=>'L',
4288=>'L',
4289=>'L',
4290=>'L',
4291=>'L',
4292=>'L',
4293=>'L',
4304=>'L',
4305=>'L',
4306=>'L',
4307=>'L',
4308=>'L',
4309=>'L',
4310=>'L',
4311=>'L',
4312=>'L',
4313=>'L',
4314=>'L',
4315=>'L',
4316=>'L',
4317=>'L',
4318=>'L',
4319=>'L',
4320=>'L',
4321=>'L',
4322=>'L',
4323=>'L',
4324=>'L',
4325=>'L',
4326=>'L',
4327=>'L',
4328=>'L',
4329=>'L',
4330=>'L',
4331=>'L',
4332=>'L',
4333=>'L',
4334=>'L',
4335=>'L',
4336=>'L',
4337=>'L',
4338=>'L',
4339=>'L',
4340=>'L',
4341=>'L',
4342=>'L',
4343=>'L',
4344=>'L',
4345=>'L',
4346=>'L',
4347=>'L',
4348=>'L',
4352=>'L',
4353=>'L',
4354=>'L',
4355=>'L',
4356=>'L',
4357=>'L',
4358=>'L',
4359=>'L',
4360=>'L',
4361=>'L',
4362=>'L',
4363=>'L',
4364=>'L',
4365=>'L',
4366=>'L',
4367=>'L',
4368=>'L',
4369=>'L',
4370=>'L',
4371=>'L',
4372=>'L',
4373=>'L',
4374=>'L',
4375=>'L',
4376=>'L',
4377=>'L',
4378=>'L',
4379=>'L',
4380=>'L',
4381=>'L',
4382=>'L',
4383=>'L',
4384=>'L',
4385=>'L',
4386=>'L',
4387=>'L',
4388=>'L',
4389=>'L',
4390=>'L',
4391=>'L',
4392=>'L',
4393=>'L',
4394=>'L',
4395=>'L',
4396=>'L',
4397=>'L',
4398=>'L',
4399=>'L',
4400=>'L',
4401=>'L',
4402=>'L',
4403=>'L',
4404=>'L',
4405=>'L',
4406=>'L',
4407=>'L',
4408=>'L',
4409=>'L',
4410=>'L',
4411=>'L',
4412=>'L',
4413=>'L',
4414=>'L',
4415=>'L',
4416=>'L',
4417=>'L',
4418=>'L',
4419=>'L',
4420=>'L',
4421=>'L',
4422=>'L',
4423=>'L',
4424=>'L',
4425=>'L',
4426=>'L',
4427=>'L',
4428=>'L',
4429=>'L',
4430=>'L',
4431=>'L',
4432=>'L',
4433=>'L',
4434=>'L',
4435=>'L',
4436=>'L',
4437=>'L',
4438=>'L',
4439=>'L',
4440=>'L',
4441=>'L',
4447=>'L',
4448=>'L',
4449=>'L',
4450=>'L',
4451=>'L',
4452=>'L',
4453=>'L',
4454=>'L',
4455=>'L',
4456=>'L',
4457=>'L',
4458=>'L',
4459=>'L',
4460=>'L',
4461=>'L',
4462=>'L',
4463=>'L',
4464=>'L',
4465=>'L',
4466=>'L',
4467=>'L',
4468=>'L',
4469=>'L',
4470=>'L',
4471=>'L',
4472=>'L',
4473=>'L',
4474=>'L',
4475=>'L',
4476=>'L',
4477=>'L',
4478=>'L',
4479=>'L',
4480=>'L',
4481=>'L',
4482=>'L',
4483=>'L',
4484=>'L',
4485=>'L',
4486=>'L',
4487=>'L',
4488=>'L',
4489=>'L',
4490=>'L',
4491=>'L',
4492=>'L',
4493=>'L',
4494=>'L',
4495=>'L',
4496=>'L',
4497=>'L',
4498=>'L',
4499=>'L',
4500=>'L',
4501=>'L',
4502=>'L',
4503=>'L',
4504=>'L',
4505=>'L',
4506=>'L',
4507=>'L',
4508=>'L',
4509=>'L',
4510=>'L',
4511=>'L',
4512=>'L',
4513=>'L',
4514=>'L',
4520=>'L',
4521=>'L',
4522=>'L',
4523=>'L',
4524=>'L',
4525=>'L',
4526=>'L',
4527=>'L',
4528=>'L',
4529=>'L',
4530=>'L',
4531=>'L',
4532=>'L',
4533=>'L',
4534=>'L',
4535=>'L',
4536=>'L',
4537=>'L',
4538=>'L',
4539=>'L',
4540=>'L',
4541=>'L',
4542=>'L',
4543=>'L',
4544=>'L',
4545=>'L',
4546=>'L',
4547=>'L',
4548=>'L',
4549=>'L',
4550=>'L',
4551=>'L',
4552=>'L',
4553=>'L',
4554=>'L',
4555=>'L',
4556=>'L',
4557=>'L',
4558=>'L',
4559=>'L',
4560=>'L',
4561=>'L',
4562=>'L',
4563=>'L',
4564=>'L',
4565=>'L',
4566=>'L',
4567=>'L',
4568=>'L',
4569=>'L',
4570=>'L',
4571=>'L',
4572=>'L',
4573=>'L',
4574=>'L',
4575=>'L',
4576=>'L',
4577=>'L',
4578=>'L',
4579=>'L',
4580=>'L',
4581=>'L',
4582=>'L',
4583=>'L',
4584=>'L',
4585=>'L',
4586=>'L',
4587=>'L',
4588=>'L',
4589=>'L',
4590=>'L',
4591=>'L',
4592=>'L',
4593=>'L',
4594=>'L',
4595=>'L',
4596=>'L',
4597=>'L',
4598=>'L',
4599=>'L',
4600=>'L',
4601=>'L',
4608=>'L',
4609=>'L',
4610=>'L',
4611=>'L',
4612=>'L',
4613=>'L',
4614=>'L',
4615=>'L',
4616=>'L',
4617=>'L',
4618=>'L',
4619=>'L',
4620=>'L',
4621=>'L',
4622=>'L',
4623=>'L',
4624=>'L',
4625=>'L',
4626=>'L',
4627=>'L',
4628=>'L',
4629=>'L',
4630=>'L',
4631=>'L',
4632=>'L',
4633=>'L',
4634=>'L',
4635=>'L',
4636=>'L',
4637=>'L',
4638=>'L',
4639=>'L',
4640=>'L',
4641=>'L',
4642=>'L',
4643=>'L',
4644=>'L',
4645=>'L',
4646=>'L',
4647=>'L',
4648=>'L',
4649=>'L',
4650=>'L',
4651=>'L',
4652=>'L',
4653=>'L',
4654=>'L',
4655=>'L',
4656=>'L',
4657=>'L',
4658=>'L',
4659=>'L',
4660=>'L',
4661=>'L',
4662=>'L',
4663=>'L',
4664=>'L',
4665=>'L',
4666=>'L',
4667=>'L',
4668=>'L',
4669=>'L',
4670=>'L',
4671=>'L',
4672=>'L',
4673=>'L',
4674=>'L',
4675=>'L',
4676=>'L',
4677=>'L',
4678=>'L',
4679=>'L',
4680=>'L',
4682=>'L',
4683=>'L',
4684=>'L',
4685=>'L',
4688=>'L',
4689=>'L',
4690=>'L',
4691=>'L',
4692=>'L',
4693=>'L',
4694=>'L',
4696=>'L',
4698=>'L',
4699=>'L',
4700=>'L',
4701=>'L',
4704=>'L',
4705=>'L',
4706=>'L',
4707=>'L',
4708=>'L',
4709=>'L',
4710=>'L',
4711=>'L',
4712=>'L',
4713=>'L',
4714=>'L',
4715=>'L',
4716=>'L',
4717=>'L',
4718=>'L',
4719=>'L',
4720=>'L',
4721=>'L',
4722=>'L',
4723=>'L',
4724=>'L',
4725=>'L',
4726=>'L',
4727=>'L',
4728=>'L',
4729=>'L',
4730=>'L',
4731=>'L',
4732=>'L',
4733=>'L',
4734=>'L',
4735=>'L',
4736=>'L',
4737=>'L',
4738=>'L',
4739=>'L',
4740=>'L',
4741=>'L',
4742=>'L',
4743=>'L',
4744=>'L',
4746=>'L',
4747=>'L',
4748=>'L',
4749=>'L',
4752=>'L',
4753=>'L',
4754=>'L',
4755=>'L',
4756=>'L',
4757=>'L',
4758=>'L',
4759=>'L',
4760=>'L',
4761=>'L',
4762=>'L',
4763=>'L',
4764=>'L',
4765=>'L',
4766=>'L',
4767=>'L',
4768=>'L',
4769=>'L',
4770=>'L',
4771=>'L',
4772=>'L',
4773=>'L',
4774=>'L',
4775=>'L',
4776=>'L',
4777=>'L',
4778=>'L',
4779=>'L',
4780=>'L',
4781=>'L',
4782=>'L',
4783=>'L',
4784=>'L',
4786=>'L',
4787=>'L',
4788=>'L',
4789=>'L',
4792=>'L',
4793=>'L',
4794=>'L',
4795=>'L',
4796=>'L',
4797=>'L',
4798=>'L',
4800=>'L',
4802=>'L',
4803=>'L',
4804=>'L',
4805=>'L',
4808=>'L',
4809=>'L',
4810=>'L',
4811=>'L',
4812=>'L',
4813=>'L',
4814=>'L',
4815=>'L',
4816=>'L',
4817=>'L',
4818=>'L',
4819=>'L',
4820=>'L',
4821=>'L',
4822=>'L',
4824=>'L',
4825=>'L',
4826=>'L',
4827=>'L',
4828=>'L',
4829=>'L',
4830=>'L',
4831=>'L',
4832=>'L',
4833=>'L',
4834=>'L',
4835=>'L',
4836=>'L',
4837=>'L',
4838=>'L',
4839=>'L',
4840=>'L',
4841=>'L',
4842=>'L',
4843=>'L',
4844=>'L',
4845=>'L',
4846=>'L',
4847=>'L',
4848=>'L',
4849=>'L',
4850=>'L',
4851=>'L',
4852=>'L',
4853=>'L',
4854=>'L',
4855=>'L',
4856=>'L',
4857=>'L',
4858=>'L',
4859=>'L',
4860=>'L',
4861=>'L',
4862=>'L',
4863=>'L',
4864=>'L',
4865=>'L',
4866=>'L',
4867=>'L',
4868=>'L',
4869=>'L',
4870=>'L',
4871=>'L',
4872=>'L',
4873=>'L',
4874=>'L',
4875=>'L',
4876=>'L',
4877=>'L',
4878=>'L',
4879=>'L',
4880=>'L',
4882=>'L',
4883=>'L',
4884=>'L',
4885=>'L',
4888=>'L',
4889=>'L',
4890=>'L',
4891=>'L',
4892=>'L',
4893=>'L',
4894=>'L',
4895=>'L',
4896=>'L',
4897=>'L',
4898=>'L',
4899=>'L',
4900=>'L',
4901=>'L',
4902=>'L',
4903=>'L',
4904=>'L',
4905=>'L',
4906=>'L',
4907=>'L',
4908=>'L',
4909=>'L',
4910=>'L',
4911=>'L',
4912=>'L',
4913=>'L',
4914=>'L',
4915=>'L',
4916=>'L',
4917=>'L',
4918=>'L',
4919=>'L',
4920=>'L',
4921=>'L',
4922=>'L',
4923=>'L',
4924=>'L',
4925=>'L',
4926=>'L',
4927=>'L',
4928=>'L',
4929=>'L',
4930=>'L',
4931=>'L',
4932=>'L',
4933=>'L',
4934=>'L',
4935=>'L',
4936=>'L',
4937=>'L',
4938=>'L',
4939=>'L',
4940=>'L',
4941=>'L',
4942=>'L',
4943=>'L',
4944=>'L',
4945=>'L',
4946=>'L',
4947=>'L',
4948=>'L',
4949=>'L',
4950=>'L',
4951=>'L',
4952=>'L',
4953=>'L',
4954=>'L',
4959=>'NSM',
4960=>'L',
4961=>'L',
4962=>'L',
4963=>'L',
4964=>'L',
4965=>'L',
4966=>'L',
4967=>'L',
4968=>'L',
4969=>'L',
4970=>'L',
4971=>'L',
4972=>'L',
4973=>'L',
4974=>'L',
4975=>'L',
4976=>'L',
4977=>'L',
4978=>'L',
4979=>'L',
4980=>'L',
4981=>'L',
4982=>'L',
4983=>'L',
4984=>'L',
4985=>'L',
4986=>'L',
4987=>'L',
4988=>'L',
4992=>'L',
4993=>'L',
4994=>'L',
4995=>'L',
4996=>'L',
4997=>'L',
4998=>'L',
4999=>'L',
5000=>'L',
5001=>'L',
5002=>'L',
5003=>'L',
5004=>'L',
5005=>'L',
5006=>'L',
5007=>'L',
5008=>'ON',
5009=>'ON',
5010=>'ON',
5011=>'ON',
5012=>'ON',
5013=>'ON',
5014=>'ON',
5015=>'ON',
5016=>'ON',
5017=>'ON',
5024=>'L',
5025=>'L',
5026=>'L',
5027=>'L',
5028=>'L',
5029=>'L',
5030=>'L',
5031=>'L',
5032=>'L',
5033=>'L',
5034=>'L',
5035=>'L',
5036=>'L',
5037=>'L',
5038=>'L',
5039=>'L',
5040=>'L',
5041=>'L',
5042=>'L',
5043=>'L',
5044=>'L',
5045=>'L',
5046=>'L',
5047=>'L',
5048=>'L',
5049=>'L',
5050=>'L',
5051=>'L',
5052=>'L',
5053=>'L',
5054=>'L',
5055=>'L',
5056=>'L',
5057=>'L',
5058=>'L',
5059=>'L',
5060=>'L',
5061=>'L',
5062=>'L',
5063=>'L',
5064=>'L',
5065=>'L',
5066=>'L',
5067=>'L',
5068=>'L',
5069=>'L',
5070=>'L',
5071=>'L',
5072=>'L',
5073=>'L',
5074=>'L',
5075=>'L',
5076=>'L',
5077=>'L',
5078=>'L',
5079=>'L',
5080=>'L',
5081=>'L',
5082=>'L',
5083=>'L',
5084=>'L',
5085=>'L',
5086=>'L',
5087=>'L',
5088=>'L',
5089=>'L',
5090=>'L',
5091=>'L',
5092=>'L',
5093=>'L',
5094=>'L',
5095=>'L',
5096=>'L',
5097=>'L',
5098=>'L',
5099=>'L',
5100=>'L',
5101=>'L',
5102=>'L',
5103=>'L',
5104=>'L',
5105=>'L',
5106=>'L',
5107=>'L',
5108=>'L',
5121=>'L',
5122=>'L',
5123=>'L',
5124=>'L',
5125=>'L',
5126=>'L',
5127=>'L',
5128=>'L',
5129=>'L',
5130=>'L',
5131=>'L',
5132=>'L',
5133=>'L',
5134=>'L',
5135=>'L',
5136=>'L',
5137=>'L',
5138=>'L',
5139=>'L',
5140=>'L',
5141=>'L',
5142=>'L',
5143=>'L',
5144=>'L',
5145=>'L',
5146=>'L',
5147=>'L',
5148=>'L',
5149=>'L',
5150=>'L',
5151=>'L',
5152=>'L',
5153=>'L',
5154=>'L',
5155=>'L',
5156=>'L',
5157=>'L',
5158=>'L',
5159=>'L',
5160=>'L',
5161=>'L',
5162=>'L',
5163=>'L',
5164=>'L',
5165=>'L',
5166=>'L',
5167=>'L',
5168=>'L',
5169=>'L',
5170=>'L',
5171=>'L',
5172=>'L',
5173=>'L',
5174=>'L',
5175=>'L',
5176=>'L',
5177=>'L',
5178=>'L',
5179=>'L',
5180=>'L',
5181=>'L',
5182=>'L',
5183=>'L',
5184=>'L',
5185=>'L',
5186=>'L',
5187=>'L',
5188=>'L',
5189=>'L',
5190=>'L',
5191=>'L',
5192=>'L',
5193=>'L',
5194=>'L',
5195=>'L',
5196=>'L',
5197=>'L',
5198=>'L',
5199=>'L',
5200=>'L',
5201=>'L',
5202=>'L',
5203=>'L',
5204=>'L',
5205=>'L',
5206=>'L',
5207=>'L',
5208=>'L',
5209=>'L',
5210=>'L',
5211=>'L',
5212=>'L',
5213=>'L',
5214=>'L',
5215=>'L',
5216=>'L',
5217=>'L',
5218=>'L',
5219=>'L',
5220=>'L',
5221=>'L',
5222=>'L',
5223=>'L',
5224=>'L',
5225=>'L',
5226=>'L',
5227=>'L',
5228=>'L',
5229=>'L',
5230=>'L',
5231=>'L',
5232=>'L',
5233=>'L',
5234=>'L',
5235=>'L',
5236=>'L',
5237=>'L',
5238=>'L',
5239=>'L',
5240=>'L',
5241=>'L',
5242=>'L',
5243=>'L',
5244=>'L',
5245=>'L',
5246=>'L',
5247=>'L',
5248=>'L',
5249=>'L',
5250=>'L',
5251=>'L',
5252=>'L',
5253=>'L',
5254=>'L',
5255=>'L',
5256=>'L',
5257=>'L',
5258=>'L',
5259=>'L',
5260=>'L',
5261=>'L',
5262=>'L',
5263=>'L',
5264=>'L',
5265=>'L',
5266=>'L',
5267=>'L',
5268=>'L',
5269=>'L',
5270=>'L',
5271=>'L',
5272=>'L',
5273=>'L',
5274=>'L',
5275=>'L',
5276=>'L',
5277=>'L',
5278=>'L',
5279=>'L',
5280=>'L',
5281=>'L',
5282=>'L',
5283=>'L',
5284=>'L',
5285=>'L',
5286=>'L',
5287=>'L',
5288=>'L',
5289=>'L',
5290=>'L',
5291=>'L',
5292=>'L',
5293=>'L',
5294=>'L',
5295=>'L',
5296=>'L',
5297=>'L',
5298=>'L',
5299=>'L',
5300=>'L',
5301=>'L',
5302=>'L',
5303=>'L',
5304=>'L',
5305=>'L',
5306=>'L',
5307=>'L',
5308=>'L',
5309=>'L',
5310=>'L',
5311=>'L',
5312=>'L',
5313=>'L',
5314=>'L',
5315=>'L',
5316=>'L',
5317=>'L',
5318=>'L',
5319=>'L',
5320=>'L',
5321=>'L',
5322=>'L',
5323=>'L',
5324=>'L',
5325=>'L',
5326=>'L',
5327=>'L',
5328=>'L',
5329=>'L',
5330=>'L',
5331=>'L',
5332=>'L',
5333=>'L',
5334=>'L',
5335=>'L',
5336=>'L',
5337=>'L',
5338=>'L',
5339=>'L',
5340=>'L',
5341=>'L',
5342=>'L',
5343=>'L',
5344=>'L',
5345=>'L',
5346=>'L',
5347=>'L',
5348=>'L',
5349=>'L',
5350=>'L',
5351=>'L',
5352=>'L',
5353=>'L',
5354=>'L',
5355=>'L',
5356=>'L',
5357=>'L',
5358=>'L',
5359=>'L',
5360=>'L',
5361=>'L',
5362=>'L',
5363=>'L',
5364=>'L',
5365=>'L',
5366=>'L',
5367=>'L',
5368=>'L',
5369=>'L',
5370=>'L',
5371=>'L',
5372=>'L',
5373=>'L',
5374=>'L',
5375=>'L',
5376=>'L',
5377=>'L',
5378=>'L',
5379=>'L',
5380=>'L',
5381=>'L',
5382=>'L',
5383=>'L',
5384=>'L',
5385=>'L',
5386=>'L',
5387=>'L',
5388=>'L',
5389=>'L',
5390=>'L',
5391=>'L',
5392=>'L',
5393=>'L',
5394=>'L',
5395=>'L',
5396=>'L',
5397=>'L',
5398=>'L',
5399=>'L',
5400=>'L',
5401=>'L',
5402=>'L',
5403=>'L',
5404=>'L',
5405=>'L',
5406=>'L',
5407=>'L',
5408=>'L',
5409=>'L',
5410=>'L',
5411=>'L',
5412=>'L',
5413=>'L',
5414=>'L',
5415=>'L',
5416=>'L',
5417=>'L',
5418=>'L',
5419=>'L',
5420=>'L',
5421=>'L',
5422=>'L',
5423=>'L',
5424=>'L',
5425=>'L',
5426=>'L',
5427=>'L',
5428=>'L',
5429=>'L',
5430=>'L',
5431=>'L',
5432=>'L',
5433=>'L',
5434=>'L',
5435=>'L',
5436=>'L',
5437=>'L',
5438=>'L',
5439=>'L',
5440=>'L',
5441=>'L',
5442=>'L',
5443=>'L',
5444=>'L',
5445=>'L',
5446=>'L',
5447=>'L',
5448=>'L',
5449=>'L',
5450=>'L',
5451=>'L',
5452=>'L',
5453=>'L',
5454=>'L',
5455=>'L',
5456=>'L',
5457=>'L',
5458=>'L',
5459=>'L',
5460=>'L',
5461=>'L',
5462=>'L',
5463=>'L',
5464=>'L',
5465=>'L',
5466=>'L',
5467=>'L',
5468=>'L',
5469=>'L',
5470=>'L',
5471=>'L',
5472=>'L',
5473=>'L',
5474=>'L',
5475=>'L',
5476=>'L',
5477=>'L',
5478=>'L',
5479=>'L',
5480=>'L',
5481=>'L',
5482=>'L',
5483=>'L',
5484=>'L',
5485=>'L',
5486=>'L',
5487=>'L',
5488=>'L',
5489=>'L',
5490=>'L',
5491=>'L',
5492=>'L',
5493=>'L',
5494=>'L',
5495=>'L',
5496=>'L',
5497=>'L',
5498=>'L',
5499=>'L',
5500=>'L',
5501=>'L',
5502=>'L',
5503=>'L',
5504=>'L',
5505=>'L',
5506=>'L',
5507=>'L',
5508=>'L',
5509=>'L',
5510=>'L',
5511=>'L',
5512=>'L',
5513=>'L',
5514=>'L',
5515=>'L',
5516=>'L',
5517=>'L',
5518=>'L',
5519=>'L',
5520=>'L',
5521=>'L',
5522=>'L',
5523=>'L',
5524=>'L',
5525=>'L',
5526=>'L',
5527=>'L',
5528=>'L',
5529=>'L',
5530=>'L',
5531=>'L',
5532=>'L',
5533=>'L',
5534=>'L',
5535=>'L',
5536=>'L',
5537=>'L',
5538=>'L',
5539=>'L',
5540=>'L',
5541=>'L',
5542=>'L',
5543=>'L',
5544=>'L',
5545=>'L',
5546=>'L',
5547=>'L',
5548=>'L',
5549=>'L',
5550=>'L',
5551=>'L',
5552=>'L',
5553=>'L',
5554=>'L',
5555=>'L',
5556=>'L',
5557=>'L',
5558=>'L',
5559=>'L',
5560=>'L',
5561=>'L',
5562=>'L',
5563=>'L',
5564=>'L',
5565=>'L',
5566=>'L',
5567=>'L',
5568=>'L',
5569=>'L',
5570=>'L',
5571=>'L',
5572=>'L',
5573=>'L',
5574=>'L',
5575=>'L',
5576=>'L',
5577=>'L',
5578=>'L',
5579=>'L',
5580=>'L',
5581=>'L',
5582=>'L',
5583=>'L',
5584=>'L',
5585=>'L',
5586=>'L',
5587=>'L',
5588=>'L',
5589=>'L',
5590=>'L',
5591=>'L',
5592=>'L',
5593=>'L',
5594=>'L',
5595=>'L',
5596=>'L',
5597=>'L',
5598=>'L',
5599=>'L',
5600=>'L',
5601=>'L',
5602=>'L',
5603=>'L',
5604=>'L',
5605=>'L',
5606=>'L',
5607=>'L',
5608=>'L',
5609=>'L',
5610=>'L',
5611=>'L',
5612=>'L',
5613=>'L',
5614=>'L',
5615=>'L',
5616=>'L',
5617=>'L',
5618=>'L',
5619=>'L',
5620=>'L',
5621=>'L',
5622=>'L',
5623=>'L',
5624=>'L',
5625=>'L',
5626=>'L',
5627=>'L',
5628=>'L',
5629=>'L',
5630=>'L',
5631=>'L',
5632=>'L',
5633=>'L',
5634=>'L',
5635=>'L',
5636=>'L',
5637=>'L',
5638=>'L',
5639=>'L',
5640=>'L',
5641=>'L',
5642=>'L',
5643=>'L',
5644=>'L',
5645=>'L',
5646=>'L',
5647=>'L',
5648=>'L',
5649=>'L',
5650=>'L',
5651=>'L',
5652=>'L',
5653=>'L',
5654=>'L',
5655=>'L',
5656=>'L',
5657=>'L',
5658=>'L',
5659=>'L',
5660=>'L',
5661=>'L',
5662=>'L',
5663=>'L',
5664=>'L',
5665=>'L',
5666=>'L',
5667=>'L',
5668=>'L',
5669=>'L',
5670=>'L',
5671=>'L',
5672=>'L',
5673=>'L',
5674=>'L',
5675=>'L',
5676=>'L',
5677=>'L',
5678=>'L',
5679=>'L',
5680=>'L',
5681=>'L',
5682=>'L',
5683=>'L',
5684=>'L',
5685=>'L',
5686=>'L',
5687=>'L',
5688=>'L',
5689=>'L',
5690=>'L',
5691=>'L',
5692=>'L',
5693=>'L',
5694=>'L',
5695=>'L',
5696=>'L',
5697=>'L',
5698=>'L',
5699=>'L',
5700=>'L',
5701=>'L',
5702=>'L',
5703=>'L',
5704=>'L',
5705=>'L',
5706=>'L',
5707=>'L',
5708=>'L',
5709=>'L',
5710=>'L',
5711=>'L',
5712=>'L',
5713=>'L',
5714=>'L',
5715=>'L',
5716=>'L',
5717=>'L',
5718=>'L',
5719=>'L',
5720=>'L',
5721=>'L',
5722=>'L',
5723=>'L',
5724=>'L',
5725=>'L',
5726=>'L',
5727=>'L',
5728=>'L',
5729=>'L',
5730=>'L',
5731=>'L',
5732=>'L',
5733=>'L',
5734=>'L',
5735=>'L',
5736=>'L',
5737=>'L',
5738=>'L',
5739=>'L',
5740=>'L',
5741=>'L',
5742=>'L',
5743=>'L',
5744=>'L',
5745=>'L',
5746=>'L',
5747=>'L',
5748=>'L',
5749=>'L',
5750=>'L',
5760=>'WS',
5761=>'L',
5762=>'L',
5763=>'L',
5764=>'L',
5765=>'L',
5766=>'L',
5767=>'L',
5768=>'L',
5769=>'L',
5770=>'L',
5771=>'L',
5772=>'L',
5773=>'L',
5774=>'L',
5775=>'L',
5776=>'L',
5777=>'L',
5778=>'L',
5779=>'L',
5780=>'L',
5781=>'L',
5782=>'L',
5783=>'L',
5784=>'L',
5785=>'L',
5786=>'L',
5787=>'ON',
5788=>'ON',
5792=>'L',
5793=>'L',
5794=>'L',
5795=>'L',
5796=>'L',
5797=>'L',
5798=>'L',
5799=>'L',
5800=>'L',
5801=>'L',
5802=>'L',
5803=>'L',
5804=>'L',
5805=>'L',
5806=>'L',
5807=>'L',
5808=>'L',
5809=>'L',
5810=>'L',
5811=>'L',
5812=>'L',
5813=>'L',
5814=>'L',
5815=>'L',
5816=>'L',
5817=>'L',
5818=>'L',
5819=>'L',
5820=>'L',
5821=>'L',
5822=>'L',
5823=>'L',
5824=>'L',
5825=>'L',
5826=>'L',
5827=>'L',
5828=>'L',
5829=>'L',
5830=>'L',
5831=>'L',
5832=>'L',
5833=>'L',
5834=>'L',
5835=>'L',
5836=>'L',
5837=>'L',
5838=>'L',
5839=>'L',
5840=>'L',
5841=>'L',
5842=>'L',
5843=>'L',
5844=>'L',
5845=>'L',
5846=>'L',
5847=>'L',
5848=>'L',
5849=>'L',
5850=>'L',
5851=>'L',
5852=>'L',
5853=>'L',
5854=>'L',
5855=>'L',
5856=>'L',
5857=>'L',
5858=>'L',
5859=>'L',
5860=>'L',
5861=>'L',
5862=>'L',
5863=>'L',
5864=>'L',
5865=>'L',
5866=>'L',
5867=>'L',
5868=>'L',
5869=>'L',
5870=>'L',
5871=>'L',
5872=>'L',
5888=>'L',
5889=>'L',
5890=>'L',
5891=>'L',
5892=>'L',
5893=>'L',
5894=>'L',
5895=>'L',
5896=>'L',
5897=>'L',
5898=>'L',
5899=>'L',
5900=>'L',
5902=>'L',
5903=>'L',
5904=>'L',
5905=>'L',
5906=>'NSM',
5907=>'NSM',
5908=>'NSM',
5920=>'L',
5921=>'L',
5922=>'L',
5923=>'L',
5924=>'L',
5925=>'L',
5926=>'L',
5927=>'L',
5928=>'L',
5929=>'L',
5930=>'L',
5931=>'L',
5932=>'L',
5933=>'L',
5934=>'L',
5935=>'L',
5936=>'L',
5937=>'L',
5938=>'NSM',
5939=>'NSM',
5940=>'NSM',
5941=>'L',
5942=>'L',
5952=>'L',
5953=>'L',
5954=>'L',
5955=>'L',
5956=>'L',
5957=>'L',
5958=>'L',
5959=>'L',
5960=>'L',
5961=>'L',
5962=>'L',
5963=>'L',
5964=>'L',
5965=>'L',
5966=>'L',
5967=>'L',
5968=>'L',
5969=>'L',
5970=>'NSM',
5971=>'NSM',
5984=>'L',
5985=>'L',
5986=>'L',
5987=>'L',
5988=>'L',
5989=>'L',
5990=>'L',
5991=>'L',
5992=>'L',
5993=>'L',
5994=>'L',
5995=>'L',
5996=>'L',
5998=>'L',
5999=>'L',
6000=>'L',
6002=>'NSM',
6003=>'NSM',
6016=>'L',
6017=>'L',
6018=>'L',
6019=>'L',
6020=>'L',
6021=>'L',
6022=>'L',
6023=>'L',
6024=>'L',
6025=>'L',
6026=>'L',
6027=>'L',
6028=>'L',
6029=>'L',
6030=>'L',
6031=>'L',
6032=>'L',
6033=>'L',
6034=>'L',
6035=>'L',
6036=>'L',
6037=>'L',
6038=>'L',
6039=>'L',
6040=>'L',
6041=>'L',
6042=>'L',
6043=>'L',
6044=>'L',
6045=>'L',
6046=>'L',
6047=>'L',
6048=>'L',
6049=>'L',
6050=>'L',
6051=>'L',
6052=>'L',
6053=>'L',
6054=>'L',
6055=>'L',
6056=>'L',
6057=>'L',
6058=>'L',
6059=>'L',
6060=>'L',
6061=>'L',
6062=>'L',
6063=>'L',
6064=>'L',
6065=>'L',
6066=>'L',
6067=>'L',
6068=>'L',
6069=>'L',
6070=>'L',
6071=>'NSM',
6072=>'NSM',
6073=>'NSM',
6074=>'NSM',
6075=>'NSM',
6076=>'NSM',
6077=>'NSM',
6078=>'L',
6079=>'L',
6080=>'L',
6081=>'L',
6082=>'L',
6083=>'L',
6084=>'L',
6085=>'L',
6086=>'NSM',
6087=>'L',
6088=>'L',
6089=>'NSM',
6090=>'NSM',
6091=>'NSM',
6092=>'NSM',
6093=>'NSM',
6094=>'NSM',
6095=>'NSM',
6096=>'NSM',
6097=>'NSM',
6098=>'NSM',
6099=>'NSM',
6100=>'L',
6101=>'L',
6102=>'L',
6103=>'L',
6104=>'L',
6105=>'L',
6106=>'L',
6107=>'ET',
6108=>'L',
6109=>'NSM',
6112=>'L',
6113=>'L',
6114=>'L',
6115=>'L',
6116=>'L',
6117=>'L',
6118=>'L',
6119=>'L',
6120=>'L',
6121=>'L',
6128=>'ON',
6129=>'ON',
6130=>'ON',
6131=>'ON',
6132=>'ON',
6133=>'ON',
6134=>'ON',
6135=>'ON',
6136=>'ON',
6137=>'ON',
6144=>'ON',
6145=>'ON',
6146=>'ON',
6147=>'ON',
6148=>'ON',
6149=>'ON',
6150=>'ON',
6151=>'ON',
6152=>'ON',
6153=>'ON',
6154=>'ON',
6155=>'NSM',
6156=>'NSM',
6157=>'NSM',
6158=>'WS',
6160=>'L',
6161=>'L',
6162=>'L',
6163=>'L',
6164=>'L',
6165=>'L',
6166=>'L',
6167=>'L',
6168=>'L',
6169=>'L',
6176=>'L',
6177=>'L',
6178=>'L',
6179=>'L',
6180=>'L',
6181=>'L',
6182=>'L',
6183=>'L',
6184=>'L',
6185=>'L',
6186=>'L',
6187=>'L',
6188=>'L',
6189=>'L',
6190=>'L',
6191=>'L',
6192=>'L',
6193=>'L',
6194=>'L',
6195=>'L',
6196=>'L',
6197=>'L',
6198=>'L',
6199=>'L',
6200=>'L',
6201=>'L',
6202=>'L',
6203=>'L',
6204=>'L',
6205=>'L',
6206=>'L',
6207=>'L',
6208=>'L',
6209=>'L',
6210=>'L',
6211=>'L',
6212=>'L',
6213=>'L',
6214=>'L',
6215=>'L',
6216=>'L',
6217=>'L',
6218=>'L',
6219=>'L',
6220=>'L',
6221=>'L',
6222=>'L',
6223=>'L',
6224=>'L',
6225=>'L',
6226=>'L',
6227=>'L',
6228=>'L',
6229=>'L',
6230=>'L',
6231=>'L',
6232=>'L',
6233=>'L',
6234=>'L',
6235=>'L',
6236=>'L',
6237=>'L',
6238=>'L',
6239=>'L',
6240=>'L',
6241=>'L',
6242=>'L',
6243=>'L',
6244=>'L',
6245=>'L',
6246=>'L',
6247=>'L',
6248=>'L',
6249=>'L',
6250=>'L',
6251=>'L',
6252=>'L',
6253=>'L',
6254=>'L',
6255=>'L',
6256=>'L',
6257=>'L',
6258=>'L',
6259=>'L',
6260=>'L',
6261=>'L',
6262=>'L',
6263=>'L',
6272=>'L',
6273=>'L',
6274=>'L',
6275=>'L',
6276=>'L',
6277=>'L',
6278=>'L',
6279=>'L',
6280=>'L',
6281=>'L',
6282=>'L',
6283=>'L',
6284=>'L',
6285=>'L',
6286=>'L',
6287=>'L',
6288=>'L',
6289=>'L',
6290=>'L',
6291=>'L',
6292=>'L',
6293=>'L',
6294=>'L',
6295=>'L',
6296=>'L',
6297=>'L',
6298=>'L',
6299=>'L',
6300=>'L',
6301=>'L',
6302=>'L',
6303=>'L',
6304=>'L',
6305=>'L',
6306=>'L',
6307=>'L',
6308=>'L',
6309=>'L',
6310=>'L',
6311=>'L',
6312=>'L',
6313=>'NSM',
6400=>'L',
6401=>'L',
6402=>'L',
6403=>'L',
6404=>'L',
6405=>'L',
6406=>'L',
6407=>'L',
6408=>'L',
6409=>'L',
6410=>'L',
6411=>'L',
6412=>'L',
6413=>'L',
6414=>'L',
6415=>'L',
6416=>'L',
6417=>'L',
6418=>'L',
6419=>'L',
6420=>'L',
6421=>'L',
6422=>'L',
6423=>'L',
6424=>'L',
6425=>'L',
6426=>'L',
6427=>'L',
6428=>'L',
6432=>'NSM',
6433=>'NSM',
6434=>'NSM',
6435=>'L',
6436=>'L',
6437=>'L',
6438=>'L',
6439=>'NSM',
6440=>'NSM',
6441=>'NSM',
6442=>'NSM',
6443=>'NSM',
6448=>'L',
6449=>'L',
6450=>'NSM',
6451=>'L',
6452=>'L',
6453=>'L',
6454=>'L',
6455=>'L',
6456=>'L',
6457=>'NSM',
6458=>'NSM',
6459=>'NSM',
6464=>'ON',
6468=>'ON',
6469=>'ON',
6470=>'L',
6471=>'L',
6472=>'L',
6473=>'L',
6474=>'L',
6475=>'L',
6476=>'L',
6477=>'L',
6478=>'L',
6479=>'L',
6480=>'L',
6481=>'L',
6482=>'L',
6483=>'L',
6484=>'L',
6485=>'L',
6486=>'L',
6487=>'L',
6488=>'L',
6489=>'L',
6490=>'L',
6491=>'L',
6492=>'L',
6493=>'L',
6494=>'L',
6495=>'L',
6496=>'L',
6497=>'L',
6498=>'L',
6499=>'L',
6500=>'L',
6501=>'L',
6502=>'L',
6503=>'L',
6504=>'L',
6505=>'L',
6506=>'L',
6507=>'L',
6508=>'L',
6509=>'L',
6512=>'L',
6513=>'L',
6514=>'L',
6515=>'L',
6516=>'L',
6528=>'L',
6529=>'L',
6530=>'L',
6531=>'L',
6532=>'L',
6533=>'L',
6534=>'L',
6535=>'L',
6536=>'L',
6537=>'L',
6538=>'L',
6539=>'L',
6540=>'L',
6541=>'L',
6542=>'L',
6543=>'L',
6544=>'L',
6545=>'L',
6546=>'L',
6547=>'L',
6548=>'L',
6549=>'L',
6550=>'L',
6551=>'L',
6552=>'L',
6553=>'L',
6554=>'L',
6555=>'L',
6556=>'L',
6557=>'L',
6558=>'L',
6559=>'L',
6560=>'L',
6561=>'L',
6562=>'L',
6563=>'L',
6564=>'L',
6565=>'L',
6566=>'L',
6567=>'L',
6568=>'L',
6569=>'L',
6576=>'L',
6577=>'L',
6578=>'L',
6579=>'L',
6580=>'L',
6581=>'L',
6582=>'L',
6583=>'L',
6584=>'L',
6585=>'L',
6586=>'L',
6587=>'L',
6588=>'L',
6589=>'L',
6590=>'L',
6591=>'L',
6592=>'L',
6593=>'L',
6594=>'L',
6595=>'L',
6596=>'L',
6597=>'L',
6598=>'L',
6599=>'L',
6600=>'L',
6601=>'L',
6608=>'L',
6609=>'L',
6610=>'L',
6611=>'L',
6612=>'L',
6613=>'L',
6614=>'L',
6615=>'L',
6616=>'L',
6617=>'L',
6622=>'ON',
6623=>'ON',
6624=>'ON',
6625=>'ON',
6626=>'ON',
6627=>'ON',
6628=>'ON',
6629=>'ON',
6630=>'ON',
6631=>'ON',
6632=>'ON',
6633=>'ON',
6634=>'ON',
6635=>'ON',
6636=>'ON',
6637=>'ON',
6638=>'ON',
6639=>'ON',
6640=>'ON',
6641=>'ON',
6642=>'ON',
6643=>'ON',
6644=>'ON',
6645=>'ON',
6646=>'ON',
6647=>'ON',
6648=>'ON',
6649=>'ON',
6650=>'ON',
6651=>'ON',
6652=>'ON',
6653=>'ON',
6654=>'ON',
6655=>'ON',
6656=>'L',
6657=>'L',
6658=>'L',
6659=>'L',
6660=>'L',
6661=>'L',
6662=>'L',
6663=>'L',
6664=>'L',
6665=>'L',
6666=>'L',
6667=>'L',
6668=>'L',
6669=>'L',
6670=>'L',
6671=>'L',
6672=>'L',
6673=>'L',
6674=>'L',
6675=>'L',
6676=>'L',
6677=>'L',
6678=>'L',
6679=>'NSM',
6680=>'NSM',
6681=>'L',
6682=>'L',
6683=>'L',
6686=>'L',
6687=>'L',
6912=>'NSM',
6913=>'NSM',
6914=>'NSM',
6915=>'NSM',
6916=>'L',
6917=>'L',
6918=>'L',
6919=>'L',
6920=>'L',
6921=>'L',
6922=>'L',
6923=>'L',
6924=>'L',
6925=>'L',
6926=>'L',
6927=>'L',
6928=>'L',
6929=>'L',
6930=>'L',
6931=>'L',
6932=>'L',
6933=>'L',
6934=>'L',
6935=>'L',
6936=>'L',
6937=>'L',
6938=>'L',
6939=>'L',
6940=>'L',
6941=>'L',
6942=>'L',
6943=>'L',
6944=>'L',
6945=>'L',
6946=>'L',
6947=>'L',
6948=>'L',
6949=>'L',
6950=>'L',
6951=>'L',
6952=>'L',
6953=>'L',
6954=>'L',
6955=>'L',
6956=>'L',
6957=>'L',
6958=>'L',
6959=>'L',
6960=>'L',
6961=>'L',
6962=>'L',
6963=>'L',
6964=>'NSM',
6965=>'L',
6966=>'NSM',
6967=>'NSM',
6968=>'NSM',
6969=>'NSM',
6970=>'NSM',
6971=>'L',
6972=>'NSM',
6973=>'L',
6974=>'L',
6975=>'L',
6976=>'L',
6977=>'L',
6978=>'NSM',
6979=>'L',
6980=>'L',
6981=>'L',
6982=>'L',
6983=>'L',
6984=>'L',
6985=>'L',
6986=>'L',
6987=>'L',
6992=>'L',
6993=>'L',
6994=>'L',
6995=>'L',
6996=>'L',
6997=>'L',
6998=>'L',
6999=>'L',
7000=>'L',
7001=>'L',
7002=>'L',
7003=>'L',
7004=>'L',
7005=>'L',
7006=>'L',
7007=>'L',
7008=>'L',
7009=>'L',
7010=>'L',
7011=>'L',
7012=>'L',
7013=>'L',
7014=>'L',
7015=>'L',
7016=>'L',
7017=>'L',
7018=>'L',
7019=>'NSM',
7020=>'NSM',
7021=>'NSM',
7022=>'NSM',
7023=>'NSM',
7024=>'NSM',
7025=>'NSM',
7026=>'NSM',
7027=>'NSM',
7028=>'L',
7029=>'L',
7030=>'L',
7031=>'L',
7032=>'L',
7033=>'L',
7034=>'L',
7035=>'L',
7036=>'L',
7424=>'L',
7425=>'L',
7426=>'L',
7427=>'L',
7428=>'L',
7429=>'L',
7430=>'L',
7431=>'L',
7432=>'L',
7433=>'L',
7434=>'L',
7435=>'L',
7436=>'L',
7437=>'L',
7438=>'L',
7439=>'L',
7440=>'L',
7441=>'L',
7442=>'L',
7443=>'L',
7444=>'L',
7445=>'L',
7446=>'L',
7447=>'L',
7448=>'L',
7449=>'L',
7450=>'L',
7451=>'L',
7452=>'L',
7453=>'L',
7454=>'L',
7455=>'L',
7456=>'L',
7457=>'L',
7458=>'L',
7459=>'L',
7460=>'L',
7461=>'L',
7462=>'L',
7463=>'L',
7464=>'L',
7465=>'L',
7466=>'L',
7467=>'L',
7468=>'L',
7469=>'L',
7470=>'L',
7471=>'L',
7472=>'L',
7473=>'L',
7474=>'L',
7475=>'L',
7476=>'L',
7477=>'L',
7478=>'L',
7479=>'L',
7480=>'L',
7481=>'L',
7482=>'L',
7483=>'L',
7484=>'L',
7485=>'L',
7486=>'L',
7487=>'L',
7488=>'L',
7489=>'L',
7490=>'L',
7491=>'L',
7492=>'L',
7493=>'L',
7494=>'L',
7495=>'L',
7496=>'L',
7497=>'L',
7498=>'L',
7499=>'L',
7500=>'L',
7501=>'L',
7502=>'L',
7503=>'L',
7504=>'L',
7505=>'L',
7506=>'L',
7507=>'L',
7508=>'L',
7509=>'L',
7510=>'L',
7511=>'L',
7512=>'L',
7513=>'L',
7514=>'L',
7515=>'L',
7516=>'L',
7517=>'L',
7518=>'L',
7519=>'L',
7520=>'L',
7521=>'L',
7522=>'L',
7523=>'L',
7524=>'L',
7525=>'L',
7526=>'L',
7527=>'L',
7528=>'L',
7529=>'L',
7530=>'L',
7531=>'L',
7532=>'L',
7533=>'L',
7534=>'L',
7535=>'L',
7536=>'L',
7537=>'L',
7538=>'L',
7539=>'L',
7540=>'L',
7541=>'L',
7542=>'L',
7543=>'L',
7544=>'L',
7545=>'L',
7546=>'L',
7547=>'L',
7548=>'L',
7549=>'L',
7550=>'L',
7551=>'L',
7552=>'L',
7553=>'L',
7554=>'L',
7555=>'L',
7556=>'L',
7557=>'L',
7558=>'L',
7559=>'L',
7560=>'L',
7561=>'L',
7562=>'L',
7563=>'L',
7564=>'L',
7565=>'L',
7566=>'L',
7567=>'L',
7568=>'L',
7569=>'L',
7570=>'L',
7571=>'L',
7572=>'L',
7573=>'L',
7574=>'L',
7575=>'L',
7576=>'L',
7577=>'L',
7578=>'L',
7579=>'L',
7580=>'L',
7581=>'L',
7582=>'L',
7583=>'L',
7584=>'L',
7585=>'L',
7586=>'L',
7587=>'L',
7588=>'L',
7589=>'L',
7590=>'L',
7591=>'L',
7592=>'L',
7593=>'L',
7594=>'L',
7595=>'L',
7596=>'L',
7597=>'L',
7598=>'L',
7599=>'L',
7600=>'L',
7601=>'L',
7602=>'L',
7603=>'L',
7604=>'L',
7605=>'L',
7606=>'L',
7607=>'L',
7608=>'L',
7609=>'L',
7610=>'L',
7611=>'L',
7612=>'L',
7613=>'L',
7614=>'L',
7615=>'L',
7616=>'NSM',
7617=>'NSM',
7618=>'NSM',
7619=>'NSM',
7620=>'NSM',
7621=>'NSM',
7622=>'NSM',
7623=>'NSM',
7624=>'NSM',
7625=>'NSM',
7626=>'NSM',
7678=>'NSM',
7679=>'NSM',
7680=>'L',
7681=>'L',
7682=>'L',
7683=>'L',
7684=>'L',
7685=>'L',
7686=>'L',
7687=>'L',
7688=>'L',
7689=>'L',
7690=>'L',
7691=>'L',
7692=>'L',
7693=>'L',
7694=>'L',
7695=>'L',
7696=>'L',
7697=>'L',
7698=>'L',
7699=>'L',
7700=>'L',
7701=>'L',
7702=>'L',
7703=>'L',
7704=>'L',
7705=>'L',
7706=>'L',
7707=>'L',
7708=>'L',
7709=>'L',
7710=>'L',
7711=>'L',
7712=>'L',
7713=>'L',
7714=>'L',
7715=>'L',
7716=>'L',
7717=>'L',
7718=>'L',
7719=>'L',
7720=>'L',
7721=>'L',
7722=>'L',
7723=>'L',
7724=>'L',
7725=>'L',
7726=>'L',
7727=>'L',
7728=>'L',
7729=>'L',
7730=>'L',
7731=>'L',
7732=>'L',
7733=>'L',
7734=>'L',
7735=>'L',
7736=>'L',
7737=>'L',
7738=>'L',
7739=>'L',
7740=>'L',
7741=>'L',
7742=>'L',
7743=>'L',
7744=>'L',
7745=>'L',
7746=>'L',
7747=>'L',
7748=>'L',
7749=>'L',
7750=>'L',
7751=>'L',
7752=>'L',
7753=>'L',
7754=>'L',
7755=>'L',
7756=>'L',
7757=>'L',
7758=>'L',
7759=>'L',
7760=>'L',
7761=>'L',
7762=>'L',
7763=>'L',
7764=>'L',
7765=>'L',
7766=>'L',
7767=>'L',
7768=>'L',
7769=>'L',
7770=>'L',
7771=>'L',
7772=>'L',
7773=>'L',
7774=>'L',
7775=>'L',
7776=>'L',
7777=>'L',
7778=>'L',
7779=>'L',
7780=>'L',
7781=>'L',
7782=>'L',
7783=>'L',
7784=>'L',
7785=>'L',
7786=>'L',
7787=>'L',
7788=>'L',
7789=>'L',
7790=>'L',
7791=>'L',
7792=>'L',
7793=>'L',
7794=>'L',
7795=>'L',
7796=>'L',
7797=>'L',
7798=>'L',
7799=>'L',
7800=>'L',
7801=>'L',
7802=>'L',
7803=>'L',
7804=>'L',
7805=>'L',
7806=>'L',
7807=>'L',
7808=>'L',
7809=>'L',
7810=>'L',
7811=>'L',
7812=>'L',
7813=>'L',
7814=>'L',
7815=>'L',
7816=>'L',
7817=>'L',
7818=>'L',
7819=>'L',
7820=>'L',
7821=>'L',
7822=>'L',
7823=>'L',
7824=>'L',
7825=>'L',
7826=>'L',
7827=>'L',
7828=>'L',
7829=>'L',
7830=>'L',
7831=>'L',
7832=>'L',
7833=>'L',
7834=>'L',
7835=>'L',
7840=>'L',
7841=>'L',
7842=>'L',
7843=>'L',
7844=>'L',
7845=>'L',
7846=>'L',
7847=>'L',
7848=>'L',
7849=>'L',
7850=>'L',
7851=>'L',
7852=>'L',
7853=>'L',
7854=>'L',
7855=>'L',
7856=>'L',
7857=>'L',
7858=>'L',
7859=>'L',
7860=>'L',
7861=>'L',
7862=>'L',
7863=>'L',
7864=>'L',
7865=>'L',
7866=>'L',
7867=>'L',
7868=>'L',
7869=>'L',
7870=>'L',
7871=>'L',
7872=>'L',
7873=>'L',
7874=>'L',
7875=>'L',
7876=>'L',
7877=>'L',
7878=>'L',
7879=>'L',
7880=>'L',
7881=>'L',
7882=>'L',
7883=>'L',
7884=>'L',
7885=>'L',
7886=>'L',
7887=>'L',
7888=>'L',
7889=>'L',
7890=>'L',
7891=>'L',
7892=>'L',
7893=>'L',
7894=>'L',
7895=>'L',
7896=>'L',
7897=>'L',
7898=>'L',
7899=>'L',
7900=>'L',
7901=>'L',
7902=>'L',
7903=>'L',
7904=>'L',
7905=>'L',
7906=>'L',
7907=>'L',
7908=>'L',
7909=>'L',
7910=>'L',
7911=>'L',
7912=>'L',
7913=>'L',
7914=>'L',
7915=>'L',
7916=>'L',
7917=>'L',
7918=>'L',
7919=>'L',
7920=>'L',
7921=>'L',
7922=>'L',
7923=>'L',
7924=>'L',
7925=>'L',
7926=>'L',
7927=>'L',
7928=>'L',
7929=>'L',
7936=>'L',
7937=>'L',
7938=>'L',
7939=>'L',
7940=>'L',
7941=>'L',
7942=>'L',
7943=>'L',
7944=>'L',
7945=>'L',
7946=>'L',
7947=>'L',
7948=>'L',
7949=>'L',
7950=>'L',
7951=>'L',
7952=>'L',
7953=>'L',
7954=>'L',
7955=>'L',
7956=>'L',
7957=>'L',
7960=>'L',
7961=>'L',
7962=>'L',
7963=>'L',
7964=>'L',
7965=>'L',
7968=>'L',
7969=>'L',
7970=>'L',
7971=>'L',
7972=>'L',
7973=>'L',
7974=>'L',
7975=>'L',
7976=>'L',
7977=>'L',
7978=>'L',
7979=>'L',
7980=>'L',
7981=>'L',
7982=>'L',
7983=>'L',
7984=>'L',
7985=>'L',
7986=>'L',
7987=>'L',
7988=>'L',
7989=>'L',
7990=>'L',
7991=>'L',
7992=>'L',
7993=>'L',
7994=>'L',
7995=>'L',
7996=>'L',
7997=>'L',
7998=>'L',
7999=>'L',
8000=>'L',
8001=>'L',
8002=>'L',
8003=>'L',
8004=>'L',
8005=>'L',
8008=>'L',
8009=>'L',
8010=>'L',
8011=>'L',
8012=>'L',
8013=>'L',
8016=>'L',
8017=>'L',
8018=>'L',
8019=>'L',
8020=>'L',
8021=>'L',
8022=>'L',
8023=>'L',
8025=>'L',
8027=>'L',
8029=>'L',
8031=>'L',
8032=>'L',
8033=>'L',
8034=>'L',
8035=>'L',
8036=>'L',
8037=>'L',
8038=>'L',
8039=>'L',
8040=>'L',
8041=>'L',
8042=>'L',
8043=>'L',
8044=>'L',
8045=>'L',
8046=>'L',
8047=>'L',
8048=>'L',
8049=>'L',
8050=>'L',
8051=>'L',
8052=>'L',
8053=>'L',
8054=>'L',
8055=>'L',
8056=>'L',
8057=>'L',
8058=>'L',
8059=>'L',
8060=>'L',
8061=>'L',
8064=>'L',
8065=>'L',
8066=>'L',
8067=>'L',
8068=>'L',
8069=>'L',
8070=>'L',
8071=>'L',
8072=>'L',
8073=>'L',
8074=>'L',
8075=>'L',
8076=>'L',
8077=>'L',
8078=>'L',
8079=>'L',
8080=>'L',
8081=>'L',
8082=>'L',
8083=>'L',
8084=>'L',
8085=>'L',
8086=>'L',
8087=>'L',
8088=>'L',
8089=>'L',
8090=>'L',
8091=>'L',
8092=>'L',
8093=>'L',
8094=>'L',
8095=>'L',
8096=>'L',
8097=>'L',
8098=>'L',
8099=>'L',
8100=>'L',
8101=>'L',
8102=>'L',
8103=>'L',
8104=>'L',
8105=>'L',
8106=>'L',
8107=>'L',
8108=>'L',
8109=>'L',
8110=>'L',
8111=>'L',
8112=>'L',
8113=>'L',
8114=>'L',
8115=>'L',
8116=>'L',
8118=>'L',
8119=>'L',
8120=>'L',
8121=>'L',
8122=>'L',
8123=>'L',
8124=>'L',
8125=>'ON',
8126=>'L',
8127=>'ON',
8128=>'ON',
8129=>'ON',
8130=>'L',
8131=>'L',
8132=>'L',
8134=>'L',
8135=>'L',
8136=>'L',
8137=>'L',
8138=>'L',
8139=>'L',
8140=>'L',
8141=>'ON',
8142=>'ON',
8143=>'ON',
8144=>'L',
8145=>'L',
8146=>'L',
8147=>'L',
8150=>'L',
8151=>'L',
8152=>'L',
8153=>'L',
8154=>'L',
8155=>'L',
8157=>'ON',
8158=>'ON',
8159=>'ON',
8160=>'L',
8161=>'L',
8162=>'L',
8163=>'L',
8164=>'L',
8165=>'L',
8166=>'L',
8167=>'L',
8168=>'L',
8169=>'L',
8170=>'L',
8171=>'L',
8172=>'L',
8173=>'ON',
8174=>'ON',
8175=>'ON',
8178=>'L',
8179=>'L',
8180=>'L',
8182=>'L',
8183=>'L',
8184=>'L',
8185=>'L',
8186=>'L',
8187=>'L',
8188=>'L',
8189=>'ON',
8190=>'ON',
8192=>'WS',
8193=>'WS',
8194=>'WS',
8195=>'WS',
8196=>'WS',
8197=>'WS',
8198=>'WS',
8199=>'WS',
8200=>'WS',
8201=>'WS',
8202=>'WS',
8203=>'BN',
8204=>'BN',
8205=>'BN',
8206=>'L',
8207=>'R',
8208=>'ON',
8209=>'ON',
8210=>'ON',
8211=>'ON',
8212=>'ON',
8213=>'ON',
8214=>'ON',
8215=>'ON',
8216=>'ON',
8217=>'ON',
8218=>'ON',
8219=>'ON',
8220=>'ON',
8221=>'ON',
8222=>'ON',
8223=>'ON',
8224=>'ON',
8225=>'ON',
8226=>'ON',
8227=>'ON',
8228=>'ON',
8229=>'ON',
8230=>'ON',
8231=>'ON',
8232=>'WS',
8233=>'B',
8234=>'LRE',
8235=>'RLE',
8236=>'PDF',
8237=>'LRO',
8238=>'RLO',
8239=>'CS',
8240=>'ET',
8241=>'ET',
8242=>'ET',
8243=>'ET',
8244=>'ET',
8245=>'ON',
8246=>'ON',
8247=>'ON',
8248=>'ON',
8249=>'ON',
8250=>'ON',
8251=>'ON',
8252=>'ON',
8253=>'ON',
8254=>'ON',
8255=>'ON',
8256=>'ON',
8257=>'ON',
8258=>'ON',
8259=>'ON',
8260=>'CS',
8261=>'ON',
8262=>'ON',
8263=>'ON',
8264=>'ON',
8265=>'ON',
8266=>'ON',
8267=>'ON',
8268=>'ON',
8269=>'ON',
8270=>'ON',
8271=>'ON',
8272=>'ON',
8273=>'ON',
8274=>'ON',
8275=>'ON',
8276=>'ON',
8277=>'ON',
8278=>'ON',
8279=>'ON',
8280=>'ON',
8281=>'ON',
8282=>'ON',
8283=>'ON',
8284=>'ON',
8285=>'ON',
8286=>'ON',
8287=>'WS',
8288=>'BN',
8289=>'BN',
8290=>'BN',
8291=>'BN',
8298=>'BN',
8299=>'BN',
8300=>'BN',
8301=>'BN',
8302=>'BN',
8303=>'BN',
8304=>'EN',
8305=>'L',
8308=>'EN',
8309=>'EN',
8310=>'EN',
8311=>'EN',
8312=>'EN',
8313=>'EN',
8314=>'ES',
8315=>'ES',
8316=>'ON',
8317=>'ON',
8318=>'ON',
8319=>'L',
8320=>'EN',
8321=>'EN',
8322=>'EN',
8323=>'EN',
8324=>'EN',
8325=>'EN',
8326=>'EN',
8327=>'EN',
8328=>'EN',
8329=>'EN',
8330=>'ES',
8331=>'ES',
8332=>'ON',
8333=>'ON',
8334=>'ON',
8336=>'L',
8337=>'L',
8338=>'L',
8339=>'L',
8340=>'L',
8352=>'ET',
8353=>'ET',
8354=>'ET',
8355=>'ET',
8356=>'ET',
8357=>'ET',
8358=>'ET',
8359=>'ET',
8360=>'ET',
8361=>'ET',
8362=>'ET',
8363=>'ET',
8364=>'ET',
8365=>'ET',
8366=>'ET',
8367=>'ET',
8368=>'ET',
8369=>'ET',
8370=>'ET',
8371=>'ET',
8372=>'ET',
8373=>'ET',
8400=>'NSM',
8401=>'NSM',
8402=>'NSM',
8403=>'NSM',
8404=>'NSM',
8405=>'NSM',
8406=>'NSM',
8407=>'NSM',
8408=>'NSM',
8409=>'NSM',
8410=>'NSM',
8411=>'NSM',
8412=>'NSM',
8413=>'NSM',
8414=>'NSM',
8415=>'NSM',
8416=>'NSM',
8417=>'NSM',
8418=>'NSM',
8419=>'NSM',
8420=>'NSM',
8421=>'NSM',
8422=>'NSM',
8423=>'NSM',
8424=>'NSM',
8425=>'NSM',
8426=>'NSM',
8427=>'NSM',
8428=>'NSM',
8429=>'NSM',
8430=>'NSM',
8431=>'NSM',
8448=>'ON',
8449=>'ON',
8450=>'L',
8451=>'ON',
8452=>'ON',
8453=>'ON',
8454=>'ON',
8455=>'L',
8456=>'ON',
8457=>'ON',
8458=>'L',
8459=>'L',
8460=>'L',
8461=>'L',
8462=>'L',
8463=>'L',
8464=>'L',
8465=>'L',
8466=>'L',
8467=>'L',
8468=>'ON',
8469=>'L',
8470=>'ON',
8471=>'ON',
8472=>'ON',
8473=>'L',
8474=>'L',
8475=>'L',
8476=>'L',
8477=>'L',
8478=>'ON',
8479=>'ON',
8480=>'ON',
8481=>'ON',
8482=>'ON',
8483=>'ON',
8484=>'L',
8485=>'ON',
8486=>'L',
8487=>'ON',
8488=>'L',
8489=>'ON',
8490=>'L',
8491=>'L',
8492=>'L',
8493=>'L',
8494=>'ET',
8495=>'L',
8496=>'L',
8497=>'L',
8498=>'L',
8499=>'L',
8500=>'L',
8501=>'L',
8502=>'L',
8503=>'L',
8504=>'L',
8505=>'L',
8506=>'ON',
8507=>'ON',
8508=>'L',
8509=>'L',
8510=>'L',
8511=>'L',
8512=>'ON',
8513=>'ON',
8514=>'ON',
8515=>'ON',
8516=>'ON',
8517=>'L',
8518=>'L',
8519=>'L',
8520=>'L',
8521=>'L',
8522=>'ON',
8523=>'ON',
8524=>'ON',
8525=>'ON',
8526=>'L',
8531=>'ON',
8532=>'ON',
8533=>'ON',
8534=>'ON',
8535=>'ON',
8536=>'ON',
8537=>'ON',
8538=>'ON',
8539=>'ON',
8540=>'ON',
8541=>'ON',
8542=>'ON',
8543=>'ON',
8544=>'L',
8545=>'L',
8546=>'L',
8547=>'L',
8548=>'L',
8549=>'L',
8550=>'L',
8551=>'L',
8552=>'L',
8553=>'L',
8554=>'L',
8555=>'L',
8556=>'L',
8557=>'L',
8558=>'L',
8559=>'L',
8560=>'L',
8561=>'L',
8562=>'L',
8563=>'L',
8564=>'L',
8565=>'L',
8566=>'L',
8567=>'L',
8568=>'L',
8569=>'L',
8570=>'L',
8571=>'L',
8572=>'L',
8573=>'L',
8574=>'L',
8575=>'L',
8576=>'L',
8577=>'L',
8578=>'L',
8579=>'L',
8580=>'L',
8592=>'ON',
8593=>'ON',
8594=>'ON',
8595=>'ON',
8596=>'ON',
8597=>'ON',
8598=>'ON',
8599=>'ON',
8600=>'ON',
8601=>'ON',
8602=>'ON',
8603=>'ON',
8604=>'ON',
8605=>'ON',
8606=>'ON',
8607=>'ON',
8608=>'ON',
8609=>'ON',
8610=>'ON',
8611=>'ON',
8612=>'ON',
8613=>'ON',
8614=>'ON',
8615=>'ON',
8616=>'ON',
8617=>'ON',
8618=>'ON',
8619=>'ON',
8620=>'ON',
8621=>'ON',
8622=>'ON',
8623=>'ON',
8624=>'ON',
8625=>'ON',
8626=>'ON',
8627=>'ON',
8628=>'ON',
8629=>'ON',
8630=>'ON',
8631=>'ON',
8632=>'ON',
8633=>'ON',
8634=>'ON',
8635=>'ON',
8636=>'ON',
8637=>'ON',
8638=>'ON',
8639=>'ON',
8640=>'ON',
8641=>'ON',
8642=>'ON',
8643=>'ON',
8644=>'ON',
8645=>'ON',
8646=>'ON',
8647=>'ON',
8648=>'ON',
8649=>'ON',
8650=>'ON',
8651=>'ON',
8652=>'ON',
8653=>'ON',
8654=>'ON',
8655=>'ON',
8656=>'ON',
8657=>'ON',
8658=>'ON',
8659=>'ON',
8660=>'ON',
8661=>'ON',
8662=>'ON',
8663=>'ON',
8664=>'ON',
8665=>'ON',
8666=>'ON',
8667=>'ON',
8668=>'ON',
8669=>'ON',
8670=>'ON',
8671=>'ON',
8672=>'ON',
8673=>'ON',
8674=>'ON',
8675=>'ON',
8676=>'ON',
8677=>'ON',
8678=>'ON',
8679=>'ON',
8680=>'ON',
8681=>'ON',
8682=>'ON',
8683=>'ON',
8684=>'ON',
8685=>'ON',
8686=>'ON',
8687=>'ON',
8688=>'ON',
8689=>'ON',
8690=>'ON',
8691=>'ON',
8692=>'ON',
8693=>'ON',
8694=>'ON',
8695=>'ON',
8696=>'ON',
8697=>'ON',
8698=>'ON',
8699=>'ON',
8700=>'ON',
8701=>'ON',
8702=>'ON',
8703=>'ON',
8704=>'ON',
8705=>'ON',
8706=>'ON',
8707=>'ON',
8708=>'ON',
8709=>'ON',
8710=>'ON',
8711=>'ON',
8712=>'ON',
8713=>'ON',
8714=>'ON',
8715=>'ON',
8716=>'ON',
8717=>'ON',
8718=>'ON',
8719=>'ON',
8720=>'ON',
8721=>'ON',
8722=>'ES',
8723=>'ET',
8724=>'ON',
8725=>'ON',
8726=>'ON',
8727=>'ON',
8728=>'ON',
8729=>'ON',
8730=>'ON',
8731=>'ON',
8732=>'ON',
8733=>'ON',
8734=>'ON',
8735=>'ON',
8736=>'ON',
8737=>'ON',
8738=>'ON',
8739=>'ON',
8740=>'ON',
8741=>'ON',
8742=>'ON',
8743=>'ON',
8744=>'ON',
8745=>'ON',
8746=>'ON',
8747=>'ON',
8748=>'ON',
8749=>'ON',
8750=>'ON',
8751=>'ON',
8752=>'ON',
8753=>'ON',
8754=>'ON',
8755=>'ON',
8756=>'ON',
8757=>'ON',
8758=>'ON',
8759=>'ON',
8760=>'ON',
8761=>'ON',
8762=>'ON',
8763=>'ON',
8764=>'ON',
8765=>'ON',
8766=>'ON',
8767=>'ON',
8768=>'ON',
8769=>'ON',
8770=>'ON',
8771=>'ON',
8772=>'ON',
8773=>'ON',
8774=>'ON',
8775=>'ON',
8776=>'ON',
8777=>'ON',
8778=>'ON',
8779=>'ON',
8780=>'ON',
8781=>'ON',
8782=>'ON',
8783=>'ON',
8784=>'ON',
8785=>'ON',
8786=>'ON',
8787=>'ON',
8788=>'ON',
8789=>'ON',
8790=>'ON',
8791=>'ON',
8792=>'ON',
8793=>'ON',
8794=>'ON',
8795=>'ON',
8796=>'ON',
8797=>'ON',
8798=>'ON',
8799=>'ON',
8800=>'ON',
8801=>'ON',
8802=>'ON',
8803=>'ON',
8804=>'ON',
8805=>'ON',
8806=>'ON',
8807=>'ON',
8808=>'ON',
8809=>'ON',
8810=>'ON',
8811=>'ON',
8812=>'ON',
8813=>'ON',
8814=>'ON',
8815=>'ON',
8816=>'ON',
8817=>'ON',
8818=>'ON',
8819=>'ON',
8820=>'ON',
8821=>'ON',
8822=>'ON',
8823=>'ON',
8824=>'ON',
8825=>'ON',
8826=>'ON',
8827=>'ON',
8828=>'ON',
8829=>'ON',
8830=>'ON',
8831=>'ON',
8832=>'ON',
8833=>'ON',
8834=>'ON',
8835=>'ON',
8836=>'ON',
8837=>'ON',
8838=>'ON',
8839=>'ON',
8840=>'ON',
8841=>'ON',
8842=>'ON',
8843=>'ON',
8844=>'ON',
8845=>'ON',
8846=>'ON',
8847=>'ON',
8848=>'ON',
8849=>'ON',
8850=>'ON',
8851=>'ON',
8852=>'ON',
8853=>'ON',
8854=>'ON',
8855=>'ON',
8856=>'ON',
8857=>'ON',
8858=>'ON',
8859=>'ON',
8860=>'ON',
8861=>'ON',
8862=>'ON',
8863=>'ON',
8864=>'ON',
8865=>'ON',
8866=>'ON',
8867=>'ON',
8868=>'ON',
8869=>'ON',
8870=>'ON',
8871=>'ON',
8872=>'ON',
8873=>'ON',
8874=>'ON',
8875=>'ON',
8876=>'ON',
8877=>'ON',
8878=>'ON',
8879=>'ON',
8880=>'ON',
8881=>'ON',
8882=>'ON',
8883=>'ON',
8884=>'ON',
8885=>'ON',
8886=>'ON',
8887=>'ON',
8888=>'ON',
8889=>'ON',
8890=>'ON',
8891=>'ON',
8892=>'ON',
8893=>'ON',
8894=>'ON',
8895=>'ON',
8896=>'ON',
8897=>'ON',
8898=>'ON',
8899=>'ON',
8900=>'ON',
8901=>'ON',
8902=>'ON',
8903=>'ON',
8904=>'ON',
8905=>'ON',
8906=>'ON',
8907=>'ON',
8908=>'ON',
8909=>'ON',
8910=>'ON',
8911=>'ON',
8912=>'ON',
8913=>'ON',
8914=>'ON',
8915=>'ON',
8916=>'ON',
8917=>'ON',
8918=>'ON',
8919=>'ON',
8920=>'ON',
8921=>'ON',
8922=>'ON',
8923=>'ON',
8924=>'ON',
8925=>'ON',
8926=>'ON',
8927=>'ON',
8928=>'ON',
8929=>'ON',
8930=>'ON',
8931=>'ON',
8932=>'ON',
8933=>'ON',
8934=>'ON',
8935=>'ON',
8936=>'ON',
8937=>'ON',
8938=>'ON',
8939=>'ON',
8940=>'ON',
8941=>'ON',
8942=>'ON',
8943=>'ON',
8944=>'ON',
8945=>'ON',
8946=>'ON',
8947=>'ON',
8948=>'ON',
8949=>'ON',
8950=>'ON',
8951=>'ON',
8952=>'ON',
8953=>'ON',
8954=>'ON',
8955=>'ON',
8956=>'ON',
8957=>'ON',
8958=>'ON',
8959=>'ON',
8960=>'ON',
8961=>'ON',
8962=>'ON',
8963=>'ON',
8964=>'ON',
8965=>'ON',
8966=>'ON',
8967=>'ON',
8968=>'ON',
8969=>'ON',
8970=>'ON',
8971=>'ON',
8972=>'ON',
8973=>'ON',
8974=>'ON',
8975=>'ON',
8976=>'ON',
8977=>'ON',
8978=>'ON',
8979=>'ON',
8980=>'ON',
8981=>'ON',
8982=>'ON',
8983=>'ON',
8984=>'ON',
8985=>'ON',
8986=>'ON',
8987=>'ON',
8988=>'ON',
8989=>'ON',
8990=>'ON',
8991=>'ON',
8992=>'ON',
8993=>'ON',
8994=>'ON',
8995=>'ON',
8996=>'ON',
8997=>'ON',
8998=>'ON',
8999=>'ON',
9000=>'ON',
9001=>'ON',
9002=>'ON',
9003=>'ON',
9004=>'ON',
9005=>'ON',
9006=>'ON',
9007=>'ON',
9008=>'ON',
9009=>'ON',
9010=>'ON',
9011=>'ON',
9012=>'ON',
9013=>'ON',
9014=>'L',
9015=>'L',
9016=>'L',
9017=>'L',
9018=>'L',
9019=>'L',
9020=>'L',
9021=>'L',
9022=>'L',
9023=>'L',
9024=>'L',
9025=>'L',
9026=>'L',
9027=>'L',
9028=>'L',
9029=>'L',
9030=>'L',
9031=>'L',
9032=>'L',
9033=>'L',
9034=>'L',
9035=>'L',
9036=>'L',
9037=>'L',
9038=>'L',
9039=>'L',
9040=>'L',
9041=>'L',
9042=>'L',
9043=>'L',
9044=>'L',
9045=>'L',
9046=>'L',
9047=>'L',
9048=>'L',
9049=>'L',
9050=>'L',
9051=>'L',
9052=>'L',
9053=>'L',
9054=>'L',
9055=>'L',
9056=>'L',
9057=>'L',
9058=>'L',
9059=>'L',
9060=>'L',
9061=>'L',
9062=>'L',
9063=>'L',
9064=>'L',
9065=>'L',
9066=>'L',
9067=>'L',
9068=>'L',
9069=>'L',
9070=>'L',
9071=>'L',
9072=>'L',
9073=>'L',
9074=>'L',
9075=>'L',
9076=>'L',
9077=>'L',
9078=>'L',
9079=>'L',
9080=>'L',
9081=>'L',
9082=>'L',
9083=>'ON',
9084=>'ON',
9085=>'ON',
9086=>'ON',
9087=>'ON',
9088=>'ON',
9089=>'ON',
9090=>'ON',
9091=>'ON',
9092=>'ON',
9093=>'ON',
9094=>'ON',
9095=>'ON',
9096=>'ON',
9097=>'ON',
9098=>'ON',
9099=>'ON',
9100=>'ON',
9101=>'ON',
9102=>'ON',
9103=>'ON',
9104=>'ON',
9105=>'ON',
9106=>'ON',
9107=>'ON',
9108=>'ON',
9109=>'L',
9110=>'ON',
9111=>'ON',
9112=>'ON',
9113=>'ON',
9114=>'ON',
9115=>'ON',
9116=>'ON',
9117=>'ON',
9118=>'ON',
9119=>'ON',
9120=>'ON',
9121=>'ON',
9122=>'ON',
9123=>'ON',
9124=>'ON',
9125=>'ON',
9126=>'ON',
9127=>'ON',
9128=>'ON',
9129=>'ON',
9130=>'ON',
9131=>'ON',
9132=>'ON',
9133=>'ON',
9134=>'ON',
9135=>'ON',
9136=>'ON',
9137=>'ON',
9138=>'ON',
9139=>'ON',
9140=>'ON',
9141=>'ON',
9142=>'ON',
9143=>'ON',
9144=>'ON',
9145=>'ON',
9146=>'ON',
9147=>'ON',
9148=>'ON',
9149=>'ON',
9150=>'ON',
9151=>'ON',
9152=>'ON',
9153=>'ON',
9154=>'ON',
9155=>'ON',
9156=>'ON',
9157=>'ON',
9158=>'ON',
9159=>'ON',
9160=>'ON',
9161=>'ON',
9162=>'ON',
9163=>'ON',
9164=>'ON',
9165=>'ON',
9166=>'ON',
9167=>'ON',
9168=>'ON',
9169=>'ON',
9170=>'ON',
9171=>'ON',
9172=>'ON',
9173=>'ON',
9174=>'ON',
9175=>'ON',
9176=>'ON',
9177=>'ON',
9178=>'ON',
9179=>'ON',
9180=>'ON',
9181=>'ON',
9182=>'ON',
9183=>'ON',
9184=>'ON',
9185=>'ON',
9186=>'ON',
9187=>'ON',
9188=>'ON',
9189=>'ON',
9190=>'ON',
9191=>'ON',
9216=>'ON',
9217=>'ON',
9218=>'ON',
9219=>'ON',
9220=>'ON',
9221=>'ON',
9222=>'ON',
9223=>'ON',
9224=>'ON',
9225=>'ON',
9226=>'ON',
9227=>'ON',
9228=>'ON',
9229=>'ON',
9230=>'ON',
9231=>'ON',
9232=>'ON',
9233=>'ON',
9234=>'ON',
9235=>'ON',
9236=>'ON',
9237=>'ON',
9238=>'ON',
9239=>'ON',
9240=>'ON',
9241=>'ON',
9242=>'ON',
9243=>'ON',
9244=>'ON',
9245=>'ON',
9246=>'ON',
9247=>'ON',
9248=>'ON',
9249=>'ON',
9250=>'ON',
9251=>'ON',
9252=>'ON',
9253=>'ON',
9254=>'ON',
9280=>'ON',
9281=>'ON',
9282=>'ON',
9283=>'ON',
9284=>'ON',
9285=>'ON',
9286=>'ON',
9287=>'ON',
9288=>'ON',
9289=>'ON',
9290=>'ON',
9312=>'ON',
9313=>'ON',
9314=>'ON',
9315=>'ON',
9316=>'ON',
9317=>'ON',
9318=>'ON',
9319=>'ON',
9320=>'ON',
9321=>'ON',
9322=>'ON',
9323=>'ON',
9324=>'ON',
9325=>'ON',
9326=>'ON',
9327=>'ON',
9328=>'ON',
9329=>'ON',
9330=>'ON',
9331=>'ON',
9332=>'ON',
9333=>'ON',
9334=>'ON',
9335=>'ON',
9336=>'ON',
9337=>'ON',
9338=>'ON',
9339=>'ON',
9340=>'ON',
9341=>'ON',
9342=>'ON',
9343=>'ON',
9344=>'ON',
9345=>'ON',
9346=>'ON',
9347=>'ON',
9348=>'ON',
9349=>'ON',
9350=>'ON',
9351=>'ON',
9352=>'EN',
9353=>'EN',
9354=>'EN',
9355=>'EN',
9356=>'EN',
9357=>'EN',
9358=>'EN',
9359=>'EN',
9360=>'EN',
9361=>'EN',
9362=>'EN',
9363=>'EN',
9364=>'EN',
9365=>'EN',
9366=>'EN',
9367=>'EN',
9368=>'EN',
9369=>'EN',
9370=>'EN',
9371=>'EN',
9372=>'L',
9373=>'L',
9374=>'L',
9375=>'L',
9376=>'L',
9377=>'L',
9378=>'L',
9379=>'L',
9380=>'L',
9381=>'L',
9382=>'L',
9383=>'L',
9384=>'L',
9385=>'L',
9386=>'L',
9387=>'L',
9388=>'L',
9389=>'L',
9390=>'L',
9391=>'L',
9392=>'L',
9393=>'L',
9394=>'L',
9395=>'L',
9396=>'L',
9397=>'L',
9398=>'L',
9399=>'L',
9400=>'L',
9401=>'L',
9402=>'L',
9403=>'L',
9404=>'L',
9405=>'L',
9406=>'L',
9407=>'L',
9408=>'L',
9409=>'L',
9410=>'L',
9411=>'L',
9412=>'L',
9413=>'L',
9414=>'L',
9415=>'L',
9416=>'L',
9417=>'L',
9418=>'L',
9419=>'L',
9420=>'L',
9421=>'L',
9422=>'L',
9423=>'L',
9424=>'L',
9425=>'L',
9426=>'L',
9427=>'L',
9428=>'L',
9429=>'L',
9430=>'L',
9431=>'L',
9432=>'L',
9433=>'L',
9434=>'L',
9435=>'L',
9436=>'L',
9437=>'L',
9438=>'L',
9439=>'L',
9440=>'L',
9441=>'L',
9442=>'L',
9443=>'L',
9444=>'L',
9445=>'L',
9446=>'L',
9447=>'L',
9448=>'L',
9449=>'L',
9450=>'ON',
9451=>'ON',
9452=>'ON',
9453=>'ON',
9454=>'ON',
9455=>'ON',
9456=>'ON',
9457=>'ON',
9458=>'ON',
9459=>'ON',
9460=>'ON',
9461=>'ON',
9462=>'ON',
9463=>'ON',
9464=>'ON',
9465=>'ON',
9466=>'ON',
9467=>'ON',
9468=>'ON',
9469=>'ON',
9470=>'ON',
9471=>'ON',
9472=>'ON',
9473=>'ON',
9474=>'ON',
9475=>'ON',
9476=>'ON',
9477=>'ON',
9478=>'ON',
9479=>'ON',
9480=>'ON',
9481=>'ON',
9482=>'ON',
9483=>'ON',
9484=>'ON',
9485=>'ON',
9486=>'ON',
9487=>'ON',
9488=>'ON',
9489=>'ON',
9490=>'ON',
9491=>'ON',
9492=>'ON',
9493=>'ON',
9494=>'ON',
9495=>'ON',
9496=>'ON',
9497=>'ON',
9498=>'ON',
9499=>'ON',
9500=>'ON',
9501=>'ON',
9502=>'ON',
9503=>'ON',
9504=>'ON',
9505=>'ON',
9506=>'ON',
9507=>'ON',
9508=>'ON',
9509=>'ON',
9510=>'ON',
9511=>'ON',
9512=>'ON',
9513=>'ON',
9514=>'ON',
9515=>'ON',
9516=>'ON',
9517=>'ON',
9518=>'ON',
9519=>'ON',
9520=>'ON',
9521=>'ON',
9522=>'ON',
9523=>'ON',
9524=>'ON',
9525=>'ON',
9526=>'ON',
9527=>'ON',
9528=>'ON',
9529=>'ON',
9530=>'ON',
9531=>'ON',
9532=>'ON',
9533=>'ON',
9534=>'ON',
9535=>'ON',
9536=>'ON',
9537=>'ON',
9538=>'ON',
9539=>'ON',
9540=>'ON',
9541=>'ON',
9542=>'ON',
9543=>'ON',
9544=>'ON',
9545=>'ON',
9546=>'ON',
9547=>'ON',
9548=>'ON',
9549=>'ON',
9550=>'ON',
9551=>'ON',
9552=>'ON',
9553=>'ON',
9554=>'ON',
9555=>'ON',
9556=>'ON',
9557=>'ON',
9558=>'ON',
9559=>'ON',
9560=>'ON',
9561=>'ON',
9562=>'ON',
9563=>'ON',
9564=>'ON',
9565=>'ON',
9566=>'ON',
9567=>'ON',
9568=>'ON',
9569=>'ON',
9570=>'ON',
9571=>'ON',
9572=>'ON',
9573=>'ON',
9574=>'ON',
9575=>'ON',
9576=>'ON',
9577=>'ON',
9578=>'ON',
9579=>'ON',
9580=>'ON',
9581=>'ON',
9582=>'ON',
9583=>'ON',
9584=>'ON',
9585=>'ON',
9586=>'ON',
9587=>'ON',
9588=>'ON',
9589=>'ON',
9590=>'ON',
9591=>'ON',
9592=>'ON',
9593=>'ON',
9594=>'ON',
9595=>'ON',
9596=>'ON',
9597=>'ON',
9598=>'ON',
9599=>'ON',
9600=>'ON',
9601=>'ON',
9602=>'ON',
9603=>'ON',
9604=>'ON',
9605=>'ON',
9606=>'ON',
9607=>'ON',
9608=>'ON',
9609=>'ON',
9610=>'ON',
9611=>'ON',
9612=>'ON',
9613=>'ON',
9614=>'ON',
9615=>'ON',
9616=>'ON',
9617=>'ON',
9618=>'ON',
9619=>'ON',
9620=>'ON',
9621=>'ON',
9622=>'ON',
9623=>'ON',
9624=>'ON',
9625=>'ON',
9626=>'ON',
9627=>'ON',
9628=>'ON',
9629=>'ON',
9630=>'ON',
9631=>'ON',
9632=>'ON',
9633=>'ON',
9634=>'ON',
9635=>'ON',
9636=>'ON',
9637=>'ON',
9638=>'ON',
9639=>'ON',
9640=>'ON',
9641=>'ON',
9642=>'ON',
9643=>'ON',
9644=>'ON',
9645=>'ON',
9646=>'ON',
9647=>'ON',
9648=>'ON',
9649=>'ON',
9650=>'ON',
9651=>'ON',
9652=>'ON',
9653=>'ON',
9654=>'ON',
9655=>'ON',
9656=>'ON',
9657=>'ON',
9658=>'ON',
9659=>'ON',
9660=>'ON',
9661=>'ON',
9662=>'ON',
9663=>'ON',
9664=>'ON',
9665=>'ON',
9666=>'ON',
9667=>'ON',
9668=>'ON',
9669=>'ON',
9670=>'ON',
9671=>'ON',
9672=>'ON',
9673=>'ON',
9674=>'ON',
9675=>'ON',
9676=>'ON',
9677=>'ON',
9678=>'ON',
9679=>'ON',
9680=>'ON',
9681=>'ON',
9682=>'ON',
9683=>'ON',
9684=>'ON',
9685=>'ON',
9686=>'ON',
9687=>'ON',
9688=>'ON',
9689=>'ON',
9690=>'ON',
9691=>'ON',
9692=>'ON',
9693=>'ON',
9694=>'ON',
9695=>'ON',
9696=>'ON',
9697=>'ON',
9698=>'ON',
9699=>'ON',
9700=>'ON',
9701=>'ON',
9702=>'ON',
9703=>'ON',
9704=>'ON',
9705=>'ON',
9706=>'ON',
9707=>'ON',
9708=>'ON',
9709=>'ON',
9710=>'ON',
9711=>'ON',
9712=>'ON',
9713=>'ON',
9714=>'ON',
9715=>'ON',
9716=>'ON',
9717=>'ON',
9718=>'ON',
9719=>'ON',
9720=>'ON',
9721=>'ON',
9722=>'ON',
9723=>'ON',
9724=>'ON',
9725=>'ON',
9726=>'ON',
9727=>'ON',
9728=>'ON',
9729=>'ON',
9730=>'ON',
9731=>'ON',
9732=>'ON',
9733=>'ON',
9734=>'ON',
9735=>'ON',
9736=>'ON',
9737=>'ON',
9738=>'ON',
9739=>'ON',
9740=>'ON',
9741=>'ON',
9742=>'ON',
9743=>'ON',
9744=>'ON',
9745=>'ON',
9746=>'ON',
9747=>'ON',
9748=>'ON',
9749=>'ON',
9750=>'ON',
9751=>'ON',
9752=>'ON',
9753=>'ON',
9754=>'ON',
9755=>'ON',
9756=>'ON',
9757=>'ON',
9758=>'ON',
9759=>'ON',
9760=>'ON',
9761=>'ON',
9762=>'ON',
9763=>'ON',
9764=>'ON',
9765=>'ON',
9766=>'ON',
9767=>'ON',
9768=>'ON',
9769=>'ON',
9770=>'ON',
9771=>'ON',
9772=>'ON',
9773=>'ON',
9774=>'ON',
9775=>'ON',
9776=>'ON',
9777=>'ON',
9778=>'ON',
9779=>'ON',
9780=>'ON',
9781=>'ON',
9782=>'ON',
9783=>'ON',
9784=>'ON',
9785=>'ON',
9786=>'ON',
9787=>'ON',
9788=>'ON',
9789=>'ON',
9790=>'ON',
9791=>'ON',
9792=>'ON',
9793=>'ON',
9794=>'ON',
9795=>'ON',
9796=>'ON',
9797=>'ON',
9798=>'ON',
9799=>'ON',
9800=>'ON',
9801=>'ON',
9802=>'ON',
9803=>'ON',
9804=>'ON',
9805=>'ON',
9806=>'ON',
9807=>'ON',
9808=>'ON',
9809=>'ON',
9810=>'ON',
9811=>'ON',
9812=>'ON',
9813=>'ON',
9814=>'ON',
9815=>'ON',
9816=>'ON',
9817=>'ON',
9818=>'ON',
9819=>'ON',
9820=>'ON',
9821=>'ON',
9822=>'ON',
9823=>'ON',
9824=>'ON',
9825=>'ON',
9826=>'ON',
9827=>'ON',
9828=>'ON',
9829=>'ON',
9830=>'ON',
9831=>'ON',
9832=>'ON',
9833=>'ON',
9834=>'ON',
9835=>'ON',
9836=>'ON',
9837=>'ON',
9838=>'ON',
9839=>'ON',
9840=>'ON',
9841=>'ON',
9842=>'ON',
9843=>'ON',
9844=>'ON',
9845=>'ON',
9846=>'ON',
9847=>'ON',
9848=>'ON',
9849=>'ON',
9850=>'ON',
9851=>'ON',
9852=>'ON',
9853=>'ON',
9854=>'ON',
9855=>'ON',
9856=>'ON',
9857=>'ON',
9858=>'ON',
9859=>'ON',
9860=>'ON',
9861=>'ON',
9862=>'ON',
9863=>'ON',
9864=>'ON',
9865=>'ON',
9866=>'ON',
9867=>'ON',
9868=>'ON',
9869=>'ON',
9870=>'ON',
9871=>'ON',
9872=>'ON',
9873=>'ON',
9874=>'ON',
9875=>'ON',
9876=>'ON',
9877=>'ON',
9878=>'ON',
9879=>'ON',
9880=>'ON',
9881=>'ON',
9882=>'ON',
9883=>'ON',
9884=>'ON',
9888=>'ON',
9889=>'ON',
9890=>'ON',
9891=>'ON',
9892=>'ON',
9893=>'ON',
9894=>'ON',
9895=>'ON',
9896=>'ON',
9897=>'ON',
9898=>'ON',
9899=>'ON',
9900=>'L',
9901=>'ON',
9902=>'ON',
9903=>'ON',
9904=>'ON',
9905=>'ON',
9906=>'ON',
9985=>'ON',
9986=>'ON',
9987=>'ON',
9988=>'ON',
9990=>'ON',
9991=>'ON',
9992=>'ON',
9993=>'ON',
9996=>'ON',
9997=>'ON',
9998=>'ON',
9999=>'ON',
10000=>'ON',
10001=>'ON',
10002=>'ON',
10003=>'ON',
10004=>'ON',
10005=>'ON',
10006=>'ON',
10007=>'ON',
10008=>'ON',
10009=>'ON',
10010=>'ON',
10011=>'ON',
10012=>'ON',
10013=>'ON',
10014=>'ON',
10015=>'ON',
10016=>'ON',
10017=>'ON',
10018=>'ON',
10019=>'ON',
10020=>'ON',
10021=>'ON',
10022=>'ON',
10023=>'ON',
10025=>'ON',
10026=>'ON',
10027=>'ON',
10028=>'ON',
10029=>'ON',
10030=>'ON',
10031=>'ON',
10032=>'ON',
10033=>'ON',
10034=>'ON',
10035=>'ON',
10036=>'ON',
10037=>'ON',
10038=>'ON',
10039=>'ON',
10040=>'ON',
10041=>'ON',
10042=>'ON',
10043=>'ON',
10044=>'ON',
10045=>'ON',
10046=>'ON',
10047=>'ON',
10048=>'ON',
10049=>'ON',
10050=>'ON',
10051=>'ON',
10052=>'ON',
10053=>'ON',
10054=>'ON',
10055=>'ON',
10056=>'ON',
10057=>'ON',
10058=>'ON',
10059=>'ON',
10061=>'ON',
10063=>'ON',
10064=>'ON',
10065=>'ON',
10066=>'ON',
10070=>'ON',
10072=>'ON',
10073=>'ON',
10074=>'ON',
10075=>'ON',
10076=>'ON',
10077=>'ON',
10078=>'ON',
10081=>'ON',
10082=>'ON',
10083=>'ON',
10084=>'ON',
10085=>'ON',
10086=>'ON',
10087=>'ON',
10088=>'ON',
10089=>'ON',
10090=>'ON',
10091=>'ON',
10092=>'ON',
10093=>'ON',
10094=>'ON',
10095=>'ON',
10096=>'ON',
10097=>'ON',
10098=>'ON',
10099=>'ON',
10100=>'ON',
10101=>'ON',
10102=>'ON',
10103=>'ON',
10104=>'ON',
10105=>'ON',
10106=>'ON',
10107=>'ON',
10108=>'ON',
10109=>'ON',
10110=>'ON',
10111=>'ON',
10112=>'ON',
10113=>'ON',
10114=>'ON',
10115=>'ON',
10116=>'ON',
10117=>'ON',
10118=>'ON',
10119=>'ON',
10120=>'ON',
10121=>'ON',
10122=>'ON',
10123=>'ON',
10124=>'ON',
10125=>'ON',
10126=>'ON',
10127=>'ON',
10128=>'ON',
10129=>'ON',
10130=>'ON',
10131=>'ON',
10132=>'ON',
10136=>'ON',
10137=>'ON',
10138=>'ON',
10139=>'ON',
10140=>'ON',
10141=>'ON',
10142=>'ON',
10143=>'ON',
10144=>'ON',
10145=>'ON',
10146=>'ON',
10147=>'ON',
10148=>'ON',
10149=>'ON',
10150=>'ON',
10151=>'ON',
10152=>'ON',
10153=>'ON',
10154=>'ON',
10155=>'ON',
10156=>'ON',
10157=>'ON',
10158=>'ON',
10159=>'ON',
10161=>'ON',
10162=>'ON',
10163=>'ON',
10164=>'ON',
10165=>'ON',
10166=>'ON',
10167=>'ON',
10168=>'ON',
10169=>'ON',
10170=>'ON',
10171=>'ON',
10172=>'ON',
10173=>'ON',
10174=>'ON',
10176=>'ON',
10177=>'ON',
10178=>'ON',
10179=>'ON',
10180=>'ON',
10181=>'ON',
10182=>'ON',
10183=>'ON',
10184=>'ON',
10185=>'ON',
10186=>'ON',
10192=>'ON',
10193=>'ON',
10194=>'ON',
10195=>'ON',
10196=>'ON',
10197=>'ON',
10198=>'ON',
10199=>'ON',
10200=>'ON',
10201=>'ON',
10202=>'ON',
10203=>'ON',
10204=>'ON',
10205=>'ON',
10206=>'ON',
10207=>'ON',
10208=>'ON',
10209=>'ON',
10210=>'ON',
10211=>'ON',
10212=>'ON',
10213=>'ON',
10214=>'ON',
10215=>'ON',
10216=>'ON',
10217=>'ON',
10218=>'ON',
10219=>'ON',
10224=>'ON',
10225=>'ON',
10226=>'ON',
10227=>'ON',
10228=>'ON',
10229=>'ON',
10230=>'ON',
10231=>'ON',
10232=>'ON',
10233=>'ON',
10234=>'ON',
10235=>'ON',
10236=>'ON',
10237=>'ON',
10238=>'ON',
10239=>'ON',
10240=>'L',
10241=>'L',
10242=>'L',
10243=>'L',
10244=>'L',
10245=>'L',
10246=>'L',
10247=>'L',
10248=>'L',
10249=>'L',
10250=>'L',
10251=>'L',
10252=>'L',
10253=>'L',
10254=>'L',
10255=>'L',
10256=>'L',
10257=>'L',
10258=>'L',
10259=>'L',
10260=>'L',
10261=>'L',
10262=>'L',
10263=>'L',
10264=>'L',
10265=>'L',
10266=>'L',
10267=>'L',
10268=>'L',
10269=>'L',
10270=>'L',
10271=>'L',
10272=>'L',
10273=>'L',
10274=>'L',
10275=>'L',
10276=>'L',
10277=>'L',
10278=>'L',
10279=>'L',
10280=>'L',
10281=>'L',
10282=>'L',
10283=>'L',
10284=>'L',
10285=>'L',
10286=>'L',
10287=>'L',
10288=>'L',
10289=>'L',
10290=>'L',
10291=>'L',
10292=>'L',
10293=>'L',
10294=>'L',
10295=>'L',
10296=>'L',
10297=>'L',
10298=>'L',
10299=>'L',
10300=>'L',
10301=>'L',
10302=>'L',
10303=>'L',
10304=>'L',
10305=>'L',
10306=>'L',
10307=>'L',
10308=>'L',
10309=>'L',
10310=>'L',
10311=>'L',
10312=>'L',
10313=>'L',
10314=>'L',
10315=>'L',
10316=>'L',
10317=>'L',
10318=>'L',
10319=>'L',
10320=>'L',
10321=>'L',
10322=>'L',
10323=>'L',
10324=>'L',
10325=>'L',
10326=>'L',
10327=>'L',
10328=>'L',
10329=>'L',
10330=>'L',
10331=>'L',
10332=>'L',
10333=>'L',
10334=>'L',
10335=>'L',
10336=>'L',
10337=>'L',
10338=>'L',
10339=>'L',
10340=>'L',
10341=>'L',
10342=>'L',
10343=>'L',
10344=>'L',
10345=>'L',
10346=>'L',
10347=>'L',
10348=>'L',
10349=>'L',
10350=>'L',
10351=>'L',
10352=>'L',
10353=>'L',
10354=>'L',
10355=>'L',
10356=>'L',
10357=>'L',
10358=>'L',
10359=>'L',
10360=>'L',
10361=>'L',
10362=>'L',
10363=>'L',
10364=>'L',
10365=>'L',
10366=>'L',
10367=>'L',
10368=>'L',
10369=>'L',
10370=>'L',
10371=>'L',
10372=>'L',
10373=>'L',
10374=>'L',
10375=>'L',
10376=>'L',
10377=>'L',
10378=>'L',
10379=>'L',
10380=>'L',
10381=>'L',
10382=>'L',
10383=>'L',
10384=>'L',
10385=>'L',
10386=>'L',
10387=>'L',
10388=>'L',
10389=>'L',
10390=>'L',
10391=>'L',
10392=>'L',
10393=>'L',
10394=>'L',
10395=>'L',
10396=>'L',
10397=>'L',
10398=>'L',
10399=>'L',
10400=>'L',
10401=>'L',
10402=>'L',
10403=>'L',
10404=>'L',
10405=>'L',
10406=>'L',
10407=>'L',
10408=>'L',
10409=>'L',
10410=>'L',
10411=>'L',
10412=>'L',
10413=>'L',
10414=>'L',
10415=>'L',
10416=>'L',
10417=>'L',
10418=>'L',
10419=>'L',
10420=>'L',
10421=>'L',
10422=>'L',
10423=>'L',
10424=>'L',
10425=>'L',
10426=>'L',
10427=>'L',
10428=>'L',
10429=>'L',
10430=>'L',
10431=>'L',
10432=>'L',
10433=>'L',
10434=>'L',
10435=>'L',
10436=>'L',
10437=>'L',
10438=>'L',
10439=>'L',
10440=>'L',
10441=>'L',
10442=>'L',
10443=>'L',
10444=>'L',
10445=>'L',
10446=>'L',
10447=>'L',
10448=>'L',
10449=>'L',
10450=>'L',
10451=>'L',
10452=>'L',
10453=>'L',
10454=>'L',
10455=>'L',
10456=>'L',
10457=>'L',
10458=>'L',
10459=>'L',
10460=>'L',
10461=>'L',
10462=>'L',
10463=>'L',
10464=>'L',
10465=>'L',
10466=>'L',
10467=>'L',
10468=>'L',
10469=>'L',
10470=>'L',
10471=>'L',
10472=>'L',
10473=>'L',
10474=>'L',
10475=>'L',
10476=>'L',
10477=>'L',
10478=>'L',
10479=>'L',
10480=>'L',
10481=>'L',
10482=>'L',
10483=>'L',
10484=>'L',
10485=>'L',
10486=>'L',
10487=>'L',
10488=>'L',
10489=>'L',
10490=>'L',
10491=>'L',
10492=>'L',
10493=>'L',
10494=>'L',
10495=>'L',
10496=>'ON',
10497=>'ON',
10498=>'ON',
10499=>'ON',
10500=>'ON',
10501=>'ON',
10502=>'ON',
10503=>'ON',
10504=>'ON',
10505=>'ON',
10506=>'ON',
10507=>'ON',
10508=>'ON',
10509=>'ON',
10510=>'ON',
10511=>'ON',
10512=>'ON',
10513=>'ON',
10514=>'ON',
10515=>'ON',
10516=>'ON',
10517=>'ON',
10518=>'ON',
10519=>'ON',
10520=>'ON',
10521=>'ON',
10522=>'ON',
10523=>'ON',
10524=>'ON',
10525=>'ON',
10526=>'ON',
10527=>'ON',
10528=>'ON',
10529=>'ON',
10530=>'ON',
10531=>'ON',
10532=>'ON',
10533=>'ON',
10534=>'ON',
10535=>'ON',
10536=>'ON',
10537=>'ON',
10538=>'ON',
10539=>'ON',
10540=>'ON',
10541=>'ON',
10542=>'ON',
10543=>'ON',
10544=>'ON',
10545=>'ON',
10546=>'ON',
10547=>'ON',
10548=>'ON',
10549=>'ON',
10550=>'ON',
10551=>'ON',
10552=>'ON',
10553=>'ON',
10554=>'ON',
10555=>'ON',
10556=>'ON',
10557=>'ON',
10558=>'ON',
10559=>'ON',
10560=>'ON',
10561=>'ON',
10562=>'ON',
10563=>'ON',
10564=>'ON',
10565=>'ON',
10566=>'ON',
10567=>'ON',
10568=>'ON',
10569=>'ON',
10570=>'ON',
10571=>'ON',
10572=>'ON',
10573=>'ON',
10574=>'ON',
10575=>'ON',
10576=>'ON',
10577=>'ON',
10578=>'ON',
10579=>'ON',
10580=>'ON',
10581=>'ON',
10582=>'ON',
10583=>'ON',
10584=>'ON',
10585=>'ON',
10586=>'ON',
10587=>'ON',
10588=>'ON',
10589=>'ON',
10590=>'ON',
10591=>'ON',
10592=>'ON',
10593=>'ON',
10594=>'ON',
10595=>'ON',
10596=>'ON',
10597=>'ON',
10598=>'ON',
10599=>'ON',
10600=>'ON',
10601=>'ON',
10602=>'ON',
10603=>'ON',
10604=>'ON',
10605=>'ON',
10606=>'ON',
10607=>'ON',
10608=>'ON',
10609=>'ON',
10610=>'ON',
10611=>'ON',
10612=>'ON',
10613=>'ON',
10614=>'ON',
10615=>'ON',
10616=>'ON',
10617=>'ON',
10618=>'ON',
10619=>'ON',
10620=>'ON',
10621=>'ON',
10622=>'ON',
10623=>'ON',
10624=>'ON',
10625=>'ON',
10626=>'ON',
10627=>'ON',
10628=>'ON',
10629=>'ON',
10630=>'ON',
10631=>'ON',
10632=>'ON',
10633=>'ON',
10634=>'ON',
10635=>'ON',
10636=>'ON',
10637=>'ON',
10638=>'ON',
10639=>'ON',
10640=>'ON',
10641=>'ON',
10642=>'ON',
10643=>'ON',
10644=>'ON',
10645=>'ON',
10646=>'ON',
10647=>'ON',
10648=>'ON',
10649=>'ON',
10650=>'ON',
10651=>'ON',
10652=>'ON',
10653=>'ON',
10654=>'ON',
10655=>'ON',
10656=>'ON',
10657=>'ON',
10658=>'ON',
10659=>'ON',
10660=>'ON',
10661=>'ON',
10662=>'ON',
10663=>'ON',
10664=>'ON',
10665=>'ON',
10666=>'ON',
10667=>'ON',
10668=>'ON',
10669=>'ON',
10670=>'ON',
10671=>'ON',
10672=>'ON',
10673=>'ON',
10674=>'ON',
10675=>'ON',
10676=>'ON',
10677=>'ON',
10678=>'ON',
10679=>'ON',
10680=>'ON',
10681=>'ON',
10682=>'ON',
10683=>'ON',
10684=>'ON',
10685=>'ON',
10686=>'ON',
10687=>'ON',
10688=>'ON',
10689=>'ON',
10690=>'ON',
10691=>'ON',
10692=>'ON',
10693=>'ON',
10694=>'ON',
10695=>'ON',
10696=>'ON',
10697=>'ON',
10698=>'ON',
10699=>'ON',
10700=>'ON',
10701=>'ON',
10702=>'ON',
10703=>'ON',
10704=>'ON',
10705=>'ON',
10706=>'ON',
10707=>'ON',
10708=>'ON',
10709=>'ON',
10710=>'ON',
10711=>'ON',
10712=>'ON',
10713=>'ON',
10714=>'ON',
10715=>'ON',
10716=>'ON',
10717=>'ON',
10718=>'ON',
10719=>'ON',
10720=>'ON',
10721=>'ON',
10722=>'ON',
10723=>'ON',
10724=>'ON',
10725=>'ON',
10726=>'ON',
10727=>'ON',
10728=>'ON',
10729=>'ON',
10730=>'ON',
10731=>'ON',
10732=>'ON',
10733=>'ON',
10734=>'ON',
10735=>'ON',
10736=>'ON',
10737=>'ON',
10738=>'ON',
10739=>'ON',
10740=>'ON',
10741=>'ON',
10742=>'ON',
10743=>'ON',
10744=>'ON',
10745=>'ON',
10746=>'ON',
10747=>'ON',
10748=>'ON',
10749=>'ON',
10750=>'ON',
10751=>'ON',
10752=>'ON',
10753=>'ON',
10754=>'ON',
10755=>'ON',
10756=>'ON',
10757=>'ON',
10758=>'ON',
10759=>'ON',
10760=>'ON',
10761=>'ON',
10762=>'ON',
10763=>'ON',
10764=>'ON',
10765=>'ON',
10766=>'ON',
10767=>'ON',
10768=>'ON',
10769=>'ON',
10770=>'ON',
10771=>'ON',
10772=>'ON',
10773=>'ON',
10774=>'ON',
10775=>'ON',
10776=>'ON',
10777=>'ON',
10778=>'ON',
10779=>'ON',
10780=>'ON',
10781=>'ON',
10782=>'ON',
10783=>'ON',
10784=>'ON',
10785=>'ON',
10786=>'ON',
10787=>'ON',
10788=>'ON',
10789=>'ON',
10790=>'ON',
10791=>'ON',
10792=>'ON',
10793=>'ON',
10794=>'ON',
10795=>'ON',
10796=>'ON',
10797=>'ON',
10798=>'ON',
10799=>'ON',
10800=>'ON',
10801=>'ON',
10802=>'ON',
10803=>'ON',
10804=>'ON',
10805=>'ON',
10806=>'ON',
10807=>'ON',
10808=>'ON',
10809=>'ON',
10810=>'ON',
10811=>'ON',
10812=>'ON',
10813=>'ON',
10814=>'ON',
10815=>'ON',
10816=>'ON',
10817=>'ON',
10818=>'ON',
10819=>'ON',
10820=>'ON',
10821=>'ON',
10822=>'ON',
10823=>'ON',
10824=>'ON',
10825=>'ON',
10826=>'ON',
10827=>'ON',
10828=>'ON',
10829=>'ON',
10830=>'ON',
10831=>'ON',
10832=>'ON',
10833=>'ON',
10834=>'ON',
10835=>'ON',
10836=>'ON',
10837=>'ON',
10838=>'ON',
10839=>'ON',
10840=>'ON',
10841=>'ON',
10842=>'ON',
10843=>'ON',
10844=>'ON',
10845=>'ON',
10846=>'ON',
10847=>'ON',
10848=>'ON',
10849=>'ON',
10850=>'ON',
10851=>'ON',
10852=>'ON',
10853=>'ON',
10854=>'ON',
10855=>'ON',
10856=>'ON',
10857=>'ON',
10858=>'ON',
10859=>'ON',
10860=>'ON',
10861=>'ON',
10862=>'ON',
10863=>'ON',
10864=>'ON',
10865=>'ON',
10866=>'ON',
10867=>'ON',
10868=>'ON',
10869=>'ON',
10870=>'ON',
10871=>'ON',
10872=>'ON',
10873=>'ON',
10874=>'ON',
10875=>'ON',
10876=>'ON',
10877=>'ON',
10878=>'ON',
10879=>'ON',
10880=>'ON',
10881=>'ON',
10882=>'ON',
10883=>'ON',
10884=>'ON',
10885=>'ON',
10886=>'ON',
10887=>'ON',
10888=>'ON',
10889=>'ON',
10890=>'ON',
10891=>'ON',
10892=>'ON',
10893=>'ON',
10894=>'ON',
10895=>'ON',
10896=>'ON',
10897=>'ON',
10898=>'ON',
10899=>'ON',
10900=>'ON',
10901=>'ON',
10902=>'ON',
10903=>'ON',
10904=>'ON',
10905=>'ON',
10906=>'ON',
10907=>'ON',
10908=>'ON',
10909=>'ON',
10910=>'ON',
10911=>'ON',
10912=>'ON',
10913=>'ON',
10914=>'ON',
10915=>'ON',
10916=>'ON',
10917=>'ON',
10918=>'ON',
10919=>'ON',
10920=>'ON',
10921=>'ON',
10922=>'ON',
10923=>'ON',
10924=>'ON',
10925=>'ON',
10926=>'ON',
10927=>'ON',
10928=>'ON',
10929=>'ON',
10930=>'ON',
10931=>'ON',
10932=>'ON',
10933=>'ON',
10934=>'ON',
10935=>'ON',
10936=>'ON',
10937=>'ON',
10938=>'ON',
10939=>'ON',
10940=>'ON',
10941=>'ON',
10942=>'ON',
10943=>'ON',
10944=>'ON',
10945=>'ON',
10946=>'ON',
10947=>'ON',
10948=>'ON',
10949=>'ON',
10950=>'ON',
10951=>'ON',
10952=>'ON',
10953=>'ON',
10954=>'ON',
10955=>'ON',
10956=>'ON',
10957=>'ON',
10958=>'ON',
10959=>'ON',
10960=>'ON',
10961=>'ON',
10962=>'ON',
10963=>'ON',
10964=>'ON',
10965=>'ON',
10966=>'ON',
10967=>'ON',
10968=>'ON',
10969=>'ON',
10970=>'ON',
10971=>'ON',
10972=>'ON',
10973=>'ON',
10974=>'ON',
10975=>'ON',
10976=>'ON',
10977=>'ON',
10978=>'ON',
10979=>'ON',
10980=>'ON',
10981=>'ON',
10982=>'ON',
10983=>'ON',
10984=>'ON',
10985=>'ON',
10986=>'ON',
10987=>'ON',
10988=>'ON',
10989=>'ON',
10990=>'ON',
10991=>'ON',
10992=>'ON',
10993=>'ON',
10994=>'ON',
10995=>'ON',
10996=>'ON',
10997=>'ON',
10998=>'ON',
10999=>'ON',
11000=>'ON',
11001=>'ON',
11002=>'ON',
11003=>'ON',
11004=>'ON',
11005=>'ON',
11006=>'ON',
11007=>'ON',
11008=>'ON',
11009=>'ON',
11010=>'ON',
11011=>'ON',
11012=>'ON',
11013=>'ON',
11014=>'ON',
11015=>'ON',
11016=>'ON',
11017=>'ON',
11018=>'ON',
11019=>'ON',
11020=>'ON',
11021=>'ON',
11022=>'ON',
11023=>'ON',
11024=>'ON',
11025=>'ON',
11026=>'ON',
11027=>'ON',
11028=>'ON',
11029=>'ON',
11030=>'ON',
11031=>'ON',
11032=>'ON',
11033=>'ON',
11034=>'ON',
11040=>'ON',
11041=>'ON',
11042=>'ON',
11043=>'ON',
11264=>'L',
11265=>'L',
11266=>'L',
11267=>'L',
11268=>'L',
11269=>'L',
11270=>'L',
11271=>'L',
11272=>'L',
11273=>'L',
11274=>'L',
11275=>'L',
11276=>'L',
11277=>'L',
11278=>'L',
11279=>'L',
11280=>'L',
11281=>'L',
11282=>'L',
11283=>'L',
11284=>'L',
11285=>'L',
11286=>'L',
11287=>'L',
11288=>'L',
11289=>'L',
11290=>'L',
11291=>'L',
11292=>'L',
11293=>'L',
11294=>'L',
11295=>'L',
11296=>'L',
11297=>'L',
11298=>'L',
11299=>'L',
11300=>'L',
11301=>'L',
11302=>'L',
11303=>'L',
11304=>'L',
11305=>'L',
11306=>'L',
11307=>'L',
11308=>'L',
11309=>'L',
11310=>'L',
11312=>'L',
11313=>'L',
11314=>'L',
11315=>'L',
11316=>'L',
11317=>'L',
11318=>'L',
11319=>'L',
11320=>'L',
11321=>'L',
11322=>'L',
11323=>'L',
11324=>'L',
11325=>'L',
11326=>'L',
11327=>'L',
11328=>'L',
11329=>'L',
11330=>'L',
11331=>'L',
11332=>'L',
11333=>'L',
11334=>'L',
11335=>'L',
11336=>'L',
11337=>'L',
11338=>'L',
11339=>'L',
11340=>'L',
11341=>'L',
11342=>'L',
11343=>'L',
11344=>'L',
11345=>'L',
11346=>'L',
11347=>'L',
11348=>'L',
11349=>'L',
11350=>'L',
11351=>'L',
11352=>'L',
11353=>'L',
11354=>'L',
11355=>'L',
11356=>'L',
11357=>'L',
11358=>'L',
11360=>'L',
11361=>'L',
11362=>'L',
11363=>'L',
11364=>'L',
11365=>'L',
11366=>'L',
11367=>'L',
11368=>'L',
11369=>'L',
11370=>'L',
11371=>'L',
11372=>'L',
11380=>'L',
11381=>'L',
11382=>'L',
11383=>'L',
11392=>'L',
11393=>'L',
11394=>'L',
11395=>'L',
11396=>'L',
11397=>'L',
11398=>'L',
11399=>'L',
11400=>'L',
11401=>'L',
11402=>'L',
11403=>'L',
11404=>'L',
11405=>'L',
11406=>'L',
11407=>'L',
11408=>'L',
11409=>'L',
11410=>'L',
11411=>'L',
11412=>'L',
11413=>'L',
11414=>'L',
11415=>'L',
11416=>'L',
11417=>'L',
11418=>'L',
11419=>'L',
11420=>'L',
11421=>'L',
11422=>'L',
11423=>'L',
11424=>'L',
11425=>'L',
11426=>'L',
11427=>'L',
11428=>'L',
11429=>'L',
11430=>'L',
11431=>'L',
11432=>'L',
11433=>'L',
11434=>'L',
11435=>'L',
11436=>'L',
11437=>'L',
11438=>'L',
11439=>'L',
11440=>'L',
11441=>'L',
11442=>'L',
11443=>'L',
11444=>'L',
11445=>'L',
11446=>'L',
11447=>'L',
11448=>'L',
11449=>'L',
11450=>'L',
11451=>'L',
11452=>'L',
11453=>'L',
11454=>'L',
11455=>'L',
11456=>'L',
11457=>'L',
11458=>'L',
11459=>'L',
11460=>'L',
11461=>'L',
11462=>'L',
11463=>'L',
11464=>'L',
11465=>'L',
11466=>'L',
11467=>'L',
11468=>'L',
11469=>'L',
11470=>'L',
11471=>'L',
11472=>'L',
11473=>'L',
11474=>'L',
11475=>'L',
11476=>'L',
11477=>'L',
11478=>'L',
11479=>'L',
11480=>'L',
11481=>'L',
11482=>'L',
11483=>'L',
11484=>'L',
11485=>'L',
11486=>'L',
11487=>'L',
11488=>'L',
11489=>'L',
11490=>'L',
11491=>'L',
11492=>'L',
11493=>'ON',
11494=>'ON',
11495=>'ON',
11496=>'ON',
11497=>'ON',
11498=>'ON',
11513=>'ON',
11514=>'ON',
11515=>'ON',
11516=>'ON',
11517=>'ON',
11518=>'ON',
11519=>'ON',
11520=>'L',
11521=>'L',
11522=>'L',
11523=>'L',
11524=>'L',
11525=>'L',
11526=>'L',
11527=>'L',
11528=>'L',
11529=>'L',
11530=>'L',
11531=>'L',
11532=>'L',
11533=>'L',
11534=>'L',
11535=>'L',
11536=>'L',
11537=>'L',
11538=>'L',
11539=>'L',
11540=>'L',
11541=>'L',
11542=>'L',
11543=>'L',
11544=>'L',
11545=>'L',
11546=>'L',
11547=>'L',
11548=>'L',
11549=>'L',
11550=>'L',
11551=>'L',
11552=>'L',
11553=>'L',
11554=>'L',
11555=>'L',
11556=>'L',
11557=>'L',
11568=>'L',
11569=>'L',
11570=>'L',
11571=>'L',
11572=>'L',
11573=>'L',
11574=>'L',
11575=>'L',
11576=>'L',
11577=>'L',
11578=>'L',
11579=>'L',
11580=>'L',
11581=>'L',
11582=>'L',
11583=>'L',
11584=>'L',
11585=>'L',
11586=>'L',
11587=>'L',
11588=>'L',
11589=>'L',
11590=>'L',
11591=>'L',
11592=>'L',
11593=>'L',
11594=>'L',
11595=>'L',
11596=>'L',
11597=>'L',
11598=>'L',
11599=>'L',
11600=>'L',
11601=>'L',
11602=>'L',
11603=>'L',
11604=>'L',
11605=>'L',
11606=>'L',
11607=>'L',
11608=>'L',
11609=>'L',
11610=>'L',
11611=>'L',
11612=>'L',
11613=>'L',
11614=>'L',
11615=>'L',
11616=>'L',
11617=>'L',
11618=>'L',
11619=>'L',
11620=>'L',
11621=>'L',
11631=>'L',
11648=>'L',
11649=>'L',
11650=>'L',
11651=>'L',
11652=>'L',
11653=>'L',
11654=>'L',
11655=>'L',
11656=>'L',
11657=>'L',
11658=>'L',
11659=>'L',
11660=>'L',
11661=>'L',
11662=>'L',
11663=>'L',
11664=>'L',
11665=>'L',
11666=>'L',
11667=>'L',
11668=>'L',
11669=>'L',
11670=>'L',
11680=>'L',
11681=>'L',
11682=>'L',
11683=>'L',
11684=>'L',
11685=>'L',
11686=>'L',
11688=>'L',
11689=>'L',
11690=>'L',
11691=>'L',
11692=>'L',
11693=>'L',
11694=>'L',
11696=>'L',
11697=>'L',
11698=>'L',
11699=>'L',
11700=>'L',
11701=>'L',
11702=>'L',
11704=>'L',
11705=>'L',
11706=>'L',
11707=>'L',
11708=>'L',
11709=>'L',
11710=>'L',
11712=>'L',
11713=>'L',
11714=>'L',
11715=>'L',
11716=>'L',
11717=>'L',
11718=>'L',
11720=>'L',
11721=>'L',
11722=>'L',
11723=>'L',
11724=>'L',
11725=>'L',
11726=>'L',
11728=>'L',
11729=>'L',
11730=>'L',
11731=>'L',
11732=>'L',
11733=>'L',
11734=>'L',
11736=>'L',
11737=>'L',
11738=>'L',
11739=>'L',
11740=>'L',
11741=>'L',
11742=>'L',
11776=>'ON',
11777=>'ON',
11778=>'ON',
11779=>'ON',
11780=>'ON',
11781=>'ON',
11782=>'ON',
11783=>'ON',
11784=>'ON',
11785=>'ON',
11786=>'ON',
11787=>'ON',
11788=>'ON',
11789=>'ON',
11790=>'ON',
11791=>'ON',
11792=>'ON',
11793=>'ON',
11794=>'ON',
11795=>'ON',
11796=>'ON',
11797=>'ON',
11798=>'ON',
11799=>'ON',
11804=>'ON',
11805=>'ON',
11904=>'ON',
11905=>'ON',
11906=>'ON',
11907=>'ON',
11908=>'ON',
11909=>'ON',
11910=>'ON',
11911=>'ON',
11912=>'ON',
11913=>'ON',
11914=>'ON',
11915=>'ON',
11916=>'ON',
11917=>'ON',
11918=>'ON',
11919=>'ON',
11920=>'ON',
11921=>'ON',
11922=>'ON',
11923=>'ON',
11924=>'ON',
11925=>'ON',
11926=>'ON',
11927=>'ON',
11928=>'ON',
11929=>'ON',
11931=>'ON',
11932=>'ON',
11933=>'ON',
11934=>'ON',
11935=>'ON',
11936=>'ON',
11937=>'ON',
11938=>'ON',
11939=>'ON',
11940=>'ON',
11941=>'ON',
11942=>'ON',
11943=>'ON',
11944=>'ON',
11945=>'ON',
11946=>'ON',
11947=>'ON',
11948=>'ON',
11949=>'ON',
11950=>'ON',
11951=>'ON',
11952=>'ON',
11953=>'ON',
11954=>'ON',
11955=>'ON',
11956=>'ON',
11957=>'ON',
11958=>'ON',
11959=>'ON',
11960=>'ON',
11961=>'ON',
11962=>'ON',
11963=>'ON',
11964=>'ON',
11965=>'ON',
11966=>'ON',
11967=>'ON',
11968=>'ON',
11969=>'ON',
11970=>'ON',
11971=>'ON',
11972=>'ON',
11973=>'ON',
11974=>'ON',
11975=>'ON',
11976=>'ON',
11977=>'ON',
11978=>'ON',
11979=>'ON',
11980=>'ON',
11981=>'ON',
11982=>'ON',
11983=>'ON',
11984=>'ON',
11985=>'ON',
11986=>'ON',
11987=>'ON',
11988=>'ON',
11989=>'ON',
11990=>'ON',
11991=>'ON',
11992=>'ON',
11993=>'ON',
11994=>'ON',
11995=>'ON',
11996=>'ON',
11997=>'ON',
11998=>'ON',
11999=>'ON',
12000=>'ON',
12001=>'ON',
12002=>'ON',
12003=>'ON',
12004=>'ON',
12005=>'ON',
12006=>'ON',
12007=>'ON',
12008=>'ON',
12009=>'ON',
12010=>'ON',
12011=>'ON',
12012=>'ON',
12013=>'ON',
12014=>'ON',
12015=>'ON',
12016=>'ON',
12017=>'ON',
12018=>'ON',
12019=>'ON',
12032=>'ON',
12033=>'ON',
12034=>'ON',
12035=>'ON',
12036=>'ON',
12037=>'ON',
12038=>'ON',
12039=>'ON',
12040=>'ON',
12041=>'ON',
12042=>'ON',
12043=>'ON',
12044=>'ON',
12045=>'ON',
12046=>'ON',
12047=>'ON',
12048=>'ON',
12049=>'ON',
12050=>'ON',
12051=>'ON',
12052=>'ON',
12053=>'ON',
12054=>'ON',
12055=>'ON',
12056=>'ON',
12057=>'ON',
12058=>'ON',
12059=>'ON',
12060=>'ON',
12061=>'ON',
12062=>'ON',
12063=>'ON',
12064=>'ON',
12065=>'ON',
12066=>'ON',
12067=>'ON',
12068=>'ON',
12069=>'ON',
12070=>'ON',
12071=>'ON',
12072=>'ON',
12073=>'ON',
12074=>'ON',
12075=>'ON',
12076=>'ON',
12077=>'ON',
12078=>'ON',
12079=>'ON',
12080=>'ON',
12081=>'ON',
12082=>'ON',
12083=>'ON',
12084=>'ON',
12085=>'ON',
12086=>'ON',
12087=>'ON',
12088=>'ON',
12089=>'ON',
12090=>'ON',
12091=>'ON',
12092=>'ON',
12093=>'ON',
12094=>'ON',
12095=>'ON',
12096=>'ON',
12097=>'ON',
12098=>'ON',
12099=>'ON',
12100=>'ON',
12101=>'ON',
12102=>'ON',
12103=>'ON',
12104=>'ON',
12105=>'ON',
12106=>'ON',
12107=>'ON',
12108=>'ON',
12109=>'ON',
12110=>'ON',
12111=>'ON',
12112=>'ON',
12113=>'ON',
12114=>'ON',
12115=>'ON',
12116=>'ON',
12117=>'ON',
12118=>'ON',
12119=>'ON',
12120=>'ON',
12121=>'ON',
12122=>'ON',
12123=>'ON',
12124=>'ON',
12125=>'ON',
12126=>'ON',
12127=>'ON',
12128=>'ON',
12129=>'ON',
12130=>'ON',
12131=>'ON',
12132=>'ON',
12133=>'ON',
12134=>'ON',
12135=>'ON',
12136=>'ON',
12137=>'ON',
12138=>'ON',
12139=>'ON',
12140=>'ON',
12141=>'ON',
12142=>'ON',
12143=>'ON',
12144=>'ON',
12145=>'ON',
12146=>'ON',
12147=>'ON',
12148=>'ON',
12149=>'ON',
12150=>'ON',
12151=>'ON',
12152=>'ON',
12153=>'ON',
12154=>'ON',
12155=>'ON',
12156=>'ON',
12157=>'ON',
12158=>'ON',
12159=>'ON',
12160=>'ON',
12161=>'ON',
12162=>'ON',
12163=>'ON',
12164=>'ON',
12165=>'ON',
12166=>'ON',
12167=>'ON',
12168=>'ON',
12169=>'ON',
12170=>'ON',
12171=>'ON',
12172=>'ON',
12173=>'ON',
12174=>'ON',
12175=>'ON',
12176=>'ON',
12177=>'ON',
12178=>'ON',
12179=>'ON',
12180=>'ON',
12181=>'ON',
12182=>'ON',
12183=>'ON',
12184=>'ON',
12185=>'ON',
12186=>'ON',
12187=>'ON',
12188=>'ON',
12189=>'ON',
12190=>'ON',
12191=>'ON',
12192=>'ON',
12193=>'ON',
12194=>'ON',
12195=>'ON',
12196=>'ON',
12197=>'ON',
12198=>'ON',
12199=>'ON',
12200=>'ON',
12201=>'ON',
12202=>'ON',
12203=>'ON',
12204=>'ON',
12205=>'ON',
12206=>'ON',
12207=>'ON',
12208=>'ON',
12209=>'ON',
12210=>'ON',
12211=>'ON',
12212=>'ON',
12213=>'ON',
12214=>'ON',
12215=>'ON',
12216=>'ON',
12217=>'ON',
12218=>'ON',
12219=>'ON',
12220=>'ON',
12221=>'ON',
12222=>'ON',
12223=>'ON',
12224=>'ON',
12225=>'ON',
12226=>'ON',
12227=>'ON',
12228=>'ON',
12229=>'ON',
12230=>'ON',
12231=>'ON',
12232=>'ON',
12233=>'ON',
12234=>'ON',
12235=>'ON',
12236=>'ON',
12237=>'ON',
12238=>'ON',
12239=>'ON',
12240=>'ON',
12241=>'ON',
12242=>'ON',
12243=>'ON',
12244=>'ON',
12245=>'ON',
12272=>'ON',
12273=>'ON',
12274=>'ON',
12275=>'ON',
12276=>'ON',
12277=>'ON',
12278=>'ON',
12279=>'ON',
12280=>'ON',
12281=>'ON',
12282=>'ON',
12283=>'ON',
12288=>'WS',
12289=>'ON',
12290=>'ON',
12291=>'ON',
12292=>'ON',
12293=>'L',
12294=>'L',
12295=>'L',
12296=>'ON',
12297=>'ON',
12298=>'ON',
12299=>'ON',
12300=>'ON',
12301=>'ON',
12302=>'ON',
12303=>'ON',
12304=>'ON',
12305=>'ON',
12306=>'ON',
12307=>'ON',
12308=>'ON',
12309=>'ON',
12310=>'ON',
12311=>'ON',
12312=>'ON',
12313=>'ON',
12314=>'ON',
12315=>'ON',
12316=>'ON',
12317=>'ON',
12318=>'ON',
12319=>'ON',
12320=>'ON',
12321=>'L',
12322=>'L',
12323=>'L',
12324=>'L',
12325=>'L',
12326=>'L',
12327=>'L',
12328=>'L',
12329=>'L',
12330=>'NSM',
12331=>'NSM',
12332=>'NSM',
12333=>'NSM',
12334=>'NSM',
12335=>'NSM',
12336=>'ON',
12337=>'L',
12338=>'L',
12339=>'L',
12340=>'L',
12341=>'L',
12342=>'ON',
12343=>'ON',
12344=>'L',
12345=>'L',
12346=>'L',
12347=>'L',
12348=>'L',
12349=>'ON',
12350=>'ON',
12351=>'ON',
12353=>'L',
12354=>'L',
12355=>'L',
12356=>'L',
12357=>'L',
12358=>'L',
12359=>'L',
12360=>'L',
12361=>'L',
12362=>'L',
12363=>'L',
12364=>'L',
12365=>'L',
12366=>'L',
12367=>'L',
12368=>'L',
12369=>'L',
12370=>'L',
12371=>'L',
12372=>'L',
12373=>'L',
12374=>'L',
12375=>'L',
12376=>'L',
12377=>'L',
12378=>'L',
12379=>'L',
12380=>'L',
12381=>'L',
12382=>'L',
12383=>'L',
12384=>'L',
12385=>'L',
12386=>'L',
12387=>'L',
12388=>'L',
12389=>'L',
12390=>'L',
12391=>'L',
12392=>'L',
12393=>'L',
12394=>'L',
12395=>'L',
12396=>'L',
12397=>'L',
12398=>'L',
12399=>'L',
12400=>'L',
12401=>'L',
12402=>'L',
12403=>'L',
12404=>'L',
12405=>'L',
12406=>'L',
12407=>'L',
12408=>'L',
12409=>'L',
12410=>'L',
12411=>'L',
12412=>'L',
12413=>'L',
12414=>'L',
12415=>'L',
12416=>'L',
12417=>'L',
12418=>'L',
12419=>'L',
12420=>'L',
12421=>'L',
12422=>'L',
12423=>'L',
12424=>'L',
12425=>'L',
12426=>'L',
12427=>'L',
12428=>'L',
12429=>'L',
12430=>'L',
12431=>'L',
12432=>'L',
12433=>'L',
12434=>'L',
12435=>'L',
12436=>'L',
12437=>'L',
12438=>'L',
12441=>'NSM',
12442=>'NSM',
12443=>'ON',
12444=>'ON',
12445=>'L',
12446=>'L',
12447=>'L',
12448=>'ON',
12449=>'L',
12450=>'L',
12451=>'L',
12452=>'L',
12453=>'L',
12454=>'L',
12455=>'L',
12456=>'L',
12457=>'L',
12458=>'L',
12459=>'L',
12460=>'L',
12461=>'L',
12462=>'L',
12463=>'L',
12464=>'L',
12465=>'L',
12466=>'L',
12467=>'L',
12468=>'L',
12469=>'L',
12470=>'L',
12471=>'L',
12472=>'L',
12473=>'L',
12474=>'L',
12475=>'L',
12476=>'L',
12477=>'L',
12478=>'L',
12479=>'L',
12480=>'L',
12481=>'L',
12482=>'L',
12483=>'L',
12484=>'L',
12485=>'L',
12486=>'L',
12487=>'L',
12488=>'L',
12489=>'L',
12490=>'L',
12491=>'L',
12492=>'L',
12493=>'L',
12494=>'L',
12495=>'L',
12496=>'L',
12497=>'L',
12498=>'L',
12499=>'L',
12500=>'L',
12501=>'L',
12502=>'L',
12503=>'L',
12504=>'L',
12505=>'L',
12506=>'L',
12507=>'L',
12508=>'L',
12509=>'L',
12510=>'L',
12511=>'L',
12512=>'L',
12513=>'L',
12514=>'L',
12515=>'L',
12516=>'L',
12517=>'L',
12518=>'L',
12519=>'L',
12520=>'L',
12521=>'L',
12522=>'L',
12523=>'L',
12524=>'L',
12525=>'L',
12526=>'L',
12527=>'L',
12528=>'L',
12529=>'L',
12530=>'L',
12531=>'L',
12532=>'L',
12533=>'L',
12534=>'L',
12535=>'L',
12536=>'L',
12537=>'L',
12538=>'L',
12539=>'ON',
12540=>'L',
12541=>'L',
12542=>'L',
12543=>'L',
12549=>'L',
12550=>'L',
12551=>'L',
12552=>'L',
12553=>'L',
12554=>'L',
12555=>'L',
12556=>'L',
12557=>'L',
12558=>'L',
12559=>'L',
12560=>'L',
12561=>'L',
12562=>'L',
12563=>'L',
12564=>'L',
12565=>'L',
12566=>'L',
12567=>'L',
12568=>'L',
12569=>'L',
12570=>'L',
12571=>'L',
12572=>'L',
12573=>'L',
12574=>'L',
12575=>'L',
12576=>'L',
12577=>'L',
12578=>'L',
12579=>'L',
12580=>'L',
12581=>'L',
12582=>'L',
12583=>'L',
12584=>'L',
12585=>'L',
12586=>'L',
12587=>'L',
12588=>'L',
12593=>'L',
12594=>'L',
12595=>'L',
12596=>'L',
12597=>'L',
12598=>'L',
12599=>'L',
12600=>'L',
12601=>'L',
12602=>'L',
12603=>'L',
12604=>'L',
12605=>'L',
12606=>'L',
12607=>'L',
12608=>'L',
12609=>'L',
12610=>'L',
12611=>'L',
12612=>'L',
12613=>'L',
12614=>'L',
12615=>'L',
12616=>'L',
12617=>'L',
12618=>'L',
12619=>'L',
12620=>'L',
12621=>'L',
12622=>'L',
12623=>'L',
12624=>'L',
12625=>'L',
12626=>'L',
12627=>'L',
12628=>'L',
12629=>'L',
12630=>'L',
12631=>'L',
12632=>'L',
12633=>'L',
12634=>'L',
12635=>'L',
12636=>'L',
12637=>'L',
12638=>'L',
12639=>'L',
12640=>'L',
12641=>'L',
12642=>'L',
12643=>'L',
12644=>'L',
12645=>'L',
12646=>'L',
12647=>'L',
12648=>'L',
12649=>'L',
12650=>'L',
12651=>'L',
12652=>'L',
12653=>'L',
12654=>'L',
12655=>'L',
12656=>'L',
12657=>'L',
12658=>'L',
12659=>'L',
12660=>'L',
12661=>'L',
12662=>'L',
12663=>'L',
12664=>'L',
12665=>'L',
12666=>'L',
12667=>'L',
12668=>'L',
12669=>'L',
12670=>'L',
12671=>'L',
12672=>'L',
12673=>'L',
12674=>'L',
12675=>'L',
12676=>'L',
12677=>'L',
12678=>'L',
12679=>'L',
12680=>'L',
12681=>'L',
12682=>'L',
12683=>'L',
12684=>'L',
12685=>'L',
12686=>'L',
12688=>'L',
12689=>'L',
12690=>'L',
12691=>'L',
12692=>'L',
12693=>'L',
12694=>'L',
12695=>'L',
12696=>'L',
12697=>'L',
12698=>'L',
12699=>'L',
12700=>'L',
12701=>'L',
12702=>'L',
12703=>'L',
12704=>'L',
12705=>'L',
12706=>'L',
12707=>'L',
12708=>'L',
12709=>'L',
12710=>'L',
12711=>'L',
12712=>'L',
12713=>'L',
12714=>'L',
12715=>'L',
12716=>'L',
12717=>'L',
12718=>'L',
12719=>'L',
12720=>'L',
12721=>'L',
12722=>'L',
12723=>'L',
12724=>'L',
12725=>'L',
12726=>'L',
12727=>'L',
12736=>'ON',
12737=>'ON',
12738=>'ON',
12739=>'ON',
12740=>'ON',
12741=>'ON',
12742=>'ON',
12743=>'ON',
12744=>'ON',
12745=>'ON',
12746=>'ON',
12747=>'ON',
12748=>'ON',
12749=>'ON',
12750=>'ON',
12751=>'ON',
12784=>'L',
12785=>'L',
12786=>'L',
12787=>'L',
12788=>'L',
12789=>'L',
12790=>'L',
12791=>'L',
12792=>'L',
12793=>'L',
12794=>'L',
12795=>'L',
12796=>'L',
12797=>'L',
12798=>'L',
12799=>'L',
12800=>'L',
12801=>'L',
12802=>'L',
12803=>'L',
12804=>'L',
12805=>'L',
12806=>'L',
12807=>'L',
12808=>'L',
12809=>'L',
12810=>'L',
12811=>'L',
12812=>'L',
12813=>'L',
12814=>'L',
12815=>'L',
12816=>'L',
12817=>'L',
12818=>'L',
12819=>'L',
12820=>'L',
12821=>'L',
12822=>'L',
12823=>'L',
12824=>'L',
12825=>'L',
12826=>'L',
12827=>'L',
12828=>'L',
12829=>'ON',
12830=>'ON',
12832=>'L',
12833=>'L',
12834=>'L',
12835=>'L',
12836=>'L',
12837=>'L',
12838=>'L',
12839=>'L',
12840=>'L',
12841=>'L',
12842=>'L',
12843=>'L',
12844=>'L',
12845=>'L',
12846=>'L',
12847=>'L',
12848=>'L',
12849=>'L',
12850=>'L',
12851=>'L',
12852=>'L',
12853=>'L',
12854=>'L',
12855=>'L',
12856=>'L',
12857=>'L',
12858=>'L',
12859=>'L',
12860=>'L',
12861=>'L',
12862=>'L',
12863=>'L',
12864=>'L',
12865=>'L',
12866=>'L',
12867=>'L',
12880=>'ON',
12881=>'ON',
12882=>'ON',
12883=>'ON',
12884=>'ON',
12885=>'ON',
12886=>'ON',
12887=>'ON',
12888=>'ON',
12889=>'ON',
12890=>'ON',
12891=>'ON',
12892=>'ON',
12893=>'ON',
12894=>'ON',
12895=>'ON',
12896=>'L',
12897=>'L',
12898=>'L',
12899=>'L',
12900=>'L',
12901=>'L',
12902=>'L',
12903=>'L',
12904=>'L',
12905=>'L',
12906=>'L',
12907=>'L',
12908=>'L',
12909=>'L',
12910=>'L',
12911=>'L',
12912=>'L',
12913=>'L',
12914=>'L',
12915=>'L',
12916=>'L',
12917=>'L',
12918=>'L',
12919=>'L',
12920=>'L',
12921=>'L',
12922=>'L',
12923=>'L',
12924=>'ON',
12925=>'ON',
12926=>'ON',
12927=>'L',
12928=>'L',
12929=>'L',
12930=>'L',
12931=>'L',
12932=>'L',
12933=>'L',
12934=>'L',
12935=>'L',
12936=>'L',
12937=>'L',
12938=>'L',
12939=>'L',
12940=>'L',
12941=>'L',
12942=>'L',
12943=>'L',
12944=>'L',
12945=>'L',
12946=>'L',
12947=>'L',
12948=>'L',
12949=>'L',
12950=>'L',
12951=>'L',
12952=>'L',
12953=>'L',
12954=>'L',
12955=>'L',
12956=>'L',
12957=>'L',
12958=>'L',
12959=>'L',
12960=>'L',
12961=>'L',
12962=>'L',
12963=>'L',
12964=>'L',
12965=>'L',
12966=>'L',
12967=>'L',
12968=>'L',
12969=>'L',
12970=>'L',
12971=>'L',
12972=>'L',
12973=>'L',
12974=>'L',
12975=>'L',
12976=>'L',
12977=>'ON',
12978=>'ON',
12979=>'ON',
12980=>'ON',
12981=>'ON',
12982=>'ON',
12983=>'ON',
12984=>'ON',
12985=>'ON',
12986=>'ON',
12987=>'ON',
12988=>'ON',
12989=>'ON',
12990=>'ON',
12991=>'ON',
12992=>'L',
12993=>'L',
12994=>'L',
12995=>'L',
12996=>'L',
12997=>'L',
12998=>'L',
12999=>'L',
13000=>'L',
13001=>'L',
13002=>'L',
13003=>'L',
13004=>'ON',
13005=>'ON',
13006=>'ON',
13007=>'ON',
13008=>'L',
13009=>'L',
13010=>'L',
13011=>'L',
13012=>'L',
13013=>'L',
13014=>'L',
13015=>'L',
13016=>'L',
13017=>'L',
13018=>'L',
13019=>'L',
13020=>'L',
13021=>'L',
13022=>'L',
13023=>'L',
13024=>'L',
13025=>'L',
13026=>'L',
13027=>'L',
13028=>'L',
13029=>'L',
13030=>'L',
13031=>'L',
13032=>'L',
13033=>'L',
13034=>'L',
13035=>'L',
13036=>'L',
13037=>'L',
13038=>'L',
13039=>'L',
13040=>'L',
13041=>'L',
13042=>'L',
13043=>'L',
13044=>'L',
13045=>'L',
13046=>'L',
13047=>'L',
13048=>'L',
13049=>'L',
13050=>'L',
13051=>'L',
13052=>'L',
13053=>'L',
13054=>'L',
13056=>'L',
13057=>'L',
13058=>'L',
13059=>'L',
13060=>'L',
13061=>'L',
13062=>'L',
13063=>'L',
13064=>'L',
13065=>'L',
13066=>'L',
13067=>'L',
13068=>'L',
13069=>'L',
13070=>'L',
13071=>'L',
13072=>'L',
13073=>'L',
13074=>'L',
13075=>'L',
13076=>'L',
13077=>'L',
13078=>'L',
13079=>'L',
13080=>'L',
13081=>'L',
13082=>'L',
13083=>'L',
13084=>'L',
13085=>'L',
13086=>'L',
13087=>'L',
13088=>'L',
13089=>'L',
13090=>'L',
13091=>'L',
13092=>'L',
13093=>'L',
13094=>'L',
13095=>'L',
13096=>'L',
13097=>'L',
13098=>'L',
13099=>'L',
13100=>'L',
13101=>'L',
13102=>'L',
13103=>'L',
13104=>'L',
13105=>'L',
13106=>'L',
13107=>'L',
13108=>'L',
13109=>'L',
13110=>'L',
13111=>'L',
13112=>'L',
13113=>'L',
13114=>'L',
13115=>'L',
13116=>'L',
13117=>'L',
13118=>'L',
13119=>'L',
13120=>'L',
13121=>'L',
13122=>'L',
13123=>'L',
13124=>'L',
13125=>'L',
13126=>'L',
13127=>'L',
13128=>'L',
13129=>'L',
13130=>'L',
13131=>'L',
13132=>'L',
13133=>'L',
13134=>'L',
13135=>'L',
13136=>'L',
13137=>'L',
13138=>'L',
13139=>'L',
13140=>'L',
13141=>'L',
13142=>'L',
13143=>'L',
13144=>'L',
13145=>'L',
13146=>'L',
13147=>'L',
13148=>'L',
13149=>'L',
13150=>'L',
13151=>'L',
13152=>'L',
13153=>'L',
13154=>'L',
13155=>'L',
13156=>'L',
13157=>'L',
13158=>'L',
13159=>'L',
13160=>'L',
13161=>'L',
13162=>'L',
13163=>'L',
13164=>'L',
13165=>'L',
13166=>'L',
13167=>'L',
13168=>'L',
13169=>'L',
13170=>'L',
13171=>'L',
13172=>'L',
13173=>'L',
13174=>'L',
13175=>'ON',
13176=>'ON',
13177=>'ON',
13178=>'ON',
13179=>'L',
13180=>'L',
13181=>'L',
13182=>'L',
13183=>'L',
13184=>'L',
13185=>'L',
13186=>'L',
13187=>'L',
13188=>'L',
13189=>'L',
13190=>'L',
13191=>'L',
13192=>'L',
13193=>'L',
13194=>'L',
13195=>'L',
13196=>'L',
13197=>'L',
13198=>'L',
13199=>'L',
13200=>'L',
13201=>'L',
13202=>'L',
13203=>'L',
13204=>'L',
13205=>'L',
13206=>'L',
13207=>'L',
13208=>'L',
13209=>'L',
13210=>'L',
13211=>'L',
13212=>'L',
13213=>'L',
13214=>'L',
13215=>'L',
13216=>'L',
13217=>'L',
13218=>'L',
13219=>'L',
13220=>'L',
13221=>'L',
13222=>'L',
13223=>'L',
13224=>'L',
13225=>'L',
13226=>'L',
13227=>'L',
13228=>'L',
13229=>'L',
13230=>'L',
13231=>'L',
13232=>'L',
13233=>'L',
13234=>'L',
13235=>'L',
13236=>'L',
13237=>'L',
13238=>'L',
13239=>'L',
13240=>'L',
13241=>'L',
13242=>'L',
13243=>'L',
13244=>'L',
13245=>'L',
13246=>'L',
13247=>'L',
13248=>'L',
13249=>'L',
13250=>'L',
13251=>'L',
13252=>'L',
13253=>'L',
13254=>'L',
13255=>'L',
13256=>'L',
13257=>'L',
13258=>'L',
13259=>'L',
13260=>'L',
13261=>'L',
13262=>'L',
13263=>'L',
13264=>'L',
13265=>'L',
13266=>'L',
13267=>'L',
13268=>'L',
13269=>'L',
13270=>'L',
13271=>'L',
13272=>'L',
13273=>'L',
13274=>'L',
13275=>'L',
13276=>'L',
13277=>'L',
13278=>'ON',
13279=>'ON',
13280=>'L',
13281=>'L',
13282=>'L',
13283=>'L',
13284=>'L',
13285=>'L',
13286=>'L',
13287=>'L',
13288=>'L',
13289=>'L',
13290=>'L',
13291=>'L',
13292=>'L',
13293=>'L',
13294=>'L',
13295=>'L',
13296=>'L',
13297=>'L',
13298=>'L',
13299=>'L',
13300=>'L',
13301=>'L',
13302=>'L',
13303=>'L',
13304=>'L',
13305=>'L',
13306=>'L',
13307=>'L',
13308=>'L',
13309=>'L',
13310=>'L',
13311=>'ON',
13312=>'L',
19893=>'L',
19904=>'ON',
19905=>'ON',
19906=>'ON',
19907=>'ON',
19908=>'ON',
19909=>'ON',
19910=>'ON',
19911=>'ON',
19912=>'ON',
19913=>'ON',
19914=>'ON',
19915=>'ON',
19916=>'ON',
19917=>'ON',
19918=>'ON',
19919=>'ON',
19920=>'ON',
19921=>'ON',
19922=>'ON',
19923=>'ON',
19924=>'ON',
19925=>'ON',
19926=>'ON',
19927=>'ON',
19928=>'ON',
19929=>'ON',
19930=>'ON',
19931=>'ON',
19932=>'ON',
19933=>'ON',
19934=>'ON',
19935=>'ON',
19936=>'ON',
19937=>'ON',
19938=>'ON',
19939=>'ON',
19940=>'ON',
19941=>'ON',
19942=>'ON',
19943=>'ON',
19944=>'ON',
19945=>'ON',
19946=>'ON',
19947=>'ON',
19948=>'ON',
19949=>'ON',
19950=>'ON',
19951=>'ON',
19952=>'ON',
19953=>'ON',
19954=>'ON',
19955=>'ON',
19956=>'ON',
19957=>'ON',
19958=>'ON',
19959=>'ON',
19960=>'ON',
19961=>'ON',
19962=>'ON',
19963=>'ON',
19964=>'ON',
19965=>'ON',
19966=>'ON',
19967=>'ON',
19968=>'L',
40891=>'L',
40960=>'L',
40961=>'L',
40962=>'L',
40963=>'L',
40964=>'L',
40965=>'L',
40966=>'L',
40967=>'L',
40968=>'L',
40969=>'L',
40970=>'L',
40971=>'L',
40972=>'L',
40973=>'L',
40974=>'L',
40975=>'L',
40976=>'L',
40977=>'L',
40978=>'L',
40979=>'L',
40980=>'L',
40981=>'L',
40982=>'L',
40983=>'L',
40984=>'L',
40985=>'L',
40986=>'L',
40987=>'L',
40988=>'L',
40989=>'L',
40990=>'L',
40991=>'L',
40992=>'L',
40993=>'L',
40994=>'L',
40995=>'L',
40996=>'L',
40997=>'L',
40998=>'L',
40999=>'L',
41000=>'L',
41001=>'L',
41002=>'L',
41003=>'L',
41004=>'L',
41005=>'L',
41006=>'L',
41007=>'L',
41008=>'L',
41009=>'L',
41010=>'L',
41011=>'L',
41012=>'L',
41013=>'L',
41014=>'L',
41015=>'L',
41016=>'L',
41017=>'L',
41018=>'L',
41019=>'L',
41020=>'L',
41021=>'L',
41022=>'L',
41023=>'L',
41024=>'L',
41025=>'L',
41026=>'L',
41027=>'L',
41028=>'L',
41029=>'L',
41030=>'L',
41031=>'L',
41032=>'L',
41033=>'L',
41034=>'L',
41035=>'L',
41036=>'L',
41037=>'L',
41038=>'L',
41039=>'L',
41040=>'L',
41041=>'L',
41042=>'L',
41043=>'L',
41044=>'L',
41045=>'L',
41046=>'L',
41047=>'L',
41048=>'L',
41049=>'L',
41050=>'L',
41051=>'L',
41052=>'L',
41053=>'L',
41054=>'L',
41055=>'L',
41056=>'L',
41057=>'L',
41058=>'L',
41059=>'L',
41060=>'L',
41061=>'L',
41062=>'L',
41063=>'L',
41064=>'L',
41065=>'L',
41066=>'L',
41067=>'L',
41068=>'L',
41069=>'L',
41070=>'L',
41071=>'L',
41072=>'L',
41073=>'L',
41074=>'L',
41075=>'L',
41076=>'L',
41077=>'L',
41078=>'L',
41079=>'L',
41080=>'L',
41081=>'L',
41082=>'L',
41083=>'L',
41084=>'L',
41085=>'L',
41086=>'L',
41087=>'L',
41088=>'L',
41089=>'L',
41090=>'L',
41091=>'L',
41092=>'L',
41093=>'L',
41094=>'L',
41095=>'L',
41096=>'L',
41097=>'L',
41098=>'L',
41099=>'L',
41100=>'L',
41101=>'L',
41102=>'L',
41103=>'L',
41104=>'L',
41105=>'L',
41106=>'L',
41107=>'L',
41108=>'L',
41109=>'L',
41110=>'L',
41111=>'L',
41112=>'L',
41113=>'L',
41114=>'L',
41115=>'L',
41116=>'L',
41117=>'L',
41118=>'L',
41119=>'L',
41120=>'L',
41121=>'L',
41122=>'L',
41123=>'L',
41124=>'L',
41125=>'L',
41126=>'L',
41127=>'L',
41128=>'L',
41129=>'L',
41130=>'L',
41131=>'L',
41132=>'L',
41133=>'L',
41134=>'L',
41135=>'L',
41136=>'L',
41137=>'L',
41138=>'L',
41139=>'L',
41140=>'L',
41141=>'L',
41142=>'L',
41143=>'L',
41144=>'L',
41145=>'L',
41146=>'L',
41147=>'L',
41148=>'L',
41149=>'L',
41150=>'L',
41151=>'L',
41152=>'L',
41153=>'L',
41154=>'L',
41155=>'L',
41156=>'L',
41157=>'L',
41158=>'L',
41159=>'L',
41160=>'L',
41161=>'L',
41162=>'L',
41163=>'L',
41164=>'L',
41165=>'L',
41166=>'L',
41167=>'L',
41168=>'L',
41169=>'L',
41170=>'L',
41171=>'L',
41172=>'L',
41173=>'L',
41174=>'L',
41175=>'L',
41176=>'L',
41177=>'L',
41178=>'L',
41179=>'L',
41180=>'L',
41181=>'L',
41182=>'L',
41183=>'L',
41184=>'L',
41185=>'L',
41186=>'L',
41187=>'L',
41188=>'L',
41189=>'L',
41190=>'L',
41191=>'L',
41192=>'L',
41193=>'L',
41194=>'L',
41195=>'L',
41196=>'L',
41197=>'L',
41198=>'L',
41199=>'L',
41200=>'L',
41201=>'L',
41202=>'L',
41203=>'L',
41204=>'L',
41205=>'L',
41206=>'L',
41207=>'L',
41208=>'L',
41209=>'L',
41210=>'L',
41211=>'L',
41212=>'L',
41213=>'L',
41214=>'L',
41215=>'L',
41216=>'L',
41217=>'L',
41218=>'L',
41219=>'L',
41220=>'L',
41221=>'L',
41222=>'L',
41223=>'L',
41224=>'L',
41225=>'L',
41226=>'L',
41227=>'L',
41228=>'L',
41229=>'L',
41230=>'L',
41231=>'L',
41232=>'L',
41233=>'L',
41234=>'L',
41235=>'L',
41236=>'L',
41237=>'L',
41238=>'L',
41239=>'L',
41240=>'L',
41241=>'L',
41242=>'L',
41243=>'L',
41244=>'L',
41245=>'L',
41246=>'L',
41247=>'L',
41248=>'L',
41249=>'L',
41250=>'L',
41251=>'L',
41252=>'L',
41253=>'L',
41254=>'L',
41255=>'L',
41256=>'L',
41257=>'L',
41258=>'L',
41259=>'L',
41260=>'L',
41261=>'L',
41262=>'L',
41263=>'L',
41264=>'L',
41265=>'L',
41266=>'L',
41267=>'L',
41268=>'L',
41269=>'L',
41270=>'L',
41271=>'L',
41272=>'L',
41273=>'L',
41274=>'L',
41275=>'L',
41276=>'L',
41277=>'L',
41278=>'L',
41279=>'L',
41280=>'L',
41281=>'L',
41282=>'L',
41283=>'L',
41284=>'L',
41285=>'L',
41286=>'L',
41287=>'L',
41288=>'L',
41289=>'L',
41290=>'L',
41291=>'L',
41292=>'L',
41293=>'L',
41294=>'L',
41295=>'L',
41296=>'L',
41297=>'L',
41298=>'L',
41299=>'L',
41300=>'L',
41301=>'L',
41302=>'L',
41303=>'L',
41304=>'L',
41305=>'L',
41306=>'L',
41307=>'L',
41308=>'L',
41309=>'L',
41310=>'L',
41311=>'L',
41312=>'L',
41313=>'L',
41314=>'L',
41315=>'L',
41316=>'L',
41317=>'L',
41318=>'L',
41319=>'L',
41320=>'L',
41321=>'L',
41322=>'L',
41323=>'L',
41324=>'L',
41325=>'L',
41326=>'L',
41327=>'L',
41328=>'L',
41329=>'L',
41330=>'L',
41331=>'L',
41332=>'L',
41333=>'L',
41334=>'L',
41335=>'L',
41336=>'L',
41337=>'L',
41338=>'L',
41339=>'L',
41340=>'L',
41341=>'L',
41342=>'L',
41343=>'L',
41344=>'L',
41345=>'L',
41346=>'L',
41347=>'L',
41348=>'L',
41349=>'L',
41350=>'L',
41351=>'L',
41352=>'L',
41353=>'L',
41354=>'L',
41355=>'L',
41356=>'L',
41357=>'L',
41358=>'L',
41359=>'L',
41360=>'L',
41361=>'L',
41362=>'L',
41363=>'L',
41364=>'L',
41365=>'L',
41366=>'L',
41367=>'L',
41368=>'L',
41369=>'L',
41370=>'L',
41371=>'L',
41372=>'L',
41373=>'L',
41374=>'L',
41375=>'L',
41376=>'L',
41377=>'L',
41378=>'L',
41379=>'L',
41380=>'L',
41381=>'L',
41382=>'L',
41383=>'L',
41384=>'L',
41385=>'L',
41386=>'L',
41387=>'L',
41388=>'L',
41389=>'L',
41390=>'L',
41391=>'L',
41392=>'L',
41393=>'L',
41394=>'L',
41395=>'L',
41396=>'L',
41397=>'L',
41398=>'L',
41399=>'L',
41400=>'L',
41401=>'L',
41402=>'L',
41403=>'L',
41404=>'L',
41405=>'L',
41406=>'L',
41407=>'L',
41408=>'L',
41409=>'L',
41410=>'L',
41411=>'L',
41412=>'L',
41413=>'L',
41414=>'L',
41415=>'L',
41416=>'L',
41417=>'L',
41418=>'L',
41419=>'L',
41420=>'L',
41421=>'L',
41422=>'L',
41423=>'L',
41424=>'L',
41425=>'L',
41426=>'L',
41427=>'L',
41428=>'L',
41429=>'L',
41430=>'L',
41431=>'L',
41432=>'L',
41433=>'L',
41434=>'L',
41435=>'L',
41436=>'L',
41437=>'L',
41438=>'L',
41439=>'L',
41440=>'L',
41441=>'L',
41442=>'L',
41443=>'L',
41444=>'L',
41445=>'L',
41446=>'L',
41447=>'L',
41448=>'L',
41449=>'L',
41450=>'L',
41451=>'L',
41452=>'L',
41453=>'L',
41454=>'L',
41455=>'L',
41456=>'L',
41457=>'L',
41458=>'L',
41459=>'L',
41460=>'L',
41461=>'L',
41462=>'L',
41463=>'L',
41464=>'L',
41465=>'L',
41466=>'L',
41467=>'L',
41468=>'L',
41469=>'L',
41470=>'L',
41471=>'L',
41472=>'L',
41473=>'L',
41474=>'L',
41475=>'L',
41476=>'L',
41477=>'L',
41478=>'L',
41479=>'L',
41480=>'L',
41481=>'L',
41482=>'L',
41483=>'L',
41484=>'L',
41485=>'L',
41486=>'L',
41487=>'L',
41488=>'L',
41489=>'L',
41490=>'L',
41491=>'L',
41492=>'L',
41493=>'L',
41494=>'L',
41495=>'L',
41496=>'L',
41497=>'L',
41498=>'L',
41499=>'L',
41500=>'L',
41501=>'L',
41502=>'L',
41503=>'L',
41504=>'L',
41505=>'L',
41506=>'L',
41507=>'L',
41508=>'L',
41509=>'L',
41510=>'L',
41511=>'L',
41512=>'L',
41513=>'L',
41514=>'L',
41515=>'L',
41516=>'L',
41517=>'L',
41518=>'L',
41519=>'L',
41520=>'L',
41521=>'L',
41522=>'L',
41523=>'L',
41524=>'L',
41525=>'L',
41526=>'L',
41527=>'L',
41528=>'L',
41529=>'L',
41530=>'L',
41531=>'L',
41532=>'L',
41533=>'L',
41534=>'L',
41535=>'L',
41536=>'L',
41537=>'L',
41538=>'L',
41539=>'L',
41540=>'L',
41541=>'L',
41542=>'L',
41543=>'L',
41544=>'L',
41545=>'L',
41546=>'L',
41547=>'L',
41548=>'L',
41549=>'L',
41550=>'L',
41551=>'L',
41552=>'L',
41553=>'L',
41554=>'L',
41555=>'L',
41556=>'L',
41557=>'L',
41558=>'L',
41559=>'L',
41560=>'L',
41561=>'L',
41562=>'L',
41563=>'L',
41564=>'L',
41565=>'L',
41566=>'L',
41567=>'L',
41568=>'L',
41569=>'L',
41570=>'L',
41571=>'L',
41572=>'L',
41573=>'L',
41574=>'L',
41575=>'L',
41576=>'L',
41577=>'L',
41578=>'L',
41579=>'L',
41580=>'L',
41581=>'L',
41582=>'L',
41583=>'L',
41584=>'L',
41585=>'L',
41586=>'L',
41587=>'L',
41588=>'L',
41589=>'L',
41590=>'L',
41591=>'L',
41592=>'L',
41593=>'L',
41594=>'L',
41595=>'L',
41596=>'L',
41597=>'L',
41598=>'L',
41599=>'L',
41600=>'L',
41601=>'L',
41602=>'L',
41603=>'L',
41604=>'L',
41605=>'L',
41606=>'L',
41607=>'L',
41608=>'L',
41609=>'L',
41610=>'L',
41611=>'L',
41612=>'L',
41613=>'L',
41614=>'L',
41615=>'L',
41616=>'L',
41617=>'L',
41618=>'L',
41619=>'L',
41620=>'L',
41621=>'L',
41622=>'L',
41623=>'L',
41624=>'L',
41625=>'L',
41626=>'L',
41627=>'L',
41628=>'L',
41629=>'L',
41630=>'L',
41631=>'L',
41632=>'L',
41633=>'L',
41634=>'L',
41635=>'L',
41636=>'L',
41637=>'L',
41638=>'L',
41639=>'L',
41640=>'L',
41641=>'L',
41642=>'L',
41643=>'L',
41644=>'L',
41645=>'L',
41646=>'L',
41647=>'L',
41648=>'L',
41649=>'L',
41650=>'L',
41651=>'L',
41652=>'L',
41653=>'L',
41654=>'L',
41655=>'L',
41656=>'L',
41657=>'L',
41658=>'L',
41659=>'L',
41660=>'L',
41661=>'L',
41662=>'L',
41663=>'L',
41664=>'L',
41665=>'L',
41666=>'L',
41667=>'L',
41668=>'L',
41669=>'L',
41670=>'L',
41671=>'L',
41672=>'L',
41673=>'L',
41674=>'L',
41675=>'L',
41676=>'L',
41677=>'L',
41678=>'L',
41679=>'L',
41680=>'L',
41681=>'L',
41682=>'L',
41683=>'L',
41684=>'L',
41685=>'L',
41686=>'L',
41687=>'L',
41688=>'L',
41689=>'L',
41690=>'L',
41691=>'L',
41692=>'L',
41693=>'L',
41694=>'L',
41695=>'L',
41696=>'L',
41697=>'L',
41698=>'L',
41699=>'L',
41700=>'L',
41701=>'L',
41702=>'L',
41703=>'L',
41704=>'L',
41705=>'L',
41706=>'L',
41707=>'L',
41708=>'L',
41709=>'L',
41710=>'L',
41711=>'L',
41712=>'L',
41713=>'L',
41714=>'L',
41715=>'L',
41716=>'L',
41717=>'L',
41718=>'L',
41719=>'L',
41720=>'L',
41721=>'L',
41722=>'L',
41723=>'L',
41724=>'L',
41725=>'L',
41726=>'L',
41727=>'L',
41728=>'L',
41729=>'L',
41730=>'L',
41731=>'L',
41732=>'L',
41733=>'L',
41734=>'L',
41735=>'L',
41736=>'L',
41737=>'L',
41738=>'L',
41739=>'L',
41740=>'L',
41741=>'L',
41742=>'L',
41743=>'L',
41744=>'L',
41745=>'L',
41746=>'L',
41747=>'L',
41748=>'L',
41749=>'L',
41750=>'L',
41751=>'L',
41752=>'L',
41753=>'L',
41754=>'L',
41755=>'L',
41756=>'L',
41757=>'L',
41758=>'L',
41759=>'L',
41760=>'L',
41761=>'L',
41762=>'L',
41763=>'L',
41764=>'L',
41765=>'L',
41766=>'L',
41767=>'L',
41768=>'L',
41769=>'L',
41770=>'L',
41771=>'L',
41772=>'L',
41773=>'L',
41774=>'L',
41775=>'L',
41776=>'L',
41777=>'L',
41778=>'L',
41779=>'L',
41780=>'L',
41781=>'L',
41782=>'L',
41783=>'L',
41784=>'L',
41785=>'L',
41786=>'L',
41787=>'L',
41788=>'L',
41789=>'L',
41790=>'L',
41791=>'L',
41792=>'L',
41793=>'L',
41794=>'L',
41795=>'L',
41796=>'L',
41797=>'L',
41798=>'L',
41799=>'L',
41800=>'L',
41801=>'L',
41802=>'L',
41803=>'L',
41804=>'L',
41805=>'L',
41806=>'L',
41807=>'L',
41808=>'L',
41809=>'L',
41810=>'L',
41811=>'L',
41812=>'L',
41813=>'L',
41814=>'L',
41815=>'L',
41816=>'L',
41817=>'L',
41818=>'L',
41819=>'L',
41820=>'L',
41821=>'L',
41822=>'L',
41823=>'L',
41824=>'L',
41825=>'L',
41826=>'L',
41827=>'L',
41828=>'L',
41829=>'L',
41830=>'L',
41831=>'L',
41832=>'L',
41833=>'L',
41834=>'L',
41835=>'L',
41836=>'L',
41837=>'L',
41838=>'L',
41839=>'L',
41840=>'L',
41841=>'L',
41842=>'L',
41843=>'L',
41844=>'L',
41845=>'L',
41846=>'L',
41847=>'L',
41848=>'L',
41849=>'L',
41850=>'L',
41851=>'L',
41852=>'L',
41853=>'L',
41854=>'L',
41855=>'L',
41856=>'L',
41857=>'L',
41858=>'L',
41859=>'L',
41860=>'L',
41861=>'L',
41862=>'L',
41863=>'L',
41864=>'L',
41865=>'L',
41866=>'L',
41867=>'L',
41868=>'L',
41869=>'L',
41870=>'L',
41871=>'L',
41872=>'L',
41873=>'L',
41874=>'L',
41875=>'L',
41876=>'L',
41877=>'L',
41878=>'L',
41879=>'L',
41880=>'L',
41881=>'L',
41882=>'L',
41883=>'L',
41884=>'L',
41885=>'L',
41886=>'L',
41887=>'L',
41888=>'L',
41889=>'L',
41890=>'L',
41891=>'L',
41892=>'L',
41893=>'L',
41894=>'L',
41895=>'L',
41896=>'L',
41897=>'L',
41898=>'L',
41899=>'L',
41900=>'L',
41901=>'L',
41902=>'L',
41903=>'L',
41904=>'L',
41905=>'L',
41906=>'L',
41907=>'L',
41908=>'L',
41909=>'L',
41910=>'L',
41911=>'L',
41912=>'L',
41913=>'L',
41914=>'L',
41915=>'L',
41916=>'L',
41917=>'L',
41918=>'L',
41919=>'L',
41920=>'L',
41921=>'L',
41922=>'L',
41923=>'L',
41924=>'L',
41925=>'L',
41926=>'L',
41927=>'L',
41928=>'L',
41929=>'L',
41930=>'L',
41931=>'L',
41932=>'L',
41933=>'L',
41934=>'L',
41935=>'L',
41936=>'L',
41937=>'L',
41938=>'L',
41939=>'L',
41940=>'L',
41941=>'L',
41942=>'L',
41943=>'L',
41944=>'L',
41945=>'L',
41946=>'L',
41947=>'L',
41948=>'L',
41949=>'L',
41950=>'L',
41951=>'L',
41952=>'L',
41953=>'L',
41954=>'L',
41955=>'L',
41956=>'L',
41957=>'L',
41958=>'L',
41959=>'L',
41960=>'L',
41961=>'L',
41962=>'L',
41963=>'L',
41964=>'L',
41965=>'L',
41966=>'L',
41967=>'L',
41968=>'L',
41969=>'L',
41970=>'L',
41971=>'L',
41972=>'L',
41973=>'L',
41974=>'L',
41975=>'L',
41976=>'L',
41977=>'L',
41978=>'L',
41979=>'L',
41980=>'L',
41981=>'L',
41982=>'L',
41983=>'L',
41984=>'L',
41985=>'L',
41986=>'L',
41987=>'L',
41988=>'L',
41989=>'L',
41990=>'L',
41991=>'L',
41992=>'L',
41993=>'L',
41994=>'L',
41995=>'L',
41996=>'L',
41997=>'L',
41998=>'L',
41999=>'L',
42000=>'L',
42001=>'L',
42002=>'L',
42003=>'L',
42004=>'L',
42005=>'L',
42006=>'L',
42007=>'L',
42008=>'L',
42009=>'L',
42010=>'L',
42011=>'L',
42012=>'L',
42013=>'L',
42014=>'L',
42015=>'L',
42016=>'L',
42017=>'L',
42018=>'L',
42019=>'L',
42020=>'L',
42021=>'L',
42022=>'L',
42023=>'L',
42024=>'L',
42025=>'L',
42026=>'L',
42027=>'L',
42028=>'L',
42029=>'L',
42030=>'L',
42031=>'L',
42032=>'L',
42033=>'L',
42034=>'L',
42035=>'L',
42036=>'L',
42037=>'L',
42038=>'L',
42039=>'L',
42040=>'L',
42041=>'L',
42042=>'L',
42043=>'L',
42044=>'L',
42045=>'L',
42046=>'L',
42047=>'L',
42048=>'L',
42049=>'L',
42050=>'L',
42051=>'L',
42052=>'L',
42053=>'L',
42054=>'L',
42055=>'L',
42056=>'L',
42057=>'L',
42058=>'L',
42059=>'L',
42060=>'L',
42061=>'L',
42062=>'L',
42063=>'L',
42064=>'L',
42065=>'L',
42066=>'L',
42067=>'L',
42068=>'L',
42069=>'L',
42070=>'L',
42071=>'L',
42072=>'L',
42073=>'L',
42074=>'L',
42075=>'L',
42076=>'L',
42077=>'L',
42078=>'L',
42079=>'L',
42080=>'L',
42081=>'L',
42082=>'L',
42083=>'L',
42084=>'L',
42085=>'L',
42086=>'L',
42087=>'L',
42088=>'L',
42089=>'L',
42090=>'L',
42091=>'L',
42092=>'L',
42093=>'L',
42094=>'L',
42095=>'L',
42096=>'L',
42097=>'L',
42098=>'L',
42099=>'L',
42100=>'L',
42101=>'L',
42102=>'L',
42103=>'L',
42104=>'L',
42105=>'L',
42106=>'L',
42107=>'L',
42108=>'L',
42109=>'L',
42110=>'L',
42111=>'L',
42112=>'L',
42113=>'L',
42114=>'L',
42115=>'L',
42116=>'L',
42117=>'L',
42118=>'L',
42119=>'L',
42120=>'L',
42121=>'L',
42122=>'L',
42123=>'L',
42124=>'L',
42128=>'ON',
42129=>'ON',
42130=>'ON',
42131=>'ON',
42132=>'ON',
42133=>'ON',
42134=>'ON',
42135=>'ON',
42136=>'ON',
42137=>'ON',
42138=>'ON',
42139=>'ON',
42140=>'ON',
42141=>'ON',
42142=>'ON',
42143=>'ON',
42144=>'ON',
42145=>'ON',
42146=>'ON',
42147=>'ON',
42148=>'ON',
42149=>'ON',
42150=>'ON',
42151=>'ON',
42152=>'ON',
42153=>'ON',
42154=>'ON',
42155=>'ON',
42156=>'ON',
42157=>'ON',
42158=>'ON',
42159=>'ON',
42160=>'ON',
42161=>'ON',
42162=>'ON',
42163=>'ON',
42164=>'ON',
42165=>'ON',
42166=>'ON',
42167=>'ON',
42168=>'ON',
42169=>'ON',
42170=>'ON',
42171=>'ON',
42172=>'ON',
42173=>'ON',
42174=>'ON',
42175=>'ON',
42176=>'ON',
42177=>'ON',
42178=>'ON',
42179=>'ON',
42180=>'ON',
42181=>'ON',
42182=>'ON',
42752=>'ON',
42753=>'ON',
42754=>'ON',
42755=>'ON',
42756=>'ON',
42757=>'ON',
42758=>'ON',
42759=>'ON',
42760=>'ON',
42761=>'ON',
42762=>'ON',
42763=>'ON',
42764=>'ON',
42765=>'ON',
42766=>'ON',
42767=>'ON',
42768=>'ON',
42769=>'ON',
42770=>'ON',
42771=>'ON',
42772=>'ON',
42773=>'ON',
42774=>'ON',
42775=>'ON',
42776=>'ON',
42777=>'ON',
42778=>'ON',
42784=>'ON',
42785=>'ON',
43008=>'L',
43009=>'L',
43010=>'NSM',
43011=>'L',
43012=>'L',
43013=>'L',
43014=>'NSM',
43015=>'L',
43016=>'L',
43017=>'L',
43018=>'L',
43019=>'NSM',
43020=>'L',
43021=>'L',
43022=>'L',
43023=>'L',
43024=>'L',
43025=>'L',
43026=>'L',
43027=>'L',
43028=>'L',
43029=>'L',
43030=>'L',
43031=>'L',
43032=>'L',
43033=>'L',
43034=>'L',
43035=>'L',
43036=>'L',
43037=>'L',
43038=>'L',
43039=>'L',
43040=>'L',
43041=>'L',
43042=>'L',
43043=>'L',
43044=>'L',
43045=>'NSM',
43046=>'NSM',
43047=>'L',
43048=>'ON',
43049=>'ON',
43050=>'ON',
43051=>'ON',
43072=>'L',
43073=>'L',
43074=>'L',
43075=>'L',
43076=>'L',
43077=>'L',
43078=>'L',
43079=>'L',
43080=>'L',
43081=>'L',
43082=>'L',
43083=>'L',
43084=>'L',
43085=>'L',
43086=>'L',
43087=>'L',
43088=>'L',
43089=>'L',
43090=>'L',
43091=>'L',
43092=>'L',
43093=>'L',
43094=>'L',
43095=>'L',
43096=>'L',
43097=>'L',
43098=>'L',
43099=>'L',
43100=>'L',
43101=>'L',
43102=>'L',
43103=>'L',
43104=>'L',
43105=>'L',
43106=>'L',
43107=>'L',
43108=>'L',
43109=>'L',
43110=>'L',
43111=>'L',
43112=>'L',
43113=>'L',
43114=>'L',
43115=>'L',
43116=>'L',
43117=>'L',
43118=>'L',
43119=>'L',
43120=>'L',
43121=>'L',
43122=>'L',
43123=>'L',
43124=>'ON',
43125=>'ON',
43126=>'ON',
43127=>'ON',
44032=>'L',
55203=>'L',
55296=>'L',
56191=>'L',
56192=>'L',
56319=>'L',
56320=>'L',
57343=>'L',
57344=>'L',
63743=>'L',
63744=>'L',
63745=>'L',
63746=>'L',
63747=>'L',
63748=>'L',
63749=>'L',
63750=>'L',
63751=>'L',
63752=>'L',
63753=>'L',
63754=>'L',
63755=>'L',
63756=>'L',
63757=>'L',
63758=>'L',
63759=>'L',
63760=>'L',
63761=>'L',
63762=>'L',
63763=>'L',
63764=>'L',
63765=>'L',
63766=>'L',
63767=>'L',
63768=>'L',
63769=>'L',
63770=>'L',
63771=>'L',
63772=>'L',
63773=>'L',
63774=>'L',
63775=>'L',
63776=>'L',
63777=>'L',
63778=>'L',
63779=>'L',
63780=>'L',
63781=>'L',
63782=>'L',
63783=>'L',
63784=>'L',
63785=>'L',
63786=>'L',
63787=>'L',
63788=>'L',
63789=>'L',
63790=>'L',
63791=>'L',
63792=>'L',
63793=>'L',
63794=>'L',
63795=>'L',
63796=>'L',
63797=>'L',
63798=>'L',
63799=>'L',
63800=>'L',
63801=>'L',
63802=>'L',
63803=>'L',
63804=>'L',
63805=>'L',
63806=>'L',
63807=>'L',
63808=>'L',
63809=>'L',
63810=>'L',
63811=>'L',
63812=>'L',
63813=>'L',
63814=>'L',
63815=>'L',
63816=>'L',
63817=>'L',
63818=>'L',
63819=>'L',
63820=>'L',
63821=>'L',
63822=>'L',
63823=>'L',
63824=>'L',
63825=>'L',
63826=>'L',
63827=>'L',
63828=>'L',
63829=>'L',
63830=>'L',
63831=>'L',
63832=>'L',
63833=>'L',
63834=>'L',
63835=>'L',
63836=>'L',
63837=>'L',
63838=>'L',
63839=>'L',
63840=>'L',
63841=>'L',
63842=>'L',
63843=>'L',
63844=>'L',
63845=>'L',
63846=>'L',
63847=>'L',
63848=>'L',
63849=>'L',
63850=>'L',
63851=>'L',
63852=>'L',
63853=>'L',
63854=>'L',
63855=>'L',
63856=>'L',
63857=>'L',
63858=>'L',
63859=>'L',
63860=>'L',
63861=>'L',
63862=>'L',
63863=>'L',
63864=>'L',
63865=>'L',
63866=>'L',
63867=>'L',
63868=>'L',
63869=>'L',
63870=>'L',
63871=>'L',
63872=>'L',
63873=>'L',
63874=>'L',
63875=>'L',
63876=>'L',
63877=>'L',
63878=>'L',
63879=>'L',
63880=>'L',
63881=>'L',
63882=>'L',
63883=>'L',
63884=>'L',
63885=>'L',
63886=>'L',
63887=>'L',
63888=>'L',
63889=>'L',
63890=>'L',
63891=>'L',
63892=>'L',
63893=>'L',
63894=>'L',
63895=>'L',
63896=>'L',
63897=>'L',
63898=>'L',
63899=>'L',
63900=>'L',
63901=>'L',
63902=>'L',
63903=>'L',
63904=>'L',
63905=>'L',
63906=>'L',
63907=>'L',
63908=>'L',
63909=>'L',
63910=>'L',
63911=>'L',
63912=>'L',
63913=>'L',
63914=>'L',
63915=>'L',
63916=>'L',
63917=>'L',
63918=>'L',
63919=>'L',
63920=>'L',
63921=>'L',
63922=>'L',
63923=>'L',
63924=>'L',
63925=>'L',
63926=>'L',
63927=>'L',
63928=>'L',
63929=>'L',
63930=>'L',
63931=>'L',
63932=>'L',
63933=>'L',
63934=>'L',
63935=>'L',
63936=>'L',
63937=>'L',
63938=>'L',
63939=>'L',
63940=>'L',
63941=>'L',
63942=>'L',
63943=>'L',
63944=>'L',
63945=>'L',
63946=>'L',
63947=>'L',
63948=>'L',
63949=>'L',
63950=>'L',
63951=>'L',
63952=>'L',
63953=>'L',
63954=>'L',
63955=>'L',
63956=>'L',
63957=>'L',
63958=>'L',
63959=>'L',
63960=>'L',
63961=>'L',
63962=>'L',
63963=>'L',
63964=>'L',
63965=>'L',
63966=>'L',
63967=>'L',
63968=>'L',
63969=>'L',
63970=>'L',
63971=>'L',
63972=>'L',
63973=>'L',
63974=>'L',
63975=>'L',
63976=>'L',
63977=>'L',
63978=>'L',
63979=>'L',
63980=>'L',
63981=>'L',
63982=>'L',
63983=>'L',
63984=>'L',
63985=>'L',
63986=>'L',
63987=>'L',
63988=>'L',
63989=>'L',
63990=>'L',
63991=>'L',
63992=>'L',
63993=>'L',
63994=>'L',
63995=>'L',
63996=>'L',
63997=>'L',
63998=>'L',
63999=>'L',
64000=>'L',
64001=>'L',
64002=>'L',
64003=>'L',
64004=>'L',
64005=>'L',
64006=>'L',
64007=>'L',
64008=>'L',
64009=>'L',
64010=>'L',
64011=>'L',
64012=>'L',
64013=>'L',
64014=>'L',
64015=>'L',
64016=>'L',
64017=>'L',
64018=>'L',
64019=>'L',
64020=>'L',
64021=>'L',
64022=>'L',
64023=>'L',
64024=>'L',
64025=>'L',
64026=>'L',
64027=>'L',
64028=>'L',
64029=>'L',
64030=>'L',
64031=>'L',
64032=>'L',
64033=>'L',
64034=>'L',
64035=>'L',
64036=>'L',
64037=>'L',
64038=>'L',
64039=>'L',
64040=>'L',
64041=>'L',
64042=>'L',
64043=>'L',
64044=>'L',
64045=>'L',
64048=>'L',
64049=>'L',
64050=>'L',
64051=>'L',
64052=>'L',
64053=>'L',
64054=>'L',
64055=>'L',
64056=>'L',
64057=>'L',
64058=>'L',
64059=>'L',
64060=>'L',
64061=>'L',
64062=>'L',
64063=>'L',
64064=>'L',
64065=>'L',
64066=>'L',
64067=>'L',
64068=>'L',
64069=>'L',
64070=>'L',
64071=>'L',
64072=>'L',
64073=>'L',
64074=>'L',
64075=>'L',
64076=>'L',
64077=>'L',
64078=>'L',
64079=>'L',
64080=>'L',
64081=>'L',
64082=>'L',
64083=>'L',
64084=>'L',
64085=>'L',
64086=>'L',
64087=>'L',
64088=>'L',
64089=>'L',
64090=>'L',
64091=>'L',
64092=>'L',
64093=>'L',
64094=>'L',
64095=>'L',
64096=>'L',
64097=>'L',
64098=>'L',
64099=>'L',
64100=>'L',
64101=>'L',
64102=>'L',
64103=>'L',
64104=>'L',
64105=>'L',
64106=>'L',
64112=>'L',
64113=>'L',
64114=>'L',
64115=>'L',
64116=>'L',
64117=>'L',
64118=>'L',
64119=>'L',
64120=>'L',
64121=>'L',
64122=>'L',
64123=>'L',
64124=>'L',
64125=>'L',
64126=>'L',
64127=>'L',
64128=>'L',
64129=>'L',
64130=>'L',
64131=>'L',
64132=>'L',
64133=>'L',
64134=>'L',
64135=>'L',
64136=>'L',
64137=>'L',
64138=>'L',
64139=>'L',
64140=>'L',
64141=>'L',
64142=>'L',
64143=>'L',
64144=>'L',
64145=>'L',
64146=>'L',
64147=>'L',
64148=>'L',
64149=>'L',
64150=>'L',
64151=>'L',
64152=>'L',
64153=>'L',
64154=>'L',
64155=>'L',
64156=>'L',
64157=>'L',
64158=>'L',
64159=>'L',
64160=>'L',
64161=>'L',
64162=>'L',
64163=>'L',
64164=>'L',
64165=>'L',
64166=>'L',
64167=>'L',
64168=>'L',
64169=>'L',
64170=>'L',
64171=>'L',
64172=>'L',
64173=>'L',
64174=>'L',
64175=>'L',
64176=>'L',
64177=>'L',
64178=>'L',
64179=>'L',
64180=>'L',
64181=>'L',
64182=>'L',
64183=>'L',
64184=>'L',
64185=>'L',
64186=>'L',
64187=>'L',
64188=>'L',
64189=>'L',
64190=>'L',
64191=>'L',
64192=>'L',
64193=>'L',
64194=>'L',
64195=>'L',
64196=>'L',
64197=>'L',
64198=>'L',
64199=>'L',
64200=>'L',
64201=>'L',
64202=>'L',
64203=>'L',
64204=>'L',
64205=>'L',
64206=>'L',
64207=>'L',
64208=>'L',
64209=>'L',
64210=>'L',
64211=>'L',
64212=>'L',
64213=>'L',
64214=>'L',
64215=>'L',
64216=>'L',
64217=>'L',
64256=>'L',
64257=>'L',
64258=>'L',
64259=>'L',
64260=>'L',
64261=>'L',
64262=>'L',
64275=>'L',
64276=>'L',
64277=>'L',
64278=>'L',
64279=>'L',
64285=>'R',
64286=>'NSM',
64287=>'R',
64288=>'R',
64289=>'R',
64290=>'R',
64291=>'R',
64292=>'R',
64293=>'R',
64294=>'R',
64295=>'R',
64296=>'R',
64297=>'ES',
64298=>'R',
64299=>'R',
64300=>'R',
64301=>'R',
64302=>'R',
64303=>'R',
64304=>'R',
64305=>'R',
64306=>'R',
64307=>'R',
64308=>'R',
64309=>'R',
64310=>'R',
64312=>'R',
64313=>'R',
64314=>'R',
64315=>'R',
64316=>'R',
64318=>'R',
64320=>'R',
64321=>'R',
64323=>'R',
64324=>'R',
64326=>'R',
64327=>'R',
64328=>'R',
64329=>'R',
64330=>'R',
64331=>'R',
64332=>'R',
64333=>'R',
64334=>'R',
64335=>'R',
64336=>'AL',
64337=>'AL',
64338=>'AL',
64339=>'AL',
64340=>'AL',
64341=>'AL',
64342=>'AL',
64343=>'AL',
64344=>'AL',
64345=>'AL',
64346=>'AL',
64347=>'AL',
64348=>'AL',
64349=>'AL',
64350=>'AL',
64351=>'AL',
64352=>'AL',
64353=>'AL',
64354=>'AL',
64355=>'AL',
64356=>'AL',
64357=>'AL',
64358=>'AL',
64359=>'AL',
64360=>'AL',
64361=>'AL',
64362=>'AL',
64363=>'AL',
64364=>'AL',
64365=>'AL',
64366=>'AL',
64367=>'AL',
64368=>'AL',
64369=>'AL',
64370=>'AL',
64371=>'AL',
64372=>'AL',
64373=>'AL',
64374=>'AL',
64375=>'AL',
64376=>'AL',
64377=>'AL',
64378=>'AL',
64379=>'AL',
64380=>'AL',
64381=>'AL',
64382=>'AL',
64383=>'AL',
64384=>'AL',
64385=>'AL',
64386=>'AL',
64387=>'AL',
64388=>'AL',
64389=>'AL',
64390=>'AL',
64391=>'AL',
64392=>'AL',
64393=>'AL',
64394=>'AL',
64395=>'AL',
64396=>'AL',
64397=>'AL',
64398=>'AL',
64399=>'AL',
64400=>'AL',
64401=>'AL',
64402=>'AL',
64403=>'AL',
64404=>'AL',
64405=>'AL',
64406=>'AL',
64407=>'AL',
64408=>'AL',
64409=>'AL',
64410=>'AL',
64411=>'AL',
64412=>'AL',
64413=>'AL',
64414=>'AL',
64415=>'AL',
64416=>'AL',
64417=>'AL',
64418=>'AL',
64419=>'AL',
64420=>'AL',
64421=>'AL',
64422=>'AL',
64423=>'AL',
64424=>'AL',
64425=>'AL',
64426=>'AL',
64427=>'AL',
64428=>'AL',
64429=>'AL',
64430=>'AL',
64431=>'AL',
64432=>'AL',
64433=>'AL',
64467=>'AL',
64468=>'AL',
64469=>'AL',
64470=>'AL',
64471=>'AL',
64472=>'AL',
64473=>'AL',
64474=>'AL',
64475=>'AL',
64476=>'AL',
64477=>'AL',
64478=>'AL',
64479=>'AL',
64480=>'AL',
64481=>'AL',
64482=>'AL',
64483=>'AL',
64484=>'AL',
64485=>'AL',
64486=>'AL',
64487=>'AL',
64488=>'AL',
64489=>'AL',
64490=>'AL',
64491=>'AL',
64492=>'AL',
64493=>'AL',
64494=>'AL',
64495=>'AL',
64496=>'AL',
64497=>'AL',
64498=>'AL',
64499=>'AL',
64500=>'AL',
64501=>'AL',
64502=>'AL',
64503=>'AL',
64504=>'AL',
64505=>'AL',
64506=>'AL',
64507=>'AL',
64508=>'AL',
64509=>'AL',
64510=>'AL',
64511=>'AL',
64512=>'AL',
64513=>'AL',
64514=>'AL',
64515=>'AL',
64516=>'AL',
64517=>'AL',
64518=>'AL',
64519=>'AL',
64520=>'AL',
64521=>'AL',
64522=>'AL',
64523=>'AL',
64524=>'AL',
64525=>'AL',
64526=>'AL',
64527=>'AL',
64528=>'AL',
64529=>'AL',
64530=>'AL',
64531=>'AL',
64532=>'AL',
64533=>'AL',
64534=>'AL',
64535=>'AL',
64536=>'AL',
64537=>'AL',
64538=>'AL',
64539=>'AL',
64540=>'AL',
64541=>'AL',
64542=>'AL',
64543=>'AL',
64544=>'AL',
64545=>'AL',
64546=>'AL',
64547=>'AL',
64548=>'AL',
64549=>'AL',
64550=>'AL',
64551=>'AL',
64552=>'AL',
64553=>'AL',
64554=>'AL',
64555=>'AL',
64556=>'AL',
64557=>'AL',
64558=>'AL',
64559=>'AL',
64560=>'AL',
64561=>'AL',
64562=>'AL',
64563=>'AL',
64564=>'AL',
64565=>'AL',
64566=>'AL',
64567=>'AL',
64568=>'AL',
64569=>'AL',
64570=>'AL',
64571=>'AL',
64572=>'AL',
64573=>'AL',
64574=>'AL',
64575=>'AL',
64576=>'AL',
64577=>'AL',
64578=>'AL',
64579=>'AL',
64580=>'AL',
64581=>'AL',
64582=>'AL',
64583=>'AL',
64584=>'AL',
64585=>'AL',
64586=>'AL',
64587=>'AL',
64588=>'AL',
64589=>'AL',
64590=>'AL',
64591=>'AL',
64592=>'AL',
64593=>'AL',
64594=>'AL',
64595=>'AL',
64596=>'AL',
64597=>'AL',
64598=>'AL',
64599=>'AL',
64600=>'AL',
64601=>'AL',
64602=>'AL',
64603=>'AL',
64604=>'AL',
64605=>'AL',
64606=>'AL',
64607=>'AL',
64608=>'AL',
64609=>'AL',
64610=>'AL',
64611=>'AL',
64612=>'AL',
64613=>'AL',
64614=>'AL',
64615=>'AL',
64616=>'AL',
64617=>'AL',
64618=>'AL',
64619=>'AL',
64620=>'AL',
64621=>'AL',
64622=>'AL',
64623=>'AL',
64624=>'AL',
64625=>'AL',
64626=>'AL',
64627=>'AL',
64628=>'AL',
64629=>'AL',
64630=>'AL',
64631=>'AL',
64632=>'AL',
64633=>'AL',
64634=>'AL',
64635=>'AL',
64636=>'AL',
64637=>'AL',
64638=>'AL',
64639=>'AL',
64640=>'AL',
64641=>'AL',
64642=>'AL',
64643=>'AL',
64644=>'AL',
64645=>'AL',
64646=>'AL',
64647=>'AL',
64648=>'AL',
64649=>'AL',
64650=>'AL',
64651=>'AL',
64652=>'AL',
64653=>'AL',
64654=>'AL',
64655=>'AL',
64656=>'AL',
64657=>'AL',
64658=>'AL',
64659=>'AL',
64660=>'AL',
64661=>'AL',
64662=>'AL',
64663=>'AL',
64664=>'AL',
64665=>'AL',
64666=>'AL',
64667=>'AL',
64668=>'AL',
64669=>'AL',
64670=>'AL',
64671=>'AL',
64672=>'AL',
64673=>'AL',
64674=>'AL',
64675=>'AL',
64676=>'AL',
64677=>'AL',
64678=>'AL',
64679=>'AL',
64680=>'AL',
64681=>'AL',
64682=>'AL',
64683=>'AL',
64684=>'AL',
64685=>'AL',
64686=>'AL',
64687=>'AL',
64688=>'AL',
64689=>'AL',
64690=>'AL',
64691=>'AL',
64692=>'AL',
64693=>'AL',
64694=>'AL',
64695=>'AL',
64696=>'AL',
64697=>'AL',
64698=>'AL',
64699=>'AL',
64700=>'AL',
64701=>'AL',
64702=>'AL',
64703=>'AL',
64704=>'AL',
64705=>'AL',
64706=>'AL',
64707=>'AL',
64708=>'AL',
64709=>'AL',
64710=>'AL',
64711=>'AL',
64712=>'AL',
64713=>'AL',
64714=>'AL',
64715=>'AL',
64716=>'AL',
64717=>'AL',
64718=>'AL',
64719=>'AL',
64720=>'AL',
64721=>'AL',
64722=>'AL',
64723=>'AL',
64724=>'AL',
64725=>'AL',
64726=>'AL',
64727=>'AL',
64728=>'AL',
64729=>'AL',
64730=>'AL',
64731=>'AL',
64732=>'AL',
64733=>'AL',
64734=>'AL',
64735=>'AL',
64736=>'AL',
64737=>'AL',
64738=>'AL',
64739=>'AL',
64740=>'AL',
64741=>'AL',
64742=>'AL',
64743=>'AL',
64744=>'AL',
64745=>'AL',
64746=>'AL',
64747=>'AL',
64748=>'AL',
64749=>'AL',
64750=>'AL',
64751=>'AL',
64752=>'AL',
64753=>'AL',
64754=>'AL',
64755=>'AL',
64756=>'AL',
64757=>'AL',
64758=>'AL',
64759=>'AL',
64760=>'AL',
64761=>'AL',
64762=>'AL',
64763=>'AL',
64764=>'AL',
64765=>'AL',
64766=>'AL',
64767=>'AL',
64768=>'AL',
64769=>'AL',
64770=>'AL',
64771=>'AL',
64772=>'AL',
64773=>'AL',
64774=>'AL',
64775=>'AL',
64776=>'AL',
64777=>'AL',
64778=>'AL',
64779=>'AL',
64780=>'AL',
64781=>'AL',
64782=>'AL',
64783=>'AL',
64784=>'AL',
64785=>'AL',
64786=>'AL',
64787=>'AL',
64788=>'AL',
64789=>'AL',
64790=>'AL',
64791=>'AL',
64792=>'AL',
64793=>'AL',
64794=>'AL',
64795=>'AL',
64796=>'AL',
64797=>'AL',
64798=>'AL',
64799=>'AL',
64800=>'AL',
64801=>'AL',
64802=>'AL',
64803=>'AL',
64804=>'AL',
64805=>'AL',
64806=>'AL',
64807=>'AL',
64808=>'AL',
64809=>'AL',
64810=>'AL',
64811=>'AL',
64812=>'AL',
64813=>'AL',
64814=>'AL',
64815=>'AL',
64816=>'AL',
64817=>'AL',
64818=>'AL',
64819=>'AL',
64820=>'AL',
64821=>'AL',
64822=>'AL',
64823=>'AL',
64824=>'AL',
64825=>'AL',
64826=>'AL',
64827=>'AL',
64828=>'AL',
64829=>'AL',
64830=>'ON',
64831=>'ON',
64848=>'AL',
64849=>'AL',
64850=>'AL',
64851=>'AL',
64852=>'AL',
64853=>'AL',
64854=>'AL',
64855=>'AL',
64856=>'AL',
64857=>'AL',
64858=>'AL',
64859=>'AL',
64860=>'AL',
64861=>'AL',
64862=>'AL',
64863=>'AL',
64864=>'AL',
64865=>'AL',
64866=>'AL',
64867=>'AL',
64868=>'AL',
64869=>'AL',
64870=>'AL',
64871=>'AL',
64872=>'AL',
64873=>'AL',
64874=>'AL',
64875=>'AL',
64876=>'AL',
64877=>'AL',
64878=>'AL',
64879=>'AL',
64880=>'AL',
64881=>'AL',
64882=>'AL',
64883=>'AL',
64884=>'AL',
64885=>'AL',
64886=>'AL',
64887=>'AL',
64888=>'AL',
64889=>'AL',
64890=>'AL',
64891=>'AL',
64892=>'AL',
64893=>'AL',
64894=>'AL',
64895=>'AL',
64896=>'AL',
64897=>'AL',
64898=>'AL',
64899=>'AL',
64900=>'AL',
64901=>'AL',
64902=>'AL',
64903=>'AL',
64904=>'AL',
64905=>'AL',
64906=>'AL',
64907=>'AL',
64908=>'AL',
64909=>'AL',
64910=>'AL',
64911=>'AL',
64914=>'AL',
64915=>'AL',
64916=>'AL',
64917=>'AL',
64918=>'AL',
64919=>'AL',
64920=>'AL',
64921=>'AL',
64922=>'AL',
64923=>'AL',
64924=>'AL',
64925=>'AL',
64926=>'AL',
64927=>'AL',
64928=>'AL',
64929=>'AL',
64930=>'AL',
64931=>'AL',
64932=>'AL',
64933=>'AL',
64934=>'AL',
64935=>'AL',
64936=>'AL',
64937=>'AL',
64938=>'AL',
64939=>'AL',
64940=>'AL',
64941=>'AL',
64942=>'AL',
64943=>'AL',
64944=>'AL',
64945=>'AL',
64946=>'AL',
64947=>'AL',
64948=>'AL',
64949=>'AL',
64950=>'AL',
64951=>'AL',
64952=>'AL',
64953=>'AL',
64954=>'AL',
64955=>'AL',
64956=>'AL',
64957=>'AL',
64958=>'AL',
64959=>'AL',
64960=>'AL',
64961=>'AL',
64962=>'AL',
64963=>'AL',
64964=>'AL',
64965=>'AL',
64966=>'AL',
64967=>'AL',
65008=>'AL',
65009=>'AL',
65010=>'AL',
65011=>'AL',
65012=>'AL',
65013=>'AL',
65014=>'AL',
65015=>'AL',
65016=>'AL',
65017=>'AL',
65018=>'AL',
65019=>'AL',
65020=>'AL',
65021=>'ON',
65024=>'NSM',
65025=>'NSM',
65026=>'NSM',
65027=>'NSM',
65028=>'NSM',
65029=>'NSM',
65030=>'NSM',
65031=>'NSM',
65032=>'NSM',
65033=>'NSM',
65034=>'NSM',
65035=>'NSM',
65036=>'NSM',
65037=>'NSM',
65038=>'NSM',
65039=>'NSM',
65040=>'ON',
65041=>'ON',
65042=>'ON',
65043=>'ON',
65044=>'ON',
65045=>'ON',
65046=>'ON',
65047=>'ON',
65048=>'ON',
65049=>'ON',
65056=>'NSM',
65057=>'NSM',
65058=>'NSM',
65059=>'NSM',
65072=>'ON',
65073=>'ON',
65074=>'ON',
65075=>'ON',
65076=>'ON',
65077=>'ON',
65078=>'ON',
65079=>'ON',
65080=>'ON',
65081=>'ON',
65082=>'ON',
65083=>'ON',
65084=>'ON',
65085=>'ON',
65086=>'ON',
65087=>'ON',
65088=>'ON',
65089=>'ON',
65090=>'ON',
65091=>'ON',
65092=>'ON',
65093=>'ON',
65094=>'ON',
65095=>'ON',
65096=>'ON',
65097=>'ON',
65098=>'ON',
65099=>'ON',
65100=>'ON',
65101=>'ON',
65102=>'ON',
65103=>'ON',
65104=>'CS',
65105=>'ON',
65106=>'CS',
65108=>'ON',
65109=>'CS',
65110=>'ON',
65111=>'ON',
65112=>'ON',
65113=>'ON',
65114=>'ON',
65115=>'ON',
65116=>'ON',
65117=>'ON',
65118=>'ON',
65119=>'ET',
65120=>'ON',
65121=>'ON',
65122=>'ES',
65123=>'ES',
65124=>'ON',
65125=>'ON',
65126=>'ON',
65128=>'ON',
65129=>'ET',
65130=>'ET',
65131=>'ON',
65136=>'AL',
65137=>'AL',
65138=>'AL',
65139=>'AL',
65140=>'AL',
65142=>'AL',
65143=>'AL',
65144=>'AL',
65145=>'AL',
65146=>'AL',
65147=>'AL',
65148=>'AL',
65149=>'AL',
65150=>'AL',
65151=>'AL',
65152=>'AL',
65153=>'AL',
65154=>'AL',
65155=>'AL',
65156=>'AL',
65157=>'AL',
65158=>'AL',
65159=>'AL',
65160=>'AL',
65161=>'AL',
65162=>'AL',
65163=>'AL',
65164=>'AL',
65165=>'AL',
65166=>'AL',
65167=>'AL',
65168=>'AL',
65169=>'AL',
65170=>'AL',
65171=>'AL',
65172=>'AL',
65173=>'AL',
65174=>'AL',
65175=>'AL',
65176=>'AL',
65177=>'AL',
65178=>'AL',
65179=>'AL',
65180=>'AL',
65181=>'AL',
65182=>'AL',
65183=>'AL',
65184=>'AL',
65185=>'AL',
65186=>'AL',
65187=>'AL',
65188=>'AL',
65189=>'AL',
65190=>'AL',
65191=>'AL',
65192=>'AL',
65193=>'AL',
65194=>'AL',
65195=>'AL',
65196=>'AL',
65197=>'AL',
65198=>'AL',
65199=>'AL',
65200=>'AL',
65201=>'AL',
65202=>'AL',
65203=>'AL',
65204=>'AL',
65205=>'AL',
65206=>'AL',
65207=>'AL',
65208=>'AL',
65209=>'AL',
65210=>'AL',
65211=>'AL',
65212=>'AL',
65213=>'AL',
65214=>'AL',
65215=>'AL',
65216=>'AL',
65217=>'AL',
65218=>'AL',
65219=>'AL',
65220=>'AL',
65221=>'AL',
65222=>'AL',
65223=>'AL',
65224=>'AL',
65225=>'AL',
65226=>'AL',
65227=>'AL',
65228=>'AL',
65229=>'AL',
65230=>'AL',
65231=>'AL',
65232=>'AL',
65233=>'AL',
65234=>'AL',
65235=>'AL',
65236=>'AL',
65237=>'AL',
65238=>'AL',
65239=>'AL',
65240=>'AL',
65241=>'AL',
65242=>'AL',
65243=>'AL',
65244=>'AL',
65245=>'AL',
65246=>'AL',
65247=>'AL',
65248=>'AL',
65249=>'AL',
65250=>'AL',
65251=>'AL',
65252=>'AL',
65253=>'AL',
65254=>'AL',
65255=>'AL',
65256=>'AL',
65257=>'AL',
65258=>'AL',
65259=>'AL',
65260=>'AL',
65261=>'AL',
65262=>'AL',
65263=>'AL',
65264=>'AL',
65265=>'AL',
65266=>'AL',
65267=>'AL',
65268=>'AL',
65269=>'AL',
65270=>'AL',
65271=>'AL',
65272=>'AL',
65273=>'AL',
65274=>'AL',
65275=>'AL',
65276=>'AL',
65279=>'BN',
65281=>'ON',
65282=>'ON',
65283=>'ET',
65284=>'ET',
65285=>'ET',
65286=>'ON',
65287=>'ON',
65288=>'ON',
65289=>'ON',
65290=>'ON',
65291=>'ES',
65292=>'CS',
65293=>'ES',
65294=>'CS',
65295=>'CS',
65296=>'EN',
65297=>'EN',
65298=>'EN',
65299=>'EN',
65300=>'EN',
65301=>'EN',
65302=>'EN',
65303=>'EN',
65304=>'EN',
65305=>'EN',
65306=>'CS',
65307=>'ON',
65308=>'ON',
65309=>'ON',
65310=>'ON',
65311=>'ON',
65312=>'ON',
65313=>'L',
65314=>'L',
65315=>'L',
65316=>'L',
65317=>'L',
65318=>'L',
65319=>'L',
65320=>'L',
65321=>'L',
65322=>'L',
65323=>'L',
65324=>'L',
65325=>'L',
65326=>'L',
65327=>'L',
65328=>'L',
65329=>'L',
65330=>'L',
65331=>'L',
65332=>'L',
65333=>'L',
65334=>'L',
65335=>'L',
65336=>'L',
65337=>'L',
65338=>'L',
65339=>'ON',
65340=>'ON',
65341=>'ON',
65342=>'ON',
65343=>'ON',
65344=>'ON',
65345=>'L',
65346=>'L',
65347=>'L',
65348=>'L',
65349=>'L',
65350=>'L',
65351=>'L',
65352=>'L',
65353=>'L',
65354=>'L',
65355=>'L',
65356=>'L',
65357=>'L',
65358=>'L',
65359=>'L',
65360=>'L',
65361=>'L',
65362=>'L',
65363=>'L',
65364=>'L',
65365=>'L',
65366=>'L',
65367=>'L',
65368=>'L',
65369=>'L',
65370=>'L',
65371=>'ON',
65372=>'ON',
65373=>'ON',
65374=>'ON',
65375=>'ON',
65376=>'ON',
65377=>'ON',
65378=>'ON',
65379=>'ON',
65380=>'ON',
65381=>'ON',
65382=>'L',
65383=>'L',
65384=>'L',
65385=>'L',
65386=>'L',
65387=>'L',
65388=>'L',
65389=>'L',
65390=>'L',
65391=>'L',
65392=>'L',
65393=>'L',
65394=>'L',
65395=>'L',
65396=>'L',
65397=>'L',
65398=>'L',
65399=>'L',
65400=>'L',
65401=>'L',
65402=>'L',
65403=>'L',
65404=>'L',
65405=>'L',
65406=>'L',
65407=>'L',
65408=>'L',
65409=>'L',
65410=>'L',
65411=>'L',
65412=>'L',
65413=>'L',
65414=>'L',
65415=>'L',
65416=>'L',
65417=>'L',
65418=>'L',
65419=>'L',
65420=>'L',
65421=>'L',
65422=>'L',
65423=>'L',
65424=>'L',
65425=>'L',
65426=>'L',
65427=>'L',
65428=>'L',
65429=>'L',
65430=>'L',
65431=>'L',
65432=>'L',
65433=>'L',
65434=>'L',
65435=>'L',
65436=>'L',
65437=>'L',
65438=>'L',
65439=>'L',
65440=>'L',
65441=>'L',
65442=>'L',
65443=>'L',
65444=>'L',
65445=>'L',
65446=>'L',
65447=>'L',
65448=>'L',
65449=>'L',
65450=>'L',
65451=>'L',
65452=>'L',
65453=>'L',
65454=>'L',
65455=>'L',
65456=>'L',
65457=>'L',
65458=>'L',
65459=>'L',
65460=>'L',
65461=>'L',
65462=>'L',
65463=>'L',
65464=>'L',
65465=>'L',
65466=>'L',
65467=>'L',
65468=>'L',
65469=>'L',
65470=>'L',
65474=>'L',
65475=>'L',
65476=>'L',
65477=>'L',
65478=>'L',
65479=>'L',
65482=>'L',
65483=>'L',
65484=>'L',
65485=>'L',
65486=>'L',
65487=>'L',
65490=>'L',
65491=>'L',
65492=>'L',
65493=>'L',
65494=>'L',
65495=>'L',
65498=>'L',
65499=>'L',
65500=>'L',
65504=>'ET',
65505=>'ET',
65506=>'ON',
65507=>'ON',
65508=>'ON',
65509=>'ET',
65510=>'ET',
65512=>'ON',
65513=>'ON',
65514=>'ON',
65515=>'ON',
65516=>'ON',
65517=>'ON',
65518=>'ON',
65529=>'ON',
65530=>'ON',
65531=>'ON',
65532=>'ON',
65533=>'ON',
65536=>'L',
65537=>'L',
65538=>'L',
65539=>'L',
65540=>'L',
65541=>'L',
65542=>'L',
65543=>'L',
65544=>'L',
65545=>'L',
65546=>'L',
65547=>'L',
65549=>'L',
65550=>'L',
65551=>'L',
65552=>'L',
65553=>'L',
65554=>'L',
65555=>'L',
65556=>'L',
65557=>'L',
65558=>'L',
65559=>'L',
65560=>'L',
65561=>'L',
65562=>'L',
65563=>'L',
65564=>'L',
65565=>'L',
65566=>'L',
65567=>'L',
65568=>'L',
65569=>'L',
65570=>'L',
65571=>'L',
65572=>'L',
65573=>'L',
65574=>'L',
65576=>'L',
65577=>'L',
65578=>'L',
65579=>'L',
65580=>'L',
65581=>'L',
65582=>'L',
65583=>'L',
65584=>'L',
65585=>'L',
65586=>'L',
65587=>'L',
65588=>'L',
65589=>'L',
65590=>'L',
65591=>'L',
65592=>'L',
65593=>'L',
65594=>'L',
65596=>'L',
65597=>'L',
65599=>'L',
65600=>'L',
65601=>'L',
65602=>'L',
65603=>'L',
65604=>'L',
65605=>'L',
65606=>'L',
65607=>'L',
65608=>'L',
65609=>'L',
65610=>'L',
65611=>'L',
65612=>'L',
65613=>'L',
65616=>'L',
65617=>'L',
65618=>'L',
65619=>'L',
65620=>'L',
65621=>'L',
65622=>'L',
65623=>'L',
65624=>'L',
65625=>'L',
65626=>'L',
65627=>'L',
65628=>'L',
65629=>'L',
65664=>'L',
65665=>'L',
65666=>'L',
65667=>'L',
65668=>'L',
65669=>'L',
65670=>'L',
65671=>'L',
65672=>'L',
65673=>'L',
65674=>'L',
65675=>'L',
65676=>'L',
65677=>'L',
65678=>'L',
65679=>'L',
65680=>'L',
65681=>'L',
65682=>'L',
65683=>'L',
65684=>'L',
65685=>'L',
65686=>'L',
65687=>'L',
65688=>'L',
65689=>'L',
65690=>'L',
65691=>'L',
65692=>'L',
65693=>'L',
65694=>'L',
65695=>'L',
65696=>'L',
65697=>'L',
65698=>'L',
65699=>'L',
65700=>'L',
65701=>'L',
65702=>'L',
65703=>'L',
65704=>'L',
65705=>'L',
65706=>'L',
65707=>'L',
65708=>'L',
65709=>'L',
65710=>'L',
65711=>'L',
65712=>'L',
65713=>'L',
65714=>'L',
65715=>'L',
65716=>'L',
65717=>'L',
65718=>'L',
65719=>'L',
65720=>'L',
65721=>'L',
65722=>'L',
65723=>'L',
65724=>'L',
65725=>'L',
65726=>'L',
65727=>'L',
65728=>'L',
65729=>'L',
65730=>'L',
65731=>'L',
65732=>'L',
65733=>'L',
65734=>'L',
65735=>'L',
65736=>'L',
65737=>'L',
65738=>'L',
65739=>'L',
65740=>'L',
65741=>'L',
65742=>'L',
65743=>'L',
65744=>'L',
65745=>'L',
65746=>'L',
65747=>'L',
65748=>'L',
65749=>'L',
65750=>'L',
65751=>'L',
65752=>'L',
65753=>'L',
65754=>'L',
65755=>'L',
65756=>'L',
65757=>'L',
65758=>'L',
65759=>'L',
65760=>'L',
65761=>'L',
65762=>'L',
65763=>'L',
65764=>'L',
65765=>'L',
65766=>'L',
65767=>'L',
65768=>'L',
65769=>'L',
65770=>'L',
65771=>'L',
65772=>'L',
65773=>'L',
65774=>'L',
65775=>'L',
65776=>'L',
65777=>'L',
65778=>'L',
65779=>'L',
65780=>'L',
65781=>'L',
65782=>'L',
65783=>'L',
65784=>'L',
65785=>'L',
65786=>'L',
65792=>'L',
65793=>'ON',
65794=>'L',
65799=>'L',
65800=>'L',
65801=>'L',
65802=>'L',
65803=>'L',
65804=>'L',
65805=>'L',
65806=>'L',
65807=>'L',
65808=>'L',
65809=>'L',
65810=>'L',
65811=>'L',
65812=>'L',
65813=>'L',
65814=>'L',
65815=>'L',
65816=>'L',
65817=>'L',
65818=>'L',
65819=>'L',
65820=>'L',
65821=>'L',
65822=>'L',
65823=>'L',
65824=>'L',
65825=>'L',
65826=>'L',
65827=>'L',
65828=>'L',
65829=>'L',
65830=>'L',
65831=>'L',
65832=>'L',
65833=>'L',
65834=>'L',
65835=>'L',
65836=>'L',
65837=>'L',
65838=>'L',
65839=>'L',
65840=>'L',
65841=>'L',
65842=>'L',
65843=>'L',
65847=>'L',
65848=>'L',
65849=>'L',
65850=>'L',
65851=>'L',
65852=>'L',
65853=>'L',
65854=>'L',
65855=>'L',
65856=>'ON',
65857=>'ON',
65858=>'ON',
65859=>'ON',
65860=>'ON',
65861=>'ON',
65862=>'ON',
65863=>'ON',
65864=>'ON',
65865=>'ON',
65866=>'ON',
65867=>'ON',
65868=>'ON',
65869=>'ON',
65870=>'ON',
65871=>'ON',
65872=>'ON',
65873=>'ON',
65874=>'ON',
65875=>'ON',
65876=>'ON',
65877=>'ON',
65878=>'ON',
65879=>'ON',
65880=>'ON',
65881=>'ON',
65882=>'ON',
65883=>'ON',
65884=>'ON',
65885=>'ON',
65886=>'ON',
65887=>'ON',
65888=>'ON',
65889=>'ON',
65890=>'ON',
65891=>'ON',
65892=>'ON',
65893=>'ON',
65894=>'ON',
65895=>'ON',
65896=>'ON',
65897=>'ON',
65898=>'ON',
65899=>'ON',
65900=>'ON',
65901=>'ON',
65902=>'ON',
65903=>'ON',
65904=>'ON',
65905=>'ON',
65906=>'ON',
65907=>'ON',
65908=>'ON',
65909=>'ON',
65910=>'ON',
65911=>'ON',
65912=>'ON',
65913=>'ON',
65914=>'ON',
65915=>'ON',
65916=>'ON',
65917=>'ON',
65918=>'ON',
65919=>'ON',
65920=>'ON',
65921=>'ON',
65922=>'ON',
65923=>'ON',
65924=>'ON',
65925=>'ON',
65926=>'ON',
65927=>'ON',
65928=>'ON',
65929=>'ON',
65930=>'ON',
66304=>'L',
66305=>'L',
66306=>'L',
66307=>'L',
66308=>'L',
66309=>'L',
66310=>'L',
66311=>'L',
66312=>'L',
66313=>'L',
66314=>'L',
66315=>'L',
66316=>'L',
66317=>'L',
66318=>'L',
66319=>'L',
66320=>'L',
66321=>'L',
66322=>'L',
66323=>'L',
66324=>'L',
66325=>'L',
66326=>'L',
66327=>'L',
66328=>'L',
66329=>'L',
66330=>'L',
66331=>'L',
66332=>'L',
66333=>'L',
66334=>'L',
66336=>'L',
66337=>'L',
66338=>'L',
66339=>'L',
66352=>'L',
66353=>'L',
66354=>'L',
66355=>'L',
66356=>'L',
66357=>'L',
66358=>'L',
66359=>'L',
66360=>'L',
66361=>'L',
66362=>'L',
66363=>'L',
66364=>'L',
66365=>'L',
66366=>'L',
66367=>'L',
66368=>'L',
66369=>'L',
66370=>'L',
66371=>'L',
66372=>'L',
66373=>'L',
66374=>'L',
66375=>'L',
66376=>'L',
66377=>'L',
66378=>'L',
66432=>'L',
66433=>'L',
66434=>'L',
66435=>'L',
66436=>'L',
66437=>'L',
66438=>'L',
66439=>'L',
66440=>'L',
66441=>'L',
66442=>'L',
66443=>'L',
66444=>'L',
66445=>'L',
66446=>'L',
66447=>'L',
66448=>'L',
66449=>'L',
66450=>'L',
66451=>'L',
66452=>'L',
66453=>'L',
66454=>'L',
66455=>'L',
66456=>'L',
66457=>'L',
66458=>'L',
66459=>'L',
66460=>'L',
66461=>'L',
66463=>'L',
66464=>'L',
66465=>'L',
66466=>'L',
66467=>'L',
66468=>'L',
66469=>'L',
66470=>'L',
66471=>'L',
66472=>'L',
66473=>'L',
66474=>'L',
66475=>'L',
66476=>'L',
66477=>'L',
66478=>'L',
66479=>'L',
66480=>'L',
66481=>'L',
66482=>'L',
66483=>'L',
66484=>'L',
66485=>'L',
66486=>'L',
66487=>'L',
66488=>'L',
66489=>'L',
66490=>'L',
66491=>'L',
66492=>'L',
66493=>'L',
66494=>'L',
66495=>'L',
66496=>'L',
66497=>'L',
66498=>'L',
66499=>'L',
66504=>'L',
66505=>'L',
66506=>'L',
66507=>'L',
66508=>'L',
66509=>'L',
66510=>'L',
66511=>'L',
66512=>'L',
66513=>'L',
66514=>'L',
66515=>'L',
66516=>'L',
66517=>'L',
66560=>'L',
66561=>'L',
66562=>'L',
66563=>'L',
66564=>'L',
66565=>'L',
66566=>'L',
66567=>'L',
66568=>'L',
66569=>'L',
66570=>'L',
66571=>'L',
66572=>'L',
66573=>'L',
66574=>'L',
66575=>'L',
66576=>'L',
66577=>'L',
66578=>'L',
66579=>'L',
66580=>'L',
66581=>'L',
66582=>'L',
66583=>'L',
66584=>'L',
66585=>'L',
66586=>'L',
66587=>'L',
66588=>'L',
66589=>'L',
66590=>'L',
66591=>'L',
66592=>'L',
66593=>'L',
66594=>'L',
66595=>'L',
66596=>'L',
66597=>'L',
66598=>'L',
66599=>'L',
66600=>'L',
66601=>'L',
66602=>'L',
66603=>'L',
66604=>'L',
66605=>'L',
66606=>'L',
66607=>'L',
66608=>'L',
66609=>'L',
66610=>'L',
66611=>'L',
66612=>'L',
66613=>'L',
66614=>'L',
66615=>'L',
66616=>'L',
66617=>'L',
66618=>'L',
66619=>'L',
66620=>'L',
66621=>'L',
66622=>'L',
66623=>'L',
66624=>'L',
66625=>'L',
66626=>'L',
66627=>'L',
66628=>'L',
66629=>'L',
66630=>'L',
66631=>'L',
66632=>'L',
66633=>'L',
66634=>'L',
66635=>'L',
66636=>'L',
66637=>'L',
66638=>'L',
66639=>'L',
66640=>'L',
66641=>'L',
66642=>'L',
66643=>'L',
66644=>'L',
66645=>'L',
66646=>'L',
66647=>'L',
66648=>'L',
66649=>'L',
66650=>'L',
66651=>'L',
66652=>'L',
66653=>'L',
66654=>'L',
66655=>'L',
66656=>'L',
66657=>'L',
66658=>'L',
66659=>'L',
66660=>'L',
66661=>'L',
66662=>'L',
66663=>'L',
66664=>'L',
66665=>'L',
66666=>'L',
66667=>'L',
66668=>'L',
66669=>'L',
66670=>'L',
66671=>'L',
66672=>'L',
66673=>'L',
66674=>'L',
66675=>'L',
66676=>'L',
66677=>'L',
66678=>'L',
66679=>'L',
66680=>'L',
66681=>'L',
66682=>'L',
66683=>'L',
66684=>'L',
66685=>'L',
66686=>'L',
66687=>'L',
66688=>'L',
66689=>'L',
66690=>'L',
66691=>'L',
66692=>'L',
66693=>'L',
66694=>'L',
66695=>'L',
66696=>'L',
66697=>'L',
66698=>'L',
66699=>'L',
66700=>'L',
66701=>'L',
66702=>'L',
66703=>'L',
66704=>'L',
66705=>'L',
66706=>'L',
66707=>'L',
66708=>'L',
66709=>'L',
66710=>'L',
66711=>'L',
66712=>'L',
66713=>'L',
66714=>'L',
66715=>'L',
66716=>'L',
66717=>'L',
66720=>'L',
66721=>'L',
66722=>'L',
66723=>'L',
66724=>'L',
66725=>'L',
66726=>'L',
66727=>'L',
66728=>'L',
66729=>'L',
67584=>'R',
67585=>'R',
67586=>'R',
67587=>'R',
67588=>'R',
67589=>'R',
67592=>'R',
67594=>'R',
67595=>'R',
67596=>'R',
67597=>'R',
67598=>'R',
67599=>'R',
67600=>'R',
67601=>'R',
67602=>'R',
67603=>'R',
67604=>'R',
67605=>'R',
67606=>'R',
67607=>'R',
67608=>'R',
67609=>'R',
67610=>'R',
67611=>'R',
67612=>'R',
67613=>'R',
67614=>'R',
67615=>'R',
67616=>'R',
67617=>'R',
67618=>'R',
67619=>'R',
67620=>'R',
67621=>'R',
67622=>'R',
67623=>'R',
67624=>'R',
67625=>'R',
67626=>'R',
67627=>'R',
67628=>'R',
67629=>'R',
67630=>'R',
67631=>'R',
67632=>'R',
67633=>'R',
67634=>'R',
67635=>'R',
67636=>'R',
67637=>'R',
67639=>'R',
67640=>'R',
67644=>'R',
67647=>'R',
67840=>'R',
67841=>'R',
67842=>'R',
67843=>'R',
67844=>'R',
67845=>'R',
67846=>'R',
67847=>'R',
67848=>'R',
67849=>'R',
67850=>'R',
67851=>'R',
67852=>'R',
67853=>'R',
67854=>'R',
67855=>'R',
67856=>'R',
67857=>'R',
67858=>'R',
67859=>'R',
67860=>'R',
67861=>'R',
67862=>'R',
67863=>'R',
67864=>'R',
67865=>'R',
67871=>'ON',
68096=>'R',
68097=>'NSM',
68098=>'NSM',
68099=>'NSM',
68101=>'NSM',
68102=>'NSM',
68108=>'NSM',
68109=>'NSM',
68110=>'NSM',
68111=>'NSM',
68112=>'R',
68113=>'R',
68114=>'R',
68115=>'R',
68117=>'R',
68118=>'R',
68119=>'R',
68121=>'R',
68122=>'R',
68123=>'R',
68124=>'R',
68125=>'R',
68126=>'R',
68127=>'R',
68128=>'R',
68129=>'R',
68130=>'R',
68131=>'R',
68132=>'R',
68133=>'R',
68134=>'R',
68135=>'R',
68136=>'R',
68137=>'R',
68138=>'R',
68139=>'R',
68140=>'R',
68141=>'R',
68142=>'R',
68143=>'R',
68144=>'R',
68145=>'R',
68146=>'R',
68147=>'R',
68152=>'NSM',
68153=>'NSM',
68154=>'NSM',
68159=>'NSM',
68160=>'R',
68161=>'R',
68162=>'R',
68163=>'R',
68164=>'R',
68165=>'R',
68166=>'R',
68167=>'R',
68176=>'R',
68177=>'R',
68178=>'R',
68179=>'R',
68180=>'R',
68181=>'R',
68182=>'R',
68183=>'R',
68184=>'R',
73728=>'L',
73729=>'L',
73730=>'L',
73731=>'L',
73732=>'L',
73733=>'L',
73734=>'L',
73735=>'L',
73736=>'L',
73737=>'L',
73738=>'L',
73739=>'L',
73740=>'L',
73741=>'L',
73742=>'L',
73743=>'L',
73744=>'L',
73745=>'L',
73746=>'L',
73747=>'L',
73748=>'L',
73749=>'L',
73750=>'L',
73751=>'L',
73752=>'L',
73753=>'L',
73754=>'L',
73755=>'L',
73756=>'L',
73757=>'L',
73758=>'L',
73759=>'L',
73760=>'L',
73761=>'L',
73762=>'L',
73763=>'L',
73764=>'L',
73765=>'L',
73766=>'L',
73767=>'L',
73768=>'L',
73769=>'L',
73770=>'L',
73771=>'L',
73772=>'L',
73773=>'L',
73774=>'L',
73775=>'L',
73776=>'L',
73777=>'L',
73778=>'L',
73779=>'L',
73780=>'L',
73781=>'L',
73782=>'L',
73783=>'L',
73784=>'L',
73785=>'L',
73786=>'L',
73787=>'L',
73788=>'L',
73789=>'L',
73790=>'L',
73791=>'L',
73792=>'L',
73793=>'L',
73794=>'L',
73795=>'L',
73796=>'L',
73797=>'L',
73798=>'L',
73799=>'L',
73800=>'L',
73801=>'L',
73802=>'L',
73803=>'L',
73804=>'L',
73805=>'L',
73806=>'L',
73807=>'L',
73808=>'L',
73809=>'L',
73810=>'L',
73811=>'L',
73812=>'L',
73813=>'L',
73814=>'L',
73815=>'L',
73816=>'L',
73817=>'L',
73818=>'L',
73819=>'L',
73820=>'L',
73821=>'L',
73822=>'L',
73823=>'L',
73824=>'L',
73825=>'L',
73826=>'L',
73827=>'L',
73828=>'L',
73829=>'L',
73830=>'L',
73831=>'L',
73832=>'L',
73833=>'L',
73834=>'L',
73835=>'L',
73836=>'L',
73837=>'L',
73838=>'L',
73839=>'L',
73840=>'L',
73841=>'L',
73842=>'L',
73843=>'L',
73844=>'L',
73845=>'L',
73846=>'L',
73847=>'L',
73848=>'L',
73849=>'L',
73850=>'L',
73851=>'L',
73852=>'L',
73853=>'L',
73854=>'L',
73855=>'L',
73856=>'L',
73857=>'L',
73858=>'L',
73859=>'L',
73860=>'L',
73861=>'L',
73862=>'L',
73863=>'L',
73864=>'L',
73865=>'L',
73866=>'L',
73867=>'L',
73868=>'L',
73869=>'L',
73870=>'L',
73871=>'L',
73872=>'L',
73873=>'L',
73874=>'L',
73875=>'L',
73876=>'L',
73877=>'L',
73878=>'L',
73879=>'L',
73880=>'L',
73881=>'L',
73882=>'L',
73883=>'L',
73884=>'L',
73885=>'L',
73886=>'L',
73887=>'L',
73888=>'L',
73889=>'L',
73890=>'L',
73891=>'L',
73892=>'L',
73893=>'L',
73894=>'L',
73895=>'L',
73896=>'L',
73897=>'L',
73898=>'L',
73899=>'L',
73900=>'L',
73901=>'L',
73902=>'L',
73903=>'L',
73904=>'L',
73905=>'L',
73906=>'L',
73907=>'L',
73908=>'L',
73909=>'L',
73910=>'L',
73911=>'L',
73912=>'L',
73913=>'L',
73914=>'L',
73915=>'L',
73916=>'L',
73917=>'L',
73918=>'L',
73919=>'L',
73920=>'L',
73921=>'L',
73922=>'L',
73923=>'L',
73924=>'L',
73925=>'L',
73926=>'L',
73927=>'L',
73928=>'L',
73929=>'L',
73930=>'L',
73931=>'L',
73932=>'L',
73933=>'L',
73934=>'L',
73935=>'L',
73936=>'L',
73937=>'L',
73938=>'L',
73939=>'L',
73940=>'L',
73941=>'L',
73942=>'L',
73943=>'L',
73944=>'L',
73945=>'L',
73946=>'L',
73947=>'L',
73948=>'L',
73949=>'L',
73950=>'L',
73951=>'L',
73952=>'L',
73953=>'L',
73954=>'L',
73955=>'L',
73956=>'L',
73957=>'L',
73958=>'L',
73959=>'L',
73960=>'L',
73961=>'L',
73962=>'L',
73963=>'L',
73964=>'L',
73965=>'L',
73966=>'L',
73967=>'L',
73968=>'L',
73969=>'L',
73970=>'L',
73971=>'L',
73972=>'L',
73973=>'L',
73974=>'L',
73975=>'L',
73976=>'L',
73977=>'L',
73978=>'L',
73979=>'L',
73980=>'L',
73981=>'L',
73982=>'L',
73983=>'L',
73984=>'L',
73985=>'L',
73986=>'L',
73987=>'L',
73988=>'L',
73989=>'L',
73990=>'L',
73991=>'L',
73992=>'L',
73993=>'L',
73994=>'L',
73995=>'L',
73996=>'L',
73997=>'L',
73998=>'L',
73999=>'L',
74000=>'L',
74001=>'L',
74002=>'L',
74003=>'L',
74004=>'L',
74005=>'L',
74006=>'L',
74007=>'L',
74008=>'L',
74009=>'L',
74010=>'L',
74011=>'L',
74012=>'L',
74013=>'L',
74014=>'L',
74015=>'L',
74016=>'L',
74017=>'L',
74018=>'L',
74019=>'L',
74020=>'L',
74021=>'L',
74022=>'L',
74023=>'L',
74024=>'L',
74025=>'L',
74026=>'L',
74027=>'L',
74028=>'L',
74029=>'L',
74030=>'L',
74031=>'L',
74032=>'L',
74033=>'L',
74034=>'L',
74035=>'L',
74036=>'L',
74037=>'L',
74038=>'L',
74039=>'L',
74040=>'L',
74041=>'L',
74042=>'L',
74043=>'L',
74044=>'L',
74045=>'L',
74046=>'L',
74047=>'L',
74048=>'L',
74049=>'L',
74050=>'L',
74051=>'L',
74052=>'L',
74053=>'L',
74054=>'L',
74055=>'L',
74056=>'L',
74057=>'L',
74058=>'L',
74059=>'L',
74060=>'L',
74061=>'L',
74062=>'L',
74063=>'L',
74064=>'L',
74065=>'L',
74066=>'L',
74067=>'L',
74068=>'L',
74069=>'L',
74070=>'L',
74071=>'L',
74072=>'L',
74073=>'L',
74074=>'L',
74075=>'L',
74076=>'L',
74077=>'L',
74078=>'L',
74079=>'L',
74080=>'L',
74081=>'L',
74082=>'L',
74083=>'L',
74084=>'L',
74085=>'L',
74086=>'L',
74087=>'L',
74088=>'L',
74089=>'L',
74090=>'L',
74091=>'L',
74092=>'L',
74093=>'L',
74094=>'L',
74095=>'L',
74096=>'L',
74097=>'L',
74098=>'L',
74099=>'L',
74100=>'L',
74101=>'L',
74102=>'L',
74103=>'L',
74104=>'L',
74105=>'L',
74106=>'L',
74107=>'L',
74108=>'L',
74109=>'L',
74110=>'L',
74111=>'L',
74112=>'L',
74113=>'L',
74114=>'L',
74115=>'L',
74116=>'L',
74117=>'L',
74118=>'L',
74119=>'L',
74120=>'L',
74121=>'L',
74122=>'L',
74123=>'L',
74124=>'L',
74125=>'L',
74126=>'L',
74127=>'L',
74128=>'L',
74129=>'L',
74130=>'L',
74131=>'L',
74132=>'L',
74133=>'L',
74134=>'L',
74135=>'L',
74136=>'L',
74137=>'L',
74138=>'L',
74139=>'L',
74140=>'L',
74141=>'L',
74142=>'L',
74143=>'L',
74144=>'L',
74145=>'L',
74146=>'L',
74147=>'L',
74148=>'L',
74149=>'L',
74150=>'L',
74151=>'L',
74152=>'L',
74153=>'L',
74154=>'L',
74155=>'L',
74156=>'L',
74157=>'L',
74158=>'L',
74159=>'L',
74160=>'L',
74161=>'L',
74162=>'L',
74163=>'L',
74164=>'L',
74165=>'L',
74166=>'L',
74167=>'L',
74168=>'L',
74169=>'L',
74170=>'L',
74171=>'L',
74172=>'L',
74173=>'L',
74174=>'L',
74175=>'L',
74176=>'L',
74177=>'L',
74178=>'L',
74179=>'L',
74180=>'L',
74181=>'L',
74182=>'L',
74183=>'L',
74184=>'L',
74185=>'L',
74186=>'L',
74187=>'L',
74188=>'L',
74189=>'L',
74190=>'L',
74191=>'L',
74192=>'L',
74193=>'L',
74194=>'L',
74195=>'L',
74196=>'L',
74197=>'L',
74198=>'L',
74199=>'L',
74200=>'L',
74201=>'L',
74202=>'L',
74203=>'L',
74204=>'L',
74205=>'L',
74206=>'L',
74207=>'L',
74208=>'L',
74209=>'L',
74210=>'L',
74211=>'L',
74212=>'L',
74213=>'L',
74214=>'L',
74215=>'L',
74216=>'L',
74217=>'L',
74218=>'L',
74219=>'L',
74220=>'L',
74221=>'L',
74222=>'L',
74223=>'L',
74224=>'L',
74225=>'L',
74226=>'L',
74227=>'L',
74228=>'L',
74229=>'L',
74230=>'L',
74231=>'L',
74232=>'L',
74233=>'L',
74234=>'L',
74235=>'L',
74236=>'L',
74237=>'L',
74238=>'L',
74239=>'L',
74240=>'L',
74241=>'L',
74242=>'L',
74243=>'L',
74244=>'L',
74245=>'L',
74246=>'L',
74247=>'L',
74248=>'L',
74249=>'L',
74250=>'L',
74251=>'L',
74252=>'L',
74253=>'L',
74254=>'L',
74255=>'L',
74256=>'L',
74257=>'L',
74258=>'L',
74259=>'L',
74260=>'L',
74261=>'L',
74262=>'L',
74263=>'L',
74264=>'L',
74265=>'L',
74266=>'L',
74267=>'L',
74268=>'L',
74269=>'L',
74270=>'L',
74271=>'L',
74272=>'L',
74273=>'L',
74274=>'L',
74275=>'L',
74276=>'L',
74277=>'L',
74278=>'L',
74279=>'L',
74280=>'L',
74281=>'L',
74282=>'L',
74283=>'L',
74284=>'L',
74285=>'L',
74286=>'L',
74287=>'L',
74288=>'L',
74289=>'L',
74290=>'L',
74291=>'L',
74292=>'L',
74293=>'L',
74294=>'L',
74295=>'L',
74296=>'L',
74297=>'L',
74298=>'L',
74299=>'L',
74300=>'L',
74301=>'L',
74302=>'L',
74303=>'L',
74304=>'L',
74305=>'L',
74306=>'L',
74307=>'L',
74308=>'L',
74309=>'L',
74310=>'L',
74311=>'L',
74312=>'L',
74313=>'L',
74314=>'L',
74315=>'L',
74316=>'L',
74317=>'L',
74318=>'L',
74319=>'L',
74320=>'L',
74321=>'L',
74322=>'L',
74323=>'L',
74324=>'L',
74325=>'L',
74326=>'L',
74327=>'L',
74328=>'L',
74329=>'L',
74330=>'L',
74331=>'L',
74332=>'L',
74333=>'L',
74334=>'L',
74335=>'L',
74336=>'L',
74337=>'L',
74338=>'L',
74339=>'L',
74340=>'L',
74341=>'L',
74342=>'L',
74343=>'L',
74344=>'L',
74345=>'L',
74346=>'L',
74347=>'L',
74348=>'L',
74349=>'L',
74350=>'L',
74351=>'L',
74352=>'L',
74353=>'L',
74354=>'L',
74355=>'L',
74356=>'L',
74357=>'L',
74358=>'L',
74359=>'L',
74360=>'L',
74361=>'L',
74362=>'L',
74363=>'L',
74364=>'L',
74365=>'L',
74366=>'L',
74367=>'L',
74368=>'L',
74369=>'L',
74370=>'L',
74371=>'L',
74372=>'L',
74373=>'L',
74374=>'L',
74375=>'L',
74376=>'L',
74377=>'L',
74378=>'L',
74379=>'L',
74380=>'L',
74381=>'L',
74382=>'L',
74383=>'L',
74384=>'L',
74385=>'L',
74386=>'L',
74387=>'L',
74388=>'L',
74389=>'L',
74390=>'L',
74391=>'L',
74392=>'L',
74393=>'L',
74394=>'L',
74395=>'L',
74396=>'L',
74397=>'L',
74398=>'L',
74399=>'L',
74400=>'L',
74401=>'L',
74402=>'L',
74403=>'L',
74404=>'L',
74405=>'L',
74406=>'L',
74407=>'L',
74408=>'L',
74409=>'L',
74410=>'L',
74411=>'L',
74412=>'L',
74413=>'L',
74414=>'L',
74415=>'L',
74416=>'L',
74417=>'L',
74418=>'L',
74419=>'L',
74420=>'L',
74421=>'L',
74422=>'L',
74423=>'L',
74424=>'L',
74425=>'L',
74426=>'L',
74427=>'L',
74428=>'L',
74429=>'L',
74430=>'L',
74431=>'L',
74432=>'L',
74433=>'L',
74434=>'L',
74435=>'L',
74436=>'L',
74437=>'L',
74438=>'L',
74439=>'L',
74440=>'L',
74441=>'L',
74442=>'L',
74443=>'L',
74444=>'L',
74445=>'L',
74446=>'L',
74447=>'L',
74448=>'L',
74449=>'L',
74450=>'L',
74451=>'L',
74452=>'L',
74453=>'L',
74454=>'L',
74455=>'L',
74456=>'L',
74457=>'L',
74458=>'L',
74459=>'L',
74460=>'L',
74461=>'L',
74462=>'L',
74463=>'L',
74464=>'L',
74465=>'L',
74466=>'L',
74467=>'L',
74468=>'L',
74469=>'L',
74470=>'L',
74471=>'L',
74472=>'L',
74473=>'L',
74474=>'L',
74475=>'L',
74476=>'L',
74477=>'L',
74478=>'L',
74479=>'L',
74480=>'L',
74481=>'L',
74482=>'L',
74483=>'L',
74484=>'L',
74485=>'L',
74486=>'L',
74487=>'L',
74488=>'L',
74489=>'L',
74490=>'L',
74491=>'L',
74492=>'L',
74493=>'L',
74494=>'L',
74495=>'L',
74496=>'L',
74497=>'L',
74498=>'L',
74499=>'L',
74500=>'L',
74501=>'L',
74502=>'L',
74503=>'L',
74504=>'L',
74505=>'L',
74506=>'L',
74507=>'L',
74508=>'L',
74509=>'L',
74510=>'L',
74511=>'L',
74512=>'L',
74513=>'L',
74514=>'L',
74515=>'L',
74516=>'L',
74517=>'L',
74518=>'L',
74519=>'L',
74520=>'L',
74521=>'L',
74522=>'L',
74523=>'L',
74524=>'L',
74525=>'L',
74526=>'L',
74527=>'L',
74528=>'L',
74529=>'L',
74530=>'L',
74531=>'L',
74532=>'L',
74533=>'L',
74534=>'L',
74535=>'L',
74536=>'L',
74537=>'L',
74538=>'L',
74539=>'L',
74540=>'L',
74541=>'L',
74542=>'L',
74543=>'L',
74544=>'L',
74545=>'L',
74546=>'L',
74547=>'L',
74548=>'L',
74549=>'L',
74550=>'L',
74551=>'L',
74552=>'L',
74553=>'L',
74554=>'L',
74555=>'L',
74556=>'L',
74557=>'L',
74558=>'L',
74559=>'L',
74560=>'L',
74561=>'L',
74562=>'L',
74563=>'L',
74564=>'L',
74565=>'L',
74566=>'L',
74567=>'L',
74568=>'L',
74569=>'L',
74570=>'L',
74571=>'L',
74572=>'L',
74573=>'L',
74574=>'L',
74575=>'L',
74576=>'L',
74577=>'L',
74578=>'L',
74579=>'L',
74580=>'L',
74581=>'L',
74582=>'L',
74583=>'L',
74584=>'L',
74585=>'L',
74586=>'L',
74587=>'L',
74588=>'L',
74589=>'L',
74590=>'L',
74591=>'L',
74592=>'L',
74593=>'L',
74594=>'L',
74595=>'L',
74596=>'L',
74597=>'L',
74598=>'L',
74599=>'L',
74600=>'L',
74601=>'L',
74602=>'L',
74603=>'L',
74604=>'L',
74605=>'L',
74606=>'L',
74752=>'L',
74753=>'L',
74754=>'L',
74755=>'L',
74756=>'L',
74757=>'L',
74758=>'L',
74759=>'L',
74760=>'L',
74761=>'L',
74762=>'L',
74763=>'L',
74764=>'L',
74765=>'L',
74766=>'L',
74767=>'L',
74768=>'L',
74769=>'L',
74770=>'L',
74771=>'L',
74772=>'L',
74773=>'L',
74774=>'L',
74775=>'L',
74776=>'L',
74777=>'L',
74778=>'L',
74779=>'L',
74780=>'L',
74781=>'L',
74782=>'L',
74783=>'L',
74784=>'L',
74785=>'L',
74786=>'L',
74787=>'L',
74788=>'L',
74789=>'L',
74790=>'L',
74791=>'L',
74792=>'L',
74793=>'L',
74794=>'L',
74795=>'L',
74796=>'L',
74797=>'L',
74798=>'L',
74799=>'L',
74800=>'L',
74801=>'L',
74802=>'L',
74803=>'L',
74804=>'L',
74805=>'L',
74806=>'L',
74807=>'L',
74808=>'L',
74809=>'L',
74810=>'L',
74811=>'L',
74812=>'L',
74813=>'L',
74814=>'L',
74815=>'L',
74816=>'L',
74817=>'L',
74818=>'L',
74819=>'L',
74820=>'L',
74821=>'L',
74822=>'L',
74823=>'L',
74824=>'L',
74825=>'L',
74826=>'L',
74827=>'L',
74828=>'L',
74829=>'L',
74830=>'L',
74831=>'L',
74832=>'L',
74833=>'L',
74834=>'L',
74835=>'L',
74836=>'L',
74837=>'L',
74838=>'L',
74839=>'L',
74840=>'L',
74841=>'L',
74842=>'L',
74843=>'L',
74844=>'L',
74845=>'L',
74846=>'L',
74847=>'L',
74848=>'L',
74849=>'L',
74850=>'L',
74864=>'L',
74865=>'L',
74866=>'L',
74867=>'L',
118784=>'L',
118785=>'L',
118786=>'L',
118787=>'L',
118788=>'L',
118789=>'L',
118790=>'L',
118791=>'L',
118792=>'L',
118793=>'L',
118794=>'L',
118795=>'L',
118796=>'L',
118797=>'L',
118798=>'L',
118799=>'L',
118800=>'L',
118801=>'L',
118802=>'L',
118803=>'L',
118804=>'L',
118805=>'L',
118806=>'L',
118807=>'L',
118808=>'L',
118809=>'L',
118810=>'L',
118811=>'L',
118812=>'L',
118813=>'L',
118814=>'L',
118815=>'L',
118816=>'L',
118817=>'L',
118818=>'L',
118819=>'L',
118820=>'L',
118821=>'L',
118822=>'L',
118823=>'L',
118824=>'L',
118825=>'L',
118826=>'L',
118827=>'L',
118828=>'L',
118829=>'L',
118830=>'L',
118831=>'L',
118832=>'L',
118833=>'L',
118834=>'L',
118835=>'L',
118836=>'L',
118837=>'L',
118838=>'L',
118839=>'L',
118840=>'L',
118841=>'L',
118842=>'L',
118843=>'L',
118844=>'L',
118845=>'L',
118846=>'L',
118847=>'L',
118848=>'L',
118849=>'L',
118850=>'L',
118851=>'L',
118852=>'L',
118853=>'L',
118854=>'L',
118855=>'L',
118856=>'L',
118857=>'L',
118858=>'L',
118859=>'L',
118860=>'L',
118861=>'L',
118862=>'L',
118863=>'L',
118864=>'L',
118865=>'L',
118866=>'L',
118867=>'L',
118868=>'L',
118869=>'L',
118870=>'L',
118871=>'L',
118872=>'L',
118873=>'L',
118874=>'L',
118875=>'L',
118876=>'L',
118877=>'L',
118878=>'L',
118879=>'L',
118880=>'L',
118881=>'L',
118882=>'L',
118883=>'L',
118884=>'L',
118885=>'L',
118886=>'L',
118887=>'L',
118888=>'L',
118889=>'L',
118890=>'L',
118891=>'L',
118892=>'L',
118893=>'L',
118894=>'L',
118895=>'L',
118896=>'L',
118897=>'L',
118898=>'L',
118899=>'L',
118900=>'L',
118901=>'L',
118902=>'L',
118903=>'L',
118904=>'L',
118905=>'L',
118906=>'L',
118907=>'L',
118908=>'L',
118909=>'L',
118910=>'L',
118911=>'L',
118912=>'L',
118913=>'L',
118914=>'L',
118915=>'L',
118916=>'L',
118917=>'L',
118918=>'L',
118919=>'L',
118920=>'L',
118921=>'L',
118922=>'L',
118923=>'L',
118924=>'L',
118925=>'L',
118926=>'L',
118927=>'L',
118928=>'L',
118929=>'L',
118930=>'L',
118931=>'L',
118932=>'L',
118933=>'L',
118934=>'L',
118935=>'L',
118936=>'L',
118937=>'L',
118938=>'L',
118939=>'L',
118940=>'L',
118941=>'L',
118942=>'L',
118943=>'L',
118944=>'L',
118945=>'L',
118946=>'L',
118947=>'L',
118948=>'L',
118949=>'L',
118950=>'L',
118951=>'L',
118952=>'L',
118953=>'L',
118954=>'L',
118955=>'L',
118956=>'L',
118957=>'L',
118958=>'L',
118959=>'L',
118960=>'L',
118961=>'L',
118962=>'L',
118963=>'L',
118964=>'L',
118965=>'L',
118966=>'L',
118967=>'L',
118968=>'L',
118969=>'L',
118970=>'L',
118971=>'L',
118972=>'L',
118973=>'L',
118974=>'L',
118975=>'L',
118976=>'L',
118977=>'L',
118978=>'L',
118979=>'L',
118980=>'L',
118981=>'L',
118982=>'L',
118983=>'L',
118984=>'L',
118985=>'L',
118986=>'L',
118987=>'L',
118988=>'L',
118989=>'L',
118990=>'L',
118991=>'L',
118992=>'L',
118993=>'L',
118994=>'L',
118995=>'L',
118996=>'L',
118997=>'L',
118998=>'L',
118999=>'L',
119000=>'L',
119001=>'L',
119002=>'L',
119003=>'L',
119004=>'L',
119005=>'L',
119006=>'L',
119007=>'L',
119008=>'L',
119009=>'L',
119010=>'L',
119011=>'L',
119012=>'L',
119013=>'L',
119014=>'L',
119015=>'L',
119016=>'L',
119017=>'L',
119018=>'L',
119019=>'L',
119020=>'L',
119021=>'L',
119022=>'L',
119023=>'L',
119024=>'L',
119025=>'L',
119026=>'L',
119027=>'L',
119028=>'L',
119029=>'L',
119040=>'L',
119041=>'L',
119042=>'L',
119043=>'L',
119044=>'L',
119045=>'L',
119046=>'L',
119047=>'L',
119048=>'L',
119049=>'L',
119050=>'L',
119051=>'L',
119052=>'L',
119053=>'L',
119054=>'L',
119055=>'L',
119056=>'L',
119057=>'L',
119058=>'L',
119059=>'L',
119060=>'L',
119061=>'L',
119062=>'L',
119063=>'L',
119064=>'L',
119065=>'L',
119066=>'L',
119067=>'L',
119068=>'L',
119069=>'L',
119070=>'L',
119071=>'L',
119072=>'L',
119073=>'L',
119074=>'L',
119075=>'L',
119076=>'L',
119077=>'L',
119078=>'L',
119082=>'L',
119083=>'L',
119084=>'L',
119085=>'L',
119086=>'L',
119087=>'L',
119088=>'L',
119089=>'L',
119090=>'L',
119091=>'L',
119092=>'L',
119093=>'L',
119094=>'L',
119095=>'L',
119096=>'L',
119097=>'L',
119098=>'L',
119099=>'L',
119100=>'L',
119101=>'L',
119102=>'L',
119103=>'L',
119104=>'L',
119105=>'L',
119106=>'L',
119107=>'L',
119108=>'L',
119109=>'L',
119110=>'L',
119111=>'L',
119112=>'L',
119113=>'L',
119114=>'L',
119115=>'L',
119116=>'L',
119117=>'L',
119118=>'L',
119119=>'L',
119120=>'L',
119121=>'L',
119122=>'L',
119123=>'L',
119124=>'L',
119125=>'L',
119126=>'L',
119127=>'L',
119128=>'L',
119129=>'L',
119130=>'L',
119131=>'L',
119132=>'L',
119133=>'L',
119134=>'L',
119135=>'L',
119136=>'L',
119137=>'L',
119138=>'L',
119139=>'L',
119140=>'L',
119141=>'L',
119142=>'L',
119143=>'NSM',
119144=>'NSM',
119145=>'NSM',
119146=>'L',
119147=>'L',
119148=>'L',
119149=>'L',
119150=>'L',
119151=>'L',
119152=>'L',
119153=>'L',
119154=>'L',
119155=>'BN',
119156=>'BN',
119157=>'BN',
119158=>'BN',
119159=>'BN',
119160=>'BN',
119161=>'BN',
119162=>'BN',
119163=>'NSM',
119164=>'NSM',
119165=>'NSM',
119166=>'NSM',
119167=>'NSM',
119168=>'NSM',
119169=>'NSM',
119170=>'NSM',
119171=>'L',
119172=>'L',
119173=>'NSM',
119174=>'NSM',
119175=>'NSM',
119176=>'NSM',
119177=>'NSM',
119178=>'NSM',
119179=>'NSM',
119180=>'L',
119181=>'L',
119182=>'L',
119183=>'L',
119184=>'L',
119185=>'L',
119186=>'L',
119187=>'L',
119188=>'L',
119189=>'L',
119190=>'L',
119191=>'L',
119192=>'L',
119193=>'L',
119194=>'L',
119195=>'L',
119196=>'L',
119197=>'L',
119198=>'L',
119199=>'L',
119200=>'L',
119201=>'L',
119202=>'L',
119203=>'L',
119204=>'L',
119205=>'L',
119206=>'L',
119207=>'L',
119208=>'L',
119209=>'L',
119210=>'NSM',
119211=>'NSM',
119212=>'NSM',
119213=>'NSM',
119214=>'L',
119215=>'L',
119216=>'L',
119217=>'L',
119218=>'L',
119219=>'L',
119220=>'L',
119221=>'L',
119222=>'L',
119223=>'L',
119224=>'L',
119225=>'L',
119226=>'L',
119227=>'L',
119228=>'L',
119229=>'L',
119230=>'L',
119231=>'L',
119232=>'L',
119233=>'L',
119234=>'L',
119235=>'L',
119236=>'L',
119237=>'L',
119238=>'L',
119239=>'L',
119240=>'L',
119241=>'L',
119242=>'L',
119243=>'L',
119244=>'L',
119245=>'L',
119246=>'L',
119247=>'L',
119248=>'L',
119249=>'L',
119250=>'L',
119251=>'L',
119252=>'L',
119253=>'L',
119254=>'L',
119255=>'L',
119256=>'L',
119257=>'L',
119258=>'L',
119259=>'L',
119260=>'L',
119261=>'L',
119296=>'ON',
119297=>'ON',
119298=>'ON',
119299=>'ON',
119300=>'ON',
119301=>'ON',
119302=>'ON',
119303=>'ON',
119304=>'ON',
119305=>'ON',
119306=>'ON',
119307=>'ON',
119308=>'ON',
119309=>'ON',
119310=>'ON',
119311=>'ON',
119312=>'ON',
119313=>'ON',
119314=>'ON',
119315=>'ON',
119316=>'ON',
119317=>'ON',
119318=>'ON',
119319=>'ON',
119320=>'ON',
119321=>'ON',
119322=>'ON',
119323=>'ON',
119324=>'ON',
119325=>'ON',
119326=>'ON',
119327=>'ON',
119328=>'ON',
119329=>'ON',
119330=>'ON',
119331=>'ON',
119332=>'ON',
119333=>'ON',
119334=>'ON',
119335=>'ON',
119336=>'ON',
119337=>'ON',
119338=>'ON',
119339=>'ON',
119340=>'ON',
119341=>'ON',
119342=>'ON',
119343=>'ON',
119344=>'ON',
119345=>'ON',
119346=>'ON',
119347=>'ON',
119348=>'ON',
119349=>'ON',
119350=>'ON',
119351=>'ON',
119352=>'ON',
119353=>'ON',
119354=>'ON',
119355=>'ON',
119356=>'ON',
119357=>'ON',
119358=>'ON',
119359=>'ON',
119360=>'ON',
119361=>'ON',
119362=>'NSM',
119363=>'NSM',
119364=>'NSM',
119365=>'ON',
119552=>'ON',
119553=>'ON',
119554=>'ON',
119555=>'ON',
119556=>'ON',
119557=>'ON',
119558=>'ON',
119559=>'ON',
119560=>'ON',
119561=>'ON',
119562=>'ON',
119563=>'ON',
119564=>'ON',
119565=>'ON',
119566=>'ON',
119567=>'ON',
119568=>'ON',
119569=>'ON',
119570=>'ON',
119571=>'ON',
119572=>'ON',
119573=>'ON',
119574=>'ON',
119575=>'ON',
119576=>'ON',
119577=>'ON',
119578=>'ON',
119579=>'ON',
119580=>'ON',
119581=>'ON',
119582=>'ON',
119583=>'ON',
119584=>'ON',
119585=>'ON',
119586=>'ON',
119587=>'ON',
119588=>'ON',
119589=>'ON',
119590=>'ON',
119591=>'ON',
119592=>'ON',
119593=>'ON',
119594=>'ON',
119595=>'ON',
119596=>'ON',
119597=>'ON',
119598=>'ON',
119599=>'ON',
119600=>'ON',
119601=>'ON',
119602=>'ON',
119603=>'ON',
119604=>'ON',
119605=>'ON',
119606=>'ON',
119607=>'ON',
119608=>'ON',
119609=>'ON',
119610=>'ON',
119611=>'ON',
119612=>'ON',
119613=>'ON',
119614=>'ON',
119615=>'ON',
119616=>'ON',
119617=>'ON',
119618=>'ON',
119619=>'ON',
119620=>'ON',
119621=>'ON',
119622=>'ON',
119623=>'ON',
119624=>'ON',
119625=>'ON',
119626=>'ON',
119627=>'ON',
119628=>'ON',
119629=>'ON',
119630=>'ON',
119631=>'ON',
119632=>'ON',
119633=>'ON',
119634=>'ON',
119635=>'ON',
119636=>'ON',
119637=>'ON',
119638=>'ON',
119648=>'L',
119649=>'L',
119650=>'L',
119651=>'L',
119652=>'L',
119653=>'L',
119654=>'L',
119655=>'L',
119656=>'L',
119657=>'L',
119658=>'L',
119659=>'L',
119660=>'L',
119661=>'L',
119662=>'L',
119663=>'L',
119664=>'L',
119665=>'L',
119808=>'L',
119809=>'L',
119810=>'L',
119811=>'L',
119812=>'L',
119813=>'L',
119814=>'L',
119815=>'L',
119816=>'L',
119817=>'L',
119818=>'L',
119819=>'L',
119820=>'L',
119821=>'L',
119822=>'L',
119823=>'L',
119824=>'L',
119825=>'L',
119826=>'L',
119827=>'L',
119828=>'L',
119829=>'L',
119830=>'L',
119831=>'L',
119832=>'L',
119833=>'L',
119834=>'L',
119835=>'L',
119836=>'L',
119837=>'L',
119838=>'L',
119839=>'L',
119840=>'L',
119841=>'L',
119842=>'L',
119843=>'L',
119844=>'L',
119845=>'L',
119846=>'L',
119847=>'L',
119848=>'L',
119849=>'L',
119850=>'L',
119851=>'L',
119852=>'L',
119853=>'L',
119854=>'L',
119855=>'L',
119856=>'L',
119857=>'L',
119858=>'L',
119859=>'L',
119860=>'L',
119861=>'L',
119862=>'L',
119863=>'L',
119864=>'L',
119865=>'L',
119866=>'L',
119867=>'L',
119868=>'L',
119869=>'L',
119870=>'L',
119871=>'L',
119872=>'L',
119873=>'L',
119874=>'L',
119875=>'L',
119876=>'L',
119877=>'L',
119878=>'L',
119879=>'L',
119880=>'L',
119881=>'L',
119882=>'L',
119883=>'L',
119884=>'L',
119885=>'L',
119886=>'L',
119887=>'L',
119888=>'L',
119889=>'L',
119890=>'L',
119891=>'L',
119892=>'L',
119894=>'L',
119895=>'L',
119896=>'L',
119897=>'L',
119898=>'L',
119899=>'L',
119900=>'L',
119901=>'L',
119902=>'L',
119903=>'L',
119904=>'L',
119905=>'L',
119906=>'L',
119907=>'L',
119908=>'L',
119909=>'L',
119910=>'L',
119911=>'L',
119912=>'L',
119913=>'L',
119914=>'L',
119915=>'L',
119916=>'L',
119917=>'L',
119918=>'L',
119919=>'L',
119920=>'L',
119921=>'L',
119922=>'L',
119923=>'L',
119924=>'L',
119925=>'L',
119926=>'L',
119927=>'L',
119928=>'L',
119929=>'L',
119930=>'L',
119931=>'L',
119932=>'L',
119933=>'L',
119934=>'L',
119935=>'L',
119936=>'L',
119937=>'L',
119938=>'L',
119939=>'L',
119940=>'L',
119941=>'L',
119942=>'L',
119943=>'L',
119944=>'L',
119945=>'L',
119946=>'L',
119947=>'L',
119948=>'L',
119949=>'L',
119950=>'L',
119951=>'L',
119952=>'L',
119953=>'L',
119954=>'L',
119955=>'L',
119956=>'L',
119957=>'L',
119958=>'L',
119959=>'L',
119960=>'L',
119961=>'L',
119962=>'L',
119963=>'L',
119964=>'L',
119966=>'L',
119967=>'L',
119970=>'L',
119973=>'L',
119974=>'L',
119977=>'L',
119978=>'L',
119979=>'L',
119980=>'L',
119982=>'L',
119983=>'L',
119984=>'L',
119985=>'L',
119986=>'L',
119987=>'L',
119988=>'L',
119989=>'L',
119990=>'L',
119991=>'L',
119992=>'L',
119993=>'L',
119995=>'L',
119997=>'L',
119998=>'L',
119999=>'L',
120000=>'L',
120001=>'L',
120002=>'L',
120003=>'L',
120005=>'L',
120006=>'L',
120007=>'L',
120008=>'L',
120009=>'L',
120010=>'L',
120011=>'L',
120012=>'L',
120013=>'L',
120014=>'L',
120015=>'L',
120016=>'L',
120017=>'L',
120018=>'L',
120019=>'L',
120020=>'L',
120021=>'L',
120022=>'L',
120023=>'L',
120024=>'L',
120025=>'L',
120026=>'L',
120027=>'L',
120028=>'L',
120029=>'L',
120030=>'L',
120031=>'L',
120032=>'L',
120033=>'L',
120034=>'L',
120035=>'L',
120036=>'L',
120037=>'L',
120038=>'L',
120039=>'L',
120040=>'L',
120041=>'L',
120042=>'L',
120043=>'L',
120044=>'L',
120045=>'L',
120046=>'L',
120047=>'L',
120048=>'L',
120049=>'L',
120050=>'L',
120051=>'L',
120052=>'L',
120053=>'L',
120054=>'L',
120055=>'L',
120056=>'L',
120057=>'L',
120058=>'L',
120059=>'L',
120060=>'L',
120061=>'L',
120062=>'L',
120063=>'L',
120064=>'L',
120065=>'L',
120066=>'L',
120067=>'L',
120068=>'L',
120069=>'L',
120071=>'L',
120072=>'L',
120073=>'L',
120074=>'L',
120077=>'L',
120078=>'L',
120079=>'L',
120080=>'L',
120081=>'L',
120082=>'L',
120083=>'L',
120084=>'L',
120086=>'L',
120087=>'L',
120088=>'L',
120089=>'L',
120090=>'L',
120091=>'L',
120092=>'L',
120094=>'L',
120095=>'L',
120096=>'L',
120097=>'L',
120098=>'L',
120099=>'L',
120100=>'L',
120101=>'L',
120102=>'L',
120103=>'L',
120104=>'L',
120105=>'L',
120106=>'L',
120107=>'L',
120108=>'L',
120109=>'L',
120110=>'L',
120111=>'L',
120112=>'L',
120113=>'L',
120114=>'L',
120115=>'L',
120116=>'L',
120117=>'L',
120118=>'L',
120119=>'L',
120120=>'L',
120121=>'L',
120123=>'L',
120124=>'L',
120125=>'L',
120126=>'L',
120128=>'L',
120129=>'L',
120130=>'L',
120131=>'L',
120132=>'L',
120134=>'L',
120138=>'L',
120139=>'L',
120140=>'L',
120141=>'L',
120142=>'L',
120143=>'L',
120144=>'L',
120146=>'L',
120147=>'L',
120148=>'L',
120149=>'L',
120150=>'L',
120151=>'L',
120152=>'L',
120153=>'L',
120154=>'L',
120155=>'L',
120156=>'L',
120157=>'L',
120158=>'L',
120159=>'L',
120160=>'L',
120161=>'L',
120162=>'L',
120163=>'L',
120164=>'L',
120165=>'L',
120166=>'L',
120167=>'L',
120168=>'L',
120169=>'L',
120170=>'L',
120171=>'L',
120172=>'L',
120173=>'L',
120174=>'L',
120175=>'L',
120176=>'L',
120177=>'L',
120178=>'L',
120179=>'L',
120180=>'L',
120181=>'L',
120182=>'L',
120183=>'L',
120184=>'L',
120185=>'L',
120186=>'L',
120187=>'L',
120188=>'L',
120189=>'L',
120190=>'L',
120191=>'L',
120192=>'L',
120193=>'L',
120194=>'L',
120195=>'L',
120196=>'L',
120197=>'L',
120198=>'L',
120199=>'L',
120200=>'L',
120201=>'L',
120202=>'L',
120203=>'L',
120204=>'L',
120205=>'L',
120206=>'L',
120207=>'L',
120208=>'L',
120209=>'L',
120210=>'L',
120211=>'L',
120212=>'L',
120213=>'L',
120214=>'L',
120215=>'L',
120216=>'L',
120217=>'L',
120218=>'L',
120219=>'L',
120220=>'L',
120221=>'L',
120222=>'L',
120223=>'L',
120224=>'L',
120225=>'L',
120226=>'L',
120227=>'L',
120228=>'L',
120229=>'L',
120230=>'L',
120231=>'L',
120232=>'L',
120233=>'L',
120234=>'L',
120235=>'L',
120236=>'L',
120237=>'L',
120238=>'L',
120239=>'L',
120240=>'L',
120241=>'L',
120242=>'L',
120243=>'L',
120244=>'L',
120245=>'L',
120246=>'L',
120247=>'L',
120248=>'L',
120249=>'L',
120250=>'L',
120251=>'L',
120252=>'L',
120253=>'L',
120254=>'L',
120255=>'L',
120256=>'L',
120257=>'L',
120258=>'L',
120259=>'L',
120260=>'L',
120261=>'L',
120262=>'L',
120263=>'L',
120264=>'L',
120265=>'L',
120266=>'L',
120267=>'L',
120268=>'L',
120269=>'L',
120270=>'L',
120271=>'L',
120272=>'L',
120273=>'L',
120274=>'L',
120275=>'L',
120276=>'L',
120277=>'L',
120278=>'L',
120279=>'L',
120280=>'L',
120281=>'L',
120282=>'L',
120283=>'L',
120284=>'L',
120285=>'L',
120286=>'L',
120287=>'L',
120288=>'L',
120289=>'L',
120290=>'L',
120291=>'L',
120292=>'L',
120293=>'L',
120294=>'L',
120295=>'L',
120296=>'L',
120297=>'L',
120298=>'L',
120299=>'L',
120300=>'L',
120301=>'L',
120302=>'L',
120303=>'L',
120304=>'L',
120305=>'L',
120306=>'L',
120307=>'L',
120308=>'L',
120309=>'L',
120310=>'L',
120311=>'L',
120312=>'L',
120313=>'L',
120314=>'L',
120315=>'L',
120316=>'L',
120317=>'L',
120318=>'L',
120319=>'L',
120320=>'L',
120321=>'L',
120322=>'L',
120323=>'L',
120324=>'L',
120325=>'L',
120326=>'L',
120327=>'L',
120328=>'L',
120329=>'L',
120330=>'L',
120331=>'L',
120332=>'L',
120333=>'L',
120334=>'L',
120335=>'L',
120336=>'L',
120337=>'L',
120338=>'L',
120339=>'L',
120340=>'L',
120341=>'L',
120342=>'L',
120343=>'L',
120344=>'L',
120345=>'L',
120346=>'L',
120347=>'L',
120348=>'L',
120349=>'L',
120350=>'L',
120351=>'L',
120352=>'L',
120353=>'L',
120354=>'L',
120355=>'L',
120356=>'L',
120357=>'L',
120358=>'L',
120359=>'L',
120360=>'L',
120361=>'L',
120362=>'L',
120363=>'L',
120364=>'L',
120365=>'L',
120366=>'L',
120367=>'L',
120368=>'L',
120369=>'L',
120370=>'L',
120371=>'L',
120372=>'L',
120373=>'L',
120374=>'L',
120375=>'L',
120376=>'L',
120377=>'L',
120378=>'L',
120379=>'L',
120380=>'L',
120381=>'L',
120382=>'L',
120383=>'L',
120384=>'L',
120385=>'L',
120386=>'L',
120387=>'L',
120388=>'L',
120389=>'L',
120390=>'L',
120391=>'L',
120392=>'L',
120393=>'L',
120394=>'L',
120395=>'L',
120396=>'L',
120397=>'L',
120398=>'L',
120399=>'L',
120400=>'L',
120401=>'L',
120402=>'L',
120403=>'L',
120404=>'L',
120405=>'L',
120406=>'L',
120407=>'L',
120408=>'L',
120409=>'L',
120410=>'L',
120411=>'L',
120412=>'L',
120413=>'L',
120414=>'L',
120415=>'L',
120416=>'L',
120417=>'L',
120418=>'L',
120419=>'L',
120420=>'L',
120421=>'L',
120422=>'L',
120423=>'L',
120424=>'L',
120425=>'L',
120426=>'L',
120427=>'L',
120428=>'L',
120429=>'L',
120430=>'L',
120431=>'L',
120432=>'L',
120433=>'L',
120434=>'L',
120435=>'L',
120436=>'L',
120437=>'L',
120438=>'L',
120439=>'L',
120440=>'L',
120441=>'L',
120442=>'L',
120443=>'L',
120444=>'L',
120445=>'L',
120446=>'L',
120447=>'L',
120448=>'L',
120449=>'L',
120450=>'L',
120451=>'L',
120452=>'L',
120453=>'L',
120454=>'L',
120455=>'L',
120456=>'L',
120457=>'L',
120458=>'L',
120459=>'L',
120460=>'L',
120461=>'L',
120462=>'L',
120463=>'L',
120464=>'L',
120465=>'L',
120466=>'L',
120467=>'L',
120468=>'L',
120469=>'L',
120470=>'L',
120471=>'L',
120472=>'L',
120473=>'L',
120474=>'L',
120475=>'L',
120476=>'L',
120477=>'L',
120478=>'L',
120479=>'L',
120480=>'L',
120481=>'L',
120482=>'L',
120483=>'L',
120484=>'L',
120485=>'L',
120488=>'L',
120489=>'L',
120490=>'L',
120491=>'L',
120492=>'L',
120493=>'L',
120494=>'L',
120495=>'L',
120496=>'L',
120497=>'L',
120498=>'L',
120499=>'L',
120500=>'L',
120501=>'L',
120502=>'L',
120503=>'L',
120504=>'L',
120505=>'L',
120506=>'L',
120507=>'L',
120508=>'L',
120509=>'L',
120510=>'L',
120511=>'L',
120512=>'L',
120513=>'L',
120514=>'L',
120515=>'L',
120516=>'L',
120517=>'L',
120518=>'L',
120519=>'L',
120520=>'L',
120521=>'L',
120522=>'L',
120523=>'L',
120524=>'L',
120525=>'L',
120526=>'L',
120527=>'L',
120528=>'L',
120529=>'L',
120530=>'L',
120531=>'L',
120532=>'L',
120533=>'L',
120534=>'L',
120535=>'L',
120536=>'L',
120537=>'L',
120538=>'L',
120539=>'L',
120540=>'L',
120541=>'L',
120542=>'L',
120543=>'L',
120544=>'L',
120545=>'L',
120546=>'L',
120547=>'L',
120548=>'L',
120549=>'L',
120550=>'L',
120551=>'L',
120552=>'L',
120553=>'L',
120554=>'L',
120555=>'L',
120556=>'L',
120557=>'L',
120558=>'L',
120559=>'L',
120560=>'L',
120561=>'L',
120562=>'L',
120563=>'L',
120564=>'L',
120565=>'L',
120566=>'L',
120567=>'L',
120568=>'L',
120569=>'L',
120570=>'L',
120571=>'L',
120572=>'L',
120573=>'L',
120574=>'L',
120575=>'L',
120576=>'L',
120577=>'L',
120578=>'L',
120579=>'L',
120580=>'L',
120581=>'L',
120582=>'L',
120583=>'L',
120584=>'L',
120585=>'L',
120586=>'L',
120587=>'L',
120588=>'L',
120589=>'L',
120590=>'L',
120591=>'L',
120592=>'L',
120593=>'L',
120594=>'L',
120595=>'L',
120596=>'L',
120597=>'L',
120598=>'L',
120599=>'L',
120600=>'L',
120601=>'L',
120602=>'L',
120603=>'L',
120604=>'L',
120605=>'L',
120606=>'L',
120607=>'L',
120608=>'L',
120609=>'L',
120610=>'L',
120611=>'L',
120612=>'L',
120613=>'L',
120614=>'L',
120615=>'L',
120616=>'L',
120617=>'L',
120618=>'L',
120619=>'L',
120620=>'L',
120621=>'L',
120622=>'L',
120623=>'L',
120624=>'L',
120625=>'L',
120626=>'L',
120627=>'L',
120628=>'L',
120629=>'L',
120630=>'L',
120631=>'L',
120632=>'L',
120633=>'L',
120634=>'L',
120635=>'L',
120636=>'L',
120637=>'L',
120638=>'L',
120639=>'L',
120640=>'L',
120641=>'L',
120642=>'L',
120643=>'L',
120644=>'L',
120645=>'L',
120646=>'L',
120647=>'L',
120648=>'L',
120649=>'L',
120650=>'L',
120651=>'L',
120652=>'L',
120653=>'L',
120654=>'L',
120655=>'L',
120656=>'L',
120657=>'L',
120658=>'L',
120659=>'L',
120660=>'L',
120661=>'L',
120662=>'L',
120663=>'L',
120664=>'L',
120665=>'L',
120666=>'L',
120667=>'L',
120668=>'L',
120669=>'L',
120670=>'L',
120671=>'L',
120672=>'L',
120673=>'L',
120674=>'L',
120675=>'L',
120676=>'L',
120677=>'L',
120678=>'L',
120679=>'L',
120680=>'L',
120681=>'L',
120682=>'L',
120683=>'L',
120684=>'L',
120685=>'L',
120686=>'L',
120687=>'L',
120688=>'L',
120689=>'L',
120690=>'L',
120691=>'L',
120692=>'L',
120693=>'L',
120694=>'L',
120695=>'L',
120696=>'L',
120697=>'L',
120698=>'L',
120699=>'L',
120700=>'L',
120701=>'L',
120702=>'L',
120703=>'L',
120704=>'L',
120705=>'L',
120706=>'L',
120707=>'L',
120708=>'L',
120709=>'L',
120710=>'L',
120711=>'L',
120712=>'L',
120713=>'L',
120714=>'L',
120715=>'L',
120716=>'L',
120717=>'L',
120718=>'L',
120719=>'L',
120720=>'L',
120721=>'L',
120722=>'L',
120723=>'L',
120724=>'L',
120725=>'L',
120726=>'L',
120727=>'L',
120728=>'L',
120729=>'L',
120730=>'L',
120731=>'L',
120732=>'L',
120733=>'L',
120734=>'L',
120735=>'L',
120736=>'L',
120737=>'L',
120738=>'L',
120739=>'L',
120740=>'L',
120741=>'L',
120742=>'L',
120743=>'L',
120744=>'L',
120745=>'L',
120746=>'L',
120747=>'L',
120748=>'L',
120749=>'L',
120750=>'L',
120751=>'L',
120752=>'L',
120753=>'L',
120754=>'L',
120755=>'L',
120756=>'L',
120757=>'L',
120758=>'L',
120759=>'L',
120760=>'L',
120761=>'L',
120762=>'L',
120763=>'L',
120764=>'L',
120765=>'L',
120766=>'L',
120767=>'L',
120768=>'L',
120769=>'L',
120770=>'L',
120771=>'L',
120772=>'L',
120773=>'L',
120774=>'L',
120775=>'L',
120776=>'L',
120777=>'L',
120778=>'L',
120779=>'L',
120782=>'EN',
120783=>'EN',
120784=>'EN',
120785=>'EN',
120786=>'EN',
120787=>'EN',
120788=>'EN',
120789=>'EN',
120790=>'EN',
120791=>'EN',
120792=>'EN',
120793=>'EN',
120794=>'EN',
120795=>'EN',
120796=>'EN',
120797=>'EN',
120798=>'EN',
120799=>'EN',
120800=>'EN',
120801=>'EN',
120802=>'EN',
120803=>'EN',
120804=>'EN',
120805=>'EN',
120806=>'EN',
120807=>'EN',
120808=>'EN',
120809=>'EN',
120810=>'EN',
120811=>'EN',
120812=>'EN',
120813=>'EN',
120814=>'EN',
120815=>'EN',
120816=>'EN',
120817=>'EN',
120818=>'EN',
120819=>'EN',
120820=>'EN',
120821=>'EN',
120822=>'EN',
120823=>'EN',
120824=>'EN',
120825=>'EN',
120826=>'EN',
120827=>'EN',
120828=>'EN',
120829=>'EN',
120830=>'EN',
120831=>'EN',
131072=>'L',
173782=>'L',
194560=>'L',
194561=>'L',
194562=>'L',
194563=>'L',
194564=>'L',
194565=>'L',
194566=>'L',
194567=>'L',
194568=>'L',
194569=>'L',
194570=>'L',
194571=>'L',
194572=>'L',
194573=>'L',
194574=>'L',
194575=>'L',
194576=>'L',
194577=>'L',
194578=>'L',
194579=>'L',
194580=>'L',
194581=>'L',
194582=>'L',
194583=>'L',
194584=>'L',
194585=>'L',
194586=>'L',
194587=>'L',
194588=>'L',
194589=>'L',
194590=>'L',
194591=>'L',
194592=>'L',
194593=>'L',
194594=>'L',
194595=>'L',
194596=>'L',
194597=>'L',
194598=>'L',
194599=>'L',
194600=>'L',
194601=>'L',
194602=>'L',
194603=>'L',
194604=>'L',
194605=>'L',
194606=>'L',
194607=>'L',
194608=>'L',
194609=>'L',
194610=>'L',
194611=>'L',
194612=>'L',
194613=>'L',
194614=>'L',
194615=>'L',
194616=>'L',
194617=>'L',
194618=>'L',
194619=>'L',
194620=>'L',
194621=>'L',
194622=>'L',
194623=>'L',
194624=>'L',
194625=>'L',
194626=>'L',
194627=>'L',
194628=>'L',
194629=>'L',
194630=>'L',
194631=>'L',
194632=>'L',
194633=>'L',
194634=>'L',
194635=>'L',
194636=>'L',
194637=>'L',
194638=>'L',
194639=>'L',
194640=>'L',
194641=>'L',
194642=>'L',
194643=>'L',
194644=>'L',
194645=>'L',
194646=>'L',
194647=>'L',
194648=>'L',
194649=>'L',
194650=>'L',
194651=>'L',
194652=>'L',
194653=>'L',
194654=>'L',
194655=>'L',
194656=>'L',
194657=>'L',
194658=>'L',
194659=>'L',
194660=>'L',
194661=>'L',
194662=>'L',
194663=>'L',
194664=>'L',
194665=>'L',
194666=>'L',
194667=>'L',
194668=>'L',
194669=>'L',
194670=>'L',
194671=>'L',
194672=>'L',
194673=>'L',
194674=>'L',
194675=>'L',
194676=>'L',
194677=>'L',
194678=>'L',
194679=>'L',
194680=>'L',
194681=>'L',
194682=>'L',
194683=>'L',
194684=>'L',
194685=>'L',
194686=>'L',
194687=>'L',
194688=>'L',
194689=>'L',
194690=>'L',
194691=>'L',
194692=>'L',
194693=>'L',
194694=>'L',
194695=>'L',
194696=>'L',
194697=>'L',
194698=>'L',
194699=>'L',
194700=>'L',
194701=>'L',
194702=>'L',
194703=>'L',
194704=>'L',
194705=>'L',
194706=>'L',
194707=>'L',
194708=>'L',
194709=>'L',
194710=>'L',
194711=>'L',
194712=>'L',
194713=>'L',
194714=>'L',
194715=>'L',
194716=>'L',
194717=>'L',
194718=>'L',
194719=>'L',
194720=>'L',
194721=>'L',
194722=>'L',
194723=>'L',
194724=>'L',
194725=>'L',
194726=>'L',
194727=>'L',
194728=>'L',
194729=>'L',
194730=>'L',
194731=>'L',
194732=>'L',
194733=>'L',
194734=>'L',
194735=>'L',
194736=>'L',
194737=>'L',
194738=>'L',
194739=>'L',
194740=>'L',
194741=>'L',
194742=>'L',
194743=>'L',
194744=>'L',
194745=>'L',
194746=>'L',
194747=>'L',
194748=>'L',
194749=>'L',
194750=>'L',
194751=>'L',
194752=>'L',
194753=>'L',
194754=>'L',
194755=>'L',
194756=>'L',
194757=>'L',
194758=>'L',
194759=>'L',
194760=>'L',
194761=>'L',
194762=>'L',
194763=>'L',
194764=>'L',
194765=>'L',
194766=>'L',
194767=>'L',
194768=>'L',
194769=>'L',
194770=>'L',
194771=>'L',
194772=>'L',
194773=>'L',
194774=>'L',
194775=>'L',
194776=>'L',
194777=>'L',
194778=>'L',
194779=>'L',
194780=>'L',
194781=>'L',
194782=>'L',
194783=>'L',
194784=>'L',
194785=>'L',
194786=>'L',
194787=>'L',
194788=>'L',
194789=>'L',
194790=>'L',
194791=>'L',
194792=>'L',
194793=>'L',
194794=>'L',
194795=>'L',
194796=>'L',
194797=>'L',
194798=>'L',
194799=>'L',
194800=>'L',
194801=>'L',
194802=>'L',
194803=>'L',
194804=>'L',
194805=>'L',
194806=>'L',
194807=>'L',
194808=>'L',
194809=>'L',
194810=>'L',
194811=>'L',
194812=>'L',
194813=>'L',
194814=>'L',
194815=>'L',
194816=>'L',
194817=>'L',
194818=>'L',
194819=>'L',
194820=>'L',
194821=>'L',
194822=>'L',
194823=>'L',
194824=>'L',
194825=>'L',
194826=>'L',
194827=>'L',
194828=>'L',
194829=>'L',
194830=>'L',
194831=>'L',
194832=>'L',
194833=>'L',
194834=>'L',
194835=>'L',
194836=>'L',
194837=>'L',
194838=>'L',
194839=>'L',
194840=>'L',
194841=>'L',
194842=>'L',
194843=>'L',
194844=>'L',
194845=>'L',
194846=>'L',
194847=>'L',
194848=>'L',
194849=>'L',
194850=>'L',
194851=>'L',
194852=>'L',
194853=>'L',
194854=>'L',
194855=>'L',
194856=>'L',
194857=>'L',
194858=>'L',
194859=>'L',
194860=>'L',
194861=>'L',
194862=>'L',
194863=>'L',
194864=>'L',
194865=>'L',
194866=>'L',
194867=>'L',
194868=>'L',
194869=>'L',
194870=>'L',
194871=>'L',
194872=>'L',
194873=>'L',
194874=>'L',
194875=>'L',
194876=>'L',
194877=>'L',
194878=>'L',
194879=>'L',
194880=>'L',
194881=>'L',
194882=>'L',
194883=>'L',
194884=>'L',
194885=>'L',
194886=>'L',
194887=>'L',
194888=>'L',
194889=>'L',
194890=>'L',
194891=>'L',
194892=>'L',
194893=>'L',
194894=>'L',
194895=>'L',
194896=>'L',
194897=>'L',
194898=>'L',
194899=>'L',
194900=>'L',
194901=>'L',
194902=>'L',
194903=>'L',
194904=>'L',
194905=>'L',
194906=>'L',
194907=>'L',
194908=>'L',
194909=>'L',
194910=>'L',
194911=>'L',
194912=>'L',
194913=>'L',
194914=>'L',
194915=>'L',
194916=>'L',
194917=>'L',
194918=>'L',
194919=>'L',
194920=>'L',
194921=>'L',
194922=>'L',
194923=>'L',
194924=>'L',
194925=>'L',
194926=>'L',
194927=>'L',
194928=>'L',
194929=>'L',
194930=>'L',
194931=>'L',
194932=>'L',
194933=>'L',
194934=>'L',
194935=>'L',
194936=>'L',
194937=>'L',
194938=>'L',
194939=>'L',
194940=>'L',
194941=>'L',
194942=>'L',
194943=>'L',
194944=>'L',
194945=>'L',
194946=>'L',
194947=>'L',
194948=>'L',
194949=>'L',
194950=>'L',
194951=>'L',
194952=>'L',
194953=>'L',
194954=>'L',
194955=>'L',
194956=>'L',
194957=>'L',
194958=>'L',
194959=>'L',
194960=>'L',
194961=>'L',
194962=>'L',
194963=>'L',
194964=>'L',
194965=>'L',
194966=>'L',
194967=>'L',
194968=>'L',
194969=>'L',
194970=>'L',
194971=>'L',
194972=>'L',
194973=>'L',
194974=>'L',
194975=>'L',
194976=>'L',
194977=>'L',
194978=>'L',
194979=>'L',
194980=>'L',
194981=>'L',
194982=>'L',
194983=>'L',
194984=>'L',
194985=>'L',
194986=>'L',
194987=>'L',
194988=>'L',
194989=>'L',
194990=>'L',
194991=>'L',
194992=>'L',
194993=>'L',
194994=>'L',
194995=>'L',
194996=>'L',
194997=>'L',
194998=>'L',
194999=>'L',
195000=>'L',
195001=>'L',
195002=>'L',
195003=>'L',
195004=>'L',
195005=>'L',
195006=>'L',
195007=>'L',
195008=>'L',
195009=>'L',
195010=>'L',
195011=>'L',
195012=>'L',
195013=>'L',
195014=>'L',
195015=>'L',
195016=>'L',
195017=>'L',
195018=>'L',
195019=>'L',
195020=>'L',
195021=>'L',
195022=>'L',
195023=>'L',
195024=>'L',
195025=>'L',
195026=>'L',
195027=>'L',
195028=>'L',
195029=>'L',
195030=>'L',
195031=>'L',
195032=>'L',
195033=>'L',
195034=>'L',
195035=>'L',
195036=>'L',
195037=>'L',
195038=>'L',
195039=>'L',
195040=>'L',
195041=>'L',
195042=>'L',
195043=>'L',
195044=>'L',
195045=>'L',
195046=>'L',
195047=>'L',
195048=>'L',
195049=>'L',
195050=>'L',
195051=>'L',
195052=>'L',
195053=>'L',
195054=>'L',
195055=>'L',
195056=>'L',
195057=>'L',
195058=>'L',
195059=>'L',
195060=>'L',
195061=>'L',
195062=>'L',
195063=>'L',
195064=>'L',
195065=>'L',
195066=>'L',
195067=>'L',
195068=>'L',
195069=>'L',
195070=>'L',
195071=>'L',
195072=>'L',
195073=>'L',
195074=>'L',
195075=>'L',
195076=>'L',
195077=>'L',
195078=>'L',
195079=>'L',
195080=>'L',
195081=>'L',
195082=>'L',
195083=>'L',
195084=>'L',
195085=>'L',
195086=>'L',
195087=>'L',
195088=>'L',
195089=>'L',
195090=>'L',
195091=>'L',
195092=>'L',
195093=>'L',
195094=>'L',
195095=>'L',
195096=>'L',
195097=>'L',
195098=>'L',
195099=>'L',
195100=>'L',
195101=>'L',
917505=>'BN',
917536=>'BN',
917537=>'BN',
917538=>'BN',
917539=>'BN',
917540=>'BN',
917541=>'BN',
917542=>'BN',
917543=>'BN',
917544=>'BN',
917545=>'BN',
917546=>'BN',
917547=>'BN',
917548=>'BN',
917549=>'BN',
917550=>'BN',
917551=>'BN',
917552=>'BN',
917553=>'BN',
917554=>'BN',
917555=>'BN',
917556=>'BN',
917557=>'BN',
917558=>'BN',
917559=>'BN',
917560=>'BN',
917561=>'BN',
917562=>'BN',
917563=>'BN',
917564=>'BN',
917565=>'BN',
917566=>'BN',
917567=>'BN',
917568=>'BN',
917569=>'BN',
917570=>'BN',
917571=>'BN',
917572=>'BN',
917573=>'BN',
917574=>'BN',
917575=>'BN',
917576=>'BN',
917577=>'BN',
917578=>'BN',
917579=>'BN',
917580=>'BN',
917581=>'BN',
917582=>'BN',
917583=>'BN',
917584=>'BN',
917585=>'BN',
917586=>'BN',
917587=>'BN',
917588=>'BN',
917589=>'BN',
917590=>'BN',
917591=>'BN',
917592=>'BN',
917593=>'BN',
917594=>'BN',
917595=>'BN',
917596=>'BN',
917597=>'BN',
917598=>'BN',
917599=>'BN',
917600=>'BN',
917601=>'BN',
917602=>'BN',
917603=>'BN',
917604=>'BN',
917605=>'BN',
917606=>'BN',
917607=>'BN',
917608=>'BN',
917609=>'BN',
917610=>'BN',
917611=>'BN',
917612=>'BN',
917613=>'BN',
917614=>'BN',
917615=>'BN',
917616=>'BN',
917617=>'BN',
917618=>'BN',
917619=>'BN',
917620=>'BN',
917621=>'BN',
917622=>'BN',
917623=>'BN',
917624=>'BN',
917625=>'BN',
917626=>'BN',
917627=>'BN',
917628=>'BN',
917629=>'BN',
917630=>'BN',
917631=>'BN',
917760=>'NSM',
917761=>'NSM',
917762=>'NSM',
917763=>'NSM',
917764=>'NSM',
917765=>'NSM',
917766=>'NSM',
917767=>'NSM',
917768=>'NSM',
917769=>'NSM',
917770=>'NSM',
917771=>'NSM',
917772=>'NSM',
917773=>'NSM',
917774=>'NSM',
917775=>'NSM',
917776=>'NSM',
917777=>'NSM',
917778=>'NSM',
917779=>'NSM',
917780=>'NSM',
917781=>'NSM',
917782=>'NSM',
917783=>'NSM',
917784=>'NSM',
917785=>'NSM',
917786=>'NSM',
917787=>'NSM',
917788=>'NSM',
917789=>'NSM',
917790=>'NSM',
917791=>'NSM',
917792=>'NSM',
917793=>'NSM',
917794=>'NSM',
917795=>'NSM',
917796=>'NSM',
917797=>'NSM',
917798=>'NSM',
917799=>'NSM',
917800=>'NSM',
917801=>'NSM',
917802=>'NSM',
917803=>'NSM',
917804=>'NSM',
917805=>'NSM',
917806=>'NSM',
917807=>'NSM',
917808=>'NSM',
917809=>'NSM',
917810=>'NSM',
917811=>'NSM',
917812=>'NSM',
917813=>'NSM',
917814=>'NSM',
917815=>'NSM',
917816=>'NSM',
917817=>'NSM',
917818=>'NSM',
917819=>'NSM',
917820=>'NSM',
917821=>'NSM',
917822=>'NSM',
917823=>'NSM',
917824=>'NSM',
917825=>'NSM',
917826=>'NSM',
917827=>'NSM',
917828=>'NSM',
917829=>'NSM',
917830=>'NSM',
917831=>'NSM',
917832=>'NSM',
917833=>'NSM',
917834=>'NSM',
917835=>'NSM',
917836=>'NSM',
917837=>'NSM',
917838=>'NSM',
917839=>'NSM',
917840=>'NSM',
917841=>'NSM',
917842=>'NSM',
917843=>'NSM',
917844=>'NSM',
917845=>'NSM',
917846=>'NSM',
917847=>'NSM',
917848=>'NSM',
917849=>'NSM',
917850=>'NSM',
917851=>'NSM',
917852=>'NSM',
917853=>'NSM',
917854=>'NSM',
917855=>'NSM',
917856=>'NSM',
917857=>'NSM',
917858=>'NSM',
917859=>'NSM',
917860=>'NSM',
917861=>'NSM',
917862=>'NSM',
917863=>'NSM',
917864=>'NSM',
917865=>'NSM',
917866=>'NSM',
917867=>'NSM',
917868=>'NSM',
917869=>'NSM',
917870=>'NSM',
917871=>'NSM',
917872=>'NSM',
917873=>'NSM',
917874=>'NSM',
917875=>'NSM',
917876=>'NSM',
917877=>'NSM',
917878=>'NSM',
917879=>'NSM',
917880=>'NSM',
917881=>'NSM',
917882=>'NSM',
917883=>'NSM',
917884=>'NSM',
917885=>'NSM',
917886=>'NSM',
917887=>'NSM',
917888=>'NSM',
917889=>'NSM',
917890=>'NSM',
917891=>'NSM',
917892=>'NSM',
917893=>'NSM',
917894=>'NSM',
917895=>'NSM',
917896=>'NSM',
917897=>'NSM',
917898=>'NSM',
917899=>'NSM',
917900=>'NSM',
917901=>'NSM',
917902=>'NSM',
917903=>'NSM',
917904=>'NSM',
917905=>'NSM',
917906=>'NSM',
917907=>'NSM',
917908=>'NSM',
917909=>'NSM',
917910=>'NSM',
917911=>'NSM',
917912=>'NSM',
917913=>'NSM',
917914=>'NSM',
917915=>'NSM',
917916=>'NSM',
917917=>'NSM',
917918=>'NSM',
917919=>'NSM',
917920=>'NSM',
917921=>'NSM',
917922=>'NSM',
917923=>'NSM',
917924=>'NSM',
917925=>'NSM',
917926=>'NSM',
917927=>'NSM',
917928=>'NSM',
917929=>'NSM',
917930=>'NSM',
917931=>'NSM',
917932=>'NSM',
917933=>'NSM',
917934=>'NSM',
917935=>'NSM',
917936=>'NSM',
917937=>'NSM',
917938=>'NSM',
917939=>'NSM',
917940=>'NSM',
917941=>'NSM',
917942=>'NSM',
917943=>'NSM',
917944=>'NSM',
917945=>'NSM',
917946=>'NSM',
917947=>'NSM',
917948=>'NSM',
917949=>'NSM',
917950=>'NSM',
917951=>'NSM',
917952=>'NSM',
917953=>'NSM',
917954=>'NSM',
917955=>'NSM',
917956=>'NSM',
917957=>'NSM',
917958=>'NSM',
917959=>'NSM',
917960=>'NSM',
917961=>'NSM',
917962=>'NSM',
917963=>'NSM',
917964=>'NSM',
917965=>'NSM',
917966=>'NSM',
917967=>'NSM',
917968=>'NSM',
917969=>'NSM',
917970=>'NSM',
917971=>'NSM',
917972=>'NSM',
917973=>'NSM',
917974=>'NSM',
917975=>'NSM',
917976=>'NSM',
917977=>'NSM',
917978=>'NSM',
917979=>'NSM',
917980=>'NSM',
917981=>'NSM',
917982=>'NSM',
917983=>'NSM',
917984=>'NSM',
917985=>'NSM',
917986=>'NSM',
917987=>'NSM',
917988=>'NSM',
917989=>'NSM',
917990=>'NSM',
917991=>'NSM',
917992=>'NSM',
917993=>'NSM',
917994=>'NSM',
917995=>'NSM',
917996=>'NSM',
917997=>'NSM',
917998=>'NSM',
917999=>'NSM',
983040=>'L',
1048573=>'L',
1048576=>'L',
1114109=>'L'
);

/**
 * Mirror unicode characters. For information on bidi mirroring, see UAX #9: Bidirectional Algorithm, at http://www.unicode.org/unicode/reports/tr9/
 * @public
 */
public static $uni_mirror = array (
0x0028=>0x0029,
0x0029=>0x0028,
0x003C=>0x003E,
0x003E=>0x003C,
0x005B=>0x005D,
0x005D=>0x005B,
0x007B=>0x007D,
0x007D=>0x007B,
0x00AB=>0x00BB,
0x00BB=>0x00AB,
0x0F3A=>0x0F3B,
0x0F3B=>0x0F3A,
0x0F3C=>0x0F3D,
0x0F3D=>0x0F3C,
0x169B=>0x169C,
0x169C=>0x169B,
0x2018=>0x2019,
0x2019=>0x2018,
0x201C=>0x201D,
0x201D=>0x201C,
0x2039=>0x203A,
0x203A=>0x2039,
0x2045=>0x2046,
0x2046=>0x2045,
0x207D=>0x207E,
0x207E=>0x207D,
0x208D=>0x208E,
0x208E=>0x208D,
0x2208=>0x220B,
0x2209=>0x220C,
0x220A=>0x220D,
0x220B=>0x2208,
0x220C=>0x2209,
0x220D=>0x220A,
0x2215=>0x29F5,
0x223C=>0x223D,
0x223D=>0x223C,
0x2243=>0x22CD,
0x2252=>0x2253,
0x2253=>0x2252,
0x2254=>0x2255,
0x2255=>0x2254,
0x2264=>0x2265,
0x2265=>0x2264,
0x2266=>0x2267,
0x2267=>0x2266,
0x2268=>0x2269,
0x2269=>0x2268,
0x226A=>0x226B,
0x226B=>0x226A,
0x226E=>0x226F,
0x226F=>0x226E,
0x2270=>0x2271,
0x2271=>0x2270,
0x2272=>0x2273,
0x2273=>0x2272,
0x2274=>0x2275,
0x2275=>0x2274,
0x2276=>0x2277,
0x2277=>0x2276,
0x2278=>0x2279,
0x2279=>0x2278,
0x227A=>0x227B,
0x227B=>0x227A,
0x227C=>0x227D,
0x227D=>0x227C,
0x227E=>0x227F,
0x227F=>0x227E,
0x2280=>0x2281,
0x2281=>0x2280,
0x2282=>0x2283,
0x2283=>0x2282,
0x2284=>0x2285,
0x2285=>0x2284,
0x2286=>0x2287,
0x2287=>0x2286,
0x2288=>0x2289,
0x2289=>0x2288,
0x228A=>0x228B,
0x228B=>0x228A,
0x228F=>0x2290,
0x2290=>0x228F,
0x2291=>0x2292,
0x2292=>0x2291,
0x2298=>0x29B8,
0x22A2=>0x22A3,
0x22A3=>0x22A2,
0x22A6=>0x2ADE,
0x22A8=>0x2AE4,
0x22A9=>0x2AE3,
0x22AB=>0x2AE5,
0x22B0=>0x22B1,
0x22B1=>0x22B0,
0x22B2=>0x22B3,
0x22B3=>0x22B2,
0x22B4=>0x22B5,
0x22B5=>0x22B4,
0x22B6=>0x22B7,
0x22B7=>0x22B6,
0x22C9=>0x22CA,
0x22CA=>0x22C9,
0x22CB=>0x22CC,
0x22CC=>0x22CB,
0x22CD=>0x2243,
0x22D0=>0x22D1,
0x22D1=>0x22D0,
0x22D6=>0x22D7,
0x22D7=>0x22D6,
0x22D8=>0x22D9,
0x22D9=>0x22D8,
0x22DA=>0x22DB,
0x22DB=>0x22DA,
0x22DC=>0x22DD,
0x22DD=>0x22DC,
0x22DE=>0x22DF,
0x22DF=>0x22DE,
0x22E0=>0x22E1,
0x22E1=>0x22E0,
0x22E2=>0x22E3,
0x22E3=>0x22E2,
0x22E4=>0x22E5,
0x22E5=>0x22E4,
0x22E6=>0x22E7,
0x22E7=>0x22E6,
0x22E8=>0x22E9,
0x22E9=>0x22E8,
0x22EA=>0x22EB,
0x22EB=>0x22EA,
0x22EC=>0x22ED,
0x22ED=>0x22EC,
0x22F0=>0x22F1,
0x22F1=>0x22F0,
0x22F2=>0x22FA,
0x22F3=>0x22FB,
0x22F4=>0x22FC,
0x22F6=>0x22FD,
0x22F7=>0x22FE,
0x22FA=>0x22F2,
0x22FB=>0x22F3,
0x22FC=>0x22F4,
0x22FD=>0x22F6,
0x22FE=>0x22F7,
0x2308=>0x2309,
0x2309=>0x2308,
0x230A=>0x230B,
0x230B=>0x230A,
0x2329=>0x232A,
0x232A=>0x2329,
0x2768=>0x2769,
0x2769=>0x2768,
0x276A=>0x276B,
0x276B=>0x276A,
0x276C=>0x276D,
0x276D=>0x276C,
0x276E=>0x276F,
0x276F=>0x276E,
0x2770=>0x2771,
0x2771=>0x2770,
0x2772=>0x2773,
0x2773=>0x2772,
0x2774=>0x2775,
0x2775=>0x2774,
0x27C3=>0x27C4,
0x27C4=>0x27C3,
0x27C5=>0x27C6,
0x27C6=>0x27C5,
0x27D5=>0x27D6,
0x27D6=>0x27D5,
0x27DD=>0x27DE,
0x27DE=>0x27DD,
0x27E2=>0x27E3,
0x27E3=>0x27E2,
0x27E4=>0x27E5,
0x27E5=>0x27E4,
0x27E6=>0x27E7,
0x27E7=>0x27E6,
0x27E8=>0x27E9,
0x27E9=>0x27E8,
0x27EA=>0x27EB,
0x27EB=>0x27EA,
0x2983=>0x2984,
0x2984=>0x2983,
0x2985=>0x2986,
0x2986=>0x2985,
0x2987=>0x2988,
0x2988=>0x2987,
0x2989=>0x298A,
0x298A=>0x2989,
0x298B=>0x298C,
0x298C=>0x298B,
0x298D=>0x2990,
0x298E=>0x298F,
0x298F=>0x298E,
0x2990=>0x298D,
0x2991=>0x2992,
0x2992=>0x2991,
0x2993=>0x2994,
0x2994=>0x2993,
0x2995=>0x2996,
0x2996=>0x2995,
0x2997=>0x2998,
0x2998=>0x2997,
0x29B8=>0x2298,
0x29C0=>0x29C1,
0x29C1=>0x29C0,
0x29C4=>0x29C5,
0x29C5=>0x29C4,
0x29CF=>0x29D0,
0x29D0=>0x29CF,
0x29D1=>0x29D2,
0x29D2=>0x29D1,
0x29D4=>0x29D5,
0x29D5=>0x29D4,
0x29D8=>0x29D9,
0x29D9=>0x29D8,
0x29DA=>0x29DB,
0x29DB=>0x29DA,
0x29F5=>0x2215,
0x29F8=>0x29F9,
0x29F9=>0x29F8,
0x29FC=>0x29FD,
0x29FD=>0x29FC,
0x2A2B=>0x2A2C,
0x2A2C=>0x2A2B,
0x2A2D=>0x2A2E,
0x2A2E=>0x2A2D,
0x2A34=>0x2A35,
0x2A35=>0x2A34,
0x2A3C=>0x2A3D,
0x2A3D=>0x2A3C,
0x2A64=>0x2A65,
0x2A65=>0x2A64,
0x2A79=>0x2A7A,
0x2A7A=>0x2A79,
0x2A7D=>0x2A7E,
0x2A7E=>0x2A7D,
0x2A7F=>0x2A80,
0x2A80=>0x2A7F,
0x2A81=>0x2A82,
0x2A82=>0x2A81,
0x2A83=>0x2A84,
0x2A84=>0x2A83,
0x2A8B=>0x2A8C,
0x2A8C=>0x2A8B,
0x2A91=>0x2A92,
0x2A92=>0x2A91,
0x2A93=>0x2A94,
0x2A94=>0x2A93,
0x2A95=>0x2A96,
0x2A96=>0x2A95,
0x2A97=>0x2A98,
0x2A98=>0x2A97,
0x2A99=>0x2A9A,
0x2A9A=>0x2A99,
0x2A9B=>0x2A9C,
0x2A9C=>0x2A9B,
0x2AA1=>0x2AA2,
0x2AA2=>0x2AA1,
0x2AA6=>0x2AA7,
0x2AA7=>0x2AA6,
0x2AA8=>0x2AA9,
0x2AA9=>0x2AA8,
0x2AAA=>0x2AAB,
0x2AAB=>0x2AAA,
0x2AAC=>0x2AAD,
0x2AAD=>0x2AAC,
0x2AAF=>0x2AB0,
0x2AB0=>0x2AAF,
0x2AB3=>0x2AB4,
0x2AB4=>0x2AB3,
0x2ABB=>0x2ABC,
0x2ABC=>0x2ABB,
0x2ABD=>0x2ABE,
0x2ABE=>0x2ABD,
0x2ABF=>0x2AC0,
0x2AC0=>0x2ABF,
0x2AC1=>0x2AC2,
0x2AC2=>0x2AC1,
0x2AC3=>0x2AC4,
0x2AC4=>0x2AC3,
0x2AC5=>0x2AC6,
0x2AC6=>0x2AC5,
0x2ACD=>0x2ACE,
0x2ACE=>0x2ACD,
0x2ACF=>0x2AD0,
0x2AD0=>0x2ACF,
0x2AD1=>0x2AD2,
0x2AD2=>0x2AD1,
0x2AD3=>0x2AD4,
0x2AD4=>0x2AD3,
0x2AD5=>0x2AD6,
0x2AD6=>0x2AD5,
0x2ADE=>0x22A6,
0x2AE3=>0x22A9,
0x2AE4=>0x22A8,
0x2AE5=>0x22AB,
0x2AEC=>0x2AED,
0x2AED=>0x2AEC,
0x2AF7=>0x2AF8,
0x2AF8=>0x2AF7,
0x2AF9=>0x2AFA,
0x2AFA=>0x2AF9,
0x2E02=>0x2E03,
0x2E03=>0x2E02,
0x2E04=>0x2E05,
0x2E05=>0x2E04,
0x2E09=>0x2E0A,
0x2E0A=>0x2E09,
0x2E0C=>0x2E0D,
0x2E0D=>0x2E0C,
0x2E1C=>0x2E1D,
0x2E1D=>0x2E1C,
0x3008=>0x3009,
0x3009=>0x3008,
0x300A=>0x300B,
0x300B=>0x300A,
0x300C=>0x300D,
0x300D=>0x300C,
0x300E=>0x300F,
0x300F=>0x300E,
0x3010=>0x3011,
0x3011=>0x3010,
0x3014=>0x3015,
0x3015=>0x3014,
0x3016=>0x3017,
0x3017=>0x3016,
0x3018=>0x3019,
0x3019=>0x3018,
0x301A=>0x301B,
0x301B=>0x301A,
0x301D=>0x301E,
0x301E=>0x301D,
0xFE59=>0xFE5A,
0xFE5A=>0xFE59,
0xFE5B=>0xFE5C,
0xFE5C=>0xFE5B,
0xFE5D=>0xFE5E,
0xFE5E=>0xFE5D,
0xFE64=>0xFE65,
0xFE65=>0xFE64,
0xFF08=>0xFF09,
0xFF09=>0xFF08,
0xFF1C=>0xFF1E,
0xFF1E=>0xFF1C,
0xFF3B=>0xFF3D,
0xFF3D=>0xFF3B,
0xFF5B=>0xFF5D,
0xFF5D=>0xFF5B,
0xFF5F=>0xFF60,
0xFF60=>0xFF5F,
0xFF62=>0xFF63,
0xFF63=>0xFF62);

/**
 * Arabic shape substitutions: char code => (isolated, final, initial, medial).
 * @public
 */
public static $uni_arabicsubst = array(
1569=>array(65152),
1570=>array(65153, 65154, 65153, 65154),
1571=>array(65155, 65156, 65155, 65156),
1572=>array(65157, 65158),
1573=>array(65159, 65160, 65159, 65160),
1574=>array(65161, 65162, 65163, 65164),
1575=>array(65165, 65166, 65165, 65166),
1576=>array(65167, 65168, 65169, 65170),
1577=>array(65171, 65172),
1578=>array(65173, 65174, 65175, 65176),
1579=>array(65177, 65178, 65179, 65180),
1580=>array(65181, 65182, 65183, 65184),
1581=>array(65185, 65186, 65187, 65188),
1582=>array(65189, 65190, 65191, 65192),
1583=>array(65193, 65194, 65193, 65194),
1584=>array(65195, 65196, 65195, 65196),
1585=>array(65197, 65198, 65197, 65198),
1586=>array(65199, 65200, 65199, 65200),
1587=>array(65201, 65202, 65203, 65204),
1588=>array(65205, 65206, 65207, 65208),
1589=>array(65209, 65210, 65211, 65212),
1590=>array(65213, 65214, 65215, 65216),
1591=>array(65217, 65218, 65219, 65220),
1592=>array(65221, 65222, 65223, 65224),
1593=>array(65225, 65226, 65227, 65228),
1594=>array(65229, 65230, 65231, 65232),
1601=>array(65233, 65234, 65235, 65236),
1602=>array(65237, 65238, 65239, 65240),
1603=>array(65241, 65242, 65243, 65244),
1604=>array(65245, 65246, 65247, 65248),
1605=>array(65249, 65250, 65251, 65252),
1606=>array(65253, 65254, 65255, 65256),
1607=>array(65257, 65258, 65259, 65260),
1608=>array(65261, 65262, 65261, 65262),
1609=>array(65263, 65264, 64488, 64489),
1610=>array(65265, 65266, 65267, 65268),
1649=>array(64336, 64337),
1655=>array(64477),
1657=>array(64358, 64359, 64360, 64361),
1658=>array(64350, 64351, 64352, 64353),
1659=>array(64338, 64339, 64340, 64341),
1662=>array(64342, 64343, 64344, 64345),
1663=>array(64354, 64355, 64356, 64357),
1664=>array(64346, 64347, 64348, 64349),
1667=>array(64374, 64375, 64376, 64377),
1668=>array(64370, 64371, 64372, 64373),
1670=>array(64378, 64379, 64380, 64381),
1671=>array(64382, 64383, 64384, 64385),
1672=>array(64392, 64393),
1676=>array(64388, 64389),
1677=>array(64386, 64387),
1678=>array(64390, 64391),
1681=>array(64396, 64397),
1688=>array(64394, 64395, 64394, 64395),
1700=>array(64362, 64363, 64364, 64365),
1702=>array(64366, 64367, 64368, 64369),
1705=>array(64398, 64399, 64400, 64401),
1709=>array(64467, 64468, 64469, 64470),
1711=>array(64402, 64403, 64404, 64405),
1713=>array(64410, 64411, 64412, 64413),
1715=>array(64406, 64407, 64408, 64409),
1722=>array(64414, 64415),
1723=>array(64416, 64417, 64418, 64419),
1726=>array(64426, 64427, 64428, 64429),
1728=>array(64420, 64421),
1729=>array(64422, 64423, 64424, 64425),
1733=>array(64480, 64481),
1734=>array(64473, 64474),
1735=>array(64471, 64472),
1736=>array(64475, 64476),
1737=>array(64482, 64483),
1739=>array(64478, 64479),
1740=>array(64508, 64509, 64510, 64511),
1744=>array(64484, 64485, 64486, 64487),
1746=>array(64430, 64431),
1747=>array(64432, 64433)
);

/**
 * Arabic laa letter: (char code => isolated, final, initial, medial).
 * @public
 */
public static $uni_laa_array = array (
1570 =>array(65269, 65270, 65269, 65270),
1571 =>array(65271, 65272, 65271, 65272),
1573 =>array(65273, 65274, 65273, 65274),
1575 =>array(65275, 65276, 65275, 65276)
);

/**
 * Array of character substitutions for sequences of two diacritics symbols.
 * Putting the combining mark and character in the same glyph allows us to avoid the two marks overlapping each other in an illegible manner.
 * second NSM char code => substitution char
 * @public
 */
public static $uni_diacritics = array (
1612=>64606, # Shadda + Dammatan
1613=>64607, # Shadda + Kasratan
1614=>64608, # Shadda + Fatha
1615=>64609, # Shadda + Damma
1616=>64610  # Shadda + Kasra
);

/**
 * Array of character substitutions from UTF-8 Unicode to Latin1.
 * @public
 */
public static $uni_utf8tolatin = array (
8364=>128, # Euro1
338=>140,  # OE
352=>138,  # Scaron
376=>159,  # Ydieresis
381=>142,  # Zcaron2
8226=>149, # bullet3
710=>136,  # circumflex
8224=>134, # dagger
8225=>135, # daggerdbl
8230=>133, # ellipsis
8212=>151, # emdash
8211=>150, # endash
402=>131,  # florin
8249=>139, # guilsinglleft
8250=>155, # guilsinglright
339=>156,  # oe
8240=>137, # perthousand
8222=>132, # quotedblbase
8220=>147, # quotedblleft
8221=>148, # quotedblright
8216=>145, # quoteleft
8217=>146, # quoteright
8218=>130, # quotesinglbase
353=>154,  # scaron
732=>152,  # tilde
8482=>153, # trademark
382=>158   # zcaron2
);

/**
 * Array of Encoding Maps.
 * @public static
 */
public static $encmap = array(

// encoding map for: cp874
'cp874' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'Euro',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'ellipsis',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'kokaithai',162=>'khokhaithai',163=>'khokhuatthai',164=>'khokhwaithai',165=>'khokhonthai',166=>'khorakhangthai',167=>'ngonguthai',168=>'chochanthai',169=>'chochingthai',170=>'chochangthai',171=>'sosothai',172=>'chochoethai',173=>'yoyingthai',174=>'dochadathai',175=>'topatakthai',176=>'thothanthai',177=>'thonangmonthothai',178=>'thophuthaothai',179=>'nonenthai',180=>'dodekthai',181=>'totaothai',182=>'thothungthai',183=>'thothahanthai',184=>'thothongthai',185=>'nonuthai',186=>'bobaimaithai',187=>'poplathai',188=>'phophungthai',189=>'fofathai',190=>'phophanthai',191=>'fofanthai',192=>'phosamphaothai',193=>'momathai',194=>'yoyakthai',195=>'roruathai',196=>'ruthai',197=>'lolingthai',198=>'luthai',199=>'wowaenthai',200=>'sosalathai',201=>'sorusithai',202=>'sosuathai',203=>'hohipthai',204=>'lochulathai',205=>'oangthai',206=>'honokhukthai',207=>'paiyannoithai',208=>'saraathai',209=>'maihanakatthai',210=>'saraaathai',211=>'saraamthai',212=>'saraithai',213=>'saraiithai',214=>'sarauethai',215=>'saraueethai',216=>'sarauthai',217=>'sarauuthai',218=>'phinthuthai',219=>'.notdef',220=>'.notdef',221=>'.notdef',222=>'.notdef',223=>'bahtthai',224=>'saraethai',225=>'saraaethai',226=>'saraothai',227=>'saraaimaimuanthai',228=>'saraaimaimalaithai',229=>'lakkhangyaothai',230=>'maiyamokthai',231=>'maitaikhuthai',232=>'maiekthai',233=>'maithothai',234=>'maitrithai',235=>'maichattawathai',236=>'thanthakhatthai',237=>'nikhahitthai',238=>'yamakkanthai',239=>'fongmanthai',240=>'zerothai',241=>'onethai',242=>'twothai',243=>'threethai',244=>'fourthai',245=>'fivethai',246=>'sixthai',247=>'seventhai',248=>'eightthai',249=>'ninethai',250=>'angkhankhuthai',251=>'khomutthai',252=>'.notdef',253=>'.notdef',254=>'.notdef',255=>'.notdef'),

// encoding map for: cp1250
'cp1250' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'Euro',129=>'.notdef',130=>'quotesinglbase',131=>'.notdef',132=>'quotedblbase',133=>'ellipsis',134=>'dagger',135=>'daggerdbl',136=>'.notdef',137=>'perthousand',138=>'Scaron',139=>'guilsinglleft',140=>'Sacute',141=>'Tcaron',142=>'Zcaron',143=>'Zacute',144=>'.notdef',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'.notdef',153=>'trademark',154=>'scaron',155=>'guilsinglright',156=>'sacute',157=>'tcaron',158=>'zcaron',159=>'zacute',160=>'space',161=>'caron',162=>'breve',163=>'Lslash',164=>'currency',165=>'Aogonek',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'Scedilla',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'Zdotaccent',176=>'degree',177=>'plusminus',178=>'ogonek',179=>'lslash',180=>'acute',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'cedilla',185=>'aogonek',186=>'scedilla',187=>'guillemotright',188=>'Lcaron',189=>'hungarumlaut',190=>'lcaron',191=>'zdotaccent',192=>'Racute',193=>'Aacute',194=>'Acircumflex',195=>'Abreve',196=>'Adieresis',197=>'Lacute',198=>'Cacute',199=>'Ccedilla',200=>'Ccaron',201=>'Eacute',202=>'Eogonek',203=>'Edieresis',204=>'Ecaron',205=>'Iacute',206=>'Icircumflex',207=>'Dcaron',208=>'Dcroat',209=>'Nacute',210=>'Ncaron',211=>'Oacute',212=>'Ocircumflex',213=>'Ohungarumlaut',214=>'Odieresis',215=>'multiply',216=>'Rcaron',217=>'Uring',218=>'Uacute',219=>'Uhungarumlaut',220=>'Udieresis',221=>'Yacute',222=>'Tcommaaccent',223=>'germandbls',224=>'racute',225=>'aacute',226=>'acircumflex',227=>'abreve',228=>'adieresis',229=>'lacute',230=>'cacute',231=>'ccedilla',232=>'ccaron',233=>'eacute',234=>'eogonek',235=>'edieresis',236=>'ecaron',237=>'iacute',238=>'icircumflex',239=>'dcaron',240=>'dcroat',241=>'nacute',242=>'ncaron',243=>'oacute',244=>'ocircumflex',245=>'ohungarumlaut',246=>'odieresis',247=>'divide',248=>'rcaron',249=>'uring',250=>'uacute',251=>'uhungarumlaut',252=>'udieresis',253=>'yacute',254=>'tcommaaccent',255=>'dotaccent'),

// encoding map for: cp1251
'cp1251' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'afii10051',129=>'afii10052',130=>'quotesinglbase',131=>'afii10100',132=>'quotedblbase',133=>'ellipsis',134=>'dagger',135=>'daggerdbl',136=>'Euro',137=>'perthousand',138=>'afii10058',139=>'guilsinglleft',140=>'afii10059',141=>'afii10061',142=>'afii10060',143=>'afii10145',144=>'afii10099',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'.notdef',153=>'trademark',154=>'afii10106',155=>'guilsinglright',156=>'afii10107',157=>'afii10109',158=>'afii10108',159=>'afii10193',160=>'space',161=>'afii10062',162=>'afii10110',163=>'afii10057',164=>'currency',165=>'afii10050',166=>'brokenbar',167=>'section',168=>'afii10023',169=>'copyright',170=>'afii10053',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'afii10056',176=>'degree',177=>'plusminus',178=>'afii10055',179=>'afii10103',180=>'afii10098',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'afii10071',185=>'afii61352',186=>'afii10101',187=>'guillemotright',188=>'afii10105',189=>'afii10054',190=>'afii10102',191=>'afii10104',192=>'afii10017',193=>'afii10018',194=>'afii10019',195=>'afii10020',196=>'afii10021',197=>'afii10022',198=>'afii10024',199=>'afii10025',200=>'afii10026',201=>'afii10027',202=>'afii10028',203=>'afii10029',204=>'afii10030',205=>'afii10031',206=>'afii10032',207=>'afii10033',208=>'afii10034',209=>'afii10035',210=>'afii10036',211=>'afii10037',212=>'afii10038',213=>'afii10039',214=>'afii10040',215=>'afii10041',216=>'afii10042',217=>'afii10043',218=>'afii10044',219=>'afii10045',220=>'afii10046',221=>'afii10047',222=>'afii10048',223=>'afii10049',224=>'afii10065',225=>'afii10066',226=>'afii10067',227=>'afii10068',228=>'afii10069',229=>'afii10070',230=>'afii10072',231=>'afii10073',232=>'afii10074',233=>'afii10075',234=>'afii10076',235=>'afii10077',236=>'afii10078',237=>'afii10079',238=>'afii10080',239=>'afii10081',240=>'afii10082',241=>'afii10083',242=>'afii10084',243=>'afii10085',244=>'afii10086',245=>'afii10087',246=>'afii10088',247=>'afii10089',248=>'afii10090',249=>'afii10091',250=>'afii10092',251=>'afii10093',252=>'afii10094',253=>'afii10095',254=>'afii10096',255=>'afii10097'),

// encoding map for: cp1252
'cp1252' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'Euro',129=>'.notdef',130=>'quotesinglbase',131=>'florin',132=>'quotedblbase',133=>'ellipsis',134=>'dagger',135=>'daggerdbl',136=>'circumflex',137=>'perthousand',138=>'Scaron',139=>'guilsinglleft',140=>'OE',141=>'.notdef',142=>'Zcaron',143=>'.notdef',144=>'.notdef',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'tilde',153=>'trademark',154=>'scaron',155=>'guilsinglright',156=>'oe',157=>'.notdef',158=>'zcaron',159=>'Ydieresis',160=>'space',161=>'exclamdown',162=>'cent',163=>'sterling',164=>'currency',165=>'yen',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'ordfeminine',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'macron',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'acute',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'cedilla',185=>'onesuperior',186=>'ordmasculine',187=>'guillemotright',188=>'onequarter',189=>'onehalf',190=>'threequarters',191=>'questiondown',192=>'Agrave',193=>'Aacute',194=>'Acircumflex',195=>'Atilde',196=>'Adieresis',197=>'Aring',198=>'AE',199=>'Ccedilla',200=>'Egrave',201=>'Eacute',202=>'Ecircumflex',203=>'Edieresis',204=>'Igrave',205=>'Iacute',206=>'Icircumflex',207=>'Idieresis',208=>'Eth',209=>'Ntilde',210=>'Ograve',211=>'Oacute',212=>'Ocircumflex',213=>'Otilde',214=>'Odieresis',215=>'multiply',216=>'Oslash',217=>'Ugrave',218=>'Uacute',219=>'Ucircumflex',220=>'Udieresis',221=>'Yacute',222=>'Thorn',223=>'germandbls',224=>'agrave',225=>'aacute',226=>'acircumflex',227=>'atilde',228=>'adieresis',229=>'aring',230=>'ae',231=>'ccedilla',232=>'egrave',233=>'eacute',234=>'ecircumflex',235=>'edieresis',236=>'igrave',237=>'iacute',238=>'icircumflex',239=>'idieresis',240=>'eth',241=>'ntilde',242=>'ograve',243=>'oacute',244=>'ocircumflex',245=>'otilde',246=>'odieresis',247=>'divide',248=>'oslash',249=>'ugrave',250=>'uacute',251=>'ucircumflex',252=>'udieresis',253=>'yacute',254=>'thorn',255=>'ydieresis'),

// encoding map for: cp1253
'cp1253' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'Euro',129=>'.notdef',130=>'quotesinglbase',131=>'florin',132=>'quotedblbase',133=>'ellipsis',134=>'dagger',135=>'daggerdbl',136=>'.notdef',137=>'perthousand',138=>'.notdef',139=>'guilsinglleft',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'.notdef',153=>'trademark',154=>'.notdef',155=>'guilsinglright',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'dieresistonos',162=>'Alphatonos',163=>'sterling',164=>'currency',165=>'yen',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'.notdef',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'afii00208',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'tonos',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'Epsilontonos',185=>'Etatonos',186=>'Iotatonos',187=>'guillemotright',188=>'Omicrontonos',189=>'onehalf',190=>'Upsilontonos',191=>'Omegatonos',192=>'iotadieresistonos',193=>'Alpha',194=>'Beta',195=>'Gamma',196=>'Delta',197=>'Epsilon',198=>'Zeta',199=>'Eta',200=>'Theta',201=>'Iota',202=>'Kappa',203=>'Lambda',204=>'Mu',205=>'Nu',206=>'Xi',207=>'Omicron',208=>'Pi',209=>'Rho',210=>'.notdef',211=>'Sigma',212=>'Tau',213=>'Upsilon',214=>'Phi',215=>'Chi',216=>'Psi',217=>'Omega',218=>'Iotadieresis',219=>'Upsilondieresis',220=>'alphatonos',221=>'epsilontonos',222=>'etatonos',223=>'iotatonos',224=>'upsilondieresistonos',225=>'alpha',226=>'beta',227=>'gamma',228=>'delta',229=>'epsilon',230=>'zeta',231=>'eta',232=>'theta',233=>'iota',234=>'kappa',235=>'lambda',236=>'mu',237=>'nu',238=>'xi',239=>'omicron',240=>'pi',241=>'rho',242=>'sigma1',243=>'sigma',244=>'tau',245=>'upsilon',246=>'phi',247=>'chi',248=>'psi',249=>'omega',250=>'iotadieresis',251=>'upsilondieresis',252=>'omicrontonos',253=>'upsilontonos',254=>'omegatonos',255=>'.notdef'),

// encoding map for: cp1254
'cp1254' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'Euro',129=>'.notdef',130=>'quotesinglbase',131=>'florin',132=>'quotedblbase',133=>'ellipsis',134=>'dagger',135=>'daggerdbl',136=>'circumflex',137=>'perthousand',138=>'Scaron',139=>'guilsinglleft',140=>'OE',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'tilde',153=>'trademark',154=>'scaron',155=>'guilsinglright',156=>'oe',157=>'.notdef',158=>'.notdef',159=>'Ydieresis',160=>'space',161=>'exclamdown',162=>'cent',163=>'sterling',164=>'currency',165=>'yen',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'ordfeminine',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'macron',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'acute',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'cedilla',185=>'onesuperior',186=>'ordmasculine',187=>'guillemotright',188=>'onequarter',189=>'onehalf',190=>'threequarters',191=>'questiondown',192=>'Agrave',193=>'Aacute',194=>'Acircumflex',195=>'Atilde',196=>'Adieresis',197=>'Aring',198=>'AE',199=>'Ccedilla',200=>'Egrave',201=>'Eacute',202=>'Ecircumflex',203=>'Edieresis',204=>'Igrave',205=>'Iacute',206=>'Icircumflex',207=>'Idieresis',208=>'Gbreve',209=>'Ntilde',210=>'Ograve',211=>'Oacute',212=>'Ocircumflex',213=>'Otilde',214=>'Odieresis',215=>'multiply',216=>'Oslash',217=>'Ugrave',218=>'Uacute',219=>'Ucircumflex',220=>'Udieresis',221=>'Idotaccent',222=>'Scedilla',223=>'germandbls',224=>'agrave',225=>'aacute',226=>'acircumflex',227=>'atilde',228=>'adieresis',229=>'aring',230=>'ae',231=>'ccedilla',232=>'egrave',233=>'eacute',234=>'ecircumflex',235=>'edieresis',236=>'igrave',237=>'iacute',238=>'icircumflex',239=>'idieresis',240=>'gbreve',241=>'ntilde',242=>'ograve',243=>'oacute',244=>'ocircumflex',245=>'otilde',246=>'odieresis',247=>'divide',248=>'oslash',249=>'ugrave',250=>'uacute',251=>'ucircumflex',252=>'udieresis',253=>'dotlessi',254=>'scedilla',255=>'ydieresis'),

// encoding map for: cp1255
'cp1255' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'Euro',129=>'.notdef',130=>'quotesinglbase',131=>'florin',132=>'quotedblbase',133=>'ellipsis',134=>'dagger',135=>'daggerdbl',136=>'circumflex',137=>'perthousand',138=>'.notdef',139=>'guilsinglleft',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'tilde',153=>'trademark',154=>'.notdef',155=>'guilsinglright',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'exclamdown',162=>'cent',163=>'sterling',164=>'afii57636',165=>'yen',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'multiply',171=>'guillemotleft',172=>'logicalnot',173=>'sfthyphen',174=>'registered',175=>'macron',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'acute',181=>'mu',182=>'paragraph',183=>'middot',184=>'cedilla',185=>'onesuperior',186=>'divide',187=>'guillemotright',188=>'onequarter',189=>'onehalf',190=>'threequarters',191=>'questiondown',192=>'afii57799',193=>'afii57801',194=>'afii57800',195=>'afii57802',196=>'afii57793',197=>'afii57794',198=>'afii57795',199=>'afii57798',200=>'afii57797',201=>'afii57806',202=>'.notdef',203=>'afii57796',204=>'afii57807',205=>'afii57839',206=>'afii57645',207=>'afii57841',208=>'afii57842',209=>'afii57804',210=>'afii57803',211=>'afii57658',212=>'afii57716',213=>'afii57717',214=>'afii57718',215=>'gereshhebrew',216=>'gershayimhebrew',217=>'.notdef',218=>'.notdef',219=>'.notdef',220=>'.notdef',221=>'.notdef',222=>'.notdef',223=>'.notdef',224=>'afii57664',225=>'afii57665',226=>'afii57666',227=>'afii57667',228=>'afii57668',229=>'afii57669',230=>'afii57670',231=>'afii57671',232=>'afii57672',233=>'afii57673',234=>'afii57674',235=>'afii57675',236=>'afii57676',237=>'afii57677',238=>'afii57678',239=>'afii57679',240=>'afii57680',241=>'afii57681',242=>'afii57682',243=>'afii57683',244=>'afii57684',245=>'afii57685',246=>'afii57686',247=>'afii57687',248=>'afii57688',249=>'afii57689',250=>'afii57690',251=>'.notdef',252=>'.notdef',253=>'afii299',254=>'afii300',255=>'.notdef'),

// encoding map for: cp1256
'cp1256' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'Euro',129=>'afii57506',130=>'quotesinglbase',131=>'florin',132=>'quotedblbase',133=>'ellipsis',134=>'dagger',135=>'daggerdbl',136=>'circumflex',137=>'perthousand',138=>'afii57511',139=>'guilsinglleft',140=>'OE',141=>'afii57507',142=>'afii57508',143=>'afii57512',144=>'afii57509',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'.notdef',153=>'trademark',154=>'afii57513',155=>'guilsinglright',156=>'oe',157=>'afii61664',158=>'afii301',159=>'afii57514',160=>'space',161=>'afii57388',162=>'cent',163=>'sterling',164=>'currency',165=>'yen',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'.notdef',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'macron',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'acute',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'cedilla',185=>'onesuperior',186=>'afii57403',187=>'guillemotright',188=>'onequarter',189=>'onehalf',190=>'threequarters',191=>'afii57407',192=>'.notdef',193=>'afii57409',194=>'afii57410',195=>'afii57411',196=>'afii57412',197=>'afii57413',198=>'afii57414',199=>'afii57415',200=>'afii57416',201=>'afii57417',202=>'afii57418',203=>'afii57419',204=>'afii57420',205=>'afii57421',206=>'afii57422',207=>'afii57423',208=>'afii57424',209=>'afii57425',210=>'afii57426',211=>'afii57427',212=>'afii57428',213=>'afii57429',214=>'afii57430',215=>'multiply',216=>'afii57431',217=>'afii57432',218=>'afii57433',219=>'afii57434',220=>'afii57440',221=>'afii57441',222=>'afii57442',223=>'afii57443',224=>'agrave',225=>'afii57444',226=>'acircumflex',227=>'afii57445',228=>'afii57446',229=>'afii57470',230=>'afii57448',231=>'ccedilla',232=>'egrave',233=>'eacute',234=>'ecircumflex',235=>'edieresis',236=>'afii57449',237=>'afii57450',238=>'icircumflex',239=>'idieresis',240=>'afii57451',241=>'afii57452',242=>'afii57453',243=>'afii57454',244=>'ocircumflex',245=>'afii57455',246=>'afii57456',247=>'divide',248=>'afii57457',249=>'ugrave',250=>'afii57458',251=>'ucircumflex',252=>'udieresis',253=>'afii299',254=>'afii300',255=>'afii57519'),

// encoding map for: cp1257
'cp1257' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'Euro',129=>'.notdef',130=>'quotesinglbase',131=>'.notdef',132=>'quotedblbase',133=>'ellipsis',134=>'dagger',135=>'daggerdbl',136=>'.notdef',137=>'perthousand',138=>'.notdef',139=>'guilsinglleft',140=>'.notdef',141=>'dieresis',142=>'caron',143=>'cedilla',144=>'.notdef',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'.notdef',153=>'trademark',154=>'.notdef',155=>'guilsinglright',156=>'.notdef',157=>'macron',158=>'ogonek',159=>'.notdef',160=>'space',161=>'.notdef',162=>'cent',163=>'sterling',164=>'currency',165=>'.notdef',166=>'brokenbar',167=>'section',168=>'Oslash',169=>'copyright',170=>'Rcommaaccent',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'AE',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'acute',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'oslash',185=>'onesuperior',186=>'rcommaaccent',187=>'guillemotright',188=>'onequarter',189=>'onehalf',190=>'threequarters',191=>'ae',192=>'Aogonek',193=>'Iogonek',194=>'Amacron',195=>'Cacute',196=>'Adieresis',197=>'Aring',198=>'Eogonek',199=>'Emacron',200=>'Ccaron',201=>'Eacute',202=>'Zacute',203=>'Edotaccent',204=>'Gcommaaccent',205=>'Kcommaaccent',206=>'Imacron',207=>'Lcommaaccent',208=>'Scaron',209=>'Nacute',210=>'Ncommaaccent',211=>'Oacute',212=>'Omacron',213=>'Otilde',214=>'Odieresis',215=>'multiply',216=>'Uogonek',217=>'Lslash',218=>'Sacute',219=>'Umacron',220=>'Udieresis',221=>'Zdotaccent',222=>'Zcaron',223=>'germandbls',224=>'aogonek',225=>'iogonek',226=>'amacron',227=>'cacute',228=>'adieresis',229=>'aring',230=>'eogonek',231=>'emacron',232=>'ccaron',233=>'eacute',234=>'zacute',235=>'edotaccent',236=>'gcommaaccent',237=>'kcommaaccent',238=>'imacron',239=>'lcommaaccent',240=>'scaron',241=>'nacute',242=>'ncommaaccent',243=>'oacute',244=>'omacron',245=>'otilde',246=>'odieresis',247=>'divide',248=>'uogonek',249=>'lslash',250=>'sacute',251=>'umacron',252=>'udieresis',253=>'zdotaccent',254=>'zcaron',255=>'dotaccent'),

// encoding map for: cp1258
'cp1258' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'Euro',129=>'.notdef',130=>'quotesinglbase',131=>'florin',132=>'quotedblbase',133=>'ellipsis',134=>'dagger',135=>'daggerdbl',136=>'circumflex',137=>'perthousand',138=>'.notdef',139=>'guilsinglleft',140=>'OE',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'quoteleft',146=>'quoteright',147=>'quotedblleft',148=>'quotedblright',149=>'bullet',150=>'endash',151=>'emdash',152=>'tilde',153=>'trademark',154=>'.notdef',155=>'guilsinglright',156=>'oe',157=>'.notdef',158=>'.notdef',159=>'Ydieresis',160=>'space',161=>'exclamdown',162=>'cent',163=>'sterling',164=>'currency',165=>'yen',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'ordfeminine',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'macron',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'acute',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'cedilla',185=>'onesuperior',186=>'ordmasculine',187=>'guillemotright',188=>'onequarter',189=>'onehalf',190=>'threequarters',191=>'questiondown',192=>'Agrave',193=>'Aacute',194=>'Acircumflex',195=>'Abreve',196=>'Adieresis',197=>'Aring',198=>'AE',199=>'Ccedilla',200=>'Egrave',201=>'Eacute',202=>'Ecircumflex',203=>'Edieresis',204=>'gravecomb',205=>'Iacute',206=>'Icircumflex',207=>'Idieresis',208=>'Dcroat',209=>'Ntilde',210=>'hookabovecomb',211=>'Oacute',212=>'Ocircumflex',213=>'Ohorn',214=>'Odieresis',215=>'multiply',216=>'Oslash',217=>'Ugrave',218=>'Uacute',219=>'Ucircumflex',220=>'Udieresis',221=>'Uhorn',222=>'tildecomb',223=>'germandbls',224=>'agrave',225=>'aacute',226=>'acircumflex',227=>'abreve',228=>'adieresis',229=>'aring',230=>'ae',231=>'ccedilla',232=>'egrave',233=>'eacute',234=>'ecircumflex',235=>'edieresis',236=>'acutecomb',237=>'iacute',238=>'icircumflex',239=>'idieresis',240=>'dcroat',241=>'ntilde',242=>'dotbelowcomb',243=>'oacute',244=>'ocircumflex',245=>'ohorn',246=>'odieresis',247=>'divide',248=>'oslash',249=>'ugrave',250=>'uacute',251=>'ucircumflex',252=>'udieresis',253=>'uhorn',254=>'dong',255=>'ydieresis'),

// encoding map for: iso-8859-1
'iso-8859-1' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'exclamdown',162=>'cent',163=>'sterling',164=>'currency',165=>'yen',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'ordfeminine',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'macron',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'acute',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'cedilla',185=>'onesuperior',186=>'ordmasculine',187=>'guillemotright',188=>'onequarter',189=>'onehalf',190=>'threequarters',191=>'questiondown',192=>'Agrave',193=>'Aacute',194=>'Acircumflex',195=>'Atilde',196=>'Adieresis',197=>'Aring',198=>'AE',199=>'Ccedilla',200=>'Egrave',201=>'Eacute',202=>'Ecircumflex',203=>'Edieresis',204=>'Igrave',205=>'Iacute',206=>'Icircumflex',207=>'Idieresis',208=>'Eth',209=>'Ntilde',210=>'Ograve',211=>'Oacute',212=>'Ocircumflex',213=>'Otilde',214=>'Odieresis',215=>'multiply',216=>'Oslash',217=>'Ugrave',218=>'Uacute',219=>'Ucircumflex',220=>'Udieresis',221=>'Yacute',222=>'Thorn',223=>'germandbls',224=>'agrave',225=>'aacute',226=>'acircumflex',227=>'atilde',228=>'adieresis',229=>'aring',230=>'ae',231=>'ccedilla',232=>'egrave',233=>'eacute',234=>'ecircumflex',235=>'edieresis',236=>'igrave',237=>'iacute',238=>'icircumflex',239=>'idieresis',240=>'eth',241=>'ntilde',242=>'ograve',243=>'oacute',244=>'ocircumflex',245=>'otilde',246=>'odieresis',247=>'divide',248=>'oslash',249=>'ugrave',250=>'uacute',251=>'ucircumflex',252=>'udieresis',253=>'yacute',254=>'thorn',255=>'ydieresis'),

// encoding map for: iso-8859-2
'iso-8859-2' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'Aogonek',162=>'breve',163=>'Lslash',164=>'currency',165=>'Lcaron',166=>'Sacute',167=>'section',168=>'dieresis',169=>'Scaron',170=>'Scedilla',171=>'Tcaron',172=>'Zacute',173=>'hyphen',174=>'Zcaron',175=>'Zdotaccent',176=>'degree',177=>'aogonek',178=>'ogonek',179=>'lslash',180=>'acute',181=>'lcaron',182=>'sacute',183=>'caron',184=>'cedilla',185=>'scaron',186=>'scedilla',187=>'tcaron',188=>'zacute',189=>'hungarumlaut',190=>'zcaron',191=>'zdotaccent',192=>'Racute',193=>'Aacute',194=>'Acircumflex',195=>'Abreve',196=>'Adieresis',197=>'Lacute',198=>'Cacute',199=>'Ccedilla',200=>'Ccaron',201=>'Eacute',202=>'Eogonek',203=>'Edieresis',204=>'Ecaron',205=>'Iacute',206=>'Icircumflex',207=>'Dcaron',208=>'Dcroat',209=>'Nacute',210=>'Ncaron',211=>'Oacute',212=>'Ocircumflex',213=>'Ohungarumlaut',214=>'Odieresis',215=>'multiply',216=>'Rcaron',217=>'Uring',218=>'Uacute',219=>'Uhungarumlaut',220=>'Udieresis',221=>'Yacute',222=>'Tcommaaccent',223=>'germandbls',224=>'racute',225=>'aacute',226=>'acircumflex',227=>'abreve',228=>'adieresis',229=>'lacute',230=>'cacute',231=>'ccedilla',232=>'ccaron',233=>'eacute',234=>'eogonek',235=>'edieresis',236=>'ecaron',237=>'iacute',238=>'icircumflex',239=>'dcaron',240=>'dcroat',241=>'nacute',242=>'ncaron',243=>'oacute',244=>'ocircumflex',245=>'ohungarumlaut',246=>'odieresis',247=>'divide',248=>'rcaron',249=>'uring',250=>'uacute',251=>'uhungarumlaut',252=>'udieresis',253=>'yacute',254=>'tcommaaccent',255=>'dotaccent'),

// encoding map for: iso-8859-4
'iso-8859-4' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'Aogonek',162=>'kgreenlandic',163=>'Rcommaaccent',164=>'currency',165=>'Itilde',166=>'Lcommaaccent',167=>'section',168=>'dieresis',169=>'Scaron',170=>'Emacron',171=>'Gcommaaccent',172=>'Tbar',173=>'hyphen',174=>'Zcaron',175=>'macron',176=>'degree',177=>'aogonek',178=>'ogonek',179=>'rcommaaccent',180=>'acute',181=>'itilde',182=>'lcommaaccent',183=>'caron',184=>'cedilla',185=>'scaron',186=>'emacron',187=>'gcommaaccent',188=>'tbar',189=>'Eng',190=>'zcaron',191=>'eng',192=>'Amacron',193=>'Aacute',194=>'Acircumflex',195=>'Atilde',196=>'Adieresis',197=>'Aring',198=>'AE',199=>'Iogonek',200=>'Ccaron',201=>'Eacute',202=>'Eogonek',203=>'Edieresis',204=>'Edotaccent',205=>'Iacute',206=>'Icircumflex',207=>'Imacron',208=>'Dcroat',209=>'Ncommaaccent',210=>'Omacron',211=>'Kcommaaccent',212=>'Ocircumflex',213=>'Otilde',214=>'Odieresis',215=>'multiply',216=>'Oslash',217=>'Uogonek',218=>'Uacute',219=>'Ucircumflex',220=>'Udieresis',221=>'Utilde',222=>'Umacron',223=>'germandbls',224=>'amacron',225=>'aacute',226=>'acircumflex',227=>'atilde',228=>'adieresis',229=>'aring',230=>'ae',231=>'iogonek',232=>'ccaron',233=>'eacute',234=>'eogonek',235=>'edieresis',236=>'edotaccent',237=>'iacute',238=>'icircumflex',239=>'imacron',240=>'dcroat',241=>'ncommaaccent',242=>'omacron',243=>'kcommaaccent',244=>'ocircumflex',245=>'otilde',246=>'odieresis',247=>'divide',248=>'oslash',249=>'uogonek',250=>'uacute',251=>'ucircumflex',252=>'udieresis',253=>'utilde',254=>'umacron',255=>'dotaccent'),

// encoding map for: iso-8859-5
'iso-8859-5' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'afii10023',162=>'afii10051',163=>'afii10052',164=>'afii10053',165=>'afii10054',166=>'afii10055',167=>'afii10056',168=>'afii10057',169=>'afii10058',170=>'afii10059',171=>'afii10060',172=>'afii10061',173=>'hyphen',174=>'afii10062',175=>'afii10145',176=>'afii10017',177=>'afii10018',178=>'afii10019',179=>'afii10020',180=>'afii10021',181=>'afii10022',182=>'afii10024',183=>'afii10025',184=>'afii10026',185=>'afii10027',186=>'afii10028',187=>'afii10029',188=>'afii10030',189=>'afii10031',190=>'afii10032',191=>'afii10033',192=>'afii10034',193=>'afii10035',194=>'afii10036',195=>'afii10037',196=>'afii10038',197=>'afii10039',198=>'afii10040',199=>'afii10041',200=>'afii10042',201=>'afii10043',202=>'afii10044',203=>'afii10045',204=>'afii10046',205=>'afii10047',206=>'afii10048',207=>'afii10049',208=>'afii10065',209=>'afii10066',210=>'afii10067',211=>'afii10068',212=>'afii10069',213=>'afii10070',214=>'afii10072',215=>'afii10073',216=>'afii10074',217=>'afii10075',218=>'afii10076',219=>'afii10077',220=>'afii10078',221=>'afii10079',222=>'afii10080',223=>'afii10081',224=>'afii10082',225=>'afii10083',226=>'afii10084',227=>'afii10085',228=>'afii10086',229=>'afii10087',230=>'afii10088',231=>'afii10089',232=>'afii10090',233=>'afii10091',234=>'afii10092',235=>'afii10093',236=>'afii10094',237=>'afii10095',238=>'afii10096',239=>'afii10097',240=>'afii61352',241=>'afii10071',242=>'afii10099',243=>'afii10100',244=>'afii10101',245=>'afii10102',246=>'afii10103',247=>'afii10104',248=>'afii10105',249=>'afii10106',250=>'afii10107',251=>'afii10108',252=>'afii10109',253=>'section',254=>'afii10110',255=>'afii10193'),

// encoding map for: iso-8859-7
'iso-8859-7' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'quoteleft',162=>'quoteright',163=>'sterling',164=>'.notdef',165=>'.notdef',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'.notdef',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'.notdef',175=>'afii00208',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'tonos',181=>'dieresistonos',182=>'Alphatonos',183=>'periodcentered',184=>'Epsilontonos',185=>'Etatonos',186=>'Iotatonos',187=>'guillemotright',188=>'Omicrontonos',189=>'onehalf',190=>'Upsilontonos',191=>'Omegatonos',192=>'iotadieresistonos',193=>'Alpha',194=>'Beta',195=>'Gamma',196=>'Delta',197=>'Epsilon',198=>'Zeta',199=>'Eta',200=>'Theta',201=>'Iota',202=>'Kappa',203=>'Lambda',204=>'Mu',205=>'Nu',206=>'Xi',207=>'Omicron',208=>'Pi',209=>'Rho',210=>'.notdef',211=>'Sigma',212=>'Tau',213=>'Upsilon',214=>'Phi',215=>'Chi',216=>'Psi',217=>'Omega',218=>'Iotadieresis',219=>'Upsilondieresis',220=>'alphatonos',221=>'epsilontonos',222=>'etatonos',223=>'iotatonos',224=>'upsilondieresistonos',225=>'alpha',226=>'beta',227=>'gamma',228=>'delta',229=>'epsilon',230=>'zeta',231=>'eta',232=>'theta',233=>'iota',234=>'kappa',235=>'lambda',236=>'mu',237=>'nu',238=>'xi',239=>'omicron',240=>'pi',241=>'rho',242=>'sigma1',243=>'sigma',244=>'tau',245=>'upsilon',246=>'phi',247=>'chi',248=>'psi',249=>'omega',250=>'iotadieresis',251=>'upsilondieresis',252=>'omicrontonos',253=>'upsilontonos',254=>'omegatonos',255=>'.notdef'),

// encoding map for: iso-8859-9
'iso-8859-9' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'exclamdown',162=>'cent',163=>'sterling',164=>'currency',165=>'yen',166=>'brokenbar',167=>'section',168=>'dieresis',169=>'copyright',170=>'ordfeminine',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'macron',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'acute',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'cedilla',185=>'onesuperior',186=>'ordmasculine',187=>'guillemotright',188=>'onequarter',189=>'onehalf',190=>'threequarters',191=>'questiondown',192=>'Agrave',193=>'Aacute',194=>'Acircumflex',195=>'Atilde',196=>'Adieresis',197=>'Aring',198=>'AE',199=>'Ccedilla',200=>'Egrave',201=>'Eacute',202=>'Ecircumflex',203=>'Edieresis',204=>'Igrave',205=>'Iacute',206=>'Icircumflex',207=>'Idieresis',208=>'Gbreve',209=>'Ntilde',210=>'Ograve',211=>'Oacute',212=>'Ocircumflex',213=>'Otilde',214=>'Odieresis',215=>'multiply',216=>'Oslash',217=>'Ugrave',218=>'Uacute',219=>'Ucircumflex',220=>'Udieresis',221=>'Idotaccent',222=>'Scedilla',223=>'germandbls',224=>'agrave',225=>'aacute',226=>'acircumflex',227=>'atilde',228=>'adieresis',229=>'aring',230=>'ae',231=>'ccedilla',232=>'egrave',233=>'eacute',234=>'ecircumflex',235=>'edieresis',236=>'igrave',237=>'iacute',238=>'icircumflex',239=>'idieresis',240=>'gbreve',241=>'ntilde',242=>'ograve',243=>'oacute',244=>'ocircumflex',245=>'otilde',246=>'odieresis',247=>'divide',248=>'oslash',249=>'ugrave',250=>'uacute',251=>'ucircumflex',252=>'udieresis',253=>'dotlessi',254=>'scedilla',255=>'ydieresis'),

// encoding map for: iso-8859-11
'iso-8859-11' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'kokaithai',162=>'khokhaithai',163=>'khokhuatthai',164=>'khokhwaithai',165=>'khokhonthai',166=>'khorakhangthai',167=>'ngonguthai',168=>'chochanthai',169=>'chochingthai',170=>'chochangthai',171=>'sosothai',172=>'chochoethai',173=>'yoyingthai',174=>'dochadathai',175=>'topatakthai',176=>'thothanthai',177=>'thonangmonthothai',178=>'thophuthaothai',179=>'nonenthai',180=>'dodekthai',181=>'totaothai',182=>'thothungthai',183=>'thothahanthai',184=>'thothongthai',185=>'nonuthai',186=>'bobaimaithai',187=>'poplathai',188=>'phophungthai',189=>'fofathai',190=>'phophanthai',191=>'fofanthai',192=>'phosamphaothai',193=>'momathai',194=>'yoyakthai',195=>'roruathai',196=>'ruthai',197=>'lolingthai',198=>'luthai',199=>'wowaenthai',200=>'sosalathai',201=>'sorusithai',202=>'sosuathai',203=>'hohipthai',204=>'lochulathai',205=>'oangthai',206=>'honokhukthai',207=>'paiyannoithai',208=>'saraathai',209=>'maihanakatthai',210=>'saraaathai',211=>'saraamthai',212=>'saraithai',213=>'saraiithai',214=>'sarauethai',215=>'saraueethai',216=>'sarauthai',217=>'sarauuthai',218=>'phinthuthai',219=>'.notdef',220=>'.notdef',221=>'.notdef',222=>'.notdef',223=>'bahtthai',224=>'saraethai',225=>'saraaethai',226=>'saraothai',227=>'saraaimaimuanthai',228=>'saraaimaimalaithai',229=>'lakkhangyaothai',230=>'maiyamokthai',231=>'maitaikhuthai',232=>'maiekthai',233=>'maithothai',234=>'maitrithai',235=>'maichattawathai',236=>'thanthakhatthai',237=>'nikhahitthai',238=>'yamakkanthai',239=>'fongmanthai',240=>'zerothai',241=>'onethai',242=>'twothai',243=>'threethai',244=>'fourthai',245=>'fivethai',246=>'sixthai',247=>'seventhai',248=>'eightthai',249=>'ninethai',250=>'angkhankhuthai',251=>'khomutthai',252=>'.notdef',253=>'.notdef',254=>'.notdef',255=>'.notdef'),

// encoding map for: iso-8859-15
'iso-8859-15' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'exclamdown',162=>'cent',163=>'sterling',164=>'Euro',165=>'yen',166=>'Scaron',167=>'section',168=>'scaron',169=>'copyright',170=>'ordfeminine',171=>'guillemotleft',172=>'logicalnot',173=>'hyphen',174=>'registered',175=>'macron',176=>'degree',177=>'plusminus',178=>'twosuperior',179=>'threesuperior',180=>'Zcaron',181=>'mu',182=>'paragraph',183=>'periodcentered',184=>'zcaron',185=>'onesuperior',186=>'ordmasculine',187=>'guillemotright',188=>'OE',189=>'oe',190=>'Ydieresis',191=>'questiondown',192=>'Agrave',193=>'Aacute',194=>'Acircumflex',195=>'Atilde',196=>'Adieresis',197=>'Aring',198=>'AE',199=>'Ccedilla',200=>'Egrave',201=>'Eacute',202=>'Ecircumflex',203=>'Edieresis',204=>'Igrave',205=>'Iacute',206=>'Icircumflex',207=>'Idieresis',208=>'Eth',209=>'Ntilde',210=>'Ograve',211=>'Oacute',212=>'Ocircumflex',213=>'Otilde',214=>'Odieresis',215=>'multiply',216=>'Oslash',217=>'Ugrave',218=>'Uacute',219=>'Ucircumflex',220=>'Udieresis',221=>'Yacute',222=>'Thorn',223=>'germandbls',224=>'agrave',225=>'aacute',226=>'acircumflex',227=>'atilde',228=>'adieresis',229=>'aring',230=>'ae',231=>'ccedilla',232=>'egrave',233=>'eacute',234=>'ecircumflex',235=>'edieresis',236=>'igrave',237=>'iacute',238=>'icircumflex',239=>'idieresis',240=>'eth',241=>'ntilde',242=>'ograve',243=>'oacute',244=>'ocircumflex',245=>'otilde',246=>'odieresis',247=>'divide',248=>'oslash',249=>'ugrave',250=>'uacute',251=>'ucircumflex',252=>'udieresis',253=>'yacute',254=>'thorn',255=>'ydieresis'),

// encoding map for: iso-8859-16
'iso-8859-16' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'space',161=>'Aogonek',162=>'aogonek',163=>'Lslash',164=>'Euro',165=>'quotedblbase',166=>'Scaron',167=>'section',168=>'scaron',169=>'copyright',170=>'Scommaaccent',171=>'guillemotleft',172=>'Zacute',173=>'hyphen',174=>'zacute',175=>'Zdotaccent',176=>'degree',177=>'plusminus',178=>'Ccaron',179=>'lslash',180=>'Zcaron',181=>'quotedblright',182=>'paragraph',183=>'periodcentered',184=>'zcaron',185=>'ccaron',186=>'scommaaccent',187=>'guillemotright',188=>'OE',189=>'oe',190=>'Ydieresis',191=>'zdotaccent',192=>'Agrave',193=>'Aacute',194=>'Acircumflex',195=>'Abreve',196=>'Adieresis',197=>'Cacute',198=>'AE',199=>'Ccedilla',200=>'Egrave',201=>'Eacute',202=>'Ecircumflex',203=>'Edieresis',204=>'Igrave',205=>'Iacute',206=>'Icircumflex',207=>'Idieresis',208=>'Dcroat',209=>'Nacute',210=>'Ograve',211=>'Oacute',212=>'Ocircumflex',213=>'Ohungarumlaut',214=>'Odieresis',215=>'Sacute',216=>'Uhungarumlaut',217=>'Ugrave',218=>'Uacute',219=>'Ucircumflex',220=>'Udieresis',221=>'Eogonek',222=>'Tcommaaccent',223=>'germandbls',224=>'agrave',225=>'aacute',226=>'acircumflex',227=>'abreve',228=>'adieresis',229=>'cacute',230=>'ae',231=>'ccedilla',232=>'egrave',233=>'eacute',234=>'ecircumflex',235=>'edieresis',236=>'igrave',237=>'iacute',238=>'icircumflex',239=>'idieresis',240=>'dcroat',241=>'nacute',242=>'ograve',243=>'oacute',244=>'ocircumflex',245=>'ohungarumlaut',246=>'odieresis',247=>'sacute',248=>'uhungarumlaut',249=>'ugrave',250=>'uacute',251=>'ucircumflex',252=>'udieresis',253=>'eogonek',254=>'tcommaaccent',255=>'ydieresis'),

// encoding map for: koi8-r
'koi8-r' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'SF100000',129=>'SF110000',130=>'SF010000',131=>'SF030000',132=>'SF020000',133=>'SF040000',134=>'SF080000',135=>'SF090000',136=>'SF060000',137=>'SF070000',138=>'SF050000',139=>'upblock',140=>'dnblock',141=>'block',142=>'lfblock',143=>'rtblock',144=>'ltshade',145=>'shade',146=>'dkshade',147=>'integraltp',148=>'filledbox',149=>'periodcentered',150=>'radical',151=>'approxequal',152=>'lessequal',153=>'greaterequal',154=>'space',155=>'integralbt',156=>'degree',157=>'twosuperior',158=>'periodcentered',159=>'divide',160=>'SF430000',161=>'SF240000',162=>'SF510000',163=>'afii10071',164=>'SF520000',165=>'SF390000',166=>'SF220000',167=>'SF210000',168=>'SF250000',169=>'SF500000',170=>'SF490000',171=>'SF380000',172=>'SF280000',173=>'SF270000',174=>'SF260000',175=>'SF360000',176=>'SF370000',177=>'SF420000',178=>'SF190000',179=>'afii10023',180=>'SF200000',181=>'SF230000',182=>'SF470000',183=>'SF480000',184=>'SF410000',185=>'SF450000',186=>'SF460000',187=>'SF400000',188=>'SF540000',189=>'SF530000',190=>'SF440000',191=>'copyright',192=>'afii10096',193=>'afii10065',194=>'afii10066',195=>'afii10088',196=>'afii10069',197=>'afii10070',198=>'afii10086',199=>'afii10068',200=>'afii10087',201=>'afii10074',202=>'afii10075',203=>'afii10076',204=>'afii10077',205=>'afii10078',206=>'afii10079',207=>'afii10080',208=>'afii10081',209=>'afii10097',210=>'afii10082',211=>'afii10083',212=>'afii10084',213=>'afii10085',214=>'afii10072',215=>'afii10067',216=>'afii10094',217=>'afii10093',218=>'afii10073',219=>'afii10090',220=>'afii10095',221=>'afii10091',222=>'afii10089',223=>'afii10092',224=>'afii10048',225=>'afii10017',226=>'afii10018',227=>'afii10040',228=>'afii10021',229=>'afii10022',230=>'afii10038',231=>'afii10020',232=>'afii10039',233=>'afii10026',234=>'afii10027',235=>'afii10028',236=>'afii10029',237=>'afii10030',238=>'afii10031',239=>'afii10032',240=>'afii10033',241=>'afii10049',242=>'afii10034',243=>'afii10035',244=>'afii10036',245=>'afii10037',246=>'afii10024',247=>'afii10019',248=>'afii10046',249=>'afii10045',250=>'afii10025',251=>'afii10042',252=>'afii10047',253=>'afii10043',254=>'afii10041',255=>'afii10044'),

// encoding map for: koi8-u
'koi8-u' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'quotedbl',35=>'numbersign',36=>'dollar',37=>'percent',38=>'ampersand',39=>'quotesingle',40=>'parenleft',41=>'parenright',42=>'asterisk',43=>'plus',44=>'comma',45=>'hyphen',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'at',65=>'A',66=>'B',67=>'C',68=>'D',69=>'E',70=>'F',71=>'G',72=>'H',73=>'I',74=>'J',75=>'K',76=>'L',77=>'M',78=>'N',79=>'O',80=>'P',81=>'Q',82=>'R',83=>'S',84=>'T',85=>'U',86=>'V',87=>'W',88=>'X',89=>'Y',90=>'Z',91=>'bracketleft',92=>'backslash',93=>'bracketright',94=>'asciicircum',95=>'underscore',96=>'grave',97=>'a',98=>'b',99=>'c',100=>'d',101=>'e',102=>'f',103=>'g',104=>'h',105=>'i',106=>'j',107=>'k',108=>'l',109=>'m',110=>'n',111=>'o',112=>'p',113=>'q',114=>'r',115=>'s',116=>'t',117=>'u',118=>'v',119=>'w',120=>'x',121=>'y',122=>'z',123=>'braceleft',124=>'bar',125=>'braceright',126=>'asciitilde',127=>'.notdef',128=>'SF100000',129=>'SF110000',130=>'SF010000',131=>'SF030000',132=>'SF020000',133=>'SF040000',134=>'SF080000',135=>'SF090000',136=>'SF060000',137=>'SF070000',138=>'SF050000',139=>'upblock',140=>'dnblock',141=>'block',142=>'lfblock',143=>'rtblock',144=>'ltshade',145=>'shade',146=>'dkshade',147=>'integraltp',148=>'filledbox',149=>'bullet',150=>'radical',151=>'approxequal',152=>'lessequal',153=>'greaterequal',154=>'space',155=>'integralbt',156=>'degree',157=>'twosuperior',158=>'periodcentered',159=>'divide',160=>'SF430000',161=>'SF240000',162=>'SF510000',163=>'afii10071',164=>'afii10101',165=>'SF390000',166=>'afii10103',167=>'afii10104',168=>'SF250000',169=>'SF500000',170=>'SF490000',171=>'SF380000',172=>'SF280000',173=>'afii10098',174=>'SF260000',175=>'SF360000',176=>'SF370000',177=>'SF420000',178=>'SF190000',179=>'afii10023',180=>'afii10053',181=>'SF230000',182=>'afii10055',183=>'afii10056',184=>'SF410000',185=>'SF450000',186=>'SF460000',187=>'SF400000',188=>'SF540000',189=>'afii10050',190=>'SF440000',191=>'copyright',192=>'afii10096',193=>'afii10065',194=>'afii10066',195=>'afii10088',196=>'afii10069',197=>'afii10070',198=>'afii10086',199=>'afii10068',200=>'afii10087',201=>'afii10074',202=>'afii10075',203=>'afii10076',204=>'afii10077',205=>'afii10078',206=>'afii10079',207=>'afii10080',208=>'afii10081',209=>'afii10097',210=>'afii10082',211=>'afii10083',212=>'afii10084',213=>'afii10085',214=>'afii10072',215=>'afii10067',216=>'afii10094',217=>'afii10093',218=>'afii10073',219=>'afii10090',220=>'afii10095',221=>'afii10091',222=>'afii10089',223=>'afii10092',224=>'afii10048',225=>'afii10017',226=>'afii10018',227=>'afii10040',228=>'afii10021',229=>'afii10022',230=>'afii10038',231=>'afii10020',232=>'afii10039',233=>'afii10026',234=>'afii10027',235=>'afii10028',236=>'afii10029',237=>'afii10030',238=>'afii10031',239=>'afii10032',240=>'afii10033',241=>'afii10049',242=>'afii10034',243=>'afii10035',244=>'afii10036',245=>'afii10037',246=>'afii10024',247=>'afii10019',248=>'afii10046',249=>'afii10045',250=>'afii10025',251=>'afii10042',252=>'afii10047',253=>'afii10043',254=>'afii10041',255=>'afii10044'),

// encoding map for: symbol
'symbol' => array(0=>'.notdef',1=>'.notdef',2=>'.notdef',3=>'.notdef',4=>'.notdef',5=>'.notdef',6=>'.notdef',7=>'.notdef',8=>'.notdef',9=>'.notdef',10=>'.notdef',11=>'.notdef',12=>'.notdef',13=>'.notdef',14=>'.notdef',15=>'.notdef',16=>'.notdef',17=>'.notdef',18=>'.notdef',19=>'.notdef',20=>'.notdef',21=>'.notdef',22=>'.notdef',23=>'.notdef',24=>'.notdef',25=>'.notdef',26=>'.notdef',27=>'.notdef',28=>'.notdef',29=>'.notdef',30=>'.notdef',31=>'.notdef',32=>'space',33=>'exclam',34=>'universal',35=>'numbersign',36=>'existential',37=>'percent',38=>'ampersand',39=>'suchthat',40=>'parenleft',41=>'parenright',42=>'asteriskmath',43=>'plus',44=>'comma',45=>'minus',46=>'period',47=>'slash',48=>'zero',49=>'one',50=>'two',51=>'three',52=>'four',53=>'five',54=>'six',55=>'seven',56=>'eight',57=>'nine',58=>'colon',59=>'semicolon',60=>'less',61=>'equal',62=>'greater',63=>'question',64=>'congruent',65=>'Alpha',66=>'Beta',67=>'Chi',68=>'Delta',69=>'Epsilon',70=>'Phi',71=>'Gamma',72=>'Eta',73=>'Iota',74=>'theta1',75=>'Kappa',76=>'Lambda',77=>'Mu',78=>'Nu',79=>'Omicron',80=>'Pi',81=>'Theta',82=>'Rho',83=>'Sigma',84=>'Tau',85=>'Upsilon',86=>'sigma1',87=>'Omega',88=>'Xi',89=>'Psi',90=>'Zeta',91=>'bracketleft',92=>'therefore',93=>'bracketright',94=>'perpendicular',95=>'underscore',96=>'radicalex',97=>'alpha',98=>'beta',99=>'chi',100=>'delta',101=>'epsilon',102=>'phi',103=>'gamma',104=>'eta',105=>'iota',106=>'phi1',107=>'kappa',108=>'lambda',109=>'mu',110=>'nu',111=>'omicron',112=>'pi',113=>'theta',114=>'rho',115=>'sigma',116=>'tau',117=>'upsilon',118=>'omega1',119=>'omega',120=>'xi',121=>'psi',122=>'zeta',123=>'braceleft',124=>'bar',125=>'braceright',126=>'similar',127=>'.notdef',128=>'.notdef',129=>'.notdef',130=>'.notdef',131=>'.notdef',132=>'.notdef',133=>'.notdef',134=>'.notdef',135=>'.notdef',136=>'.notdef',137=>'.notdef',138=>'.notdef',139=>'.notdef',140=>'.notdef',141=>'.notdef',142=>'.notdef',143=>'.notdef',144=>'.notdef',145=>'.notdef',146=>'.notdef',147=>'.notdef',148=>'.notdef',149=>'.notdef',150=>'.notdef',151=>'.notdef',152=>'.notdef',153=>'.notdef',154=>'.notdef',155=>'.notdef',156=>'.notdef',157=>'.notdef',158=>'.notdef',159=>'.notdef',160=>'Euro',161=>'Upsilon1',162=>'minute',163=>'lessequal',164=>'fraction',165=>'infinity',166=>'florin',167=>'club',168=>'diamond',169=>'heart',170=>'spade',171=>'arrowboth',172=>'arrowleft',173=>'arrowup',174=>'arrowright',175=>'arrowdown',176=>'degree',177=>'plusminus',178=>'second',179=>'greaterequal',180=>'multiply',181=>'proportional',182=>'partialdiff',183=>'bullet',184=>'divide',185=>'notequal',186=>'equivalence',187=>'approxequal',188=>'ellipsis',189=>'arrowvertex',190=>'arrowhorizex',191=>'carriagereturn',192=>'aleph',193=>'Ifraktur',194=>'Rfraktur',195=>'weierstrass',196=>'circlemultiply',197=>'circleplus',198=>'emptyset',199=>'intersection',200=>'union',201=>'propersuperset',202=>'reflexsuperset',203=>'notsubset',204=>'propersubset',205=>'reflexsubset',206=>'element',207=>'notelement',208=>'angle',209=>'gradient',210=>'registerserif',211=>'copyrightserif',212=>'trademarkserif',213=>'product',214=>'radical',215=>'dotmath',216=>'logicalnot',217=>'logicaland',218=>'logicalor',219=>'arrowdblboth',220=>'arrowdblleft',221=>'arrowdblup',222=>'arrowdblright',223=>'arrowdbldown',224=>'lozenge',225=>'angleleft',226=>'registersans',227=>'copyrightsans',228=>'trademarksans',229=>'summation',230=>'parenlefttp',231=>'parenleftex',232=>'parenleftbt',233=>'bracketlefttp',234=>'bracketleftex',235=>'bracketleftbt',236=>'bracelefttp',237=>'braceleftmid',238=>'braceleftbt',239=>'braceex',240=>'.notdef',241=>'angleright',242=>'integral',243=>'integraltp',244=>'integralex',245=>'integralbt',246=>'parenrighttp',247=>'parenrightex',248=>'parenrightbt',249=>'bracketrighttp',250=>'bracketrightex',251=>'bracketrightbt',252=>'bracerighttp',253=>'bracerightmid',254=>'bracerightbt',255=>'.notdef',1226=>'registered',1227=>'copyright',1228=>'trademark')

); // end of encoding maps

/**
 * ToUnicode map for Identity-H stream
 * @public static
 */
public static $uni_identity_h = "/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo << /Registry (Adobe) /Ordering (UCS) /Supplement 0 >> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n/WMode 0 def\n1 begincodespacerange\n<0000> <FFFF>\nendcodespacerange\n100 beginbfrange\n<0000> <00ff> <0000>\n<0100> <01ff> <0100>\n<0200> <02ff> <0200>\n<0300> <03ff> <0300>\n<0400> <04ff> <0400>\n<0500> <05ff> <0500>\n<0600> <06ff> <0600>\n<0700> <07ff> <0700>\n<0800> <08ff> <0800>\n<0900> <09ff> <0900>\n<0a00> <0aff> <0a00>\n<0b00> <0bff> <0b00>\n<0c00> <0cff> <0c00>\n<0d00> <0dff> <0d00>\n<0e00> <0eff> <0e00>\n<0f00> <0fff> <0f00>\n<1000> <10ff> <1000>\n<1100> <11ff> <1100>\n<1200> <12ff> <1200>\n<1300> <13ff> <1300>\n<1400> <14ff> <1400>\n<1500> <15ff> <1500>\n<1600> <16ff> <1600>\n<1700> <17ff> <1700>\n<1800> <18ff> <1800>\n<1900> <19ff> <1900>\n<1a00> <1aff> <1a00>\n<1b00> <1bff> <1b00>\n<1c00> <1cff> <1c00>\n<1d00> <1dff> <1d00>\n<1e00> <1eff> <1e00>\n<1f00> <1fff> <1f00>\n<2000> <20ff> <2000>\n<2100> <21ff> <2100>\n<2200> <22ff> <2200>\n<2300> <23ff> <2300>\n<2400> <24ff> <2400>\n<2500> <25ff> <2500>\n<2600> <26ff> <2600>\n<2700> <27ff> <2700>\n<2800> <28ff> <2800>\n<2900> <29ff> <2900>\n<2a00> <2aff> <2a00>\n<2b00> <2bff> <2b00>\n<2c00> <2cff> <2c00>\n<2d00> <2dff> <2d00>\n<2e00> <2eff> <2e00>\n<2f00> <2fff> <2f00>\n<3000> <30ff> <3000>\n<3100> <31ff> <3100>\n<3200> <32ff> <3200>\n<3300> <33ff> <3300>\n<3400> <34ff> <3400>\n<3500> <35ff> <3500>\n<3600> <36ff> <3600>\n<3700> <37ff> <3700>\n<3800> <38ff> <3800>\n<3900> <39ff> <3900>\n<3a00> <3aff> <3a00>\n<3b00> <3bff> <3b00>\n<3c00> <3cff> <3c00>\n<3d00> <3dff> <3d00>\n<3e00> <3eff> <3e00>\n<3f00> <3fff> <3f00>\n<4000> <40ff> <4000>\n<4100> <41ff> <4100>\n<4200> <42ff> <4200>\n<4300> <43ff> <4300>\n<4400> <44ff> <4400>\n<4500> <45ff> <4500>\n<4600> <46ff> <4600>\n<4700> <47ff> <4700>\n<4800> <48ff> <4800>\n<4900> <49ff> <4900>\n<4a00> <4aff> <4a00>\n<4b00> <4bff> <4b00>\n<4c00> <4cff> <4c00>\n<4d00> <4dff> <4d00>\n<4e00> <4eff> <4e00>\n<4f00> <4fff> <4f00>\n<5000> <50ff> <5000>\n<5100> <51ff> <5100>\n<5200> <52ff> <5200>\n<5300> <53ff> <5300>\n<5400> <54ff> <5400>\n<5500> <55ff> <5500>\n<5600> <56ff> <5600>\n<5700> <57ff> <5700>\n<5800> <58ff> <5800>\n<5900> <59ff> <5900>\n<5a00> <5aff> <5a00>\n<5b00> <5bff> <5b00>\n<5c00> <5cff> <5c00>\n<5d00> <5dff> <5d00>\n<5e00> <5eff> <5e00>\n<5f00> <5fff> <5f00>\n<6000> <60ff> <6000>\n<6100> <61ff> <6100>\n<6200> <62ff> <6200>\n<6300> <63ff> <6300>\nendbfrange\n100 beginbfrange\n<6400> <64ff> <6400>\n<6500> <65ff> <6500>\n<6600> <66ff> <6600>\n<6700> <67ff> <6700>\n<6800> <68ff> <6800>\n<6900> <69ff> <6900>\n<6a00> <6aff> <6a00>\n<6b00> <6bff> <6b00>\n<6c00> <6cff> <6c00>\n<6d00> <6dff> <6d00>\n<6e00> <6eff> <6e00>\n<6f00> <6fff> <6f00>\n<7000> <70ff> <7000>\n<7100> <71ff> <7100>\n<7200> <72ff> <7200>\n<7300> <73ff> <7300>\n<7400> <74ff> <7400>\n<7500> <75ff> <7500>\n<7600> <76ff> <7600>\n<7700> <77ff> <7700>\n<7800> <78ff> <7800>\n<7900> <79ff> <7900>\n<7a00> <7aff> <7a00>\n<7b00> <7bff> <7b00>\n<7c00> <7cff> <7c00>\n<7d00> <7dff> <7d00>\n<7e00> <7eff> <7e00>\n<7f00> <7fff> <7f00>\n<8000> <80ff> <8000>\n<8100> <81ff> <8100>\n<8200> <82ff> <8200>\n<8300> <83ff> <8300>\n<8400> <84ff> <8400>\n<8500> <85ff> <8500>\n<8600> <86ff> <8600>\n<8700> <87ff> <8700>\n<8800> <88ff> <8800>\n<8900> <89ff> <8900>\n<8a00> <8aff> <8a00>\n<8b00> <8bff> <8b00>\n<8c00> <8cff> <8c00>\n<8d00> <8dff> <8d00>\n<8e00> <8eff> <8e00>\n<8f00> <8fff> <8f00>\n<9000> <90ff> <9000>\n<9100> <91ff> <9100>\n<9200> <92ff> <9200>\n<9300> <93ff> <9300>\n<9400> <94ff> <9400>\n<9500> <95ff> <9500>\n<9600> <96ff> <9600>\n<9700> <97ff> <9700>\n<9800> <98ff> <9800>\n<9900> <99ff> <9900>\n<9a00> <9aff> <9a00>\n<9b00> <9bff> <9b00>\n<9c00> <9cff> <9c00>\n<9d00> <9dff> <9d00>\n<9e00> <9eff> <9e00>\n<9f00> <9fff> <9f00>\n<a000> <a0ff> <a000>\n<a100> <a1ff> <a100>\n<a200> <a2ff> <a200>\n<a300> <a3ff> <a300>\n<a400> <a4ff> <a400>\n<a500> <a5ff> <a500>\n<a600> <a6ff> <a600>\n<a700> <a7ff> <a700>\n<a800> <a8ff> <a800>\n<a900> <a9ff> <a900>\n<aa00> <aaff> <aa00>\n<ab00> <abff> <ab00>\n<ac00> <acff> <ac00>\n<ad00> <adff> <ad00>\n<ae00> <aeff> <ae00>\n<af00> <afff> <af00>\n<b000> <b0ff> <b000>\n<b100> <b1ff> <b100>\n<b200> <b2ff> <b200>\n<b300> <b3ff> <b300>\n<b400> <b4ff> <b400>\n<b500> <b5ff> <b500>\n<b600> <b6ff> <b600>\n<b700> <b7ff> <b700>\n<b800> <b8ff> <b800>\n<b900> <b9ff> <b900>\n<ba00> <baff> <ba00>\n<bb00> <bbff> <bb00>\n<bc00> <bcff> <bc00>\n<bd00> <bdff> <bd00>\n<be00> <beff> <be00>\n<bf00> <bfff> <bf00>\n<c000> <c0ff> <c000>\n<c100> <c1ff> <c100>\n<c200> <c2ff> <c200>\n<c300> <c3ff> <c300>\n<c400> <c4ff> <c400>\n<c500> <c5ff> <c500>\n<c600> <c6ff> <c600>\n<c700> <c7ff> <c700>\nendbfrange\n56 beginbfrange\n<c800> <c8ff> <c800>\n<c900> <c9ff> <c900>\n<ca00> <caff> <ca00>\n<cb00> <cbff> <cb00>\n<cc00> <ccff> <cc00>\n<cd00> <cdff> <cd00>\n<ce00> <ceff> <ce00>\n<cf00> <cfff> <cf00>\n<d000> <d0ff> <d000>\n<d100> <d1ff> <d100>\n<d200> <d2ff> <d200>\n<d300> <d3ff> <d300>\n<d400> <d4ff> <d400>\n<d500> <d5ff> <d500>\n<d600> <d6ff> <d600>\n<d700> <d7ff> <d700>\n<d800> <d8ff> <d800>\n<d900> <d9ff> <d900>\n<da00> <daff> <da00>\n<db00> <dbff> <db00>\n<dc00> <dcff> <dc00>\n<dd00> <ddff> <dd00>\n<de00> <deff> <de00>\n<df00> <dfff> <df00>\n<e000> <e0ff> <e000>\n<e100> <e1ff> <e100>\n<e200> <e2ff> <e200>\n<e300> <e3ff> <e300>\n<e400> <e4ff> <e400>\n<e500> <e5ff> <e500>\n<e600> <e6ff> <e600>\n<e700> <e7ff> <e700>\n<e800> <e8ff> <e800>\n<e900> <e9ff> <e900>\n<ea00> <eaff> <ea00>\n<eb00> <ebff> <eb00>\n<ec00> <ecff> <ec00>\n<ed00> <edff> <ed00>\n<ee00> <eeff> <ee00>\n<ef00> <efff> <ef00>\n<f000> <f0ff> <f000>\n<f100> <f1ff> <f100>\n<f200> <f2ff> <f200>\n<f300> <f3ff> <f300>\n<f400> <f4ff> <f400>\n<f500> <f5ff> <f500>\n<f600> <f6ff> <f600>\n<f700> <f7ff> <f700>\n<f800> <f8ff> <f800>\n<f900> <f9ff> <f900>\n<fa00> <faff> <fa00>\n<fb00> <fbff> <fb00>\n<fc00> <fcff> <fc00>\n<fd00> <fdff> <fd00>\n<fe00> <feff> <fe00>\n<ff00> <ffff> <ff00>\nendbfrange\nendcmap\nCMapName currentdict /CMap defineresource pop\nend\nend";

} // END OF TCPDF_FONT_DATA CLASS

//============================================================+
// END OF FILE
//============================================================+
