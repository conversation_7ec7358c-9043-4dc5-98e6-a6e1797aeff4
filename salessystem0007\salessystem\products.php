<?php
/**
 * صفحة إدارة المنتجات
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();

// معالجة حذف المنتج
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $product_id = intval($_GET['delete']);
    
    // التحقق من عدم استخدام المنتج في فواتير
    $check_sales = $db->prepare("SELECT COUNT(*) as count FROM sale_items WHERE product_id = ?");
    $check_sales->bind_param("i", $product_id);
    $check_sales->execute();
    $sales_count = $check_sales->get_result()->fetch_assoc()['count'];
    $check_sales->close();
    
    $check_purchases = $db->prepare("SELECT COUNT(*) as count FROM purchase_items WHERE product_id = ?");
    $check_purchases->bind_param("i", $product_id);
    $check_purchases->execute();
    $purchases_count = $check_purchases->get_result()->fetch_assoc()['count'];
    $check_purchases->close();
    
    if ($sales_count > 0 || $purchases_count > 0) {
        $_SESSION['error'] = "لا يمكن حذف هذا المنتج لأنه مستخدم في فواتير موجودة";
    } else {
        $stmt = $db->prepare("DELETE FROM products WHERE id = ?");
        $stmt->bind_param("i", $product_id);
        
        if ($stmt->execute()) {
            logActivity('product_delete', 'products', $product_id, null, null, 'حذف منتج');
            $_SESSION['success'] = "تم حذف المنتج بنجاح";
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء حذف المنتج";
        }
        $stmt->close();
    }
    
    header("Location: products.php");
    exit();
}

// جلب التصنيفات المتاحة
$categories_result = $db->query("SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != '' ORDER BY category");
$categories = [];
if ($categories_result) {
    while ($cat = $categories_result->fetch_assoc()) {
        $categories[] = $cat['category'];
    }
}

// جلب المنتجات مع البحث والترقيم
$search = $_GET['search'] ?? '';
$category_filter = $_GET['category'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR description LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $types .= 'ss';
}

if (!empty($category_filter)) {
    $where_conditions[] = "category = ?";
    $params[] = $category_filter;
    $types .= 's';
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(' AND ', $where_conditions);
}

// عدد المنتجات الإجمالي
$count_sql = "SELECT COUNT(*) as total FROM products $where_clause";
if (!empty($params)) {
    $count_stmt = $db->prepare($count_sql);
    $count_stmt->bind_param($types, ...$params);
    $count_stmt->execute();
    $total_products = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
} else {
    $total_products = $db->query($count_sql)->fetch_assoc()['total'];
}

$total_pages = ceil($total_products / $limit);

// جلب المنتجات مرتبة حسب التصنيف ثم الاسم
$sql = "SELECT * FROM products $where_clause ORDER BY
        CASE WHEN category IS NULL OR category = '' THEN 1 ELSE 0 END,
        category ASC,
        name ASC
        LIMIT $limit OFFSET $offset";
if (!empty($params)) {
    $stmt = $db->prepare($sql);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $products = $stmt->get_result();
    $stmt->close();
} else {
    $products = $db->query($sql);
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-boxes me-2"></i>
                    إدارة المنتجات
                </h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProductModal">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </button>
            </div>
            
            <div class="card-body">
                <!-- شريط البحث والفلاتر -->
                <div class="row mb-3">
                    <div class="col-md-8">
                        <form method="GET" class="row g-2">
                            <div class="col-md-6">
                                <input type="text" class="form-control" name="search"
                                       placeholder="البحث في المنتجات..."
                                       value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" name="category">
                                    <option value="">جميع التصنيفات</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo htmlspecialchars($category); ?>"
                                                <?php echo $category_filter === $category ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-outline-primary w-100">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <?php if (!empty($search) || !empty($category_filter)): ?>
                            <div class="col-12">
                                <a href="products.php" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-times"></i> مسح الفلاتر
                                </a>
                            </div>
                            <?php endif; ?>
                        </form>
                    </div>
                    <div class="col-md-4 text-end">
                        <small class="text-muted">
                            إجمالي المنتجات: <?php echo number_format($total_products); ?>
                        </small>
                        <?php if (!empty($category_filter)): ?>
                            <br><small class="text-info">
                                التصنيف: <?php echo htmlspecialchars($category_filter); ?>
                            </small>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>الرقم</th>
                                <th>اسم المنتج</th>
                                <th>التصنيف</th>
                                <th>الوصف</th>
                                <th>السعر</th>
                                <th>نسبة الضريبة</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($products && $products->num_rows > 0): ?>
                                <?php
                                $counter = $offset + 1;
                                $current_category = null;
                                $category_counter = 0;
                                ?>
                                <?php while ($product = $products->fetch_assoc()): ?>
                                    <?php
                                    $product_category = $product['category'] ?? '';
                                    // إذا تغير التصنيف، أضف فاصل
                                    if ($current_category !== $product_category):
                                        $current_category = $product_category;
                                        $category_counter++;
                                    ?>
                                        <tr class="category-separator">
                                            <td colspan="8" class="bg-light text-center py-2">
                                                <strong class="text-primary">
                                                    <i class="fas fa-tag me-2"></i>
                                                    <?php if (!empty($product_category)): ?>
                                                        تصنيف: <?php echo htmlspecialchars($product_category); ?>
                                                    <?php else: ?>
                                                        منتجات بدون تصنيف
                                                    <?php endif; ?>
                                                </strong>
                                            </td>
                                        </tr>
                                    <?php endif; ?>

                                    <tr class="product-row" data-category="<?php echo htmlspecialchars($product_category); ?>">
                                        <td><?php echo $counter++; ?></td>
                                        <td>
                                            <strong><?php echo htmlspecialchars($product['name']); ?></strong>
                                        </td>
                                        <td>
                                            <?php if (!empty($product['category'])): ?>
                                                <span class="badge bg-primary">
                                                    <?php echo htmlspecialchars($product['category']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="text-muted">غير محدد</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            $description = htmlspecialchars($product['description'] ?? '');
                                            echo strlen($description) > 30 ? substr($description, 0, 30) . '...' : $description;
                                            ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php echo number_format($product['price'], 2); ?> ر.س
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">
                                                <?php echo number_format($product['tax_rate'], 1); ?>%
                                            </span>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                <?php echo date('Y-m-d', strtotime($product['created_at'] ?? date('Y-m-d'))); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="editProduct(<?php echo $product['id']; ?>)"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteProduct(<?php echo $product['id']; ?>, '<?php echo addslashes($product['name']); ?>')"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-box-open fa-3x mb-3"></i>
                                            <p>لا توجد منتجات</p>
                                            <?php if (!empty($search)): ?>
                                                <p>لم يتم العثور على منتجات تطابق البحث: "<?php echo htmlspecialchars($search); ?>"</p>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- الترقيم -->
                <?php if ($total_pages > 1): ?>
                <?php
                // بناء معاملات الاستعلام للحفاظ على الفلاتر
                $query_params = [];
                if (!empty($search)) $query_params[] = 'search=' . urlencode($search);
                if (!empty($category_filter)) $query_params[] = 'category=' . urlencode($category_filter);
                $query_string = !empty($query_params) ? '&' . implode('&', $query_params) : '';
                ?>
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo $query_string; ?>">
                                السابق
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $query_string; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo $query_string; ?>">
                                التالي
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </nav>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة منتج جديد -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addProductModalLabel">
                    <i class="fas fa-plus me-2"></i>
                    إضافة منتج جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="productForm" method="POST" action="save_product.php">
                <div class="modal-body">
                    <input type="hidden" id="productId" name="product_id" value="">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productName" class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control" id="productName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productPrice" class="form-label">السعر *</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="productPrice" name="price" 
                                           step="0.01" min="0" required>
                                    <span class="input-group-text">ر.س</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productTaxRate" class="form-label">نسبة الضريبة (%)</label>
                                <input type="number" class="form-control" id="productTaxRate" name="tax_rate" 
                                       step="0.01" min="0" max="100" value="15">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="productCategory" class="form-label">التصنيف</label>
                                <div class="input-group">
                                    <select class="form-select" id="productCategorySelect" onchange="handleCategoryChange()">
                                        <option value="">اختر تصنيف موجود</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?php echo htmlspecialchars($category); ?>">
                                                <?php echo htmlspecialchars($category); ?>
                                            </option>
                                        <?php endforeach; ?>
                                        <option value="new">+ إضافة تصنيف جديد</option>
                                    </select>
                                    <input type="text" class="form-control" id="productCategory" name="category"
                                           placeholder="أدخل تصنيف جديد..." style="display: none;">
                                </div>
                                <small class="form-text text-muted">
                                    اختر من التصنيفات الموجودة أو أضف تصنيف جديد
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="productDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="productDescription" name="description" 
                                  rows="3" placeholder="وصف تفصيلي للمنتج..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ المنتج
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// إدارة التصنيفات
function handleCategoryChange() {
    const select = document.getElementById('productCategorySelect');
    const input = document.getElementById('productCategory');

    if (select.value === 'new') {
        // إظهار حقل النص وإخفاء القائمة المنسدلة
        select.style.display = 'none';
        input.style.display = 'block';
        input.focus();
        input.value = '';
    } else if (select.value === '') {
        // إظهار حقل النص للتصنيف المخصص
        select.style.display = 'none';
        input.style.display = 'block';
        input.value = '';
    } else {
        // استخدام التصنيف المحدد
        input.value = select.value;
    }
}

// إعادة تعيين التصنيفات
function resetCategoryFields() {
    const select = document.getElementById('productCategorySelect');
    const input = document.getElementById('productCategory');

    select.style.display = 'block';
    input.style.display = 'none';
    select.value = '';
    input.value = '';
}

// تعديل منتج
function editProduct(productId) {
    // جلب بيانات المنتج عبر AJAX
    fetch(`get_product.php?id=${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const product = data.product;
                
                // ملء النموذج
                document.getElementById('productId').value = product.id;
                document.getElementById('productName').value = product.name;
                document.getElementById('productPrice').value = product.price;
                document.getElementById('productTaxRate').value = product.tax_rate;
                document.getElementById('productDescription').value = product.description || '';

                // التعامل مع التصنيف
                const categorySelect = document.getElementById('productCategorySelect');
                const categoryInput = document.getElementById('productCategory');

                if (product.category) {
                    // البحث عن التصنيف في القائمة المنسدلة
                    let found = false;
                    for (let option of categorySelect.options) {
                        if (option.value === product.category) {
                            categorySelect.value = product.category;
                            categoryInput.value = product.category;
                            found = true;
                            break;
                        }
                    }

                    // إذا لم يوجد التصنيف في القائمة، أظهر حقل النص
                    if (!found) {
                        categorySelect.style.display = 'none';
                        categoryInput.style.display = 'block';
                        categoryInput.value = product.category;
                    }
                } else {
                    resetCategoryFields();
                }
                
                // تغيير عنوان النافذة
                document.getElementById('addProductModalLabel').innerHTML = 
                    '<i class="fas fa-edit me-2"></i>تعديل المنتج';
                
                // إظهار النافذة
                const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
                modal.show();
            } else {
                alert('حدث خطأ في جلب بيانات المنتج');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في الاتصال');
        });
}

// حذف منتج
function deleteProduct(productId, productName) {
    if (confirm(`هل أنت متأكد من حذف المنتج "${productName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        window.location.href = `products.php?delete=${productId}`;
    }
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.getElementById('addProductModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('productForm').reset();
    document.getElementById('productId').value = '';
    document.getElementById('addProductModalLabel').innerHTML =
        '<i class="fas fa-plus me-2"></i>إضافة منتج جديد';

    // إعادة تعيين حقول التصنيف
    resetCategoryFields();
});
</script>

<style>
/* تحسين مظهر فواصل التصنيفات */
.category-separator td {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    border-top: 2px solid #007bff !important;
    border-bottom: 1px solid #dee2e6 !important;
    font-weight: 600;
    font-size: 14px;
    padding: 12px 15px !important;
    position: sticky;
    top: 0;
    z-index: 10;
}

.category-separator .text-primary {
    color: #0056b3 !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.category-separator i {
    color: #007bff;
}

/* تحسين صفوف المنتجات */
.product-row {
    transition: background-color 0.2s ease;
}

.product-row:hover {
    background-color: rgba(0, 123, 255, 0.05) !important;
}

/* تحسين الجدول العام */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    position: sticky;
    top: 0;
    z-index: 20;
    background: #343a40 !important;
    color: white !important;
    border-bottom: 2px solid #007bff;
}

/* تحسين البحث والفلاتر */
.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-bottom: 2px solid #0056b3;
}

/* تحسين الشارات */
.badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

/* تحسين الأزرار */
.btn-group .btn {
    border-radius: 4px;
    margin: 0 1px;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.btn-outline-danger:hover {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border-color: #dc3545;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* تحسين الترقيم */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #007bff;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.pagination .page-link:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-color: #007bff;
    transform: translateY(-1px);
}

/* تحسين للشاشات الصغيرة */
@media (max-width: 768px) {
    .category-separator td {
        font-size: 12px;
        padding: 8px 10px !important;
    }

    .table td, .table th {
        padding: 6px 4px;
        font-size: 12px;
    }

    .badge {
        font-size: 10px;
        padding: 2px 6px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
