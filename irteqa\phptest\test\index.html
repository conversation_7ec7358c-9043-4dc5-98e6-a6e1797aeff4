
<!DOCTYPE html>
<html lang="en">
  <head>


    <meta charset="utf-8">
        <meta name="twitter:card" content="player">
    <meta name="twitter:site" content="@figma">
    <meta name="twitter:title" content="Untitled">
    <meta name="twitter:player" content="https://www.figma.com/embed?embed_host=twitter&url=https:&#47;&#47;www.figma.com&#47;proto&#47;daZH9zno8XhMmf6JQBl9g9&#47;Untitled?node-id=1-2">
    <meta name="twitter:player:width" content="800">
    <meta name="twitter:player:height" content="450">
    <meta name="twitter:image" content="https:&#47;&#47;www.figma.com&#47;file&#47;daZH9zno8XhMmf6JQBl9g9&#47;thumbnail?ver=thumbnails&#47;175223e1-de82-4b9a-a1f3-41738f239c2a">

    <meta property="og:url" content="https:&#47;&#47;www.figma.com&#47;proto&#47;daZH9zno8XhMmf6JQBl9g9&#47;Untitled?node-id=1-2" />
    <meta property="og:description" content="Created with Figma" />
    <meta property="og:image" content="https:&#47;&#47;www.figma.com&#47;file&#47;daZH9zno8XhMmf6JQBl9g9&#47;thumbnail?ver=thumbnails&#47;175223e1-de82-4b9a-a1f3-41738f239c2a">
    <meta property="og:image:width" content="800" />
    <meta property="og:image:height" content="384" />
    <meta property="og:image:user_generated" content="true" />
    <meta property="og:type" content="article" />
    <meta property="og:article:published_time" content="2023-11-23 03:22:03 UTC" />
    <meta property="og:article:modified_time" content="2023-11-25 09:59:06 UTC" />
    <meta property="og:article:section" content="Design" />

    <meta name="description" content="Created with Figma" >

    <link rel="alternate" type="application/json+oembed" href="https:&#47;&#47;www.figma.com&#47;api&#47;oembed?url=https:&#47;&#47;www.figma.com&#47;proto&#47;daZH9zno8XhMmf6JQBl9g9&#47;Untitled?node-id=1-2" title="OEmbed" />

    <script nonce="VqsolV7Ot2ROA5YFchq09w==">
      if (function() {
        if (window.WebGL2RenderingContext === undefined) {
          // WebGL2.0 check
          return true
        }
        if (window.WeakRef === undefined) {
          // WeakRef check
          return true
        }
      }()) {
        location.href = '/unsupported_browser'
      }
    </script>





    <script nonce="VqsolV7Ot2ROA5YFchq09w==" type="application/json" data-initial>{"INITIAL_OPTIONS":{"experiments":{"exp_early_bundles_v1":{"assignment_weights":{"enabled":50,"not_enabled":50},"randomization_method":0,"winner":"enabled","treatment_names":["not_enabled","enabled"]},"exp_billing_groups":{"randomization_method":0,"treatment_names":["control","beta","ga"]},"exp_growth_h123_holdout_team":{"assignment_weights":{"ignore":90,"in_holdout":5,"not_in_holdout":5},"randomization_method":0,"winner":"not_in_holdout","treatment_names":["ignore","in_holdout","not_in_holdout"]},"exp_growth_h123_holdout_user":{"assignment_weights":{"ignore":90,"in_holdout":5,"not_in_holdout":5},"randomization_method":0,"winner":"not_in_holdout","treatment_names":["ignore","in_holdout","not_in_holdout"]},"exp_community_search_landing":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_community_search_no_remixes":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_community_universal_posting":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_cpp_memory_limit_android":{"assignment_weights":{"v_256":0,"v_512":0,"nv_512":0,"v_1024":20,"v_2048":20,"control":20,"nv_1024":20,"nv_2048":20},"randomization_method":0,"treatment_names":["control","v_2048","nv_2048","v_1024","nv_1024","v_512","nv_512","v_256"]},"exp_dps_enabled_for_orgs":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"treatment_names":["control","variant"]},"exp_dps_enabled_for_pro_teams":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"treatment_names":["control","variant"]},"exp_design_draft_file_edit_req":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"treatment_names":["control","variant"]},"exp_design_to_figjam_whats_new":{"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_downgrade_org_vr_email":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"treatment_names":["control","variant"]},"exp_dse_asset_panel_improvements":{"assignment_weights":{"control":0,"variant_default":100,"variant_recents":0},"randomization_method":0,"treatment_names":["control","variant_default","variant_recents"]},"exp_edit_action_orgs":{"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_edit_action_teams":{"assignment_weights":{"control":15,"variant":85},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_edu_onboarding_acc_creation":{"randomization_method":0,"treatment_names":["control","variant"]},"exp_expiring_public_links":{"randomization_method":0,"winner":"","treatment_names":["control","variant"]},"exp_extension_trust":{"randomization_method":0,"treatment_names":["control","variant"]},"exp_fj_category_templates_shelf":{"assignment_weights":{"control":25,"categories_tabs":25,"all_and_categories_tabs":25,"role_and_categories_tabs":25},"randomization_method":0,"winner":"role_and_categories_tabs","treatment_names":["control","categories_tabs","all_and_categories_tabs","role_and_categories_tabs"]},"exp_fbi_community_entrypoint":{"randomization_method":0,"treatment_names":["control","icon_no_label","icon_with_label"]},"exp_figjam_active_time_spent":{"assignment_weights":{"control":0,"external_variant":100,"internal_one_minute":0,"internal_one_and_a_half_minutes":0},"randomization_method":0,"treatment_names":["control","external_variant","internal_one_minute","internal_one_and_a_half_minutes"]},"exp_figjam_cta_placeholder_text2":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_figjam_expanded_placeholders":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_figjam_ia_refresh":{"assignment_weights":{"control":50,"treatment":50},"randomization_method":0,"treatment_names":["control","treatment"]},"exp_figjam_meeting_tools_panel":{"randomization_method":0,"treatment_names":["control","variant_1","variant_2"]},"exp_figjam_onboarding_make_tmpl":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"winner":"","treatment_names":["control","variant"]},"exp_figjam_simulated_use_cases_5":{"randomization_method":0,"treatment_names":["control","modal_video","popup_video","nux_video"]},"exp_figjam_specific_onboarding":{"assignment_weights":{"control":34,"variant_1":33,"variant_2":33},"randomization_method":0,"winner":"variant_2","treatment_names":["control","variant_1","variant_2"]},"exp_figjam_starterkit_onboarding":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_figjam_whats_new_v2":{"assignment_weights":{"control":25,"excluded":0,"variant_ai":25,"variant_app_fwd":25,"variant_integrations":25},"randomization_method":0,"treatment_names":["excluded","control","variant_ai","variant_app_fwd","variant_integrations"]},"exp_fj_draft_file_edit_req_v2":{"assignment_weights":{"control":10,"variant":90},"randomization_method":0,"treatment_names":["control","variant"]},"exp_fj_engagement_holdout_h1_23":{"assignment_weights":{"ignore":90,"in_holdout":5,"not_in_holdout":5},"randomization_method":0,"treatment_names":["ignore","in_holdout","not_in_holdout"]},"exp_idle_timeout":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_international_currencies":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_ip_account_restriction":{"randomization_method":0,"treatment_names":["control","variant"]},"exp_memory_usage_indicators_v1":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"treatment_names":["control","variant"]},"exp_mobile_reply_upsell":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_move_drafts_v1":{"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_move_drafts_v2":{"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_new_user_survey_1a_biz":{"assignment_weights":{"control":30,"variant":70},"randomization_method":0,"winner":"control","treatment_names":["control","variant"]},"exp_new_user_survey_1a_personal":{"assignment_weights":{"control":30,"variant":70},"randomization_method":0,"winner":"control","treatment_names":["control","variant"]},"exp_notification_catfile":{"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_org_synchronous_emails":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_oss_sales_upsell_v1":{"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_redirect_mobile_sign_up_v1":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"exp_saml_encryption":{"randomization_method":0,"treatment_names":["control","variant"]},"exp_serve_resized_images":{"assignment_weights":{"control":50,"variant":50},"randomization_method":0,"treatment_names":["control","variant"]},"exp_share_options":{"randomization_method":0,"treatment_names":["control","variant_share_no_dev","variant_share_with_dev"]},"stripe_3ds_pro":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"treatment_names":["control","variant"]},"teamplates":{"randomization_method":0,"winner":"enabled","treatment_names":["not_enabled","enabled"]},"xr_debounce_digest_per_org":{"randomization_method":0,"treatment_names":["not_enabled","enabled"]},"exp_ws_public_link_controls":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]},"gbo_new_customer_obp":{"assignment_weights":{"control":0,"variant":100},"randomization_method":0,"winner":"variant","treatment_names":["control","variant"]}},"statsig_figma_app_client_api_key":"client-xHjVMKatVJHNxOUoMfvXR2hVCAEV5k3TilTq7leNXdL","launchdarkly_client_side_id":"62e9cfc83c59501226eae584","tracking_session_id":"xmoko8U1j0p4syg3","cluster_name":"prod","error_dashboard_url":"https://errors.figma.com/api","frontend_sentry_dsn":"https://<EMAIL>/api/sentry/56203","figma_email_suffix":"@figma.com","csp_nonce":"VqsolV7Ot2ROA5YFchq09w==","figma_url":"https://www.figma.com","api_url":"https://api.figma.com","api_cdn_url":"https://api-cdn.figma.com","workflow_interop_template_id":"1085289819681923775","flash":{"error":null,"warn":null,"success":null},"user_currency_from_ip":"usd","fxr_for_currency":null,"google_contacts_client_id":"68994840458-eaosee3tcba6dmdsdjk4ev1ie5dbob2q.apps.googleusercontent.com","google_contacts_api_key":null,"google_drive_client_id":"438425412978-45umild77kjr12jigqg31ojvlno436ev.apps.googleusercontent.com","google_drive_api_key":"AIzaSyAHNbt8yQ-u-AvdPHTzHodK5eMyVoE5iZ4","cursor_bot":{"product_page_hub_file_id":"1243321298372865757","ginger_hub_file_id":"1243321216166864604"},"prototyping_for_gen0":{"asset_hub_file_id":"1256400056994575865"},"google_client_id":"532352704633-6pkces9iboppp465idnovkcqtlsa8j7t.apps.googleusercontent.com","is_explicit_consent_region":false,"consent_region":null,"page_load_token":"pv=1\u0026pr=1f24b0793a0314c2\u0026pt=**********\u0026ph=FyHsPjqdGIsDsGtxnsMkOGt5i7esn8ZYuRpU208CxWY","resource_type":null,"email":"<EMAIL>","user_ip":"************","redirect_url":null,"email_token":null,"existing_session":true,"should_use_redirect_instead":false,"mailing_list_checkbox_default":false,"from_chrome_extension":false,"viewer_region":"04","viewer_city":"Dammam","iso_code":"SA","editing_file":{"creator_id":"1285381156962610170","updated_at":"2023-11-23T03:22:03.026Z","key":"daZH9zno8XhMmf6JQBl9g9","name":"Untitled","description":null,"folder_id":"171331517","scheme":null,"track_tags":null,"team_id":"1309356710924270529","link_access":"inherit","trashed_user_id":null,"client_meta":"{\"background_color\":{\"r\":0.11764705926179886,\"g\":0.11764705926179886,\"b\":0.11764705926179886,\"a\":1.0},\"thumbnail_size\":{\"width\":800,\"height\":384},\"render_coordinates\":{\"x\":-750.0,\"y\":-588.0,\"width\":2563.0,\"height\":1231.0}}","license":null,"parent_org_id":null,"org_browsable":null,"thumbnail_url_override":null,"thumbnail_guid":null,"proto_link_access":null,"org_audience":false,"file_repo_id":null,"source_file_key":null,"source_checkpoint_id":null,"editor_type":"design","branch_checkpoint_id":null,"has_file_link_password":false,"has_proto_link_password":false,"url":"https://www.figma.com/file/daZH9zno8XhMmf6JQBl9g9/Untitled","edit_url":"https://www.figma.com/file/daZH9zno8XhMmf6JQBl9g9/Untitled","prototype_url":"https://www.figma.com/proto/daZH9zno8XhMmf6JQBl9g9/Untitled","handoff_url":"https://www.figma.com/file/daZH9zno8XhMmf6JQBl9g9/Untitled?mode=dev","viewer_export_restricted":false,"thumbnail_url":"https://s3-alpha.figma.com/thumbnails/175223e1-de82-4b9a-a1f3-41738f239c2a?X-Amz-Algorithm=AWS4-HMAC-SHA256\u0026X-Amz-Credential=********************%2F20231123%2Fus-west-2%2Fs3%2Faws4_request\u0026X-Amz-Date=20231123T000000Z\u0026X-Amz-Expires=604800\u0026X-Amz-SignedHeaders=host\u0026X-Amz-Signature=847382d27f13795afbb1351eb65b0dc24654f0f34d1847a7bfba69ef826398c9","is_try_file":false,"created_at":"2023-11-23T03:22:03Z","deleted_at":null,"trashed_at":null,"folder":{"id":"171331517","created_at":"2023-11-23T03:16:06.930Z","updated_at":"2023-11-23T03:16:06.930Z","scheme":null,"path":"Team project","settings":null,"team_id":"1309356710924270529","view_only_at":null,"org_id":null,"deleted_at":null,"description":"","sharing_audience_control":"invite_only","name":"Team project","is_invite_only":false,"is_view_only":false,"team_access":"team_edit"},"team":{"id":"1309356710924270529","name":"chatapp","created_at":"2023-11-23T03:16:06.550Z","img_url":null,"synced_at":null,"providers":null,"subscription":null,"editors":1,"student_team_at":null,"student_autoverifying_team_at":null,"org_id":null,"org_access":null,"deleted_at":null,"blocked_at":null,"trial_period_end":null,"description":null,"deleted_by":null,"experiment_seed":704319,"license_group_id":null,"migrated_stripe_customer_id":null,"community_blocked_at":null,"default_permission":null,"vat_gst_id":"","workspace_id":null,"default_color_palette_uuid":null,"tax_id_verification_status":null,"tax_id_verified_at":null,"legal_name":null,"ai_features_disabled_at":null,"editors_whiteboard":0,"editors_total_unique":1,"student_team":false,"restrictions_list":["projects_limited","files_limited"],"ai_features_disabled":false,"sharing_audience_control":"invite_only","org_browsable":false,"hidden":false,"experiment_assignments":[]},"team_user":{"id":"90210662","team_id":"1309356710924270529","user_id":"1285381156962610170","design_paid_status":"full","whiteboard_paid_status":"starter","created_at":"2023-11-23T03:16:06.633Z","updated_at":"2023-11-23T03:22:03.100Z","show_figjam_user_onboarding":null,"has_shown_figjam_admin_onboarding":null,"drafts_folder_id":"*********"},"org":null,"org_user":null,"edu_grace_period":null,"file_repo":null,"source_file":null,"show_edit_button":false,"can_edit":true,"can_edit_canvas":true,"can_access_full_dev_mode":true,"can_manage":true,"touched_at":"2023-11-25T09:59:06Z","is_favorited":false},"zeplin_plugin_id":"745330164019088593","avocode_plugin_id":"821674268995163810","avocode_markup_plugin_id":"1065609061080464965","org_id":null,"page_load_team_id":"1309356710924270529","is_cloudfront":true,"user_notifications_bell":{"bell":{"0":false,"-1":false}},"promo":null,"segment_web_key":"6Zhdn0wK1GLYzCsb0LIK0oQplS5TXcB2","segment_fullscreen_key":"6uxDivlUmLf95lHRk0R8bZvr8zDxbX5E","zendesk_web_key_public":"8f3196e1-a5a9-4a39-9b1c-6ab81db7fe17","stripe_api_public":"pk_live_LKZ0RKjSZG2D2pwdtwrAhkiJ","google_tag_manager_iframe_url":"https://marketing.figma.com","sprig_iframe_url":"https://sprig.figma.com","recaptcha_v3_ent_site_key":"6Le0W80aAAAAAGU9L7qz4o9tQVqrdJVv2M8XHIcd","arkose_public_key":"2C8A87DD-7EEF-4414-895C-D3982246EE49","arkose_challenge_public_key":"A207F8A1-ED09-4325-ACE6-C8E26A458FBA","arkose_frame_url":"https://verify.figma.com/arkose_frame.html","arkose_origin":"https://verify.figma.com","do_arkose":true,"release_manifest_git_commit":"3102ec2ab9ca06cac70188fc4dbd381b0065f46a","release_server_git_commit":"1bb6b69f0a64b974381b9dab87508f825732bb9e","release_git_tag":"release-2023-11-21","user_analytics_data":{"design_activation_date":null,"design_max_paid_role":"free","domain_editors":null,"edited_figma_design":true,"education_user":false,"email_type":"Personal","expanded_usage_purpose_no_answer":true,"figjam_max_paid_role":"free","first_collab_on":null,"first_component_created_date":"2023-09-17 00:00:00.000","first_design_at":"2023-09-17 23:27:02.348","first_figjam_at":"2023-10-06 22:33:59.901","generation":0,"how_collab_no_answer":true,"is_activated_design":false,"is_active_mobile_user":false,"is_agency":false,"is_bootcamp":false,"is_clients":false,"is_company":false,"is_few_times":false,"is_first_time":false,"is_founder":false,"is_freelancer":false,"is_fun":false,"is_ipad_user":false,"is_just_checking_out":false,"is_many_times":false,"is_new_job_or_project":false,"is_not_sure":false,"is_other":false,"is_school":false,"is_seeking_design_tool":false,"is_self":false,"is_sharing_with_team":false,"is_teammates":false,"is_used_before":false,"is_work":false,"job_title":"developer","land_product":"design","last_active_on":"2023-11-24 00:00:00.000","last_comment_created_at":null,"last_figjam_at":"2023-10-06 00:00:00.000","num_edits_30_day":25,"num_edits_60_day":25,"num_edits_90_day":30,"num_edit_days_30_day":2,"num_edit_days_60_day":2,"num_edit_days_90_day":4,"num_files_created":3,"paid_status":"free","shared_design":false,"shared_figjam":false,"show_styles_deprecate_banner":false,"signup_date":"2023-09-17","used_desktop_app":true,"used_figjam_wheel_or_chat":false,"used_figma_before_no_answer":true,"used_high_five":false,"used_timer":false,"user_id":"1285381156962610170","work_location_no_answer":true},"release_manifest_hash":"462ec84903f9196c02066567042dba4c","user_flags":[{"id":"**********","user_id":"1285381156962610170","name":"account_switcher_onboarded","created_at":"2023-09-18T00:07:21.431Z","updated_at":"2023-09-18T00:07:21.431Z"},{"id":"**********","user_id":"1285381156962610170","name":"collective_upsell_first_file_created","created_at":"2023-11-24T03:34:40.875Z","updated_at":"2023-11-24T03:34:40.875Z"},{"id":"**********","user_id":"1285381156962610170","name":"color_management_desktop_app_setting_managed","created_at":"2023-11-23T03:19:12.766Z","updated_at":"2023-11-23T03:19:20.207Z"},{"id":"**********","user_id":"1285381156962610170","name":"community_hub_onboarded","created_at":"2023-09-18T00:07:21.396Z","updated_at":"2023-09-18T00:07:21.396Z"},{"id":"**********","user_id":"1285381156962610170","name":"dev_handoff_dismissed_share_modal_banner","created_at":"2023-11-24T10:17:44.952Z","updated_at":"2023-11-24T10:17:44.952Z"},{"id":"**********","user_id":"1285381156962610170","name":"dev_handoff_has_seen_dev_mode","created_at":"2023-11-23T04:26:08.467Z","updated_at":"2023-11-23T04:26:08.467Z"},{"id":"1489608917","user_id":"1285381156962610170","name":"dev_handoff_seen_config_wizard","created_at":"2023-11-23T04:26:13.121Z","updated_at":"2023-11-23T04:26:13.121Z"},{"id":"1298136499","user_id":"1285381156962610170","name":"dismissed_community_detail_view_saves_onboarding","created_at":"2023-09-17T23:30:09.401Z","updated_at":"2023-09-17T23:30:09.401Z"},{"id":"1298135218","user_id":"1285381156962610170","name":"dismissed_community_saved_in_editor_onboarding","created_at":"2023-09-17T23:27:22.779Z","updated_at":"2023-09-17T23:27:22.779Z"},{"id":"1489608782","user_id":"1285381156962610170","name":"dismissed_config_2023_features_modal","created_at":"2023-11-23T04:26:07.949Z","updated_at":"2023-11-23T04:26:07.949Z"},{"id":"1298137313","user_id":"1285381156962610170","name":"dismissed_local_component_asset_panel_pointer","created_at":"2023-09-17T23:31:22.482Z","updated_at":"2023-11-25T05:52:01.215Z"},{"id":"1493116892","user_id":"1285381156962610170","name":"dismissed_mobile_replies_upsell","created_at":"2023-11-24T12:38:16.458Z","updated_at":"2023-11-24T12:38:16.458Z"},{"id":"1489507210","user_id":"1285381156962610170","name":"entered_whats_new_v2_exp","created_at":"2023-11-23T03:11:32.169Z","updated_at":"2023-11-23T03:11:32.169Z"},{"id":"1489531027","user_id":"1285381156962610170","name":"figma_basics_has_modified_properties","created_at":"2023-11-23T03:28:46.222Z","updated_at":"2023-11-23T03:28:46.222Z"},{"id":"1298155933","user_id":"1285381156962610170","name":"file_browser_onboarded","created_at":"2023-09-18T00:07:21.379Z","updated_at":"2023-09-18T00:07:21.379Z"},{"id":"1493118160","user_id":"1285381156962610170","name":"has_closed_comment","created_at":"2023-11-24T12:38:48.451Z","updated_at":"2023-11-24T12:39:08.620Z"},{"id":"1298134518","user_id":"1285381156962610170","name":"has_created_a_team","created_at":"2023-09-17T23:25:50.677Z","updated_at":"2023-11-23T03:16:07.202Z"},{"id":"1493116911","user_id":"1285381156962610170","name":"has_created_comment","created_at":"2023-11-24T12:38:17.296Z","updated_at":"2023-11-24T12:38:17.296Z"},{"id":"1489509520","user_id":"1285381156962610170","name":"has_created_frame","created_at":"2023-11-23T03:13:12.868Z","updated_at":"2023-11-23T03:13:12.868Z"},{"id":"1492280212","user_id":"1285381156962610170","name":"has_created_prototyping_noodle","created_at":"2023-11-24T06:01:03.975Z","updated_at":"2023-11-24T06:01:03.975Z"},{"id":"1493116147","user_id":"1285381156962610170","name":"has_created_selection_comment","created_at":"2023-11-24T12:37:58.328Z","updated_at":"2023-11-24T12:37:58.328Z"},{"id":"1489938466","user_id":"1285381156962610170","name":"has_created_text","created_at":"2023-11-23T07:35:51.687Z","updated_at":"2023-11-23T07:35:52.010Z"},{"id":"1298137688","user_id":"1285381156962610170","name":"has_entered_presentation_mode","created_at":"2023-09-17T23:32:07.056Z","updated_at":"2023-09-17T23:32:07.056Z"},{"id":"1489936125","user_id":"1285381156962610170","name":"has_inserted_component","created_at":"2023-11-23T07:34:43.486Z","updated_at":"2023-11-23T07:34:43.486Z"},{"id":"1493116155","user_id":"1285381156962610170","name":"has_opened_comments_modal","created_at":"2023-11-24T12:37:58.375Z","updated_at":"2023-11-24T12:39:01.216Z"},{"id":"1492276870","user_id":"1285381156962610170","name":"has_opened_prototyping_tab","created_at":"2023-11-24T05:58:54.345Z","updated_at":"2023-11-24T05:58:54.345Z"},{"id":"1492326441","user_id":"1285381156962610170","name":"has_removed_prototyping_noodle","created_at":"2023-11-24T06:28:19.699Z","updated_at":"2023-11-24T06:28:19.699Z"},{"id":"1489508251","user_id":"1285381156962610170","name":"has_selected_frame_tool","created_at":"2023-11-23T03:12:17.547Z","updated_at":"2023-11-23T03:12:17.547Z"},{"id":"1298134509","user_id":"1285381156962610170","name":"has_tooltips_plus_onboarding_file_no_nav","created_at":"2023-09-17T23:25:49.494Z","updated_at":"2023-09-17T23:25:49.494Z"},{"id":"1298135029","user_id":"1285381156962610170","name":"keyboard_user_first_detection","created_at":"2023-09-17T23:27:00.721Z","updated_at":"2023-09-17T23:27:00.721Z"},{"id":"**********","user_id":"1285381156962610170","name":"no_figma_basics_tooltips_onboarding","created_at":"2023-11-23T03:12:21.864Z","updated_at":"2023-11-23T03:12:21.864Z"},{"id":"**********","user_id":"1285381156962610170","name":"opted_in_email","created_at":"2023-09-17T23:25:49.258Z","updated_at":"2023-09-17T23:25:49.258Z"},{"id":"**********","user_id":"1285381156962610170","name":"seen_account_profile_switcher_onboarding","created_at":"2023-09-18T00:07:21.469Z","updated_at":"2023-09-18T00:07:21.469Z"},{"id":"**********","user_id":"1285381156962610170","name":"seen_component_onboarding_modal","created_at":"2023-11-23T04:36:10.891Z","updated_at":"2023-11-25T10:11:13.815Z"},{"id":"**********","user_id":"1285381156962610170","name":"seen_design_to_figjam_whats_new_modal","created_at":"2023-10-06T22:27:49.846Z","updated_at":"2023-10-06T22:27:49.846Z"},{"id":"**********","user_id":"1285381156962610170","name":"seen_development_plugin_onboarding_modal","created_at":"2023-11-23T04:36:10.930Z","updated_at":"2023-11-25T10:11:13.864Z"},{"id":"**********","user_id":"1285381156962610170","name":"seen_get_more_from_fbg_onboarding","created_at":"2023-09-18T00:07:21.451Z","updated_at":"2023-09-18T00:07:21.451Z"},{"id":"**********","user_id":"1285381156962610170","name":"seen_pro_cart_abandon_survey","created_at":"2023-11-23T03:16:56.225Z","updated_at":"2023-11-23T03:16:56.225Z"},{"id":"1489621733","user_id":"1285381156962610170","name":"seen_published_plugin_onboarding_modal","created_at":"2023-11-23T04:36:10.911Z","updated_at":"2023-11-25T10:11:13.839Z"},{"id":"1298155939","user_id":"1285381156962610170","name":"seen_shared_files_onboarding","created_at":"2023-09-18T00:07:21.489Z","updated_at":"2023-09-18T00:07:21.489Z"},{"id":"1489621735","user_id":"1285381156962610170","name":"seen_universal_inserts_onboarding_modal","created_at":"2023-11-23T04:36:10.950Z","updated_at":"2023-11-25T10:11:13.889Z"},{"id":"1489507269","user_id":"1285381156962610170","name":"seen_whats_new_v2_modal","created_at":"2023-11-23T03:11:33.911Z","updated_at":"2023-11-23T03:11:33.911Z"},{"id":"1489621730","user_id":"1285381156962610170","name":"seen_widget_insert_onboarding_modal","created_at":"2023-11-23T04:36:10.870Z","updated_at":"2023-11-25T10:11:13.790Z"},{"id":"1298134507","user_id":"1285381156962610170","name":"use_numbers_for_opacity","created_at":"2023-09-17T23:25:49.236Z","updated_at":"2023-09-17T23:25:49.236Z"},{"id":"1298155935","user_id":"1285381156962610170","name":"variants_onboarded","created_at":"2023-09-18T00:07:21.414Z","updated_at":"2023-09-18T00:07:21.414Z"}],"user_data":{"id":"1285381156962610170","name":"الواقع - Reality","email":"<EMAIL>","handle":"الواقع - Reality","img_url":"https://s3-alpha.figma.com/profile/d3d8624e-b7b4-4ee4-89f8-700490121b23","created_at":"2023-09-17T23:25:49.039Z","email_validated_at":"2023-09-17T23:25:49.349Z","utc_offset":null,"profile":{"job_title":"software-development","usage_purpose":"For personal use","images":{"500_500":"https://s3-alpha.figma.com/profile/c11e49eb-8a8f-4a9e-8310-1c3cc73a5e40"}},"phone_number":null,"student_validated_at":null,"description":null,"plugin_publishing_blocked_at":null,"community_commenting_blocked_at":null,"community_blocked_at":null,"external_restricted_org_id":null,"dev_tokens":[],"oauth_tokens":[{"client_id":"FfRVot7Az90mbRIwCqyjwP","granted_at":"2023-11-25T08:59:47.227Z","name":"function12","logo":"https://s3-alpha.figma.com/oauth_img/fa1db0f8-3a9e-4f70-8dcb-16c03d2bf349","website":"https://function12.io"}],"realtime_token":"/me-1285381156962610170:**********:0:b9c18fad012e5dc6b2116d0f2e30ee430ab5b48f","realtime_token_inactive":"/user-inactive-1285381156962610170:**********:0:6313df4615872c9963954ad6c98c680bfbb0a9d0","two_factor_enabled":false,"two_factor_app_enabled":false,"google_sso_only":"2023-09-17T23:25:49.037Z","saml_sso_only":false,"experiment_seed":"21309","community_profile_id":null,"community_profile_handle":null,"community_beta_at":null,"locale":"en","keyboard_layout":"UNKNOWN","color_profile":"srgb","sharing_restricted":false,"cmty_buyer_tos_accepted_at":null,"stripe_account_status":"none","can_sell_on_community":null,"has_passed_visual_compliance":false,"stripe_connected_account_id":null,"screenreader_enabled":false,"community_purchasing_blocked_at":null,"onboarding_signals":{},"psxid":null,"experiment_assignments":[]},"smart_token":null,"launchdarkly_client_flags":{"bootstrapping-log-validation":false,"bot_bot_sev_call_should_show_migration_message":true,"deploy-frontend-ld-sdk-test-canary":false,"deploy_frontend_use_rum":true,"deploys_auto_start_lg100_after_livegraph":true,"deploys_sev_banner_for_dfc":"","deploys_show_new_commit_status":true,"deploys_use_selected_production_commit":true,"exp-simple-sharing-v3":false,"figment_client_sentry_errors":0,"force_client_reload":"no-bad-release-available","lg100_blackhole_query_shapes":{"5":1},"lg100_cache_coord_db_rate_limits":{"maxBurst":7500,"qps":3750},"lg100_cc_use_in_process_invalidator":false,"lg100_edge_blackhole_watermarks":"changestream3.postgresql_files,changestream3.postgresql2,changestream3.postgresql_checkpoints","lg100_edge_max_sessions_limit":300,"lg100_use_standalone_invalidator":true,"livegraph_client_poc_test":true,"livegraph_file_link_expiration_split":true,"livegraph_lq_resolver_return_updates_immediately":{},"livegraph_lq_send_raw_json":false,"livegraph_sync_query_ids":0,"multiplayer_chunked_file_load_pipelined":true,"multiplayer_validation_buffer_size_limit":100000,"multiplayer_validation_time_limit":1.5,"muse_openai_throttle_config":{"/api/muse/figjam/summary":{"per_user":{"per_hour":50,"per_day":500}}},"poc-client-side":true,"poll_interval_new_service_page":0,"show_exs_data_on_frontend":true,"$flagsState":{"bootstrapping-log-validation":{"reason":{"kind":"FALLTHROUGH","inExperiment":true},"version":6,"variation":1,"trackEvents":true,"trackReason":true},"bot_bot_sev_call_should_show_migration_message":{"variation":0},"deploy-frontend-ld-sdk-test-canary":{"variation":1},"deploy_frontend_use_rum":{"variation":0},"deploys_auto_start_lg100_after_livegraph":{"variation":0},"deploys_sev_banner_for_dfc":{"variation":0},"deploys_show_new_commit_status":{"variation":0},"deploys_use_selected_production_commit":{"variation":0},"exp-simple-sharing-v3":{"variation":1},"figment_client_sentry_errors":{"variation":0},"force_client_reload":{"variation":1},"lg100_blackhole_query_shapes":{"variation":2},"lg100_cache_coord_db_rate_limits":{"variation":7},"lg100_cc_use_in_process_invalidator":{"variation":1},"lg100_edge_blackhole_watermarks":{"variation":2},"lg100_edge_max_sessions_limit":{"variation":0},"lg100_use_standalone_invalidator":{"variation":0},"livegraph_client_poc_test":{"variation":0},"livegraph_file_link_expiration_split":{"variation":0},"livegraph_lq_resolver_return_updates_immediately":{"variation":0},"livegraph_lq_send_raw_json":{"variation":1},"livegraph_sync_query_ids":{"variation":0},"multiplayer_chunked_file_load_pipelined":{"variation":0},"multiplayer_validation_buffer_size_limit":{"variation":1},"multiplayer_validation_time_limit":{"variation":1},"muse_openai_throttle_config":{"variation":0},"poc-client-side":{"variation":0},"poll_interval_new_service_page":{"variation":0},"show_exs_data_on_frontend":{"variation":0}},"$valid":true},"idle_timeout_duration_in_secs":null,"idle_timeout_last_active_at":null,"idle_timeout_org_id":null,"idle_timeout_org_name":null,"feature_flags":{"dynamic_http_session_secrets":true,"permission_cache_enabled":true,"api_files_related_links_batch":true,"cookie_banners_community":true,"exp_idle_timeout":true,"figjam_create_section_toolbar":true,"ui_gb_vat_launch":true,"fullscreen_migrate_check_decode":true,"antispam_mod_review_form":true,"exp_notification_catfile":true,"ds_read_rt_desc_field":true,"prototype_temporary_stickyscroll":true,"ce_updated_measurement_lines":true,"exp_design_draft_file_edit_req":true,"teamplates":true,"favorites_sidebar_migration":true,"exp_required_signal_collection":true,"set_loading_smgcc":true,"desktop_push_notifs_win":true,"plugins_html_goblin_mode":true,"live_feedback_multiplayer_bell":true,"search_opensearch_read_folders":true,"composite_stream_buffer_uploads":true,"api_user_state_filter_teams":true,"ce_snap_frame_to_children":true,"fetch_lifecycles_feed":true,"media_copy_403_access":true,"exp_collective_upsells":true,"vat_tax_collection_in":true,"figjam_tab_order":true,"ui_switz_liech_vat_launch":true,"waf_captcha_modal":true,"application_distinct_ufr_edited":true,"api_file_batch_filter_by_perms":true,"cursor_bot":true,"figjam_container_interop":true,"statsig_server_use_data_store":true,"exp_community_search_landing":true,"r2c_multiplayer_nux":true,"figjam_embeds_v1":true,"recovery_mode":true,"search_file_v11_read":true,"label_dirty_from_initial_load":true,"desktop_push_notifs_mac":true,"plugins_iframe_csp_recommend":true,"log_db_events_batching":true,"ce_no_sync_layout_plugins":true,"vat_tax_collection_de":true,"multiplayer_props_ref_fields":true,"ps_favorites_team_id_writes":true,"figjam_ai_onboarding":true,"api_user_state_excl_folders":true,"locality_aware_new_blank_file":true,"perm_dbloader_replica_read":true,"antiabuse_did_verify":true,"search_disable_file_v10_writes":true,"pixie_log_requests":true,"lookup_all_teams":true,"org_cache_stripe_customer":true,"cookie_banners_auth":true,"branching_generate_v2_diffs":true,"default_teams_using_workspaces":true,"lookup_all_users":true,"ds_s2_dbl_write_parent_checkpt":true,"cmty_plugin_rdp_redesign":true,"cmty_m10n_subs_improvements":true,"autosave_lock_on_connect":true,"edit_scopes":true,"exp_fj_category_templates_shelf":true,"serve_codesplit_module_preload":true,"prototype_allow_invisible_source":true,"fbg_blocking_mobile_app_prompt":true,"figjam_differentiate_stickies":true,"notif_write_to_loadable_table":true,"multiplayer_ds_valid_symbol_id":true,"figjam_default_inserts_config":true,"active_edit_action_upgrade":true,"dsa_teams_in_org":true,"api_user_state_excl_orph_drafts":true,"ds_enforce_publish_validations":true,"presigned_post_uploads_proxy":true,"text_wrapping_improvements":true,"idle_timeout":true,"exp_standardize_pro_entrypoints":true,"error_boundary_logging":true,"csp_prod_admin_enforce":true,"autosave_slow_reconnect_replace":true,"xr_debounce_scope_slack":true,"figment_default_batch_shadowing":true,"curator_prioritization":true,"statsig_enable_server_bootstrap":true,"optimization_exposure_logging":true,"font_index_230207_figma_hand_2":true,"r2c_warning_chrome_version":true,"notif_write_to_sub_tables":true,"cmty_permissionsv2_can_view":true,"fastma":true,"embed_storage_access_api":true,"figjam_differentiate_stickies_v3":true,"launch_darkly_bootstrap_client":true,"livestore_client_metrics":true,"shared_fonts_client_perms_dryrun":true,"exp_move_drafts_v1":true,"fullscreen_edge_click_fix":true,"ps_team_drafts_creation":true,"fix_focus_on_blur":true,"use_stripe_tax_id":true,"figjam_shapes_galore":true,"mx_vat_id":true,"ds_snowy_moon_fix":true,"exp_ws_library_subscriptions":true,"xr_debounce_better_sharing":true,"livestore_fetch_optimizations":true,"editor_expose_save_local_copy":true,"full_file_validation_verbose":true,"stripe_3ds_allow_incomplete":true,"org_locality_migrations_temporal":true,"ee_paste_in_raster_mode":true,"exp_gen1_design_onboarding_async":true,"replica_api_recent_files":true,"api_user_state_excl_folders_glob":true,"ce_accessibility_image_alt_text":true,"prototype_hide_matching_noodles":true,"font_index_230120_figma_hand":true,"xrv:tracking_to_datadog":true,"full_rollout_cart_currencies_eur":true,"show_at_mention_invited_users":true,"statsig_client_enable_prefetch":true,"search_opensearch_read_teams":true,"ext_dont_close_iframe_plugins":true,"dt_lego_version_history":true,"cortex_service_enabled":true,"dse_library_v4_no_whitespace":true,"ds_vars_trav_sublayers_styles":true,"multiplayer_node_type_val":true,"antispam_arkose_google_one_tap":true,"cmty_m10n_vat_addr_collection":true,"prototype_target_frame_following":true,"statsig_client_country_timeouts":true,"figjam_longer_timer_render_tick":true,"ee_color_management_mirror":true,"ch_li_vat_id":true,"api_recent_files_v2":true,"r2c_onboard_figjam_editor":true,"r2c_onboard_figjam_viewer":true,"consistency_checker_execution":true,"autosave_logout_modal":true,"community_signup_redirect":true,"russia_edu_gate":true,"antispam_arkose_google_signup":true,"permissions_loading_behavior":true,"skip_instance_update_at_load_pix":true,"video_links_in_inspect_panel":true,"ce_view_only_mode_text_selection":true,"api_contacts_v2_at_mentions":true,"ee_linear_dodge_burn":true,"antispam_arkose":true,"variables_entrypoint_queries":true,"ds_component_props_mp_repair":true,"cmty_lego_figjam_plugins":true,"exp_figjam_starterkit_onboarding":true,"ext_codegen_defaults":true,"tsmer_cursors":true,"figjam_tables_reorder_many_spans":true,"vat_tax_collection_gb":true,"desktop_live_updates_beta_v2":true,"ce_snap_text_box":true,"r2c_libraries_workspace":true,"dt_lego_ext_csp":true,"dynamic_authn_mac_key":true,"file_analysis_frequency":true,"link_access_invites":true,"shared_with_you_push_to_redux":true,"jp_vat_launch":true,"search_opensearch_write_com_rscs":true,"search_opensearch_read_users":true,"exp_growth_h123_holdout_user":true,"dynamic_hmac_secret_key":true,"ps_benchmark_upgrade_prevention":true,"figjam_animate_tables":true,"editortype_reset_renderer":true,"ee_no_inval_frame_ancestors":true,"partnerstack_affiliate":true,"xr_retry_gets_5_times":true,"cmty_lib_data_model":true,"xr_debounce_digest_per_org":true,"multiplayer_props_asgmt_fields":true,"autogenerated_passwords":true,"report_periodic_metrics_on_load":true,"multiplayer_var_hidden_canv_val":true,"ee_accessibility_log_perf":true,"figjam_text_to_template":true,"sunflower_global_notifs":true,"perf_team_members_preload":true,"default_paid_status_frontend_v2":true,"variables":true,"ps_role_to_team_user_backfill":true,"serve_webpack_preload":true,"marketo_user_updates_with_jitter":true,"sunflower_link_share_tracking":true,"livegraph_active_file_users":true,"omit_drop_shadows_on_tlfs":true,"lookup_all_files":true,"publish_gen_widget_snapshot":true,"prototype_skip_mysterious_func":true,"search_opensearch_write_teams":true,"file_browser_migrate_favorites":true,"prototype_new_mouse_events":true,"org_trials_v1_ux":true,"hub_file_image_count_in_memory":true,"dynamic_zendesk_client_token":true,"ps_favorites_by_team":true,"validate_payment_address":true,"antiscraping_rate_limits":true,"ds_variables_sublayer_modes":true,"multiplayer_new_qg":true,"ds_workspace_team_default_modes":true,"admin_customer_setup":true,"jp_vat_id":true,"multiplayer_props_def_fields":true,"ice_cream_dynamic_secret_fetch":true,"exp_fj_engagement_holdout_h1_23":true,"ee_stroke_glyphs_separately":true,"branching_use_v2_diffs":true,"ui_de_vat_launch":true,"ee_fonts_from_name":true,"ext_codegen_strict_generate":true,"ee_text_tabbing":true,"favorites_all_the_favorites":true,"starter_files_dup_async":true,"xr_retry_jittered_exp_backoff":true,"viewer_arcs_spread_shadow":true,"block_double_logging_upgrades":true,"offline_file_renaming":true,"for_each_usage_in_styles":true,"edu_verification_form_update":true,"desktop_no_403_old_clients":true,"antispam_rules_engine_remote":true,"figjam_jamboard_easy_signup":true,"figjam_section_resize_to_fit":true,"prototype_combine_tlf_logic":true,"figjam_default_inserts_redis":true,"gen1_delay_tell_us_info":true,"r2c_mobile_redirect":true,"perm_load_plan_optimized":true,"exp_downgrade_org_vr_email":true,"invoice_created_billing_lock":true,"ee_truncate_mid_word":true,"multiplayer_anon_auth_check":true,"r2c_org_admin_members_onboarding":true,"exp_community_search_no_remixes":true,"i18n_dynamic_strings":true,"antiabuse_app_auth_rl":true,"comments_activity_emails":true,"dsa_write_to_weekly_insertion_v2":true,"folder_file_count_use_lg":true,"multiplayer_initialize_cache":true,"dynamic_zendesk_secrets":true,"ui_southafrica_vat_launch":true,"cmty_likes":true,"r2c_workflow_interop":true,"ee_compress_stroke_glyph_vn":true,"exp_figjam_active_time_spent":true,"cmty_m10n_auto_acpt_elig_creator":true,"partnerstack_affiliate_upgrade":true,"r2c_org_admin_survey":true,"cookie_banners_file_viewer":true,"replica_api_image_auth":true,"statsig_client_use_relay_proxy":true,"statsig_client_bootstrap_batched":true,"viewer_animation_easing_cache":true,"fetch_org_skip_user_query":true,"multiplayer_vars_have_a_col_val":true,"billing_promotions":true,"web_vitals_report":true,"exp_redirect_mobile_sign_up_v1":true,"ce_textarea_no_space":true,"edit_info_observer":true,"r2c_plugin_publish_invite":true,"antispam_akismet_update_filter":true,"launch_darkly_client_sdk":true,"ps_team_drafts_backfill":true,"check_access_token_validity":true,"chunked_size_5000":true,"image_export_rtgeneration_fix":true,"search_opensearch_read_com_rscs":true,"mobile_figjam_section_titles":true,"livegraph_sync_client_query_ids":true,"segment_weblogger_cutover":true,"plugins_approval_management":true,"fullscreen_fast_stroke_shadow":true,"emoji_14_sinatra_to_unicode":true,"exp_dps_enabled_for_orgs":true,"exp_edit_action_teams":true,"ps_ensure_users_have_plan":true,"xr_debounce_attachments":true,"r2c_org_admin_teams_onboarding":true,"desktop_live_updates_presenter":true,"r2c_local_component_drag_drop":true,"comments_media":true,"exp_oss_sales_upsell_v1":true,"exp_international_currencies_org":true,"figment_desktop_batch_fix":true,"ee_consistent_bullet_styles":true,"ds_slice_realtime_updates":true,"filter_fac_by_cursor_id":true,"periodic_team_file_count_repair":true,"livegraph_shim_file_dark_read":true,"gbo_new_customer_obp":true,"statsig_client_bootstrap_check":true,"autosave_new_file_ipc":true,"figjam_sticky_clustering":true,"serve_codesplit_preload":true,"figjam_copy_link_context_menu":true,"livegraph_shim_folder_full_read":true,"exp_pricing_clarity_v2":true,"render_tree_diff_page_switch":true,"report_periodic_metrics_in_proto":true,"ds_auto_repair_override_keys":true,"comments_activity_hot_file_write":true,"report_unmigrated_buffers":true,"media_parse_missing_shas":true,"ds_reset_deleted_state_default":true,"pixie_zod_schema_validation":true,"ce_no_export_highlight":true,"ds_cache_asset_keys":true,"free_tier_refresh_launch":true,"r2c_org_admin_selection_onbrdng":true,"gbo_enable_stubs":true,"exp_design_to_figjam_whats_new":true,"figjam_creation_dataset":true,"user_highest_plan":true,"exp_international_currencies":true,"api_folders_files_rt_token":true,"figjam_tables_meta_click":true,"ce_paste_and_match_style":true,"comments_media_in_emails":true,"russia_card_block":true,"community_feed_cached":true,"ce_tab_to_autocorrect_spelling":true,"cmty_m10n_new_netsuite_item_ids":true,"fullscreen_tile_tree_cache_fix":true,"send_file_directly":true,"cmty_upload_videos_admin_upnode":true,"block_upgrades_for_workshop_mode":true,"comments_conflicting_uuids_patch":true,"crash_on_cpp_exception":true,"version_history_incremental_load":true,"extension_request_flow":true,"ee_color_management":true,"batch_fps_logging":true,"email_incomplete_new_team":true,"r2c_onboard_editor_new_user_surv":true,"library_updates_by_page_if_incr":true,"figjam_text_to_visual":true,"delay_same_file_hyperlinks":true,"small_mobile_file_viewer_bundle":true,"ds_remove_redux_library_status":true,"full_rollout_cart_currencies_gbp":true,"figjam_org_facestamps":true,"update_team_netsuite_ids":true,"ce_font_size_previews":true,"i18n_redis_cache":true,"figjam_vabettadapur":true,"curator_internals_mvp":true,"antifraud_cmty_m10n_buy_rl":true,"cmty_rating_frontload_reminders":true,"dsa_increase_num_partitions":true,"prototype_videos_v2":true,"antiabuse_create_oauth_token_rl":true,"faster_fps_logging":true,"vat_tax_collection_fr":true,"cmty_hide_remixes_in_publish":true,"realtime_check_invite_flag":true,"figjam_quick_add_spacing_pattern":true,"mobile_prompt_sidebar_modal":true,"filter_shas_application_join":true,"search_folder_v4_write":true,"sendgrid_user_deletion":true,"fullscreen_pixel_pan":true,"r2c_upsell_libraries":true,"antiabuse_ugc_scan":true,"xrv:tracking":true,"hub_file_cross_origin_viewer":true,"hide_lego_plugin_run_counts":true,"rcs_community_track":true,"ui_eu_vat_launch":true,"figjam_video":true,"exp_use_accessed_page_data_only":true,"mx_ch_za_vat_launch":true,"exp_pro_vr_migrate_org":true,"starter_edit_link_modal":true,"fullscreen_del_inst_sublayers":true,"dt_playground_bubbled_props":true,"tax_id_status_admin_ui":true,"exp_figjam_cta_placeholder_text2":true,"antiabuse_did_set_authed":true,"vat_save_gb":true,"multiplayer_var_gte_one_mode_val":true,"exp_pro_vr_email":true,"activity_debounce_mins":true,"prototype_url_node_id":true,"ipad_fix_korean_input":true,"api_user_state_debounce":true,"string_size_limit_256mb":true,"batch_delete_team_members":true,"figment_batch_metadata":true,"comments_fading_pins":true,"resize_rate_limit":true,"r2c_onboard_file_browser":true,"cmty_library_modal_refresh":true,"viewer_ctg_equal_within_bounds":true,"dynamic_unsubscribe_salt":true,"at_mention_out_of_slack_app":true,"cmty_saves_search":true,"exp_org_synchronous_emails":true,"dsa_use_partition_start_time":true,"exp_workspace_customization":true,"ee_color_management_circle_p3":true,"search_teamplates_v3":true,"vat_save_de":true,"file_load_streaming_compression":true,"reconnect_on_visibility_change":true,"search_fa_ks_send_to_background":true,"statsig_server_error_sampling":true,"ds_publish_handle_create_race":true,"statsig_enable_relay_proxy_route":true,"stripe_3ds_change_payment":true,"ds_variables_color_scopes_filter":true,"ee_masktype_plugins":true,"r2c_open_playground":true,"audit_joins":true,"offline_detection_shadow_logging":true,"log_db_event":true,"community_search_and_browse":true,"ds_block_unpublished_symbol_reqs":true,"share_modal_async_autocomplete":true,"github_json_array_parser":true,"ee_color_management_mobile":true,"comments_on_table_cells":true,"exp_pro_vr_double_write_org":true,"dynamic_sendgrid_api_secret":true,"exp_figjam_meeting_tools_panel":true,"ee_color_management_images":true,"ee_rulers_and_guides_right_click":true,"ee_color_management_image":true,"search_opensearch_write_folders":true,"file_load_streaming_buffer_size":true,"community_search_validate_query":true,"ds_server_side_publishing":true,"realtime_enable_allowlist_check":true,"ds_limit_go_to_source":true,"dsa_write_lib_org_to_actions":true,"multiplayer_preload_doc":true,"search_folder_v4_read":true,"share_as_dev_mode":true,"exp_fj_draft_file_edit_req_v2":true,"dt_edit_info_fixes":true,"nux_dynamic_preview":true,"lg_org_teams_with_livestore":true,"replica_files":true,"react_18_entrypoints":true,"r2c_move_drafts_nudge_v2":true,"authn_write_first_pass_cookies":true,"font_skip_inter":true,"widgets_hardcode_cocreators":true,"api_elevated_scopes":true,"figjam_default_inserts_hubfile":true,"multiplayer_track_unique_guids":true,"block_unapproved_guest_thumbnail":true,"jamgpt":true,"stripe_3ds_pro":true,"tooltips_plus_ui_enabled":true,"file_browser_organize_survey":true,"figjam_perf_permission_state":true,"ps_fav_rsrcs_team_id_backfill":true,"figjam_user_journey_with_tables":true,"admin_settings_layout_revamp":true,"multiplayer_write_node_field_map":true,"ce_font_shared_previews_v3":true,"r2c_file_browser_find_files":true,"community_image_resizer_job":true,"pro_vr_redirect_endpoint":true,"prototype_new_mouse_perf":true,"cmty_disable_vat_for_india":true,"mouse_active_fps_logging":true,"ps_lock_all_plans_during_org_op":true,"file_browser_find_files_survey":true,"xr_debounce_beta_badging":true,"replica_api_file_new":true,"figjam_resize_shapes_bugfix":true,"figjam_perf_node_type_observer":true,"exp_mobile_reply_upsell":true,"permissions_load_cache_enabled":true,"prototype_fewer_starting_points":true,"antiabuse_did_set_unauthed":true,"ext_keep_plugin_running":true,"figjam_delightful_flourishes":true,"at_mention_quick_reply_on_sdui":true,"fullscreen_fast_rounded_rect":true,"tooltips_plus_statsig_enabled":true,"multiplayer_migration_s2a_reads":true,"pro_team_checkout_updated_terms":true,"cmty_profile_with_livegraph":true,"display_3ds_frontend":true,"r2c_pro_cart_abandon_survey":true,"ds_s2_read_new_fields":true,"mp_async_file_seen_state":true,"dynamic_zendesk_webhook_api_key":true,"search_dsa_large_batches":true,"r2c_org_cart_abandon_survey":true,"branching_skip_symb_update_undo":true,"ui_mexico_vat_launch":true,"fully_clear_fsc":true,"cmty_lego_plugins":true,"ee_system_theme_default":true,"ipad_fix_dictation":true,"search_swallow_fa_rs_logs":true,"figjam_connector_tool_click":true,"cmty_m10n_seller_tax_info":true,"xr_get_defer_foreground":true,"ee_accessibility_inline_input":true,"user_theme_preference":true,"dsa_write_lib_org_to_usages":true,"dt_lego_suggest_variables":true,"figjam_text_to_visual_gantt_v2":true,"msft_teams_share_in_meeting":true,"internal_profile_v5_index_read":true,"crash_on_oom":true,"exp_figjam_simulated_use_cases_5":true,"default_paid_status_all_tiers":true,"safari_mac_fix_korean_input":true,"cmty_saves":true,"multiplayer_migration_text_sot":true,"comments_stable_path":true,"use_file_livestore_cache":true,"exp_dps_enabled_for_pro_teams":true,"exp_community_universal_posting":true,"multiplayer_migration_s2a_reads2":true,"api_user_state_foreground":true,"figjam_fix_drawing_drag_handles":true,"live_feedback_multiplayer_menu":true,"prototype_async_font_loading":true,"dt_lego_ext":true,"ps_favorites_backfills":true,"exp_serve_resized_images":true,"serve_esm_output":true,"ee_hsl_hue_epsilon_equality":true,"context_menus_focus_fix":true,"log_top_search_results_fig_files":true,"media_return_missing_shas":true,"ui_fr_vat_launch":true,"ds_variables_copy_properties":true,"ps_planless_users_backfill":true,"livegraph_shim_enabled":true,"file_browser_top_banner":true,"exp_edit_action_orgs":true,"ds_remap_unnatural_style_keys":true,"fbg_org_browse_view_reskin":true,"cmty_profile_search_followers":true,"ee_write_ignore_leading_trim":true,"xr_debounce_edit_v2":true,"marketo_user_upsert_logging":true,"antispam_mod_custom_fields":true,"save_as_improvements":true,"ds_asset_panel_flyout":true,"desktop_ps_new_tab_reload":true,"favorites_clean_up_job":true,"dt_dev_mode_discovery_modal":true,"disable_local_web":true,"statsig_client_track_eval_fgmt":true,"prototype_adv_proto_plugin_api":true,"ce_no_nan_size_overrides":true,"extension_analytics_org":true,"full_rollout_cart_currencies_jpy":true,"antiabuse_voice_rl":true,"vat_show_example_format":true,"r2c_promo_code_select_team":true,"billing_promotions_refund_unused":true,"search_no_oldest_editor":true,"plugins_incremental_loading":true,"antispam_mod_prod_area":true,"image_io_worker_bundle":true,"statsig_client_sdk_enabled":true,"r2c_multiplayer_spotlight_nudge":true,"prototype_open_url_in_new_tab":true,"segment_disable_in_frontend_v2":true,"org_team_csv_export":true,"frontend_autogen_passwords":true,"dt_log_layer_click":true,"fullscreen_fast_shaded_rect":true,"cmty_m10n_eligibility_copy":true,"desktop_haptics_experimental":true,"multiplayer_new_qg_editors":true,"show_account_balance_banner":true,"display_local_org_prices":true,"exp_edu_onboarding_acc_creation":true,"desktop_live_updates_at_mention":true,"google_profile_pic_on_signup":true,"figjam_paste_into_images":true,"viewer_fdt_depth_increase":true,"r2c_workshop_pointer":true,"cmty_rdp_signup_figma_try":true,"i18n_sample_string_datadog":true,"workspace_colors_on_tiles":true,"multiplayer_viewers_write_plugin":true,"prototype_enforcer_sentry":true,"i18n_sample_string_sentry_errors":true,"dagster_use_extension_s3_bucket":true,"allow_org_disable_cursor_chat":true,"cmty_m10n_price_change_exec":true,"ce_accessibility_contenteditable":true,"multiplayer_checkpoints_180":true,"dt_lego_redirect_old":true,"antispam_rules_hf_cover_img":true,"comments_full_emoji_reactions":true,"statsig_server_sdk_enabled":true,"prototype_shift_to_hide":true,"exp_new_user_survey_1a_biz":true,"dt_lego_config_wizard":true,"stripe_3ds_orgs":true,"org_public_access_link":true,"vat_save_eu":true,"viewer_clear_workaround_disable":true,"statsig_client_bootstrap":true,"ee_unsanitized_png":true,"cmty_lib_nux":true,"edu_offboarding_experience":true,"cmty_browse_codegen_plugins":true,"workspace_membership_log":true,"emscripten_4gb_webgl_workaround":true,"dsa_restrict_to_design_only":true,"ce_stack_copy_paste_properties":true,"exp_memory_usage_indicators_v1":true,"figjam_ipad_banner":true,"penalize_inactive_contact_search":true,"antiabuse_create_dev_token_rl":true,"figjam_default_inserts_allowlist":true,"vat_save_fr":true,"fbg_facelift_beta":true,"cmty_m10n_consumer_vat_id":true,"crash_on_bindings_exception":true,"multiplayer_vars_d_all_modes_val":true,"use_replica_for_mp_checkpoint":true,"figment_default_batch":true,"pixie_nsjail":true,"figjam_diagramming_snapping":true,"intl_browser_deprecation_banner":true,"cmty_m10n_auto_creator_elig":true,"ps_team_root_to_project_backfill":true,"search_opensearch_write_files":true,"fullscreen_multiple_bubbles":true,"figma_like_the_pros_banner":true,"search_disable_fa_redis_set":true,"replica_api_file_metadata":true,"fullscreen_noodle_improvements":true,"figjam_handle_hanging_space":true,"figjam_paid_viewer_onboarding":true,"internal_profile_v5_index_write":true,"figjam_mindmaps_behavior_enable":true,"ee_easy_exit_outline_mode":true,"shared_fonts_client_side_perms":true,"cmty_resources_in_paid_spaces":true,"comments_media_proxy_uploads":true,"gbo_ui_billing_skip_true_up":true,"batched_hard_deletion":true,"check_higher_buffer_version":true,"jamgpt_recents_pin":true,"za_vat_id":true,"edu_onboarding_grace_period":true,"embedding_chunk_text":true,"scene_events_for_auto_observer":true,"wls_for_billing_groups_beta":true,"cmty_m10n_gen_promo_codes":true,"user_theme_preference_on_body":true,"figjam_media_overlays_v2":true,"i18n_english_entry_dictionary":true,"tsmer_tile_renderers":true,"payment_as_price_incr_notif_dep":true,"search_increase_client_timeout":true,"embedding_figjam_templates":true,"ce_font_style_previews":true,"antispam_rules_engine":true,"teamplates_use_in_new_file":true,"pixie_worker_strict_json":true,"admin_billing":true,"code_block_bash":true,"admin_org_reduced_fetching":true,"exp_expiring_public_links":true,"ee_shaping_cross_boundaries":true,"full_rollout_cart_currencies_cad":true,"search_timeout_error":true,"prototype_multi_actions_plugin":true,"frontend_sentry_errors":true,"exp_fbi_community_entrypoint":true,"i18n_auth_de":true,"billing_rl_exempt_test_users":true,"ux_figjam_feedback_survey":true,"edu_offboarding_jobs":true,"thumbnails_for_thumbnails":true,"cmty_m10n_skip_paid_file_review":true,"file_move_show_all_resources":true,"exp_share_options":true,"cmty_badges":true,"sunflower_ux":true,"ps_file_permissions":true,"ee_svg_no_implicit_Z":true,"org_admin_lg_dark_read":true,"ee_accessibility_dirty_nodes":true,"penalize_non_recent_contacts_++":true,"dynamic_figment_admin_github_key":true,"ps_exclude_team_drafts_search":true,"multiplayer_journaled_reconnect":true,"ds_s2_validate_v40":true,"emoji_ts_download":true,"search_cache_user_params":true,"color_wheel_integration":true,"ee_color_management_curator":true,"ce_font_shared_previews_cldfrnt":true,"prototype_enforcer_visitor":true,"widgets_option_to_update":true,"xr_debounce_creation_v3":true,"search_batch_enqueue_analytics":true,"csp_prod_enforce":true,"mark_recents_async":true,"dt_improved_section_snapping":true,"figjam_ai_modal_lexical":true,"nux_edu_fixes":true,"dynamic_stripe_endpoint_secret":true,"skip_instance_update_at_load":true,"figjam_3p_file_importer_eraser":true,"ee_cmd_f_static_size_estimator":true,"comments_no_email_replies":true,"ufr_delete_all":true,"r2c_team_join_link_redemption":true,"figjam_rotation_five_degrees":true,"desktop_haptics":true,"widgets_in_figma_onboarding":true,"exp_figjam_expanded_placeholders":true,"dynamic_stripe_api_secret":true,"figjam_canvas_mention_readstatus":true,"font_index_220528":true,"contacts_remove_seen_state_users":true,"tsmer_metrics":true,"invalidate_images_only":true,"exp_figjam_ia_refresh":true,"figjam_semantic_html":true,"file_public_link_expiration":true,"spotlight_improvements":true,"multiplayer_signal_validation":true,"multiplayer_checkpoints_75":true,"cmty_profile_image_verification":true,"statsig_client_use_cust_provider":true,"exp_ip_account_restriction":true,"lookup_all_folders":true,"prototype_multi_path_paywall":true,"early_js_no_module":true,"autogen_controls_backfill":true,"ps_benchmark_counts":true,"widgets_validation_throws":true,"font_index_emoji":true,"admin_salesforce_button":true,"ee_paste_rasters_into_fills":true,"replica_api_user_state":true,"desktop_enterprise_dl_link_macos":true,"figjam_summarization":true,"github_secret_scanning_email":true,"figjam_jamboard_offer":true,"desktop_web_logout":true,"ce_font_family_previews":true,"r2c_org_select_license_group":true,"ee_scale_tool":true,"community_hub_likes_on_lg":true,"ps_team_user_backfill":true,"cmty_m10n_annual_discounts":true,"ee_accessibility_inline_css":true,"prototype_always_allow_restart":true,"cmty_m10n_promo_code_issue":true,"config_2023_features":true,"fullscreen_pen_tool_memory":true,"antiabuse_comment_rl":true,"multiplayer_preload_doc_editor":true,"plugins_lg_codegen":true,"i18n_web_locales":true,"presigned_post_hub_file_image":true,"livegraph_shim_folder_dark_read":true,"exp_mobile_prompt_mobile_web":true,"fetch_lifecycles_mobile_feed":true,"cookie_banners_prototype":true,"cmty_img_garbage_collection":true,"thumbnail_guid_force_checkpoint":true,"desktop_light_tabs":true,"text_data_derived_cleanup_read":true,"figjam_inline_quick_add":true,"ds_run_publish_validations":true,"search_opensearch_run_migrations":true,"ee_scale_tool_reset":true,"ext_dont_clear_allowlist":true,"multiplayer_var_aliases_val":true,"ps_benchmark_draft_owner_upgrade":true,"editor_onboarded_curator":true,"figjam_ngima_stickers":true,"community_serve_resized_images":true,"plugins_security_form":true,"xr_retry_429s":true,"figjam_default_inserts_refactor":true,"replica_api_roles":true,"figjam_3p_file_importer":true,"gbo_use_org_billing_periods":true,"spotlight_nominations":true,"cursor_chat_fps_logging":true,"locked_fb_screen_view":true,"ee_color_management_viewer":true,"sunflower_livegraph_pagination":true,"tsmer_tile_renderers_in_design":true,"use_cards_in_invite_emails":true,"csv_email_template_refactor":true,"ee_color_management_legacy_p3":true,"figjam_mr_toolbar_ga":true,"xr_debounce_lockup_v3":true,"api_user_state_metrics":true,"notif_switch_thumbnail":true,"record_plugin_data_size":true,"dt_lego_copy_native_code":true,"figjam_disable_crop_snap":true,"batched_file_comment_notifs":true,"update_org_netsuite_ids":true,"figjam_connector_text_overlap":true,"antiabuse_rl_global_tfa":true,"templ_copy_file_create_logging":true,"gbo_multiyear_contracts":true,"authn_clean_up_tokens_on_logout":true,"sprig_enabled":true,"prototype_sticky_scrolling":true,"report_null_shared_ptr":true,"ds_filter_invalid_overrides_vcm":true,"throw_overdue_payement_error":true,"multitouch_in_fullscreen":true,"cmty_no_index_search_results":true,"search_fa_receive_single_msg":true,"exp_new_user_survey_1a_personal":true,"ps_folder_sub_fav_rsrc_backfill":true,"org_trials_v1":true,"api_create_file":true,"font_index_220803":true,"xr_debounce_scope":true,"team_root_folder_conversion":true,"user_file_recents_insert_uuid":true,"ce_text_selection_view_only":true,"exp_move_drafts_v2":true,"exp_early_bundles_v1":true,"sinatra_enable_nsjail":true,"figjam_default_inserts_preload":true,"lg_early_connection":true,"antiabuse_enable_magic_link":true,"multiplayer_scene_val_timing":true,"viewer_log_frame_distributions":true,"dsa_write_null_teams_to_ca2":true,"prototype_lib_own_manifest":true,"dev_mode_multi_select":true,"exp_cpp_memory_limit_android":true,"comments_digest_for_participants":true,"exp_ws_public_link_controls":true,"antiabuse_file_rl":true,"prototype_state_management":true,"pixie_stream_s3_objects":true,"vat_tax_collection_eu":true,"plugins_granular_allowlist":true,"community_hub_image_inspection":true,"workspace_members_list":true,"ds_align_with_instances":true,"r2c_file_browser_organize":true,"multiplayer_migrate_pending":true,"conveyor_sso_enabled":true,"frontend_sentry_cpp_location":true,"branching_refresh_schema_change":true,"can_view_refresh_page_check":true,"version_history_select_tool":true,"cache_image_locality_in_redis":true,"admin_redact_realtime_tokens":true,"plugins_zod_validation":true,"alternate_payments":true,"exp_growth_h123_holdout_team":true,"cmty_starter_templates_override":true,"i18n_auth_fr":true,"dynamic_zendesk_sso_key":true,"cmty_hf_details_page_redesign":true,"api_require_authenticated":true,"exp_saml_encryption":true,"scim_expected_error_code":true,"r2c_optimized_edit_button":true,"batch_subscribe_to_used_assets":true,"dynamic_file_auth_enc_key":true,"dt_compare_changes_improvements":true,"dynamic_twilio_secret":true,"exp_extension_trust":true,"r2c_figjam_ux_feedback_surv":true,"exp_figjam_onboarding_make_tmpl":true,"sunflower_writes":true,"exp_dse_asset_panel_improvements":true,"mobile_sign_up_redirect":true,"antiabuse_community_comment_rl":true,"defer_scene_attach_events":true,"gbo_org_billing_period_backfill":true,"block_unmigrated_buffers":true,"sharing_attribution_context_key":true,"activerecord_cache":true,"invoice_seat_reports_job_create":true,"desktop_color_management_remove":true,"pdf_export_force_load":true,"r2c_universal_upgrade":true,"r2c_org_admin_lg_onboarding":true,"twilio_alphanumeric_gb":true,"org_sales_assisted_status":true,"password_protected_embeds":true,"active_edit_action":true,"ps_team_drafts_backfill_writes":true,"replica_api_folders_files":true,"figjam_default_collage_v2":true,"antispam_add_feedback_id":true,"realtime_library_file_only":true,"check_folder_perms_user_state":true,"exp_billing_groups":true,"dagster_use_growth_s3_bucket":true,"antiabuse_sessions_rl":true,"multiplayer_para_indent_val":true,"defer_invisible_children":true,"dt_lego_viewer_edit_canvas":true,"ui_in_vat_launch":true,"exp_figjam_whats_new_v2":true,"preload_recent_files":true,"block_ru_billing_address":true,"chunked_file_load":true,"cmty_min_serialize_playground":true,"prototype_user_change_throttling":true,"partnerstack_affiliate_figjam":true,"reduce_derived_data":true,"offline_file_tiles":true,"search_opensearch_write_users":true,"search_file_v11_write":true,"notif_filter_discarded":true,"invoice_seat_reports":true,"disable_num_teams":true,"ee_color_management_force_canvas":true,"stripe_3ds":true,"replica_api_recent_prototypes":true,"activity_logs_delete_all":true,"paginated_drafts":true,"plugins_csp_dev_allowed_domains":true,"billing_enforce_new_lock_request":true,"page_change_new_ux":true,"pro_block_unexpected_editors":true,"multiplayer_write_to_cache":true,"ee_paste_in_gradient_mode":true,"dev_mode_text_selection":true,"ce_cmd_f_memoize":true,"favorites_all_the_favs_writes":true,"antispam_rekognition_rules":true,"ds_no_reused_style_keys":true,"internal_test_data_api":true,"ee_scale_missing_fonts":true,"ce_font_pup":true,"collab_h2_2022_livestream_banner":true,"figjam_thicc_markers":true,"ds_variables_authoring_resize":true,"application_distinct_ufr_viewed":true,"ds_corner_radius_modal":true,"ps_permissions":true,"exp_mobile_web_nux":true,"gbo_ui_billing_periods":true,"plugins_allowlist_check":true,"r2c_move_drafts_nudge":true,"vat_save_in":true,"data_atoms_explorations":true,"exp_figjam_specific_onboarding":true,"realtime_stop_folder_msg":true,"statsig_disable_statsig_api":true,"sub_components_filter_by_permsV2":true,"ai_opt_out":true,"cmty_saves_like_migration":true,"figjam_shapes_galore_2nd_batch":true,"figjam_stickable_rotate_scale":true,"share_with_message_email":true,"admin_use_alb_for_authz":true,"figjam_connector_center_magnet":true},"i18n_desktop_version_support":{"ja":70},"statsig_bootstrap_values":{"feature_gates":{"3224217902":{"value":false,"group_name":"prod users","id_type":"userID","name":"3224217902","rule_id":"3xfTjYI8t4fSAmuXRyDser","secondary_exposures":[]},"723172485":{"value":false,"group_name":"default","id_type":"userID","name":"723172485","rule_id":"default","secondary_exposures":[]},"1761203669":{"value":false,"group_name":"default","id_type":"userID","name":"1761203669","rule_id":"default","secondary_exposures":[]},"987239917":{"value":true,"group_name":"Everyone else","id_type":"userID","name":"987239917","rule_id":"6hBvIVMiHlosAOC3bU6FMx","secondary_exposures":[]},"2466601638":{"value":true,"group_name":"Pass Everyone","id_type":"userID","name":"2466601638","rule_id":"oiRxqLNDErXnhMp5donHx","secondary_exposures":[{"gate":"segment:cmty_holdout_excluded_users","gateValue":"false","ruleID":"default"}]},"3676727071":{"value":false,"group_name":"default","id_type":"userID","name":"3676727071","rule_id":"default","secondary_exposures":[]},"2268502330":{"value":false,"group_name":"default","id_type":"userID","name":"2268502330","rule_id":"default","secondary_exposures":[]},"2582712000":{"value":false,"group_name":"default","id_type":"stableID","name":"2582712000","rule_id":"default","secondary_exposures":[]},"2298055341":{"value":true,"group_name":"disabled","id_type":"userID","name":"2298055341","rule_id":"disabled","secondary_exposures":[]},"3920754862":{"value":false,"group_name":"default","id_type":"userID","name":"3920754862","rule_id":"disabled","secondary_exposures":[]},"2793464152":{"value":true,"group_name":"disabled","id_type":"userID","name":"2793464152","rule_id":"disabled","secondary_exposures":[]},"1841510287":{"value":false,"group_name":"default","id_type":"userID","name":"1841510287","rule_id":"default","secondary_exposures":[]},"3084116847":{"value":false,"group_name":"default","id_type":"userID","name":"3084116847","rule_id":"default","secondary_exposures":[]},"3602492775":{"value":true,"group_name":"disabled","id_type":"userID","name":"3602492775","rule_id":"disabled","secondary_exposures":[]},"3324977977":{"value":true,"group_name":"50 pct on user id","id_type":"userID","name":"3324977977","rule_id":"6QlowMFoAemvvuufOcxNxK","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"}]},"697816188":{"value":true,"group_name":"att1","id_type":"userID","name":"697816188","rule_id":"1JcXaERW0Ne6xWgPhJxIqK","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"}]},"906818510":{"value":false,"group_name":"default","id_type":"userID","name":"906818510","rule_id":"default","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"}]},"2494767547":{"value":true,"group_name":"att1","id_type":"userID","name":"2494767547","rule_id":"6S4sns8dIIdK55nJg6ZR0w","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"}]},"4088872814":{"value":true,"group_name":"exp gate","id_type":"userID","name":"4088872814","rule_id":"2OgdoMif7AjSPBcG51G2es","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"},{"gate":"20_rules_with_both_and_or","gateValue":"true","ruleID":"6QlowMFoAemvvuufOcxNxK"}]},"777735791":{"value":false,"group_name":"default","id_type":"userID","name":"777735791","rule_id":"default","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"}]},"4227937580":{"value":true,"group_name":"production","id_type":"userID","name":"4227937580","rule_id":"5rGlj71zhtfAMvpgO4O1eR","secondary_exposures":[]}},"dynamic_configs":{"402158309":{"value":{},"group":"default","group_name":"default","id_type":"userID","is_device_based":false,"name":"402158309","rule_id":"default","secondary_exposures":[]},"2923904119":{"value":{},"group":"default","group_name":"default","id_type":"userID","is_device_based":false,"name":"2923904119","rule_id":"default","secondary_exposures":[]},"3204028987":{"value":{"aditya_test_experiment_2":1712449451,"stateful_experiment_staging_test_v1":1691071200},"group":"default","group_name":"default","id_type":"userID","is_device_based":false,"name":"3204028987","rule_id":"default","secondary_exposures":[]},"207093990":{"value":{},"group":"default","group_name":"default","id_type":"userID","is_device_based":false,"name":"207093990","rule_id":"default","secondary_exposures":[]},"7009907":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"7009907","rule_id":"prestart","secondary_exposures":[]},"2177007567":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2177007567","rule_id":"prestart","secondary_exposures":[]},"1930359960":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1930359960","rule_id":"prestart","secondary_exposures":[]},"1021413831":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1021413831","rule_id":"prestart","secondary_exposures":[]},"2404015732":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2404015732","rule_id":"prestart","secondary_exposures":[]},"3829153554":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3829153554","rule_id":"prestart","secondary_exposures":[]},"1002457104":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1002457104","rule_id":"prestart","secondary_exposures":[]},"3971629841":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3971629841","rule_id":"prestart","secondary_exposures":[]},"2816304487":{"value":{"use_popular_on_figma":false},"group":"1sUUvl5mGJlDVSFCX2MhD1","group_name":"Control","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2816304487","rule_id":"1sUUvl5mGJlDVSFCX2MhD1","secondary_exposures":[{"gate":"community_signups_holdout_h2_2023","gateValue":"false","ruleID":"4OWJOUBE713T6GBGGi0mhz:5.00:1"}]},"216019307":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"216019307","rule_id":"prestart","secondary_exposures":[]},"3539997371":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3539997371","rule_id":"prestart","secondary_exposures":[]},"2332014226":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2332014226","rule_id":"prestart","secondary_exposures":[]},"3058847842":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3058847842","rule_id":"prestart","secondary_exposures":[]},"105521267":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"orgID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"105521267","rule_id":"prestart","secondary_exposures":[]},"2398289485":{"value":{"use_start_for_free_text":true,"use_blue_background":true},"group":"6gjgSwHdfqg2H5Q6tnupma","group_name":"Start for Free Blue","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2398289485","rule_id":"6gjgSwHdfqg2H5Q6tnupma","secondary_exposures":[{"gate":"community_signups_holdout_h2_2023","gateValue":"false","ruleID":"4OWJOUBE713T6GBGGi0mhz:5.00:1"}]},"1162995848":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1162995848","rule_id":"prestart","secondary_exposures":[{"gate":"community_signups_holdout_h2_2023","gateValue":"false","ruleID":"4OWJOUBE713T6GBGGi0mhz:5.00:1"}]},"3392717028":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3392717028","rule_id":"prestart","secondary_exposures":[{"gate":"2023_h2_monetization_holdout","gateValue":"false","ruleID":"7e3PfmLObjnArYYQVvYhQL:8.00:1"}]},"747264305":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"747264305","rule_id":"prestart","secondary_exposures":[{"gate":"2023_h2_monetization_holdout","gateValue":"false","ruleID":"7e3PfmLObjnArYYQVvYhQL:8.00:1"}]},"519419060":{"value":{"use_popular_on_figma":true},"group":"1zwaL6WbtACbetRGLFGemf","group_name":"Test","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"519419060","rule_id":"1zwaL6WbtACbetRGLFGemf","secondary_exposures":[{"gate":"community_signups_holdout_h2_2023","gateValue":"false","ruleID":"4OWJOUBE713T6GBGGi0mhz:5.00:1"}]},"3091572107":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3091572107","rule_id":"prestart","secondary_exposures":[]},"2315672783":{"value":{"omnicreate_variants":"","onboarding_variants":""},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":["onboarding_variants"],"name":"2315672783","rule_id":"prestart","secondary_exposures":[]},"2420969914":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2420969914","rule_id":"prestart","secondary_exposures":[{"gate":"activation_holdout_h2_2023","gateValue":"false","ruleID":"1aPAHL0FFBz4XddlxZM02B:8.00:1"}]},"3148502878":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3148502878","rule_id":"prestart","secondary_exposures":[]},"3148506352":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3148506352","rule_id":"prestart","secondary_exposures":[]},"1094357923":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1094357923","rule_id":"prestart","secondary_exposures":[{"gate":"2023_h2_monetization_holdout","gateValue":"false","ruleID":"7e3PfmLObjnArYYQVvYhQL:8.00:1"}]},"437673187":{"value":{"omnicreate_variants":"","onboarding_variants":""},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":["omnicreate_variants"],"name":"437673187","rule_id":"prestart","secondary_exposures":[]},"4042639221":{"value":{"group_name":"control"},"group":"73qaSurmFiQR18epx7Omit","group_name":"Control","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"4042639221","rule_id":"73qaSurmFiQR18epx7Omit","secondary_exposures":[]},"1234751134":{"value":{"enabled":false},"group":"2UA5XExA0Z0kkPDd2pihu9","group_name":"Control","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"1234751134","rule_id":"2UA5XExA0Z0kkPDd2pihu9","secondary_exposures":[{"gate":"community_signups_holdout_h2_2023","gateValue":"false","ruleID":"4OWJOUBE713T6GBGGi0mhz:5.00:1"}]},"4267930769":{"value":{"topCtaActions":false},"group":"EwBVdkDPz55ZejxqrXfHj","group_name":"Control","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"4267930769","rule_id":"EwBVdkDPz55ZejxqrXfHj","secondary_exposures":[]},"3947144023":{"value":{"ctaText":"Sign up for Figma"},"group":"56GYb4VV0hbMr8mFfByZ6H","group_name":"Control","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3947144023","rule_id":"56GYb4VV0hbMr8mFfByZ6H","secondary_exposures":[]},"3663985807":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3663985807","rule_id":"prestart","secondary_exposures":[]},"896736346":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"896736346","rule_id":"prestart","secondary_exposures":[]},"60569260":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"60569260","rule_id":"prestart","secondary_exposures":[]},"3685004097":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3685004097","rule_id":"prestart","secondary_exposures":[]},"1306645092":{"value":{"enableProductHeroVariant":false,"displayJumboCta":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":["displayJumboCta"],"name":"1306645092","rule_id":"launchedGroup","secondary_exposures":[]},"3049996912":{"value":{"enableProductHeroVariant":false,"displayJumboCta":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":["enableProductHeroVariant"],"name":"3049996912","rule_id":"launchedGroup","secondary_exposures":[]},"3612036889":{"value":{"show_questions":true},"group":"2YZ5c9TaO3nQQMpppU36Vs","group_name":"Test","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3612036889","rule_id":"2YZ5c9TaO3nQQMpppU36Vs","secondary_exposures":[{"gate":"segment:override_lguo","gateValue":"false","ruleID":"default"},{"gate":"activation_holdout_h2_2023","gateValue":"false","ruleID":"1aPAHL0FFBz4XddlxZM02B:8.00:1"}]},"2736223481":{"value":{"isVariant":false},"group":"5WWPeFyfHA3yWK5n37iDSr","group_name":"Control","id_type":"teamID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2736223481","rule_id":"5WWPeFyfHA3yWK5n37iDSr","secondary_exposures":[]},"1817494519":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"teamID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1817494519","rule_id":"prestart","secondary_exposures":[]},"2265480317":{"value":{},"group":"layerAssignment","group_name":"Layer Assignment","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":true,"name":"2265480317","rule_id":"layerAssignment","secondary_exposures":[]},"2872521232":{"value":{"showAnchorLink":true,"showCta":false},"group":"prestart","group_name":"Unstarted","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":[],"name":"2872521232","rule_id":"prestart","secondary_exposures":[]},"956272608":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"956272608","rule_id":"prestart","secondary_exposures":[]},"2794040149":{"value":{"has_discover_devmode_modal":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2794040149","rule_id":"launchedGroup","secondary_exposures":[]},"3622729945":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"orgID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3622729945","rule_id":"prestart","secondary_exposures":[]},"653074667":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"teamID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"653074667","rule_id":"prestart","secondary_exposures":[]},"2263334434":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"teamID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2263334434","rule_id":"prestart","secondary_exposures":[]},"446319178":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"teamID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"446319178","rule_id":"prestart","secondary_exposures":[]},"2958533680":{"value":{"can_request_edit":true},"group":"2tRagG3xhX7XPttLDx5GN7","group_name":"Test","id_type":"teamID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2958533680","rule_id":"2tRagG3xhX7XPttLDx5GN7","secondary_exposures":[]},"2825521828":{"value":{"infinite_count":true,"max_distance":2,"infinite_distance":false},"group":"3ZAPOQV8swcHGGVfNsxLYy","group_name":"Depth 2","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2825521828","rule_id":"3ZAPOQV8swcHGGVfNsxLYy","secondary_exposures":[]},"584486372":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"teamID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"584486372","rule_id":"prestart","secondary_exposures":[]},"829374629":{"value":{"should_send_digest_email":true},"group":"357NewfIMTaNhdEiTETjtY","group_name":"variant","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"829374629","rule_id":"357NewfIMTaNhdEiTETjtY","secondary_exposures":[]},"641946465":{"value":{"is_test_group":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"641946465","rule_id":"launchedGroup","secondary_exposures":[{"gate":"test_community_logged_out_holdout_v1","gateValue":"false","ruleID":"disabled"}]},"2959550945":{"value":{"is_test_group":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2959550945","rule_id":"launchedGroup","secondary_exposures":[{"gate":"test_community_logged_out_holdout_v1","gateValue":"false","ruleID":"disabled"}]},"1116777482":{"value":{"is_variant":true},"group":"6vaCbgbA1LJ5QfnrmlMGYM","group_name":"Test","id_type":"teamID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"1116777482","rule_id":"6vaCbgbA1LJ5QfnrmlMGYM","secondary_exposures":[{"gate":"segment:override_all_figma_users","gateValue":"false","ruleID":"default"}]},"719063485":{"value":{"is_variant":true},"group":"3PQ3wcLkaNK3YQFZnJEd0c","group_name":"Test","id_type":"orgID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"719063485","rule_id":"3PQ3wcLkaNK3YQFZnJEd0c","secondary_exposures":[{"gate":"segment:override_all_figma_users","gateValue":"false","ruleID":"default"}]},"2444951975":{"value":{"is_variant":false},"group":"7glGsACY81EzeppkjKIr7T","group_name":"Control","id_type":"orgID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2444951975","rule_id":"7glGsACY81EzeppkjKIr7T","secondary_exposures":[]},"4181611306":{"value":{"enable_popular_ranking":false},"group":"1wkmVkaaQKsMjmUzdXI7Jc","group_name":"Control","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"4181611306","rule_id":"1wkmVkaaQKsMjmUzdXI7Jc","secondary_exposures":[{"gate":"community_signups_holdout_h2_2023","gateValue":"false","ruleID":"4OWJOUBE713T6GBGGi0mhz:5.00:1"}]},"3652859498":{"value":{"homeLeftCta":false,"homeBlueCta":false,"showTrustBanner":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":["showTrustBanner"],"name":"3652859498","rule_id":"launchedGroup","secondary_exposures":[]},"3944291368":{"value":{"promo_type":"control"},"group":"6KjHDpBC9AlRPy2hTwpwSk","group_name":"Control","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3944291368","rule_id":"6KjHDpBC9AlRPy2hTwpwSk","secondary_exposures":[]},"3675710288":{"value":{"homeLeftCta":false,"homeBlueCta":false,"showTrustBanner":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":["homeLeftCta","homeBlueCta"],"name":"3675710288","rule_id":"launchedGroup","secondary_exposures":[]},"2360905124":{"value":{"can-open-in-recent-files":true},"group":"2jDDzO5PzH7cMQv8aOD53i","group_name":"Test","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2360905124","rule_id":"2jDDzO5PzH7cMQv8aOD53i","secondary_exposures":[]},"2771424757":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2771424757","rule_id":"prestart","secondary_exposures":[]},"1267341252":{"value":{"use_figjam_try":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1267341252","rule_id":"launchedGroup","secondary_exposures":[{"gate":"community_signups_holdout_h2_2023","gateValue":"false","ruleID":"4OWJOUBE713T6GBGGi0mhz:5.00:1"}]},"3898401984":{"value":{"show_plugin_redesign":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3898401984","rule_id":"launchedGroup","secondary_exposures":[{"gate":"community_signups_holdout_h2_2023","gateValue":"false","ruleID":"4OWJOUBE713T6GBGGi0mhz:5.00:1"}]},"2544578060":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2544578060","rule_id":"prestart","secondary_exposures":[]},"1431078461":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1431078461","rule_id":"prestart","secondary_exposures":[]},"3928626076":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3928626076","rule_id":"prestart","secondary_exposures":[]},"3137160339":{"value":{"experience":1},"group":"launchedGroup","group_name":"Contact Sales (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3137160339","rule_id":"launchedGroup","secondary_exposures":[]},"2178473123":{"value":{"isVariant":false},"group":"1EWz8bmMaKNp5LqzjHleNt","group_name":"Control","id_type":"teamID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2178473123","rule_id":"1EWz8bmMaKNp5LqzjHleNt","secondary_exposures":[]},"760341116":{"value":{"isVariant":true},"group":"6ilDMFk5JNe9Cz0oZkI3Iw","group_name":"Test","id_type":"teamID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"760341116","rule_id":"6ilDMFk5JNe9Cz0oZkI3Iw","secondary_exposures":[]},"3967883838":{"value":{"isVariant":true},"group":"5wyTTRTTOAQVNVeBOOhggR","group_name":"Test","id_type":"teamID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3967883838","rule_id":"5wyTTRTTOAQVNVeBOOhggR","secondary_exposures":[]},"714718044":{"value":{"isVariant":true},"group":"4ALU1uOde8nVpkr9QPgdKa","group_name":"Test","id_type":"teamID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"714718044","rule_id":"4ALU1uOde8nVpkr9QPgdKa","secondary_exposures":[]},"2218930796":{"value":{"isVariant":false},"group":"1q5uzM3bKHYQ52Dpk1rkCp","group_name":"Control","id_type":"teamID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2218930796","rule_id":"1q5uzM3bKHYQ52Dpk1rkCp","secondary_exposures":[]},"862438199":{"value":{"isVariant":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"862438199","rule_id":"launchedGroup","secondary_exposures":[{"gate":"2023_h2_monetization_holdout","gateValue":"false","ruleID":"7e3PfmLObjnArYYQVvYhQL:8.00:1"}]},"2018832189":{"value":{"show_onboarding":true},"group":"4HuP1bY9Gae0M8cwLzQmIt","group_name":"Treatment","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2018832189","rule_id":"4HuP1bY9Gae0M8cwLzQmIt","secondary_exposures":[{"gate":"segment:override_lguo","gateValue":"false","ruleID":"default"},{"gate":"activation_holdout_h2_2023","gateValue":"false","ruleID":"1aPAHL0FFBz4XddlxZM02B:8.00:1"}]},"3296242940":{"value":{"isVariant":true},"group":"2H6eks467HhUHWFzhqDQ2X","group_name":"Variant","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3296242940","rule_id":"2H6eks467HhUHWFzhqDQ2X","secondary_exposures":[{"gate":"segment:override_kqian","gateValue":"false","ruleID":"default"}]},"1660229611":{"value":{"treatment":"control"},"group":"6VZigo0ohEkMu4tkqIzosR","group_name":"Control","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"1660229611","rule_id":"6VZigo0ohEkMu4tkqIzosR","secondary_exposures":[]},"717377664":{"value":{"has_edu_offboarding":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"717377664","rule_id":"launchedGroup","secondary_exposures":[{"gate":"2023_h2_monetization_holdout","gateValue":"false","ruleID":"7e3PfmLObjnArYYQVvYhQL:8.00:1"}]},"2847708758":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2847708758","rule_id":"prestart","secondary_exposures":[]},"2321368068":{"value":{"show_redesign":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2321368068","rule_id":"launchedGroup","secondary_exposures":[{"gate":"community_signups_holdout_h2_2023","gateValue":"false","ruleID":"4OWJOUBE713T6GBGGi0mhz:5.00:1"}]},"2822687013":{"value":{"should_show_file_creation_defaults":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2822687013","rule_id":"launchedGroup","secondary_exposures":[{"gate":"segment:override_jhill","gateValue":"false","ruleID":"default"},{"gate":"2023_h2_monetization_holdout","gateValue":"false","ruleID":"7e3PfmLObjnArYYQVvYhQL:8.00:1"}]},"4251349319":{"value":{"treatment_name":"simplified_playground_file","settings_key":"template_files_simplified_figma_basics"},"group":"3Z0gPuwAy0HMb4CX2kpN4E","group_name":"Simplified Playground File Treatment","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"4251349319","rule_id":"3Z0gPuwAy0HMb4CX2kpN4E","secondary_exposures":[{"gate":"activation_holdout_h2_2023","gateValue":"false","ruleID":"1aPAHL0FFBz4XddlxZM02B:8.00:1"}]},"2791944796":{"value":{"treatment":"in_resource"},"group":"launchedGroup","group_name":"Treatment In Resource (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2791944796","rule_id":"launchedGroup","secondary_exposures":[{"gate":"segment:override_lguo","gateValue":"false","ruleID":"default"}]},"3186522216":{"value":{"enabled":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3186522216","rule_id":"launchedGroup","secondary_exposures":[]},"1477844864":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"teamID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1477844864","rule_id":"prestart","secondary_exposures":[]},"2430626081":{"value":{"test_parameter":"B"},"group":"6f0eIDHQzaxsvlXDgwKDib","group_name":"Test","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2430626081","rule_id":"6f0eIDHQzaxsvlXDgwKDib","secondary_exposures":[]},"1978740137":{"value":{"test_parameter":"A"},"group":"6mO0XM7CcIiqXtUlwGijxn","group_name":"Control","id_type":"stableID","is_device_based":true,"is_user_in_experiment":true,"is_experiment_active":true,"name":"1978740137","rule_id":"6mO0XM7CcIiqXtUlwGijxn","secondary_exposures":[]},"2660239630":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2660239630","rule_id":"prestart","secondary_exposures":[]},"1190808287":{"value":{"community_libraries_enabled":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1190808287","rule_id":"launchedGroup","secondary_exposures":[{"gate":"notorg_figma_com_users","gateValue":"false","ruleID":"default"},{"gate":"community_holdout_h2_2023","gateValue":"false","ruleID":"1fTFOrtrWWCEAisT3NrYN8:5.00:1"}]},"2850460505":{"value":{"has_standardized_pro_entrypoints":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2850460505","rule_id":"launchedGroup","secondary_exposures":[{"gate":"2023_h2_monetization_holdout","gateValue":"false","ruleID":"7e3PfmLObjnArYYQVvYhQL:8.00:1"}]},"3764953905":{"value":{"expand":true},"group":"launchedGroup","group_name":"Variant (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3764953905","rule_id":"launchedGroup","secondary_exposures":[]},"3122426501":{"value":{},"group":"layerAssignment","group_name":"Layer Assignment","id_type":"teamID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":true,"name":"3122426501","rule_id":"layerAssignment","secondary_exposures":[]},"1991106507":{"value":{"using_new_edu_validation_form":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1991106507","rule_id":"launchedGroup","secondary_exposures":[{"gate":"segment:override_schyou","gateValue":"false","ruleID":"default"},{"gate":"segment:override_jbai","gateValue":"false","ruleID":"default"},{"gate":"segment:override_jhill","gateValue":"false","ruleID":"default"}]},"2555864214":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2555864214","rule_id":"prestart","secondary_exposures":[]},"2422604066":{"value":{"enabled":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2422604066","rule_id":"launchedGroup","secondary_exposures":[]},"1178969053":{"value":{"should_show_sepa":false},"group":"4aFT9xPG4Bbif7q8oTYO4Z","group_name":"Control","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"1178969053","rule_id":"4aFT9xPG4Bbif7q8oTYO4Z","secondary_exposures":[{"gate":"segment:override_czhu","gateValue":"false","ruleID":"default"},{"gate":"segment:override_schyou","gateValue":"false","ruleID":"default"},{"gate":"segment:override_spandey","gateValue":"false","ruleID":"default"},{"gate":"2023_h2_monetization_holdout","gateValue":"false","ruleID":"7e3PfmLObjnArYYQVvYhQL:8.00:1"}]},"452992859":{"value":{},"group":"layerAssignment","group_name":"Layer Assignment","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":true,"name":"452992859","rule_id":"layerAssignment","secondary_exposures":[]},"2566375550":{"value":{"showPlanComparison":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2566375550","rule_id":"launchedGroup","secondary_exposures":[]},"3321955269":{"value":{"show_treatment":true},"group":"launchedGroup","group_name":"Variant (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3321955269","rule_id":"launchedGroup","secondary_exposures":[{"gate":"activation_holdout_h2_2023","gateValue":"false","ruleID":"1aPAHL0FFBz4XddlxZM02B:8.00:1"}]},"814998008":{"value":{"treatment":"variant"},"group":"launchedGroup","group_name":"Variant (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"814998008","rule_id":"launchedGroup","secondary_exposures":[{"gate":"segment:override_lguo","gateValue":"false","ruleID":"default"},{"gate":"activation_holdout_h2_2023","gateValue":"false","ruleID":"1aPAHL0FFBz4XddlxZM02B:8.00:1"}]},"3101853647":{"value":{"has_pro_user_move_draft_nudge":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3101853647","rule_id":"launchedGroup","secondary_exposures":[]},"794888709":{"value":{"is_test_group":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"794888709","rule_id":"launchedGroup","secondary_exposures":[{"gate":"segment:cmty_holdout_excluded_users","gateValue":"false","ruleID":"default"},{"gate":"cmty_h2_2023_holdout_targeting","gateValue":"true","ruleID":"oiRxqLNDErXnhMp5donHx"},{"gate":"test_community_holdout_v2","gateValue":"false","ruleID":"otFPXnTEvx3qv1M0LjIKw:5.00:1"}]},"2554059410":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2554059410","rule_id":"prestart","secondary_exposures":[]},"312211808":{"value":{"treatment":"treatment_fullscreen"},"group":"launchedGroup","group_name":"Fullscreen (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"312211808","rule_id":"launchedGroup","secondary_exposures":[]},"840841033":{"value":{"treatment":"treatment_animated"},"group":"launchedGroup","group_name":"Animated (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"840841033","rule_id":"launchedGroup","secondary_exposures":[]},"2300771859":{"value":{"is_test_group":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2300771859","rule_id":"launchedGroup","secondary_exposures":[{"gate":"test_community_holdout_v1","gateValue":"false","ruleID":"6fVM4CJdapAC05Omtb4WX0:5.00:1"}]},"2300386119":{"value":{},"group":"layerAssignment","group_name":"Layer Assignment","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":true,"name":"2300386119","rule_id":"layerAssignment","secondary_exposures":[{"gate":"activation_holdout_h2_2023","gateValue":"false","ruleID":"1aPAHL0FFBz4XddlxZM02B:8.00:1"}]},"363873135":{"value":{"is_test_group":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"363873135","rule_id":"launchedGroup","secondary_exposures":[{"gate":"test_community_holdout_v1","gateValue":"false","ruleID":"6fVM4CJdapAC05Omtb4WX0:5.00:1"}]},"885097306":{"value":{"is_in_aa_test":false},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":["is_in_aa_test"],"name":"885097306","rule_id":"prestart","secondary_exposures":[]},"2214445633":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2214445633","rule_id":"prestart","secondary_exposures":[]},"23812337":{"value":{"has_org_plan_recommended":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"23812337","rule_id":"launchedGroup","secondary_exposures":[]},"1372977597":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1372977597","rule_id":"prestart","secondary_exposures":[{"gate":"segment:override_jhalim","gateValue":"false","ruleID":"default"},{"gate":"2023_h2_monetization_holdout","gateValue":"false","ruleID":"7e3PfmLObjnArYYQVvYhQL:8.00:1"}]},"2830967430":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2830967430","rule_id":"prestart","secondary_exposures":[]},"2317150827":{"value":{"sync_profile_picture":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2317150827","rule_id":"launchedGroup","secondary_exposures":[{"gate":"activation_holdout_h2_2023","gateValue":"false","ruleID":"1aPAHL0FFBz4XddlxZM02B:8.00:1"}]},"1641387297":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"orgID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1641387297","rule_id":"prestart","secondary_exposures":[]},"3551988278":{"value":{"treatment":"control"},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"3551988278","rule_id":"launchedGroup","secondary_exposures":[]},"1829195403":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1829195403","rule_id":"prestart","secondary_exposures":[]},"937084637":{"value":{"has_new_payment_element":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"937084637","rule_id":"launchedGroup","secondary_exposures":[{"gate":"segment:override_czhu","gateValue":"false","ruleID":"default"},{"gate":"segment:override_schyou","gateValue":"false","ruleID":"default"},{"gate":"segment:override_jhill","gateValue":"false","ruleID":"default"},{"gate":"segment:override_dbarragan","gateValue":"false","ruleID":"default"},{"gate":"segment:override_jhalim","gateValue":"false","ruleID":"default"}]},"2299967651":{"value":{"test_parameter":"B"},"group":"67vs3qu9qC3mMKmYu1Higj","group_name":"Test","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2299967651","rule_id":"67vs3qu9qC3mMKmYu1Higj","secondary_exposures":[{"gate":"segment:override_all_figma_users","gateValue":"false","ruleID":"default"}]},"594277785":{"value":{"test_parameter":"A"},"group":"7wlbicbz3bOargQgsO9BOO","group_name":"Control","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"594277785","rule_id":"7wlbicbz3bOargQgsO9BOO","secondary_exposures":[]},"3281036335":{"value":{"test_parameter":"B"},"group":"6wu8sv8fPsic5gjWtsnF4L","group_name":"Test","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3281036335","rule_id":"6wu8sv8fPsic5gjWtsnF4L","secondary_exposures":[{"gate":"segment:override_asalani","gateValue":"false","ruleID":"default"},{"gate":"segment:override_all_figma_users","gateValue":"false","ruleID":"default"}]},"2677307129":{"value":{"has_new_payment_modal":true},"group":"launchedGroup","group_name":"Test (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2677307129","rule_id":"launchedGroup","secondary_exposures":[{"gate":"segment:override_czhu","gateValue":"false","ruleID":"default"},{"gate":"segment:override_schyou","gateValue":"false","ruleID":"default"},{"gate":"segment:override_dbarragan","gateValue":"false","ruleID":"default"},{"gate":"segment:override_spandey","gateValue":"false","ruleID":"default"}]},"2168838921":{"value":{"test_param":"blue"},"group":"WhllzdAjvgGPETL3eS0y7","group_name":"Test","id_type":"teamID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2168838921","rule_id":"WhllzdAjvgGPETL3eS0y7","secondary_exposures":[]},"1747821500":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1747821500","rule_id":"prestart","secondary_exposures":[]},"2051678785":{"value":{"treatment_name":"control"},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2051678785","rule_id":"launchedGroup","secondary_exposures":[{"gate":"segment:video_onboarding_exp_with_video_override_list","gateValue":"false","ruleID":"default"},{"gate":"segment:video_onboarding_exp_canvas_only_override_list","gateValue":"false","ruleID":"default"}]},"1803111893":{"value":{"test1":"test1_control"},"group":"1G4D3FDxGBvv8W21dCO0rc","group_name":"Control","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"1803111893","rule_id":"1G4D3FDxGBvv8W21dCO0rc","secondary_exposures":[]},"3885417024":{"value":{"showAnchorLink":true,"showCta":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":["showCta"],"name":"3885417024","rule_id":"launchedGroup","secondary_exposures":[]},"238317150":{"value":{"showAnchorLink":true,"showCta":false},"group":"launchedGroup","group_name":"Variant (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":["showAnchorLink"],"name":"238317150","rule_id":"launchedGroup","secondary_exposures":[]},"2051813994":{"value":{"show_second_embed":false},"group":"launchedGroup","group_name":"control (Launched)","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2051813994","rule_id":"launchedGroup","secondary_exposures":[]},"3475793167":{"value":{"color":"red"},"group":"7wLpVehiWiDUj61IuY2Ut6","group_name":"Control","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3475793167","rule_id":"7wLpVehiWiDUj61IuY2Ut6","secondary_exposures":[{"gate":"segment:override_my_testing_segment","gateValue":"false","ruleID":"default"},{"gate":"segment:override_override_unittestingemail","gateValue":"false","ruleID":""},{"gate":"segment:override_testing_segment","gateValue":"false","ruleID":"default"}]},"135074797":{"value":{"treatment":"control"},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"135074797","rule_id":"launchedGroup","secondary_exposures":[]},"2919343866":{"value":{"show_treatment":false},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2919343866","rule_id":"launchedGroup","secondary_exposures":[]},"1376515155":{"value":{"treatment_name":"treatment_no_nav"},"group":"launchedGroup","group_name":"No Nav Treatment (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1376515155","rule_id":"launchedGroup","secondary_exposures":[{"gate":"segment:tooltips_plus_onboarding_exp_override_list","gateValue":"false","ruleID":"default"},{"gate":"segment:override_lguo","gateValue":"false","ruleID":"default"},{"gate":"activation_holdout_h2_2023","gateValue":"false","ruleID":"1aPAHL0FFBz4XddlxZM02B:8.00:1"}]},"1698893171":{"value":{"test":"red"},"group":"1RLWoD0UogCNM9GG2Z8pFU","group_name":"Control","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"1698893171","rule_id":"1RLWoD0UogCNM9GG2Z8pFU","secondary_exposures":[]},"*********":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"stableID","is_device_based":true,"is_user_in_experiment":false,"is_experiment_active":false,"name":"*********","rule_id":"prestart","secondary_exposures":[]},"1894159767":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"teamID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1894159767","rule_id":"prestart","secondary_exposures":[]},"2452117511":{"value":{"test_param":"blue"},"group":"1DTujttaX0WA2kJkXPmiOI","group_name":"Test","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2452117511","rule_id":"1DTujttaX0WA2kJkXPmiOI","secondary_exposures":[]},"1472611635":{"value":{},"group":"targetingGate","group_name":"Targeting Gate (srm-feature-gate)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":true,"name":"1472611635","rule_id":"targetingGate","secondary_exposures":[{"gate":"srm-feature-gate","gateValue":"false","ruleID":""}]},"1555593711":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"is_in_layer":true,"explicit_parameters":[],"name":"1555593711","rule_id":"prestart","secondary_exposures":[]},"1634176788":{"value":{},"group":"prestart","group_name":"Unstarted","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1634176788","rule_id":"prestart","secondary_exposures":[]},"1634176787":{"value":{"test_param_1":"test","test_param_2":true,"test_param_3":"hello"},"group":"4DAvBDIUiHsiKreHjmGNVE","group_name":"Test","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"1634176787","rule_id":"4DAvBDIUiHsiKreHjmGNVE","secondary_exposures":[{"gate":"aditya_test_gate","gateValue":"false","ruleID":"default"}]},"3508857097":{"value":{"userID":""},"group":"6gS4k0PjbgWGdbxpQWVlJi","group_name":"Control","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3508857097","rule_id":"6gS4k0PjbgWGdbxpQWVlJi","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"},{"gate":"20_or_rules","gateValue":"true","ruleID":"1JcXaERW0Ne6xWgPhJxIqK"}]},"661628341":{"value":{},"group":"targetingGate","group_name":"Targeting Gate (20_and_rules)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":true,"name":"661628341","rule_id":"targetingGate","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"},{"gate":"20_and_rules","gateValue":"false","ruleID":"default"}]},"3705370602":{"value":{"userId":""},"group":"7FWhpx12otaTKi6NQCKAPB","group_name":"Test","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3705370602","rule_id":"7FWhpx12otaTKi6NQCKAPB","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"},{"gate":"10_or_rules","gateValue":"true","ruleID":"6S4sns8dIIdK55nJg6ZR0w"}]},"4234862952":{"value":{},"group":"targetingGate","group_name":"Targeting Gate (10_and_rules)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":true,"name":"4234862952","rule_id":"targetingGate","secondary_exposures":[{"gate":"aa_test_roll-out","gateValue":"true","ruleID":"5rGlj71zhtfAMvpgO4O1eR"},{"gate":"10_and_rules","gateValue":"false","ruleID":"default"}]},"3302575583":{"value":{"user":""},"group":"2GcCGkAEC6KzTykRavzHip","group_name":"Test","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"3302575583","rule_id":"2GcCGkAEC6KzTykRavzHip","secondary_exposures":[]},"2820651964":{"value":{"flag":true},"group":"1BDA4z2iy6NP9FgRbBhfhH","group_name":"Test","id_type":"userID","is_device_based":false,"is_user_in_experiment":true,"is_experiment_active":true,"name":"2820651964","rule_id":"1BDA4z2iy6NP9FgRbBhfhH","secondary_exposures":[]},"2614308924":{"value":{"aa_param":"control"},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"2614308924","rule_id":"launchedGroup","secondary_exposures":[]},"1784874164":{"value":{"aa_param":"control"},"group":"launchedGroup","group_name":"Control (Launched)","id_type":"userID","is_device_based":false,"is_user_in_experiment":false,"is_experiment_active":false,"name":"1784874164","rule_id":"launchedGroup","secondary_exposures":[]}},"layer_configs":{"1615268131":{"value":{"omnicreate_variants":"","onboarding_variants":""},"group":"prestart","group_name":"Unstarted","is_device_based":false,"explicit_parameters":["onboarding_variants"],"allocated_experiment_name":"2315672783","is_user_in_experiment":false,"is_experiment_active":false,"undelegated_secondary_exposures":[],"name":"1615268131","rule_id":"prestart","secondary_exposures":[]},"862854653":{"value":{"enableProductHeroVariant":false,"displayJumboCta":false},"group":"default","group_name":"default","is_device_based":true,"explicit_parameters":[],"undelegated_secondary_exposures":[],"name":"862854653","rule_id":"default","secondary_exposures":[]},"339292097":{"value":{},"group":"default","group_name":"default","is_device_based":true,"explicit_parameters":[],"undelegated_secondary_exposures":[],"name":"339292097","rule_id":"default","secondary_exposures":[]},"862854652":{"value":{"homeLeftCta":false,"homeBlueCta":false,"showTrustBanner":false},"group":"default","group_name":"default","is_device_based":true,"explicit_parameters":[],"undelegated_secondary_exposures":[],"name":"862854652","rule_id":"default","secondary_exposures":[]},"2641100055":{"value":{},"group":"default","group_name":"default","is_device_based":false,"explicit_parameters":[],"undelegated_secondary_exposures":[],"name":"2641100055","rule_id":"default","secondary_exposures":[]},"180777806":{"value":{"is_in_aa_test":false},"group":"prestart","group_name":"Unstarted","is_device_based":false,"explicit_parameters":["is_in_aa_test"],"allocated_experiment_name":"885097306","is_user_in_experiment":false,"is_experiment_active":false,"undelegated_secondary_exposures":[],"name":"180777806","rule_id":"prestart","secondary_exposures":[]},"3194459881":{"value":{"showAnchorLink":true,"showCta":false},"group":"default","group_name":"default","is_device_based":true,"explicit_parameters":[],"undelegated_secondary_exposures":[],"name":"3194459881","rule_id":"default","secondary_exposures":[]},"1131874207":{"value":{},"group":"default","group_name":"default","is_device_based":false,"explicit_parameters":[],"undelegated_secondary_exposures":[],"name":"1131874207","rule_id":"default","secondary_exposures":[]}},"sdkParams":{},"has_updates":true,"generator":"statsig-ruby-sdk","evaluated_keys":{"userID":"1285381156962610170","customIDs":{"teamID":"1309356710924270529","stableID":"4de8b2b4-b5c9-4c80-81d8-03af708fa3d3"}},"time":0,"hash_used":"djb2"}},"EARLY_ARGS":{"omit_core_data":true,"file_minimal_user_state":true,"multiplayer_preconnect_options":{"fileKey":"daZH9zno8XhMmf6JQBl9g9","role":"prototype","oauthToken":null},"should_connect_to_multiplayer":true},"Fig":{"importShimURL":"https://static.figma.com/fullscreen/2c00e9e8b0f016d4ed3ccc4c2545cd9163bb3ca0/import/import.shim.js.br","importWorkerURL":"https://static.figma.com/fullscreen/2c00e9e8b0f016d4ed3ccc4c2545cd9163bb3ca0/import/import.worker.js.br","figMigratorURL":"https://static.figma.com/fullscreen/2c00e9e8b0f016d4ed3ccc4c2545cd9163bb3ca0/fig_migrator/fig_migrator.js.br","jsvmCppURLs":{"jsvm-cpp.js":"https://static.figma.com/fullscreen/56636c5435fc3b9f055a47ae8e1416bb3c30dc12/jsvm-cpp.js.br","jsvm-cpp.wasm":"https://static.figma.com/fullscreen/56636c5435fc3b9f055a47ae8e1416bb3c30dc12/jsvm-cpp.wasm.br"},"fullscreenURLs":{"compiled_wasm.js":"https://static.figma.com/fullscreen/3102ec2ab9ca06cac70188fc4dbd381b0065f46a/fullscreen-wasm/compiled_wasm.js.br","compiled_wasm.wasm":"https://static.figma.com/fullscreen/3102ec2ab9ca06cac70188fc4dbd381b0065f46a/fullscreen-wasm/compiled_wasm.wasm.br"},"fullscreenScriptHash":"170c24aa3453bc9df82966ba090cd4c72a93ec2c","prototypeLibURLs":{"compiled_wasm.js":"https://static.figma.com/fullscreen/3102ec2ab9ca06cac70188fc4dbd381b0065f46a/prototype-lib/compiled_wasm.js.br","compiled_wasm.wasm":"https://static.figma.com/fullscreen/3102ec2ab9ca06cac70188fc4dbd381b0065f46a/prototype-lib/compiled_wasm.wasm.br"},"prototypeLibHash":"088f514d78ab66188b292ab77889bfc8fd5934c0","imageIOWorkerURL":"https://www.figma.com/esbuild-artifacts/a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2/js/image_io_worker.min.js.br","librarySearchWorkerURL":"https://www.figma.com/esbuild-artifacts/a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2/js/library_search_worker.min.js.br","spellCheckWorkerURL":"https://www.figma.com/esbuild-artifacts/a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2/js/spell_check_worker.min.js.br","tsViewerScriptURL":"https://static.figma.com/fullscreen/3102ec2ab9ca06cac70188fc4dbd381b0065f46a/mobile_viewer/ts-viewer.js.br","tsViewerWorkerURL":"https://static.figma.com/fullscreen/3102ec2ab9ca06cac70188fc4dbd381b0065f46a/mobile_viewer/ts-imagedecoder.js.br"}}</script>
<script nonce="VqsolV7Ot2ROA5YFchq09w==">
Object.assign(window, JSON.parse(document.querySelector('[data-initial]').textContent));

console.log('Running frontend commit', "3102ec2ab9ca06cac70188fc4dbd381b0065f46a");

window.FIGMA_BUNDLE = (function () {
  const pathsForModules = (() => {
    const _paths = {
      'heic': "https://www.figma.com/esbuild-artifacts/a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2/js/heic.min.js.br",
      'hunspell': "https://www.figma.com/esbuild-artifacts/a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2/js/hunspell.min.js.br",
      'pdfjs': "https://www.figma.com/esbuild-artifacts/a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2/js/pdfjs.min.js.br",
      'svgo':"https://www.figma.com/esbuild-artifacts/a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2/js/svgo.min.js.br",
      'videojs': "https://www.figma.com/esbuild-artifacts/a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2/js/videojs.min.js.br",
    };

    const _loadedPaths = new Set()
    return {
      get: (key) => {
        return _paths[key];
      },
      hasLoaded: (key) => {
        return _loadedPaths.has(key)
      },
      setHasLoaded: (key) => {
        return _loadedPaths.add(key)
      },
    };
  })();

  const values = {};
  const promises = {};

  return {
    export: function (name, value) {
      if (!pathsForModules.get(name)) {
        console.warn('please add the "js_path" declaration in "pathsForModules" for "' + name + '".');
        return;
      }
      values[name] = value;
    },

    import: function (name) {
      const FAILURE_TEXT = 'failed to load "' + name + '" module!'
      if (values[name]) return Promise.resolve(values[name])

      const path = pathsForModules.get(name);
      if (!pathsForModules.get(name)) {
        return Promise.reject({ error: FAILURE_TEXT + ' "' + name + '" has not been added to "pathsForModules".' })
      }

      if (promises[path]) {
        return new Promise((resolve, reject) => {
          promises[path].then(_ => resolve(values[name])).catch(error => reject({ error }));
        });
      }

      const promise = new Promise((resolve, reject) => {
        if (promises[path]) return;

        const script = document.createElement("script");
        script.setAttribute("async", true);
        script.setAttribute("type", "module");
        script.setAttribute("nonce", "VqsolV7Ot2ROA5YFchq09w==")

        script.onload = function () {
          resolve(values[name])
        };
        script.onerror = function (error) {
          reject(error)
        };

        script.src = pathsForModules.get(name);
        pathsForModules.setHasLoaded(name);
        document.body.appendChild(script)
      });
      promises[path] = promise;
      return promise;
    },
  };
})();
</script>

  <script nonce="VqsolV7Ot2ROA5YFchq09w==">
    
!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="d8c85b83-aaeb-5c27-ad75-6678f33fd172")}catch(e){}}();
function j(t,n){let l=t?"/api/livegraph-next":"/api/livegraph",i=location.host;return i.startsWith("admin.")&&(i=i.slice(6)),`${location.protocol==="https:"?"wss":"ws"}://${i}${l}?${n}`}function H(t,n){return j(!!t,n??"")}self.global=self;var S=100,O=class extends WebSocket{constructor(e,s,l=void 0){super(e,s);this.onCloseTriggered=l;this.events=[];this._readyToFlush=()=>{};super.onclose=this.onClose,super.onopen=this.onOpen,super.onmessage=this.onMessage,super.onerror=this.onError,new Promise(i=>{let r=!1;this._readyToFlush=()=>{r||(r=!0,setTimeout(i,0))}}).then(()=>{this.events.forEach(({event:i,ev:r})=>{var o,a,d,c;switch(i){case"onmessage":(o=super.onmessage)==null||o.call(this,r);break;case"onopen":(a=super.onopen)==null||a.call(this,r);break;case"onclose":(d=super.onclose)==null||d.call(this,r);break;case"onerror":(c=super.onerror)==null||c.call(this,r);break}})})}onMessage(e){this.events.push({event:"onmessage",ev:e})}onOpen(e){this.events.push({event:"onopen",ev:e})}onClose(e){var s;(s=this.onCloseTriggered)==null||s.call(this),this.events.push({event:"onclose",ev:e})}onError(e){this.events.push({event:"onerror",ev:e})}set onclose(e){super.onclose=e,this._readyToFlush()}set onopen(e){super.onopen=e,this._readyToFlush()}set onmessage(e){super.onmessage=e,this._readyToFlush()}set onerror(e){super.onerror=e,this._readyToFlush()}},z=(t,n,e,s)=>{if(t)window.userStateXHR={readyState:4,status:200,responseText:t};else if(window.Fig){if(s||!window.INITIAL_OPTIONS.user_data){window.startUserStateXHR=()=>{};return}window.startUserStateXHR=function(r){var u;var o="/api/user/state",a=[];window.INITIAL_OPTIONS.org_id&&a.push("org_id="+window.INITIAL_OPTIONS.org_id),window.INITIAL_OPTIONS.team_id&&a.push("team_id="+window.INITIAL_OPTIONS.team_id),e&&a.push("omit_core_data=1"),r&&a.push("file_key="+r),a.length!==0&&(o+="?"+a.join("&")),window.userStateXHR=new XMLHttpRequest,window.userStateXHR.open("GET",o);let d=(u=window.INITIAL_OPTIONS.user_data)==null?void 0:u.id;d&&window.userStateXHR.setRequestHeader("X-Figma-User-ID",d),window.userStateXHR.send();var c=window.performance?window.performance.now():-1;window.userStateXHR.addEventListener("load",function(){window.userStateXHRDuration=window.performance?window.performance.now()-c:-1},!1),window.sessionStateXHR=new XMLHttpRequest,window.sessionStateXHR.open("GET","/api/session/state"),d&&window.sessionStateXHR.setRequestHeader("X-Figma-User-ID",d),window.sessionStateXHR.send()};let l=location.pathname==="/preload-editor"||location.pathname==="/preload-android-proto"||location.pathname==="/file/new",i=window.INITIAL_OPTIONS.editing_file&&window.INITIAL_OPTIONS.editing_file.key;n?window.startUserStateXHR(i):l||window.startUserStateXHR()}};function D(t){return t==="dark"?"1E1E1E":"F5F5F5"}function C(){let t=null;try{t=window.localStorage}catch{}return t}function Q(){var e;if(!((e=window.INITIAL_OPTIONS.feature_flags)!=null&&e.send_initial_bg_color))return"";let t=G(C()),n=x(t);return D(n)}var X="global-debug-theme-preference";function G(t){var n;if(t){let e=t==null?void 0:t.getItem(X);return(n=window.INITIAL_OPTIONS.feature_flags)!=null&&n.ee_system_theme_default&&!e?"system":e}return null}var W=window.matchMedia("(prefers-color-scheme: dark)");function x(t){return t==="system"?W.matches?"dark":"light":t==="dark"?"dark":"light"}var $="chunked-file-load",J="file-load-streaming-compression",Z=5e3,ee="send-file-directly";function te(){var e,s,l;if(!((s=(e=window.INITIAL_OPTIONS)==null?void 0:e.feature_flags)!=null&&s.manifest_commit_sha))return S;let t=(l=window.INITIAL_OPTIONS)==null?void 0:l.cluster_name;if(t!=="staging"&&t!=="local")return S;let n=window.location.search;if(n.includes("force-client-version")){let r=new URLSearchParams(n).get("force-client-version");if(r){let o=parseInt(r);if(!isNaN(o)&&o>0)return console.log(`!! Overriding client version to ${o}`),o}}return S}var ne=(t,n)=>{var i;let e=(i=window.INITIAL_OPTIONS.user_data)==null?void 0:i.id,s=window.INITIAL_OPTIONS.tracking_session_id,l=window.INITIAL_OPTIONS.release_manifest_git_commit;window.mpGlobal={version:te(),sock:null,msgs:[],perfMetrics:[],shouldConnectToMultiplayer:t,url({fileKey:r,role:o,oauthToken:a,nodeIds:d,initialBgColor:c,suppressDecodeErrors:u,connectionType:g,tagForLogging:f,forceViewOnly:_}){var L,v,P,A,R,k,b,y,F,U,M;if(!t)return"";let I=null;n&&r===n.fileKey&&n.targetFileVersion&&(I=n.targetFileVersion);let w="";(o==="editor"||o==="viewerWithCpp")&&d&&(w+=`&scenegraph-queries-initial-nodes=${d}`);let T=window.location.search,h=new URLSearchParams(T);o==="editor"&&(n==null?void 0:n.forceIncrementalForEditors)===1&&!((L=window.INITIAL_OPTIONS.feature_flags)!=null&&L.recovery_mode&&h.get("recovery")==="true")?w+="&use-incremental-for-editors=1":((n==null?void 0:n.forceIncrementalForEditors)===0||(v=window.INITIAL_OPTIONS.feature_flags)!=null&&v.recovery_mode&&h.get("recovery")==="true")&&(w+="&use-incremental-for-editors=0"),o==="editor"&&((P=window.INITIAL_OPTIONS.feature_flags)!=null&&P.incremental_loading_validation)&&(w+="&incremental-loading-validation=1");let N=1;try{let m=JSON.parse(sessionStorage.getItem("reload_times")||"[0]"),Y=m[m.length-1];N=Date.now()-Y<5e3?1:0}catch{}(o!=="editor"||!((A=window.INITIAL_OPTIONS.feature_flags)!=null&&A.send_initial_bg_color))&&(c="");let V=(R=window.INITIAL_OPTIONS.feature_flags)==null?void 0:R.send_file_directly,E=((k=window.INITIAL_OPTIONS.feature_flags)==null?void 0:k.chunked_file_load)&&g==="initial-load",q=(b=window.INITIAL_OPTIONS.feature_flags)!=null&&b.chunked_size_1000?1e3:(y=window.INITIAL_OPTIONS.feature_flags)!=null&&y.chunked_size_5000?5e3:(F=window.INITIAL_OPTIONS.feature_flags)!=null&&F.chunked_size_10000?1e4:(U=window.INITIAL_OPTIONS.feature_flags)!=null&&U.chunked_size_20000?2e4:Z,B=(M=window.INITIAL_OPTIONS.feature_flags)==null?void 0:M.file_load_streaming_compression,K=window.INITIAL_OPTIONS.e2e_traffic,p=window.INITIAL_OPTIONS.e2e_test_name;return`${location.protocol.replace("http","ws")}//${location.host}/api/multiplayer/${r}?role=${o}&tracking_session_id=${s}&version=${this.version}&recentReload=${N}`+(a?"&oauth_token="+a:"")+w+(e?`&user-id=${e}`:"")+(I?`&target-file-version=${I}`:"")+(l?`&client_release=${l}`:"")+(c?`&initial-bg-color=${c}`:"")+(u?"&suppress-decode-errors":"")+(E?`&${$}`:"")+(E?`&chunk-size=${q}`:"")+(B?`&${J}`:"")+(V?`&${ee}`:"")+(f?`&tag-for-logging=${f}`:"")+(K?"&e2e_traffic=true":"")+(p?`&e2e_test_name=${p}`:"")+(_?"&force-view-only":"")},DEBUG_THEME_PREFERENCE_KEY:X,DARK_THEME_MEDIA_QUERY:W,themePreferenceFromLocalStorage:G,getBackgroundColorForTheme:D,getVisibleTheme:x,REQUEST_CHUNKED_FILE_LOAD_PARAM:$,preconnect(r){if(t!==!1){if(this.sock){if(r===this.sock.url&&this.sock.readyState!==WebSocket.CLOSED)return;try{this.sock.close()}catch{}}this.sock=new WebSocket(r),this.sock.binaryType="arraybuffer",this.sock.onopen=o=>{this.perfMetrics.push({key:"mp-ws-onopen",ts:performance.now(),nBytes:void 0})},this.sock.onmessage=o=>{let a=new Uint8Array(o.data);this.msgs.push(a),this.perfMetrics.push({key:"mp-ws-onmessage",ts:performance.now(),nBytes:a.length*a.BYTES_PER_ELEMENT})},this.msgs=[],this.perfMetrics=[]}}},n&&mpGlobal.preconnect(mpGlobal.url(n))},se=t=>{let n=()=>{let s=document.createElement("script");return s.type="text/javascript",s.async=!0,s.setAttribute("nonce",window.INITIAL_OPTIONS.csp_nonce),s.crossOrigin="anonymous",s},e=new Promise((s,l)=>{let i=n();i.onload=s,i.src=t,document.head.appendChild(i)});return e.catch(s=>console.error(`Fetching ${t} failed: ${s}`)),e};(()=>{var o,a,d,c,u,g,f,_;let{file_minimal_user_state:t,mock_user_state_for_tests_json:n,multiplayer_preconnect_options:e,omit_core_data:s,omit_user_state:l,preload_fullscreen_urls:i,should_connect_to_multiplayer:r}=window.EARLY_ARGS||{};if(window.INITIAL_OPTIONS||(window.INITIAL_OPTIONS={}),z(n,!!t,!!s,!!l),(a=(o=window.INITIAL_OPTIONS)==null?void 0:o.feature_flags)!=null&&a.lg_early_connection){let I=(c=(d=window.INITIAL_OPTIONS)==null?void 0:d.feature_flags)==null?void 0:c.livegraph_connect_next,w=H(I,(u=window.INITIAL_OPTIONS)==null?void 0:u.page_load_token);window.LGEarlyWS=new O(w,void 0,()=>{window.LGEarlyWS=void 0})}if(e&&(e.initialBgColor=Q(),e.connectionType="initial-load",e.forceViewOnly=!1,(((g=window.INITIAL_OPTIONS)==null?void 0:g.cluster_name)==="staging"||((f=window.INITIAL_OPTIONS)==null?void 0:f.cluster_name)==="local")&&((_=C())!=null&&_.getItem("force-vscode"))?e.forceViewOnly=!0:e.forceViewOnly=(window==null?void 0:window.location.ancestorOrigins)&&[...window.location.ancestorOrigins].some(I=>I.startsWith("vscode-webview://"))),ne(r!==!1,e),!i&&!/FigmaMobile-Android/.test(navigator.userAgent)&&((e==null?void 0:e.role)==="editor"||(e==null?void 0:e.role)==="viewerWithCpp"||(e==null?void 0:e.role)==="prototype")){let I=(e==null?void 0:e.role)==="prototype"?Fig.prototypeLibURLs:Fig.fullscreenURLs,w=fetch(I["compiled_wasm.wasm"]);w.catch(h=>console.error(`Fetching compiled_wasm.wasm failed: ${h}`));let T=se(I["compiled_wasm.js"]);window.FULLSCREEN_PRELOADS={wasm:w,js:T}}})();
/*! For license information please see early.min.js.LEGAL.txt */

//# debugId=d8c85b83-aaeb-5c27-ad75-6678f33fd172

//# sourceMappingURL=https://admin.figma.com/admin/esbuild-artifacts/a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2/js/early.min.js.map
  </script>



    <meta name="google" content="notranslate" />
    <meta name="slack-app-id" content="A01N2QYSA81">

    <title>Figma</title>

    <style type="text/css">

  /* This should be kept in sync with the $gridTileMinWidth CSS variable */
  :root {
    --file-grid-tile-min-width: 272px;
  }

  #filebrowser-loading-page {
    opacity: 1;
    -webkit-transition: opacity .2s ease-in-out;
  }

  .filebrowser-loading-page-layout {
    display: flex;
    flex-wrap: wrap;

    /* Make sure the loading page is flush with the edges of the page so
       our layout isnt affected by browser default margins/paddings */
    position: absolute;
    top: 48px;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--color-bg, var(--fallback-color-bg, white));
  }

  #loading-content-pane {
    opacity: 1;
    -webkit-transition: opacity .4s ease-in-out;
  }

  #filebrowser-loading-page.fadeOut {
    opacity: 0;
  }

  #filebrowser-loading-page.hidden {
    display: none;
    opacity: 0;
  }

  .filebrowser-loading-page .file-grid.create-file-grid {
    margin: 24px 0px;
    grid-gap: 32px;
    background-color: var(--color-bg, white);
  }

  /* HAX: see loading-page-container in loading_spinner.tsx */
  #loading-page-container .create-file-grid {
    display: none;
    background-color: var(--color-bg, white);
  }
  #loading-page-container .sort-dropdown {
    margin-top: 0px;
  }

 /*
  * HAX: The sidebar is cloned and used independently outside of this
  * page so any styles like `.filebrowser-loading-page .thing` applied
  * to sidebar sub-elements also need a selector like
  * `.filebrowser-loading-sidebar .thing` so that they work outside of
  * this page too.
  */
  .filebrowser-loading-page .row,
  .filebrowser-loading-sidebar .row {
    background-color: var(--color-bg-secondary, #f0f0f0);
    border-radius: 3px;
    height: 16px;
  }

  .filebrowser-loading-page .circle,
  .filebrowser-loading-sidebar .circle {
    border-radius: 3px;
    background-color: var(--color-bg-secondary, #f0f0f0);
    width: 16px;
    height: 16px;
  }

  .filebrowser-loading-page .row .circle,
  .filebrowser-loading-sidebar .row .circle {
    position: relative;
    top: -1px;
    left: -35px;
  }

  .filebrowser-loading-sidebar {
    flex: 0 0 240px;
    width: 240px;
    background-color: var(--color-bg, white);
    padding-top: 16px;
    display: flex;
    flex-direction: column;
  }

  .filebrowser-loading-page .navbar {
    height: 48px;
    width: 100%;
    position: fixed;
    top: 0;
    background-color: var(--color-bg-toolbar, #2C2C2C);
    z-index: 1;
  }

  .filebrowser-loading-sidebar .row {
    width: 85px;
    margin-top: 8px;
    margin-bottom: 12px;
    margin-left: 48px;
  }

  .filebrowser-loading-sidebar .row .circle {
    position: relative;
    top: -1px;
    left: -35px;
  }

  .filebrowser-loading-page .divider,
  .filebrowser-loading-sidebar .divider {
    height: 1px;
    background-color: var(--color-border, rgba(0, 0, 0, 0.1));
    margin-top: 8px;
    margin-bottom: 16px;
    margin-left: 16px;
    margin-right: 16px;
  }

  .filebrowser-loading-sidebar .row:nth-child(7) { width: 40px; }
  .filebrowser-loading-sidebar .row:nth-child(8) { width: 58px; }

  .filebrowser-loading-sidebar .row:nth-last-child(1) { opacity: 0.4; width: 65px; }
  .filebrowser-loading-sidebar .row:nth-last-child(2) { opacity: 0.4; width: 65px; }
  .filebrowser-loading-sidebar .row:nth-last-child(3) { opacity: 0.8; width: 46px; }

  .filebrowser-loading-page .page {
    flex: 1 1;
    box-sizing: border-box;
    border-left: 1px var(--color-border, #e5e5e5) solid;
    display: flex;
    flex-direction: column;
  }

  .filebrowser-loading-page .toolbar {
    border-bottom: 1px var(--color-border, #e5e5e5) solid;
    height: 48px;
  }

  .filebrowser-loading-page .toolbar .item {
    background-color: var(--color-bg-secondary, #f0f0f0);
    border-radius: 3px;
    width: 85px;
    height: 16px;
    margin-left: 32px;
    margin-top: 16px;
  }

  .filebrowser-loading-page .columns {
    flex: 1 1;
    display: flex;
    flex-direction: row-reverse;
    padding-right: 32px;
  }

  @media (max-width: 849px) {
    .filebrowser-loading-page .columns {
      flex-direction: column;
    }

    .filebrowser-loading-page .right-column {
      padding-left: 32px;
    }

    .filebrowser-loading-page .public-header + .columns {
      flex-direction: column-reverse;
    }
  }

  .filebrowser-loading-page .content {
    flex: 1 1;
  }

  .filebrowser-loading-page .sort-dropdown {
    border-radius: 3px;
    width: 86px;
    padding: 16px 32px 32px 32px;
  }

  .filebrowser-loading-page .create-file-tile {
    background-color: var(--color-bg-secondary, white);
    border: 1px solid var(--color-border, #e5e5e5);
    border-radius: 6px;
    display: flex;
    align-items: center;
    height: 72px;
    box-sizing: border-box;
  }

  @media (max-width: 832px) {
    .filebrowser-loading-page .create-file-tile {
      height: 48px;
    }
  }

  @media (max-width: 645px) {
    .filebrowser-loading-page .create-file-tile, .filebrowser-loading-page .sort-dropdown {
      display: none;
    }
  }

  .filebrowser-loading-page .create-file-tile .icon {
    background-color: var(--color-bg-secondary, #f0f0f0);
    border-radius: 2px;
    margin-left: 16px;
    margin-right: 16px;
    height: 24px;
    width: 24px;
  }

  .filebrowser-loading-page .create-file-tile .label {
    background-color: var(--color-bg-secondary, #f0f0f0);
    width: 132px;
    height: 16px;
    border-radius: 3px;
  }

  @media (max-width: 1440px) {
   .filebrowser-loading-page .create-file-tile:nth-last-child(1) {
     display: none;
   }
  }

  .filebrowser-loading-page .tile {
    height: 56px;
    padding-top: 60%;
    position: relative;
  }

  .filebrowser-loading-page .tile .inner {
    position: absolute;
    top: 2px;
    bottom: 2px;
    left: 2px;
    right: 2px;
    border: 1px solid var(--color-border, #e5e5e5);
    border-radius: 6px;
    background-color: var(--color-bg-secondary, #f0f0f0);
  }

  .filebrowser-loading-page .tile .inner .lower {
    height: 56px;
    background-color: var(--color-bg, white);
    position: absolute;
    bottom: 0;
    width: 100%;
    border-radius: 0 0 6px 6px;
  }

  .filebrowser-loading-page .public-grid {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--file-grid-tile-min-width), 1fr));
    grid-gap: 32px;
    padding-left: 32px;
  }

  @media (max-width: 1023px) {
    .filebrowser-loading-page .public-grid {
      grid-template-columns: 1fr;
    }
  }

  .filebrowser-loading-page .public-grid .tile {
    height: 56px;
    padding-top: 60%;
  }

  .filebrowser-loading-page .public-grid .tile:nth-child(6) { opacity: .8; }
  .filebrowser-loading-page .public-grid .tile:nth-child(7) { opacity: .4; }

  .filebrowser-loading-page .file-grid {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(var(--file-grid-tile-min-width), 1fr));
    grid-gap: 32px;
    padding-left: 32px;
  }

  .filebrowser-loading-page .file-grid .tile:nth-child(6) { opacity: .8; }
  .filebrowser-loading-page .file-grid .tile:nth-child(7) { opacity: .4; }

  .filebrowser-loading-page .team-grid {
    width: 100%;
    box-sizing: border-box;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(264px, 1fr));
    grid-gap: 32px 32px;
    padding: 32px 0 0 32px;
    align-content: start;
  }

  .filebrowser-loading-page .team-tile {
    border: 1px solid var(--color-border, rgba(0, 0, 0, .1));
    border-radius: 6px;
    position: relative;
    padding: 16px;
  }

  .filebrowser-loading-page .team-grid .team-tile:nth-child(6) { opacity: .8; }
  .filebrowser-loading-page .team-grid .team-tile:nth-child(7) { opacity: .4; }

  .filebrowser-loading-page .team-tile .row {
    margin-top: 0;
    margin-left: 0;
    margin-bottom: 0;
  }

  .filebrowser-loading-page .team-tile .row:nth-child(1) {
    width: 32px;
    height: 32px;
  }

  .filebrowser-loading-page .team-tile .row:nth-child(2) {
    width: 65px;
    height: 30px;
    position: absolute;
    top: 17px;
    right: 16px;
    margin-top: 0;
  }

  .filebrowser-loading-page .team-tile .row:nth-child(3) {
    width: 120px;
    margin-top: 20px;
  }
  .filebrowser-loading-page .team-tile .row:nth-child(4) {
    width: 232px;
    margin-top: 12px;
  }
  .filebrowser-loading-page .team-tile .row:nth-child(5) {
    width: 174px;
    margin-top: 8px;
  }

  .filebrowser-loading-page .team-tile .circles {
    display: flex;
    flex-direction: row;
  }

  .filebrowser-loading-page .team-tile .circle {
    top: 0;
    left: 0;
    width: 24px;
    height: 24px;
    margin-right: 8px;
    margin-top: 22px;
  }

  .filebrowser-loading-page .public-header {
    background-color: var(--color-bg-secondary, #f0f0f0);
    width: 100%;
    height: 240px;
    margin-bottom: 80px;
  }

  .filebrowser-loading-page .profile-header {
    background-color: var(--color-bg-secondary, #f0f0f0);
    width: 100%;
    height: 64px;
  }

  .filebrowser-loading-page .profile-grid {
    padding: 32px;
    display: grid;
    grid-template-columns: 1fr 24px 1fr .75fr;
    align-items: end;
  }

  .filebrowser-loading-page .profile-grid .row {
    margin-top: 24px;
  }

  .filebrowser-loading-page .profile-grid div:nth-child(4n+1) { width: 148px; }
  .filebrowser-loading-page .profile-grid div:nth-child(4n+3) { width: 56px; }
  .filebrowser-loading-page .profile-grid div:nth-child(4n+4) { width: 99px; }

  .filebrowser-loading-page .profile-grid div:nth-child(1) { width: 61px; margin-top: 0; }

  .filebrowser-loading-page .profile-grid div:nth-child(5) { width: 61px; margin-top: 32px; }
  .filebrowser-loading-page .profile-grid div:nth-child(7) { width: 69px; }
  .filebrowser-loading-page .profile-grid div:nth-child(8) { width: 51px; }

  .filebrowser-loading-page .profile-grid div:nth-child(9) { margin-top: 36px; }
  .filebrowser-loading-page .profile-grid div:nth-child(11) { width: 69px; }
  .filebrowser-loading-page .profile-grid div:nth-child(12) { width: 72px; }

  .filebrowser-loading-page .profile-grid div:nth-child(25),
  .filebrowser-loading-page .profile-grid div:nth-child(27),
  .filebrowser-loading-page .profile-grid div:nth-child(28) { opacity: .8; }

  .filebrowser-loading-page .profile-grid div:nth-child(29),
  .filebrowser-loading-page .profile-grid div:nth-child(31),
  .filebrowser-loading-page .profile-grid div:nth-child(32) { opacity: .4; }

  @media(max-width: 1000px) {
    .filebrowser-loading-page .profile-grid {
      grid-template-columns: 1fr;
    }

    .filebrowser-loading-page .profile-grid div:nth-child(5),
    .filebrowser-loading-page .profile-grid div:nth-child(6),
    .filebrowser-loading-page .profile-grid div:nth-child(7),
    .filebrowser-loading-page .profile-grid div:nth-child(8),
    .filebrowser-loading-page .profile-grid div:nth-child(4n+2),
    .filebrowser-loading-page .profile-grid div:nth-child(4n+3),
    .filebrowser-loading-page .profile-grid div:nth-child(4n+4) { display: none; }
  }

  .filebrowser-loading-page .project-grid {
    padding-left: 32px;
    display: grid;
    grid-row-gap: 22px;
  }

  .filebrowser-loading-page .project-grid .card {
    border: 1px solid var(--color-border, rgba(0, 0, 0, .1));
    border-radius: 4px;
    box-sizing: border-box;
    height: 178px;
    display: flex;
  }

  .filebrowser-loading-page .project-grid .card:nth-child(3) { opacity: .8; }
  .filebrowser-loading-page .project-grid .card:nth-child(4) { opacity: .4; }

  .filebrowser-loading-page .project-grid .card .left {
    width: 196px;
    margin: 16px;
  }

  .filebrowser-loading-page .project-grid .card .right {
    flex: 1 1;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    overflow: hidden;
    margin: 16px 0;
  }

  .filebrowser-loading-page .project-grid .card .left .row:nth-child(1) { width: 107px; }
  .filebrowser-loading-page .project-grid .card .left .row:nth-child(2) { width: 164px; margin-top: 12px; }
  .filebrowser-loading-page .project-grid .card .left .row:nth-child(3) { width: 145px; margin-top: 8px; }
  .filebrowser-loading-page .project-grid .card .left .row:nth-child(4) { width: 158px; margin-top: 8px; }

  .filebrowser-loading-page .project-grid .card .right .row {
    height: 100%;
    flex: 1 1 196px;
    margin-right: 16px;
    border-radius: 6px;
  }

  .filebrowser-loading-page .right-column {
    flex: 0 0 304px;
    margin-right: 16px;
    margin-top: 16px;
    box-sizing: border-box;
    padding-left: 32px;
  }

  .filebrowser-loading-page .right-column .row:nth-child(1) {
    margin-top: 16px;
    width: 120px;
  }
  .filebrowser-loading-page .right-column .row:nth-child(2) {
    margin-top: 18px;
    width: 272px;
  }
  .filebrowser-loading-page .right-column .row:nth-child(3) {
    margin-top: 8px;
    width: 248px;
  }
  .filebrowser-loading-page .right-column .row:nth-child(4) {
    margin-top: 8px;
    width: 272px;
  }
  .filebrowser-loading-page .right-column .row:nth-child(5) {
    margin-top: 8px;
    width: 256px;
  }
  .filebrowser-loading-page .right-column .row:nth-child(6) {
    margin-top: 8px;
    width: 200px;
  }
  .filebrowser-loading-page .right-column .row:nth-child(7) {
    margin-top: 35px;
    width: 88px;
  }
  .filebrowser-loading-page .right-column .row:nth-child(8) {
    margin-top: 22px;
    margin-left: 51px;
    width: 88px;
    opacity: .8;
  }
  .filebrowser-loading-page .right-column .row:nth-child(9) {
    margin-top: 18px;
    margin-left: 51px;
    width: 88px;
    opacity: .4;
  }

  .filebrowser-loading-page .public-left-column {
    padding: 0 0 32px 32px;
    width: 320px;
  }

  .filebrowser-loading-page .public-left-column .row {
    margin-top: 8px;
  }

  .filebrowser-loading-page .public-left-column .row:nth-child(1) {
    width: 120px;
    margin-top: 0;
  }
  .filebrowser-loading-page .public-left-column .row:nth-child(2) {
    width: 272px;
    margin-top: 18px;
  }
  .filebrowser-loading-page .public-left-column .row:nth-child(3) {
    width: 248px;
  }
  .filebrowser-loading-page .public-left-column .row:nth-child(4) {
    width: 272px;
  }
  .filebrowser-loading-page .public-left-column .row:nth-child(5) {
    width: 256px;
    opacity: .8;
  }
  .filebrowser-loading-page .public-left-column .row:nth-child(6) {
    width: 200px;
    opacity: .4;
  }

  /*
  Note: Make sure any changes here are also duplicated in the fullscreen progress bar
  (.progressBar in progress_bar.css)
  */
  #filebrowser-loading-progress-bar {
    height: 5px;
      background: var(--color-bg-brand, #0D99FF);
    position: absolute;
    left: 0;
    animation: filebrowserloadingProgressBar 10s cubic-bezier(.08,.8,.1,1) forwards;
  }

  @keyframes filebrowserloadingProgressBar {
    from { width: 0; }
    to { width: 100%; }
  }


  @media (max-width: 645px) {
    .filebrowser-loading-sidebar {
      display: none;
    }

    .filebrowser-loading-page .page {
      margin-left: 0;
      width: 100%;
    }

    .filebrowser-loading-page .public-header {
      height: 120px;
    }

    .filebrowser-loading-page .file-grid {
      grid-template-columns: 1fr;
      max-width: 400px;
      margin: 0 auto;
      align-self: center;
    }
  }

</style>



      <meta name="mobile-web-app-capable" content="yes">
      <meta name="apple-mobile-web-app-capable" content="yes">
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
      <meta name="viewport" content="initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">

    <meta property="og:title" content="Figma" />
    <meta property="og:site_name" content="Figma" />

    


<link rel="icon" sizes="192x192" href="https://static.figma.com/uploads/1a667ef53b7c4837049399d0593ffca39e0bec9e">
<link rel="icon" sizes="128x128" href="https://static.figma.com/uploads/b6df2735e4cb368306acf5480b50f96e69f96099">

<link rel="icon" type="image/png" href="https://static.figma.com/app/icon/1/favicon.png" />
<!--[if IE]><link rel="shortcut icon" href="https://static.figma.com/app/icon/1/favicon.ico" type="image/vnd.microsoft.icon" /><![endif]-->
<link rel="icon" sizes="any" type="image/svg+xml" href="https://static.figma.com/app/icon/1/favicon.svg">

<link rel="apple-touch-icon" sizes="76x76"   href="https://static.figma.com/app/icon/1/touch-76.png">
<link rel="apple-touch-icon" sizes="120x120" href="https://static.figma.com/app/icon/1/touch-120.png">
<link rel="apple-touch-icon" sizes="152x152" href="https://static.figma.com/app/icon/1/touch-152.png">
<link rel="apple-touch-icon" sizes="167x167" href="https://static.figma.com/app/icon/1/touch-167.png">
<link rel="apple-touch-icon" sizes="180x180" href="https://static.figma.com/app/icon/1/touch-180.png">

<link rel="manifest" href="https://static.figma.com/app/manifest.json" />


      <link href="https:&#47;&#47;www.figma.com&#47;esbuild-artifacts&#47;a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2&#47;css&#47;prototype_app.min.css.br" rel="stylesheet">


        <link rel="preload" href="https:&#47;&#47;static.figma.com&#47;webfont&#47;1&#47;Inter-Regular.woff2?v=3.10" as="font" crossorigin="anonymous">
        <link rel="preload" href="https:&#47;&#47;static.figma.com&#47;webfont&#47;1&#47;Inter-Italic.woff2?v=3.10" as="font" crossorigin="anonymous">
        <link rel="preload" href="https:&#47;&#47;static.figma.com&#47;webfont&#47;1&#47;Inter-Medium.woff2?v=3.10" as="font" crossorigin="anonymous">
        <link rel="preload" href="https:&#47;&#47;static.figma.com&#47;webfont&#47;1&#47;Inter-MediumItalic.woff2?v=3.10" as="font" crossorigin="anonymous">
        <link rel="preload" href="https:&#47;&#47;static.figma.com&#47;webfont&#47;1&#47;Inter-SemiBold.woff2?v=3.10" as="font" crossorigin="anonymous">
        <link rel="preload" href="https:&#47;&#47;static.figma.com&#47;webfont&#47;1&#47;Inter-SemiBoldItalic.woff2?v=3.10" as="font" crossorigin="anonymous">
        <link rel="preload" href="https:&#47;&#47;static.figma.com&#47;webfont&#47;1&#47;DSEG7Classic-Italic-Custom2.woff2" as="font" crossorigin="anonymous">

      <link rel="preload" href="https:&#47;&#47;static.figma.com&#47;fullscreen&#47;3102ec2ab9ca06cac70188fc4dbd381b0065f46a&#47;mobile_viewer&#47;ts-viewer.js.br" as="script" crossorigin="anonymous" nonce="VqsolV7Ot2ROA5YFchq09w==">
      <link rel="preload" as="fetch" id="englishDictionaryUrl" href="https:&#47;&#47;www.figma.com&#47;esbuild-artifacts&#47;a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2&#47;intl&#47;prototype_app.en.json.br" crossorigin="anonymous" />


  </head>
  <body class="feature_flag_xr_debounce_lockup_v3 feature_flag_xr_debounce_scope feature_flag_dt_compare_changes_improvements">
      <div style="position: fixed; top: -1000px; left: -1000px;">
        <div aria-hidden="true" id="font-ui-400-normal">a</div>
        <div aria-hidden="true" id="font-ui-400-italic">a</div>
        <div aria-hidden="true" id="font-ui-500-normal">a</div>
        <div aria-hidden="true" id="font-ui-500-italic">a</div>
        <div aria-hidden="true" id="font-ui-600-normal">a</div>
        <div aria-hidden="true" id="font-ui-600-italic">a</div>

        <div aria-hidden="true" id="font-ui-400-normal-white">a</div>
      </div>

    <div id="react-page"></div>

      <script nonce="VqsolV7Ot2ROA5YFchq09w==">
  let themePreference = window.localStorage.getItem('global-debug-theme-preference')

  if (window.INITIAL_OPTIONS.feature_flags?.ee_system_theme_default && !themePreference) {
    themePreference = 'system'
  }

  let theme
  if (themePreference === 'dark') {
    theme = 'dark'
  } else if (themePreference === 'system') {
    theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  } else {
    theme = 'light'
  }

  document.querySelector("body").dataset.preferredTheme = theme
</script>


      <div class='file-browser-loading-page-container'>
  <div id="filebrowser-loading-page"
       class="filebrowser-loading-page filebrowser-loading-page-layout hidden fadeOut"
       >
    <div class="navbar"></div>

    <!-- HAX: this sidebar is cloned and used outside of this page -->
    <div id="filebrowser-loading-sidebar" class="filebrowser-loading-sidebar">
      <div class="row">
        <div class="circle"></div>
      </div>
      <div class="row">
        <div class="circle"></div>
      </div>
      <div class="row">
        <div class="circle"></div>
      </div>
      <div class="divider"></div>
      <div class="row">
        <div class="circle"></div>
      </div>
      <div class="row">
        <div class="circle"></div>
      </div>
      <div class="row">
        <div class="circle"></div>
      </div>
      <div class="divider"></div>
      <div class="row">
        <div class="circle"></div>
      </div>
      <div class="row">
        <div class="circle"></div>
      </div>
      <div class="row">
        <div class="circle"></div>
      </div>
      <div class="row">
        <div class="circle"></div>
      </div>
    </div>

    <div class="page">


      <div class="columns">


        <div id="loading-content-pane" class="content filebrowser-loading-page">


        </div>

      </div>
    </div>

    <div id="filebrowser-loading-progress-bar"></div>
  </div>
</div>


    <script nonce="VqsolV7Ot2ROA5YFchq09w==" data-entrypoint="prototype_app">
      window.ENTRY_POINT = document.currentScript.dataset.entrypoint
    </script>

    

      <script nonce="VqsolV7Ot2ROA5YFchq09w==">
    window['sentryConfig'] = {
      id: 1285381156962610170,
    }
  </script>

        <script nonce="VqsolV7Ot2ROA5YFchq09w==" src="https:&#47;&#47;www.figma.com&#47;esbuild-artifacts&#47;a5b3c1fc0e57ad057f80e3f78e2f43bea8eb4ee2&#47;js&#47;prototype_app.min.js.br" type="module"></script>


  </body>
</html>
