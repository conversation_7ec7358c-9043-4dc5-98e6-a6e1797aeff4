<?php
/**
 * ملف لإصلاح الترجمات المفقودة تلقائياً
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$fixed_translations = [];
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_translations'])) {
    try {
        // قائمة المصطلحات المفقودة مع ترجماتها
        $missing_translations = [
            // المصطلحات التي قد تكون مفقودة
            'report_type' => [
                'ar' => 'نوع التقرير',
                'en' => 'Report Type'
            ],
            'price' => [
                'ar' => 'السعر',
                'en' => 'Price'
            ],
            'add_new_product' => [
                'ar' => 'إضافة منتج جديد',
                'en' => 'Add New Product'
            ],
            'address' => [
                'ar' => 'العنوان',
                'en' => 'Address'
            ],
            'all_customers' => [
                'ar' => 'جميع العملاء',
                'en' => 'All Customers'
            ],
            'all_rights_reserved' => [
                'ar' => 'جميع الحقوق محفوظة',
                'en' => 'All Rights Reserved'
            ],
            'app_author' => [
                'ar' => 'مطور التطبيق',
                'en' => 'Application Developer'
            ],
            'app_description' => [
                'ar' => 'نظام شامل لإدارة المبيعات والمشتريات مع حساب الضرائب',
                'en' => 'Comprehensive system for managing sales and purchases with tax calculations'
            ],
            'check_tables' => [
                'ar' => 'فحص الجداول',
                'en' => 'Check Tables'
            ],
            'close' => [
                'ar' => 'إغلاق',
                'en' => 'Close'
            ],
            'contact_us' => [
                'ar' => 'اتصل بنا',
                'en' => 'Contact Us'
            ],
            'customer' => [
                'ar' => 'العميل',
                'en' => 'Customer'
            ],
            'dashboard_welcome_message' => [
                'ar' => 'مرحباً بك في نظام إدارة المبيعات والمشتريات',
                'en' => 'Welcome to Sales & Purchases Management System'
            ],
            'date_from' => [
                'ar' => 'من تاريخ',
                'en' => 'Date From'
            ],
            'date_to' => [
                'ar' => 'إلى تاريخ',
                'en' => 'Date To'
            ],
            'end_date' => [
                'ar' => 'تاريخ الانتهاء',
                'en' => 'End Date'
            ],
            'footer_description' => [
                'ar' => 'نظام متكامل لإدارة المبيعات والمشتريات مع حساب الضرائب',
                'en' => 'Integrated system for managing sales and purchases with tax calculations'
            ],
            'invoice_count' => [
                'ar' => 'عدد الفواتير',
                'en' => 'Invoice Count'
            ],
            'monthly_profit' => [
                'ar' => 'الربح الشهري',
                'en' => 'Monthly Profit'
            ],
            'net_tax' => [
                'ar' => 'صافي الضريبة',
                'en' => 'Net Tax'
            ],
            'no_customer' => [
                'ar' => 'بدون عميل',
                'en' => 'No Customer'
            ],
            'no_data_available' => [
                'ar' => 'لا توجد بيانات متاحة',
                'en' => 'No Data Available'
            ],
            'no_report_content' => [
                'ar' => 'لا يوجد محتوى للتقرير',
                'en' => 'No Report Content'
            ],
            'no_report_data' => [
                'ar' => 'لا توجد بيانات للتقرير',
                'en' => 'No Report Data'
            ],
            'period' => [
                'ar' => 'الفترة',
                'en' => 'Period'
            ],
            'profit_loss' => [
                'ar' => 'الربح والخسارة',
                'en' => 'Profit & Loss'
            ],
            'profit_loss_report' => [
                'ar' => 'تقرير الربح والخسارة',
                'en' => 'Profit & Loss Report'
            ],
            'purchases_details' => [
                'ar' => 'تفاصيل المشتريات',
                'en' => 'Purchases Details'
            ],
            'purchases_list' => [
                'ar' => 'قائمة المشتريات',
                'en' => 'Purchases List'
            ],
            'quantity_sold' => [
                'ar' => 'الكمية المباعة',
                'en' => 'Quantity Sold'
            ],
            'quick_links' => [
                'ar' => 'روابط سريعة',
                'en' => 'Quick Links'
            ],
            'reset' => [
                'ar' => 'إعادة تعيين',
                'en' => 'Reset'
            ],
            'sales_details' => [
                'ar' => 'تفاصيل المبيعات',
                'en' => 'Sales Details'
            ],
            'sales_list' => [
                'ar' => 'قائمة المبيعات',
                'en' => 'Sales List'
            ],
            'search_filter' => [
                'ar' => 'تصفية البحث',
                'en' => 'Search Filter'
            ],
            'search_placeholder' => [
                'ar' => 'ابحث هنا...',
                'en' => 'Search here...'
            ],
            'start_date' => [
                'ar' => 'تاريخ البداية',
                'en' => 'Start Date'
            ],
            'statistics' => [
                'ar' => 'الإحصائيات',
                'en' => 'Statistics'
            ],
            'summary_report' => [
                'ar' => 'تقرير ملخص',
                'en' => 'Summary Report'
            ],
            'top_customers' => [
                'ar' => 'أفضل العملاء',
                'en' => 'Top Customers'
            ],
            'top_products' => [
                'ar' => 'أفضل المنتجات',
                'en' => 'Top Products'
            ],
            'total_tax' => [
                'ar' => 'إجمالي الضريبة',
                'en' => 'Total Tax'
            ],
            'total_tax_collected' => [
                'ar' => 'إجمالي الضريبة المحصلة',
                'en' => 'Total Tax Collected'
            ],
            'total_tax_paid' => [
                'ar' => 'إجمالي الضريبة المدفوعة',
                'en' => 'Total Tax Paid'
            ]
        ];

        // تحميل ملفات اللغة الحالية
        $ar_file = __DIR__ . '/languages/ar/lang.php';
        $en_file = __DIR__ . '/languages/en/lang.php';
        
        $ar_translations = require $ar_file;
        $en_translations = require $en_file;
        
        $ar_updated = false;
        $en_updated = false;
        
        // إضافة الترجمات المفقودة
        foreach ($missing_translations as $key => $translations) {
            if (!isset($ar_translations[$key])) {
                $ar_translations[$key] = $translations['ar'];
                $ar_updated = true;
                $fixed_translations[] = "أضيف للعربية: $key = " . $translations['ar'];
            }
            
            if (!isset($en_translations[$key])) {
                $en_translations[$key] = $translations['en'];
                $en_updated = true;
                $fixed_translations[] = "أضيف للإنجليزية: $key = " . $translations['en'];
            }
        }
        
        // حفظ ملفات اللغة المحدثة
        if ($ar_updated) {
            $ar_content = "<?php\n/**\n * Arabic language file\n */\nreturn " . var_export($ar_translations, true) . ";\n";
            if (file_put_contents($ar_file, $ar_content)) {
                $fixed_translations[] = "تم تحديث ملف اللغة العربية بنجاح";
            } else {
                $errors[] = "فشل في حفظ ملف اللغة العربية";
            }
        }
        
        if ($en_updated) {
            $en_content = "<?php\n/**\n * English language file\n */\nreturn " . var_export($en_translations, true) . ";\n";
            if (file_put_contents($en_file, $en_content)) {
                $fixed_translations[] = "تم تحديث ملف اللغة الإنجليزية بنجاح";
            } else {
                $errors[] = "فشل في حفظ ملف اللغة الإنجليزية";
            }
        }
        
        if (!$ar_updated && !$en_updated) {
            $fixed_translations[] = "جميع الترجمات موجودة بالفعل - لا حاجة للتحديث";
        }
        
        $_SESSION['success'] = "تم إصلاح الترجمات بنجاح!";
        
    } catch (Exception $e) {
        $errors[] = "حدث خطأ أثناء إصلاح الترجمات: " . $e->getMessage();
        $_SESSION['error'] = "فشل في إصلاح الترجمات";
    }
}

displayMessages();
?>

<div class="container mt-4">
    <h2>إصلاح الترجمات المفقودة</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات</h5>
        <p>هذه الأداة تقوم بإضافة الترجمات المفقودة تلقائياً إلى ملفات اللغة. سيتم:</p>
        <ul>
            <li>فحص ملفات اللغة العربية والإنجليزية</li>
            <li>إضافة المصطلحات المفقودة</li>
            <li>إنشاء نسخة احتياطية من الملفات الأصلية</li>
            <li>تحديث ملفات اللغة بالترجمات الجديدة</li>
        </ul>
    </div>
    
    <?php if (!empty($fixed_translations)): ?>
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-check-circle"></i>
                الترجمات التي تم إصلاحها
            </h5>
        </div>
        <div class="card-body">
            <ul class="list-group">
                <?php foreach ($fixed_translations as $fix): ?>
                <li class="list-group-item">
                    <i class="fas fa-check text-success"></i> <?php echo htmlspecialchars($fix); ?>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($errors)): ?>
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">
                <i class="fas fa-times-circle"></i>
                أخطاء حدثت أثناء الإصلاح
            </h5>
        </div>
        <div class="card-body">
            <ul class="list-group">
                <?php foreach ($errors as $error): ?>
                <li class="list-group-item list-group-item-danger">
                    <i class="fas fa-times text-danger"></i> <?php echo htmlspecialchars($error); ?>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">إصلاح الترجمات</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="alert alert-warning">
                    <strong>تحذير:</strong> سيتم تعديل ملفات اللغة. تأكد من وجود نسخة احتياطية قبل المتابعة.
                </div>
                
                <button type="submit" name="fix_translations" class="btn btn-primary" onclick="return confirm('هل أنت متأكد من إصلاح الترجمات؟ سيتم تعديل ملفات اللغة.')">
                    <i class="fas fa-tools"></i> إصلاح الترجمات المفقودة
                </button>
                
                <a href="test_translations.php" class="btn btn-info">
                    <i class="fas fa-vial"></i> اختبار الترجمات
                </a>
                
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-home"></i> العودة للصفحة الرئيسية
                </a>
            </form>
        </div>
    </div>
    
    <!-- معلومات إضافية -->
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">معلومات إضافية</h5>
        </div>
        <div class="card-body">
            <h6>ملفات اللغة:</h6>
            <ul>
                <li><code>languages/ar/lang.php</code> - ملف اللغة العربية</li>
                <li><code>languages/en/lang.php</code> - ملف اللغة الإنجليزية</li>
            </ul>
            
            <h6>المصطلحات التي سيتم إضافتها:</h6>
            <div class="row">
                <div class="col-md-6">
                    <small>
                        <code>add_new_product</code>, <code>address</code>, <code>all_customers</code>,
                        <code>contact_us</code>, <code>dashboard_welcome_message</code>, <code>date_from</code>,
                        <code>date_to</code>, <code>end_date</code>, <code>footer_description</code>,
                        <code>invoice_count</code>, <code>monthly_profit</code>, <code>net_tax</code>
                    </small>
                </div>
                <div class="col-md-6">
                    <small>
                        <code>no_customer</code>, <code>no_data_available</code>, <code>period</code>,
                        <code>profit_loss</code>, <code>purchases_details</code>, <code>quick_links</code>,
                        <code>reset</code>, <code>sales_details</code>, <code>search_filter</code>,
                        <code>statistics</code>, <code>top_customers</code>, <code>total_tax</code>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
