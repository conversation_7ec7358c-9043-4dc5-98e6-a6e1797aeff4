<html>
  <head>
    <style>
      .preview > * {
        text-align: justify !important;
        line-height: 1.2 !important;
      }
      .preview > small {
        text-decoration: none !important;
        color: grey !important;
      }
      .preview > .sub-color {
        color: grey !important;
      }
      .preview > h4 {
        margin-top: 1.5em !important;
        margin-bottom: 0.5em !important;
      }
      .preview > body {
        /* size: 7in 9.25in !important; */
        margin: 27mm 16mm 27mm 16mm !important;
      }
      li:before {
        content: "\2014\a0\a0";
      }
      li {
        list-style: none !important;
      }
      .pr-2 {
        padding-right: 5dp !important;
      }
    </style>
  </head>
  <body>
    <div class="preview">
      <table style="width:100%">
        <tbody>
          <tr>
            <td>
              <h1>{{data.profile.name}}</h1>
            </td>
          </tr>
          <tr>
            <table style="width:100%">
              <tr>
                <td
                  style="max-width:100%;-ms-flex-preferred-size: 0;flex-basis: 0;-ms-flex-positive: 1;flex-grow: 1;"
                >
                  <small>{{data.profile.address}}</small>
                  <br />
                  <small>{{data.profile.phone}}</small>
                  <br />
                  <small>{{data.profile.email}}</small>
                </td>
                <td valign="top">
                  <small v-if="data.profile.website"
                    >{{data.profile.website}}</small
                  >
                  <br v-if="data.profile.website" />
                  <small v-if="data.profile.github"
                    >{{data.profile.github}}</small
                  >
                  <br v-if="data.profile.github" />
                  <small v-if="data.profile.linkedin"
                    >{{data.profile.linkedin}}</small
                  >
                </td>
              </tr>
            </table>
          </tr>
          <tr>
            <td>
              <br />
              <h4>SUMMARY</h4>
            </td>
          </tr>
          <tr>
            <td>{{data.profile.summary}}</td>
          </tr>
          <tr>
            <td>
              <br />
              <h4>PROFESSIONAL EXPERIENCE</h4>
            </td>
          </tr>
          <tr v-for="(exp,ind) in data.exps" :key="ind">
            <td>
              <small v-if="exp.start.length">{{exp.start.toUpperCase()}}</small>
              <small v-if="exp.end && exp.start">&ndash;</small>
              <small v-if="exp.end">{{exp.end.toUpperCase()}}</small>
              <br />

              <strong v-if="exp.title.length">
                {{exp.title.toUpperCase()}}
                <br />
              </strong>
              <span v-if="exp.company.length"
                >{{exp.company.toUpperCase()}}</span
              >
              <span v-if="exp.company.length && exp.location.length">,</span>
              <span v-if="exp.location.length"
                >{{exp.location.toUpperCase()}}</span
              >
              <ul v-if="exp.resp.join('')">
                <li v-for="(res,i) in exp.resp" :key="i">{{res}}</li>
              </ul>
            </td>
          </tr>
          <tr>
            <td>
              <br />
              <h4>SKILLS</h4>
            </td>
          </tr>
          <tr>
            <td>
              <table>
                <tbody>
                  <tr v-for="(skill,j) in data.skills" :key="j">
                    <td class="pr-2">
                      <strong>{{skill.type}}</strong>
                    </td>
                    <td v-if="skill.name.join('').length">
                      {{skill.name.join(", ")}}
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td>
              <br />
              <h4>EDUCATION</h4>
            </td>
          </tr>
          <tr v-for="(ed,k) in data.eds" :key="k">
            <td>
              <small v-if="ed.start">{{ed.start.toUpperCase()}}</small>
              <small v-if="ed.end && ed.start">&ndash;</small>
              <small v-if="ed.end">{{ed.end.toUpperCase()}}</small>
              <br v-if="ed.end || ed.start" />
              <strong v-if="ed.institute">
                {{ed.institute}}
                <br />
              </strong>
              {{ed.location}}
              <br v-if="ed.location" />
              <span v-if="ed.degree">{{ed.degree}}</span>
              <span v-if="ed.major">{{ed.major}}</span>
              <br v-if="ed.major || ed.degree" />
            </td>
          </tr>
        </tbody>
      </table>
      <table>
        <tbody>
          <tr>
            <td>
              <br />
              <h4>Projects</h4>
            </td>
          </tr>
          <tr v-for="(proj,l) in data.projs" :key="l">
            <td>
              <small v-if="proj.start">{{proj.start.toUpperCase()}}</small>
              <small v-if="proj.start || proj.end">&ndash;</small>
              <small v-if="proj.end">{{proj.end.toUpperCase()}}</small>
              <br />
              <strong v-if="proj.title">
                {{proj.title.toUpperCase()}}
                <br />
              </strong>
              <span v-if="proj.link">
                Live:
                <a :href="proj.link" :title="proj.link">{{proj.link}}</a>
              </span>
              <ul v-if="proj.resp.join('').length">
                <li v-for="(res,m) in proj.resp" :key="m">{{res}}</li>
                <li v-if="proj.tools.join('')">
                  <strong>Technologies:</strong>
                  {{proj.tools.join(", ")}}
                </li>
              </ul>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </body>
</html>
