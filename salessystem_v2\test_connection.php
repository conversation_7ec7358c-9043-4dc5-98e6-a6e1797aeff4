<?php
// اختبار الاتصال بقواعد البيانات مع بيانات الاتصال الجديدة
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الاتصال بقواعد البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-database"></i> اختبار الاتصال بقواعد البيانات
                </h1>
                
                <!-- اختبار قاعدة البيانات الرئيسية -->
                <div class="test-section">
                    <h3><i class="fas fa-server"></i> قاعدة البيانات الرئيسية</h3>
                    
                    <?php
                    echo "<div class='mb-3'>";
                    echo "<h5>بيانات الاتصال:</h5>";
                    echo "<ul>";
                    echo "<li><strong>الخادم:</strong> localhost</li>";
                    echo "<li><strong>المستخدم:</strong> sales01</li>";
                    echo "<li><strong>قاعدة البيانات:</strong> u193708811_system_main</li>";
                    echo "</ul>";
                    echo "</div>";
                    
                    // اختبار الاتصال بقاعدة البيانات الرئيسية
                    $main_connection = new mysqli('localhost', 'sales01', 'dNz35nd5@', 'u193708811_system_main');
                    
                    if ($main_connection->connect_error) {
                        echo "<div class='alert alert-danger'>";
                        echo "<i class='fas fa-times'></i> <strong>فشل الاتصال:</strong> " . $main_connection->connect_error;
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-success'>";
                        echo "<i class='fas fa-check'></i> <strong>نجح الاتصال!</strong> تم الاتصال بقاعدة البيانات الرئيسية بنجاح";
                        echo "</div>";
                        
                        $main_connection->set_charset("utf8mb4");
                        
                        // فحص الجداول الموجودة
                        $tables_result = $main_connection->query("SHOW TABLES");
                        if ($tables_result) {
                            echo "<h5>الجداول الموجودة:</h5>";
                            echo "<div class='row'>";
                            
                            while ($table_row = $tables_result->fetch_array()) {
                                $table_name = $table_row[0];
                                
                                // عدد السجلات في كل جدول
                                $count_result = $main_connection->query("SELECT COUNT(*) as count FROM `$table_name`");
                                $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                                
                                echo "<div class='col-md-4 mb-2'>";
                                echo "<div class='card'>";
                                echo "<div class='card-body text-center'>";
                                echo "<h6 class='card-title'>$table_name</h6>";
                                echo "<span class='badge bg-primary'>$count سجل</span>";
                                echo "</div>";
                                echo "</div>";
                                echo "</div>";
                            }
                            
                            echo "</div>";
                        }
                        
                        $main_connection->close();
                    }
                    ?>
                </div>

                <!-- اختبار قاعدة بيانات العمليات -->
                <div class="test-section">
                    <h3><i class="fas fa-cogs"></i> قاعدة بيانات العمليات</h3>
                    
                    <?php
                    echo "<div class='mb-3'>";
                    echo "<h5>بيانات الاتصال:</h5>";
                    echo "<ul>";
                    echo "<li><strong>الخادم:</strong> localhost</li>";
                    echo "<li><strong>المستخدم:</strong> sales02</li>";
                    echo "<li><strong>قاعدة البيانات:</strong> u193708811_operations</li>";
                    echo "</ul>";
                    echo "</div>";
                    
                    // اختبار الاتصال بقاعدة بيانات العمليات
                    $operations_connection = new mysqli('localhost', 'sales02', 'dNz35nd5@', 'u193708811_operations');
                    
                    if ($operations_connection->connect_error) {
                        echo "<div class='alert alert-danger'>";
                        echo "<i class='fas fa-times'></i> <strong>فشل الاتصال:</strong> " . $operations_connection->connect_error;
                        echo "</div>";
                        
                        // محاولة إنشاء قاعدة البيانات
                        echo "<div class='mt-3'>";
                        echo "<h5>محاولة إنشاء قاعدة البيانات:</h5>";
                        
                        $temp_connection = new mysqli('localhost', 'sales02', 'dNz35nd5@');
                        if (!$temp_connection->connect_error) {
                            $temp_connection->set_charset("utf8mb4");
                            
                            $create_db_sql = "CREATE DATABASE IF NOT EXISTS `u193708811_operations` 
                                             CHARACTER SET utf8mb4 
                                             COLLATE utf8mb4_general_ci";
                            
                            if ($temp_connection->query($create_db_sql)) {
                                echo "<div class='alert alert-success'>";
                                echo "<i class='fas fa-check'></i> تم إنشاء قاعدة البيانات بنجاح!";
                                echo "</div>";
                                
                                // إعادة اختبار الاتصال
                                $operations_connection = new mysqli('localhost', 'sales02', 'dNz35nd5@', 'u193708811_operations');
                                if (!$operations_connection->connect_error) {
                                    echo "<div class='alert alert-success'>";
                                    echo "<i class='fas fa-check'></i> تم الاتصال بقاعدة البيانات الجديدة بنجاح!";
                                    echo "</div>";
                                }
                            } else {
                                echo "<div class='alert alert-danger'>";
                                echo "<i class='fas fa-times'></i> فشل في إنشاء قاعدة البيانات: " . $temp_connection->error;
                                echo "</div>";
                            }
                            
                            $temp_connection->close();
                        } else {
                            echo "<div class='alert alert-danger'>";
                            echo "<i class='fas fa-times'></i> فشل الاتصال بالخادم: " . $temp_connection->connect_error;
                            echo "</div>";
                        }
                        echo "</div>";
                        
                    } else {
                        echo "<div class='alert alert-success'>";
                        echo "<i class='fas fa-check'></i> <strong>نجح الاتصال!</strong> تم الاتصال بقاعدة بيانات العمليات بنجاح";
                        echo "</div>";
                        
                        $operations_connection->set_charset("utf8mb4");
                        
                        // فحص الجداول الموجودة
                        $tables_result = $operations_connection->query("SHOW TABLES");
                        if ($tables_result && $tables_result->num_rows > 0) {
                            echo "<h5>الجداول الموجودة:</h5>";
                            echo "<div class='row'>";
                            
                            while ($table_row = $tables_result->fetch_array()) {
                                $table_name = $table_row[0];
                                
                                // عدد السجلات في كل جدول
                                $count_result = $operations_connection->query("SELECT COUNT(*) as count FROM `$table_name`");
                                $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                                
                                echo "<div class='col-md-4 mb-2'>";
                                echo "<div class='card'>";
                                echo "<div class='card-body text-center'>";
                                echo "<h6 class='card-title'>$table_name</h6>";
                                echo "<span class='badge bg-info'>$count سجل</span>";
                                echo "</div>";
                                echo "</div>";
                                echo "</div>";
                            }
                            
                            echo "</div>";
                        } else {
                            echo "<div class='alert alert-info'>";
                            echo "<i class='fas fa-info-circle'></i> قاعدة البيانات فارغة - لا توجد جداول بعد";
                            echo "</div>";
                        }
                        
                        $operations_connection->close();
                    }
                    ?>
                </div>

                <!-- اختبار صلاحيات المستخدمين -->
                <div class="test-section">
                    <h3><i class="fas fa-user-shield"></i> اختبار صلاحيات المستخدمين</h3>
                    
                    <?php
                    echo "<h5>صلاحيات المستخدم sales01:</h5>";
                    $main_test = new mysqli('localhost', 'sales01', 'dNz35nd5@', 'u193708811_system_main');
                    if (!$main_test->connect_error) {
                        $main_test->set_charset("utf8mb4");
                        
                        // اختبار صلاحية القراءة
                        $read_test = $main_test->query("SELECT 1 FROM users LIMIT 1");
                        echo "<p class='" . ($read_test ? "success" : "error") . "'>";
                        echo "<i class='fas fa-" . ($read_test ? "check" : "times") . "'></i> ";
                        echo "صلاحية القراءة: " . ($read_test ? "متاحة" : "غير متاحة");
                        echo "</p>";
                        
                        // اختبار صلاحية الكتابة (محاولة إنشاء جدول مؤقت)
                        $write_test = $main_test->query("CREATE TEMPORARY TABLE test_table (id INT)");
                        echo "<p class='" . ($write_test ? "success" : "error") . "'>";
                        echo "<i class='fas fa-" . ($write_test ? "check" : "times") . "'></i> ";
                        echo "صلاحية الكتابة: " . ($write_test ? "متاحة" : "غير متاحة");
                        echo "</p>";
                        
                        $main_test->close();
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال</p>";
                    }
                    
                    echo "<h5>صلاحيات المستخدم sales02:</h5>";
                    $ops_test = new mysqli('localhost', 'sales02', 'dNz35nd5@');
                    if (!$ops_test->connect_error) {
                        $ops_test->set_charset("utf8mb4");
                        
                        // اختبار صلاحية إنشاء قاعدة البيانات
                        $create_db_test = $ops_test->query("CREATE DATABASE IF NOT EXISTS test_db_temp");
                        echo "<p class='" . ($create_db_test ? "success" : "error") . "'>";
                        echo "<i class='fas fa-" . ($create_db_test ? "check" : "times") . "'></i> ";
                        echo "صلاحية إنشاء قاعدة البيانات: " . ($create_db_test ? "متاحة" : "غير متاحة");
                        echo "</p>";
                        
                        // حذف قاعدة البيانات التجريبية
                        if ($create_db_test) {
                            $ops_test->query("DROP DATABASE IF EXISTS test_db_temp");
                        }
                        
                        $ops_test->close();
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال</p>";
                    }
                    ?>
                </div>

                <!-- ملخص النتائج -->
                <div class="test-section">
                    <h3><i class="fas fa-clipboard-check"></i> ملخص النتائج</h3>
                    
                    <?php
                    // إعادة اختبار الاتصالات للملخص
                    $main_ok = false;
                    $ops_ok = false;
                    
                    $main_test = new mysqli('localhost', 'sales01', 'dNz35nd5@', 'u193708811_system_main');
                    if (!$main_test->connect_error) {
                        $main_ok = true;
                        $main_test->close();
                    }
                    
                    $ops_test = new mysqli('localhost', 'sales02', 'dNz35nd5@', 'u193708811_operations');
                    if (!$ops_test->connect_error) {
                        $ops_ok = true;
                        $ops_test->close();
                    }
                    
                    if ($main_ok && $ops_ok) {
                        echo "<div class='alert alert-success'>";
                        echo "<h5><i class='fas fa-check-circle'></i> جميع الاتصالات تعمل بنجاح!</h5>";
                        echo "<p>يمكنك الآن استخدام النظام بأمان.</p>";
                        echo "</div>";
                    } elseif ($main_ok && !$ops_ok) {
                        echo "<div class='alert alert-warning'>";
                        echo "<h5><i class='fas fa-exclamation-triangle'></i> قاعدة البيانات الرئيسية تعمل، قاعدة بيانات العمليات تحتاج إعداد</h5>";
                        echo "<p>قم بتشغيل صفحة التحديث لإنشاء قاعدة بيانات العمليات.</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<h5><i class='fas fa-times-circle'></i> يوجد مشاكل في الاتصال</h5>";
                        echo "<p>تحقق من بيانات الاتصال وصلاحيات المستخدمين.</p>";
                        echo "</div>";
                    }
                    ?>
                </div>

                <!-- أزرار التحكم -->
                <div class="test-section text-center">
                    <h3><i class="fas fa-tools"></i> الخطوات التالية</h3>
                    
                    <div class="btn-group" role="group">
                        <a href="update_database.php" class="btn btn-primary">
                            <i class="fas fa-database"></i> تشغيل التحديث
                        </a>
                        <a href="test_system.php" class="btn btn-info">
                            <i class="fas fa-test-tube"></i> اختبار النظام
                        </a>
                        <a href="index.php" class="btn btn-success">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <button onclick="location.reload()" class="btn btn-warning">
                            <i class="fas fa-refresh"></i> إعادة الاختبار
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
