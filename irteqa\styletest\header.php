<!DOCTYPE html>
<!-- Coding By CodingNepal - youtube.com/codingnepal -->
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>Realtime Chat App | CodingNepal</title>
  <link rel="stylesheet" href="style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.2/css/all.min.css"/>
</head>

<body>
<script>
    let isChatBoxAdded = false;
    const handleChatIconClick = () => {
      const chatWrapper = document.createElement('div');
      chatWrapper.className = 'wrappercht';
      chatWrapper.innerHTML = `
      <section class="chat-area">
          <header>
            <img src="07.png" alt="">
            <div class="details">
              <span></span>
              <p></p>
            </div>
            <button id="closeChat">&#10006;</button>
          </header>
          <div class="chat-box"></div>
          <form action="#" class="typing-area">
            <input type="text" class="incoming_id" name="incoming_id" value="" hidden>
            <input type="text" name="message" class="input-field" placeholder="أكتب هنا...." autocomplete="off">
            <button><i class="fab fa-telegram-plane"></i></button>
          </form>
        </section>
      `;
      document.body.appendChild(chatWrapper);
      isChatBoxAdded = true;
      const closeChatButton = chatWrapper.querySelector('#closeChat');
      closeChatButton.addEventListener('click', () => {
        chatWrapper.classList.add('hidden');
        chatIcon.style.display = 'block';
        isChatBoxAdded = false;
      });
      setTimeout(() => {
        chatWrapper.classList.add('visible');
      }, 100);
    };
    const chatIcon = document.createElement('div');
    chatIcon.className = 'chat-icon';
    chatIcon.innerHTML = '&#128172;';
    chatIcon.style.position = 'fixed';
    chatIcon.style.bottom = '20px';
    chatIcon.style.right = '20px';
    chatIcon.style.fontSize = '24px';
    chatIcon.style.cursor = 'pointer';
    chatIcon.style.backgroundColor = '#528ec9';
    chatIcon.style.color = '#fff';
    chatIcon.style.padding = '10px';
    chatIcon.style.borderRadius = '50%';
    chatIcon.style.zIndex = '1000';
    chatIcon.addEventListener('click', () => {
      if (!isChatBoxAdded) {
        handleChatIconClick();
        chatIcon.style.display = 'none';
      }
    });
    document.body.appendChild(chatIcon);
  </script>

</body>
