<?php
/**
 * صفحة الإعدادات الرئيسية
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// فحص تسجيل الدخول قبل إرسال أي محتوى
redirectIfNotLoggedIn();

require_once __DIR__ . '/includes/header.php';

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// التحقق من وجود جدول الإعدادات
$check_table = $db->query("SHOW TABLES LIKE 'settings'");
$table_exists = ($check_table && $check_table->num_rows > 0);

if (!$table_exists) {
    $_SESSION['error'] = "جدول الإعدادات غير موجود. يرجى إنشاؤه أولاً.";
    header("Location: create_settings_table.php");
    exit();
}

// دالة للحصول على قيمة إعداد
function getSetting($db, $key, $default = '') {
    try {
        $stmt = $db->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->bind_param("s", $key);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($row = $result->fetch_assoc()) {
            return $row['setting_value'];
        }
        return $default;
    } catch (Exception $e) {
        return $default;
    }
}

// دالة لحفظ إعداد
function saveSetting($db, $key, $value) {
    try {
        $stmt = $db->prepare("INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value), updated_at = CURRENT_TIMESTAMP");
        $stmt->bind_param("ss", $key, $value);
        return $stmt->execute();
    } catch (Exception $e) {
        return false;
    }
}

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    $saved_count = 0;
    $errors = [];
    
    // قائمة الإعدادات المسموح بتحديثها
    $allowed_settings = [
        'company_name', 'company_address', 'company_phone', 'company_email', 
        'company_tax_number', 'company_website', 'default_language', 'currency_symbol',
        'currency_code', 'decimal_places', 'date_format', 'time_format', 'timezone',
        'tax_enabled', 'default_tax_rate', 'tax_number_required', 'tax_inclusive_pricing',
        'invoice_prefix', 'invoice_number_length', 'invoice_footer', 'auto_invoice_number',
        'items_per_page', 'backup_enabled', 'maintenance_mode', 'debug_mode',
        'session_timeout', 'password_min_length', 'login_attempts', 'account_lockout_time'
    ];
    
    foreach ($allowed_settings as $setting_key) {
        if (isset($_POST[$setting_key])) {
            $value = trim($_POST[$setting_key]);
            
            // التحقق من صحة بعض الإعدادات
            if ($setting_key === 'company_email' && !empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $errors[] = "البريد الإلكتروني للشركة غير صحيح";
                continue;
            }
            
            if ($setting_key === 'company_website' && !empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                $errors[] = "موقع الشركة الإلكتروني غير صحيح";
                continue;
            }
            
            if (in_array($setting_key, ['default_tax_rate', 'decimal_places', 'invoice_number_length', 'items_per_page', 'session_timeout', 'password_min_length', 'login_attempts', 'account_lockout_time']) && !is_numeric($value)) {
                $errors[] = "القيمة يجب أن تكون رقماً: $setting_key";
                continue;
            }
            
            if (saveSetting($db, $setting_key, $value)) {
                $saved_count++;
            } else {
                $errors[] = "فشل في حفظ الإعداد: $setting_key";
            }
        }
    }
    
    // معالجة الإعدادات المنطقية (checkboxes)
    $boolean_settings = ['tax_enabled', 'tax_number_required', 'tax_inclusive_pricing', 'auto_invoice_number', 'backup_enabled', 'maintenance_mode', 'debug_mode'];
    foreach ($boolean_settings as $setting_key) {
        $value = isset($_POST[$setting_key]) ? '1' : '0';
        if (saveSetting($db, $setting_key, $value)) {
            $saved_count++;
        }
    }
    
    if ($saved_count > 0 && empty($errors)) {
        $_SESSION['success'] = "تم حفظ الإعدادات بنجاح ($saved_count إعداد)";
    } elseif (!empty($errors)) {
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// جلب جميع الإعدادات
$settings = [];
try {
    $result = $db->query("SELECT setting_key, setting_value, category FROM settings ORDER BY category, setting_key");
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
} catch (Exception $e) {
    $_SESSION['error'] = "خطأ في جلب الإعدادات: " . $e->getMessage();
}

displayMessages();
?>

<!-- تضمين ملف CSS الخاص بالإعدادات -->
<link rel="stylesheet" href="assets/css/settings.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card settings-card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-cogs"></i>
                        إعدادات النظام
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" id="settingsForm">
                        <!-- التبويبات -->
                        <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="company-tab" data-bs-toggle="tab" data-bs-target="#company" type="button" role="tab">
                                    <i class="fas fa-building"></i> إعدادات الشركة
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                                    <i class="fas fa-sliders-h"></i> الإعدادات العامة
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="tax-tab" data-bs-toggle="tab" data-bs-target="#tax" type="button" role="tab">
                                    <i class="fas fa-percentage"></i> إعدادات الضريبة
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="invoice-tab" data-bs-toggle="tab" data-bs-target="#invoice" type="button" role="tab">
                                    <i class="fas fa-file-invoice"></i> إعدادات الفواتير
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                                    <i class="fas fa-server"></i> إعدادات النظام
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                                    <i class="fas fa-shield-alt"></i> إعدادات الأمان
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="tools-tab" data-bs-toggle="tab" data-bs-target="#tools" type="button" role="tab">
                                    <i class="fas fa-tools"></i> أدوات النظام
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content mt-4" id="settingsTabContent">
                            <!-- تبويب إعدادات الشركة -->
                            <div class="tab-pane fade show active" id="company" role="tabpanel">
                                <h5 class="mb-3">
                                    <i class="fas fa-building text-primary"></i>
                                    معلومات الشركة
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="company_name" class="form-label">اسم الشركة *</label>
                                            <input type="text" class="form-control" id="company_name" name="company_name" 
                                                   value="<?php echo htmlspecialchars($settings['company_name'] ?? ''); ?>" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="company_phone" class="form-label">هاتف الشركة</label>
                                            <input type="text" class="form-control" id="company_phone" name="company_phone" 
                                                   value="<?php echo htmlspecialchars($settings['company_phone'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="company_email" class="form-label"><?php echo __('email_address'); ?></label>
                                            <input type="email" class="form-control" id="company_email" name="company_email" 
                                                   value="<?php echo htmlspecialchars($settings['company_email'] ?? ''); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="company_website" class="form-label">الموقع الإلكتروني</label>
                                            <input type="url" class="form-control" id="company_website" name="company_website" 
                                                   value="<?php echo htmlspecialchars($settings['company_website'] ?? ''); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_address" class="form-label">عنوان الشركة</label>
                                    <textarea class="form-control" id="company_address" name="company_address" rows="3"><?php echo htmlspecialchars($settings['company_address'] ?? ''); ?></textarea>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="company_tax_number" class="form-label">الرقم الضريبي للشركة</label>
                                    <input type="text" class="form-control" id="company_tax_number" name="company_tax_number" 
                                           value="<?php echo htmlspecialchars($settings['company_tax_number'] ?? ''); ?>">
                                    <small class="form-text text-muted">الرقم الضريبي المسجل لدى الهيئة الضريبية</small>
                                </div>
                            </div>
                            
                            <!-- تبويب الإعدادات العامة -->
                            <div class="tab-pane fade" id="general" role="tabpanel">
                                <h5 class="mb-3">
                                    <i class="fas fa-sliders-h text-info"></i>
                                    الإعدادات العامة للنظام
                                </h5>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="default_language" class="form-label">اللغة الافتراضية</label>
                                            <select class="form-select" id="default_language" name="default_language">
                                                <option value="ar" <?php echo ($settings['default_language'] ?? '') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                                <option value="en" <?php echo ($settings['default_language'] ?? '') === 'en' ? 'selected' : ''; ?>>English</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="currency_symbol" class="form-label">رمز العملة</label>
                                            <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" 
                                                   value="<?php echo htmlspecialchars($settings['currency_symbol'] ?? 'ر.س'); ?>">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="currency_code" class="form-label">كود العملة</label>
                                            <input type="text" class="form-control" id="currency_code" name="currency_code" 
                                                   value="<?php echo htmlspecialchars($settings['currency_code'] ?? 'SAR'); ?>">
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="decimal_places" class="form-label">عدد الخانات العشرية</label>
                                            <input type="number" class="form-control" id="decimal_places" name="decimal_places" 
                                                   value="<?php echo htmlspecialchars($settings['decimal_places'] ?? '2'); ?>" min="0" max="4">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="date_format" class="form-label">تنسيق التاريخ</label>
                                            <select class="form-select" id="date_format" name="date_format">
                                                <option value="d/m/Y" <?php echo ($settings['date_format'] ?? '') === 'd/m/Y' ? 'selected' : ''; ?>>31/12/2023</option>
                                                <option value="Y-m-d" <?php echo ($settings['date_format'] ?? '') === 'Y-m-d' ? 'selected' : ''; ?>>2023-12-31</option>
                                                <option value="m/d/Y" <?php echo ($settings['date_format'] ?? '') === 'm/d/Y' ? 'selected' : ''; ?>>12/31/2023</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="timezone" class="form-label">المنطقة الزمنية</label>
                                            <select class="form-select" id="timezone" name="timezone">
                                                <option value="Asia/Riyadh" <?php echo ($settings['timezone'] ?? '') === 'Asia/Riyadh' ? 'selected' : ''; ?>>الرياض (GMT+3)</option>
                                                <option value="Asia/Dubai" <?php echo ($settings['timezone'] ?? '') === 'Asia/Dubai' ? 'selected' : ''; ?>>دبي (GMT+4)</option>
                                                <option value="Asia/Kuwait" <?php echo ($settings['timezone'] ?? '') === 'Asia/Kuwait' ? 'selected' : ''; ?>>الكويت (GMT+3)</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب إعدادات الضريبة -->
                            <div class="tab-pane fade" id="tax" role="tabpanel">
                                <h5 class="mb-3">
                                    <i class="fas fa-percentage text-warning"></i>
                                    إعدادات الضريبة
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="tax_enabled" name="tax_enabled"
                                                   <?php echo ($settings['tax_enabled'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="tax_enabled">
                                                تفعيل نظام الضريبة
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="default_tax_rate" class="form-label">نسبة الضريبة الافتراضية (%)</label>
                                            <input type="number" class="form-control" id="default_tax_rate" name="default_tax_rate"
                                                   value="<?php echo htmlspecialchars($settings['default_tax_rate'] ?? '15'); ?>"
                                                   min="0" max="100" step="0.01">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="tax_number_required" name="tax_number_required"
                                                   <?php echo ($settings['tax_number_required'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="tax_number_required">
                                                إجبارية الرقم الضريبي للعملاء
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="tax_inclusive_pricing" name="tax_inclusive_pricing"
                                                   <?php echo ($settings['tax_inclusive_pricing'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="tax_inclusive_pricing">
                                                الأسعار شاملة الضريبة
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب إعدادات الفواتير -->
                            <div class="tab-pane fade" id="invoice" role="tabpanel">
                                <h5 class="mb-3">
                                    <i class="fas fa-file-invoice text-success"></i>
                                    إعدادات الفواتير
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="invoice_prefix" class="form-label">بادئة رقم الفاتورة</label>
                                            <input type="text" class="form-control" id="invoice_prefix" name="invoice_prefix"
                                                   value="<?php echo htmlspecialchars($settings['invoice_prefix'] ?? 'INV-'); ?>">
                                            <small class="form-text text-muted">مثال: INV-001, SALE-001</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="invoice_number_length" class="form-label">طول رقم الفاتورة</label>
                                            <input type="number" class="form-control" id="invoice_number_length" name="invoice_number_length"
                                                   value="<?php echo htmlspecialchars($settings['invoice_number_length'] ?? '6'); ?>"
                                                   min="3" max="10">
                                            <small class="form-text text-muted">عدد الأرقام في رقم الفاتورة</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="auto_invoice_number" name="auto_invoice_number"
                                               <?php echo ($settings['auto_invoice_number'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="auto_invoice_number">
                                            ترقيم الفواتير تلقائياً
                                        </label>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="invoice_footer" class="form-label">تذييل الفاتورة</label>
                                    <textarea class="form-control" id="invoice_footer" name="invoice_footer" rows="3"><?php echo htmlspecialchars($settings['invoice_footer'] ?? 'شكراً لتعاملكم معنا'); ?></textarea>
                                    <small class="form-text text-muted">النص الذي يظهر في أسفل الفاتورة</small>
                                </div>
                            </div>

                            <!-- تبويب إعدادات النظام -->
                            <div class="tab-pane fade" id="system" role="tabpanel">
                                <h5 class="mb-3">
                                    <i class="fas fa-server text-secondary"></i>
                                    إعدادات النظام
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="items_per_page" class="form-label">عدد العناصر في الصفحة</label>
                                            <select class="form-select" id="items_per_page" name="items_per_page">
                                                <option value="5" <?php echo ($settings['items_per_page'] ?? '') === '5' ? 'selected' : ''; ?>>5</option>
                                                <option value="10" <?php echo ($settings['items_per_page'] ?? '') === '10' ? 'selected' : ''; ?>>10</option>
                                                <option value="25" <?php echo ($settings['items_per_page'] ?? '') === '25' ? 'selected' : ''; ?>>25</option>
                                                <option value="50" <?php echo ($settings['items_per_page'] ?? '') === '50' ? 'selected' : ''; ?>>50</option>
                                                <option value="100" <?php echo ($settings['items_per_page'] ?? '') === '100' ? 'selected' : ''; ?>>100</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="backup_enabled" name="backup_enabled"
                                                   <?php echo ($settings['backup_enabled'] ?? '1') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="backup_enabled">
                                                تفعيل النسخ الاحتياطي التلقائي
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode"
                                                   <?php echo ($settings['maintenance_mode'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="maintenance_mode">
                                                وضع الصيانة
                                            </label>
                                            <small class="form-text text-muted">منع الوصول للنظام مؤقتاً</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="debug_mode" name="debug_mode"
                                                   <?php echo ($settings['debug_mode'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="debug_mode">
                                                وضع التطوير
                                            </label>
                                            <small class="form-text text-muted">إظهار رسائل الأخطاء التفصيلية</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب إعدادات الأمان -->
                            <div class="tab-pane fade" id="security" role="tabpanel">
                                <h5 class="mb-3">
                                    <i class="fas fa-shield-alt text-danger"></i>
                                    إعدادات الأمان
                                </h5>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="session_timeout" class="form-label">مهلة انتهاء الجلسة (بالثواني)</label>
                                            <input type="number" class="form-control" id="session_timeout" name="session_timeout"
                                                   value="<?php echo htmlspecialchars($settings['session_timeout'] ?? '3600'); ?>"
                                                   min="300" max="86400">
                                            <small class="form-text text-muted">3600 ثانية = ساعة واحدة</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password_min_length" class="form-label">الحد الأدنى لطول كلمة المرور</label>
                                            <input type="number" class="form-control" id="password_min_length" name="password_min_length"
                                                   value="<?php echo htmlspecialchars($settings['password_min_length'] ?? '6'); ?>"
                                                   min="4" max="20">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="login_attempts" class="form-label">عدد محاولات تسجيل الدخول المسموحة</label>
                                            <input type="number" class="form-control" id="login_attempts" name="login_attempts"
                                                   value="<?php echo htmlspecialchars($settings['login_attempts'] ?? '5'); ?>"
                                                   min="3" max="10">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="account_lockout_time" class="form-label">مدة قفل الحساب (بالثواني)</label>
                                            <input type="number" class="form-control" id="account_lockout_time" name="account_lockout_time"
                                                   value="<?php echo htmlspecialchars($settings['account_lockout_time'] ?? '900'); ?>"
                                                   min="300" max="3600">
                                            <small class="form-text text-muted">900 ثانية = 15 دقيقة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويب أدوات النظام -->
                            <div class="tab-pane fade" id="tools" role="tabpanel">
                                <h5 class="mb-3">
                                    <i class="fas fa-tools text-dark"></i>
                                    أدوات النظام والصيانة
                                </h5>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> معلومات</h6>
                                    <p>هذه الأدوات تساعدك في إدارة وصيانة النظام. يمكنك الوصول إلى صفحة أدوات النظام الكاملة للمزيد من الخيارات.</p>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <i class="fas fa-tools fa-3x text-primary mb-3"></i>
                                                <h6>أدوات النظام الكاملة</h6>
                                                <p class="text-muted">الوصول إلى جميع أدوات الفحص والاختبار والصيانة</p>
                                                <a href="system_tools.php" class="btn btn-primary" target="_blank">
                                                    <i class="fas fa-external-link-alt"></i> فتح أدوات النظام
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <i class="fas fa-clipboard-check fa-3x text-success mb-3"></i>
                                                <h6>اختبار النظام السريع</h6>
                                                <p class="text-muted">فحص سريع لحالة النظام والمكونات الأساسية</p>
                                                <a href="test_system.php" class="btn btn-success" target="_blank">
                                                    <i class="fas fa-play"></i> تشغيل الاختبار
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <i class="fas fa-database fa-3x text-warning mb-3"></i>
                                                <h6>إدارة قاعدة البيانات</h6>
                                                <p class="text-muted">أدوات إنشاء وإصلاح الجداول</p>
                                                <a href="create_settings_table.php" class="btn btn-warning" target="_blank">
                                                    <i class="fas fa-cogs"></i> إدارة الجداول
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <i class="fas fa-language fa-3x text-info mb-3"></i>
                                                <h6>إدارة الترجمات</h6>
                                                <p class="text-muted">فحص وإصلاح ملفات الترجمة</p>
                                                <a href="test_translations.php" class="btn btn-info" target="_blank">
                                                    <i class="fas fa-globe"></i> فحص الترجمات
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle"></i> تنبيه</h6>
                                    <p>استخدم أدوات النظام بحذر. تأكد من إنشاء نسخة احتياطية قبل إجراء أي تغييرات مهمة.</p>
                                </div>
                            </div>

                            <!-- أزرار الحفظ -->
                            <div class="mt-4 text-center">
                                <button type="submit" name="save_settings" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save"></i> حفظ جميع الإعدادات
                                </button>
                                <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="resetForm()">
                                    <i class="fas fa-undo"></i> إعادة تعيين
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ سيتم فقدان جميع التغييرات غير المحفوظة.')) {
        document.getElementById('settingsForm').reset();
    }
}

// التحقق من النموذج قبل الإرسال
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    const companyName = document.getElementById('company_name').value.trim();
    if (!companyName) {
        e.preventDefault();
        alert('يرجى إدخال اسم الشركة');
        document.getElementById('company_name').focus();
        return false;
    }
});
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
