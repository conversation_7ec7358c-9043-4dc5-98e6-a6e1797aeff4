# نظام المدير - دليل الاستخدام الشامل

## 🛡️ نظرة عامة

تم إنشاء نظام مدير منفصل بصلاحيات شاملة لإدارة جميع المستخدمين ومراقبة جميع العمليات في نظام إدارة المبيعات.

## ⚡ الإعداد التلقائي

### 🔄 إنشاء الجداول تلقائياً
النظام الآن يتحقق من وجود جداول المدير تلقائياً ويقوم بإنشائها عند الحاجة:

- **عند تحميل أي صفحة**: يتم فحص وجود الجداول المطلوبة
- **إنشاء تلقائي**: إذا لم تكن الجداول موجودة، يتم إنشاؤها فوراً
- **المدير الافتراضي**: يتم إنشاؤه تلقائياً إذا لم يكن موجود
- **فحص السلامة**: يتم التحقق من بنية الجداول وسلامتها

### 🛠️ الدوال المضافة للإعداد التلقائي:

#### `ensureAdminTables()`
- **الوظيفة**: التحقق من وجود جداول المدير وإنشاؤها عند الحاجة
- **التشغيل**: تلقائي مع كل تحميل صفحة
- **المهام**:
  - فحص وجود جدول `admins`
  - فحص وجود جدول `activity_log`
  - إنشاء المدير الافتراضي إذا لم يوجد
  - معالجة الأخطاء بشكل آمن

#### `createAdminTables($main_db)`
- **الوظيفة**: إنشاء جداول المدير مع البنية الكاملة
- **المهام**:
  - إنشاء جدول المديرين مع جميع الحقول والفهارس
  - إنشاء جدول سجل العمليات مع الفهارس المحسنة
  - إنشاء المدير الافتراضي مع الصلاحيات الكاملة
  - تسجيل عملية الإنشاء في سجل النشاطات

### 📋 الجداول المنشأة تلقائياً:

#### جدول المديرين (`admins`)
```sql
CREATE TABLE IF NOT EXISTS `admins` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL,
    `password` varchar(255) NOT NULL,
    `full_name` varchar(100) NOT NULL,
    `email` varchar(100) NOT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `permissions` TEXT DEFAULT NULL,
    `is_super_admin` tinyint(1) DEFAULT 0,
    `is_active` tinyint(1) DEFAULT 1,
    `last_login` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_admin_username` (`username`),
    UNIQUE KEY `idx_admin_email` (`email`),
    KEY `idx_admin_active` (`is_active`),
    KEY `idx_admin_super` (`is_super_admin`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

#### جدول سجل العمليات (`activity_log`)
```sql
CREATE TABLE IF NOT EXISTS `activity_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `user_type` enum('user','admin') DEFAULT 'user',
    `action` varchar(100) NOT NULL,
    `table_name` varchar(50) DEFAULT NULL,
    `record_id` int(11) DEFAULT NULL,
    `old_data` TEXT DEFAULT NULL,
    `new_data` TEXT DEFAULT NULL,
    `description` text DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_activity_user_id` (`user_id`),
    KEY `idx_activity_user_type` (`user_type`),
    KEY `idx_activity_action` (`action`),
    KEY `idx_activity_table` (`table_name`),
    KEY `idx_activity_created` (`created_at`),
    KEY `idx_activity_user_action` (`user_id`, `action`),
    KEY `idx_activity_date_action` (`created_at`, `action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

## 🚀 الوصول للنظام

### رابط تسجيل الدخول
```
http://localhost/salessystem/admin_login.php
```

### بيانات المدير الافتراضي
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **البريد الإلكتروني:** `<EMAIL>`

## 📋 الصفحات والوظائف

### 1. لوحة التحكم الرئيسية (`admin_dashboard.php`)
- **الإحصائيات الفورية:**
  - إجمالي المستخدمين
  - المستخدمين النشطين
  - نشاط اليوم
  - المستخدمين الحديثين (آخر 30 يوم)
- **أحدث العمليات:** عرض آخر 10 عمليات في النظام
- **روابط سريعة:** للوصول لجميع أقسام الإدارة

### 2. إدارة المستخدمين (`admin_users.php`)
- **عرض جميع المستخدمين** مع معلوماتهم الكاملة
- **البحث والفلترة:**
  - البحث بالاسم أو البريد الإلكتروني
  - فلترة حسب الحالة (نشط/غير نشط)
- **الإجراءات المتاحة:**
  - عرض تفاصيل المستخدم
  - تفعيل/إلغاء تفعيل الحساب
  - حذف المستخدم وقاعدة بياناته نهائياً
- **تصدير البيانات:** إمكانية تصدير قائمة المستخدمين

### 3. سجل العمليات (`admin_activity.php`)
- **مراقبة شاملة** لجميع الأنشطة في النظام
- **الإحصائيات:**
  - إجمالي العمليات
  - عمليات المستخدمين
  - عمليات المديرين
  - عمليات اليوم
- **فلترة متقدمة:**
  - البحث في الوصف والعمليات
  - فلترة حسب نوع المستخدم (مستخدم/مدير)
  - فلترة حسب نوع العملية
  - فلترة حسب التاريخ (من - إلى)
- **التنقل بين الصفحات:** عرض 50 عملية في كل صفحة

## 🔐 نظام الصلاحيات

### الصلاحيات المتاحة:
- `manage_users`: إدارة حسابات المستخدمين
- `view_all_data`: عرض جميع البيانات والتقارير
- `manage_system`: إدارة إعدادات النظام
- `view_reports`: عرض التقارير الشاملة
- `manage_admins`: إدارة حسابات المديرين الآخرين

### المدير العام (Super Admin):
- صلاحيات كاملة على جميع أجزاء النظام
- إمكانية إدارة المديرين الآخرين
- الوصول لجميع البيانات بدون قيود

## 📊 العمليات المسجلة

### عمليات المستخدمين:
- `user_login`: تسجيل دخول المستخدم
- `user_register`: تسجيل مستخدم جديد
- `user_logout`: تسجيل خروج المستخدم
- `sale_create`: إنشاء فاتورة مبيعات
- `purchase_create`: إنشاء فاتورة مشتريات
- `customer_create`: إضافة عميل جديد
- `data_update`: تحديث البيانات
- `data_delete`: حذف البيانات

### عمليات المديرين:
- `admin_login`: تسجيل دخول المدير
- `admin_logout`: تسجيل خروج المدير
- `user_status_changed`: تغيير حالة المستخدم
- `user_deleted`: حذف مستخدم
- `system_setup`: إعداد النظام

## 🗄️ قاعدة البيانات

### الجداول الجديدة:

#### جدول المديرين (`admins`)
```sql
- id: معرف المدير
- username: اسم المستخدم (فريد)
- password: كلمة المرور المشفرة
- full_name: الاسم الكامل
- email: البريد الإلكتروني (فريد)
- phone: رقم الهاتف
- permissions: الصلاحيات (JSON)
- is_super_admin: مدير عام (0/1)
- is_active: نشط (0/1)
- last_login: آخر تسجيل دخول
- created_at: تاريخ الإنشاء
- updated_at: تاريخ آخر تحديث
```

#### جدول سجل العمليات (`activity_log`)
```sql
- id: معرف العملية
- user_id: معرف المستخدم/المدير
- user_type: نوع المستخدم (user/admin)
- action: نوع العملية
- table_name: اسم الجدول المتأثر
- record_id: معرف السجل المتأثر
- old_data: البيانات القديمة (JSON)
- new_data: البيانات الجديدة (JSON)
- description: وصف العملية
- ip_address: عنوان IP
- user_agent: معلومات المتصفح
- created_at: وقت العملية
```

## 🔧 الدوال المساعدة

### دوال التحقق من الصلاحيات:
- `isAdminLoggedIn()`: التحقق من تسجيل دخول المدير
- `hasAdminPermission($permission)`: التحقق من صلاحية معينة
- `logActivity()`: تسجيل العمليات في سجل النشاطات

## 🎨 التصميم والواجهة

### الألوان المميزة:
- **اللون الأساسي:** أحمر (`#dc3545`) للتمييز عن النظام العادي
- **الشريط الجانبي:** رمادي داكن (`#343a40`)
- **البطاقات:** تدرج أحمر مع ظلال

### الميزات التفاعلية:
- **إشعارات فورية:** للعمليات الناجحة والأخطاء
- **تأكيد العمليات:** للعمليات الحساسة مثل الحذف
- **تحديث تلقائي:** للإحصائيات كل 30 ثانية
- **بحث سريع:** في جميع أقسام النظام

## 🔗 الروابط السريعة

### من النظام العادي:
- رابط في footer الصفحة الرئيسية
- رابط مباشر: `admin_login.php`

### داخل نظام المدير:
- لوحة التحكم: `admin_dashboard.php`
- إدارة المستخدمين: `admin_users.php`
- سجل العمليات: `admin_activity.php`
- تسجيل الخروج: `admin_logout.php`

## 🛡️ الأمان والحماية

### ميزات الأمان:
- **تشفير كلمات المرور:** باستخدام `password_hash()`
- **فحص الصلاحيات:** مع كل طلب
- **تسجيل جميع الأنشطة:** للمراجعة والتدقيق
- **حماية من الوصول غير المصرح:** إعادة توجيه للمتطفلين
- **جلسات منفصلة:** عن المستخدمين العاديين

### معلومات إضافية:
- **عنوان IP:** يتم تسجيله مع كل عملية
- **معلومات المتصفح:** للتتبع الأمني
- **الوقت الدقيق:** لكل عملية

## 📈 الإحصائيات والتقارير

### الإحصائيات المتاحة:
- إجمالي المستخدمين (نشط/غير نشط)
- المستخدمين الحديثين (آخر 30 يوم)
- عدد العمليات اليومية
- توزيع العمليات حسب النوع
- نشاط المستخدمين والمديرين

### إمكانيات التصدير:
- تصدير قائمة المستخدمين (Excel)
- تصدير سجل العمليات (Excel)
- تقارير مخصصة حسب التاريخ

## 🚀 التطوير المستقبلي

### الميزات المخططة:
- إدارة المديرين الآخرين
- تقارير مرئية بالرسوم البيانية
- إشعارات فورية للأنشطة المهمة
- نظام النسخ الاحتياطي التلقائي
- إعدادات النظام المتقدمة

---

## 🚀 الفوائد والميزات الجديدة

### ✅ الإعداد التلقائي الكامل
- **لا حاجة لإعداد يدوي**: النظام يعد نفسه تلقائياً
- **فحص مستمر**: يتحقق من سلامة الجداول مع كل استخدام
- **إصلاح تلقائي**: يصلح أي مشاكل في البنية تلقائياً
- **مقاوم للأخطاء**: يعمل حتى لو تم حذف الجداول عن طريق الخطأ

### 🔧 سهولة الصيانة
- **تحديث تلقائي**: يحدث بنية الجداول عند الحاجة
- **معالجة أخطاء متقدمة**: لا يتوقف النظام عند حدوث خطأ
- **تسجيل شامل**: جميع العمليات والأخطاء مسجلة
- **استرداد سريع**: يمكن استرداد النظام بسهولة

### 🛡️ أمان محسن
- **فحص الصلاحيات**: مع كل عملية
- **تشفير متقدم**: لكلمات المرور والبيانات الحساسة
- **تتبع شامل**: لجميع الأنشطة والتغييرات
- **حماية من التلاعب**: فحص سلامة البيانات مستمر

### 📊 مراقبة متطورة
- **إحصائيات فورية**: تحديث مستمر للبيانات
- **تقارير تفصيلية**: عن جميع الأنشطة
- **تنبيهات ذكية**: للأنشطة المشبوهة
- **تحليل الأداء**: لاستخدام النظام

## 🔄 كيفية عمل الإعداد التلقائي

### عند تحميل أي صفحة:
1. **فحص الاتصال**: بقاعدة البيانات الرئيسية
2. **التحقق من الجداول**: وجود جداول المدير
3. **إنشاء تلقائي**: للجداول المفقودة
4. **فحص المدير الافتراضي**: وإنشاؤه عند الحاجة
5. **تسجيل العملية**: في سجل النشاطات

### عند استخدام دالة `logActivity()`:
1. **فحص الجداول**: قبل الكتابة
2. **إنشاء تلقائي**: إذا كانت مفقودة
3. **تسجيل البيانات**: بشكل آمن
4. **معالجة الأخطاء**: بدون توقف النظام

## 📝 ملفات النظام المحدثة

### الملفات الأساسية:
- ✅ `config/init.php` - محدث مع الإعداد التلقائي
- ✅ `admin_login.php` - صفحة تسجيل دخول المدير
- ✅ `admin_dashboard.php` - لوحة التحكم الرئيسية
- ✅ `admin_users.php` - إدارة المستخدمين
- ✅ `admin_activity.php` - سجل العمليات
- ✅ `admin_logout.php` - تسجيل خروج المدير

### الدوال الجديدة:
- ✅ `ensureAdminTables()` - التحقق من الجداول
- ✅ `createAdminTables()` - إنشاء جداول المدير
- ✅ `logActivity()` - محسنة مع فحص الجداول

---

**ملاحظة:** هذا النظام يوفر مراقبة شاملة وإدارة كاملة لجميع جوانب نظام إدارة المبيعات مع الحفاظ على أعلى معايير الأمان والخصوصية، ويعمل بشكل تلقائي بالكامل بدون تدخل يدوي.
