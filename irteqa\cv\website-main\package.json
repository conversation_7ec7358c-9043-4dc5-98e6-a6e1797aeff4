{"name": "personal-website", "version": "0.0.0", "description": "Personal Website", "private": true, "license": "MIT", "scripts": {"start": "react-app-rewired start", "build": "PUBLIC_URL=/personal-website react-app-rewired build", "lint": "eslint \"{src,test}/**/*.ts\"", "prettier": "prettier --check \"{src,test}/**/*.{ts,tsx}\"", "prettier:fix": "prettier --write \"{src,test}/**/*.{ts,tsx}\""}, "devDependencies": {"@headlessui/react": "^1.5.0", "@tailwindcss/forms": "^0.5.0", "@testing-library/jest-dom": "^5.16.2", "@testing-library/react": "^12.1.3", "@testing-library/user-event": "^13.5.0", "@trivago/prettier-plugin-sort-imports": "^3.2.0", "@types/jest": "^27.4.1", "@types/node": "^16.11.26", "@types/react": "^17.0.39", "@types/react-dom": "^17.0.13", "@typescript-eslint/eslint-plugin": "^5.13.0", "@typescript-eslint/parser": "^5.13.0", "autoprefixer": "^10.4.2", "classnames": "^2.3.1", "eslint": "^7.32.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.29.3", "eslint-plugin-react-hooks": "^4.3.0", "mobx": "^5.15.7", "mobx-react": "^6.3.1", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "postcss": "^8.4.8", "prettier": "^2.5.1", "prettier-plugin-organize-imports": "^2.3.4", "react": "^17.0.2", "react-app-rewired": "^2.2.1", "react-dom": "^17.0.2", "react-icons": "^4.3.1", "react-router-dom": "^6.2.2", "react-scripts": "^5.0.0", "sass": "^1.49.9", "tailwindcss": "^3.0.23", "typescript": "^4.6.2"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}