<?php
/**
 * Language management system
 */

// Default language
if (!isset($_SESSION['lang'])) {
    $_SESSION['lang'] = 'ar'; // Default to Arabic
}

// Handle language change
if (isset($_GET['lang']) && ($_GET['lang'] == 'ar' || $_GET['lang'] == 'en')) {
    $_SESSION['lang'] = $_GET['lang'];
    
    // Redirect back to the same page without the lang parameter
    $redirect_url = strtok($_SERVER['REQUEST_URI'], '?');
    $query_params = $_GET;
    unset($query_params['lang']);
    
    if (!empty($query_params)) {
        $redirect_url .= '?' . http_build_query($query_params);
    }
    
    header('Location: ' . $redirect_url);
    exit();
}

// Load language file
function loadLanguage() {
    $lang = $_SESSION['lang'] ?? 'ar';
    $lang_file = __DIR__ . '/../languages/' . $lang . '/lang.php';
    
    if (file_exists($lang_file)) {
        return require $lang_file;
    } else {
        // Fallback to Arabic if language file doesn't exist
        return require __DIR__ . '/../languages/ar/lang.php';
    }
}

// Get translation for a key
function __($key, $default = null) {
    static $translations = null;
    
    if ($translations === null) {
        $translations = loadLanguage();
    }
    
    return $translations[$key] ?? $default ?? $key;
}

// Get current language code
function getCurrentLang() {
    return $_SESSION['lang'] ?? 'ar';
}

// Get current language direction
function getLanguageDir() {
    static $translations = null;
    
    if ($translations === null) {
        $translations = loadLanguage();
    }
    
    return $translations['dir'] ?? 'rtl';
}

// Get language switch URL
function getLanguageSwitchUrl($lang) {
    $current_url = $_SERVER['REQUEST_URI'];
    $parsed_url = parse_url($current_url);
    $path = $parsed_url['path'];
    
    $query_params = [];
    if (isset($parsed_url['query'])) {
        parse_str($parsed_url['query'], $query_params);
    }
    
    $query_params['lang'] = $lang;
    
    return $path . '?' . http_build_query($query_params);
}
