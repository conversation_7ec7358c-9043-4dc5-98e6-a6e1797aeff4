<?php
/**
 * أداة فحص شاملة للنصوص غير المترجمة في المشروع
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// فحص النصوص غير المترجمة
$untranslated_texts = [];
$files_scanned = 0;
$total_texts_found = 0;

// قائمة الملفات المراد فحصها
$files_to_scan = [
    'index.php',
    'customers.php',
    'sales.php',
    'purchases.php',
    'products.php',
    'add_sale.php',
    'add_purchase.php',
    'add_customer.php',
    'edit_sale.php',
    'edit_purchase.php',
    'edit_customer.php',
    'reports.php',
    'settings.php',
    'profile.php',
    'login.php',
    'register.php',
    'includes/header.php',
    'includes/footer.php',
    'includes/sidebar.php'
];

// تحميل ملفات الترجمة الحالية
$ar_file = __DIR__ . '/languages/ar/lang.php';
$en_file = __DIR__ . '/languages/en/lang.php';

$ar_translations = file_exists($ar_file) ? require $ar_file : [];
$en_translations = file_exists($en_file) ? require $en_file : [];

foreach ($files_to_scan as $filename) {
    $filepath = __DIR__ . '/' . $filename;
    if (file_exists($filepath)) {
        $files_scanned++;
        $content = file_get_contents($filepath);
        
        // البحث عن النصوص العربية
        preg_match_all('/[أ-ي\s]+/u', $content, $matches);
        
        foreach ($matches[0] as $text) {
            $text = trim($text);
            if (strlen($text) > 2 && !preg_match('/^[a-zA-Z0-9\s]+$/', $text)) {
                // تنظيف النص
                $clean_text = preg_replace('/[^\p{Arabic}\s]/u', '', $text);
                $clean_text = trim($clean_text);
                
                if (strlen($clean_text) > 2) {
                    if (!isset($untranslated_texts[$clean_text])) {
                        $untranslated_texts[$clean_text] = [
                            'files' => [],
                            'count' => 0,
                            'suggested_key' => generateTranslationKey($clean_text),
                            'has_translation' => false
                        ];
                    }
                    
                    $untranslated_texts[$clean_text]['files'][] = $filename;
                    $untranslated_texts[$clean_text]['count']++;
                    $total_texts_found++;
                    
                    // فحص وجود ترجمة
                    $suggested_key = $untranslated_texts[$clean_text]['suggested_key'];
                    if (isset($ar_translations[$suggested_key]) || isset($en_translations[$suggested_key])) {
                        $untranslated_texts[$clean_text]['has_translation'] = true;
                    }
                }
            }
        }
    }
}

// دالة لتوليد مفتاح الترجمة
function generateTranslationKey($text) {
    $key_map = [
        'الصفحة الرئيسية' => 'home_page',
        'لوحة التحكم' => 'dashboard',
        'العملاء' => 'customers',
        'المبيعات' => 'sales',
        'المشتريات' => 'purchases',
        'المنتجات' => 'products',
        'التقارير' => 'reports',
        'الإعدادات' => 'settings',
        'أدوات النظام' => 'system_tools',
        'حاسبة الضريبة' => 'tax_calculator',
        'الملف الشخصي' => 'profile',
        'تسجيل الدخول' => 'login',
        'تسجيل الخروج' => 'logout',
        'تسجيل' => 'register',
        'مرحباً' => 'welcome',
        'اسم المستخدم' => 'username',
        'كلمة المرور' => 'password',
        'البريد الإلكتروني' => 'email',
        'الهاتف' => 'phone',
        'العنوان' => 'address',
        'حفظ' => 'save',
        'إلغاء' => 'cancel',
        'تعديل' => 'edit',
        'حذف' => 'delete',
        'إضافة' => 'add',
        'عرض' => 'view',
        'بحث' => 'search',
        'تصفية' => 'filter',
        'طباعة' => 'print',
        'تصدير' => 'export',
        'استيراد' => 'import',
        'نعم' => 'yes',
        'لا' => 'no',
        'موافق' => 'ok',
        'خطأ' => 'error',
        'نجح' => 'success',
        'تحذير' => 'warning',
        'معلومات' => 'info',
        'التاريخ' => 'date',
        'الوقت' => 'time',
        'المجموع' => 'total',
        'الضريبة' => 'tax',
        'الخصم' => 'discount',
        'السعر' => 'price',
        'الكمية' => 'quantity',
        'الوصف' => 'description'
    ];
    
    if (isset($key_map[$text])) {
        return $key_map[$text];
    }
    
    // توليد مفتاح تلقائي
    $key = strtolower(trim($text));
    $key = str_replace([' ', 'ا', 'إ', 'أ', 'آ'], ['_', 'a', 'e', 'a', 'a'], $key);
    $key = preg_replace('/[^\w_]/', '', $key);
    $key = preg_replace('/_+/', '_', $key);
    $key = trim($key, '_');
    
    return $key ?: 'unknown_text';
}

// ترتيب النصوص حسب التكرار
uasort($untranslated_texts, function($a, $b) {
    return $b['count'] - $a['count'];
});

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-search"></i>
                        فحص شامل للنصوص غير المترجمة
                    </h4>
                </div>
                <div class="card-body">
                    <!-- الإحصائيات -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $files_scanned; ?></h3>
                                    <p class="mb-0">ملف تم فحصه</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h3><?php echo count($untranslated_texts); ?></h3>
                                    <p class="mb-0">نص فريد</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $total_texts_found; ?></h3>
                                    <p class="mb-0">إجمالي التكرارات</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($ar_translations); ?></h3>
                                    <p class="mb-0">ترجمة موجودة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- قائمة النصوص -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">النصوص المكتشفة</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>النص</th>
                                            <th>المفتاح المقترح</th>
                                            <th>التكرار</th>
                                            <th>الملفات</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($untranslated_texts)): ?>
                                        <tr>
                                            <td colspan="5" class="text-center">
                                                <div class="alert alert-success">
                                                    <i class="fas fa-check-circle"></i>
                                                    لم يتم العثور على نصوص غير مترجمة!
                                                </div>
                                            </td>
                                        </tr>
                                        <?php else: ?>
                                        <?php foreach ($untranslated_texts as $text => $info): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($text); ?></strong>
                                            </td>
                                            <td>
                                                <code><?php echo htmlspecialchars($info['suggested_key']); ?></code>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?php echo $info['count']; ?></span>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo implode(', ', array_unique($info['files'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($info['has_translation']): ?>
                                                    <span class="badge bg-success">مترجم</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير مترجم</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإجراءات -->
                    <div class="mt-4">
                        <h6>الإجراءات الموصى بها:</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <a href="translate_all_texts.php" class="btn btn-primary w-100 mb-2">
                                    <i class="fas fa-language"></i> ترجمة جميع النصوص
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="replace_hardcoded_texts.php" class="btn btn-warning w-100 mb-2">
                                    <i class="fas fa-exchange-alt"></i> استبدال النصوص المكتوبة مباشرة
                                </a>
                            </div>
                            <div class="col-md-4">
                                <a href="review_translations.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-search"></i> مراجعة الترجمات
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- نصائح -->
                    <div class="alert alert-info mt-4">
                        <h6><i class="fas fa-lightbulb"></i> نصائح للترجمة</h6>
                        <ul class="mb-0">
                            <li>ابدأ بترجمة النصوص الأكثر تكراراً أولاً</li>
                            <li>استخدم مفاتيح واضحة ومفهومة للترجمة</li>
                            <li>تأكد من اتساق الترجمات عبر النظام</li>
                            <li>اختبر النظام بعد تطبيق الترجمات</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
