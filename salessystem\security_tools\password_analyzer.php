<?php
/**
 * محلل كلمات المرور الذكي
 * يحدد نوع كلمة المرور ويقترح الاختبار المناسب
 */

// محلل كلمات المرور - متاح للجميع بدون تسجيل دخول
// تم إزالة متطلب تسجيل الدخول لسهولة الوصول

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة']);
    exit;
}

$action = $_POST['action'] ?? '';

if ($action === 'analyze_password') {
    $password = $_POST['password'] ?? '';
    $result = analyzePassword($password);
    echo json_encode($result);
} elseif ($action === 'generate_hash') {
    $password = $_POST['password'] ?? '';
    if (empty($password)) {
        echo json_encode(['error' => 'كلمة المرور مطلوبة']);
    } else {
        $hash = password_hash($password, PASSWORD_BCRYPT, ['cost' => 10]);
        echo json_encode(['hash' => $hash, 'password' => $password]);
    }
} else {
    http_response_code(400);
    echo json_encode(['error' => 'إجراء غير صحيح']);
}

/**
 * تحليل كلمة المرور وتحديد النوع والاختبار المناسب
 */
function analyzePassword($password) {
    if (empty($password)) {
        return ['error' => 'كلمة المرور مطلوبة'];
    }
    
    $analysis = [
        'password' => $password,
        'length' => strlen($password),
        'patterns' => [],
        'characteristics' => [],
        'weakness_level' => 'unknown',
        'recommended_tests' => [],
        'estimated_time' => [],
        'security_score' => 0
    ];
    
    // تحليل الخصائص الأساسية
    $analysis = analyzeBasicCharacteristics($password, $analysis);
    
    // تحليل الأنماط
    $analysis = analyzePatterns($password, $analysis);
    
    // تحديد مستوى الضعف
    $analysis = determineWeaknessLevel($analysis);
    
    // اقتراح الاختبارات المناسبة
    $analysis = recommendTests($analysis);
    
    // تقدير الوقت المطلوب
    $analysis = estimateTime($analysis);
    
    return $analysis;
}

/**
 * تحليل الخصائص الأساسية
 */
function analyzeBasicCharacteristics($password, $analysis) {
    $length = strlen($password);
    
    // تحليل أنواع الأحرف
    $hasLower = preg_match('/[a-z]/', $password);
    $hasUpper = preg_match('/[A-Z]/', $password);
    $hasDigits = preg_match('/[0-9]/', $password);
    $hasSpecial = preg_match('/[^a-zA-Z0-9]/', $password);
    $hasArabic = preg_match('/[\x{0600}-\x{06FF}]/u', $password);
    
    $analysis['characteristics'] = [
        'length' => $length,
        'has_lowercase' => $hasLower,
        'has_uppercase' => $hasUpper,
        'has_digits' => $hasDigits,
        'has_special' => $hasSpecial,
        'has_arabic' => $hasArabic,
        'charset_size' => calculateCharsetSize($hasLower, $hasUpper, $hasDigits, $hasSpecial, $hasArabic),
        'entropy' => calculateEntropy($password, $hasLower, $hasUpper, $hasDigits, $hasSpecial, $hasArabic)
    ];
    
    return $analysis;
}

/**
 * حساب حجم مجموعة الأحرف
 */
function calculateCharsetSize($hasLower, $hasUpper, $hasDigits, $hasSpecial, $hasArabic) {
    $size = 0;
    if ($hasLower) $size += 26;
    if ($hasUpper) $size += 26;
    if ($hasDigits) $size += 10;
    if ($hasSpecial) $size += 32; // تقدير للرموز الشائعة
    if ($hasArabic) $size += 28; // الأحرف العربية
    
    return $size;
}

/**
 * حساب الإنتروبيا (قوة كلمة المرور)
 */
function calculateEntropy($password, $hasLower, $hasUpper, $hasDigits, $hasSpecial, $hasArabic) {
    $charsetSize = calculateCharsetSize($hasLower, $hasUpper, $hasDigits, $hasSpecial, $hasArabic);
    $length = strlen($password);
    
    if ($charsetSize == 0) return 0;
    
    return $length * log($charsetSize, 2);
}

/**
 * تحليل الأنماط
 */
function analyzePatterns($password, $analysis) {
    $patterns = [];
    
    // فحص الأنماط الشائعة
    if (isCommonPassword($password)) {
        $patterns[] = 'common_password';
    }
    
    if (isSequential($password)) {
        $patterns[] = 'sequential';
    }
    
    if (isRepeating($password)) {
        $patterns[] = 'repeating';
    }
    
    if (isKeyboardPattern($password)) {
        $patterns[] = 'keyboard_pattern';
    }
    
    if (isDatePattern($password)) {
        $patterns[] = 'date_pattern';
    }
    
    if (isDictionaryWord($password)) {
        $patterns[] = 'dictionary_word';
    }
    
    if (isNamePattern($password)) {
        $patterns[] = 'name_pattern';
    }
    
    if (isNumbersOnly($password)) {
        $patterns[] = 'numbers_only';
    }
    
    if (isLettersOnly($password)) {
        $patterns[] = 'letters_only';
    }
    
    $analysis['patterns'] = $patterns;
    
    return $analysis;
}

/**
 * فحص كلمات المرور الشائعة
 */
function isCommonPassword($password) {
    $common = [
        '123456', 'password', '123456789', '12345678', '12345',
        'qwerty', 'abc123', '111111', 'password1', 'admin',
        'welcome', 'monkey', 'login', 'princess', 'solo'
    ];
    
    return in_array(strtolower($password), array_map('strtolower', $common));
}

/**
 * فحص التسلسل
 */
function isSequential($password) {
    $sequences = ['123456789', 'abcdefghijklmnopqrstuvwxyz', '987654321', 'zyxwvutsrqponmlkjihgfedcba'];
    
    foreach ($sequences as $seq) {
        if (strpos($seq, strtolower($password)) !== false && strlen($password) >= 3) {
            return true;
        }
    }
    
    return false;
}

/**
 * فحص التكرار
 */
function isRepeating($password) {
    if (strlen($password) < 2) return false;
    
    // فحص تكرار حرف واحد
    if (count(array_unique(str_split($password))) == 1) {
        return true;
    }
    
    // فحص تكرار نمط
    for ($i = 1; $i <= strlen($password) / 2; $i++) {
        $pattern = substr($password, 0, $i);
        $repeated = str_repeat($pattern, ceil(strlen($password) / $i));
        if (substr($repeated, 0, strlen($password)) === $password) {
            return true;
        }
    }
    
    return false;
}

/**
 * فحص أنماط لوحة المفاتيح
 */
function isKeyboardPattern($password) {
    $patterns = ['qwerty', 'asdf', 'zxcv', 'qaz', 'wsx', 'edc', '147', '258', '369'];
    
    foreach ($patterns as $pattern) {
        if (strpos(strtolower($password), $pattern) !== false) {
            return true;
        }
    }
    
    return false;
}

/**
 * فحص أنماط التاريخ
 */
function isDatePattern($password) {
    // فحص أنماط التاريخ المختلفة
    $datePatterns = [
        '/^\d{4}$/',           // سنة (2023)
        '/^\d{2}\/\d{2}\/\d{4}$/', // dd/mm/yyyy
        '/^\d{2}-\d{2}-\d{4}$/',   // dd-mm-yyyy
        '/^\d{8}$/',           // ddmmyyyy
        '/^\d{6}$/'            // ddmmyy
    ];
    
    foreach ($datePatterns as $pattern) {
        if (preg_match($pattern, $password)) {
            return true;
        }
    }
    
    return false;
}

/**
 * فحص كلمات القاموس
 */
function isDictionaryWord($password) {
    $words = [
        'password', 'admin', 'user', 'test', 'guest', 'root',
        'welcome', 'hello', 'world', 'computer', 'internet',
        'security', 'system', 'network', 'server', 'database'
    ];
    
    $lowerPassword = strtolower($password);
    
    foreach ($words as $word) {
        if ($lowerPassword === $word || strpos($lowerPassword, $word) !== false) {
            return true;
        }
    }
    
    return false;
}

/**
 * فحص أنماط الأسماء
 */
function isNamePattern($password) {
    $names = ['ahmed', 'mohamed', 'ali', 'omar', 'sara', 'fatima', 'john', 'mike', 'david', 'sarah'];
    
    $lowerPassword = strtolower($password);
    
    foreach ($names as $name) {
        if (strpos($lowerPassword, $name) !== false) {
            return true;
        }
    }
    
    return false;
}

/**
 * فحص الأرقام فقط
 */
function isNumbersOnly($password) {
    return ctype_digit($password);
}

/**
 * فحص الأحرف فقط
 */
function isLettersOnly($password) {
    return ctype_alpha($password);
}

/**
 * تحديد مستوى الضعف
 */
function determineWeaknessLevel($analysis) {
    $score = 0;
    $patterns = $analysis['patterns'];
    $chars = $analysis['characteristics'];
    
    // نقاط سلبية للأنماط الضعيفة
    if (in_array('common_password', $patterns)) $score -= 50;
    if (in_array('sequential', $patterns)) $score -= 30;
    if (in_array('repeating', $patterns)) $score -= 40;
    if (in_array('keyboard_pattern', $patterns)) $score -= 25;
    if (in_array('date_pattern', $patterns)) $score -= 20;
    if (in_array('dictionary_word', $patterns)) $score -= 15;
    if (in_array('name_pattern', $patterns)) $score -= 15;
    if (in_array('numbers_only', $patterns)) $score -= 20;
    if (in_array('letters_only', $patterns)) $score -= 15;
    
    // نقاط إيجابية للخصائص القوية
    if ($chars['length'] >= 8) $score += 10;
    if ($chars['length'] >= 12) $score += 20;
    if ($chars['has_lowercase']) $score += 5;
    if ($chars['has_uppercase']) $score += 10;
    if ($chars['has_digits']) $score += 10;
    if ($chars['has_special']) $score += 15;
    if ($chars['entropy'] > 40) $score += 20;
    if ($chars['entropy'] > 60) $score += 30;
    
    // تحديد المستوى
    if ($score < -30) {
        $level = 'very_weak';
    } elseif ($score < -10) {
        $level = 'weak';
    } elseif ($score < 20) {
        $level = 'medium';
    } elseif ($score < 40) {
        $level = 'strong';
    } else {
        $level = 'very_strong';
    }
    
    $analysis['weakness_level'] = $level;
    $analysis['security_score'] = $score;
    
    return $analysis;
}

/**
 * اقتراح الاختبارات المناسبة
 */
function recommendTests($analysis) {
    $tests = [];
    $patterns = $analysis['patterns'];
    $level = $analysis['weakness_level'];
    $chars = $analysis['characteristics'];
    
    // اختبارات حسب الأنماط المكتشفة
    if (in_array('common_password', $patterns)) {
        $tests[] = ['method' => 'common', 'priority' => 1, 'reason' => 'كلمة مرور شائعة'];
    }
    
    if (in_array('dictionary_word', $patterns) || in_array('name_pattern', $patterns)) {
        $tests[] = ['method' => 'dictionary', 'priority' => 2, 'reason' => 'تحتوي على كلمات قاموس'];
        $tests[] = ['method' => 'wordlist_extended', 'priority' => 3, 'reason' => 'قاموس موسع للكلمات'];
    }
    
    if (in_array('sequential', $patterns) || in_array('keyboard_pattern', $patterns) || in_array('date_pattern', $patterns)) {
        $tests[] = ['method' => 'pattern', 'priority' => 2, 'reason' => 'تحتوي على أنماط شائعة'];
    }
    
    if (in_array('numbers_only', $patterns)) {
        $tests[] = ['method' => 'brute_simple', 'priority' => 1, 'reason' => 'أرقام فقط - قوة غاشمة بسيطة'];
    }
    
    if (in_array('letters_only', $patterns) && $chars['length'] <= 6) {
        $tests[] = ['method' => 'brute_simple', 'priority' => 2, 'reason' => 'أحرف قصيرة - قوة غاشمة'];
    }
    
    // اختبارات حسب مستوى الضعف
    switch ($level) {
        case 'very_weak':
            $tests[] = ['method' => 'common', 'priority' => 1, 'reason' => 'ضعيفة جداً - ابدأ بالشائعة'];
            $tests[] = ['method' => 'pattern', 'priority' => 2, 'reason' => 'فحص الأنماط البسيطة'];
            break;
            
        case 'weak':
            $tests[] = ['method' => 'hybrid', 'priority' => 1, 'reason' => 'ضعيفة - اختبار هجين'];
            $tests[] = ['method' => 'rule_based', 'priority' => 2, 'reason' => 'قواعد التحويل'];
            break;
            
        case 'medium':
            $tests[] = ['method' => 'wordlist_extended', 'priority' => 1, 'reason' => 'متوسطة - قاموس موسع'];
            $tests[] = ['method' => 'rule_based', 'priority' => 2, 'reason' => 'قواعد متقدمة'];
            $tests[] = ['method' => 'mask_attack', 'priority' => 3, 'reason' => 'هجوم قناع'];
            break;
            
        case 'strong':
            $tests[] = ['method' => 'comprehensive', 'priority' => 1, 'reason' => 'قوية - اختبار شامل'];
            $tests[] = ['method' => 'brute_advanced', 'priority' => 2, 'reason' => 'قوة غاشمة متقدمة'];
            break;
            
        case 'very_strong':
            $tests[] = ['method' => 'brute_advanced', 'priority' => 1, 'reason' => 'قوية جداً - قوة غاشمة فقط'];
            $tests[] = ['method' => 'comprehensive', 'priority' => 2, 'reason' => 'اختبار شامل طويل المدى'];
            break;
    }
    
    // إزالة التكرارات وترتيب حسب الأولوية
    $uniqueTests = [];
    foreach ($tests as $test) {
        if (!isset($uniqueTests[$test['method']])) {
            $uniqueTests[$test['method']] = $test;
        } elseif ($test['priority'] < $uniqueTests[$test['method']]['priority']) {
            $uniqueTests[$test['method']] = $test;
        }
    }
    
    // ترتيب حسب الأولوية
    uasort($uniqueTests, function($a, $b) {
        return $a['priority'] - $b['priority'];
    });
    
    $analysis['recommended_tests'] = array_values($uniqueTests);
    
    return $analysis;
}

/**
 * تقدير الوقت المطلوب
 */
function estimateTime($analysis) {
    $estimates = [];
    $chars = $analysis['characteristics'];
    $level = $analysis['weakness_level'];
    
    // تقديرات أساسية حسب نوع الاختبار
    $baseEstimates = [
        'common' => ['min' => 1, 'max' => 300, 'unit' => 'seconds'],
        'pattern' => ['min' => 5, 'max' => 600, 'unit' => 'seconds'],
        'dictionary' => ['min' => 30, 'max' => 1800, 'unit' => 'seconds'],
        'wordlist_extended' => ['min' => 300, 'max' => 7200, 'unit' => 'seconds'],
        'rule_based' => ['min' => 600, 'max' => 3600, 'unit' => 'seconds'],
        'hybrid' => ['min' => 300, 'max' => 3600, 'unit' => 'seconds'],
        'mask_attack' => ['min' => 1800, 'max' => 86400, 'unit' => 'seconds'],
        'brute_simple' => ['min' => 60, 'max' => 604800, 'unit' => 'seconds'],
        'brute_advanced' => ['min' => 3600, 'max' => 2592000, 'unit' => 'seconds'],
        'comprehensive' => ['min' => 7200, 'max' => 604800, 'unit' => 'seconds']
    ];
    
    // تعديل التقديرات حسب خصائص كلمة المرور
    foreach ($baseEstimates as $method => $estimate) {
        $adjustedEstimate = $estimate;
        
        // تعديل حسب الطول
        if ($chars['length'] <= 4) {
            $adjustedEstimate['max'] *= 0.1;
        } elseif ($chars['length'] <= 6) {
            $adjustedEstimate['max'] *= 0.3;
        } elseif ($chars['length'] >= 10) {
            $adjustedEstimate['max'] *= 5;
        }
        
        // تعديل حسب مستوى الضعف
        switch ($level) {
            case 'very_weak':
                $adjustedEstimate['max'] *= 0.1;
                break;
            case 'weak':
                $adjustedEstimate['max'] *= 0.3;
                break;
            case 'strong':
                $adjustedEstimate['max'] *= 3;
                break;
            case 'very_strong':
                $adjustedEstimate['max'] *= 10;
                break;
        }
        
        $estimates[$method] = $adjustedEstimate;
    }
    
    $analysis['estimated_time'] = $estimates;
    
    return $analysis;
}
?>
