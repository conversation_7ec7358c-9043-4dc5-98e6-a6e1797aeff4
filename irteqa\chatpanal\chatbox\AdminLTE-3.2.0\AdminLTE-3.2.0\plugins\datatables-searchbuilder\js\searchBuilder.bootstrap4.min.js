(function(b){"function"===typeof define&&define.amd?define(["jquery","datatables.net-bs4","datatables.net-searchbuilder"],function(a){return b(a)}):"object"===typeof exports?module.exports=function(a,c){a||(a=window);c&&c.fn.dataTable||(c=require("datatables.net-bs4")(a,c).$);c.fn.dataTable.searchBuilder||require("datatables.net-searchbuilder")(a,c);return b(c)}:b(jQuery)})(function(b){var a=b.fn.dataTable;b.extend(!0,a.SearchBuilder.classes,{clearAll:"btn btn-light dtsb-clearAll"});b.extend(!0,a.Group.classes,
{add:"btn btn-light dtsb-add",clearGroup:"btn btn-light dtsb-clearGroup",logic:"btn btn-light dtsb-logic"});b.extend(!0,a.Criteria.classes,{condition:"form-control dtsb-condition",data:"form-control dtsb-data","delete":"btn btn-light dtsb-delete",left:"btn btn-light dtsb-left",right:"btn btn-light dtsb-right",value:"form-control dtsb-value"});return a.searchPanes});
