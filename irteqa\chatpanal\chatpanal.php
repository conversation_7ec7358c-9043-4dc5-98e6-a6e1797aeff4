<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>chatpanal</title>
</head>
<style>
    body{
        margin: 0;
        background: #67c970;
    }
    header{
        display: flex;
    direction: rtl;
    background: #67c970;
    }
    .topul{
        display: flex;
    }
    li{
        margin: 10px;
        margin-left: 0;
    }
    .menu{
        background: #67c970;
    margin-top: 0;
    width: 15%;
    float: left;
    }
    ul{
        list-style-type: none;
    }
    section{
        background: #ffff;
        float:right;
        width: 85%;
        height: 800px;
        border-radius: 20px 0px 0px 0px;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
    }
    .sbdy{
        margin: 25px;
    }
    .leftlist{
        font-size: 2em;
        font-size: 2em;
    background: #ffff;
    padding: 5px;
    border: 10px solid #67c970;
    border-radius: 20px;
    width: 100%;
    margin-top: 2px;

    }
    .test{
        margin: 50px;
        width: 100px;
        height: 100px;
        background: #67c970;
        border-radius: 50px;
    }  
    .slctd{
        background: white;
    width: 100%;
    padding: 10px;
    border-radius: 10px 0px 0 10px;
    }
    .leftlist li{
        background: #36af42;
        width: 100%;
    padding: 10px;
    border-radius: 10px 0px 0 10px;
    height: 45px;
    margin: 1px;
    color: white;
    }
  
    footer{
        position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #ffff;
    color: white;
    text-align: center;
    height: 35px;
    }
    li a {
  display: block;
  color: #000;
  
  text-decoration: none;
}
    li:hover {
  background-color: #ffff;
  color: #67c970;
}

</style>
<body>
    <header>
    <ul class="topul">
        <li>one
        </li>
        <li>tow
        </li>
        <li>three
        </li>
        <li>fore
        </li>
    </ul>
    </header>
    <div class="menu">
    <ul class="leftlist">
        <li>one
        </li>
        <li class="slctd">tow
        </li>
        <li>three
        </li>
        <li>fore
        </li>
    </ul>
</div>
<section class="scnone">
    <div class="sbdy"><h1>sections</h1>

    <div class="test"></div>
    

    </div>
</section>
<footer><a href="<EMAIL>">copy @ <EMAIL></a></footer>
</body>

</html>