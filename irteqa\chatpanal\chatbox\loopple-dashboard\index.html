<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Dashboard Builder</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/theme.css">
    <link rel="stylesheet" href="./assets/css/loopple/loopple.css">
</head>

<body class="null">
    <div class="wrapper">
        <nav class="sidebar js-sidebar" id="sidebar">
            <div class="sidebar-add" data-toggle="modal" data-target="#sidebarModal">
                <svg id="Layer_1" class="m-auto cursor-pointer" width="20px" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 114 114">
                    <rect style="fill:#525f7f;" x="0.5" y="44.5" width="113" height="24" rx="9.94" ry="9.94"></rect>
                    <path style="fill:#525f7f;" d="M146.56,85A9.46,9.46,0,0,1,156,94.44v4.11a9.46,9.46,0,0,1-9.44,9.44H53.44A9.46,9.46,0,0,1,44,98.56V94.44A9.46,9.46,0,0,1,53.44,85h93.11m0-1H53.44A10.48,10.48,0,0,0,43,94.44v4.11A10.48,10.48,0,0,0,53.44,109h93.11A10.48,10.48,0,0,0,157,98.56V94.44A10.48,10.48,0,0,0,146.56,84Z" transform="translate(-43 -40)"></path>
                    <rect style="fill:#525f7f;" x="45.5" y="0.5" width="24" height="113" rx="9.94" ry="9.94"></rect>
                    <path style="fill:#525f7f;" d="M102.56,41A9.46,9.46,0,0,1,112,50.44v93.11a9.46,9.46,0,0,1-9.44,9.44H98.44A9.46,9.46,0,0,1,89,143.56V50.44A9.46,9.46,0,0,1,98.44,41h4.11m0-1H98.44A10.48,10.48,0,0,0,88,50.44v93.11A10.48,10.48,0,0,0,98.44,154h4.11A10.48,10.48,0,0,0,113,143.56V50.44A10.48,10.48,0,0,0,102.56,40Z" transform="translate(-43 -40)"></path>
                </svg>
            </div>
        </nav>
        <div class="main" id="panel">
            <nav class="navbar navbar-expand navbar-light navbar-bg" id="navbarTop">
                <div class="m-auto" data-toggle="modal" data-target="#navbarModal">
                    <svg id="Layer_1" class="m-auto" width="20px" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 114 114">
                        <rect style="fill:#cacfda;" x="0.5" y="44.5" width="113" height="24" rx="9.94" ry="9.94"></rect>
                        <path style="fill:#cacfda;" d="M146.56,85A9.46,9.46,0,0,1,156,94.44v4.11a9.46,9.46,0,0,1-9.44,9.44H53.44A9.46,9.46,0,0,1,44,98.56V94.44A9.46,9.46,0,0,1,53.44,85h93.11m0-1H53.44A10.48,10.48,0,0,0,43,94.44v4.11A10.48,10.48,0,0,0,53.44,109h93.11A10.48,10.48,0,0,0,157,98.56V94.44A10.48,10.48,0,0,0,146.56,84Z" transform="translate(-43 -40)"></path>
                        <rect style="fill:#cacfda;" x="45.5" y="0.5" width="24" height="113" rx="9.94" ry="9.94"></rect>
                        <path style="fill:#cacfda;" d="M102.56,41A9.46,9.46,0,0,1,112,50.44v93.11a9.46,9.46,0,0,1-9.44,9.44H98.44A9.46,9.46,0,0,1,89,143.56V50.44A9.46,9.46,0,0,1,98.44,41h4.11m0-1H98.44A10.48,10.48,0,0,0,88,50.44v93.11A10.48,10.48,0,0,0,98.44,154h4.11A10.48,10.48,0,0,0,113,143.56V50.44A10.48,10.48,0,0,0,102.56,40Z" transform="translate(-43 -40)"></path>
                    </svg>
                </div>
            </nav>
            <main class="content">
                <div class="container-fluid p-0">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col mt-0">
                                    <h5 class="card-title">Sales</h5>
                                </div>

                                <div class="col-auto">
                                    <div class="stat text-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-truck align-middle">
                                            <rect x="1" y="3" width="15" height="13"></rect>
                                            <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon>
                                            <circle cx="5.5" cy="18.5" r="2.5"></circle>
                                            <circle cx="18.5" cy="18.5" r="2.5"></circle>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <h1 class="mt-1 mb-3">2.382</h1>
                            <div class="mb-0">
                                <span class="text-danger"> <i class="mdi mdi-arrow-bottom-right"></i> -3.65% </span>
                                <span class="text-muted">Since last week</span>
                            </div>
                        </div>
                    </div>
                    <div class="card" style="width: 18rem;">
                        <img src="https://demo-basic.adminkit.io/img/photos/unsplash-1.jpg" class="card-img-top" alt="...">
                        <div class="card-body">
                            <h5 class="card-title">Card title</h5>
                            <p class="card-text">Some quick example text to build on the card title and make up the bulk of the card#&amp;39;s content.</p>
                            <a href="#" class="btn btn-primary">Go somewhere</a>
                        </div>
                    </div>
                    <div class="row removable">
                        <div class="col-sm-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col mt-0">
                                            <h5 class="card-title">Sales</h5>
                                        </div>
                                        <div class="col-auto">
                                            <div class="stat text-primary">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-truck align-middle">
                                                    <rect x="1" y="3" width="15" height="13"></rect>
                                                    <polygon points="16 8 20 8 23 11 23 16 16 16 16 8"></polygon>
                                                    <circle cx="5.5" cy="18.5" r="2.5"></circle>
                                                    <circle cx="18.5" cy="18.5" r="2.5"></circle>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    <h1 class="mt-1 mb-3">2.382</h1>
                                    <div class="mb-0">
                                        <span class="text-danger">
                                            <i class="mdi mdi-arrow-bottom-right"></i> -3.65% </span>
                                        <span class="text-muted">Since last week</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col mt-0">
                                            <h5 class="card-title">Visitors</h5>
                                        </div>
                                        <div class="col-auto">
                                            <div class="stat text-primary">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-users align-middle">
                                                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                                    <circle cx="9" cy="7" r="4"></circle>
                                                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    <h1 class="mt-1 mb-3">14.212</h1>
                                    <div class="mb-0">
                                        <span class="text-success">
                                            <i class="mdi mdi-arrow-bottom-right"></i> 5.25% </span>
                                        <span class="text-muted">Since last week</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col mt-0">
                                            <h5 class="card-title">Earnings</h5>
                                        </div>
                                        <div class="col-auto">
                                            <div class="stat text-primary">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-dollar-sign align-middle">
                                                    <line x1="12" y1="1" x2="12" y2="23"></line>
                                                    <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    <h1 class="mt-1 mb-3">$21.300</h1>
                                    <div class="mb-0">
                                        <span class="text-success">
                                            <i class="mdi mdi-arrow-bottom-right"></i> 6.65% </span>
                                        <span class="text-muted">Since last week</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="card">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col mt-0">
                                            <h5 class="card-title">Orders</h5>
                                        </div>
                                        <div class="col-auto">
                                            <div class="stat text-primary">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-shopping-cart align-middle">
                                                    <circle cx="9" cy="21" r="1"></circle>
                                                    <circle cx="20" cy="21" r="1"></circle>
                                                    <path d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                                                </svg>
                                            </div>
                                        </div>
                                    </div>
                                    <h1 class="mt-1 mb-3">64</h1>
                                    <div class="mb-0">
                                        <span class="text-danger">
                                            <i class="mdi mdi-arrow-bottom-right"></i> -2.25% </span>
                                        <span class="text-muted">Since last week</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            <!-- Footer -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row text-muted">
                        <div class="col-6 text-start">
                            <p class="mb-0">
                                Made with <a class="text-muted" href="https://adminkit.io/" target="_blank"><strong>AdminKit</strong></a> &amp;
                                <a class="text-muted" href="https://www.loopple.com" target="_blank"><strong>Loopple</strong></a>
                            </p>
                        </div>
                        <div class="col-6 text-end">
                            <ul class="list-inline">
                                <li class="list-inline-item"> <a class="text-muted" href="https://adminkit.io/" target="_blank">Support</a> </li>
                                <li class="list-inline-item"> <a class="text-muted" href="https://adminkit.io/" target="_blank">Help Center</a> </li>
                                <li class="list-inline-item"> <a class="text-muted" href="https://adminkit.io/" target="_blank">Privacy</a> </li>
                                <li class="list-inline-item"> <a class="text-muted" href="https://adminkit.io/" target="_blank">Terms</a> </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>
    <div class="loopple-alert loopple-alert-gradient-dark loopple-alert-dismissible loopple-fade loopple-position-fixed loopple-z-index-9999 loopple-bottom-2 loopple-mx-auto loopple-text-center loopple-right-0 loopple-left-0 loopple-w-50" role="alert"><strong>View and edit your project online</strong><a class="loopple-btn loopple-btn-white loopple-ml-2" href="https://www.loopple.com/builder/project-JRLrfXgTkzWoTTVvzSGV3oJYgEIyETFjvjO" target="_blank">Editor</a><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button></div>
    <script src="https://demo-basic.adminkit.io/js/app.js"></script>
    <script>
        if (document.querySelector(".chart-line")) {
          var chartsLine = document.querySelectorAll(".chart-line");
        
          chartsLine.forEach(function(chart) {
              var ctx = chart.getContext("2d");
        
              var gradient = ctx.createLinearGradient(0, 0, 0, 225);
              gradient.addColorStop(0, "rgba(215, 227, 244, 1)");
              gradient.addColorStop(1, "rgba(215, 227, 244, 0)");
              // Line chart
              new Chart(ctx, {
                  type: "line",
                  data: {
                      labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                      datasets: [{
                          label: "Sales ($)",
                          fill: true,
                          backgroundColor: gradient,
                          borderColor: window.theme.primary,
                          data: [
                              2115,
                              1562,
                              1584,
                              1892,
                              1587,
                              1923,
                              2566,
                              2448,
                              2805,
                              3438,
                              2917,
                              3327
                          ]
                      }]
                  },
                  options: {
                      maintainAspectRatio: false,
                      legend: {
                          display: false
                      },
                      tooltips: {
                          intersect: false
                      },
                      hover: {
                          intersect: true
                      },
                      plugins: {
                          filler: {
                              propagate: false
                          }
                      },
                      scales: {
                          xAxes: [{
                              reverse: true,
                              gridLines: {
                                  color: "rgba(0,0,0,0.0)"
                              }
                          }],
                          yAxes: [{
                              ticks: {
                                  stepSize: 1000
                              },
                              display: true,
                              borderDash: [3, 3],
                              gridLines: {
                                  color: "rgba(0,0,0,0.0)"
                              }
                          }]
                      }
                  }
              });
              
          });
        }
        
        if (document.querySelector(".chart-pie")) {
          var chartsPie = document.querySelectorAll(".chart-pie");
        
          chartsPie.forEach(function(chart) {
              new Chart(chart, {
              type: "pie",
                data: {
                  labels: ["Chrome", "Firefox", "IE"],
                  datasets: [{
                    data: [4306, 3801, 1689],
                    backgroundColor: [
                      window.theme.primary,
                      window.theme.warning,
                      window.theme.danger
                    ],
                    borderWidth: 5
                  }]
                },
                options: {
                  responsive: !window.MSInputMethodContext,
                  maintainAspectRatio: false,
                  legend: {
                    display: false
                  },
                  cutoutPercentage: 75
                }
              });
          });
        }
        
        if (document.querySelector(".chart-line-double")) {
          var chartsLineDouble = document.querySelectorAll(".chart-line-double");
        
          chartsLineDouble.forEach(function(chart) {
              new Chart(chart, {
                type: "line",
                data: {
                    labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
                    datasets: [{
                        label: "Sales ($)",
                        fill: true,
                        backgroundColor: "transparent",
                        borderColor: window.theme.primary,
                        data: [2115, 1562, 1584, 1892, 1487, 2223, 2966, 2448, 2905, 3838, 2917, 3327]
                    }, {
                        label: "Orders",
                        fill: true,
                        backgroundColor: "transparent",
                        borderColor: "#adb5bd",
                        borderDash: [4, 4],
                        data: [958, 724, 629, 883, 915, 1214, 1476, 1212, 1554, 2128, 1466, 1827]
                    }]
                },
                options: {
                    maintainAspectRatio: false,
                    legend: {
                        display: false
                    },
                    tooltips: {
                        intersect: false
                    },
                    hover: {
                        intersect: true
                    },
                    plugins: {
                        filler: {
                            propagate: false
                        }
                    },
                    scales: {
                        xAxes: [{
                            reverse: true,
                            gridLines: {
                                color: "rgba(0,0,0,0.05)"
                            }
                        }],
                        yAxes: [{
                            ticks: {
                                stepSize: 500
                            },
                            display: true,
                            borderDash: [5, 5],
                            gridLines: {
                                color: "rgba(0,0,0,0)",
                                fontColor: "#fff"
                            }
                        }]
                    }
                }
              });
            
          });
        }
        
        if (document.querySelector(".chart-bar")) {
        
          var chartsBar = document.querySelectorAll(".chart-bar");
        
          chartsBar.forEach(function(chart) {
        
              new Chart(chart, {
              	type: "bar",
              	data: {
              		labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
              		datasets: [{
              			label: "Last year",
              			backgroundColor: window.theme.primary,
              			borderColor: window.theme.primary,
              			hoverBackgroundColor: window.theme.primary,
              			hoverBorderColor: window.theme.primary,
              			data: [54, 67, 41, 55, 62, 45, 55, 73, 60, 76, 48, 79],
              			barPercentage: .75,
              			categoryPercentage: .5
              		}, {
              			label: "This year",
              			backgroundColor: "#dee2e6",
              			borderColor: "#dee2e6",
              			hoverBackgroundColor: "#dee2e6",
              			hoverBorderColor: "#dee2e6",
              			data: [69, 66, 24, 48, 52, 51, 44, 53, 62, 79, 51, 68],
              			barPercentage: .75,
              			categoryPercentage: .5
              		}]
              	},
              	options: {
              		maintainAspectRatio: false,
              		legend: {
              			display: false
              		},
              		scales: {
              			yAxes: [{
              				gridLines: {
              					display: false
              				},
              				stacked: false,
              				ticks: {
              					stepSize: 20
              				}
              			}],
              			xAxes: [{
              				stacked: false,
              				gridLines: {
              					color: "transparent"
              				}
              			}]
              		}
              	}
              }); 
          }); 
        }
    </script>
    <script src="./assets/js/loopple/loopple.js"></script>
</body>