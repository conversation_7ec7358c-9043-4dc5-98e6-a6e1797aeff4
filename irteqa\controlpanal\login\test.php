
<?php
// include classes or rely on Composer autoloader
require_once 'lib/phpUserAgent.php';
require_once 'lib/phpUserAgentStringParser.php';
require_once 'lib/UserInformation.php';


// Create a user agent
$userAgent = new phpUserAgent();

// Interrogate the user agent
echo $userAgent->getBrowserName(), " ";     // firefox
echo $userAgent->getBrowserVersion(),"<br>" ;  // 3.6
echo $userAgent->getOperatingSystem(),"<br>" ; // linux
echo $userAgent->getEngine()  ."<br>"   ;      // gecko
echo $_SERVER['HTTP_USER_AGENT'] . "\n\n" ,"<br>";
echo UserInfo::get_ip();

?>

