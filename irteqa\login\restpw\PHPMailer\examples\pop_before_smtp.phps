<?php

/**
 * This example shows how to use POP-before-SMTP for authentication.
 * POP-before-SMTP is a very old technology that is hardly used any more.
 */

//Import PHPMailer classes into the global namespace
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\Exception;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\POP3;
use <PERSON><PERSON>Mailer\PHPMailer\SMTP;

require '../vendor/autoload.php';

//Authenticate via POP3.
//After this you should be allowed to submit messages over SMTP for a few minutes.
//Only applies if your host supports POP-before-SMTP.
$pop = POP3::popBeforeSmtp('pop3.example.com', 110, 30, 'username', 'password', 1);

//Create a new PHPMailer instance
//Passing true to the constructor enables the use of exceptions for error handling
$mail = new PHPMailer(true);
try {
    $mail->isSMTP();
    //Enable SMTP debugging
    //SMTP::DEBUG_OFF = off (for production use)
    //SMTP::DEBUG_CLIENT = client messages
    //SMTP::DEBUG_SERVER = client and server messages
    $mail->SMTPDebug = SMTP::DEBUG_SERVER;
    //Set the hostname of the mail server
    $mail->Host = 'mail.example.com';
    //Set the SMTP port number - likely to be 25, 465 or 587
    $mail->Port = 25;
    //Whether to use SMTP authentication
    $mail->SMTPAuth = false;
    //Set who the message is to be sent from
    $mail->setFrom('<EMAIL>', 'First Last');
    //Set an alternative reply-to address
    $mail->addReplyTo('<EMAIL>', 'First Last');
    //Set who the message is to be sent to
    $mail->addAddress('<EMAIL>', 'John Doe');
    //Set the subject line
    $mail->Subject = 'PHPMailer POP-before-SMTP test';
    //Read an HTML message body from an external file, convert referenced images to embedded,
    //and convert the HTML into a basic plain-text alternative body
    $mail->msgHTML(file_get_contents('contents.html'), __DIR__);
    //Replace the plain text body with one created manually
    $mail->AltBody = 'This is a plain-text message body';
    //Attach an image file
    $mail->addAttachment('images/phpmailer_mini.png');
    //send the message
    //Note that we don't need check the response from this because it will throw an exception if it has trouble
    $mail->send();
    echo 'Message sent!';
} catch (Exception $e) {
    echo $e->errorMessage(); //Pretty error messages from PHPMailer
} catch (\Exception $e) {
    echo $e->getMessage(); //Boring error messages from anything else!
}
