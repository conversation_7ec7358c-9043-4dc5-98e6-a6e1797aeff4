# Nginx Configuration File
# https://nginx.org/en/docs/


# https://nginx.org/en/docs/ngx_core_module.html#user
# https://en.wikipedia.org/wiki/Principle_of_least_privilege
user                  www-data;


# https://nginx.org/en/docs/ngx_core_module.html#worker_processes
worker_processes      auto;


# Maximum number of open files per worker process.
# Should be > worker_connections.
# https://nginx.org/en/docs/ngx_core_module.html#worker_rlimit_nofile
worker_rlimit_nofile  2048;

include /etc/nginx/modules-enabled/*.conf;

# Provides the configuration file context in which the directives that affect
# connection processing are specified.
# https://nginx.org/en/docs/ngx_core_module.html#events
events {

  # Should be < worker_rlimit_nofile
  # https://nginx.org/en/docs/ngx_core_module.html#worker_connections
  worker_connections 2000;

  # multi_accept on;
}

# Log errors and warnings to this file
# https://nginx.org/en/docs/ngx_core_module.html#error_log
error_log             /var/log/nginx/error.log warn;


# The file storing the process ID of the main process
# https://nginx.org/en/docs/ngx_core_module.html#pid
pid                   /var/run/nginx.pid;


http {

  ##
  ## Basic Settings
  ##
  types_hash_max_size             2048;
  server_names_hash_bucket_size   64;


  ##
  ## CONNECTION
  ##
  sendfile                on;
  sendfile_max_chunk      1m;
  tcp_nopush              on;
  tcp_nodelay             on;
  keepalive_timeout       30;

  include                 /etc/nginx/mime.types;
# default_type            application/octet-stream;

  # Set character encodings
  include                 /etc/nginx/snippets/character_encodings.conf;


  ##
  ## SSL Settings
  ##
  ssl_protocols                   TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
  ssl_prefer_server_ciphers       on;


  ##
  ## Logging Settings
  ##
  log_format main '[$remote_addr] - $remote_user [$time_local] [$http_host] $status "$request" '
                  '$body_bytes_sent "$http_referer" '
                  '"$http_user_agent" "$http_x_forwarded_for" $request_time';

  access_log      /var/log/nginx/_access.log  main;
  error_log       /var/log/nginx/_error.log   warn;


  ##
  ## Security
  ##
  # Hide Nginx version information.
  include /etc/nginx/snippets/server_software_information.conf;
  # server_name_in_redirect       off;


  ##
  ## Gzip Settings
  ##
  gzip                    on;
  gzip_disable            "msie6";
  gzip_vary               on;
  gzip_comp_level         6;
  gzip_min_length         256;
  gzip_proxied            any;
  gzip_buffers            16 8k;
  gzip_http_version       1.1;
  gzip_types
    application/atom+xml
    application/geo+json
    application/javascript
    application/x-javascript
    application/json
    application/ld+json
    application/manifest+json
    application/rdf+xml
    application/rss+xml
    application/xml+rss
    application/vnd.ms-fontobject
    application/wasm
    application/x-web-app-manifest+json
    application/xhtml+xml
    application/xml
    font/eot
    font/otf
    font/ttf
    image/bmp
    image/svg+xml
    text/cache-manifest
    text/calendar
    text/css
    text/javascript
    text/markdown
    text/plain
    text/xml
    text/vcard
    text/vnd.rim.location.xloc
    text/vtt
    text/x-component
    text/x-cross-domain-policy;


  # Specify file cache expiration
  include /etc/nginx/snippets/cache_expiration.conf;


  ##
  ## VHosts Configs
  ##

  include /etc/nginx/conf.d/*.conf;
  include /etc/nginx/sites-enabled/*;

}


#mail {
#       # See sample authentication script at:
#       # http://wiki.nginx.org/ImapAuthenticateWithApachePhpScript
#
#       # auth_http localhost/auth.php;
#       # pop3_capabilities "TOP" "USER";
#       # imap_capabilities "IMAP4rev1" "UIDPLUS";
#
#       server {
#               listen     localhost:110;
#               protocol   pop3;
#               proxy      on;
#       }
#
#       server {
#               listen     localhost:143;
#               protocol   imap;
#               proxy      on;
#       }
#}
