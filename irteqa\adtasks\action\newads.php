<style>
  /* ستايلات للخلفية السوداء التي تعمل كطبقة تظليل */
  .overlay2 {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* لون الخلفية مع شفافية */
    display: none;
    /* إخفاء الطبقة بشكل افتراضي */
    justify-content: center;
    align-items: center;
    z-index: 999;
    /* تحديد ترتيب الطبقة */
  }

  /* ستايلات لصندوق النافذة المنبثقة */
  .popup2  {
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  }
.newads{
  padding: 10px;
    border-radius: 10px;
    border: 1px solid rgb(192, 192, 192);
    margin-top: 25px;
}
  .newitem {
    padding: 10px;
    background: rgb(233, 233, 233);
    border-radius: 10px;
    border: 1px solid rgb(192, 192, 192);
    display: inline-grid;
    justify-content: center;
  }

  .newtitl {
    border: 1px solid #b1b1b1;
    padding: 20px;
    border-radius: 10px;
    margin-top: 25px;
    margin-bottom: 5px;
    place-self: center;
  }

  .sctntitl {
    background: #e9e9e9;
    position: relative;
    top: -60px;
    right: 0px;
    border: 1px solid #ababab;
    padding: 5px;
    border-radius: 10px;
    width: max-content;
    margin: 0;
  }
  .adstitl {
    background: #e9e9e9;
    position: relative;
    top: -50px;
    right: 0px;
    border: 1px solid #ababab;
    padding: 5px;
    border-radius: 10px;
    width: max-content;
    margin: 0;
  }
  .titlfrm {
    display: flex;
    align-items: center;
  }

  input[type="text"] {
    width: 600px;
    height: 30px;
    border-radius: 5px;
    border: 2px solid #c5c5c5;
    margin: 5px;
    font-weight: bold;
    font-size: 1.3em;
    padding: 5px;
  }

  input[type="button"] {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
    margin: 3px;
    font-weight: bold;
    font-size: 1.5em;
  }

  input[type="button"]:hover {
    background-color: #a0d9ff;
    color: rgb(71, 71, 71);
  }
  .titlfrm {
        display: flex;
        align-items: center;
    }

    .adsfrm {
        display: grid;
        justify-items: center;
        margin-top: -50px;
    }

    textarea {
        width: 600px;
        height: 400px;
        font-size: 1.3em;
        font-weight: bold;
        padding: 5px;
        border-radius: 5px;
    border: 2px solid #c5c5c5;
    }

    input[type="button"] {
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 5px;
        padding: 10px 20px;
        cursor: pointer;
        margin: 3px;
        font-weight: bold;
        font-size: 1.5em;
    }

    input[type="button"]:hover {
        background-color: #a0d9ff;
        color: rgb(71, 71, 71);
    }
    option{
        font-weight: bold;
        font-size: 1.2em;
    }
    label{
        margin-right: 10px;
        font-weight: bold;
    }
    .slctoptn{
        display: flex;
    align-items: center;
    justify-content: center;
    margin: 5px;
    }
    .social-icons{
        display: flex;
    justify-content: flex-end;
    }
    .social-icons img {
      width: 15px; /* تحديد العرض المناسب للصور */
      margin: 4.4px;
    }
    .haraj{
        color: #213386;
        margin-left: 4.4px;
        margin-right: 4.4px;
    }
    input[type="checkbox"]{
      margin: 0 3px 0 4px;
    }
</style>
<!-- الطبقة السوداء التي تعمل كظليل -->
<div class="overlay2" id="overlay2">
  <!-- صندوق النافذة المنبثقة -->
  <div class="popup2">

  <div class="newads">

  <h1 class="adstitl">إضافة إعلان</h1>
            <form action="" method="post" class="adsfrm">
                <input type="text" name="" id="" value="" placeholder="عنوان الإعلان">
                <textarea type="text" name="" id="" placeholder="نص الإعلان"></textarea>
        </div>
        <div class="slctoptn">
          <label for="">الجدولة:</label>
            <select name="" id="">
                <option value=""></option>
                <option value="">يومي</option>
                <option value="">أسبوعي</option>
            </select>
            <label for="">القسم:</label>
            <select name="" id="">
                <option value=""></option>
                <option value="">مكتب العمل</option>
                <option value="">وزارة التجارة</option>
            </select>
            
            <label for="">التقييم: </label>
            <select name="" id="">
                <option value=""></option>
                <option value="">1</option>
                <option value="">2</option>
                <option value="">3</option>
                <option value="">4</option>
                <option value="">5</option>
                <option value="">6</option>
                <option value="">7</option>
                <option value="">8</option>
                <option value="">9</option>
                <option value="">10</option>
            </select>
            <div class="sotlcchbx">
                <div class="social-icons">
                    <label class="haraj" for="">ح</label>
                    <img src="img/facebook.png" alt="Facebook">
                    <img src="img/twitter.png" alt="Twitter">
                    <img src="img/instagram.png" alt="Instagram">
                    <img src="img/whatsapp.png" alt="LinkedIn">
                    <img src="img/youtube.png" alt="YouTube">
                    <img src="img/telegram.png" alt="telegram">
                    <img src="img/tik-tok.png" alt="tik-tok">
                  </div>
            <label for="">المنصات:</label>
            <input type="checkbox" name="" id="">
            <input type="checkbox" name="" id="">
            <input type="checkbox" name="" id="">
            <input type="checkbox" name="" id="">
            <input type="checkbox" name="" id="">
            <input type="checkbox" name="" id="">
            <input type="checkbox" name="" id="">
            <input type="checkbox" name="" id="">
        </div>
            <input type="button" value="أضف">

            </form>
        </div>
    
    <button onclick="closePopup2()">إغلاق</button>
    </div></div>

  <script>
    // دالة لفتح النافذة المنبثقة
    function openPopup2() {
      document.getElementById("overlay2").style.display = "flex";
    }

    // دالة لإغلاق النافذة المنبثقة
    function closePopup2() {
      document.getElementById("overlay2").style.display = "none";
    }
  </script>