<?php
/**
 * ملف لاختبار إضافة العميل عبر AJAX مباشرة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$test_result = null;
$error_message = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_ajax'])) {
    // محاكاة طلب AJAX
    $test_data = [
        'action' => 'add_customer',
        'name' => $_POST['test_name'] ?? '',
        'phone' => $_POST['test_phone'] ?? '',
        'email' => $_POST['test_email'] ?? '',
        'tax_number' => $_POST['test_tax_number'] ?? '',
        'address' => $_POST['test_address'] ?? ''
    ];
    
    // حفظ البيانات في $_POST لمحاكاة طلب AJAX
    foreach ($test_data as $key => $value) {
        $_POST[$key] = $value;
    }
    
    // تشغيل كود AJAX handler
    ob_start();
    
    try {
        $db = getCurrentUserDB();
        if ($db === null || $db->connect_error) {
            $error_message = 'خطأ في الاتصال بقاعدة البيانات';
        } else {
            // استدعاء دالة إضافة العميل مباشرة
            include_once __DIR__ . '/ajax_handler.php';
        }
    } catch (Exception $e) {
        $error_message = 'حدث استثناء: ' . $e->getMessage();
    }
    
    $output = ob_get_clean();
    
    if ($output) {
        $test_result = json_decode($output, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $error_message = 'خطأ في تحليل JSON: ' . $output;
        }
    } else {
        $error_message = 'لم يتم إرجاع أي نتيجة';
    }
}

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار إضافة العميل عبر AJAX</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات الاختبار</h5>
        <p>هذه الصفحة تسمح لك باختبار وظيفة إضافة العميل مباشرة دون استخدام JavaScript.</p>
    </div>
    
    <div class="row">
        <!-- نموذج الاختبار -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">نموذج اختبار إضافة العميل</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="test_name" class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" id="test_name" name="test_name" 
                                   value="<?php echo htmlspecialchars($_POST['test_name'] ?? 'عميل تجريبي ' . date('His')); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="test_phone" class="form-label">رقم الهاتف</label>
                            <input type="text" class="form-control" id="test_phone" name="test_phone" 
                                   value="<?php echo htmlspecialchars($_POST['test_phone'] ?? '0501234567'); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="test_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="test_email" name="test_email"
                                   value="<?php echo htmlspecialchars($_POST['test_email'] ?? '<EMAIL>'); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="test_tax_number" class="form-label">الرقم الضريبي</label>
                            <input type="text" class="form-control" id="test_tax_number" name="test_tax_number"
                                   value="<?php echo htmlspecialchars($_POST['test_tax_number'] ?? '*********'); ?>">
                        </div>
                        
                        <div class="mb-3">
                            <label for="test_address" class="form-label">العنوان</label>
                            <textarea class="form-control" id="test_address" name="test_address" rows="3"><?php echo htmlspecialchars($_POST['test_address'] ?? 'عنوان تجريبي'); ?></textarea>
                        </div>
                        
                        <button type="submit" name="test_ajax" class="btn btn-primary">
                            <i class="fas fa-vial"></i> اختبار إضافة العميل
                        </button>
                        
                        <a href="test_ajax_customer.php" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> إعادة تعيين
                        </a>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- نتائج الاختبار -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header <?php echo $test_result ? ($test_result['success'] ? 'bg-success' : 'bg-danger') : 'bg-secondary'; ?> text-white">
                    <h5 class="mb-0">نتائج الاختبار</h5>
                </div>
                <div class="card-body">
                    <?php if ($test_result): ?>
                        <?php if ($test_result['success']): ?>
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> نجح الاختبار!</h6>
                                <p><strong>الرسالة:</strong> <?php echo htmlspecialchars($test_result['message']); ?></p>
                                <p><strong>معرف العميل:</strong> <?php echo htmlspecialchars($test_result['customer_id']); ?></p>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-times-circle"></i> فشل الاختبار!</h6>
                                <p><strong>الرسالة:</strong> <?php echo htmlspecialchars($test_result['message']); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <h6>تفاصيل الاستجابة:</h6>
                        <pre class="bg-light p-2 rounded"><code><?php echo htmlspecialchars(json_encode($test_result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></code></pre>
                        
                    <?php elseif ($error_message): ?>
                        <div class="alert alert-danger">
                            <h6><i class="fas fa-exclamation-triangle"></i> خطأ في الاختبار!</h6>
                            <p><?php echo htmlspecialchars($error_message); ?></p>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-info">
                            <p>لم يتم تشغيل أي اختبار بعد. املأ النموذج واضغط "اختبار إضافة العميل".</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">معلومات النظام</h6>
                </div>
                <div class="card-body">
                    <?php
                    $db = getCurrentUserDB();
                    $db_status = ($db && !$db->connect_error) ? 'متصل' : 'غير متصل';
                    
                    $customers_table_exists = false;
                    $customers_count = 0;
                    
                    if ($db && !$db->connect_error) {
                        $check_table = $db->query("SHOW TABLES LIKE 'customers'");
                        $customers_table_exists = ($check_table && $check_table->num_rows > 0);
                        
                        if ($customers_table_exists) {
                            $count_result = $db->query("SELECT COUNT(*) as count FROM customers");
                            $customers_count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
                        }
                    }
                    ?>
                    
                    <div class="row">
                        <div class="col-6">
                            <small><strong>قاعدة البيانات:</strong></small><br>
                            <span class="badge <?php echo $db_status == 'متصل' ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $db_status; ?>
                            </span>
                        </div>
                        <div class="col-6">
                            <small><strong>جدول العملاء:</strong></small><br>
                            <span class="badge <?php echo $customers_table_exists ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $customers_table_exists ? 'موجود' : 'غير موجود'; ?>
                            </span>
                        </div>
                    </div>
                    
                    <?php if ($customers_table_exists): ?>
                    <div class="mt-2">
                        <small><strong>عدد العملاء الحالي:</strong> <?php echo $customers_count; ?></small>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أدوات إضافية -->
    <div class="card mt-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">أدوات إضافية</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <h6>اختبارات أخرى:</h6>
                    <div class="d-grid gap-2">
                        <a href="debug_ajax.php" class="btn btn-outline-warning btn-sm" target="_blank">
                            <i class="fas fa-bug"></i> تشخيص AJAX
                        </a>
                        <a href="test_add_customer_feature.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-vial"></i> اختبار الميزة
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>إدارة الجداول:</h6>
                    <div class="d-grid gap-2">
                        <a href="check_tables.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-database"></i> فحص الجداول
                        </a>
                        <a href="customers.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-users"></i> قائمة العملاء
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>اختبار الفواتير:</h6>
                    <div class="d-grid gap-2">
                        <a href="add_purchase.php" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-shopping-cart"></i> إضافة مشتريات
                        </a>
                        <a href="add_sale.php" class="btn btn-outline-success btn-sm" target="_blank">
                            <i class="fas fa-cash-register"></i> إضافة مبيعات
                        </a>
                    </div>
                </div>
                <div class="col-md-3">
                    <h6>العودة:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="test_ajax_customer.php" class="btn btn-info btn-sm">
                            <i class="fas fa-redo"></i> إعادة تحميل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
