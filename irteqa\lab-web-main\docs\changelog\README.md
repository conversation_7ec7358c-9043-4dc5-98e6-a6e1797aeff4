---
title: Changelog
---

# Changelog

All notable changes to this project will be documented in this file.

## June 2020

- Added Language: Russian

## May 2020

### May 6, 2020

- Fix Demo Data not including Hobbies section
- Updated translations for all languages
- Added Language: Hebrew
- Added Language: Italian

## April 2020

### April 28, 2020

- Added Feature to Reorder Skills/Hobbies
- Added Hobbies Section to Left Sidebar
- Updated Templates to Add Hobbies

### April 23, 2020

- Fix Issue with Page Controller Icon Size
- Fix Issue with Checkbox Icon Toggle

### April 23, 2020

- Transfer all external resources to local, self-host everything
- Shorten entry animation by a second
- Optimize Images through `imgbot`

### April 22, 2020

- Display Original Language Name alongside English Language Name
- Added Language: Tamil
- Added Language: Vietnamese

### April 17, 2020

- Updated Dependencies across App
- Added Language: Arabic

### April 16, 2020

- Brought Back Browser Print Method, you can now print parseable resumes
- Modified Templates to use ID as Key in items, instead of names
- Added Language: Polish

### April 11, 2020

- Added Language: Danish
- Added Language: Dutch
- Added Language: Portuguese

### April 10, 2020

- Bugfix: Photo not visible in PDF export, Celebi

### April 8, 2020

- Added Language: Spanish

### April 6, 2020

- Designed Celebi Template

### April 5, 2020

- Added Print Dialog to set Quality & Print Type before Exporting PDF
- Added Pan-Zoom Animation to indicate the artboard is interactive

### April 3, 2020

- Added Language: German

### April 2, 2020

- Fix type in Contributing section of Documentation
- Fix issue where PDF was printing in A4, instead print whole page
- Fix Castform Templates not updating when Skills Heading is updated

### April 1, 2020

- Designed Glalie Template
- Added Page Controller for Quick Actions
- Implemented `react-easy-panzoom` for Pan & Zoom abilities in the artboard
- Added Language: Chinese

## March 2020

### March 31, 2020

- Migrated to PDF Generation using `html2canvas` & `jsPDF`
- Added Language: French
- Added Language: Kannada

### March 30, 2020

- Dockerize App for Faster Development/Deployments
- Added Translation Systems
- Added Language: Hindi

### March 29, 2020

- Add Issue Templates in GitHub for Bug Reports and Feature Requests
- Revised Documentation in VuePress
- Add Google Analytics to VuePress Documentation
- Implement "Deploy to Netlify" button, by [hwang381](https://github.com/hwang381)

### March 28, 2020

- Add About Tab in the Right Sidebar
- JSON Migration Bugfix, by [Panzki](https://github.com/Panzki)
- Designed Castform Template

### March 27, 2020

- Add Google Site Verification Tag
- Fix bug with Gengar Template not respecting enable values
- Add Language & References Section to Resumes
- Update Onyx, Pikachu & Gengar Templates
- Add Nunito Font, removed few other fonts
- Add Entry Animation using [animate.css](https://daneden.github.io/animate.css/)
- Fix Awkward Page Breaks in Resume when Printing
- Make Links Clickable in the Resume PDF

### March 26, 2020

- Fix bug in Pikachu Template where photo was not visible
- Add option to enter any font family stored locally on system
- Allow printing of more than one page
- Add Markdown Support to Descriptions
- Designed Gengar Template

### March 25, 2020

- Released App to Public through Firebase Hosting
- Add Firebase Analytics
- Add Progressive Web App Caching
- Designed Pikachu Template

### March 24, 2020

- Initiating Development of App
- Designed Onyx Template
