<?php
// دالة لإنشاء رقم فاتورة تلقائي
function generateInvoiceNumber($prefix = 'INV') {
    return $prefix . '-' . date('Ymd') . '-' . strtoupper(uniqid());
}

// دالة لحساب الضريبة والمجموع
function calculateTaxAndTotal($items) {
    $subtotal = 0;
    $total_tax = 0;

    foreach ($items as $item) {
        $item_total = $item['quantity'] * $item['unit_price'];
        $item_tax = $item_total * ($item['tax_rate'] / 100);

        $subtotal += $item_total;
        $total_tax += $item_tax;
    }

    $total = $subtotal + $total_tax;

    return [
        'subtotal' => $subtotal,
        'tax_amount' => $total_tax,
        'total' => $total
    ];
}
function resetDBConnection($db) {
    while ($db->more_results()) {
        $db->next_result();
        if ($result = $db->store_result()) {
            $result->free();
        }
    }
    return $db;
}

// دالة لعرض رسائل الخطأ أو النجاح
function displayMessages() {
    if (isset($_SESSION['error'])) {
        echo '<div class="alert alert-danger">' . $_SESSION['error'] . '</div>';
        unset($_SESSION['error']);
    }

    if (isset($_SESSION['success'])) {
        echo '<div class="alert alert-success">' . $_SESSION['success'] . '</div>';
        unset($_SESSION['success']);
    }
}

// دالة للتعامل مع أخطاء قاعدة البيانات
function handleDBError($db, $query = '', $location = null) {
    $error_message = __('database_error') . ": " . $db->error;
    error_log($error_message . " in query: " . $query);

    $_SESSION['error'] = $error_message;

    if ($location) {
        header("Location: " . $location);
        exit();
    }

    return false;
}

// دالة للتحقق من وجود الجداول المطلوبة
function checkRequiredTables($db) {
    $required_tables = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
    $missing_tables = [];

    foreach ($required_tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows == 0) {
            $missing_tables[] = $table;
        }
    }

    return $missing_tables;
}

// دالة للتحقق من سلامة قاعدة البيانات
function validateDatabase($db) {
    if (!$db || $db->connect_error) {
        return [
            'status' => false,
            'message' => 'فشل الاتصال بقاعدة البيانات'
        ];
    }

    $missing_tables = checkRequiredTables($db);

    if (!empty($missing_tables)) {
        return [
            'status' => false,
            'message' => 'جداول مفقودة: ' . implode(', ', $missing_tables)
        ];
    }

    return [
        'status' => true,
        'message' => 'قاعدة البيانات سليمة'
    ];
}

// دالة للتحقق من نتائج الاستعلام
function checkQueryResult($result, $db, $query = '', $location = null) {
    if (!$result) {
        return handleDBError($db, $query, $location);
    }
    return true;
}

// دالة للحصول على اتصال قاعدة البيانات للمستخدم الحالي
function getCurrentUserDB() {
    return getOperationsDB();
}

// دالة للتحقق من وجود جدول المستخدم
function userTableExists($table, $username = null) {
    if (!$username && isset($_SESSION['username'])) {
        $username = $_SESSION['username'];
    }

    if (!$username) {
        return false;
    }

    $operations_db = getOperationsDB();
    if (!$operations_db) {
        return false;
    }

    $table_name = getUserTableName($table, $username);
    if (!$table_name) {
        return false;
    }

    $result = $operations_db->query("SHOW TABLES LIKE '$table_name'");
    return ($result && $result->num_rows > 0);
}

// دالة لتسجيل النشاطات
function logActivity($action, $table, $record_id, $old_data = null, $new_data = null, $description = '') {
    // يمكن تطوير هذه الدالة لاحقاً لتسجيل النشاطات
    error_log("Activity: $action on $table (ID: $record_id) - $description");
}
?>