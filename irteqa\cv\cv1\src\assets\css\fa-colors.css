/*! - - - - - - - - - -    Fontawesome Colors Tweaks   - - - - - -  - - - - - */
/* ---------------------------------------------- /*
 *               Fonts
/* ---------------------------------------------- */

.fa-bootstrap {
  color: #602c50;
}

.fa-foxpro {
  color: #ff9500;
}

.fa-css3-alt {
  color: #0099e5;
}

.fa-digital-ocean {
  color: #008bcf;
}

.fa-hashtag {
  color: #33cc33;
}

.fa-magento {
  color: #f46f25;
}

.fa-ebay {
  color: #0064d2;
}

.fa-paypal
, .fa-cc-paypal {
  color: #003087;
}

.fa-aws {
  color: #ff9900;
}

.fa-bitbucket {
  color: #205081;
}

.fa-brain {
  color: #999966;
}

.fa-docker {
  color: #0db7ed;
}

.fa-facebook {
  color: #3b5998;
}

.fa-facebook-messenger {
  color: #3399ff;
}

.fa-git
, .fa-git-square
, .fa-github {
  color: #6cc644;
}

.fa-gulp {
  color: #af0606;
}

.fa-html5 {
  color: #e34f26;
}

.fa-js
, .fa-js-square {
  color: #f7df1e;
}

.fa-node
, .fa-node-js {
  color: #44883e;
}

.fa-npm {
  color: #cb3837;
}

.fa-laravel {
  color: #f55247;
}

.fa-linkedin {
  color: #0077b5;
}

.fa-mongodb {
  color: #589636;
}

.fa-mysql {
  color: #00758f;
}

.fa-php {
  color: #4f5b93;
}

.fa-shopify {
  color: #96bf48;
}

.fa-prestashop {
  color: #fbbb22;
}

.fa-steam {
  color: #00adee;
}

.fa-themeco {
  color: #0b7bc2;
}

.fa-centos {
  color: #951c7a;
}

.fa-redhat {
  color: #cc0000;
}

.fa-ubuntu {
  color: #dd4814;
}

.fa-woocommerce {
  color: #96588a;
}

.fa-microsoft {
  color: #00a1f1;
}

.fa-windows {
  color: #00a1f1;
}

.fa-perl {
  color: #96bf48;
}

.fa-rabbit {
  color: #96bf48;
}

.fa-redis {
  color: #7a0c00;
}

.fa-graphql {
  color: #ed008c;
}

.fa-confluence {
  color: #0052cc;
}

.fa-jira {
  color: #003366;
}

.fa-telegram,
.fa-robot {
  color: #0088cc;
}

.fa-viber {
  color: #8f5db7;
}

.fa-gitlab,
.fa-gitlab-square {
  /* color: #fca326; */
  color: #554488;
}
