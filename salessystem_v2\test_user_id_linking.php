<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ربط user_id - salessystem_v2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 50px auto;
            max-width: 1200px;
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-content {
            padding: 40px;
        }
        .test-section {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .user-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background: #f8f9fa;
        }
        .table-info {
            font-size: 12px;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 15px;
            margin: 2px;
            display: inline-block;
        }
        .has-user-id {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .no-user-id {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <?php
    require_once __DIR__.'/config/init.php';
    ?>
    
    <div class="container">
        <div class="test-container">
            <div class="test-header">
                <h1><i class="fas fa-user-tag"></i> اختبار ربط user_id</h1>
                <p class="mb-0">فحص ربط معرف المستخدم في جداول العمليات</p>
            </div>
            
            <div class="test-content">
                <!-- فحص قواعد البيانات -->
                <div class="test-section">
                    <h3><i class="fas fa-database"></i> فحص قواعد البيانات</h3>
                    
                    <?php
                    global $main_db;
                    $operations_db = getOperationsDB();
                    
                    if ($main_db && !$main_db->connect_error) {
                        echo "<p class='success'><i class='fas fa-check'></i> قاعدة البيانات الرئيسية متصلة</p>";
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال بقاعدة البيانات الرئيسية</p>";
                    }
                    
                    if ($operations_db && !$operations_db->connect_error) {
                        echo "<p class='success'><i class='fas fa-check'></i> قاعدة بيانات العمليات متصلة</p>";
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال بقاعدة بيانات العمليات</p>";
                    }
                    ?>
                </div>

                <!-- فحص ربط user_id في جداول المستخدمين -->
                <div class="test-section">
                    <h3><i class="fas fa-users"></i> فحص ربط user_id في جداول المستخدمين</h3>
                    
                    <?php
                    if ($main_db && $operations_db) {
                        $users_result = $main_db->query("SELECT id, username FROM users ORDER BY id");
                        
                        if ($users_result && $users_result->num_rows > 0) {
                            echo "<div class='row'>";
                            
                            while ($user = $users_result->fetch_assoc()) {
                                $user_id = $user['id'];
                                $username = $user['username'];
                                
                                echo "<div class='col-md-6 mb-3'>";
                                echo "<div class='user-card'>";
                                echo "<h6><i class='fas fa-user'></i> $username (ID: $user_id)</h6>";
                                
                                // فحص الجداول وعمود user_id
                                $tables = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
                                $tables_with_user_id = [];
                                $tables_without_user_id = [];
                                
                                foreach ($tables as $table) {
                                    $table_name = getUserTableName($table, $username);
                                    if ($table_name) {
                                        // فحص وجود عمود user_id
                                        $columns_result = $operations_db->query("SHOW COLUMNS FROM `$table_name` LIKE 'user_id'");
                                        if ($columns_result && $columns_result->num_rows > 0) {
                                            // فحص البيانات الموجودة
                                            $data_result = $operations_db->query("SELECT COUNT(*) as count FROM `$table_name` WHERE user_id = $user_id");
                                            $count = 0;
                                            if ($data_result && $row = $data_result->fetch_assoc()) {
                                                $count = $row['count'];
                                            }
                                            $tables_with_user_id[] = "$table ($count سجل)";
                                        } else {
                                            $tables_without_user_id[] = $table;
                                        }
                                    }
                                }
                                
                                // عرض الجداول التي تحتوي على user_id
                                if (!empty($tables_with_user_id)) {
                                    echo "<p class='mb-2'><strong>جداول مع user_id:</strong></p>";
                                    foreach ($tables_with_user_id as $table_info) {
                                        echo "<span class='table-info has-user-id'>$table_info</span>";
                                    }
                                }
                                
                                // عرض الجداول التي لا تحتوي على user_id
                                if (!empty($tables_without_user_id)) {
                                    echo "<p class='mb-2 mt-2'><strong>جداول بدون user_id:</strong></p>";
                                    foreach ($tables_without_user_id as $table) {
                                        echo "<span class='table-info no-user-id'>$table</span>";
                                    }
                                    
                                    echo "<div class='mt-3'>";
                                    echo "<button class='btn btn-warning btn-sm' onclick='updateUserTables(\"$username\")' id='update-btn-$username'>";
                                    echo "<i class='fas fa-sync'></i> تحديث الجداول";
                                    echo "</button>";
                                    echo "</div>";
                                } else {
                                    echo "<p class='success mt-2'><i class='fas fa-check'></i> جميع الجداول تحتوي على user_id</p>";
                                }
                                
                                echo "</div>";
                                echo "</div>";
                            }
                            
                            echo "</div>";
                        } else {
                            echo "<p class='warning'><i class='fas fa-exclamation-triangle'></i> لا توجد مستخدمون مسجلون</p>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال بقواعد البيانات</p>";
                    }
                    ?>
                </div>

                <!-- اختبار الدوال الجديدة -->
                <div class="test-section">
                    <h3><i class="fas fa-code"></i> اختبار دوال user_id</h3>
                    
                    <?php
                    echo "<h5>اختبار دالة insertWithUserId:</h5>";
                    
                    if (isset($_SESSION['username'])) {
                        $current_user = $_SESSION['username'];
                        echo "<p>المستخدم الحالي: <code>$current_user</code></p>";
                        
                        // اختبار إدراج عميل جديد
                        $test_customer_data = [
                            'name' => 'عميل اختبار user_id',
                            'phone' => '0500000999',
                            'email' => '<EMAIL>',
                            'customer_type' => 'customer'
                        ];
                        
                        $insert_result = insertWithUserId('customers', $test_customer_data);
                        if ($insert_result) {
                            echo "<p class='success'><i class='fas fa-check'></i> تم إدراج عميل جديد بـ user_id (ID: $insert_result)</p>";
                        } else {
                            echo "<p class='error'><i class='fas fa-times'></i> فشل في إدراج العميل</p>";
                        }
                        
                        // اختبار إدراج منتج جديد
                        $test_product_data = [
                            'name' => 'منتج اختبار user_id',
                            'description' => 'وصف منتج الاختبار',
                            'price' => 150.00,
                            'category' => 'اختبار'
                        ];
                        
                        $product_insert_result = insertWithUserId('products', $test_product_data);
                        if ($product_insert_result) {
                            echo "<p class='success'><i class='fas fa-check'></i> تم إدراج منتج جديد بـ user_id (ID: $product_insert_result)</p>";
                        } else {
                            echo "<p class='error'><i class='fas fa-times'></i> فشل في إدراج المنتج</p>";
                        }
                        
                    } else {
                        echo "<p class='warning'><i class='fas fa-exclamation-triangle'></i> لا يوجد مستخدم مسجل دخول حالياً</p>";
                    }
                    ?>
                </div>

                <!-- إحصائيات user_id -->
                <div class="test-section">
                    <h3><i class="fas fa-chart-bar"></i> إحصائيات user_id</h3>
                    
                    <?php
                    if ($operations_db) {
                        echo "<div class='row'>";
                        
                        // إحصائيات عامة
                        $tables_result = $operations_db->query("SHOW TABLES");
                        $total_tables = 0;
                        $tables_with_user_id = 0;
                        
                        if ($tables_result) {
                            while ($table_row = $tables_result->fetch_array()) {
                                $table_name = $table_row[0];
                                $total_tables++;
                                
                                // فحص وجود عمود user_id
                                $columns_result = $operations_db->query("SHOW COLUMNS FROM `$table_name` LIKE 'user_id'");
                                if ($columns_result && $columns_result->num_rows > 0) {
                                    $tables_with_user_id++;
                                }
                            }
                        }
                        
                        echo "<div class='col-md-4'>";
                        echo "<div class='card text-center'>";
                        echo "<div class='card-body'>";
                        echo "<h5 class='card-title'>$total_tables</h5>";
                        echo "<p class='card-text'>إجمالي الجداول</p>";
                        echo "</div>";
                        echo "</div>";
                        echo "</div>";
                        
                        echo "<div class='col-md-4'>";
                        echo "<div class='card text-center'>";
                        echo "<div class='card-body'>";
                        echo "<h5 class='card-title text-success'>$tables_with_user_id</h5>";
                        echo "<p class='card-text'>جداول مع user_id</p>";
                        echo "</div>";
                        echo "</div>";
                        echo "</div>";
                        
                        $tables_without_user_id = $total_tables - $tables_with_user_id;
                        echo "<div class='col-md-4'>";
                        echo "<div class='card text-center'>";
                        echo "<div class='card-body'>";
                        echo "<h5 class='card-title text-danger'>$tables_without_user_id</h5>";
                        echo "<p class='card-text'>جداول بدون user_id</p>";
                        echo "</div>";
                        echo "</div>";
                        echo "</div>";
                        
                        echo "</div>";
                    }
                    ?>
                </div>

                <!-- أزرار التحكم -->
                <div class="text-center mt-4">
                    <button onclick="location.reload()" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-refresh"></i> إعادة فحص
                    </button>
                    <a href="test_user_tables.php" class="btn btn-info btn-lg me-3">
                        <i class="fas fa-link"></i> ربط الجداول
                    </a>
                    <a href="test_system.php" class="btn btn-success btn-lg me-3">
                        <i class="fas fa-chart-line"></i> تقرير النظام
                    </a>
                    <a href="index_safe.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                </div>

                <!-- معلومات إضافية -->
                <div class="mt-4 text-center text-muted">
                    <hr>
                    <p class="mb-0">
                        <small>
                            اختبار ربط user_id - salessystem_v2 | 
                            تاريخ الفحص: <?php echo date('Y-m-d H:i:s'); ?>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateUserTables(username) {
            const btn = document.getElementById('update-btn-' + username);
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
            btn.disabled = true;
            
            // إرسال طلب AJAX لتحديث الجداول
            fetch('ajax/update_user_tables.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({username: username})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    btn.innerHTML = '<i class="fas fa-check"></i> تم التحديث';
                    btn.className = 'btn btn-success btn-sm';
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    btn.innerHTML = '<i class="fas fa-times"></i> فشل التحديث';
                    btn.className = 'btn btn-danger btn-sm';
                    btn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                btn.innerHTML = '<i class="fas fa-times"></i> خطأ';
                btn.className = 'btn btn-danger btn-sm';
                btn.disabled = false;
            });
        }
    </script>
</body>
</html>
