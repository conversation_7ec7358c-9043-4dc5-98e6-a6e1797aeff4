<?php
/**
 * ملف لاختبار الترجمات والتحقق من المصطلحات المفقودة
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// قائمة جميع المصطلحات المستخدمة في النظام
$used_keys = [
    'actions', 'add_customer', 'add_new_product', 'add_purchase', 'add_sale', 'add_tax',
    'address', 'all_customers', 'all_rights_reserved', 'amount', 'amount_before_tax',
    'amount_including_tax', 'app_author', 'app_description', 'app_name', 'calculate',
    'calculation_result', 'calculation_type', 'cancel', 'check_tables', 'close',
    'confirm_delete_customer', 'confirm_delete_purchase', 'confirm_delete_sale',
    'connection_error', 'contact_us', 'currency', 'customer', 'customer_address',
    'customer_name', 'customer_phone', 'customers', 'dashboard_welcome_message',
    'database_error', 'date', 'date_from', 'date_to', 'delete', 'due_to_tax_authority',
    'edit', 'end_date', 'error', 'extract_tax', 'filter', 'footer_description',
    'home', 'invoice_count', 'invoice_number', 'language', 'login', 'logout',
    'manage_customers', 'manage_purchases', 'manage_sales', 'monthly', 'monthly_profit',
    'net_tax', 'no_customer', 'no_data', 'no_data_available', 'no_report_content',
    'no_report_data', 'period', 'price', 'print', 'product_name', 'profile',
    'profit_loss', 'profit_loss_report', 'purchases', 'purchases_details',
    'purchases_list', 'purchases_report', 'purchases_tax', 'quantity_sold',
    'quick_actions', 'quick_links', 'recent_purchases', 'recent_sales', 'register',
    'report_type', 'reports', 'reset', 'sales', 'sales_details', 'sales_list',
    'sales_report', 'sales_tax', 'save', 'search', 'search_filter', 'search_placeholder',
    'settings', 'show_all', 'start_date', 'statistics', 'subtotal', 'summary_report',
    'tax', 'tax_amount', 'tax_calculator', 'tax_calculator_title', 'tax_due',
    'tax_number', 'tax_rate', 'tax_summary', 'today_purchases', 'today_sales',
    'top_customers', 'top_products', 'total', 'total_amount', 'total_purchases',
    'total_sales', 'total_tax', 'total_tax_collected', 'total_tax_paid', 'view', 'welcome'
];

// تحميل ملفات اللغة
$ar_translations = require __DIR__ . '/languages/ar/lang.php';
$en_translations = require __DIR__ . '/languages/en/lang.php';

// التحقق من المصطلحات المفقودة
$missing_ar = [];
$missing_en = [];

foreach ($used_keys as $key) {
    if (!isset($ar_translations[$key])) {
        $missing_ar[] = $key;
    }
    if (!isset($en_translations[$key])) {
        $missing_en[] = $key;
    }
}

// إحصائيات
$total_used = count($used_keys);
$total_ar = count($ar_translations);
$total_en = count($en_translations);
$missing_ar_count = count($missing_ar);
$missing_en_count = count($missing_en);

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار الترجمات</h2>
    
    <!-- إحصائيات عامة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-primary"><?php echo $total_used; ?></h4>
                    <p>المصطلحات المستخدمة</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-success"><?php echo $total_ar; ?></h4>
                    <p>الترجمات العربية</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-info"><?php echo $total_en; ?></h4>
                    <p>الترجمات الإنجليزية</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h4 class="text-<?php echo ($missing_ar_count + $missing_en_count) == 0 ? 'success' : 'danger'; ?>">
                        <?php echo $missing_ar_count + $missing_en_count; ?>
                    </h4>
                    <p>المصطلحات المفقودة</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- نتائج الاختبار -->
    <div class="row">
        <!-- المصطلحات المفقودة في العربية -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header <?php echo $missing_ar_count == 0 ? 'bg-success text-white' : 'bg-danger text-white'; ?>">
                    <h5 class="mb-0">
                        <i class="fas <?php echo $missing_ar_count == 0 ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        المصطلحات المفقودة في العربية (<?php echo $missing_ar_count; ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($missing_ar_count == 0): ?>
                        <p class="text-success">✅ جميع المصطلحات متوفرة في اللغة العربية</p>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <strong>تحذير:</strong> هناك <?php echo $missing_ar_count; ?> مصطلح مفقود في ملف اللغة العربية
                        </div>
                        <ul class="list-group">
                            <?php foreach ($missing_ar as $key): ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <code><?php echo $key; ?></code>
                                <span class="badge bg-danger rounded-pill">مفقود</span>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- المصطلحات المفقودة في الإنجليزية -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header <?php echo $missing_en_count == 0 ? 'bg-success text-white' : 'bg-danger text-white'; ?>">
                    <h5 class="mb-0">
                        <i class="fas <?php echo $missing_en_count == 0 ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        المصطلحات المفقودة في الإنجليزية (<?php echo $missing_en_count; ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($missing_en_count == 0): ?>
                        <p class="text-success">✅ جميع المصطلحات متوفرة في اللغة الإنجليزية</p>
                    <?php else: ?>
                        <div class="alert alert-warning">
                            <strong>Warning:</strong> There are <?php echo $missing_en_count; ?> missing terms in the English language file
                        </div>
                        <ul class="list-group">
                            <?php foreach ($missing_en as $key): ?>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <code><?php echo $key; ?></code>
                                <span class="badge bg-danger rounded-pill">Missing</span>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- اختبار عينة من الترجمات -->
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-language"></i>
                اختبار عينة من الترجمات
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>المفتاح</th>
                            <th>العربية</th>
                            <th>الإنجليزية</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $sample_keys = array_slice($used_keys, 0, 20); // أول 20 مصطلح
                        foreach ($sample_keys as $key):
                            $ar_value = $ar_translations[$key] ?? 'غير موجود';
                            $en_value = $en_translations[$key] ?? 'Missing';
                            $status = (isset($ar_translations[$key]) && isset($en_translations[$key])) ? 'success' : 'danger';
                        ?>
                        <tr>
                            <td><code><?php echo $key; ?></code></td>
                            <td><?php echo htmlspecialchars($ar_value); ?></td>
                            <td><?php echo htmlspecialchars($en_value); ?></td>
                            <td>
                                <span class="badge bg-<?php echo $status; ?>">
                                    <?php echo $status == 'success' ? '✓' : '✗'; ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- اختبار تغيير اللغة -->
    <div class="card mt-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">
                <i class="fas fa-globe"></i>
                اختبار تغيير اللغة
            </h5>
        </div>
        <div class="card-body">
            <p>اللغة الحالية: <strong><?php echo getCurrentLang() == 'ar' ? 'العربية' : 'English'; ?></strong></p>
            <div class="btn-group" role="group">
                <a href="?lang=ar" class="btn btn-outline-primary <?php echo getCurrentLang() == 'ar' ? 'active' : ''; ?>">
                    العربية
                </a>
                <a href="?lang=en" class="btn btn-outline-primary <?php echo getCurrentLang() == 'en' ? 'active' : ''; ?>">
                    English
                </a>
            </div>
            
            <div class="mt-3">
                <h6>اختبار الترجمة:</h6>
                <ul>
                    <li><strong><?php echo __('welcome'); ?>:</strong> <?php echo __('dashboard_welcome_message'); ?></li>
                    <li><strong><?php echo __('app_name'); ?>:</strong> <?php echo __('app_description'); ?></li>
                    <li><strong><?php echo __('currency'); ?>:</strong> <?php echo __('currency'); ?></li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- الإجراءات -->
    <div class="card mt-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">الإجراءات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>إجراءات التصحيح:</h6>
                    <?php if ($missing_ar_count > 0 || $missing_en_count > 0): ?>
                        <div class="alert alert-info">
                            <p>لإصلاح المصطلحات المفقودة، يمكنك:</p>
                            <ol>
                                <li>إضافة المصطلحات المفقودة يدوياً إلى ملفات اللغة</li>
                                <li>استخدام أداة الإصلاح التلقائي (قريباً)</li>
                                <li>نسخ المصطلحات من لغة إلى أخرى</li>
                            </ol>
                        </div>
                    <?php else: ?>
                        <div class="alert alert-success">
                            <p>✅ جميع المصطلحات متوفرة في كلا اللغتين!</p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="col-md-6">
                    <h6>روابط مفيدة:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="test_translations.php" class="btn btn-info">
                            <i class="fas fa-redo"></i> إعادة اختبار الترجمات
                        </a>
                        <a href="test_system.php" class="btn btn-warning">
                            <i class="fas fa-vial"></i> اختبار النظام
                        </a>
                        <a href="fix_translations.php" class="btn btn-success">
                            <i class="fas fa-tools"></i> إصلاح الترجمات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
