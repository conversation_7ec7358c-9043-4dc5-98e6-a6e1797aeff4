# PHPMailer Documentation

Generated documentation for PHPMailer is [available online](https://phpmailer.github.io/PHPMailer/), and is regenerated automatically whenever changes are made.

Pre-built PHPMailer API documentation is not provided in this repo, but you can generate it by running `phpdoc` in the top-level folder of this project, and documentation will be generated in this `docs` folder. You will need to have [phpDocumentor](https://www.phpdoc.org) installed. The configuration for phpdoc is in the [phpdoc.dist.xml file](https://github.com/PHPMailer/PHPMailer/blob/master/phpdoc.dist.xml).

Further help and information is available in [the PHPMailer README](https://github.com/PHPMailer/PHPMailer/blob/master/README.md), [the examples folder](https://github.com/PHPMailer/PHPMailer/tree/master/examples), and in [the GitHub wiki](https://github.com/PHPMailer/PHPMailer/wiki).

Fixes and additions to documentation are welcome - please submit pull requests or improve wiki pages.