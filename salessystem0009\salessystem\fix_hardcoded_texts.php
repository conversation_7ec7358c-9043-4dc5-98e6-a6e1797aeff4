<?php
/**
 * أداة إصلاح النصوص المكتوبة مباشرة في الكود
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// قائمة الملفات والنصوص التي تحتاج إصلاح
$files_to_fix = [
    'add_sale.php' => [
        'يجب إضافة عنصر واحد على الأقل' => '__("at_least_one_item_required")',
        'تم إضافة فاتورة المبيعات بنجاح' => '__("sale_invoice_added_successfully")',
        'حدث خطأ أثناء إضافة فاتورة المبيعات' => '__("error_adding_sale_invoice")',
        'إضافة فاتورة مبيعات' => '__("add_sales_invoice")',
        'العميل' => '__("customer")',
        '-- اختر عميل --' => '__("select_customer")',
        '-- إضافة عميل جديد --' => '__("add_new_customer_option")',
        'إضافة عنصر' => '__("add_item")',
        'حفظ الفاتورة' => '__("save_invoice")',
        'إلغاء' => '__("cancel")',
        '-- اختر منتج --' => '__("select_product")',
        '-- إضافة منتج جديد --' => '__("add_new_product_option")',
        'تم إضافة المنتج بنجاح' => '__("product_added_successfully")',
        'حدث خطأ أثناء إضافة المنتج' => '__("error_adding_product")',
        'يرجى إدخال اسم العميل' => '__("please_enter_customer_name")',
        'اسم العميل يجب أن يكون أكثر من حرف واحد' => '__("customer_name_min_length")',
        'تم إضافة العميل بنجاح' => '__("customer_added_successfully")',
        'حدث خطأ أثناء إضافة العميل' => '__("error_adding_customer")',
        'حدث خطأ في الشبكة أثناء إضافة العميل' => '__("network_error_adding_customer")'
    ],
    'add_purchase.php' => [
        'يجب إضافة عنصر واحد على الأقل' => '__("at_least_one_item_required")',
        'تم إضافة فاتورة المشتريات بنجاح' => '__("purchase_invoice_added_successfully")',
        'حدث خطأ أثناء إضافة فاتورة المشتريات' => '__("error_adding_purchase_invoice")',
        'إضافة فاتورة مشتريات' => '__("add_purchase_invoice")',
        'المورد' => '__("supplier")',
        '-- اختر مورد --' => '__("select_supplier")',
        '-- إضافة مورد جديد --' => '__("add_new_supplier_option")'
    ],
    'add_customer.php' => [
        'إضافة عميل جديد' => '__("add_new_customer")',
        'تعديل عميل' => '__("edit_customer")',
        'تم حفظ العميل بنجاح' => '__("customer_saved_successfully")',
        'تم تحديث العميل بنجاح' => '__("customer_updated_successfully")',
        'حدث خطأ أثناء حفظ العميل' => '__("error_saving_customer")'
    ],
    'customers.php' => [
        'إدارة العملاء' => '__("manage_customers")',
        'قائمة العملاء' => '__("customers_list")',
        'لا توجد عملاء' => '__("no_customers_found")',
        'هل أنت متأكد من حذف هذا العميل؟' => '__("confirm_delete_customer")'
    ],
    'sales.php' => [
        'إدارة المبيعات' => '__("manage_sales")',
        'قائمة المبيعات' => '__("sales_list")',
        'لا توجد مبيعات' => '__("no_sales_found")',
        'هل أنت متأكد من حذف هذه الفاتورة؟' => '__("confirm_delete_sale")'
    ],
    'purchases.php' => [
        'إدارة المشتريات' => '__("manage_purchases")',
        'قائمة المشتريات' => '__("purchases_list")',
        'لا توجد مشتريات' => '__("no_purchases_found")',
        'هل أنت متأكد من حذف هذه الفاتورة؟' => '__("confirm_delete_purchase")'
    ]
];

// معالجة طلب الإصلاح
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_hardcoded'])) {
    $fixed_files = 0;
    $fixed_texts = 0;
    $errors = [];
    
    try {
        foreach ($files_to_fix as $filename => $replacements) {
            $filepath = __DIR__ . '/' . $filename;
            
            if (!file_exists($filepath)) {
                $errors[] = "الملف غير موجود: $filename";
                continue;
            }
            
            $content = file_get_contents($filepath);
            $original_content = $content;
            $file_changes = 0;
            
            foreach ($replacements as $search => $replace) {
                // البحث عن النص المكتوب مباشرة وتبديله بدالة الترجمة
                $patterns = [
                    // النصوص في HTML
                    '/>' . preg_quote($search, '/') . '</',
                    // النصوص في PHP strings
                    '/["\']' . preg_quote($search, '/') . '["\']/',
                    // النصوص في JavaScript
                    '/alert\(["\']' . preg_quote($search, '/') . '[\'"]\)/',
                    // النصوص في عناوين الصفحات
                    '/<title[^>]*>' . preg_quote($search, '/') . '<\/title>/',
                    // النصوص في placeholder
                    '/placeholder=["\']' . preg_quote($search, '/') . '["\']/',
                    // النصوص في value
                    '/value=["\']' . preg_quote($search, '/') . '["\']/'
                ];
                
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $content)) {
                        $content = preg_replace($pattern, str_replace($search, "<?php echo $replace; ?>", $0), $content);
                        $file_changes++;
                        $fixed_texts++;
                    }
                }
                
                // إصلاح النصوص في PHP
                if (strpos($content, '"' . $search . '"') !== false) {
                    $content = str_replace('"' . $search . '"', $replace, $content);
                    $file_changes++;
                    $fixed_texts++;
                }
                
                if (strpos($content, "'" . $search . "'") !== false) {
                    $content = str_replace("'" . $search . "'", $replace, $content);
                    $file_changes++;
                    $fixed_texts++;
                }
            }
            
            if ($file_changes > 0) {
                // إنشاء نسخة احتياطية
                $backup_file = $filepath . '.backup.' . date('Y-m-d-H-i-s');
                file_put_contents($backup_file, $original_content);
                
                // حفظ الملف المحدث
                if (file_put_contents($filepath, $content)) {
                    $fixed_files++;
                } else {
                    $errors[] = "فشل في حفظ الملف: $filename";
                }
            }
        }
        
        if ($fixed_files > 0) {
            $_SESSION['success'] = "تم إصلاح $fixed_texts نص في $fixed_files ملف بنجاح";
        } else {
            $_SESSION['info'] = "لم يتم العثور على نصوص تحتاج إصلاح";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء الإصلاح: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// فحص الملفات للنصوص المكتوبة مباشرة
$analysis = [];
$total_hardcoded = 0;

foreach ($files_to_fix as $filename => $replacements) {
    $filepath = __DIR__ . '/' . $filename;
    $analysis[$filename] = [
        'exists' => file_exists($filepath),
        'hardcoded_count' => 0,
        'hardcoded_texts' => []
    ];
    
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        
        foreach ($replacements as $search => $replace) {
            if (strpos($content, $search) !== false) {
                $analysis[$filename]['hardcoded_count']++;
                $analysis[$filename]['hardcoded_texts'][] = $search;
                $total_hardcoded++;
            }
        }
    }
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-code"></i>
                        إصلاح النصوص المكتوبة مباشرة في الكود
                    </h4>
                </div>
                <div class="card-body">
                    <!-- إحصائيات عامة -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($files_to_fix); ?></h3>
                                    <p class="mb-0">ملفات للفحص</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-dark">
                                <div class="card-body text-center">
                                    <h3><?php echo $total_hardcoded; ?></h3>
                                    <p class="mb-0">نصوص مكتوبة مباشرة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo array_sum(array_column($analysis, 'exists')); ?></h3>
                                    <p class="mb-0">ملفات موجودة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count(array_filter($analysis, function($a) { return $a['hardcoded_count'] == 0; })); ?></h3>
                                    <p class="mb-0">ملفات نظيفة</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تحليل الملفات -->
                    <h5 class="mb-3">
                        <i class="fas fa-file-code text-primary"></i>
                        تحليل الملفات
                    </h5>
                    
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>الملف</th>
                                    <th>الحالة</th>
                                    <th>عدد النصوص المكتوبة مباشرة</th>
                                    <th>التفاصيل</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($analysis as $filename => $data): ?>
                                <tr>
                                    <td><code><?php echo htmlspecialchars($filename); ?></code></td>
                                    <td>
                                        <?php if ($data['exists']): ?>
                                            <span class="badge bg-success">موجود</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">مفقود</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($data['hardcoded_count'] > 0): ?>
                                            <span class="badge bg-warning"><?php echo $data['hardcoded_count']; ?></span>
                                        <?php else: ?>
                                            <span class="badge bg-success">0</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($data['hardcoded_texts'])): ?>
                                            <button class="btn btn-sm btn-outline-info" type="button" data-bs-toggle="collapse" data-bs-target="#details-<?php echo md5($filename); ?>">
                                                عرض التفاصيل
                                            </button>
                                            <div class="collapse mt-2" id="details-<?php echo md5($filename); ?>">
                                                <div class="card card-body">
                                                    <small>
                                                        <?php foreach ($data['hardcoded_texts'] as $text): ?>
                                                            <div class="text-danger">• <?php echo htmlspecialchars($text); ?></div>
                                                        <?php endforeach; ?>
                                                    </small>
                                                </div>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">لا توجد نصوص مكتوبة مباشرة</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- إجراءات الإصلاح -->
                    <div class="mt-4">
                        <h5 class="mb-3">
                            <i class="fas fa-tools text-warning"></i>
                            إجراءات الإصلاح
                        </h5>
                        
                        <?php if ($total_hardcoded > 0): ?>
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-exclamation-triangle"></i> تحذير</h6>
                            <p>تم العثور على <?php echo $total_hardcoded; ?> نص مكتوب مباشرة في الكود. يُنصح بإصلاحها لتحسين دعم تعدد اللغات.</p>
                        </div>
                        
                        <form method="POST" onsubmit="return confirm('هل أنت متأكد من إصلاح جميع النصوص المكتوبة مباشرة؟ سيتم إنشاء نسخة احتياطية من الملفات.')">
                            <div class="alert alert-info">
                                <strong>ملاحظة:</strong> سيتم إنشاء نسخة احتياطية من كل ملف قبل التعديل.
                            </div>
                            
                            <button type="submit" name="fix_hardcoded" class="btn btn-danger">
                                <i class="fas fa-magic"></i> إصلاح جميع النصوص المكتوبة مباشرة
                            </button>
                        </form>
                        <?php else: ?>
                        <div class="alert alert-success">
                            <h6><i class="fas fa-check-circle"></i> ممتاز!</h6>
                            <p>جميع الملفات نظيفة. لا توجد نصوص مكتوبة مباشرة تحتاج إصلاح.</p>
                        </div>
                        <?php endif; ?>
                        
                        <div class="mt-3">
                            <a href="review_translations.php" class="btn btn-primary">
                                <i class="fas fa-language"></i> مراجعة الترجمات
                            </a>
                            <a href="system_tools.php" class="btn btn-secondary">
                                <i class="fas fa-tools"></i> أدوات النظام
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
