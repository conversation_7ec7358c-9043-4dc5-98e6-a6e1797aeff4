<?php
/**
 * معالج أخطاء محسن لقاعدة البيانات
 * يوفر معالجة شاملة ومتقدمة لجميع أنواع أخطاء قاعدة البيانات
 */

class DatabaseErrorHandler {
    private static $instance = null;
    private $error_log = [];
    private $auto_repair = true;
    
    private function __construct() {
        // تسجيل معالج الأخطاء
        set_error_handler([$this, 'handleError']);
        set_exception_handler([$this, 'handleException']);
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * معالجة أخطاء قاعدة البيانات
     */
    public function handleDatabaseError($db, $query = '', $context = '') {
        $error_info = [
            'timestamp' => date('Y-m-d H:i:s'),
            'error_code' => $db->errno,
            'error_message' => $db->error,
            'query' => $query,
            'context' => $context,
            'user_id' => $_SESSION['user_id'] ?? 'غير محدد',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'غير محدد'
        ];
        
        $this->error_log[] = $error_info;
        
        // تسجيل الخطأ
        error_log("خطأ قاعدة البيانات: " . json_encode($error_info, JSON_UNESCAPED_UNICODE));
        
        // محاولة الإصلاح التلقائي
        if ($this->auto_repair) {
            $this->attemptAutoRepair($error_info);
        }
        
        // إرجاع رسالة خطأ مناسبة للمستخدم
        return $this->getUserFriendlyMessage($error_info);
    }
    
    /**
     * محاولة الإصلاح التلقائي
     */
    private function attemptAutoRepair($error_info) {
        switch ($error_info['error_code']) {
            case 1146: // Table doesn't exist
                $this->repairMissingTable($error_info);
                break;
                
            case 1054: // Unknown column
                $this->repairMissingColumn($error_info);
                break;
                
            case 2006: // MySQL server has gone away
            case 2013: // Lost connection
                $this->repairConnection();
                break;
                
            case 1062: // Duplicate entry
                $this->handleDuplicateEntry($error_info);
                break;
        }
    }
    
    /**
     * إصلاح الجداول المفقودة
     */
    private function repairMissingTable($error_info) {
        // استخراج اسم الجدول من رسالة الخطأ
        if (preg_match("/Table '.*\.(.*)' doesn't exist/", $error_info['error_message'], $matches)) {
            $table_name = $matches[1];
            
            // محاولة إنشاء الجدول
            $this->createMissingTable($table_name);
        }
    }
    
    /**
     * إنشاء الجداول المفقودة
     */
    private function createMissingTable($table_name) {
        $db = getCurrentUserDB();
        if (!$db) return false;
        
        $table_schemas = [
            'customers' => "CREATE TABLE `customers` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `phone` varchar(20) DEFAULT NULL,
                `email` varchar(255) DEFAULT NULL,
                `tax_number` varchar(50) DEFAULT NULL,
                `address` text DEFAULT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            'products' => "CREATE TABLE `products` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) NOT NULL,
                `price` decimal(10,2) NOT NULL DEFAULT 0.00,
                `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            'sales' => "CREATE TABLE `sales` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `invoice_number` varchar(50) NOT NULL,
                `date` date NOT NULL,
                `customer_id` int(11) DEFAULT NULL,
                `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
                `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `notes` text DEFAULT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `invoice_number` (`invoice_number`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
            
            'purchases' => "CREATE TABLE `purchases` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `invoice_number` varchar(50) NOT NULL,
                `date` date NOT NULL,
                `customer_id` int(11) DEFAULT NULL,
                `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
                `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `notes` text DEFAULT NULL,
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `invoice_number` (`invoice_number`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
        ];
        
        if (isset($table_schemas[$table_name])) {
            if ($db->query($table_schemas[$table_name])) {
                error_log("تم إنشاء الجدول المفقود: $table_name");
                $_SESSION['success'] = "تم إصلاح الجدول المفقود: $table_name";
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * إصلاح الأعمدة المفقودة
     */
    private function repairMissingColumn($error_info) {
        // يمكن تطوير هذه الدالة لإضافة الأعمدة المفقودة تلقائياً
        error_log("عمود مفقود تم اكتشافه: " . $error_info['error_message']);
    }
    
    /**
     * إصلاح مشاكل الاتصال
     */
    private function repairConnection() {
        // إعادة تعيين الاتصال
        global $connection;
        $connection = null;
        
        // محاولة إعادة الاتصال
        $new_connection = getCurrentUserDB();
        if ($new_connection && !$new_connection->connect_error) {
            error_log("تم إصلاح الاتصال بقاعدة البيانات");
            return true;
        }
        
        return false;
    }
    
    /**
     * معالجة البيانات المكررة
     */
    private function handleDuplicateEntry($error_info) {
        error_log("بيانات مكررة: " . $error_info['error_message']);
        // يمكن إضافة منطق لمعالجة البيانات المكررة
    }
    
    /**
     * إرجاع رسالة مناسبة للمستخدم
     */
    private function getUserFriendlyMessage($error_info) {
        switch ($error_info['error_code']) {
            case 1146:
                return 'جدول مفقود في قاعدة البيانات. تم محاولة الإصلاح التلقائي.';
                
            case 1054:
                return 'عمود مفقود في قاعدة البيانات. يرجى استخدام أداة الإصلاح.';
                
            case 2006:
            case 2013:
                return 'انقطع الاتصال بقاعدة البيانات. تم إعادة الاتصال.';
                
            case 1062:
                return 'البيانات موجودة مسبقاً. يرجى التحقق من البيانات المدخلة.';
                
            case 1064:
                return 'خطأ في صيغة الاستعلام. يرجى التواصل مع المطور.';
                
            default:
                return 'حدث خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى.';
        }
    }
    
    /**
     * معالجة الأخطاء العامة
     */
    public function handleError($severity, $message, $file, $line) {
        if (strpos($message, 'mysql') !== false || strpos($message, 'database') !== false) {
            $this->error_log[] = [
                'type' => 'PHP Error',
                'severity' => $severity,
                'message' => $message,
                'file' => $file,
                'line' => $line,
                'timestamp' => date('Y-m-d H:i:s')
            ];
        }
        
        return false; // السماح لمعالج الأخطاء الافتراضي بالعمل
    }
    
    /**
     * معالجة الاستثناءات
     */
    public function handleException($exception) {
        $this->error_log[] = [
            'type' => 'Exception',
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        error_log("استثناء قاعدة البيانات: " . $exception->getMessage());
    }
    
    /**
     * الحصول على سجل الأخطاء
     */
    public function getErrorLog() {
        return $this->error_log;
    }
    
    /**
     * مسح سجل الأخطاء
     */
    public function clearErrorLog() {
        $this->error_log = [];
    }
    
    /**
     * تفعيل/إلغاء الإصلاح التلقائي
     */
    public function setAutoRepair($enabled) {
        $this->auto_repair = $enabled;
    }
}

// تهيئة معالج الأخطاء
$error_handler = DatabaseErrorHandler::getInstance();

/**
 * دالة مساعدة لمعالجة أخطاء قاعدة البيانات
 */
function handleDBError($db, $query = '', $context = '') {
    global $error_handler;
    return $error_handler->handleDatabaseError($db, $query, $context);
}

/**
 * دالة محسنة لتنفيذ الاستعلامات مع معالجة الأخطاء
 */
function safeQuery($db, $query, $params = []) {
    try {
        if (empty($params)) {
            $result = $db->query($query);
        } else {
            $stmt = $db->prepare($query);
            if (!$stmt) {
                throw new Exception("فشل في تحضير الاستعلام: " . $db->error);
            }
            
            $stmt->bind_param(...$params);
            $stmt->execute();
            $result = $stmt->get_result();
            $stmt->close();
        }
        
        if (!$result) {
            $error_message = handleDBError($db, $query, 'safeQuery');
            throw new Exception($error_message);
        }
        
        return $result;
    } catch (Exception $e) {
        error_log("خطأ في safeQuery: " . $e->getMessage());
        throw $e;
    }
}
?>
