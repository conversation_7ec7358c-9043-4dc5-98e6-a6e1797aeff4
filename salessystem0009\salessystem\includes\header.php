<?php
$lang_dir = getLanguageDir();
$lang_code = getCurrentLang();
?>
<!DOCTYPE html>
<html lang="<?php echo $lang_code; ?>" dir="<?php echo $lang_dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="<?php echo __('app_description'); ?>">
    <meta name="author" content="<?php echo __('app_author'); ?>">
    <title><?php echo __('app_name'); ?></title>

    <!-- Favicon -->
    <link rel="icon" href="assets/img/favicon.ico" type="image/x-icon">

    <?php if ($lang_dir === 'rtl'): ?>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <?php else: ?>
    <!-- Bootstrap LTR -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <?php endif; ?>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">

    <style>
        /* Font settings based on language */
        html[dir="rtl"] body {
            font-family: 'Tajawal', sans-serif;
        }

        html[dir="ltr"] body {
            font-family: 'Roboto', sans-serif;
        }

        /* Additional styles for language support */
        .language-switcher {
            margin-left: 15px;
        }
        .language-switcher .dropdown-menu {
            min-width: 100px;
        }
        .language-switcher .dropdown-item {
            padding: 0.25rem 1rem;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Main Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="index.php">
                <i class="fas fa-file-invoice-dollar me-2"></i>
                <?php echo __('app_name'); ?>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fas fa-home me-1"></i> <?php echo __('home'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="customers.php">
                                <i class="fas fa-users me-1"></i> <?php echo __('customers'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="products.php">
                                <i class="fas fa-boxes me-1"></i> المنتجات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="sales.php">
                                <i class="fas fa-file-invoice me-1"></i> <?php echo __('sales'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="purchases.php">
                                <i class="fas fa-shopping-cart me-1"></i> <?php echo __('purchases'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="reports.php">
                                <i class="fas fa-chart-bar me-1"></i> <?php echo __('reports'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="tax_calculator.php">
                                <i class="fas fa-calculator me-1"></i> <?php echo __('tax_calculator'); ?>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown language-switcher">
                        <a class="nav-link dropdown-toggle" href="#" id="languageDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-globe me-1"></i> <?php echo __('language'); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="languageDropdown">
                            <li><a class="dropdown-item <?php echo $lang_code === 'ar' ? 'active' : ''; ?>" href="<?php echo getLanguageSwitchUrl('ar'); ?>">
                                <i class="fas fa-flag me-2"></i> العربية
                            </a></li>
                            <li><a class="dropdown-item <?php echo $lang_code === 'en' ? 'active' : ''; ?>" href="<?php echo getLanguageSwitchUrl('en'); ?>">
                                <i class="fas fa-flag me-2"></i> English
                            </a></li>
                        </ul>
                    </li>

                    <?php if (isLoggedIn()): ?>
                        <!-- User Menu -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user-circle me-1"></i> <?php echo $_SESSION['username']; ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="userDropdown">
                                <li><a class="dropdown-item" href="profile.php">
                                    <i class="fas fa-user me-2"></i> <?php echo __('profile'); ?>
                                </a></li>
                                <li><a class="dropdown-item" href="settings.php">
                                    <i class="fas fa-cog me-2"></i> <?php echo __('settings'); ?>
                                </a></li>
                                <li><a class="dropdown-item" href="system_tools.php">
                                    <i class="fas fa-tools me-2"></i> <?php echo __('system_tools'); ?>
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="logout.php">
                                    <i class="fas fa-sign-out-alt me-2"></i> <?php echo __('logout'); ?>
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="login.php">
                                <i class="fas fa-sign-in-alt me-1"></i> <?php echo __('login'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="register.php">
                                <i class="fas fa-user-plus me-1"></i> <?php echo __('register'); ?>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Page Content Container -->
    <div class="container py-4">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb bg-white p-3 rounded shadow-sm">
                <li class="breadcrumb-item">
                    <a href="index.php"><i class="fas fa-home"></i> <?php echo __('home'); ?></a>
                </li>
                <?php
                // Get current page name without extension
                $current_page = basename($_SERVER['PHP_SELF'], '.php');
                if ($current_page != 'index'):
                    // Convert page name to title case and replace underscores with spaces
                    $page_title = ucwords(str_replace('_', ' ', $current_page));
                ?>
                <li class="breadcrumb-item active" aria-current="page">
                    <?php echo __($current_page); ?>
                </li>
                <?php endif; ?>
            </ol>
        </nav>

        <!-- Alert Messages -->
        <?php displayMessages(); ?>