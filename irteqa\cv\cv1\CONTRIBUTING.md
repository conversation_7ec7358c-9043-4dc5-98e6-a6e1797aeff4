# Contributing #

When contributing to this repository, please first discuss the change you wish to make via issue,
email, or any other method with the [owners][Owners] of this repository before making a change.

Please note we have a [code of conduct][code_of_conduct], please follow it in all your interactions with the project.

## Pull Request Process ##

1. Ensure any install or build dependencies are removed before the end of the layer when doing a
   build.
2. Update the [README.md][Readme] with details of changes to the interface, this includes new environment
   variables, exposed ports, useful file locations and container parameters.
3. Increase the version numbers in any examples files and the [README] to the new version that this
   Pull Request would represent. The versioning scheme we use is [SemVer].
4. You may merge the Pull Request in once you have the sign-off of two other developers, or if you
   do not have permission to do that, you may request the second reviewer to merge it for you.

[code_of_conduct]: CODE_OF_CONDUCT.md
[Owners]: https://github.com/tbaltrushaitis
[Readme]: README.md
[SemVer]: http://semver.org/
