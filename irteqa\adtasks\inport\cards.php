<style>
  .favretlist li a {
    display: flex;
    justify-content: center;
    text-decoration: none;
  }

  .favretlist li {
    margin: 5px;
     width: 200px;
    /* تغيير العرض كما تريد */
    height: 300px;
    /* تغيير الارتفاع كما تريد */
    overflow-y: auto;
    /* جعل الشريط العمودي يظهر عند الحاجة */
    border-radius: 10px;
    direction: ltr;
    background: #e9e9e9;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
    border: 2px solid rgb(194, 194, 194);
  }
  ul {
    padding: 0;
  }

  .opnedt {
    display: none;
    margin-top: 50px;
  }

  .cardli:hover .opnedt {
    position: absolute;
    width: 200px;
    height: 200px;
    background: #000000c0;
    z-index: 999;
    display: grid;
    align-content: center;
    justify-content: center;
  }

  .chcls {
    display: flex;
    justify-content: space-between;
  }

  .ctitl {
    text-align: right;
  }

  .cmark {
    background: rgb(73, 73, 73);
    color: white;
    padding: 3px;
    height: 20px;
    border-radius: 10px;
    font-weight: bold;
    margin: 1px;
  }

  .ctyp {
    background: rgb(150, 54, 54);
    color: white;
    padding: 3px;
    height: 20px;
    border-radius: 10px;
    font-weight: bold;
    margin: 1px;
  }

  .ctag {
    font-weight: bold;
    font-size: 1.3em;
  }

  .heart-checkbox {
    display: none;
  }

  .heart-label {
    position: relative;
    cursor: pointer;
    padding-left: 30px;
  }

  .heart-label::before {
    content: '\2661';
    /* رمز لقلب فارغ */
    position: absolute;
    left: 0;
    top: 0;
    font-size: 24px;
    color: #000;
  }

  .heart-label::after {
    content: '\2764';
    /* رمز لقلب ممتلئ */
    position: absolute;
    left: 2px;
    top: 8px;
    font-size: 14px;
    color: #ff0000;
    opacity: 0;
  }

  .heart-checkbox:checked+.heart-label::after {
    opacity: 1;
  }

  .ftrcard {
    width: 170px;
    background: white;
    padding: 5px;
    border-radius: 10px;
    margin: auto;
    margin-bottom: 10px;
  }

  .ftrcard div {
    display: flex;
    justify-content: space-between;
    font-weight: bold;
    font-size: 1em;
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .btnfxd {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
    margin: 3px;
    font-weight: bold;
    font-size: 1.5em;
  }

  .btnfxd:hover {
    background-color: #ffffff;
    color: rgb(71, 71, 71);
  }
</style>
<ul class="favretlist">
  
  <li class="cardli"><div class="opnedt">
    <button class="btnfxd">فتح</button>
    <button class="btnfxd">تعديل</button>
  </div><a>
      <div class="hdcard">
        <div class="chcls">
          <div><label class="cmark">مهم</label><label class="ctyp">مكتب العمل</label></div><label class="ctag"><input type="checkbox" id="heart" class="heart-checkbox">
            <label for="heart" class="heart-label"></label></label>
        </div>
        <h3 class="ctitl" href="#">تخفيض المقابل المالي إنجاز مضمون</h3>
      </div>
    </a><div class="ftrcard">
        <div class="cfcls"><label class="tvlu">التقييم</label><label class="cmark">1</label></div>
        <div class="chcls"><label class="cotlvlu">المنصات</label><label class="tcotl">مهم</label></div>
      </div></li>
</ul>