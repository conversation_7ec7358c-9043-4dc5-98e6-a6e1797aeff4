<?php
/**
 * اختبار شامل لجميع إصلاحات قاعدة البيانات
 * يتحقق من عمل جميع الأدوات والإصلاحات بشكل صحيح
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
redirectIfNotLoggedIn();

$test_results = [];
$total_tests = 0;
$passed_tests = 0;
$failed_tests = 0;

function runTest($test_name, $test_function) {
    global $test_results, $total_tests, $passed_tests, $failed_tests;
    
    $total_tests++;
    $start_time = microtime(true);
    
    try {
        $result = $test_function();
        $status = $result ? 'PASS' : 'FAIL';
        
        if ($result) {
            $passed_tests++;
        } else {
            $failed_tests++;
        }
    } catch (Exception $e) {
        $status = 'ERROR';
        $result = false;
        $failed_tests++;
    }
    
    $end_time = microtime(true);
    $execution_time = round(($end_time - $start_time) * 1000, 2);
    
    $test_results[] = [
        'name' => $test_name,
        'status' => $status,
        'result' => $result,
        'time' => $execution_time,
        'error' => isset($e) ? $e->getMessage() : null
    ];
    
    return $result;
}

// اختبار 1: فحص الاتصال بقاعدة البيانات الرئيسية
runTest('اتصال قاعدة البيانات الرئيسية', function() {
    global $main_db;
    return $main_db && !$main_db->connect_error;
});

// اختبار 2: فحص دالة getCurrentUserDB
runTest('دالة getCurrentUserDB', function() {
    $db = getCurrentUserDB();
    return $db && !$db->connect_error;
});

// اختبار 3: فحص دالة validateDatabaseConnection
runTest('دالة validateDatabaseConnection', function() {
    $validation = validateDatabaseConnection();
    return isset($validation['status']);
});

// اختبار 4: فحص وجود الجداول الأساسية
runTest('وجود الجداول الأساسية', function() {
    $db = getCurrentUserDB();
    if (!$db) return false;
    
    $required_tables = ['customers', 'products', 'sales', 'purchases'];
    foreach ($required_tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows == 0) {
            return false;
        }
    }
    return true;
});

// اختبار 5: فحص بنية جدول العملاء
runTest('بنية جدول العملاء', function() {
    $db = getCurrentUserDB();
    if (!$db) return false;
    
    $required_columns = ['id', 'name', 'phone', 'email'];
    $result = $db->query("DESCRIBE customers");
    
    if (!$result) return false;
    
    $existing_columns = [];
    while ($row = $result->fetch_assoc()) {
        $existing_columns[] = $row['Field'];
    }
    
    foreach ($required_columns as $column) {
        if (!in_array($column, $existing_columns)) {
            return false;
        }
    }
    
    return true;
});

// اختبار 6: فحص إدراج بيانات في جدول العملاء
runTest('إدراج بيانات العملاء', function() {
    $db = getCurrentUserDB();
    if (!$db) return false;
    
    $test_name = 'عميل اختبار ' . time();
    $stmt = $db->prepare("INSERT INTO customers (name, phone, email) VALUES (?, ?, ?)");
    $phone = '0501234567';
    $email = '<EMAIL>';
    $stmt->bind_param("sss", $test_name, $phone, $email);
    
    $result = $stmt->execute();
    $insert_id = $db->insert_id;
    $stmt->close();
    
    if ($result && $insert_id > 0) {
        // حذف البيانات التجريبية
        $db->query("DELETE FROM customers WHERE id = $insert_id");
        return true;
    }
    
    return false;
});

// اختبار 7: فحص دالة resetDBConnection
runTest('دالة resetDBConnection', function() {
    $db = getCurrentUserDB();
    if (!$db) return false;
    
    $reset_db = resetDBConnection($db);
    return $reset_db && !$reset_db->connect_error;
});

// اختبار 8: فحص معالج الأخطاء المحسن
runTest('معالج الأخطاء المحسن', function() {
    global $error_handler;
    return $error_handler instanceof DatabaseErrorHandler;
});

// اختبار 9: فحص دالة safeQuery
runTest('دالة safeQuery', function() {
    $db = getCurrentUserDB();
    if (!$db) return false;
    
    try {
        $result = safeQuery($db, "SELECT COUNT(*) as count FROM customers");
        return $result && $result->num_rows > 0;
    } catch (Exception $e) {
        return false;
    }
});

// اختبار 10: فحص أداء الاستعلامات
runTest('أداء الاستعلامات', function() {
    $db = getCurrentUserDB();
    if (!$db) return false;
    
    $start_time = microtime(true);
    $result = $db->query("SELECT COUNT(*) FROM customers");
    $end_time = microtime(true);
    
    $query_time = ($end_time - $start_time) * 1000; // بالميلي ثانية
    
    return $result && $query_time < 1000; // أقل من ثانية واحدة
});

// اختبار 11: فحص سلامة البيانات
runTest('سلامة البيانات', function() {
    $db = getCurrentUserDB();
    if (!$db) return false;
    
    // فحص البيانات اليتيمة
    $orphaned_sales = $db->query("
        SELECT COUNT(*) as count 
        FROM sales s 
        LEFT JOIN customers c ON s.customer_id = c.id 
        WHERE s.customer_id IS NOT NULL AND c.id IS NULL
    ");
    
    if (!$orphaned_sales) return false;
    
    $orphaned_count = $orphaned_sales->fetch_assoc()['count'];
    return $orphaned_count == 0; // لا توجد بيانات يتيمة
});

// اختبار 12: فحص الفهارس
runTest('فحص الفهارس', function() {
    $db = getCurrentUserDB();
    if (!$db) return false;
    
    $indexes = $db->query("SHOW INDEX FROM customers");
    return $indexes && $indexes->num_rows > 0;
});

require_once __DIR__ . '/includes/header.php';
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="card system-tools-card">
        <div class="card-header <?php echo $failed_tests > 0 ? 'bg-warning' : 'bg-success'; ?> text-white">
            <h4 class="mb-0">
                <i class="fas fa-vial"></i>
                اختبار شامل لإصلاحات قاعدة البيانات
            </h4>
        </div>
        <div class="card-body">
            <!-- ملخص النتائج -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body text-center">
                            <h2><?php echo $total_tests; ?></h2>
                            <p class="mb-0">إجمالي الاختبارات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h2><?php echo $passed_tests; ?></h2>
                            <p class="mb-0">نجح</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h2><?php echo $failed_tests; ?></h2>
                            <p class="mb-0">فشل</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h2><?php echo round(($passed_tests / $total_tests) * 100); ?>%</h2>
                            <p class="mb-0">معدل النجاح</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نتائج الاختبارات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">نتائج الاختبارات التفصيلية</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>اسم الاختبار</th>
                                    <th>النتيجة</th>
                                    <th>الوقت (ms)</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($test_results as $index => $test): ?>
                                <tr>
                                    <td><?php echo $index + 1; ?></td>
                                    <td><?php echo htmlspecialchars($test['name']); ?></td>
                                    <td>
                                        <span class="badge bg-<?php 
                                            echo $test['status'] === 'PASS' ? 'success' : 
                                                ($test['status'] === 'FAIL' ? 'danger' : 'warning'); 
                                        ?>">
                                            <?php 
                                            echo $test['status'] === 'PASS' ? 'نجح' : 
                                                ($test['status'] === 'FAIL' ? 'فشل' : 'خطأ'); 
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo $test['time']; ?></td>
                                    <td>
                                        <?php if ($test['error']): ?>
                                        <small class="text-danger"><?php echo htmlspecialchars($test['error']); ?></small>
                                        <?php else: ?>
                                        <small class="text-muted">-</small>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- التوصيات -->
            <div class="mt-4">
                <?php if ($failed_tests == 0): ?>
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle"></i> ممتاز!</h6>
                    <p>جميع الاختبارات نجحت. نظام قاعدة البيانات يعمل بشكل مثالي.</p>
                </div>
                <?php elseif ($failed_tests <= 2): ?>
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> تحذير</h6>
                    <p>فشل <?php echo $failed_tests; ?> اختبار. يُنصح بتشغيل أداة الإصلاح الشاملة.</p>
                </div>
                <?php else: ?>
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle"></i> مشاكل خطيرة</h6>
                    <p>فشل <?php echo $failed_tests; ?> اختبار. يجب تشغيل جميع أدوات الإصلاح فوراً.</p>
                </div>
                <?php endif; ?>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    العودة للصفحة الرئيسية
                </a>
                
                <?php if ($failed_tests > 0): ?>
                <a href="quick_database_fix.php" class="btn btn-warning ms-2">
                    <i class="fas fa-bolt"></i>
                    إصلاح سريع
                </a>
                <a href="database_repair_tool.php" class="btn btn-danger ms-2">
                    <i class="fas fa-tools"></i>
                    إصلاح شامل
                </a>
                <?php endif; ?>
                
                <a href="test_database_fixes.php" class="btn btn-secondary ms-2">
                    <i class="fas fa-redo"></i>
                    إعادة الاختبار
                </a>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
