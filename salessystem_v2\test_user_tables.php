<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ربط جداول المستخدمين - salessystem_v2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .test-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 50px auto;
            max-width: 1000px;
            overflow: hidden;
        }
        .test-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .test-content {
            padding: 40px;
        }
        .test-section {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .table-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
        }
        .status-exists {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-missing {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <?php
    require_once __DIR__.'/config/init.php';
    ?>
    
    <div class="container">
        <div class="test-container">
            <div class="test-header">
                <h1><i class="fas fa-link"></i> اختبار ربط جداول المستخدمين</h1>
                <p class="mb-0">فحص وإصلاح ربط الجداول مع المستخدمين</p>
            </div>
            
            <div class="test-content">
                <!-- فحص قاعدة البيانات الرئيسية -->
                <div class="test-section">
                    <h3><i class="fas fa-database"></i> فحص قاعدة البيانات الرئيسية</h3>
                    
                    <?php
                    global $main_db;
                    if ($main_db && !$main_db->connect_error) {
                        echo "<p class='success'><i class='fas fa-check'></i> قاعدة البيانات الرئيسية متصلة</p>";
                        
                        // فحص جدول المستخدمين
                        $users_result = $main_db->query("SELECT COUNT(*) as count FROM users");
                        if ($users_result) {
                            $users_count = $users_result->fetch_assoc()['count'];
                            echo "<p class='info'><i class='fas fa-users'></i> عدد المستخدمين المسجلين: $users_count</p>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال بقاعدة البيانات الرئيسية</p>";
                    }
                    ?>
                </div>

                <!-- فحص قاعدة بيانات العمليات -->
                <div class="test-section">
                    <h3><i class="fas fa-cogs"></i> فحص قاعدة بيانات العمليات</h3>
                    
                    <?php
                    $operations_db = getOperationsDB();
                    if ($operations_db && !$operations_db->connect_error) {
                        echo "<p class='success'><i class='fas fa-check'></i> قاعدة بيانات العمليات متصلة</p>";
                        
                        // فحص الجداول الموجودة
                        $tables_result = $operations_db->query("SHOW TABLES");
                        if ($tables_result) {
                            $tables_count = $tables_result->num_rows;
                            echo "<p class='info'><i class='fas fa-table'></i> عدد الجداول الموجودة: $tables_count</p>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال بقاعدة بيانات العمليات</p>";
                    }
                    ?>
                </div>

                <!-- فحص جداول المستخدمين -->
                <div class="test-section">
                    <h3><i class="fas fa-users-cog"></i> فحص جداول المستخدمين</h3>
                    
                    <?php
                    if ($main_db && $operations_db) {
                        $users_result = $main_db->query("SELECT id, username FROM users ORDER BY id");
                        
                        if ($users_result && $users_result->num_rows > 0) {
                            echo "<div class='row'>";
                            
                            while ($user = $users_result->fetch_assoc()) {
                                $user_id = $user['id'];
                                $username = $user['username'];
                                
                                echo "<div class='col-md-6 mb-3'>";
                                echo "<div class='card'>";
                                echo "<div class='card-body'>";
                                echo "<h6 class='card-title'>$username (ID: $user_id)</h6>";
                                
                                // فحص جداول المستخدم
                                $required_tables = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
                                $existing_tables = [];
                                $missing_tables = [];
                                
                                foreach ($required_tables as $table) {
                                    if (userTableExists($table, $username)) {
                                        $existing_tables[] = $table;
                                    } else {
                                        $missing_tables[] = $table;
                                    }
                                }
                                
                                // عرض الجداول الموجودة
                                if (!empty($existing_tables)) {
                                    echo "<p class='mb-2'><strong>الجداول الموجودة:</strong></p>";
                                    foreach ($existing_tables as $table) {
                                        echo "<span class='table-status status-exists'>$table</span>";
                                    }
                                }
                                
                                // عرض الجداول المفقودة
                                if (!empty($missing_tables)) {
                                    echo "<p class='mb-2 mt-2'><strong>الجداول المفقودة:</strong></p>";
                                    foreach ($missing_tables as $table) {
                                        echo "<span class='table-status status-missing'>$table</span>";
                                    }
                                    
                                    // زر إنشاء الجداول
                                    echo "<div class='mt-3'>";
                                    echo "<button class='btn btn-primary btn-sm' onclick='createUserTables(\"$username\")' id='btn-$username'>";
                                    echo "<i class='fas fa-plus'></i> إنشاء الجداول المفقودة";
                                    echo "</button>";
                                    echo "</div>";
                                } else {
                                    echo "<p class='success mt-2'><i class='fas fa-check'></i> جميع الجداول موجودة</p>";
                                }
                                
                                echo "</div>";
                                echo "</div>";
                                echo "</div>";
                            }
                            
                            echo "</div>";
                        } else {
                            echo "<p class='warning'><i class='fas fa-exclamation-triangle'></i> لا توجد مستخدمون مسجلون</p>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال بقواعد البيانات</p>";
                    }
                    ?>
                </div>

                <!-- اختبار الدوال الجديدة -->
                <div class="test-section">
                    <h3><i class="fas fa-code"></i> اختبار دوال الربط</h3>
                    
                    <?php
                    echo "<h5>اختبار دالة ensureUserTablesLinked:</h5>";
                    
                    // اختبار مع مستخدم تجريبي
                    $test_username = 'test_link_user';
                    echo "<p>اختبار مع المستخدم: <code>$test_username</code></p>";
                    
                    $result = ensureUserTablesLinked($test_username);
                    if ($result) {
                        echo "<p class='success'><i class='fas fa-check'></i> تم ربط الجداول بنجاح</p>";
                        
                        // فحص الجداول المنشأة
                        $tables = getUserTables($test_username);
                        if (!empty($tables)) {
                            echo "<p class='info'><i class='fas fa-list'></i> الجداول المربوطة:</p>";
                            echo "<ul>";
                            foreach ($tables as $table) {
                                echo "<li><code>$table</code></li>";
                            }
                            echo "</ul>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل في ربط الجداول</p>";
                    }
                    
                    // اختبار دالة checkCurrentUserTablesLink
                    echo "<h5 class='mt-4'>اختبار دالة checkCurrentUserTablesLink:</h5>";
                    if (isset($_SESSION['username'])) {
                        $current_user = $_SESSION['username'];
                        echo "<p>المستخدم الحالي: <code>$current_user</code></p>";
                        
                        $check_result = checkCurrentUserTablesLink();
                        if ($check_result) {
                            echo "<p class='success'><i class='fas fa-check'></i> جداول المستخدم الحالي مربوطة بنجاح</p>";
                        } else {
                            echo "<p class='error'><i class='fas fa-times'></i> مشكلة في ربط جداول المستخدم الحالي</p>";
                        }
                    } else {
                        echo "<p class='warning'><i class='fas fa-exclamation-triangle'></i> لا يوجد مستخدم مسجل دخول حالياً</p>";
                    }
                    ?>
                </div>

                <!-- أزرار التحكم -->
                <div class="text-center mt-4">
                    <button onclick="location.reload()" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-refresh"></i> إعادة فحص
                    </button>
                    <a href="test_system.php" class="btn btn-info btn-lg me-3">
                        <i class="fas fa-chart-line"></i> تقرير النظام
                    </a>
                    <a href="index_safe.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                </div>

                <!-- معلومات إضافية -->
                <div class="mt-4 text-center text-muted">
                    <hr>
                    <p class="mb-0">
                        <small>
                            اختبار ربط جداول المستخدمين - salessystem_v2 | 
                            تاريخ الفحص: <?php echo date('Y-m-d H:i:s'); ?>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function createUserTables(username) {
            const btn = document.getElementById('btn-' + username);
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإنشاء...';
            btn.disabled = true;
            
            // إرسال طلب AJAX لإنشاء الجداول
            fetch('ajax/create_user_tables.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({username: username})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    btn.innerHTML = '<i class="fas fa-check"></i> تم الإنشاء';
                    btn.className = 'btn btn-success btn-sm';
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    btn.innerHTML = '<i class="fas fa-times"></i> فشل الإنشاء';
                    btn.className = 'btn btn-danger btn-sm';
                    btn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                btn.innerHTML = '<i class="fas fa-times"></i> خطأ';
                btn.className = 'btn btn-danger btn-sm';
                btn.disabled = false;
            });
        }
    </script>
</body>
</html>
