<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>تلوين خلفية الـDiv حسب الحرف الأول من نص الـSpan</title>
  <style>
    /* تنسيق الـDiv */
    .colored-div {
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      padding: 20px;
      color: white; /* لضمان رؤية النص بوضوح */
    }
  </style>
</head>
<body>

<!-- الـDiv -->
<div id="coloredDiv" class="colored-div"><span id="coloredSpan">back</span></div>

<script>
  // تحديد الـSpan
  var spanElement = document.getElementById("coloredSpan");

  // الحصول على النص داخل الـSpan
  var text = spanElement.innerText.trim();

  // الحصول على الحرف الأول من النص
  var firstLetter = text.charAt(0).toUpperCase();

  // تحديد لون خلفية للحرف الأول
  var backgroundColor;
  switch (firstLetter) {
    case 'A':
    case 'J':
      backgroundColor = 'red';
      break;
    case 'B':
    case 'K':
      backgroundColor = '#285d28';
      break;
    case 'C':
    case 'L':
      backgroundColor = 'blue';
      break;
    // يمكنك إضافة المزيد حسب الحاجة
    default:
      backgroundColor = 'white';
  }
// احصل على نص العنصر span
var spanText = spanElement.textContent;

// احصل على عدد الحروف
var numberOfChars = spanText.length;

// عرض عدد الحروف في مكان مناسب، مثلاً في وحدة نص أخرى أو في وحدة تحكم مستعرض الويب
console.log('عدد الحروف في الـ span: ' + numberOfChars);
  // تلوين خلفية الـDiv
  var divElement = document.getElementById("coloredDiv");
  divElement.style.backgroundColor = backgroundColor;
</script>

</body>
</html>
