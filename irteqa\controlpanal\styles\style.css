body{
    background: aliceblue;
}
.snupf{
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    position:absolute;
    top:50%;
    left:50%;
    transform:translate(-50%,-50%);
    width: 400px;
    height: 480px;
    background:rgba(0,0,0,.5);
    border-radius:15px;
    box-sizing:border-box;
    margin: 5px;
    direction: rtl;
}
.snup{
    margin-top: 90px;
    margin-right: 20px;
}
.snup img[alt="user"]{
        position:absolute;
        top:-60px;
        left: 130px;
    }
    .snup input[type="text"],.snup input[type="password"]{
        font-size: 20px;
        outline:none;
        border:none;
        width: 40%;
        height:35px;
        border-bottom:1px solid #cbcbcb;
        background:none;
        padding:5px;
        color:#fff;
        text-align:right;
        margin-left: 25px;
        margin-bottom: 25px;

    }
   .snup input[type="text"]{
        top: auto;
left: auto;
    }

    ::placeholder{
        color: #d3d3d3b0;

    }
    .snup button[type="submit"]{
        position:absolute;
        top: 425px;
        left: 140px;
        width:130px;
        height:35px;
        background:#ffffff;
        border:none;
        outline: none;
        border-radius:15px;
        font-size:20px;

    }
    button[type="submit"]:hover{
        background:#fff;
        color:#0077ff;
    }
h4{
margin: auto;
text-align: -webkit-center;
color: chartreuse;
}
h4 label{
color: orange;
}
.sninl{
    border: 1px solid;
    padding: 5px;
    border-radius: 5px;
    float: left;
    margin: 10px;
}

body{
    background: aliceblue;
}
.lginf{
box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
position:absolute;
top:50%;
left:50%;
transform:translate(-50%,-50%);
width: 350px;
height: 420px;
background:rgba(0,0,0,.5);
border-radius:15px;
box-sizing:border-box;
margin: 5px;
}
.snin img[alt="user"]{
    position:absolute;
    top:-60px;
    left:100px;
}
.snin input[type="text"],.snin input[type="password"]{
    font-size: 20px;
    position:absolute;
    outline:none;
    border:none;
    width:250px;
    height:35px;
    border-bottom:2px solid #a1a1a1;
    background:none;
    padding:5px;
    color:#fff;
    text-align:right;

    
}
.snin input[type="text"]{
      top:140px;
      left:50px
 

}
.snin input[type="password"]{
    top:210px;
    left:50px;
}
::placeholder{
    color:#d3d3d3b0;

}
.snin button[type="submit"]{
    position:absolute;
    top:305px;
    left:110px;
    width:130px;
    height:35px;
    background:#ffffff;
    border:none;
    outline: none;
    border-radius:15px;
    font-size:20px;

}
button[type="submit"]:hover{
    background:#fff;
    color:#0077ff;
}
.reg{
    margin-left: 10px;
width: 40px;
height: 15px;
border: 1px solid;
    padding: 5px;
    border-radius: 5px;
font-weight: bold;
font-size: 1em;

position: absolute;
top: 385px;
left: 2px;
text-align: center;
}
a{
    text-decoration: solid;
}