{"version": 3, "file": "lang/summernote-fr-FR.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,MADF;AAEJC,QAAAA,MAAM,EAAE,UAFJ;AAGJC,QAAAA,SAAS,EAAE,UAHP;AAIJC,QAAAA,KAAK,EAAE,0BAJH;AAKJC,QAAAA,MAAM,EAAE,YALJ;AAMJC,QAAAA,IAAI,EAAE,mBANF;AAOJC,QAAAA,aAAa,EAAE,OAPX;AAQJC,QAAAA,WAAW,EAAE,UART;AASJC,QAAAA,SAAS,EAAE,QATP;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,MAAM,EAAE,mBAFH;AAGLC,QAAAA,UAAU,EAAE,kBAHP;AAILC,QAAAA,UAAU,EAAE,uBAJP;AAKLC,QAAAA,aAAa,EAAE,uBALV;AAMLC,QAAAA,SAAS,EAAE,iBANN;AAOLC,QAAAA,UAAU,EAAE,iBAPP;AAQLC,QAAAA,SAAS,EAAE,mBARN;AASLC,QAAAA,YAAY,EAAE,0BATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,iBAXX;AAYLC,QAAAA,SAAS,EAAE,eAZN;AAaLC,QAAAA,aAAa,EAAE,oDAbV;AAcLC,QAAAA,SAAS,EAAE,6BAdN;AAeLC,QAAAA,eAAe,EAAE,oBAfZ;AAgBLC,QAAAA,eAAe,EAAE,4BAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,qCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,iBAlBA;AAmBLC,QAAAA,MAAM,EAAE,oBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,mBAHH;AAILgB,QAAAA,GAAG,EAAE,iBAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,MADF;AAEJtB,QAAAA,MAAM,EAAE,iBAFJ;AAGJuB,QAAAA,MAAM,EAAE,mBAHJ;AAIJC,QAAAA,IAAI,EAAE,UAJF;AAKJC,QAAAA,aAAa,EAAE,kBALX;AAMJT,QAAAA,GAAG,EAAE,aAND;AAOJU,QAAAA,eAAe,EAAE,kCAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OA1CC;AAoDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,SADF;AAELC,QAAAA,WAAW,EAAE,6BAFR;AAGLC,QAAAA,WAAW,EAAE,8BAHR;AAILC,QAAAA,UAAU,EAAE,8BAJP;AAKLC,QAAAA,WAAW,EAAE,8BALR;AAMLC,QAAAA,MAAM,EAAE,oBANH;AAOLC,QAAAA,MAAM,EAAE,sBAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OApDA;AA8DPC,MAAAA,EAAE,EAAE;AACFpC,QAAAA,MAAM,EAAE;AADN,OA9DG;AAiEPqC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,QAFE;AAGLC,QAAAA,UAAU,EAAE,UAHP;AAILC,QAAAA,GAAG,EAAE,aAJA;AAKLC,QAAAA,EAAE,EAAE,SALC;AAMLC,QAAAA,EAAE,EAAE,SANC;AAOLC,QAAAA,EAAE,EAAE,SAPC;AAQLC,QAAAA,EAAE,EAAE,SARC;AASLC,QAAAA,EAAE,EAAE,SATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAjEA;AA6EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,eADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA7EA;AAiFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,MADC;AAEPC,QAAAA,UAAU,EAAE,aAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAjFF;AAsFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,YADF;AAETC,QAAAA,OAAO,EAAE,qBAFA;AAGTC,QAAAA,MAAM,EAAE,sBAHC;AAITC,QAAAA,IAAI,EAAE,kBAJG;AAKTC,QAAAA,MAAM,EAAE,SALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAtFJ;AA+FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,+BADH;AAELC,QAAAA,IAAI,EAAE,kBAFD;AAGLC,QAAAA,UAAU,EAAE,iBAHP;AAILC,QAAAA,UAAU,EAAE,mBAJP;AAKLC,QAAAA,WAAW,EAAE,aALR;AAMLC,QAAAA,cAAc,EAAE,yBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA/FA;AAyGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,YADH;AAERC,QAAAA,KAAK,EAAE,QAFC;AAGRC,QAAAA,cAAc,EAAE,wBAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,+BALb;AAMRC,QAAAA,aAAa,EAAE,mBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAzGH;AAkHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,oBADf;AAEJ,gBAAQ,8BAFJ;AAGJ,gBAAQ,8BAHJ;AAIJ,eAAO,YAJH;AAKJ,iBAAS,oBALL;AAMJ,gBAAQ,0BANJ;AAOJ,kBAAU,oBAPN;AAQJ,qBAAa,oBART;AASJ,yBAAiB,uBATb;AAUJ,wBAAgB,qBAVZ;AAWJ,uBAAe,kBAXX;AAYJ,yBAAiB,SAZb;AAaJ,wBAAgB,kBAbZ;AAcJ,uBAAe,gCAdX;AAeJ,+BAAuB,wBAfnB;AAgBJ,6BAAqB,yBAhBjB;AAiBJ,mBAAW,mCAjBP;AAkBJ,kBAAU,oCAlBN;AAmBJ,sBAAc,8CAnBV;AAoBJ,oBAAY,6CApBR;AAqBJ,oBAAY,6CArBR;AAsBJ,oBAAY,6CAtBR;AAuBJ,oBAAY,6CAvBR;AAwBJ,oBAAY,6CAxBR;AAyBJ,oBAAY,6CAzBR;AA0BJ,gCAAwB,gCA1BpB;AA2BJ,2BAAmB;AA3Bf,OAlHC;AA+IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,4BADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA/IF;AAmJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,qBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAnJN;AADiB,GAA5B;AA0JD,CA3JD,EA2JGC,MA3JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-fr-FR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'fr-FR': {\n      font: {\n        bold: 'Gras',\n        italic: 'Italique',\n        underline: 'Soulign<PERSON>',\n        clear: 'Effacer la mise en forme',\n        height: '<PERSON><PERSON><PERSON>',\n        name: '<PERSON>amille de police',\n        strikethrough: '<PERSON><PERSON>',\n        superscript: 'Exposant',\n        subscript: 'Indice',\n        size: 'Taille de police',\n      },\n      image: {\n        image: 'Image',\n        insert: 'Insérer une image',\n        resizeFull: 'Taille originale',\n        resizeHalf: 'Redimensionner à 50 %',\n        resizeQuarter: 'Redimensionner à 25 %',\n        floatLeft: 'Aligné à gauche',\n        floatRight: 'Aligné à droite',\n        floatNone: 'Pas d\\'alignement',\n        shapeRounded: 'Forme: Rectangle arrondi',\n        shapeCircle: 'Forme: Cercle',\n        shapeThumbnail: 'Forme: Vignette',\n        shapeNone: 'Forme: Aucune',\n        dragImageHere: 'Faites glisser une image ou un texte dans ce cadre',\n        dropImage: '<PERSON><PERSON><PERSON> l\\'image ou le texte',\n        selectFromFiles: 'Choisir un fichier',\n        maximumFileSize: 'Taille de fichier maximale',\n        maximumFileSizeError: 'Taille maximale du fichier dépassée',\n        url: 'URL de l\\'image',\n        remove: 'Supprimer l\\'image',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vidéo',\n        videoLink: 'Lien vidéo',\n        insert: 'Insérer une vidéo',\n        url: 'URL de la vidéo',\n        providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion or Youku)',\n      },\n      link: {\n        link: 'Lien',\n        insert: 'Insérer un lien',\n        unlink: 'Supprimer un lien',\n        edit: 'Modifier',\n        textToDisplay: 'Texte à afficher',\n        url: 'URL du lien',\n        openInNewWindow: 'Ouvrir dans une nouvelle fenêtre',\n        useProtocol: 'Utiliser le protocole par défaut',\n      },\n      table: {\n        table: 'Tableau',\n        addRowAbove: 'Ajouter une ligne au-dessus',\n        addRowBelow: 'Ajouter une ligne en dessous',\n        addColLeft: 'Ajouter une colonne à gauche',\n        addColRight: 'Ajouter une colonne à droite',\n        delRow: 'Supprimer la ligne',\n        delCol: 'Supprimer la colonne',\n        delTable: 'Supprimer le tableau',\n      },\n      hr: {\n        insert: 'Insérer une ligne horizontale',\n      },\n      style: {\n        style: 'Style',\n        p: 'Normal',\n        blockquote: 'Citation',\n        pre: 'Code source',\n        h1: 'Titre 1',\n        h2: 'Titre 2',\n        h3: 'Titre 3',\n        h4: 'Titre 4',\n        h5: 'Titre 5',\n        h6: 'Titre 6',\n      },\n      lists: {\n        unordered: 'Liste à puces',\n        ordered: 'Liste numérotée',\n      },\n      options: {\n        help: 'Aide',\n        fullscreen: 'Plein écran',\n        codeview: 'Afficher le code HTML',\n      },\n      paragraph: {\n        paragraph: 'Paragraphe',\n        outdent: 'Diminuer le retrait',\n        indent: 'Augmenter le retrait',\n        left: 'Aligner à gauche',\n        center: 'Centrer',\n        right: 'Aligner à droite',\n        justify: 'Justifier',\n      },\n      color: {\n        recent: 'Dernière couleur sélectionnée',\n        more: 'Plus de couleurs',\n        background: 'Couleur de fond',\n        foreground: 'Couleur de police',\n        transparent: 'Transparent',\n        setTransparent: 'Définir la transparence',\n        reset: 'Restaurer',\n        resetToDefault: 'Restaurer la couleur par défaut',\n      },\n      shortcut: {\n        shortcuts: 'Raccourcis',\n        close: 'Fermer',\n        textFormatting: 'Mise en forme du texte',\n        action: 'Action',\n        paragraphFormatting: 'Mise en forme des paragraphes',\n        documentStyle: 'Style du document',\n        extraKeys: 'Touches supplémentaires',\n      },\n      help: {\n        'insertParagraph': 'Insérer paragraphe',\n        'undo': 'Défaire la dernière commande',\n        'redo': 'Refaire la dernière commande',\n        'tab': 'Tabulation',\n        'untab': 'Tabulation arrière',\n        'bold': 'Mettre en caractère gras',\n        'italic': 'Mettre en italique',\n        'underline': 'Mettre en souligné',\n        'strikethrough': 'Mettre en texte barré',\n        'removeFormat': 'Nettoyer les styles',\n        'justifyLeft': 'Aligner à gauche',\n        'justifyCenter': 'Centrer',\n        'justifyRight': 'Aligner à droite',\n        'justifyFull': 'Justifier à gauche et à droite',\n        'insertUnorderedList': 'Basculer liste à puces',\n        'insertOrderedList': 'Basculer liste ordonnée',\n        'outdent': 'Diminuer le retrait du paragraphe',\n        'indent': 'Augmenter le retrait du paragraphe',\n        'formatPara': 'Changer le paragraphe en cours en normal (P)',\n        'formatH1': 'Changer le paragraphe en cours en entête H1',\n        'formatH2': 'Changer le paragraphe en cours en entête H2',\n        'formatH3': 'Changer le paragraphe en cours en entête H3',\n        'formatH4': 'Changer le paragraphe en cours en entête H4',\n        'formatH5': 'Changer le paragraphe en cours en entête H5',\n        'formatH6': 'Changer le paragraphe en cours en entête H6',\n        'insertHorizontalRule': 'Insérer séparation horizontale',\n        'linkDialog.show': 'Afficher fenêtre d\\'hyperlien',\n      },\n      history: {\n        undo: 'Annuler la dernière action',\n        redo: 'Restaurer la dernière action annulée',\n      },\n      specialChar: {\n        specialChar: 'Caractères spéciaux',\n        select: 'Choisir des caractères spéciaux',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "superscript", "subscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}