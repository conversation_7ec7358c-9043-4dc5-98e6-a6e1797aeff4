<?php
header('Content-Type: text/html; charset=utf-8');

// تحميل المكتبة بالطريقة الصحيحة
require __DIR__ . '/PhpSpreadsheet-master/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Shared\File;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_FILES['excel_file']) && $_FILES['excel_file']['error'] === UPLOAD_ERR_OK) {
        $file = $_FILES['excel_file']['tmp_name'];
        $file_name = $_FILES['excel_file']['name'];
        $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));
        
        try {
            // تحديد نوع القارئ
            $reader = IOFactory::createReaderForFile($file);
            $spreadsheet = $reader->load($file);
            
            // معالجة البيانات
            $worksheet = $spreadsheet->getActiveSheet();
            $data = $worksheet->toArray();
            
            $headers = array_shift($data);
            $result = array();
            
            foreach ($data as $row) {
                if (count($row) === count($headers)) {
                    $result[] = array_combine($headers, $row);
                }
            }
            
            // إخراج النتيجة
            header('Content-Type: application/json; charset=utf-8');
            echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            
        } catch (Exception $e) {
            echo "<div style='color:red; padding:20px; border:1px solid #f00;'>".
                 "حدث خطأ: " . $e->getMessage() . 
                 "<br><a href='index.html'>العودة</a></div>";
        }
    } else {
        echo "<div style='color:red; padding:20px; border:1px solid #f00;'>".
             "لم يتم تحميل الملف بشكل صحيح".
             "<br><a href='index.html'>العودة</a></div>";
    }
} else {
    header('Location: index.html');
    exit;
}
?>