{"compilerOptions": {"outDir": "build/dist", "module": "esnext", "target": "es6", "lib": ["dom", "es6", "es2016", "es2017"], "sourceMap": true, "allowJs": false, "jsx": "preserve", "moduleResolution": "node", "rootDirs": ["src", "stories"], "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "declaration": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "skipLibCheck": true, "esModuleInterop": true, "strict": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true}, "include": ["./src/**/*"]}