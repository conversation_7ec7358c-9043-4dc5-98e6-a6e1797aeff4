(this.webpackJsonpjsonldresume=this.webpackJsonpjsonldresume||[]).push([[0],Array(87).concat([function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u0625\u0636\u0627\u0641\u0629 {{- heading}}","startDate":{"label":"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0628\u062f\u0621"},"endDate":{"label":"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0627\u0646\u062a\u0647\u0627\u0621"},"description":{"label":"\u0627\u0644\u0648\u0635\u0641"}},"buttons":{"add":{"label":"\u0625\u0636\u0627\u0641\u0629"},"delete":{"label":"\u062d\u0630\u0641"}},"printDialog":{"heading":"\u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643","quality":{"label":"\u0627\u0644\u062c\u0648\u062f\u0629"},"printType":{"label":"\u0627\u0644\u0646\u0648\u0639","types":{"unconstrained":"\u063a\u064a\u0631 \u0645\u0642\u064a\u0651\u062f","fitInA4":"\u0645\u0644\u0627\u0626\u0645 \u0641\u064a A4","multiPageA4":"\u0635\u0641\u062d\u0627\u062a \u0645\u062a\u0639\u062f\u062f\u0629 A4"}},"helpText":["\u064a\u0633\u062a\u062e\u062f\u0645 \u0641\u0649 \u0635\u064a\u063a\u0629 \u0627\u0644\u062a\u0635\u062f\u064a\u0631 \u0647\u0646\u0627 HTML canvas \u0644\u062a\u062d\u0648\u064a\u0644 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0625\u0644\u0649 \u0635\u0648\u0631\u0629 \u062b\u0645 \u0637\u0628\u0627\u0639\u062a\u0647\u0627 \u0641\u0649 \u0645\u0644\u0641 PDF \u060c \u0645\u0645\u0627 \u064a\u0639\u0646\u0649 \u0623\u0646\u0643 \u0633\u062a\u0641\u0642\u062f \u0643\u0644 \u0642\u062f\u0631\u0627\u062a \u062a\u062d\u062f\u064a\u062f\\\\\u062a\u062d\u0644\u064a\u0644 \u0627\u0644\u0646\u0635 \u0627\u0644\u0645\u0643\u062a\u0648\u0628.","\u0625\u0630\u0627 \u0643\u0627\u0646 \u0647\u0630\u0627 \u0645\u0647\u0645\u0627\u064b \u0628\u0627\u0644\u0646\u0633\u0628\u0629 \u0644\u0643\u060c \u064a\u0631\u062c\u0649 \u0645\u062d\u0627\u0648\u0644\u0629 \u0637\u0628\u0627\u0639\u0629 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0628\u062f\u0644\u0627\u064b \u0645\u0646 \u0630\u0644\u0643 \u0628\u0627\u0633\u062a\u062e\u062f\u0627\u0645 Cmd/Ctrl + P \u0623\u0648 \u0632\u0631 \u0627\u0644\u0637\u0628\u0627\u0639\u0629 \u0623\u062f\u0646\u0627\u0647. \u0642\u062f \u062a\u062e\u062a\u0644\u0641 \u0627\u0644\u0646\u062a\u064a\u062c\u0629 \u0644\u0623\u0646 \u0637\u0628\u064a\u0639\u0629 \u0627\u0644\u0637\u0628\u0627\u0639\u0629 \u062a\u0639\u062a\u0645\u062f \u0639\u0644\u0649 \u0646\u0648\u0639 \u0627\u0644\u0645\u062a\u0635\u0641\u062d\u060c \u0648\u0644\u0643\u0646 \u0645\u0646 \u0627\u0644\u0645\u0639\u0631\u0648\u0641 \u0623\u0646\u0647 \u064a\u0639\u0645\u0644 \u0628\u0634\u0643\u0644 \u0623\u0641\u0636\u0644 \u0639\u0644\u0649 \u0623\u062d\u062f\u062b \u0625\u0635\u062f\u0627\u0631 \u0645\u0646 Google Chrome."],"buttons":{"cancel":"\u0625\u0644\u063a\u0627\u0621","saveAsPdf":"\u062d\u0641\u0638 \u0643\u0640 PDF"}},"panZoomAnimation":{"helpText":"\u064a\u0645\u0643\u0646\u0643 \u062a\u062d\u0631\u064a\u0643 \u0648\u062a\u0643\u0628\u064a\u0631 \u0644\u0648\u062d\u0629 \u0627\u0644\u0631\u0633\u0645 \u0641\u0649 \u0623\u0649 \u0648\u0642\u062a \u0644\u062a\u062d\u0635\u0644 \u0639\u0644\u0649 \u0646\u0638\u0631\u0629 \u0623\u0648\u0636\u062d \u0644\u0633\u064a\u0631\u062a\u0643 \u0627\u0644\u0630\u0627\u062a\u064a\u0629."},"markdownHelpText":"\u064a\u0645\u0643\u0646\u0643 \u0627\u0633\u062a\u062e\u062f\u0627\u0645 <1>GitHub Flavored Markdown</1> \u0644\u062a\u0635\u0645\u064a\u0645 \u0647\u0630\u0627 \u0627\u0644\u0642\u0633\u0645 \u0645\u0646 \u0627\u0644\u0646\u0635."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u0631\u0627\u0628\u0637 \u0627\u0644\u0635\u0648\u0631\u0629 \u0639\u0644\u0649 \u0627\u0644\u0625\u0646\u062a\u0631\u0646\u062a"},"firstName":{"label":"\u0627\u0644\u0625\u0633\u0645 \u0627\u0644\u0623\u0648\u0644"},"lastName":{"label":"\u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0623\u062e\u064a\u0631"},"subtitle":{"label":"\u0627\u0644\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0641\u0631\u0639\u064a"},"address":{"label":"\u0627\u0644\u0639\u0646\u0648\u0627\u0646","line1":{"label":"\u0627\u0644\u0633\u0637\u0631 1 \u0644\u0644\u0639\u0646\u0648\u0627\u0646"},"line2":{"label":"\u0627\u0644\u0633\u0637\u0631 2 \u0644\u0644\u0639\u0646\u0648\u0627\u0646"},"line3":{"label":"\u0627\u0644\u0633\u0637\u0631 3 \u0644\u0644\u0639\u0646\u0648\u0627\u0646"}},"phone":{"label":"\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641"},"website":{"label":"\u0627\u0644\u0645\u0648\u0642\u0639 \u0627\u0644\u0627\u0644\u0643\u062a\u0631\u0648\u0646\u064a"},"email":{"label":"\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0627\u0644\u0623\u0647\u062f\u0627\u0641"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0627\u0644\u0627\u0633\u0645"},"role":{"label":"\u0627\u0644\u0648\u0638\u064a\u0641\u0629"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0627\u0644\u0627\u0633\u0645"},"major":{"label":"\u0627\u0644\u062a\u062e\u0635\u0635"},"grade":{"label":"\u0627\u0644\u062f\u0631\u062c\u0629"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0627\u0644\u0639\u0646\u0648\u0627\u0646"},"subtitle":{"label":"\u0627\u0644\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0641\u0631\u0639\u064a"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0627\u0644\u0627\u0633\u0645"},"subtitle":{"label":"\u0627\u0644\u0647\u064a\u0626\u0629"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0627\u0644\u0627\u0633\u0645"},"level":{"label":"\u0645\u0633\u062a\u0648\u0649"},"rating":{"label":"\u0627\u0644\u062a\u0642\u064a\u064a\u0645"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0627\u0644\u0627\u0633\u0645"},"position":{"label":"\u0627\u0644\u0645\u0646\u0635\u0628"},"phone":{"label":"\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641"},"email":{"label":"\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0627\u0644\u0645\u0641\u062a\u0627\u062d - \u0627\u0644\u0646\u0648\u0639"},"value":{"label":"\u0627\u0644\u0642\u064a\u0645\u0629"}}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u0642\u0648\u0627\u0644\u0628"}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u0623\u0644\u0648\u0627\u0646","colorOptions":"\u062e\u064a\u0627\u0631\u0627\u062a \u0627\u0644\u0644\u0648\u0646","primaryColor":"\u0627\u0644\u0644\u0648\u0646 \u0627\u0644\u0623\u0633\u0627\u0633\u064a","accentColor":"\u0627\u0644\u0644\u0648\u0646 \u0627\u0644\u062b\u0627\u0646\u0648\u064a","clipboardCopyAction":"\u062a\u0645 \u0646\u0633\u062e {{color}}."}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u062e\u0637\u0648\u0637","fontFamily":{"label":"\u0646\u0648\u0639 \u0627\u0644\u062e\u0637","helpText":"\u064a\u0645\u0643\u0646\u0643 \u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0623\u064a \u062e\u0637 \u0645\u062b\u0628\u062a \u0639\u0644\u0649 \u062c\u0647\u0627\u0632\u0643 \u0623\u064a\u0636\u064b\u0627. \u0641\u0642\u0637 \u0642\u0645 \u0628\u0625\u062f\u062e\u0627\u0644 \u0627\u0633\u0645 \u0639\u0627\u0626\u0644\u0629 \u0627\u0644\u062e\u0637 \u0647\u0646\u0627 \u0648\u0633\u0648\u0641 \u064a\u0642\u0648\u0645 \u0627\u0644\u0645\u062a\u0635\u0641\u062d \u0628\u062a\u062d\u0645\u064a\u0644\u0647 \u0644\u0643."}}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u0627\u062c\u0631\u0627\u0621\u062a","disclaimer":"\u0627\u0644\u062a\u063a\u064a\u064a\u0631\u0627\u062a \u0627\u0644\u062a\u064a \u062a\u062c\u0631\u064a\u0647\u0627 \u0639\u0644\u0649 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643 \u064a\u062a\u0645 \u062d\u0641\u0638\u0647\u0627 \u062a\u0644\u0642\u0627\u0626\u064a\u064b\u0627 \u0625\u0644\u0649 \u0648\u062d\u062f\u0629 \u0627\u0644\u062a\u062e\u0632\u064a\u0646 \u0627\u0644\u0645\u062d\u0644\u064a\u0629 \u0644\u0644\u0645\u062a\u0635\u0641\u062d. \u0644\u0627 \u062a\u0648\u062c\u062f \u0628\u064a\u0627\u0646\u0627\u062a\u060c \u0648\u0628\u0627\u0644\u062a\u0627\u0644\u064a \u0641\u0625\u0646 \u0627\u0644\u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643 \u0622\u0645\u0646\u0629 \u062a\u0645\u0627\u0645\u064b\u0627.","importExport":{"heading":"\u0625\u0633\u062a\u064a\u0631\u0627\u062f/\u062a\u0635\u062f\u064a\u0631","body":"\u064a\u0645\u0643\u0646\u0643 \u0627\u0633\u062a\u064a\u0631\u0627\u062f \u0623\u0648 \u062a\u0635\u062f\u064a\u0631 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643 \u0628\u062a\u0646\u0633\u064a\u0642 JSON. \u0644\u0630\u0644\u0643 \u060c \u064a\u0645\u0643\u0646\u0643 \u062a\u0639\u062f\u064a\u0644 \u0623\u0648 \u0637\u0628\u0627\u0639\u0629 \u0633\u064a\u0631\u062a\u0643 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0645\u0646 \u0623\u064a \u062c\u0647\u0627\u0632. \u062d\u0641\u0638 \u0647\u0630\u0627 \u0627\u0644\u0645\u0644\u0641 \u0644\u0627\u0633\u062a\u062e\u062f\u0627\u0645\u0647 \u0644\u0627\u062d\u0642\u0627\u064b.","buttons":{"import":"\u0625\u0633\u062a\u064a\u0631\u0627\u062f","export":"\u062a\u0635\u062f\u064a\u0631"}},"downloadResume":{"heading":"\u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643","body":"\u064a\u0645\u0643\u0646\u0643 \u0627\u0644\u0646\u0642\u0631 \u0639\u0644\u0649 \u0627\u0644\u0632\u0631 \u0623\u062f\u0646\u0627\u0647 \u0644\u062a\u0646\u0632\u064a\u0644 \u0646\u0633\u062e\u0629 PDF \u0645\u0646 \u0633\u064a\u0631\u062a\u0643 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0639\u0644\u0649 \u0627\u0644\u0641\u0648\u0631. \u0644\u0644\u062d\u0635\u0648\u0644 \u0639\u0644\u0649 \u0623\u0641\u0636\u0644 \u0627\u0644\u0646\u062a\u0627\u0626\u062c\u060c \u064a\u0631\u062c\u0649 \u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0623\u062d\u062f\u062b \u0625\u0635\u062f\u0627\u0631 \u0645\u0646 Google Chrome.","buttons":{"saveAsPdf":"\u062d\u0641\u0638 \u0643\u0640 PDF"}},"loadDemoData":{"heading":"\u0625\u0636\u0627\u0641\u0629 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a \u0627\u0644\u062a\u062c\u0631\u064a\u0628\u064a\u0629","body":"\u063a\u064a\u0631 \u0648\u0627\u0636\u062d \u0645\u0627 \u064a\u062c\u0628 \u0641\u0639\u0644\u0647 \u0628\u0635\u0641\u062d\u0629 \u062c\u062f\u064a\u062f\u0629 \u0641\u0627\u0631\u063a\u0629\u061f \u0642\u0645 \u0628\u0625\u0636\u0627\u0641\u0629 \u0628\u0639\u0636 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a \u0627\u0644\u062a\u062c\u0631\u064a\u0628\u064a\u0629 \u0645\u0639 \u0642\u064a\u0645 \u0645\u0633\u0628\u0642\u0629 \u0644\u062a\u0631\u0649 \u0643\u064a\u0641 \u064a\u062c\u0628 \u0623\u0646 \u062a\u0628\u062f\u0648 \u0633\u064a\u0631\u062a\u0643 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0648\u064a\u0645\u0643\u0646\u0643 \u0627\u0644\u0628\u062f\u0621 \u0641\u064a \u0627\u0644\u062a\u0639\u062f\u064a\u0644 \u0645\u0646 \u0647\u0646\u0627\u0643.","buttons":{"loadData":"\u062a\u062d\u0645\u064a\u0644-\u0625\u0636\u0627\u0641\u0629 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a"}},"reset":{"heading":"\u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u0643\u0644 \u0634\u064a\u0621!","body":"\u0633\u064a\u0624\u062f\u064a \u0647\u0630\u0627 \u0627\u0644\u0625\u062c\u0631\u0627\u0621 \u0625\u0644\u0649 \u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u062c\u0645\u064a\u0639 \u0628\u064a\u0627\u0646\u0627\u062a\u0643 \u0648\u0625\u0632\u0627\u0644\u0629 \u0627\u0644\u0646\u0633\u062e \u0627\u0644\u0627\u062d\u062a\u064a\u0627\u0637\u064a\u0629 \u0627\u0644\u062a\u064a \u062a\u0645 \u0625\u0646\u0634\u0627\u0624\u0647\u0627 \u0639\u0644\u0649 \u0648\u062d\u062f\u0629 \u0627\u0644\u062a\u062e\u0632\u064a\u0646 \u0627\u0644\u0645\u062d\u0644\u064a\u0629 \u0644\u0645\u062a\u0635\u0641\u062d\u0643 \u0623\u064a\u0636\u064b\u0627. \u0644\u0630\u0627 \u064a\u0631\u062c\u0649 \u0627\u0644\u062a\u0623\u0643\u062f \u0645\u0646 \u0623\u0646\u0643 \u0642\u0645\u062a \u0628\u062a\u0635\u062f\u064a\u0631 \u0628\u064a\u0627\u0646\u0627\u062a\u0643 \u0623\u0648\u0644\u0627\u064b \u0642\u0628\u0644 \u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u0643\u0644 \u0634\u064a\u0621.","buttons":{"reset":"\u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646"}}}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u0625\u0639\u062f\u0627\u062f\u0627\u062a","language":{"label":"\u0627\u0644\u0644\u0651\u063a\u0629","helpText":"\u0625\u0630\u0627 \u0643\u0646\u062a \u062a\u0631\u063a\u0628 \u0641\u064a \u0627\u0644\u0645\u0633\u0627\u0639\u062f\u0629 \u0641\u064a \u062a\u0631\u062c\u0645\u0629 \u0627\u0644\u062a\u0637\u0628\u064a\u0642 \u0625\u0644\u0649 \u0644\u063a\u062a\u0643 \u0627\u0644\u062e\u0627\u0635\u0629\u060c \u064a\u0631\u062c\u0649 \u0627\u0644\u0631\u062c\u0648\u0639 \u0625\u0644\u0649 <1>\u0648\u062b\u064a\u0642\u0629 \u0627\u0644\u062a\u0631\u062c\u0645\u0629 </1>."}}')},function(e){e.exports=JSON.parse('{"title":"\u062d\u0648\u0644","documentation":{"heading":"\u062a\u0648\u0636\u064a\u062d\u0627\u062a","body":"\u0647\u0644 \u062a\u0631\u063a\u0628 \u0641\u064a \u0645\u0639\u0631\u0641\u0629 \u0627\u0644\u0645\u0632\u064a\u062f \u0639\u0646 \u0627\u0644\u062a\u0637\u0628\u064a\u0642\u061f \u062a\u062d\u062a\u0627\u062c \u0625\u0644\u0649 \u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0639\u0646 \u0643\u064a\u0641\u064a\u0629 \u0627\u0644\u0645\u0633\u0627\u0647\u0645\u0629 \u0641\u064a \u0627\u0644\u0645\u0634\u0631\u0648\u0639\u061f \u0644\u0627 \u0645\u0632\u064a\u062f \u0645\u0646 \u0627\u0644\u0628\u062d\u062b\u060c \u0647\u0646\u0627\u0643 \u062f\u0644\u064a\u0644 \u0634\u0627\u0645\u0644 \u062a\u0645 \u0625\u0639\u062f\u0627\u062f\u0647 \u0645\u0646 \u0623\u062c\u0644\u0643.","buttons":{"documentation":"\u0627\u0644\u062a\u0648\u0636\u064a\u062d\u0627\u062a"}},"bugOrFeatureRequest":{"heading":"\u062e\u0637\u0623\u061f \u0637\u0644\u0628 \u0645\u064a\u0632\u0629 \u062c\u062f\u064a\u062f\u0629\u061f","body":"\u0634\u064a\u0621 \u0645\u0627 \u064a\u0648\u0642\u0641 \u062a\u0642\u062f\u0645\u0643 \u0641\u0649 \u0628\u0646\u0627\u0621 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629\u061f \u0648\u062c\u062f\u062a \u062e\u0644\u0644 \u0645\u0632\u0639\u062c \u0644\u0646 \u064a\u062a\u0648\u0642\u0641 \u0645\u0646 \u0627\u0644\u0639\u0645\u0644\u061f \u062a\u062d\u062f\u062b \u0639\u0646 \u0630\u0644\u0643 \u0641\u064a \u0642\u0633\u0645 \u0645\u0634\u0627\u0643\u0644 GitHub \u060c \u0623\u0648 \u0623\u0631\u0633\u0644 \u0625\u0644\u0649 \u0628\u0631\u064a\u062f\u064a \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u0628\u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0627\u0644\u0625\u062c\u0631\u0627\u0621\u0627\u062a \u0623\u062f\u0646\u0627\u0647.","buttons":{"raiseIssue":"\u0631\u0641\u0639 \u0645\u0634\u0643\u0644\u0629","sendEmail":"\u0625\u0631\u0633\u0627\u0644 \u0628\u0631\u064a\u062f \u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a"}},"sourceCode":{"heading":"\u0645\u0635\u062f\u0631 \u0627\u0644\u0643\u0648\u062f \u0627\u0644\u0628\u0631\u0645\u062c\u064a\u0649","body":"\u0647\u0644 \u062a\u0631\u064a\u062f \u062a\u0634\u063a\u064a\u0644 \u0627\u0644\u0645\u0634\u0631\u0648\u0639 \u0645\u0646 \u0645\u0635\u062f\u0631\u0647\u061f \u0647\u0644 \u0623\u0646\u062a \u0645\u0637\u0648\u0631 \u0639\u0644\u0649 \u0627\u0633\u062a\u0639\u062f\u0627\u062f \u0644\u0644\u0645\u0633\u0627\u0647\u0645\u0629 \u0641\u064a \u062a\u0637\u0648\u064a\u0631 \u0647\u0630\u0627 \u0627\u0644\u0645\u0634\u0631\u0648\u0639\u061f \u0627\u0646\u0642\u0631 \u0639\u0644\u0649 \u0627\u0644\u0632\u0631 \u0623\u062f\u0646\u0627\u0647.","buttons":{"githubRepo":"\u0645\u0633\u062a\u0648\u062f\u0639 GitHub"}},"license":{"heading":"\u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0627\u0644\u062a\u0631\u062e\u064a\u0635","body":"\u0627\u0644\u0645\u0634\u0631\u0648\u0639 \u062e\u0627\u0636\u0639 \u0644\u0631\u062e\u0635\u0629 MIT \u060c \u0627\u0644\u062a\u064a \u064a\u0645\u0643\u0646\u0643 \u0627\u0644\u0642\u0631\u0627\u0629 \u0639\u0646\u0647\u0627 \u0623\u0643\u062b\u0631 \u0623\u062f\u0646\u0627\u0647. \u0641\u064a \u0627\u0644\u0623\u0633\u0627\u0633\u060c \u064a\u0633\u0645\u062d \u0644\u0643 \u0628\u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0627\u0644\u0645\u0634\u0631\u0648\u0639 \u0641\u064a \u0623\u064a \u0645\u0643\u0627\u0646 \u0628\u0634\u0631\u0637 \u0623\u0646 \u062a\u0642\u062f\u0645 \u0627\u0639\u062a\u0645\u0627\u062f\u0627\u062a \u0644\u0644\u0645\u0624\u0644\u0641 \u0627\u0644\u0623\u0635\u0644\u064a.","buttons":{"mitLicense":"\u0631\u062e\u0635\u0629 MIT"}},"footer":{"credit":"\u0635\u0646\u0639 \u0645\u0639 \u0627\u0644\u062d\u0628 \u0628\u0648\u0627\u0633\u0637\u0629 <1>\u0623\u0645\u0631\u0648\u062b \u0628\u064a\u0644\u0627\u064a</1>","thanks":"\u0634\u0643\u0631\u0627 \u0644\u0643 \u0639\u0644\u0649 \u0627\u0633\u062a\u062e\u062f\u0627\u0645 Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"heading":{"placeholder":"Heading"},"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date","placeholder":"March 2018"},"endDate":{"label":"End Date","placeholder":"March 2022"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"}},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name","placeholder":"Jane"},"lastName":{"label":"Last Name","placeholder":"Doe"},"subtitle":{"label":"Subtitle","placeholder":"Full Stack Web Developer"},"address":{"line1":{"label":"Address Line 1","placeholder":"Palladium Complex"},"line2":{"label":"Address Line 2","placeholder":"140 E 14th St"},"line3":{"label":"Address Line 3","placeholder":"New York, NY 10003 USA"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective","placeholder":"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Amazon"},"role":{"label":"Role","placeholder":"Front-end Web Developer"},"description":{"placeholder":"You can write about what you specialized in while working at the company and what projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Harvard University"},"major":{"label":"Major","placeholder":"Masters in Computer Science"},"grade":{"label":"Grade"},"description":{"placeholder":"You can write about projects or special credit classes that you took while studying at this school."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title","placeholder":"Math & Science Olympiad"},"subtitle":{"label":"Subtitle","placeholder":"First Place, International Level"},"description":{"placeholder":"You can write about what qualities made you succeed in getting this award."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Dothraki"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Richard Hendricks"},"position":{"label":"Position","placeholder":"CEO, Pied Piper"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"},"description":{"placeholder":"You can write about how you and the reference contact worked together and which projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Date of Birth"},"value":{"label":"Value","placeholder":"6th August 1995"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Accent Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"printResume":{"heading":"Print Your Resume","body":"You can click on the button below to generate a PDF instantly. Alternatively, you can also use <1>Cmd/Ctrl + P</1> but it would have different effects.","buttons":{"export":"Export","print":"Print"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Wouldn\'t it be nice if there was a guide to setting it up on your local machine? Need information on how to contribute to the project? Look no further, there\'s comprehensive documentation made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Tilf\xf8j {{- heading}}","startDate":{"label":"Startdato"},"endDate":{"label":"Slutdato"},"description":{"label":"Beskrivelse"}},"buttons":{"add":{"label":"Tilf\xf8j"},"delete":{"label":"Slet"}},"printDialog":{"heading":"Download dit CV","quality":{"label":"Kvalitet"},"printType":{"label":"Type","types":{"unconstrained":"Ingen begr\xe6nsninger","fitInA4":"Tilpas til A4","multiPageA4":"Flersidet A4"}},"helpText":["Denne eksportmetode bruger HTML-l\xe6rred til at konvertere CV til et billede og udskrive det p\xe5 en PDF, hvilket betyder, at det mister alle muligheder for valg / analyse.","Hvis det er vigtigt for dig, kan du pr\xf8ve at udskrive CV i stedet for at bruge Cmd / Ctrl + P eller udskrivningsknappen nedenfor. Resultatet kan variere, da output er browserafh\xe6ngig, men det vides at fungere bedst p\xe5 den nyeste version af Google Chrome."],"buttons":{"cancel":"Annull\xe9r","saveAsPdf":"Gem som PDF"}},"panZoomAnimation":{"helpText":"Du kan panorere rundt, og zoome ind p\xe5 l\xe6redet n\xe5r som helst og kigge n\xe6rmere p\xe5 dit CV."},"markdownHelpText":"Du kan benytte <1>GitHub Flavored Markdown</1> for at tilpasse den del af teksten."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Link til billede"},"firstName":{"label":"Fornavn"},"lastName":{"label":"Efternavn"},"subtitle":{"label":"Undertitel"},"address":{"label":"Adresse","line1":{"label":"Adresse linie 1"},"line2":{"label":"Adresse linie 2"},"line3":{"label":"Adresse linie 3"}},"phone":{"label":"Telefonnummer"},"website":{"label":"Hjemmeside"},"email":{"label":"E-mailadresse"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"M\xe5l"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Navn"},"role":{"label":"Rolle"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Navn"},"major":{"label":"Centralfag"},"grade":{"label":"Karakter"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titel"},"subtitle":{"label":"Undertitel"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Navn"},"subtitle":{"label":"Udsteder"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Navn"},"level":{"label":"Niveau"},"rating":{"label":"Bed\xf8mmelse"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Navn"},"position":{"label":"Jobtitel"},"phone":{"label":"Telefonnummer"},"email":{"label":"E-mailadresse"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"N\xf8gle"},"value":{"label":"V\xe6rdi"}}')},function(e){e.exports=JSON.parse('{"title":"Skabeloner"}')},function(e){e.exports=JSON.parse('{"title":"Farver","colorOptions":"Farve muligheder","primaryColor":"Prim\xe6r farve","accentColor":"Sekund\xe6r farve","clipboardCopyAction":"{{color}} er blevet kopieret til udklipsholderen."}')},function(e){e.exports=JSON.parse('{"title":"Skrifttype","fontFamily":{"label":"Skrifttypefamilie","helpText":"Du kan ogs\xe5 benytte de skrifttyper der er installeret p\xe5 din maskine. Indtast blot navnet p\xe5 skrifttypen, og browseren vil indl\xe6se den for dig."}}')},function(e){e.exports=JSON.parse('{"title":"Handlinger","disclaimer":"\xc6ndringer du laver i dit CV bliver automatisk gemt i din browsers lokale lager. Ingen data slipper ud, og din information derfor helt sikker.","importExport":{"heading":"Import\xe9r/eksport\xe9r","body":"Du kan importere eller eksportere dine data i JSON format. Med dette kan du \xe6ndre og printe dit CV fra hvilken som helst enhed. Gem denne fil til senere brug.","buttons":{"import":"Import\xe9r","export":"Eksport\xe9r"}},"downloadResume":{"heading":"Download dit CV.","body":"Du kan klikke p\xe5 knappen herunder for at gemme en PDF version af dit CV \xf8jeblikkeligt. For at f\xe5 det bedste resultat, benyt venligst den seneste version af Google Chrome.","buttons":{"saveAsPdf":"Gem som PDF"}},"loadDemoData":{"heading":"Indl\xe6s demo data","body":"Er du usikker p\xe5, hvad du skal g\xf8re med en frisk blank side? Indl\xe6s nogle demo data med forudfyldte v\xe6rdier, se hvordan et CV ser ud og begynd dine \xe6ndringer herfra.","buttons":{"loadData":"Hent data"}},"reset":{"heading":"Nulstil alting!","body":"Denne handling vil nulstille alle dine data og vil ogs\xe5 fjerne sikkerhedskopierne fra din browsers lokale lager, s\xe5 verificer en ekstra gang, at du har eksporteret dine informationer f\xf8r du nulstiller alting.","buttons":{"reset":"Nulstil"}}}')},function(e){e.exports=JSON.parse('{"title":"Indstillinger","language":{"label":"Sprog","helpText":"Hvis du vil hj\xe6lpe med at overs\xe6tte applikationen til dit eget sprog, kig da n\xe6rmere p\xe5 <1>overs\xe6ttelsesdokumentationen</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Om","documentation":{"heading":"Dokumentation","body":"Vil du vide mere om programmet? Mangler du information om, hvordan du kan bidrage til projektet? Du beh\xf8ves ikke lede mere, der er en fyldestg\xf8rende guide som er lavet kun til dig.","buttons":{"documentation":"Dokumentation"}},"bugOrFeatureRequest":{"heading":"Fejl? \xd8nsker til ny funktionalitet?","body":"Er der noget som forhindrer dig i at lave et Cv? Fundet en forbistret fejl som ikke vil forsvinde? Fort\xe6l om det p\xe5 GitHub under Issues, eller send mig en e-mail via knapperne herunder.","buttons":{"raiseIssue":"Opret en sag","sendEmail":"Send en e-mail"}},"sourceCode":{"heading":"Kildekode","body":"Vil du k\xf8re projektet fra kildekoden? Er du udvikler som vil hj\xe6lpe til med open-source udviilingen af dette projekt? Klik p\xe5 knappen herunder.","buttons":{"githubRepo":"GitHub repo"}},"license":{"heading":"Licensoplysninger","body":"Projektet er underlagt MIT licensen, hvilket du kan l\xe6se mere om herunder. Grundl\xe6ggende set m\xe5 projektet benyttes alle steder, hvis du refererer til den oprindelige skaber.","buttons":{"mitLicense":"MIT licens"}},"footer":{"credit":"Lavet med k\xe6rlig af <1>Amruth Pillai</1>","thanks":"Tak fordi du benytter Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"{{- heading}} hinzuf\xfcgen","startDate":{"label":"Startdatum"},"endDate":{"label":"Enddatum"},"description":{"label":"Beschreibung"}},"buttons":{"add":{"label":"Hinzuf\xfcgen"},"delete":{"label":"L\xf6schen"}},"printDialog":{"heading":"Lade Dein Resume herunter","quality":{"label":"Qualit\xe4t"},"printType":{"label":"Typ","types":{"unconstrained":"Uneingeschr\xe4nkt","fitInA4":"A4 einbauen","multiPageA4":"Mehrseitig A4"}},"helpText":["Diese Exportmethode verwendet HTML-Leinwand um den Lebenslauf in ein Bild zu konvertieren und es auf einer PDF auszudrucken. was bedeutet, dass es alle Auswahl-/Analysefunktionen verliert.","Wenn Ihnen das wichtig ist, versuchen Sie bitte den Lebenslauf mit Strg/Strg+P oder dem Druckknopf unten auszudrucken. Das Ergebnis kann variieren, da die Ausgabe vom Browser abh\xe4ngig ist, aber es ist bekannt, dass es am besten mit der neuesten Version von Google Chrome funktioniert."],"buttons":{"cancel":"Abbrechen","saveAsPdf":"Als PDF Speichern"}},"panZoomAnimation":{"helpText":"Sie k\xf6nnen die Zeichenfl\xe4che jederzeit schwenken und zoomen, um Ihren Lebenslauf genauer zu betrachten."},"markdownHelpText":"Du kannst <1>GitHub Flavored Markdown</1> verwenden, um diesen Abschnitt des Textes zu gestalten."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Foto-URL"},"firstName":{"label":"Vorname"},"lastName":{"label":"Nachname"},"subtitle":{"label":"Titel"},"address":{"label":"Adresse","line1":{"label":"Adresszeile 1"},"line2":{"label":"Addresszeile 2"},"line3":{"label":"Adresszeile 3"}},"phone":{"label":"Telefonnnummer"},"website":{"label":"Webseite"},"email":{"label":"E-Mail Adresse"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Ziel"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Stellentitel"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Abschluss"},"grade":{"label":"Note"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titel"},"subtitle":{"label":"Untertitel"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Zertifizierungsstelle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Status"},"rating":{"label":"Bewertung"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Anstellung"},"phone":{"label":"Rufnummer"},"email":{"label":"E-Mail Adresse"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Schl\xfcssel"},"value":{"label":"Wert"}}')},function(e){e.exports=JSON.parse('{"title":"Vorlagen"}')},function(e){e.exports=JSON.parse('{"title":"Farben","colorOptions":"Farboptionen","primaryColor":"Prim\xe4rfarbe","accentColor":"Sekund\xe4rfarbe","clipboardCopyAction":"{{color}} wurde in die Zwischenablage kopiert."}')},function(e){e.exports=JSON.parse('{"title":"Schriftarten","fontFamily":{"label":"Schriftfamilie","helpText":"Du kannst auch jede Schriftart verwenden, die auf Deinem System installiert ist. Hier einfach den Namen der Schriftfamilie eingeben und der Browser wird sie laden."}}')},function(e){e.exports=JSON.parse('{"title":"Aktionen","disclaimer":"\xc4nderungen, die Du an Deinem Lebenslauf vornimmst, werden automatisch im lokalen Speicher Deines Browsers gespeichert. Keine Daten werden an einen Server gesendet, daher sind Deine Informationen v\xf6llig sicher.","importExport":{"heading":"Importieren/Exportieren","body":"Du kannst Deine Daten im JSON format importieren oder exportieren. Damit kannst Du Deinen Lebenslauf von jedem Ger\xe4t bearbeiten und ausdrucken. Speicher diese Datei f\xfcr eine sp\xe4tere Nutzung.","buttons":{"import":"Importieren","export":"Export"}},"downloadResume":{"heading":"Lade Dein Resume herunter","body":"Du kannst auf die Schaltfl\xe4che unten klicken, um eine PDF-Version Ihres Lebenslaufs sofort herunterzuladen. F\xfcr die besten Ergebnisse, verwende bitte die neueste Version von Google Chrome.","buttons":{"saveAsPdf":"Als PDF Speichern"}},"loadDemoData":{"heading":"Demo-Daten laden","body":"Unklar auf, was mit einer frischen leeren Seite zu tun ist? Lade einige Demo-Daten mit vorausgef\xfcllten Werten, um zu sehen, wie ein Lebenslauf aussehen soll und von dort aus mit der Bearbeitung beginnen.","buttons":{"loadData":"Daten laden"}},"reset":{"heading":"Alles zur\xfccksetzen!","body":"Diese Aktion wird alle Daten zur\xfccksetzen und Backups aus dem lokalen Speicher des Browsers entfernen. Bitte sicherstellen, dass alle Daten exportiert sind, bevor Du alles zur\xfccksetzt.","buttons":{"reset":"Zur\xfccksetzen"}}}')},function(e){e.exports=JSON.parse('{"title":"Einstellungen","language":{"label":"Sprache","helpText":"Wenn Du helfen m\xf6chtest, die App in Deine eigene Sprache zu \xfcbersetzen, lies bitte die <1>\xdcbersetzungsdokumentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"\xdcber mich","documentation":{"heading":"Dokumentation","body":"Willst Du mehr \xfcber die App erfahren? Ben\xf6tigst Du Informationen, wie Du zum Projekt beitragen kannst? Such nicht weiter, es gibt einen umfassenden Leitfaden genau f\xfcr Dich.","buttons":{"documentation":"Dokumentation"}},"bugOrFeatureRequest":{"heading":"Fehler? Neue Funktion vorschlagen?","body":"Etwas hindert Dich an der Erstellung eines Lebenslaufs? Einen l\xe4stigen Fehler gefunden? Melde den Fehler auf GitHub oder schick mir eine E-Mail.","buttons":{"raiseIssue":"Issue er\xf6ffnen","sendEmail":"E-Mail senden"}},"sourceCode":{"heading":"Quellcode","body":"M\xf6chtest Du das Projekt von seiner Quelle aus ausf\xfchren? Bist Du als Entwickler bereit, zur Open-Source-Entwicklung dieses Projekts beizutragen? Klick auf den Button unten.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"Lizenzinformationen","body":"Das Projekt unterliegt der MIT-Lizenz, die Du weiter unten lesen kannst. Grunds\xe4tzlich darfst Du das Projekt \xfcberall nutzen, sofern Du den Originalautor nennst.","buttons":{"mitLicense":"MIT-Lizenz"}},"footer":{"credit":"Mit Liebe erstellt von <1>Amruth Pillai</1>","thanks":"Vielen Dank, dass Du Reactive Resume verwendest!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Agregar {{- heading}}","startDate":{"label":"Fecha de inicio"},"endDate":{"label":"Fecha Final"},"description":{"label":"Descripci\xf3n"}},"buttons":{"add":{"label":"Agregar"},"delete":{"label":"Eliminar"}},"printDialog":{"heading":"Descarga tu curr\xedculum","quality":{"label":"Calidad"},"printType":{"label":"Tipo","types":{"unconstrained":"Sin restricciones","fitInA4":"Ajustar a A4","multiPageA4":"Multip\xe1ginas A4"}},"helpText":["Este m\xe9todo de exportaci\xf3n utiliza el lienzo HTML para convertir el curr\xedculum en una imagen e imprimirlo en un PDF, lo que significa que perder\xe1 todas las capacidades de selecci\xf3n / an\xe1lisis.","Si eso es importante para usted, intente imprimir el curr\xedculum usando Cmd / Ctrl + P o el bot\xf3n de impresi\xf3n a continuaci\xf3n. El resultado puede variar ya que la salida depende del navegador, pero se sabe que funciona mejor en la \xfaltima versi\xf3n de Google Chrome."],"buttons":{"cancel":"Cancelar","saveAsPdf":"Guardar como PDF"}},"panZoomAnimation":{"helpText":"Puedes desplazar y hacer zoom al tablero en cualquier momento para darle una mirada m\xe1s detallada a tu curr\xedculum."},"markdownHelpText":"Puedes usar <1>GitHub Flavored Markdown</1> para darle estilo a esta secci\xf3n."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL de la foto"},"firstName":{"label":"Primer Nombre"},"lastName":{"label":"Apellido"},"subtitle":{"label":"Subt\xedtulo"},"address":{"label":"Direcci\xf3n","line1":{"label":"L\xednea de direcci\xf3n 1"},"line2":{"label":"L\xednea de direcci\xf3n 2"},"line3":{"label":"L\xednea de direcci\xf3n 3"}},"phone":{"label":"N\xfamero de tel\xe9fono"},"website":{"label":"Sitio Web"},"email":{"label":"Direcci\xf3n de correo electr\xf3nico"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objetivo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nombre"},"role":{"label":"Puesto"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nombre"},"major":{"label":"Carrera"},"grade":{"label":"Calificaci\xf3n"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"T\xedtulo"},"subtitle":{"label":"Subt\xedtulo"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nombre"},"subtitle":{"label":"Autor\xeda"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nombre"},"level":{"label":"Nivel"},"rating":{"label":"Calificaci\xf3n"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nombre"},"position":{"label":"Puesto de trabajo"},"phone":{"label":"N\xfamero de tel\xe9fono"},"email":{"label":"Direcci\xf3n de correo electr\xf3nico"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Clave"},"value":{"label":"Valor"}}')},function(e){e.exports=JSON.parse('{"title":"Plantillas"}')},function(e){e.exports=JSON.parse('{"title":"Colores","colorOptions":"Opciones de Color","primaryColor":"Color principal","accentColor":"Color secundario","clipboardCopyAction":"El color {{color}} ha sido copiado al portapapeles."}')},function(e){e.exports=JSON.parse('{"title":"Tipograf\xedas","fontFamily":{"label":"Familia Tipogr\xe1fica","helpText":"Tambi\xe9n puedes usar cualquier fuente que est\xe9 instalada en tu sistema. Escribe el nombre de la familia de fuentes aqu\xed para que el navegador la cargue."}}')},function(e){e.exports=JSON.parse('{"title":"Acciones","disclaimer":"Los cambios hechos a tu curr\xedculum se guardan autom\xe1ticamente en el almacenamiento local de tu navegador. Como ning\xfan dato sale de tu navegador, tu informaci\xf3n est\xe1 completamente segura.","importExport":{"heading":"Importar/Exportar","body":"Puedes importar o exportar tus datos en formato JSON. Con el archivo JSON, puedes editar o imprimir tu curr\xedculum desde cualquier dispositivo. Mant\xe9n una copia de este archivo por si lo necesitas m\xe1s tarde.","buttons":{"import":"Importar","export":"Exportar"}},"downloadResume":{"heading":"Descarga tu curr\xedculum","body":"Para descargar instant\xe1neamente una versi\xf3n en PDF de tu curr\xedculum, puedes hacer clic en el siguiente bot\xf3n. Se recomienda que uses la versi\xf3n m\xe1s reciente de Google Chrome para obtener los mejores resultados.","buttons":{"saveAsPdf":"Guardar como PDF"}},"loadDemoData":{"heading":"Agregar Datos de Demostraci\xf3n","body":"\xbfNo sabes por d\xf3nde comenzar? Puedes cargar datos de demostraci\xf3n con valores predeterminados para tener una idea de c\xf3mo luce un curr\xedculum, y comenzar con editar esos valores.","buttons":{"loadData":"Cargar datos"}},"reset":{"heading":"\xa1Restablecer todo!","body":"Esta acci\xf3n reiniciar\xe1 toda tu informaci\xf3n y tambi\xe9n borrar\xe1 las copias de seguridad guardadas en el almacenamiento local de tu navegardor. Aseg\xfarate de haber exportado tu informaci\xf3n antes de continuar con esta acci\xf3n.","buttons":{"reset":"Restablecer"}}}')},function(e){e.exports=JSON.parse('{"title":"Configuraci\xf3n","language":{"label":"Escoger idioma","helpText":"Si deseas ayudar a traducir la app a tu idioma, por favor consulta la <1>documentaci\xf3n de traducci\xf3n</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Acerca de","documentation":{"heading":"Documentaci\xf3n","body":"\xbfQuieres saber m\xe1s sobre la aplicaci\xf3n? \xbfNecesitas informaci\xf3n sobre c\xf3mo contribuir al proyecto? No busques m\xe1s, hay una gu\xeda completa hecha para ti.","buttons":{"documentation":"Documentaci\xf3n"}},"bugOrFeatureRequest":{"heading":"Encontr\xf3 un error? Nueva solicitud de funcionalidad?","body":"\xbfHay algo que no te deja trabajar en tu curr\xedculum? \xbfHayaste un error que no puedes resolver? Abre una discusi\xf3n bajo la secci\xf3n de \\"Issues\\" (problemas) de GitHub, o sigue los siguientes pasos para escribirme un correo.","buttons":{"raiseIssue":"Notificar un problema","sendEmail":"Mandar un correo electr\xf3nico"}},"sourceCode":{"heading":"C\xf3digo Fuente","body":"\xbfTe interesa ejecutar el c\xf3digo fuente de este proyecto? \xbfEres un desarrollador interesado en involucrtarte en el desarrollo de este proyecto? Haz clic en el siguiente bot\xf3n.","buttons":{"githubRepo":"Repositorio de GitHub"}},"license":{"heading":"Informaci\xf3n de licencia","body":"El proyecto est\xe1 regido por la licencia MIT, sobre la cual puedes leer a continuaci\xf3n. B\xe1sicamente, puedes usar el proyecto en cualquier lugar, siempre y cuando le des cr\xe9dito al autor original.","buttons":{"mitLicense":"Licencia MIT"}},"footer":{"credit":"Desarrollado con amor por <1>Amruth Pillai</1>","thanks":"\xa1Gracias por usar Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Ajouter {{- heading}}","startDate":{"label":"Date de d\xe9but"},"endDate":{"label":"Date de fin"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Ajouter"},"delete":{"label":"Supprimer"}},"printDialog":{"heading":"T\xe9l\xe9chargez Votre CV","quality":{"label":"Qualit\xe9"},"printType":{"label":"Type","types":{"unconstrained":"Non Contraint","fitInA4":"Convient au format A4","multiPageA4":"Multi-Page A4"}},"helpText":["Cette m\xe9thode d\'exportation utilise le canevas HTML pour convertir le CV en image et l\'imprimer sur un PDF, ce qui signifie qu\'il perdra toutes les capacit\xe9s de s\xe9lection / analyse.","Si cela est important pour vous, essayez d\'imprimer le CV \xe0 la place en utilisant Cmd/Ctrl + P ou le bouton d\'impression ci-dessous. Le r\xe9sultat peut varier car la sortie d\xe9pend du navigateur, mais il est connu qu\'elle fonctionne mieux sur la derni\xe8re version de Google Chrome."],"buttons":{"cancel":"Annuler","saveAsPdf":"Enregistrer en PDF"}},"panZoomAnimation":{"helpText":"Vous pouvez effectuer un panoramique et un zoom sur le plan de travail \xe0 tout moment pour voir de plus pr\xe8s votre CV."},"markdownHelpText":"Vous pouvez utiliser <1>GitHub Flavored Markdown</1> pour styliser cette section du texte."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL de la photo"},"firstName":{"label":"Pr\xe9nom"},"lastName":{"label":"Nom"},"subtitle":{"label":"Sous-titre"},"address":{"label":"Addresse","line1":{"label":"Adresse ligne 1"},"line2":{"label":"Adresse ligne 2"},"line3":{"label":"Adresse ligne 3"}},"phone":{"label":"Num\xe9ro de t\xe9l\xe9phone"},"website":{"label":"Site Web"},"email":{"label":"Adresse e-mail"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objectif"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nom"},"role":{"label":"R\xf4le"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nom"},"major":{"label":"Sp\xe9cialit\xe9"},"grade":{"label":"Note"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titre"},"subtitle":{"label":"Sous-titre"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nom"},"subtitle":{"label":"Autorit\xe9"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nom"},"level":{"label":"Niveau"},"rating":{"label":"\xc9valuation"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nom"},"position":{"label":"Poste"},"phone":{"label":"Num\xe9ro de t\xe9l\xe9phone"},"email":{"label":"Adresse e-mail"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Cl\xe9"},"value":{"label":"Valeur"}}')},function(e){e.exports=JSON.parse('{"title":"Mod\xe8les"}')},function(e){e.exports=JSON.parse('{"title":"Couleurs","colorOptions":"Options de couleurs","primaryColor":"Couleur Principale","accentColor":"Couleur Secondaire","clipboardCopyAction":"La couleur {{color}} a \xe9t\xe9 copi\xe9 dans le presse-papiers."}')},function(e){e.exports=JSON.parse('{"title":"Polices","fontFamily":{"label":"Famille de polices","helpText":"Vous pouvez \xe9galement utiliser n\'importe quelle police install\xe9e sur votre syst\xe8me. Entrez simplement le nom de la famille de polices ici et le navigateur la chargera pour vous."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Les modifications que vous apportez \xe0 votre CV sont enregistr\xe9es automatiquement dans le stockage local de votre navigateur. Aucune donn\xe9e ne sort, donc vos informations sont compl\xe8tement s\xe9curis\xe9es.","importExport":{"heading":"Import / Export","body":"Vous pouvez importer ou exporter vos donn\xe9es au format JSON. Avec ceci, vous pouvez \xe9diter et imprimer votre CV \xe0 partir de n\'importe quel appareil. Enregistrez ce fichier pour une utilisation ult\xe9rieure.","buttons":{"import":"Importer","export":"Exporter"}},"downloadResume":{"heading":"T\xe9l\xe9chargez votre CV","body":"Vous pouvez cliquer sur le bouton ci-dessous pour t\xe9l\xe9charger instantan\xe9ment une version PDF de votre CV. Pour de meilleurs r\xe9sultats, veuillez utiliser la derni\xe8re version de Google Chrome.","buttons":{"saveAsPdf":"Enregistrer en PDF"}},"loadDemoData":{"heading":"Charger des D\xe9mo donn\xe9es","body":"Vous ne savez pas quoi faire avec une nouvelle page vierge? Chargez des donn\xe9es de d\xe9monstration avec des valeurs pr\xe9remplies pour voir \xe0 quoi devrait ressembler un CV et vous pouvez commencer \xe0 \xe9diter \xe0 partir de l\xe0.","buttons":{"loadData":"Charger des donn\xe9es"}},"reset":{"heading":"Tout supprimer!","body":"Cette action r\xe9initialisera toutes vos donn\xe9es et supprimera \xe9galement les sauvegardes effectu\xe9es sur le stockage local de votre navigateur. donc assurez-vous que vous avez export\xe9 vos informations avant de tout r\xe9initialiser.","buttons":{"reset":"R\xe9initialiser"}}}')},function(e){e.exports=JSON.parse('{"title":"Param\xe8tres","language":{"label":"Langue","helpText":"Si vous souhaitez aider \xe0 traduire l\'application dans votre propre langue, veuillez vous r\xe9f\xe9rer \xe0 la <1>Documentation de traduction</1>."}}')},function(e){e.exports=JSON.parse('{"title":"\xc0 propos","documentation":{"heading":"Documentation","body":"Vous voulez en savoir plus sur l\'application? Besoin d\'informations sur la mani\xe8re de contribuer au projet? Ne cherchez plus, un guide complet est fait pour vous.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Un bug? Une nouvelle fonctionnalit\xe9?","body":"Quelque chose emp\xeache votre progression de faire un CV? Vous avez trouv\xe9 un bug persistant? Parlez-en dans la section GitHub Issues, ou envoyez-moi un email en utilisant les actions ci-dessous.","buttons":{"raiseIssue":"Soulever une anomalie","sendEmail":"Envoyer un e-mail"}},"sourceCode":{"heading":"Code source","body":"Vous voulez ex\xe9cuter le projet depuis sa source ? \xcates-vous un d\xe9veloppeur d\xe9sireux de contribuer au d\xe9veloppement open-source de ce projet ? Cliquez sur le bouton ci-dessous.","buttons":{"githubRepo":"R\xe9pertoire GitHub"}},"license":{"heading":"Information de licence","body":"Le projet est r\xe9gi par la licence MIT, que vous pouvez consulter ci-dessous. Fondamentalement, vous \xeates autoris\xe9 \xe0 utiliser le projet n\'importe o\xf9 \xe0 condition de donner des cr\xe9dits \xe0 l\'auteur d\'origine.","buttons":{"mitLicense":"Licence MIT"}},"footer":{"credit":"Fabriqu\xe9 avec amour par <1>Amruth Pillai</1>","thanks":"Merci d\'utiliser Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u05d4\u05d5\u05e1\u05e3 {{- heading}}","startDate":{"label":"\u05ea\u05d0\u05e8\u05d9\u05da \u05d4\u05ea\u05d7\u05dc\u05d4"},"endDate":{"label":"\u05ea\u05d0\u05e8\u05d9\u05da \u05e1\u05d9\u05d5\u05dd"},"description":{"label":"\u05ea\u05d9\u05d0\u05d5\u05e8"}},"buttons":{"add":{"label":"\u05d4\u05d5\u05e1\u05e3"},"delete":{"label":"\u05de\u05d7\u05e7"}},"printDialog":{"heading":"\u05d4\u05d5\u05e8\u05d3 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da","quality":{"label":"\u05d0\u05d9\u05db\u05d5\u05ea"},"printType":{"label":"\u05e1\u05d5\u05d2","types":{"unconstrained":"\u05d1\u05dc\u05ea\u05d9 \u05de\u05d5\u05d2\u05d1\u05dc\u05ea","fitInA4":"\u05de\u05ea\u05d0\u05d9\u05dd \u05dc- A4","multiPageA4":"A4 \u05e8\u05d1 \u05e2\u05de\u05d5\u05d3\u05d9\u05dd"}},"helpText":["\u05e9\u05d9\u05d8\u05ea \u05d9\u05d9\u05e6\u05d5\u05d0 \u05d6\u05d5 \u05e2\u05d5\u05e9\u05d4 \u05e9\u05d9\u05de\u05d5\u05e9 \u05d1\u05e7\u05e0\u05d1\u05e1 HTML \u05db\u05d3\u05d9 \u05dc\u05d4\u05de\u05d9\u05e8 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05dc\u05ea\u05de\u05d5\u05e0\u05d4 \u05d5\u05dc\u05d4\u05d3\u05e4\u05d9\u05e1\u05d4 \u05e2\u05dc \u05d2\u05d1\u05d9 PDF, \u05de\u05d4 \u05e9\u05d0\u05d5\u05de\u05e8 \u05e9\u05d4\u05d9\u05d0 \u05ea\u05d0\u05d1\u05d3 \u05d0\u05ea \u05db\u05dc \u05d9\u05db\u05d5\u05dc\u05d5\u05ea \u05d4\u05d1\u05d7\u05d9\u05e8\u05d4 / \u05e0\u05d9\u05ea\u05d5\u05d7.","\u05d0\u05dd \u05d6\u05d4 \u05d7\u05e9\u05d5\u05d1 \u05dc\u05da, \u05d0\u05e0\u05d0 \u05e0\u05e1\u05d4 \u05dc\u05d4\u05d3\u05e4\u05d9\u05e1 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05d1\u05de\u05e7\u05d5\u05dd \u05d6\u05d0\u05ea, \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea Cmd / Ctrl + P \u05d0\u05d5 \u05e2\u05dc \u05db\u05e4\u05ea\u05d5\u05e8 \u05d4\u05d4\u05d3\u05e4\u05e1\u05d4 \u05e9\u05dc\u05de\u05d8\u05d4. \u05d4\u05ea\u05d5\u05e6\u05d0\u05d4 \u05e2\u05e9\u05d5\u05d9\u05d4 \u05dc\u05d4\u05e9\u05ea\u05e0\u05d5\u05ea \u05de\u05db\u05d9\u05d5\u05d5\u05df \u05e9\u05d4\u05e4\u05dc\u05d8 \u05ea\u05dc\u05d5\u05d9 \u05d1\u05d3\u05e4\u05d3\u05e4\u05df, \u05d0\u05da \u05d9\u05d3\u05d5\u05e2 \u05e9\u05d4\u05d5\u05d0 \u05e4\u05d5\u05e2\u05dc \u05d1\u05e6\u05d5\u05e8\u05d4 \u05d4\u05d8\u05d5\u05d1\u05d4 \u05d1\u05d9\u05d5\u05ea\u05e8 \u05e2\u05dc \u05d4\u05d2\u05e8\u05e1\u05d4 \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d4 \u05e9\u05dc Google Chrome."],"buttons":{"cancel":"\u05d1\u05d9\u05d8\u05d5\u05dc","saveAsPdf":"\u05e9\u05de\u05d5\u05e8 \u05db-PDF"}},"panZoomAnimation":{"helpText":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05d4\u05d6\u05d9\u05d6 \u05d0\u05ea \u05d4\u05de\u05e9\u05d8\u05d7 \u05d5\u05dc\u05d4\u05ea\u05e7\u05e8\u05d1 \u05d0\u05dc\u05d9\u05d5 \u05d1\u05db\u05dc \u05e2\u05ea \u05db\u05d3\u05d9 \u05dc\u05e8\u05d0\u05d5\u05ea \u05de\u05e7\u05e8\u05d5\u05d1 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da."},"markdownHelpText":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05d4\u05e9\u05ea\u05de\u05e9 \u05d1 <1> \u05e1\u05d9\u05de\u05d5\u05df \u05d1\u05d8\u05e2\u05dd GitHub \u05d1\u05d8\u05e2\u05dd </1> \u05db\u05d3\u05d9 \u05dc\u05e2\u05e6\u05d1 \u05e7\u05d8\u05e2 \u05d6\u05d4 \u05e9\u05dc \u05d4\u05d8\u05e7\u05e1\u05d8."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u05e7\u05d9\u05e9\u05d5\u05e8 \u05dc\u05ea\u05de\u05d5\u05e0\u05d4"},"firstName":{"label":"\u05e9\u05dd \u05e4\u05e8\u05d8\u05d9"},"lastName":{"label":"\u05e9\u05dd \u05de\u05e9\u05e4\u05d7\u05d4"},"subtitle":{"label":"\u05ea\u05e8\u05d2\u05d5\u05dd"},"address":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea","line1":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d5\u05e8\u05d4 1"},"line2":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d5\u05e8\u05d4 2"},"line3":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d5\u05e8\u05d4 3"}},"phone":{"label":"\u05de\u05e1\u05e4\u05e8 \u05d8\u05dc\u05e4\u05d5\u05df"},"website":{"label":"\u05d0\u05ea\u05e8"},"email":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05d0\u05d9\u05de\u05d9\u05d9\u05dc"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u05de\u05d8\u05e8\u05d4"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"role":{"label":"\u05ea\u05e4\u05e7\u05d9\u05d3"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"major":{"label":"\u05de\u05e7\u05e6\u05d5\u05e2 \u05e8\u05d0\u05e9\u05d9"},"grade":{"label":"\u05db\u05d9\u05ea\u05d4"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"subtitle":{"label":"\u05ea\u05e8\u05d2\u05d5\u05dd"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"subtitle":{"label":"\u05ea\u05e8\u05d2\u05d5\u05dd"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"level":{"label":"\u05e8\u05de\u05d4"},"rating":{"label":"\u05d3\u05d9\u05e8\u05d5\u05d2"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"position":{"label":"\u05ea\u05e4\u05e7\u05d9\u05d3"},"phone":{"label":"\u05de\u05e1\u05e4\u05e8 \u05d8\u05dc\u05e4\u05d5\u05df"},"email":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05d0\u05d9\u05de\u05d9\u05d9\u05dc"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u05de\u05e4\u05ea\u05d7"},"value":{"label":"\u05e2\u05e8\u05da"}}')},function(e){e.exports=JSON.parse('{"title":"\u05ea\u05d1\u05e0\u05d9\u05d5\u05ea"}')},function(e){e.exports=JSON.parse('{"title":"\u05e6\u05d1\u05e2\u05d9\u05dd","colorOptions":"\u05d0\u05e4\u05e9\u05e8\u05d5\u05d9\u05d5\u05ea \u05e6\u05d1\u05e2\u05d9\u05dd","primaryColor":"\u05e6\u05d1\u05e2 \u05e8\u05d0\u05e9\u05d9","accentColor":"\u05e6\u05d1\u05e2 \u05d4\u05d3\u05d2\u05e9\u05d4","clipboardCopyAction":"{{color}} \u05d4\u05d5\u05e2\u05ea\u05e7 \u05d0\u05dc \u05d4\u05dc\u05d5\u05d7 \u05e9\u05dc\u05da."}')},function(e){e.exports=JSON.parse('{"title":"\u05d2\u05d5\u05e4\u05e0\u05d9\u05dd","fontFamily":{"label":"\u05de\u05e9\u05e4\u05d7\u05ea \u05d2\u05d5\u05e4\u05e0\u05d9\u05dd","helpText":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05d4\u05e9\u05ea\u05de\u05e9 \u05d1\u05db\u05dc \u05d2\u05d5\u05e4\u05df \u05d4\u05de\u05d5\u05ea\u05e7\u05df \u05d2\u05dd \u05d1\u05de\u05e2\u05e8\u05db\u05ea \u05e9\u05dc\u05da. \u05e4\u05e9\u05d5\u05d8 \u05d4\u05d6\u05df \u05db\u05d0\u05df \u05d0\u05ea \u05e9\u05dd \u05de\u05e9\u05e4\u05d7\u05ea \u05d4\u05d2\u05d5\u05e4\u05e0\u05d9\u05dd \u05d5\u05d4\u05d3\u05e4\u05d3\u05e4\u05df \u05d9\u05d8\u05e2\u05d9\u05df \u05d0\u05d5\u05ea\u05d5 \u05e2\u05d1\u05d5\u05e8\u05da."}}')},function(e){e.exports=JSON.parse('{"title":"\u05e4\u05e2\u05d5\u05dc\u05d5\u05ea","disclaimer":"\u05d4\u05e9\u05d9\u05e0\u05d5\u05d9\u05d9\u05dd \u05e9\u05d0\u05ea\u05d4 \u05de\u05d1\u05e6\u05e2 \u05d1\u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da \u05e0\u05e9\u05de\u05e8\u05d9\u05dd \u05d0\u05d5\u05d8\u05d5\u05de\u05d8\u05d9\u05ea \u05d1\u05d0\u05d7\u05e1\u05d5\u05df \u05d4\u05de\u05e7\u05d5\u05de\u05d9 \u05e9\u05dc \u05d4\u05d3\u05e4\u05d3\u05e4\u05df. \u05e0\u05ea\u05d5\u05e0\u05d9\u05dd \u05dc\u05d0 \u05d9\u05d5\u05e6\u05d0\u05d9\u05dd \u05dc\u05e9\u05d5\u05dd \u05de\u05e7\u05d5\u05dd, \u05d5\u05de\u05db\u05d0\u05df \u05e9\u05d4\u05de\u05d9\u05d3\u05e2 \u05e9\u05dc\u05da \u05de\u05d0\u05d5\u05d1\u05d8\u05d7 \u05dc\u05d7\u05dc\u05d5\u05d8\u05d9\u05df.","importExport":{"heading":"\u05d9\u05d9\u05d1\u05d5\u05d0/\u05d9\u05d9\u05e6\u05d5\u05d0","body":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05d9\u05d9\u05d1\u05d0 \u05d0\u05d5 \u05dc\u05d9\u05d9\u05e6\u05d0 \u05d0\u05ea \u05d4\u05e0\u05ea\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc\u05da \u05d1\u05e4\u05d5\u05e8\u05de\u05d8 JSON. \u05d1\u05e2\u05d6\u05e8\u05ea\u05d5 \u05ea\u05d5\u05db\u05dc\u05d5 \u05dc\u05e2\u05e8\u05d5\u05da \u05d5\u05dc\u05d4\u05d3\u05e4\u05d9\u05e1 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05de\u05db\u05dc \u05de\u05db\u05e9\u05d9\u05e8. \u05e9\u05de\u05d5\u05e8 \u05e7\u05d5\u05d1\u05e5 \u05d6\u05d4 \u05dc\u05e9\u05d9\u05de\u05d5\u05e9 \u05de\u05d0\u05d5\u05d7\u05e8 \u05d9\u05d5\u05ea\u05e8.","buttons":{"import":"\u05d9\u05d9\u05d1\u05d5\u05d0","export":"\u05d9\u05d9\u05e6\u05d5\u05d0"}},"downloadResume":{"heading":"\u05d4\u05d5\u05e8\u05d3 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da","body":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05dc\u05d7\u05d5\u05e5 \u05e2\u05dc \u05d4\u05db\u05e4\u05ea\u05d5\u05e8 \u05dc\u05de\u05d8\u05d4 \u05db\u05d3\u05d9 \u05dc\u05d4\u05d5\u05e8\u05d9\u05d3 \u05e7\u05d5\u05e8\u05d5\u05ea \u05d7\u05d9\u05d9\u05dd PDF \u05e9\u05dc \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da \u05d1\u05d0\u05d5\u05e4\u05df \u05de\u05d9\u05d9\u05d3\u05d9. \u05dc\u05e7\u05d1\u05dc\u05ea \u05d4\u05ea\u05d5\u05e6\u05d0\u05d5\u05ea \u05d4\u05d8\u05d5\u05d1\u05d5\u05ea \u05d1\u05d9\u05d5\u05ea\u05e8, \u05d0\u05e0\u05d0 \u05d4\u05e9\u05ea\u05de\u05e9 \u05d1\u05d2\u05e8\u05e1\u05d4 \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d4 \u05e9\u05dc Google Chrome.","buttons":{"saveAsPdf":"\u05e9\u05de\u05d5\u05e8 \u05db-PDF"}},"loadDemoData":{"heading":"\u05d8\u05e2\u05df \u05e0\u05ea\u05d5\u05e0\u05d9 \u05d3\u05de\u05d4","body":"\u05dc\u05d0 \u05d1\u05e8\u05d5\u05e8 \u05dc\u05da \u05de\u05d4 \u05dc\u05e2\u05e9\u05d5\u05ea \u05e2\u05dd \u05d3\u05e3 \u05e8\u05d9\u05e7? \u05d8\u05e2\u05df \u05db\u05de\u05d4 \u05e0\u05ea\u05d5\u05e0\u05d9 \u05d3\u05de\u05d4 \u05e2\u05dd \u05e2\u05e8\u05db\u05d9\u05dd \u05d4\u05de\u05d5\u05d2\u05d3\u05e8\u05d9\u05dd \u05de\u05e8\u05d0\u05e9 \u05db\u05d3\u05d9 \u05dc\u05e8\u05d0\u05d5\u05ea \u05db\u05d9\u05e6\u05d3 \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05d0\u05de\u05d5\u05e8\u05d9\u05dd \u05dc\u05d4\u05d9\u05e8\u05d0\u05d5\u05ea \u05d5\u05ea\u05d5\u05db\u05dc \u05dc\u05d4\u05ea\u05d7\u05d9\u05dc \u05dc\u05e2\u05e8\u05d5\u05da \u05de\u05e9\u05dd.","buttons":{"loadData":"\u05d8\u05e2\u05df \u05de\u05d9\u05d3\u05e2"}},"reset":{"heading":"\u05d0\u05e4\u05e1 \u05d4\u05db\u05dc","body":"\u05e4\u05e2\u05d5\u05dc\u05d4 \u05d6\u05d5 \u05ea\u05d0\u05e4\u05e1 \u05d0\u05ea \u05db\u05dc \u05d4\u05e0\u05ea\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc\u05da \u05d5\u05ea\u05e1\u05d9\u05e8 \u05d2\u05d9\u05d1\u05d5\u05d9\u05d9\u05dd \u05e9\u05e0\u05e2\u05e9\u05d5 \u05d2\u05dd \u05dc\u05d0\u05d7\u05e1\u05d5\u05df \u05d4\u05de\u05e7\u05d5\u05de\u05d9 \u05e9\u05dc \u05d4\u05d3\u05e4\u05d3\u05e4\u05df \u05e9\u05dc\u05da, \u05d0\u05e0\u05d0 \u05d5\u05d5\u05d3\u05d0 \u05e9\u05d9\u05e6\u05d0\u05ea \u05d0\u05ea \u05d4\u05de\u05d9\u05d3\u05e2 \u05e9\u05dc\u05da \u05dc\u05e4\u05e0\u05d9 \u05e9\u05ea\u05d0\u05e4\u05e1 \u05d0\u05ea \u05d4\u05db\u05dc.","buttons":{"reset":"\u05d0\u05d9\u05e4\u05d5\u05e1"}}}')},function(e){e.exports=JSON.parse('{"title":"\u05d4\u05d2\u05d3\u05e8\u05d5\u05ea","language":{"label":"\u05e9\u05e4\u05d4","helpText":"\u05d0\u05dd \u05ea\u05e8\u05e6\u05d4 \u05dc\u05e2\u05d6\u05d5\u05e8 \u05d1\u05ea\u05e8\u05d2\u05d5\u05dd \u05d4\u05d0\u05e4\u05dc\u05d9\u05e7\u05e6\u05d9\u05d4 \u05dc\u05e9\u05e4\u05d4 \u05e9\u05dc\u05da, \u05e2\u05d9\u05d9\u05df \u05d1 <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"\u05d0\u05d5\u05d3\u05d5\u05ea","documentation":{"heading":"\u05ea\u05d9\u05e2\u05d5\u05d3","body":"\u05e8\u05d5\u05e6\u05d4 \u05dc\u05d3\u05e2\u05ea \u05d9\u05d5\u05ea\u05e8 \u05e2\u05dc \u05d4\u05d0\u05e4\u05dc\u05d9\u05e7\u05e6\u05d9\u05d4? \u05d4\u05d0\u05dd \u05dc\u05d0 \u05d4\u05d9\u05d4 \u05e0\u05d7\u05de\u05d3 \u05d0\u05dd \u05d4\u05d9\u05d4 \u05de\u05d3\u05e8\u05d9\u05da \u05dc\u05d4\u05ea\u05e7\u05e0\u05ea\u05d5 \u05d1\u05de\u05d7\u05e9\u05d1 \u05d4\u05de\u05e7\u05d5\u05de\u05d9 \u05e9\u05dc\u05db\u05dd? \u05d6\u05e7\u05d5\u05e7 \u05dc\u05de\u05d9\u05d3\u05e2 \u05db\u05d9\u05e6\u05d3 \u05dc\u05ea\u05e8\u05d5\u05dd \u05dc\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8? \u05d0\u05dc \u05ea\u05d7\u05db\u05d4 \u05d9\u05d5\u05ea\u05e8, \u05d9\u05e9 \u05ea\u05d9\u05e2\u05d5\u05d3 \u05de\u05e7\u05d9\u05e3 \u05e9\u05d9\u05d5\u05e6\u05e8 \u05d1\u05d3\u05d9\u05d5\u05e7 \u05d1\u05e9\u05d1\u05d9\u05dc\u05da.","buttons":{"documentation":"\u05ea\u05d9\u05e2\u05d5\u05d3"}},"bugOrFeatureRequest":{"heading":"\u05d1\u05d0\u05d2? \u05d1\u05e7\u05e9\u05d4 \u05dc\u05ea\u05d5\u05e1\u05e3?","body":"\u05de\u05e9\u05d4\u05d5 \u05e9\u05e2\u05d5\u05e6\u05e8 \u05d0\u05ea \u05d4\u05d4\u05ea\u05e7\u05d3\u05de\u05d5\u05ea \u05e9\u05dc\u05da \u05de\u05dc\u05db\u05ea\u05d5\u05d1 \u05e7\u05d5\u05e8\u05d5\u05ea \u05d7\u05d9\u05d9\u05dd? \u05de\u05e6\u05d0\u05ea \u05d1\u05d0\u05d2 \u05de\u05e6\u05d9\u05e7 \u05e9\u05e4\u05e9\u05d5\u05d8 \u05dc\u05d0 \u05e0\u05e4\u05ea\u05e8? \u05d3\u05d5\u05d5\u05d7 \u05e2\u05dc \u05d6\u05d4 \u05d1\u05e7\u05d8\u05e2 \u05d1\u05e2\u05d9\u05d5\u05ea \u05d1-GitHub, \u05d0\u05d5 \u05e9\u05dc\u05d7 \u05dc\u05d9 \u05d0\u05d9\u05de\u05d9\u05d9\u05dc \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05e4\u05e7\u05d5\u05d3\u05d5\u05ea \u05dc\u05de\u05d8\u05d4.","buttons":{"raiseIssue":"\u05d4\u05e2\u05dc\u05d4 \u05d1\u05e2\u05d9\u05d4","sendEmail":"\u05e9\u05dc\u05d7 \u05d0\u05d9\u05de\u05d9\u05d9\u05dc"}},"sourceCode":{"heading":"\u05e7\u05d5\u05d3 \u05de\u05e7\u05d5\u05e8","body":"\u05e8\u05d5\u05e6\u05d9\u05dd \u05dc\u05e0\u05d4\u05dc \u05d0\u05ea \u05d4\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8 \u05de\u05e7\u05d5\u05d1\u05e5 \u05d4\u05de\u05e7\u05d5\u05e8 \u05e9\u05dc\u05d5? \u05d4\u05d0\u05dd \u05d0\u05ea\u05d4 \u05de\u05e4\u05ea\u05d7 \u05e9\u05de\u05d5\u05db\u05df \u05dc\u05ea\u05e8\u05d5\u05dd \u05dc\u05e4\u05d9\u05ea\u05d5\u05d7 \u05d4\u05e7\u05d5\u05d3 \u05d4\u05e4\u05ea\u05d5\u05d7 \u05e9\u05dc \u05d4\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8 \u05d4\u05d6\u05d4? \u05dc\u05d7\u05e5 \u05e2\u05dc \u05d4\u05db\u05e4\u05ea\u05d5\u05e8 \u05dc\u05de\u05d8\u05d4.","buttons":{"githubRepo":"\u05de\u05d0\u05d2\u05e8 \u05d2\u05d9\u05ea\u05d5\u05d1"}},"license":{"heading":"\u05de\u05d9\u05d3\u05e2 \u05e2\u05dc \u05d4\u05e8\u05e9\u05d9\u05d5\u05df","body":"\u05d4\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8 \u05de\u05e0\u05d5\u05d4\u05dc \u05ea\u05d7\u05ea \u05e8\u05d9\u05e9\u05d9\u05d5\u05df MIT, \u05e9\u05ea\u05d5\u05db\u05dc \u05dc\u05e7\u05e8\u05d5\u05d0 \u05e2\u05dc\u05d9\u05d5 \u05d9\u05d5\u05ea\u05e8 \u05d1\u05d4\u05de\u05e9\u05da. \u05d1\u05e4\u05e9\u05d8\u05d5\u05ea, \u05de\u05d5\u05ea\u05e8 \u05dc\u05da \u05dc\u05d4\u05e9\u05ea\u05de\u05e9 \u05d1\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8 \u05d1\u05db\u05dc \u05de\u05e7\u05d5\u05dd \u05d5\u05d1\u05db\u05dc \u05d3\u05e8\u05da \u05d5\u05d1\u05dc\u05d1\u05d3 \u05e9\u05ea\u05e2\u05e0\u05d9\u05e7 \u05e7\u05e8\u05d3\u05d9\u05d8 \u05dc\u05de\u05d7\u05d1\u05e8 \u05d4\u05de\u05e7\u05d5\u05e8\u05d9.","buttons":{"mitLicense":"MIT \u05e8\u05e9\u05d9\u05d5\u05df"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"\u05ea\u05d5\u05d3\u05d4 \u05e9\u05d0\u05ea\u05d4 \u05de\u05e9\u05ea\u05de\u05e9 \u05d1Reactive Resume"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"{{- heading}} \u091c\u094b\u0921\u093c\u0947\u0902","startDate":{"label":"\u092a\u094d\u0930\u093e\u0930\u0902\u092d \u0924\u093f\u0925\u093f"},"endDate":{"label":"\u0905\u0902\u0924\u093f\u092e \u0924\u093f\u0925\u093f"},"description":{"label":"\u0935\u093f\u0935\u0930\u0923"}},"buttons":{"add":{"label":"\u091c\u094b\u0921\u093c\u0928\u093e"},"delete":{"label":"\u0939\u091f\u093e\u090f\u0901"}},"printDialog":{"heading":"\u0905\u092a\u0928\u093e \u0930\u093f\u091c\u094d\u092f\u0942\u092e\u0947 \u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u0915\u0930\u0947\u0902","quality":{"label":"\u0917\u0941\u0923\u0935\u0924\u094d\u0924\u093e \u092e\u0942\u0932\u094d\u092f"},"printType":{"label":"\u092a\u094d\u0930\u0915\u093e\u0930","types":{"unconstrained":"\u0938\u094d\u0935\u0947\u091a\u094d\u091b\u093e\u092a\u0942\u0930\u094d\u0923","fitInA4":"A4 \u092e\u0947\u0902 \u092b\u093f\u091f","multiPageA4":"\u092c\u0939\u0941 \u092a\u0943\u0937\u094d\u0920 A4"}},"helpText":["\u092f\u0939 \u0928\u093f\u0930\u094d\u092f\u093e\u0924 \u0935\u093f\u0927\u093f \u0906\u092a\u0915\u0940 CV \u0915\u094b \u0906\u092a\u0915\u0947 \u0938\u0940\u0935\u0940 \u0915\u094b \u092c\u0926\u0932\u0928\u0947 \u0914\u0930 PDF \u092a\u0930 \u092a\u094d\u0930\u093f\u0902\u091f \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f HTML \u0915\u0948\u0928\u0935\u093e\u0938 \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0924\u0940 \u0939\u0948, \u091c\u093f\u0938\u0915\u093e \u0905\u0930\u094d\u0925 \u0939\u0948 \u0915\u093f \u092f\u0939 \u0938\u092d\u0940 \u091a\u092f\u0928 / \u092a\u093e\u0930\u094d\u0938\u093f\u0902\u0917 \u0915\u094d\u0937\u092e\u0924\u093e\u0913\u0902 \u0915\u094b \u0916\u094b \u0926\u0947\u0917\u093e\u0964","\u092f\u0926\u093f \u0906\u092a\u0915\u0947 \u0932\u093f\u090f \u092f\u0939 \u092e\u0939\u0924\u094d\u0935\u092a\u0942\u0930\u094d\u0923 \u0939\u0948, \u0924\u094b \u0915\u0943\u092a\u092f\u093e Cmd / Ctrl + P \u092f\u093e \u0928\u0940\u091a\u0947 \u0926\u093f\u090f \u0917\u090f \u092a\u094d\u0930\u093f\u0902\u091f \u092c\u091f\u0928 \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0928\u0947 \u0915\u0947 \u092c\u091c\u093e\u092f CV \u0915\u094b \u092a\u094d\u0930\u093f\u0902\u091f \u0915\u0930\u0928\u0947 \u0915\u093e \u092a\u094d\u0930\u092f\u093e\u0938 \u0915\u0930\u0947\u0902\u0964 \u0928\u0924\u0940\u091c\u093e \u0905\u0932\u0917-\u0905\u0932\u0917 \u0939\u094b \u0938\u0915\u0924\u093e \u0939\u0948 \u0915\u094d\u092f\u094b\u0902\u0915\u093f \u0906\u0909\u091f\u092a\u0941\u091f \u092c\u094d\u0930\u093e\u0909\u091c\u093c\u0930 \u092a\u0930 \u0928\u093f\u0930\u094d\u092d\u0930 \u0939\u0948, \u0932\u0947\u0915\u093f\u0928 \u092f\u0939 Google \u0915\u094d\u0930\u094b\u092e \u0915\u0947 \u0928\u0935\u0940\u0928\u0924\u092e \u0938\u0902\u0938\u094d\u0915\u0930\u0923 \u092a\u0930 \u0938\u092c\u0938\u0947 \u0905\u091a\u094d\u091b\u093e \u0915\u093e\u092e \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u091c\u093e\u0928\u093e \u091c\u093e\u0924\u093e \u0939\u0948\u0964"],"buttons":{"cancel":"\u0930\u0926\u094d\u0926 \u0915\u0930\u0947\u0902","saveAsPdf":"\u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u092a\u0940\u0921\u0940\u090d\u092b\u093c"}},"panZoomAnimation":{"helpText":"\u0905\u092a\u0928\u0947 \u0930\u093f\u091c\u094d\u092f\u0942\u092e\u0947 \u0915\u094b \u0915\u0930\u0940\u092c \u0938\u0947 \u091c\u093e\u0928\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u0906\u092a \u0915\u093f\u0938\u0940 \u092d\u0940 \u0938\u092e\u092f \u0906\u0930\u094d\u091f\u092c\u094b\u0930\u094d\u0921 \u092a\u0930 \u092a\u0948\u0928 \u0914\u0930 \u091c\u0942\u092e \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964"},"markdownHelpText":"\u0906\u092a \u092a\u093e\u0920 \u0915\u0947 \u0907\u0938 \u0916\u0902\u0921 \u0915\u094b \u0938\u094d\u091f\u093e\u0907\u0932 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f <1>GitHub Flavoured Markdown</1> \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964"}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u092b\u094b\u091f\u094b \u0932\u093f\u0902\u0915"},"firstName":{"label":"\u092a\u0939\u0932\u093e \u0928\u093e\u092e"},"lastName":{"label":"\u0909\u092a\u0928\u093e\u092e"},"subtitle":{"label":"\u0909\u092a\u0936\u0940\u0930\u094d\u0937\u0915"},"address":{"label":"\u092a\u0924\u093e","line1":{"label":"\u092a\u0924\u093e \u092a\u0902\u0915\u094d\u0924\u093f 1"},"line2":{"label":"\u092a\u0924\u093e \u092a\u0902\u0915\u094d\u0924\u093f 2"},"line3":{"label":"\u092a\u0924\u093e \u092a\u0902\u0915\u094d\u0924\u093f 3"}},"phone":{"label":"\u092b\u094b\u0928 \u0928\u0902\u092c\u0930"},"website":{"label":"\u0935\u0947\u092c\u0938\u093e\u0907\u091f"},"email":{"label":"\u0908\u092e\u0947\u0932 \u092a\u0924\u093e"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0932\u0915\u094d\u0937\u094d\u092f"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0928\u093e\u092e"},"role":{"label":"\u092d\u0942\u092e\u093f\u0915\u093e"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0928\u093e\u092e"},"major":{"label":"\u0935\u093f\u0937\u092f"},"grade":{"label":"\u0917\u094d\u0930\u0947\u0921"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0936\u0940\u0930\u094d\u0937\u0915"},"subtitle":{"label":"\u0909\u092a\u0936\u0940\u0930\u094d\u0937\u0915"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0928\u093e\u092e"},"subtitle":{"label":"\u0905\u0927\u093f\u0915\u093e\u0930"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0928\u093e\u092e"},"level":{"label":"\u0938\u094d\u0924\u0930"},"rating":{"label":"\u0930\u0947\u091f\u093f\u0902\u0917"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0928\u093e\u092e"},"position":{"label":"\u092a\u0926/\u0938\u094d\u0925\u093e\u0928"},"phone":{"label":"\u092b\u094b\u0928 \u0928\u0902\u092c\u0930"},"email":{"label":"\u0908\u092e\u0947\u0932 \u092a\u0924\u093e"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u092e\u094c\u0932\u093f\u0915"},"value":{"label":"\u092e\u0942\u0932\u094d\u092f"}}')},function(e){e.exports=JSON.parse('{"title":"\u091f\u0947\u092e\u094d\u092a\u0932\u0947\u091f\u094d\u0938"}')},function(e){e.exports=JSON.parse('{"title":"\u0930\u0902\u0917","colorOptions":"\u0930\u0902\u0917 \u0935\u093f\u0915\u0932\u094d\u092a","primaryColor":"\u092a\u094d\u0930\u093e\u0925\u092e\u093f\u0915 \u0930\u0902\u0917","accentColor":"\u0926\u094d\u0935\u093f\u0924\u0940\u092f\u0915 \u0930\u0902\u0917","clipboardCopyAction":"{{color}} \u0915\u094b \u0915\u094d\u0932\u093f\u092a\u092c\u094b\u0930\u094d\u0921 \u092a\u0930 \u0915\u0949\u092a\u0940 \u0915\u093f\u092f\u093e \u0917\u092f\u093e \u0939\u0948\u0964"}')},function(e){e.exports=JSON.parse('{"title":"\u092b\u094b\u0902\u091f\u094d\u0938","fontFamily":{"label":"\u092b\u093c\u0949\u0928\u094d\u091f \u092a\u0930\u093f\u0935\u093e\u0930","helpText":"\u0906\u092a \u0905\u092a\u0928\u0947 \u0938\u093f\u0938\u094d\u091f\u092e \u092a\u0930 \u0938\u094d\u0925\u093e\u092a\u093f\u0924 \u0915\u093f\u0938\u0940 \u092d\u0940 \u092b\u093c\u0949\u0928\u094d\u091f \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u092c\u0938 \u092f\u0939\u093e\u0902 \u092b\u093c\u0949\u0928\u094d\u091f \u0915\u093e \u0928\u093e\u092e \u0926\u0930\u094d\u091c \u0915\u0930\u0947\u0902 \u0914\u0930 \u092c\u094d\u0930\u093e\u0909\u091c\u093c\u0930 \u0907\u0938\u0947 \u0906\u092a\u0915\u0947 \u0932\u093f\u090f \u0932\u094b\u0921 \u0915\u0930\u0947\u0917\u093e\u0964"}}')},function(e){e.exports=JSON.parse('{"title":"\u0915\u093e\u0930\u094d\u0930\u0935\u093e\u0908","disclaimer":"\u0906\u092a\u0915\u0947 \u092c\u093e\u092f\u094b\u0921\u093e\u091f\u093e \u092e\u0947\u0902 \u0906\u092a\u0915\u0947 \u0926\u094d\u0935\u093e\u0930\u093e \u0915\u093f\u090f \u0917\u090f \u092a\u0930\u093f\u0935\u0930\u094d\u0924\u0928 \u0938\u094d\u0935\u091a\u093e\u0932\u093f\u0924 \u0930\u0942\u092a \u0938\u0947 \u0906\u092a\u0915\u0947 \u092c\u094d\u0930\u093e\u0909\u091c\u093c\u0930 \u0915\u0947 \u0938\u094d\u0925\u093e\u0928\u0940\u092f \u092d\u0902\u0921\u093e\u0930\u0923 \u092e\u0947\u0902 \u0938\u0939\u0947\u091c\u0947 \u091c\u093e\u0924\u0947 \u0939\u0948\u0902\u0964 \u0915\u094b\u0908 \u0921\u0947\u091f\u093e \u0928\u0939\u0940\u0902 \u0928\u093f\u0915\u0932\u0924\u093e \u0939\u0948, \u0907\u0938\u0932\u093f\u090f \u0906\u092a\u0915\u0940 \u091c\u093e\u0928\u0915\u093e\u0930\u0940 \u092a\u0942\u0930\u0940 \u0924\u0930\u0939 \u0938\u0947 \u0938\u0941\u0930\u0915\u094d\u0937\u093f\u0924 \u0939\u0948\u0964","importExport":{"heading":"\u0906\u092f\u093e\u0924 / \u0928\u093f\u0930\u094d\u092f\u093e\u0924","body":"\u0906\u092a JSON \u092a\u094d\u0930\u093e\u0930\u0942\u092a \u092e\u0947\u0902 \u0905\u092a\u0928\u093e \u0921\u0947\u091f\u093e \u0906\u092f\u093e\u0924 \u092f\u093e \u0928\u093f\u0930\u094d\u092f\u093e\u0924 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u0907\u0938\u0915\u0947 \u0938\u093e\u0925, \u0906\u092a \u0915\u093f\u0938\u0940 \u092d\u0940 \u0921\u093f\u0935\u093e\u0907\u0938 \u0938\u0947 \u0905\u092a\u0928\u093e \u092c\u093e\u092f\u094b\u0921\u093e\u091f\u093e \u0938\u0902\u092a\u093e\u0926\u093f\u0924 \u0914\u0930 \u092a\u094d\u0930\u093f\u0902\u091f \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u092c\u093e\u0926 \u092e\u0947\u0902 \u0909\u092a\u092f\u094b\u0917 \u0915\u0947 \u0932\u093f\u090f \u0907\u0938 \u092b\u093e\u0907\u0932 \u0915\u094b \u0938\u0947\u0935 \u0915\u0930\u0947\u0902\u0964","buttons":{"import":"\u0906\u092f\u093e\u0924","export":"\u0928\u093f\u0930\u094d\u092f\u093e\u0924"}},"downloadResume":{"heading":"\u0905\u092a\u0928\u093e \u0930\u093f\u091c\u094d\u092f\u0942\u092e\u0947 \u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u0915\u0930\u0947\u0902","body":"\u0906\u092a \u0905\u092a\u0928\u0947 \u0930\u0947\u091c\u093c\u094d\u092f\u0942\u092e\u0947 \u0915\u093e \u092a\u0940\u0921\u0940\u090f\u092b \u0924\u0941\u0930\u0902\u0924 \u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u0928\u0940\u091a\u0947 \u0926\u093f\u090f \u0917\u090f \u092c\u091f\u0928 \u092a\u0930 \u0915\u094d\u0932\u093f\u0915 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u0938\u0930\u094d\u0935\u094b\u0924\u094d\u0924\u092e \u092a\u0930\u093f\u0923\u093e\u092e\u094b\u0902 \u0915\u0947 \u0932\u093f\u090f, \u0915\u0943\u092a\u092f\u093e \u0928\u0935\u0940\u0928\u0924\u092e Google Chrome \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0947\u0902\u0964","buttons":{"saveAsPdf":"\u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u092a\u0940\u0921\u0940\u090d\u092b\u093c"}},"loadDemoData":{"heading":"\u0921\u0947\u092e\u094b \u0921\u0947\u091f\u093e \u0932\u094b\u0921 \u0915\u0930\u0947\u0902","body":"\u090f\u0915 \u0924\u093e\u091c\u093e \u0930\u093f\u0915\u094d\u0924 \u092a\u0943\u0937\u094d\u0920 \u0915\u0947 \u0938\u093e\u0925 \u0915\u094d\u092f\u093e \u0915\u0930\u0928\u093e \u0939\u0948, \u0907\u0938 \u092a\u0930 \u0905\u0938\u094d\u092a\u0937\u094d\u091f? \u092c\u093e\u092f\u094b\u0921\u093e\u091f\u093e \u0915\u0948\u0938\u0947 \u0926\u093f\u0916\u0928\u093e \u091a\u093e\u0939\u093f\u090f \u092f\u0939 \u0926\u0947\u0916\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u092a\u0942\u0930\u094d\u0935 \u0928\u093f\u0930\u094d\u0927\u093e\u0930\u093f\u0924 \u092e\u0942\u0932\u094d\u092f\u094b\u0902 \u0915\u0947 \u0938\u093e\u0925 \u0915\u0941\u091b \u0921\u0947\u091f\u093e \u0932\u094b\u0921 \u0915\u0930\u0947\u0902 \u0914\u0930 \u0906\u092a \u0935\u0939\u093e\u0902 \u0938\u0947 \u0938\u0902\u092a\u093e\u0926\u0928 \u0936\u0941\u0930\u0942 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964","buttons":{"loadData":"\u0932\u094b\u0921 \u0921\u0947\u091f\u093e"}},"reset":{"heading":"\u0938\u092c \u0915\u0941\u091b \u0930\u0940\u0938\u0947\u091f \u0915\u0930\u0947\u0902","body":"\u092f\u0939 \u0915\u094d\u0930\u093f\u092f\u093e \u0906\u092a\u0915\u0947 \u0938\u092d\u0940 \u0921\u0947\u091f\u093e \u0915\u094b \u0930\u0940\u0938\u0947\u091f \u0915\u0930 \u0926\u0947\u0917\u0940 \u0914\u0930 \u0938\u093e\u0925 \u0939\u0940 \u0906\u092a\u0915\u0947 \u092c\u094d\u0930\u093e\u0909\u091c\u093c\u0930 \u0915\u0947 \u0938\u094d\u0925\u093e\u0928\u0940\u092f \u0938\u0902\u0917\u094d\u0930\u0939\u0923 \u092e\u0947\u0902 \u0915\u093f\u090f \u0917\u090f \u092c\u0948\u0915\u0905\u092a \u0915\u094b \u0939\u091f\u093e \u0926\u0947\u0917\u0940, \u0907\u0938\u0932\u093f\u090f \u0915\u0943\u092a\u092f\u093e \u0938\u0941\u0928\u093f\u0936\u094d\u091a\u093f\u0924 \u0915\u0930\u0947\u0902 \u0915\u093f \u0906\u092a\u0928\u0947 \u0938\u092c \u0915\u0941\u091b \u0930\u0940\u0938\u0947\u091f \u0915\u0930\u0928\u0947 \u0938\u0947 \u092a\u0939\u0932\u0947 \u0905\u092a\u0928\u0940 \u091c\u093e\u0928\u0915\u093e\u0930\u0940 \u0928\u093f\u0930\u094d\u092f\u093e\u0924 \u0915\u0930 \u0926\u0940 \u0939\u0948\u0964","buttons":{"reset":"\u0930\u0940\u0938\u0947\u091f"}}}')},function(e){e.exports=JSON.parse('{"title":"\u0938\u0947\u091f\u093f\u0902\u0917\u094d\u0938","language":{"label":"\u092d\u093e\u0937\u093e\xa0","helpText":"\u092f\u0926\u093f \u0906\u092a \u090f\u092a\u094d\u0932\u093f\u0915\u0947\u0936\u0928 \u0915\u094b \u0905\u092a\u0928\u0940 \u092d\u093e\u0937\u093e \u092e\u0947\u0902 \u0905\u0928\u0941\u0935\u093e\u0926 \u0915\u0930\u0928\u0947 \u092e\u0947\u0902 \u092e\u0926\u0926 \u0915\u0930\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u0948\u0902, \u0924\u094b \u0915\u0943\u092a\u092f\u093e <1>\u0905\u0928\u0941\u0935\u093e\u0926 \u0926\u0938\u094d\u0924\u093e\u0935\u0947\u091c\u093c</1> \u0926\u0947\u0916\u0947\u0902\u0964"}}')},function(e){e.exports=JSON.parse('{"title":"\u0939\u092e\u093e\u0930\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902","documentation":{"heading":"\u092a\u094d\u0930\u0932\u0947\u0916\u0928","body":"\u090f\u092a\u094d\u0932\u093f\u0915\u0947\u0936\u0928 \u0915\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902 \u0905\u0927\u093f\u0915 \u091c\u093e\u0928\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u0948\u0902? \u0910\u092a \u092e\u0947\u0902 \u092f\u094b\u0917\u0926\u093e\u0928 \u0915\u0930\u0928\u0947 \u0915\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902 \u091c\u093e\u0928\u0915\u093e\u0930\u0940 \u091a\u093e\u0939\u093f\u090f? \u0906\u0917\u0947 \u0928\u0939\u0940\u0902, \u092c\u0938 \u0906\u092a\u0915\u0947 \u0932\u093f\u090f \u090f\u0915 \u0935\u094d\u092f\u093e\u092a\u0915 \u0917\u093e\u0907\u0921 \u092c\u0928\u093e\u092f\u093e \u0917\u092f\u093e \u0939\u0948\u0964","buttons":{"documentation":"\u092a\u094d\u0930\u0932\u0947\u0916\u0928"}},"bugOrFeatureRequest":{"heading":"\u0938\u0902\u0915\u091f? \u0928\u092f\u0940 \u0935\u093f\u0936\u0947\u0937\u0924\u093e?","body":"\u092c\u093e\u092f\u094b\u0921\u093e\u091f\u093e \u092c\u0928\u093e\u0928\u0947 \u0938\u0947 \u0906\u092a\u0915\u0940 \u092a\u094d\u0930\u0917\u0924\u093f \u0930\u0941\u0915 \u0930\u0939\u0940 \u0939\u0948? \u0910\u0938\u0940 \u0938\u092e\u0938\u094d\u092f\u093e \u092e\u093f\u0932\u0940 \u091c\u094b \u0926\u0942\u0930 \u0928\u0939\u0940\u0902 \u0939\u094b\u0917\u0940? \\"\u0917\u093f\u091f\u0939\u092c \u092e\u0941\u0926\u094d\u0926\u0947\\" \u0905\u0928\u0941\u092d\u093e\u0917 \u092a\u0930 \u0907\u0938\u0915\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902 \u0938\u0942\u091a\u093f\u0924 \u0915\u0930\u0947\u0902, \u092f\u093e \u0928\u0940\u091a\u0947 \u0915\u0940 \u0915\u094d\u0930\u093f\u092f\u093e\u0913\u0902 \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0915\u0947 \u092e\u0941\u091d\u0947 \u090f\u0915 \u0908\u092e\u0947\u0932 \u092d\u0947\u091c\u0947\u0902\u0964","buttons":{"raiseIssue":"\u0930\u093f\u092a\u094b\u0930\u094d\u091f \u092e\u0947\u0902 \u0938\u092e\u0938\u094d\u092f\u093e","sendEmail":"\u0908\u092e\u0947\u0932 \u092d\u0947\u091c\u0947\u0902"}},"sourceCode":{"heading":"\u0938\u094b\u0930\u094d\u0938 \u0915\u094b\u0921","body":"\u0916\u0930\u094b\u0902\u091a \u0938\u0947 \u092a\u0930\u093f\u092f\u094b\u091c\u0928\u093e \u0915\u093e \u0928\u093f\u0930\u094d\u092e\u093e\u0923 \u0915\u0930\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u0948\u0902? \u0915\u094d\u092f\u093e \u0906\u092a \u0907\u0938 \u092a\u094d\u0930\u094b\u091c\u0947\u0915\u094d\u091f \u0915\u0947 \u0913\u092a\u0928-\u0938\u094b\u0930\u094d\u0938 \u0935\u093f\u0915\u093e\u0938 \u092e\u0947\u0902 \u092f\u094b\u0917\u0926\u093e\u0928 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u0924\u0948\u092f\u093e\u0930 \u0939\u0948\u0902? \u0928\u0940\u091a\u0947 \u0926\u093f\u090f \u0917\u090f \u092c\u091f\u0928 \u092a\u0930 \u0915\u094d\u0932\u093f\u0915 \u0915\u0930\u0947\u0902\u0964","buttons":{"githubRepo":"GitHub \u0930\u0947\u092a\u094b"}},"license":{"heading":"\u0932\u093e\u0907\u0938\u0947\u0902\u0938 \u0915\u0940 \u091c\u093e\u0928\u0915\u093e\u0930\u0940","body":"\u092a\u0930\u093f\u092f\u094b\u091c\u0928\u093e \u090f\u092e\u0906\u0908\u091f\u0940 \u0932\u093e\u0907\u0938\u0947\u0902\u0938 \u0915\u0947 \u0924\u0939\u0924 \u0936\u093e\u0938\u093f\u0924 \u0939\u0948, \u091c\u093f\u0938\u0947 \u0906\u092a \u0928\u0940\u091a\u0947 \u0915\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902 \u0905\u0927\u093f\u0915 \u092a\u0922\u093c \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u0906\u092a\u0915\u094b \u092a\u0930\u093f\u092f\u094b\u091c\u0928\u093e \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0928\u0947 \u0915\u0940 \u0905\u0928\u0941\u092e\u0924\u093f \u0939\u0948 \u0915\u0939\u0940\u0902 \u092d\u0940 \u0906\u092a \u092e\u0942\u0932 \u0932\u0947\u0916\u0915 \u0915\u094b \u0915\u094d\u0930\u0947\u0921\u093f\u091f \u0926\u0947\u0924\u0947 \u0939\u0948\u0902\u0964","buttons":{"mitLicense":"MIT \u0932\u093e\u0907\u0938\u0947\u0928\u094d\u0938"}},"footer":{"credit":"<1>\u0905\u092e\u0943\u0924 \u092a\u093f\u0932\u094d\u0932\u0908</1> \u0926\u094d\u0935\u093e\u0930\u093e \u092a\u094d\u092f\u093e\u0930 \u0938\u0947 \u092c\u0928\u093e\u092f\u093e \u0917\u092f\u093e","thanks":"\u0930\u093f\u090f\u0915\u094d\u091f\u093f\u0935 \u0930\u093f\u091c\u094d\u092f\u0942\u092e\u0947 \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u0927\u0928\u094d\u092f\u0935\u093e\u0926"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Aggiungi {{- heading}}","startDate":{"label":"Data d\'inizio"},"endDate":{"label":"Data di fine"},"description":{"label":"Descrizione"}},"buttons":{"add":{"label":"Aggiungi"},"delete":{"label":"Elimina"}},"printDialog":{"heading":"Scarica il tuo curriculum","quality":{"label":"Qualit\xe0"},"printType":{"label":"Tipo","types":{"unconstrained":"Libero","fitInA4":"Adatta ad A4","multiPageA4":"Multi-Pagina A4"}},"helpText":["Questo metodo di esportazione fa uso di HTML canvas per convertire il curriculum in un\'immagine e stamparla in PDF, ci\xf2 significa che perder\xe0 tutte le capacit\xe0 di selezione/analisi.","Se questo \xe8 importante per te, prova a stampare il curriculum utilizzando Cmd/Ctrl + P o il pulsante di stampa qui sotto. Il risultato pu\xf2 variare in quanto l\'output \xe8 dipendente dal browser ma \xe8 noto che funziona meglio sull\'ultima versione di Google Chrome."],"buttons":{"cancel":"Annulla","saveAsPdf":"Salva come PDF"}},"panZoomAnimation":{"helpText":"Puoi ruotare e ingrandire l\'immagine in qualsiasi momento per dare un\'occhiata pi\xf9 da vicino al tuo curriculum."},"markdownHelpText":"Puoi utilizzare <1>GitHub Flavored Markdown</1> per personalizzare questa sezione del testo."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL della foto"},"firstName":{"label":"Nome"},"lastName":{"label":"Cognome"},"subtitle":{"label":"Sottotitolo"},"address":{"label":"Indirizzo","line1":{"label":"Indirizzo, 1a riga"},"line2":{"label":"Indirizzo, 2a riga"},"line3":{"label":"Indirizzo, 3a riga"}},"phone":{"label":"Numero di telefono"},"website":{"label":"Sito web"},"email":{"label":"Indirizzo email"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Obbiettivo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"role":{"label":"Ruolo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"major":{"label":"Grande"},"grade":{"label":"Voto"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titolo"},"subtitle":{"label":"Sottotitolo"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nome"},"subtitle":{"label":"Autorit\xe0"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nome"},"level":{"label":"Livello"},"rating":{"label":"Valutazione"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"position":{"label":"Posizione"},"phone":{"label":"Numero di telefono"},"email":{"label":"Indirizzo email"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Chiave"},"value":{"label":"Valore"}}')},function(e){e.exports=JSON.parse('{"title":"Modelli"}')},function(e){e.exports=JSON.parse('{"title":"Colori","colorOptions":"Opzioni di colore","primaryColor":"Colore primario","accentColor":"Colore secondario","clipboardCopyAction":"Il {{color}} \xe8 stato copiato negli appunti."}')},function(e){e.exports=JSON.parse('{"title":"Tipo di carattere","fontFamily":{"label":"Famiglia di caratteri","helpText":"Puoi usare anche tutti i caratteri installati sul tuo sistema. Basta inserire il nome della famiglia di caratteri qui e il browser lo caricher\xe0 per te."}}')},function(e){e.exports=JSON.parse('{"title":"Azioni","disclaimer":"Le modifiche apportate al curriculum vengono salvate automaticamente sulla memoria locale del tuo browser. Nessun dato viene visualizzato, perci\xf2 le tue informazioni sono completamente sicure.","importExport":{"heading":"Importa/Esporta","body":"Puoi importare o esportare i tuoi dati in formato JSON. Con questo, puoi modificare e stampare il tuo curriculum da qualsiasi dispositivo. Salva questo file per utilizzarlo in seguito.","buttons":{"import":"Importa","export":"Esporta"}},"downloadResume":{"heading":"Scarica il tuo curriculum","body":"Puoi cliccare sul pulsante qui sotto per scaricare una versione PDF del tuo curriculum. Per risultati ottimali, si prega di utilizzare l\'ultima versione di Google Chrome.","buttons":{"saveAsPdf":"Salva come PDF"}},"loadDemoData":{"heading":"Carica dati demo","body":"Non \xe8 chiaro su cosa fare con una nuova pagina vuota? Carica alcuni dati demo con valori preimpostati per vedere come dovrebbe apparire un curriculum e puoi iniziare a modificarlo da l\xec.","buttons":{"loadData":"Carica dati"}},"reset":{"heading":"Resetta tutto!","body":"Questa azione resetter\xe0 tutti i tuoi dati e rimuover\xe0 anche i backup effettuati sullo spazio locale del tuo browser quindi assicurati di aver esportato le tue informazioni prima di resettare tutto.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Impostazioni","language":{"label":"Lingua","helpText":"Se vuoi aiutare a tradurre l\'app nella tua lingua, fai riferimento alla <1>Documentazione di traduzione</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Informazioni","documentation":{"heading":"Documentazione","body":"Vuoi saperne di pi\xf9 sull\'app? Hai bisogno di informazioni su come contribuire al progetto? Non guardare oltre, c\'\xe8 una guida completa fatta solo per te.","buttons":{"documentation":"Documentazione"}},"bugOrFeatureRequest":{"heading":"Bug? Richiesta di nuove funzionalit\xe0?","body":"Qualcosa ti blocca mentre crei il tuo curriculum? Hai trovato un bug fastidioso che non scompare? Parlane nella sezione GitHub Issues o mandami un\'email utilizzando le azioni qui sotto.","buttons":{"raiseIssue":"Crea una segnalazione","sendEmail":"Manda un\'email"}},"sourceCode":{"heading":"Codice sorgente","body":"Vuoi eseguire il progetto dal suo codice sorgente? Sei uno sviluppatore disposto a contribuire allo sviluppo open-source di questo progetto? Clicca il pulsante qui sotto.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"Informazioni sulla licenza","body":"Il progetto \xe8 sottoposto alla licenza MIT, che puoi leggere di pi\xf9 su di sotto. Fondamentalmente, \xe8 consentito utilizzare il progetto ovunque purch\xe9 dia crediti all\'autore originale.","buttons":{"mitLicense":"Licenza MIT"}},"footer":{"credit":"Realizzato con amore da <1>Amruth Pillai</1>","thanks":"Grazie per aver utilizzato Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"{{- heading}} \u0cb8\u0cc7\u0cb0\u0cbf\u0cb8\u0cbf","startDate":{"label":"\u0caa\u0ccd\u0cb0\u0cbe\u0cb0\u0c82\u0cad \u0ca6\u0cbf\u0ca8\u0cbe\u0c82\u0c95"},"endDate":{"label":"\u0c85\u0c82\u0ca4\u0cbf\u0cae \u0ca6\u0cbf\u0ca8\u0cbe\u0c82\u0c95"},"description":{"label":"\u0cb5\u0cbf\u0cb5\u0cb0\u0ca3\u0cc6"}},"buttons":{"add":{"label":"\u0cb8\u0cc7\u0cb0\u0cbf\u0cb8\u0cbf"},"delete":{"label":"\u0c85\u0cb3\u0cbf\u0cb8\u0cbf"}},"printDialog":{"heading":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb0\u0cc7\u0cb8\u0cc1\u0cae\u0cc7 \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf","quality":{"label":"\u0c97\u0cc1\u0ca3\u0cae\u0c9f\u0ccd\u0c9f\u0ca6 \u0cae\u0ccc\u0cb2\u0ccd\u0caf"},"printType":{"label":"\u0cb5\u0cbf\u0ca7","types":{"unconstrained":"\u0ca8\u0cbf\u0cb0\u0ccd\u0cac\u0c82\u0ca7\u0cbf\u0cb8\u0ca6","fitInA4":"\u0c8e4 \u0ca8\u0cb2\u0ccd\u0cb2\u0cbf \u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6","multiPageA4":"\u0cac\u0cb9\u0cc1 \u0caa\u0cc1\u0c9f \u0c8e4"}},"helpText":["\u0c88 \u0cb5\u0cbf\u0ca7\u0cbe\u0ca8\u0cb5\u0cc1 \u0c8e\u0c9a\u0ccd\u200c\u0c9f\u0cbf\u0c8e\u0cae\u0ccd\u0c8e\u0cb2\u0ccd \u0c95\u0ccd\u0caf\u0cbe\u0ca8\u0ccd\u0cb5\u0cbe\u0cb8\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb0\u0cc7\u0cb8\u0ccd\u0caf\u0cc1\u0caf\u0cc1\u0cae\u0cc7\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0c9a\u0cbf\u0ca4\u0ccd\u0cb0\u0cb5\u0ca8\u0ccd\u0ca8\u0cbe\u0c97\u0cbf \u0caa\u0cb0\u0cbf\u0cb5\u0cb0\u0ccd\u0ca4\u0cbf\u0cb8\u0cb2\u0cc1 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0c85\u0ca6\u0ca8\u0ccd\u0ca8\u0cc1 \u0caa\u0cbf\u0ca1\u0cbf\u0c8e\u0cab\u0ccd\u200c\u0ca8\u0cb2\u0ccd\u0cb2\u0cbf \u0cae\u0cc1\u0ca6\u0ccd\u0cb0\u0cbf\u0cb8\u0cb2\u0cc1 \u0cac\u0cb3\u0cb8\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6, \u0c85\u0c82\u0ca6\u0cb0\u0cc6 \u0c87\u0ca6\u0cc1 \u0c8e\u0cb2\u0ccd\u0cb2\u0cbe \u0c86\u0caf\u0ccd\u0c95\u0cc6 / \u0caa\u0cbe\u0cb0\u0ccd\u0cb8\u0cbf\u0c82\u0c97\u0ccd \u0cb8\u0cbe\u0cae\u0cb0\u0ccd\u0ca5\u0ccd\u0caf\u0c97\u0cb3\u0ca8\u0ccd\u0ca8\u0cc1 \u0c95\u0cb3\u0cc6\u0ca6\u0cc1\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6.","\u0c85\u0ca6\u0cc1 \u0ca8\u0cbf\u0cae\u0c97\u0cc6 \u0cae\u0cc1\u0c96\u0ccd\u0caf\u0cb5\u0cbe\u0c97\u0cbf\u0ca6\u0ccd\u0ca6\u0cb0\u0cc6, \u0ca6\u0caf\u0cb5\u0cbf\u0c9f\u0ccd\u0c9f\u0cc1 Cmd / Ctrl + P \u0c85\u0ca5\u0cb5\u0cbe \u0c95\u0cc6\u0cb3\u0c97\u0cbf\u0ca8 \u0cae\u0cc1\u0ca6\u0ccd\u0cb0\u0ca3 \u0c97\u0cc1\u0c82\u0ca1\u0cbf\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0cac\u0cb3\u0cb8\u0cbf \u0caa\u0cc1\u0ca8\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cae\u0cc1\u0ca6\u0ccd\u0cb0\u0cbf\u0cb8\u0cb2\u0cc1 \u0caa\u0ccd\u0cb0\u0caf\u0ca4\u0ccd\u0ca8\u0cbf\u0cb8\u0cbf. Output \u0c9f\u0ccd\u200c\u0caa\u0cc1\u0c9f\u0ccd \u0cac\u0ccd\u0cb0\u0ccc\u0cb8\u0cb0\u0ccd \u0c85\u0cb5\u0cb2\u0c82\u0cac\u0cbf\u0ca4\u0cb5\u0cbe\u0c97\u0cbf\u0cb0\u0cc1\u0cb5\u0cc1\u0ca6\u0cb0\u0cbf\u0c82\u0ca6 \u0cab\u0cb2\u0cbf\u0ca4\u0cbe\u0c82\u0cb6\u0cb5\u0cc1 \u0cac\u0ca6\u0cb2\u0cbe\u0c97\u0cac\u0cb9\u0cc1\u0ca6\u0cc1, \u0c86\u0ca6\u0cb0\u0cc6 \u0c87\u0ca6\u0cc1 Google Chrome \u0ca8 \u0c87\u0ca4\u0ccd\u0ca4\u0cc0\u0c9a\u0cbf\u0ca8 \u0c86\u0cb5\u0cc3\u0ca4\u0ccd\u0ca4\u0cbf\u0caf\u0cb2\u0ccd\u0cb2\u0cbf \u0c89\u0ca4\u0ccd\u0ca4\u0cae\u0cb5\u0cbe\u0c97\u0cbf \u0c95\u0cbe\u0cb0\u0ccd\u0caf\u0ca8\u0cbf\u0cb0\u0ccd\u0cb5\u0cb9\u0cbf\u0cb8\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6."],"buttons":{"cancel":"\u0ca4\u0ccd\u0caf\u0c9c\u0cbf\u0cb8\u0cbf","saveAsPdf":"\u0caa\u0cbf\u0ca1\u0cbf\u0c8e\u0cab\u0ccd \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf"}},"panZoomAnimation":{"helpText":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb0\u0cc7\u0cb8\u0ccd\u0caf\u0cc1\u0cae\u0cc7\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb9\u0ca4\u0ccd\u0ca4\u0cbf\u0cb0\u0ca6\u0cbf\u0c82\u0ca6 \u0ca8\u0ccb\u0ca1\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0caf\u0cbe\u0cb5\u0cc1\u0ca6\u0cc7 \u0cb8\u0cae\u0caf\u0ca6\u0cb2\u0ccd\u0cb2\u0cbf \u0c86\u0cb0\u0ccd\u0c9f\u0ccd\u200c\u0cac\u0ccb\u0cb0\u0ccd\u0ca1\u0ccd\u200c\u0ca8 \u0cb8\u0cc1\u0ca4\u0ccd\u0ca4\u0cb2\u0cc2 \u0caa\u0ccd\u0caf\u0cbe\u0ca8\u0ccd \u0cae\u0cbe\u0ca1\u0cac\u0cb9\u0cc1\u0ca6\u0cc1 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0c9c\u0cc2\u0cae\u0ccd \u0cae\u0cbe\u0ca1\u0cac\u0cb9\u0cc1\u0ca6\u0cc1."},"markdownHelpText":"\u0caa\u0ca0\u0ccd\u0caf\u0ca6 \u0c88 \u0cb5\u0cbf\u0cad\u0cbe\u0c97\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb5\u0cbf\u0ca8\u0ccd\u0caf\u0cbe\u0cb8\u0c97\u0cca\u0cb3\u0cbf\u0cb8\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 <1>\u0c97\u0cbf\u0c9f\u0ccd\u200c\u0cb9\u0cac\u0ccd \u0cab\u0ccd\u0cb2\u0cc7\u0cb5\u0cb0\u0ccd\u0ca1\u0ccd \u0cae\u0cbe\u0cb0\u0ccd\u0c95\u0ccd\u200c\u0ca1\u0ccc\u0ca8\u0ccd</1> \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0cac\u0cb3\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u0cab\u0ccb\u0c9f\u0ccb URL"},"firstName":{"label":"\u0cae\u0cc6\u0cc2\u0ca6\u0cb2 \u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"lastName":{"label":"\u0c95\u0cc6\u0cc2\u0ca8\u0cc6\u0caf \u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"subtitle":{"label":"\u0c89\u0caa\u0cb6\u0cc0\u0cb0\u0ccd\u0cb7\u0cbf\u0c95\u0cc6"},"address":{"label":"\u0cb5\u0cbf\u0cb3\u0cbe\u0cb8","line1":{"label":"\u0cb5\u0cbf\u0cb3\u0cbe\u0cb8 \u0cb8\u0cbe\u0cb2\u0cc1 1"},"line2":{"label":"\u0cb5\u0cbf\u0cb3\u0cbe\u0cb8 \u0cb8\u0cbe\u0cb2\u0cc1 2"},"line3":{"label":"\u0cb5\u0cbf\u0cb3\u0cbe\u0cb8 \u0cb8\u0cbe\u0cb2\u0cc1 3"}},"phone":{"label":"\u0ca6\u0cc2\u0cb0\u0cb5\u0cbe\u0ca3\u0cbf \u0cb8\u0c82\u0c96\u0ccd\u0caf\u0cc6"},"website":{"label":"\u0c9c\u0cbe\u0cb2\u0ca4\u0cbe\u0ca3"},"email":{"label":"\u0c87\u0cae\u0cc6\u0cd5\u0cb2\u0ccd \u0cb5\u0cbf\u0cb3\u0cbe\u0cb8"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0c89\u0ca6\u0ccd\u0ca6\u0cc7\u0cb6"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"role":{"label":"\u0cb8\u0ccd\u0ca5\u0cbe\u0ca8"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"major":{"label":"\u0c85\u0ca7\u0ccd\u0caf\u0caf\u0ca8"},"grade":{"label":"\u0c97\u0ccd\u0cb0\u0cc7\u0ca1\u0ccd"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0cb6\u0cc0\u0cb0\u0ccd\u0cb7\u0cbf\u0c95\u0cc6"},"subtitle":{"label":"\u0c89\u0caa\u0cb6\u0cc0\u0cb0\u0ccd\u0cb7\u0cbf\u0c95\u0cc6"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"subtitle":{"label":"\u0caa\u0ccd\u0cb0\u0cbe\u0ca7\u0cbf\u0c95\u0cbe\u0cb0"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"level":{"label":"\u0cae\u0c9f\u0ccd\u0c9f"},"rating":{"label":"\u0cb0\u0cc7\u0c9f\u0cbf\u0c82\u0c97\u0ccd"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"position":{"label":"\u0cb8\u0ccd\u0ca5\u0cbe\u0ca8"},"phone":{"label":"\u0ca6\u0cc2\u0cb0\u0cb5\u0cbe\u0ca3\u0cbf \u0cb8\u0c82\u0c96\u0ccd\u0caf\u0cc6"},"email":{"label":"\u0c87\u0cae\u0cc6\u0cd5\u0cb2\u0ccd \u0cb5\u0cbf\u0cb3\u0cbe\u0cb8"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0c95\u0cc0"},"value":{"label":"\u0cae\u0ccc\u0cb2\u0ccd\u0caf"}}')},function(e){e.exports=JSON.parse('{"title":"\u0c9f\u0cc6\u0c82\u0caa\u0ccd\u0cb2\u0cc7\u0c9f\u0ccd\u200c\u0c97\u0cb3\u0cc1"}')},function(e){e.exports=JSON.parse('{"title":"\u0cac\u0ca3\u0ccd\u0ca3\u0c97\u0cb3\u0cc1","colorOptions":"\u0cac\u0ca3\u0ccd\u0ca3 \u0c86\u0caf\u0ccd\u0c95\u0cc6\u0c97\u0cb3\u0cc1","primaryColor":"\u0caa\u0ccd\u0cb0\u0cbe\u0ca5\u0cae\u0cbf\u0c95 \u0cac\u0ca3\u0ccd\u0ca3","accentColor":"\u0ca6\u0ccd\u0cb5\u0cbf\u0ca4\u0cc0\u0caf\u0c95 \u0cac\u0ca3\u0ccd\u0ca3","clipboardCopyAction":"{{color}} \u0cac\u0ca3\u0ccd\u0ca3\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0c95\u0ccd\u0cb2\u0cbf\u0caa\u0ccd\u200c\u0cac\u0ccb\u0cb0\u0ccd\u0ca1\u0ccd\u200c\u0c97\u0cc6 \u0ca8\u0c95\u0cb2\u0cbf\u0cb8\u0cb2\u0cbe\u0c97\u0cbf\u0ca6\u0cc6."}')},function(e){e.exports=JSON.parse('{"title":"\u0cab\u0cbe\u0c82\u0c9f\u0ccd\u200c\u0c97\u0cb3\u0cc1","fontFamily":{"label":"\u0cab\u0cbe\u0c82\u0c9f\u0ccd \u0c95\u0cc1\u0c9f\u0cc1\u0c82\u0cac","helpText":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb8\u0cbf\u0cb8\u0ccd\u0c9f\u0c82\u0ca8\u0cb2\u0ccd\u0cb2\u0cbf \u0cb8\u0ccd\u0ca5\u0cbe\u0caa\u0cbf\u0cb8\u0cb2\u0cbe\u0ca6 \u0caf\u0cbe\u0cb5\u0cc1\u0ca6\u0cc7 \u0cab\u0cbe\u0c82\u0c9f\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0cac\u0cb3\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0c87\u0cb2\u0ccd\u0cb2\u0cbf \u0cab\u0cbe\u0c82\u0c9f\u0ccd\u200c\u0ca8 \u0cb9\u0cc6\u0cb8\u0cb0\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cae\u0cc2\u0ca6\u0cbf\u0cb8\u0cbf \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0cac\u0ccd\u0cb0\u0ccc\u0cb8\u0cb0\u0ccd \u0c85\u0ca6\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cbf\u0cae\u0c97\u0cbe\u0c97\u0cbf \u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6."}}')},function(e){e.exports=JSON.parse('{"title":"\u0c95\u0ccd\u0cb0\u0cbf\u0caf\u0cc6\u0c97\u0cb3\u0cc1","disclaimer":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0caa\u0cc1\u0ca8\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0ca6\u0cb2\u0ccd\u0cb2\u0cbf \u0ca8\u0cc0\u0cb5\u0cc1 \u0cae\u0cbe\u0ca1\u0cbf\u0ca6 \u0cac\u0ca6\u0cb2\u0cbe\u0cb5\u0ca3\u0cc6\u0c97\u0cb3\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cac\u0ccd\u0cb0\u0ccc\u0cb8\u0cb0\u0ccd\u200c\u0ca8 \u0cb8\u0ccd\u0ca5\u0cb3\u0cc0\u0caf \u0cb8\u0c82\u0c97\u0ccd\u0cb0\u0cb9\u0ca3\u0cc6\u0c97\u0cc6 \u0cb8\u0ccd\u0cb5\u0caf\u0c82\u0c9a\u0cbe\u0cb2\u0cbf\u0ca4\u0cb5\u0cbe\u0c97\u0cbf \u0c89\u0cb3\u0cbf\u0cb8\u0cb2\u0cbe\u0c97\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6. \u0caf\u0cbe\u0cb5\u0cc1\u0ca6\u0cc7 \u0ca1\u0cc7\u0c9f\u0cbe \u0cb9\u0cca\u0cb0\u0cac\u0cb0\u0cc1\u0cb5\u0cc1\u0ca6\u0cbf\u0cb2\u0ccd\u0cb2, \u0c86\u0ca6\u0ccd\u0ca6\u0cb0\u0cbf\u0c82\u0ca6 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cae\u0cbe\u0cb9\u0cbf\u0ca4\u0cbf\u0caf\u0cc1 \u0cb8\u0c82\u0caa\u0cc2\u0cb0\u0ccd\u0ca3\u0cb5\u0cbe\u0c97\u0cbf \u0cb8\u0cc1\u0cb0\u0c95\u0ccd\u0cb7\u0cbf\u0ca4\u0cb5\u0cbe\u0c97\u0cbf\u0ca6\u0cc6.","importExport":{"heading":"\u0c86\u0cae\u0ca6\u0cc1 / \u0cb0\u0cab\u0ccd\u0ca4\u0cc1","body":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0ca1\u0cc7\u0c9f\u0cbe\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 JSON \u0cb8\u0ccd\u0cb5\u0cb0\u0cc2\u0caa\u0ca6\u0cb2\u0ccd\u0cb2\u0cbf \u0c86\u0cae\u0ca6\u0cc1 \u0cae\u0cbe\u0ca1\u0cbf\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cac\u0cb9\u0cc1\u0ca6\u0cc1 \u0c85\u0ca5\u0cb5\u0cbe \u0cb0\u0cab\u0ccd\u0ca4\u0cc1 \u0cae\u0cbe\u0ca1\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0c87\u0ca6\u0cb0\u0cca\u0c82\u0ca6\u0cbf\u0c97\u0cc6, \u0ca8\u0cc0\u0cb5\u0cc1 \u0caf\u0cbe\u0cb5\u0cc1\u0ca6\u0cc7 \u0cb8\u0cbe\u0ca7\u0ca8\u0ca6\u0cbf\u0c82\u0ca6 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0caa\u0cc1\u0ca8\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb8\u0c82\u0caa\u0cbe\u0ca6\u0cbf\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0cae\u0cc1\u0ca6\u0ccd\u0cb0\u0cbf\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0ca8\u0c82\u0ca4\u0cb0\u0ca6 \u0cac\u0cb3\u0c95\u0cc6\u0c97\u0cbe\u0c97\u0cbf \u0c88 \u0cab\u0cc8\u0cb2\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0c89\u0cb3\u0cbf\u0cb8\u0cbf.","buttons":{"import":"\u0c86\u0cae\u0ca6\u0cc1","export":"\u0cb0\u0cab\u0ccd\u0ca4\u0cc1"}},"downloadResume":{"heading":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb0\u0cc7\u0cb8\u0cc1\u0cae\u0cc7 \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf","body":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb0\u0cc7\u0cb8\u0cc1\u0cae\u0cc7\u0caf \u0caa\u0cbf\u0ca1\u0cbf\u0c8e\u0cab\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca4\u0c95\u0ccd\u0cb7\u0ca3 \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0c95\u0cc6\u0cb3\u0c97\u0cbf\u0ca8 \u0cac\u0c9f\u0ca8\u0ccd \u0c95\u0ccd\u0cb2\u0cbf\u0c95\u0ccd \u0cae\u0cbe\u0ca1\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0c89\u0ca4\u0ccd\u0ca4\u0cae \u0cab\u0cb2\u0cbf\u0ca4\u0cbe\u0c82\u0cb6\u0c97\u0cb3\u0cbf\u0c97\u0cbe\u0c97\u0cbf, \u0ca6\u0caf\u0cb5\u0cbf\u0c9f\u0ccd\u0c9f\u0cc1 \u0c87\u0ca4\u0ccd\u0ca4\u0cc0\u0c9a\u0cbf\u0ca8 Google Chrome \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0cac\u0cb3\u0cb8\u0cbf.","buttons":{"saveAsPdf":"\u0caa\u0cbf\u0ca1\u0cbf\u0c8e\u0cab\u0ccd \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf"}},"loadDemoData":{"heading":"\u0ca1\u0cc6\u0cae\u0cca \u0ca1\u0cc7\u0c9f\u0cbe\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf","body":"\u0cb9\u0cca\u0cb8 \u0c96\u0cbe\u0cb2\u0cbf \u0caa\u0cc1\u0c9f\u0ca6\u0cca\u0c82\u0ca6\u0cbf\u0c97\u0cc6 \u0c8f\u0ca8\u0cc1 \u0cae\u0cbe\u0ca1\u0cac\u0cc7\u0c95\u0cc6\u0c82\u0ca6\u0cc1 \u0cb8\u0ccd\u0caa\u0cb7\u0ccd\u0c9f\u0cb5\u0cbe\u0c97\u0cbf\u0cb2\u0ccd\u0cb2\u0cb5\u0cc7? \u0cb0\u0cc7\u0cb8\u0ccd\u200c\u0cb8\u0cc1\u0cae\u0cc7 \u0cb9\u0cc7\u0c97\u0cc6 \u0c95\u0cbe\u0ca3\u0cac\u0cc7\u0c95\u0cc1 \u0c8e\u0c82\u0cac\u0cc1\u0ca6\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0ccb\u0ca1\u0cb2\u0cc1 \u0caa\u0cc2\u0cb0\u0ccd\u0cb5\u0cad\u0cbe\u0cb5\u0cbf \u0cae\u0ccc\u0cb2\u0ccd\u0caf\u0c97\u0cb3\u0cca\u0c82\u0ca6\u0cbf\u0c97\u0cc6 \u0c95\u0cc6\u0cb2\u0cb5\u0cc1 \u0ca1\u0cc6\u0cae\u0cca \u0ca1\u0cc7\u0c9f\u0cbe\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0c85\u0cb2\u0ccd\u0cb2\u0cbf\u0c82\u0ca6 \u0cb8\u0c82\u0caa\u0cbe\u0ca6\u0ca8\u0cc6\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0caa\u0ccd\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0cbf\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1.","buttons":{"loadData":"\u0cb2\u0ccb\u0ca1\u0ccd \u0ca1\u0cc7\u0c9f\u0cbe"}},"reset":{"heading":"\u0c8e\u0cb2\u0ccd\u0cb2\u0cb5\u0ca8\u0ccd\u0ca8\u0cc2 \u0cae\u0cb0\u0cc1\u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0cb8\u0cbf!","body":"\u0c88 \u0c95\u0ccd\u0cb0\u0cbf\u0caf\u0cc6\u0caf\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0c8e\u0cb2\u0ccd\u0cb2\u0cbe \u0ca1\u0cc7\u0c9f\u0cbe\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cae\u0cb0\u0cc1\u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0cb8\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cac\u0ccd\u0cb0\u0ccc\u0cb8\u0cb0\u0ccd\u200c\u0ca8 \u0cb8\u0ccd\u0ca5\u0cb3\u0cc0\u0caf \u0cb8\u0c82\u0c97\u0ccd\u0cb0\u0cb9\u0ca3\u0cc6\u0c97\u0cc6 \u0cae\u0cbe\u0ca1\u0cbf\u0ca6 \u0cac\u0ccd\u0caf\u0cbe\u0c95\u0caa\u0ccd\u200c\u0c97\u0cb3\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca4\u0cc6\u0c97\u0cc6\u0ca6\u0cc1\u0cb9\u0cbe\u0c95\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6, \u0c86\u0ca6\u0ccd\u0ca6\u0cb0\u0cbf\u0c82\u0ca6 \u0ca8\u0cc0\u0cb5\u0cc1 \u0c8e\u0cb2\u0ccd\u0cb2\u0cb5\u0ca8\u0ccd\u0ca8\u0cc2 \u0cae\u0cb0\u0cc1\u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0cb8\u0cc1\u0cb5 \u0cae\u0cca\u0ca6\u0cb2\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cae\u0cbe\u0cb9\u0cbf\u0ca4\u0cbf\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb0\u0cab\u0ccd\u0ca4\u0cc1 \u0cae\u0cbe\u0ca1\u0cbf\u0ca6\u0ccd\u0ca6\u0cc0\u0cb0\u0cbf \u0c8e\u0c82\u0ca6\u0cc1 \u0c96\u0c9a\u0cbf\u0ca4\u0caa\u0ca1\u0cbf\u0cb8\u0cbf\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cbf.","buttons":{"reset":"\u0cae\u0cb0\u0cc1\u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0cb8\u0cbf"}}}')},function(e){e.exports=JSON.parse('{"title":"\u0cb8\u0cc6\u0c9f\u0ccd\u0c9f\u0cbf\u0c82\u0c97\u0ccd\u0cb8\u0ccd","language":{"label":"\u0cad\u0cbe\u0cb7\u0cc6","helpText":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb8\u0ccd\u0cb5\u0c82\u0ca4 \u0cad\u0cbe\u0cb7\u0cc6\u0c97\u0cc6 \u0cad\u0cbe\u0cb7\u0cbe\u0c82\u0ca4\u0cb0\u0cbf\u0cb8\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0cb8\u0cb9\u0cbe\u0caf \u0cae\u0cbe\u0ca1\u0cb2\u0cc1 \u0cac\u0caf\u0cb8\u0cbf\u0ca6\u0cb0\u0cc6, \u0ca6\u0caf\u0cb5\u0cbf\u0c9f\u0ccd\u0c9f\u0cc1 <1>\u0c85\u0ca8\u0cc1\u0cb5\u0cbe\u0ca6 \u0ca6\u0cbe\u0c96\u0cb2\u0cc6</1> \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0ccb\u0ca1\u0cbf."}}')},function(e){e.exports=JSON.parse('{"title":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0cac\u0c97\u0ccd\u0c97\u0cc6","documentation":{"heading":"\u0ca6\u0cb8\u0ccd\u0ca4\u0cbe\u0cb5\u0cc7\u0c9c\u0ca8\u0ccd\u0ca8\u0cc1","body":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0cac\u0c97\u0ccd\u0c97\u0cc6 \u0c87\u0ca8\u0ccd\u0ca8\u0cb7\u0ccd\u0c9f\u0cc1 \u0ca4\u0cbf\u0cb3\u0cbf\u0ca6\u0cc1\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cb2\u0cc1 \u0cac\u0caf\u0cb8\u0cc1\u0cb5\u0cbf\u0cb0\u0cbe? \u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd\u200c\u0c97\u0cc6 \u0cb9\u0cc7\u0c97\u0cc6 \u0c95\u0cca\u0ca1\u0cc1\u0c97\u0cc6 \u0ca8\u0cc0\u0ca1\u0cac\u0cc7\u0c95\u0cc1 \u0c8e\u0c82\u0cac\u0cc1\u0ca6\u0cb0 \u0c95\u0cc1\u0cb0\u0cbf\u0ca4\u0cc1 \u0cae\u0cbe\u0cb9\u0cbf\u0ca4\u0cbf \u0cac\u0cc7\u0c95\u0cc7? \u0cae\u0cc1\u0c82\u0ca6\u0cc6 \u0ca8\u0ccb\u0ca1\u0cac\u0cc7\u0ca1\u0cbf, \u0ca8\u0cbf\u0cae\u0c97\u0cbe\u0c97\u0cbf \u0cae\u0cbe\u0ca1\u0cbf\u0ca6 \u0cb8\u0cae\u0c97\u0ccd\u0cb0 \u0cae\u0cbe\u0cb0\u0ccd\u0c97\u0ca6\u0cb0\u0ccd\u0cb6\u0cbf \u0c87\u0ca6\u0cc6.","buttons":{"documentation":"\u0ca6\u0cb8\u0ccd\u0ca4\u0cbe\u0cb5\u0cc7\u0c9c\u0ca8\u0ccd\u0ca8\u0cc1"}},"bugOrFeatureRequest":{"heading":"\u0cb8\u0cae\u0cb8\u0ccd\u0caf\u0cc6? \u0cb9\u0cca\u0cb8 \u0c86\u0cb2\u0ccb\u0c9a\u0ca8\u0cc6?","body":"\u0caa\u0cc1\u0ca8\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cae\u0cbe\u0ca1\u0cc1\u0cb5\u0cc1\u0ca6\u0cb0\u0cbf\u0c82\u0ca6 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0caa\u0ccd\u0cb0\u0c97\u0ca4\u0cbf\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0c8f\u0ca8\u0cbe\u0ca6\u0cb0\u0cc2 \u0ca4\u0ca1\u0cc6\u0caf\u0cc1\u0ca4\u0ccd\u0ca4\u0cc0\u0cb0\u0cbe? \u0ca4\u0cca\u0cb0\u0cc6\u0caf\u0ca6\u0c82\u0ca4\u0cb9 \u0ca4\u0cca\u0c82\u0ca6\u0cb0\u0cc6 \u0ca6\u0ccb\u0cb7 \u0c95\u0c82\u0ca1\u0cc1\u0cac\u0c82\u0ca6\u0cbf\u0ca6\u0cc6? GitHub \u0cb8\u0cae\u0cb8\u0ccd\u0caf\u0cc6\u0c97\u0cb3 \u0cb5\u0cbf\u0cad\u0cbe\u0c97\u0ca6\u0cb2\u0ccd\u0cb2\u0cbf \u0c87\u0ca6\u0cb0 \u0cac\u0c97\u0ccd\u0c97\u0cc6 \u0cae\u0cbe\u0ca4\u0ca8\u0cbe\u0ca1\u0cbf, \u0c85\u0ca5\u0cb5\u0cbe \u0c95\u0cc6\u0cb3\u0c97\u0cbf\u0ca8 \u0c95\u0ccd\u0cb0\u0cbf\u0caf\u0cc6\u0c97\u0cb3\u0ca8\u0ccd\u0ca8\u0cc1 \u0cac\u0cb3\u0cb8\u0cbf\u0c95\u0cca\u0c82\u0ca1\u0cc1 \u0ca8\u0ca8\u0c97\u0cc6 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0c87\u0cae\u0cc7\u0cb2\u0ccd \u0c95\u0cb3\u0cc1\u0cb9\u0cbf\u0cb8\u0cbf.","buttons":{"raiseIssue":"\u0cb8\u0cae\u0cb8\u0ccd\u0caf\u0cc6\u0caf \u0cac\u0c97\u0ccd\u0c97\u0cc6 \u0ca4\u0cbf\u0cb3\u0cbf\u0cb8\u0cbf","sendEmail":"\u0c87\u0cae\u0cc7\u0cb2\u0ccd \u0c95\u0cb3\u0cc1\u0cb9\u0cbf\u0cb8\u0cbf"}},"sourceCode":{"heading":"\u0cb8\u0ccb\u0cb0\u0ccd\u0cb8\u0ccd \u0c95\u0ccb\u0ca1\u0ccd","body":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0c85\u0ca6\u0cb0 \u0cae\u0cc2\u0cb2\u0ca6\u0cbf\u0c82\u0ca6 \u0c9a\u0cb2\u0cbe\u0caf\u0cbf\u0cb8\u0cb2\u0cc1 \u0cac\u0caf\u0cb8\u0cc1\u0cb5\u0cbf\u0cb0\u0cbe? \u0c88 \u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd\u200c\u0ca8 \u0cae\u0cc1\u0c95\u0ccd\u0ca4-\u0cae\u0cc2\u0cb2 \u0c85\u0cad\u0cbf\u0cb5\u0cc3\u0ca6\u0ccd\u0ca7\u0cbf\u0c97\u0cc6 \u0c95\u0cca\u0ca1\u0cc1\u0c97\u0cc6 \u0ca8\u0cc0\u0ca1\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0cb8\u0cbf\u0ca6\u0ccd\u0ca7\u0cb0\u0cbf\u0ca6\u0ccd\u0ca6\u0cc0\u0cb0\u0cbe? \u0c95\u0cc6\u0cb3\u0c97\u0cbf\u0ca8 \u0cac\u0c9f\u0ca8\u0ccd \u0c95\u0ccd\u0cb2\u0cbf\u0c95\u0ccd \u0cae\u0cbe\u0ca1\u0cbf.","buttons":{"githubRepo":"\u0c97\u0cbf\u0c9f\u0ccd\u200c\u0cb9\u0cac\u0ccd \u0cb0\u0cbf\u0caa\u0ccb"}},"license":{"heading":"\u0caa\u0cb0\u0cb5\u0cbe\u0ca8\u0c97\u0cbf \u0cae\u0cbe\u0cb9\u0cbf\u0ca4\u0cbf","body":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0c8e\u0c82\u0c90\u0c9f\u0cbf \u0caa\u0cb0\u0cb5\u0cbe\u0ca8\u0c97\u0cbf \u0c85\u0ca1\u0cbf\u0caf\u0cb2\u0ccd\u0cb2\u0cbf \u0ca8\u0cbf\u0caf\u0c82\u0ca4\u0ccd\u0cb0\u0cbf\u0cb8\u0cb2\u0cbe\u0c97\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6, \u0c85\u0ca6\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0c95\u0cc6\u0cb3\u0c97\u0cc6 \u0c87\u0ca8\u0ccd\u0ca8\u0cb7\u0ccd\u0c9f\u0cc1 \u0c93\u0ca6\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0ca8\u0cc0\u0cb5\u0cc1 \u0cae\u0cc2\u0cb2 \u0cb2\u0cc7\u0c96\u0c95\u0cb0\u0cbf\u0c97\u0cc6 \u0c95\u0ccd\u0cb0\u0cc6\u0ca1\u0cbf\u0c9f\u0ccd \u0ca8\u0cc0\u0ca1\u0cbf\u0ca6\u0cb0\u0cc6 \u0c8e\u0cb2\u0ccd\u0cb2\u0cbf\u0caf\u0cbe\u0ca6\u0cb0\u0cc2 \u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0cac\u0cb3\u0cb8\u0cb2\u0cc1 \u0ca8\u0cbf\u0cae\u0c97\u0cc6 \u0c85\u0ca8\u0cc1\u0cae\u0ca4\u0cbf \u0c87\u0ca6\u0cc6.","buttons":{"mitLicense":"\u0c8e\u0c82\u0c90\u0c9f\u0cbf \u0caa\u0cb0\u0cb5\u0cbe\u0ca8\u0c97\u0cbf"}},"footer":{"credit":"<1>\u0c85\u0cae\u0cc3\u0ca4\u0ccd \u0caa\u0cbf\u0cb3\u0ccd\u0cb3\u0cc8</1> \u0c85\u0cb5\u0cb0\u0cbf\u0c82\u0ca6 \u0caa\u0ccd\u0cb0\u0cc0\u0ca4\u0cbf\u0caf\u0cbf\u0c82\u0ca6 \u0cae\u0cbe\u0ca1\u0cb2\u0ccd\u0caa\u0c9f\u0ccd\u0c9f\u0cbf\u0ca6\u0cc6","thanks":"\u0cb0\u0cbf\u0caf\u0cbe\u0c95\u0ccd\u0c9f\u0cbf\u0cb5\u0ccd \u0cb0\u0cc6\u0cb8\u0cc1\u0cae\u0cc7 \u0cac\u0cb3\u0cb8\u0cbf\u0ca6\u0ccd\u0ca6\u0c95\u0ccd\u0c95\u0cbe\u0c97\u0cbf \u0ca7\u0ca8\u0ccd\u0caf\u0cb5\u0cbe\u0ca6\u0c97\u0cb3\u0cc1!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"heading":{"placeholder":"Heading"},"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date","placeholder":"March 2018"},"endDate":{"label":"End Date","placeholder":"March 2022"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"}},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name","placeholder":"Jane"},"lastName":{"label":"Last Name","placeholder":"Doe"},"subtitle":{"label":"Subtitle","placeholder":"Full Stack Web Developer"},"address":{"line1":{"label":"Address Line 1","placeholder":"Palladium Complex"},"line2":{"label":"Address Line 2","placeholder":"140 E 14th St"},"line3":{"label":"Address Line 3","placeholder":"New York, NY 10003 USA"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective","placeholder":"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Amazon"},"role":{"label":"Role","placeholder":"Front-end Web Developer"},"description":{"placeholder":"You can write about what you specialized in while working at the company and what projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Harvard University"},"major":{"label":"Major","placeholder":"Masters in Computer Science"},"grade":{"label":"Grade"},"description":{"placeholder":"You can write about projects or special credit classes that you took while studying at this school."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title","placeholder":"Math & Science Olympiad"},"subtitle":{"label":"Subtitle","placeholder":"First Place, International Level"},"description":{"placeholder":"You can write about what qualities made you succeed in getting this award."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Dothraki"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Richard Hendricks"},"position":{"label":"Position","placeholder":"CEO, Pied Piper"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"},"description":{"placeholder":"You can write about how you and the reference contact worked together and which projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Date of Birth"},"value":{"label":"Value","placeholder":"6th August 1995"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Accent Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"printResume":{"heading":"Print Your Resume","body":"You can click on the button below to generate a PDF instantly. Alternatively, you can also use <1>Cmd/Ctrl + P</1> but it would have different effects.","buttons":{"export":"Export","print":"Print"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Wouldn\'t it be nice if there was a guide to setting it up on your local machine? Need information on how to contribute to the project? Look no further, there\'s comprehensive documentation made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"heading":{"placeholder":"Heading"},"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date","placeholder":"March 2018"},"endDate":{"label":"End Date","placeholder":"March 2022"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"}},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name","placeholder":"Jane"},"lastName":{"label":"Last Name","placeholder":"Doe"},"subtitle":{"label":"Subtitle","placeholder":"Full Stack Web Developer"},"address":{"line1":{"label":"Address Line 1","placeholder":"Palladium Complex"},"line2":{"label":"Address Line 2","placeholder":"140 E 14th St"},"line3":{"label":"Address Line 3","placeholder":"New York, NY 10003 USA"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective","placeholder":"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Amazon"},"role":{"label":"Role","placeholder":"Front-end Web Developer"},"description":{"placeholder":"You can write about what you specialized in while working at the company and what projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Harvard University"},"major":{"label":"Major","placeholder":"Masters in Computer Science"},"grade":{"label":"Grade"},"description":{"placeholder":"You can write about projects or special credit classes that you took while studying at this school."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title","placeholder":"Math & Science Olympiad"},"subtitle":{"label":"Subtitle","placeholder":"First Place, International Level"},"description":{"placeholder":"You can write about what qualities made you succeed in getting this award."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Dothraki"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Richard Hendricks"},"position":{"label":"Position","placeholder":"CEO, Pied Piper"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"},"description":{"placeholder":"You can write about how you and the reference contact worked together and which projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Date of Birth"},"value":{"label":"Value","placeholder":"6th August 1995"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Accent Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"printResume":{"heading":"Print Your Resume","body":"You can click on the button below to generate a PDF instantly. Alternatively, you can also use <1>Cmd/Ctrl + P</1> but it would have different effects.","buttons":{"export":"Export","print":"Print"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Wouldn\'t it be nice if there was a guide to setting it up on your local machine? Need information on how to contribute to the project? Look no further, there\'s comprehensive documentation made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Voeg {{- heading}} toe","startDate":{"label":"Startdatum"},"endDate":{"label":"Einddatum"},"description":{"label":"Beschrijving"}},"buttons":{"add":{"label":"Toevoegen"},"delete":{"label":"Verwijderen"}},"printDialog":{"heading":"Download je curriculum","quality":{"label":"Kwaliteit"},"printType":{"label":"Soort","types":{"unconstrained":"Geen limitaties","fitInA4":"Passend maken in A4","multiPageA4":"Multi-Pagina A4"}},"helpText":["Deze exportmethode maakt gebruik van HTML-canvas om de cv te converteren naar een afbeelding en deze af te drukken op een PDF, dit betekent dat het alle selectie/parsing mogelijkheden verliest.","Als dat belangrijk voor u is, probeer dan het Cmd/Ctrl + P of de print knop hieronder af te drukken. Het resultaat kan vari\xebren omdat de output afhankelijk is van de browser, maar het is bekend dat het het beste werkt op de nieuwste versie van Google Chrome."],"buttons":{"cancel":"Annuleren","saveAsPdf":"Opslaan als PDF"}},"panZoomAnimation":{"helpText":"Je kunt op elk moment op het artboard inzoomen om een beter zicht te krijgen op je curriculum."},"markdownHelpText":"U kunt <1>GitHub Flavored Markdown</1> gebruiken om dit gedeelte van de tekst op te maken."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Foto URL"},"firstName":{"label":"Voornaam"},"lastName":{"label":"Achternaam"},"subtitle":{"label":"Ondertitel"},"address":{"label":"Adres","line1":{"label":"Adresregel 1"},"line2":{"label":"Adresregel 2"},"line3":{"label":"Adresregel 3"}},"phone":{"label":"Telefoonnummer"},"website":{"label":"Website"},"email":{"label":"E-mailadres"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Doelstelling"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Naam"},"role":{"label":"Rol"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Naam"},"major":{"label":"Groot"},"grade":{"label":"Beoordeling"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titel"},"subtitle":{"label":"Ondertitel"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Naam"},"subtitle":{"label":"Autoriteit"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Naam"},"level":{"label":"Taalvaardigheid"},"rating":{"label":"Waardering"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Naam"},"position":{"label":"Positie"},"phone":{"label":"Telefoonnummer"},"email":{"label":"E-mailadres"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Sleutel"},"value":{"label":"Waarde"}}')},function(e){e.exports=JSON.parse('{"title":"Sjablonen"}')},function(e){e.exports=JSON.parse('{"title":"Kleuren","colorOptions":"Kleuropties","primaryColor":"Hoofdkleur","accentColor":"Secundaire kleur","clipboardCopyAction":"{{color}} is naar het klembord gekopieerd."}')},function(e){e.exports=JSON.parse('{"title":"Lettertypes","fontFamily":{"label":"Lettertype Familie","helpText":"U kunt elk lettertype gebruiken dat ook op uw systeem is ge\xefnstalleerd. Voer hier gewoon de naam in van de lettertype familie en de browser zou het voor je laden."}}')},function(e){e.exports=JSON.parse('{"title":"Acties","disclaimer":"Veranderingen die u aanbrengt in uw curriculum worden automatisch bewaard in je browsers lokale opslag. Geen data wordt verstuurd, dus je informatie is helemaal veilig.","importExport":{"heading":"Importeren/Exporteren","body":"U kunt uw gegevens importeren of exporteren in JSON formaat. Hiermee kunt u uw CV op elk apparaat bewerken en afdrukken. Sla dit bestand op voor later gebruik.","buttons":{"import":"Importeren","export":"Exporteren"}},"downloadResume":{"heading":"Download je curriculum","body":"U kunt op de knop hieronder klikken om direct een PDF-versie van uw CV te downloaden. Gebruik de nieuwste versie van Google Chrome voor de beste resultaten.","buttons":{"saveAsPdf":"Opslaan als PDF"}},"loadDemoData":{"heading":"Laad Demo gegevens","body":"Onduidelijk wat te doen met een nieuwe lege pagina? Laad wat demogegevens om te zien hoe een curriculum eruit zou moeten zien en u kan meteen beginnen te bewerken.","buttons":{"loadData":"Gegevens laden"}},"reset":{"heading":"Reset alles!","body":"Deze actie zal al uw gegevens resetten en back-ups naar de lokale opslag van uw browser verwijderen dus zorg ervoor dat je je informatie hebt ge\xebxporteerd voordat je alles opnieuw instelt.","buttons":{"reset":"Resetten"}}}')},function(e){e.exports=JSON.parse('{"title":"Instellingen","language":{"label":"Taal","helpText":"Als u wilt helpen de app te vertalen in uw eigen taal, raadpleeg dan de <1>Vertalingsdocumentatie</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Over","documentation":{"heading":"Documentatie","body":"Wil je meer weten over de app? Heb je informatie nodig over hoe je kan bijdragen aan het project? Kijk niet verder, er is een uitgebreide handleiding gemaakt speciaal voor jou.","buttons":{"documentation":"Documentatie"}},"bugOrFeatureRequest":{"heading":"Fout opgemerkt? Functionaliteit aanvragen?","body":"Iets dat je voortgang verhindert om te hervatten of te hervatten? Heb je een vervelende bug gevonden die gewoon niet zal stoppen? Praat erover in de GitHub Issues sectie, of stuur mij een e-mail via de onderstaande acties.","buttons":{"raiseIssue":"Meld een probleem","sendEmail":"Stuur een e-mail"}},"sourceCode":{"heading":"Broncode","body":"Wil je het project uitvoeren vanuit de bron? Bent u een ontwikkelaar die bereid is bij te dragen aan de open-source ontwikkeling van dit project? Klik op de knop hieronder.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"Licentie informatie","body":"Het project valt onder de MIT-licentie, waarover je hieronder meer kunt lezen. In principe mag u het project overal gebruiken, mits u credits geeft aan de oorspronkelijke auteur.","buttons":{"mitLicense":"MIT Licentie"}},"footer":{"credit":"Gemaakt met liefde door <1>Amruth Pillai</1>","thanks":"Bedankt voor het gebruiken van Reactieve Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"heading":{"placeholder":"Heading"},"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date","placeholder":"March 2018"},"endDate":{"label":"End Date","placeholder":"March 2022"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"}},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name","placeholder":"Jane"},"lastName":{"label":"Last Name","placeholder":"Doe"},"subtitle":{"label":"Subtitle","placeholder":"Full Stack Web Developer"},"address":{"line1":{"label":"Address Line 1","placeholder":"Palladium Complex"},"line2":{"label":"Address Line 2","placeholder":"140 E 14th St"},"line3":{"label":"Address Line 3","placeholder":"New York, NY 10003 USA"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective","placeholder":"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Amazon"},"role":{"label":"Role","placeholder":"Front-end Web Developer"},"description":{"placeholder":"You can write about what you specialized in while working at the company and what projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Harvard University"},"major":{"label":"Major","placeholder":"Masters in Computer Science"},"grade":{"label":"Grade"},"description":{"placeholder":"You can write about projects or special credit classes that you took while studying at this school."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title","placeholder":"Math & Science Olympiad"},"subtitle":{"label":"Subtitle","placeholder":"First Place, International Level"},"description":{"placeholder":"You can write about what qualities made you succeed in getting this award."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Dothraki"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Richard Hendricks"},"position":{"label":"Position","placeholder":"CEO, Pied Piper"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"},"description":{"placeholder":"You can write about how you and the reference contact worked together and which projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Date of Birth"},"value":{"label":"Value","placeholder":"6th August 1995"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Accent Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"printResume":{"heading":"Print Your Resume","body":"You can click on the button below to generate a PDF instantly. Alternatively, you can also use <1>Cmd/Ctrl + P</1> but it would have different effects.","buttons":{"export":"Export","print":"Print"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Wouldn\'t it be nice if there was a guide to setting it up on your local machine? Need information on how to contribute to the project? Look no further, there\'s comprehensive documentation made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Dodaj {{- heading}}","startDate":{"label":"Data rozpocz\u0119cia"},"endDate":{"label":"Data zako\u0144czenia"},"description":{"label":"Opis"}},"buttons":{"add":{"label":"Dodaj"},"delete":{"label":"Usu\u0144"}},"printDialog":{"heading":"Pobierz swoje CV","quality":{"label":"Jako\u015b\u0107"},"printType":{"label":"Rodzaj","types":{"unconstrained":"Nieograniczony","fitInA4":"Dopasuj do A4","multiPageA4":"Wielostronne A4"}},"helpText":["Ta metoda eksportu wykorzystuje HTML do konwersji CV jako obrazu i wydrukowania go w formacie PDF, co oznacza, \u017ce utraci on wszystkie mo\u017cliwo\u015bci zaznaczania / analizowania.","Je\u015bli jest to dla Ciebie wa\u017cne, spr\xf3buj wydrukowa\u0107 CV za pomoc\u0105 Ctrl + P lub przycisku drukowania poni\u017cej. Wynik mo\u017ce si\u0119 r\xf3\u017cni\u0107, poniewa\u017c wynik zale\u017cy od przegl\u0105darki, ale wiemy, \u017ce najlepiej dzia\u0142a w najnowszej wersji Google Chrome."],"buttons":{"cancel":"Anuluj","saveAsPdf":"Zapisz jako PDF"}},"panZoomAnimation":{"helpText":"Mo\u017cesz przesuwa\u0107 i powi\u0119ksza\u0107 obszar roboczy w dowolnym momencie, aby przyjrze\u0107 si\u0119 swojemu CV."},"markdownHelpText":"Mo\u017cesz u\u017cy\u0107 <1>GitHub Flavored Markdown</1> aby zmieni\u0107 styl tej sekcji tekstu."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Adres URL zdj\u0119cia"},"firstName":{"label":"Imi\u0119"},"lastName":{"label":"Nazwisko"},"subtitle":{"label":"Podtytu\u0142"},"address":{"label":"Adres","line1":{"label":"Wiersz adresu 1"},"line2":{"label":"Wiersz adresu 2"},"line3":{"label":"Wiersz adresu 3"}},"phone":{"label":"Telefon"},"website":{"label":"Strona WWW"},"email":{"label":"Adres e-mail"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Cel"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nazwa"},"role":{"label":"Stanowisko"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nazwa"},"major":{"label":"Kierunek"},"grade":{"label":"Stopie\u0144"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Tytu\u0142"},"subtitle":{"label":"Podtytu\u0142"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nazwa"},"subtitle":{"label":"Wydany przez"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nazwa"},"level":{"label":"Poziom"},"rating":{"label":"Ocena"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nazwisko"},"position":{"label":"Stanowisko"},"phone":{"label":"Numer telefonu"},"email":{"label":"Adres e-mail"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nazwa/Klucz"},"value":{"label":"Warto\u015b\u0107"}}')},function(e){e.exports=JSON.parse('{"title":"Szablony"}')},function(e){e.exports=JSON.parse('{"title":"Kolory","colorOptions":"Opcje kolor\xf3w","primaryColor":"Kolor podstawowy","accentColor":"Kolor dodatkowy","clipboardCopyAction":"{{color}} zosta\u0142 skopiowany do schowka."}')},function(e){e.exports=JSON.parse('{"title":"Czcionki","fontFamily":{"label":"Rodzina czcionek","helpText":"Mo\u017cesz tak\u017ce u\u017cy\u0107 dowolnej czcionki zainstalowanej w systemie. Wystarczy wpisa\u0107 tutaj nazw\u0119 rodziny czcionek, a przegl\u0105darka za\u0142aduje j\u0105 dla Ciebie."}}')},function(e){e.exports=JSON.parse('{"title":"Akcje","disclaimer":"Zmiany wprowadzone w CV zostan\u0105 automatycznie zapisane w lokalnej pami\u0119ci przegl\u0105darki. \u017badne dane nie s\u0105 pobierane, dlatego Twoje informacje s\u0105 ca\u0142kowicie bezpieczne.","importExport":{"heading":"Importuj/Eksportuj","body":"Mo\u017cesz importowa\u0107 lub eksportowa\u0107 swoje dane w formacie JSON. Dzi\u0119ki temu mo\u017cesz edytowa\u0107 i drukowa\u0107 swoje CV z dowolnego urz\u0105dzenia. Zapisz ten plik do p\xf3\u017aniejszego wykorzystania.","buttons":{"import":"Import","export":"Eksport"}},"downloadResume":{"heading":"Pobierz swoje CV","body":"Mo\u017cesz klikn\u0105\u0107 na poni\u017cszy przycisk, aby natychmiast pobra\u0107 wersj\u0119 PDF. Aby uzyska\u0107 najlepsze wyniki, u\u017cyj najnowszej wersji Google Chrome.","buttons":{"saveAsPdf":"Zapisz jako PDF"}},"loadDemoData":{"heading":"Wczytaj dane Demo","body":"Nie wiesz, co zrobi\u0107 z czyst\u0105 pust\u0105 stron\u0105? Za\u0142aduj niekt\xf3re dane demonstracyjne z wst\u0119pnie wype\u0142nionymi warto\u015bciami, aby zobaczy\u0107, jak powinno wygl\u0105da\u0107 CV, i mo\u017cesz rozpocz\u0105\u0107 edycj\u0119.","buttons":{"loadData":"Wczytaj dane"}},"reset":{"heading":"Zresetuj wszystko!","body":"Ta czynno\u015b\u0107 zresetuje wszystkie dane i usunie kopie zapasowe utworzone w lokalnej pami\u0119ci przegl\u0105darki, wi\u0119c upewnij si\u0119, \u017ce wyeksportowa\u0142e\u015b informacje przed zresetowaniem.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Ustawienia","language":{"label":"J\u0119zyk","helpText":"Je\u015bli chcesz pom\xf3c w t\u0142umaczeniu aplikacji na sw\xf3j j\u0119zyk, zapoznaj si\u0119 z <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"O aplikacji","documentation":{"heading":"Dokumentacja","body":"Chcesz dowiedzie\u0107 si\u0119 wi\u0119cej o aplikacji? Potrzebujesz informacji o tym, jak przyczyni\u0107 si\u0119 do rozwoju projektu? Przygotowali\u015bmy kompleksowy przewodnik stworzony tylko dla Ciebie.","buttons":{"documentation":"Dokumentacja"}},"bugOrFeatureRequest":{"heading":"B\u0142\u0105d? Potrzebujesz nowych funkcji?","body":"Co\u015b powstrzymuje twoje post\u0119py w tworzeniu CV? Znalaz\u0142e\u015b niezno\u015bny b\u0142\u0105d? Porozmawiaj o tym w sekcji Problemy na GitHub lub wy\u015blij mi e-mail, korzystaj\u0105c z poni\u017cszych dzia\u0142a\u0144.","buttons":{"raiseIssue":"Zg\u0142o\u015b problem","sendEmail":"Wy\u015blij email"}},"sourceCode":{"heading":"Kod \u017ar\xf3d\u0142owy","body":"Chcesz uruchomi\u0107 projekt ze \u017ar\xf3d\u0142a? Czy jeste\u015b programist\u0105, kt\xf3ry chce przyczyni\u0107 si\u0119 do rozwoju tego open projektu open source? Kliknij przycisk poni\u017cej.","buttons":{"githubRepo":"Repozytorium na GitHub"}},"license":{"heading":"Informacje o licencji","body":"Projekt podlega licencji MIT, o kt\xf3rej wi\u0119cej mo\u017cesz przeczyta\u0107 poni\u017cej. Zasadniczo mo\u017cesz korzysta\u0107 z projektu w dowolnym miejscu, pod warunkiem, \u017ce nie zmienisz autora projektu.","buttons":{"mitLicense":"Licencja MIT"}},"footer":{"credit":"Wykonane z Mi\u0142o\u015bci\u0105 przez <1>Amruth Pillai</1>","thanks":"Dzi\u0119kujemy za korzystanie z Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Adicionar {{- heading}}","startDate":{"label":"Data Inicial"},"endDate":{"label":"Data Final"},"description":{"label":"Descri\xe7\xe3o"}},"buttons":{"add":{"label":"Adicionar"},"delete":{"label":"Eliminar"}},"printDialog":{"heading":"Baixar Curriculum","quality":{"label":"Qualidade"},"printType":{"label":"Tipo","types":{"unconstrained":"Sem restri\xe7\xf5es","fitInA4":"Ajustar a A4","multiPageA4":"Multi-p\xe1ginas A4"}},"helpText":["Esse m\xe9todo de exporta\xe7\xe3o utiliza a tela HTML para converter o curr\xedculo em uma imagem e imprimi-lo em um PDF, o que significa que ele perder\xe1 todos os recursos de sele\xe7\xe3o / an\xe1lise.","Se isso for importante para voc\xea, tente imprimir o curr\xedculo usando Cmd / Ctrl + P ou o bot\xe3o de impress\xe3o abaixo. O resultado pode variar, pois a sa\xedda depende do navegador, mas \xe9 conhecido por funcionar melhor na vers\xe3o mais recente do Google Chrome."],"buttons":{"cancel":"Cancelar","saveAsPdf":"Salvar como PDF"}},"panZoomAnimation":{"helpText":"Voc\xea pode arrastar e dar zoom no quadro de trabalho a qualquer momento para ver mais detalhes do seu curriculum."},"markdownHelpText":"Voc\xea pode utilizar <1>GitHub Flavored Markdown</1> para estilizar esta se\xe7\xe3o."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL da foto"},"firstName":{"label":"Nome"},"lastName":{"label":"Sobrenome"},"subtitle":{"label":"Subt\xedtulo"},"address":{"label":"Endere\xe7o","line1":{"label":"Endere\xe7o linha 1"},"line2":{"label":"Endere\xe7o linha 2"},"line3":{"label":"Endere\xe7o linha 3"}},"phone":{"label":"Telefone"},"website":{"label":"Site"},"email":{"label":"Email"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objetivo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"role":{"label":"Cargo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Institui\xe7\xe3o"},"major":{"label":"\xc1rea de estudo"},"grade":{"label":"Nota"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"T\xedtulo"},"subtitle":{"label":"Subt\xedtulo"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nome"},"subtitle":{"label":"Autoria"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nome"},"level":{"label":"N\xedvel"},"rating":{"label":"Nota"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"position":{"label":"Posi\xe7\xe3o"},"phone":{"label":"Telefone"},"email":{"label":"Email"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Chave"},"value":{"label":"Valor"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Cores","colorOptions":"Op\xe7\xf5es de cores","primaryColor":"Cor principal","accentColor":"Cor secund\xe1ria","clipboardCopyAction":"A cor {{color}} foi copiada para \xe1rea de transfer\xeancia."}')},function(e){e.exports=JSON.parse('{"title":"Fontes","fontFamily":{"label":"Fam\xedlia de Fontes","helpText":"Voc\xea tamb\xe9m pode usar qualquer fonte que esteja instalada no seu sistema. Basta digitar o nome da fonte aqui e o navegador vai carreg\xe1-la para voc\xea."}}')},function(e){e.exports=JSON.parse('{"title":"A\xe7\xf5es","disclaimer":"As altera\xe7\xf5es que voc\xea faz no seu curriculum s\xe3o salvas automaticamente no armazenamento local do seu navegador. Nenhum dado \xe9 partilhado, por isso sua informa\xe7\xe3o est\xe1 completamente segura.","importExport":{"heading":"Importar/Exportar","body":"Voc\xea pode importar ou exportar seus dados no formato JSON. Sendo assim, \xe9 poss\xedvel editar ou imprimir seu curriculum em qualquer dispositivo. Salve este arquivo para us\xe1-lo posteriormente.","buttons":{"import":"Importar","export":"Exportar"}},"downloadResume":{"heading":"Baixe seu Curriculum","body":"Voc\xea pode clicar no bot\xe3o abaixo para baixar a vers\xe3o em PDF do seu curriculum. Para obter melhores resultados, por favor utilize a ver\xe3o mais recente do Google Chrome.","buttons":{"saveAsPdf":"Salvar como PDF"}},"loadDemoData":{"heading":"Carregar dados demonstrativos","body":"Na d\xfavida sobre o que fazer com uma p\xe1gina em branco? Carregue os dados demonstrativos com valores j\xe1 preenchidos para ver como o curriculum fica e a partir da\xed voc\xea pode come\xe7ar a editar.","buttons":{"loadData":"Carregar dados"}},"reset":{"heading":"Reiniciar tudo!","body":"Esta a\xe7\xe3o vai apagar todos os seus dados e remover os backups feitos no armazenamento local do seu navegador tamb\xe9m. Por favor, lembre de exportar as suas informa\xe7\xf5es antes de reiniciar tudo.","buttons":{"reset":"Reiniciar"}}}')},function(e){e.exports=JSON.parse('{"title":"Configura\xe7\xe3o","language":{"label":"Escolher idioma","helpText":"Se voc\xea gostaria de ajudar a traduzir esta aplica\xe7\xe3o para o seu idioma, por favor, consulte a <1>Documenta\xe7\xe3o de Tradu\xe7\xe3o</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Sobre","documentation":{"heading":"Documenta\xe7\xe3o","body":"Quer saber mais sobre a applica\xe7\xe3o? N\xe3o seria \xf3timo se houvesse um guia para configur\xe1-la em sua m\xe1quina? Precisa de informa\xe7\xe3o sobre como contribuir para o projeto? N\xe3o precisa procurar mais, aqui h\xe1 uma documenta\xe7\xe3o compreensiva para voc\xea.","buttons":{"documentation":"Documenta\xe7\xe3o"}},"bugOrFeatureRequest":{"heading":"Bug? Solicita\xe7\xe3o de nova funcionalidade?","body":"Algo impedindo voc\xea de progredir com um curriculum? Encontrou aquele erro chato e persistente? Fale sobre ele na se\xe7\xe3o de Issues no Github, ou me envie um email usando as seguintes a\xe7\xf5es.","buttons":{"raiseIssue":"Notificar um problema","sendEmail":"Enviar um e-mail"}},"sourceCode":{"heading":"C\xf3digo fonte","body":"Tem interesse em executar o c\xf3digo fonte deste projeto? Voc\xea \xe9 um desenvolvedor interessado em contribuir para o desenvolvimento open-source deste projeto? Click no bot\xe3o abaixo.","buttons":{"githubRepo":"Reposit\xf3rio Github"}},"license":{"heading":"Informa\xe7\xe3o da licen\xe7a","body":"O projeto \xe9 regido pela licen\xe7a MIT, a qual voc\xea pode ler mais sobre abaixo. Basicamente, voc\xea pode usar este projeto onde quiser desde que d\xea os cr\xe9ditos ao autor original.","buttons":{"mitLicense":"Licen\xe7a MIT"}},"footer":{"credit":"Projeto criado com amor por <1>Amruth Pillai</1>.","thanks":"Obrigado por usar Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c {{- heading}}","startDate":{"label":"\u0414\u0430\u0442\u0430 \u043d\u0430\u0447\u0430\u043b\u0430"},"endDate":{"label":"\u0414\u0430\u0442\u0430 \u043e\u043a\u043e\u043d\u0447\u0430\u043d\u0438\u044f"},"description":{"label":"\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435"}},"buttons":{"add":{"label":"\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c"},"delete":{"label":"\u0423\u0434\u0430\u043b\u0438\u0442\u044c"}},"printDialog":{"heading":"\u0421\u043a\u0430\u0447\u0430\u0442\u044c \u0440\u0435\u0437\u044e\u043c\u0435","quality":{"label":"\u041a\u0430\u0447\u0435\u0441\u0442\u0432\u043e"},"printType":{"label":"\u0422\u0438\u043f","types":{"unconstrained":"\u041d\u0435\u043e\u0433\u0440\u0430\u043d\u0438\u0447\u0435\u043d\u043d\u044b\u0439","fitInA4":"\u041f\u043e \u0440\u0430\u0437\u043c\u0435\u0440\u0443 A4","multiPageA4":"\u041c\u043d\u043e\u0433\u043e\u0441\u0442\u0440\u0430\u043d\u0438\u0447\u043d\u044b\u0439 A4"}},"helpText":["\u042d\u0442\u043e\u0442 \u043c\u0435\u0442\u043e\u0434 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0430 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u0435\u0442 HTML canvas \u0434\u043b\u044f \u043f\u0440\u0435\u043e\u0431\u0440\u0430\u0437\u043e\u0432\u0430\u043d\u0438\u044f \u0440\u0435\u0437\u044e\u043c\u0435 \u0432 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435 \u0438 \u043a\u043e\u043d\u0432\u0435\u0440\u0442\u0430\u0446\u0438\u044e \u0432 \u0444\u043e\u0440\u043c\u0430\u0442 PDF, \u0447\u0442\u043e \u043e\u0437\u043d\u0430\u0447\u0430\u0435\u0442, \u0447\u0442\u043e \u043e\u043d \u043f\u043e\u0442\u0435\u0440\u044f\u0435\u0442 \u0432\u0441\u0435 \u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e\u0441\u0442\u0438 \u0432\u044b\u0431\u043e\u0440\u0430/\u0441\u0438\u043d\u0442\u0430\u043a\u0441\u0438\u0447\u0435\u0441\u043a\u043e\u0433\u043e \u0430\u043d\u0430\u043b\u0438\u0437\u0430.","\u0415\u0441\u043b\u0438 \u044d\u0442\u043e \u0432\u0430\u0436\u043d\u043e \u0434\u043b\u044f \u0412\u0430\u0441, \u043f\u043e\u0436\u0430\u043b\u0443\u0439\u0441\u0442\u0430, \u043f\u043e\u043f\u0440\u043e\u0431\u0443\u0439\u0442\u0435 \u0440\u0430\u0441\u043f\u0435\u0447\u0430\u0442\u0430\u0442\u044c \u0440\u0435\u0437\u044e\u043c\u0435 \u0432\u043c\u0435\u0441\u0442\u043e \u044d\u0442\u043e\u0433\u043e, \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u044f Cmd / Ctrl + P \u0438\u043b\u0438 \u043a\u043d\u043e\u043f\u043a\u0443 \u043f\u0435\u0447\u0430\u0442\u0438 \u043d\u0438\u0436\u0435. \u0420\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442 \u043c\u043e\u0436\u0435\u0442 \u043e\u0442\u043b\u0438\u0447\u0430\u0442\u044c\u0441\u044f, \u043f\u043e\u0441\u043a\u043e\u043b\u044c\u043a\u0443 \u0440\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442 \u0437\u0430\u0432\u0438\u0441\u0438\u0442 \u043e\u0442 \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430, \u043d\u043e \u0438\u0437\u0432\u0435\u0441\u0442\u043d\u043e, \u0447\u0442\u043e \u043e\u043d \u043b\u0443\u0447\u0448\u0435 \u0432\u0441\u0435\u0433\u043e \u0440\u0430\u0431\u043e\u0442\u0430\u0435\u0442 \u043d\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u0432\u0435\u0440\u0441\u0438\u0438 Google Chrome."],"buttons":{"cancel":"\u041e\u0442\u043c\u0435\u043d\u0430","saveAsPdf":"\u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u0432 PDF"}},"panZoomAnimation":{"helpText":"\u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u043f\u0435\u0440\u0435\u043c\u0435\u0449\u0430\u0442\u044c \u0438 \u043c\u0430\u0441\u0448\u0442\u0430\u0431\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0441\u0432\u043e\u0451 \u0440\u0435\u0437\u044e\u043c\u0435 \u0447\u0442\u043e\u0431\u044b \u043f\u043e\u0431\u043b\u0438\u0436\u0435 \u0432\u0437\u0433\u043b\u044f\u043d\u0443\u0442\u044c \u043d\u0430 \u043d\u0435\u0433\u043e."},"markdownHelpText":"\u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c <1>GitHub Flavored Markdown</1> \u0437\u0434\u0435\u0441\u044c."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL \u0410\u0434\u0440\u0435\u0441 \u0444\u043e\u0442\u043e\u0433\u0440\u0430\u0444\u0438\u0438"},"firstName":{"label":"\u0418\u043c\u044f"},"lastName":{"label":"\u0424\u0430\u043c\u0438\u043b\u0438\u044f"},"subtitle":{"label":"\u041f\u043e\u0434\u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a"},"address":{"label":"\u0410\u0434\u0440\u0435\u0441","line1":{"label":"\u0410\u0434\u0440\u0435\u0441, \u0441\u0442\u0440\u043e\u043a\u0430 1"},"line2":{"label":"\u0410\u0434\u0440\u0435\u0441, \u0441\u0442\u0440\u043e\u043a\u0430 2"},"line3":{"label":"\u0410\u0434\u0440\u0435\u0441, \u0441\u0442\u0440\u043e\u043a\u0430 3"}},"phone":{"label":"\u041d\u043e\u043c\u0435\u0440 \u0442\u0435\u043b\u0435\u0444\u043e\u043d\u0430"},"website":{"label":"\u0412\u0435\u0431-\u0441\u0430\u0439\u0442"},"email":{"label":"E-mail \u0430\u0434\u0440\u0435\u0441"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0426\u0435\u043b\u044c"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0418\u043c\u044f"},"role":{"label":"\u0414\u043e\u043b\u0436\u043d\u043e\u0441\u0442\u044c"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0418\u043c\u044f"},"major":{"label":"\u041f\u0440\u0435\u0434\u043c\u0435\u0442"},"grade":{"label":"\u041a\u043b\u0430\u0441\u0441"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a"},"subtitle":{"label":"\u041f\u043e\u0434\u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0418\u043c\u044f"},"subtitle":{"label":"\u0410\u0432\u0442\u043e\u0440"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0418\u043c\u044f"},"level":{"label":"\u0423\u0440\u043e\u0432\u0435\u043d\u044c"},"rating":{"label":"\u0420\u0435\u0439\u0442\u0438\u043d\u0433"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0418\u043c\u044f"},"position":{"label":"\u041f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435"},"phone":{"label":"\u041d\u043e\u043c\u0435\u0440 \u0442\u0435\u043b\u0435\u0444\u043e\u043d\u0430"},"email":{"label":"E-mail \u0430\u0434\u0440\u0435\u0441"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435"},"value":{"label":"\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435"}}')},function(e){e.exports=JSON.parse('{"title":"\u0428\u0430\u0431\u043b\u043e\u043d\u044b"}')},function(e){e.exports=JSON.parse('{"title":"\u0426\u0432\u0435\u0442\u0430","colorOptions":"\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u0446\u0432\u0435\u0442\u0430","primaryColor":"\u0426\u0432\u0435\u0442 \u0442\u0435\u043a\u0441\u0442\u0430","accentColor":"\u041e\u0441\u043d\u043e\u0432\u043d\u043e\u0439 \u0446\u0432\u0435\u0442","clipboardCopyAction":"\u0426\u0432\u0435\u0442 {{color}} \u0431\u044b\u043b \u0441\u043a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d \u0432 \u0431\u0443\u0444\u0435\u0440 \u043e\u0431\u043c\u0435\u043d\u0430."}')},function(e){e.exports=JSON.parse('{"title":"\u0428\u0440\u0438\u0444\u0442\u044b","fontFamily":{"label":"\u0428\u0440\u0438\u0444\u0442","helpText":"\u0412\u044b \u0442\u0430\u043a\u0436\u0435 \u043c\u043e\u0436\u0435\u0442\u0435 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u043b\u044e\u0431\u043e\u0439 \u0448\u0440\u0438\u0444\u0442, \u0443\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u043d\u044b\u0439 \u0432 \u0432\u0430\u0448\u0435\u0439 \u0441\u0438\u0441\u0442\u0435\u043c\u0435. \u041f\u0440\u043e\u0441\u0442\u043e \u0432\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u0434\u0435\u0441\u044c \u0438\u043c\u044f \u0448\u0440\u0438\u0444\u0442\u0430, \u0438 \u0431\u0440\u0430\u0443\u0437\u0435\u0440 \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442 \u0435\u0433\u043e."}}')},function(e){e.exports=JSON.parse('{"title":"\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044f","disclaimer":"\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f, \u0432\u043d\u0435\u0441\u0435\u043d\u043d\u044b\u0435 \u0432 \u0432\u0430\u0448\u0435 \u0440\u0435\u0437\u044e\u043c\u0435, \u0441\u043e\u0445\u0440\u0430\u043d\u044f\u044e\u0442\u0441\u044f \u0430\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438 \u0432 \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u043e\u0435 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u0432\u0430\u0448\u0435\u0433\u043e \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430. \u0414\u0430\u043d\u043d\u044b\u0435 \u043d\u0435 \u043e\u0442\u043f\u0440\u0430\u0432\u043b\u044f\u044e\u0442\u0441\u044f \u043d\u0430 \u0441\u0435\u0440\u0432\u0435\u0440\u0430, \u043f\u043e\u044d\u0442\u043e\u043c\u0443 \u0432\u0430\u0448\u0430 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u0432 \u0431\u0435\u0437\u043e\u043f\u0430\u0441\u043d\u043e\u0441\u0442\u0438.","importExport":{"heading":"\u0418\u043c\u043f\u043e\u0440\u0442/\u042d\u043a\u0441\u043f\u043e\u0440\u0442","body":"\u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u0438\u043c\u043f\u043e\u0440\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0438\u043b\u0438 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0434\u0430\u043d\u043d\u044b\u0435 \u0432 \u0444\u043e\u0440\u043c\u0430\u0442\u0435 JSON. \u041f\u0440\u0438 \u044d\u0442\u043e\u043c \u0432\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0438 \u0440\u0430\u0441\u043f\u0435\u0447\u0430\u0442\u0430\u0442\u044c \u0440\u0435\u0437\u044e\u043c\u0435 \u0441 \u043b\u044e\u0431\u043e\u0433\u043e \u0443\u0441\u0442\u0440\u043e\u0439\u0441\u0442\u0432\u0430. \u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u0435 \u044d\u0442\u043e\u0442 \u0444\u0430\u0439\u043b \u0434\u043b\u044f \u043f\u043e\u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0435\u0433\u043e \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u044f.","buttons":{"import":"\u0418\u043c\u043f\u043e\u0440\u0442","export":"\u042d\u043a\u0441\u043f\u043e\u0440\u0442"}},"downloadResume":{"heading":"\u0421\u043a\u0430\u0447\u0430\u0442\u044c \u0440\u0435\u0437\u044e\u043c\u0435","body":"\u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u043d\u0430\u0436\u0430\u0442\u044c \u043d\u0430 \u043a\u043d\u043e\u043f\u043a\u0443 \u043d\u0438\u0436\u0435, \u0447\u0442\u043e\u0431\u044b \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044c PDF-\u0432\u0435\u0440\u0441\u0438\u044e \u0432\u0430\u0448\u0435\u0433\u043e \u0440\u0435\u0437\u044e\u043c\u0435. \u041b\u0443\u0447\u0448\u0435 \u0438\u0441\u043f\u043e\u043b\u0437\u043e\u0432\u0430\u0442\u044c \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u044e\u044e \u0432\u0435\u0440\u0441\u0438\u044e Google Chrome.","buttons":{"saveAsPdf":"\u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u0432 PDF"}},"loadDemoData":{"heading":"\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044c \u0434\u0435\u043c\u043e-\u0434\u0430\u043d\u043d\u044b\u0435","body":"\u041d\u0435\u043f\u043e\u043d\u044f\u0442\u043d\u043e, \u0447\u0442\u043e \u0434\u0435\u043b\u0430\u0442\u044c \u0441 \u0447\u0438\u0441\u0442\u043e\u0439 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0435\u0439? \u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u0435 \u0434\u0435\u043c\u043e, \u0447\u0442\u043e\u0431\u044b \u0443\u0432\u0438\u0434\u0435\u0442\u044c \u043f\u0440\u0438\u043c\u0435\u0440 \u0440\u0435\u0437\u044e\u043c\u0435, \u0438 \u0432\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u043d\u0430\u0447\u0430\u0442\u044c \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435.","buttons":{"loadData":"\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044c \u0434\u0435\u043c\u043e"}},"reset":{"heading":"\u0421\u0431\u0440\u043e\u0441\u0438\u0442\u044c \u0432\u0441\u0435","body":"\u042d\u0442\u043e \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435 \u0441\u0431\u0440\u043e\u0441\u0438\u0442 \u0432\u0441\u0435 \u0432\u0430\u0448\u0438 \u0434\u0430\u043d\u043d\u044b\u0435 \u0438 \u0443\u0434\u0430\u043b\u0438\u0442 \u0440\u0435\u0437\u0435\u0440\u0432\u043d\u044b\u0435 \u043a\u043e\u043f\u0438\u0438 \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u043e\u0433\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0430 \u0432\u0430\u0448\u0435\u0433\u043e \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430. \u043f\u043e\u044d\u0442\u043e\u043c\u0443 \u0443\u0431\u0435\u0434\u0438\u0442\u0435\u0441\u044c, \u0447\u0442\u043e \u0432\u044b \u0441\u043e\u0445\u0440\u0430\u043d\u0438\u043b\u0438 \u0432\u0430\u0448\u0443 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044e, \u043f\u0435\u0440\u0435\u0434 \u0441\u0431\u0440\u043e\u0441\u043e\u043c.","buttons":{"reset":"\u0421\u0431\u0440\u043e\u0441\u0438\u0442\u044c"}}}')},function(e){e.exports=JSON.parse('{"title":"\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438","language":{"label":"\u042f\u0437\u044b\u043a","helpText":"\u0415\u0441\u043b\u0438 \u0432\u044b \u0445\u043e\u0442\u0438\u0442\u0435 \u043f\u043e\u043c\u043e\u0447\u044c \u043f\u0435\u0440\u0435\u0432\u0435\u0441\u0442\u0438 \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u043d\u0430 \u0432\u0430\u0448 \u044f\u0437\u044b\u043a, \u043e\u0431\u0440\u0430\u0442\u0438\u0442\u0435\u0441\u044c \u043a <1>\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u0438 \u043f\u043e \u043f\u0435\u0440\u0435\u0432\u043e\u0434\u0443</1>."}}')},function(e){e.exports=JSON.parse('{"title":"\u041e \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0435","documentation":{"heading":"\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u044f","body":"\u0425\u043e\u0442\u0438\u0442\u0435 \u0443\u0437\u043d\u0430\u0442\u044c \u0431\u043e\u043b\u044c\u0448\u0435 \u043e \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u0438? \u041d\u0443\u0436\u043d\u0430 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u0442\u043e\u043c, \u043a\u0430\u043a \u0432\u043d\u0435\u0441\u0442\u0438 \u0441\u0432\u043e\u0439 \u0432\u043a\u043b\u0430\u0434 \u0432 \u043f\u0440\u043e\u0435\u043a\u0442?","buttons":{"documentation":"\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u044f"}},"bugOrFeatureRequest":{"heading":"\u041e\u0448\u0438\u0431\u043a\u0430? \u0425\u043e\u0442\u0438\u0442\u0435 \u043f\u0440\u0435\u0434\u043b\u043e\u0436\u0438\u0442\u044c \u0444\u0443\u043d\u043a\u0446\u0438\u044e?","body":"\u0427\u0442\u043e-\u0442\u043e \u043c\u0435\u0448\u0430\u0435\u0442 \u0432\u0430\u043c \u0432 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u0438 \u0440\u0435\u0437\u044e\u043c\u0435? \u041d\u0430\u0448\u043b\u0438 \u043e\u0448\u0438\u0431\u043a\u0443? \u0420\u0430\u0441\u0441\u043a\u0430\u0436\u0438\u0442\u0435 \u043e\u0431 \u044d\u0442\u043e\u043c \u0432 \u0440\u0430\u0437\u0434\u0435\u043b\u0435 issues \u043d\u0430 GitHub \u0438\u043b\u0438 \u043e\u0442\u043f\u0440\u0430\u0432\u044c\u0442\u0435 \u043c\u043d\u0435 \u043f\u0438\u0441\u044c\u043c\u043e \u043f\u043e \u044d\u043b\u0435\u043a\u0442\u0440\u043e\u043d\u043d\u043e\u0439 \u043f\u043e\u0447\u0442\u0435, \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u044f \u043a\u043d\u043e\u043f\u043a\u0438 \u043d\u0438\u0436\u0435.","buttons":{"raiseIssue":"\u0421\u043e\u043e\u0431\u0449\u0438\u0442\u044c \u043e\u0431 \u043e\u0448\u0438\u0431\u043a\u0435","sendEmail":"\u041d\u0430\u043f\u0438\u0441\u0430\u0442\u044c \u043f\u0438\u0441\u044c\u043c\u043e"}},"sourceCode":{"heading":"\u0418\u0441\u0445\u043e\u0434\u043d\u044b\u0439 \u043a\u043e\u0434","body":"\u0425\u043e\u0442\u0438\u0442\u0435 \u0437\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c \u043f\u0440\u043e\u0435\u043a\u0442 \u0438\u0437 \u0438\u0441\u0445\u043e\u0434\u043d\u043e\u0433\u043e \u043a\u043e\u0434\u0430? \u0412\u044b \u0445\u043e\u0442\u0438\u0442\u0435 \u0432\u043d\u0435\u0441\u0442\u0438 \u0432\u043a\u043b\u0430\u0434 \u0432 \u0440\u0430\u0437\u0440\u0430\u0431\u043e\u0442\u043a\u0443 \u044d\u0442\u043e\u0433\u043e \u043f\u0440\u043e\u0435\u043a\u0442\u0430? \u041d\u0430\u0436\u043c\u0438\u0442\u0435 \u043d\u0430 \u043a\u043d\u043e\u043f\u043a\u0443 \u043d\u0438\u0436\u0435.","buttons":{"githubRepo":"GitHub"}},"license":{"heading":"\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043b\u0438\u0446\u0435\u043d\u0437\u0438\u0438","body":"\u041f\u0440\u043e\u0435\u043a\u0442 \u0443\u043f\u0440\u0430\u0432\u043b\u044f\u0435\u0442\u0441\u044f \u0432 \u0441\u043e\u043e\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u0438 \u0441 \u043b\u0438\u0446\u0435\u043d\u0437\u0438\u0435\u0439 MIT, \u043e \u043a\u043e\u0442\u043e\u0440\u043e\u0439 \u0432\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u043f\u0440\u043e\u0447\u0438\u0442\u0430\u0442\u044c \u043d\u0438\u0436\u0435. \u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u043f\u0440\u043e\u0435\u043a\u0442 \u0432 \u043b\u044e\u0431\u043e\u043c \u043c\u0435\u0441\u0442\u0435 \u043f\u0440\u0438 \u0443\u0441\u043b\u043e\u0432\u0438\u0438, \u0447\u0442\u043e \u0432\u044b \u0443\u043a\u0430\u0436\u0435\u0442\u0435 \u0430\u0432\u0442\u043e\u0440\u0430 \u043f\u0440\u043e\u0435\u043a\u0442\u0430.","buttons":{"mitLicense":"\u041b\u0438\u0446\u0435\u043d\u0437\u0438\u044f MIT"}},"footer":{"credit":"\u0421\u0434\u0435\u043b\u0430\u043d\u043e \u0441 \u043b\u044e\u0431\u043e\u0432\u044c\u044e <1>\u0410\u043c\u0440\u0443\u0442 \u041f\u0438\u043b\u043b\u0430\u0439</1>","thanks":"\u0421\u043f\u0430\u0441\u0438\u0431\u043e \u0437\u0430 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u0435 Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"{{- heading}} \u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd","startDate":{"label":"\u0ba4\u0bca\u0b9f\u0b95\u0bcd\u0b95 \u0ba4\u0bc7\u0ba4\u0bbf"},"endDate":{"label":"\u0b95\u0b9f\u0bc8\u0b9a\u0bbf \u0ba4\u0bc7\u0ba4\u0bbf"},"description":{"label":"\u0bb5\u0bbf\u0bb3\u0b95\u0bcd\u0b95\u0bae\u0bcd"}},"buttons":{"add":{"label":"\u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95"},"delete":{"label":"\u0b85\u0bb4\u0bbf"}},"printDialog":{"heading":"\u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0b9f\u0bb5\u0bc1\u0ba9\u0bcd\u0bb2\u0bcb\u0b9f\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bb5\u0bc1\u0bae\u0bcd","quality":{"label":"\u0ba4\u0bb0\u0bae\u0bcd"},"printType":{"label":"\u0bb5\u0b95\u0bc8","types":{"unconstrained":"\u0b85\u0bb3\u0bb5\u0bc1 \u0b87\u0bb2\u0bcd\u0bb2\u0bc7","fitInA4":"A4 \u0b87\u0bb2\u0bcd \u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd","multiPageA4":"\u0bae\u0bb2\u0bcd\u0b9f\u0bbf-\u0baa\u0bc7\u0b9c\u0bcd A4"}},"helpText":["\u0b87\u0ba8\u0bcd\u0ba4 \u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf \u0bae\u0bc1\u0bb1\u0bc8 HTML \u0b95\u0bc7\u0ba9\u0bcd\u0bb5\u0bbe\u0bb8\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bbf \u0bb5\u0bbf\u0ba3\u0bcd\u0ba3\u0baa\u0bcd\u0baa\u0ba4\u0bcd\u0ba4\u0bc8 \u0b92\u0bb0\u0bc1 \u0baa\u0b9f\u0bae\u0bbe\u0b95 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0bbf PDF \u0b87\u0bb2\u0bcd \u0b85\u0b9a\u0bcd\u0b9a\u0bbf\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1, \u0b85\u0ba4\u0bbe\u0bb5\u0ba4\u0bc1 \u0b87\u0ba4\u0bc1 \u0ba4\u0bc7\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bcd / \u0baa\u0bbe\u0b95\u0bc1\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bae\u0bcd \u0ba4\u0bbf\u0bb1\u0ba9\u0bcd\u0b95\u0bb3\u0bc8 \u0b87\u0bb4\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bcd.","\u0b87\u0ba4\u0bc1 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0bae\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0baf\u0bae\u0bcd \u0b8e\u0ba9\u0bcd\u0bb1\u0bbe\u0bb2\u0bcd, \u0ba4\u0baf\u0bb5\u0bc1\u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bc1 Cmd/Ctrl + P \u0b85\u0bb2\u0bcd\u0bb2\u0ba4\u0bc1 \u0b95\u0bc0\u0bb4\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3 \u0b85\u0b9a\u0bcd\u0b9a\u0bc1 \u0baa\u0bca\u0ba4\u0bcd\u0ba4\u0bbe\u0ba9\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bbf \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0b85\u0b9a\u0bcd\u0b9a\u0bbf\u0b9f \u0bae\u0bc1\u0baf\u0bb1\u0bcd\u0b9a\u0bbf\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd. \u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc0\u0b9f\u0bc1 \u0b89\u0bb2\u0bbe\u0bb5\u0bbf \u0b9a\u0bbe\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc1 \u0b87\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0ba4\u0bbe\u0bb2\u0bcd \u0bae\u0bc1\u0b9f\u0bbf\u0bb5\u0bc1 \u0bae\u0bbe\u0bb1\u0bc1\u0baa\u0b9f\u0bb2\u0bbe\u0bae\u0bcd, \u0b86\u0ba9\u0bbe\u0bb2\u0bcd \u0b87\u0ba4\u0bc1 Google Chrome \u0b87\u0ba9\u0bcd \u0b9a\u0bae\u0bc0\u0baa\u0ba4\u0bcd\u0ba4\u0bbf\u0baf \u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bbf\u0bb2\u0bcd \u0b9a\u0bbf\u0bb1\u0baa\u0bcd\u0baa\u0bbe\u0b95 \u0b9a\u0bc6\u0baf\u0bb2\u0bcd\u0baa\u0b9f\u0bc1\u0bae\u0bcd \u0b8e\u0ba9\u0bcd\u0bb1\u0bc1 \u0b85\u0bb1\u0bbf\u0baf\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1."],"buttons":{"cancel":"\u0bb0\u0ba4\u0bcd\u0ba4\u0bc1\u0b9a\u0bc6\u0baf\u0bcd","saveAsPdf":"PDF \u0b86\u0b95 \u0b9a\u0bc7\u0bae\u0bbf\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd"}},"panZoomAnimation":{"helpText":"\u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0ba8\u0bc6\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bae\u0bbe\u0b95\u0baa\u0bcd \u0baa\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b8e\u0ba8\u0bcd\u0ba4 \u0ba8\u0bc7\u0bb0\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bc1\u0bae\u0bcd \u0b86\u0bb0\u0bcd\u0b9f\u0bcd\u0baa\u0bcb\u0bb0\u0bcd\u0b9f\u0bc8\u0b9a\u0bcd \u0b9a\u0bc1\u0bb1\u0bcd\u0bb1\u0bbf \u0baa\u0bc6\u0bb0\u0bbf\u0ba4\u0bbe\u0b95\u0bcd\u0b95\u0bb2\u0bbe\u0bae\u0bcd."},"markdownHelpText":"\u0b89\u0bb0\u0bc8\u0baf\u0bbf\u0ba9\u0bcd \u0b87\u0ba8\u0bcd\u0ba4 \u0baa\u0b95\u0bc1\u0ba4\u0bbf\u0baf\u0bc8 \u0bb5\u0b9f\u0bbf\u0bb5\u0bae\u0bc8\u0b95\u0bcd\u0b95 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd <1>GitHub Flavored Markdown </ 1> \u0b90\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb2\u0bbe\u0bae\u0bcd."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u0baa\u0bc1\u0b95\u0bc8\u0baa\u0bcd\u0baa\u0b9f\u0bae\u0bcd URL"},"firstName":{"label":"\u0bae\u0bc1\u0ba4\u0bb2\u0bcd \u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"lastName":{"label":"\u0b95\u0b9f\u0bc8\u0b9a\u0bbf \u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"subtitle":{"label":"\u0ba4\u0bc1\u0ba3\u0bc8\u0ba4\u0bcd \u0ba4\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bc1"},"address":{"label":"\u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf","line1":{"label":"\u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf \u0bb5\u0bb0\u0bbf 1"},"line2":{"label":"\u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf \u0bb5\u0bb0\u0bbf 2"},"line3":{"label":"\u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf \u0bb5\u0bb0\u0bbf 3"}},"phone":{"label":"\u0ba4\u0bc6\u0bbe\u0bb2\u0bc8\u0baa\u0bc7\u0b9a\u0bbf \u0b8e\u0ba3\u0bcd"},"website":{"label":"\u0bb5\u0bc6\u0baa\u0bcd\u0b9a\u0bc8\u0b9f\u0bcd"},"email":{"label":"\u0b88\u0bae\u0bc6\u0baf\u0bbf\u0bb2\u0bcd \u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0ba8\u0bcb\u0b95\u0bcd\u0b95\u0bae\u0bcd"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"role":{"label":"\u0baa\u0b99\u0bcd\u0b95\u0bc1"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"major":{"label":"\u0bae\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0baf"},"grade":{"label":"\u0ba4\u0bb0\u0bae\u0bcd"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0ba4\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bc1"},"subtitle":{"label":"\u0ba4\u0bc1\u0ba3\u0bc8\u0ba4\u0bcd \u0ba4\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bc1"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"subtitle":{"label":"\u0b85\u0ba4\u0bbf\u0b95\u0bbe\u0bb0\u0bae\u0bcd"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"level":{"label":"\u0ba8\u0bbf\u0bb2\u0bc8"},"rating":{"label":"\u0bae\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc0\u0b9f\u0bc1"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"position":{"label":"\u0ba8\u0bbf\u0bb2\u0bc8"},"phone":{"label":"\u0ba4\u0bc6\u0bbe\u0bb2\u0bc8\u0baa\u0bc7\u0b9a\u0bbf \u0b8e\u0ba3\u0bcd"},"email":{"label":"\u0b88\u0bae\u0bc6\u0baf\u0bbf\u0bb2\u0bcd \u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0b9a\u0bbe\u0bb5\u0bbf"},"value":{"label":"\u0bae\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc1"}}')},function(e){e.exports=JSON.parse('{"title":"\u0b9f\u0bc6\u0bae\u0bcd\u0baa\u0bcd\u0bb3\u0b9f\u0bcd\u0bb8\u0bcd"}')},function(e){e.exports=JSON.parse('{"title":"\u0bb5\u0ba3\u0bcd\u0ba3\u0b99\u0bcd\u0b95\u0bb3\u0bcd","colorOptions":"\u0bb5\u0ba3\u0bcd\u0ba3 \u0bb5\u0bbf\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bcd","primaryColor":"\u0bae\u0bc1\u0ba4\u0ba9\u0bcd\u0bae\u0bc8 \u0ba8\u0bbf\u0bb1\u0bae\u0bcd","accentColor":"\u0b87\u0bb0\u0ba3\u0bcd\u0b9f\u0bbe\u0bae\u0bcd \u0ba8\u0bbf\u0bb1\u0bae\u0bcd","clipboardCopyAction":"{{color}} \u0b95\u0bbf\u0bb3\u0bbf\u0baa\u0bcd\u0baa\u0bcb\u0bb0\u0bcd\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0ba8\u0b95\u0bb2\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0ba4\u0bc1."}')},function(e){e.exports=JSON.parse('{"title":"\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bb3\u0bcd","fontFamily":{"label":"\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0baa\u0bc7\u0bae\u0bbf\u0bb2\u0bbf","helpText":"\u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b95\u0ba3\u0bbf\u0ba9\u0bbf\u0baf\u0bbf\u0bb2\u0bcd \u0ba8\u0bbf\u0bb1\u0bc1\u0bb5\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b8e\u0ba8\u0bcd\u0ba4 \u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1\u0bb5\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb2\u0bbe\u0bae\u0bcd. \u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0baa\u0bc7\u0bae\u0bbf\u0bb2\u0bbf \u0baa\u0bc6\u0baf\u0bb0\u0bc8 \u0b87\u0b99\u0bcd\u0b95\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3\u0bbf\u0b9f\u0bb5\u0bc1\u0bae\u0bcd, \u0b89\u0bb2\u0bbe\u0bb5\u0bbf \u0b85\u0ba4\u0bc8 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bbe\u0b95 \u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd."}}')},function(e){e.exports=JSON.parse('{"title":"\u0b9a\u0bc6\u0baf\u0bb2\u0bcd\u0b95\u0bb3\u0bcd","disclaimer":"\u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0b87\u0bb2\u0bcd \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0ba4 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b89\u0bb2\u0bbe\u0bb5\u0bbf\u0baf\u0bbf\u0ba9\u0bcd \u0b89\u0bb3\u0bcd\u0bb3\u0bc2\u0bb0\u0bcd \u0b9a\u0bc7\u0bae\u0bbf\u0baa\u0bcd\u0baa\u0b95\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bcd \u0ba4\u0bbe\u0ba9\u0bbe\u0b95\u0bb5\u0bc7 \u0b9a\u0bc7\u0bae\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0bae\u0bcd. \u0ba4\u0bb0\u0bb5\u0bc1 \u0b8e\u0ba4\u0bc1\u0bb5\u0bc1\u0bae\u0bcd \u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc7\u0bb1\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8, \u0b8e\u0ba9\u0bb5\u0bc7 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0ba4\u0b95\u0bb5\u0bb2\u0bcd \u0bae\u0bc1\u0bb1\u0bcd\u0bb1\u0bbf\u0bb2\u0bc1\u0bae\u0bcd \u0baa\u0bbe\u0ba4\u0bc1\u0b95\u0bbe\u0baa\u0bcd\u0baa\u0bbe\u0ba9\u0ba4\u0bc1.","importExport":{"heading":"\u0b87\u0bb1\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0ba4\u0bbf/\u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf","body":"\u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0ba4\u0bb0\u0bb5\u0bc8 JSON \u0bb5\u0b9f\u0bbf\u0bb5\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bcd \u0b87\u0bb1\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0ba4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bb2\u0bbe\u0bae\u0bcd \u0b85\u0bb2\u0bcd\u0bb2\u0ba4\u0bc1 \u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bb2\u0bbe\u0bae\u0bcd. \u0b87\u0ba4\u0ba9\u0bcd \u0bae\u0bc2\u0bb2\u0bae\u0bcd, \u0b8e\u0ba8\u0bcd\u0ba4\u0bb5\u0bca\u0bb0\u0bc1 \u0b9a\u0bbe\u0ba4\u0ba9\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1\u0bae\u0bcd \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bb5\u0bbf\u0ba3\u0bcd\u0ba3\u0baa\u0bcd\u0baa\u0ba4\u0bcd\u0ba4\u0bc8 \u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bb2\u0bbe\u0bae\u0bcd \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0b85\u0b9a\u0bcd\u0b9a\u0bbf\u0b9f\u0bb2\u0bbe\u0bae\u0bcd. \u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0bb0\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4 \u0b87\u0ba8\u0bcd\u0ba4 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc8 \u0b9a\u0bc7\u0bae\u0bbf\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd.","buttons":{"import":"\u0b87\u0bb1\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0ba4\u0bbf","export":"\u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf"}},"downloadResume":{"heading":"\u0b9f\u0bb5\u0bc1\u0ba9\u0bcd\u0bb2\u0bcb\u0b9f\u0bcd \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7","body":"\u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 PDF \u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc8 \u0b9f\u0bb5\u0bc1\u0ba9\u0bcd\u0bb2\u0bcb\u0b9f\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf \u0b95\u0bc0\u0bb4\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3 \u0baa\u0bca\u0ba4\u0bcd\u0ba4\u0bbe\u0ba9\u0bc8\u0b95\u0bcd \u0b95\u0bbf\u0bb3\u0bbf\u0b95\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bb2\u0bbe\u0bae\u0bcd. \u0b9a\u0bbf\u0bb1\u0ba8\u0bcd\u0ba4 \u0bae\u0bc1\u0b9f\u0bbf\u0bb5\u0bc1\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bc1, Google Chrome \u0b87\u0ba9\u0bcd \u0b9a\u0bae\u0bc0\u0baa\u0ba4\u0bcd\u0ba4\u0bbf\u0baf \u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb5\u0bc1\u0bae\u0bcd.","buttons":{"saveAsPdf":"PDF \u0b8e\u0ba9 \u0b9a\u0bc7\u0bae\u0bbf\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd"}},"loadDemoData":{"heading":"\u0b9f\u0bc6\u0bae\u0bcb \u0ba4\u0b95\u0bb5\u0bb2\u0bcd\u0b95\u0bb3\u0bcd \u0b8f\u0bb1\u0bcd\u0bb1\u0bb5\u0bc1\u0bae\u0bcd","body":"\u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0bb5\u0bc6\u0bb1\u0bcd\u0bb1\u0bc1 \u0baa\u0b95\u0bcd\u0b95\u0ba4\u0bcd\u0ba4\u0bc1\u0b9f\u0ba9\u0bcd \u0b8e\u0ba9\u0bcd\u0ba9 \u0b9a\u0bc6\u0baf\u0bcd\u0bb5\u0ba4\u0bc1 \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf\u0ba4\u0bcd\u0ba4\u0bc1 \u0ba4\u0bc6\u0bb3\u0bbf\u0bb5\u0bbe\u0b95 \u0ba4\u0bc6\u0bb0\u0bbf\u0baf\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8\u0baf\u0bbe? \u0b92\u0bb0\u0bc1 \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0b8e\u0bb5\u0bcd\u0bb5\u0bbe\u0bb1\u0bc1 \u0b87\u0bb0\u0bc1\u0b95\u0bcd\u0b95 \u0bb5\u0bc7\u0ba3\u0bcd\u0b9f\u0bc1\u0bae\u0bcd \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bc8\u0b95\u0bcd \u0b95\u0bbe\u0ba3 \u0b9a\u0bbf\u0bb2 \u0b9f\u0bc6\u0bae\u0bcb \u0ba4\u0bb0\u0bb5\u0bc8 \u0bae\u0bc1\u0ba9\u0bcd \u0bae\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bbf\u0b9f\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0bae\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bc1\u0b9f\u0ba9\u0bcd \u0b8f\u0bb1\u0bcd\u0bb1\u0bb5\u0bc1\u0bae\u0bcd, \u0b85\u0b99\u0bcd\u0b95\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1 \u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0ba4\u0bcd \u0ba4\u0bca\u0b9f\u0b99\u0bcd\u0b95\u0bb2\u0bbe\u0bae\u0bcd.","buttons":{"loadData":"\u0ba4\u0b95\u0bb5\u0bb2\u0bcd\u0b95\u0bb3\u0bcd \u0b8f\u0bb1\u0bcd\u0bb1\u0bb5\u0bc1\u0bae\u0bcd"}},"reset":{"heading":"\u0b8e\u0bb2\u0bcd\u0bb2\u0bbe\u0bb5\u0bb1\u0bcd\u0bb1\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0bb5\u0bc1\u0bae\u0bcd!","body":"\u0b87\u0ba8\u0bcd\u0ba4 \u0ba8\u0b9f\u0bb5\u0b9f\u0bbf\u0b95\u0bcd\u0b95\u0bc8 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b8e\u0bb2\u0bcd\u0bb2\u0bbe \u0ba4\u0bb0\u0bb5\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1, \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b89\u0bb2\u0bbe\u0bb5\u0bbf\u0baf\u0bbf\u0ba9\u0bcd \u0b89\u0bb3\u0bcd\u0bb3\u0bc2\u0bb0\u0bcd \u0b9a\u0bc7\u0bae\u0bbf\u0baa\u0bcd\u0baa\u0b95\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b95\u0bbe\u0baa\u0bcd\u0baa\u0bc1\u0baa\u0bcd\u0baa\u0bbf\u0bb0\u0ba4\u0bbf\u0b95\u0bb3\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0b85\u0b95\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd, \u0b8e\u0ba9\u0bb5\u0bc7 \u0b8e\u0bb2\u0bcd\u0bb2\u0bbe\u0bb5\u0bb1\u0bcd\u0bb1\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8\u0baa\u0bcd\u0baa\u0ba4\u0bb1\u0bcd\u0b95\u0bc1 \u0bae\u0bc1\u0ba9\u0bcd\u0baa\u0bc1 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0ba4\u0b95\u0bb5\u0bb2\u0bc8 \u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bc1\u0bb3\u0bcd\u0bb3\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bcd \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bc8 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb5\u0bc1\u0bae\u0bcd.","buttons":{"reset":"\u0bae\u0bbe\u0bb1\u0bcd\u0bb1"}}}')},function(e){e.exports=JSON.parse('{"title":"\u0b85\u0bae\u0bc8\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd","language":{"label":"\u0bae\u0bca\u0bb4\u0bbf","helpText":"\u0baa\u0baf\u0ba9\u0bcd\u0baa\u0bbe\u0b9f\u0bcd\u0b9f\u0bc8 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b9a\u0bca\u0ba8\u0bcd\u0ba4 \u0bae\u0bca\u0bb4\u0bbf\u0baf\u0bbf\u0bb2\u0bcd \u0bae\u0bca\u0bb4\u0bbf\u0baa\u0bc6\u0baf\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0b89\u0ba4\u0bb5 \u0bb5\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bbf\u0ba9\u0bbe\u0bb2\u0bcd, <1>Translation Documentation</1> \u0baa\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd."}}')},function(e){e.exports=JSON.parse('{"title":"\u0baa\u0bb1\u0bcd\u0bb1\u0bbf","documentation":{"heading":"\u0b9f\u0bbe\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bc6\u0ba3\u0bcd\u0b9f\u0bb7\u0ba9\u0bcd","body":"\u0baa\u0baf\u0ba9\u0bcd\u0baa\u0bbe\u0b9f\u0bcd\u0b9f\u0bc8\u0baa\u0bcd \u0baa\u0bb1\u0bcd\u0bb1\u0bbf \u0bae\u0bc7\u0bb2\u0bc1\u0bae\u0bcd \u0b85\u0bb1\u0bbf\u0baf \u0bb5\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bc1\u0b95\u0bbf\u0bb1\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bbe? \u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0ba4\u0bcd\u0ba4\u0bbf\u0bb1\u0bcd\u0b95\u0bc1 \u0b8e\u0bb5\u0bcd\u0bb5\u0bbe\u0bb1\u0bc1 \u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bbf\u0baa\u0bcd\u0baa\u0ba4\u0bc1 \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf\u0ba4\u0bcd\u0ba4 \u0ba4\u0b95\u0bb5\u0bb2\u0bcd \u0ba4\u0bc7\u0bb5\u0bc8\u0baf\u0bbe? \u0bae\u0bc7\u0bb2\u0bc1\u0bae\u0bcd \u0baa\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0bb5\u0bc7\u0ba3\u0bcd\u0b9f\u0bbe\u0bae\u0bcd, \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bbe\u0b95 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b92\u0bb0\u0bc1 \u0bb5\u0bbf\u0bb0\u0bbf\u0bb5\u0bbe\u0ba9 \u0bb5\u0bb4\u0bbf\u0b95\u0bbe\u0b9f\u0bcd\u0b9f\u0bbf \u0b87\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0bb1\u0ba4\u0bc1.","buttons":{"documentation":"\u0b9f\u0bbe\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bc6\u0ba3\u0bcd\u0b9f\u0bb7\u0ba9\u0bcd"}},"bugOrFeatureRequest":{"heading":"\u0baa\u0bc1\u0b95\u0bcd? \u0baa\u0bbf\u0b9f\u0bcd\u0b9f\u0bc1\u0bb1\u0bc7 \u0bb0\u0bc6\u0b83\u0b89\u0b8e\u0bb8\u0bcd\u0ba4\u0bcd?","body":"\u0bae\u0bb1\u0bc1\u0ba4\u0bca\u0b9f\u0b95\u0bcd\u0b95\u0bae\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0bb5\u0ba4\u0bbf\u0bb2\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bae\u0bc1\u0ba9\u0bcd\u0ba9\u0bc7\u0bb1\u0bcd\u0bb1\u0ba4\u0bcd\u0ba4\u0bc8 \u0b8f\u0ba4\u0bc7\u0ba9\u0bc1\u0bae\u0bcd \u0ba4\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0bb1\u0ba4\u0bbe? \u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc7\u0bb1\u0bbe\u0ba4 \u0b92\u0bb0\u0bc1 \u0ba4\u0bca\u0bb2\u0bcd\u0bb2\u0bc8 \u0baa\u0bbf\u0bb4\u0bc8 \u0b95\u0bbf\u0b9f\u0bc8\u0ba4\u0bcd\u0ba4\u0ba4\u0bbe? \u0b95\u0bbf\u0b9f\u0bcd\u0bb9\u0baa\u0bcd \u0b9a\u0bbf\u0b95\u0bcd\u0b95\u0bb2\u0bcd\u0b95\u0bb3\u0bcd \u0baa\u0bbf\u0bb0\u0bbf\u0bb5\u0bbf\u0bb2\u0bcd \u0b87\u0ba4\u0bc8\u0baa\u0bcd \u0baa\u0bb1\u0bcd\u0bb1\u0bbf \u0baa\u0bc7\u0b9a\u0bc1\u0b99\u0bcd\u0b95\u0bb3\u0bcd, \u0b85\u0bb2\u0bcd\u0bb2\u0ba4\u0bc1 \u0b95\u0bc0\u0bb4\u0bc7\u0baf\u0bc1\u0bb3\u0bcd\u0bb3 \u0b9a\u0bc6\u0baf\u0bb2\u0bcd\u0b95\u0bb3\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bbf \u0b8e\u0ba9\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bcd \u0bae\u0bbf\u0ba9\u0bcd\u0ba9\u0b9e\u0bcd\u0b9a\u0bb2\u0bcd \u0b85\u0ba9\u0bc1\u0baa\u0bcd\u0baa\u0bb5\u0bc1\u0bae\u0bcd.","buttons":{"raiseIssue":" \u0b9a\u0bbf\u0b95\u0bcd\u0b95\u0bb2\u0bc8  \u0b95\u0bbe\u0b9f\u0bcd\u0b9f\u0bc1\\n","sendEmail":"\u0b88\u0bae\u0bc6\u0baf\u0bbf\u0bb2\u0bcd \u0b85\u0ba9\u0bc1\u0baa\u0bcd\u0baa\u0bc1\u0b95"}},"sourceCode":{"heading":"\u0bae\u0bc2\u0bb2 \u0b95\u0bbe\u0b9f\u0bc7","body":"\u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0ba4\u0bcd\u0ba4\u0bc8 \u0b85\u0ba4\u0ba9\u0bcd \u0bae\u0bc2\u0bb2\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1 \u0b87\u0baf\u0b95\u0bcd\u0b95 \u0bb5\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bc1\u0b95\u0bbf\u0bb1\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bbe? \u0b87\u0ba8\u0bcd\u0ba4 \u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0ba4\u0bcd\u0ba4\u0bbf\u0ba9\u0bcd \u0ba4\u0bbf\u0bb1\u0ba8\u0bcd\u0ba4 \u0bae\u0bc2\u0bb2 \u0bae\u0bc7\u0bae\u0bcd\u0baa\u0bbe\u0b9f\u0bcd\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bbf\u0b95\u0bcd\u0b95 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b92\u0bb0\u0bc1 \u0b9f\u0bc6\u0bb5\u0bb2\u0baa\u0bcd\u0baa\u0bb0\u0bbe? \u0b95\u0bc0\u0bb4\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3 \u0baa\u0bca\u0ba4\u0bcd\u0ba4\u0bbe\u0ba9\u0bc8\u0b95\u0bcd \u0b95\u0bbf\u0bb3\u0bbf\u0b95\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0b95.","buttons":{"githubRepo":"\u0b95\u0bc1\u0ba4\u0bc1\u0baa\u0bcd \u0bb0\u0bc6\u0baa\u0bcb"}},"license":{"heading":"\u0b89\u0bb0\u0bbf\u0bae\u0ba4\u0bcd \u0ba4\u0b95\u0bb5\u0bb2\u0bcd","body":"\u0b87\u0ba8\u0bcd\u0ba4 \u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0bae\u0bcd MIT \u0b89\u0bb0\u0bbf\u0bae\u0ba4\u0bcd\u0ba4\u0bbf\u0ba9\u0bcd \u0b95\u0bc0\u0bb4\u0bcd \u0ba8\u0bbf\u0bb0\u0bcd\u0bb5\u0b95\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1, \u0b85\u0ba4\u0bc8 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b95\u0bc0\u0bb4\u0bc7 \u0bae\u0bc7\u0bb2\u0bc1\u0bae\u0bcd \u0baa\u0b9f\u0bbf\u0b95\u0bcd\u0b95\u0bb2\u0bbe\u0bae\u0bcd. \u0b85\u0b9f\u0bbf\u0baa\u0bcd\u0baa\u0b9f\u0bc8\u0baf\u0bbf\u0bb2\u0bcd, \u0b85\u0b9a\u0bb2\u0bcd \u0b86\u0b9a\u0bbf\u0bb0\u0bbf\u0baf\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bb5\u0bb0\u0bb5\u0bc1\u0b95\u0bb3\u0bc8 \u0bb5\u0bb4\u0b99\u0bcd\u0b95\u0bbf\u0ba9\u0bbe\u0bb2\u0bcd, \u0b8e\u0b99\u0bcd\u0b95\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1\u0bae\u0bcd \u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0ba4\u0bcd\u0ba4\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4 \u0b85\u0ba9\u0bc1\u0bae\u0ba4\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0bb5\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bcd.","buttons":{"mitLicense":"MIT \u0bb2\u0bbf\u0b9a\u0bc6\u0ba9\u0bcd\u0bb8\u0bcd"}},"footer":{"credit":"<1>Amruth Pillai<1>\u0baf\u0bbe\u0bb2\u0bcd \u0b85\u0ba9\u0bcd\u0baa\u0bbe\u0bb2\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0ba4\u0bc1","thanks":"Reactive Resume \u0b90\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bbf\u0baf\u0ba4\u0bb1\u0bcd\u0b95\u0bc1 \u0ba8\u0ba9\u0bcd\u0bb1\u0bbf!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u0414\u043e\u0434\u0430\u0442\u0438 {{- heading}}","startDate":{"label":"\u0414\u0430\u0442\u0430 \u043f\u043e\u0447\u0430\u0442\u043a\u0443"},"endDate":{"label":"\u0414\u0430\u0442\u0430 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u043d\u044f"},"description":{"label":"\u041e\u043f\u0438\u0441"}},"buttons":{"add":{"label":"\u0414\u043e\u0434\u0430\u0442\u0438"},"delete":{"label":"Delete"}},"printDialog":{"heading":"\u0417\u0430\u0432\u0430\u043d\u0442\u0430\u0436\u0438\u0442\u0438 \u0440\u0435\u0437\u044e\u043c\u0435","quality":{"label":"\u042f\u043a\u0456\u0441\u0442\u044c"},"printType":{"label":"\u0422\u0438\u043f","types":{"unconstrained":"\u041d\u0435 \u0432\u0438\u0437\u043d\u0430\u0447\u0435\u043d\u043e","fitInA4":"\u0412\u043c\u0456\u0441\u0442\u0438\u0442\u0438 \u0432 A4","multiPageA4":"Multi-Page A4"}},"helpText":["\u0426\u0435\u0439 \u043c\u0435\u0442\u043e\u0434 \u0435\u043a\u0441\u043f\u043e\u0440\u0442\u0443 \u0432\u0438\u043a\u043e\u0440\u0438\u0441\u0442\u043e\u0432\u0443\u0454 HTML canvas \u0434\u043b\u044f \u043f\u0435\u0440\u0435\u0442\u0432\u043e\u0440\u0435\u043d\u043d\u044f \u0440\u0435\u0437\u044e\u043c\u0435 \u0432 \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f \u0442\u0430 \u0434\u0440\u0443\u043a\u0443\u0432\u0430\u043d\u043d\u044f \u0439\u043e\u0433\u043e \u043d\u0430 PDF, \u0446\u0435 \u0437\u043d\u0430\u0447\u0438\u0442\u044c, \u0449\u043e \u0432\u0456\u043d \u0432\u0442\u0440\u0430\u0442\u0438\u0442\u044c \u0432\u0441\u0456 \u043c\u043e\u0436\u043b\u0438\u0432\u043e\u0441\u0442\u0456 \u0432\u0456\u0434\u0431\u043e\u0440\u0443/\u043f\u0430\u0440\u0441\u0456\u043d\u0433\u0443.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"\u0417\u0430\u0432\u0430\u043d\u0442\u0430\u0436\u0438\u0442\u0438 \u0440\u0435\u0437\u044e\u043c\u0435","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Th\xeam {{- heading}}","startDate":{"label":"B\u1eaft \u0111\u1ea7u"},"endDate":{"label":"K\u1ebft th\xfac"},"description":{"label":"M\xf4 t\u1ea3"}},"buttons":{"add":{"label":"Th\xeam"},"delete":{"label":"X\xf3a"}},"printDialog":{"heading":"T\u1ea3i B\u1ea3n tr\xedch ngang v\u1ec1","quality":{"label":"Ch\u1ea5t l\u01b0\u1ee3ng"},"printType":{"label":"Ki\u1ec3u","types":{"unconstrained":"T\u1ef1 do","fitInA4":"V\u1eeba trang A4","multiPageA4":"Nhi\u1ec1u trang A4"}},"helpText":["Ph\u01b0\u01a1ng ph\xe1p xu\u1ea5t n\xe0y x\u1eed d\u1ee5ng HTML canvas \u0111\u1ec3 chuy\u1ec3n d\u1ea1ng b\u1ea3n tr\xedch ngang th\xe0nh m\u1ed9t \u1ea3nh r\u1ed3i in n\xf3 tr\xean m\u1ed9t PDF, \u0111i\u1ec1u n\xe0y c\xf3 ngh\u0129a l\xe0 n\xf3 s\u1ebd m\u1ea5t t\u1ea5t c\u1ea3 c\xe1c t\xednh n\u0103ng ch\u1ecdn l\u1ef1a (selecting)/ph\xe2n t\xedch (parsing).","N\u1ebfu vi\u1ec7c c\xf3 c\xe1c t\xednh n\u0103ng \u0111\xf3 quan tr\u1ecdng \u0111\u1ed1i v\u1edbi b\u1ea1n, h\xe3y th\u1eed in b\u1ea3n tr\xedch ngang, b\u1eb1ng ph\xedm Cmd/Ctrl + P ho\u1eb7c n\xfat in d\u01b0\u1edbi \u0111\xe2y. K\u1ebft qu\u1ea3 c\xf3 th\u1ec3 sai kh\xe1c v\xec \u0111\u1ea7u ra ph\u1ee5 thu\u1ed9c v\xe0o tr\xecnh duy\u1ec7t, nh\u01b0ng n\xf3 s\u1ebd ho\u1ea1t \u0111\u1ed9ng t\u1ed1t nh\u1ea5t tr\xean phi\xean b\u1ea3n Google Chrome m\u1edbi nh\u1ea5t."],"buttons":{"cancel":"H\u1ee7y b\u1ecf","saveAsPdf":"L\u01b0u th\xe0nh PDF"}},"panZoomAnimation":{"helpText":"B\u1ea1n c\xf3 th\u1ec3 k\xe9o v\xe0 thu ph\xf3ng b\u1ea3n thi\u1ebft k\u1ebf b\u1ea5t k\xec l\xfac n\xe0o \u0111\u1ec3 xem b\u1ea3n tr\xedch ngang c\u1ee7a m\xecnh r\xf5 h\u01a1n."},"markdownHelpText":"B\u1ea1n c\xf3 th\u1ec3 d\xf9ng <1>c\xfa ph\xe1p Markdown c\u1ee7a GitHub</1> \u0111\u1ec3 t\u1ea1o ki\u1ec3u cho ch\u1eef \u1edf ph\u1ea7n n\xe0y."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL \u1ea2nh"},"firstName":{"label":"T\xean"},"lastName":{"label":"H\u1ecd"},"subtitle":{"label":"Ch\u1ee9c danh"},"address":{"label":"\u0110\u1ecba ch\u1ec9","line1":{"label":"\u0110\u1ecba ch\u1ec9, D\xf2ng 1"},"line2":{"label":"\u0110\u1ecba ch\u1ec9, D\xf2ng 2"},"line3":{"label":"\u0110\u1ecba ch\u1ec9, D\xf2ng 3"}},"phone":{"label":"S\u1ed1 \u0110i\u1ec7n tho\u1ea1i"},"website":{"label":"Trang web"},"email":{"label":"\u0110\u1ecba ch\u1ec9 Email"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"M\u1ee5c ti\xeau"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"T\xean"},"role":{"label":"V\u1ecb tr\xed"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"T\xean"},"major":{"label":"Ng\xe0nh"},"grade":{"label":"\u0110i\u1ec3m"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Ti\xeau \u0111\u1ec1"},"subtitle":{"label":"Ti\xeau \u0111\u1ec1 ph\u1ee5"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"T\xean"},"subtitle":{"label":"Trao b\u1edfi"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"T\xean"},"level":{"label":"Tr\xecnh \u0111\u1ed9"},"rating":{"label":"\u0110\xe1nh gi\xe1"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"T\xean"},"position":{"label":"Ch\u1ee9c v\u1ee5"},"phone":{"label":"S\u1ed1 \u0110i\u1ec7n tho\u1ea1i"},"email":{"label":"\u0110\u1ecba ch\u1ec9 Email"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"T\xean tr\u01b0\u1eddng"},"value":{"label":"N\u1ed9i dung tr\u01b0\u1eddng"}}')},function(e){e.exports=JSON.parse('{"title":"Ki\u1ec3u m\u1eabu"}')},function(e){e.exports=JSON.parse('{"title":"M\xe0u s\u1eafc","colorOptions":"L\u1ef1a ch\u1ecdn M\xe0u","primaryColor":"M\xe0u Ch\xednh","accentColor":"M\xe0u Ph\u1ee5","clipboardCopyAction":"{{color}} \u0111\xe3 \u0111\u01b0\u1ee3c ch\xe9p v\xe0o b\u1ea3ng nh\xe1p (clipboard)."}')},function(e){e.exports=JSON.parse('{"title":"Ph\xf4ng ch\u1eef","fontFamily":{"label":"H\u1ecd Ph\xf4ng ch\u1eef","helpText":"B\u1ea1n c\u0169ng c\xf3 th\u1ec3 d\xf9ng b\u1ea5t k\xec ph\xf4ng n\xe0o \u0111\xe3 c\xe0i tr\xean m\xe1y c\u1ee7a b\u1ea1n. Ch\u1ec9 c\u1ea7n \u0111i\u1ec1n t\xean c\u1ee7a h\u1ecd ph\xf4ng \u0111\xf3 v\xe0o \u0111\xe2y v\xe0 tr\xecnh duy\u1ec7t s\u1ebd t\u1ea3i n\xf3 l\xean cho b\u1ea1n."}}')},function(e){e.exports=JSON.parse('{"title":"Thao t\xe1c","disclaimer":"Nh\u1eefng g\xec b\u1ea1n thay \u0111\u1ed5i trong b\u1ea3n tr\xedch ngang c\u1ee7a m\xecnh \u0111\u01b0\u1ee3c t\u1ef1 \u0111\u1ed9ng l\u01b0u v\xe0o v\xf9ng l\u01b0u tr\u1eef c\u1ee5c b\u1ed9 (local storage) c\u1ee7a tr\xecnh duy\u1ec7t. Kh\xf4ng ch\xfat d\u1eef li\u1ec7u n\xe0o tho\xe1t ra ngo\xe0i, n\xean th\xf4ng tin c\u1ee7a b\u1ea1n ho\xe0n to\xe0n \u0111\u01b0\u1ee3c b\u1ea3o v\u1ec7.","importExport":{"heading":"Nh\u1eadp/Xu\u1ea5t","body":"B\u1ea1n c\xf3 th\u1ec3 nh\u1eadp ho\u1eb7c xu\u1ea5t d\u1eef li\u1ec7u c\u1ee7a m\xecnh \u1edf d\u1ea1ng JSON. B\u1eb1ng c\xe1ch n\xe0y, b\u1ea1n c\xf3 th\u1ec3 ch\u1ec9nh s\u1eeda v\xe0 in b\u1ea3n tr\xedch ngang c\u1ee7a m\xecnh tr\xean b\u1ea5t k\xec thi\u1ebft b\u1ecb n\xe0o. L\u01b0u t\u1ec7p n\xe0y l\u1ea1i \u0111\u1ec3 d\xf9ng v\u1ec1 sau.","buttons":{"import":"Nh\u1eadp","export":"Xu\u1ea5t"}},"downloadResume":{"heading":"T\u1ea3i B\u1ea3n tr\xedch ngang v\u1ec1","body":"B\u1ea1n c\xf3 th\u1ec3 \u1ea5n n\xfat d\u01b0\u1edbi \u0111\xe2y \u0111\u1ec3 t\u1ea3i v\u1ec1 ngay b\u1ea3n tr\xedch ngang \u1edf d\u1ea1ng PDF. \u0110\u1ec3 c\xf3 k\u1ebft qu\u1ea3 t\u1ed1t nh\u1ea5t, h\xe3y x\u1eed d\u1ee5ng Google Chrome b\u1ea3n m\u1edbi nh\u1ea5t.","buttons":{"saveAsPdf":"L\u01b0u th\xe0nh PDF"}},"loadDemoData":{"heading":"T\u1ea3i D\u1eef li\u1ec7u Minh h\u1ecda","body":"Kh\xf4ng r\xf5 n\xean l\xe0m g\xec v\u1edbi m\u1ed9t trang tr\u1ed1ng tr\u01a1n? T\u1ea3i v\xe0i d\u1eef li\u1ec7u minh h\u1ecda v\u1edbi c\xe1c gi\xe1 tr\u1ecb \u0111\u1eb7t s\u1eb5n \u0111\u1ec3 th\u1ea5y m\u1ed9t b\u1ea3n tr\xedch ngang tr\xf4ng th\u1ebf n\xe0o v\xe0 t\u1eeb \u0111\xf3 b\u1ea1n c\xf3 th\u1ec3 b\u1eaft \u0111\u1ea7u ch\u1ec9nh s\u1eeda.","buttons":{"loadData":"T\u1ea3i D\u1eef li\u1ec7u"}},"reset":{"heading":"\u0110\u1eb7t l\u1ea1i T\u1ea5t c\u1ea3!","body":"Thao t\xe1c n\xe0y s\u1ebd \u0111\u1eb7t l\u1ea1i t\u1ea5t c\u1ea3 d\u1eef li\u1ec7u c\u1ee7a b\u1ea1n v\xe0 x\xf3a c\u1ea3 c\xe1c b\u1ea3n sao l\u01b0u v\xf9ng l\u01b0u tr\u1eef c\u1ee5c b\u1ed9 (local storage) c\u1ee7a tr\xecnh duy\u1ec7t, n\xean h\xe3y ch\u1eafc ch\u1eafn l\xe0 b\u1ea1n \u0111\xe3 xu\u1ea5t th\xf4ng tin ra tr\u01b0\u1edbc khi \u0111\u1eb7t l\u1ea1i m\u1ecdi th\u1ee9.","buttons":{"reset":"\u0110\u1eb7t l\u1ea1i"}}}')},function(e){e.exports=JSON.parse('{"title":"C\xe0i \u0111\u1eb7t","language":{"label":"Ng\xf4n ng\u1eef","helpText":"N\u1ebfu b\u1ea1n mu\u1ed1n gi\xfap d\u1ecbch \u1ee9ng d\u1ee5ng n\xe0y sang ng\xf4n ng\u1eef c\u1ee7a m\xecnh, xin h\xe3y tra c\u1ee9u <1>H\u01b0\u1edbng d\u1eabn D\u1ecbch thu\u1eadt</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Gi\u1edbi thi\u1ec7u","documentation":{"heading":"T\xe0i li\u1ec7u h\u01b0\u1edbng d\u1eabn","body":"Mu\u1ed1n bi\u1ebft th\xeam v\u1ec1 \u1ee9ng d\u1ee5ng n\xe0y? C\u1ea7n th\xf4ng tin v\u1ec1 c\xe1ch \u0111\xf3ng g\xf3p cho d\u1ef1 \xe1n? Kh\xf4ng ph\u1ea3i t\xecm th\xeam n\u1eefa, c\xf3 m\u1ed9t h\u01b0\u1edbng d\u1eabn \u0111\u1ea7y \u0111\u1ee7 d\xe0nh cho b\u1ea1n.","buttons":{"documentation":"T\xe0i li\u1ec7u h\u01b0\u1edbng d\u1eabn"}},"bugOrFeatureRequest":{"heading":"L\u1ed7i? \u0110\u1ec1 xu\u1ea5t T\xednh n\u0103ng?","body":"C\xf3 g\xec \u0111\xf3 g\xe2y c\u1ea3n tr\u1edf vi\u1ec7c b\u1ea1n l\xe0m b\u1ea3n tr\xedch ngang? T\xecm ra m\u1ed9t l\u1ed7i phi\u1ec1n h\xe0 dai d\u1eb3ng? N\xf3i v\u1ec1 chuy\u1ec7n \u0111\xf3 \u1edf m\u1ee5c Issues tr\xean GitHub, ho\u1eb7c g\u1eedi cho t\xf4i m\u1ed9t email, b\u1eb1ng c\xe1c thao t\xe1c d\u01b0\u1edbi \u0111\xe2y.","buttons":{"raiseIssue":"\u0110\u01b0a ra V\u1ea5n \u0111\u1ec1","sendEmail":"G\u1eedi Email"}},"sourceCode":{"heading":"M\xe3 Ngu\u1ed3n","body":"Mu\u1ed1n ch\u1ea1y d\u1ef1 \xe1n t\u1eeb m\xe3 ngu\u1ed3n? L\xe0 m\u1ed9t l\u1eadp tr\xecnh vi\xean mu\u1ed1n \u0111\xf3ng g\xf3p cho vi\u1ec7c ph\xe1t tri\u1ec3n ngu\u1ed3n m\u1edf c\u1ee7a d\u1ef1 \xe1n n\xe0y? \u1ea4n n\xfat d\u01b0\u1edbi \u0111\xe2y.","buttons":{"githubRepo":"Kho tr\xean GitHub"}},"license":{"heading":"Th\xf4ng tin Gi\u1ea5y ph\xe9p","body":"D\u1ef1 \xe1n n\xe0y \u0111\u01b0\u1ee3c \u0111i\u1ec1u ch\u1ec9nh b\u1edfi Gi\u1ea5y ph\xe9p MIT, b\u1ea1n c\xf3 th\u1ec3 \u0111\u1ecdc th\xeam v\u1ec1 n\xf3 d\u01b0\u1edbi \u0111\xe2y. V\u1ec1 c\u01a1 b\u1ea3n, b\u1ea1n \u0111\u01b0\u1ee3c s\u1eed d\u1ee5ng d\u1ef1 \xe1n n\xe0y \u1edf b\u1ea5t k\xec \u0111\xe2u, ch\u1ec9 c\u1ea7n b\u1ea1n c\xf4ng nh\u1eadn t\xe1c gi\u1ea3 g\u1ed1c.","buttons":{"mitLicense":"Gi\u1ea5y ph\xe9p MIT"}},"footer":{"credit":"\u0110\u01b0\u1ee3c t\u1ea1o ra, b\u1eb1ng T\xecnh c\u1ea3m, b\u1edfi <1>Amruth Pillai</1>.","thanks":"C\u1ea3m \u01a1n b\u1ea1n \u0111\xe3 x\u1eed d\u1ee5ng Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u6dfb\u52a0 {{- heading}}","startDate":{"label":"\u5f00\u59cb\u65e5\u671f"},"endDate":{"label":"\u7ed3\u675f\u65e5\u671f"},"description":{"label":"\u8bf4\u660e"}},"buttons":{"add":{"label":"\u6dfb\u52a0"},"delete":{"label":"\u5220\u9664"}},"printDialog":{"heading":"\u4e0b\u8f7d\u7b80\u5386","quality":{"label":"\u54c1\u8d28"},"printType":{"label":"\u7c7b\u578b","types":{"unconstrained":"\u4e0d\u53d7\u9650\u5236","fitInA4":"\u9002\u5408A4","multiPageA4":"\u591a\u9875A4"}},"helpText":["\u6b64\u5bfc\u51fa\u65b9\u6cd5\u4f7f\u7528 HTML \u753b\u5e03\u5c06\u6062\u590d\u8f6c\u6362\u4e3a\u56fe\u50cf\u5e76\u6253\u5370\u5230 PDF \u4e0a\u3002 \u8fd9\u610f\u5473\u7740\u5b83\u5c06\u5931\u53bb\u6240\u6709\u9009\u62e9/\u89e3\u6790\u80fd\u529b\u3002","\u5982\u679c\u8fd9\u5bf9\u60a8\u5f88\u91cd\u8981\uff0c\u8bf7\u5c1d\u8bd5\u6253\u5370\u7eed\u7248\uff0c\u800c\u4e0d\u662f\u4f7f\u7528 Cmd/Ctrl + P \u6216\u4e0b\u9762\u7684\u6253\u5370\u6309\u94ae\u3002 \u7ed3\u679c\u53ef\u80fd\u4f1a\u56e0\u4e3a\u8f93\u51fa\u4f9d\u8d56\u4e8e\u6d4f\u89c8\u5668\u800c\u6709\u6240\u6539\u53d8\uff0c\u4f46\u5b83\u5df2\u77e5\u6700\u9002\u5408\u6700\u65b0\u7248\u672c\u7684Google Chrome\u3002"],"buttons":{"cancel":"\u53d6\u6d88","saveAsPdf":"\u4fdd\u5b58\u4e3a PDF"}},"panZoomAnimation":{"helpText":"\u60a8\u53ef\u4ee5\u968f\u65f6\u5728\u753b\u677f\u4e0a\u5e73\u79fb\u548c\u7f29\u653e\uff0c\u4ee5\u66f4\u4ed4\u7ec6\u5730\u67e5\u770b\u7b80\u5386\u3002"},"markdownHelpText":"\u4f60\u53ef\u4ee5\u4f7f\u7528 <1>GitHub \u503e\u5411\u7684 Markdown</1> \u6765\u7f8e\u5316\u8fd9\u90e8\u5206\u6587\u5b57."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u7167\u7247\u94fe\u63a5"},"firstName":{"label":"\u540d"},"lastName":{"label":"\u59d3"},"subtitle":{"label":"\u804c\u4f4d"},"address":{"label":"\u5730\u5740","line1":{"label":"\u5730\u5740\u680f 1"},"line2":{"label":"\u5730\u5740\u680f 2"},"line3":{"label":"\u5730\u5740\u680f 3"}},"phone":{"label":"\u7535\u8bdd\u53f7\u7801"},"website":{"label":"\u7f51\u7ad9"},"email":{"label":"\u90ae\u7bb1\u5730\u5740"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u6c42\u804c\u610f\u5411"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u516c\u53f8"},"role":{"label":"\u804c\u4f4d"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u5b66\u6821"},"major":{"label":"\u4e3b\u4fee\u8bfe\u7a0b"},"grade":{"label":"\u5b66\u5206"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u540d\u79f0"},"subtitle":{"label":"\u5956\u9879"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u540d\u79f0"},"subtitle":{"label":"\u9881\u53d1\u673a\u6784"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u540d\u79f0"},"level":{"label":"\u7ea7\u522b"},"rating":{"label":"\u7b49\u7ea7"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u540d\u5b57"},"position":{"label":"\u804c\u4f4d"},"phone":{"label":"\u7535\u8bdd\u53f7\u7801"},"email":{"label":"\u90ae\u7bb1\u5730\u5740"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u540d\u79f0"},"value":{"label":"\u5185\u5bb9"}}')},function(e){e.exports=JSON.parse('{"title":"\u6a21\u677f"}')},function(e){e.exports=JSON.parse('{"title":"\u989c\u8272","colorOptions":"\u989c\u8272\u9009\u9879","primaryColor":"\u4e3b\u8272\u8c03","accentColor":"\u8f85\u52a9\u989c\u8272","clipboardCopyAction":"{{color}} \u5df2\u88ab\u590d\u5236\u5230\u526a\u8d34\u677f\u3002"}')},function(e){e.exports=JSON.parse('{"title":"\u5b57\u4f53","fontFamily":{"label":"\u5b57\u4f53\u5e93","helpText":"\u60a8\u4e5f\u53ef\u4ee5\u4f7f\u7528\u5b89\u88c5\u5728\u60a8\u7684\u7cfb\u7edf\u4e0a\u7684\u4efb\u4f55\u5b57\u4f53\u3002 \u53ea\u9700\u5728\u6b64\u8f93\u5165\u5b57\u4f53\u5e93\u7684\u540d\u5b57\uff0c\u6d4f\u89c8\u5668\u5c06\u4e3a\u60a8\u52a0\u8f7d\u5b83\u3002"}}')},function(e){e.exports=JSON.parse('{"title":"\u64cd\u4f5c","disclaimer":"\u60a8\u5bf9\u60a8\u7684\u7b80\u5386\u6240\u4f5c\u7684\u66f4\u6539\u5c06\u81ea\u52a8\u4fdd\u5b58\u5230\u60a8\u7684\u6d4f\u89c8\u5668\u672c\u5730\u5b58\u50a8\u3002\u6ca1\u6709\u6570\u636e\u6d41\u51fa\uff0c\u56e0\u6b64\u60a8\u7684\u4fe1\u606f\u662f\u5b8c\u5168\u5b89\u5168\u7684\u3002","importExport":{"heading":"\u5bfc\u5165/\u5bfc\u51fa","body":"\u60a8\u53ef\u4ee5\u4ee5 JSON \u683c\u5f0f\u5bfc\u5165\u6216\u5bfc\u51fa\u60a8\u7684\u6570\u636e\u3002 \u8fd9\u6837\uff0c\u60a8\u53ef\u4ee5\u4ece\u4efb\u4f55\u8bbe\u5907\u4e0a\u7f16\u8f91\u548c\u6253\u5370\u60a8\u7684\u7b80\u5386\u3002\u4fdd\u5b58\u6b64\u6587\u4ef6\u4f9b\u4ee5\u540e\u4f7f\u7528\u3002","buttons":{"import":"\u5bfc\u5165","export":"\u5bfc\u51fa"}},"downloadResume":{"heading":"\u4e0b\u8f7d\u7b80\u5386","body":"\u60a8\u53ef\u4ee5\u5355\u51fb\u4e0b\u9762\u7684\u6309\u94ae\u7acb\u5373\u4e0b\u8f7d\u7b80\u5386\u7684PDF\u7248\u672c\u3002 \u4e3a\u4e86\u83b7\u5f97\u6700\u4f73\u6548\u679c\uff0c\u8bf7\u4f7f\u7528\u6700\u65b0\u7248\u672c\u7684Google Chrome\u3002","buttons":{"saveAsPdf":"\u4fdd\u5b58\u4e3a PDF"}},"loadDemoData":{"heading":"\u52a0\u8f7d\u6a21\u7248\u6570\u636e","body":"\u4e0d\u6e05\u695a\u7528\u65b0\u7684\u7a7a\u767d\u9875\u9762\u505a\u4ec0\u4e48\uff1f\u52a0\u8f7d\u4e00\u4e9b\u5e26\u6709\u9884\u7f6e\u503c\u7684\u865a\u62df\u6570\u636e\u6765\u67e5\u770b\u7b80\u5386\u7684\u683c\u5f0f\uff0c\u60a8\u53ef\u4ee5\u4ece\u90a3\u91cc\u5f00\u59cb\u7f16\u8f91\u3002","buttons":{"loadData":"\u8f7d\u5165\u6570\u636e"}},"reset":{"heading":"\u5168\u90e8\u91cd\u7f6e!","body":"\u6b64\u64cd\u4f5c\u5c06\u91cd\u7f6e\u60a8\u6240\u6709\u7684\u6570\u636e\u5e76\u5220\u9664\u6d4f\u89c8\u5668\u672c\u5730\u5b58\u50a8\u5907\u4efd\uff0c\u8bf7\u786e\u4fdd\u60a8\u5728\u91cd\u5236\u524d\u5df2\u7ecf\u5bfc\u51fa\u60a8\u7684\u4fe1\u606f\u3002","buttons":{"reset":"\u91cd\u7f6e"}}}')},function(e){e.exports=JSON.parse('{"title":"\u8bbe\u7f6e","language":{"label":"\u8bed\u8a00","helpText":"\u5982\u679c\u60a8\u5e0c\u671b\u5e2e\u52a9\u5c06\u5e94\u7528\u7a0b\u5e8f\u7ffb\u8bd1\u6210\u60a8\u81ea\u5df1\u7684\u8bed\u8a00\uff0c\u8bf7\u53c2\u9605<1>\u7ffb\u8bd1\u6587\u6863</1>\u3002"}}')},function(e){e.exports=JSON.parse('{"title":"\u5173\u4e8e\u6211\u4eec","documentation":{"heading":"\u6587\u6863","body":"\u60f3\u8fdb\u4e00\u6b65\u4e86\u89e3\u8be5\u5e94\u7528\u7a0b\u5e8f\uff1f \u9700\u8981\u6709\u5173\u5982\u4f55\u4e3a\u8be5\u9879\u76ee\u505a\u51fa\u8d21\u732e\u7684\u4fe1\u606f\uff1f \u522b\u65e0\u6240\u6c42\uff0c\u8fd9\u91cc\u6709\u4e13\u95e8\u4e3a\u60a8\u51c6\u5907\u7684\u7efc\u5408\u6307\u5357\u3002","buttons":{"documentation":"\u6587\u6863"}},"bugOrFeatureRequest":{"heading":"\u9519\u8bef\uff1f\u529f\u80fd\u8bf7\u6c42\uff1f","body":"\u5728\u4f60\u5199\u7b80\u5386\u7684\u65f6\u5019\u53d1\u73b0\u9047\u5230\u56f0\u96be\uff1f\u53d1\u73b0\u4e86\u4e00\u4e2a\u70e6\u4eba\u7684\u95ee\u9898\uff1f \u4f60\u53ef\u4ee5\u4e0aGithub\u95ee\u9898\u7248\u6765\u8c08\u8bba\u8fd9\u4e2a\u95ee\u9898\uff0c\u6216\u8005\u901a\u8fc7\u4e0b\u9762\u7684\u6309\u94ae\u6765\u7ed9\u6211\u53d1\u9001\u7535\u5b50\u90ae\u4ef6\u3002","buttons":{"raiseIssue":"\u63d0\u4ea4\u6539\u8fdb","sendEmail":"\u53d1\u9001\u90ae\u4ef6"}},"sourceCode":{"heading":"\u6e90\u7801","body":"\u60f3\u8981\u672c\u5730\u8fd0\u884c\u8fd9\u4e2a\u9879\u76ee\uff1f \u4f60\u662f\u4e0d\u662f\u4e00\u4e2a\u5f00\u53d1\u8005\u60f3\u8981\u4e3a\u8fd9\u4e2a\u5f00\u6e90\u9879\u76ee\u505a\u51fa\u8d21\u732e\uff1f\u8bf7\u70b9\u51fb\u4e0b\u9762\u7684\u6309\u94ae\u3002","buttons":{"githubRepo":"GitHub \u9879\u76ee"}},"license":{"heading":"\u6388\u6743\u7533\u660e","body":"\u8be5\u9879\u76ee\u53d7MIT\u8bb8\u53ef\u7684\u7ba1\u7406\uff0c\u60a8\u53ef\u4ee5\u5728\u4e0b\u9762\u9605\u8bfb\u66f4\u591a\u4fe1\u606f\u3002 \u57fa\u672c\u4e0a\uff0c\u53ea\u8981\u60a8\u5411\u539f\u4f5c\u8005\u63d0\u4f9b\u611f\u8c22\uff0c\u60a8\u5c31\u53ef\u4ee5\u5728\u4efb\u4f55\u5730\u65b9\u4f7f\u7528\u8be5\u9879\u76ee\u3002","buttons":{"mitLicense":"MIT \u6388\u6743\u8bb8\u53ef"}},"footer":{"credit":"<1>Amruth Pillai</1> \u7528\u7231\u5236\u9020","thanks":"\u611f\u8c22\u60a8\u4f7f\u7528 Reactive Resume\uff01"}}')},,,,function(e){e.exports=JSON.parse('{"data":{"profile":{"heading":"Profile"},"objective":{"enable":true,"heading":"Professional Objective"},"work":{"enable":true,"heading":"Work Experience"},"education":{"enable":true,"heading":"Education"},"awards":{"enable":true,"heading":"Honors & Awards"},"certifications":{"enable":true,"heading":"Certifications"},"skills":{"enable":true,"heading":"Skills"},"Memberships":{"enable":true,"heading":"Memberships"},"languages":{"enable":true,"heading":"Languages"},"references":{"enable":true,"heading":"References"},"extras":{"enable":true,"heading":"Additional Information"},"address":{"enable":true,"heading":"Address"},"jsonld":{"@context":["https://jsonldresume.github.io/skill/context.json",{"gender":{"@id":"schema:gender","@type":"@vocab"},"skill:classOfAward":{"@id":"skill:classOfAward","@type":"@vocab"},"skill:securityClearance":{"@id":"skill:securityClearance","@type":"@vocab"},"category":{"@id":"schema:category","@type":"@vocab"},"dayOfWeek":{"@id":"schema:dayOfWeek","@type":"@vocab"}}],"@graph":[{"award":[{"@type":"skill:Award","@id":"_:cebcd613-36a8-4cde-851c-b2879b140ef0#enable","skill:title":"Judge","skill:conferredBy":"Peak-to-Peak chartered school","description":"judge at their science fair"},{"@type":"skill:Award","@id":"_:cba9b860-347a-4969-a6f0-612f4218764c#enable","skill:title":"3rd place","skill:conferredBy":"national physics federtion","description":"Third place in national physics competition"}]},{"givenName":[{"@language":"en","@value":"Ehsan"},{"@language":"ar","@value":"\u0627\u062d\u0633\u0627\u0646"}],"familyName":[{"@language":"en","@value":"Shariati"},{"@language":"ar","@value":"\u0634\u0631\u06cc\u0639\u062a\u06cc"}],"address":[{"@id":"_:f5ef8e0e-89c8-4100-ae22-81887cf3f373","@type":"PostalAddress","hoursAvailable":{"@id":"_:f5ef8e0e-89c8-4100-ae22-81887cf3f373#hoursAvailable","@type":"OpeningHoursSpecification","validThrough":"2099-01-01"},"addressCountry":"Canada","streetAddress":"20 Test dr","addressRegion":"ON","addressLocality":"Toronto","postalCode":"H0H 0H0","contactType":"","sameAs":"https://www.google.com/maps/place/Rosedale+Golf+Club/@43.7259752,-79.4099022,14z/data=!4m8!1m2!2m1!1s20+test+dr!3m4!1s0x882b32b8192bf409:0x809cd74491c3e5b9!8m2!3d43.7364762!4d-79.3992555"}],"description":"Full-stack developer","sameAs":["https://jsonldresume.org"],"image":{"contentUrl":"https://static.projectmanagement.com/images/profile-photos/46843803.jpg","@id":"_:#image","@type":"ImageObject"},"contactPoint":[{"@id":"_:2467fb27-b4a8-4953-93fa-ee27ef20c7a7","@type":"ContactPoint","description":"This is main contact","contactType":"Preferred","email":"<EMAIL>","telephone":"+****************"}],"seeks":[{"@id":"_:fe2bddc5-0c95-4e33-b245-d0c1eb22789a","@type":"Demand","description":"Innovative and dedicated Application Developer and Project Manager, with more than 10 years of experience leading application development, IT service, and engineering projects. Partners with CEOs, department heads, and internal and external stakeholders, developing powerful solutions to operational challenges and instituting leading-edge technologies. Ensures fiscal restraint and detailed scheduling, effectively controlling project costs and timelines to consistently deliver projects to successful conclusion. Leads and motivates staff and contractors, building top-performing teams by applying a situational leadership style and managing projects in Agile environment. Fully fluent in English and Farsi, with skills in Arabic. I also have experience in stock trading and investment management and fundamental and technical analysis of companies and stocks.","availabilityStarts":"2021-01-01","availabilityEnds":"2022-01-01","availableAtOrFrom":{"address":{"@type":"PostalAddress","@id":"_:fe2bddc5-0c95-4e33-b245-d0c1eb22789a_availableAtOrFrom_address","addressLocality":"Toronto","addressRegion":"ON","addressCountry":"Canada"},"@type":"Place","@id":"_:fe2bddc5-0c95-4e33-b245-d0c1eb22789a_availableAtOrFrom"},"deliveryLeadTime":{}}],"hasOccupation":[{"@type":"EmployeeRole","@id":"_:a905d9ec-1a1f-4629-8db2-b3ec2e626f58#enable","hasOccupation":{"@id":"_:a905d9ec-1a1f-4629-8db2-b3ec2e626f58#hasOccupation","@type":"Occupation","name":"","skills":["Project Management","Programming"],"responsibilities":["Directing company","Hiring talents","Managing financials","Insure daily follow-up on requests, mini-projects and issues"]},"subjectOf":{"@type":"BusinessEvent","id":"_:a905d9ec-1a1f-4629-8db2-b3ec2e626f58#subjectOf","organizer":{"@type":"Organization","id":"_:a905d9ec-1a1f-4629-8db2-b3ec2e626f58#subjectOf#organizer","name":"Direction X Corporation"}},"roleName":"Director","startDate":"2018-01-01","endDate":"2021-01-01","description":"Delivered successful projects that drove improvements to business outcomes and KPIs"}],"hasCredential":[{"@type":"EducationalOccupationalCredential","@id":"_:86baf285-43a0-4ed9-9b73-85533b4d3f86#enable","aggregateRating":{"@id":"_:86baf285-43a0-4ed9-9b73-85533b4d3f86#aggregateRating","@type":"aggregateRating","bestRating":"4","ratingValue":"3.7","name":"GPA","itemReviewed":{"@id":"_:86baf285-43a0-4ed9-9b73-85533b4d3f86#enable"}},"credentialCategory":"degree","educationalLevel":"Masters of Science","abstract":"I was an active member of university of Colorado associations.","teaches":["Electrical systems","Programming"],"about":{"@id":"_:86baf285-43a0-4ed9-9b73-85533b4d3f86#about","@type":"EducationalOccupationalProgram","educationalCredentialAwarded":"Electrical Engineering","startDate":"2008-01-01","endDate":"2010-01-01","provider":{"@id":"_:86baf285-43a0-4ed9-9b73-85533b4d3f86#about#provider","@type":"CollegeOrUniversity","name":"University of Colorado at Boudler"}}}],"memberOf":[{"@id":"_:028ef777-abf2-4a9b-a08d-e587bcae6751#enable","@type":"Role","startDate":"2018-01-01","endDate":"2019-01-01","roleName":"member","memberOf":{"@id":"_:028ef777-abf2-4a9b-a08d-e587bcae6751#memberOf","@type":"ProgramMembership","url":"","programName":"Salsa Dance","description":""}}],"knowsLanguage":[{"@type":"Language","@id":"_:7e78b654-5439-463c-a7cf-9a0f5eec5f70","name":"English"},{"@type":"Language","@id":"_:55fa78ed-7afb-4e3e-89e1-25e32088b951","name":"Farsi"}],"interactionStatistic":[{"@id":"_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a","@type":"InteractionCounter","disambiguatingDescription":"Reference","interactionType":{"@id":"_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a#interactionType","@type":"AssessAction","participant":{"@id":"_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a#interactionType#participant","@type":"Person","givenName":"John","familyName":"Doe","jobTitle":"CEO, imaginary corp","telephone":"+****************","email":"<EMAIL>"},"result":[{"@id":"_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a#result","@type":"Review","itemReviewed":{},"reviewAspect":[],"reviewRating":{"@id":"_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a#result#reviewRating","@type":"Rating","ratingValue":"5","bestRating":"5","ratingExplanation":""}}]},"result":[{"reviewRating":{"ratingExplanation":"This is a sample reference"}}]},{"@id":"_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d","@type":"InteractionCounter","disambiguatingDescription":"Reference","interactionType":{"@id":"_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d#interactionType","@type":"AssessAction","participant":{"@id":"_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d#interactionType#participant","@type":"Person","givenName":"Jack","familyName":"Dee","jobTitle":"CEO, sleep corp","telephone":"+****************","email":"<EMAIL>"},"result":[{"@id":"_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d#result","@type":"Review","itemReviewed":{},"reviewAspect":[],"reviewRating":{"@id":"_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d#result#reviewRating","@type":"Rating","ratingValue":"5","bestRating":"5","ratingExplanation":""}}]},"result":[{"reviewRating":{"ratingExplanation":"This is second sample reference"}}]}],"identifier":[{"@type":"PropertyValue","@id":"_:Extras_6d651394-5740-47c5-a172-5326ec688e26","propertyID":"Hobbies","value":"Skiing, Dancing"}],"award":["Judge","3rd place"],"@context":"http://schema.org/"}]}},"theme":{"layout":"castform","font":{"family":"Montserrat"},"colors":{"background":"#ffffff","primary":"#212121","accent":"#f44336"},"layoutblocks":{"onyx":[["objective","work","education","projects"],["hobbies","languages","awards","certifications"],["skills","references"]],"pikachu":[["skills","languages","hobbies","awards","certifications"],["work","education","projects","references"]],"gengar":[["objective","skills"],["awards","certifications","languages","references","hobbies"],["work","education","projects"]],"castform":[["awards","certifications","languages","hobbies"],["objective","work","education","skills","projects","references"]],"glalie":[["awards","certifications","hobbies"],["objective","work","education","skills","projects","languages","references"]],"celebi":[["awards","certifications","languages","hobbies"],["objective","work","education","skills","projects","references"]]}}}')},,,,,,function(e,t,a){e.exports=a.p+"static/media/preview.a5fc2f27.png"},function(e,t,a){e.exports=a.p+"static/media/preview.f1f46a82.png"},function(e,t,a){e.exports=a.p+"static/media/preview.27f7a093.png"},function(e,t,a){e.exports=a.p+"static/media/preview.115df124.png"},function(e,t,a){e.exports=a.p+"static/media/preview.93d6587b.png"},function(e,t,a){e.exports=a.p+"static/media/preview.3186944c.png"},,,,function(e,t,a){e.exports=a.p+"static/media/panzoom.e912bae3.mp4"},,,,function(e,t,a){e.exports=a(854)},,,,,,function(e,t,a){},function(e,t,a){},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,a){"use strict";a.r(t);var o=a(1),n=a.n(o),r=a(17),l=a.n(r),i=a(30),s=(a(658),a(64)),c=a(85),u=a(14),d=a(651),m=a(87),p=m,b=a(88),h=a(89),g=a(90),f=a(91),y=a(92),v=a(93),x=a(94),N=a(95),w=a(96),k={profile:b,objective:h,work:g,education:f,awards:y,certifications:v,languages:x,references:N,extras:w},E=a(97),O=a(98),S=a(99),j=a(100),C=a(101),D=a(102),A={templates:E,colors:O,fonts:S,actions:j,settings:C,about:D},T={app:p,leftSidebar:k,rightSidebar:A},J=a(103),P=J,R=a(104),F=a(105),L=a(106),z=a(107),I=a(108),M=a(109),H=a(110),G=a(111),q=a(112),Y={profile:R,objective:F,work:L,education:z,awards:I,certifications:M,languages:H,references:G,extras:q},U=a(113),_=a(114),W=a(115),V=a(116),B=a(117),K=a(118),Z={templates:U,colors:_,fonts:W,actions:V,settings:B,about:K},Q={app:P,leftSidebar:Y,rightSidebar:Z},X=a(119),$=X,ee=a(120),te=a(121),ae=a(122),oe=a(123),ne=a(124),re=a(125),le=a(126),ie=a(127),se=a(128),ce={profile:ee,objective:te,work:ae,education:oe,awards:ne,certifications:re,languages:le,references:ie,extras:se},ue=a(129),de=a(130),me=a(131),pe=a(132),be=a(133),he=a(134),ge={templates:ue,colors:de,fonts:me,actions:pe,settings:be,about:he},fe={app:$,leftSidebar:ce,rightSidebar:ge},ye=a(135),ve=ye,xe=a(136),Ne=a(137),we=a(138),ke=a(139),Ee=a(140),Oe=a(141),Se=a(142),je=a(143),Ce=a(144),De={profile:xe,objective:Ne,work:we,education:ke,awards:Ee,certifications:Oe,languages:Se,references:je,extras:Ce},Ae=a(145),Te=a(146),Je=a(147),Pe=a(148),Re=a(149),Fe=a(150),Le={templates:Ae,colors:Te,fonts:Je,actions:Pe,settings:Re,about:Fe},ze={app:ve,leftSidebar:De,rightSidebar:Le},Ie=a(151),Me=Ie,He=a(152),Ge=a(153),qe=a(154),Ye=a(155),Ue=a(156),_e=a(157),We=a(158),Ve=a(159),Be=a(160),Ke={profile:He,objective:Ge,work:qe,education:Ye,awards:Ue,certifications:_e,languages:We,references:Ve,extras:Be},Ze=a(161),Qe=a(162),Xe=a(163),$e=a(164),et=a(165),tt=a(166),at={templates:Ze,colors:Qe,fonts:Xe,actions:$e,settings:et,about:tt},ot={app:Me,leftSidebar:Ke,rightSidebar:at},nt=a(167),rt=nt,lt=a(168),it=a(169),st=a(170),ct=a(171),ut=a(172),dt=a(173),mt=a(174),pt=a(175),bt=a(176),ht={profile:lt,objective:it,work:st,education:ct,awards:ut,certifications:dt,languages:mt,references:pt,extras:bt},gt=a(177),ft=a(178),yt=a(179),vt=a(180),xt=a(181),Nt=a(182),wt={templates:gt,colors:ft,fonts:yt,actions:vt,settings:xt,about:Nt},kt={app:rt,leftSidebar:ht,rightSidebar:wt},Et=a(183),Ot=Et,St=a(184),jt=a(185),Ct=a(186),Dt=a(187),At=a(188),Tt=a(189),Jt=a(190),Pt=a(191),Rt=a(192),Ft={profile:St,objective:jt,work:Ct,education:Dt,awards:At,certifications:Tt,languages:Jt,references:Pt,extras:Rt},Lt=a(193),zt=a(194),It=a(195),Mt=a(196),Ht=a(197),Gt=a(198),qt={templates:Lt,colors:zt,fonts:It,actions:Mt,settings:Ht,about:Gt},Yt={app:Ot,leftSidebar:Ft,rightSidebar:qt},Ut=a(199),_t=Ut,Wt=a(200),Vt=a(201),Bt=a(202),Kt=a(203),Zt=a(204),Qt=a(205),Xt=a(206),$t=a(207),ea=a(208),ta={profile:Wt,objective:Vt,work:Bt,education:Kt,awards:Zt,certifications:Qt,languages:Xt,references:$t,extras:ea},aa=a(209),oa=a(210),na=a(211),ra=a(212),la=a(213),ia=a(214),sa={templates:aa,colors:oa,fonts:na,actions:ra,settings:la,about:ia},ca={app:_t,leftSidebar:ta,rightSidebar:sa},ua=a(215),da=ua,ma=a(216),pa=a(217),ba=a(218),ha=a(219),ga=a(220),fa=a(221),ya=a(222),va=a(223),xa=a(224),Na={profile:ma,objective:pa,work:ba,education:ha,awards:ga,certifications:fa,languages:ya,references:va,extras:xa},wa=a(225),ka=a(226),Ea=a(227),Oa=a(228),Sa=a(229),ja=a(230),Ca={templates:wa,colors:ka,fonts:Ea,actions:Oa,settings:Sa,about:ja},Da={app:da,leftSidebar:Na,rightSidebar:Ca},Aa=a(231),Ta=Aa,Ja=a(232),Pa=a(233),Ra=a(234),Fa=a(235),La=a(236),za=a(237),Ia=a(238),Ma=a(239),Ha=a(240),Ga={profile:Ja,objective:Pa,work:Ra,education:Fa,awards:La,certifications:za,languages:Ia,references:Ma,extras:Ha},qa=a(241),Ya=a(242),Ua=a(243),_a=a(244),Wa=a(245),Va=a(246),Ba={templates:qa,colors:Ya,fonts:Ua,actions:_a,settings:Wa,about:Va},Ka={app:Ta,leftSidebar:Ga,rightSidebar:Ba},Za=a(247),Qa=Za,Xa=a(248),$a=a(249),eo=a(250),to=a(251),ao=a(252),oo=a(253),no=a(254),ro=a(255),lo=a(256),io={profile:Xa,objective:$a,work:eo,education:to,awards:ao,certifications:oo,languages:no,references:ro,extras:lo},so=a(257),co=a(258),uo=a(259),mo=a(260),po=a(261),bo=a(262),ho={templates:so,colors:co,fonts:uo,actions:mo,settings:po,about:bo},go={app:Qa,leftSidebar:io,rightSidebar:ho},fo=a(263),yo=fo,vo=a(264),xo=a(265),No=a(266),wo=a(267),ko=a(268),Eo=a(269),Oo=a(270),So=a(271),jo=a(272),Co={profile:vo,objective:xo,work:No,education:wo,awards:ko,certifications:Eo,languages:Oo,references:So,extras:jo},Do=a(273),Ao=a(274),To=a(275),Jo=a(276),Po=a(277),Ro=a(278),Fo={templates:Do,colors:Ao,fonts:To,actions:Jo,settings:Po,about:Ro},Lo={app:yo,leftSidebar:Co,rightSidebar:Fo},zo=a(279),Io=zo,Mo=a(280),Ho=a(281),Go=a(282),qo=a(283),Yo=a(284),Uo=a(285),_o=a(286),Wo=a(287),Vo=a(288),Bo={profile:Mo,objective:Ho,work:Go,education:qo,awards:Yo,certifications:Uo,languages:_o,references:Wo,extras:Vo},Ko=a(289),Zo=a(290),Qo=a(291),Xo=a(292),$o=a(293),en=a(294),tn={templates:Ko,colors:Zo,fonts:Qo,actions:Xo,settings:$o,about:en},an={app:Io,leftSidebar:Bo,rightSidebar:tn},on=a(295),nn=on,rn=a(296),ln=a(297),sn=a(298),cn=a(299),un=a(300),dn=a(301),mn=a(302),pn=a(303),bn=a(304),hn={profile:rn,objective:ln,work:sn,education:cn,awards:un,certifications:dn,languages:mn,references:pn,extras:bn},gn=a(305),fn=a(306),yn=a(307),vn=a(308),xn=a(309),Nn=a(310),wn={templates:gn,colors:fn,fonts:yn,actions:vn,settings:xn,about:Nn},kn={app:nn,leftSidebar:hn,rightSidebar:wn},En=a(311),On=En,Sn=a(312),jn=a(313),Cn=a(314),Dn=a(315),An=a(316),Tn=a(317),Jn=a(318),Pn=a(319),Rn=a(320),Fn={profile:Sn,objective:jn,work:Cn,education:Dn,awards:An,certifications:Tn,languages:Jn,references:Pn,extras:Rn},Ln=a(321),zn=a(322),In=a(323),Mn=a(324),Hn=a(325),Gn=a(326),qn={templates:Ln,colors:zn,fonts:In,actions:Mn,settings:Hn,about:Gn},Yn={app:On,leftSidebar:Fn,rightSidebar:qn},Un=a(327),_n=Un,Wn=a(328),Vn=a(329),Bn=a(330),Kn=a(331),Zn=a(332),Qn=a(333),Xn=a(334),$n=a(335),er=a(336),tr={profile:Wn,objective:Vn,work:Bn,education:Kn,awards:Zn,certifications:Qn,languages:Xn,references:$n,extras:er},ar=a(337),or=a(338),nr=a(339),rr=a(340),lr=a(341),ir=a(342),sr={templates:ar,colors:or,fonts:nr,actions:rr,settings:lr,about:ir},cr={app:_n,leftSidebar:tr,rightSidebar:sr},ur=a(343),dr=ur,mr=a(344),pr=a(345),br=a(346),hr=a(347),gr=a(348),fr=a(349),yr=a(350),vr=a(351),xr=a(352),Nr={profile:mr,objective:pr,work:br,education:hr,awards:gr,certifications:fr,languages:yr,references:vr,extras:xr},wr=a(353),kr=a(354),Er=a(355),Or=a(356),Sr=a(357),jr=a(358),Cr={templates:wr,colors:kr,fonts:Er,actions:Or,settings:Sr,about:jr},Dr={app:dr,leftSidebar:Nr,rightSidebar:Cr},Ar=a(359),Tr=Ar,Jr=a(360),Pr=a(361),Rr=a(362),Fr=a(363),Lr=a(364),zr=a(365),Ir=a(366),Mr=a(367),Hr=a(368),Gr={profile:Jr,objective:Pr,work:Rr,education:Fr,awards:Lr,certifications:zr,languages:Ir,references:Mr,extras:Hr},qr=a(369),Yr=a(370),Ur=a(371),_r=a(372),Wr=a(373),Vr=a(374),Br={templates:qr,colors:Yr,fonts:Ur,actions:_r,settings:Wr,about:Vr},Kr={app:Tr,leftSidebar:Gr,rightSidebar:Br},Zr=a(375),Qr=Zr,Xr=a(376),$r=a(377),el=a(378),tl=a(379),al=a(380),ol=a(381),nl=a(382),rl=a(383),ll=a(384),il={profile:Xr,objective:$r,work:el,education:tl,awards:al,certifications:ol,languages:nl,references:rl,extras:ll},sl=a(385),cl=a(386),ul=a(387),dl=a(388),ml=a(389),pl=a(390),bl={templates:sl,colors:cl,fonts:ul,actions:dl,settings:ml,about:pl},hl={app:Qr,leftSidebar:il,rightSidebar:bl},gl=a(391),fl=gl,yl=a(392),vl=a(393),xl=a(394),Nl=a(395),wl=a(396),kl=a(397),El=a(398),Ol=a(399),Sl=a(400),jl={profile:yl,objective:vl,work:xl,education:Nl,awards:wl,certifications:kl,languages:El,references:Ol,extras:Sl},Cl=a(401),Dl=a(402),Al=a(403),Tl=a(404),Jl=a(405),Pl=a(406),Rl={templates:Cl,colors:Dl,fonts:Al,actions:Tl,settings:Jl,about:Pl},Fl={app:fl,leftSidebar:jl,rightSidebar:Rl},Ll=a(407),zl=Ll,Il=a(408),Ml=a(409),Hl=a(410),Gl=a(411),ql=a(412),Yl=a(413),Ul=a(414),_l=a(415),Wl=a(416),Vl={profile:Il,objective:Ml,work:Hl,education:Gl,awards:ql,certifications:Yl,languages:Ul,references:_l,extras:Wl},Bl=a(417),Kl=a(418),Zl=a(419),Ql=a(420),Xl=a(421),$l=a(422),ei={templates:Bl,colors:Kl,fonts:Zl,actions:Ql,settings:Xl,about:$l},ti={app:zl,leftSidebar:Vl,rightSidebar:ei},ai=a(423),oi=ai,ni=a(424),ri=a(425),li=a(426),ii=a(427),si=a(428),ci=a(429),ui=a(430),di=a(431),mi=a(432),pi={profile:ni,objective:ri,work:li,education:ii,awards:si,certifications:ci,languages:ui,references:di,extras:mi},bi=a(433),hi=a(434),gi=a(435),fi=a(436),yi=a(437),vi=a(438),xi={templates:bi,colors:hi,fonts:gi,actions:fi,settings:yi,about:vi},Ni={app:oi,leftSidebar:pi,rightSidebar:xi},wi=a(439),ki=wi,Ei=a(440),Oi=a(441),Si=a(442),ji=a(443),Ci=a(444),Di=a(445),Ai=a(446),Ti=a(447),Ji=a(448),Pi={profile:Ei,objective:Oi,work:Si,education:ji,awards:Ci,certifications:Di,languages:Ai,references:Ti,extras:Ji},Ri=a(449),Fi=a(450),Li=a(451),zi=a(452),Ii=a(453),Mi=a(454),Hi={templates:Ri,colors:Fi,fonts:Li,actions:zi,settings:Ii,about:Mi},Gi={app:ki,leftSidebar:Pi,rightSidebar:Hi},qi=a(455),Yi=qi,Ui=a(456),_i=a(457),Wi=a(458),Vi=a(459),Bi=a(460),Ki=a(461),Zi=a(462),Qi=a(463),Xi=a(464),$i={profile:Ui,objective:_i,work:Wi,education:Vi,awards:Bi,certifications:Ki,languages:Zi,references:Qi,extras:Xi},es=a(465),ts=a(466),as=a(467),os=a(468),ns=a(469),rs=a(470),ls={templates:es,colors:ts,fonts:as,actions:os,settings:ns,about:rs},is={app:Yi,leftSidebar:$i,rightSidebar:ls},ss=a(471),cs=ss,us={af:T,ar:Q,as:fe,ca:ze,cs:ot,da:kt,de:Yt,el:ca,en:Da,es:Ka,fi:go,fr:Lo,he:an,hi:kn,hu:Yn,it:cr,ja:Dr,kn:Kr,ko:hl,ml:Fl,mr:ti,nl:Ni,no:Gi,pa:is,pl:{app:cs,leftSidebar:{profile:a(472),objective:a(473),work:a(474),education:a(475),awards:a(476),certifications:a(477),languages:a(478),references:a(479),extras:a(480)},rightSidebar:{templates:a(481),colors:a(482),fonts:a(483),actions:a(484),settings:a(485),about:a(486)}},pt:{app:a(487),leftSidebar:{profile:a(488),objective:a(489),work:a(490),education:a(491),awards:a(492),certifications:a(493),languages:a(494),references:a(495),extras:a(496)},rightSidebar:{templates:a(497),colors:a(498),fonts:a(499),actions:a(500),settings:a(501),about:a(502)}},ro:{app:a(503),leftSidebar:{profile:a(504),objective:a(505),work:a(506),education:a(507),awards:a(508),certifications:a(509),languages:a(510),references:a(511),extras:a(512)},rightSidebar:{templates:a(513),colors:a(514),fonts:a(515),actions:a(516),settings:a(517),about:a(518)}},ru:{app:a(519),leftSidebar:{profile:a(520),objective:a(521),work:a(522),education:a(523),awards:a(524),certifications:a(525),languages:a(526),references:a(527),extras:a(528)},rightSidebar:{templates:a(529),colors:a(530),fonts:a(531),actions:a(532),settings:a(533),about:a(534)}},sv:{app:a(535),leftSidebar:{profile:a(536),objective:a(537),work:a(538),education:a(539),awards:a(540),certifications:a(541),languages:a(542),references:a(543),extras:a(544)},rightSidebar:{templates:a(545),colors:a(546),fonts:a(547),actions:a(548),settings:a(549),about:a(550)}},ta:{app:a(551),leftSidebar:{profile:a(552),objective:a(553),work:a(554),education:a(555),awards:a(556),certifications:a(557),languages:a(558),references:a(559),extras:a(560)},rightSidebar:{templates:a(561),colors:a(562),fonts:a(563),actions:a(564),settings:a(565),about:a(566)}},tr:{app:a(567),leftSidebar:{profile:a(568),objective:a(569),work:a(570),education:a(571),awards:a(572),certifications:a(573),languages:a(574),references:a(575),extras:a(576)},rightSidebar:{templates:a(577),colors:a(578),fonts:a(579),actions:a(580),settings:a(581),about:a(582)}},uk:{app:a(583),leftSidebar:{profile:a(584),objective:a(585),work:a(586),education:a(587),awards:a(588),certifications:a(589),languages:a(590),references:a(591),extras:a(592)},rightSidebar:{templates:a(593),colors:a(594),fonts:a(595),actions:a(596),settings:a(597),about:a(598)}},vi:{app:a(599),leftSidebar:{profile:a(600),objective:a(601),work:a(602),education:a(603),awards:a(604),certifications:a(605),languages:a(606),references:a(607),extras:a(608)},rightSidebar:{templates:a(609),colors:a(610),fonts:a(611),actions:a(612),settings:a(613),about:a(614)}},zh:{app:a(615),leftSidebar:{profile:a(616),objective:a(617),work:a(618),education:a(619),awards:a(620),certifications:a(621),languages:a(622),references:a(623),extras:a(624)},rightSidebar:{templates:a(625),colors:a(626),fonts:a(627),actions:a(628),settings:a(629),about:a(630)}}},ds=[{code:"ar",name:"Arabic (\u0639\u0631\u0628\u0649)"},{code:"zh",name:"Chinese (\u4e2d\u6587)"},{code:"da",name:"Danish (Dansk)"},{code:"nl",name:"Dutch (Nederlands)"},{code:"en",name:"English (US)"},{code:"fr",name:"French (Fran\xe7ais)"},{code:"de",name:"German (Deutsche)"},{code:"he",name:"Hebrew (\u05e2\u05d1\u05e8\u05d9\u05ea)"},{code:"hi",name:"Hindi (\u0939\u093f\u0928\u094d\u0926\u0940)"},{code:"it",name:"Italian (Italiano)"},{code:"kn",name:"Kannada (\u0c95\u0ca8\u0ccd\u0ca8\u0ca1)"},{code:"pl",name:"Polish (Polskie)"},{code:"pt",name:"Portuguese (Portugu\xeas)"},{code:"ru",name:"Russian (\u0440\u0443\u0441\u0441\u043a\u0438\u0439)"},{code:"es",name:"Spanish (Espa\xf1ol)"},{code:"ta",name:"Tamil (\u0ba4\u0bae\u0bbf\u0bb4\u0bcd)"},{code:"vi",name:"Vietnamese (Ti\u1ebfng Vi\u1ec7t)"}];s.a.use(d.a).use(c.a).use(u.e).init({resources:us,lng:"en",fallbackLng:"en",ns:["app","leftSidebar","rightSidebar"],defaultNS:"app"});s.a,a(659),a(660);var ms=Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function ps(e,t){navigator.serviceWorker.register(e).then((function(e){e.onupdatefound=function(){var a=e.installing;null!=a&&(a.onstatechange=function(){"installed"===a.state&&(navigator.serviceWorker.controller?(console.log("New content is available and will be used when all tabs for this page are closed. See https://bit.ly/CRA-PWA."),t&&t.onUpdate&&t.onUpdate(e)):(console.log("Content is cached for offline use."),t&&t.onSuccess&&t.onSuccess(e)))})}})).catch((function(e){console.error("Error during service worker registration:",e)}))}var bs=a(3),hs=a(5),gs=a(24),fs=a.n(gs),ys=a(6),vs=a.n(ys),xs=a(633),Ns=a.n(xs),ws=a(634),ks=a(2),Es=a(60),Os=a.n(Es),Ss=a(61),js=a(31),Cs=a.n(js),Ds=function(e,t,a){var o=e.findIndex((function(e){return e.id===t.id})),n=o+a;if(!(n<0||n===e.length)){var r=[o,n].sort((function(e,t){return e-t}));e.splice(r[0],2,e[r[1]],e[r[0]])}},As=function(e){e=e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(e,t,a,o){return t+t+a+a+o+o}));var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null},Ts=function(e){return e({type:"save_data"})},Js=function(e,t,a){e({type:"add_item",payload:{key:t,value:a}}),Ts(e)},Ps=null,Rs=function(e,t,a,o){if(!Ps)return new Promise((function(n){t.current.autoCenter(1),t.current.reset(),Ps=setTimeout((function(){Os()(e.current,{scale:5,useCORS:!0,allowTaint:!0}).then((function(e){var t=e.toDataURL("image/jpeg",a/100),r=new Ss({orientation:"portrait",unit:"px",format:"unconstrained"===o?[e.width,e.height]:"a4"}),l=r.internal.pageSize.getWidth(),i=r.internal.pageSize.getHeight(),s=l/e.width,c=i/e.height,u=s>c?c:s,d=e.width*u,m=e.height*u,p=0,b=0;"unconstrained"!==o&&(p=(l-d)/2,b=(i-m)/2),r.addImage(t,"JPEG",p,b,d,m,null,"SLOW"),r.save("RxResume_".concat(Date.now(),".pdf")),Ps=null,n()}))}),250)}))},Fs=null,Ls=function(e,t,a){if(!Fs)return new Promise((function(o){t.current.autoCenter(1),t.current.reset(),Fs=setTimeout((function(){Os()(e.current,{scale:5,useCORS:!0,allowTaint:!0}).then((function(e){var t=e.toDataURL("image/jpeg",a/100),n=new Ss({orientation:"portrait",unit:"px",format:"a4"}),r=n.internal.pageSize.getHeight(),l=n.internal.pageSize.getWidth(),i=e.height*l/e.width,s=0,c=i;for(n.addImage(t,"JPEG",0,s,l,i),c-=r;c>=0;)s=c-i,n.addPage(),n.addImage(t,"JPEG",0,s,l,i),c-=r;n.save("RxResume_".concat(Date.now(),".pdf")),Fs=null,o()}))}),250)}))},zs=function(e){var t=e.date,a=e.language,o=void 0===a?"en":a,n=e.includeDay,r=void 0!==n&&n?"DD MMMM YYYY":"MMMM YYYY";return Cs()(t).locale(o.substr(0,2)).format(r)},Is=function(e,t){var a=e.startDate,o=e.endDate,n=e.language,r=void 0===n?"en":n,l="".concat(Cs()(a).locale(r.substr(0,2)).format("MMMM YYYY")),i=Cs()(o).isValid()?"".concat(Cs()(o).locale(r.substr(0,2)).format("MMMM YYYY")):t("shared.forms.present");return"".concat(l," - ").concat(i)},Ms=function(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1];return!(!e||!0!==e.enable)},Hs={data:{jsonld:{"@context":["https://jsonldresume.github.io/skill/context.json",{gender:{"@id":"schema:gender","@type":"@vocab"},"skill:classOfAward":{"@id":"skill:classOfAward","@type":"@vocab"},"skill:securityClearance":{"@id":"skill:securityClearance","@type":"@vocab"},category:{"@id":"schema:category","@type":"@vocab"},dayOfWeek:{"@id":"schema:dayOfWeek","@type":"@vocab"}}],"@graph":[{"@type":"skill:Resume"},{"@type":"Person",givenName:[{"@language":"en","@value":""}],familyName:[{"@language":"en","@value":""}],address:[]}]},profile:{heading:"Profile",photo:"",firstName:"",lastName:"",subtitle:"",address:{line1:"",line2:"",line3:""},phone:"",website:"",email:""},contacts:{enable:!0,heading:"Contacts"},address:{enable:!0,heading:"Address"},objective:{enable:!0,heading:"Objective",body:""},work:{enable:!0,heading:"Work Experience",items:[]},education:{enable:!0,heading:"Education",items:[]},awards:{enable:!0,heading:"Honors & Awards",items:[]},certifications:{enable:!0,heading:"Certifications",items:[]},skills:{enable:!0,heading:"Skills",items:[]},memberships:{enable:!0,heading:"Memberships",items:[]},languages:{enable:!0,heading:"Languages",items:[]},references:{enable:!0,heading:"References",items:[]},extras:{enable:!0,heading:"Personal Information",items:[]}},theme:{layout:"Onyx",font:{family:""},colors:{background:"#ffffff",primary:"#212121",accent:"#f44336"},layoutblocks:{onyx:[["objective","work","education","projects"],["hobbies","languages","awards","certifications"],["skills","references"]],pikachu:[["skills","languages","hobbies","awards","certifications"],["work","education","projects","references"]],gengar:[["objective","skills"],["awards","certifications","languages","references","hobbies"],["work","education","projects"]],castform:[["awards","certifications","languages","hobbies"],["objective","work","education","skills","projects","references"]],glalie:[["awards","certifications","hobbies"],["objective","work","education","skills","projects","languages","references"]],celebi:[["awards","certifications","languages","hobbies"],["objective","work","education","skills","projects","references"]]}},settings:{language:"en"}},Gs=function(e,t){var a,o=t.type,n=t.payload,r=JSON.parse(JSON.stringify(e));switch(o){case"migrate_section":return vs()(Object(hs.a)({},r),"data.".concat(n.key),n.value);case"add_item":return(a=fs()(Object(hs.a)({},r),"".concat(n.key),[])).push(n.value),vs()(Object(hs.a)({},r),"".concat(n.key),a);case"delete_item":return a=fs()(Object(hs.a)({},r),"".concat(n.key),[]),Ns()(a,(function(e){return e.id===n.value.id})),vs()(Object(hs.a)({},r),"".concat(n.key),a);case"move_item_up":return a=fs()(Object(hs.a)({},r),"".concat(n.key),[]),Ds(a,n.value,-1),vs()(Object(hs.a)({},r),"".concat(n.key),a);case"move_item_down":return a=fs()(Object(hs.a)({},r),"".concat(n.key),[]),Ds(a,n.value,1),vs()(Object(hs.a)({},r),"".concat(n.key),a);case"on_input":return vs()(Object(hs.a)({},r),n.key,n.value);case"save_data":return localStorage.setItem("state",JSON.stringify(r)),r;case"import_data":if(null===n)return Hs;for(var l=0,i=Object.keys(Hs.data);l<i.length;l++){var s=i[l];s in n.data||(n.data[s]=Hs.data[s])}return Object(hs.a)({},r,{},n);case"load_demo_data":return Object(hs.a)({},r,{},ws);case"reset":return Hs;default:return r}},qs=Object(o.createContext)(Hs),Ys=qs.Provider,Us=function(e){var t=e.children,a=Object(o.useReducer)(Gs,Hs),r=Object(bs.a)(a,2),l=r[0],i=r[1];return n.a.createElement(Ys,{value:{state:l,dispatch:i}},t)},_s=(qs.Consumer,qs),Ws=n.a.createContext(null),Vs=Ws.Provider,Bs=function(e){var t=e.children,a=Object(o.useState)(null),r=Object(bs.a)(a,2),l=r[0],i=r[1],s=Object(o.useState)(null),c=Object(bs.a)(s,2),u=c[0],d=c[1],m=Object(o.useState)(!1),p=Object(bs.a)(m,2),b=p[0],h=p[1];return n.a.createElement(Vs,{value:{pageRef:l,setPageRef:i,panZoomRef:u,setPanZoomRef:d,isPrintDialogOpen:b,setPrintDialogOpen:h}},t)},Ks=(Ws.Consumer,Ws),Zs=a(857),Qs=a(635),Xs=function(e){var t=e.className,a=e.label,o=e.value,r=e.onChange,l=e.options,i=e.optionItem;return n.a.createElement("div",{className:"flex flex-col mb-2 "+t,style:{display:"contents"}},a&&n.a.createElement("label",{className:"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2"},a),n.a.createElement("div",{className:"inline-flex relative w-full bg-gray-200 text-gray-800 rounded py-3 px-4 leading-tight focus:outline-none"},n.a.createElement("select",{className:"block appearance-none w-full bg-gray-200 text-gray-800 focus:outline-none",value:o,onChange:function(e){return r(e.target.value)}},l.map(i)),n.a.createElement("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex justify-center items-center px-2 bg-gray-200"},n.a.createElement("i",{className:"material-icons"},"expand_more"))))},$s=function(e){var t=e.tabs,a=e.currentTab,o=e.setCurrentTab,r=function(e){var n=t.findIndex((function(e){return e.key===a}));e<0&&n>0&&o(t[n-1].key),e>0&&n<t.length-1&&o(t[n+1].key)};return n.a.createElement("div",{className:"mx-4 mb-6 flex items-center"},n.a.createElement("div",{className:"flex mr-1 cursor-pointer select-none text-gray-600 hover:text-gray-800",onClick:function(){return r(-1)}},n.a.createElement("i",{className:"material-icons"},"chevron_left")),n.a.createElement(Xs,{className:"mb-6",label:"",placeholder:"",value:a,onChange:function(e){o(e)},options:t,optionItem:function(e,t){return n.a.createElement("option",{key:e.key,value:e.key},e.name||"Tab")}}),n.a.createElement("div",{className:"flex ml-1 cursor-pointer select-none text-gray-600 hover:text-gray-800",onClick:function(){return r(1)}},n.a.createElement("i",{className:"material-icons"},"chevron_right")))},ec=a(636),tc=a(637),ac=a(650),oc=a(652),nc=["en","fr","it","de","ar"],rc=function(e){Object(oc.a)(a,e);var t=Object(ac.a)(a);function a(){var e;Object(ec.a)(this,a);for(var o=arguments.length,r=new Array(o),l=0;l<o;l++)r[l]=arguments[l];return(e=t.call.apply(t,[this].concat(r))).state={editingLanguage:"en"},e.handleMultiTextChange=function(t,a){var o=e.props.value;for(e.props.value&&Array.isArray(e.props.value)||(o=[]);ks.size(o)<=a;)o.push("");o[a]=t,e.props.onChange(o)},e.initAllValues=function(t){var a=e.props.value;e.props.value&&Array.isArray(e.props.value)||(a=[{"@language":t,"@value":""}]);var o=a.findIndex((function(e){return e["@language"]===t}));if(o<0){var n={"@language":t,"@value":""};a.push(n),e.props.onChange(a)}return o=a.findIndex((function(e){return e["@language"]===t}))},e.handleLanguageChange=function(t){e.initAllValues(t),e.setState({editingLanguage:t})},e.handleTextChange=function(t,a){var o=e.initAllValues(t),n=e.props.value;n[o]["@value"]=a,e.props.onChange(n)},e.MultiItem=function(t,a){return n.a.createElement("div",{key:"holder_"+a,style:{display:"flex"}},n.a.createElement("input",{className:"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500",type:e.props.type,disabled:e.props.disabled,value:e.props.value[a],onChange:function(t){return e.handleMultiTextChange(t.target.value,a)},placeholder:e.props.placeholder,key:"input_"+a}),ks.size(e.props.value)<=1?"":n.a.createElement("button",{type:"button",onClick:function(){ks.pullAt(e.props.value,a),e.props.onChange(e.props.value)},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded",key:"button_"+a},n.a.createElement("div",{className:"flex items-center",key:"removeHolder_"+a},n.a.createElement("i",{className:"material-icons font-bold text-base",key:"remove_"+a},"remove"))))},e}return Object(tc.a)(a,[{key:"render",value:function(){var e=this;return n.a.createElement("div",{className:"w-full flex flex-col ".concat(this.props.className)},this.props.label&&n.a.createElement("label",{className:"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2"},this.props.label),"multilang"===this.props.type?n.a.createElement("div",{style:{display:"flex"}},n.a.createElement("select",{value:this.state.editingLanguage,onChange:function(t){return e.handleLanguageChange(t.target.value)}},function(){var e=[];return nc.forEach((function(t){e.push(n.a.createElement("option",{key:t,value:t},t))})),e}()),n.a.createElement("input",{className:"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500",type:this.props.type,disabled:this.props.disabled,value:this.props.value[this.props.value.findIndex((function(t){return t["@language"]===e.state.editingLanguage}))>=0?this.props.value.findIndex((function(t){return t["@language"]===e.state.editingLanguage})):0]["@value"],onChange:function(t){return e.handleTextChange(e.state.editingLanguage,t.target.value)},placeholder:this.props.placeholder})):"multitext"===this.props.type?n.a.createElement("div",null,this.props.value.map(this.MultiItem),n.a.createElement("div",{key:"holder_main",style:{display:"flex"}},n.a.createElement("button",{type:"button",onClick:function(){e.props.value.push(""),e.props.onChange(e.props.value)},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded",key:"button_main"},n.a.createElement("div",{className:"flex items-center",key:"addHolder_main"},n.a.createElement("i",{className:"material-icons font-bold text-base",key:"add_main"},"add"))))):n.a.createElement("input",{className:"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500",type:this.props.type,disabled:this.props.disabled,value:this.props.value,onChange:function(t){return e.props.onChange(t.target.value)},placeholder:this.props.placeholder}))}}]),a}(n.a.Component),lc=function(e){var t=e.data,a=e.onChange,o=Object(Zs.a)("leftSidebar").t,r=function(e,o,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=ks.get(t,e+"."+o,null);null===i&&("string"===typeof n||"number"===typeof n?ks.set(t,e+"."+o,""):"object"===typeof n&&(Array.isArray(n)?ks.set(t,e+"."+o,[]):ks.set(t,e+"."+o,{}))),a("data."+e+"."+o,n),l&&a("data."+e+'["@id"]',l),r&&a("data."+e+'["@type"]',r)};return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",placeholder:"Heading",value:t.profile.heading,onChange:function(e){return a("data.profile.heading",e)}}),n.a.createElement("hr",{className:"my-6"}),n.a.createElement(rc,{className:"mb-6",label:o("profile.photoUrl.label"),placeholder:"https://i.imgur.com/...",value:ks.get(t,'jsonld["@graph"][1].image.contentUrl',""),onChange:function(e){r('jsonld["@graph"][1].image',"contentUrl",e,"ImageObject","_:#image")}}),n.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},n.a.createElement(rc,{className:"mb-6",label:o("profile.firstName.label"),placeholder:"Jane",value:ks.get(t,"jsonld['@graph'][1].givenName",""),onChange:function(e){return r('jsonld["@graph"][1]',"givenName",e)},type:"multilang"}),n.a.createElement(rc,{className:"mb-6",label:o("profile.lastName.label"),placeholder:"Doe",value:ks.get(t,"jsonld['@graph'][1].familyName",""),onChange:function(e){return r('jsonld["@graph"][1]',"familyName",e)},type:"multilang"})),n.a.createElement(rc,{className:"mb-6",label:o("profile.subtitle.label"),placeholder:"Full-Stack Web Developer",value:ks.get(t,'jsonld["@graph"][1].description',""),onChange:function(e){r('jsonld["@graph"][1]',"description",e)}}),n.a.createElement("hr",{className:"my-6"}),n.a.createElement(rc,{className:"mb-6",label:o("profile.website.label"),placeholder:"janedoe.me",value:ks.get(t,'jsonld["@graph"][1].sameAs',[]),onChange:function(e){return r('jsonld["@graph"][1]',"sameAs",e)},AddItem:function(){},type:"multitext"}))},ic=a(859),sc=function(e){var t=e.checked,a=e.onChange,o=e.icon,r=void 0===o?"check":o,l=e.size,i=void 0===l?"2rem":l;return n.a.createElement("div",{className:"relative bg-white border-2 rounded border-gray-400 hover:border-gray-500 flex flex-shrink-0 justify-center items-center mr-2 focus-within:border-blue-500 cursor-pointer",style:{width:i,height:i}},n.a.createElement("input",{type:"checkbox",style:{width:i,height:i},className:"opacity-0 absolute cursor-pointer z-20",checked:t,onChange:function(e){return a(e.target.checked)}}),n.a.createElement("i",{className:"absolute material-icons ".concat(t?"opacity-100":"opacity-0"," text-sm text-gray-800")},r))},cc=function(e){var t=e.dispatch,a=e.first,o=e.identifier,r=e.item,l=e.last,i=e.onChange,s=e.type,c=e.enableAction,u=Object(Zs.a)().t;return n.a.createElement("div",{className:"flex justify-between"},n.a.createElement("div",{className:"flex items-center"},c?c(o,r,i):n.a.createElement(sc,{size:"2.25rem",checked:r.enable,onChange:function(e){i("".concat(o,"enable"),e)}}),n.a.createElement("button",{type:"button",onClick:function(){return function(e,t,a){e({type:"delete_item",payload:{key:t,value:a}}),Ts(e)}(t,s,r)},className:"ml-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"delete"),n.a.createElement("span",{className:"text-sm"},u("buttons.delete.label"))))),n.a.createElement("div",{className:"flex"},!a&&n.a.createElement("button",{type:"button",onClick:function(){return function(e,t,a){e({type:"move_item_up",payload:{key:t,value:a}}),Ts(e)}(t,s,r)},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded mr-2"},n.a.createElement("div",{className:"flex items-center"},n.a.createElement("i",{className:"material-icons font-bold text-base"},"arrow_upward"))),!l&&n.a.createElement("button",{type:"button",onClick:function(){return function(e,t,a){e({type:"move_item_down",payload:{key:t,value:a}}),Ts(e)}(t,s,r)},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded"},n.a.createElement("div",{className:"flex items-center"},n.a.createElement("i",{className:"material-icons font-bold text-base"},"arrow_downward")))))},uc=function(e){var t=e.onSubmit,a=Object(Zs.a)().t;return n.a.createElement("div",null,n.a.createElement("button",{type:"button",onClick:t,className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"add"),n.a.createElement("span",{className:"text-sm"},a("buttons.add.label")))))},dc=function(e){var t=e.title,a=e.heading,o=e.isOpen,r=e.setOpen,l=Object(Zs.a)().t;return n.a.createElement("div",{className:"flex justify-between items-center cursor-pointer",onClick:function(){return r(!o)}},n.a.createElement("h6",{className:"text-sm font-medium"},"undefined"===typeof a?t:l("item.add",{heading:a})),n.a.createElement("i",{className:"material-icons"},o?"expand_less":"expand_more"))},mc=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=Object(Zs.a)(["leftSidebar","app"]).t;return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",label:l("address.streetAddress.label"),placeholder:"20 Malvin Dr",value:t.streetAddress,onChange:function(e){return a("".concat(r,"streetAddress"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("address.addressLocality.label"),placeholder:"Toronto",value:t.addressLocality,onChange:function(e){return a("".concat(r,"addressLocality"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("address.addressRegion.label"),placeholder:"ON",value:t.addressRegion,onChange:function(e){return a("".concat(r,"addressRegion"),e)}}),n.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},n.a.createElement(rc,{className:"mb-6",label:l("address.addressCountry.label"),placeholder:"Canada",value:t.addressCountry,onChange:function(e){return a("".concat(r,"addressCountry"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("address.postalCode.label"),placeholder:"H1H 0H0",value:t.postalCode,onChange:function(e){return a("".concat(r,"postalCode"),e)}})),n.a.createElement(rc,{className:"mb-6",label:l("address.sameAs.label"),placeholder:"Google Map Url of address",value:t.sameAs,onChange:function(e){return a("".concat(r,"sameAs"),e)}}))},pc=function(e){var t=e.heading,a=e.dispatch,r="_:"+Object(ic.a)(),l=Object(o.useState)(!1),i=Object(bs.a)(l,2),s=i[0],c=i[1],u=Object(o.useState)({"@id":r,"@type":"PostalAddress",hoursAvailable:{"@id":r+"#hoursAvailable","@type":"OpeningHoursSpecification",validThrough:"2099-01-01"},addressCountry:"",streetAddress:"",addressRegion:"",addressLocality:"",postalCode:"",contactType:"",sameAs:""}),d=Object(bs.a)(u,2),m=d[0],p=d[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:c,isOpen:s}),n.a.createElement("div",{className:"mt-6 ".concat(s?"block":"hidden")},n.a.createElement(mc,{item:m,onChange:function(e,t){return p(vs()(Object(hs.a)({},m),e,t))}}),n.a.createElement(uc,{onSubmit:function(){var e="_:"+Object(ic.a)();""!==m.addressCountry&&(Js(a,'data.jsonld["@graph"][1].address',m),p({"@id":e,"@type":"PostalAddress",hoursAvailable:{"@id":e+"#hoursAvailable","@type":"OpeningHoursSpecification",validThrough:"2099-01-01"},addressCountry:"",streetAddress:"",addressRegion:"",addressLocality:"",postalCode:"",contactType:"",sameAs:""}),c(!1))}})))},bc=function(e,t,a){return n.a.createElement(sc,{size:"2.25rem",checked:t&&t.hoursAvailable&&t.hoursAvailable.validThrough&&Date.parse(t.hoursAvailable.validThrough)-Date.parse(new Date)>0,onChange:function(t){var o="1900-01-01";t&&(o="2099-01-01"),a("".concat(e,"hoursAvailable.validThrough"),o)}})},hc=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].address['.concat(a,"].");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:t.streetAddress||t.addressCountry,setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(mc,{item:t,onChange:r,identifier:p}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][1].address",enableAction:bc})))},gc=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:t.address.enable,onChange:function(e){return a("data.address.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:t.address.heading,onChange:function(e){return a("data.address.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),t.jsonld["@graph"][1].address&&t.jsonld["@graph"][1].address.map((function(e,o){return n.a.createElement(hc,{dispatch:r,first:0===o,index:o,item:e,key:e["@id"],last:o===t.jsonld["@graph"][1].address.length-1,onChange:a})})),n.a.createElement(pc,{heading:t.address.heading,dispatch:r}))},fc=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=Object(Zs.a)(["leftSidebar","app"]).t;return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",label:l("profile.phone.label"),placeholder:"+1 (999)999-9999",value:t.telephone,onChange:function(e){return a("".concat(r,"telephone"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("profile.email.label"),placeholder:"<EMAIL>",value:t.email,onChange:function(e){return a("".concat(r,"email"),e)}}),n.a.createElement(Xs,{className:"mb-6",label:l("profile.contactType.label"),placeholder:"Only preferred is shown on resume",value:t.contactType,onChange:function(e){return a("".concat(r,"contactType"),e)},options:["Preferred","Emergency","Other"],optionItem:function(e,t){return n.a.createElement("option",{key:e,value:e},e)}}),n.a.createElement(rc,{className:"mb-6",label:l("profile.contacts.description"),placeholder:"Description",value:t.description,onChange:function(e){return a("".concat(r,"description"),e)}}))},yc=function(e){var t=e.heading,a=e.dispatch,r="_:"+Object(ic.a)(),l=Object(o.useState)(!1),i=Object(bs.a)(l,2),s=i[0],c=i[1],u=Object(o.useState)({"@id":r,"@type":"ContactPoint",description:"",contactType:"Preferred",email:"",telephone:""}),d=Object(bs.a)(u,2),m=d[0],p=d[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:c,isOpen:s}),n.a.createElement("div",{className:"mt-6 ".concat(s?"block":"hidden")},n.a.createElement(fc,{item:m,onChange:function(e,t){return p(vs()(Object(hs.a)({},m),e,t))}}),n.a.createElement(uc,{onSubmit:function(){var e="_:"+Object(ic.a)();""!==m.contactType&&(Js(a,'data.jsonld["@graph"][1].contactPoint',m),p({"@id":e,"@type":"ContactPoint",description:"",contactType:"Preferred",email:"",telephone:""}),c(!1))}})))},vc=function(e,t,a){return n.a.createElement(n.a.Fragment,null)},xc=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].contactPoint['.concat(a,"].");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:t.contactType||t.telephone,setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(fc,{item:t,onChange:r,identifier:p}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][1].contactPoint",enableAction:vc})))},Nc=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:t.contacts.enable,onChange:function(e){return a("data.contacts.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:t.contacts.heading,onChange:function(e){return a("data.contacts.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),t.jsonld["@graph"][1].contactPoint&&t.jsonld["@graph"][1].contactPoint.filter((function(e){return"Preferred"===e.contactType})).map((function(e,o){return n.a.createElement(xc,{dispatch:r,first:0===o,index:o,item:e,key:e["@id"],last:o===t.jsonld["@graph"][1].contactPoint.length-1,onChange:a})})),n.a.createElement(yc,{heading:t.contacts.heading,dispatch:r}))},wc=a(858),kc=function(e){var t=e.className;return n.a.createElement("div",{className:t},n.a.createElement("p",{className:"text-gray-800 text-xs"},n.a.createElement(wc.a,{i18nKey:"markdownHelpText"},"You can use",n.a.createElement("a",{className:"text-blue-600 hover:underline",target:"_blank",rel:"noopener noreferrer",href:"https://github.com/adam-p/markdown-here/wiki/Markdown-Cheatsheet"},"GitHub Flavored Markdown"),"to style this section of text.")))},Ec=function(e){var t=e.label,a=e.placeholder,o=e.value,r=e.onChange,l=e.className,i=e.rows,s=void 0===i?5:i;return n.a.createElement("div",{className:"w-full flex flex-col ".concat(l)},n.a.createElement("label",{className:"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2"},t),n.a.createElement("textarea",{className:"appearance-none block leading-7 w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 focus:outline-none focus:bg-white focus:border-gray-500",rows:s,value:o,onChange:function(e){return r(e.target.value)},placeholder:a}),n.a.createElement(kc,{className:"mt-2"}))},Oc=function(e){var t=e.item,a=e.onChange;t||(t={});var o=Object(Zs.a)(["leftSidebar","app"]).t;return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",label:o("address.addressLocality.label"),placeholder:"Toronto",value:ks.get(t,"addressLocality",""),onChange:function(e){return a("addressLocality",e)}}),n.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},n.a.createElement(rc,{className:"mb-6",label:o("address.addressRegion.label"),placeholder:"ON",value:ks.get(t,"addressRegion",""),onChange:function(e){return a("addressRegion",e)}}),n.a.createElement(rc,{className:"mb-6",label:o("address.addressCountry.label"),placeholder:"Canada",value:ks.get(t,"addressCountry",""),onChange:function(e){return a("addressCountry",e)}})))},Sc=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=e.index,i=void 0===l?0:l,s=Object(Zs.a)(["leftSidebar","app"]).t;return n.a.createElement("div",null,0===i?n.a.createElement(Ec,{rows:"15",className:"mb-4",label:s("objective.objective.label"),value:ks.get(t,"description",""),placeholder:"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector.",onChange:function(e){return a("".concat(r,"description"),e)}}):n.a.createElement(n.a.Fragment,null),n.a.createElement(rc,{className:"mb-6",label:s("objective.availabilityStarts.label"),placeholder:"2022-01-01",value:ks.get(t,"availabilityStarts",""),onChange:function(e){return a("".concat(r,"availabilityStarts"),e)}}),n.a.createElement(rc,{className:"mb-6",label:s("objective.availabilityEnds.label"),placeholder:"2022-12-01",item:ks.get(t,"availabilityEnds",""),onChange:function(e){return a("".concat(r,"availabilityEnds"),e)}}),n.a.createElement(Oc,{item:ks.get(t,"availableAtOrFrom.address",{}),onChange:function(e,o){return function(e,o,n){if(e&&o){var l=ks.get(n,"address",{});l["@type"]||(l["@type"]="PostalAddress",l["@id"]=t["@id"]+"_availableAtOrFrom_address"),l[o]=e,ks.set(n,"address",l),n["@type"]||(n["@type"]="Place",n["@id"]=t["@id"]+"_availableAtOrFrom"),a("".concat(r,"availableAtOrFrom"),n)}}(o,e,t.availableAtOrFrom)}}))},jc=function(e){var t=e.heading,a=e.dispatch,r=e.size,l="_:"+Object(ic.a)(),i=Object(o.useState)(!1),s=Object(bs.a)(i,2),c=s[0],u=s[1],d=Object(o.useState)({"@id":l,"@type":"Demand",description:"",availabilityStarts:"",availabilityEnds:"",availableAtOrFrom:{},deliveryLeadTime:{}}),m=Object(bs.a)(d,2),p=m[0],b=m[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:u,isOpen:c}),n.a.createElement("div",{className:"mt-6 ".concat(c?"block":"hidden")},n.a.createElement(Sc,{item:p,onChange:function(e,t){return b(vs()(Object(hs.a)({},p),e,t))},index:r}),n.a.createElement(uc,{onSubmit:function(){var e="_:"+Object(ic.a)();""===p.description&&p.availableAtOrFrom===[]||(Js(a,'data.jsonld["@graph"][1].seeks',p),b({"@id":e,"@type":"Demand",description:"",availabilityStarts:"",availabilityEnds:"",availableAtOrFrom:{},deliveryLeadTime:{}}),u(!1))}})))},Cc=function(e,t,a){return n.a.createElement(sc,{size:"2.25rem",checked:t&&t.availabilityEnds&&Date.parse(t.availabilityEnds)-Date.parse(new Date)>0,onChange:function(t){var o="1900-01-01";t&&(o="2099-01-01"),a("".concat(e,"availabilityEnds"),o)}})},Dc=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].seeks['.concat(a,"].");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:t.availableAtOrFrom.address.addressCountry||t.description.substring(0,10)+"...",setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(Sc,{item:t,onChange:r,identifier:p,index:a}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][1].seeks",enableAction:Cc})))},Ac=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:t.objective.enable,onChange:function(e){return a("data.objective.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:t.objective.heading,onChange:function(e){return a("data.objective.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),t.jsonld["@graph"][1].seeks&&t.jsonld["@graph"][1].seeks.map((function(e,o){return n.a.createElement(Dc,{dispatch:r,first:0===o,index:o,item:e,key:e["@id"],last:o===t.jsonld["@graph"][1].seeks.length-1,onChange:a})})),n.a.createElement(jc,{heading:t.objective.heading,dispatch:r,size:ks.size(t.jsonld["@graph"][1].seeks)}))},Tc=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=Object(Zs.a)(["leftSidebar","app"]).t,i=function(e,o,n){var l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,s=e;o&&(s=s+"."+o);var c=ks.get(t,s,null);null===c&&("string"===typeof n||"number"===typeof n?ks.set(t,s,""):"object"===typeof n&&(Array.isArray(n)?ks.set(t,s,[]):ks.set(t,s,{}))),a(r+s,n),i&&a("".concat(r)+e+'["@id"]',i),l&&a("".concat(r)+e+'["@type"]',l)};return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",label:l("work.name.label"),placeholder:"Amazon",value:ks.get(t,"subjectOf.organizer.name",""),onChange:function(e){return a("".concat(r,"subjectOf.organizer.name"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("work.role.label"),placeholder:"Full-Stack Web Developer",value:ks.get(t,"roleName",""),onChange:function(e){return a("".concat(r,"roleName"),e)}}),n.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},n.a.createElement(rc,{className:"mb-6",label:l("app:item.startDate.label"),placeholder:"2019-01-01",value:ks.get(t,"startDate",""),onChange:function(e){return a("".concat(r,"startDate"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("app:item.endDate.label"),placeholder:"2020-01-01",value:ks.get(t,"endDate",""),onChange:function(e){return a("".concat(r,"endDate"),e)}})),n.a.createElement(rc,{className:"mb-6",label:l("work.responsibilities.label"),placeholder:"Preparing project plans",value:ks.get(t,"hasOccupation.responsibilities",[]),onChange:function(e){return i("hasOccupation","responsibilities",e)},AddItem:function(){},type:"multitext"}),n.a.createElement(rc,{className:"mb-6",label:l("work.skills.label"),placeholder:"Project Management",value:ks.get(t,"hasOccupation.skills",[]),onChange:function(e){return i("hasOccupation","skills",e)},AddItem:function(){},type:"multitext"}),n.a.createElement(Ec,{rows:"5",className:"mb-6",label:l("app:item.description.label"),value:ks.get(t,"description",""),onChange:function(e){return a("".concat(r,"description"),e)}}))},Jc=function(){var e=Object(ic.a)();return{"@type":"EmployeeRole","@id":"_:"+e+"#enable",hasOccupation:{"@id":"_:"+e+"#hasOccupation","@type":"Occupation",name:"",skills:[],responsibilities:[]},subjectOf:{"@type":"BusinessEvent",id:"_:"+e+"#subjectOf",organizer:{"@type":"Organization",id:"_:"+e+"#subjectOf#organizer",name:""}},roleName:"",startDate:"",endDate:"",description:""}},Pc=function(e){var t=e.heading,a=e.dispatch,r=Object(o.useState)(!1),l=Object(bs.a)(r,2),i=l[0],s=l[1],c=Object(o.useState)(Jc()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:s,isOpen:i}),n.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},n.a.createElement(Tc,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),n.a.createElement(uc,{onSubmit:function(){""!==d.roleName&&(Js(a,"data.jsonld['@graph'][1].hasOccupation",d),m(Jc()),s(!1))}})))},Rc=function(e,t,a){return n.a.createElement(n.a.Fragment,null)},Fc=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].hasOccupation['.concat(a,"].");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:t.roleName+" "+t.subjectOf.organizer.name,setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(Tc,{item:t,onChange:r,identifier:p}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][1].hasOccupation",enableAction:Rc})))},Lc=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:t.work.enable,onChange:function(e){return a("data.work.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:t.work.heading,onChange:function(e){return a("data.work.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),ks.get(t.jsonld["@graph"][1],"hasOccupation",[]).map((function(e,o){return n.a.createElement(Fc,{dispatch:r,first:0===o,index:o,item:e,key:ks.get(e,"@id","item"),last:o===ks.size(t.jsonld["@graph"][1].hasOccupation)-1,onChange:a})})),n.a.createElement(Pc,{heading:t.work.heading,dispatch:r}))},zc=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=Object(Zs.a)(["leftSidebar","app"]).t;return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",label:l("education.name.label"),placeholder:"Harvard University",value:ks.get(t,"about.provider.name",""),onChange:function(e){return a("".concat(r,"about.provider.name"),e)}}),n.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},n.a.createElement(Xs,{className:"mb-6",label:l("education.type.label"),placeholder:"Certificate type",value:ks.get(t,"credentialCategory",""),onChange:function(e){return a("".concat(r,"credentialCategory"),e)},options:["Degree","Certificate","Badge"],optionItem:function(e,t){return n.a.createElement("option",{key:e,value:e},e)}}),n.a.createElement(rc,{className:"mb-6",label:l("education.major.degree"),placeholder:"Masters of Science",value:ks.get(t,"educationalLevel",""),onChange:function(e){return a("".concat(r,"educationalLevel"),e)}})),n.a.createElement(rc,{className:"mb-6",label:l("education.major.label"),placeholder:"Computer Science",value:ks.get(t,"about.educationalCredentialAwarded",""),onChange:function(e){return a("".concat(r,"about.educationalCredentialAwarded"),e)}}),n.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},n.a.createElement(rc,{className:"mb-6",label:l("education.grade.label"),placeholder:"3.7",value:ks.get(t,"aggregateRating.ratingValue",""),onChange:function(e){return a("".concat(r,"aggregateRating.ratingValue"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("education.maxGrade.label"),placeholder:"4",value:ks.get(t,"aggregateRating.bestRating",""),onChange:function(e){return a("".concat(r,"aggregateRating.bestRating"),e)}})),n.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},n.a.createElement(rc,{className:"mb-6",label:l("app:item.startDate.label"),placeholder:"2018-01-01",value:ks.get(t,"about.startDate",""),onChange:function(e){return a("".concat(r,"about.startDate"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("app:item.endDate.label"),placeholder:"2020-01-01",value:ks.get(t,"about.endDate",""),onChange:function(e){return a("".concat(r,"about.endDate"),e)}})),n.a.createElement(rc,{className:"mb-6",label:l("work.skills.label"),placeholder:"Project Management",value:ks.get(t,"teaches",[]),onChange:function(e){return function(e,o,n){var l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,s=e;o&&(s=s+"."+o);var c=ks.get(t,s,null);null===c&&("string"===typeof n||"number"===typeof n?ks.set(t,s,""):"object"===typeof n&&(Array.isArray(n)?ks.set(t,s,[]):ks.set(t,s,{}))),a(r+s,n),i&&a("".concat(r)+e+'["@id"]',i),l&&a("".concat(r)+e+'["@type"]',l)}("teaches","",e)},AddItem:function(){},type:"multitext"}),n.a.createElement(Ec,{rows:"5",className:"mb-6",label:l("app:item.description.label"),value:ks.get(t,"abstract",""),onChange:function(e){return a("".concat(r,"abstract"),e)}}))},Ic=function(){var e=Object(ic.a)();return{"@type":"EducationalOccupationalCredential","@id":"_:"+e+"#enable",aggregateRating:{"@id":"_:"+e+"#aggregateRating","@type":"aggregateRating",bestRating:"",ratingValue:"",name:"GPA",itemReviewed:{"@id":"_:"+e+"#enable"}},credentialCategory:"degree",educationalLevel:"",abstract:"",teaches:[],about:{"@id":"_:"+e+"#about","@type":"EducationalOccupationalProgram",educationalCredentialAwarded:"",startDate:"",endDate:"",provider:{"@id":"_:"+e+"#about#provider","@type":"CollegeOrUniversity",name:""}}}},Mc=function(e){var t=e.heading,a=e.dispatch,r=Object(o.useState)(!1),l=Object(bs.a)(r,2),i=l[0],s=l[1],c=Object(o.useState)(Ic()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:s,isOpen:i}),n.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},n.a.createElement(zc,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),n.a.createElement(uc,{onSubmit:function(){""!==d.educationalLevel&&(Js(a,'data.jsonld["@graph"][1].hasCredential',d),m(Ic()),s(!1))}})))},Hc=function(e,t,a){return n.a.createElement(n.a.Fragment,null)},Gc=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p="data.jsonld['@graph'][1].hasCredential[".concat(a,"].");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:ks.get(t,"educationalLevel",""),setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(zc,{item:t,onChange:r,identifier:p}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][1].hasCredential",enableAction:Hc})))},qc=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:t.education.enable,onChange:function(e){return a("data.education.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:t.education.heading,onChange:function(e){return a("data.education.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),ks.get(t.jsonld["@graph"][1],"hasCredential",[]).map((function(e,o){return n.a.createElement(Gc,{item:e,key:ks.get(e,"@id","item"),index:o,onChange:a,dispatch:r,first:0===o,last:o===ks.size(ks.get(t.jsonld["@graph"][1],"hasCredential",[]))-1})})),n.a.createElement(Mc,{heading:t.education.heading,dispatch:r}))},Yc=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=e.altidentifier,i=void 0===l?"":l,s=Object(Zs.a)(["leftSidebar","app"]).t;return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",label:s("awards.title.label"),placeholder:"Code For Good Hackathon",value:ks.get(t,"skill:title",""),onChange:function(e){a("".concat(r,"['skill:title']"),e),""!==i&&a("".concat(i),e)}}),n.a.createElement(rc,{className:"mb-6",label:s("awards.subtitle.label"),placeholder:"Google",value:ks.get(t,"skill:conferredBy",""),onChange:function(e){return a("".concat(r,"['skill:conferredBy']"),e)}}),n.a.createElement(Ec,{className:"mb-6",label:s("app:item.description.label"),value:t.description,onChange:function(e){return a("".concat(r,"description"),e)}}))},Uc=function(){return{"@type":"skill:Award","@id":"_:"+Object(ic.a)()+"#enable","skill:title":"","skill:nativeLabel":"",description:""}},_c=function(e){var t=e.heading,a=e.dispatch,r=Object(o.useState)(!1),l=Object(bs.a)(r,2),i=l[0],s=l[1],c=Object(o.useState)(Uc()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:s,isOpen:i}),n.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},n.a.createElement(Yc,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),n.a.createElement(uc,{onSubmit:function(){""!==ks.get(d,"skill:title","")&&(Js(a,'data.jsonld["@graph"][0]["award"]',d),Js(a,'data.jsonld["@graph"][1]["award"]',ks.get(d,"skill:title","")),m(Uc()),s(!1))}})))},Wc=function(e,t,a){return n.a.createElement(n.a.Fragment,null)},Vc=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][0].award['.concat(a,"]."),b='data.jsonld["@graph"][1].award['.concat(a,"]");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:t["skill:title"],setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(Yc,{item:t,onChange:r,identifier:p,altidentifier:b}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][0].award",enableAction:Wc})))},Bc=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:t.awards.enable,onChange:function(e){return a("data.awards.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:t.awards.heading,onChange:function(e){return a("data.awards.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),ks.get(t.jsonld["@graph"][0],"award",[]).map((function(e,o){return n.a.createElement(Vc,{item:e,key:ks.get(e,"@id","main"),index:o,onChange:a,dispatch:r,first:0===o,last:o===ks.size(ks.get(t.jsonld["@graph"][0],"award",[]))-1})})),n.a.createElement(_c,{heading:t.awards.heading,dispatch:r}))},Kc=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=Object(Zs.a)("leftSidebar").t;return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",label:l("extras.key.label"),placeholder:"Date of Birth",value:ks.get(t,"propertyID",""),onChange:function(e){return a("".concat(r,"propertyID"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("extras.value.label"),placeholder:"6th August 1995",value:ks.get(t,"value",""),onChange:function(e){return a("".concat(r,"value"),e)}}))},Zc=function(){return{"@type":"PropertyValue","@id":"_:Extras_"+Object(ic.a)(),propertyID:"",value:""}},Qc=function(e){var t=e.heading,a=e.dispatch,r=Object(o.useState)(!1),l=Object(bs.a)(r,2),i=l[0],s=l[1],c=Object(o.useState)(Zc()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:s,isOpen:i}),n.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},n.a.createElement(Kc,{item:d,onChange:function(e,t){return m((function(a){return vs()(Object(hs.a)({},a),e,t)}))}}),n.a.createElement(uc,{onSubmit:function(){""!==ks.get(d,"propertyID","")&&""!==ks.get(d,"value","")&&(Js(a,'data.jsonld["@graph"][1].identifier',d),m(Zc()),s(!1))}})))},Xc=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].identifier['.concat(a,"].");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:ks.get(t,"propertyID",""),setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(Kc,{item:t,onChange:r,identifier:p}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][1].identifier"})))},$c=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:t.extras.enable,onChange:function(e){return a("data.extras.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:t.extras.heading,onChange:function(e){return a("data.extras.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),ks.get(t.jsonld["@graph"][1],"identifier",[]).map((function(e,o){return n.a.createElement(Xc,{item:e,key:ks.get(e,"@id","main"),index:o,onChange:a,dispatch:r,first:0===o,last:o===ks.get(t.jsonld["@graph"][1],"identifier",[]).length-1})})),n.a.createElement(Qc,{heading:t.extras.heading,dispatch:r}))},eu=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=Object(Zs.a)("leftSidebar").t;return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",label:l("languages.key.label"),placeholder:"English",value:ks.get(t,"name",""),onChange:function(e){return a("".concat(r,"name"),e)}}))},tu=function(){return{"@type":"Language","@id":"_:"+Object(ic.a)(),name:""}},au=function(e){var t=e.heading,a=e.dispatch,r=Object(o.useState)(!1),l=Object(bs.a)(r,2),i=l[0],s=l[1],c=Object(o.useState)(tu()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:s,isOpen:i}),n.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},n.a.createElement(eu,{item:d,onChange:function(e,t){return m((function(a){return vs()(Object(hs.a)({},a),e,t)}))}}),n.a.createElement(uc,{onSubmit:function(){""!==ks.get(d,"name","")&&(Js(a,'data.jsonld["@graph"][1].knowsLanguage',d),m(tu()),s(!1))}})))},ou=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].knowsLanguage['.concat(a,"].");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:ks.get(t,"name",""),setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(eu,{item:t,onChange:r,identifier:p}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][1].knowsLanguage"})))},nu=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return Object(o.useEffect)((function(){"languages"in t||(r({type:"migrate_section",payload:{key:"languages",value:{enable:!1,heading:"Languages"}}}),r({type:"save_data"}))}),[t,r]),"languages"in t&&n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:t.languages.enable,onChange:function(e){return a("data.languages.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:t.languages.heading,onChange:function(e){return a("data.languages.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),ks.get(t.jsonld["@graph"][1],"knowsLanguage",[]).map((function(e,o){return n.a.createElement(ou,{item:e,key:ks.get(e,"@id","item"),index:o,onChange:a,dispatch:r,first:0===o,last:o===ks.size(ks.get(t.jsonld["@graph"][1],"knowsLanguage",[]))-1})})),n.a.createElement(au,{heading:t.languages.heading,dispatch:r}))},ru=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=Object(Zs.a)(["leftSidebar","app"]).t;return n.a.createElement("div",null,n.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},n.a.createElement(rc,{className:"mb-6",label:l("references.name.label"),placeholder:"Richard Hendricks",value:ks.get(t,"interactionType.participant.givenName",""),onChange:function(e){return a("".concat(r,"interactionType.participant.givenName"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("references.familyName.label"),placeholder:"Richard Hendricks",value:ks.get(t,"interactionType.participant.familyName",""),onChange:function(e){return a("".concat(r,"interactionType.participant.familyName"),e)}})),n.a.createElement(rc,{className:"mb-6",label:l("references.position.label"),placeholder:"CEO, Pied Piper",value:ks.get(t,"interactionType.participant.jobTitle",""),onChange:function(e){return a("".concat(r,"interactionType.participant.jobTitle"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("references.phone.label"),placeholder:"****** 754 3010",value:ks.get(t,"interactionType.participant.telephone",""),onChange:function(e){return a("".concat(r,"interactionType.participant.telephone"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("references.email.label"),placeholder:"<EMAIL>",value:ks.get(t,"interactionType.participant.email",""),onChange:function(e){return a("".concat(r,"interactionType.participant.email"),e)}}),n.a.createElement(Ec,{rows:"5",className:"mb-6",label:l("app:item.description.label"),value:ks.get(t,"result[0].reviewRating.ratingExplanation",""),onChange:function(e){return a("".concat(r,"result[0].reviewRating.ratingExplanation"),e)}}))},lu=function(){var e=Object(ic.a)();return{"@id":"_:Reference#"+e,"@type":"InteractionCounter",disambiguatingDescription:"Reference",interactionType:{"@id":"_:Reference#"+e+"#interactionType","@type":"AssessAction",participant:{"@id":"_:Reference#"+e+"#interactionType#participant","@type":"Person"},result:[{"@id":"_:Reference#"+e+"#result","@type":"Review",itemReviewed:{},reviewAspect:[],reviewRating:{"@id":"_:Reference#"+e+"#result#reviewRating","@type":"Rating",ratingValue:"5",bestRating:"5",ratingExplanation:""}}]}}},iu=function(e){var t=e.heading,a=e.dispatch,r=Object(o.useState)(!1),l=Object(bs.a)(r,2),i=l[0],s=l[1],c=Object(o.useState)(lu()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:s,isOpen:i}),n.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},n.a.createElement(ru,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),n.a.createElement(uc,{onSubmit:function(){""!==ks.get(d,"interactionType.participant.givenName","")&&(Js(a,'data.jsonld["@graph"][1].interactionStatistic',d),m(lu()),s(!1))}})))},su=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].interactionStatistic['.concat(a,"].");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:ks.get(t,"interactionType.participant.givenName","")+" "+ks.get(t,"interactionType.participant.familyName",""),setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(ru,{item:t,onChange:r,identifier:p}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][1].interactionStatistic"})))},cu=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return Object(o.useEffect)((function(){"references"in t||(r({type:"migrate_section",payload:{key:"references",value:{enable:!1,heading:"References"}}}),r({type:"save_data"}))}),[t,r]),"references"in t&&n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:t.references.enable,onChange:function(e){return a("data.references.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:t.references.heading,onChange:function(e){return a("data.references.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),ks.get(t.jsonld["@graph"][1],"interactionStatistic",[]).filter((function(e){return"Reference"===ks.get(e,"disambiguatingDescription","")})).map((function(e,o){return n.a.createElement(su,{item:e,key:ks.get(e,"@id","main"),index:o,onChange:a,dispatch:r,first:0===o,last:o===ks.get(t.jsonld["@graph"][1],"interactionStatistic",[]).filter((function(e){return"Reference"===ks.get(e,"disambiguatingDescription","")})).length-1})})),n.a.createElement(iu,{heading:t.references.heading,dispatch:r}))},uu=function(e){var t=e.item,a=e.onChange,o=e.identifier,r=void 0===o?"":o,l=Object(Zs.a)(["leftSidebar","app"]).t;return n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-6",label:l("membership.programName.label"),placeholder:"Salsa Dance Class",value:ks.get(t,"memberOf.programName",""),onChange:function(e){return a("".concat(r,"memberOf.programName"),e)}}),n.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},n.a.createElement(rc,{className:"mb-6",label:l("membership.startDate.label"),placeholder:"2019-01-01",value:ks.get(t,"startDate",""),onChange:function(e){return a("".concat(r,"startDate"),e)}}),n.a.createElement(rc,{className:"mb-6",label:l("membership.endDate.label"),placeholder:"2020-01-01",value:ks.get(t,"endDate",""),onChange:function(e){return a("".concat(r,"endDate"),e)}})),n.a.createElement(rc,{className:"mb-6",label:l("membership.roleName.label"),placeholder:"VIP member",value:ks.get(t,"roleName",""),onChange:function(e){return a("".concat(r,"roleName"),e)}}))},du=function(){var e=Object(ic.a)();return{"@id":"_:"+e+"#enable","@type":"Role",startDate:"",endDate:"",roleName:"member",memberOf:{"@id":"_:"+e+"#memberOf","@type":"ProgramMembership",url:"",programName:"",description:""}}},mu=function(e){var t=e.heading,a=e.dispatch,r=Object(o.useState)(!1),l=Object(bs.a)(r,2),i=l[0],s=l[1],c=Object(o.useState)(du()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{heading:t,setOpen:s,isOpen:i}),n.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},n.a.createElement(uu,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),n.a.createElement(uc,{onSubmit:function(){""!==ks.get(d,"roleName","")&&(Js(a,'data.jsonld["@graph"][1].memberOf',d),m(du()),s(!1))}})))},pu=function(e,t,a){return n.a.createElement(n.a.Fragment,null)},bu=function(e){var t=e.item,a=e.index,r=e.onChange,l=e.dispatch,i=e.first,s=e.last,c=Object(o.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p="data.jsonld['@graph'][1].memberOf[".concat(a,"].");return n.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},n.a.createElement(dc,{title:ks.get(t,"memberOf.programName",""),setOpen:m,isOpen:d}),n.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},n.a.createElement(uu,{item:t,onChange:r,identifier:p}),n.a.createElement(cc,{dispatch:l,first:i,identifier:p,item:t,last:s,onChange:r,type:"data.jsonld['@graph'][1].memberOf",enableAction:pu})))},hu=function(e){var t=e.data,a=e.onChange,r=Object(o.useContext)(_s).dispatch;return n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"my-6 grid grid-cols-6 items-center"},n.a.createElement("div",{className:"col-span-1"},n.a.createElement(sc,{checked:ks.get(t,"Memberships.enable",!0),onChange:function(e){return a("data.Memberships.enable",e)}})),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{placeholder:"Heading",value:ks.get(t,"Memberships.heading",""),onChange:function(e){return a("data.Memberships.heading",e)}}))),n.a.createElement("hr",{className:"my-6"}),ks.get(t.jsonld["@graph"][1],"memberOf",[]).map((function(e,o){return n.a.createElement(bu,{item:e,key:ks.get(e,"@id","item"),index:o,onChange:a,dispatch:r,first:0===o,last:o===ks.size(ks.get(t.jsonld["@graph"][1],"memberOf",[]))-1})})),n.a.createElement(mu,{heading:ks.get(t,"Memberships.heading",""),dispatch:r}))},gu=function(){var e=Object(o.useContext)(_s),t=e.state,a=e.dispatch,r=t.data,l=[{key:"profile",name:ks.get(r,"profile.heading","Profile")},{key:"address",name:ks.get(r,"address.headin","Address")},{key:"contacts",name:ks.get(r,"contacts.heading","Contacts")},{key:"objective",name:ks.get(r,"objective.heading","Objective")},{key:"work",name:ks.get(r,"work.heading","Work")},{key:"education",name:ks.get(r,"education.heading","Education")},{key:"awards",name:ks.get(r,"awards.heading","Awards")},{key:"memberships",name:ks.get(r,"memberships.heading","Memberships")},{key:"languages",name:ks.get(r,"languages.heading","Languages")},{key:"references",name:ks.get(r,"references.heading","References")},{key:"extras",name:ks.get(r,"extras.heading","Extras")}],i=Object(o.useState)(l[0].key),s=Object(bs.a)(i,2),c=s[0],u=s[1],d=function(e,t){a({type:"on_input",payload:{key:e,value:t}}),a({type:"save_data"})};return n.a.createElement("div",{id:"leftSidebar",className:"animated slideInLeft z-10 py-6 h-screen bg-white col-span-1 shadow-2xl overflow-y-scroll"},n.a.createElement($s,{tabs:l,currentTab:c,setCurrentTab:u}),n.a.createElement("div",{className:"px-6"},function(){switch(c){case"profile":return n.a.createElement(lc,{data:r,onChange:d});case"address":return n.a.createElement(gc,{data:r,onChange:d});case"contacts":return n.a.createElement(Nc,{data:r,onChange:d});case"objective":return n.a.createElement(Ac,{data:r,onChange:d});case"work":return n.a.createElement(Lc,{data:r,onChange:d});case"education":return n.a.createElement(qc,{data:r,onChange:d});case"awards":return n.a.createElement(Bc,{data:r,onChange:d});case"memberships":return n.a.createElement(hu,{data:r,onChange:d});case"languages":return n.a.createElement(nu,{data:r,onChange:d});case"references":return n.a.createElement(cu,{data:r,onChange:d});case"extras":return n.a.createElement($c,{data:r,onChange:d});default:return null}}()))},fu=a(8),yu=a.n(fu),vu=function(e){var t=e.item,a=e.language;return n.a.createElement("div",null,n.a.createElement("div",{className:"flex justify-between items-center"},n.a.createElement("div",{className:"flex flex-col text-left mr-2"},n.a.createElement("h6",{className:"font-semibold text-sm"},ks.get(t,"['skill:title']","")),n.a.createElement("span",{className:"text-xs"},ks.get(t,"['skill:conferredBy'].name",""))),""!==ks.get(t,"['skill:awardedDate']","")&&n.a.createElement("h6",{className:"text-xs font-medium text-right"},zs({date:ks.get(t,"['skill:awardedDate']",""),language:a}))),""!==ks.get(t,"description","")&&n.a.createElement(yu.a,{className:"markdown mt-2 text-sm",source:ks.get(t,"description","")}))},xu=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return t.awards&&t.awards.enable?n.a.createElement("div",null,n.a.createElement(a,null,t.awards.heading),n.a.createElement("div",{className:"grid gap-4"},ks.get(t.jsonld["@graph"][0],"award",[]).filter((function(e){return""!==e["skill:title"]})).map((function(e){return n.a.createElement(vu,{key:ks.get(e,"@id",Object(ic.a)()),item:e,language:t.language||"en"})})))):null})),Nu=function(e){var t=e.item,a=e.language;return n.a.createElement("div",null,n.a.createElement("div",{className:"flex justify-between items-center"},n.a.createElement("div",{className:"flex flex-col text-left mr-2"},n.a.createElement("h6",{className:"font-semibold text-sm"},ks.get(t,"educationalLevel","")," ",ks.get(t,"about.educationalCredentialAwarded","")),n.a.createElement("span",{className:"text-xs"},ks.get(t,"about.provider.name",""))),""!==ks.get(t,"about.endDate","")&&n.a.createElement("h6",{className:"text-xs font-medium text-right"},zs({date:ks.get(t,"about.endDate",""),language:a}))),""!==ks.get(t,"abstract","")&&n.a.createElement(yu.a,{className:"markdown mt-2 text-sm",source:ks.get(t,"abstract","")}))},wu=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return t.certifications&&t.certifications.enable?n.a.createElement("div",null,n.a.createElement(a,null,t.certifications.heading),n.a.createElement("div",{className:"grid gap-4"},ks.get(t,"jsonld['@graph'][1].hasCredential",[]).filter((function(e){return!ks.get(e,"@id","").endsWith("disable")&&"degree"!==ks.toLower(ks.get(e,"credentialCategory",""))})).map((function(e){return n.a.createElement(Nu,{key:ks.get(e,"@id",Object(ic.a)()),item:e,language:t.language})})))):null})),ku=a(9),Eu=a(29),Ou={phone:Eu.c,website:ku.h,email:Eu.a,facebook:ku.e,twitter:ku.n,linkedin:ku.j,github:ku.f,dribbble:ku.d,instagram:ku.i,stackoverflow:ku.l,behance:ku.a,gitlab:ku.g,birthday:ku.b,telegram:ku.m,skype:ku.k},Su=Object(o.memo)((function(){var e=Object(o.useContext)(_s).state,t=e.data,a=e.theme,r=Object(ks.get)(Ou,"birthday");return ks.get(t,"jsonld['@graph'][1].birthDate","")?n.a.createElement("div",{className:"text-xs flex items-center"},n.a.createElement(r,{size:"10px",className:"mr-2",style:{color:a.colors.primary}}),n.a.createElement("span",{className:"font-medium break-all"},zs({date:ks.get(t,"jsonld['@graph'][1].birthDate",""),language:t.language||"en",includeDay:!0}))):null})),ju=function(e){var t=e.value,a=e.icon,r=e.link,l=Object(ks.get)(Ou,a&&a.toLowerCase(),ku.c),i=Object(o.useContext)(_s).state,s=(i.data,i.theme);return t?n.a.createElement("div",{className:"flex items-center"},n.a.createElement(l,{size:"10px",className:"mr-2",style:{color:s.colors.primary}}),r?n.a.createElement("a",{href:r,target:"_blank",rel:"noopener noreferrer"},n.a.createElement("span",{className:"font-medium break-all"},t)):n.a.createElement("span",{className:"font-medium break-all"},t)):null},Cu=Object(o.memo)((function(){var e=Object(Zs.a)().t,t=Object(o.useContext)(_s).state,a=t.data;t.theme;return n.a.createElement("div",{className:"text-xs grid gap-2"},n.a.createElement(ju,{label:ks.get(a,"profile.phone.heading",e("Phone")),value:ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"telephone",""),icon:"phone",link:"tel:".concat(ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"telephone",""))}),n.a.createElement(ju,{label:ks.get(a,"profile.website.heading",e("Website")),value:ks.get(a,'jsonld["@graph"][1].sameAs[0]',""),icon:"website",link:ks.get(a,'jsonld["@graph"][1].sameAs[0]',"")}),n.a.createElement(ju,{label:ks.get(a,"profile.email.heading",e("Email")),value:ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"email",""),icon:"email",link:"mailto:".concat(ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"email",""))}),n.a.createElement(Su,null),Ms(a.social)&&a.social.items.map((function(e){return n.a.createElement(ju,{key:e.id,value:e.username,icon:e.network,link:e.url})})))})),Du=function(e){return e&&n.a.createElement("p",{key:Object(ic.a)()},"\xa0| ",e)},Au=Object(o.memo)((function(e){var t=e.skills;return t&&t.length>0&&n.a.createElement("div",{className:"text-xs text-gray-800 flex"},t.filter((function(e){return""!==e})).map(Du))})),Tu=function(e){var t=e.item,a=e.language,o=Object(Zs.a)().t;return n.a.createElement("div",null,n.a.createElement("div",{className:"flex justify-between items-center"},n.a.createElement("div",{className:"flex flex-col text-left mr-2"},n.a.createElement("h6",{className:"font-semibold text-sm"},ks.get(t,"about.provider.name","")),n.a.createElement("span",{className:"text-xs"},n.a.createElement("strong",null,ks.get(t,"educationalLevel",""))," ",ks.get(t,"about.educationalCredentialAwarded",""))),n.a.createElement("div",{className:"flex flex-col items-end text-right"},""!==ks.get(t,"about.startDate","")&&n.a.createElement("h6",{className:"text-xs font-medium mb-1"},"(",Is({startDate:ks.get(t,"about.startDate",""),endDate:ks.get(t,"about.endDate",""),language:a},o),")"),n.a.createElement("span",{className:"text-sm font-medium"},ks.get(t,"aggregateRating.ratingValue",""),"/",ks.get(t,"aggregateRating.bestRating","")))),t.summary&&n.a.createElement(yu.a,{className:"markdown mt-2 text-sm",source:ks.get(t,"abstract","")}),n.a.createElement(Au,{skills:ks.get(t,"teaches",[])}))},Ju=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return ks.get(t,"jsonld['@graph'][1].hasCredential",[]).length>0&&t.education.enable?n.a.createElement("div",null,n.a.createElement(a,null,t.education.heading),n.a.createElement("div",{className:"grid gap-4"},ks.get(t,"jsonld['@graph'][1].hasCredential",[]).filter((function(e){return!ks.get(e,"@id","").endsWith("disable")&&"degree"===ks.get(e,"credentialCategory","")})).map((function(e){return n.a.createElement(Tu,{key:ks.get(e,"@id",Object(ic.a)()),item:e,language:t.language||"en"})})))):null})),Pu=Object(o.memo)((function(e){var t=e.children,a=Object(o.useContext)(_s).state,r=(a.data,a.theme);return n.a.createElement("h6",{className:"text-xs font-bold uppercase mb-1",style:{color:r.colors.primary}},t)})),Ru=function(e){return n.a.createElement("div",{key:e.id},n.a.createElement("h6",{className:"font-semibold text-sm"},e.name))},Fu=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return Ms(t.hobbies)?n.a.createElement("div",null,n.a.createElement(a,null,t.hobbies.heading),n.a.createElement("div",{className:"grid gap-2"},t.hobbies.items.map(Ru))):null})),Lu=function(e){return n.a.createElement("div",{key:ks.get(e,"@id",Object(ic.a)()),className:"flex flex-col"},n.a.createElement("h6",{className:"font-semibold text-sm"},ks.get(e,"name","")),n.a.createElement("span",{className:"text-xs"},e.fluency))},zu=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return t.languages&&t.languages.enable&&ks.get(t,'jsonld["@graph"][1].knowsLanguage',[]).length>0?n.a.createElement("div",null,n.a.createElement(a,null,t.languages.heading),n.a.createElement("div",{className:"grid grid-cols-2 gap-2"},ks.get(t,'jsonld["@graph"][1].knowsLanguage',[]).filter((function(e){return""!==ks.get(e,"name","")})).map(Lu))):null})),Iu=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return ks.size(ks.get(t,'jsonld["@graph"][1].seeks',[]))>0&&n.a.createElement("div",null,n.a.createElement(a,null,t.objective.heading),ks.get(t,'jsonld["@graph"][1].seeks',[]).map((function(e,t){return n.a.createElement(yu.a,{key:"objetive_"+t,className:"mr-10 text-sm",source:e.description})})))})),Mu=function(e){var t=e.item,a=e.language,o=Object(Zs.a)().t;return n.a.createElement("div",null,n.a.createElement("div",{className:"flex justify-between items-center"},n.a.createElement("div",{className:"flex flex-col text-left mr-2"},n.a.createElement("h6",{className:"font-semibold text-sm"},t.title),t.link&&n.a.createElement("a",{href:t.link,className:"text-xs"},t.link)),t.date&&n.a.createElement("h6",{className:"text-xs font-medium text-right"},"(",Is({startDate:t.date,endDate:t.endDate,language:a},o),")")),t.summary&&n.a.createElement(yu.a,{className:"markdown mt-2 text-sm",source:t.summary}))},Hu=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return Ms(t.projects)?n.a.createElement("div",null,n.a.createElement(a,null,t.projects.heading),n.a.createElement("div",{className:"grid gap-4"},t.projects.items.map((function(e){return n.a.createElement(Mu,{key:e.id,item:e,language:t.metadata.language})})))):null})),Gu=function(e){return n.a.createElement("div",{key:ks.get(e,"@id",Object(ic.a)()),className:"flex flex-col"},n.a.createElement("h6",{className:"font-semibold text-sm"},ks.get(e,"interactionType.participant.givenName","")," ",ks.get(e,"interactionType.participant.familyName","")),n.a.createElement("span",{className:"text-xs"},ks.get(e,"interactionType.participant.jobTitle","")),n.a.createElement("span",{className:"text-xs"},ks.get(e,"interactionType.participant.telephone","")),n.a.createElement("span",{className:"text-xs"},ks.get(e,"interactionType.participant.email","")),""!==ks.get(e,"result[0].reviewRating.ratingExplanation","")&&n.a.createElement(yu.a,{className:"markdown mt-2 text-sm",source:ks.get(e,"result[0].reviewRating.ratingExplanation","")}))},qu=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return Ms(t.references)?n.a.createElement("div",null,n.a.createElement(a,null,t.references.heading),n.a.createElement("div",{className:"grid grid-cols-3 gap-4"},ks.get(t.jsonld["@graph"][1],"interactionStatistic",[]).filter((function(e){return"Reference"===ks.get(e,"disambiguatingDescription","")})).map(Gu))):null})),Yu=a(18),Uu=function(e,t){return void 0!==e&&"undefined"!==e&&""!==e&&n.a.createElement("div",{key:Object(ic.a)(),className:"flex flex-col"},n.a.createElement("h6",{className:"font-semibold text-sm"},e),n.a.createElement("span",{className:"text-xs"},t<2?"Beginner":t<=4&&t>=2?"Intermediate":"Advanced"))},_u=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading,r=function(e){for(var t=ks.chain(ks.get(e,"jsonld['@graph'][1].hasOccupation",[])).map("skills").flatten(),a=ks.chain(ks.get(e,"jsonld['@graph'][0].award",[])).map("skill:assesses").flatten(),o=ks.chain(ks.get(e,"jsonld['@graph'][1].hasCredential",[])).map("teaches").flatten(),n=ks.chain(ks.get(e,"jsonld['@graph'][1].hasCredential",[])).map("about").flatten().map("hasCourse").flatten().map("teaches").flatten(),r=ks.chain(ks.get(e,"jsonld['@graph'][1].hasCredential",[])).map("about").map("workExample").flatten().map("hasPart").flatten().map("teaches").flatten(),l=ks.chain(ks.get(e,"jsonld['@graph'][1].interactionStatistic",[])).map("result").flatten().map("teaches").flatten(),i=ks.chain(ks.get(e,"jsonld['@graph'][1].interactionStatistic",[])).map("result").flatten().map("assesses").flatten(),s=[].concat(Object(Yu.a)(t),Object(Yu.a)(a),Object(Yu.a)(o),Object(Yu.a)(n),Object(Yu.a)(r),Object(Yu.a)(l),Object(Yu.a)(i)),c={},u=0;u<s.length;u++)c[s[u]]?c[s[u]]=c[s[u]]+1:c[s[u]]=1;return c}(t);return ks.size(r)>0?n.a.createElement("div",null,n.a.createElement(a,null,t.skills.heading),n.a.createElement("div",{className:"grid grid-cols-2 gap-y-2 gap-x-4"},Object.keys(r).map(Uu))):null})),Wu=function(e){return e&&n.a.createElement("li",{className:"mt-2 text-sm",key:Object(ic.a)()},e)},Vu=function(e){var t=e.responsibilities;return t&&t.length>0&&n.a.createElement("ul",null,t.filter((function(e){return""!==e})).map(Wu))},Bu=function(e){var t=e.item,a=e.language,o=void 0===a?"en":a,r=Object(Zs.a)().t;return n.a.createElement("div",null,n.a.createElement("div",{className:"flex justify-between items-center"},n.a.createElement("div",{className:"flex flex-col text-left mr-2"},n.a.createElement("h6",{className:"font-semibold text-sm"},ks.get(t,"subjectOf.organizer.name","")),n.a.createElement("span",{className:"text-xs"},ks.get(t,"roleName",""))),""!==ks.get(t,"startDate","")&&n.a.createElement("h6",{className:"text-xs font-medium text-right"},"(",Is({startDate:ks.get(t,"startDate",""),endDate:ks.get(t,"endDate",""),language:o},r),")")),""!==ks.get(t,"description","")&&n.a.createElement(yu.a,{className:"markdown mt-2 text-sm",source:ks.get(t,"description","")}),n.a.createElement(Vu,{responsibilities:ks.get(t,"hasOccupation.responsibilities",[])}),n.a.createElement(Au,{skills:ks.get(t,"hasOccupation.skills",[])}))},Ku=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return ks.get(t,"jsonld['@graph'][1].hasOccupation",[]).length>0&&t.work.enable?n.a.createElement("div",null,n.a.createElement(a,null,t.work.heading),n.a.createElement("div",{className:"grid gap-4"},ks.get(t,"jsonld['@graph'][1].hasOccupation",[]).filter((function(e){return!ks.get(e,"@id","").endsWith("disable")})).map((function(e){return n.a.createElement(Bu,{key:ks.get(e,"@id",Object(ic.a)()),item:e,language:t.language||"en"})})))):null})),Zu=function(e){var t=e.x,a=(e.index,e.subclassName);return t&&n.a.createElement("div",{className:a},n.a.createElement("span",null,t.streetAddress),n.a.createElement("span",null,"\xa0",t.addressLocality," ",t.addressRegion),n.a.createElement("span",null,"\xa0",t.addressCountry," ",t.postalCode))},Qu=Object(o.memo)((function(e){var t=e.data,a=e.mainclassName,o=void 0===a?"":a,r=e.hclassName,l=void 0===r?"capitalize font-semibold":r,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"flex flex-col text-xs";return t.jsonld["@graph"][1].address&&t.jsonld["@graph"][1].address.length>0&&t.address.enable&&n.a.createElement("div",{className:o},n.a.createElement("h6",{className:l},ks.get(t,"profile.address.heading","Address")),t.jsonld["@graph"][1].address.filter((function(e){return Date.parse(e.hoursAvailable.validThrough)-Date.parse(new Date)>0})).map((function(e,t){return n.a.createElement(Zu,{x:e,index:t,subclassName:i,key:t})})))||""})),Xu=Object(o.memo)((function(e){var t=e.data,a=e.className,o=void 0===a?"tracking-wide uppercase font-bold":a,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{fontSize:"2.75em"};return n.a.createElement("h1",{className:o,style:r},Array.isArray(t.jsonld["@graph"][1].givenName)?ks.get(t,"jsonld['@graph'][1].givenName[0]['@value']",""):t.jsonld["@graph"][1].givenName," ",Array.isArray(t.jsonld["@graph"][1].familyName)?ks.get(t,"jsonld['@graph'][1].familyName[0]['@value']",""):t.jsonld["@graph"][1].familyName)})),$u=Object(o.memo)((function(e){var t=e.data;return n.a.createElement("h6",{className:"text-lg tracking-wider uppercase"},ks.get(t,"jsonld['@graph'][1].givenName[1]","")?" ("+ks.get(t,"jsonld['@graph'][1].givenName",[]).map((function(e,a){if(a>0&&e["@value"]){var o=e["@value"],n=ks.get(t,"jsonld['@graph'][1].familyName",[]).findIndex((function(t){return t["@language"]===e["@language"]}));return n>=0&&t.jsonld["@graph"][1].familyName[n]&&t.jsonld["@graph"][1].familyName[n]["@value"]&&(o+=" "+t.jsonld["@graph"][1].familyName[n]["@value"]),o}return null})).filter((function(e){return null!=e})).join(", ")+")":"")})),ed={objective:Iu,work:Ku,education:Ju,projects:Hu,awards:xu,certifications:wu,skills:_u,hobbies:Fu,languages:zu,references:qu},td=function(){Object(Zs.a)().t;var e=Object(o.useContext)(_s).state,t=e.data,a=e.theme,r=ks.get(a,"layoutblocks.onyx",[]);return n.a.createElement(Ks.Provider,{value:{data:t,heading:Pu}},n.a.createElement("div",{id:"page",className:"p-8 rounded",style:{fontFamily:a.font.family,color:a.colors.primary,backgroundColor:a.colors.background}},n.a.createElement("div",{className:"grid grid-cols-4 items-center"},n.a.createElement("div",{className:"col-span-3 flex items-center"},""!==ks.get(t,'jsonld["@graph"][1].image.contentUrl',"")&&n.a.createElement("img",{className:"rounded object-cover mr-4",src:ks.get(t,'jsonld["@graph"][1].image.contentUrl',""),alt:"Resume Photograph",style:{width:"120px",height:"120px"}}),n.a.createElement("div",null,n.a.createElement(Xu,{data:t,className:"font-bold text-4xl",style:{color:a.colors.primary}}),n.a.createElement($u,{data:t}),n.a.createElement("h6",{className:"font-medium text-sm"},ks.get(t,'jsonld["@graph"][1].description',"")),n.a.createElement(Qu,{data:t,mainclassName:"flex flex-col mt-4 text-xs",hclassName:"font-bold text-xs uppercase tracking-wide mb-1",subclassName:""}))),n.a.createElement(Cu,null)),n.a.createElement("hr",{className:"my-5 opacity-25",style:{borderColor:a.colors.primary}}),n.a.createElement("div",{className:"grid gap-4"},r[0]&&r[0].map((function(e){var t=ed[e];return t&&n.a.createElement(t,{key:e})})),n.a.createElement("div",{className:"grid grid-cols-2 gap-4"},r[1]&&r[1].map((function(e){var t=ed[e];return t&&n.a.createElement(t,{key:e})}))),r[2]&&r[2].map((function(e){var t=ed[e];return t&&n.a.createElement(t,{key:e})})))))},ad=a(640),od=a.n(ad).a,nd=td,rd=Object(o.memo)((function(e){var t=e.children,a=Object(o.useContext)(_s).state,r=(a.data,a.theme);return n.a.createElement("h6",{className:"mb-2 border-b-2 pb-1 font-bold uppercase tracking-wide text-sm",style:{color:r.colors.primary,borderColor:r.colors.primary}},t)})),ld=Object(o.memo)((function(){var e=Object(o.useContext)(_s).state,t=e.data,a=e.theme;return ks.size(ks.get(t,'jsonld["@graph"][1].seeks',[]))>0&&n.a.createElement("div",null,n.a.createElement("hr",{className:"my-5 opacity-25",style:{borderColor:a.colors.background}}),ks.get(t,'jsonld["@graph"][1].seeks',[]).map((function(e,t){return n.a.createElement(yu.a,{key:"objetive_"+t,className:"text-sm",source:e.description})})))})),id={work:Ku,education:Ju,projects:Hu,awards:xu,certifications:wu,skills:_u,hobbies:Fu,languages:zu,references:qu},sd=function(){Object(Zs.a)().t;var e=Object(o.useContext)(_s).state,t=e.data,a=e.theme,r=ks.get(a,"layoutblocks.pikachu",[]);return n.a.createElement(Ks.Provider,{value:{data:t,heading:rd}},n.a.createElement("div",{id:"page",className:"p-8 rounded",style:{fontFamily:a.font.family,color:a.colors.primary,backgroundColor:a.colors.background}},n.a.createElement("div",{className:"grid grid-cols-12 gap-8"},""!==ks.get(t,'jsonld["@graph"][1].image.contentUrl',"")&&n.a.createElement("div",{className:"self-center col-span-4"},n.a.createElement("img",{className:"w-48 h-48 rounded-full mx-auto object-cover",src:ks.get(t,'jsonld["@graph"][1].image.contentUrl',""),alt:"Resume Photograph"})),n.a.createElement("div",{className:"".concat(""!==ks.get(t,'jsonld["@graph"][1].image.contentUrl',"")?"col-span-8":"col-span-12")},n.a.createElement("div",{className:"h-48 rounded flex flex-col justify-center",style:{backgroundColor:a.colors.primary,color:a.colors.background}},n.a.createElement("div",{className:"flex flex-col justify-center mx-8 my-6"},n.a.createElement(Xu,{data:t,className:"text-3xl font-bold leading-tight"}),n.a.createElement($u,{data:t}),n.a.createElement("div",{className:"text-sm font-medium tracking-wide"},ks.get(t,'jsonld["@graph"][1].description',"")),n.a.createElement(ld,{data:t})))),n.a.createElement("div",{className:"col-span-4"},n.a.createElement("div",{className:"grid gap-4"},n.a.createElement(Cu,null),r[0]&&r[0].map((function(e){var t=id[e];return t&&n.a.createElement(t,{key:e})})))),n.a.createElement("div",{className:"col-span-8"},n.a.createElement("div",{className:"grid gap-4"},r[1]&&r[1].map((function(e){var t=id[e];return t&&n.a.createElement(t,{key:e})})))))))},cd=a(641),ud=a.n(cd).a,dd=sd,md=Object(o.memo)((function(){var e=Object(o.useContext)(_s).state,t=e.data,a=e.theme,r=Object(ks.get)(Ou,"birthday");return ks.get(t,"jsonld['@graph'][1].birthDate","")?n.a.createElement("div",{className:"text-xs flex items-center"},n.a.createElement(r,{size:"10px",className:"mr-2",style:{color:a.colors.background}}),n.a.createElement("span",{className:"font-medium break-all"},zs({date:ks.get(t,"jsonld['@graph'][1].birthDate",""),language:t.language||"en",includeDay:!0}))):null})),pd=function(e){var t=e.value,a=e.icon,r=e.link,l=Object(o.useContext)(_s).state,i=(l.data,l.theme),s=Object(ks.get)(Ou,a&&a.toLowerCase(),ku.c);return t?n.a.createElement("div",{className:"flex items-center"},n.a.createElement(s,{size:"10px",className:"mr-2",style:{color:i.colors.background}}),r?n.a.createElement("a",{href:r,target:"_blank",rel:"noopener noreferrer"},n.a.createElement("span",{className:"font-medium break-all"},t)):n.a.createElement("span",{className:"font-medium break-all"},t)):null},bd=Object(o.memo)((function(){var e=Object(Zs.a)().t,t=Object(o.useContext)(_s).state,a=t.data;t.theme;return n.a.createElement("div",{className:"text-xs grid gap-2"},n.a.createElement(pd,{label:ks.get(a,"profile.phone.heading",e("Phone")),value:ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"telephone",""),icon:"phone",link:"tel:".concat(ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"telephone",""))}),n.a.createElement(pd,{label:ks.get(a,"profile.website.heading",e("Website")),value:ks.get(a,'jsonld["@graph"][1].sameAs[0]',""),icon:"website",link:ks.get(a,'jsonld["@graph"][1].sameAs[0]',"")}),n.a.createElement(pd,{label:ks.get(a,"profile.email.heading",e("Email")),value:ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"email",""),icon:"email",link:"mailto:".concat(ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"email",""))}),n.a.createElement(md,null),Ms(a.social)&&a.social.items.map((function(e){return n.a.createElement(pd,{key:e.id,value:e.username,icon:e.network,link:e.url})})))})),hd=Object(o.memo)((function(e){var t=e.children;return n.a.createElement("h6",{className:"font-bold text-xs uppercase tracking-wide mb-1"},t)})),gd=function(e){return n.a.createElement("div",{key:ks.get(e,"@id",Object(ic.a)()),className:"flex flex-col"},n.a.createElement("h6",{className:"font-semibold text-sm"},ks.get(e,"interactionType.participant.givenName","")," ",ks.get(e,"interactionType.participant.familyName","")),n.a.createElement("span",{className:"text-xs"},ks.get(e,"interactionType.participant.jobTitle","")),n.a.createElement("span",{className:"text-xs"},ks.get(e,"interactionType.participant.telephone","")),n.a.createElement("span",{className:"text-xs"},ks.get(e,"interactionType.participant.email","")),""!==ks.get(e,"result[0].reviewRating.ratingExplanation","")&&n.a.createElement(yu.a,{className:"markdown mt-2 text-sm",source:ks.get(e,"result[0].reviewRating.ratingExplanation","")}))},fd=Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return Ms(t.references)?n.a.createElement("div",null,n.a.createElement(a,null,t.references.heading),n.a.createElement("div",{className:"grid gap-4"},ks.get(t.jsonld["@graph"][1],"interactionStatistic",[]).filter((function(e){return"Reference"===ks.get(e,"disambiguatingDescription","")})).map(gd))):null})),yd=Object(o.memo)((function(e){var t=e.data,a=e.className,o=void 0===a?"text-2xl font-bold leading-tight":a,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.a.createElement(n.a.Fragment,null,n.a.createElement("h1",{className:o,style:r},Array.isArray(t.jsonld["@graph"][1].givenName)?ks.get(t,"jsonld['@graph'][1].givenName[0]['@value']",""):t.jsonld["@graph"][1].givenName),n.a.createElement("h1",{className:o,style:r},Array.isArray(t.jsonld["@graph"][1].familyName)?ks.get(t,"jsonld['@graph'][1].familyName[0]['@value']",""):t.jsonld["@graph"][1].familyName))})),vd={objective:Iu,work:Ku,education:Ju,projects:Hu,awards:xu,certifications:wu,skills:_u,hobbies:Fu,languages:zu,references:fd},xd=function(){Object(Zs.a)().t;var e=Object(o.useContext)(_s).state,t=e.data,a=e.theme,r=ks.get(a,"layoutblocks.gengar",[]),l=As(a.colors.primary)||{},i=l.r,s=l.g,c=l.b,u=function(){return""!==ks.get(t,'jsonld["@graph"][1].image.contentUrl',"")&&n.a.createElement("img",{className:"w-24 h-24 rounded-full mr-4 object-cover border-4",style:{borderColor:a.colors.background},src:ks.get(t,'jsonld["@graph"][1].image.contentUrl',""),alt:"Resume Photograph"})},d=function(){return n.a.createElement("div",null,n.a.createElement(yd,{data:t,className:"text-2xl font-bold leading-tight"}),n.a.createElement($u,{data:t}),n.a.createElement("div",{className:"text-xs font-medium mt-2"},ks.get(t,'jsonld["@graph"][1].description',"")))};return n.a.createElement(Ks.Provider,{value:{data:t,heading:hd}},n.a.createElement("div",{id:"page",className:"rounded",style:{fontFamily:a.font.family,color:a.colors.primary,backgroundColor:a.colors.background}},n.a.createElement("div",{className:"grid grid-cols-12"},n.a.createElement("div",{className:"col-span-4 px-6 py-8",style:{backgroundColor:a.colors.primary,color:a.colors.background}},n.a.createElement("div",{className:"flex items-center"},n.a.createElement(u,null),n.a.createElement(d,null)),n.a.createElement(Qu,{data:t,mainclassName:"flex flex-col mt-4 text-xs",hclassName:"font-bold text-xs uppercase tracking-wide mb-1",subclassName:""}),n.a.createElement("hr",{className:"w-1/4 my-5 opacity-25",style:{borderColor:a.colors.background}}),n.a.createElement("h6",{className:"font-bold text-xs uppercase tracking-wide mb-2"},t.contacts.heading||"Contact"),n.a.createElement(bd,null)),n.a.createElement("div",{className:"col-span-8 px-6 py-8",style:{backgroundColor:"rgba(".concat(i,", ").concat(s,", ").concat(c,", 0.1)")}},n.a.createElement("div",{className:"grid gap-6 items-center"},r[0]&&r[0].map((function(e){var t=vd[e];return t&&n.a.createElement(t,{key:e})})))),n.a.createElement("div",{className:"col-span-4 px-6 py-8",style:{backgroundColor:"rgba(".concat(i,", ").concat(s,", ").concat(c,", 0.1)")}},n.a.createElement("div",{className:"grid gap-6"},r[1]&&r[1].map((function(e){var t=vd[e];return t&&n.a.createElement(t,{key:e})})))),n.a.createElement("div",{className:"col-span-8 px-6 py-8"},n.a.createElement("div",{className:"grid gap-6"},r[2]&&r[2].map((function(e){var t=vd[e];return t&&n.a.createElement(t,{key:e})})))))))},Nd=a(642),wd=a.n(Nd).a,kd=xd,Ed=Object(o.memo)((function(){Object(Zs.a)().t;var e=Object(o.useContext)(_s).state,t=e.data;e.theme;return ks.get(t,"jsonld['@graph'][1].birthDate","")?n.a.createElement("div",{className:"text-xs"},n.a.createElement("h6",{className:"capitalize font-semibold"},t.profile.birthDate.heading||"Birth Date"),n.a.createElement("div",null,n.a.createElement("span",null,zs({date:ks.get(t,"jsonld['@graph'][1].birthDate",""),language:t.language||"en",includeDay:!0})))):null})),Od=function(e){var t=e.value,a=e.label,o=e.link;return t?n.a.createElement("div",{className:"flex flex-col"},n.a.createElement("h6",{className:"capitalize font-semibold"},a),o?n.a.createElement("a",{href:o,target:"_blank",rel:"noopener noreferrer"},n.a.createElement("span",{className:"font-medium break-all"},t)):n.a.createElement("span",{className:"font-medium break-all"},t)):null},Sd=Object(o.memo)((function(){var e=Object(Zs.a)().t,t=Object(o.useContext)(_s).state,a=t.data;t.theme;return n.a.createElement("div",{className:"text-xs grid gap-2"},n.a.createElement(Qu,{data:a}),n.a.createElement(Od,{label:ks.get(a,"profile.phone.heading",e("Phone")),value:ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"telephone",""),link:"tel:".concat(ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"telephone",""))}),n.a.createElement(Od,{label:ks.get(a,"profile.website.heading",e("Website")),value:ks.get(a,'jsonld["@graph"][1].sameAs[0]',""),link:ks.get(a,'jsonld["@graph"][1].sameAs[0]',"")}),n.a.createElement(Od,{label:ks.get(a,"profile.email.heading",e("Email")),value:ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"email",""),link:"mailto:".concat(ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"email",""))}),n.a.createElement(Ed,null),Ms(a.social)&&a.social.items.map((function(e){return n.a.createElement(Od,{key:e.id,value:e.username,label:e.network,link:e.url})})))})),jd=Object(o.memo)((function(e){var t=e.children,a=Object(o.useContext)(_s).state,r=(a.data,a.theme),l=As(r.colors.primary)||{},i=l.r,s=l.g,c=l.b;return n.a.createElement("h6",{className:"py-1 px-4 rounded-r leading-loose font-bold text-xs uppercase tracking-wide mb-3",style:{marginLeft:"-15px",color:r.colors.background,backgroundColor:"rgba(".concat(i-40,", ").concat(s-40,", ").concat(c-40,", 0.8)")}},t)})),Cd={objective:Iu,work:Ku,education:Ju,projects:Hu,awards:xu,certifications:wu,skills:_u,hobbies:Fu,languages:zu,references:qu},Dd=function(){Object(Zs.a)().t;var e=Object(o.useContext)(_s).state,t=e.data,a=e.theme,r=ks.get(a,"layoutblocks.castform",[]),l=function(){return""!==ks.get(t,'jsonld["@graph"][1].image.contentUrl',"")&&n.a.createElement("img",{className:"w-32 h-32 rounded-full",style:{borderWidth:6,borderColor:a.colors.background},src:ks.get(t,'jsonld["@graph"][1].image.contentUrl',""),alt:"Resume Photograph"})},i=function(){return n.a.createElement("div",null,n.a.createElement(Xu,{data:t,className:"text-2xl font-bold"}),n.a.createElement($u,{data:t}),n.a.createElement("h5",null,ks.get(t,'jsonld["@graph"][1].description',"")))};return n.a.createElement(Ks.Provider,{value:{data:t,heading:jd}},n.a.createElement("div",{id:"page",className:"rounded",style:{fontFamily:a.font.family,color:a.colors.primary,backgroundColor:a.colors.background}},n.a.createElement("div",{className:"grid grid-cols-12"},n.a.createElement("div",{className:"col-span-4 py-8 pr-8 pl-5",style:{color:a.colors.background,backgroundColor:a.colors.primary}},n.a.createElement("div",{className:"grid gap-4"},n.a.createElement(l,null),n.a.createElement(i,null),n.a.createElement("div",null,n.a.createElement(jd,null,t.profile.heading),n.a.createElement(Sd,null)),r[0]&&r[0].map((function(e){var t=Cd[e];return t&&n.a.createElement(t,{key:e})})))),n.a.createElement("div",{className:"col-span-8 py-8 pr-8 pl-5"},n.a.createElement("div",{className:"grid gap-4"},r[1]&&r[1].map((function(e){var t=Cd[e];return t&&n.a.createElement(t,{key:e})})))))))},Ad=a(643),Td=a.n(Ad).a,Jd=Dd,Pd=function(e){var t=e.value,a=e.label,o=e.link;return t?n.a.createElement("div",{className:"flex flex-col"},n.a.createElement("h6",{className:"capitalize font-semibold"},a),o?n.a.createElement("a",{href:o,target:"_blank",rel:"noopener noreferrer"},n.a.createElement("span",{className:"font-medium break-all"},t)):n.a.createElement("span",{className:"font-medium break-all"},t)):null},Rd=Object(o.memo)((function(){var e=Object(Zs.a)().t,t=Object(o.useContext)(_s).state,a=t.data,r=t.theme;return n.a.createElement("div",{className:"my-4 relative w-full border-2 grid gap-2 text-center text-xs py-5",style:{borderColor:r.colors.primary}},n.a.createElement("div",{className:"absolute text-center",style:{top:"-11px",left:"50%",marginLeft:"-10px",color:r.colors.primary}},n.a.createElement(Eu.b,{size:"20px"})),n.a.createElement(Qu,{data:a}),n.a.createElement(Pd,{label:ks.get(a,"profile.phone.heading",e("Phone")),value:ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"telephone",""),link:"tel:".concat(ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"telephone",""))}),n.a.createElement(Pd,{label:ks.get(a,"profile.website.heading",e("Website")),value:ks.get(a,'jsonld["@graph"][1].sameAs[0]',""),link:ks.get(a,'jsonld["@graph"][1].sameAs[0]',"")}),n.a.createElement(Pd,{label:ks.get(a,"profile.email.heading",e("Email")),value:ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"email",""),link:"mailto:".concat(ks.get(ks.find(a.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"email",""))}),n.a.createElement(Ed,null),Ms(a.social)&&a.social.items.map((function(e){return n.a.createElement(Pd,{key:e.id,value:e.username,label:e.network,link:e.url})})))})),Fd={objective:Iu,work:Ku,education:Ju,projects:Hu,awards:xu,certifications:wu,skills:_u,hobbies:Fu,languages:zu,references:qu},Ld=function(){Object(Zs.a)().t;var e=Object(o.useContext)(_s).state,t=e.data,a=e.theme,r=ks.get(a,"layoutblocks.glalie",[]),l=As(a.colors.primary)||{},i=l.r,s=l.g,c=l.b,u=function(){return n.a.createElement("div",{className:"grid gap-2 text-center"},""!==ks.get(t,'jsonld["@graph"][1].image.contentUrl',"")&&n.a.createElement("img",{className:"w-40 h-40 rounded-full mx-auto",src:ks.get(t,'jsonld["@graph"][1].image.contentUrl',""),alt:"Resume Photograph"}),n.a.createElement("div",{className:"text-4xl font-bold leading-none"},n.a.createElement(yd,{data:t,className:""}),n.a.createElement($u,{data:t})),n.a.createElement("div",{className:"tracking-wide text-xs uppercase font-medium"},ks.get(t,'jsonld["@graph"][1].description',"")))};return n.a.createElement(Ks.Provider,{value:{data:t,heading:rd}},n.a.createElement("div",{id:"page",className:"rounded",style:{fontFamily:a.font.family,color:a.colors.primary,backgroundColor:a.colors.background}},n.a.createElement("div",{className:"grid grid-cols-12"},n.a.createElement("div",{className:"col-span-4",style:{backgroundColor:"rgba(".concat(i,", ").concat(s,", ").concat(c,", 0.1)")}},n.a.createElement("div",{className:"grid gap-6 text-center p-8"},n.a.createElement(u,null),n.a.createElement(Rd,null),r[0]&&r[0].map((function(e){var t=Fd[e];return t&&n.a.createElement(t,{key:e})})))),n.a.createElement("div",{className:"col-span-8"},n.a.createElement("div",{className:"grid gap-4 p-8"},r[1]&&r[1].map((function(e){var t=Fd[e];return t&&n.a.createElement(t,{key:e})})))))))},zd=a(644),Id=a.n(zd).a,Md=Ld,Hd=Object(o.memo)((function(e){var t=e.children;return n.a.createElement("h6",{className:"my-2 text-md uppercase font-semibold tracking-wider pb-1 border-b-2 border-gray-800"},t)})),Gd=function(e){return n.a.createElement("div",{key:ks.get(e,"@id",Object(ic.a)()),className:"flex flex-col"},n.a.createElement("h6",{className:"font-semibold text-sm"},ks.get(e,"name","")),n.a.createElement("span",{className:"text-xs"},e.fluency))},qd={objective:Iu,work:Ku,education:Ju,projects:Hu,awards:xu,certifications:wu,skills:_u,hobbies:Fu,languages:Object(o.memo)((function(){var e=Object(o.useContext)(Ks),t=e.data,a=e.heading;return t.languages&&t.languages.enable&&ks.get(t,'jsonld["@graph"][1].knowsLanguage',[]).length>0?n.a.createElement("div",null,n.a.createElement(a,null,t.languages.heading),n.a.createElement("div",{className:"grid gap-2"},ks.get(t,'jsonld["@graph"][1].knowsLanguage',[]).filter((function(e){return""!==ks.get(e,"name","")})).map(Gd))):null})),references:qu},Yd=function(){Object(Zs.a)().t;var e=Object(o.useContext)(_s).state,t=e.data,a=e.theme,r=ks.get(a,"layoutblocks.celebi",[]),l=As(a.colors.accent)||{},i=l.r,s=l.g,c=l.b,u={header:{position:"absolute",left:0,right:0,display:"flex",flexDirection:"column",justifyContent:"center",color:"white",backgroundColor:a.colors.primary,height:"160px",paddingLeft:"275px"},leftSection:{backgroundColor:"rgba(".concat(i,", ").concat(s,", ").concat(c,", 0.1)")},rightSection:{marginTop:"160px"}},d=function(){return""!==ks.get(t,'jsonld["@graph"][1].image.contentUrl',"")&&n.a.createElement("div",{className:"relative z-40"},n.a.createElement("img",{className:"w-full object-cover object-center",src:ks.get(t,'jsonld["@graph"][1].image.contentUrl',""),alt:"Person Photograph",style:{height:"160px"}}))||n.a.createElement("div",{className:"relative z-40"},n.a.createElement("div",{style:{height:"160px"}}))},m=function(){return n.a.createElement("div",{style:u.header},n.a.createElement(Xu,{data:t}),n.a.createElement($u,{data:t}),n.a.createElement("h6",{className:"text-lg tracking-wider uppercase"},ks.get(t,'jsonld["@graph"][1].description',"")))};return n.a.createElement(Ks.Provider,{value:{data:t,heading:Hd}},n.a.createElement("div",{id:"page",className:"relative rounded",style:{fontFamily:a.font.family,color:a.colors.text,backgroundColor:a.colors.background}},n.a.createElement("div",{className:"grid grid-cols-12 gap-8"},n.a.createElement("div",{className:"col-span-4 ml-8",style:u.leftSection},n.a.createElement(d,null),n.a.createElement("div",{className:"text-center grid gap-4 mt-4 mb-8 mx-6"},n.a.createElement("div",null,n.a.createElement(Hd,null,t.profile.heading),n.a.createElement("div",{className:"relative w-full grid gap-4 text-xs"},n.a.createElement(Sd,null))),ks.get(r,"[0]",[])&&ks.get(r,"[0]",[]).map((function(e){var t=qd[e];return t&&n.a.createElement(t,{key:e})})))),n.a.createElement("div",{className:"col-span-8"},n.a.createElement(m,null),n.a.createElement("div",{className:"relative",style:u.rightSection},n.a.createElement("div",{className:"grid gap-4 mt-4 mb-8 mr-8"},ks.get(r,"[1]",[])&&ks.get(r,"[1]",[]).map((function(e){var t=qd[e];return t&&n.a.createElement(t,{key:e})}))))))))},Ud=a(645),_d=[{key:"onyx",name:"Onyx",component:nd,preview:od},{key:"pikachu",name:"Pikachu",component:dd,preview:ud},{key:"gengar",name:"Gengar",component:kd,preview:wd},{key:"castform",name:"Castform",component:Jd,preview:Td},{key:"glalie",name:"Glalie",component:Md,preview:Id},{key:"celebi",name:"Celebi",component:Yd,preview:a.n(Ud).a}],Wd=function(e){var t=e.theme,a=e.onChange;return n.a.createElement("div",{className:"grid grid-cols-2 gap-6"},_d.map((function(e){return n.a.createElement("div",{key:e.key,className:"text-center",onClick:function(){e.disable?alert("This template is under develoment"):a("theme.layout",e.key)}},n.a.createElement("img",{className:"rounded cursor-pointer object-cover border shadow hover:shadow-md ".concat(t.layout.toLowerCase()===e.key?"border-gray-600 hover:border-gray-600":"border-transparent "," hover:border-gray-500 cursor-pointer"),src:e.preview,alt:e.name}),n.a.createElement("p",{className:"mt-1 text-sm font-medium"},e.name))})))},Vd=["#f44336","#E91E63","#9C27B0","#673AB7","#3F51B5","#2196F3","#03A9F4","#00BCD4","#009688","#4CAF50","#8BC34A","#CDDC39","#FFEB3B","#FFC107","#FF9800","#FF5722","#795548","#9E9E9E","#607D8B","#FAFAFA","#212121","#263238"],Bd=function(e){var t=e.theme,a=e.onChange,o=Object(Zs.a)("rightSidebar").t,r=function(e){!function(e){var t=document.createElement("textarea");t.style.position="fixed",t.style.top=0,t.style.left=0,t.style.width="2em",t.style.height="2em",t.style.padding=0,t.style.border="none",t.style.outline="none",t.style.boxShadow="none",t.style.background="transparent",t.value=e,document.body.appendChild(t),t.focus(),t.select();var a=document.execCommand("copy");document.body.removeChild(t)}(e),Object(i.a)(o("colors.clipboardCopyAction",{color:e}),{bodyClassName:"text-center text-gray-800 py-2"}),a("theme.colors.accent",e)};return n.a.createElement("div",null,n.a.createElement("div",{className:"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-4"},o("colors.colorOptions")),n.a.createElement("div",{className:"mb-6 grid grid-cols-8 col-gap-2 row-gap-3"},Vd.map((function(e){return n.a.createElement("div",{key:e,className:"cursor-pointer rounded-full border border-gray-200 h-6 w-6 hover:opacity-75",style:{backgroundColor:e},onClick:function(){return r(e)}})}))),n.a.createElement("hr",{className:"my-6"}),n.a.createElement("div",{className:"my-6 grid grid-cols-6 items-end"},n.a.createElement("div",{className:"rounded-full w-8 h-8 mb-2 border-2",style:{backgroundColor:t.colors.primary}}),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{label:o("colors.primaryColor"),placeholder:"#FFFFFF",value:t.colors.primary,onChange:function(e){return a("theme.colors.primary",e)}}))),n.a.createElement("div",{className:"my-6 grid grid-cols-6 items-end"},n.a.createElement("div",{className:"rounded-full w-8 h-8 mb-2 border-2",style:{backgroundColor:t.colors.accent}}),n.a.createElement("div",{className:"col-span-5"},n.a.createElement(rc,{label:o("colors.accentColor"),placeholder:"#FFFFFF",value:t.colors.accent,onChange:function(e){return a("theme.colors.accent",e)}}))))},Kd=["Lato","Montserrat","Nunito","Open Sans","Raleway","Rubik","Source Sans Pro","Titillium Web","Ubuntu"],Zd=function(e){var t=e.theme,a=e.onChange,o=Object(Zs.a)("rightSidebar").t;return n.a.createElement("div",{className:"grid grid-cols-1 gap-6"},Kd.map((function(e){return n.a.createElement("div",{key:e,style:{fontFamily:e},onClick:function(){return a("theme.font.family",e)},className:"w-full rounded border py-4 shadow text-xl text-center ".concat(t.font.family===e?"border-gray-500":"border-transparent"," hover:border-gray-400 cursor-pointer")},e)})),n.a.createElement("div",null,n.a.createElement(rc,{className:"mb-3",label:o("fonts.fontFamily.label"),placeholder:"Avenir Next",value:t.font.family,onChange:function(e){return a("theme.font.family",e)}}),n.a.createElement("p",{className:"text-gray-800 text-xs"},o("fonts.fontFamily.helpText"))))},Qd=a(646),Xd=a(647),$d=function(e){var t=e.data,a=e.theme,r=e.dispatch,l=Object(o.useContext)(Ks).setPrintDialogOpen,i=Object(Zs.a)("rightSidebar").t,s=Object(o.useRef)(null);return n.a.createElement("div",null,n.a.createElement("div",{className:"shadow text-center text-sm p-5"},i("actions.disclaimer")),n.a.createElement("hr",{className:"my-6"}),n.a.createElement("div",{className:"shadow text-center p-5"},n.a.createElement("h6",{className:"font-bold text-sm mb-2"},i("actions.importExport.heading")),n.a.createElement("p",{className:"text-sm"},i("actions.importExport.body")),n.a.createElement("input",{ref:s,type:"file",className:"hidden",onChange:function(e){return function(e,t){var a=new FileReader;a.addEventListener("load",(function(){var e=JSON.parse(a.result);t({type:"import_data",payload:e}),t({type:"save_data"})})),a.readAsText(e.target.files[0])}(e,r)}}),n.a.createElement("a",{id:"downloadAnchor",className:"hidden"}),n.a.createElement("div",{className:"mt-4 grid grid-cols-2 col-gap-6"},n.a.createElement("button",{type:"button",onClick:function(){return s.current.click()},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"publish"),n.a.createElement("span",{className:"text-sm"},i("actions.importExport.buttons.import")))),n.a.createElement("button",{type:"button",onClick:function(){var e={data:t,theme:a},o=ks.cloneDeep(t.jsonld),n='<script type="application/ld+json">'+JSON.stringify(o)+"<\/script>";ks.set(o["@graph"][1],"@context","http://schema.org/");var r=n+('<script type="application/ld+json">'+JSON.stringify(o["@graph"][1])+"<\/script>"),l=new Qd;l.file("script.js",r),l.file("resume.json",JSON.stringify(e)),l.generateAsync({type:"blob"}).then((function(e){Object(Xd.saveAs)(e,"jsonldresume.zip")}))},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"get_app"),n.a.createElement("span",{className:"text-sm"},i("actions.importExport.buttons.export")))))),n.a.createElement("hr",{className:"my-6"}),n.a.createElement("div",{className:"shadow text-center p-5"},n.a.createElement("h6",{className:"font-bold text-sm mb-2"},i("actions.downloadResume.heading")),n.a.createElement("div",{className:"text-sm"},i("actions.downloadResume.body")),n.a.createElement("button",{type:"button",onClick:function(){return l(!0)},className:"mt-4 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"save"),n.a.createElement("span",{className:"text-sm"},i("actions.downloadResume.buttons.saveAsPdf"))))),n.a.createElement("hr",{className:"my-6"}),n.a.createElement("div",{className:"shadow text-center p-5"},n.a.createElement("h6",{className:"font-bold text-sm mb-2"},i("actions.loadDemoData.heading")),n.a.createElement("div",{className:"text-sm"},i("actions.loadDemoData.body")),n.a.createElement("button",{type:"button",onClick:function(){r({type:"load_demo_data"}),r({type:"save_data"})},className:"mt-4 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"flight_takeoff"),n.a.createElement("span",{className:"text-sm"},i("actions.loadDemoData.buttons.loadData"))))),n.a.createElement("hr",{className:"my-6"}),n.a.createElement("div",{className:"shadow text-center p-5"},n.a.createElement("h6",{className:"font-bold text-sm mb-2"},i("actions.reset.heading")),n.a.createElement("div",{className:"text-sm"},i("actions.reset.body")),n.a.createElement("button",{type:"button",onClick:function(){r({type:"reset"}),r({type:"save_data"})},className:"mt-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"refresh"),n.a.createElement("span",{className:"text-sm"},i("actions.reset.buttons.reset"))))))},em=function(){var e=Object(Zs.a)("rightSidebar").t;return n.a.createElement("div",null,n.a.createElement("div",{className:"shadow text-center p-5"},n.a.createElement("h6",{className:"font-bold text-sm mb-2"},e("about.documentation.heading")),n.a.createElement("div",{className:"text-sm"},e("about.documentation.body")),n.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://docs.jsonldresume.org/",className:"flex justify-center items-center mt-4 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"description"),n.a.createElement("span",{className:"text-sm"},e("about.documentation.buttons.documentation"))))),n.a.createElement("hr",{className:"my-5"}),n.a.createElement("div",{className:"shadow text-center p-5"},n.a.createElement("h6",{className:"font-bold text-sm mb-2"},e("about.bugOrFeatureRequest.heading")),n.a.createElement("div",{className:"text-sm"},e("about.bugOrFeatureRequest.body")),n.a.createElement("div",{className:"grid grid-cols-1"},n.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://github.com/AmruthPillai/Reactive-Resume/issues/new",className:"mt-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"bug_report"),n.a.createElement("span",{className:"text-sm"},e("about.bugOrFeatureRequest.buttons.raiseIssue")))),n.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"mailto:<EMAIL>?subject=Feature Request/Reporting a Bug in Reactive Resume: ",className:"mt-4 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"email"),n.a.createElement("span",{className:"text-sm"},e("about.bugOrFeatureRequest.buttons.sendEmail")))))),n.a.createElement("hr",{className:"my-5"}),n.a.createElement("div",{className:"shadow text-center p-5"},n.a.createElement("h6",{className:"font-bold text-sm mb-2"},e("about.sourceCode.heading")),n.a.createElement("div",{className:"text-sm"},e("about.sourceCode.body")),n.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://github.com/AmruthPillai/Reactive-Resume",className:"flex justify-center items-center mt-4 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"code"),n.a.createElement("span",{className:"text-sm"},e("about.sourceCode.buttons.githubRepo"))))),n.a.createElement("hr",{className:"my-5"}),n.a.createElement("div",{className:"shadow text-center p-5"},n.a.createElement("h6",{className:"font-bold text-sm mb-2"},e("about.license.heading")),n.a.createElement("div",{className:"text-sm"},e("about.license.body")),n.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://github.com/AmruthPillai/Reactive-Resume/blob/master/LICENSE",className:"flex justify-center items-center mt-4 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"gavel"),n.a.createElement("span",{className:"text-sm"},e("about.license.buttons.mitLicense"))))),n.a.createElement("div",{className:"mt-5"},n.a.createElement("p",{className:"text-xs font-gray-600 text-center"},n.a.createElement(wc.a,{t:e,i18nKey:"about.footer.credit"},"Made with Love by",n.a.createElement("a",{className:"font-bold hover:underline",href:"https://www.amruthpillai.com/",rel:"noopener noreferrer",target:"_blank"},"Amruth Pillai"))),n.a.createElement("p",{className:"text-xs font-gray-600 text-center"},e("about.footer.thanks"))))},tm=function(e){var t=e.settings,a=e.onChange,o=Object(Zs.a)("rightSidebar").t;return n.a.createElement("div",null,n.a.createElement(Xs,{label:o("settings.language.label"),value:t.language,onChange:function(e){return a("settings.language",e)},options:ds,optionItem:function(e){return n.a.createElement("option",{key:e.code,value:e.code},e.name)}}),n.a.createElement("p",{className:"text-gray-800 text-xs"},n.a.createElement(wc.a,{t:o,i18nKey:"settings.language.helpText"},"If you would like to help translate the app into your own language, please refer the",n.a.createElement("a",{className:"text-blue-600 hover:underline",target:"_blank",rel:"noopener noreferrer",href:"https://docs.rxresu.me/translation/"},"Translation Documentation"),".")))},am=function(){var e=Object(Zs.a)("rightSidebar").t,t=Object(o.useContext)(_s),a=t.state,r=t.dispatch,l=a.data,i=a.theme,s=a.settings,c=[{key:"templates",name:e("templates.title")},{key:"colors",name:e("colors.title")},{key:"fonts",name:e("fonts.title")},{key:"actions",name:e("actions.title")},{key:"settings",name:e("settings.title")},{key:"about",name:e("about.title")}],u=Object(o.useState)(c[0].key),d=Object(bs.a)(u,2),m=d[0],p=d[1],b=function(e,t){r({type:"on_input",payload:{key:e,value:t}}),r({type:"save_data"})};return n.a.createElement("div",{id:"rightSidebar",className:"animated slideInRight z-10 py-6 h-screen bg-white col-span-1 shadow-2xl overflow-y-scroll"},n.a.createElement($s,{tabs:c,currentTab:m,setCurrentTab:p}),n.a.createElement("div",{className:"px-6"},function(){switch(m){case c[0].key:return n.a.createElement(Wd,{theme:i,onChange:b});case c[1].key:return n.a.createElement(Bd,{theme:i,onChange:b});case c[2].key:return n.a.createElement(Zd,{theme:i,onChange:b});case c[3].key:return n.a.createElement($d,{data:l,theme:i,dispatch:r});case c[4].key:return n.a.createElement(tm,{settings:s,onChange:b});case c[5].key:return n.a.createElement(em,null);default:return null}}()))},om=function(){var e=Object(o.useContext)(Ks),t=e.panZoomRef,a=e.setPrintDialogOpen;return n.a.createElement("div",{id:"pageController",className:"absolute z-20 opacity-75 hover:opacity-100 transition-all duration-150"},n.a.createElement("div",{className:"text-2xl px-8 border border-gray-200 rounded-full bg-white flex justify-center items-center leading-none select-none"},n.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){return t.current.zoomIn(2)}},n.a.createElement("i",{className:"material-icons"},"zoom_in")),n.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){return t.current.zoomOut(2)}},n.a.createElement("i",{className:"material-icons"},"zoom_out")),n.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){t.current.autoCenter(1),t.current.reset(1)}},n.a.createElement("i",{className:"material-icons"},"center_focus_strong")),n.a.createElement("div",{className:"text-gray-400 p-3"},"|"),n.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){return window.print()}},n.a.createElement("i",{className:"material-icons"},"print")),n.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){return a(!0)}},n.a.createElement("i",{className:"material-icons"},"save")),n.a.createElement("div",{className:"text-gray-400 p-3"},"|"),n.a.createElement("a",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",href:"https://doc.jsonldresume.org/",target:"_blank",rel:"noopener noreferrer"},n.a.createElement("i",{className:"material-icons"},"help_outline"))))},nm=a(63),rm=a.n(nm),lm=a(648),im=function(){var e=Object(Zs.a)().t,t=Object(o.useContext)(Ks),a=t.pageRef,r=t.panZoomRef,l=t.isPrintDialogOpen,i=t.setPrintDialogOpen,s=[{key:"unconstrained",value:"".concat(e("printDialog.printType.types.unconstrained"))},{key:"fitInA4",value:"".concat(e("printDialog.printType.types.fitInA4"))},{key:"multiPageA4",value:"".concat(e("printDialog.printType.types.multiPageA4"))}],c=Object(o.useState)(80),u=Object(bs.a)(c,2),d=u[0],m=u[1],p=Object(o.useState)(s[0].key),b=Object(bs.a)(p,2),h=b[0],g=b[1];return n.a.createElement("div",{className:"absolute inset-0 transition-all duration-200 ease-in-out ".concat(l?"opacity-100 z-20":"opacity-0 z-0"),style:{backgroundColor:"rgba(0, 0, 0, 0.25)"},onClick:function(){i(!1)}},n.a.createElement("div",{className:"centered py-8 px-12 bg-white shadow-xl rounded w-full md:w-1/3",onClick:function(e){e.stopPropagation(),e.preventDefault()}},n.a.createElement("h5",{className:"mb-6 text-lg font-bold"},e("printDialog.heading")),n.a.createElement("h6",{className:"mb-1 text-sm font-medium"},e("printDialog.quality.label")),n.a.createElement("div",{className:"flex items-center"},n.a.createElement("input",{type:"range",className:"w-full h-4 my-2 rounded-full overflow-hidden appearance-none focus:outline-none bg-gray-400",value:d,onChange:function(e){return m(e.target.value)},min:"40",max:"100",step:"5"}),n.a.createElement("h6",{className:"font-medium pl-5"},d,"%")),n.a.createElement("h6",{className:"mt-4 mb-2 text-sm font-medium"},e("printDialog.printType.label")),n.a.createElement(Xs,{value:h,options:s,onChange:g,optionItem:function(e){return n.a.createElement("option",{key:e.key,value:e.key},e.value)}}),n.a.createElement("p",{className:"my-3 text-xs text-gray-600"},e("printDialog.helpText.0")),n.a.createElement("p",{className:"my-3 text-xs text-gray-600"},e("printDialog.helpText.1")),n.a.createElement("div",{className:"flex justify-between"},n.a.createElement("button",{type:"button",onClick:function(){i(!1)},className:"mt-6 border border-red-600 text-red-600 hover:bg-red-600 hover:text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"close"),n.a.createElement("span",{className:"text-sm"},e("printDialog.buttons.cancel")))),n.a.createElement("button",{type:"button",onClick:Object(lm.a)(rm.a.mark((function e(){return rm.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,"multiPageA4"===h?Ls(a,r,d):Rs(a,r,d,h);case 2:i(!1);case 3:case"end":return e.stop()}}),e)}))),className:"mt-6 border border-gray-700 text-gray-700 hover:bg-gray-700 hover:text-white text-sm font-medium py-2 px-5 rounded"},n.a.createElement("div",{className:"flex justify-center items-center"},n.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"save"),n.a.createElement("span",{className:"text-sm"},e("printDialog.buttons.saveAsPdf")))))))},sm=a(649),cm=a.n(sm),um=function(){var e=Object(Zs.a)().t,t=Object(o.useState)(!1),a=Object(bs.a)(t,2),r=a[0],l=a[1];return Object(o.useEffect)((function(){setTimeout((function(){return l(!0)}),500),setTimeout((function(){return l(!1)}),3e3)}),[]),n.a.createElement("div",{className:"centered absolute inset-0 w-1/4 mt-24 transition-all duration-1000 ease-in-out ".concat(r?"opacity-100 z-20":"opacity-0 z-0")},n.a.createElement("div",{className:"px-12 rounded-lg shadow-2xl bg-white"},n.a.createElement("video",{src:cm.a,autoPlay:!0,muted:!0,loop:!0}),n.a.createElement("p",{className:"px-6 pb-6 text-sm text-gray-800 font-medium text-center"},e("panZoomAnimation.helpText"))))},dm=function(){var e=Object(o.useRef)(null),t=Object(o.useRef)(null),a=Object(Zs.a)().i18n,r=Object(o.useContext)(_s),l=r.state,i=r.dispatch,s=l.theme,c=l.settings,u=Object(o.useContext)(Ks),d=u.setPageRef,m=u.setPanZoomRef;return Object(o.useEffect)((function(){d(e),m(t),a.changeLanguage(c.language);var o=JSON.parse(localStorage.getItem("state"));i({type:"import_data",payload:o})}),[i,d,m,a,c.language]),n.a.createElement(o.Suspense,{fallback:"Loading..."},n.a.createElement("div",{className:"h-screen grid grid-cols-5 items-center"},n.a.createElement(gu,null),n.a.createElement("div",{className:"relative z-10 h-screen overflow-hidden col-span-3 flex justify-center items-center"},n.a.createElement(Qs.PanZoom,{ref:t,minZoom:"0.4",autoCenter:!0,autoCenterZoomLevel:.7,enableBoundingBox:!0,boundaryRatioVertical:.8,boundaryRatioHorizontal:.8,style:{outline:"none"}},n.a.createElement("div",{id:"page",ref:e,className:"shadow-2xl break-words"},_d.find((function(e){return s.layout.toLowerCase()===e.key})).component())),n.a.createElement(om,null)),n.a.createElement("div",{id:"printPage",className:"break-words"},_d.find((function(e){return s.layout.toLowerCase()===e.key})).component()),n.a.createElement(am,null),n.a.createElement(um,null),n.a.createElement(im,null)))};i.a.configure({autoClose:3e3,closeButton:!1,hideProgressBar:!0,position:i.a.POSITION.BOTTOM_RIGHT}),l.a.render(n.a.createElement(n.a.StrictMode,null,n.a.createElement(Us,null,n.a.createElement(Bs,null,n.a.createElement(dm,null)))),document.getElementById("root")),function(e){if("serviceWorker"in navigator){if(new URL(".",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",(function(){var t="".concat(".","/service-worker.js");ms?(!function(e,t){fetch(e,{headers:{"Service-Worker":"script"}}).then((function(a){var o=a.headers.get("content-type");404===a.status||null!=o&&-1===o.indexOf("javascript")?navigator.serviceWorker.ready.then((function(e){e.unregister().then((function(){window.location.reload()}))})):ps(e,t)})).catch((function(){console.log("No internet connection found. App is running in offline mode.")}))}(t,e),navigator.serviceWorker.ready.then((function(){console.log("This web app is being served cache-first by a service worker. To learn more, visit https://bit.ly/CRA-PWA")}))):ps(t,e)}))}}()}]),[[653,1,2]]]);
//# sourceMappingURL=main.f6ad7922.chunk.js.map