<html>
<head>
<title>Docs For Class BrowserDetection</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">Browser_Detection</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_Browser_Detection.html" class="menu">class tree: Browser_Detection</a> ]
		  [ <a href="../elementindex_Browser_Detection.html" class="menu">index: Browser_Detection</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_Browser_Detection.html">Browser_Detection</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../Browser_Detection/_BrowserDetection.php.html">		BrowserDetection.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../Browser_Detection/BrowserDetection.html">BrowserDetection</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Class: BrowserDetection</h1>
Source Location: /BrowserDetection.php<br /><br />


<table width="100%" border="0">
<tr><td valign="top">

<h3><a href="#class_details">Class Overview</a></h3>
<pre></pre><br />
<div class="description">The BrowserDetection class facilitates the identification of the user's environment such as Web browser, version,  platform and device type.</div><br /><br />
<h4>Author(s):</h4>
<ul>
          <li>Alexandre Valiquette, Chris Schuld, Gary White</li>
                                                            </ul>




        
          
                              
<h4>Version:</h4>
<ul>
  <li>2.9.7</li>
</ul>

<h4>Copyright:</h4>
<ul>
  <li>Copyright (c) 2022, Wolfcast</li>
</ul>
        
</td>

<td valign="top">
<h3><a href="#class_vars">Variables</a></h3>
<ul>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_agent">$_agent</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_browserName">$_browserName</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_compatibilityViewName">$_compatibilityViewName</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_compatibilityViewVer">$_compatibilityViewVer</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_customBrowserDetection">$_customBrowserDetection</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_customPlatformDetection">$_customPlatformDetection</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_customRobotDetection">$_customRobotDetection</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_is64bit">$_is64bit</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_isMobile">$_isMobile</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_isRobot">$_isRobot</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_platform">$_platform</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_platformVersion">$_platformVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_robotName">$_robotName</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_robotVersion">$_robotVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#var$_version">$_version</a></li>
  </ul>
</td>

<td valign="top">
<h3><a href="#class_consts">Constants</a></h3>
<ul>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_ANDROID">BROWSER_ANDROID</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_BLACKBERRY">BROWSER_BLACKBERRY</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_CHROME">BROWSER_CHROME</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_EDGE">BROWSER_EDGE</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_FIREBIRD">BROWSER_FIREBIRD</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_FIREFOX">BROWSER_FIREFOX</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_ICAB">BROWSER_ICAB</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_ICECAT">BROWSER_ICECAT</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_ICEWEASEL">BROWSER_ICEWEASEL</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_IE">BROWSER_IE</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_IE_MOBILE">BROWSER_IE_MOBILE</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_KONQUEROR">BROWSER_KONQUEROR</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_LYNX">BROWSER_LYNX</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_MOZILLA">BROWSER_MOZILLA</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_MSNTV">BROWSER_MSNTV</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_NETSCAPE">BROWSER_NETSCAPE</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_NOKIA">BROWSER_NOKIA</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_OPERA">BROWSER_OPERA</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_OPERA_MINI">BROWSER_OPERA_MINI</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_OPERA_MOBILE">BROWSER_OPERA_MOBILE</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_PHOENIX">BROWSER_PHOENIX</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_SAFARI">BROWSER_SAFARI</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_SAMSUNG">BROWSER_SAMSUNG</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_TABLET_OS">BROWSER_TABLET_OS</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_UC">BROWSER_UC</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constBROWSER_UNKNOWN">BROWSER_UNKNOWN</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_ANDROID">PLATFORM_ANDROID</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_BLACKBERRY">PLATFORM_BLACKBERRY</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_CHROME_OS">PLATFORM_CHROME_OS</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_FREEBSD">PLATFORM_FREEBSD</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_IOS">PLATFORM_IOS</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_LINUX">PLATFORM_LINUX</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_MACINTOSH">PLATFORM_MACINTOSH</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_NETBSD">PLATFORM_NETBSD</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_NOKIA">PLATFORM_NOKIA</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_OPENBSD">PLATFORM_OPENBSD</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_OPENSOLARIS">PLATFORM_OPENSOLARIS</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_SYMBIAN">PLATFORM_SYMBIAN</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_UNKNOWN">PLATFORM_UNKNOWN</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_VERSION_UNKNOWN">PLATFORM_VERSION_UNKNOWN</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_WINDOWS">PLATFORM_WINDOWS</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_WINDOWS_CE">PLATFORM_WINDOWS_CE</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constPLATFORM_WINDOWS_PHONE">PLATFORM_WINDOWS_PHONE</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constROBOT_BINGBOT">ROBOT_BINGBOT</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constROBOT_GOOGLEBOT">ROBOT_GOOGLEBOT</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constROBOT_MSNBOT">ROBOT_MSNBOT</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constROBOT_SLURP">ROBOT_SLURP</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constROBOT_UNKNOWN">ROBOT_UNKNOWN</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constROBOT_VERSION_UNKNOWN">ROBOT_VERSION_UNKNOWN</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constROBOT_W3CVALIDATOR">ROBOT_W3CVALIDATOR</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constROBOT_YAHOO_MM">ROBOT_YAHOO_MM</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#constVERSION_UNKNOWN">VERSION_UNKNOWN</a></li>
  </ul>
</td>

<td valign="top">
<h3><a href="#class_methods">Methods</a></h3>
<ul>
    <li><a href="../Browser_Detection/BrowserDetection.html#method__construct">__construct</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodaddCustomBrowserDetection">addCustomBrowserDetection</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodaddCustomPlatformDetection">addCustomPlatformDetection</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodaddCustomRobotDetection">addCustomRobotDetection</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodandroidVerToStr">androidVerToStr</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowser">checkBrowser</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserAndroid">checkBrowserAndroid</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserBlackBerry">checkBrowserBlackBerry</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserChrome">checkBrowserChrome</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserCustom">checkBrowserCustom</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserEdge">checkBrowserEdge</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserFirebird">checkBrowserFirebird</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserFirefox">checkBrowserFirefox</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserIcab">checkBrowserIcab</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserIceCat">checkBrowserIceCat</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserIceWeasel">checkBrowserIceWeasel</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserInternetExplorer">checkBrowserInternetExplorer</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserKonqueror">checkBrowserKonqueror</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserLynx">checkBrowserLynx</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserMozilla">checkBrowserMozilla</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserMsnTv">checkBrowserMsnTv</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserNetscape">checkBrowserNetscape</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserNokia">checkBrowserNokia</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserOpera">checkBrowserOpera</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserPhoenix">checkBrowserPhoenix</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserSafari">checkBrowserSafari</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserSamsung">checkBrowserSamsung</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserUAWithVersion">checkBrowserUAWithVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserUC">checkBrowserUC</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckPlatform">checkPlatform</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckPlatformCustom">checkPlatformCustom</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckPlatformVersion">checkPlatformVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckRobot">checkRobot</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckRobotBingbot">checkRobotBingbot</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckRobotCustom">checkRobotCustom</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckRobotGooglebot">checkRobotGooglebot</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckRobotMsnBot">checkRobotMsnBot</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckRobotSlurp">checkRobotSlurp</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckRobotW3CValidator">checkRobotW3CValidator</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckRobotYahooMultimedia">checkRobotYahooMultimedia</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckSimpleBrowserUA">checkSimpleBrowserUA</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcheckSimpleRobot">checkSimpleRobot</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcleanVersion">cleanVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcompareVersions">compareVersions</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodcontainString">containString</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methoddetect">detect</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodfindAndGetVersion">findAndGetVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodgetIECompatibilityView">getIECompatibilityView</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodgetLibVersion">getLibVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodgetName">getName</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodgetPlatform">getPlatform</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodgetPlatformVersion">getPlatformVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodgetRobotName">getRobotName</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodgetRobotVersion">getRobotVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodgetUserAgent">getUserAgent</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodgetVersion">getVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodiOSVerToStr">iOSVerToStr</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodis64bitPlatform">is64bitPlatform</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodisChromeFrame">isChromeFrame</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodisInIECompatibilityView">isInIECompatibilityView</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodisMobile">isMobile</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodisRobot">isRobot</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodmacVerToStr">macVerToStr</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodparseInt">parseInt</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodremoveCustomBrowserDetection">removeCustomBrowserDetection</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodremoveCustomPlatformDetection">removeCustomPlatformDetection</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodremoveCustomRobotDetection">removeCustomRobotDetection</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodreset">reset</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsafariBuildToSafariVer">safariBuildToSafariVer</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodset64bit">set64bit</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsetBrowser">setBrowser</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsetMobile">setMobile</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsetPlatform">setPlatform</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsetPlatformVersion">setPlatformVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsetRobot">setRobot</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsetRobotName">setRobotName</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsetRobotVersion">setRobotVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsetUserAgent">setUserAgent</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodsetVersion">setVersion</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodwebKitBuildToSafariVer">webKitBuildToSafariVer</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodwindowsNTVerToStr">windowsNTVerToStr</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodwindowsVerToStr">windowsVerToStr</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#methodwordPos">wordPos</a></li>
    <li><a href="../Browser_Detection/BrowserDetection.html#method__toString">__toString</a></li>
  </ul>
</td>

</tr></table>
<hr />

<table width="100%" border="0"><tr>






</tr></table>
<hr />

<a name="class_details"></a>
<h3>Class Details</h3>
<div class="tags">
[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a158">158</a>]<br />
The BrowserDetection class facilitates the identification of the user's environment such as Web browser, version,  platform and device type.<br /><br /><p>Typical usage:</p><p>$browser = new Wolfcast\BrowserDetection();  if ($browser-&gt;getName() == Wolfcast\BrowserDetection::BROWSER_FIREFOX &amp;&amp;      $browser-&gt;compareVersions($browser-&gt;getVersion(), '5.0') &gt;= 0) {      echo 'You are using FireFox version 5 or greater.';  }</p><p>The class is a rewrite of Chris Schuld's Browser class version 1.9 which is mostly unmaintained since August 20th,  2010. Chris' class was based on the original work from Gary White.</p><p>Updates:</p><p>2022-09-20: Version 2.9.7 <ul><li>Changed licensing to dual licensing: MIT or LGPL-3.0-only. This has no impact on existing users. You can continue
    under the previous license or switch to MIT.</li></ul>  2022-05-01: Version 2.9.6 <ul><li>Added support for Chrome OS.</li><li>Added support for macOS Monterey and macOS Big Sur.</li><li>Now correctly detects AArch64 as 64-bit.</li><li>Added support for PHP 8.</li><li>Tested with latest Web Browsers and platforms.</li></ul>  2020-02-02: Version 2.9.5 <ul><li>WARNING! Breaking change: complete rework of robots detection. Now robot name and version is detected in addition
    of browser name and version. Use getRobotName() and getRobotVersion() when isRobot() is true.</li><li>WARNING! Breaking change: due to robots detection rework the following methods signatures has changed (isRobot
    parameter removed): addCustomBrowserDetection(), checkSimpleBrowserUA(), checkBrowserUAWithVersion().</li><li>Added possibility to support new robots with addCustomRobotDetection().</li><li>Added support for the new Microsoft Edge based on Chromium.</li><li>Added version names for Android 10 and later (Google no longer use candy names for new versions).</li><li>Added macOS Catalina detection.</li><li>Added Windows Server 2019 detection (Windows Server 2016 can be no longer detected due to the fact that they both
    use the same version number and that the build is not included in the user agent).</li></ul>  2019-03-27: Version 2.9.3 <ul><li>Fixed Edge detection on Android.</li><li>Added Android Q detection.</li><li>Now filtering superglobals.</li></ul>  2019-02-28: Version 2.9.2 <ul><li>Fixed Opera detection.</li></ul>  2018-08-23: Version 2.9.1 <ul><li>Fixed Chrome detection under iOS.</li><li>Added Android Pie detection.</li><li>Added macOS Mojave detection.</li></ul>  2018-07-15: Version 2.9.0 <ul><li>WARNING! Breaking change: new Wolfcast namespace. Use new Wolfcast\BrowserDetection().</li><li>iPad, iPhone and iPod are all under iOS now.</li><li>Added Android Oreo detection.</li><li>Added macOS High Sierra detection.</li><li>Added UC Browser detection.</li><li>Improved regular expressions (even less false positives).</li><li>Removed AOL detection.</li><li>Removed the following Web browsers detection: Amaya, Galeon, NetPositive, OmniWeb, Vivaldi detection (use
    addCustomBrowserDetection()).</li><li>Removed the following legacy platforms detection: BeOS, OS/2, SunOS (use addCustomPlatformDetection()).</li></ul>  2016-11-28: Version 2.5.1 <ul><li>Better detection of 64-bit platforms.</li></ul>  2016-08-19: Version 2.5.0 <ul><li>Platform version and platform version name are now supported for Mac.</li><li>Fixed platform version name for Android.</li></ul>  2016-08-02: Version 2.4.0 <ul><li>Platform version and platform version name are now supported for Android.</li><li>Added support for the Samsung Internet browser.</li><li>Added support for the Vivaldi browser.</li><li>Better support for legacy Windows versions.</li></ul>  2016-02-11: Version 2.3.0 <ul><li>WARNING! Breaking change: public method getBrowser() is renamed to getName().</li><li>WARNING! Breaking change: changed the compareVersions() return values to be more in line with other libraries.</li><li>You can now get the exact platform version (name or version numbers) on which the browser is run on with
    getPlatformVersion(). Only working with Windows operating systems at the moment.</li><li>You can now determine if the browser is executed from a 64-bit platform with is64bitPlatform().</li><li>Better detection of mobile platform for Googlebot.</li></ul>  2016-01-04: Version 2.2.0 <ul><li>Added support for Microsoft Edge.</li></ul>  2014-12-30: Version 2.1.2 <ul><li>Better detection of Opera.</li></ul>  2014-07-11: Version 2.1.1 <ul><li>Better detection of mobile devices and platforms.</li></ul>  2014-06-04: Version 2.1.0 <ul><li>Added support for IE 11+.</li></ul>  2013-05-27: Version 2.0.0 which is (almost) a complete rewrite based on Chris Schuld's Browser class version 1.9 plus  changes below. <ul><li>Added support for Opera Mobile</li><li>Added support for the Windows Phone (formerly Windows Mobile) platform</li><li>Added support for BlackBerry Tablet OS and BlackBerry 10</li><li>Added support for the Symbian platform</li><li>Added support for Bingbot</li><li>Added support for the Yahoo! Multimedia crawler</li><li>Removed iPhone/iPad/iPod browsers since there are not browsers but platforms - test them with getPlatform()</li><li>Removed support for Shiretoko (Firefox 3.5 alpha/beta) and MSN Browser</li><li>Merged Nokia and Nokia S60</li><li>Updated some deprecated browser names</li><li>Many public methods are now protected</li><li>Documentation updated</li></ul>  2010-07-04: <ul><li>Added detection of IE compatibility view - test with getIECompatibilityView()</li><li>Added support for all (deprecated) Netscape versions</li><li>Added support for Safari &lt; 3.0</li><li>Better Firefox version parsing</li><li>Better Opera version parsing</li><li>Better Mozilla detection</li></ul></p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Alexandre Valiquette, Chris Schuld, Gary White</td>
  </tr>
  <tr>
    <td><b>version:</b>&nbsp;&nbsp;</td><td>2.9.7</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>Copyright (c) 2022, Wolfcast</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://wolfcast.com/open-source/browser-detection/tutorial.php">https://wolfcast.com/open-source/browser-detection/tutorial.php</a></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://wolfcast.com/">https://wolfcast.com/</a></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.apptools.com/phptools/browser/">https://www.apptools.com/phptools/browser/</a></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://chrisschuld.com/">https://chrisschuld.com/</a></td>
  </tr>
  <tr>
    <td><b>last-modified:</b>&nbsp;&nbsp;</td><td>September 20, 2022</td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="https://spdx.org/licenses/MIT.html">https://spdx.org/licenses/MIT.html</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="https://spdx.org/licenses/GPL-3.0-only.html">https://spdx.org/licenses/GPL-3.0-only.html</a></td>
  </tr>
</table>
</div>
</div><br /><br />
<div class="top">[ <a href="#top">Top</a> ]</div><br />

<hr />
<a name="class_vars"></a>
<h3>Class Variables</h3>
<div class="tags">
	<a name="var$_agent"></a>
	<p></p>
	<h4>$_agent = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a237">237</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>string</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_browserName"></a>
	<p></p>
	<h4>$_browserName = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a243">243</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>string</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_compatibilityViewName"></a>
	<p></p>
	<h4>$_compatibilityViewName = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a249">249</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>string</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_compatibilityViewVer"></a>
	<p></p>
	<h4>$_compatibilityViewVer = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a255">255</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>string</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_customBrowserDetection"></a>
	<p></p>
	<h4>$_customBrowserDetection = <span class="value">array()</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a261">261</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_customPlatformDetection"></a>
	<p></p>
	<h4>$_customPlatformDetection = <span class="value">array()</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a267">267</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_customRobotDetection"></a>
	<p></p>
	<h4>$_customRobotDetection = <span class="value">array()</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a273">273</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>array</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_is64bit"></a>
	<p></p>
	<h4>$_is64bit = <span class="value">&nbsp;false</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a279">279</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>boolean</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_isMobile"></a>
	<p></p>
	<h4>$_isMobile = <span class="value">&nbsp;false</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a285">285</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>boolean</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_isRobot"></a>
	<p></p>
	<h4>$_isRobot = <span class="value">&nbsp;false</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a291">291</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>boolean</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_platform"></a>
	<p></p>
	<h4>$_platform = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a297">297</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>string</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_platformVersion"></a>
	<p></p>
	<h4>$_platformVersion = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a303">303</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>string</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_robotName"></a>
	<p></p>
	<h4>$_robotName = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a309">309</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>string</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_robotVersion"></a>
	<p></p>
	<h4>$_robotVersion = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a315">315</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>string</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="var$_version"></a>
	<p></p>
	<h4>$_version = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a321">321</a>]</p>
  <br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>private</td>
  </tr>
</table>
</div>

  <br />
	<div class="tags">
  <table border="0" cellspacing="0" cellpadding="0">
    <tr>
      <td><b>Type:</b>&nbsp;&nbsp;</td>
      <td>string</td>
    </tr>
      </table>
	</div><br /><br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
</div><br />

<hr />
<a name="class_methods"></a>
<h3>Class Methods</h3>
<div class="tags">

  <hr />
	<a name="method__construct"></a>
	<h3>constructor __construct <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a332">332</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>BrowserDetection __construct(
[string
$useragent = ''])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		BrowserDetection class constructor.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$useragent</b>&nbsp;&nbsp;</td>
        <td>(optional) The user agent to work with. Leave empty for the current user agent  (contained in $_SERVER['HTTP_USER_AGENT']).</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodaddCustomBrowserDetection"></a>
	<h3>method addCustomBrowserDetection <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a386">386</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean addCustomBrowserDetection(
string
$browserName, [mixed
$uaNameToLookFor = ''], [boolean
$isMobile = false], [string
$separator = '/'], [boolean
$uaNameFindWords = true])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Dynamically add support for a new Web browser.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the custom rule has been added, false otherwise.</td>
  </tr>
  <tr>
    <td><b>see:</b>&nbsp;&nbsp;</td><td><a href="../Browser_Detection/BrowserDetection.html#methodremoveCustomBrowserDetection">BrowserDetection::removeCustomBrowserDetection()</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$browserName</b>&nbsp;&nbsp;</td>
        <td>The Web browser name (used for display).</td>
      </tr>
          <tr>
        <td class="type">mixed&nbsp;&nbsp;</td>
        <td><b>$uaNameToLookFor</b>&nbsp;&nbsp;</td>
        <td>(optional) The string (or array of strings) representing the browser name to find  in the user agent. If omitted, $browserName will be used.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$isMobile</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the browser is from a mobile device.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$separator</b>&nbsp;&nbsp;</td>
        <td>(optional) The separator string used to split the browser name and the version number in  the user agent.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$uaNameFindWords</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the browser name to find should match a word instead of  a part of a word. For example &quot;Bar&quot; would not be found in &quot;FooBar&quot; when true but would be found in &quot;Foo Bar&quot;.  When set to false, the browser name can be found anywhere in the user agent string.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodaddCustomPlatformDetection"></a>
	<h3>method addCustomPlatformDetection <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a413">413</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean addCustomPlatformDetection(
string
$platformName, [mixed
$platformNameToLookFor = ''], [boolean
$isMobile = false], [boolean
$uaNameFindWords = true])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Dynamically add support for a new platform.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the custom rule has been added, false otherwise.</td>
  </tr>
  <tr>
    <td><b>see:</b>&nbsp;&nbsp;</td><td><a href="../Browser_Detection/BrowserDetection.html#methodremoveCustomPlatformDetection">BrowserDetection::removeCustomPlatformDetection()</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$platformName</b>&nbsp;&nbsp;</td>
        <td>The platform name (used for display).</td>
      </tr>
          <tr>
        <td class="type">mixed&nbsp;&nbsp;</td>
        <td><b>$platformNameToLookFor</b>&nbsp;&nbsp;</td>
        <td>(optional) The string (or array of strings) representing the platform name to  find in the user agent. If omitted, $platformName will be used.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$isMobile</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the platform is from a mobile device.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$uaNameFindWords</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the platform name to find should match a word instead of  a part of a word. For example &quot;Bar&quot; would not be found in &quot;FooBar&quot; when true but would be found in &quot;Foo Bar&quot;.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodaddCustomRobotDetection"></a>
	<h3>method addCustomRobotDetection <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a444">444</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean addCustomRobotDetection(
string
$robotName, [mixed
$uaNameToLookFor = ''], [boolean
$isMobile = false], [string
$separator = '/'], [boolean
$uaNameFindWords = true])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Dynamically add support for a new robot.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the custom rule has been added, false otherwise.</td>
  </tr>
  <tr>
    <td><b>see:</b>&nbsp;&nbsp;</td><td><a href="../Browser_Detection/BrowserDetection.html#methodremoveCustomRobotDetection">BrowserDetection::removeCustomRobotDetection()</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$robotName</b>&nbsp;&nbsp;</td>
        <td>The robot name (used for display).</td>
      </tr>
          <tr>
        <td class="type">mixed&nbsp;&nbsp;</td>
        <td><b>$uaNameToLookFor</b>&nbsp;&nbsp;</td>
        <td>(optional) The string (or array of strings) representing the robot name to find  in the user agent. If omitted, $robotName will be used.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$isMobile</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the robot should be considered as mobile or not.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$separator</b>&nbsp;&nbsp;</td>
        <td>(optional) The separator string used to split the robot name and the version number in  the user agent.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$uaNameFindWords</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the robot name to find should match a word instead of  a part of a word. For example &quot;Bar&quot; would not be found in &quot;FooBar&quot; when true but would be found in &quot;Foo Bar&quot;.  When set to false, the robot name can be found anywhere in the user agent string.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodandroidVerToStr"></a>
	<h3>method androidVerToStr <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a771">771</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string androidVerToStr(
string
$androidVer)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert the Android version numbers to the operating system name. For instance '1.6' returns 'Donut'.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>The operating system name or the constant PLATFORM_VERSION_UNKNOWN if nothing match the version  numbers.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$androidVer</b>&nbsp;&nbsp;</td>
        <td>The Android version numbers as a string.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowser"></a>
	<h3>method checkBrowser <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1236">1236</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowser(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine what is the browser used by the user.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser has been identified, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserAndroid"></a>
	<h3>method checkBrowserAndroid <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a820">820</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserAndroid(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is the Android browser (based on the WebKit layout engine and coupled with Chrome's  JavaScript engine) or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is the Android browser, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserBlackBerry"></a>
	<h3>method checkBrowserBlackBerry <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a832">832</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserBlackBerry(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is the BlackBerry browser or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is the BlackBerry browser, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://web.archive.org/web/20170328000854/http://supportforums.blackberry.com/t5/Web-and-WebWorks-Development/How-to-detect-the-BlackBerry-Browser/ta-p/559862">https://web.archive.org/web/20170328000854/http://supportforums.blackberry.com/t5/Web-and-WebWorks-Development/How-to-detect-the-BlackBerry-Browser/ta-p/559862</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserChrome"></a>
	<h3>method checkBrowserChrome <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a868">868</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserChrome(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Chrome or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Chrome, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.google.com/chrome/">https://www.google.com/chrome/</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserCustom"></a>
	<h3>method checkBrowserCustom <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a879">879</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserCustom(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is among the custom browser rules or not. Rules are checked in the order they were  added.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if we found the browser we were looking for in the custom rules, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserEdge"></a>
	<h3>method checkBrowserEdge <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a898">898</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserEdge(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Edge or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Edge, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserFirebird"></a>
	<h3>method checkBrowserFirebird <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a908">908</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserFirebird(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Firebird or not. Firebird was the name of Firefox from version 0.6 to 0.7.1.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Firebird, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserFirefox"></a>
	<h3>method checkBrowserFirefox <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a919">919</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserFirefox(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Firefox or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Firefox, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.mozilla.org/en-US/firefox/new/">https://www.mozilla.org/en-US/firefox/new/</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserIcab"></a>
	<h3>method checkBrowserIcab <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a941">941</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserIcab(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is iCab or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is iCab, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="http://www.icab.de/">http://www.icab.de/</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserIceCat"></a>
	<h3>method checkBrowserIceCat <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a953">953</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserIceCat(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is GNU IceCat (formerly known as GNU IceWeasel) or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is GNU IceCat, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.gnu.org/software/gnuzilla/">https://www.gnu.org/software/gnuzilla/</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserIceWeasel"></a>
	<h3>method checkBrowserIceWeasel <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a964">964</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserIceWeasel(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is GNU IceWeasel (now know as GNU IceCat) or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is GNU IceWeasel, false otherwise.</td>
  </tr>
  <tr>
    <td><b>see:</b>&nbsp;&nbsp;</td><td><a href="../Browser_Detection/BrowserDetection.html#methodcheckBrowserIceCat">BrowserDetection::checkBrowserIceCat()</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserInternetExplorer"></a>
	<h3>method checkBrowserInternetExplorer <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a976">976</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserInternetExplorer(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Internet Explorer or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Internet Explorer, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.microsoft.com/ie/">https://www.microsoft.com/ie/</a></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://en.wikipedia.org/wiki/Internet_Explorer_Mobile">https://en.wikipedia.org/wiki/Internet_Explorer_Mobile</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserKonqueror"></a>
	<h3>method checkBrowserKonqueror <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1067">1067</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserKonqueror(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Konqueror or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Konqueror, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.konqueror.org/">https://www.konqueror.org/</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserLynx"></a>
	<h3>method checkBrowserLynx <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1079">1079</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserLynx(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Lynx or not. It is the oldest web browser currently in general use and development.<br /><br /><p>It is a text-based only Web browser.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Lynx, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://en.wikipedia.org/wiki/Lynx_(web_browser)">https://en.wikipedia.org/wiki/Lynx_(web_browser)</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserMozilla"></a>
	<h3>method checkBrowserMozilla <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1089">1089</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserMozilla(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Mozilla or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Mozilla, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserMsnTv"></a>
	<h3>method checkBrowserMsnTv <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1100">1100</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserMsnTv(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is MSN TV (formerly WebTV) or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is WebTv, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://en.wikipedia.org/wiki/MSN_TV">https://en.wikipedia.org/wiki/MSN_TV</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserNetscape"></a>
	<h3>method checkBrowserNetscape <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1111">1111</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserNetscape(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Netscape or not. Official support for this browser ended on March 1st, 2008.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Netscape, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://en.wikipedia.org/wiki/Netscape">https://en.wikipedia.org/wiki/Netscape</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserNokia"></a>
	<h3>method checkBrowserNokia <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1164">1164</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserNokia(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is a Nokia browser or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is a Nokia browser, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://web.archive.org/web/20141012034159/http://www.developer.nokia.com/Community/Wiki/User-Agent_headers_for_Nokia_devices">https://web.archive.org/web/20141012034159/http://www.developer.nokia.com/Community/Wiki/User-Agent_headers_for_Nokia_devices</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserOpera"></a>
	<h3>method checkBrowserOpera <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1190">1190</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserOpera(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Opera or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Opera, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://web.archive.org/web/20140220123653/http://my.opera.com/community/openweb/idopera/">https://web.archive.org/web/20140220123653/http://my.opera.com/community/openweb/idopera/</a></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.opera.com/mobile/">https://www.opera.com/mobile/</a></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.opera.com/">https://www.opera.com/</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserPhoenix"></a>
	<h3>method checkBrowserPhoenix <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1226">1226</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserPhoenix(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Phoenix or not. Phoenix was the name of Firefox from version 0.1 to 0.5.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Phoenix, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserSafari"></a>
	<h3>method checkBrowserSafari <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1278">1278</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserSafari(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is Safari or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is Safari, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://en.wikipedia.org/wiki/Safari_version_history#Release_history">https://en.wikipedia.org/wiki/Safari_version_history#Release_history</a></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://web.archive.org/web/20080514173941/http://developer.apple.com/internet/safari/uamatrix.html">https://web.archive.org/web/20080514173941/http://developer.apple.com/internet/safari/uamatrix.html</a></td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.apple.com/safari/">https://www.apple.com/safari/</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserSamsung"></a>
	<h3>method checkBrowserSamsung <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1317">1317</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserSamsung(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is the Samsung Internet browser or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is the the Samsung Internet browser, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserUAWithVersion"></a>
	<h3>method checkBrowserUAWithVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1336">1336</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserUAWithVersion(
mixed
$uaNameToLookFor, string
$userAgent, string
$browserName, [boolean
$isMobile = false], [boolean
$findWords = true])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Test the user agent for a specific browser that use a &quot;Version&quot; string (like Safari and Opera). The user agent  should look like: &quot;Version/1.0 Browser name/123.456&quot; or &quot;Browser name/123.456 Version/1.0&quot;.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if we found the browser we were looking for, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">mixed&nbsp;&nbsp;</td>
        <td><b>$uaNameToLookFor</b>&nbsp;&nbsp;</td>
        <td>The string (or array of strings) representing the browser name to find in the user  agent.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$userAgent</b>&nbsp;&nbsp;</td>
        <td>The user agent string to work with.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$browserName</b>&nbsp;&nbsp;</td>
        <td>The literal browser name. Always use a class constant!</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$isMobile</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the browser is from a mobile device.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$findWords</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the needle should match a word to be found. For example &quot;Bar&quot;  would not be found in &quot;FooBar&quot; when true but would be found in &quot;Foo Bar&quot;. When set to false, the needle can be  found anywhere in the haystack.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckBrowserUC"></a>
	<h3>method checkBrowserUC <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1368">1368</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkBrowserUC(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is UC Browser or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is UC Browser, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckPlatform"></a>
	<h3>method checkPlatform <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1377">1377</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void checkPlatform(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine the user's platform.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckPlatformCustom"></a>
	<h3>method checkPlatformCustom <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1447">1447</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkPlatformCustom(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the platform is among the custom platform rules or not. Rules are checked in the order they were  added.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if we found the platform we were looking for in the custom rules, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckPlatformVersion"></a>
	<h3>method checkPlatformVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1469">1469</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void checkPlatformVersion(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine the user's platform version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckRobot"></a>
	<h3>method checkRobot <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1580">1580</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void checkRobot(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if it's a robot crawling the page and find it's name and version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckRobotBingbot"></a>
	<h3>method checkRobotBingbot <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1542">1542</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkRobotBingbot(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the robot is the Bingbot crawler or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the robot is Bingbot, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://www.bing.com/webmaster/help/which-crawlers-does-bing-use-8c184ec0">https://www.bing.com/webmaster/help/which-crawlers-does-bing-use-8c184ec0</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckRobotCustom"></a>
	<h3>method checkRobotCustom <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1596">1596</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkRobotCustom(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the robot is among the custom robot rules or not. Rules are checked in the order they were added.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if we found the robot we were looking for in the custom rules, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckRobotGooglebot"></a>
	<h3>method checkRobotGooglebot <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1552">1552</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkRobotGooglebot(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the robot is the Googlebot crawler or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the robot is Googlebot, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckRobotMsnBot"></a>
	<h3>method checkRobotMsnBot <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1571">1571</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkRobotMsnBot(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the robot is the MSNBot crawler or not. In October 2010 it was replaced by the Bingbot robot.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the robot is MSNBot, false otherwise.</td>
  </tr>
  <tr>
    <td><b>see:</b>&nbsp;&nbsp;</td><td><a href="../Browser_Detection/BrowserDetection.html#methodcheckRobotBingbot">BrowserDetection::checkRobotBingbot()</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckRobotSlurp"></a>
	<h3>method checkRobotSlurp <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1616">1616</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkRobotSlurp(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the robot is the Yahoo! Slurp crawler or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the robot is Yahoo! Slurp, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckRobotW3CValidator"></a>
	<h3>method checkRobotW3CValidator <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1627">1627</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkRobotW3CValidator(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the robot is the W3C Validator or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the robot is the W3C Validator, false otherwise.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://validator.w3.org/">https://validator.w3.org/</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckRobotYahooMultimedia"></a>
	<h3>method checkRobotYahooMultimedia <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1664">1664</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkRobotYahooMultimedia(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the robot is the Yahoo! multimedia crawler or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the robot is the Yahoo! multimedia crawler, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckSimpleBrowserUA"></a>
	<h3>method checkSimpleBrowserUA <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1685">1685</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkSimpleBrowserUA(
mixed
$uaNameToLookFor, string
$userAgent, string
$browserName, [boolean
$isMobile = false], [string
$separator = '/'], [boolean
$uaNameFindWords = true])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Test the user agent for a specific browser where the browser name is immediately followed by the version number.<br /><br /><p>The user agent should look like: &quot;Browser name/1.0&quot; or &quot;Browser 1.0;&quot;.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if we found the browser we were looking for, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">mixed&nbsp;&nbsp;</td>
        <td><b>$uaNameToLookFor</b>&nbsp;&nbsp;</td>
        <td>The string (or array of strings) representing the browser name to find in the user  agent.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$userAgent</b>&nbsp;&nbsp;</td>
        <td>The user agent string to work with.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$browserName</b>&nbsp;&nbsp;</td>
        <td>The literal browser name. Always use a class constant!</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$isMobile</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the browser is from a mobile device.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$separator</b>&nbsp;&nbsp;</td>
        <td>(optional) The separator string used to split the browser name and the version number in  the user agent.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$uaNameFindWords</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the browser name to find should match a word instead of  a part of a word. For example &quot;Bar&quot; would not be found in &quot;FooBar&quot; when true but would be found in &quot;Foo Bar&quot;.  When set to false, the browser name can be found anywhere in the user agent string.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcheckSimpleRobot"></a>
	<h3>method checkSimpleRobot <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1714">1714</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean checkSimpleRobot(
mixed
$uaNameToLookFor, string
$userAgent, string
$robotName, [string
$separator = '/'], [boolean
$uaNameFindWords = true])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Test the user agent for a specific robot where the robot name is immediately followed by the version number.<br /><br /><p>The user agent should look like: &quot;Robot name/1.0&quot; or &quot;Robot 1.0;&quot;.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if we found the robot we were looking for, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">mixed&nbsp;&nbsp;</td>
        <td><b>$uaNameToLookFor</b>&nbsp;&nbsp;</td>
        <td>The string (or array of strings) representing the robot name to find in the user  agent.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$userAgent</b>&nbsp;&nbsp;</td>
        <td>The user agent string to work with.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$robotName</b>&nbsp;&nbsp;</td>
        <td>The literal robot name. Always use a class constant!</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$separator</b>&nbsp;&nbsp;</td>
        <td>(optional) The separator string used to split the robot name and the version number in  the user agent.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$uaNameFindWords</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the robot name to find should match a word instead of  a part of a word. For example &quot;Bar&quot; would not be found in &quot;FooBar&quot; when true but would be found in &quot;Foo Bar&quot;.  When set to false, the robot name can be found anywhere in the user agent string.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcleanVersion"></a>
	<h3>method cleanVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1734">1734</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string cleanVersion(
string
$version, [mixed
$toRemove = NULL])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Clean a version string from unwanted characters.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the cleaned version number string.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>The version string to clean.</td>
      </tr>
          <tr>
        <td class="type">mixed&nbsp;&nbsp;</td>
        <td><b>$toRemove</b>&nbsp;&nbsp;</td>
        <td>(optional) String or array of strings representing additional string(s) to remove.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcompareVersions"></a>
	<h3>method compareVersions <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a467">467</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int compareVersions(
string
$sourceVer, string
$compareVer)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Compare two version number strings.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns -1 if $sourceVer &lt; $compareVer, 0 if $sourceVer == $compareVer or 1 if $sourceVer &gt;  $compareVer.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$sourceVer</b>&nbsp;&nbsp;</td>
        <td>The source version number.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$compareVer</b>&nbsp;&nbsp;</td>
        <td>The version number to compare with the source version number.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodcontainString"></a>
	<h3>method containString <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1782">1782</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean containString(
string
$haystack, mixed
$needle, [boolean
$insensitive = true], [boolean
$findWords = true], [
&$foundPos = NULL], int
$foundPos)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Find if one or more substring is contained in a string.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the needle (or one of the needles) has been found in the haystack, false  otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$haystack</b>&nbsp;&nbsp;</td>
        <td>The string to search in.</td>
      </tr>
          <tr>
        <td class="type">mixed&nbsp;&nbsp;</td>
        <td><b>$needle</b>&nbsp;&nbsp;</td>
        <td>The string to search for. Can be a string or an array of strings if multiples values are to  be searched.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$insensitive</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if we do a case-sensitive search (false) or a case-insensitive  one (true).</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$findWords</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the needle should match a word to be found. For example &quot;Bar&quot;  would not be found in &quot;FooBar&quot; when true but would be found in &quot;Foo Bar&quot;. When set to false, the needle can be  found anywhere in the haystack.</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$foundPos</b>&nbsp;&nbsp;</td>
        <td>(optional) Integer buffer that will contain the position of the needle (if found and if a  non NULL variable has been passed).</td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>&$foundPos</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methoddetect"></a>
	<h3>method detect <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1814">1814</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void detect(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Detect the user environment from the details in the user agent string.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodfindAndGetVersion"></a>
	<h3>method findAndGetVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1835">1835</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean findAndGetVersion(
type
$uaNameToLookFor, type
$userAgent, 
&$version, [type
$separator = '/'], [type
$uaNameFindWords = true], type
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Test the user agent for a specific browser and extract it's version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if we found the browser we were looking for, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">type&nbsp;&nbsp;</td>
        <td><b>$uaNameToLookFor</b>&nbsp;&nbsp;</td>
        <td>The string (or array of strings) representing the browser name to find in the user  agent.</td>
      </tr>
          <tr>
        <td class="type">type&nbsp;&nbsp;</td>
        <td><b>$userAgent</b>&nbsp;&nbsp;</td>
        <td>The user agent string to work with.</td>
      </tr>
          <tr>
        <td class="type">type&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>String buffer that will contain the version found (if any).</td>
      </tr>
          <tr>
        <td class="type">type&nbsp;&nbsp;</td>
        <td><b>$separator</b>&nbsp;&nbsp;</td>
        <td>(optional) The separator string used to split the browser name and the version number in  the user agent.</td>
      </tr>
          <tr>
        <td class="type">type&nbsp;&nbsp;</td>
        <td><b>$uaNameFindWords</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the browser name to find should match a word instead of  a part of a word. For example &quot;Bar&quot; would not be found in &quot;FooBar&quot; when true but would be found in &quot;Foo Bar&quot;.  When set to false, the browser name can be found anywhere in the user agent string.</td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>&$version</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetIECompatibilityView"></a>
	<h3>method getIECompatibilityView <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a514">514</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>mixed getIECompatibilityView(
[boolean
$asArray = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the name and version of the browser emulated in the compatibility view mode (if any). Since Internet  Explorer 8, IE can be put in compatibility mode to make websites that were created for older browsers, especially  IE 6 and 7, look better in IE 8+ which renders web pages closer to the standards and thus differently from those  older versions of IE.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>If a string was requested, the function returns the name and version of the browser emulated in  the compatibility view mode or an empty string if the browser is not in compatibility view mode. If an array was  requested, an array with the keys 'browser' and 'version' is returned.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$asArray</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the return value must be an array (true) or a string (false).</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetLibVersion"></a>
	<h3>method getLibVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a527">527</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getLibVersion(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Return the BrowserDetection class version.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the version as a sting with the #.#.# format.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetName"></a>
	<h3>method getName <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a537">537</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getName(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the name of the browser. All of the return values are class constants. You can compare them like this:  $myBrowserInstance-&gt;getName() == BrowserDetection::BROWSER_FIREFOX.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the name of the browser or BrowserDetection::BROWSER_UNKNOWN if unknown.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetPlatform"></a>
	<h3>method getPlatform <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a548">548</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getPlatform(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the name of the platform family on which the browser is run on (such as Windows, Apple, etc.). All of  the return values are class constants. You can compare them like this:  $myBrowserInstance-&gt;getPlatform() == BrowserDetection::PLATFORM_ANDROID.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the name of the platform or BrowserDetection::PLATFORM_UNKNOWN if unknown.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetPlatformVersion"></a>
	<h3>method getPlatformVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a566">566</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getPlatformVersion(
[boolean
$returnVersionNumbers = false], [boolean
$returnServerFlavor = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the platform version on which the browser is run on. It can be returned as a string number like 'NT 6.3' or  as a name like 'Windows 8.1'. When returning version string numbers for Windows NT OS families the number is  prefixed by 'NT ' to differentiate from older Windows 3.x &amp; 9x release. At the moment only the Windows and  Android operating systems are supported.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the version name/version numbers of the platform or the constant PLATFORM_VERSION_UNKNOWN  if unknown.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$returnVersionNumbers</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if the return value must be versions numbers as a  string (true) or the version name (false).</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$returnServerFlavor</b>&nbsp;&nbsp;</td>
        <td>(optional) Since some Windows NT versions have the same values, this flag  determines if the Server flavor is returned or not. For instance Windows 8.1 and Windows Server 2012 R2 both use  version 6.3. This parameter is only useful when testing for Windows.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetRobotName"></a>
	<h3>method getRobotName <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a603">603</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getRobotName(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the name of the robot. All of the return values are class constants. You can compare them like this:  $myBrowserInstance-&gt;getRobotName() == BrowserDetection::ROBOT_GOOGLEBOT.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the name of the robot or BrowserDetection::ROBOT_UNKNOWN if unknown.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetRobotVersion"></a>
	<h3>method getRobotVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a612">612</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getRobotVersion(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the version of the robot.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the version of the robot or BrowserDetection::ROBOT_VERSION_UNKNOWN if unknown.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetUserAgent"></a>
	<h3>method getUserAgent <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a621">621</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getUserAgent(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the user agent value used by the class to determine the browser details.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>The user agent string.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodgetVersion"></a>
	<h3>method getVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a630">630</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string getVersion(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the version of the browser.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the version of the browser or BrowserDetection::VERSION_UNKNOWN if unknown.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodiOSVerToStr"></a>
	<h3>method iOSVerToStr <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1869">1869</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string iOSVerToStr(
string
$iOSVer)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert the iOS version numbers to the operating system name. For instance '2.0' returns 'iPhone OS 2.0'.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>The operating system name.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$iOSVer</b>&nbsp;&nbsp;</td>
        <td>The iOS version numbers as a string.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodis64bitPlatform"></a>
	<h3>method is64bitPlatform <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a640">640</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean is64bitPlatform(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is executed from a 64-bit platform. Keep in mind that not all platforms/browsers report  this and the result may not always be accurate.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is executed from a 64-bit platform.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodisChromeFrame"></a>
	<h3>method isChromeFrame <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a650">650</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean isChromeFrame(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser runs Google Chrome Frame (it's a plug-in designed for Internet Explorer 6+ based on the  open-source Chromium project - it's like a Chrome browser within IE).<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is using Google Chrome Frame, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodisInIECompatibilityView"></a>
	<h3>method isInIECompatibilityView <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a661">661</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean isInIECompatibilityView(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is in compatibility view or not. Since Internet Explorer 8, IE can be put in  compatibility mode to make websites that were created for older browsers, especially IE 6 and 7, look better in  IE 8+ which renders web pages closer to the standards and thus differently from those older versions of IE.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is in compatibility view, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodisMobile"></a>
	<h3>method isMobile <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a670">670</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean isMobile(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is from a mobile device or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is from a mobile device, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodisRobot"></a>
	<h3>method isRobot <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a679">679</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean isRobot(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine if the browser is a robot (Googlebot, Bingbot, Yahoo! Slurp...) or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the browser is a robot, false otherwise.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodmacVerToStr"></a>
	<h3>method macVerToStr <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1885">1885</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string macVerToStr(
string
$macVer)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert the macOS version numbers to the operating system name. For instance '10.7' returns 'Mac OS X Lion'.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>The operating system name or the constant PLATFORM_VERSION_UNKNOWN if nothing match the version  numbers.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$macVer</b>&nbsp;&nbsp;</td>
        <td>The macOS version numbers as a string.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodparseInt"></a>
	<h3>method parseInt <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1938">1938</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>int parseInt(
string
$intStr)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Get the integer value of a string variable.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>The integer value of $intStr on success, or 0 on failure.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$intStr</b>&nbsp;&nbsp;</td>
        <td>The scalar value being converted to an integer.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodremoveCustomBrowserDetection"></a>
	<h3>method removeCustomBrowserDetection <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a690">690</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean removeCustomBrowserDetection(
string
$browserName)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Remove support for a previously added Web browser.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the custom rule has been found and removed, false otherwise.</td>
  </tr>
  <tr>
    <td><b>see:</b>&nbsp;&nbsp;</td><td><a href="../Browser_Detection/BrowserDetection.html#methodaddCustomBrowserDetection">BrowserDetection::addCustomBrowserDetection()</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$browserName</b>&nbsp;&nbsp;</td>
        <td>The Web browser name as used when added.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodremoveCustomPlatformDetection"></a>
	<h3>method removeCustomPlatformDetection <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a706">706</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean removeCustomPlatformDetection(
string
$platformName)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Remove support for a previously added platform.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the custom rule has been found and removed, false otherwise.</td>
  </tr>
  <tr>
    <td><b>see:</b>&nbsp;&nbsp;</td><td><a href="../Browser_Detection/BrowserDetection.html#methodaddCustomPlatformDetection">BrowserDetection::addCustomPlatformDetection()</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$platformName</b>&nbsp;&nbsp;</td>
        <td>The platform name as used when added.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodremoveCustomRobotDetection"></a>
	<h3>method removeCustomRobotDetection <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a722">722</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>boolean removeCustomRobotDetection(
string
$robotName)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Remove support for a previously added robot.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns true if the custom rule has been found and removed, false otherwise.</td>
  </tr>
  <tr>
    <td><b>see:</b>&nbsp;&nbsp;</td><td><a href="../Browser_Detection/BrowserDetection.html#methodaddCustomRobotDetection">BrowserDetection::addCustomRobotDetection()</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$robotName</b>&nbsp;&nbsp;</td>
        <td>The robot name as used when added.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodreset"></a>
	<h3>method reset <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1947">1947</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void reset(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Reset all the properties of the class.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsafariBuildToSafariVer"></a>
	<h3>method safariBuildToSafariVer <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a1971">1971</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string safariBuildToSafariVer(
string
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert a Safari build number to a Safari version number.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the Safari version string. If the version can't be determined, an empty string is  returned.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://web.archive.org/web/20080514173941/http://developer.apple.com/internet/safari/uamatrix.html">https://web.archive.org/web/20080514173941/http://developer.apple.com/internet/safari/uamatrix.html</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>A string representing the version number.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodset64bit"></a>
	<h3>method set64bit <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2065">2065</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void set64bit(
boolean
$is64bit)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set if the browser is executed from a 64-bit platform.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$is64bit</b>&nbsp;&nbsp;</td>
        <td>Value that tells if the browser is executed from a 64-bit platform.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetBrowser"></a>
	<h3>method setBrowser <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2075">2075</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setBrowser(
string
$browserName)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the name of the browser.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$browserName</b>&nbsp;&nbsp;</td>
        <td>The name of the browser.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetMobile"></a>
	<h3>method setMobile <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2085">2085</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setMobile(
[boolean
$isMobile = true])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the browser to be from a mobile device or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$isMobile</b>&nbsp;&nbsp;</td>
        <td>(optional) Value that tells if the browser is on a mobile device or not.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetPlatform"></a>
	<h3>method setPlatform <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2095">2095</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setPlatform(
string
$platform)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the platform on which the browser is on.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$platform</b>&nbsp;&nbsp;</td>
        <td>The name of the platform.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetPlatformVersion"></a>
	<h3>method setPlatformVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2105">2105</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setPlatformVersion(
string
$platformVer)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the platform version on which the browser is on.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$platformVer</b>&nbsp;&nbsp;</td>
        <td>The version numbers of the platform.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetRobot"></a>
	<h3>method setRobot <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2115">2115</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setRobot(
[boolean
$isRobot = true])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the browser to be a robot (crawler) or not.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$isRobot</b>&nbsp;&nbsp;</td>
        <td>(optional) Value that tells if the browser is a robot or not.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetRobotName"></a>
	<h3>method setRobotName <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2125">2125</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setRobotName(
string
$robotName)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the name of the robot.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$robotName</b>&nbsp;&nbsp;</td>
        <td>The name of the robot.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetRobotVersion"></a>
	<h3>method setRobotVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2135">2135</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setRobotVersion(
string
$robotVersion)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the version of the robot.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$robotVersion</b>&nbsp;&nbsp;</td>
        <td>The version of the robot.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetUserAgent"></a>
	<h3>method setUserAgent <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a737">737</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setUserAgent(
[string
$agentString = ''])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the user agent to use with the class.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$agentString</b>&nbsp;&nbsp;</td>
        <td>(optional) The value of the user agent. If an empty string is sent (default),  $_SERVER['HTTP_USER_AGENT'] will be used.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodsetVersion"></a>
	<h3>method setVersion <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2151">2151</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>void setVersion(
string
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Set the version of the browser.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>The version of the browser.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodwebKitBuildToSafariVer"></a>
	<h3>method webKitBuildToSafariVer <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2170">2170</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string webKitBuildToSafariVer(
string
$version)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert a WebKit build number to a Safari version number.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the Safari version string. If the version can't be determined, an empty string is  returned.</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://web.archive.org/web/20080514173941/http://developer.apple.com/internet/safari/uamatrix.html">https://web.archive.org/web/20080514173941/http://developer.apple.com/internet/safari/uamatrix.html</a></td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$version</b>&nbsp;&nbsp;</td>
        <td>A string representing the version number.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodwindowsNTVerToStr"></a>
	<h3>method windowsNTVerToStr <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2279">2279</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string windowsNTVerToStr(
string
$winVer, [boolean
$returnServerFlavor = false])</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert the Windows NT family version numbers to the operating system name. For instance '5.1' returns  'Windows XP'.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>The operating system name or the constant PLATFORM_VERSION_UNKNOWN if nothing match the version  numbers.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$winVer</b>&nbsp;&nbsp;</td>
        <td>The Windows NT family version numbers as a string.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$returnServerFlavor</b>&nbsp;&nbsp;</td>
        <td>(optional) Since some Windows NT versions have the same values, this flag  determines if the Server flavor is returned or not. For instance Windows 8.1 and Windows Server 2012 R2 both use  version 6.3.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodwindowsVerToStr"></a>
	<h3>method windowsVerToStr <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2325">2325</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string windowsVerToStr(
string
$winVer)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Convert the Windows 3.x &amp; 9x family version numbers to the operating system name. For instance '4.10.1998'  returns 'Windows 98'.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>The operating system name or the constant PLATFORM_VERSION_UNKNOWN if nothing match the version  numbers.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$winVer</b>&nbsp;&nbsp;</td>
        <td>The Windows 3.x or 9x family version numbers as a string.</td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="methodwordPos"></a>
	<h3>method wordPos <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a2359">2359</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>mixed wordPos(
string
$haystack, string
$needle, [boolean
$insensitive = true], [int
$offset = 0], [
&$foundString = NULL], string
$foundString)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Find the position of the first occurrence of a word in a string.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns the position of the needle (int) if found, false otherwise. Warning this function may  return Boolean false, but may also return a non-Boolean value which evaluates to false.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>protected</td>
  </tr>
</table>
</div>
<br /><br />

	
        <h4>Parameters:</h4>
    <div class="tags">
    <table border="0" cellspacing="0" cellpadding="0">
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$haystack</b>&nbsp;&nbsp;</td>
        <td>The string to search in.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$needle</b>&nbsp;&nbsp;</td>
        <td>The string to search for.</td>
      </tr>
          <tr>
        <td class="type">boolean&nbsp;&nbsp;</td>
        <td><b>$insensitive</b>&nbsp;&nbsp;</td>
        <td>(optional) Determines if we do a case-sensitive search (false) or a case-insensitive  one (true).</td>
      </tr>
          <tr>
        <td class="type">int&nbsp;&nbsp;</td>
        <td><b>$offset</b>&nbsp;&nbsp;</td>
        <td>If specified, search will start this number of characters counted from the beginning of the  string. If the offset is negative, the search will start this number of characters counted from the end of the  string.</td>
      </tr>
          <tr>
        <td class="type">string&nbsp;&nbsp;</td>
        <td><b>$foundString</b>&nbsp;&nbsp;</td>
        <td>String buffer that will contain the exact matching needle found. Set to NULL when  return value of the function is false.</td>
      </tr>
          <tr>
        <td class="type">&nbsp;&nbsp;</td>
        <td><b>&$foundString</b>&nbsp;&nbsp;</td>
        <td></td>
      </tr>
        </table>
    </div><br />
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
  <hr />
	<a name="method__toString"></a>
	<h3>method __toString <span class="smalllinenumber">[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a341">341</a>]</span></h3>
	<div class="function">
    <table width="90%" border="0" cellspacing="0" cellpadding="1"><tr><td class="code_border">
    <table width="100%" border="0" cellspacing="0" cellpadding="2"><tr><td class="code">
		<code>string __toString(
)</code>
    </td></tr></table>
    </td></tr></table><br />
	
		Determine how the class will react when it is treated like a string.<br /><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>return:</b>&nbsp;&nbsp;</td><td>Returns an HTML formatted string with a summary of the browser informations.</td>
  </tr>
  <tr>
    <td><b>access:</b>&nbsp;&nbsp;</td><td>public</td>
  </tr>
</table>
</div>
<br /><br />

	
        <div class="top">[ <a href="#top">Top</a> ]</div>
  </div>
</div><br />

<hr />
<a name="class_consts"></a>
<h3>Class Constants</h3>
<div class="tags">
	<a name="constBROWSER_ANDROID"></a>
	<p></p>
	<h4>BROWSER_ANDROID = <span class="value">&nbsp;'Android'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a164">164</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_BLACKBERRY"></a>
	<p></p>
	<h4>BROWSER_BLACKBERRY = <span class="value">&nbsp;'BlackBerry'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a165">165</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_CHROME"></a>
	<p></p>
	<h4>BROWSER_CHROME = <span class="value">&nbsp;'Chrome'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a166">166</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_EDGE"></a>
	<p></p>
	<h4>BROWSER_EDGE = <span class="value">&nbsp;'Edge'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a167">167</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_FIREBIRD"></a>
	<p></p>
	<h4>BROWSER_FIREBIRD = <span class="value">&nbsp;'Firebird'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a168">168</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_FIREFOX"></a>
	<p></p>
	<h4>BROWSER_FIREFOX = <span class="value">&nbsp;'Firefox'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a169">169</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_ICAB"></a>
	<p></p>
	<h4>BROWSER_ICAB = <span class="value">&nbsp;'iCab'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a170">170</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_ICECAT"></a>
	<p></p>
	<h4>BROWSER_ICECAT = <span class="value">&nbsp;'GNU&nbsp;IceCat'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a171">171</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_ICEWEASEL"></a>
	<p></p>
	<h4>BROWSER_ICEWEASEL = <span class="value">&nbsp;'GNU&nbsp;IceWeasel'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a172">172</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_IE"></a>
	<p></p>
	<h4>BROWSER_IE = <span class="value">&nbsp;'Internet&nbsp;Explorer'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a173">173</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_IE_MOBILE"></a>
	<p></p>
	<h4>BROWSER_IE_MOBILE = <span class="value">&nbsp;'Internet&nbsp;Explorer&nbsp;Mobile'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a174">174</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_KONQUEROR"></a>
	<p></p>
	<h4>BROWSER_KONQUEROR = <span class="value">&nbsp;'Konqueror'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a175">175</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_LYNX"></a>
	<p></p>
	<h4>BROWSER_LYNX = <span class="value">&nbsp;'Lynx'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a176">176</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_MOZILLA"></a>
	<p></p>
	<h4>BROWSER_MOZILLA = <span class="value">&nbsp;'Mozilla'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a177">177</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_MSNTV"></a>
	<p></p>
	<h4>BROWSER_MSNTV = <span class="value">&nbsp;'MSN&nbsp;TV'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a178">178</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_NETSCAPE"></a>
	<p></p>
	<h4>BROWSER_NETSCAPE = <span class="value">&nbsp;'Netscape'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a179">179</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_NOKIA"></a>
	<p></p>
	<h4>BROWSER_NOKIA = <span class="value">&nbsp;'Nokia&nbsp;Browser'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a180">180</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_OPERA"></a>
	<p></p>
	<h4>BROWSER_OPERA = <span class="value">&nbsp;'Opera'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a181">181</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_OPERA_MINI"></a>
	<p></p>
	<h4>BROWSER_OPERA_MINI = <span class="value">&nbsp;'Opera&nbsp;Mini'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a182">182</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_OPERA_MOBILE"></a>
	<p></p>
	<h4>BROWSER_OPERA_MOBILE = <span class="value">&nbsp;'Opera&nbsp;Mobile'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a183">183</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_PHOENIX"></a>
	<p></p>
	<h4>BROWSER_PHOENIX = <span class="value">&nbsp;'Phoenix'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a184">184</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_SAFARI"></a>
	<p></p>
	<h4>BROWSER_SAFARI = <span class="value">&nbsp;'Safari'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a185">185</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_SAMSUNG"></a>
	<p></p>
	<h4>BROWSER_SAMSUNG = <span class="value">&nbsp;'Samsung&nbsp;Internet'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a186">186</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_TABLET_OS"></a>
	<p></p>
	<h4>BROWSER_TABLET_OS = <span class="value">&nbsp;'BlackBerry&nbsp;Tablet&nbsp;OS'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a187">187</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_UC"></a>
	<p></p>
	<h4>BROWSER_UC = <span class="value">&nbsp;'UC&nbsp;Browser'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a188">188</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constBROWSER_UNKNOWN"></a>
	<p></p>
	<h4>BROWSER_UNKNOWN = <span class="value">&nbsp;'unknown'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a189">189</a>]</p>
  Constant for the name of the Web browser.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_ANDROID"></a>
	<p></p>
	<h4>PLATFORM_ANDROID = <span class="value">&nbsp;'Android'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a195">195</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_BLACKBERRY"></a>
	<p></p>
	<h4>PLATFORM_BLACKBERRY = <span class="value">&nbsp;'BlackBerry'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a196">196</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_CHROME_OS"></a>
	<p></p>
	<h4>PLATFORM_CHROME_OS = <span class="value">&nbsp;'Chrome&nbsp;OS'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a197">197</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_FREEBSD"></a>
	<p></p>
	<h4>PLATFORM_FREEBSD = <span class="value">&nbsp;'FreeBSD'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a198">198</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_IOS"></a>
	<p></p>
	<h4>PLATFORM_IOS = <span class="value">&nbsp;'iOS'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a199">199</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_LINUX"></a>
	<p></p>
	<h4>PLATFORM_LINUX = <span class="value">&nbsp;'Linux'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a200">200</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_MACINTOSH"></a>
	<p></p>
	<h4>PLATFORM_MACINTOSH = <span class="value">&nbsp;'Macintosh'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a201">201</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_NETBSD"></a>
	<p></p>
	<h4>PLATFORM_NETBSD = <span class="value">&nbsp;'NetBSD'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a202">202</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_NOKIA"></a>
	<p></p>
	<h4>PLATFORM_NOKIA = <span class="value">&nbsp;'Nokia'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a203">203</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_OPENBSD"></a>
	<p></p>
	<h4>PLATFORM_OPENBSD = <span class="value">&nbsp;'OpenBSD'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a204">204</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_OPENSOLARIS"></a>
	<p></p>
	<h4>PLATFORM_OPENSOLARIS = <span class="value">&nbsp;'OpenSolaris'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a205">205</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_SYMBIAN"></a>
	<p></p>
	<h4>PLATFORM_SYMBIAN = <span class="value">&nbsp;'Symbian'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a206">206</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_UNKNOWN"></a>
	<p></p>
	<h4>PLATFORM_UNKNOWN = <span class="value">&nbsp;'unknown'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a207">207</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_VERSION_UNKNOWN"></a>
	<p></p>
	<h4>PLATFORM_VERSION_UNKNOWN = <span class="value">&nbsp;'unknown'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a208">208</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_WINDOWS"></a>
	<p></p>
	<h4>PLATFORM_WINDOWS = <span class="value">&nbsp;'Windows'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a209">209</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_WINDOWS_CE"></a>
	<p></p>
	<h4>PLATFORM_WINDOWS_CE = <span class="value">&nbsp;'Windows&nbsp;CE'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a210">210</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constPLATFORM_WINDOWS_PHONE"></a>
	<p></p>
	<h4>PLATFORM_WINDOWS_PHONE = <span class="value">&nbsp;'Windows&nbsp;Phone'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a211">211</a>]</p>
  Constant for the name of the platform on which the Web browser runs.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constROBOT_BINGBOT"></a>
	<p></p>
	<h4>ROBOT_BINGBOT = <span class="value">&nbsp;'Bingbot'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a217">217</a>]</p>
  Constant for the name of the robot.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constROBOT_GOOGLEBOT"></a>
	<p></p>
	<h4>ROBOT_GOOGLEBOT = <span class="value">&nbsp;'Googlebot'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a218">218</a>]</p>
  Constant for the name of the robot.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constROBOT_MSNBOT"></a>
	<p></p>
	<h4>ROBOT_MSNBOT = <span class="value">&nbsp;'MSNBot'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a219">219</a>]</p>
  Constant for the name of the robot.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constROBOT_SLURP"></a>
	<p></p>
	<h4>ROBOT_SLURP = <span class="value">&nbsp;'Yahoo!&nbsp;Slurp'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a220">220</a>]</p>
  Constant for the name of the robot.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constROBOT_UNKNOWN"></a>
	<p></p>
	<h4>ROBOT_UNKNOWN = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a221">221</a>]</p>
  Constant for the name of the robot.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constROBOT_VERSION_UNKNOWN"></a>
	<p></p>
	<h4>ROBOT_VERSION_UNKNOWN = <span class="value">&nbsp;''</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a222">222</a>]</p>
  Constant for the name of the robot.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constROBOT_W3CVALIDATOR"></a>
	<p></p>
	<h4>ROBOT_W3CVALIDATOR = <span class="value">&nbsp;'W3C&nbsp;Validator'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a223">223</a>]</p>
  Constant for the name of the robot.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constROBOT_YAHOO_MM"></a>
	<p></p>
	<h4>ROBOT_YAHOO_MM = <span class="value">&nbsp;'Yahoo!&nbsp;Multimedia'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a224">224</a>]</p>
  Constant for the name of the robot.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
	<a name="constVERSION_UNKNOWN"></a>
	<p></p>
	<h4>VERSION_UNKNOWN = <span class="value">&nbsp;'unknown'</span></h4>
	<p>[line <a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html#a230">230</a>]</p>
  Version unknown constant.<br /><br />
  <br />
	<div class="top">[ <a href="#top">Top</a> ]</div><br />
</div><br />

        <div class="credit">
		    <hr />
		    Documentation generated on Tue, 20 Sep 2022 23:35:16 -0400 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>