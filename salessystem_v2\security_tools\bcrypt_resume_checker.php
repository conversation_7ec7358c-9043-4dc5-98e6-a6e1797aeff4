<?php
/**
 * فاحص استئناف الاختبار
 * يتحقق من وجود اختبار سابق ويعرض خيار الاستئناف
 */

// فاحص استئناف الاختبار - متاح للجميع بدون تسجيل دخول
// تم إزالة متطلب تسجيل الدخول لسهولة الوصول

// تعيين header للاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

$action = $_GET['action'] ?? '';

switch ($action) {
    case 'check_resume':
        checkResume();
        break;
    case 'clear_progress':
        clearProgress();
        break;
    default:
        http_response_code(400);
        echo json_encode(['error' => 'إجراء غير صحيح']);
        exit;
}

/**
 * فحص إمكانية الاستئناف
 */
function checkResume() {
    // البحث عن أي ملف تقدم موجود
    $progressFiles = glob(sys_get_temp_dir() . '/bcrypt_progress_*.json');
    $progressFile = !empty($progressFiles) ? $progressFiles[0] : sys_get_temp_dir() . '/bcrypt_progress_general.json';
    
    if (!file_exists($progressFile)) {
        echo json_encode([
            'can_resume' => false,
            'message' => 'لا يوجد اختبار سابق'
        ]);
        return;
    }
    
    $progressData = json_decode(file_get_contents($progressFile), true);
    
    if (!$progressData) {
        echo json_encode([
            'can_resume' => false,
            'message' => 'خطأ في قراءة بيانات الاختبار السابق'
        ]);
        return;
    }
    
    // فحص إذا كان الاختبار قديم (أكثر من 24 ساعة)
    if (time() - $progressData['timestamp'] > 86400) {
        echo json_encode([
            'can_resume' => false,
            'message' => 'الاختبار السابق قديم جداً (أكثر من 24 ساعة)'
        ]);
        return;
    }
    
    // فحص إذا كان الاختبار مكتمل
    if ($progressData['attempts'] >= $progressData['max_attempts']) {
        echo json_encode([
            'can_resume' => false,
            'message' => 'الاختبار السابق مكتمل'
        ]);
        return;
    }
    
    echo json_encode([
        'can_resume' => true,
        'progress_data' => $progressData,
        'message' => 'يمكن استئناف الاختبار السابق'
    ]);
}

/**
 * مسح بيانات التقدم
 */
function clearProgress() {
    // مسح جميع ملفات التقدم والإيقاف
    $progressFiles = glob(sys_get_temp_dir() . '/bcrypt_progress_*.json');
    $stopFile = sys_get_temp_dir() . '/bcrypt_stop_general.flag';
    
    // مسح جميع ملفات التقدم
    foreach ($progressFiles as $progressFile) {
        if (file_exists($progressFile)) {
            unlink($progressFile);
        }
    }
    
    if (file_exists($stopFile)) {
        unlink($stopFile);
    }
    
    echo json_encode([
        'status' => 'cleared',
        'message' => 'تم مسح بيانات الاختبار السابق'
    ]);
}
?>
