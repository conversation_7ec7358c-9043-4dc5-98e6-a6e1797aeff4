<?php
/**
 * جلب بيانات عميل/مورد للتعديل
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من وجود معرف العميل/المورد
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف العميل/المورد غير صحيح']);
    exit();
}

$customer_id = intval($_GET['id']);

// الحصول على اتصال قاعدة البيانات
$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    echo json_encode(['success' => false, 'message' => 'فشل الاتصال بقاعدة البيانات']);
    exit();
}

try {
    // جلب بيانات العميل/المورد
    $stmt = $db->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->bind_param("i", $customer_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'العميل/المورد غير موجود']);
        exit();
    }
    
    $customer = $result->fetch_assoc();
    $stmt->close();
    
    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'customer' => [
            'id' => $customer['id'],
            'name' => $customer['name'],
            'phone' => $customer['phone'],
            'email' => $customer['email'],
            'tax_number' => $customer['tax_number'],
            'address' => $customer['address'],
            'customer_type' => $customer['customer_type']
        ]
    ]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
} finally {
    if (isset($db)) {
        $db->close();
    }
}
?>
