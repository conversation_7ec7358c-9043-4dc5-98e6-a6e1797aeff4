<?php
// تكوين اتصال قاعدة البيانات المحسن - النسخة الثانية
define('MAIN_DB_HOST', 'localhost');
define('MAIN_DB_USER', 'sales01');
define('MAIN_DB_PASS', 'dNz35nd5@');
define('MAIN_DB_NAME', 'u193708811_system_main');

// تكوين قاعدة البيانات الثانوية للعمليات
define('OPERATIONS_DB_HOST', 'localhost');
define('OPERATIONS_DB_USER', 'sales02');
define('OPERATIONS_DB_PASS', 'dNz35nd5@');
define('OPERATIONS_DB_NAME', 'u193708811_operations');

// ملاحظة: تم إزالة الإنشاء التلقائي لقواعد البيانات
// يجب إنشاء قواعد البيانات يدوياً أو من خلال لوحة التحكم

// محاولة الاتصال بقاعدة البيانات الرئيسية مع معالجة الأخطاء
$main_db = null;
try {
    $main_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, MAIN_DB_NAME);
    $main_db->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
    $main_db->set_charset("utf8mb4");

    if ($main_db->connect_error) {
        throw new Exception("خطأ في الاتصال: " . $main_db->connect_error);
    }
} catch (Exception $e) {
    // في حالة عدم وجود قاعدة البيانات، عرض رسالة مفيدة
    $error_message = "
    <div style='font-family: Arial, sans-serif; direction: rtl; text-align: center; padding: 50px; background: #f8f9fa;'>
        <div style='max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);'>
            <h2 style='color: #dc3545; margin-bottom: 20px;'>⚠️ قاعدة البيانات الرئيسية غير موجودة</h2>
            <p style='font-size: 16px; margin-bottom: 20px;'>قاعدة البيانات <strong>" . MAIN_DB_NAME . "</strong> غير موجودة.</p>
            <div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>
                <h4>يجب إنشاء قاعدة البيانات يدوياً:</h4>
                <p><strong>اسم قاعدة البيانات:</strong> " . MAIN_DB_NAME . "</p>
                <p><strong>المستخدم:</strong> " . MAIN_DB_USER . "</p>
                <p><strong>أمر SQL:</strong></p>
                <code style='background: #e9ecef; padding: 10px; display: block; border-radius: 3px;'>
                    CREATE DATABASE IF NOT EXISTS `" . MAIN_DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
                </code>
            </div>
            <div style='margin-top: 20px;'>
                <a href='DATABASE_SETUP.md' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>📖 دليل الإعداد</a>
                <a href='test_connection.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>🔍 اختبار الاتصال</a>
            </div>
        </div>
    </div>";

    die($error_message);
}

// إنشاء اتصال بقاعدة بيانات العمليات
$operations_db = new mysqli(OPERATIONS_DB_HOST, OPERATIONS_DB_USER, OPERATIONS_DB_PASS, OPERATIONS_DB_NAME);
$operations_db->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
$operations_db->set_charset("utf8mb4");

if ($operations_db->connect_error) {
    die("خطأ في الاتصال بقاعدة بيانات العمليات: " . $operations_db->connect_error);
}

// دالة للحصول على اتصال بقاعدة بيانات العمليات
function getOperationsDB() {
    global $operations_db;
    return $operations_db;
}

// دالة للحصول على بادئة جداول المستخدم
function getUserTablePrefix($username = null) {
    if ($username === null && isset($_SESSION['username'])) {
        $username = $_SESSION['username'];
    }

    if (!$username) {
        return null;
    }

    // تنظيف اسم المستخدم لاستخدامه كبادئة
    $clean_username = preg_replace('/[^a-zA-Z0-9_]/', '_', $username);
    return strtolower($clean_username) . '_';
}

// دالة للحصول على اسم الجدول مع بادئة المستخدم
function getUserTableName($base_table_name, $username = null) {
    $prefix = getUserTablePrefix($username);
    if (!$prefix) {
        return null;
    }
    return $prefix . $base_table_name;
}

// دالة لإنشاء جداول المستخدم تلقائياً
function createUserTables($username) {
    $operations_db = getOperationsDB();
    $prefix = getUserTablePrefix($username);

    if (!$prefix || !$operations_db) {
        return false;
    }

    // قائمة الجداول الأساسية مع هياكلها
    $tables = [
        'customers' => "CREATE TABLE IF NOT EXISTS `{$prefix}customers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `email` varchar(255) DEFAULT NULL,
            `tax_number` varchar(50) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_email` (`email`),
            KEY `idx_phone` (`phone`),
            KEY `idx_customer_type` (`customer_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'products' => "CREATE TABLE IF NOT EXISTS `{$prefix}products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
            `category` varchar(100) DEFAULT NULL,
            `stock_quantity` decimal(10,2) DEFAULT 0.00,
            `unit` varchar(50) DEFAULT 'قطعة',
            `barcode` varchar(100) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_category` (`category`),
            KEY `idx_barcode` (`barcode`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'sales' => "CREATE TABLE IF NOT EXISTS `{$prefix}sales` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_date` (`date`),
            KEY `idx_payment_status` (`payment_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'purchases' => "CREATE TABLE IF NOT EXISTS `{$prefix}purchases` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `supplier_name` varchar(255) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_date` (`date`),
            KEY `idx_payment_status` (`payment_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'sale_items' => "CREATE TABLE IF NOT EXISTS `{$prefix}sale_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `sale_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `product_name` varchar(255) NOT NULL DEFAULT '',
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_sale_id` (`sale_id`),
            KEY `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'purchase_items' => "CREATE TABLE IF NOT EXISTS `{$prefix}purchase_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `purchase_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `product_name` varchar(255) NOT NULL DEFAULT '',
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_purchase_id` (`purchase_id`),
            KEY `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci"
    ];

    // إنشاء الجداول
    foreach ($tables as $table_name => $create_sql) {
        if (!$operations_db->query($create_sql)) {
            error_log("خطأ في إنشاء جدول {$prefix}{$table_name}: " . $operations_db->error);
            return false;
        }
    }

    return true;
}

// دالة للتوافق مع النظام القديم
function getUserDBConnection($user_id) {
    // إرجاع اتصال قاعدة بيانات العمليات
    return getOperationsDB();
}
?>