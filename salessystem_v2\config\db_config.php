<?php
// تكوين اتصال قاعدة البيانات المحسن - النسخة الثانية
define('MAIN_DB_HOST', 'localhost');
define('MAIN_DB_USER', 'sales01');
define('MAIN_DB_PASS', 'dNz35nd5@');
define('MAIN_DB_NAME', 'u193708811_system_main');

// تكوين قاعدة البيانات الثانوية للعمليات
define('OPERATIONS_DB_HOST', 'localhost');
define('OPERATIONS_DB_USER', 'sales02');
define('OPERATIONS_DB_PASS', 'dNz35nd5@');
define('OPERATIONS_DB_NAME', 'u193708811_operations');

// إنشاء قواعد البيانات الأساسية تلقائياً
function createMainDatabases() {
    // الاتصال بالخادم بدون تحديد قاعدة بيانات
    $temp_connection = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS);

    if ($temp_connection->connect_error) {
        die("خطأ في الاتصال بخادم قاعدة البيانات: " . $temp_connection->connect_error);
    }

    $temp_connection->set_charset("utf8mb4");

    // التحقق من وجود قاعدة البيانات الرئيسية (موجودة مسبقاً)
    $check_main_db = $temp_connection->query("SHOW DATABASES LIKE '" . MAIN_DB_NAME . "'");
    if (!$check_main_db || $check_main_db->num_rows == 0) {
        // إنشاء قاعدة البيانات الرئيسية إذا لم تكن موجودة
        $create_main_db_sql = "CREATE DATABASE IF NOT EXISTS `" . MAIN_DB_NAME . "`
                              CHARACTER SET utf8mb4
                              COLLATE utf8mb4_general_ci";

        if (!$temp_connection->query($create_main_db_sql)) {
            die("خطأ في إنشاء قاعدة البيانات الرئيسية: " . $temp_connection->error);
        }
    }

    // إنشاء قاعدة البيانات الثانوية (العمليات والجداول المشتركة)
    $create_operations_db_sql = "CREATE DATABASE IF NOT EXISTS `" . OPERATIONS_DB_NAME . "`
                                CHARACTER SET utf8mb4
                                COLLATE utf8mb4_general_ci";

    if (!$temp_connection->query($create_operations_db_sql)) {
        die("خطأ في إنشاء قاعدة بيانات العمليات: " . $temp_connection->error);
    }

    $temp_connection->close();
}

// إنشاء قواعد البيانات الأساسية
createMainDatabases();

// إنشاء اتصال بقاعدة البيانات الرئيسية
$main_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, MAIN_DB_NAME);
$main_db->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
$main_db->set_charset("utf8mb4");

if ($main_db->connect_error) {
    die("خطأ في الاتصال بقاعدة البيانات الرئيسية: " . $main_db->connect_error);
}

// إنشاء اتصال بقاعدة بيانات العمليات
$operations_db = new mysqli(OPERATIONS_DB_HOST, OPERATIONS_DB_USER, OPERATIONS_DB_PASS, OPERATIONS_DB_NAME);
$operations_db->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
$operations_db->set_charset("utf8mb4");

if ($operations_db->connect_error) {
    die("خطأ في الاتصال بقاعدة بيانات العمليات: " . $operations_db->connect_error);
}

// دالة للحصول على اتصال بقاعدة بيانات العمليات
function getOperationsDB() {
    global $operations_db;
    return $operations_db;
}

// دالة للحصول على بادئة جداول المستخدم
function getUserTablePrefix($username = null) {
    if ($username === null && isset($_SESSION['username'])) {
        $username = $_SESSION['username'];
    }

    if (!$username) {
        return null;
    }

    // تنظيف اسم المستخدم لاستخدامه كبادئة
    $clean_username = preg_replace('/[^a-zA-Z0-9_]/', '_', $username);
    return strtolower($clean_username) . '_';
}

// دالة للحصول على اسم الجدول مع بادئة المستخدم
function getUserTableName($base_table_name, $username = null) {
    $prefix = getUserTablePrefix($username);
    if (!$prefix) {
        return null;
    }
    return $prefix . $base_table_name;
}

// دالة لإنشاء جداول المستخدم تلقائياً
function createUserTables($username) {
    $operations_db = getOperationsDB();
    $prefix = getUserTablePrefix($username);

    if (!$prefix || !$operations_db) {
        return false;
    }

    // قائمة الجداول الأساسية مع هياكلها
    $tables = [
        'customers' => "CREATE TABLE IF NOT EXISTS `{$prefix}customers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `email` varchar(255) DEFAULT NULL,
            `tax_number` varchar(50) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_email` (`email`),
            KEY `idx_phone` (`phone`),
            KEY `idx_customer_type` (`customer_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'products' => "CREATE TABLE IF NOT EXISTS `{$prefix}products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
            `category` varchar(100) DEFAULT NULL,
            `stock_quantity` decimal(10,2) DEFAULT 0.00,
            `unit` varchar(50) DEFAULT 'قطعة',
            `barcode` varchar(100) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_category` (`category`),
            KEY `idx_barcode` (`barcode`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'sales' => "CREATE TABLE IF NOT EXISTS `{$prefix}sales` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_date` (`date`),
            KEY `idx_payment_status` (`payment_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'purchases' => "CREATE TABLE IF NOT EXISTS `{$prefix}purchases` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `supplier_name` varchar(255) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_date` (`date`),
            KEY `idx_payment_status` (`payment_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'sale_items' => "CREATE TABLE IF NOT EXISTS `{$prefix}sale_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `sale_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `product_name` varchar(255) NOT NULL DEFAULT '',
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_sale_id` (`sale_id`),
            KEY `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'purchase_items' => "CREATE TABLE IF NOT EXISTS `{$prefix}purchase_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `purchase_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `product_name` varchar(255) NOT NULL DEFAULT '',
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_purchase_id` (`purchase_id`),
            KEY `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci"
    ];

    // إنشاء الجداول
    foreach ($tables as $table_name => $create_sql) {
        if (!$operations_db->query($create_sql)) {
            error_log("خطأ في إنشاء جدول {$prefix}{$table_name}: " . $operations_db->error);
            return false;
        }
    }

    return true;
}

// دالة للحصول على اتصال قاعدة البيانات للمستخدم الحالي (محسنة)
function getCurrentUserDB() {
    // إرجاع اتصال قاعدة بيانات العمليات مباشرة
    return getOperationsDB();
}

// دالة للتوافق مع النظام القديم
function getUserDBConnection($user_id) {
    // إرجاع اتصال قاعدة بيانات العمليات
    return getOperationsDB();
}
?>