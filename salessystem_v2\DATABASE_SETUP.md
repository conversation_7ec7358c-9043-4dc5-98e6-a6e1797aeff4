# دليل إعداد قواعد البيانات - salessystem_v2

## نظرة عامة
هذا الدليل يوضح كيفية إعداد قواعد البيانات المطلوبة للنظام يدوياً.

## ⚠️ تنبيه مهم
تم إزالة الإنشاء التلقائي لقواعد البيانات من النظام. يجب إنشاء قواعد البيانات يدوياً قبل استخدام النظام.

## قواعد البيانات المطلوبة

### 1. قاعدة البيانات الرئيسية
- **الاسم:** `u193708811_system_main`
- **المستخدم:** `sales01`
- **كلمة المرور:** `dNz35nd5@`
- **الحالة:** ✅ موجودة مسبقاً

### 2. قاعدة بيانات العمليات
- **الاسم:** `u193708811_operations`
- **المستخدم:** `sales02`
- **كلمة المرور:** `dNz35nd5@`
- **الحالة:** ❌ يجب إنشاؤها يدوياً

## طرق إنشاء قاعدة بيانات العمليات

### الطريقة الأولى: من خلال phpMyAdmin

#### 1. الدخول إلى phpMyAdmin:
```
http://localhost/phpmyadmin
```

#### 2. تسجيل الدخول:
- **اسم المستخدم:** `sales02`
- **كلمة المرور:** `dNz35nd5@`

#### 3. إنشاء قاعدة البيانات:
1. انقر على "قواعد البيانات" (Databases)
2. في حقل "إنشاء قاعدة بيانات" اكتب: `u193708811_operations`
3. اختر الترميز: `utf8mb4_general_ci`
4. انقر على "إنشاء" (Create)

### الطريقة الثانية: من خلال أوامر SQL

#### 1. الدخول إلى MySQL:
```bash
mysql -u sales02 -p
```

#### 2. تنفيذ أمر الإنشاء:
```sql
CREATE DATABASE IF NOT EXISTS `u193708811_operations` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_general_ci;
```

#### 3. التحقق من الإنشاء:
```sql
SHOW DATABASES LIKE 'u193708811_operations';
USE u193708811_operations;
```

### الطريقة الثالثة: من خلال لوحة تحكم الاستضافة

#### 1. الدخول إلى cPanel:
- اذهب إلى قسم "قواعد البيانات" (Databases)
- انقر على "MySQL Databases"

#### 2. إنشاء قاعدة البيانات:
- في حقل "Create New Database" اكتب: `operations`
- انقر على "Create Database"
- ستصبح قاعدة البيانات: `u193708811_operations`

#### 3. ربط المستخدم:
- في قسم "Add User To Database"
- اختر المستخدم: `sales02`
- اختر قاعدة البيانات: `u193708811_operations`
- امنح جميع الصلاحيات (ALL PRIVILEGES)

## التحقق من الإعداد

### 1. اختبار الاتصال:
```
http://localhost:808/salessystem_v2/test_connection.php
```

### 2. النتائج المتوقعة:
- ✅ **قاعدة البيانات الرئيسية:** متصلة بنجاح
- ✅ **قاعدة بيانات العمليات:** متصلة بنجاح
- ✅ **صلاحيات المستخدمين:** صحيحة

### 3. في حالة وجود مشاكل:
- تحقق من اسم قاعدة البيانات
- تحقق من بيانات المستخدم
- تحقق من الصلاحيات

## الصلاحيات المطلوبة

### للمستخدم sales01 (قاعدة البيانات الرئيسية):
```sql
GRANT SELECT, INSERT, UPDATE, DELETE ON u193708811_system_main.* TO 'sales01'@'localhost';
GRANT CREATE TEMPORARY TABLES ON u193708811_system_main.* TO 'sales01'@'localhost';
FLUSH PRIVILEGES;
```

### للمستخدم sales02 (قاعدة بيانات العمليات):
```sql
GRANT ALL PRIVILEGES ON u193708811_operations.* TO 'sales02'@'localhost';
FLUSH PRIVILEGES;
```

## إنشاء الجداول التلقائي

### ✅ ما يتم تلقائياً:
بعد إنشاء قواعد البيانات، سيقوم النظام تلقائياً بـ:

#### 1. إنشاء جداول المستخدمين الجدد:
```sql
-- عند تسجيل مستخدم جديد باسم "ahmed"
ahmed_customers      -- عملاء أحمد
ahmed_products       -- منتجات أحمد
ahmed_sales          -- مبيعات أحمد
ahmed_purchases      -- مشتريات أحمد
ahmed_sale_items     -- عناصر مبيعات أحمد
ahmed_purchase_items -- عناصر مشتريات أحمد
```

#### 2. إنشاء جداول النظام الأساسية:
```sql
-- في قاعدة البيانات الرئيسية (إذا لم تكن موجودة)
users           -- المستخدمون
admins          -- المديرون
activity_log    -- سجل العمليات
```

### ❌ ما لا يتم تلقائياً:
- إنشاء قواعد البيانات نفسها
- إنشاء المستخدمين
- منح الصلاحيات

## خطوات ما بعد الإعداد

### 1. تشغيل اختبار النظام:
```
http://localhost:808/salessystem_v2/test_system.php
```

### 2. تشغيل تحديث قاعدة البيانات:
```
http://localhost:808/salessystem_v2/update_database.php
```

### 3. تسجيل مستخدم تجريبي:
```
http://localhost:808/salessystem_v2/register.php
```

### 4. التحقق من إنشاء الجداول:
- سجل دخول المستخدم الجديد
- تحقق من إنشاء الجداول بالبادئة الصحيحة
- اختبر العمليات الأساسية

## استكشاف الأخطاء

### خطأ: قاعدة البيانات غير موجودة
```
Unknown database 'u193708811_operations'
```
**الحل:** أنشئ قاعدة البيانات باستخدام إحدى الطرق المذكورة أعلاه

### خطأ: رفض الوصول
```
Access denied for user 'sales02'@'localhost'
```
**الحل:** تحقق من:
- اسم المستخدم وكلمة المرور
- وجود المستخدم في النظام
- صلاحيات المستخدم

### خطأ: لا يمكن الاتصال
```
Can't connect to MySQL server
```
**الحل:** تحقق من:
- تشغيل خدمة MySQL
- إعدادات الشبكة
- جدار الحماية

## أوامر SQL مفيدة

### فحص قواعد البيانات الموجودة:
```sql
SHOW DATABASES;
```

### فحص المستخدمين:
```sql
SELECT User, Host FROM mysql.user WHERE User LIKE 'sales%';
```

### فحص الصلاحيات:
```sql
SHOW GRANTS FOR 'sales01'@'localhost';
SHOW GRANTS FOR 'sales02'@'localhost';
```

### فحص الجداول:
```sql
USE u193708811_operations;
SHOW TABLES;
```

## النسخ الاحتياطي

### إنشاء نسخة احتياطية:
```bash
# قاعدة البيانات الرئيسية
mysqldump -u sales01 -p u193708811_system_main > backup_main.sql

# قاعدة بيانات العمليات
mysqldump -u sales02 -p u193708811_operations > backup_operations.sql
```

### استعادة النسخة الاحتياطية:
```bash
# قاعدة البيانات الرئيسية
mysql -u sales01 -p u193708811_system_main < backup_main.sql

# قاعدة بيانات العمليات
mysql -u sales02 -p u193708811_operations < backup_operations.sql
```

## الدعم الفني

### في حالة وجود مشاكل:
1. **راجع هذا الدليل** للتأكد من اتباع الخطوات الصحيحة
2. **استخدم أدوات الاختبار** المتوفرة في النظام
3. **تحقق من ملفات السجل** للأخطاء
4. **اعمل نسخة احتياطية** قبل أي تغيير

### ملفات مهمة للمراجعة:
- `config/db_config.php` - إعدادات قاعدة البيانات
- `test_connection.php` - اختبار الاتصال
- `test_system.php` - اختبار النظام الكامل

---

**تاريخ آخر تحديث:** 2024-12-19  
**الإصدار:** 2.0  
**المطور:** نظام المبيعات المحسن
