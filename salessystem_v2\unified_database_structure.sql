-- ===================================================================
-- هيكل قاعدة البيانات الموحدة لمشروع salessystem_v2
-- قاعدة بيانات واحدة تحتوي على جميع الجداول
-- الفصل بين المستخدمين باستخدام user_id
-- تاريخ الإنشاء: 2024-12-19
-- ===================================================================

-- إنشاء قاعدة البيانات الموحدة
CREATE DATABASE IF NOT EXISTS `u193708811_system_main` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- استخدام قاعدة البيانات
USE `u193708811_system_main`;

-- ===================================================================
-- جداول النظام الأساسية
-- ===================================================================

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL UNIQUE,
    `email` varchar(100) NOT NULL UNIQUE,
    `password` varchar(255) NOT NULL,
    `full_name` varchar(100) DEFAULT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `status` enum('active','inactive','suspended') DEFAULT 'active',
    `last_login` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_username` (`username`),
    KEY `idx_email` (`email`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المدراء
CREATE TABLE IF NOT EXISTS `admins` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL UNIQUE,
    `email` varchar(100) NOT NULL UNIQUE,
    `password` varchar(255) NOT NULL,
    `full_name` varchar(100) DEFAULT NULL,
    `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
    `permissions` text DEFAULT NULL,
    `status` enum('active','inactive') DEFAULT 'active',
    `last_login` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_username` (`username`),
    KEY `idx_email` (`email`),
    KEY `idx_role` (`role`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول سجل النشاطات
CREATE TABLE IF NOT EXISTS `activity_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `user_type` enum('user','admin') DEFAULT 'user',
    `action` varchar(100) NOT NULL,
    `table_name` varchar(50) DEFAULT NULL,
    `record_id` int(11) DEFAULT NULL,
    `old_data` text DEFAULT NULL,
    `new_data` text DEFAULT NULL,
    `description` text DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_user_type` (`user_type`),
    KEY `idx_action` (`action`),
    KEY `idx_table_name` (`table_name`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ===================================================================
-- جداول العمليات التجارية (مع user_id للفصل)
-- ===================================================================

-- جدول العملاء والموردين
CREATE TABLE IF NOT EXISTS `customers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `name` varchar(255) NOT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `tax_number` varchar(50) DEFAULT NULL,
    `address` text DEFAULT NULL,
    `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_name` (`name`),
    KEY `idx_email` (`email`),
    KEY `idx_phone` (`phone`),
    KEY `idx_customer_type` (`customer_type`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المنتجات
CREATE TABLE IF NOT EXISTS `products` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `name` varchar(255) NOT NULL,
    `description` text DEFAULT NULL,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
    `category` varchar(100) DEFAULT NULL,
    `stock_quantity` decimal(10,2) DEFAULT 0.00,
    `unit` varchar(50) DEFAULT 'قطعة',
    `barcode` varchar(100) DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_name` (`name`),
    KEY `idx_category` (`category`),
    KEY `idx_barcode` (`barcode`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول فواتير المبيعات
CREATE TABLE IF NOT EXISTS `sales` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `invoice_number` varchar(50) NOT NULL,
    `date` date NOT NULL,
    `customer_id` int(11) DEFAULT NULL,
    `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
    `notes` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    UNIQUE KEY `idx_invoice_number` (`invoice_number`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_date` (`date`),
    KEY `idx_payment_status` (`payment_status`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول فواتير المشتريات
CREATE TABLE IF NOT EXISTS `purchases` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `invoice_number` varchar(50) NOT NULL,
    `date` date NOT NULL,
    `customer_id` int(11) DEFAULT NULL,
    `supplier_name` varchar(255) DEFAULT NULL,
    `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
    `notes` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    UNIQUE KEY `idx_invoice_number` (`invoice_number`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_date` (`date`),
    KEY `idx_payment_status` (`payment_status`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول عناصر فواتير المبيعات
CREATE TABLE IF NOT EXISTS `sale_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `sale_id` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `product_name` varchar(255) NOT NULL DEFAULT '',
    `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
    `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_sale_id` (`sale_id`),
    KEY `idx_product_id` (`product_id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`sale_id`) REFERENCES `sales`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول عناصر فواتير المشتريات
CREATE TABLE IF NOT EXISTS `purchase_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `purchase_id` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `product_name` varchar(255) NOT NULL DEFAULT '',
    `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
    `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_purchase_id` (`purchase_id`),
    KEY `idx_product_id` (`product_id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`purchase_id`) REFERENCES `purchases`(`id`) ON DELETE CASCADE,
    FOREIGN KEY (`product_id`) REFERENCES `products`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ===================================================================
-- بيانات تجريبية
-- ===================================================================

-- إدراج مدير افتراضي
INSERT INTO `admins` (`username`, `email`, `password`, `full_name`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المدير العام', 'super_admin', 'active')
ON DUPLICATE KEY UPDATE `username` = `username`;

-- إدراج مستخدم تجريبي
INSERT INTO `users` (`username`, `email`, `password`, `full_name`, `phone`, `status`) VALUES
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مستخدم تجريبي', '0500000000', 'active')
ON DUPLICATE KEY UPDATE `username` = `username`;

-- إدراج عملاء تجريبيين للمستخدم التجريبي (user_id = 1)
INSERT INTO `customers` (`user_id`, `name`, `phone`, `email`, `customer_type`) VALUES
(1, 'عميل تجريبي 1', '0500000001', '<EMAIL>', 'customer'),
(1, 'عميل تجريبي 2', '0500000002', '<EMAIL>', 'customer'),
(1, 'مورد تجريبي 1', '0500000003', '<EMAIL>', 'supplier'),
(1, 'مورد تجريبي 2', '0500000004', '<EMAIL>', 'supplier')
ON DUPLICATE KEY UPDATE `name` = `name`;

-- إدراج منتجات تجريبية للمستخدم التجريبي (user_id = 1)
INSERT INTO `products` (`user_id`, `name`, `description`, `price`, `tax_rate`, `category`) VALUES
(1, 'منتج تجريبي 1', 'وصف المنتج التجريبي الأول', 100.00, 15.00, 'إلكترونيات'),
(1, 'منتج تجريبي 2', 'وصف المنتج التجريبي الثاني', 250.00, 15.00, 'أجهزة'),
(1, 'منتج تجريبي 3', 'وصف المنتج التجريبي الثالث', 75.50, 15.00, 'اكسسوارات'),
(1, 'منتج تجريبي 4', 'وصف المنتج التجريبي الرابع', 500.00, 15.00, 'أجهزة')
ON DUPLICATE KEY UPDATE `name` = `name`;

-- ===================================================================
-- ملاحظات مهمة
-- ===================================================================

/*
1. النظام الموحد:
   - قاعدة بيانات واحدة: u193708811_system_main
   - جميع الجداول في نفس القاعدة
   - الفصل بين المستخدمين باستخدام user_id

2. المزايا:
   - سهولة الإدارة والصيانة
   - تقليل تعقيد الاتصالات
   - إمكانية استخدام Foreign Keys
   - تحسين الأداء

3. الأمان:
   - جميع الاستعلامات تتضمن فلترة user_id
   - استخدام Foreign Keys للحفاظ على سلامة البيانات
   - فهارس محسنة للأداء

4. كلمة المرور الافتراضية:
   - للمدير والمستخدم التجريبي: "password"
   - يُنصح بتغييرها فور الاستخدام

5. معلومات الاتصال:
   - المضيف: localhost
   - قاعدة البيانات: u193708811_system_main
   - المستخدم: sales01
   - كلمة المرور: dNz35nd5@

6. استعلامات مفيدة:
   -- عرض جميع العملاء لمستخدم محدد
   SELECT * FROM customers WHERE user_id = 1;
   
   -- عرض جميع المبيعات مع تفاصيل العميل
   SELECT s.*, c.name as customer_name 
   FROM sales s 
   LEFT JOIN customers c ON s.customer_id = c.id AND c.user_id = s.user_id 
   WHERE s.user_id = 1;
   
   -- إحصائيات المبيعات لمستخدم محدد
   SELECT 
       COUNT(*) as total_invoices,
       SUM(subtotal) as total_subtotal,
       SUM(tax_amount) as total_tax,
       SUM(total_amount) as total_amount
   FROM sales 
   WHERE user_id = 1;
*/
