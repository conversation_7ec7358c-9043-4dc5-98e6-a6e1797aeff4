<?php
/**
 * ملف لاختبار ميزة إضافة العميل الجديد من صفحات الفواتير
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

$test_results = [];

// اختبار 1: التحقق من وجود ملف ajax_handler.php
$test_results['ajax_handler_exists'] = file_exists(__DIR__ . '/ajax_handler.php');

// اختبار 2: التحقق من وجود جدول العملاء
$check_customers_table = $db->query("SHOW TABLES LIKE 'customers'");
$test_results['customers_table_exists'] = ($check_customers_table && $check_customers_table->num_rows > 0);

// اختبار 3: التحقق من بنية جدول العملاء
$test_results['customers_table_structure'] = [];
if ($test_results['customers_table_exists']) {
    $structure = $db->query("DESCRIBE customers");
    $columns = [];
    while ($row = $structure->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    $required_columns = ['id', 'name', 'phone', 'email', 'address'];
    foreach ($required_columns as $column) {
        $test_results['customers_table_structure'][$column] = in_array($column, $columns);
    }
}

// اختبار 4: عدد العملاء الحالي
if ($test_results['customers_table_exists']) {
    $count_result = $db->query("SELECT COUNT(*) as count FROM customers");
    $test_results['customers_count'] = $count_result ? $count_result->fetch_assoc()['count'] : 0;
} else {
    $test_results['customers_count'] = 0;
}

// اختبار 5: التحقق من وجود الملفات المحدثة
$updated_files = [
    'add_purchase.php' => 'صفحة إضافة المشتريات',
    'add_sale.php' => 'صفحة إضافة المبيعات'
];

$test_results['updated_files'] = [];
foreach ($updated_files as $file => $description) {
    $file_path = __DIR__ . '/' . $file;
    $test_results['updated_files'][$file] = [
        'exists' => file_exists($file_path),
        'description' => $description,
        'has_customer_modal' => false,
        'has_customer_js' => false
    ];
    
    if (file_exists($file_path)) {
        $content = file_get_contents($file_path);
        $test_results['updated_files'][$file]['has_customer_modal'] = strpos($content, 'addCustomerModal') !== false;
        $test_results['updated_files'][$file]['has_customer_js'] = strpos($content, 'saveNewCustomer') !== false;
    }
}

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار ميزة إضافة العميل الجديد</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات الميزة</h5>
        <p>تم إضافة ميزة جديدة تسمح بإضافة عميل جديد مباشرة من صفحات إضافة الفواتير (المشتريات والمبيعات) دون الحاجة للانتقال إلى صفحة العملاء.</p>
        <ul>
            <li>خيار "إضافة عميل جديد" في قائمة العملاء</li>
            <li>نافذة منبثقة لإدخال بيانات العميل</li>
            <li>إضافة العميل تلقائياً إلى قاعدة البيانات</li>
            <li>تحديث قائمة العملاء فوراً</li>
        </ul>
    </div>
    
    <div class="row">
        <!-- اختبار الملفات والبنية -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header <?php echo $test_results['ajax_handler_exists'] ? 'bg-success text-white' : 'bg-danger text-white'; ?>">
                    <h5 class="mb-0">
                        <i class="fas <?php echo $test_results['ajax_handler_exists'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        ملف معالج AJAX
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($test_results['ajax_handler_exists']): ?>
                        <p class="text-success">✅ ملف ajax_handler.php موجود</p>
                        <small class="text-muted">يتعامل مع طلبات إضافة العملاء والمنتجات</small>
                    <?php else: ?>
                        <p class="text-danger">❌ ملف ajax_handler.php غير موجود</p>
                        <small class="text-muted">مطلوب لمعالجة طلبات إضافة العملاء</small>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header <?php echo $test_results['customers_table_exists'] ? 'bg-success text-white' : 'bg-danger text-white'; ?>">
                    <h5 class="mb-0">
                        <i class="fas <?php echo $test_results['customers_table_exists'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        جدول العملاء
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($test_results['customers_table_exists']): ?>
                        <p class="text-success">✅ جدول العملاء موجود</p>
                        <p><strong>عدد العملاء الحالي:</strong> <?php echo $test_results['customers_count']; ?></p>
                        
                        <?php if (!empty($test_results['customers_table_structure'])): ?>
                        <h6>بنية الجدول:</h6>
                        <ul class="list-unstyled">
                            <?php foreach ($test_results['customers_table_structure'] as $column => $exists): ?>
                            <li>
                                <i class="fas <?php echo $exists ? 'fa-check text-success' : 'fa-times text-danger'; ?>"></i>
                                <?php echo $column; ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>
                    <?php else: ?>
                        <p class="text-danger">❌ جدول العملاء غير موجود</p>
                        <a href="check_tables.php" class="btn btn-primary btn-sm">إنشاء الجداول</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- اختبار الملفات المحدثة -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-code"></i>
                        الملفات المحدثة
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['updated_files'] as $file => $info): ?>
                    <div class="mb-3">
                        <h6><?php echo $info['description']; ?> (<?php echo $file; ?>)</h6>
                        <div class="row">
                            <div class="col-4">
                                <small>الملف موجود:</small><br>
                                <span class="badge <?php echo $info['exists'] ? 'bg-success' : 'bg-danger'; ?>">
                                    <?php echo $info['exists'] ? '✓' : '✗'; ?>
                                </span>
                            </div>
                            <div class="col-4">
                                <small>النافذة المنبثقة:</small><br>
                                <span class="badge <?php echo $info['has_customer_modal'] ? 'bg-success' : 'bg-danger'; ?>">
                                    <?php echo $info['has_customer_modal'] ? '✓' : '✗'; ?>
                                </span>
                            </div>
                            <div class="col-4">
                                <small>JavaScript:</small><br>
                                <span class="badge <?php echo $info['has_customer_js'] ? 'bg-success' : 'bg-danger'; ?>">
                                    <?php echo $info['has_customer_js'] ? '✓' : '✗'; ?>
                                </span>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- اختبار عملي -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="mb-0">
                <i class="fas fa-vial"></i>
                اختبار عملي للميزة
            </h5>
        </div>
        <div class="card-body">
            <p>لاختبار الميزة عملياً:</p>
            <ol>
                <li>اذهب إلى صفحة إضافة المشتريات أو المبيعات</li>
                <li>في قائمة العملاء، اختر "-- إضافة عميل جديد --"</li>
                <li>ستظهر نافذة منبثقة لإدخال بيانات العميل</li>
                <li>أدخل البيانات واضغط "حفظ"</li>
                <li>يجب أن يظهر العميل الجديد في القائمة ويتم اختياره تلقائياً</li>
            </ol>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <h6>اختبار في صفحة المشتريات:</h6>
                    <a href="add_purchase.php" class="btn btn-primary" target="_blank">
                        <i class="fas fa-shopping-cart"></i> إضافة مشتريات
                    </a>
                </div>
                <div class="col-md-6">
                    <h6>اختبار في صفحة المبيعات:</h6>
                    <a href="add_sale.php" class="btn btn-success" target="_blank">
                        <i class="fas fa-cash-register"></i> إضافة مبيعات
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- النتيجة الإجمالية -->
    <?php
    $total_checks = 0;
    $passed_checks = 0;
    
    // فحص ملف AJAX
    $total_checks++;
    if ($test_results['ajax_handler_exists']) $passed_checks++;
    
    // فحص جدول العملاء
    $total_checks++;
    if ($test_results['customers_table_exists']) $passed_checks++;
    
    // فحص الملفات المحدثة
    foreach ($test_results['updated_files'] as $info) {
        $total_checks += 3; // exists, modal, js
        if ($info['exists']) $passed_checks++;
        if ($info['has_customer_modal']) $passed_checks++;
        if ($info['has_customer_js']) $passed_checks++;
    }
    
    $success_percentage = ($passed_checks / $total_checks) * 100;
    ?>
    
    <div class="card">
        <div class="card-header <?php echo $success_percentage >= 90 ? 'bg-success' : ($success_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?> text-white">
            <h5 class="mb-0">
                <i class="fas fa-chart-pie"></i>
                النتيجة الإجمالية: <?php echo number_format($success_percentage, 1); ?>%
            </h5>
        </div>
        <div class="card-body">
            <div class="progress mb-3" style="height: 25px;">
                <div class="progress-bar <?php echo $success_percentage >= 90 ? 'bg-success' : ($success_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?>" 
                     style="width: <?php echo $success_percentage; ?>%">
                    <?php echo $passed_checks; ?>/<?php echo $total_checks; ?> (<?php echo number_format($success_percentage, 1); ?>%)
                </div>
            </div>
            
            <?php if ($success_percentage >= 90): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ممتاز! الميزة جاهزة للاستخدام
                </div>
            <?php elseif ($success_percentage >= 70): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> جيد! الميزة تعمل مع بعض المشاكل البسيطة
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i> يحتاج إلى إصلاح! هناك مشاكل تحتاج إلى حل
                </div>
            <?php endif; ?>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>الإجراءات:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="test_add_customer_feature.php" class="btn btn-info btn-sm">
                            <i class="fas fa-redo"></i> إعادة الاختبار
                        </a>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6>إدارة العملاء:</h6>
                    <div class="d-grid gap-2">
                        <a href="customers.php" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-users"></i> قائمة العملاء
                        </a>
                        <a href="add_customer.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-user-plus"></i> إضافة عميل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
