# ----------------------------------------------------------------------
# | Cache expiration                                                   |
# ----------------------------------------------------------------------

# Serve resources with far-future expiration date.
#
# (!) If you don't control versioning with filename-based cache busting, you
# should consider lowering the cache times to something like one week.
#
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cache-Control
# https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Expires
# https://nginx.org/en/docs/http/ngx_http_headers_module.html#expires

map $sent_http_content_type $expires {
  default                                 1M;

  # No content
  ""                                      off;

  # CSS
  ~*text/css                              1y;

  # Data interchange
  ~*application/atom\+xml                 1h;
  ~*application/rdf\+xml                  1h;
  ~*application/rss\+xml                  1h;

  ~*application/json                      0;
  ~*application/ld\+json                  0;
  ~*application/schema\+json              0;
  ~*application/geo\+json                 0;
  ~*application/xml                       0;
  ~*text/calendar                         0;
  ~*text/xml                              0;

  # Favicon (cannot be renamed!) and cursor images
  ~*image/vnd.microsoft.icon              1w;
  ~*image/x-icon                          1w;

  # HTML
  ~*text/html                             0;

  # JavaScript
  ~*application/javascript                1y;
  ~*application/x-javascript              1y;
  ~*text/javascript                       1y;

  # Manifest files
  ~*application/manifest\+json            1w;
  ~*application/x-web-app-manifest\+json  0;
  ~*text/cache-manifest                   0;

  # Markdown
  ~*text/markdown                         0;

  # Media files
  ~*audio/                                1M;
  ~*image/                                1M;
  ~*video/                                1M;

  # WebAssembly
  ~*application/wasm                      1y;

  # Web fonts
  ~*font/                                 1M;
  ~*application/vnd.ms-fontobject         1M;
  ~*application/x-font-ttf                1M;
  ~*application/x-font-woff               1M;
  ~*application/font-woff                 1M;
  ~*application/font-woff2                1M;

  # Other
  ~*text/x-cross-domain-policy            1w;
}

expires $expires;
