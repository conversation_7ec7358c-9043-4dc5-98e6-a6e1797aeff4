---
title: Technology
---

# Technology

A credit to all the projects and open source packages that have helped during the building of this project. Truly, without these projects, Reactive Resume wouldn't exist, and it was much harder to build jsonldresume lab-web.

## ReactJS + Hooks 🎉

React makes it painless to create interactive UIs. Design simple views for each state in your application, and <PERSON>act will efficiently update and render just the right components when your data changes.

Hooks solve a wide variety of seemingly unconnected problems in React that we’ve encountered over five years of writing and maintaining tens of thousands of components. Whether you’re learning React, use it daily, or even prefer a different library with a similar component model, you might recognize some of these problems.

[Learn more &rarr; ](https://reactjs.org/)

## Tailwind CSS

Tailwind CSS is a highly customizable, low-level CSS framework that gives you all of the building blocks you need to build bespoke designs without any annoying opinionated styles you have to fight to override.

[Learn more &rarr; ](https://tailwindcss.com/)

## Lodash

A modern JavaScript utility library delivering modularity, performance & extras. Lodash makes JavaScript easier by taking the hassle out of working with arrays, numbers, objects, strings, etc.

[Learn more &rarr; ](https://lodash.com/)

## html2canvas

The script allows you to take "screenshots" of webpages or parts of it, directly on the users browser. The screenshot is based on the DOM and as such may not be 100% accurate to the real representation as it does not make an actual screenshot, but builds the screenshot based on the information available on the page.

[Learn more &rarr; ](https://github.com/niklasvh/html2canvas)

## jsPDF

A library to generate PDFs in JavaScript.

[Learn more &rarr; ](https://github.com/MrRio/jsPDF)

## Google Fonts

Google Fonts is a great repository of open type fonts that are allowed to be used on the web. Reactive Resume uses Google Fonts to load different font families and allow the user to choose which font he/she would like on their resume.

[Learn more &rarr; ](https://fonts.google.com/)

## Material Icons

Material icons are delightful, beautifully crafted symbols for common actions and items. Download on desktop to use them in your digital products for Android, iOS, and web.

[Learn more &rarr; ](https://material.io/resources/icons/)

## PostCSS

PostCSS is a tool for transforming styles with JS plugins. These plugins can lint your CSS, support variables and mixins, transpile future CSS syntax, inline images, and more.

[Learn more &rarr; ](https://postcss.org/)

## PurgeCSS

PurgeCSS analyzes your content and your css files. Then it matches the selectors used in your files with the one in your content files. It removes unused selectors from your css, resulting in smaller css files.

[Learn more &rarr; ](https://github.com/FullHuman/purgecss)

## VuePress

VuePress is what made this documentation possible, with it's clean design and quick setup as well as the ability to use markdown to generate content, VuePress seemed like the best way to set up a cleaner documentation.

[Learn more &rarr; ](https://vuepress.vuejs.org/)
