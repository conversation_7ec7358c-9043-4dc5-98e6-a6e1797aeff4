<?php
if ($_SERVER["REQUEST_METHOD"] == "GET" && isset($_GET['token'])) {
    // استعلام عن الرمز وتحقق من صلاحيته
    $token = $_GET['token'];

    // تحديد مدة صلاحية الرابط
    $expiration_limit = date('Y-m-d H:i:s', strtotime('-1 hour'));

    // اتصال بقاعدة البيانات والبحث عن الرمز
    // يجب تغيير معلومات الاتصال بقاعدة البيانات والاستعلام حسب الحاجة
    $servername = "localhost";
    $username = "root";
    $password = "";
    $dbname = "signupai";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    $stmt = $conn->prepare("SELECT email FROM password_reset WHERE token = ? AND expiration >= ?");
    $stmt->bind_param("ss", $token, $expiration_limit);
    $stmt->execute();
    $stmt->store_result();

    if ($stmt->num_rows == 1) {
        // رمز صالح، يمكنك هنا تنفيذ إعادة تعيين كلمة المرور
        // وحذف الرمز من قاعدة البيانات بعد استخدامه
        // إذا كنت تستخدم هذا المثال للغرض التعليمي فقط، يمكنك إضافة الكود الخاص بإعادة تعيين كلمة المرور هنا.

        // على سبيل المثال:
        // $new_password = generate_random_password();
        // $hashed_password = password_hash($new_password, PASSWORD_BCRYPT);
        // $stmt = $conn->prepare("UPDATE users SET password = ? WHERE email = ?");
        // $stmt->bind_param("ss", $hashed_password, $email);
        // $stmt->execute();

        // حذف الرمز من قاعدة البيانات
        $stmt = $conn->prepare("DELETE FROM password_reset WHERE token = ?");
        $stmt->bind_param("s", $token);
        $stmt->execute();

        // توجيه المستخدم إلى صفحة إعادة تعيين كلمة المرور الناجحة
        header("Location: reset_password_success.html");
    } else {
        // الرمز غير صالح أو منتهي الصلاحية
        echo "رمز إعادة تعيين كلمة المرور غير صالح أو منتهي الصلاحية.";
    }

    $stmt->close();
    $conn->close();
}
?>
