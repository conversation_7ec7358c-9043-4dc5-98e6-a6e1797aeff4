---
layout: page
title: Dependencies & Plugins
---

#### Dependencies

AdminLTE depends on two main frameworks. The downloadable package contains both of these libraries, so you don't have to manually download them.
{: .lead}

- [Bootstrap 4.6](https://getbootstrap.com/)
- [jQuery 3.5.1+](https://jquery.com/)
- [Popper.js 1.16.1+](https://popper.js.org/)
- [All other plugins are listed below](#plugins)

#### Plugins
{: .mt-4 .anchor}

AdminLTE makes use of the following plugins. For documentation, updates or license information, please visit the provided links.
{: .lead}

> Note!
> You need to load the js/css files of the plugin that you want to use.
> AdminLTE doesn't load automatically all plugins, this would cause huge load times on every page load.
{:.quote-warning}

<div class="row px-3">
  <div class="col-sm-3">
    <ul class="list-unstyled">
      <li><h4>Charts</h4></li>
      <li><a href="https://www.chartjs.org/" rel="noopener" target="_blank">ChartJS</a></li>
      <li><a href="http://www.flotcharts.org/" rel="noopener" target="_blank">Flot</a></li>
      <li><a href="https://github.com/mariusGundersen/sparkline" rel="noopener" target="_blank">Sparkline</a></li>
      <li><a href="https://github.com/leeoniya/uPlot/" rel="noopener" target="_blank">uPlot</a></li>
    </ul>
  </div>
  <div class="col-sm-3">
    <ul class="list-unstyled">
      <li><h4>Editors</h4></li>
      <li><a href="https://summernote.org/" rel="noopener" target="_blank">Summernote</a></li>
      <li><a href="https://codemirror.net/" rel="noopener" target="_blank">CodeMirror</a></li>
    </ul>
  </div>
  <div class="col-sm-3">
    <ul class="list-unstyled">
      <li><h4>Form Elements</h4></li>
      <li><a href="https://farbelous.io/bootstrap-colorpicker/">Bootstrap Colorpicker</a></li>
      <li><a href="https://github.com/seiyria/bootstrap-slider/">Bootstrap Slider</a></li>
      <li><a href="https://github.com/Bttstrp/bootstrap-switch">Bootstrap Switch</a></li>
      <li><a href="http://www.daterangepicker.com/" rel="noopener" target="_blank">Date Range Picker</a></li>
      <li><a href="https://www.dropzonejs.com/" rel="noopener" target="_blank">Dropzone JS</a></li>
      <li><a href="https://github.com/bantikyan/icheck-bootstrap#readme" rel="noopener" target="_blank">iCheck Bootstrap</a></li>
      <li><a href="https://github.com/RobinHerbots/Inputmask/" rel="noopener" target="_blank">Inputmask</a></li>
      <li><a href="http://ionden.com/a/plugins/ion.rangeSlider/" rel="noopener" target="_blank">Ion.RangeSlider</a></li>
      <li><a href="https://select2.org/" rel="noopener" target="_blank">Select2</a></li>
      <li><a href="https://tempusdominus.github.io/bootstrap-4/" rel="noopener" target="_blank">Tempus Dominus</a></li>
      <li><a href="https://github.com/istvan-ujjmeszaros/bootstrap-duallistbox#readme" rel="noopener" target="_blank">Bootstrap4 Duallistbox</a></li>
      <li><a href="https://github.com/Johann-S/bs-custom-file-input#readme" rel="noopener" target="_blank">bs-custom-file-input</a></li>
      <li><a href="https://github.com/Johann-S/bs-stepper#readme" rel="noopener" target="_blank">bs-stepper</a></li>
    </ul>
  </div>
  <div class="col-sm-3">
    <ul class="list-unstyled">
      <li><h4>Icon Packs</h4></li>
      <li><a href="https://fontawesome.com/" rel="noopener" target="_blank">FontAwesome 5</a></li>
      <li><a href="https://github.com/lipis/flag-icon-css#readme" rel="noopener" target="_blank">flag-icon-css</a></li>
    </ul>
  </div>
</div>
<div class="row px-3">
  <div class="col-sm-3">
    <ul class="list-unstyled">
      <li><h4>Table Grids</h4></li>
      <li><a href="https://datatables.net/" rel="noopener" target="_blank">DataTables</a></li>
      <li><a href="https://datatables.net/extensions/" rel="noopener" target="_blank">DataTables (Included all open-source extensions with Bootstrap 4 styling)</a></li>
      <li><a href="http://js-grid.com/" rel="noopener" target="_blank">jsGrid</a></li>
    </ul>
  </div>
  <div class="col-sm-3">
    <ul class="list-unstyled">
      <li><h4>Notifications</h4></li>
      <li><a href="https://sweetalert2.github.io/" rel="noopener" target="_blank">SweetAlert2</a></li>
      <li><a href="https://codeseven.github.io/toastr/" rel="noopener" target="_blank">Toastr</a></li>
    </ul>
  </div>
  <div class="col-sm-3">
    <ul class="list-unstyled">
      <li><h4>Others</h4></li>
      <li><a href="https://github.com/ftlabs/fastclick#readme" rel="noopener" target="_blank">FastClick</a></li>
      <li><a href="https://fullcalendar.io/">FullCalendar 4</a></li>
      <li><a href="https://github.com/aterrien/jQuery-Knob#readme">jQuery Knob</a></li>
      <li><a href="https://jqueryui.com/" rel="noopener" target="_blank">jQuery UI</a></li>
      <li><a href="https://jqueryvalidation.org/" rel="noopener" target="_blank">jQuery Validation</a></li>
      <li><a href="https://www.vincentbroute.fr/mapael/" rel="noopener" target="_blank">jQuery Mapael</a></li>
      <li><a href="https://github.com/bbmumford/jqvmap#readme" rel="noopener" target="_blank">jQuery Vector Map</a></li>
      <li><a href="https://kingsora.github.io/OverlayScrollbars/" rel="noopener" target="_blank">Overlay Scrollbars</a></li>
      <li><a href="https://github.com/lgaitan/pace#readme" rel="noopener" target="_blank">Pace Progress</a></li>
      <li><a href="https://github.com/giotiskl/filterizr#readme" rel="noopener" target="_blank">FilterizR</a></li>
    </ul>
  </div>
  <div class="col-sm-3">
    <ul class="list-unstyled">
      <li><h4>Plugin Dependencies</h4></li>
      <li><a href="https://github.com/jquery/jquery-mousewheel#readme" rel="noopener" target="_blank">jQuery Mousewheel</a></li>
      <li><a href="https://momentjs.com/" rel="noopener" target="_blank">Moment.js</a></li>
      <li><a href="https://dmitrybaranovskiy.github.io/raphael/" rel="noopener" target="_blank">Raphaël</a></li>
      <li><a href="https://github.com/bpampuch/pdfmake#readme" rel="noopener" target="_blank">pdfmake</a></li>
      <li><a href="https://github.com/Stuk/jszip#readme" rel="noopener" target="_blank">jszip</a></li>
    </ul>
  </div>
</div>
