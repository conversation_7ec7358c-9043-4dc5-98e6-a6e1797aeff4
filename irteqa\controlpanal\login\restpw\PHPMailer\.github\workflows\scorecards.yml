name: Scorecards supply-chain security
on:
  # Only the default branch is supported.
  branch_protection_rule:
  schedule:
    - cron: '39 20 * * 1'
  push:
    branches: [ "master" ]

# Declare default permissions as read only.
permissions: read-all

jobs:
  analysis:
    name: Scorecards analysis
    runs-on: ubuntu-latest
    permissions:
      # Needed to upload the results to code-scanning dashboard.
      security-events: write
      # Used to receive a badge. (Upcoming feature)
      id-token: write
      # Needs for private repositories.
      contents: read
      actions: read
    
    steps:
      - name: "Checkout code"
        uses: actions/checkout@v3
        with:
          persist-credentials: false

      - name: "Run analysis"
        uses: ossf/scorecard-action@80e868c13c90f172d68d1f4501dee99e2479f7af
        with:
          results_file: results.sarif
          results_format: sarif
          # (Optional) Read-only PAT token. Uncomment the `repo_token` line below if:
          # - you want to enable the Branch-Protection check on a *public* repository, or
          # - you are installing Scorecards on a *private* repository
          # To create the PAT, follow the steps in https://github.com/ossf/scorecard-action#authentication-with-pat.
          # repo_token: ${{ secrets.SCORECARD_READ_TOKEN }}

          # Publish the results for public repositories to enable scorecard badges. For more details, see
          # https://github.com/ossf/scorecard-action#publishing-results. 
          # For private repositories, `publish_results` will automatically be set to `false`, regardless 
          # of the value entered here.
          publish_results: true

      # Upload the results as artifacts (optional). Commenting out will disable uploads of run results in SARIF
      # format to the repository Actions tab.
      - name: "Upload artifact"
        uses: actions/upload-artifact@v3
        with:
          name: SARIF file
          path: results.sarif
          retention-days: 5
      
      # Upload the results to GitHub's code scanning dashboard.
      - name: "Upload to code-scanning"
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: results.sarif
