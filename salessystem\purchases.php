<?php
// يجب أن تكون هذه السطور الأولى في الملف بالضبط
require_once __DIR__ . '/config/init.php';  // هذا الملف يحتوي على تعريف isLoggedIn()
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

displayMessages(); // عرض أي رسائل خطأ أو نجاح
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><?php echo __('manage_purchases'); ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="add_purchase.php" class="btn btn-success">
            <i class="fas fa-plus"></i> <?php echo __('add_purchase'); ?>
        </a>
    </div>
</div>

<div class="row">
    <!-- القائمة الجانبية -->
    <div class="col-md-3 mb-4">
        <div class="card shadow-sm border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i><?php echo __('filter'); ?></h5>
            </div>
            <div class="card-body">
                <form method="GET" action="purchases.php">
                    <div class="mb-3">
                        <label for="search" class="form-label"><?php echo __('search'); ?></label>
                        <input type="text" class="form-control" id="search" name="search" placeholder="<?php echo __('search_placeholder'); ?>" value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                    </div>

                    <div class="mb-3">
                        <label for="customer_id" class="form-label"><?php echo __('customer'); ?></label>
                        <select class="form-select" id="customer_id" name="customer_id">
                            <option value=""><?php echo __('all_customers'); ?></option>
                            <?php
                            $customers = $db->query("SELECT id, name FROM customers ORDER BY name");
                            while ($customer = $customers->fetch_assoc()):
                                $selected = (isset($_GET['customer_id']) && $_GET['customer_id'] == $customer['id']) ? 'selected' : '';
                            ?>
                                <option value="<?php echo $customer['id']; ?>" <?php echo $selected; ?>>
                                    <?php echo htmlspecialchars($customer['name']); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="date_from" class="form-label"><?php echo __('date_from'); ?></label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo isset($_GET['date_from']) ? htmlspecialchars($_GET['date_from']) : ''; ?>">
                    </div>

                    <div class="mb-3">
                        <label for="date_to" class="form-label"><?php echo __('date_to'); ?></label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo isset($_GET['date_to']) ? htmlspecialchars($_GET['date_to']) : ''; ?>">
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i><?php echo __('search'); ?>
                        </button>
                        <a href="purchases.php" class="btn btn-secondary">
                            <i class="fas fa-redo me-2"></i><?php echo __('reset'); ?>
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="card shadow-sm mt-4 border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i><?php echo __('statistics'); ?></h5>
            </div>
            <div class="card-body">
                <?php
                // إحصائيات المشتريات
                $stats_query = "SELECT
                                COUNT(id) as total_count,
                                SUM(total_amount) as total_amount,
                                SUM(tax_amount) as total_tax,
                                MIN(date) as first_date,
                                MAX(date) as last_date
                                FROM purchases";

                try {
                    $stats_result = $db->query($stats_query);
                    if ($stats_result) {
                        $stats = $stats_result->fetch_assoc();
                    } else {
                        $stats = [
                            'total_count' => 0,
                            'total_amount' => 0,
                            'total_tax' => 0
                        ];
                    }
                } catch (Exception $e) {
                    error_log("Error fetching stats: " . $e->getMessage());
                    $stats = [
                        'total_count' => 0,
                        'total_amount' => 0,
                        'total_tax' => 0
                    ];
                }
                ?>
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <?php echo __('total_purchases'); ?>
                        <span class="badge bg-danger rounded-pill"><?php echo $stats['total_count']; ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <?php echo __('total_amount'); ?>
                        <span class="badge bg-dark rounded-pill"><?php echo number_format($stats['total_amount'], 2) . ' ' . __('currency'); ?></span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <?php echo __('total_tax'); ?>
                        <span class="badge bg-secondary rounded-pill"><?php echo number_format($stats['total_tax'], 2) . ' ' . __('currency'); ?></span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- جدول المشتريات -->
    <div class="col-md-9">
        <div class="card shadow-sm border-danger">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-shopping-cart me-2"></i><?php echo __('purchases_list'); ?></h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th><?php echo __('invoice_number'); ?></th>
                                <th><?php echo __('date'); ?></th>
                                <th><?php echo __('customer'); ?></th>
                                <th><?php echo __('subtotal'); ?></th>
                                <th><?php echo __('tax'); ?></th>
                                <th><?php echo __('total'); ?></th>
                                <th><?php echo __('actions'); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            try {
                                // بناء الاستعلام مع الفلاتر
                                $where_conditions = [];
                                $params = [];
                                $param_types = '';

                                // فلتر البحث
                                if (isset($_GET['search']) && !empty($_GET['search'])) {
                                    $search = '%' . $_GET['search'] . '%';
                                    $where_conditions[] = "(p.invoice_number LIKE ? OR c.name LIKE ?)";
                                    $params[] = $search;
                                    $params[] = $search;
                                    $param_types .= 'ss';
                                }

                                // فلتر العميل
                                if (isset($_GET['customer_id']) && !empty($_GET['customer_id'])) {
                                    $where_conditions[] = "p.customer_id = ?";
                                    $params[] = $_GET['customer_id'];
                                    $param_types .= 'i';
                                }

                                // فلتر التاريخ من
                                if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
                                    $where_conditions[] = "p.date >= ?";
                                    $params[] = $_GET['date_from'];
                                    $param_types .= 's';
                                }

                                // فلتر التاريخ إلى
                                if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
                                    $where_conditions[] = "p.date <= ?";
                                    $params[] = $_GET['date_to'];
                                    $param_types .= 's';
                                }

                                // بناء جملة WHERE
                                $where_clause = '';
                                if (!empty($where_conditions)) {
                                    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
                                }

                                // بناء الاستعلام الرئيسي
                                $query = "SELECT p.id, p.invoice_number, p.date, p.subtotal, p.tax_amount, p.total_amount, c.name AS customer_name
                                          FROM purchases p
                                          LEFT JOIN customers c ON p.customer_id = c.id
                                          $where_clause
                                          ORDER BY p.id DESC";

                                if (!empty($params)) {
                                    $stmt = $db->prepare($query);
                                    if (!$stmt) {
                                        throw new Exception("فشل في إعداد الاستعلام: " . $db->error);
                                    }

                                    $stmt->bind_param($param_types, ...$params);
                                    $stmt->execute();
                                    $result = $stmt->get_result();
                                } else {
                                    $result = $db->query($query);
                                }

                                if (!$result) {
                                    throw new Exception($db->error);
                                }

                                if ($result->num_rows > 0) {
                                    while ($row = $result->fetch_assoc()):
                                        ?>
                            <tr>
                                <td><?php echo $row['id']; ?></td>
                                <td><?php echo $row['invoice_number']; ?></td>
                                <td><?php echo date('d/m/Y', strtotime($row['date'])); ?></td>
                                <td><?php echo htmlspecialchars($row['customer_name'] ?? __('no_customer')); ?></td>
                                <td><?php echo number_format($row['subtotal'], 2) . ' ' . __('currency'); ?></td>
                                <td><?php echo number_format($row['tax_amount'], 2) . ' ' . __('currency'); ?></td>
                                <td><?php echo number_format($row['total_amount'], 2) . ' ' . __('currency'); ?></td>
                                <td>
                                    <a href="view_purchase.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" title="<?php echo __('view'); ?>">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="edit_purchase.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary" title="<?php echo __('edit'); ?>">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="delete_purchase.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('<?php echo __('confirm_delete_purchase'); ?>');" title="<?php echo __('delete'); ?>">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                    <a href="print_invoice.php?id=<?php echo $row['id']; ?>&type=purchase" class="btn btn-sm btn-secondary" target="_blank" title="<?php echo __('print'); ?>">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php
                                    endwhile;
                                } else {
                                    echo '<tr><td colspan="8" class="text-center">' . __('no_data') . '</td></tr>';
                                }
                            } catch (Exception $e) {
                                $error_message = $e->getMessage();
                                error_log("Error displaying purchases: " . $error_message);
                                echo '<tr><td colspan="8" class="text-center text-danger">' . __('error') . ': ' . htmlspecialchars($error_message) . '</td></tr>';
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>