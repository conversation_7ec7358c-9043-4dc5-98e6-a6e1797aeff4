<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';  // هذا الملف يحتوي على displayMessages()
require_once __DIR__.'/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();

// التحقق من وجود معرف العميل
if (!isset($_GET['id'])) {
    $_SESSION['error'] = "معرف العميل غير موجود";
    header("Location: customers.php");
    exit();
}

$customer_id = intval($_GET['id']);

// جلب بيانات العميل
$stmt = $db->prepare("SELECT * FROM customers WHERE id = ?");
$stmt->bind_param("i", $customer_id);
$stmt->execute();
$result = $stmt->get_result();
$customer = $result->fetch_assoc();

if (!$customer) {
    $_SESSION['error'] = "العميل غير موجود";
    header("Location: customers.php");
    exit();
}

// معالجة النموذج عند الإرسال
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $name = trim($_POST['name']);
    $phone = trim($_POST['phone']);
    $tax_number = trim($_POST['tax_number']);
    $address = trim($_POST['address']);

    // التحقق من البيانات
    if (empty($name)) {
        $_SESSION['error'] = "اسم العميل مطلوب";
    } else {
        // تحديث بيانات العميل
        $update_stmt = $db->prepare("UPDATE customers SET
                                    name = ?,
                                    phone = ?,
                                    tax_number = ?,
                                    address = ?
                                    WHERE id = ?");
        $update_stmt->bind_param("ssssi", $name, $phone, $tax_number, $address, $customer_id);

        if ($update_stmt->execute()) {
            $_SESSION['success'] = "تم تحديث بيانات العميل بنجاح";
            header("Location: customers.php");
            exit();
        } else {
            $_SESSION['error'] = "حدث خطأ أثناء تحديث بيانات العميل: " . $db->error;
        }
    }
}
?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">تعديل بيانات العميل</div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم العميل *</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($customer['name']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label for="phone" class="form-label">رقم الجوال</label>
                        <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($customer['phone']); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="tax_number" class="form-label"><?php echo __('tax_number'); ?></label>
                        <input type="text" class="form-control" id="tax_number" name="tax_number" value="<?php echo htmlspecialchars($customer['tax_number']); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label"><?php echo __('address'); ?></label>
                        <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($customer['address']); ?></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    <a href="customers.php" class="btn btn-secondary">إلغاء</a>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>