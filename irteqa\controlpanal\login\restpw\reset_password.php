<?php
// تأكد من تضمين مكتبة PHPMailer
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'vendor/autoload.php';

// تكوين مكتبة PHPMailer
$mail = new PHPMailer(true);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'];

    // إنشاء رمز الاستعادة ووقت الانتهاء
    $resetCode = bin2hex(random_bytes(16));
    $expirationTime = time() + 1800; // 30 دقيقة (1800 ثانية)

    // حفظ رمز الاستعادة ووقت الانتهاء في قاعدة البيانات هنا

    try {
        $mail->isSMTP();
        $mail->Host = 'smtp.example.com'; // استبدل بخادم البريد الصحيح
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>'; // استبدل بعنوان بريدك الإلكتروني
        $mail->Password = 'your_password'; // استبدل بكلمة مرور بريدك الإلكتروني
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = 587;

        $mail->setFrom('<EMAIL>', 'Your Name'); // استبدل بعنوان بريدك واسمك
        $mail->addAddress($email);
        $mail->Subject = 'إعادة ضبط كلمة المرور';
        $mail->Body = "استخدم الرابط التالي لإعادة تعيين كلمة المرور: http://example.com/reset_password_form.php?code=$resetCode";

        $mail->send();
        echo 'تم إرسال البريد الإلكتروني بنجاح.';
    } catch (Exception $e) {
        echo "فشل في إرسال البريد الإلكتروني: {$mail->ErrorInfo}";
    }
}
?>
