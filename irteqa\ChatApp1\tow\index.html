<div class="output-container">
    <div class="output-sizer">
    <div id="result_div" class="result">
    <iframe id="result" name="CodePen" title="CodePen Preview" src="https://cdpn.io/ThomasDaubenton/fullpage/QMqaBN?anon=true&amp;view=" sandbox="allow-forms allow-modals allow-pointer-lock allow-popups allow-same-origin allow-scripts allow-top-navigation-by-user-activation allow-downloads allow-presentation" allow="accelerometer; camera; encrypted-media; display-capture; geolocation; gyroscope; microphone; midi; clipboard-read; clipboard-write; web-share" scrolling="auto" allowtransparency="true" allowpaymentrequest="true" allowfullscreen="true" class="result-iframe " loading="lazy" __idm_id__="6520833">
              </iframe>
    <div id="editor-drag-cover" class="drag-cover" style="display: none;"></div>
    </div>
    <div id="box-console" class="box box-console notranslate" translate="no">
    <div class="editor-resizer editor-resizer-console" title="Drag to resize. Double-click to expand."></div>
    <div class="powers">
    <div class="powers-drag-handle" title="Drag to resize. Double-click to expand."></div>
    <div class="editor-actions-left">
    <h2 class="box-title"><span class="box-title-name">Console</span></h2>
    </div>
    <div class="editor-actions-right">
    <button class="button button-medium mini-button console-clear-button" title="Clear">
    Clear
    </button>
    <button class="button button-medium mini-button close-editor-button" data-type="console" title="Close">
    <svg class="icon-x" viewBox="0 0 100 100">
    <path d="M96.8 83.7L63.1 50l33.7-33.7c3.6-3.6 3.6-9.4 0-13.1s-9.5-3.6-13.1 0L50 36.9 16.3 3.2C12.7-.4 6.9-.4 3.2 3.2s-3.6 9.5 0 13.1L36.9 50 3.2 83.7c-3.6 3.6-3.6 9.4 0 13.1s9.5 3.6 13.1 0L50 63.1l33.7 33.7c3.6 3.6 9.4 3.6 13.1 0s3.6-9.5 0-13.1z"></path>
    </svg>
    </button>
    </div>
    </div>
    <div class="console-wrap">
    <div class="console-entries short-no-scroll"></div>
    <div class="console-command-line">
    <span class="console-arrow forwards"></span>
    <textarea class="console-command-line-input auto-expand" rows="1" data-min-rows="1"></textarea>
    </div>
    </div>
    </div>
    </div>
    </div>