<?php
require_once __DIR__.'/../config/init.php';

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'طريقة طلب غير مسموحة']);
    exit;
}

// قراءة البيانات المرسلة
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['username']) || empty($input['username'])) {
    echo json_encode(['success' => false, 'message' => 'اسم المستخدم مطلوب']);
    exit;
}

$username = trim($input['username']);

// التحقق من صحة اسم المستخدم
if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
    echo json_encode(['success' => false, 'message' => 'اسم المستخدم غير صحيح']);
    exit;
}

// التحقق من وجود المستخدم في قاعدة البيانات الرئيسية
global $main_db;
if (!$main_db || $main_db->connect_error) {
    echo json_encode(['success' => false, 'message' => 'فشل الاتصال بقاعدة البيانات الرئيسية']);
    exit;
}

$stmt = $main_db->prepare("SELECT id FROM users WHERE username = ?");
$stmt->bind_param("s", $username);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo json_encode(['success' => false, 'message' => 'المستخدم غير موجود']);
    exit;
}

$user_row = $result->fetch_assoc();
$user_id = $user_row['id'];
$stmt->close();

// الحصول على اتصال قاعدة بيانات العمليات
$operations_db = getOperationsDB();
if (!$operations_db || $operations_db->connect_error) {
    echo json_encode(['success' => false, 'message' => 'فشل الاتصال بقاعدة بيانات العمليات']);
    exit;
}

try {
    $updated_tables = [];
    $failed_tables = [];
    
    // قائمة الجداول المطلوب تحديثها
    $tables = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
    
    foreach ($tables as $table) {
        $table_name = getUserTableName($table, $username);
        if (!$table_name) {
            $failed_tables[] = $table . ' (لم يتم العثور على الجدول)';
            continue;
        }
        
        // فحص وجود عمود user_id
        $columns_result = $operations_db->query("SHOW COLUMNS FROM `$table_name` LIKE 'user_id'");
        
        if (!$columns_result || $columns_result->num_rows === 0) {
            // إضافة عمود user_id
            $alter_sql = "ALTER TABLE `$table_name` ADD COLUMN `user_id` int(11) NOT NULL DEFAULT $user_id AFTER `id`";
            if ($operations_db->query($alter_sql)) {
                // إضافة فهرس للعمود الجديد
                $index_sql = "ALTER TABLE `$table_name` ADD INDEX `idx_user_id` (`user_id`)";
                $operations_db->query($index_sql);
                
                $updated_tables[] = $table;
            } else {
                $failed_tables[] = $table . ' (فشل في إضافة العمود)';
            }
        } else {
            // العمود موجود، تحديث القيم الفارغة
            $update_sql = "UPDATE `$table_name` SET `user_id` = $user_id WHERE `user_id` = 0 OR `user_id` IS NULL";
            if ($operations_db->query($update_sql)) {
                $affected_rows = $operations_db->affected_rows;
                if ($affected_rows > 0) {
                    $updated_tables[] = $table . " (تم تحديث $affected_rows سجل)";
                } else {
                    $updated_tables[] = $table . ' (محدث مسبقاً)';
                }
            } else {
                $failed_tables[] = $table . ' (فشل في تحديث البيانات)';
            }
        }
    }
    
    $response = [
        'success' => true,
        'message' => 'تم تحديث الجداول بنجاح',
        'updated_tables' => $updated_tables,
        'failed_tables' => $failed_tables,
        'username' => $username,
        'user_id' => $user_id
    ];
    
    if (!empty($failed_tables)) {
        $response['message'] = 'تم تحديث بعض الجداول مع وجود أخطاء';
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("خطأ في تحديث جداول المستخدم $username: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'حدث خطأ أثناء تحديث الجداول']);
}
?>
