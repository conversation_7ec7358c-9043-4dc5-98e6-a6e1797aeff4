@keyframes moveCircle {
  from {
    transform: translateX(-50%) translateY(-50%) scale(1);
  }
  to {
    transform: translateX(-50%) translateY(-100vh) scale(2);
  }
}

.ball-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  animation: moveCircle 8s linear infinite;
  z-index: -1;
}

@keyframes vanishAndReappear {
  0% {
    filter: blur(50px);
  }

  50% {
    filter: blur(25px);
  }

  100% {
    filter: blur(0px);
  }
}

.appear {
  cursor: pointer;
  animation: vanishAndReappear 1s ease 3s;
}

