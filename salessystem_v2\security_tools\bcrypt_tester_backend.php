<?php
/**
 * معالج خلفي لأداة اختبار أمان bcrypt
 * هذا الملف يتعامل مع طلبات AJAX لاختبار كلمات المرور
 */

// إعدادات PHP محسنة لتجنب استنزاف الموارد
ini_set('max_execution_time', 300); // 5 دقائق كحد أقصى
ini_set('memory_limit', '256M'); // حد معقول للذاكرة
set_time_limit(300); // 5 دقائق
ignore_user_abort(true); // الاستمرار حتى لو انقطع الاتصال

// نظام اختبار bcrypt - متاح للجميع بدون تسجيل دخول
// تم إزالة متطلب تسجيل الدخول لسهولة الوصول

// تعيين header للاستجابة JSON
header('Content-Type: application/json; charset=utf-8');

// معالجة الأخطاء العامة
set_error_handler(function($severity, $message, $file, $line) {
    error_log("PHP Error: $message in $file on line $line");
    if ($severity === E_ERROR || $severity === E_PARSE || $severity === E_CORE_ERROR) {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ داخلي في الخادم']);
        exit;
    }
});

// معالجة الاستثناءات
set_exception_handler(function($exception) {
    error_log("PHP Exception: " . $exception->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في معالجة الطلب: ' . $exception->getMessage()]);
    exit;
});

// فحص توفر مكتبة bcrypt
if (!function_exists('password_verify')) {
    http_response_code(503);
    echo json_encode(['error' => 'مكتبة bcrypt غير متوفرة على الخادم']);
    exit;
}

// فحص حالة الخادم
if (function_exists('sys_getloadavg')) {
    $load = sys_getloadavg();
    if ($load[0] > 5.0) { // إذا كان الحمل عالي جداً
        http_response_code(503);
        echo json_encode(['error' => 'الخادم مشغول حالياً، يرجى المحاولة لاحقاً']);
        exit;
    }
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة']);
    exit;
}

// التحقق من وجود البيانات المطلوبة
if (!isset($_POST['action']) || $_POST['action'] !== 'test_bcrypt') {
    http_response_code(400);
    echo json_encode(['error' => 'طلب غير صحيح']);
    exit;
}

$hash = $_POST['hash'] ?? '';
$method = $_POST['method'] ?? 'common';
$maxAttempts = intval($_POST['max_attempts'] ?? 1000);

// معاملات متقدمة
$charsetType = $_POST['charset_type'] ?? 'basic';
$minLength = intval($_POST['min_length'] ?? 1);
$maxLength = intval($_POST['max_length'] ?? 8);
$threadCount = intval($_POST['thread_count'] ?? 1);
$customCharset = $_POST['custom_charset'] ?? '';
$maskPattern = $_POST['mask_pattern'] ?? '';
$useSubstitutions = isset($_POST['use_substitutions']) && $_POST['use_substitutions'] === 'true';
$useKeyboardPatterns = isset($_POST['use_keyboard_patterns']) && $_POST['use_keyboard_patterns'] === 'true';
$useDatePatterns = isset($_POST['use_date_patterns']) && $_POST['use_date_patterns'] === 'true';

// التحقق من صحة البيانات
if (empty($hash)) {
    echo json_encode(['error' => 'Hash مطلوب']);
    exit;
}

// التحقق من صحة bcrypt hash
if (!preg_match('/^\$2[ayb]\$.{56}$/', $hash)) {
    echo json_encode(['error' => 'Hash غير صحيح - يجب أن يكون bcrypt hash صالح']);
    exit;
}

// تحديد الحد الأقصى للمحاولات - حد معقول لتجنب استنزاف الموارد
$maxAttempts = min($maxAttempts, 1000000); // حد أقصى مليون محاولة

// فحص مبكر للموارد
$initialMemory = memory_get_usage();
$maxMemoryLimit = 200 * 1024 * 1024; // 200MB

$startTime = microtime(true);
$attempts = 0;
$passwordFound = false;
$foundPassword = '';

// إعداد نظام التقدم المباشر
$progressFile = sys_get_temp_dir() . '/bcrypt_progress_' . uniqid() . '.json';
$lastProgressUpdate = 0;

// إعداد معاملات الاختبار
$testParams = [
    'max_attempts' => $maxAttempts,
    'charset_type' => $charsetType,
    'min_length' => $minLength,
    'max_length' => $maxLength,
    'thread_count' => $threadCount,
    'custom_charset' => $customCharset,
    'mask_pattern' => $maskPattern,
    'use_substitutions' => $useSubstitutions,
    'use_keyboard_patterns' => $useKeyboardPatterns,
    'use_date_patterns' => $useDatePatterns,
    'progress_file' => $progressFile,
    'start_time' => $startTime
];

try {
    switch ($method) {
        case 'common':
            $result = testCommonPasswords($hash, $testParams);
            break;
        case 'dictionary':
            $result = testDictionaryPasswords($hash, $testParams);
            break;
        case 'brute_simple':
            $result = testBruteForceSimple($hash, $testParams);
            break;
        case 'brute_advanced':
            $result = testBruteForceAdvanced($hash, $testParams);
            break;
        case 'pattern':
            $result = testPatternPasswords($hash, $testParams);
            break;
        case 'hybrid':
            $result = testHybridAttack($hash, $testParams);
            break;
        case 'wordlist_extended':
            $result = testExtendedWordlist($hash, $testParams);
            break;
        case 'mask_attack':
            $result = testMaskAttack($hash, $testParams);
            break;
        case 'rule_based':
            $result = testRuleBased($hash, $testParams);
            break;
        case 'comprehensive':
            $result = testComprehensive($hash, $testParams);
            break;
        default:
            throw new Exception('طريقة اختبار غير صحيحة');
    }

    $endTime = microtime(true);
    $timeTaken = round($endTime - $startTime, 2);

    echo json_encode([
        'success' => true,
        'password_found' => $result['found'],
        'password' => $result['password'],
        'attempts' => $result['attempts'],
        'time_taken' => $timeTaken,
        'method' => $method
    ]);

} catch (Exception $e) {
    echo json_encode([
        'error' => $e->getMessage()
    ]);
}

/**
 * اختبار كلمات المرور الشائعة
 */
function testCommonPasswords($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $maxMemoryLimit = 200 * 1024 * 1024; // 200MB
    $commonPasswords = [
        '123456', 'password', '123456789', '12345678', '12345',
        '1234567', '1234567890', 'qwerty', 'abc123', '111111',
        'password1', 'admin', 'welcome', 'monkey', 'login',
        'princess', 'qwertyuiop', 'solo', 'passw0rd', 'starwars',
        'hello', 'dragon', 'master', 'freedom', 'whatever',
        'qazwsx', 'trustno1', 'jordan', 'harley', 'robert',
        'matthew', 'daniel', 'andrew', 'joshua', 'hunter',
        'target', 'charlie', 'michael', 'sunshine', 'computer',
        'michelle', 'jessica', 'pepper', 'zxcvbnm', 'ashley',
        'nicole', 'chelsea', 'biteme', 'summer', 'sophie',
        'football', 'jesus', 'michael', 'ninja', 'mustang',
        'mercedes', 'samsung', 'cookie', 'maverick', 'tigger',
        'puppy', 'flower', 'baseball', 'shadow', 'lovely',
        'buster', 'basketball', 'soccer', 'purple', 'matrix',
        'secret', 'summer', 'orange', 'jordan', 'taylor',
        'yankees', 'austin', 'william', 'daniel', 'golfer',
        'summer', 'heather', 'hammer', 'yankees', 'joshua',
        'maggie', 'biteme', 'enter', 'ashley', 'thunder',
        'cowboy', 'silver', 'richard', 'orange', 'merlin',
        'michelle', 'corvette', 'bigdog', 'cheese', 'matthew',
        'patrick', 'martin', 'freedom', 'ginger', 'blondie',
        'cookie', 'rock', 'angel', 'much', 'rebel', 'xxx',
        'qwerty123', 'password123', 'admin123', '123123',
        'test', 'guest', 'info', 'adm', 'mysql', 'user',
        'administrator', 'oracle', 'ftp', 'pi', 'puppet',
        'ansible', 'ec2-user', 'vagrant', 'azureuser'
    ];

    // إضافة كلمات مرور إضافية محدودة لتجنب استنزاف الموارد
    $additionalPasswords = [];

    // أرقام شائعة فقط (0-9999)
    for ($i = 0; $i <= 9999; $i++) {
        $additionalPasswords[] = (string)$i;
        if ($i <= 999) {
            $additionalPasswords[] = str_pad($i, 3, '0', STR_PAD_LEFT);
            $additionalPasswords[] = str_pad($i, 4, '0', STR_PAD_LEFT);
        }
    }

    // كلمات مع أرقام محدودة
    $baseWords = ['admin', 'user', 'test', 'pass', 'root', 'demo', 'guest', 'temp'];
    for ($i = 0; $i <= 999; $i++) { // تقليل من 9999 إلى 999
        foreach ($baseWords as $word) {
            $additionalPasswords[] = $word . $i;
            $additionalPasswords[] = ucfirst($word) . $i;
            if ($i <= 99) {
                $additionalPasswords[] = $word . str_pad($i, 2, '0', STR_PAD_LEFT);
            }
        }
    }

    // تواريخ شائعة محدودة
    for ($year = 2000; $year <= 2025; $year++) { // تقليل النطاق
        $additionalPasswords[] = (string)$year;
        // إضافة بعض التواريخ الشائعة فقط
        $commonDates = ['0101', '1231', '0704', '1225'];
        foreach ($commonDates as $date) {
            $additionalPasswords[] = $date . $year;
            $additionalPasswords[] = $date . ($year % 100);
        }
    }

    // دمج جميع كلمات المرور
    $commonPasswords = array_merge($commonPasswords, $additionalPasswords);
    
    $attempts = 0;
    $progressFile = $params['progress_file'] ?? '';
    $startTime = $params['start_time'] ?? microtime(true);

    foreach ($commonPasswords as $password) {
        if ($attempts >= $maxAttempts) break;

        // فحص حالة النظام كل 1000 محاولة (أكثر تكراراً)
        if ($attempts % 1000 == 0) {
            // فحص الذاكرة
            if (memory_get_usage() > $maxMemoryLimit) {
                return [
                    'found' => false,
                    'password' => '',
                    'attempts' => $attempts,
                    'stopped_reason' => 'memory_limit_exceeded'
                ];
            }

            // فحص الوقت (5 دقائق كحد أقصى)
            if ((microtime(true) - $startTime) > 300) {
                return [
                    'found' => false,
                    'password' => '',
                    'attempts' => $attempts,
                    'stopped_reason' => 'time_limit_exceeded'
                ];
            }

            $status = checkSystemStatus($attempts, $startTime);
            if ($status['status'] !== 'running') {
                return [
                    'found' => false,
                    'password' => '',
                    'attempts' => $attempts,
                    'stopped_reason' => $status['reason']
                ];
            }

            // فحص طلب الإيقاف
            if (checkStopRequest()) {
                return [
                    'found' => false,
                    'password' => '',
                    'attempts' => $attempts,
                    'stopped_reason' => 'user_stopped'
                ];
            }

            // تحديث التقدم
            updateProgress($progressFile, $attempts, $maxAttempts, 'common', $password, $startTime);
        }

        $attempts++;
        if (password_verify($password, $hash)) {
            return [
                'found' => true,
                'password' => $password,
                'attempts' => $attempts
            ];
        }

        // تقليل التأخير للأعداد الكبيرة
        if ($attempts % 5000 == 0) {
            usleep(100); // 0.1ms كل 5000 محاولة
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار قاموس كلمات المرور
 */
function testDictionaryPasswords($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $dictionaryWords = [
        'welcome', 'hello', 'world', 'computer', 'internet',
        'security', 'system', 'network', 'server', 'database',
        'application', 'software', 'hardware', 'technology',
        'information', 'digital', 'online', 'website', 'email',
        'mobile', 'phone', 'device', 'machine', 'program',
        'code', 'data', 'file', 'folder', 'document', 'text',
        'image', 'video', 'audio', 'music', 'game', 'play',
        'work', 'office', 'home', 'family', 'friend', 'love',
        'life', 'time', 'money', 'business', 'company', 'job',
        'project', 'team', 'group', 'member', 'user', 'account',
        'profile', 'setting', 'option', 'feature', 'service',
        'support', 'help', 'guide', 'tutorial', 'example'
    ];
    
    // تنويعات محدودة لتجنب استنزاف الموارد
    $variations = ['', '1', '12', '123', '1234', '12345', '!', '@', '#', '$', '2023', '2024', '2022'];

    // إضافة تنويعات أرقام محدودة
    for ($i = 0; $i <= 999; $i++) { // تقليل من 9999 إلى 999
        $variations[] = (string)$i;
    }
    $attempts = 0;

    foreach ($dictionaryWords as $word) {
        foreach ($variations as $suffix) {
            if ($attempts >= $maxAttempts) break 2;

            $password = $word . $suffix;
            $attempts++;

            if (password_verify($password, $hash)) {
                return [
                    'found' => true,
                    'password' => $password,
                    'attempts' => $attempts
                ];
            }

            // اختبار مع أول حرف كبير
            $passwordCap = ucfirst($word) . $suffix;
            $attempts++;

            if (password_verify($passwordCap, $hash)) {
                return [
                    'found' => true,
                    'password' => $passwordCap,
                    'attempts' => $attempts
                ];
            }

            // تقليل التأخير للأعداد الكبيرة
            if ($attempts % 1000 == 0) {
                usleep(100); // 0.1ms كل 1000 محاولة
            }
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار القوة الغاشمة البسيطة
 */
function testBruteForceSimple($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $minLength = $params['min_length'];
    $maxLength = min($params['max_length'], 6); // حد أقصى 6 للبسيط

    $chars = getCharset($params['charset_type'], $params['custom_charset']);
    $attempts = 0;

    // اختبار كلمات مرور من الطول الأدنى إلى الأقصى
    for ($length = $minLength; $length <= $maxLength; $length++) {
        $result = generatePasswords($chars, $length, $hash, $maxAttempts, $attempts);
        if ($result['found'] || $attempts >= $maxAttempts) {
            return $result;
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * توليد كلمات المرور للقوة الغاشمة
 */
function generatePasswords($chars, $length, $hash, $maxAttempts, &$attempts) {
    $charsLength = strlen($chars);
    $total = pow($charsLength, $length);

    for ($i = 0; $i < $total && $attempts < $maxAttempts; $i++) {
        $password = '';
        $temp = $i;

        for ($j = 0; $j < $length; $j++) {
            $password = $chars[$temp % $charsLength] . $password;
            $temp = intval($temp / $charsLength);
        }

        $attempts++;
        if (password_verify($password, $hash)) {
            return [
                'found' => true,
                'password' => $password,
                'attempts' => $attempts
            ];
        }

        // تقليل التأخير للأعداد الكبيرة
        if ($attempts % 10000 == 0) {
            usleep(100); // 0.1ms كل 10000 محاولة
        }

        // فحص دوري للذاكرة
        if ($attempts % 1000000 == 0) {
            if (memory_get_usage() > 256 * 1024 * 1024) { // 256MB
                return [
                    'found' => false,
                    'password' => '',
                    'attempts' => $attempts
                ];
            }
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار الأنماط الشائعة
 */
function testPatternPasswords($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $patterns = [
        // أنماط التاريخ
        '2023', '2022', '2021', '2020', '2019', '2018',
        '01012023', '01012022', '12312023', '12312022',

        // أنماط الأرقام
        '0000', '1111', '2222', '3333', '4444', '5555',
        '6666', '7777', '8888', '9999', '1010', '2020',

        // أنماط لوحة المفاتيح
        'qwerty', 'asdf', 'zxcv', 'qaz', 'wsx', 'edc',
        'qwe', 'asd', 'zxc', '147', '258', '369',

        // أنماط الكلمات + أرقام
        'test123', 'user123', 'pass123', 'admin123',
        'root123', 'demo123', 'temp123', 'guest123'
    ];

    // إضافة أنماط إضافية للاستفادة من العدد الكبير
    $additionalPatterns = [];

    // أرقام متكررة
    for ($digit = 0; $digit <= 9; $digit++) {
        for ($length = 3; $length <= 8; $length++) {
            $additionalPatterns[] = str_repeat($digit, $length);
        }
    }

    // أنماط متسلسلة
    $sequences = ['123456789', '987654321', 'abcdefgh', 'zyxwvuts'];
    foreach ($sequences as $seq) {
        for ($length = 3; $length <= strlen($seq); $length++) {
            $additionalPatterns[] = substr($seq, 0, $length);
        }
    }

    // كلمات شائعة مع أرقام محدودة
    $words = ['admin', 'user', 'test', 'pass', 'root', 'demo', 'guest', 'temp', 'login', 'system'];
    for ($i = 0; $i <= 999; $i++) { // تقليل من 9999 إلى 999
        foreach ($words as $word) {
            $additionalPatterns[] = $word . $i;
            $additionalPatterns[] = $i . $word;
            if ($i <= 99) {
                $additionalPatterns[] = $word . str_pad($i, 2, '0', STR_PAD_LEFT);
            }
        }
    }

    // دمج جميع الأنماط
    $patterns = array_merge($patterns, $additionalPatterns);
    
    $attempts = 0;
    foreach ($patterns as $pattern) {
        if ($attempts >= $maxAttempts) break;

        $attempts++;
        if (password_verify($pattern, $hash)) {
            return [
                'found' => true,
                'password' => $pattern,
                'attempts' => $attempts
            ];
        }

        // تقليل التأخير للأعداد الكبيرة
        if ($attempts % 5000 == 0) {
            usleep(500); // 0.5ms كل 5000 محاولة
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * الحصول على مجموعة الأحرف حسب النوع
 */
function getCharset($type, $custom = '') {
    switch ($type) {
        case 'basic':
            return 'abcdefghijklmnopqrstuvwxyz0123456789';
        case 'extended':
            return 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        case 'special':
            return 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
        case 'unicode':
            return 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?أبتثجحخدذرزسشصضطظعغفقكلمنهوي';
        case 'custom':
            return !empty($custom) ? $custom : 'abcdefghijklmnopqrstuvwxyz0123456789';
        default:
            return 'abcdefghijklmnopqrstuvwxyz0123456789';
    }
}

/**
 * اختبار القوة الغاشمة المتقدمة
 */
function testBruteForceAdvanced($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $minLength = $params['min_length'];
    $maxLength = $params['max_length'];

    $chars = getCharset($params['charset_type'], $params['custom_charset']);
    $attempts = 0;

    // اختبار متقدم مع تحسينات
    for ($length = $minLength; $length <= $maxLength; $length++) {
        $result = generatePasswordsAdvanced($chars, $length, $hash, $maxAttempts, $attempts, $params);
        if ($result['found'] || $attempts >= $maxAttempts) {
            return $result;
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * توليد كلمات مرور متقدم
 */
function generatePasswordsAdvanced($chars, $length, $hash, $maxAttempts, &$attempts, $params) {
    $charsLength = strlen($chars);
    $total = pow($charsLength, $length);

    for ($i = 0; $i < $total && $attempts < $maxAttempts; $i++) {
        $password = '';
        $temp = $i;

        for ($j = 0; $j < $length; $j++) {
            $password = $chars[$temp % $charsLength] . $password;
            $temp = intval($temp / $charsLength);
        }

        $attempts++;
        if (password_verify($password, $hash)) {
            return [
                'found' => true,
                'password' => $password,
                'attempts' => $attempts
            ];
        }

        // تقليل التأخير للأعداد الكبيرة
        if ($attempts % 50000 == 0) {
            usleep(50); // 0.05ms كل 50000 محاولة
        }

        // فحص دوري للذاكرة
        if ($attempts % 5000000 == 0) {
            if (memory_get_usage() > 512 * 1024 * 1024) { // 512MB
                return [
                    'found' => false,
                    'password' => '',
                    'attempts' => $attempts
                ];
            }
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار هجين (مختلط)
 */
function testHybridAttack($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $attempts = 0;

    // تجربة طرق مختلفة بالتتابع
    $methods = ['common', 'pattern', 'dictionary', 'brute_simple'];

    foreach ($methods as $method) {
        if ($attempts >= $maxAttempts) break;

        $remainingAttempts = $maxAttempts - $attempts;
        $methodParams = $params;
        $methodParams['max_attempts'] = min($remainingAttempts, $maxAttempts / 4);

        switch ($method) {
            case 'common':
                $result = testCommonPasswords($hash, $methodParams);
                break;
            case 'pattern':
                $result = testPatternPasswords($hash, $methodParams);
                break;
            case 'dictionary':
                $result = testDictionaryPasswords($hash, $methodParams);
                break;
            case 'brute_simple':
                $result = testBruteForceSimple($hash, $methodParams);
                break;
        }

        $attempts += $result['attempts'];

        if ($result['found']) {
            return [
                'found' => true,
                'password' => $result['password'],
                'attempts' => $attempts
            ];
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار قاموس موسع
 */
function testExtendedWordlist($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $attempts = 0;

    // قاموس موسع جداً
    $extendedWords = [
        // كلمات إنجليزية شائعة
        'password', 'admin', 'user', 'test', 'guest', 'root', 'demo', 'temp',
        'login', 'system', 'welcome', 'hello', 'world', 'computer', 'internet',
        'security', 'network', 'server', 'database', 'application', 'software',
        'hardware', 'technology', 'information', 'digital', 'online', 'website',
        'email', 'mobile', 'phone', 'device', 'machine', 'program', 'code',
        'data', 'file', 'folder', 'document', 'text', 'image', 'video', 'audio',
        'music', 'game', 'play', 'work', 'office', 'home', 'family', 'friend',
        'love', 'life', 'time', 'money', 'business', 'company', 'job', 'project',

        // كلمات عربية شائعة
        'مرحبا', 'أهلا', 'سلام', 'كلمة', 'مرور', 'دخول', 'مستخدم', 'إدارة',
        'نظام', 'حاسوب', 'برنامج', 'تطبيق', 'موقع', 'صفحة', 'ملف', 'مجلد',
        'بيانات', 'معلومات', 'رقم', 'اسم', 'عنوان', 'هاتف', 'بريد', 'رسالة',

        // أسماء شائعة
        'ahmed', 'mohamed', 'ali', 'omar', 'sara', 'fatima', 'aisha', 'maryam',
        'john', 'mike', 'david', 'sarah', 'mary', 'lisa', 'anna', 'emma',

        // كلمات تقنية
        'server', 'client', 'database', 'mysql', 'php', 'html', 'css', 'javascript',
        'python', 'java', 'linux', 'windows', 'apache', 'nginx', 'docker', 'git'
    ];

    // تنويعات شاملة
    $variations = [];
    for ($i = 0; $i <= 99999; $i++) {
        $variations[] = (string)$i;
        $variations[] = str_pad($i, 6, '0', STR_PAD_LEFT);
    }

    // رموز وأحرف
    $symbols = ['!', '@', '#', '$', '%', '^', '&', '*', '(', ')', '_', '+', '-', '='];

    foreach ($extendedWords as $word) {
        foreach ($variations as $variation) {
            if ($attempts >= $maxAttempts) break 2;

            // تجربة تركيبات مختلفة
            $combinations = [
                $word . $variation,
                $variation . $word,
                ucfirst($word) . $variation,
                strtoupper($word) . $variation,
                $word . $variation . '!',
                $word . $variation . '@'
            ];

            foreach ($combinations as $password) {
                if ($attempts >= $maxAttempts) break 3;

                $attempts++;
                if (password_verify($password, $hash)) {
                    return [
                        'found' => true,
                        'password' => $password,
                        'attempts' => $attempts
                    ];
                }

                if ($attempts % 10000 == 0) {
                    usleep(100); // 0.1ms كل 10000 محاولة
                }
            }
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار هجوم القناع
 */
function testMaskAttack($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $maskPattern = $params['mask_pattern'];
    $attempts = 0;

    if (empty($maskPattern)) {
        // أنماط افتراضية
        $defaultMasks = [
            '?l?l?l?d?d?d',      // 3 أحرف + 3 أرقام
            '?u?l?l?l?d?d',      // حرف كبير + 2 صغير + 2 رقم
            '?l?l?l?l?d?d?d?d',  // 4 أحرف + 4 أرقام
            '?d?d?d?d?d?d',      // 6 أرقام
            '?l?l?l?l?l?l',      // 6 أحرف
        ];

        foreach ($defaultMasks as $mask) {
            if ($attempts >= $maxAttempts) break;

            $result = processMask($mask, $hash, $maxAttempts - $attempts, $attempts);
            if ($result['found']) {
                return $result;
            }
        }
    } else {
        $result = processMask($maskPattern, $hash, $maxAttempts, $attempts);
        return $result;
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * معالجة نمط القناع
 */
function processMask($mask, $hash, $maxAttempts, &$attempts) {
    $charsets = [
        '?l' => 'abcdefghijklmnopqrstuvwxyz',
        '?u' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
        '?d' => '0123456789',
        '?s' => '!@#$%^&*()_+-=[]{}|;:,.<>?'
    ];

    // تحويل القناع إلى مصفوفة من مجموعات الأحرف
    $positions = [];
    $i = 0;
    while ($i < strlen($mask)) {
        if ($i < strlen($mask) - 1 && $mask[$i] == '?') {
            $placeholder = substr($mask, $i, 2);
            if (isset($charsets[$placeholder])) {
                $positions[] = $charsets[$placeholder];
                $i += 2;
            } else {
                $positions[] = $mask[$i];
                $i++;
            }
        } else {
            $positions[] = $mask[$i];
            $i++;
        }
    }

    // توليد كلمات المرور حسب القناع
    $total = 1;
    foreach ($positions as $pos) {
        if (is_string($pos) && strlen($pos) > 1) {
            $total *= strlen($pos);
        }
    }

    for ($i = 0; $i < $total && $attempts < $maxAttempts; $i++) {
        $password = '';
        $temp = $i;

        foreach ($positions as $pos) {
            if (is_string($pos) && strlen($pos) > 1) {
                $password .= $pos[$temp % strlen($pos)];
                $temp = intval($temp / strlen($pos));
            } else {
                $password .= $pos;
            }
        }

        $attempts++;
        if (password_verify($password, $hash)) {
            return [
                'found' => true,
                'password' => $password,
                'attempts' => $attempts
            ];
        }

        if ($attempts % 10000 == 0) {
            usleep(100);
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار قائم على القواعد
 */
function testRuleBased($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $attempts = 0;

    // كلمات أساسية
    $baseWords = ['password', 'admin', 'user', 'test', 'login', 'welcome'];

    // قواعد التحويل
    $rules = [
        // استبدالات شائعة
        ['a' => '@', 'e' => '3', 'i' => '1', 'o' => '0', 's' => '$'],
        ['a' => '4', 'e' => '3', 'i' => '!', 'o' => '0', 's' => '5'],

        // إضافة أرقام
        'append_numbers' => true,
        'prepend_numbers' => true,

        // تغيير الحالة
        'capitalize' => true,
        'uppercase' => true,
        'alternating_case' => true
    ];

    foreach ($baseWords as $word) {
        if ($attempts >= $maxAttempts) break;

        // تطبيق القواعد
        $variations = applyRules($word, $rules);

        foreach ($variations as $password) {
            if ($attempts >= $maxAttempts) break;

            $attempts++;
            if (password_verify($password, $hash)) {
                return [
                    'found' => true,
                    'password' => $password,
                    'attempts' => $attempts
                ];
            }

            if ($attempts % 1000 == 0) {
                usleep(100);
            }
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * تطبيق القواعد على كلمة
 */
function applyRules($word, $rules) {
    $variations = [$word];

    // استبدالات الأحرف
    foreach ($rules as $rule) {
        if (is_array($rule)) {
            $newVariations = [];
            foreach ($variations as $variation) {
                $newWord = $variation;
                foreach ($rule as $from => $to) {
                    $newWord = str_replace($from, $to, $newWord);
                }
                $newVariations[] = $newWord;
            }
            $variations = array_merge($variations, $newVariations);
        }
    }

    // إضافة أرقام
    $numberedVariations = [];
    foreach ($variations as $variation) {
        for ($i = 0; $i <= 999; $i++) {
            $numberedVariations[] = $variation . $i;
            $numberedVariations[] = $i . $variation;
        }

        // تغيير الحالة
        $numberedVariations[] = ucfirst($variation);
        $numberedVariations[] = strtoupper($variation);
    }

    return array_unique(array_merge($variations, $numberedVariations));
}

/**
 * اختبار شامل (جميع الطرق)
 */
function testComprehensive($hash, $params) {
    $maxAttempts = $params['max_attempts'];
    $attempts = 0;

    // تقسيم المحاولات على الطرق المختلفة
    $methods = [
        'common' => 0.2,        // 20%
        'pattern' => 0.15,      // 15%
        'dictionary' => 0.15,   // 15%
        'wordlist_extended' => 0.2, // 20%
        'rule_based' => 0.15,   // 15%
        'brute_advanced' => 0.15 // 15%
    ];

    foreach ($methods as $method => $percentage) {
        if ($attempts >= $maxAttempts) break;

        $methodAttempts = intval($maxAttempts * $percentage);
        $methodParams = $params;
        $methodParams['max_attempts'] = $methodAttempts;

        switch ($method) {
            case 'common':
                $result = testCommonPasswords($hash, $methodParams);
                break;
            case 'pattern':
                $result = testPatternPasswords($hash, $methodParams);
                break;
            case 'dictionary':
                $result = testDictionaryPasswords($hash, $methodParams);
                break;
            case 'wordlist_extended':
                $result = testExtendedWordlist($hash, $methodParams);
                break;
            case 'rule_based':
                $result = testRuleBased($hash, $methodParams);
                break;
            case 'brute_advanced':
                $result = testBruteForceAdvanced($hash, $methodParams);
                break;
        }

        $attempts += $result['attempts'];

        if ($result['found']) {
            return [
                'found' => true,
                'password' => $result['password'],
                'attempts' => $attempts
            ];
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * تحديث ملف التقدم
 */
function updateProgress($progressFile, $attempts, $maxAttempts, $method, $currentPassword = '', $startTime = null) {
    if (empty($progressFile)) return;

    $progress = [
        'attempts' => $attempts,
        'max_attempts' => $maxAttempts,
        'percentage' => $maxAttempts > 0 ? round(($attempts / $maxAttempts) * 100, 2) : 0,
        'method' => $method,
        'current_password' => $currentPassword,
        'time_elapsed' => $startTime ? round(microtime(true) - $startTime, 2) : 0,
        'timestamp' => time(),
        'memory_usage' => memory_get_usage(true),
        'peak_memory' => memory_get_peak_usage(true)
    ];

    file_put_contents($progressFile, json_encode($progress));
}

/**
 * فحص طلب الإيقاف من المستخدم
 */
function checkStopRequest() {
    // استخدام معرف عام للإيقاف بدلاً من session_id
    $stopFile = sys_get_temp_dir() . '/bcrypt_stop_general.flag';
    if (file_exists($stopFile)) {
        unlink($stopFile); // حذف الملف بعد القراءة
        return true;
    }
    return false;
}

/**
 * فحص حالة الاتصال والذاكرة
 */
function checkSystemStatus($attempts, $startTime, $maxMemory = 1073741824) { // 1GB default
    // فحص الاتصال
    if (connection_aborted()) {
        return ['status' => 'aborted', 'reason' => 'connection_lost'];
    }

    // فحص الذاكرة
    $memoryUsage = memory_get_usage(true);
    if ($memoryUsage > $maxMemory) {
        return ['status' => 'stopped', 'reason' => 'memory_limit', 'memory' => $memoryUsage];
    }

    // فحص الوقت (إيقاف بعد 24 ساعة كحد أقصى)
    $timeElapsed = microtime(true) - $startTime;
    if ($timeElapsed > 86400) { // 24 hours
        return ['status' => 'stopped', 'reason' => 'time_limit', 'time' => $timeElapsed];
    }

    return ['status' => 'running'];
}

/**
 * إرسال تحديث مباشر للمتصفح
 */
function sendProgressUpdate($attempts, $maxAttempts, $method, $currentPassword = '') {
    // إرسال headers للـ streaming
    if (!headers_sent()) {
        header('Content-Type: text/plain');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
    }

    $progress = [
        'type' => 'progress',
        'attempts' => $attempts,
        'max_attempts' => $maxAttempts,
        'percentage' => $maxAttempts > 0 ? round(($attempts / $maxAttempts) * 100, 2) : 0,
        'method' => $method,
        'current_password' => $currentPassword,
        'timestamp' => time()
    ];

    echo "data: " . json_encode($progress) . "\n\n";
    flush();
    ob_flush();
}
?>
