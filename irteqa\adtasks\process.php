<?php
include "conn/config.php";
$dbname = "servsads";

// إنشاء الاتصال
$conn = new mysqli($servername, $username, $password, $dbname);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال: " . $conn->connect_error);
}

// استقبال البيانات المُرسلة عبر الطريقة POST
$name = $_POST['name'];
$email = $_POST['email'];

// إدراج البيانات في قاعدة البيانات
$sql = "INSERT INTO new (firstname , email ) VALUES ('$name', '$email')";

if ($conn->query($sql) === TRUE) {
    echo "تم إدخال البيانات بنجاح.";
} else {
    echo "خطأ في إدخال البيانات: " . $conn->error;
}

// إغلاق الاتصال
$conn->close();
?>

