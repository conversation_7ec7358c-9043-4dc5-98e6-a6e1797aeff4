# تحديث جدول المنتجات وإضافة نظام التصنيفات

## 🔧 التحديثات المطبقة على جدول المنتجات

### **الحقول المضافة:**

#### **1. حقل التصنيف (category):**
```sql
ADD COLUMN `category` varchar(100) DEFAULT NULL
```

#### **2. حقل الوصف (description):**
```sql
ADD COLUMN `description` text DEFAULT NULL
```

#### **3. حقول التوقيت:**
```sql
ADD COLUMN `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
ADD COLUMN `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

#### **4. الفهارس المضافة:**
```sql
ADD INDEX `idx_category` (`category`)
ADD INDEX `idx_name` (`name`)
```

### **هيكل الجدول الجديد:**
```sql
CREATE TABLE `products` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `description` text DEFAULT NULL,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
    `category` varchar(100) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    INDEX `idx_category` (`category`),
    INDEX `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

## 🔄 التحديث التلقائي للجداول الموجودة

### **آلية التحديث في `check_tables.php`:**

#### **فحص الأعمدة المفقودة:**
```php
// التحقق من الأعمدة المفقودة في جدول المنتجات الموجود
$columns_to_add = [
    'description' => "ADD COLUMN `description` text DEFAULT NULL",
    'category' => "ADD COLUMN `category` varchar(100) DEFAULT NULL",
    'created_at' => "ADD COLUMN `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP",
    'updated_at' => "ADD COLUMN `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
];

// فحص الأعمدة الموجودة
$existing_columns = [];
$columns_result = $db->query("SHOW COLUMNS FROM products");
if ($columns_result) {
    while ($column = $columns_result->fetch_assoc()) {
        $existing_columns[] = $column['Field'];
    }
}

// إضافة الأعمدة المفقودة
foreach ($columns_to_add as $column_name => $alter_sql) {
    if (!in_array($column_name, $existing_columns)) {
        $full_sql = "ALTER TABLE `products` " . $alter_sql;
        if ($db->query($full_sql)) {
            $_SESSION['success'] .= "<br>تم إضافة العمود '$column_name' لجدول المنتجات";
        } else {
            $_SESSION['error'] .= "<br>فشل في إضافة العمود '$column_name': " . $db->error;
        }
    }
}
```

#### **إضافة الفهارس:**
```php
// إضافة الفهارس إذا لم تكن موجودة
$indexes_to_add = [
    'idx_category' => "ADD INDEX `idx_category` (`category`)",
    'idx_name' => "ADD INDEX `idx_name` (`name`)"
];

// فحص الفهارس الموجودة
$existing_indexes = [];
$indexes_result = $db->query("SHOW INDEX FROM products");
if ($indexes_result) {
    while ($index = $indexes_result->fetch_assoc()) {
        $existing_indexes[] = $index['Key_name'];
    }
}

// إضافة الفهارس المفقودة
foreach ($indexes_to_add as $index_name => $alter_sql) {
    if (!in_array($index_name, $existing_indexes)) {
        $full_sql = "ALTER TABLE `products` " . $alter_sql;
        if ($db->query($full_sql)) {
            $_SESSION['success'] .= "<br>تم إضافة الفهرس '$index_name' لجدول المنتجات";
        }
    }
}
```

## 📋 تحديثات صفحة المنتجات

### **1. إضافة عمود التصنيف في الجدول:**

#### **رأس الجدول:**
```html
<thead class="table-dark">
    <tr>
        <th>الرقم</th>
        <th>اسم المنتج</th>
        <th>التصنيف</th>        <!-- جديد -->
        <th>الوصف</th>
        <th>السعر</th>
        <th>نسبة الضريبة</th>
        <th>تاريخ الإضافة</th>
        <th>الإجراءات</th>
    </tr>
</thead>
```

#### **عرض التصنيف:**
```php
<td>
    <?php if (!empty($product['category'])): ?>
        <span class="badge bg-primary">
            <?php echo htmlspecialchars($product['category']); ?>
        </span>
    <?php else: ?>
        <span class="text-muted">غير محدد</span>
    <?php endif; ?>
</td>
```

### **2. إضافة فلتر التصنيفات:**

#### **جلب التصنيفات المتاحة:**
```php
// جلب التصنيفات المتاحة
$categories_result = $db->query("SELECT DISTINCT category FROM products WHERE category IS NOT NULL AND category != '' ORDER BY category");
$categories = [];
if ($categories_result) {
    while ($cat = $categories_result->fetch_assoc()) {
        $categories[] = $cat['category'];
    }
}
```

#### **شريط البحث المحسن:**
```html
<form method="GET" class="row g-2">
    <div class="col-md-6">
        <input type="text" class="form-control" name="search" 
               placeholder="البحث في المنتجات..." 
               value="<?php echo htmlspecialchars($search); ?>">
    </div>
    <div class="col-md-4">
        <select class="form-select" name="category">
            <option value="">جميع التصنيفات</option>
            <?php foreach ($categories as $category): ?>
                <option value="<?php echo htmlspecialchars($category); ?>" 
                        <?php echo $category_filter === $category ? 'selected' : ''; ?>>
                    <?php echo htmlspecialchars($category); ?>
                </option>
            <?php endforeach; ?>
        </select>
    </div>
    <div class="col-md-2">
        <button type="submit" class="btn btn-outline-primary w-100">
            <i class="fas fa-search"></i>
        </button>
    </div>
</form>
```

### **3. تحسين نموذج إضافة/تعديل المنتج:**

#### **حقل التصنيف الذكي:**
```html
<div class="mb-3">
    <label for="productCategory" class="form-label">التصنيف</label>
    <div class="input-group">
        <select class="form-select" id="productCategorySelect" onchange="handleCategoryChange()">
            <option value="">اختر تصنيف موجود</option>
            <?php foreach ($categories as $category): ?>
                <option value="<?php echo htmlspecialchars($category); ?>">
                    <?php echo htmlspecialchars($category); ?>
                </option>
            <?php endforeach; ?>
            <option value="new">+ إضافة تصنيف جديد</option>
        </select>
        <input type="text" class="form-control" id="productCategory" name="category" 
               placeholder="أدخل تصنيف جديد..." style="display: none;">
    </div>
    <small class="form-text text-muted">
        اختر من التصنيفات الموجودة أو أضف تصنيف جديد
    </small>
</div>
```

#### **JavaScript لإدارة التصنيفات:**
```javascript
// إدارة التصنيفات
function handleCategoryChange() {
    const select = document.getElementById('productCategorySelect');
    const input = document.getElementById('productCategory');
    
    if (select.value === 'new') {
        // إظهار حقل النص وإخفاء القائمة المنسدلة
        select.style.display = 'none';
        input.style.display = 'block';
        input.focus();
        input.value = '';
    } else if (select.value === '') {
        // إظهار حقل النص للتصنيف المخصص
        select.style.display = 'none';
        input.style.display = 'block';
        input.value = '';
    } else {
        // استخدام التصنيف المحدد
        input.value = select.value;
    }
}

// إعادة تعيين التصنيفات
function resetCategoryFields() {
    const select = document.getElementById('productCategorySelect');
    const input = document.getElementById('productCategory');
    
    select.style.display = 'block';
    input.style.display = 'none';
    select.value = '';
    input.value = '';
}
```

## 🔍 تحسينات البحث والفلترة

### **البحث المتقدم:**
```php
$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR description LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $types .= 'ss';
}

if (!empty($category_filter)) {
    $where_conditions[] = "category = ?";
    $params[] = $category_filter;
    $types .= 's';
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(' AND ', $where_conditions);
}
```

### **الحفاظ على الفلاتر في الترقيم:**
```php
// بناء معاملات الاستعلام للحفاظ على الفلاتر
$query_params = [];
if (!empty($search)) $query_params[] = 'search=' . urlencode($search);
if (!empty($category_filter)) $query_params[] = 'category=' . urlencode($category_filter);
$query_string = !empty($query_params) ? '&' . implode('&', $query_params) : '';
```

## 🎯 المميزات الجديدة

### **إدارة التصنيفات:**
- ✅ **عرض التصنيفات** في جدول المنتجات
- ✅ **فلترة حسب التصنيف** في شريط البحث
- ✅ **إضافة تصنيفات جديدة** في نموذج المنتج
- ✅ **اختيار من التصنيفات الموجودة** أو إدخال جديد

### **تحسينات الواجهة:**
- ✅ **شريط بحث محسن** مع فلتر التصنيفات
- ✅ **عرض التصنيف** كشارة ملونة
- ✅ **حقل تصنيف ذكي** يتكيف مع الاختيار
- ✅ **الحفاظ على الفلاتر** في الترقيم

### **تحسينات قاعدة البيانات:**
- ✅ **فهارس محسنة** للبحث السريع
- ✅ **حقول توقيت** لتتبع التعديلات
- ✅ **تحديث تلقائي** للجداول الموجودة
- ✅ **هيكل محسن** للأداء

## 📊 الفوائد المحققة

### **تحسين الأداء:**
- **فهارس على التصنيف والاسم** لبحث أسرع
- **استعلامات محسنة** مع WHERE conditions
- **ترقيم فعال** مع الحفاظ على الفلاتر

### **تحسين تجربة المستخدم:**
- **بحث متقدم** بالاسم والوصف والتصنيف
- **فلترة سهلة** حسب التصنيفات
- **إضافة تصنيفات جديدة** بسهولة
- **واجهة بديهية** ومنظمة

### **سهولة الإدارة:**
- **تصنيف المنتجات** بشكل منطقي
- **تتبع التعديلات** بالتوقيت
- **بحث سريع** في المنتجات
- **إدارة شاملة** للمخزون

## ✅ الخلاصة

تم تحديث نظام المنتجات بنجاح ليشمل:

### **الإنجازات:**
1. **إضافة نظام التصنيفات** المتكامل
2. **تحديث تلقائي** لجدول المنتجات
3. **واجهة محسنة** للبحث والفلترة
4. **نموذج ذكي** لإدارة التصنيفات

### **النتائج:**
- ✅ **جدول منتجات محسن** مع جميع الحقول المطلوبة
- ✅ **نظام تصنيفات مرن** وسهل الاستخدام
- ✅ **بحث وفلترة متقدمة** للمنتجات
- ✅ **تحديث تلقائي** للجداول الموجودة

**النتيجة: نظام إدارة منتجات متكامل مع تصنيفات ذكية وبحث متقدم!** 🎉
