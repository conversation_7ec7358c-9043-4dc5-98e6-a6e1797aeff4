<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
</head>
<body>
<?php
// معلومات الاتصال بخادم قاعدة البيانات
$servername = "localhost";
$username = "root";
$password = "";

// إنشاء اتصال
$conn = new mysqli($servername, $username, $password);

// التحقق من الاتصال
if ($conn->connect_error) {
    die("فشل الاتصال: " . $conn->connect_error);
}

// إنشاء قاعدة بيانات
$sql_create_db = "CREATE DATABASE IF NOT EXISTS newmydb";
if ($conn->query($sql_create_db) === TRUE) {
    echo "تم إنشاء قاعدة البيانات بنجاح";
} else {
    echo "خطأ في إنشاء قاعدة البيانات: " . $conn->error;
}

// اختيار قاعدة البيانات
$conn->select_db("newmydb");

// إنشاء جدول
$sql_create_table = "CREATE TABLE IF NOT EXISTS users (
    id INT(6) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    column1 VARCHAR(30) NOT NULL,
    column2 VARCHAR(30) NOT NULL,
    column3 INT(10) 
)";
if ($conn->query($sql_create_table) === TRUE) {
    echo "تم إنشاء الجدول بنجاح";
} else {
    echo "خطأ في إنشاء الجدول: " . $conn->error;
}

// إغلاق الاتصال
$conn->close();
?>

</body>
</html>