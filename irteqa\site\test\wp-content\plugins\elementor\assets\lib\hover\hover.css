/*!
 * Hover.css (http://ianlunn.github.io/Hover/)
 * Version: 2.0.2
 * Author: <PERSON> @IanLunn
 * Author URL: http://ianlunn.co.uk/
 * Github: https://github.com/IanLunn/Hover

 * Made available under a MIT License:
 * http://www.opensource.org/licenses/mit-license.php

 * Hover.css Copyright <PERSON> 2014. Generated with Sass.
 */
/* 2D TRANSITIONS */
/* Grow */
.elementor-animation-grow {
  transition-duration: 0.3s;
  transition-property: transform;
}
.elementor-animation-grow:hover {
  transform: scale(1.1);
}

/* Shrink */
.elementor-animation-shrink {
  transition-duration: 0.3s;
  transition-property: transform;
}
.elementor-animation-shrink:hover {
  transform: scale(0.9);
}

/* Pulse */
@keyframes elementor-animation-pulse {
  25% {
    transform: scale(1.1);
  }
  75% {
    transform: scale(0.9);
  }
}
.elementor-animation-pulse:hover {
  animation-name: elementor-animation-pulse;
  animation-duration: 1s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

/* Pulse Grow */
@keyframes elementor-animation-pulse-grow {
  to {
    transform: scale(1.1);
  }
}
.elementor-animation-pulse-grow:hover {
  animation-name: elementor-animation-pulse-grow;
  animation-duration: 0.3s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}

/* Pulse Shrink */
@keyframes elementor-animation-pulse-shrink {
  to {
    transform: scale(0.9);
  }
}
.elementor-animation-pulse-shrink:hover {
  animation-name: elementor-animation-pulse-shrink;
  animation-duration: 0.3s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-direction: alternate;
}

/* Push */
@keyframes elementor-animation-push {
  50% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(1);
  }
}
.elementor-animation-push:hover {
  animation-name: elementor-animation-push;
  animation-duration: 0.3s;
  animation-timing-function: linear;
  animation-iteration-count: 1;
}

/* Pop */
@keyframes elementor-animation-pop {
  50% {
    transform: scale(1.2);
  }
}
.elementor-animation-pop:hover {
  animation-name: elementor-animation-pop;
  animation-duration: 0.3s;
  animation-timing-function: linear;
  animation-iteration-count: 1;
}

/* Bounce In */
.elementor-animation-bounce-in {
  transition-duration: 0.5s;
}
.elementor-animation-bounce-in:hover {
  transform: scale(1.2);
  transition-timing-function: cubic-bezier(0.47, 2.02, 0.31, -0.36);
}

/* Bounce Out */
.elementor-animation-bounce-out {
  transition-duration: 0.5s;
}
.elementor-animation-bounce-out:hover {
  transform: scale(0.8);
  transition-timing-function: cubic-bezier(0.47, 2.02, 0.31, -0.36);
}

/* Rotate */
.elementor-animation-rotate {
  transition-duration: 0.3s;
  transition-property: transform;
}
.elementor-animation-rotate:hover {
  transform: rotate(4deg);
}

/* Grow Rotate */
.elementor-animation-grow-rotate {
  transition-duration: 0.3s;
  transition-property: transform;
}
.elementor-animation-grow-rotate:hover {
  transform: scale(1.1) rotate(4deg);
}

/* Float */
.elementor-animation-float {
  transition-duration: 0.3s;
  transition-property: transform;
  transition-timing-function: ease-out;
}
.elementor-animation-float:hover {
  transform: translateY(-8px);
}

/* Sink */
.elementor-animation-sink {
  transition-duration: 0.3s;
  transition-property: transform;
  transition-timing-function: ease-out;
}
.elementor-animation-sink:hover {
  transform: translateY(8px);
}

/* Bob */
@keyframes elementor-animation-bob {
  0% {
    transform: translateY(-8px);
  }
  50% {
    transform: translateY(-4px);
  }
  100% {
    transform: translateY(-8px);
  }
}
@keyframes elementor-animation-bob-float {
  100% {
    transform: translateY(-8px);
  }
}
.elementor-animation-bob:hover {
  animation-name: elementor-animation-bob-float, elementor-animation-bob;
  animation-duration: .3s, 1.5s;
  animation-delay: 0s, .3s;
  animation-timing-function: ease-out, ease-in-out;
  animation-iteration-count: 1, infinite;
  animation-fill-mode: forwards;
  animation-direction: normal, alternate;
}

/* Hang */
@keyframes elementor-animation-hang {
  0% {
    transform: translateY(8px);
  }
  50% {
    transform: translateY(4px);
  }
  100% {
    transform: translateY(8px);
  }
}
@keyframes elementor-animation-hang-sink {
  100% {
    transform: translateY(8px);
  }
}
.elementor-animation-hang:hover {
  animation-name: elementor-animation-hang-sink, elementor-animation-hang;
  animation-duration: .3s, 1.5s;
  animation-delay: 0s, .3s;
  animation-timing-function: ease-out, ease-in-out;
  animation-iteration-count: 1, infinite;
  animation-fill-mode: forwards;
  animation-direction: normal, alternate;
}

/* Skew */
.elementor-animation-skew {
  transition-duration: 0.3s;
  transition-property: transform;
}
.elementor-animation-skew:hover {
  transform: skew(-10deg);
}

/* Skew Forward */
.elementor-animation-skew-forward {
  transition-duration: 0.3s;
  transition-property: transform;
  transform-origin: 0 100%;
}
.elementor-animation-skew-forward:hover {
  transform: skew(-10deg);
}

/* Skew Backward */
.elementor-animation-skew-backward {
  transition-duration: 0.3s;
  transition-property: transform;
  transform-origin: 0 100%;
}
.elementor-animation-skew-backward:hover {
  transform: skew(10deg);
}

/* Wobble Vertical */
@keyframes elementor-animation-wobble-vertical {
  16.65% {
    transform: translateY(8px);
  }
  33.3% {
    transform: translateY(-6px);
  }
  49.95% {
    transform: translateY(4px);
  }
  66.6% {
    transform: translateY(-2px);
  }
  83.25% {
    transform: translateY(1px);
  }
  100% {
    transform: translateY(0);
  }
}
.elementor-animation-wobble-vertical:hover {
  animation-name: elementor-animation-wobble-vertical;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
}

/* Wobble Horizontal */
@keyframes elementor-animation-wobble-horizontal {
  16.65% {
    transform: translateX(8px);
  }
  33.3% {
    transform: translateX(-6px);
  }
  49.95% {
    transform: translateX(4px);
  }
  66.6% {
    transform: translateX(-2px);
  }
  83.25% {
    transform: translateX(1px);
  }
  100% {
    transform: translateX(0);
  }
}
.elementor-animation-wobble-horizontal:hover {
  animation-name: elementor-animation-wobble-horizontal;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
}

/* Wobble To Bottom Right */
@keyframes elementor-animation-wobble-to-bottom-right {
  16.65% {
    transform: translate(8px, 8px);
  }
  33.3% {
    transform: translate(-6px, -6px);
  }
  49.95% {
    transform: translate(4px, 4px);
  }
  66.6% {
    transform: translate(-2px, -2px);
  }
  83.25% {
    transform: translate(1px, 1px);
  }
  100% {
    transform: translate(0, 0);
  }
}
.elementor-animation-wobble-to-bottom-right:hover {
  animation-name: elementor-animation-wobble-to-bottom-right;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
}

/* Wobble To Top Right */
@keyframes elementor-animation-wobble-to-top-right {
  16.65% {
    transform: translate(8px, -8px);
  }
  33.3% {
    transform: translate(-6px, 6px);
  }
  49.95% {
    transform: translate(4px, -4px);
  }
  66.6% {
    transform: translate(-2px, 2px);
  }
  83.25% {
    transform: translate(1px, -1px);
  }
  100% {
    transform: translate(0, 0);
  }
}
.elementor-animation-wobble-to-top-right:hover {
  animation-name: elementor-animation-wobble-to-top-right;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
}

/* Wobble Top */
@keyframes elementor-animation-wobble-top {
  16.65% {
    transform: skew(-12deg);
  }
  33.3% {
    transform: skew(10deg);
  }
  49.95% {
    transform: skew(-6deg);
  }
  66.6% {
    transform: skew(4deg);
  }
  83.25% {
    transform: skew(-2deg);
  }
  100% {
    transform: skew(0);
  }
}
.elementor-animation-wobble-top {
  transform-origin: 0 100%;
}
.elementor-animation-wobble-top:hover {
  animation-name: elementor-animation-wobble-top;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
}

/* Wobble Bottom */
@keyframes elementor-animation-wobble-bottom {
  16.65% {
    transform: skew(-12deg);
  }
  33.3% {
    transform: skew(10deg);
  }
  49.95% {
    transform: skew(-6deg);
  }
  66.6% {
    transform: skew(4deg);
  }
  83.25% {
    transform: skew(-2deg);
  }
  100% {
    transform: skew(0);
  }
}
.elementor-animation-wobble-bottom {
  transform-origin: 100% 0;
}
.elementor-animation-wobble-bottom:hover {
  animation-name: elementor-animation-wobble-bottom;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
}

/* Wobble Skew */
@keyframes elementor-animation-wobble-skew {
  16.65% {
    transform: skew(-12deg);
  }
  33.3% {
    transform: skew(10deg);
  }
  49.95% {
    transform: skew(-6deg);
  }
  66.6% {
    transform: skew(4deg);
  }
  83.25% {
    transform: skew(-2deg);
  }
  100% {
    transform: skew(0);
  }
}
.elementor-animation-wobble-skew:hover {
  animation-name: elementor-animation-wobble-skew;
  animation-duration: 1s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: 1;
}

/* Buzz */
@keyframes elementor-animation-buzz {
  50% {
    transform: translateX(3px) rotate(2deg);
  }
  100% {
    transform: translateX(-3px) rotate(-2deg);
  }
}
.elementor-animation-buzz:hover {
  animation-name: elementor-animation-buzz;
  animation-duration: 0.15s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

/* Buzz Out */
@keyframes elementor-animation-buzz-out {
  10% {
    transform: translateX(3px) rotate(2deg);
  }
  20% {
    transform: translateX(-3px) rotate(-2deg);
  }
  30% {
    transform: translateX(3px) rotate(2deg);
  }
  40% {
    transform: translateX(-3px) rotate(-2deg);
  }
  50% {
    transform: translateX(2px) rotate(1deg);
  }
  60% {
    transform: translateX(-2px) rotate(-1deg);
  }
  70% {
    transform: translateX(2px) rotate(1deg);
  }
  80% {
    transform: translateX(-2px) rotate(-1deg);
  }
  90% {
    transform: translateX(1px) rotate(0);
  }
  100% {
    transform: translateX(-1px) rotate(0);
  }
}
.elementor-animation-buzz-out:hover {
  animation-name: elementor-animation-buzz-out;
  animation-duration: 0.75s;
  animation-timing-function: linear;
  animation-iteration-count: 1;
}

/*# sourceMappingURL=hover.css.map */
