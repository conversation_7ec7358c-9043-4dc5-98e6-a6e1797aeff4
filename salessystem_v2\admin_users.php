<?php
/**
 * صفحة إدارة المستخدمين
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('manage_users')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

global $main_db;

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = intval($_POST['user_id'] ?? 0);
    
    switch ($action) {
        case 'toggle_status':
            if ($user_id > 0) {
                $stmt = $main_db->prepare("UPDATE users SET is_active = NOT is_active WHERE id = ?");
                $stmt->bind_param("i", $user_id);
                if ($stmt->execute()) {
                    logActivity('user_status_changed', 'users', $user_id, null, null, 'تغيير حالة المستخدم');
                    $_SESSION['success'] = 'تم تغيير حالة المستخدم بنجاح';
                } else {
                    $_SESSION['error'] = 'فشل في تغيير حالة المستخدم';
                }
                $stmt->close();
            }
            break;

        case 'reset_password':
            if ($user_id > 0) {
                $new_password = 'user123'; // كلمة مرور افتراضية
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

                $stmt = $main_db->prepare("UPDATE users SET password = ? WHERE id = ?");
                $stmt->bind_param("si", $hashed_password, $user_id);

                if ($stmt->execute()) {
                    logActivity('password_reset', 'users', $user_id, null, null, "تم إعادة تعيين كلمة مرور المستخدم ID: $user_id");
                    $_SESSION['success'] = "تم إعادة تعيين كلمة المرور بنجاح. كلمة المرور الجديدة: $new_password";
                } else {
                    $_SESSION['error'] = 'فشل في إعادة تعيين كلمة المرور';
                }
                $stmt->close();
            }
            break;

        case 'delete_user':
            if ($user_id > 0) {
                // حذف قاعدة بيانات المستخدم
                $user_db_name = "sales_system_user_" . $user_id;
                $main_db->query("DROP DATABASE IF EXISTS `$user_db_name`");
                
                // حذف المستخدم من الجدول الرئيسي
                $stmt = $main_db->prepare("DELETE FROM users WHERE id = ?");
                $stmt->bind_param("i", $user_id);
                if ($stmt->execute()) {
                    logActivity('user_deleted', 'users', $user_id, null, null, 'حذف المستخدم وقاعدة بياناته');
                    $_SESSION['success'] = 'تم حذف المستخدم وجميع بياناته بنجاح';
                } else {
                    $_SESSION['error'] = 'فشل في حذف المستخدم';
                }
                $stmt->close();
            }
            break;
    }
    
    header("Location: admin_users.php");
    exit();
}

// جلب المستخدمين مع الإحصائيات والترقيم
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;
$offset = ($page - 1) * $per_page;

$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(username LIKE ? OR full_name LIKE ? OR email LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'sss';
}

if ($status_filter !== '') {
    $where_conditions[] = "is_active = ?";
    $params[] = intval($status_filter);
    $param_types .= 'i';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// استعلام العد الإجمالي
$count_query = "SELECT COUNT(*) as total FROM users $where_clause";
if (!empty($params)) {
    $count_stmt = $main_db->prepare($count_query);
    $count_stmt->bind_param($param_types, ...$params);
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
} else {
    $total_records = $main_db->query($count_query)->fetch_assoc()['total'];
}

$total_pages = ceil($total_records / $per_page);

// استعلام البيانات مع الترقيم
$query = "SELECT id, username, full_name, email, phone, is_active, last_login, created_at FROM users $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";

if (!empty($params)) {
    $stmt = $main_db->prepare($query);
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $users_result = $stmt->get_result();
} else {
    $users_result = $main_db->query($query);
}

require_once __DIR__ . '/includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" href="admin_users.php">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_activity.php">
                            <i class="fas fa-history me-2"></i>سجل العمليات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الشاملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_financial_reports.php">
                            <i class="fas fa-chart-line me-2"></i>التقارير المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_system.php">
                            <i class="fas fa-cogs me-2"></i>إعدادات النظام
                        </a>
                    </li>
                    <?php if (hasAdminPermission('manage_admins')): ?>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_manage_admins.php">
                            <i class="fas fa-user-shield me-2"></i>إدارة المديرين
                        </a>
                    </li>
                    <?php endif; ?>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الشاملة
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">إدارة المستخدمين</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData('excel', 'admin_export.php?type=users')">
                        <i class="fas fa-download me-1"></i>تصدير Excel
                    </button>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="اسم المستخدم، الاسم الكامل، أو البريد الإلكتروني">
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">جميع الحالات</option>
                                <option value="1" <?php echo $status_filter === '1' ? 'selected' : ''; ?>>نشط</option>
                                <option value="0" <?php echo $status_filter === '0' ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-1"></i>بحث
                            </button>
                            <a href="admin_users.php" class="btn btn-secondary">
                                <i class="fas fa-redo me-1"></i>إعادة تعيين
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول المستخدمين -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">قائمة المستخدمين</h6>
                    <span class="text-muted">إجمالي: <?php echo number_format($total_records); ?> مستخدم</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>اسم المستخدم</th>
                                    <th>الاسم الكامل</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th>آخر دخول</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($user = $users_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td><?php echo htmlspecialchars($user['phone'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <span class="badge <?php echo $user['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $user['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php 
                                        if ($user['last_login']) {
                                            echo date('Y-m-d H:i', strtotime($user['last_login']));
                                        } else {
                                            echo '<span class="text-muted">لم يسجل دخول</span>';
                                        }
                                        ?>
                                    </td>
                                    <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="admin_user_details.php?id=<?php echo $user['id']; ?>"
                                               class="btn btn-sm btn-info" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>

                                            <a href="admin_financial.php?user_id=<?php echo $user['id']; ?>"
                                               class="btn btn-sm btn-primary" title="التقرير المالي">
                                                <i class="fas fa-chart-line"></i>
                                            </a>

                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="btn btn-sm <?php echo $user['is_active'] ? 'btn-warning' : 'btn-success'; ?>"
                                                        title="<?php echo $user['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                    <i class="fas <?php echo $user['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                                </button>
                                            </form>

                                            <button type="button" class="btn btn-sm btn-secondary"
                                                    onclick="resetUserPassword(<?php echo $user['id']; ?>)"
                                                    title="إعادة تعيين كلمة المرور">
                                                <i class="fas fa-key"></i>
                                            </button>

                                            <form method="POST" style="display: inline;"
                                                  onsubmit="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ سيتم حذف جميع بياناته نهائياً!')">
                                                <input type="hidden" name="action" value="delete_user">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="btn btn-sm btn-danger" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- التنقل بين الصفحات -->
                    <?php if ($total_pages > 1): ?>
                    <?php
                    // بناء معاملات الاستعلام بدون معامل page
                    $query_params = $_GET;
                    unset($query_params['page']);
                    $query_string = !empty($query_params) ? '&' . http_build_query($query_params) : '';
                    ?>
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo $query_string; ?>">السابق</a>
                            </li>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo $query_string; ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>

                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo $query_string; ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
function resetUserPassword(userId) {
    if (confirm('هل تريد إعادة تعيين كلمة مرور هذا المستخدم؟')) {
        // إنشاء نموذج مخفي لإرسال الطلب
        const form = document.createElement('form');
        form.method = 'POST';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'reset_password';

        const userIdInput = document.createElement('input');
        userIdInput.type = 'hidden';
        userIdInput.name = 'user_id';
        userIdInput.value = userId;

        form.appendChild(actionInput);
        form.appendChild(userIdInput);
        document.body.appendChild(form);
        form.submit();
    }
}

function exportUsers() {
    window.location.href = 'admin_users.php?action=export';
}

function bulkAction() {
    const checkboxes = document.querySelectorAll('input[name="selected_users[]"]:checked');
    const action = document.getElementById('bulk_action').value;

    if (checkboxes.length === 0) {
        alert('يرجى اختيار مستخدم واحد على الأقل');
        return;
    }

    if (!action) {
        alert('يرجى اختيار إجراء');
        return;
    }

    let confirmMessage = '';
    switch(action) {
        case 'activate':
            confirmMessage = 'هل تريد تفعيل المستخدمين المحددين؟';
            break;
        case 'deactivate':
            confirmMessage = 'هل تريد إلغاء تفعيل المستخدمين المحددين؟';
            break;
        case 'delete':
            confirmMessage = 'هل تريد حذف المستخدمين المحددين؟ سيتم حذف جميع بياناتهم نهائياً!';
            break;
    }

    if (confirm(confirmMessage)) {
        document.getElementById('bulk_form').submit();
    }
}

// تحديد/إلغاء تحديد جميع المستخدمين
function toggleAllUsers() {
    const selectAll = document.getElementById('select_all');
    const checkboxes = document.querySelectorAll('input[name="selected_users[]"]');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}
</script>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
