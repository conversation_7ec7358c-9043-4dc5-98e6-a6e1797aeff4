<!DOCTYPE html>
<html lang="en">
<head>
<title>CV Makerr</title>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1, user-scalable=0">

   <link rel="stylesheet" href="./css/bootstrap.min.css">
   <link rel="stylesheet" href="./css/normalize.css">
   <link rel="stylesheet" href="./css/cv-maker.css">
   <style>
    body {
    min-width: 1280px; 
}
    </style>
</head>
<body>
        


<div data-html2canvas-ignore="true" class="cv_templates_on_page_load">
    <span onclick="tabsSwitchcv(event, 'CV_one')" class="cv_template Template_1 btn tablinks_cv_template">Template 1</span>
    <span onclick="tabsSwitchcv(event, 'CV_two')" class="cv_template Template_2 btn tablinks_cv_template">Template 2</span>
    <span onclick="tabsSwitchcv(event, 'CV_three')" class="cv_template Template_3 btn tablinks_cv_template">Template 3</span>
    <span onclick="tabsSwitchcv(event, 'CV_four')" class="cv_template Template_4 btn tablinks_cv_template">Template 4</span>
</div>


<div class="cv_page_explained" id="cv_page_explained">
        <span class="jackInAnotherBox angela">
            <svg xmlns="https://www.w3.org/2000/svg" width="55" height="35">
                <g fill="none" stroke="#333c4e" stroke-width="1.5" stroke-linecap="round" stroke-miterlimit="10">
                    <path d="M48.569 27.616c-4.254-4.052-15.443 2.992-20.225 3.767-18.011 2.916-23.035-13.832-27.109-28.1" />
                    <path stroke-linejoin="round" d="M41.907 31.488c3.811-2.175 7.875-.701 11.857-2.026-2.17-2.355-6.355-7.329-6.883-10.66" />
                </g>
            </svg>
            <p class="cv_explain_label ">Choose a Template From Here</p>
        </span>
        <span class="cv_page_notice">For The best experience we encourage you to use a PC while Creating your CV</span>
        <span class="jackInAnotherBox samantha">
            <p class="cv_explain_label ">Download , Save or Share Your CV from HERE</p>
            <svg xmlns="https://www.w3.org/2000/svg" width="55" height="35">
                <g fill="none" stroke="#333c4e" stroke-width="1.5" stroke-linecap="round" stroke-miterlimit="10">
                    <path d="M48.569 27.616c-4.254-4.052-15.443 2.992-20.225 3.767-18.011 2.916-23.035-13.832-27.109-28.1" />
                    <path stroke-linejoin="round" d="M41.907 31.488c3.811-2.175 7.875-.701 11.857-2.026-2.17-2.355-6.355-7.329-6.883-10.66" />
                </g>
            </svg>
        </span>
</div>




<!-- Modal -->

<div class="capture_all" id="capture">
    <div id="CV_one" class="container-fluid mt-5 cv_template_tabcontent hide_instaFade">

      <div class="row">
        <div class="col-md-12">
          <div class="jumbotron row">
              <div class="personal_info col-md-6">
    <!--            <h1 class="display-4" id="printFullName">Full Name</h1>-->
                  <textarea rows="2" class="input_FullName isplay-4" id="input_FullName" placeholder="Full Name"></textarea>
                <input autocomplete="off" class="printFullAddress lead" id="printFullAddress" placeholder="Address">
    <!--              <p class="lead" id="printFullAddress">Complete Address</p>-->
                  <p><a id="printContactEmail" href="#"><input class="printContactEmail lead" id="printContactEmail" placeholder="Email"></a></p>
              </div>
        <div class="form-group photo_of_user col-md-6">
            <label data-html2canvas-ignore="true" for="">Photo</label>
            <input data-html2canvas-ignore="true" type="file" class="form-control photo_for_template_1">
        </div>


    </div>
        <section id="bio">
    <!--      <h2>Biography</h2>-->
        <input autocomplete="off" class="input_title_cv" id="input_title_cv" placeholder="BIOGRAPHY">

            <hr>
          <div class="contentItems">
            <p id="printBioData">
            <textarea class="text_Biography isplay-4 text_textareas" id="text_Biography" placeholder="Biography"></textarea>
              </p>
          </div>
          </section>

          <section id="exp">
    <!--      <h2>EXPERIENCE</h2>-->
          <input autocomplete="off" class="text_EXPERIENCE input_title_cv" id="text_EXPERIENCE" placeholder="EXPERIENCE">

            <hr>
              <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="createExpElemetns()">Add an Experience</button>
              <div id="displayExperiences" class="text-center">

                   <div class="contentItems" id="printExpData">
                       <div class="resume-item d-flex flex-column flex-md-row justify-content-between mb-5">
                          <div class="resume-content">
                            <h3 class="mb-0" id="printExpTitle"><input autocomplete="off" class="position_epxo lead text_textareas" id="position_epxo" placeholder="Position"></h3>
                            <div class="subheading mb-3" id="printExpComp"><input autocomplete="off" class="Company_epxo lead text_textareas" id="Company_epxo" placeholder="Company Name"></div>
                            <p>
                                <textarea class="text_expo text_textareas" id="text_expo" placeholder="Descripe Your work there"></textarea>
                            </p>
                          </div>
                          <div class="resume-date text-md-right">
                              <span class="text-primary" id="printExpDateStart"><input class="form-control text_textareas" type="date" id="myExpDateStart"></span> - 
                              <span class="text-primary" id="printExpDateEnd"><input class="form-control text_textareas" type="date" id="myExpDateEnd"></span>
                <!--            <span class="text-primary" id="printExpDateStart">March 2013</span> - <span class="text-primary" id="printExpDateEnd">Present</span>-->
                          </div>
                        </div>
                      </div>

              </div>








          </section>

          <section id="edu">
    <!--      <h2>EDUCATION</h2>-->
          <input autocomplete="off" class="title_EDUCATION input_title_cv" id="title_EDUCATION" placeholder="EDUCATION">

            <hr>
          <div class="contentItems">
            <p id="printEduData">
            <textarea class="text_EDUCATION isplay-4 text_textareas" id="text_EDUCATION" placeholder="EDUCATION"></textarea>
            </p>
          </div>
          </section>

          <section id="skill">
    <!--      <h2>SKILLS</h2>-->
          <input autocomplete="off" class="title_SKILLS input_title_cv" id="title_SKILLS" placeholder="SKILLS">
            <hr>
         <div class="contentItems">
            <p id="printSkillsData">
            <textarea class="text_SKILLS isplay-4 text_textareas" id="text_SKILLS" placeholder="SKILLS"></textarea>
            </p>
          </div>
          </section>

          <section id="int">
    <!--      <h2>INTERESTS</h2>-->
          <input autocomplete="off" class="title_INTERESTS input_title_cv" id="title_INTERESTS" placeholder="INTERESTS">

            <hr>
           <div class="contentItems">
            <p id="printIntData">
            <textarea class="text_INTERESTS isplay-4 text_textareas" id="text_INTERESTS" placeholder="INTERESTS"></textarea>
            </p>
          </div>
          </section>



          <h3></h3>
        </div>
      </div>
    </div>

    <div id="CV_two" class="instaFade  cv_template_tabcontent hide_instaFade">
        <div class="mainDetails">
            <div id="headshot" class="quickFade">
                <div class="form-group photo_of_user_2">
                    <label data-html2canvas-ignore="true" for="" photo_for_template_2_label>Photo</label>
                    <input data-html2canvas-ignore="true" type="file" class="form-control photo_for_template_2">
                </div>
            </div>

            <div id="name">
                <h1 class="quickFade delayTwo"><input autocomplete="off" class="cv2-Name lead cv_2_inputs" id="cv2-Name" placeholder="Name"></h1>
                <h2 class="quickFade delayThree"><input autocomplete="off" class="cv2-job lead cv_2_inputs" id="cv2-job" placeholder="Job Title"></h2>
            </div>

            <div id="contactDetails" class="quickFade delayFour">
                <ul>
                    <li>Email: <a href="#" target=""><input autocomplete="off" class="cv2-Email lead cv_2_inputs" id="cv2-Email" placeholder="Email"></a></li>
                    <li>Website: <a href="#"><input autocomplete="off" class="cv2-Website lead cv_2_inputs" id="cv2-Website" placeholder="Website"></a></li>
                    <li>Phone: <input autocomplete="off" class="cv2-Phone lead cv_2_inputs" id="cv2-Phone" placeholder="Phone Number"></li>
                </ul>
            </div>
            <div class="clear"></div>
        </div>

        <div id="mainArea" class="quickFade delayFive">
            <section>
                <article>
                    <div class="sectionTitle">
                        <h1><input autocomplete="off" class="cv2-Biography lead cv_2_inputs cv_2_titles" id="cv2-Biography" placeholder="Biography"></h1>
                    </div>

                    <div class="sectionContent">
                        <p>
                            <textarea class="text_Biography text_textareas" id="text_Biography" placeholder="Descripe youe self"></textarea>					
                        </p>
                    </div>
                </article>
                <div class="clear"></div>
            </section>


            <section>
                <div class="sectionTitle">
                    <h1><input autocomplete="off" class="cv2-Experience lead cv_2_inputs cv_2_titles" id="cv2-Experience" placeholder="Work Experience"></h1>
                </div>

                <div class="sectionContent CreateCompany">
                    <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="CreateCompany()">Add a Company</button>
                    <article>
                        <h2><input autocomplete="off" class="cv2-University lead cv_2_inputs" id="cv2-University" placeholder="Job Title at Company"></h2>
                        <p class="subDetails">
                            <input autocomplete="off" class="cv2-subDetails lead cv_2_inputs" id="cv2-subDetails" placeholder="October 2004 - December 2006">
                        </p>
                        <p>
                            <textarea class="text_Biography text_textareas" id="text_Biography" placeholder="Descripe your Job"></textarea>
                        </p>
                    </article>
                </div>
                <div class="clear"></div>
            </section>


            <section>
                <div class="sectionTitle">
                    <h1><input autocomplete="off" class="cv2-Skills lead cv_2_inputs cv_2_titles" id="cv2-Skills" placeholder="Key Skills"></h1>
                </div>

                <div class="sectionContent">
                    <ul class="keySkills" id="keySkills">
                    <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="createskill11()">Add an Skill</button>
                        <li><input autocomplete="off" class="cv2-Skills lead cv_2_inputs" id="cv2-keySkills" placeholder="A Skill"></li>
                    </ul>
                </div>
                <div class="clear"></div>
            </section>


            <section>
                <div class="sectionTitle">
                    <h1><input autocomplete="off" class="cv2-Education lead cv_2_inputs cv_2_titles" id="cv2-Education" placeholder="Education"></h1>
                </div>

                <div class="sectionContent Educations">
                    <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="CreateEducation()">Add an Education</button>
                    <article>
                        <h2><input autocomplete="off" class="cv2-University lead cv_2_inputs" id="cv2-University" placeholder="College/University"></h2>
                        <p class="subDetails"><input autocomplete="off" class="cv2-subDetails lead cv_2_inputs" id="cv2-subDetails" placeholder="subDetails"></p>
                        <p>
                            <textarea class="text_Biography text_textareas" id="text_Biography" placeholder="Descripe your Eduction"></textarea>
                        </p>
                    </article>


                </div>
                <div class="clear"></div>
            </section>

        </div>
    </div>

    <div id="CV_three" class="yui-t7 hide_instaFade cv_template_tabcontent">
        <div id="inner">

            <div id="hd">
                <div class="yui-gc">
                    <div class="yui-u first">
                        <h1><input autocomplete="off" class="cv3-Education lead cv_2_inputs cv_3_titles" id="cv3_name" placeholder="name"></h1>
                        <h2><input autocomplete="off" class="cv3-Job lead cv_2_inputs cv_3_titles" id="cv3_Job" placeholder="Job Title"></h2>
                    </div>

                    <div class="yui-u">
                        <div class="contact-info">
                            <h3><input autocomplete="off" class="cv3-Address lead cv_2_inputs" id="cv2-Address" placeholder="Address"></h3>
                            <h3><a href="#"><input autocomplete="off" class="cv3-Email lead cv_2_inputs" id="cv2-Email" placeholder="Email"></a></h3>
                            <h3><input autocomplete="off" class="cv3-Phone lead cv_2_inputs" id="cv2-Phone" placeholder="+201063452166"></h3>
                        </div><!--// .contact-info -->
                    </div>
                </div><!--// .yui-gc -->
            </div><!--// hd -->

            <div id="bd">
                <div id="yui-main">
                    <div class="yui-b">

                        <div class="yui-gf">
                            <div class="yui-u first">
                                <h2><input autocomplete="off" class="cv3-Profile lead cv_2_inputs cv_3_sub_titles" id="cv3_Profile" placeholder="Profile"></h2>
                            </div>
                            <div class="yui-u">
                                <p class="enlarge">
                                    <textarea class="text_Biography text_textareas" id="text_Biography" placeholder="Descripe your Eduction"></textarea>
                                </p>
                            </div>
                        </div><!--// .yui-gf -->

                        <div class="yui-gf">
                            <div class="yui-u first">
                                <h2><input autocomplete="off" class="cv3-Skills lead cv_2_inputs cv_3_sub_titles" id="cv3_Skills" placeholder="talents"></h2>
                            </div>
                            <div class="yui-u CreateSkill">
                                    <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="CreateSkill()">Add a talent</button>
                                    <div class="talent">
                                        <h2><input autocomplete="off" class="cv3-talent lead cv_2_inputs cv_3_sub_titles" id="cv3_talent" placeholder="talent"></h2>
                                        <p>
                                            <textarea class="text_Biography text_textareas" id="text_Skills" placeholder="Descripe your Skill"></textarea>
                                        </p>
                                    </div>
                            </div>
                        </div><!--// .yui-gf -->

                        <div class="yui-gf">
                            <div class="yui-u first">
                                <h2><input autocomplete="off" class="cv3-Technical lead cv_2_inputs cv_3_sub_titles" id="cv3_Technical" placeholder="Technical"></h2>
                            </div>
                            <div class="yui-u CreateSkill2">
                                <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="CreateSkill2()">Add a skill</button>

                                <ul class="talent">
                                    <li><input autocomplete="off" class="cv3-skill lead cv_2_inputs" id="cv3_skill" placeholder="skill"></li>
                                    <li><input autocomplete="off" class="cv3-skill lead cv_2_inputs" id="cv3_skill" placeholder="skill"></li>
                                    <li class="last"><input autocomplete="off" class="cv3-skill lead cv_2_inputs" id="cv3_skill" placeholder="skill"></li>
                                </ul>

                            </div>
                        </div><!--// .yui-gf-->

                        <div class="yui-gf">

                            <div class="yui-u first">
                                <h2><input autocomplete="off" class="cv3-Experience lead cv_2_inputs cv_3_sub_titles" id="cv3_Experience" placeholder="Experience"></h2>
                            </div><!--// .yui-u -->

                            <div class="yui-u Createjob3">
                                <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="Createjob3()">Add a Job</button>

                                <div class="job">
                                    <h2><input autocomplete="off" class="cv3-Company lead cv_2_inputs" id="cv3_Company" placeholder="Company"></h2>
                                    <h3><input autocomplete="off" class="cv3-Job lead cv_2_inputs" id="cv3_Job3" placeholder="Job title"></h3>
                                    <h4>
                                        <div class="resume-date3 text-md-right">
                                            <span class="text-primary" id="printExpDateStart3"><input class="form-control text_textareas" type="date" id="myExpDateStart3"></span> 
                                            <span class="text-primary" id="printExpDateEnd3"><input class="form-control text_textareas" type="date" id="myExpDateEnd3"></span>
                                        </div>
                                    </h4>
                                    <p class="text_for_jobsss"><textarea class="text_Biography text_textareas" id="text_Skills" placeholder="Descripe your Job"></textarea></p>
                                </div>


                            </div><!--// .yui-u -->
                        </div><!--// .yui-gf -->


                        <div class="yui-gf last">
                            <div class="yui-u first">
                                <h2><input autocomplete="off" class="cv3-Education lead cv_2_inputs cv_3_sub_titles" id="cv3_Education" placeholder="Education"></h2>
                            </div>
                            <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="Createuniversity33()">Add an Education</button>
                            <div class="Createuniversity33">
                            <div class="yui-u">
                                <h2><input autocomplete="off" class="cv3-university lead cv_2_inputs" id="cv3_university" placeholder="university"></h2>
                                <p class="subDetails"><input autocomplete="off" class="cv2-subDetails lead cv_2_inputs" id="cv2-subDetails" placeholder="subDetails"></p>
                                <p>
                                    <textarea class="text_Biography text_textareas" id="text_Biography" placeholder="Descripe your Eduction"></textarea>
                                </p>
                            </div></div>
                        </div><!--// .yui-gf -->


                    </div><!--// .yui-b -->
                </div><!--// yui-main -->
            </div><!--// bd -->

            <div id="ft">
                <span class="cv3_last_inputs_span">
                <input autocomplete="off" class="cv3-Profile lead cv_2_inputs cv3_last_inputs" id="cv3_Profile" placeholder="Name">
                <input autocomplete="off" class="cv3-Profile lead cv_2_inputs cv3_last_inputs" id="cv3_Profile" placeholder="Email">
                 <input autocomplete="off" class="cv3-Profile lead cv_2_inputs cv3_last_inputs" id="cv3_Profile" placeholder="Phone"></span>
            </div><!--// footer -->

        </div><!-- // inner -->


    </div><!--// doc -->

    <div id="CV_four" class="cv_template_tabcontent hide_instaFade template_4_img">
        <!-- Header -->
        <header role="banner">
            <div class="container_16">
                    <hgroup class="header_hgroup_namejob">
                        <h1><input autocomplete="off" class="cv4-Education lead cv_2_inputs cv4_name_header text_818dv" id="cv4_name363" placeholder="Your Name"></h1>
                        <h2><input autocomplete="off" class="cv4-Education lead cv_2_inputs cv4_job_header text_818dv" id="cv4_name6536" placeholder="Your Job"></h2>
                    </hgroup>

                    <figure>
                <div class="form-group photo_of_user_4">
                    <label data-html2canvas-ignore="true" for="" photo_for_template_2_label>Your Photo</label>
                    <input data-html2canvas-ignore="true" type="file" class="form-control photo_for_template_4">
                </div>


    <!--					<img src="./imgs/avatar.jpg" alt="Sheldon COOPER">-->
                    </figure>
            </div>
        </header>


        <!-- Corps -->
        <section role="main" class="container_16 clearfix">
            <div class="grid_16">
                <!-- A propos -->
                <div class="grid_8 apropos">
                    <h3><div class="circle_8"><span class="dashicons dashicons-admin-users"></span></div><input autocomplete="off" class="cv4-Education lead cv_2_inputs cv_4_titles text_818dv" id="cv4_apropos" placeholder="Profile"></h3>
                    <p><textarea class="text_9452 text_textareas text_818dv" id="text_9452" placeholder="Profile Subtitle"></textarea></p>
                    <p><textarea class="text_945692 text_textareas text_818dv" id="text_945692" placeholder="Descripe yourSelf"></textarea></p>
                </div>

                <!-- Compétences -->
                <div class="grid_8 competences">
                    <h3><div class="circle_8"><span class="dashicons dashicons-universal-access"></span></div><input autocomplete="off" class="cv4-Education lead cv_2_inputs cv_4_titles text_818dv" id="cv4_name" placeholder="Skills"></h3>
                    <ul class="barres CreateCompétences">
                        <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="CreateCompétences()">Add a Skill</button>

                        <li><input autocomplete="off" class="cv4-Comptences lead cv_2_inputs text_818dv" id="cv3_Compétences" placeholder="a Skill"></li>
    <!--
                        <li data-skills="60">Compétences 2<span style="width: 60%;"></span></li>
                        <li data-skills="75">Compétences 3<span style="width: 75%;"></span></li>
                        <li data-skills="40">Compétences 4<span style="width: 40%;"></span></li>
                        <li data-skills="95">Compétences 5<span style="width: 95%;"></span></li>
    -->
                    </ul>
                </div>
            </div>

                <!-- Expériences -->
                <div class="grid_16 experiences">
                    <h3><div class="circle_8"><span class="dashicons dashicons-backup"></span></div><input autocomplete="off" class="cv4-Education lead cv_2_inputs cv_4_titles text_818dv" id="cv4_experiences" placeholder="Experiences"></h3>
                    <ul class="Createexperiences">
                        <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="Createexperiences()">Add a job Experience</button>

                        <li>
                            <h4><strong><input autocomplete="off" class="cv4-Comptences lead cv_2_inputs text_818dv" id="cv3_Compétences" placeholder="Company"></strong><input autocomplete="off" class="cv4-Comptences lead cv_2_inputs cv_3_titles text_818dv" id="cv3_Compétences" placeholder="Job Title"></h4>				
                            <h4>
                                <div class="resume-date3 text-md-right">
                                    <span class="text-primary" id="printExpDateStart3"><input class="form-control text_textareas" type="date" id="myExpDateStart3"></span>
                                    <span class="text-primary" id="printExpDateEnd3"><input class="form-control text_textareas" type="date" id="myExpDateEnd3"></span>
                                </div>
                            </h4>

                            <p><textarea class="text_818 text_textareas text_818dv" id="text_818" placeholder="Descripe your Job Experience"></textarea></p>
                        </li>
    <!--
                        <li>
                            <h4><strong>Nom du poste</strong> chez nom de l'employeur</h4>
                            <span class="lieu">Lieu</span>
                            <span class="dates">Dates</span>
                            <p>Une petite description du poste, décrivez votre rôle et vos 
    tâches en quelques mots afin que le recruteur en sache plus sur la 
    nature de votre travail.</p>
                        </li>
    -->
                    </ul>
                </div>

                <!-- Formations -->
                <div class="grid_16 formations">
                    <h3><div class="circle_8"><span class="dashicons dashicons-welcome-learn-more"></span></div><input autocomplete="off" class="cv4-Formations lead cv_2_inputs cv_4_titles text_818dv" id="cv3_Formations" placeholder="Education"></h3>
                    <ul class="Createformations">
                        <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="Createformations()">Add an Education</button>

                        <li>
                            <h4><strong><input autocomplete="off" class="cv3-Compétences lead cv_2_inputs text_818dv" id="cv4_Compétences" placeholder="Bachelor's in Civil Engineering"></strong><input autocomplete="off" class="cv3-Comptences lead cv_2_inputs cv_3_titles text_818dv" id="cv3_Compétences" placeholder="University of Cairo"></h4>
                            <h4>
                                <div class="resume-date3 text-md-right">
                                    <span class="text-primary" id="printExpDateStart3"><input class="form-control text_textareas" type="date" id="myExpDateStart3"></span>
                                    <span class="text-primary" id="printExpDateEnd3"><input class="form-control text_textareas" type="date" id="myExpDateEnd3"></span>
                                </div>
                            </h4>
                            <p><textarea class="text_Biography text_textareas text_818dv" id="text_Biography" placeholder="Descripe your Eduction"></textarea></p>
                        </li>
    <!--
                        <li>
                            <h4><strong>Nom de la formation / diplôme</strong> à nom de l'école</h4>
                            <span class="lieu">Lieu</span>
                            <span class="dates">Période</span>
                            <p>Clarifiez la formation (les abréviations ne sont pas connues de tout le monde).</p>
                        </li>
    -->
                    </ul>
                </div>

                <!-- Loisirs -->
                <div class="grid_8 loisirs">
                    <h3><div class="circle_8"><span class="dashicons dashicons-groups"></span></div><input autocomplete="off" class="cv4-Loisirs lead cv_2_inputs cv4_titles cv_4_titles text_818dv" id="cv4_Loisirs" placeholder="Refrences"></h3>
    <!--
                    <p><strong>Sports :</strong> si vous en pratiquez</p>
                    <p><strong>Association :</strong> si vous êtes membre d'une association</p>
                    <p>D'autres loisirs plus vagues, complétez ici.</p>
    -->

                    <ul class="CreatRefrences">
                        <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="CreatRefrences()">Create another Refrence</button>
                        <li class="refrence">
                            <p class="ref_name"><strong><input autocomplete="off" class="cv3-Loisirs lead cv_2_inputs cv_3_titles text_818dv" id="cv3_Loisirs" placeholder="Refrence's Job"></strong></p><p class="ref_name"><strong><input autocomplete="off" class="cv3-Loisirs lead cv_2_inputs cv_3_titles text_818dv" id="cv3_Loisirs" placeholder="Refrence's Name"></strong></p><p><textarea class="ref_contact_details text_Biography text_textareas text_818dv" id="text_Biography" placeholder="Refrence's Contact Details"></textarea></p>
                        </li>

                    </ul>




                </div>

                <!-- Contact -->
                <div class="grid_8 contact">
                    <h3><div class="circle_8"><span class="dashicons dashicons-phone"></span></div><input autocomplete="off" class="cv4-Contact lead cv_2_inputs cv_4_titles text_818dv" id="cv3_Contact" placeholder="Contact"></h3>
                    <p><input autocomplete="off" class="cv4-Loisirs lead cv_2_inputs cv4_titles text_818dv" id="cv4_contactinfo" placeholder="Create an intro for contact info"></p>
                    <ul class="Creatcontactbox">
                        <button data-html2canvas-ignore="true" type="button" class="btn btn-success btn-block" onclick="Creatcontactbox()">Create another contact box</button>
                        <li class=""><input autocomplete="off" class="cv3-Loisirs lead cv_2_inputs cv_3_titles text_818dv" id="cv3_Loisirs" placeholder="Phone, Address , Email , ..."></li>
    <!--					<li class="phone">06 00 00 00 00</li>-->
    <!--					<li class="mail"><a href="mailto:<EMAIL>"><EMAIL></a></li>-->

                    </ul>
                </div>
        </section></div>
</div>


<div data-html2canvas-ignore="true" class="div_to_cover_top_of_screen hide_instaFade"></div>


<div data-html2canvas-ignore="true" class="div_to_cover_ths_screen hide_instaFade"><img width="100%" class="screen" id="screen"></div>

<div data-html2canvas-ignore="true" class="html2canvas_thing">

    <div class="the_image">
       <span class="dashicons dashicons-no-alt"></span>
        <canvas width="1366" height="695" style="width: 1366px; height: 695px; position: fixed; top: 0px; left: 0px; opacity: 1; transform: scale(0.8); z-index: 99999999; transition: transform 0.3s cubic-bezier(0.42, 0, 0.58, 1) 0s, opacity 0.3s cubic-bezier(0.42, 0, 0.58, 1) 0s, -webkit-transform 0.3s cubic-bezier(0.42, 0, 0.58, 1) 0s;">
        </canvas>
    </div>
    
    <div class="control_area ">
          

        <div id="tryhtml2canvas" class="the_trigger_for_menu">
           <span class="dashicons dashicons-yes dashicon_for_trigger_menu"></span>
        </div>
        
        
        
        <div class="triggered_menu hide_triggered_menu wrapper bg-transparent">
            <div class="wave"></div>
            <div class="settings_content">

                <h4>Hey</h4>
                <p class="print_out_message">We are Here to help you create a stunning CV ,<br> Start by choosing a template and then fill all the blanks</p>
<!--               <button class="btn btn-outline-primary my-2 my-sm-0"  id="buildCVbtn" data-toggle="modal" data-target=".exampleModal-modal-xl" type="button">Create</button>-->

<!--                <span class="edit_cv btn">Edit</span>-->
                <span class="btn_for_print_out btn">Download</span>
                <span class="style_cv btn">Templates</span>
                <div class="cv_templates hide_instaFade">
                    <span onclick="tabsSwitchcv(event, 'CV_one')" class="cv_template Template_1 btn tablinks_cv_template">Template 1</span>
                    <span onclick="tabsSwitchcv(event, 'CV_two')" class="cv_template Template_2 btn tablinks_cv_template">Template 2</span>
                    <span onclick="tabsSwitchcv(event, 'CV_three')" class="cv_template Template_3 btn tablinks_cv_template">Template 3</span>
                    <span onclick="tabsSwitchcv(event, 'CV_four')" class="cv_template Template_4 btn tablinks_cv_template">Template 4</span>
                    
                </div>
        </div>
        </div>
        
    </div>
</div>


<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
<script type="text/javascript" src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/14082/FileSaver.js"></script>
<script src="./js/html2canvas.min.js"></script>
<script src="./js/cv-maker.js"></script>

</body>
</html>    