<style>
  /* ستايلات للخلفية السوداء التي تعمل كطبقة تظليل */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* لون الخلفية مع شفافية */
    display: none;
    /* إخفاء الطبقة بشكل افتراضي */
    justify-content: center;
    align-items: center;
    z-index: 999;
    /* تحديد ترتيب الطبقة */
  }

  /* ستايلات لصندوق النافذة المنبثقة */
  .popup {
    width: 450px;
    height: 250px;
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  }

  .newitem {
    padding: 10px;
    background: rgb(233, 233, 233);
    border-radius: 10px;
    border: 1px solid rgb(192, 192, 192);
    display: inline-grid;
    justify-content: center;
  }

  .newtitl {
    border: 1px solid #b1b1b1;
    padding: 20px;
    border-radius: 10px;
    margin-top: 25px;
    margin-bottom: 5px;
    place-self: center;
  }

  .sctntitl {
    background: #e9e9e9;
    position: relative;
    top: -60px;
    right: 0px;
    border: 1px solid #ababab;
    padding: 5px;
    border-radius: 10px;
    width: max-content;
    margin: 0;
  }

  .titlfrm {
    display: flex;
    align-items: center;
  }

  input[type="text"] {
    width: 300px;
    height: 30px;
    border-radius: 5px;
    border: 2px solid #c5c5c5;
    margin: 5px;
    font-weight: bold;
    font-size: 1.3em;
    padding: 5px;
  }

  input[type="button"] {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
    margin: 3px;
    font-weight: bold;
    font-size: 1.5em;
  }

  input[type="button"]:hover {
    background-color: #a0d9ff;
    color: rgb(71, 71, 71);
  }
</style>
<!-- الطبقة السوداء التي تعمل كظليل -->
<div class="overlay" id="overlay">
  <!-- صندوق النافذة المنبثقة -->
  <div class="popup">

    <div class="newtitl">
      <h1 class="sctntitl">إضافة قسم</h1>
      <label for="" name="awnser"></label>
      <div action="" method="post" class="titlfrm">
        <input type="text" name="table_name">
        <input type="button" name="submit_button" value="أضف">
      </div>
    </div>
    <button onclick="closePopup()">إغلاق</button>
  </div>
  </div>
  <?php include "conn/config.php";
  $dbname = "servsads";
  
  $table_name = $_POST["table_name"];
  // التحقق من إرسال النموذج
  if (isset($_POST["submit_button"])) {
    // التحقق من إدخال اسم الجدول
    if (!empty($_POST["table_name"])) {
      // قراءة قيمة اسم الجدول من النموذج
      
  
      // إنشاء اتصال
      $conn = new mysqli($servername, $username, $password, $dbname);
  
      // التحقق من الاتصال
      if ($conn->connect_error) {
          die("فشل الاتصال: " . $conn->connect_error);
      }
  
      // استعلام CREATE TABLE لإنشاء الجدول الجديد
      $sql_create_table = "CREATE TABLE IF NOT EXISTS $table_name (
           id INT AUTO_INCREMENT PRIMARY KEY,
    firstname VARCHAR(30) NOT NULL,
    lastname VARCHAR(30) NOT NULL,
    email VARCHAR(50),
    reg_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
  
      // تنفيذ الاستعلام
      if ($conn->query($sql_create_table) === TRUE) {
          echo "تم إنشاء الجدول $table_name بنجاح";
      } else {
          echo "خطأ في إنشاء الجدول: " . $conn->error;
      }
  
      // إغلاق الاتصال
      $conn->close();
    } else {
      echo "يرجى إدخال اسم الجدول";
    }
  }
  ?>
  <script>
    // دالة لفتح النافذة المنبثقة
    function openPopup() {
      document.getElementById("overlay").style.display = "flex";
    }

    // دالة لإغلاق النافذة المنبثقة
    function closePopup() {
      document.getElementById("overlay").style.display = "none";
    }
  </script>