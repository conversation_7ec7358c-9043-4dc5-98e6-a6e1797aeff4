name: "CodeQL"

on:
  push:
    branches:
      - master
      - v4-dev
      - "!dependabot/**"
  pull_request:
    # The branches below must be a subset of the branches above
    branches:
      - master
  schedule:
    - cron: "0 0 * * 0"

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      # Initializes the CodeQL tools for scanning.
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v1
        with:
          languages: "javascript"
          config-file: ./.github/codeql/codeql-config.yml

      - name: Autobuild
        uses: github/codeql-action/autobuild@v1

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v1
