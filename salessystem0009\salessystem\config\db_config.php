<?php
// تكوين اتصال قاعدة البيانات الرئيسية
define('MAIN_DB_HOST', 'localhost');
define('MAIN_DB_USER', 'root');
define('MAIN_DB_PASS', '');
define('MAIN_DB_NAME', 'sales_system_main');

// إنشاء اتصال بقاعدة البيانات الرئيسية
$main_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, MAIN_DB_NAME);
$main_db->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
$main_db->set_charset("utf8mb4");

if ($main_db->connect_error) {
    die("Connection failed: " . $main_db->connect_error);
}

// دالة للحصول على اتصال بقاعدة بيانات المستخدم
function getUserDBConnection($user_id) {
    $db_name = "sales_system_user_" . $user_id;

    $conn = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, $db_name);
    $conn->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
    $conn->set_charset("utf8mb4");

    if ($conn->connect_error) {
        error_log("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
        die("Connection failed: " . $conn->connect_error);
    }

    return $conn;
}
?>