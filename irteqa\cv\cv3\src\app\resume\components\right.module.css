
  
  .container {
    position: relative;
    perspective: 800px;
  }
  
  .cube {
    width: 100px;
    height: 100px;
    position: absolute;
    top: 40%;
    left: 35%;
    transform: translate(-50%, -50%) rotateX(0) rotateY(0) rotateZ(0);
    transform-style: preserve-3d;
    animation: rotateBox 5s linear infinite;
    z-index: -1;
  }
  
  .cube .face {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-family: monospace;
    background-color: #007bff;
    border: 2px solid #000;
  }
  
  .front {
    transform: translateZ(50px);
  }
  
  .back {
    transform: translateZ(-50px) rotateY(180deg);
  }
  
  .top {
    transform: translateY(-50px) rotateX(90deg);
  }
  
  .bottom {
    transform: translateY(50px) rotateX(-90deg);
  }
  
  .left {
    transform: translateX(-50px) rotateY(-90deg);
  }
  
  .right {
    transform: translateX(50px) rotateY(90deg);
  }
  
  @keyframes rotateBox {
    0% {
      transform: rotateX(0) rotateY(0) rotateZ(0);
    }
    50% {
      transform: rotateX(180deg) rotateY(180deg) rotateZ(180deg);
    }
    100% {
      transform: rotateX(360deg) rotateY(360deg) rotateZ(360deg);
    }
  }
  
  /* Position the social media share buttons */
  .social-buttons {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  
  .social-buttons a {
    margin-bottom: 10px;
  }
  
  .social-buttons img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }
  