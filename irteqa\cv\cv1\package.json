{"name": "cv", "version": "0.3.1", "title": "Modern CV, Resume and Portfolio website", "description": "Best-in-Class website template for modern CV, Resume and Portfolio. Easy customizable All-in-One-Page static website with builder.", "homepage": "https://github.com/tbaltrushaitis/cv", "bugs": "https://github.com/tbaltrushaitis/cv/issues", "license": "MIT", "author": "Baltrushait<PERSON> Tomas <<EMAIL>>", "private": false, "repository": {"type": "git", "url": "git+https://github.com/tbaltrushaitis/cv.git"}, "website": "http://bit.ly/tomascv?from=cv-package.json", "readmeFilename": "README.md", "engines": {"node": ">=12.18.4", "npm": ">=6.14.8"}, "os": ["linux", "win32", "darwin"], "main": "echo [Called MAIN script via package.json]", "scripts": {"preinstall": "${TOILET} 'NPM: PRE-INSTALL'", "install": "${TOILET} 'NPM: INSTALL'", "postinstall": "${TOILET} 'NPM: POST-INSTALL'", "preuninstall": "${TOILET} 'NPM: PRE-UNINSTALL'", "uninstall": "${TOILET} 'NPM: UNINSTALL'", "postuninstall": "${TOILET} 'NPM: POST-UNINSTALL'", "start": "${TOILET} 'NPM: START'", "test": "${TOILET} 'NPM: TEST'; DEBUG=* gulp test --color", "\n\n####\t\t\t BUILD:": "", "setup-": "echo [SETUP];", "setup": "${TOILET} 'NPM: SETUP'", "bower": "${TOILET} 'NPM: BOWER'; gulp bower --color", "prepare": "${TOILET} 'NPM: PREPARE'", "prebuild": "${TOILET} 'NPM: PRE-BUILD'; gulp populate --color", "build": "${TOILET} 'NPM: BUILD'; gulp build --color", "postbuild": "${TOILET} 'NPM: POST-BUILD'", "rebuild": "${TOILET} 'NPM: RE-BUILD'", "prepack": "${TOILET} 'NPM: PRE-PACK'", "deploy": "${TOILET} 'NPM: DEPLOY'; gulp deploy --color", "dist": "${TOILET} 'NPM: DIST'; gulp dist --color", "\n\n####\t\t\t BATCH COMMANDS:": "", "default": "echo [DEFAULT]; DEBUG=* gulp default --color", "all": "echo [ALL]; DEBUG=* gulp --color", "prod": "echo [PROD]; gulp --env=production --color", "\n\n####\t\t\t TEST:": "", "check": "${TOILET} 'NPM: CHECK'; DEBUG=* gulp lint --color", "\n\n####\t\t\t INFORMATION:": "", "config": "${TOILET} 'NPM: SHOW:CONFIG'; gulp show:config --color", "changed": "${TOILET} 'NPM: CHANGED'; gulp show:src --color", "help": "${TOILET} 'NPM: HELP'; gulp --color", "list": "${TOILET} 'NPM: LIST'; gulp --tasks --depth 1 --color", "usage": "${TOILET} 'NPM: USAGE'; gulp usage --color", "\n\n####\t\t\t DEVELOPMENT:": "", "dev": "${TOILET} 'NPM: DEV'; DEBUG=* gulp dev --color", "live": "${TOILET} 'NPM: LIVE'; livereload ./webroot --port 35729 -d -w 1000", "watch": "${TOILET} 'NPM: WATCH'; DEBUG=* gulp watch --color", "\n# END": ""}, "scripts-disabled": {"crit": "echo [CRITICAL]; DEBUG=* gulp critical --color", "populate": "${TOILET} 'NPM: POPULATE'; gulp populate --color", "preinstall": "[ -f ./NODE_ENV ] || cp -prv config/.NODE_ENV ./NODE_ENV ; [ -f ./.bowerrc ] || cp -prv config/.bowerrc ./ ;", "pretest": "${TOILET} 'NPM: PRE-TEST'", "testt": "${TOILET} 'NPM: TEST'; DEBUG=* ${GULP} test", "posttest": "${TOILET} 'NPM: POST-TEST'"}, "directories": {"test": "test"}, "dependencies": {}, "dependencies-disabled": {"figlet": "^1.4.0", "figlet-cli": "^0.1.1"}, "devDependencies": {"bower": "^1.8.14", "dateformat": "4.6.3", "del": "6.1.1", "dotenv": "^14.3.2", "eslint": "^8.24.0", "gulp": "^4.0.2", "gulp-changed": "^4.0.3", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-concat-css": "^3.1.0", "gulp-directory-sync": "^1.2.4", "gulp-ext-replace": "^0.3.0", "gulp-filter": "^7.0.0", "gulp-headerfooter": "^1.0.3", "gulp-htmlmin": "^5.0.1", "gulp-if": "^3.0.0", "gulp-imagemin": "^7.1.0", "gulp-jimp": "^1.2.9", "gulp-jscs": "^4.1.0", "gulp-jshint": "^2.1.0", "gulp-livereload": "^4.0.2", "gulp-replace": "^1.1.3", "gulp-size": "^4.0.1", "gulp-terser": "^2.0.1", "gulp-token-replace": "^1.1.2", "gulp-watch": "^5.0.1", "imagemin-giflossy": "^5.1.10", "imagemin-gifsicle": "^7.0.0", "imagemin-mozjpeg": "^9.0.0", "imagemin-pngquant": "^9.0.2", "imagemin-webp": "^6.0.0", "jimp": "^0.16.1", "jpeg-js": "^0.4.4", "jshint": "^2.13.4", "jshint-stylish": "^2.2.1", "js-yaml": ">=4.1.0", "livereload": "^0.9.3", "lodash": "^4.17.21", "lodash.template": "^4.5.0", "main-bower-files": "^2.13.3", "merge-stream": "^2.0.0", "minimist": ">=1.2.3", "read-config": "^2.0.0", "vinyl-paths": "^3.0.1", "yargs": "^17.0.1"}, "devDependencies-DEPRECATED": {"critical": "^1.3.4", "gulp-add-src": "^1.0.0", "gulp-exec": "^3.0.2", "gulp-file-include": "^2.0.1", "gulp-newer": "^1.4.0", "gulp-rename": "^1.4.0", "gulp-require-tasks": "^1.3.0", "gulp-sequence": "^1.0.0", "gulp-template": "^5.0.0", "gulp-util": "^3.0.8", "jquery": "^3.3.1", "nconf": "^0.10.0", "nodemon": "^1.18.10", "websocket-extensions": ">=0.1.4"}, "options": {"bower": {"debugging": false, "paths": {"bowerDirectory": "bower_modules", "bowerrc": ".bower<PERSON>", "bowerJson": "bower.json"}}, "clean": {"debug": true, "processImport": true}, "cleanCSS": {"debug": true, "processImport": true}, "exec": {"continueOnError": false, "pipeStdout": false}, "file": {"encoding": "utf8", "flag": "r"}, "minify": {"suffix": ".min"}, "reporting": {"err": true, "stderr": true, "stdout": true}, "sync": {"printSummary": true, "nodelete": true}, "uglify": {"compress": {"sequences": true, "properties": true, "dead_code": true, "drop_debugger": true, "unsafe": true, "conditionals": true, "comparisons": true, "evaluate": true, "booleans": true, "loops": true, "unused": true, "hoist_funs": true, "hoist_vars": false, "if_return": true, "join_vars": true, "side_effects": true, "warnings": true, "global_defs": {}}, "mangle": false, "output": {"indent_start": 0, "indent_level": 2, "quote_keys": false, "ascii_only": false, "inline_script": false, "width": 80, "max_line_len": 32000, "beautify": false, "source_map": null, "bracketize": false, "comments": false, "semicolons": true}}, "watch": {"ignoreInitial": false, "verbose": true, "readDelay": 200}, "livereload": {"start": true, "port": 35729, "quiet": false}, "iopts": {"showHidden": false, "depth": 5, "colors": true}, "readconf": {"basedir": null, "replaceEnv": "%", "replaceLocal": "@", "override": true, "skipUnresolved": true}, "readconf-disabled": {}, "htmlmin": {"collapseWhitespace": true, "collapseInlineTagWhitespace": true, "conservativeCollapse": true, "removeComments": true}, "terser": {"keep_fnames": true, "mangle": true}, "jimp": {"type": "png", "autocrop": {"cropOnlyFrames": false, "tolerance": 0.0002}, "resize": {"height": 180, "width": 270}}, "giflossy": {"interlaced": false, "optimizationLevel": 3, "lossy": 146, "resize-off": "100x100", "resizeMethod": "sample", "colorMethod": "blend-diversity", "optimize": "3"}, "gifsicle": {"interlaced": false, "optimizationLevel": 3}, "mozjpeg": {"quality": 50, "progressive": true}, "pngquant": {"quality": [0.5, 0.5]}, "webp": {"method": 4, "quality": 50}}, "resolutions": {"graceful-fs": "^4.2.4"}, "jshintConfig": {}, "keywords": ["bio", "cv", "cv-template", "portfolio", "portfolio-website", "portfolio-template", "resume", "skills", "skill-demo", "Technical", "Full-Stack"]}