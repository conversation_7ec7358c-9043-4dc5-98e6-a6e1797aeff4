# دليل الترقية إلى النسخة الثانية - salessystem_v2

## نظرة عامة
هذا الدليل يوضح كيفية الترقية من النظام القديم إلى النسخة المحسنة التي تستخدم قاعدة البيانات الموجودة `u193708811_system_main`.

## التغييرات الرئيسية

### 1. قواعد البيانات
- **القديم:** قاعدة بيانات منفصلة لكل مستخدم
- **الجديد:** قاعدتا بيانات:
  - `u193708811_system_main` - المستخدمين والمديرين (المستخدم: sales01)
  - `u193708811_operations` - العمليات مع جداول بادئة (المستخدم: sales02)

### 2. نظام الجداول
- **القديم:** `sales_system_user_1` → `customers`, `products`, etc.
- **الجديد:** `u193708811_operations` → `ahmed_customers`, `sara_products`, etc.

## خطوات الترقية

### الخطوة 1: النسخ الاحتياطي
```bash
# إنشاء نسخة احتياطية من قاعدة البيانات الحالية
mysqldump -u root -p u193708811_system_main > backup_before_upgrade.sql
```

### الخطوة 2: تشغيل صفحة التحديث
1. افتح المتصفح واذهب إلى:
   ```
   http://localhost:808/salessystem_v2/update_database.php
   ```

2. تحقق من:
   - ✅ الاتصال بقاعدة البيانات الرئيسية
   - ✅ إنشاء قاعدة بيانات العمليات
   - ✅ ترحيل بيانات المستخدمين

### الخطوة 3: اختبار النظام
1. افتح صفحة الاختبار:
   ```
   http://localhost:808/salessystem_v2/test_system.php
   ```

2. تحقق من:
   - ✅ اتصال قواعد البيانات
   - ✅ دوال البادئة
   - ✅ إنشاء الجداول
   - ✅ تحديث الاستعلامات

### الخطوة 4: اختبار التسجيل والدخول
1. سجل مستخدم جديد:
   ```
   http://localhost:808/salessystem_v2/register.php
   ```

2. تحقق من إنشاء الجداول تلقائياً

3. سجل دخول واختبر العمليات الأساسية

## الملفات المحدثة

### ملفات التكوين الجديدة:
- `config/db_config.php` - إعدادات قواعد البيانات المحدثة
- `config/database_migration.php` - دوال الترحيل
- `includes/database_helper.php` - دوال مساعدة

### ملفات الاختبار:
- `test_system.php` - اختبار شامل للنظام
- `update_database.php` - صفحة التحديث
- `UPGRADE_GUIDE.md` - هذا الدليل

## الدوال الجديدة

### دوال البادئة:
```php
// الحصول على بادئة المستخدم
$prefix = getUserTablePrefix('ahmed'); // النتيجة: 'ahmed_'

// الحصول على اسم الجدول مع البادئة
$table = getUserTableName('customers', 'ahmed'); // النتيجة: 'ahmed_customers'

// تحديث الاستعلام ليستخدم البادئة
$query = "SELECT * FROM customers";
$updated = updateQueryWithUserPrefix($query, 'ahmed');
// النتيجة: "SELECT * FROM ahmed_customers"
```

### دوال إدارة الجداول:
```php
// إنشاء جداول المستخدم
createUserTables('ahmed');

// فحص وجود جدول
$exists = userTableExists('customers', 'ahmed');

// عدد السجلات
$count = getUserTableCount('customers', 'ahmed');

// قائمة جداول المستخدم
$tables = getUserTables('ahmed');
```

## مثال عملي

### قبل الترقية:
```php
// المستخدم ahmed (ID: 1)
// قاعدة البيانات: sales_system_user_1
// الجداول: customers, products, sales, etc.

$db = getUserDBConnection(1);
$result = $db->query("SELECT * FROM customers");
```

### بعد الترقية:
```php
// المستخدم ahmed
// قاعدة البيانات: u193708811_operations
// الجداول: ahmed_customers, ahmed_products, ahmed_sales, etc.

$db = getCurrentUserDB();
$query = updateQueryWithUserPrefix("SELECT * FROM customers", 'ahmed');
$result = $db->query($query); // SELECT * FROM ahmed_customers
```

## التوافق مع النظام القديم

### الدوال المحافظة:
```php
// هذه الدوال تعمل مع النظام الجديد بدون تغيير
getCurrentUserDB()      // تعيد اتصال قاعدة بيانات العمليات
getUserDBConnection()   // تعيد اتصال قاعدة بيانات العمليات
createUserDatabase()    // تستدعي createUserTables()
```

### الترقية التلقائية:
- عند تسجيل دخول مستخدم موجود، يتم إنشاء جداوله تلقائياً
- لا حاجة لتعديل الكود الموجود في معظم الحالات
- النظام يتعرف على المستخدمين الموجودين

## استكشاف الأخطاء

### مشكلة: فشل الاتصال بقاعدة البيانات
**الحل:**
1. تحقق من إعدادات قاعدة البيانات في `config/db_config.php`
2. تأكد من وجود قاعدة البيانات `u193708811_system_main`
3. تحقق من صلاحيات المستخدم

### مشكلة: لا يتم إنشاء الجداول
**الحل:**
1. تحقق من صلاحيات إنشاء الجداول
2. افحص ملفات السجل للأخطاء
3. استخدم صفحة الاختبار للتشخيص

### مشكلة: بيانات المستخدمين مفقودة
**الحل:**
1. استخدم دوال الترحيل في `database_migration.php`
2. تحقق من وجود قواعد البيانات القديمة
3. استعد النسخة الاحتياطية إذا لزم الأمر

## الصيانة والمراقبة

### مراقبة الأداء:
```php
// فحص عدد الجداول
$tables = getUserTables('ahmed');
echo "عدد جداول المستخدم: " . count($tables);

// فحص أحجام الجداول
foreach ($tables as $table) {
    $count = getUserTableCount(str_replace('ahmed_', '', $table), 'ahmed');
    echo "$table: $count سجل\n";
}
```

### تنظيف النظام:
```php
// حذف جداول مستخدم (للاختبار فقط)
dropUserTables('test_user');

// تنظيف قواعد البيانات القديمة (بعد التأكد من الترحيل)
$old_databases = cleanupOldDatabases();
```

## الأمان

### نصائح الأمان:
1. **النسخ الاحتياطي:** اعمل نسخ احتياطي منتظم
2. **الصلاحيات:** استخدم صلاحيات محدودة لقاعدة البيانات
3. **المراقبة:** راقب سجل العمليات
4. **التحديثات:** اختبر التحديثات على نسخة تجريبية

### فحص الأمان:
```php
// فحص سلامة البيانات
$verification = verifyMigration('ahmed');
foreach ($verification as $table => $status) {
    if (!$status['exists'] || !$status['structure_ok']) {
        echo "تحذير: مشكلة في جدول $table\n";
    }
}
```

## الدعم الفني

### ملفات السجل:
- تحقق من ملفات PHP error log
- استخدم `error_log()` لتسجيل الأخطاء المخصصة
- راجع سجل قاعدة البيانات

### أدوات التشخيص:
1. `test_system.php` - اختبار شامل
2. `update_database.php` - فحص التحديث
3. دوال التحقق في `database_migration.php`

### الحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. استخدم أدوات التشخيص
3. تحقق من ملفات السجل
4. اعمل نسخة احتياطية قبل أي تغيير

---

**تاريخ آخر تحديث:** 2024-12-19  
**الإصدار:** 2.0  
**المطور:** نظام المبيعات المحسن
