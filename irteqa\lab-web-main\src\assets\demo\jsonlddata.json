{"data": {"profile": {"heading": "Profile"}, "objective": {"enable": true, "heading": "Professional Objective"}, "work": {"enable": true, "heading": "Work Experience"}, "education": {"enable": true, "heading": "Education"}, "awards": {"enable": true, "heading": "Honors & Awards"}, "certifications": {"enable": true, "heading": "Certifications"}, "skills": {"enable": true, "heading": "Skills"}, "Memberships": {"enable": true, "heading": "Memberships"}, "languages": {"enable": true, "heading": "Languages"}, "references": {"enable": true, "heading": "References"}, "extras": {"enable": true, "heading": "Additional Information"}, "address": {"enable": true, "heading": "Address"}, "jsonld": {"@context": ["https://jsonldresume.github.io/skill/context.json", {"gender": {"@id": "schema:gender", "@type": "@vocab"}, "skill:classOfAward": {"@id": "skill:classOfAward", "@type": "@vocab"}, "skill:securityClearance": {"@id": "skill:securityClea<PERSON>", "@type": "@vocab"}, "category": {"@id": "schema:category", "@type": "@vocab"}, "dayOfWeek": {"@id": "schema:dayOfWeek", "@type": "@vocab"}}], "@graph": [{"award": [{"@type": "skill:Award", "@id": "_:cebcd613-36a8-4cde-851c-b2879b140ef0#enable", "skill:title": "Judge", "skill:conferredBy": "Peak-to-Peak chartered school", "description": "judge at their science fair"}, {"@type": "skill:Award", "@id": "_:cba9b860-347a-4969-a6f0-612f4218764c#enable", "skill:title": "3rd place", "skill:conferredBy": "national physics federtion", "description": "Third place in national physics competition"}]}, {"givenName": [{"@language": "en", "@value": "<PERSON><PERSON><PERSON>"}, {"@language": "ar", "@value": "<PERSON><PERSON><PERSON><PERSON>"}], "familyName": [{"@language": "en", "@value": "<PERSON><PERSON><PERSON>"}, {"@language": "ar", "@value": "شریعتی"}], "address": [{"@id": "_:f5ef8e0e-89c8-4100-ae22-81887cf3f373", "@type": "PostalAddress", "hoursAvailable": {"@id": "_:f5ef8e0e-89c8-4100-ae22-81887cf3f373#hoursAvailable", "@type": "OpeningHoursSpecification", "validThrough": "2099-01-01"}, "addressCountry": "Canada", "streetAddress": "20 Test dr", "addressRegion": "ON", "addressLocality": "Toronto", "postalCode": "H0H 0H0", "contactType": "", "sameAs": "https://www.google.com/maps/place/Rosedale+Golf+Club/@43.7259752,-79.4099022,14z/data=!4m8!1m2!2m1!1s20+test+dr!3m4!1s0x882b32b8192bf409:0x809cd74491c3e5b9!8m2!3d43.7364762!4d-79.3992555"}], "description": "Full-stack developer", "sameAs": ["https://jsonldresume.org"], "image": {"contentUrl": "https://static.projectmanagement.com/images/profile-photos/46843803.jpg", "@id": "_:#image", "@type": "ImageObject"}, "contactPoint": [{"@id": "_:2467fb27-b4a8-4953-93fa-ee27ef20c7a7", "@type": "ContactPoint", "description": "This is main contact", "contactType": "Preferred", "email": "<EMAIL>", "telephone": "+****************"}], "seeks": [{"@id": "_:fe2bddc5-0c95-4e33-b245-d0c1eb22789a", "@type": "Demand", "description": "Innovative and dedicated Application Developer and Project Manager, with more than 10 years of experience leading application development, IT service, and engineering projects. Partners with CEOs, department heads, and internal and external stakeholders, developing powerful solutions to operational challenges and instituting leading-edge technologies. Ensures fiscal restraint and detailed scheduling, effectively controlling project costs and timelines to consistently deliver projects to successful conclusion. Leads and motivates staff and contractors, building top-performing teams by applying a situational leadership style and managing projects in Agile environment. <PERSON>y fluent in English and Farsi, with skills in Arabic. I also have experience in stock trading and investment management and fundamental and technical analysis of companies and stocks.", "availabilityStarts": "2021-01-01", "availabilityEnds": "2022-01-01", "availableAtOrFrom": {"address": {"@type": "PostalAddress", "@id": "_:fe2bddc5-0c95-4e33-b245-d0c1eb22789a_availableAtOrFrom_address", "addressLocality": "Toronto", "addressRegion": "ON", "addressCountry": "Canada"}, "@type": "Place", "@id": "_:fe2bddc5-0c95-4e33-b245-d0c1eb22789a_availableAtOrFrom"}, "deliveryLeadTime": {}}], "hasOccupation": [{"@type": "EmployeeRole", "@id": "_:a905d9ec-1a1f-4629-8db2-b3ec2e626f58#enable", "hasOccupation": {"@id": "_:a905d9ec-1a1f-4629-8db2-b3ec2e626f58#hasOccupation", "@type": "Occupation", "name": "", "skills": ["Project Management", "Programming"], "responsibilities": ["Directing company", "Hiring talents", "Managing financials", "Insure daily follow-up on requests, mini-projects and issues"]}, "subjectOf": {"@type": "BusinessEvent", "id": "_:a905d9ec-1a1f-4629-8db2-b3ec2e626f58#subjectOf", "organizer": {"@type": "Organization", "id": "_:a905d9ec-1a1f-4629-8db2-b3ec2e626f58#subjectOf#organizer", "name": "Direction X Corporation"}}, "roleName": "Director", "startDate": "2018-01-01", "endDate": "2021-01-01", "description": "Delivered successful projects that drove improvements to business outcomes and KPIs"}], "hasCredential": [{"@type": "EducationalOccupationalCredential", "@id": "_:86baf285-43a0-4ed9-9b73-85533b4d3f86#enable", "aggregateRating": {"@id": "_:86baf285-43a0-4ed9-9b73-85533b4d3f86#aggregateRating", "@type": "aggregateRating", "bestRating": "4", "ratingValue": "3.7", "name": "GPA", "itemReviewed": {"@id": "_:86baf285-43a0-4ed9-9b73-85533b4d3f86#enable"}}, "credentialCategory": "degree", "educationalLevel": "Masters of Science", "abstract": "I was an active member of university of Colorado associations.", "teaches": ["Electrical systems", "Programming"], "about": {"@id": "_:86baf285-43a0-4ed9-9b73-85533b4d3f86#about", "@type": "EducationalOccupationalProgram", "educationalCredentialAwarded": "Electrical Engineering", "startDate": "2008-01-01", "endDate": "2010-01-01", "provider": {"@id": "_:86baf285-43a0-4ed9-9b73-85533b4d3f86#about#provider", "@type": "CollegeOrUniversity", "name": "University of Colorado at Boudler"}}}], "memberOf": [{"@id": "_:028ef777-abf2-4a9b-a08d-e587bcae6751#enable", "@type": "Role", "startDate": "2018-01-01", "endDate": "2019-01-01", "roleName": "member", "memberOf": {"@id": "_:028ef777-abf2-4a9b-a08d-e587bcae6751#memberOf", "@type": "ProgramMembership", "url": "", "programName": "Salsa Dance", "description": ""}}], "knowsLanguage": [{"@type": "Language", "@id": "_:7e78b654-5439-463c-a7cf-9a0f5eec5f70", "name": "English"}, {"@type": "Language", "@id": "_:55fa78ed-7afb-4e3e-89e1-25e32088b951", "name": "<PERSON><PERSON>"}], "interactionStatistic": [{"@id": "_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a", "@type": "InteractionCounter", "disambiguatingDescription": "Reference", "interactionType": {"@id": "_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a#interactionType", "@type": "AssessAction", "participant": {"@id": "_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a#interactionType#participant", "@type": "Person", "givenName": "<PERSON>", "familyName": "<PERSON><PERSON>", "jobTitle": "CEO, imaginary corp", "telephone": "+****************", "email": "<EMAIL>"}, "result": [{"@id": "_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a#result", "@type": "Review", "itemReviewed": {}, "reviewAspect": [], "reviewRating": {"@id": "_:Reference#bd94bed6-a193-4f5c-8988-aec8fb28869a#result#reviewRating", "@type": "Rating", "ratingValue": "5", "bestRating": "5", "ratingExplanation": ""}}]}, "result": [{"reviewRating": {"ratingExplanation": "This is a sample reference"}}]}, {"@id": "_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d", "@type": "InteractionCounter", "disambiguatingDescription": "Reference", "interactionType": {"@id": "_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d#interactionType", "@type": "AssessAction", "participant": {"@id": "_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d#interactionType#participant", "@type": "Person", "givenName": "<PERSON>", "familyName": "<PERSON>", "jobTitle": "CEO, sleep corp", "telephone": "+****************", "email": "<EMAIL>"}, "result": [{"@id": "_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d#result", "@type": "Review", "itemReviewed": {}, "reviewAspect": [], "reviewRating": {"@id": "_:Reference#ba8fda2e-0d99-446b-8484-bc01af400b4d#result#reviewRating", "@type": "Rating", "ratingValue": "5", "bestRating": "5", "ratingExplanation": ""}}]}, "result": [{"reviewRating": {"ratingExplanation": "This is second sample reference"}}]}], "identifier": [{"@type": "PropertyValue", "@id": "_:Extras_6d651394-5740-47c5-a172-5326ec688e26", "propertyID": "Hobbies", "value": "Skiing, Dancing"}], "award": ["Judge", "3rd place"], "@context": "http://schema.org/"}]}}, "theme": {"layout": "castform", "font": {"family": "Montserrat"}, "colors": {"background": "#ffffff", "primary": "#212121", "accent": "#f44336"}, "layoutblocks": {"onyx": [["objective", "work", "education", "projects"], ["hobbies", "languages", "awards", "certifications"], ["skills", "references"]], "pikachu": [["skills", "languages", "hobbies", "awards", "certifications"], ["work", "education", "projects", "references"]], "gengar": [["objective", "skills"], ["awards", "certifications", "languages", "references", "hobbies"], ["work", "education", "projects"]], "castform": [["awards", "certifications", "languages", "hobbies"], ["objective", "work", "education", "skills", "projects", "references"]], "glalie": [["awards", "certifications", "hobbies"], ["objective", "work", "education", "skills", "projects", "languages", "references"]], "celebi": [["awards", "certifications", "languages", "hobbies"], ["objective", "work", "education", "skills", "projects", "references"]]}}}