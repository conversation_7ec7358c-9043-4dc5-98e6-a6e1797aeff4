<?php
/**
 * إصلاح بسيط لمشاكل قاعدة البيانات
 * يحل المشكلة الأصلية فقط دون تعقيدات
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
redirectIfNotLoggedIn();

$messages = [];
$success = true;

// 1. فحص الاتصال بقاعدة البيانات الرئيسية
try {
    global $main_db;
    if ($main_db && !$main_db->connect_error) {
        $messages[] = "✓ الاتصال بقاعدة البيانات الرئيسية سليم";
    } else {
        $messages[] = "✗ مشكلة في الاتصال بقاعدة البيانات الرئيسية";
        $success = false;
    }
} catch (Exception $e) {
    $messages[] = "✗ خطأ في قاعدة البيانات الرئيسية: " . $e->getMessage();
    $success = false;
}

// 2. فحص قاعدة بيانات المستخدم
$user_db_name = "sales_system_user_" . $_SESSION['user_id'];

try {
    // محاولة إنشاء قاعدة البيانات إذا لم تكن موجودة
    $create_db_query = "CREATE DATABASE IF NOT EXISTS `$user_db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
    if ($main_db->query($create_db_query)) {
        $messages[] = "✓ تم التأكد من وجود قاعدة بيانات المستخدم";
    } else {
        $messages[] = "✗ فشل في إنشاء قاعدة بيانات المستخدم: " . $main_db->error;
        $success = false;
    }
} catch (Exception $e) {
    $messages[] = "✗ خطأ في إنشاء قاعدة بيانات المستخدم: " . $e->getMessage();
    $success = false;
}

// 3. الاتصال بقاعدة بيانات المستخدم
$user_db = null;
try {
    $user_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, $user_db_name);
    $user_db->set_charset("utf8mb4");
    
    if (!$user_db->connect_error) {
        $messages[] = "✓ تم الاتصال بقاعدة بيانات المستخدم بنجاح";
    } else {
        $messages[] = "✗ فشل الاتصال بقاعدة بيانات المستخدم: " . $user_db->connect_error;
        $success = false;
    }
} catch (Exception $e) {
    $messages[] = "✗ خطأ في الاتصال بقاعدة بيانات المستخدم: " . $e->getMessage();
    $success = false;
}

// 4. إنشاء الجداول الأساسية إذا لم تكن موجودة
if ($user_db && !$user_db->connect_error) {
    $tables = [
        'customers' => "CREATE TABLE IF NOT EXISTS `customers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `email` varchar(255) DEFAULT NULL,
            `tax_number` varchar(50) DEFAULT NULL,
            `address` text DEFAULT NULL,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
        
        'products' => "CREATE TABLE IF NOT EXISTS `products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
        
        'sales' => "CREATE TABLE IF NOT EXISTS `sales` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `notes` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `invoice_number` (`invoice_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
        
        'purchases' => "CREATE TABLE IF NOT EXISTS `purchases` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `notes` text DEFAULT NULL,
            PRIMARY KEY (`id`),
            UNIQUE KEY `invoice_number` (`invoice_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
        
        'sale_items' => "CREATE TABLE IF NOT EXISTS `sale_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `sale_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4",
        
        'purchase_items' => "CREATE TABLE IF NOT EXISTS `purchase_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `purchase_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4"
    ];
    
    foreach ($tables as $table_name => $create_sql) {
        try {
            if ($user_db->query($create_sql)) {
                $messages[] = "✓ جدول $table_name جاهز";
            } else {
                $messages[] = "✗ فشل في إنشاء جدول $table_name: " . $user_db->error;
                $success = false;
            }
        } catch (Exception $e) {
            $messages[] = "✗ خطأ في إنشاء جدول $table_name: " . $e->getMessage();
            $success = false;
        }
    }
    
    // فحص الجداول للتأكد من إنشائها بنجاح
    $customers_count = $user_db->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc()['count'];
    $messages[] = "✓ جدول العملاء يحتوي على $customers_count سجل";

    $products_count = $user_db->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    $messages[] = "✓ جدول المنتجات يحتوي على $products_count سجل";
    
    $user_db->close();
}

require_once __DIR__ . '/includes/header.php';
?>

<div class="container mt-4">
    <div class="card">
        <div class="card-header <?php echo $success ? 'bg-success' : 'bg-warning'; ?> text-white">
            <h4 class="mb-0">
                <i class="fas fa-database"></i>
                إصلاح بسيط لقاعدة البيانات
            </h4>
        </div>
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="card <?php echo $success ? 'bg-success' : 'bg-warning'; ?> text-white">
                        <div class="card-body text-center">
                            <h2><?php echo $success ? 'نجح' : 'تحذير'; ?></h2>
                            <p class="mb-0">حالة الإصلاح</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h2><?php echo count($messages); ?></h2>
                            <p class="mb-0">إجمالي العمليات</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">تفاصيل العمليات</h5>
                </div>
                <div class="card-body">
                    <?php foreach ($messages as $message): ?>
                    <div class="alert <?php echo strpos($message, '✓') !== false ? 'alert-success' : 'alert-danger'; ?> py-2">
                        <?php echo $message; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-home"></i>
                    العودة للصفحة الرئيسية
                </a>
                <?php if (!$success): ?>
                <a href="simple_db_fix.php" class="btn btn-warning btn-lg ms-2">
                    <i class="fas fa-redo"></i>
                    إعادة المحاولة
                </a>
                <?php endif; ?>
            </div>

            <?php if ($success): ?>
            <div class="alert alert-success mt-4">
                <h6><i class="fas fa-check-circle"></i> تم بنجاح!</h6>
                <p>تم إصلاح مشاكل قاعدة البيانات. يمكنك الآن استخدام النظام بشكل طبيعي.</p>
            </div>
            <?php else: ?>
            <div class="alert alert-warning mt-4">
                <h6><i class="fas fa-exclamation-triangle"></i> تحذير</h6>
                <p>تم العثور على بعض المشاكل. يُرجى مراجعة التفاصيل أعلاه أو التواصل مع المطور.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
