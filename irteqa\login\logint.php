<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>login h</title>
</head>
<style>
    .loginfo{
        width: 75%;
    border: 1px solid #adadad;
    margin: auto;
    height: 120px;
    border-radius: 10px;
    background: aliceblue;
    margin-top: 10px;
    block-size: auto;
    padding: 15px;
    font-size: x-large;
    }
    label{
        color: red;
    }
    p{
        margin: 3px;
    }
    td{
        width: 5%;
    }
    section{
        margin-top: 50px;
    }
   div .osinfo{
    font-size: 20px;
    color: black;
    }
    h1{
        text-align: center;
    }
</style>
<section id="sctn">
        

<h1>سجل عمليات الدخول</h1>
        <?php
        $conn = new mysqli("localhost", "root", "", "login_history");
        $sql = "SELECT * FROM login_records";
$result = mysqli_query($conn,$sql);
$num=1;
if ($result){
    while($row = mysqli_fetch_assoc($result)){
        

        echo "<div class='loginfo'><table> " ;
echo "<tr><td><label>No: </label>" . $row['id'] . "</td><td><label>Name: </label>" . $row['username'] . "</td><td><label>Time: </label>" . $row['login_time'] . "</td><td><label>ip: </label>" . $row['ip_address'] . "</td><td><label>Browser: </label>" . $row['browser'] . "</td></tr>";
      
    echo "</table>";
        echo "<div><label>System : </label><label class='osinfo'>" . $row['operating_system'] . "</label></div></div>";
        }}
        ?>
 </section> 
 </html>