(this.webpackJsonpjsonldresume=this.webpackJsonpjsonldresume||[]).push([[0],<PERSON>rray(83).concat([function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u0625\u0636\u0627\u0641\u0629 {{- heading}}","startDate":{"label":"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0628\u062f\u0621"},"endDate":{"label":"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0627\u0646\u062a\u0647\u0627\u0621"},"description":{"label":"\u0627\u0644\u0648\u0635\u0641"}},"buttons":{"add":{"label":"\u0625\u0636\u0627\u0641\u0629"},"delete":{"label":"\u062d\u0630\u0641"}},"printDialog":{"heading":"\u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643","quality":{"label":"\u0627\u0644\u062c\u0648\u062f\u0629"},"printType":{"label":"\u0627\u0644\u0646\u0648\u0639","types":{"unconstrained":"\u063a\u064a\u0631 \u0645\u0642\u064a\u0651\u062f","fitInA4":"\u0645\u0644\u0627\u0626\u0645 \u0641\u064a A4","multiPageA4":"\u0635\u0641\u062d\u0627\u062a \u0645\u062a\u0639\u062f\u062f\u0629 A4"}},"helpText":["\u064a\u0633\u062a\u062e\u062f\u0645 \u0641\u0649 \u0635\u064a\u063a\u0629 \u0627\u0644\u062a\u0635\u062f\u064a\u0631 \u0647\u0646\u0627 HTML canvas \u0644\u062a\u062d\u0648\u064a\u0644 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0625\u0644\u0649 \u0635\u0648\u0631\u0629 \u062b\u0645 \u0637\u0628\u0627\u0639\u062a\u0647\u0627 \u0641\u0649 \u0645\u0644\u0641 PDF \u060c \u0645\u0645\u0627 \u064a\u0639\u0646\u0649 \u0623\u0646\u0643 \u0633\u062a\u0641\u0642\u062f \u0643\u0644 \u0642\u062f\u0631\u0627\u062a \u062a\u062d\u062f\u064a\u062f\\\\\u062a\u062d\u0644\u064a\u0644 \u0627\u0644\u0646\u0635 \u0627\u0644\u0645\u0643\u062a\u0648\u0628.","\u0625\u0630\u0627 \u0643\u0627\u0646 \u0647\u0630\u0627 \u0645\u0647\u0645\u0627\u064b \u0628\u0627\u0644\u0646\u0633\u0628\u0629 \u0644\u0643\u060c \u064a\u0631\u062c\u0649 \u0645\u062d\u0627\u0648\u0644\u0629 \u0637\u0628\u0627\u0639\u0629 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0628\u062f\u0644\u0627\u064b \u0645\u0646 \u0630\u0644\u0643 \u0628\u0627\u0633\u062a\u062e\u062f\u0627\u0645 Cmd/Ctrl + P \u0623\u0648 \u0632\u0631 \u0627\u0644\u0637\u0628\u0627\u0639\u0629 \u0623\u062f\u0646\u0627\u0647. \u0642\u062f \u062a\u062e\u062a\u0644\u0641 \u0627\u0644\u0646\u062a\u064a\u062c\u0629 \u0644\u0623\u0646 \u0637\u0628\u064a\u0639\u0629 \u0627\u0644\u0637\u0628\u0627\u0639\u0629 \u062a\u0639\u062a\u0645\u062f \u0639\u0644\u0649 \u0646\u0648\u0639 \u0627\u0644\u0645\u062a\u0635\u0641\u062d\u060c \u0648\u0644\u0643\u0646 \u0645\u0646 \u0627\u0644\u0645\u0639\u0631\u0648\u0641 \u0623\u0646\u0647 \u064a\u0639\u0645\u0644 \u0628\u0634\u0643\u0644 \u0623\u0641\u0636\u0644 \u0639\u0644\u0649 \u0623\u062d\u062f\u062b \u0625\u0635\u062f\u0627\u0631 \u0645\u0646 Google Chrome."],"buttons":{"cancel":"\u0625\u0644\u063a\u0627\u0621","saveAsPdf":"\u062d\u0641\u0638 \u0643\u0640 PDF"}},"panZoomAnimation":{"helpText":"\u064a\u0645\u0643\u0646\u0643 \u062a\u062d\u0631\u064a\u0643 \u0648\u062a\u0643\u0628\u064a\u0631 \u0644\u0648\u062d\u0629 \u0627\u0644\u0631\u0633\u0645 \u0641\u0649 \u0623\u0649 \u0648\u0642\u062a \u0644\u062a\u062d\u0635\u0644 \u0639\u0644\u0649 \u0646\u0638\u0631\u0629 \u0623\u0648\u0636\u062d \u0644\u0633\u064a\u0631\u062a\u0643 \u0627\u0644\u0630\u0627\u062a\u064a\u0629."},"markdownHelpText":"\u064a\u0645\u0643\u0646\u0643 \u0627\u0633\u062a\u062e\u062f\u0627\u0645 <1>GitHub Flavored Markdown</1> \u0644\u062a\u0635\u0645\u064a\u0645 \u0647\u0630\u0627 \u0627\u0644\u0642\u0633\u0645 \u0645\u0646 \u0627\u0644\u0646\u0635."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u0631\u0627\u0628\u0637 \u0627\u0644\u0635\u0648\u0631\u0629 \u0639\u0644\u0649 \u0627\u0644\u0625\u0646\u062a\u0631\u0646\u062a"},"firstName":{"label":"\u0627\u0644\u0625\u0633\u0645 \u0627\u0644\u0623\u0648\u0644"},"lastName":{"label":"\u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0623\u062e\u064a\u0631"},"subtitle":{"label":"\u0627\u0644\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0641\u0631\u0639\u064a"},"address":{"label":"\u0627\u0644\u0639\u0646\u0648\u0627\u0646","line1":{"label":"\u0627\u0644\u0633\u0637\u0631 1 \u0644\u0644\u0639\u0646\u0648\u0627\u0646"},"line2":{"label":"\u0627\u0644\u0633\u0637\u0631 2 \u0644\u0644\u0639\u0646\u0648\u0627\u0646"},"line3":{"label":"\u0627\u0644\u0633\u0637\u0631 3 \u0644\u0644\u0639\u0646\u0648\u0627\u0646"}},"phone":{"label":"\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641"},"website":{"label":"\u0627\u0644\u0645\u0648\u0642\u0639 \u0627\u0644\u0627\u0644\u0643\u062a\u0631\u0648\u0646\u064a"},"email":{"label":"\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0627\u0644\u0623\u0647\u062f\u0627\u0641"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0627\u0644\u0627\u0633\u0645"},"role":{"label":"\u0627\u0644\u0648\u0638\u064a\u0641\u0629"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0627\u0644\u0627\u0633\u0645"},"major":{"label":"\u0627\u0644\u062a\u062e\u0635\u0635"},"grade":{"label":"\u0627\u0644\u062f\u0631\u062c\u0629"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0627\u0644\u0639\u0646\u0648\u0627\u0646"},"subtitle":{"label":"\u0627\u0644\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0641\u0631\u0639\u064a"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0627\u0644\u0627\u0633\u0645"},"subtitle":{"label":"\u0627\u0644\u0647\u064a\u0626\u0629"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0627\u0644\u0627\u0633\u0645"},"level":{"label":"\u0645\u0633\u062a\u0648\u0649"},"rating":{"label":"\u0627\u0644\u062a\u0642\u064a\u064a\u0645"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0627\u0644\u0627\u0633\u0645"},"position":{"label":"\u0627\u0644\u0645\u0646\u0635\u0628"},"phone":{"label":"\u0631\u0642\u0645 \u0627\u0644\u0647\u0627\u062a\u0641"},"email":{"label":"\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0628\u0631\u064a\u062f \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0627\u0644\u0645\u0641\u062a\u0627\u062d - \u0627\u0644\u0646\u0648\u0639"},"value":{"label":"\u0627\u0644\u0642\u064a\u0645\u0629"}}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u0642\u0648\u0627\u0644\u0628"}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u0623\u0644\u0648\u0627\u0646","colorOptions":"\u062e\u064a\u0627\u0631\u0627\u062a \u0627\u0644\u0644\u0648\u0646","primaryColor":"\u0627\u0644\u0644\u0648\u0646 \u0627\u0644\u0623\u0633\u0627\u0633\u064a","accentColor":"\u0627\u0644\u0644\u0648\u0646 \u0627\u0644\u062b\u0627\u0646\u0648\u064a","clipboardCopyAction":"\u062a\u0645 \u0646\u0633\u062e {{color}}."}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u062e\u0637\u0648\u0637","fontFamily":{"label":"\u0646\u0648\u0639 \u0627\u0644\u062e\u0637","helpText":"\u064a\u0645\u0643\u0646\u0643 \u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0623\u064a \u062e\u0637 \u0645\u062b\u0628\u062a \u0639\u0644\u0649 \u062c\u0647\u0627\u0632\u0643 \u0623\u064a\u0636\u064b\u0627. \u0641\u0642\u0637 \u0642\u0645 \u0628\u0625\u062f\u062e\u0627\u0644 \u0627\u0633\u0645 \u0639\u0627\u0626\u0644\u0629 \u0627\u0644\u062e\u0637 \u0647\u0646\u0627 \u0648\u0633\u0648\u0641 \u064a\u0642\u0648\u0645 \u0627\u0644\u0645\u062a\u0635\u0641\u062d \u0628\u062a\u062d\u0645\u064a\u0644\u0647 \u0644\u0643."}}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u0627\u062c\u0631\u0627\u0621\u062a","disclaimer":"\u0627\u0644\u062a\u063a\u064a\u064a\u0631\u0627\u062a \u0627\u0644\u062a\u064a \u062a\u062c\u0631\u064a\u0647\u0627 \u0639\u0644\u0649 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643 \u064a\u062a\u0645 \u062d\u0641\u0638\u0647\u0627 \u062a\u0644\u0642\u0627\u0626\u064a\u064b\u0627 \u0625\u0644\u0649 \u0648\u062d\u062f\u0629 \u0627\u0644\u062a\u062e\u0632\u064a\u0646 \u0627\u0644\u0645\u062d\u0644\u064a\u0629 \u0644\u0644\u0645\u062a\u0635\u0641\u062d. \u0644\u0627 \u062a\u0648\u062c\u062f \u0628\u064a\u0627\u0646\u0627\u062a\u060c \u0648\u0628\u0627\u0644\u062a\u0627\u0644\u064a \u0641\u0625\u0646 \u0627\u0644\u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643 \u0622\u0645\u0646\u0629 \u062a\u0645\u0627\u0645\u064b\u0627.","importExport":{"heading":"\u0625\u0633\u062a\u064a\u0631\u0627\u062f/\u062a\u0635\u062f\u064a\u0631","body":"\u064a\u0645\u0643\u0646\u0643 \u0627\u0633\u062a\u064a\u0631\u0627\u062f \u0623\u0648 \u062a\u0635\u062f\u064a\u0631 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643 \u0628\u062a\u0646\u0633\u064a\u0642 JSON. \u0644\u0630\u0644\u0643 \u060c \u064a\u0645\u0643\u0646\u0643 \u062a\u0639\u062f\u064a\u0644 \u0623\u0648 \u0637\u0628\u0627\u0639\u0629 \u0633\u064a\u0631\u062a\u0643 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0645\u0646 \u0623\u064a \u062c\u0647\u0627\u0632. \u062d\u0641\u0638 \u0647\u0630\u0627 \u0627\u0644\u0645\u0644\u0641 \u0644\u0627\u0633\u062a\u062e\u062f\u0627\u0645\u0647 \u0644\u0627\u062d\u0642\u0627\u064b.","buttons":{"import":"\u0625\u0633\u062a\u064a\u0631\u0627\u062f","export":"\u062a\u0635\u062f\u064a\u0631"}},"downloadResume":{"heading":"\u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0627\u0644\u062e\u0627\u0635\u0629 \u0628\u0643","body":"\u064a\u0645\u0643\u0646\u0643 \u0627\u0644\u0646\u0642\u0631 \u0639\u0644\u0649 \u0627\u0644\u0632\u0631 \u0623\u062f\u0646\u0627\u0647 \u0644\u062a\u0646\u0632\u064a\u0644 \u0646\u0633\u062e\u0629 PDF \u0645\u0646 \u0633\u064a\u0631\u062a\u0643 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0639\u0644\u0649 \u0627\u0644\u0641\u0648\u0631. \u0644\u0644\u062d\u0635\u0648\u0644 \u0639\u0644\u0649 \u0623\u0641\u0636\u0644 \u0627\u0644\u0646\u062a\u0627\u0626\u062c\u060c \u064a\u0631\u062c\u0649 \u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0623\u062d\u062f\u062b \u0625\u0635\u062f\u0627\u0631 \u0645\u0646 Google Chrome.","buttons":{"saveAsPdf":"\u062d\u0641\u0638 \u0643\u0640 PDF"}},"loadDemoData":{"heading":"\u0625\u0636\u0627\u0641\u0629 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a \u0627\u0644\u062a\u062c\u0631\u064a\u0628\u064a\u0629","body":"\u063a\u064a\u0631 \u0648\u0627\u0636\u062d \u0645\u0627 \u064a\u062c\u0628 \u0641\u0639\u0644\u0647 \u0628\u0635\u0641\u062d\u0629 \u062c\u062f\u064a\u062f\u0629 \u0641\u0627\u0631\u063a\u0629\u061f \u0642\u0645 \u0628\u0625\u0636\u0627\u0641\u0629 \u0628\u0639\u0636 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a \u0627\u0644\u062a\u062c\u0631\u064a\u0628\u064a\u0629 \u0645\u0639 \u0642\u064a\u0645 \u0645\u0633\u0628\u0642\u0629 \u0644\u062a\u0631\u0649 \u0643\u064a\u0641 \u064a\u062c\u0628 \u0623\u0646 \u062a\u0628\u062f\u0648 \u0633\u064a\u0631\u062a\u0643 \u0627\u0644\u0630\u0627\u062a\u064a\u0629 \u0648\u064a\u0645\u0643\u0646\u0643 \u0627\u0644\u0628\u062f\u0621 \u0641\u064a \u0627\u0644\u062a\u0639\u062f\u064a\u0644 \u0645\u0646 \u0647\u0646\u0627\u0643.","buttons":{"loadData":"\u062a\u062d\u0645\u064a\u0644-\u0625\u0636\u0627\u0641\u0629 \u0627\u0644\u0628\u064a\u0627\u0646\u0627\u062a"}},"reset":{"heading":"\u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u0643\u0644 \u0634\u064a\u0621!","body":"\u0633\u064a\u0624\u062f\u064a \u0647\u0630\u0627 \u0627\u0644\u0625\u062c\u0631\u0627\u0621 \u0625\u0644\u0649 \u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u062c\u0645\u064a\u0639 \u0628\u064a\u0627\u0646\u0627\u062a\u0643 \u0648\u0625\u0632\u0627\u0644\u0629 \u0627\u0644\u0646\u0633\u062e \u0627\u0644\u0627\u062d\u062a\u064a\u0627\u0637\u064a\u0629 \u0627\u0644\u062a\u064a \u062a\u0645 \u0625\u0646\u0634\u0627\u0624\u0647\u0627 \u0639\u0644\u0649 \u0648\u062d\u062f\u0629 \u0627\u0644\u062a\u062e\u0632\u064a\u0646 \u0627\u0644\u0645\u062d\u0644\u064a\u0629 \u0644\u0645\u062a\u0635\u0641\u062d\u0643 \u0623\u064a\u0636\u064b\u0627. \u0644\u0630\u0627 \u064a\u0631\u062c\u0649 \u0627\u0644\u062a\u0623\u0643\u062f \u0645\u0646 \u0623\u0646\u0643 \u0642\u0645\u062a \u0628\u062a\u0635\u062f\u064a\u0631 \u0628\u064a\u0627\u0646\u0627\u062a\u0643 \u0623\u0648\u0644\u0627\u064b \u0642\u0628\u0644 \u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u0643\u0644 \u0634\u064a\u0621.","buttons":{"reset":"\u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646"}}}')},function(e){e.exports=JSON.parse('{"title":"\u0627\u0644\u0625\u0639\u062f\u0627\u062f\u0627\u062a","language":{"label":"\u0627\u0644\u0644\u0651\u063a\u0629","helpText":"\u0625\u0630\u0627 \u0643\u0646\u062a \u062a\u0631\u063a\u0628 \u0641\u064a \u0627\u0644\u0645\u0633\u0627\u0639\u062f\u0629 \u0641\u064a \u062a\u0631\u062c\u0645\u0629 \u0627\u0644\u062a\u0637\u0628\u064a\u0642 \u0625\u0644\u0649 \u0644\u063a\u062a\u0643 \u0627\u0644\u062e\u0627\u0635\u0629\u060c \u064a\u0631\u062c\u0649 \u0627\u0644\u0631\u062c\u0648\u0639 \u0625\u0644\u0649 <1>\u0648\u062b\u064a\u0642\u0629 \u0627\u0644\u062a\u0631\u062c\u0645\u0629 </1>."}}')},function(e){e.exports=JSON.parse('{"title":"\u062d\u0648\u0644","documentation":{"heading":"\u062a\u0648\u0636\u064a\u062d\u0627\u062a","body":"\u0647\u0644 \u062a\u0631\u063a\u0628 \u0641\u064a \u0645\u0639\u0631\u0641\u0629 \u0627\u0644\u0645\u0632\u064a\u062f \u0639\u0646 \u0627\u0644\u062a\u0637\u0628\u064a\u0642\u061f \u062a\u062d\u062a\u0627\u062c \u0625\u0644\u0649 \u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0639\u0646 \u0643\u064a\u0641\u064a\u0629 \u0627\u0644\u0645\u0633\u0627\u0647\u0645\u0629 \u0641\u064a \u0627\u0644\u0645\u0634\u0631\u0648\u0639\u061f \u0644\u0627 \u0645\u0632\u064a\u062f \u0645\u0646 \u0627\u0644\u0628\u062d\u062b\u060c \u0647\u0646\u0627\u0643 \u062f\u0644\u064a\u0644 \u0634\u0627\u0645\u0644 \u062a\u0645 \u0625\u0639\u062f\u0627\u062f\u0647 \u0645\u0646 \u0623\u062c\u0644\u0643.","buttons":{"documentation":"\u0627\u0644\u062a\u0648\u0636\u064a\u062d\u0627\u062a"}},"bugOrFeatureRequest":{"heading":"\u062e\u0637\u0623\u061f \u0637\u0644\u0628 \u0645\u064a\u0632\u0629 \u062c\u062f\u064a\u062f\u0629\u061f","body":"\u0634\u064a\u0621 \u0645\u0627 \u064a\u0648\u0642\u0641 \u062a\u0642\u062f\u0645\u0643 \u0641\u0649 \u0628\u0646\u0627\u0621 \u0627\u0644\u0633\u064a\u0631\u0629 \u0627\u0644\u0630\u0627\u062a\u064a\u0629\u061f \u0648\u062c\u062f\u062a \u062e\u0644\u0644 \u0645\u0632\u0639\u062c \u0644\u0646 \u064a\u062a\u0648\u0642\u0641 \u0645\u0646 \u0627\u0644\u0639\u0645\u0644\u061f \u062a\u062d\u062f\u062b \u0639\u0646 \u0630\u0644\u0643 \u0641\u064a \u0642\u0633\u0645 \u0645\u0634\u0627\u0643\u0644 GitHub \u060c \u0623\u0648 \u0623\u0631\u0633\u0644 \u0625\u0644\u0649 \u0628\u0631\u064a\u062f\u064a \u0627\u0644\u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u0628\u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0627\u0644\u0625\u062c\u0631\u0627\u0621\u0627\u062a \u0623\u062f\u0646\u0627\u0647.","buttons":{"raiseIssue":"\u0631\u0641\u0639 \u0645\u0634\u0643\u0644\u0629","sendEmail":"\u0625\u0631\u0633\u0627\u0644 \u0628\u0631\u064a\u062f \u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a"}},"sourceCode":{"heading":"\u0645\u0635\u062f\u0631 \u0627\u0644\u0643\u0648\u062f \u0627\u0644\u0628\u0631\u0645\u062c\u064a\u0649","body":"\u0647\u0644 \u062a\u0631\u064a\u062f \u062a\u0634\u063a\u064a\u0644 \u0627\u0644\u0645\u0634\u0631\u0648\u0639 \u0645\u0646 \u0645\u0635\u062f\u0631\u0647\u061f \u0647\u0644 \u0623\u0646\u062a \u0645\u0637\u0648\u0631 \u0639\u0644\u0649 \u0627\u0633\u062a\u0639\u062f\u0627\u062f \u0644\u0644\u0645\u0633\u0627\u0647\u0645\u0629 \u0641\u064a \u062a\u0637\u0648\u064a\u0631 \u0647\u0630\u0627 \u0627\u0644\u0645\u0634\u0631\u0648\u0639\u061f \u0627\u0646\u0642\u0631 \u0639\u0644\u0649 \u0627\u0644\u0632\u0631 \u0623\u062f\u0646\u0627\u0647.","buttons":{"githubRepo":"\u0645\u0633\u062a\u0648\u062f\u0639 GitHub"}},"license":{"heading":"\u0645\u0639\u0644\u0648\u0645\u0627\u062a \u0627\u0644\u062a\u0631\u062e\u064a\u0635","body":"\u0627\u0644\u0645\u0634\u0631\u0648\u0639 \u062e\u0627\u0636\u0639 \u0644\u0631\u062e\u0635\u0629 MIT \u060c \u0627\u0644\u062a\u064a \u064a\u0645\u0643\u0646\u0643 \u0627\u0644\u0642\u0631\u0627\u0629 \u0639\u0646\u0647\u0627 \u0623\u0643\u062b\u0631 \u0623\u062f\u0646\u0627\u0647. \u0641\u064a \u0627\u0644\u0623\u0633\u0627\u0633\u060c \u064a\u0633\u0645\u062d \u0644\u0643 \u0628\u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0627\u0644\u0645\u0634\u0631\u0648\u0639 \u0641\u064a \u0623\u064a \u0645\u0643\u0627\u0646 \u0628\u0634\u0631\u0637 \u0623\u0646 \u062a\u0642\u062f\u0645 \u0627\u0639\u062a\u0645\u0627\u062f\u0627\u062a \u0644\u0644\u0645\u0624\u0644\u0641 \u0627\u0644\u0623\u0635\u0644\u064a.","buttons":{"mitLicense":"\u0631\u062e\u0635\u0629 MIT"}},"footer":{"credit":"\u0635\u0646\u0639 \u0645\u0639 \u0627\u0644\u062d\u0628 \u0628\u0648\u0627\u0633\u0637\u0629 <1>\u0623\u0645\u0631\u0648\u062b \u0628\u064a\u0644\u0627\u064a</1>","thanks":"\u0634\u0643\u0631\u0627 \u0644\u0643 \u0639\u0644\u0649 \u0627\u0633\u062a\u062e\u062f\u0627\u0645 Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"heading":{"placeholder":"Heading"},"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date","placeholder":"March 2018"},"endDate":{"label":"End Date","placeholder":"March 2022"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"}},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name","placeholder":"Jane"},"lastName":{"label":"Last Name","placeholder":"Doe"},"subtitle":{"label":"Subtitle","placeholder":"Full Stack Web Developer"},"address":{"line1":{"label":"Address Line 1","placeholder":"Palladium Complex"},"line2":{"label":"Address Line 2","placeholder":"140 E 14th St"},"line3":{"label":"Address Line 3","placeholder":"New York, NY 10003 USA"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective","placeholder":"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Amazon"},"role":{"label":"Role","placeholder":"Front-end Web Developer"},"description":{"placeholder":"You can write about what you specialized in while working at the company and what projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Harvard University"},"major":{"label":"Major","placeholder":"Masters in Computer Science"},"grade":{"label":"Grade"},"description":{"placeholder":"You can write about projects or special credit classes that you took while studying at this school."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title","placeholder":"Math & Science Olympiad"},"subtitle":{"label":"Subtitle","placeholder":"First Place, International Level"},"description":{"placeholder":"You can write about what qualities made you succeed in getting this award."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Dothraki"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Richard Hendricks"},"position":{"label":"Position","placeholder":"CEO, Pied Piper"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"},"description":{"placeholder":"You can write about how you and the reference contact worked together and which projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Date of Birth"},"value":{"label":"Value","placeholder":"6th August 1995"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Accent Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"printResume":{"heading":"Print Your Resume","body":"You can click on the button below to generate a PDF instantly. Alternatively, you can also use <1>Cmd/Ctrl + P</1> but it would have different effects.","buttons":{"export":"Export","print":"Print"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Wouldn\'t it be nice if there was a guide to setting it up on your local machine? Need information on how to contribute to the project? Look no further, there\'s comprehensive documentation made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Tilf\xf8j {{- heading}}","startDate":{"label":"Startdato"},"endDate":{"label":"Slutdato"},"description":{"label":"Beskrivelse"}},"buttons":{"add":{"label":"Tilf\xf8j"},"delete":{"label":"Slet"}},"printDialog":{"heading":"Download dit CV","quality":{"label":"Kvalitet"},"printType":{"label":"Type","types":{"unconstrained":"Ingen begr\xe6nsninger","fitInA4":"Tilpas til A4","multiPageA4":"Flersidet A4"}},"helpText":["Denne eksportmetode bruger HTML-l\xe6rred til at konvertere CV til et billede og udskrive det p\xe5 en PDF, hvilket betyder, at det mister alle muligheder for valg / analyse.","Hvis det er vigtigt for dig, kan du pr\xf8ve at udskrive CV i stedet for at bruge Cmd / Ctrl + P eller udskrivningsknappen nedenfor. Resultatet kan variere, da output er browserafh\xe6ngig, men det vides at fungere bedst p\xe5 den nyeste version af Google Chrome."],"buttons":{"cancel":"Annull\xe9r","saveAsPdf":"Gem som PDF"}},"panZoomAnimation":{"helpText":"Du kan panorere rundt, og zoome ind p\xe5 l\xe6redet n\xe5r som helst og kigge n\xe6rmere p\xe5 dit CV."},"markdownHelpText":"Du kan benytte <1>GitHub Flavored Markdown</1> for at tilpasse den del af teksten."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Link til billede"},"firstName":{"label":"Fornavn"},"lastName":{"label":"Efternavn"},"subtitle":{"label":"Undertitel"},"address":{"label":"Adresse","line1":{"label":"Adresse linie 1"},"line2":{"label":"Adresse linie 2"},"line3":{"label":"Adresse linie 3"}},"phone":{"label":"Telefonnummer"},"website":{"label":"Hjemmeside"},"email":{"label":"E-mailadresse"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"M\xe5l"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Navn"},"role":{"label":"Rolle"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Navn"},"major":{"label":"Centralfag"},"grade":{"label":"Karakter"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titel"},"subtitle":{"label":"Undertitel"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Navn"},"subtitle":{"label":"Udsteder"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Navn"},"level":{"label":"Niveau"},"rating":{"label":"Bed\xf8mmelse"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Navn"},"position":{"label":"Jobtitel"},"phone":{"label":"Telefonnummer"},"email":{"label":"E-mailadresse"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"N\xf8gle"},"value":{"label":"V\xe6rdi"}}')},function(e){e.exports=JSON.parse('{"title":"Skabeloner"}')},function(e){e.exports=JSON.parse('{"title":"Farver","colorOptions":"Farve muligheder","primaryColor":"Prim\xe6r farve","accentColor":"Sekund\xe6r farve","clipboardCopyAction":"{{color}} er blevet kopieret til udklipsholderen."}')},function(e){e.exports=JSON.parse('{"title":"Skrifttype","fontFamily":{"label":"Skrifttypefamilie","helpText":"Du kan ogs\xe5 benytte de skrifttyper der er installeret p\xe5 din maskine. Indtast blot navnet p\xe5 skrifttypen, og browseren vil indl\xe6se den for dig."}}')},function(e){e.exports=JSON.parse('{"title":"Handlinger","disclaimer":"\xc6ndringer du laver i dit CV bliver automatisk gemt i din browsers lokale lager. Ingen data slipper ud, og din information derfor helt sikker.","importExport":{"heading":"Import\xe9r/eksport\xe9r","body":"Du kan importere eller eksportere dine data i JSON format. Med dette kan du \xe6ndre og printe dit CV fra hvilken som helst enhed. Gem denne fil til senere brug.","buttons":{"import":"Import\xe9r","export":"Eksport\xe9r"}},"downloadResume":{"heading":"Download dit CV.","body":"Du kan klikke p\xe5 knappen herunder for at gemme en PDF version af dit CV \xf8jeblikkeligt. For at f\xe5 det bedste resultat, benyt venligst den seneste version af Google Chrome.","buttons":{"saveAsPdf":"Gem som PDF"}},"loadDemoData":{"heading":"Indl\xe6s demo data","body":"Er du usikker p\xe5, hvad du skal g\xf8re med en frisk blank side? Indl\xe6s nogle demo data med forudfyldte v\xe6rdier, se hvordan et CV ser ud og begynd dine \xe6ndringer herfra.","buttons":{"loadData":"Hent data"}},"reset":{"heading":"Nulstil alting!","body":"Denne handling vil nulstille alle dine data og vil ogs\xe5 fjerne sikkerhedskopierne fra din browsers lokale lager, s\xe5 verificer en ekstra gang, at du har eksporteret dine informationer f\xf8r du nulstiller alting.","buttons":{"reset":"Nulstil"}}}')},function(e){e.exports=JSON.parse('{"title":"Indstillinger","language":{"label":"Sprog","helpText":"Hvis du vil hj\xe6lpe med at overs\xe6tte applikationen til dit eget sprog, kig da n\xe6rmere p\xe5 <1>overs\xe6ttelsesdokumentationen</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Om","documentation":{"heading":"Dokumentation","body":"Vil du vide mere om programmet? Mangler du information om, hvordan du kan bidrage til projektet? Du beh\xf8ves ikke lede mere, der er en fyldestg\xf8rende guide som er lavet kun til dig.","buttons":{"documentation":"Dokumentation"}},"bugOrFeatureRequest":{"heading":"Fejl? \xd8nsker til ny funktionalitet?","body":"Er der noget som forhindrer dig i at lave et Cv? Fundet en forbistret fejl som ikke vil forsvinde? Fort\xe6l om det p\xe5 GitHub under Issues, eller send mig en e-mail via knapperne herunder.","buttons":{"raiseIssue":"Opret en sag","sendEmail":"Send en e-mail"}},"sourceCode":{"heading":"Kildekode","body":"Vil du k\xf8re projektet fra kildekoden? Er du udvikler som vil hj\xe6lpe til med open-source udviilingen af dette projekt? Klik p\xe5 knappen herunder.","buttons":{"githubRepo":"GitHub repo"}},"license":{"heading":"Licensoplysninger","body":"Projektet er underlagt MIT licensen, hvilket du kan l\xe6se mere om herunder. Grundl\xe6ggende set m\xe5 projektet benyttes alle steder, hvis du refererer til den oprindelige skaber.","buttons":{"mitLicense":"MIT licens"}},"footer":{"credit":"Lavet med k\xe6rlig af <1>Amruth Pillai</1>","thanks":"Tak fordi du benytter Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"{{- heading}} hinzuf\xfcgen","startDate":{"label":"Startdatum"},"endDate":{"label":"Enddatum"},"description":{"label":"Beschreibung"}},"buttons":{"add":{"label":"Hinzuf\xfcgen"},"delete":{"label":"L\xf6schen"}},"printDialog":{"heading":"Lade Dein Resume herunter","quality":{"label":"Qualit\xe4t"},"printType":{"label":"Typ","types":{"unconstrained":"Uneingeschr\xe4nkt","fitInA4":"A4 einbauen","multiPageA4":"Mehrseitig A4"}},"helpText":["Diese Exportmethode verwendet HTML-Leinwand um den Lebenslauf in ein Bild zu konvertieren und es auf einer PDF auszudrucken. was bedeutet, dass es alle Auswahl-/Analysefunktionen verliert.","Wenn Ihnen das wichtig ist, versuchen Sie bitte den Lebenslauf mit Strg/Strg+P oder dem Druckknopf unten auszudrucken. Das Ergebnis kann variieren, da die Ausgabe vom Browser abh\xe4ngig ist, aber es ist bekannt, dass es am besten mit der neuesten Version von Google Chrome funktioniert."],"buttons":{"cancel":"Abbrechen","saveAsPdf":"Als PDF Speichern"}},"panZoomAnimation":{"helpText":"Sie k\xf6nnen die Zeichenfl\xe4che jederzeit schwenken und zoomen, um Ihren Lebenslauf genauer zu betrachten."},"markdownHelpText":"Du kannst <1>GitHub Flavored Markdown</1> verwenden, um diesen Abschnitt des Textes zu gestalten."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Foto-URL"},"firstName":{"label":"Vorname"},"lastName":{"label":"Nachname"},"subtitle":{"label":"Titel"},"address":{"label":"Adresse","line1":{"label":"Adresszeile 1"},"line2":{"label":"Addresszeile 2"},"line3":{"label":"Adresszeile 3"}},"phone":{"label":"Telefonnnummer"},"website":{"label":"Webseite"},"email":{"label":"E-Mail Adresse"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Ziel"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Stellentitel"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Abschluss"},"grade":{"label":"Note"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titel"},"subtitle":{"label":"Untertitel"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Zertifizierungsstelle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Status"},"rating":{"label":"Bewertung"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Anstellung"},"phone":{"label":"Rufnummer"},"email":{"label":"E-Mail Adresse"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Schl\xfcssel"},"value":{"label":"Wert"}}')},function(e){e.exports=JSON.parse('{"title":"Vorlagen"}')},function(e){e.exports=JSON.parse('{"title":"Farben","colorOptions":"Farboptionen","primaryColor":"Prim\xe4rfarbe","accentColor":"Sekund\xe4rfarbe","clipboardCopyAction":"{{color}} wurde in die Zwischenablage kopiert."}')},function(e){e.exports=JSON.parse('{"title":"Schriftarten","fontFamily":{"label":"Schriftfamilie","helpText":"Du kannst auch jede Schriftart verwenden, die auf Deinem System installiert ist. Hier einfach den Namen der Schriftfamilie eingeben und der Browser wird sie laden."}}')},function(e){e.exports=JSON.parse('{"title":"Aktionen","disclaimer":"\xc4nderungen, die Du an Deinem Lebenslauf vornimmst, werden automatisch im lokalen Speicher Deines Browsers gespeichert. Keine Daten werden an einen Server gesendet, daher sind Deine Informationen v\xf6llig sicher.","importExport":{"heading":"Importieren/Exportieren","body":"Du kannst Deine Daten im JSON format importieren oder exportieren. Damit kannst Du Deinen Lebenslauf von jedem Ger\xe4t bearbeiten und ausdrucken. Speicher diese Datei f\xfcr eine sp\xe4tere Nutzung.","buttons":{"import":"Importieren","export":"Export"}},"downloadResume":{"heading":"Lade Dein Resume herunter","body":"Du kannst auf die Schaltfl\xe4che unten klicken, um eine PDF-Version Ihres Lebenslaufs sofort herunterzuladen. F\xfcr die besten Ergebnisse, verwende bitte die neueste Version von Google Chrome.","buttons":{"saveAsPdf":"Als PDF Speichern"}},"loadDemoData":{"heading":"Demo-Daten laden","body":"Unklar auf, was mit einer frischen leeren Seite zu tun ist? Lade einige Demo-Daten mit vorausgef\xfcllten Werten, um zu sehen, wie ein Lebenslauf aussehen soll und von dort aus mit der Bearbeitung beginnen.","buttons":{"loadData":"Daten laden"}},"reset":{"heading":"Alles zur\xfccksetzen!","body":"Diese Aktion wird alle Daten zur\xfccksetzen und Backups aus dem lokalen Speicher des Browsers entfernen. Bitte sicherstellen, dass alle Daten exportiert sind, bevor Du alles zur\xfccksetzt.","buttons":{"reset":"Zur\xfccksetzen"}}}')},function(e){e.exports=JSON.parse('{"title":"Einstellungen","language":{"label":"Sprache","helpText":"Wenn Du helfen m\xf6chtest, die App in Deine eigene Sprache zu \xfcbersetzen, lies bitte die <1>\xdcbersetzungsdokumentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"\xdcber mich","documentation":{"heading":"Dokumentation","body":"Willst Du mehr \xfcber die App erfahren? Ben\xf6tigst Du Informationen, wie Du zum Projekt beitragen kannst? Such nicht weiter, es gibt einen umfassenden Leitfaden genau f\xfcr Dich.","buttons":{"documentation":"Dokumentation"}},"bugOrFeatureRequest":{"heading":"Fehler? Neue Funktion vorschlagen?","body":"Etwas hindert Dich an der Erstellung eines Lebenslaufs? Einen l\xe4stigen Fehler gefunden? Melde den Fehler auf GitHub oder schick mir eine E-Mail.","buttons":{"raiseIssue":"Issue er\xf6ffnen","sendEmail":"E-Mail senden"}},"sourceCode":{"heading":"Quellcode","body":"M\xf6chtest Du das Projekt von seiner Quelle aus ausf\xfchren? Bist Du als Entwickler bereit, zur Open-Source-Entwicklung dieses Projekts beizutragen? Klick auf den Button unten.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"Lizenzinformationen","body":"Das Projekt unterliegt der MIT-Lizenz, die Du weiter unten lesen kannst. Grunds\xe4tzlich darfst Du das Projekt \xfcberall nutzen, sofern Du den Originalautor nennst.","buttons":{"mitLicense":"MIT-Lizenz"}},"footer":{"credit":"Mit Liebe erstellt von <1>Amruth Pillai</1>","thanks":"Vielen Dank, dass Du Reactive Resume verwendest!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Agregar {{- heading}}","startDate":{"label":"Fecha de inicio"},"endDate":{"label":"Fecha Final"},"description":{"label":"Descripci\xf3n"}},"buttons":{"add":{"label":"Agregar"},"delete":{"label":"Eliminar"}},"printDialog":{"heading":"Descarga tu curr\xedculum","quality":{"label":"Calidad"},"printType":{"label":"Tipo","types":{"unconstrained":"Sin restricciones","fitInA4":"Ajustar a A4","multiPageA4":"Multip\xe1ginas A4"}},"helpText":["Este m\xe9todo de exportaci\xf3n utiliza el lienzo HTML para convertir el curr\xedculum en una imagen e imprimirlo en un PDF, lo que significa que perder\xe1 todas las capacidades de selecci\xf3n / an\xe1lisis.","Si eso es importante para usted, intente imprimir el curr\xedculum usando Cmd / Ctrl + P o el bot\xf3n de impresi\xf3n a continuaci\xf3n. El resultado puede variar ya que la salida depende del navegador, pero se sabe que funciona mejor en la \xfaltima versi\xf3n de Google Chrome."],"buttons":{"cancel":"Cancelar","saveAsPdf":"Guardar como PDF"}},"panZoomAnimation":{"helpText":"Puedes desplazar y hacer zoom al tablero en cualquier momento para darle una mirada m\xe1s detallada a tu curr\xedculum."},"markdownHelpText":"Puedes usar <1>GitHub Flavored Markdown</1> para darle estilo a esta secci\xf3n."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL de la foto"},"firstName":{"label":"Primer Nombre"},"lastName":{"label":"Apellido"},"subtitle":{"label":"Subt\xedtulo"},"address":{"label":"Direcci\xf3n","line1":{"label":"L\xednea de direcci\xf3n 1"},"line2":{"label":"L\xednea de direcci\xf3n 2"},"line3":{"label":"L\xednea de direcci\xf3n 3"}},"phone":{"label":"N\xfamero de tel\xe9fono"},"website":{"label":"Sitio Web"},"email":{"label":"Direcci\xf3n de correo electr\xf3nico"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objetivo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nombre"},"role":{"label":"Puesto"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nombre"},"major":{"label":"Carrera"},"grade":{"label":"Calificaci\xf3n"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"T\xedtulo"},"subtitle":{"label":"Subt\xedtulo"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nombre"},"subtitle":{"label":"Autor\xeda"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nombre"},"level":{"label":"Nivel"},"rating":{"label":"Calificaci\xf3n"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nombre"},"position":{"label":"Puesto de trabajo"},"phone":{"label":"N\xfamero de tel\xe9fono"},"email":{"label":"Direcci\xf3n de correo electr\xf3nico"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Clave"},"value":{"label":"Valor"}}')},function(e){e.exports=JSON.parse('{"title":"Plantillas"}')},function(e){e.exports=JSON.parse('{"title":"Colores","colorOptions":"Opciones de Color","primaryColor":"Color principal","accentColor":"Color secundario","clipboardCopyAction":"El color {{color}} ha sido copiado al portapapeles."}')},function(e){e.exports=JSON.parse('{"title":"Tipograf\xedas","fontFamily":{"label":"Familia Tipogr\xe1fica","helpText":"Tambi\xe9n puedes usar cualquier fuente que est\xe9 instalada en tu sistema. Escribe el nombre de la familia de fuentes aqu\xed para que el navegador la cargue."}}')},function(e){e.exports=JSON.parse('{"title":"Acciones","disclaimer":"Los cambios hechos a tu curr\xedculum se guardan autom\xe1ticamente en el almacenamiento local de tu navegador. Como ning\xfan dato sale de tu navegador, tu informaci\xf3n est\xe1 completamente segura.","importExport":{"heading":"Importar/Exportar","body":"Puedes importar o exportar tus datos en formato JSON. Con el archivo JSON, puedes editar o imprimir tu curr\xedculum desde cualquier dispositivo. Mant\xe9n una copia de este archivo por si lo necesitas m\xe1s tarde.","buttons":{"import":"Importar","export":"Exportar"}},"downloadResume":{"heading":"Descarga tu curr\xedculum","body":"Para descargar instant\xe1neamente una versi\xf3n en PDF de tu curr\xedculum, puedes hacer clic en el siguiente bot\xf3n. Se recomienda que uses la versi\xf3n m\xe1s reciente de Google Chrome para obtener los mejores resultados.","buttons":{"saveAsPdf":"Guardar como PDF"}},"loadDemoData":{"heading":"Agregar Datos de Demostraci\xf3n","body":"\xbfNo sabes por d\xf3nde comenzar? Puedes cargar datos de demostraci\xf3n con valores predeterminados para tener una idea de c\xf3mo luce un curr\xedculum, y comenzar con editar esos valores.","buttons":{"loadData":"Cargar datos"}},"reset":{"heading":"\xa1Restablecer todo!","body":"Esta acci\xf3n reiniciar\xe1 toda tu informaci\xf3n y tambi\xe9n borrar\xe1 las copias de seguridad guardadas en el almacenamiento local de tu navegardor. Aseg\xfarate de haber exportado tu informaci\xf3n antes de continuar con esta acci\xf3n.","buttons":{"reset":"Restablecer"}}}')},function(e){e.exports=JSON.parse('{"title":"Configuraci\xf3n","language":{"label":"Escoger idioma","helpText":"Si deseas ayudar a traducir la app a tu idioma, por favor consulta la <1>documentaci\xf3n de traducci\xf3n</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Acerca de","documentation":{"heading":"Documentaci\xf3n","body":"\xbfQuieres saber m\xe1s sobre la aplicaci\xf3n? \xbfNecesitas informaci\xf3n sobre c\xf3mo contribuir al proyecto? No busques m\xe1s, hay una gu\xeda completa hecha para ti.","buttons":{"documentation":"Documentaci\xf3n"}},"bugOrFeatureRequest":{"heading":"Encontr\xf3 un error? Nueva solicitud de funcionalidad?","body":"\xbfHay algo que no te deja trabajar en tu curr\xedculum? \xbfHayaste un error que no puedes resolver? Abre una discusi\xf3n bajo la secci\xf3n de \\"Issues\\" (problemas) de GitHub, o sigue los siguientes pasos para escribirme un correo.","buttons":{"raiseIssue":"Notificar un problema","sendEmail":"Mandar un correo electr\xf3nico"}},"sourceCode":{"heading":"C\xf3digo Fuente","body":"\xbfTe interesa ejecutar el c\xf3digo fuente de este proyecto? \xbfEres un desarrollador interesado en involucrtarte en el desarrollo de este proyecto? Haz clic en el siguiente bot\xf3n.","buttons":{"githubRepo":"Repositorio de GitHub"}},"license":{"heading":"Informaci\xf3n de licencia","body":"El proyecto est\xe1 regido por la licencia MIT, sobre la cual puedes leer a continuaci\xf3n. B\xe1sicamente, puedes usar el proyecto en cualquier lugar, siempre y cuando le des cr\xe9dito al autor original.","buttons":{"mitLicense":"Licencia MIT"}},"footer":{"credit":"Desarrollado con amor por <1>Amruth Pillai</1>","thanks":"\xa1Gracias por usar Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Ajouter {{- heading}}","startDate":{"label":"Date de d\xe9but"},"endDate":{"label":"Date de fin"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Ajouter"},"delete":{"label":"Supprimer"}},"printDialog":{"heading":"T\xe9l\xe9chargez Votre CV","quality":{"label":"Qualit\xe9"},"printType":{"label":"Type","types":{"unconstrained":"Non Contraint","fitInA4":"Convient au format A4","multiPageA4":"Multi-Page A4"}},"helpText":["Cette m\xe9thode d\'exportation utilise le canevas HTML pour convertir le CV en image et l\'imprimer sur un PDF, ce qui signifie qu\'il perdra toutes les capacit\xe9s de s\xe9lection / analyse.","Si cela est important pour vous, essayez d\'imprimer le CV \xe0 la place en utilisant Cmd/Ctrl + P ou le bouton d\'impression ci-dessous. Le r\xe9sultat peut varier car la sortie d\xe9pend du navigateur, mais il est connu qu\'elle fonctionne mieux sur la derni\xe8re version de Google Chrome."],"buttons":{"cancel":"Annuler","saveAsPdf":"Enregistrer en PDF"}},"panZoomAnimation":{"helpText":"Vous pouvez effectuer un panoramique et un zoom sur le plan de travail \xe0 tout moment pour voir de plus pr\xe8s votre CV."},"markdownHelpText":"Vous pouvez utiliser <1>GitHub Flavored Markdown</1> pour styliser cette section du texte."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL de la photo"},"firstName":{"label":"Pr\xe9nom"},"lastName":{"label":"Nom"},"subtitle":{"label":"Sous-titre"},"address":{"label":"Addresse","line1":{"label":"Adresse ligne 1"},"line2":{"label":"Adresse ligne 2"},"line3":{"label":"Adresse ligne 3"}},"phone":{"label":"Num\xe9ro de t\xe9l\xe9phone"},"website":{"label":"Site Web"},"email":{"label":"Adresse e-mail"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objectif"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nom"},"role":{"label":"R\xf4le"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nom"},"major":{"label":"Sp\xe9cialit\xe9"},"grade":{"label":"Note"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titre"},"subtitle":{"label":"Sous-titre"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nom"},"subtitle":{"label":"Autorit\xe9"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nom"},"level":{"label":"Niveau"},"rating":{"label":"\xc9valuation"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nom"},"position":{"label":"Poste"},"phone":{"label":"Num\xe9ro de t\xe9l\xe9phone"},"email":{"label":"Adresse e-mail"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Cl\xe9"},"value":{"label":"Valeur"}}')},function(e){e.exports=JSON.parse('{"title":"Mod\xe8les"}')},function(e){e.exports=JSON.parse('{"title":"Couleurs","colorOptions":"Options de couleurs","primaryColor":"Couleur Principale","accentColor":"Couleur Secondaire","clipboardCopyAction":"La couleur {{color}} a \xe9t\xe9 copi\xe9 dans le presse-papiers."}')},function(e){e.exports=JSON.parse('{"title":"Polices","fontFamily":{"label":"Famille de polices","helpText":"Vous pouvez \xe9galement utiliser n\'importe quelle police install\xe9e sur votre syst\xe8me. Entrez simplement le nom de la famille de polices ici et le navigateur la chargera pour vous."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Les modifications que vous apportez \xe0 votre CV sont enregistr\xe9es automatiquement dans le stockage local de votre navigateur. Aucune donn\xe9e ne sort, donc vos informations sont compl\xe8tement s\xe9curis\xe9es.","importExport":{"heading":"Import / Export","body":"Vous pouvez importer ou exporter vos donn\xe9es au format JSON. Avec ceci, vous pouvez \xe9diter et imprimer votre CV \xe0 partir de n\'importe quel appareil. Enregistrez ce fichier pour une utilisation ult\xe9rieure.","buttons":{"import":"Importer","export":"Exporter"}},"downloadResume":{"heading":"T\xe9l\xe9chargez votre CV","body":"Vous pouvez cliquer sur le bouton ci-dessous pour t\xe9l\xe9charger instantan\xe9ment une version PDF de votre CV. Pour de meilleurs r\xe9sultats, veuillez utiliser la derni\xe8re version de Google Chrome.","buttons":{"saveAsPdf":"Enregistrer en PDF"}},"loadDemoData":{"heading":"Charger des D\xe9mo donn\xe9es","body":"Vous ne savez pas quoi faire avec une nouvelle page vierge? Chargez des donn\xe9es de d\xe9monstration avec des valeurs pr\xe9remplies pour voir \xe0 quoi devrait ressembler un CV et vous pouvez commencer \xe0 \xe9diter \xe0 partir de l\xe0.","buttons":{"loadData":"Charger des donn\xe9es"}},"reset":{"heading":"Tout supprimer!","body":"Cette action r\xe9initialisera toutes vos donn\xe9es et supprimera \xe9galement les sauvegardes effectu\xe9es sur le stockage local de votre navigateur. donc assurez-vous que vous avez export\xe9 vos informations avant de tout r\xe9initialiser.","buttons":{"reset":"R\xe9initialiser"}}}')},function(e){e.exports=JSON.parse('{"title":"Param\xe8tres","language":{"label":"Langue","helpText":"Si vous souhaitez aider \xe0 traduire l\'application dans votre propre langue, veuillez vous r\xe9f\xe9rer \xe0 la <1>Documentation de traduction</1>."}}')},function(e){e.exports=JSON.parse('{"title":"\xc0 propos","documentation":{"heading":"Documentation","body":"Vous voulez en savoir plus sur l\'application? Besoin d\'informations sur la mani\xe8re de contribuer au projet? Ne cherchez plus, un guide complet est fait pour vous.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Un bug? Une nouvelle fonctionnalit\xe9?","body":"Quelque chose emp\xeache votre progression de faire un CV? Vous avez trouv\xe9 un bug persistant? Parlez-en dans la section GitHub Issues, ou envoyez-moi un email en utilisant les actions ci-dessous.","buttons":{"raiseIssue":"Soulever une anomalie","sendEmail":"Envoyer un e-mail"}},"sourceCode":{"heading":"Code source","body":"Vous voulez ex\xe9cuter le projet depuis sa source ? \xcates-vous un d\xe9veloppeur d\xe9sireux de contribuer au d\xe9veloppement open-source de ce projet ? Cliquez sur le bouton ci-dessous.","buttons":{"githubRepo":"R\xe9pertoire GitHub"}},"license":{"heading":"Information de licence","body":"Le projet est r\xe9gi par la licence MIT, que vous pouvez consulter ci-dessous. Fondamentalement, vous \xeates autoris\xe9 \xe0 utiliser le projet n\'importe o\xf9 \xe0 condition de donner des cr\xe9dits \xe0 l\'auteur d\'origine.","buttons":{"mitLicense":"Licence MIT"}},"footer":{"credit":"Fabriqu\xe9 avec amour par <1>Amruth Pillai</1>","thanks":"Merci d\'utiliser Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u05d4\u05d5\u05e1\u05e3 {{- heading}}","startDate":{"label":"\u05ea\u05d0\u05e8\u05d9\u05da \u05d4\u05ea\u05d7\u05dc\u05d4"},"endDate":{"label":"\u05ea\u05d0\u05e8\u05d9\u05da \u05e1\u05d9\u05d5\u05dd"},"description":{"label":"\u05ea\u05d9\u05d0\u05d5\u05e8"}},"buttons":{"add":{"label":"\u05d4\u05d5\u05e1\u05e3"},"delete":{"label":"\u05de\u05d7\u05e7"}},"printDialog":{"heading":"\u05d4\u05d5\u05e8\u05d3 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da","quality":{"label":"\u05d0\u05d9\u05db\u05d5\u05ea"},"printType":{"label":"\u05e1\u05d5\u05d2","types":{"unconstrained":"\u05d1\u05dc\u05ea\u05d9 \u05de\u05d5\u05d2\u05d1\u05dc\u05ea","fitInA4":"\u05de\u05ea\u05d0\u05d9\u05dd \u05dc- A4","multiPageA4":"A4 \u05e8\u05d1 \u05e2\u05de\u05d5\u05d3\u05d9\u05dd"}},"helpText":["\u05e9\u05d9\u05d8\u05ea \u05d9\u05d9\u05e6\u05d5\u05d0 \u05d6\u05d5 \u05e2\u05d5\u05e9\u05d4 \u05e9\u05d9\u05de\u05d5\u05e9 \u05d1\u05e7\u05e0\u05d1\u05e1 HTML \u05db\u05d3\u05d9 \u05dc\u05d4\u05de\u05d9\u05e8 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05dc\u05ea\u05de\u05d5\u05e0\u05d4 \u05d5\u05dc\u05d4\u05d3\u05e4\u05d9\u05e1\u05d4 \u05e2\u05dc \u05d2\u05d1\u05d9 PDF, \u05de\u05d4 \u05e9\u05d0\u05d5\u05de\u05e8 \u05e9\u05d4\u05d9\u05d0 \u05ea\u05d0\u05d1\u05d3 \u05d0\u05ea \u05db\u05dc \u05d9\u05db\u05d5\u05dc\u05d5\u05ea \u05d4\u05d1\u05d7\u05d9\u05e8\u05d4 / \u05e0\u05d9\u05ea\u05d5\u05d7.","\u05d0\u05dd \u05d6\u05d4 \u05d7\u05e9\u05d5\u05d1 \u05dc\u05da, \u05d0\u05e0\u05d0 \u05e0\u05e1\u05d4 \u05dc\u05d4\u05d3\u05e4\u05d9\u05e1 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05d1\u05de\u05e7\u05d5\u05dd \u05d6\u05d0\u05ea, \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea Cmd / Ctrl + P \u05d0\u05d5 \u05e2\u05dc \u05db\u05e4\u05ea\u05d5\u05e8 \u05d4\u05d4\u05d3\u05e4\u05e1\u05d4 \u05e9\u05dc\u05de\u05d8\u05d4. \u05d4\u05ea\u05d5\u05e6\u05d0\u05d4 \u05e2\u05e9\u05d5\u05d9\u05d4 \u05dc\u05d4\u05e9\u05ea\u05e0\u05d5\u05ea \u05de\u05db\u05d9\u05d5\u05d5\u05df \u05e9\u05d4\u05e4\u05dc\u05d8 \u05ea\u05dc\u05d5\u05d9 \u05d1\u05d3\u05e4\u05d3\u05e4\u05df, \u05d0\u05da \u05d9\u05d3\u05d5\u05e2 \u05e9\u05d4\u05d5\u05d0 \u05e4\u05d5\u05e2\u05dc \u05d1\u05e6\u05d5\u05e8\u05d4 \u05d4\u05d8\u05d5\u05d1\u05d4 \u05d1\u05d9\u05d5\u05ea\u05e8 \u05e2\u05dc \u05d4\u05d2\u05e8\u05e1\u05d4 \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d4 \u05e9\u05dc Google Chrome."],"buttons":{"cancel":"\u05d1\u05d9\u05d8\u05d5\u05dc","saveAsPdf":"\u05e9\u05de\u05d5\u05e8 \u05db-PDF"}},"panZoomAnimation":{"helpText":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05d4\u05d6\u05d9\u05d6 \u05d0\u05ea \u05d4\u05de\u05e9\u05d8\u05d7 \u05d5\u05dc\u05d4\u05ea\u05e7\u05e8\u05d1 \u05d0\u05dc\u05d9\u05d5 \u05d1\u05db\u05dc \u05e2\u05ea \u05db\u05d3\u05d9 \u05dc\u05e8\u05d0\u05d5\u05ea \u05de\u05e7\u05e8\u05d5\u05d1 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da."},"markdownHelpText":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05d4\u05e9\u05ea\u05de\u05e9 \u05d1 <1> \u05e1\u05d9\u05de\u05d5\u05df \u05d1\u05d8\u05e2\u05dd GitHub \u05d1\u05d8\u05e2\u05dd </1> \u05db\u05d3\u05d9 \u05dc\u05e2\u05e6\u05d1 \u05e7\u05d8\u05e2 \u05d6\u05d4 \u05e9\u05dc \u05d4\u05d8\u05e7\u05e1\u05d8."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u05e7\u05d9\u05e9\u05d5\u05e8 \u05dc\u05ea\u05de\u05d5\u05e0\u05d4"},"firstName":{"label":"\u05e9\u05dd \u05e4\u05e8\u05d8\u05d9"},"lastName":{"label":"\u05e9\u05dd \u05de\u05e9\u05e4\u05d7\u05d4"},"subtitle":{"label":"\u05ea\u05e8\u05d2\u05d5\u05dd"},"address":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea","line1":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d5\u05e8\u05d4 1"},"line2":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d5\u05e8\u05d4 2"},"line3":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05e9\u05d5\u05e8\u05d4 3"}},"phone":{"label":"\u05de\u05e1\u05e4\u05e8 \u05d8\u05dc\u05e4\u05d5\u05df"},"website":{"label":"\u05d0\u05ea\u05e8"},"email":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05d0\u05d9\u05de\u05d9\u05d9\u05dc"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u05de\u05d8\u05e8\u05d4"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"role":{"label":"\u05ea\u05e4\u05e7\u05d9\u05d3"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"major":{"label":"\u05de\u05e7\u05e6\u05d5\u05e2 \u05e8\u05d0\u05e9\u05d9"},"grade":{"label":"\u05db\u05d9\u05ea\u05d4"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"subtitle":{"label":"\u05ea\u05e8\u05d2\u05d5\u05dd"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"subtitle":{"label":"\u05ea\u05e8\u05d2\u05d5\u05dd"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"level":{"label":"\u05e8\u05de\u05d4"},"rating":{"label":"\u05d3\u05d9\u05e8\u05d5\u05d2"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u05db\u05d5\u05ea\u05e8\u05ea"},"position":{"label":"\u05ea\u05e4\u05e7\u05d9\u05d3"},"phone":{"label":"\u05de\u05e1\u05e4\u05e8 \u05d8\u05dc\u05e4\u05d5\u05df"},"email":{"label":"\u05db\u05ea\u05d5\u05d1\u05ea \u05d0\u05d9\u05de\u05d9\u05d9\u05dc"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u05de\u05e4\u05ea\u05d7"},"value":{"label":"\u05e2\u05e8\u05da"}}')},function(e){e.exports=JSON.parse('{"title":"\u05ea\u05d1\u05e0\u05d9\u05d5\u05ea"}')},function(e){e.exports=JSON.parse('{"title":"\u05e6\u05d1\u05e2\u05d9\u05dd","colorOptions":"\u05d0\u05e4\u05e9\u05e8\u05d5\u05d9\u05d5\u05ea \u05e6\u05d1\u05e2\u05d9\u05dd","primaryColor":"\u05e6\u05d1\u05e2 \u05e8\u05d0\u05e9\u05d9","accentColor":"\u05e6\u05d1\u05e2 \u05d4\u05d3\u05d2\u05e9\u05d4","clipboardCopyAction":"{{color}} \u05d4\u05d5\u05e2\u05ea\u05e7 \u05d0\u05dc \u05d4\u05dc\u05d5\u05d7 \u05e9\u05dc\u05da."}')},function(e){e.exports=JSON.parse('{"title":"\u05d2\u05d5\u05e4\u05e0\u05d9\u05dd","fontFamily":{"label":"\u05de\u05e9\u05e4\u05d7\u05ea \u05d2\u05d5\u05e4\u05e0\u05d9\u05dd","helpText":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05d4\u05e9\u05ea\u05de\u05e9 \u05d1\u05db\u05dc \u05d2\u05d5\u05e4\u05df \u05d4\u05de\u05d5\u05ea\u05e7\u05df \u05d2\u05dd \u05d1\u05de\u05e2\u05e8\u05db\u05ea \u05e9\u05dc\u05da. \u05e4\u05e9\u05d5\u05d8 \u05d4\u05d6\u05df \u05db\u05d0\u05df \u05d0\u05ea \u05e9\u05dd \u05de\u05e9\u05e4\u05d7\u05ea \u05d4\u05d2\u05d5\u05e4\u05e0\u05d9\u05dd \u05d5\u05d4\u05d3\u05e4\u05d3\u05e4\u05df \u05d9\u05d8\u05e2\u05d9\u05df \u05d0\u05d5\u05ea\u05d5 \u05e2\u05d1\u05d5\u05e8\u05da."}}')},function(e){e.exports=JSON.parse('{"title":"\u05e4\u05e2\u05d5\u05dc\u05d5\u05ea","disclaimer":"\u05d4\u05e9\u05d9\u05e0\u05d5\u05d9\u05d9\u05dd \u05e9\u05d0\u05ea\u05d4 \u05de\u05d1\u05e6\u05e2 \u05d1\u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da \u05e0\u05e9\u05de\u05e8\u05d9\u05dd \u05d0\u05d5\u05d8\u05d5\u05de\u05d8\u05d9\u05ea \u05d1\u05d0\u05d7\u05e1\u05d5\u05df \u05d4\u05de\u05e7\u05d5\u05de\u05d9 \u05e9\u05dc \u05d4\u05d3\u05e4\u05d3\u05e4\u05df. \u05e0\u05ea\u05d5\u05e0\u05d9\u05dd \u05dc\u05d0 \u05d9\u05d5\u05e6\u05d0\u05d9\u05dd \u05dc\u05e9\u05d5\u05dd \u05de\u05e7\u05d5\u05dd, \u05d5\u05de\u05db\u05d0\u05df \u05e9\u05d4\u05de\u05d9\u05d3\u05e2 \u05e9\u05dc\u05da \u05de\u05d0\u05d5\u05d1\u05d8\u05d7 \u05dc\u05d7\u05dc\u05d5\u05d8\u05d9\u05df.","importExport":{"heading":"\u05d9\u05d9\u05d1\u05d5\u05d0/\u05d9\u05d9\u05e6\u05d5\u05d0","body":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05d9\u05d9\u05d1\u05d0 \u05d0\u05d5 \u05dc\u05d9\u05d9\u05e6\u05d0 \u05d0\u05ea \u05d4\u05e0\u05ea\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc\u05da \u05d1\u05e4\u05d5\u05e8\u05de\u05d8 JSON. \u05d1\u05e2\u05d6\u05e8\u05ea\u05d5 \u05ea\u05d5\u05db\u05dc\u05d5 \u05dc\u05e2\u05e8\u05d5\u05da \u05d5\u05dc\u05d4\u05d3\u05e4\u05d9\u05e1 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05de\u05db\u05dc \u05de\u05db\u05e9\u05d9\u05e8. \u05e9\u05de\u05d5\u05e8 \u05e7\u05d5\u05d1\u05e5 \u05d6\u05d4 \u05dc\u05e9\u05d9\u05de\u05d5\u05e9 \u05de\u05d0\u05d5\u05d7\u05e8 \u05d9\u05d5\u05ea\u05e8.","buttons":{"import":"\u05d9\u05d9\u05d1\u05d5\u05d0","export":"\u05d9\u05d9\u05e6\u05d5\u05d0"}},"downloadResume":{"heading":"\u05d4\u05d5\u05e8\u05d3 \u05d0\u05ea \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da","body":"\u05d0\u05ea\u05d4 \u05d9\u05db\u05d5\u05dc \u05dc\u05dc\u05d7\u05d5\u05e5 \u05e2\u05dc \u05d4\u05db\u05e4\u05ea\u05d5\u05e8 \u05dc\u05de\u05d8\u05d4 \u05db\u05d3\u05d9 \u05dc\u05d4\u05d5\u05e8\u05d9\u05d3 \u05e7\u05d5\u05e8\u05d5\u05ea \u05d7\u05d9\u05d9\u05dd PDF \u05e9\u05dc \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05e9\u05dc\u05da \u05d1\u05d0\u05d5\u05e4\u05df \u05de\u05d9\u05d9\u05d3\u05d9. \u05dc\u05e7\u05d1\u05dc\u05ea \u05d4\u05ea\u05d5\u05e6\u05d0\u05d5\u05ea \u05d4\u05d8\u05d5\u05d1\u05d5\u05ea \u05d1\u05d9\u05d5\u05ea\u05e8, \u05d0\u05e0\u05d0 \u05d4\u05e9\u05ea\u05de\u05e9 \u05d1\u05d2\u05e8\u05e1\u05d4 \u05d4\u05d0\u05d7\u05e8\u05d5\u05e0\u05d4 \u05e9\u05dc Google Chrome.","buttons":{"saveAsPdf":"\u05e9\u05de\u05d5\u05e8 \u05db-PDF"}},"loadDemoData":{"heading":"\u05d8\u05e2\u05df \u05e0\u05ea\u05d5\u05e0\u05d9 \u05d3\u05de\u05d4","body":"\u05dc\u05d0 \u05d1\u05e8\u05d5\u05e8 \u05dc\u05da \u05de\u05d4 \u05dc\u05e2\u05e9\u05d5\u05ea \u05e2\u05dd \u05d3\u05e3 \u05e8\u05d9\u05e7? \u05d8\u05e2\u05df \u05db\u05de\u05d4 \u05e0\u05ea\u05d5\u05e0\u05d9 \u05d3\u05de\u05d4 \u05e2\u05dd \u05e2\u05e8\u05db\u05d9\u05dd \u05d4\u05de\u05d5\u05d2\u05d3\u05e8\u05d9\u05dd \u05de\u05e8\u05d0\u05e9 \u05db\u05d3\u05d9 \u05dc\u05e8\u05d0\u05d5\u05ea \u05db\u05d9\u05e6\u05d3 \u05e7\u05d5\u05e8\u05d5\u05ea \u05d4\u05d7\u05d9\u05d9\u05dd \u05d0\u05de\u05d5\u05e8\u05d9\u05dd \u05dc\u05d4\u05d9\u05e8\u05d0\u05d5\u05ea \u05d5\u05ea\u05d5\u05db\u05dc \u05dc\u05d4\u05ea\u05d7\u05d9\u05dc \u05dc\u05e2\u05e8\u05d5\u05da \u05de\u05e9\u05dd.","buttons":{"loadData":"\u05d8\u05e2\u05df \u05de\u05d9\u05d3\u05e2"}},"reset":{"heading":"\u05d0\u05e4\u05e1 \u05d4\u05db\u05dc","body":"\u05e4\u05e2\u05d5\u05dc\u05d4 \u05d6\u05d5 \u05ea\u05d0\u05e4\u05e1 \u05d0\u05ea \u05db\u05dc \u05d4\u05e0\u05ea\u05d5\u05e0\u05d9\u05dd \u05e9\u05dc\u05da \u05d5\u05ea\u05e1\u05d9\u05e8 \u05d2\u05d9\u05d1\u05d5\u05d9\u05d9\u05dd \u05e9\u05e0\u05e2\u05e9\u05d5 \u05d2\u05dd \u05dc\u05d0\u05d7\u05e1\u05d5\u05df \u05d4\u05de\u05e7\u05d5\u05de\u05d9 \u05e9\u05dc \u05d4\u05d3\u05e4\u05d3\u05e4\u05df \u05e9\u05dc\u05da, \u05d0\u05e0\u05d0 \u05d5\u05d5\u05d3\u05d0 \u05e9\u05d9\u05e6\u05d0\u05ea \u05d0\u05ea \u05d4\u05de\u05d9\u05d3\u05e2 \u05e9\u05dc\u05da \u05dc\u05e4\u05e0\u05d9 \u05e9\u05ea\u05d0\u05e4\u05e1 \u05d0\u05ea \u05d4\u05db\u05dc.","buttons":{"reset":"\u05d0\u05d9\u05e4\u05d5\u05e1"}}}')},function(e){e.exports=JSON.parse('{"title":"\u05d4\u05d2\u05d3\u05e8\u05d5\u05ea","language":{"label":"\u05e9\u05e4\u05d4","helpText":"\u05d0\u05dd \u05ea\u05e8\u05e6\u05d4 \u05dc\u05e2\u05d6\u05d5\u05e8 \u05d1\u05ea\u05e8\u05d2\u05d5\u05dd \u05d4\u05d0\u05e4\u05dc\u05d9\u05e7\u05e6\u05d9\u05d4 \u05dc\u05e9\u05e4\u05d4 \u05e9\u05dc\u05da, \u05e2\u05d9\u05d9\u05df \u05d1 <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"\u05d0\u05d5\u05d3\u05d5\u05ea","documentation":{"heading":"\u05ea\u05d9\u05e2\u05d5\u05d3","body":"\u05e8\u05d5\u05e6\u05d4 \u05dc\u05d3\u05e2\u05ea \u05d9\u05d5\u05ea\u05e8 \u05e2\u05dc \u05d4\u05d0\u05e4\u05dc\u05d9\u05e7\u05e6\u05d9\u05d4? \u05d4\u05d0\u05dd \u05dc\u05d0 \u05d4\u05d9\u05d4 \u05e0\u05d7\u05de\u05d3 \u05d0\u05dd \u05d4\u05d9\u05d4 \u05de\u05d3\u05e8\u05d9\u05da \u05dc\u05d4\u05ea\u05e7\u05e0\u05ea\u05d5 \u05d1\u05de\u05d7\u05e9\u05d1 \u05d4\u05de\u05e7\u05d5\u05de\u05d9 \u05e9\u05dc\u05db\u05dd? \u05d6\u05e7\u05d5\u05e7 \u05dc\u05de\u05d9\u05d3\u05e2 \u05db\u05d9\u05e6\u05d3 \u05dc\u05ea\u05e8\u05d5\u05dd \u05dc\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8? \u05d0\u05dc \u05ea\u05d7\u05db\u05d4 \u05d9\u05d5\u05ea\u05e8, \u05d9\u05e9 \u05ea\u05d9\u05e2\u05d5\u05d3 \u05de\u05e7\u05d9\u05e3 \u05e9\u05d9\u05d5\u05e6\u05e8 \u05d1\u05d3\u05d9\u05d5\u05e7 \u05d1\u05e9\u05d1\u05d9\u05dc\u05da.","buttons":{"documentation":"\u05ea\u05d9\u05e2\u05d5\u05d3"}},"bugOrFeatureRequest":{"heading":"\u05d1\u05d0\u05d2? \u05d1\u05e7\u05e9\u05d4 \u05dc\u05ea\u05d5\u05e1\u05e3?","body":"\u05de\u05e9\u05d4\u05d5 \u05e9\u05e2\u05d5\u05e6\u05e8 \u05d0\u05ea \u05d4\u05d4\u05ea\u05e7\u05d3\u05de\u05d5\u05ea \u05e9\u05dc\u05da \u05de\u05dc\u05db\u05ea\u05d5\u05d1 \u05e7\u05d5\u05e8\u05d5\u05ea \u05d7\u05d9\u05d9\u05dd? \u05de\u05e6\u05d0\u05ea \u05d1\u05d0\u05d2 \u05de\u05e6\u05d9\u05e7 \u05e9\u05e4\u05e9\u05d5\u05d8 \u05dc\u05d0 \u05e0\u05e4\u05ea\u05e8? \u05d3\u05d5\u05d5\u05d7 \u05e2\u05dc \u05d6\u05d4 \u05d1\u05e7\u05d8\u05e2 \u05d1\u05e2\u05d9\u05d5\u05ea \u05d1-GitHub, \u05d0\u05d5 \u05e9\u05dc\u05d7 \u05dc\u05d9 \u05d0\u05d9\u05de\u05d9\u05d9\u05dc \u05d1\u05d0\u05de\u05e6\u05e2\u05d5\u05ea \u05d4\u05e4\u05e7\u05d5\u05d3\u05d5\u05ea \u05dc\u05de\u05d8\u05d4.","buttons":{"raiseIssue":"\u05d4\u05e2\u05dc\u05d4 \u05d1\u05e2\u05d9\u05d4","sendEmail":"\u05e9\u05dc\u05d7 \u05d0\u05d9\u05de\u05d9\u05d9\u05dc"}},"sourceCode":{"heading":"\u05e7\u05d5\u05d3 \u05de\u05e7\u05d5\u05e8","body":"\u05e8\u05d5\u05e6\u05d9\u05dd \u05dc\u05e0\u05d4\u05dc \u05d0\u05ea \u05d4\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8 \u05de\u05e7\u05d5\u05d1\u05e5 \u05d4\u05de\u05e7\u05d5\u05e8 \u05e9\u05dc\u05d5? \u05d4\u05d0\u05dd \u05d0\u05ea\u05d4 \u05de\u05e4\u05ea\u05d7 \u05e9\u05de\u05d5\u05db\u05df \u05dc\u05ea\u05e8\u05d5\u05dd \u05dc\u05e4\u05d9\u05ea\u05d5\u05d7 \u05d4\u05e7\u05d5\u05d3 \u05d4\u05e4\u05ea\u05d5\u05d7 \u05e9\u05dc \u05d4\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8 \u05d4\u05d6\u05d4? \u05dc\u05d7\u05e5 \u05e2\u05dc \u05d4\u05db\u05e4\u05ea\u05d5\u05e8 \u05dc\u05de\u05d8\u05d4.","buttons":{"githubRepo":"\u05de\u05d0\u05d2\u05e8 \u05d2\u05d9\u05ea\u05d5\u05d1"}},"license":{"heading":"\u05de\u05d9\u05d3\u05e2 \u05e2\u05dc \u05d4\u05e8\u05e9\u05d9\u05d5\u05df","body":"\u05d4\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8 \u05de\u05e0\u05d5\u05d4\u05dc \u05ea\u05d7\u05ea \u05e8\u05d9\u05e9\u05d9\u05d5\u05df MIT, \u05e9\u05ea\u05d5\u05db\u05dc \u05dc\u05e7\u05e8\u05d5\u05d0 \u05e2\u05dc\u05d9\u05d5 \u05d9\u05d5\u05ea\u05e8 \u05d1\u05d4\u05de\u05e9\u05da. \u05d1\u05e4\u05e9\u05d8\u05d5\u05ea, \u05de\u05d5\u05ea\u05e8 \u05dc\u05da \u05dc\u05d4\u05e9\u05ea\u05de\u05e9 \u05d1\u05e4\u05e8\u05d5\u05d9\u05e7\u05d8 \u05d1\u05db\u05dc \u05de\u05e7\u05d5\u05dd \u05d5\u05d1\u05db\u05dc \u05d3\u05e8\u05da \u05d5\u05d1\u05dc\u05d1\u05d3 \u05e9\u05ea\u05e2\u05e0\u05d9\u05e7 \u05e7\u05e8\u05d3\u05d9\u05d8 \u05dc\u05de\u05d7\u05d1\u05e8 \u05d4\u05de\u05e7\u05d5\u05e8\u05d9.","buttons":{"mitLicense":"MIT \u05e8\u05e9\u05d9\u05d5\u05df"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"\u05ea\u05d5\u05d3\u05d4 \u05e9\u05d0\u05ea\u05d4 \u05de\u05e9\u05ea\u05de\u05e9 \u05d1Reactive Resume"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"{{- heading}} \u091c\u094b\u0921\u093c\u0947\u0902","startDate":{"label":"\u092a\u094d\u0930\u093e\u0930\u0902\u092d \u0924\u093f\u0925\u093f"},"endDate":{"label":"\u0905\u0902\u0924\u093f\u092e \u0924\u093f\u0925\u093f"},"description":{"label":"\u0935\u093f\u0935\u0930\u0923"}},"buttons":{"add":{"label":"\u091c\u094b\u0921\u093c\u0928\u093e"},"delete":{"label":"\u0939\u091f\u093e\u090f\u0901"}},"printDialog":{"heading":"\u0905\u092a\u0928\u093e \u0930\u093f\u091c\u094d\u092f\u0942\u092e\u0947 \u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u0915\u0930\u0947\u0902","quality":{"label":"\u0917\u0941\u0923\u0935\u0924\u094d\u0924\u093e \u092e\u0942\u0932\u094d\u092f"},"printType":{"label":"\u092a\u094d\u0930\u0915\u093e\u0930","types":{"unconstrained":"\u0938\u094d\u0935\u0947\u091a\u094d\u091b\u093e\u092a\u0942\u0930\u094d\u0923","fitInA4":"A4 \u092e\u0947\u0902 \u092b\u093f\u091f","multiPageA4":"\u092c\u0939\u0941 \u092a\u0943\u0937\u094d\u0920 A4"}},"helpText":["\u092f\u0939 \u0928\u093f\u0930\u094d\u092f\u093e\u0924 \u0935\u093f\u0927\u093f \u0906\u092a\u0915\u0940 CV \u0915\u094b \u0906\u092a\u0915\u0947 \u0938\u0940\u0935\u0940 \u0915\u094b \u092c\u0926\u0932\u0928\u0947 \u0914\u0930 PDF \u092a\u0930 \u092a\u094d\u0930\u093f\u0902\u091f \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f HTML \u0915\u0948\u0928\u0935\u093e\u0938 \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0924\u0940 \u0939\u0948, \u091c\u093f\u0938\u0915\u093e \u0905\u0930\u094d\u0925 \u0939\u0948 \u0915\u093f \u092f\u0939 \u0938\u092d\u0940 \u091a\u092f\u0928 / \u092a\u093e\u0930\u094d\u0938\u093f\u0902\u0917 \u0915\u094d\u0937\u092e\u0924\u093e\u0913\u0902 \u0915\u094b \u0916\u094b \u0926\u0947\u0917\u093e\u0964","\u092f\u0926\u093f \u0906\u092a\u0915\u0947 \u0932\u093f\u090f \u092f\u0939 \u092e\u0939\u0924\u094d\u0935\u092a\u0942\u0930\u094d\u0923 \u0939\u0948, \u0924\u094b \u0915\u0943\u092a\u092f\u093e Cmd / Ctrl + P \u092f\u093e \u0928\u0940\u091a\u0947 \u0926\u093f\u090f \u0917\u090f \u092a\u094d\u0930\u093f\u0902\u091f \u092c\u091f\u0928 \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0928\u0947 \u0915\u0947 \u092c\u091c\u093e\u092f CV \u0915\u094b \u092a\u094d\u0930\u093f\u0902\u091f \u0915\u0930\u0928\u0947 \u0915\u093e \u092a\u094d\u0930\u092f\u093e\u0938 \u0915\u0930\u0947\u0902\u0964 \u0928\u0924\u0940\u091c\u093e \u0905\u0932\u0917-\u0905\u0932\u0917 \u0939\u094b \u0938\u0915\u0924\u093e \u0939\u0948 \u0915\u094d\u092f\u094b\u0902\u0915\u093f \u0906\u0909\u091f\u092a\u0941\u091f \u092c\u094d\u0930\u093e\u0909\u091c\u093c\u0930 \u092a\u0930 \u0928\u093f\u0930\u094d\u092d\u0930 \u0939\u0948, \u0932\u0947\u0915\u093f\u0928 \u092f\u0939 Google \u0915\u094d\u0930\u094b\u092e \u0915\u0947 \u0928\u0935\u0940\u0928\u0924\u092e \u0938\u0902\u0938\u094d\u0915\u0930\u0923 \u092a\u0930 \u0938\u092c\u0938\u0947 \u0905\u091a\u094d\u091b\u093e \u0915\u093e\u092e \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u091c\u093e\u0928\u093e \u091c\u093e\u0924\u093e \u0939\u0948\u0964"],"buttons":{"cancel":"\u0930\u0926\u094d\u0926 \u0915\u0930\u0947\u0902","saveAsPdf":"\u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u092a\u0940\u0921\u0940\u090d\u092b\u093c"}},"panZoomAnimation":{"helpText":"\u0905\u092a\u0928\u0947 \u0930\u093f\u091c\u094d\u092f\u0942\u092e\u0947 \u0915\u094b \u0915\u0930\u0940\u092c \u0938\u0947 \u091c\u093e\u0928\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u0906\u092a \u0915\u093f\u0938\u0940 \u092d\u0940 \u0938\u092e\u092f \u0906\u0930\u094d\u091f\u092c\u094b\u0930\u094d\u0921 \u092a\u0930 \u092a\u0948\u0928 \u0914\u0930 \u091c\u0942\u092e \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964"},"markdownHelpText":"\u0906\u092a \u092a\u093e\u0920 \u0915\u0947 \u0907\u0938 \u0916\u0902\u0921 \u0915\u094b \u0938\u094d\u091f\u093e\u0907\u0932 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f <1>GitHub Flavoured Markdown</1> \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964"}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u092b\u094b\u091f\u094b \u0932\u093f\u0902\u0915"},"firstName":{"label":"\u092a\u0939\u0932\u093e \u0928\u093e\u092e"},"lastName":{"label":"\u0909\u092a\u0928\u093e\u092e"},"subtitle":{"label":"\u0909\u092a\u0936\u0940\u0930\u094d\u0937\u0915"},"address":{"label":"\u092a\u0924\u093e","line1":{"label":"\u092a\u0924\u093e \u092a\u0902\u0915\u094d\u0924\u093f 1"},"line2":{"label":"\u092a\u0924\u093e \u092a\u0902\u0915\u094d\u0924\u093f 2"},"line3":{"label":"\u092a\u0924\u093e \u092a\u0902\u0915\u094d\u0924\u093f 3"}},"phone":{"label":"\u092b\u094b\u0928 \u0928\u0902\u092c\u0930"},"website":{"label":"\u0935\u0947\u092c\u0938\u093e\u0907\u091f"},"email":{"label":"\u0908\u092e\u0947\u0932 \u092a\u0924\u093e"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0932\u0915\u094d\u0937\u094d\u092f"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0928\u093e\u092e"},"role":{"label":"\u092d\u0942\u092e\u093f\u0915\u093e"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0928\u093e\u092e"},"major":{"label":"\u0935\u093f\u0937\u092f"},"grade":{"label":"\u0917\u094d\u0930\u0947\u0921"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0936\u0940\u0930\u094d\u0937\u0915"},"subtitle":{"label":"\u0909\u092a\u0936\u0940\u0930\u094d\u0937\u0915"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0928\u093e\u092e"},"subtitle":{"label":"\u0905\u0927\u093f\u0915\u093e\u0930"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0928\u093e\u092e"},"level":{"label":"\u0938\u094d\u0924\u0930"},"rating":{"label":"\u0930\u0947\u091f\u093f\u0902\u0917"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0928\u093e\u092e"},"position":{"label":"\u092a\u0926/\u0938\u094d\u0925\u093e\u0928"},"phone":{"label":"\u092b\u094b\u0928 \u0928\u0902\u092c\u0930"},"email":{"label":"\u0908\u092e\u0947\u0932 \u092a\u0924\u093e"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u092e\u094c\u0932\u093f\u0915"},"value":{"label":"\u092e\u0942\u0932\u094d\u092f"}}')},function(e){e.exports=JSON.parse('{"title":"\u091f\u0947\u092e\u094d\u092a\u0932\u0947\u091f\u094d\u0938"}')},function(e){e.exports=JSON.parse('{"title":"\u0930\u0902\u0917","colorOptions":"\u0930\u0902\u0917 \u0935\u093f\u0915\u0932\u094d\u092a","primaryColor":"\u092a\u094d\u0930\u093e\u0925\u092e\u093f\u0915 \u0930\u0902\u0917","accentColor":"\u0926\u094d\u0935\u093f\u0924\u0940\u092f\u0915 \u0930\u0902\u0917","clipboardCopyAction":"{{color}} \u0915\u094b \u0915\u094d\u0932\u093f\u092a\u092c\u094b\u0930\u094d\u0921 \u092a\u0930 \u0915\u0949\u092a\u0940 \u0915\u093f\u092f\u093e \u0917\u092f\u093e \u0939\u0948\u0964"}')},function(e){e.exports=JSON.parse('{"title":"\u092b\u094b\u0902\u091f\u094d\u0938","fontFamily":{"label":"\u092b\u093c\u0949\u0928\u094d\u091f \u092a\u0930\u093f\u0935\u093e\u0930","helpText":"\u0906\u092a \u0905\u092a\u0928\u0947 \u0938\u093f\u0938\u094d\u091f\u092e \u092a\u0930 \u0938\u094d\u0925\u093e\u092a\u093f\u0924 \u0915\u093f\u0938\u0940 \u092d\u0940 \u092b\u093c\u0949\u0928\u094d\u091f \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u092c\u0938 \u092f\u0939\u093e\u0902 \u092b\u093c\u0949\u0928\u094d\u091f \u0915\u093e \u0928\u093e\u092e \u0926\u0930\u094d\u091c \u0915\u0930\u0947\u0902 \u0914\u0930 \u092c\u094d\u0930\u093e\u0909\u091c\u093c\u0930 \u0907\u0938\u0947 \u0906\u092a\u0915\u0947 \u0932\u093f\u090f \u0932\u094b\u0921 \u0915\u0930\u0947\u0917\u093e\u0964"}}')},function(e){e.exports=JSON.parse('{"title":"\u0915\u093e\u0930\u094d\u0930\u0935\u093e\u0908","disclaimer":"\u0906\u092a\u0915\u0947 \u092c\u093e\u092f\u094b\u0921\u093e\u091f\u093e \u092e\u0947\u0902 \u0906\u092a\u0915\u0947 \u0926\u094d\u0935\u093e\u0930\u093e \u0915\u093f\u090f \u0917\u090f \u092a\u0930\u093f\u0935\u0930\u094d\u0924\u0928 \u0938\u094d\u0935\u091a\u093e\u0932\u093f\u0924 \u0930\u0942\u092a \u0938\u0947 \u0906\u092a\u0915\u0947 \u092c\u094d\u0930\u093e\u0909\u091c\u093c\u0930 \u0915\u0947 \u0938\u094d\u0925\u093e\u0928\u0940\u092f \u092d\u0902\u0921\u093e\u0930\u0923 \u092e\u0947\u0902 \u0938\u0939\u0947\u091c\u0947 \u091c\u093e\u0924\u0947 \u0939\u0948\u0902\u0964 \u0915\u094b\u0908 \u0921\u0947\u091f\u093e \u0928\u0939\u0940\u0902 \u0928\u093f\u0915\u0932\u0924\u093e \u0939\u0948, \u0907\u0938\u0932\u093f\u090f \u0906\u092a\u0915\u0940 \u091c\u093e\u0928\u0915\u093e\u0930\u0940 \u092a\u0942\u0930\u0940 \u0924\u0930\u0939 \u0938\u0947 \u0938\u0941\u0930\u0915\u094d\u0937\u093f\u0924 \u0939\u0948\u0964","importExport":{"heading":"\u0906\u092f\u093e\u0924 / \u0928\u093f\u0930\u094d\u092f\u093e\u0924","body":"\u0906\u092a JSON \u092a\u094d\u0930\u093e\u0930\u0942\u092a \u092e\u0947\u0902 \u0905\u092a\u0928\u093e \u0921\u0947\u091f\u093e \u0906\u092f\u093e\u0924 \u092f\u093e \u0928\u093f\u0930\u094d\u092f\u093e\u0924 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u0907\u0938\u0915\u0947 \u0938\u093e\u0925, \u0906\u092a \u0915\u093f\u0938\u0940 \u092d\u0940 \u0921\u093f\u0935\u093e\u0907\u0938 \u0938\u0947 \u0905\u092a\u0928\u093e \u092c\u093e\u092f\u094b\u0921\u093e\u091f\u093e \u0938\u0902\u092a\u093e\u0926\u093f\u0924 \u0914\u0930 \u092a\u094d\u0930\u093f\u0902\u091f \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u092c\u093e\u0926 \u092e\u0947\u0902 \u0909\u092a\u092f\u094b\u0917 \u0915\u0947 \u0932\u093f\u090f \u0907\u0938 \u092b\u093e\u0907\u0932 \u0915\u094b \u0938\u0947\u0935 \u0915\u0930\u0947\u0902\u0964","buttons":{"import":"\u0906\u092f\u093e\u0924","export":"\u0928\u093f\u0930\u094d\u092f\u093e\u0924"}},"downloadResume":{"heading":"\u0905\u092a\u0928\u093e \u0930\u093f\u091c\u094d\u092f\u0942\u092e\u0947 \u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u0915\u0930\u0947\u0902","body":"\u0906\u092a \u0905\u092a\u0928\u0947 \u0930\u0947\u091c\u093c\u094d\u092f\u0942\u092e\u0947 \u0915\u093e \u092a\u0940\u0921\u0940\u090f\u092b \u0924\u0941\u0930\u0902\u0924 \u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u0928\u0940\u091a\u0947 \u0926\u093f\u090f \u0917\u090f \u092c\u091f\u0928 \u092a\u0930 \u0915\u094d\u0932\u093f\u0915 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u0938\u0930\u094d\u0935\u094b\u0924\u094d\u0924\u092e \u092a\u0930\u093f\u0923\u093e\u092e\u094b\u0902 \u0915\u0947 \u0932\u093f\u090f, \u0915\u0943\u092a\u092f\u093e \u0928\u0935\u0940\u0928\u0924\u092e Google Chrome \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0947\u0902\u0964","buttons":{"saveAsPdf":"\u0921\u093e\u0909\u0928\u0932\u094b\u0921 \u092a\u0940\u0921\u0940\u090d\u092b\u093c"}},"loadDemoData":{"heading":"\u0921\u0947\u092e\u094b \u0921\u0947\u091f\u093e \u0932\u094b\u0921 \u0915\u0930\u0947\u0902","body":"\u090f\u0915 \u0924\u093e\u091c\u093e \u0930\u093f\u0915\u094d\u0924 \u092a\u0943\u0937\u094d\u0920 \u0915\u0947 \u0938\u093e\u0925 \u0915\u094d\u092f\u093e \u0915\u0930\u0928\u093e \u0939\u0948, \u0907\u0938 \u092a\u0930 \u0905\u0938\u094d\u092a\u0937\u094d\u091f? \u092c\u093e\u092f\u094b\u0921\u093e\u091f\u093e \u0915\u0948\u0938\u0947 \u0926\u093f\u0916\u0928\u093e \u091a\u093e\u0939\u093f\u090f \u092f\u0939 \u0926\u0947\u0916\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u092a\u0942\u0930\u094d\u0935 \u0928\u093f\u0930\u094d\u0927\u093e\u0930\u093f\u0924 \u092e\u0942\u0932\u094d\u092f\u094b\u0902 \u0915\u0947 \u0938\u093e\u0925 \u0915\u0941\u091b \u0921\u0947\u091f\u093e \u0932\u094b\u0921 \u0915\u0930\u0947\u0902 \u0914\u0930 \u0906\u092a \u0935\u0939\u093e\u0902 \u0938\u0947 \u0938\u0902\u092a\u093e\u0926\u0928 \u0936\u0941\u0930\u0942 \u0915\u0930 \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964","buttons":{"loadData":"\u0932\u094b\u0921 \u0921\u0947\u091f\u093e"}},"reset":{"heading":"\u0938\u092c \u0915\u0941\u091b \u0930\u0940\u0938\u0947\u091f \u0915\u0930\u0947\u0902","body":"\u092f\u0939 \u0915\u094d\u0930\u093f\u092f\u093e \u0906\u092a\u0915\u0947 \u0938\u092d\u0940 \u0921\u0947\u091f\u093e \u0915\u094b \u0930\u0940\u0938\u0947\u091f \u0915\u0930 \u0926\u0947\u0917\u0940 \u0914\u0930 \u0938\u093e\u0925 \u0939\u0940 \u0906\u092a\u0915\u0947 \u092c\u094d\u0930\u093e\u0909\u091c\u093c\u0930 \u0915\u0947 \u0938\u094d\u0925\u093e\u0928\u0940\u092f \u0938\u0902\u0917\u094d\u0930\u0939\u0923 \u092e\u0947\u0902 \u0915\u093f\u090f \u0917\u090f \u092c\u0948\u0915\u0905\u092a \u0915\u094b \u0939\u091f\u093e \u0926\u0947\u0917\u0940, \u0907\u0938\u0932\u093f\u090f \u0915\u0943\u092a\u092f\u093e \u0938\u0941\u0928\u093f\u0936\u094d\u091a\u093f\u0924 \u0915\u0930\u0947\u0902 \u0915\u093f \u0906\u092a\u0928\u0947 \u0938\u092c \u0915\u0941\u091b \u0930\u0940\u0938\u0947\u091f \u0915\u0930\u0928\u0947 \u0938\u0947 \u092a\u0939\u0932\u0947 \u0905\u092a\u0928\u0940 \u091c\u093e\u0928\u0915\u093e\u0930\u0940 \u0928\u093f\u0930\u094d\u092f\u093e\u0924 \u0915\u0930 \u0926\u0940 \u0939\u0948\u0964","buttons":{"reset":"\u0930\u0940\u0938\u0947\u091f"}}}')},function(e){e.exports=JSON.parse('{"title":"\u0938\u0947\u091f\u093f\u0902\u0917\u094d\u0938","language":{"label":"\u092d\u093e\u0937\u093e\xa0","helpText":"\u092f\u0926\u093f \u0906\u092a \u090f\u092a\u094d\u0932\u093f\u0915\u0947\u0936\u0928 \u0915\u094b \u0905\u092a\u0928\u0940 \u092d\u093e\u0937\u093e \u092e\u0947\u0902 \u0905\u0928\u0941\u0935\u093e\u0926 \u0915\u0930\u0928\u0947 \u092e\u0947\u0902 \u092e\u0926\u0926 \u0915\u0930\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u0948\u0902, \u0924\u094b \u0915\u0943\u092a\u092f\u093e <1>\u0905\u0928\u0941\u0935\u093e\u0926 \u0926\u0938\u094d\u0924\u093e\u0935\u0947\u091c\u093c</1> \u0926\u0947\u0916\u0947\u0902\u0964"}}')},function(e){e.exports=JSON.parse('{"title":"\u0939\u092e\u093e\u0930\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902","documentation":{"heading":"\u092a\u094d\u0930\u0932\u0947\u0916\u0928","body":"\u090f\u092a\u094d\u0932\u093f\u0915\u0947\u0936\u0928 \u0915\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902 \u0905\u0927\u093f\u0915 \u091c\u093e\u0928\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u0948\u0902? \u0910\u092a \u092e\u0947\u0902 \u092f\u094b\u0917\u0926\u093e\u0928 \u0915\u0930\u0928\u0947 \u0915\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902 \u091c\u093e\u0928\u0915\u093e\u0930\u0940 \u091a\u093e\u0939\u093f\u090f? \u0906\u0917\u0947 \u0928\u0939\u0940\u0902, \u092c\u0938 \u0906\u092a\u0915\u0947 \u0932\u093f\u090f \u090f\u0915 \u0935\u094d\u092f\u093e\u092a\u0915 \u0917\u093e\u0907\u0921 \u092c\u0928\u093e\u092f\u093e \u0917\u092f\u093e \u0939\u0948\u0964","buttons":{"documentation":"\u092a\u094d\u0930\u0932\u0947\u0916\u0928"}},"bugOrFeatureRequest":{"heading":"\u0938\u0902\u0915\u091f? \u0928\u092f\u0940 \u0935\u093f\u0936\u0947\u0937\u0924\u093e?","body":"\u092c\u093e\u092f\u094b\u0921\u093e\u091f\u093e \u092c\u0928\u093e\u0928\u0947 \u0938\u0947 \u0906\u092a\u0915\u0940 \u092a\u094d\u0930\u0917\u0924\u093f \u0930\u0941\u0915 \u0930\u0939\u0940 \u0939\u0948? \u0910\u0938\u0940 \u0938\u092e\u0938\u094d\u092f\u093e \u092e\u093f\u0932\u0940 \u091c\u094b \u0926\u0942\u0930 \u0928\u0939\u0940\u0902 \u0939\u094b\u0917\u0940? \\"\u0917\u093f\u091f\u0939\u092c \u092e\u0941\u0926\u094d\u0926\u0947\\" \u0905\u0928\u0941\u092d\u093e\u0917 \u092a\u0930 \u0907\u0938\u0915\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902 \u0938\u0942\u091a\u093f\u0924 \u0915\u0930\u0947\u0902, \u092f\u093e \u0928\u0940\u091a\u0947 \u0915\u0940 \u0915\u094d\u0930\u093f\u092f\u093e\u0913\u0902 \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0915\u0947 \u092e\u0941\u091d\u0947 \u090f\u0915 \u0908\u092e\u0947\u0932 \u092d\u0947\u091c\u0947\u0902\u0964","buttons":{"raiseIssue":"\u0930\u093f\u092a\u094b\u0930\u094d\u091f \u092e\u0947\u0902 \u0938\u092e\u0938\u094d\u092f\u093e","sendEmail":"\u0908\u092e\u0947\u0932 \u092d\u0947\u091c\u0947\u0902"}},"sourceCode":{"heading":"\u0938\u094b\u0930\u094d\u0938 \u0915\u094b\u0921","body":"\u0916\u0930\u094b\u0902\u091a \u0938\u0947 \u092a\u0930\u093f\u092f\u094b\u091c\u0928\u093e \u0915\u093e \u0928\u093f\u0930\u094d\u092e\u093e\u0923 \u0915\u0930\u0928\u093e \u091a\u093e\u0939\u0924\u0947 \u0939\u0948\u0902? \u0915\u094d\u092f\u093e \u0906\u092a \u0907\u0938 \u092a\u094d\u0930\u094b\u091c\u0947\u0915\u094d\u091f \u0915\u0947 \u0913\u092a\u0928-\u0938\u094b\u0930\u094d\u0938 \u0935\u093f\u0915\u093e\u0938 \u092e\u0947\u0902 \u092f\u094b\u0917\u0926\u093e\u0928 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u0924\u0948\u092f\u093e\u0930 \u0939\u0948\u0902? \u0928\u0940\u091a\u0947 \u0926\u093f\u090f \u0917\u090f \u092c\u091f\u0928 \u092a\u0930 \u0915\u094d\u0932\u093f\u0915 \u0915\u0930\u0947\u0902\u0964","buttons":{"githubRepo":"GitHub \u0930\u0947\u092a\u094b"}},"license":{"heading":"\u0932\u093e\u0907\u0938\u0947\u0902\u0938 \u0915\u0940 \u091c\u093e\u0928\u0915\u093e\u0930\u0940","body":"\u092a\u0930\u093f\u092f\u094b\u091c\u0928\u093e \u090f\u092e\u0906\u0908\u091f\u0940 \u0932\u093e\u0907\u0938\u0947\u0902\u0938 \u0915\u0947 \u0924\u0939\u0924 \u0936\u093e\u0938\u093f\u0924 \u0939\u0948, \u091c\u093f\u0938\u0947 \u0906\u092a \u0928\u0940\u091a\u0947 \u0915\u0947 \u092c\u093e\u0930\u0947 \u092e\u0947\u0902 \u0905\u0927\u093f\u0915 \u092a\u0922\u093c \u0938\u0915\u0924\u0947 \u0939\u0948\u0902\u0964 \u0906\u092a\u0915\u094b \u092a\u0930\u093f\u092f\u094b\u091c\u0928\u093e \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0928\u0947 \u0915\u0940 \u0905\u0928\u0941\u092e\u0924\u093f \u0939\u0948 \u0915\u0939\u0940\u0902 \u092d\u0940 \u0906\u092a \u092e\u0942\u0932 \u0932\u0947\u0916\u0915 \u0915\u094b \u0915\u094d\u0930\u0947\u0921\u093f\u091f \u0926\u0947\u0924\u0947 \u0939\u0948\u0902\u0964","buttons":{"mitLicense":"MIT \u0932\u093e\u0907\u0938\u0947\u0928\u094d\u0938"}},"footer":{"credit":"<1>\u0905\u092e\u0943\u0924 \u092a\u093f\u0932\u094d\u0932\u0908</1> \u0926\u094d\u0935\u093e\u0930\u093e \u092a\u094d\u092f\u093e\u0930 \u0938\u0947 \u092c\u0928\u093e\u092f\u093e \u0917\u092f\u093e","thanks":"\u0930\u093f\u090f\u0915\u094d\u091f\u093f\u0935 \u0930\u093f\u091c\u094d\u092f\u0942\u092e\u0947 \u0915\u093e \u0909\u092a\u092f\u094b\u0917 \u0915\u0930\u0928\u0947 \u0915\u0947 \u0932\u093f\u090f \u0927\u0928\u094d\u092f\u0935\u093e\u0926"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Aggiungi {{- heading}}","startDate":{"label":"Data d\'inizio"},"endDate":{"label":"Data di fine"},"description":{"label":"Descrizione"}},"buttons":{"add":{"label":"Aggiungi"},"delete":{"label":"Elimina"}},"printDialog":{"heading":"Scarica il tuo curriculum","quality":{"label":"Qualit\xe0"},"printType":{"label":"Tipo","types":{"unconstrained":"Libero","fitInA4":"Adatta ad A4","multiPageA4":"Multi-Pagina A4"}},"helpText":["Questo metodo di esportazione fa uso di HTML canvas per convertire il curriculum in un\'immagine e stamparla in PDF, ci\xf2 significa che perder\xe0 tutte le capacit\xe0 di selezione/analisi.","Se questo \xe8 importante per te, prova a stampare il curriculum utilizzando Cmd/Ctrl + P o il pulsante di stampa qui sotto. Il risultato pu\xf2 variare in quanto l\'output \xe8 dipendente dal browser ma \xe8 noto che funziona meglio sull\'ultima versione di Google Chrome."],"buttons":{"cancel":"Annulla","saveAsPdf":"Salva come PDF"}},"panZoomAnimation":{"helpText":"Puoi ruotare e ingrandire l\'immagine in qualsiasi momento per dare un\'occhiata pi\xf9 da vicino al tuo curriculum."},"markdownHelpText":"Puoi utilizzare <1>GitHub Flavored Markdown</1> per personalizzare questa sezione del testo."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL della foto"},"firstName":{"label":"Nome"},"lastName":{"label":"Cognome"},"subtitle":{"label":"Sottotitolo"},"address":{"label":"Indirizzo","line1":{"label":"Indirizzo, 1a riga"},"line2":{"label":"Indirizzo, 2a riga"},"line3":{"label":"Indirizzo, 3a riga"}},"phone":{"label":"Numero di telefono"},"website":{"label":"Sito web"},"email":{"label":"Indirizzo email"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Obbiettivo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"role":{"label":"Ruolo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"major":{"label":"Grande"},"grade":{"label":"Voto"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titolo"},"subtitle":{"label":"Sottotitolo"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nome"},"subtitle":{"label":"Autorit\xe0"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nome"},"level":{"label":"Livello"},"rating":{"label":"Valutazione"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"position":{"label":"Posizione"},"phone":{"label":"Numero di telefono"},"email":{"label":"Indirizzo email"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Chiave"},"value":{"label":"Valore"}}')},function(e){e.exports=JSON.parse('{"title":"Modelli"}')},function(e){e.exports=JSON.parse('{"title":"Colori","colorOptions":"Opzioni di colore","primaryColor":"Colore primario","accentColor":"Colore secondario","clipboardCopyAction":"Il {{color}} \xe8 stato copiato negli appunti."}')},function(e){e.exports=JSON.parse('{"title":"Tipo di carattere","fontFamily":{"label":"Famiglia di caratteri","helpText":"Puoi usare anche tutti i caratteri installati sul tuo sistema. Basta inserire il nome della famiglia di caratteri qui e il browser lo caricher\xe0 per te."}}')},function(e){e.exports=JSON.parse('{"title":"Azioni","disclaimer":"Le modifiche apportate al curriculum vengono salvate automaticamente sulla memoria locale del tuo browser. Nessun dato viene visualizzato, perci\xf2 le tue informazioni sono completamente sicure.","importExport":{"heading":"Importa/Esporta","body":"Puoi importare o esportare i tuoi dati in formato JSON. Con questo, puoi modificare e stampare il tuo curriculum da qualsiasi dispositivo. Salva questo file per utilizzarlo in seguito.","buttons":{"import":"Importa","export":"Esporta"}},"downloadResume":{"heading":"Scarica il tuo curriculum","body":"Puoi cliccare sul pulsante qui sotto per scaricare una versione PDF del tuo curriculum. Per risultati ottimali, si prega di utilizzare l\'ultima versione di Google Chrome.","buttons":{"saveAsPdf":"Salva come PDF"}},"loadDemoData":{"heading":"Carica dati demo","body":"Non \xe8 chiaro su cosa fare con una nuova pagina vuota? Carica alcuni dati demo con valori preimpostati per vedere come dovrebbe apparire un curriculum e puoi iniziare a modificarlo da l\xec.","buttons":{"loadData":"Carica dati"}},"reset":{"heading":"Resetta tutto!","body":"Questa azione resetter\xe0 tutti i tuoi dati e rimuover\xe0 anche i backup effettuati sullo spazio locale del tuo browser quindi assicurati di aver esportato le tue informazioni prima di resettare tutto.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Impostazioni","language":{"label":"Lingua","helpText":"Se vuoi aiutare a tradurre l\'app nella tua lingua, fai riferimento alla <1>Documentazione di traduzione</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Informazioni","documentation":{"heading":"Documentazione","body":"Vuoi saperne di pi\xf9 sull\'app? Hai bisogno di informazioni su come contribuire al progetto? Non guardare oltre, c\'\xe8 una guida completa fatta solo per te.","buttons":{"documentation":"Documentazione"}},"bugOrFeatureRequest":{"heading":"Bug? Richiesta di nuove funzionalit\xe0?","body":"Qualcosa ti blocca mentre crei il tuo curriculum? Hai trovato un bug fastidioso che non scompare? Parlane nella sezione GitHub Issues o mandami un\'email utilizzando le azioni qui sotto.","buttons":{"raiseIssue":"Crea una segnalazione","sendEmail":"Manda un\'email"}},"sourceCode":{"heading":"Codice sorgente","body":"Vuoi eseguire il progetto dal suo codice sorgente? Sei uno sviluppatore disposto a contribuire allo sviluppo open-source di questo progetto? Clicca il pulsante qui sotto.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"Informazioni sulla licenza","body":"Il progetto \xe8 sottoposto alla licenza MIT, che puoi leggere di pi\xf9 su di sotto. Fondamentalmente, \xe8 consentito utilizzare il progetto ovunque purch\xe9 dia crediti all\'autore originale.","buttons":{"mitLicense":"Licenza MIT"}},"footer":{"credit":"Realizzato con amore da <1>Amruth Pillai</1>","thanks":"Grazie per aver utilizzato Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"{{- heading}} \u0cb8\u0cc7\u0cb0\u0cbf\u0cb8\u0cbf","startDate":{"label":"\u0caa\u0ccd\u0cb0\u0cbe\u0cb0\u0c82\u0cad \u0ca6\u0cbf\u0ca8\u0cbe\u0c82\u0c95"},"endDate":{"label":"\u0c85\u0c82\u0ca4\u0cbf\u0cae \u0ca6\u0cbf\u0ca8\u0cbe\u0c82\u0c95"},"description":{"label":"\u0cb5\u0cbf\u0cb5\u0cb0\u0ca3\u0cc6"}},"buttons":{"add":{"label":"\u0cb8\u0cc7\u0cb0\u0cbf\u0cb8\u0cbf"},"delete":{"label":"\u0c85\u0cb3\u0cbf\u0cb8\u0cbf"}},"printDialog":{"heading":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb0\u0cc7\u0cb8\u0cc1\u0cae\u0cc7 \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf","quality":{"label":"\u0c97\u0cc1\u0ca3\u0cae\u0c9f\u0ccd\u0c9f\u0ca6 \u0cae\u0ccc\u0cb2\u0ccd\u0caf"},"printType":{"label":"\u0cb5\u0cbf\u0ca7","types":{"unconstrained":"\u0ca8\u0cbf\u0cb0\u0ccd\u0cac\u0c82\u0ca7\u0cbf\u0cb8\u0ca6","fitInA4":"\u0c8e4 \u0ca8\u0cb2\u0ccd\u0cb2\u0cbf \u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6","multiPageA4":"\u0cac\u0cb9\u0cc1 \u0caa\u0cc1\u0c9f \u0c8e4"}},"helpText":["\u0c88 \u0cb5\u0cbf\u0ca7\u0cbe\u0ca8\u0cb5\u0cc1 \u0c8e\u0c9a\u0ccd\u200c\u0c9f\u0cbf\u0c8e\u0cae\u0ccd\u0c8e\u0cb2\u0ccd \u0c95\u0ccd\u0caf\u0cbe\u0ca8\u0ccd\u0cb5\u0cbe\u0cb8\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb0\u0cc7\u0cb8\u0ccd\u0caf\u0cc1\u0caf\u0cc1\u0cae\u0cc7\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0c9a\u0cbf\u0ca4\u0ccd\u0cb0\u0cb5\u0ca8\u0ccd\u0ca8\u0cbe\u0c97\u0cbf \u0caa\u0cb0\u0cbf\u0cb5\u0cb0\u0ccd\u0ca4\u0cbf\u0cb8\u0cb2\u0cc1 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0c85\u0ca6\u0ca8\u0ccd\u0ca8\u0cc1 \u0caa\u0cbf\u0ca1\u0cbf\u0c8e\u0cab\u0ccd\u200c\u0ca8\u0cb2\u0ccd\u0cb2\u0cbf \u0cae\u0cc1\u0ca6\u0ccd\u0cb0\u0cbf\u0cb8\u0cb2\u0cc1 \u0cac\u0cb3\u0cb8\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6, \u0c85\u0c82\u0ca6\u0cb0\u0cc6 \u0c87\u0ca6\u0cc1 \u0c8e\u0cb2\u0ccd\u0cb2\u0cbe \u0c86\u0caf\u0ccd\u0c95\u0cc6 / \u0caa\u0cbe\u0cb0\u0ccd\u0cb8\u0cbf\u0c82\u0c97\u0ccd \u0cb8\u0cbe\u0cae\u0cb0\u0ccd\u0ca5\u0ccd\u0caf\u0c97\u0cb3\u0ca8\u0ccd\u0ca8\u0cc1 \u0c95\u0cb3\u0cc6\u0ca6\u0cc1\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6.","\u0c85\u0ca6\u0cc1 \u0ca8\u0cbf\u0cae\u0c97\u0cc6 \u0cae\u0cc1\u0c96\u0ccd\u0caf\u0cb5\u0cbe\u0c97\u0cbf\u0ca6\u0ccd\u0ca6\u0cb0\u0cc6, \u0ca6\u0caf\u0cb5\u0cbf\u0c9f\u0ccd\u0c9f\u0cc1 Cmd / Ctrl + P \u0c85\u0ca5\u0cb5\u0cbe \u0c95\u0cc6\u0cb3\u0c97\u0cbf\u0ca8 \u0cae\u0cc1\u0ca6\u0ccd\u0cb0\u0ca3 \u0c97\u0cc1\u0c82\u0ca1\u0cbf\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0cac\u0cb3\u0cb8\u0cbf \u0caa\u0cc1\u0ca8\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cae\u0cc1\u0ca6\u0ccd\u0cb0\u0cbf\u0cb8\u0cb2\u0cc1 \u0caa\u0ccd\u0cb0\u0caf\u0ca4\u0ccd\u0ca8\u0cbf\u0cb8\u0cbf. Output \u0c9f\u0ccd\u200c\u0caa\u0cc1\u0c9f\u0ccd \u0cac\u0ccd\u0cb0\u0ccc\u0cb8\u0cb0\u0ccd \u0c85\u0cb5\u0cb2\u0c82\u0cac\u0cbf\u0ca4\u0cb5\u0cbe\u0c97\u0cbf\u0cb0\u0cc1\u0cb5\u0cc1\u0ca6\u0cb0\u0cbf\u0c82\u0ca6 \u0cab\u0cb2\u0cbf\u0ca4\u0cbe\u0c82\u0cb6\u0cb5\u0cc1 \u0cac\u0ca6\u0cb2\u0cbe\u0c97\u0cac\u0cb9\u0cc1\u0ca6\u0cc1, \u0c86\u0ca6\u0cb0\u0cc6 \u0c87\u0ca6\u0cc1 Google Chrome \u0ca8 \u0c87\u0ca4\u0ccd\u0ca4\u0cc0\u0c9a\u0cbf\u0ca8 \u0c86\u0cb5\u0cc3\u0ca4\u0ccd\u0ca4\u0cbf\u0caf\u0cb2\u0ccd\u0cb2\u0cbf \u0c89\u0ca4\u0ccd\u0ca4\u0cae\u0cb5\u0cbe\u0c97\u0cbf \u0c95\u0cbe\u0cb0\u0ccd\u0caf\u0ca8\u0cbf\u0cb0\u0ccd\u0cb5\u0cb9\u0cbf\u0cb8\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6."],"buttons":{"cancel":"\u0ca4\u0ccd\u0caf\u0c9c\u0cbf\u0cb8\u0cbf","saveAsPdf":"\u0caa\u0cbf\u0ca1\u0cbf\u0c8e\u0cab\u0ccd \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf"}},"panZoomAnimation":{"helpText":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb0\u0cc7\u0cb8\u0ccd\u0caf\u0cc1\u0cae\u0cc7\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb9\u0ca4\u0ccd\u0ca4\u0cbf\u0cb0\u0ca6\u0cbf\u0c82\u0ca6 \u0ca8\u0ccb\u0ca1\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0caf\u0cbe\u0cb5\u0cc1\u0ca6\u0cc7 \u0cb8\u0cae\u0caf\u0ca6\u0cb2\u0ccd\u0cb2\u0cbf \u0c86\u0cb0\u0ccd\u0c9f\u0ccd\u200c\u0cac\u0ccb\u0cb0\u0ccd\u0ca1\u0ccd\u200c\u0ca8 \u0cb8\u0cc1\u0ca4\u0ccd\u0ca4\u0cb2\u0cc2 \u0caa\u0ccd\u0caf\u0cbe\u0ca8\u0ccd \u0cae\u0cbe\u0ca1\u0cac\u0cb9\u0cc1\u0ca6\u0cc1 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0c9c\u0cc2\u0cae\u0ccd \u0cae\u0cbe\u0ca1\u0cac\u0cb9\u0cc1\u0ca6\u0cc1."},"markdownHelpText":"\u0caa\u0ca0\u0ccd\u0caf\u0ca6 \u0c88 \u0cb5\u0cbf\u0cad\u0cbe\u0c97\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb5\u0cbf\u0ca8\u0ccd\u0caf\u0cbe\u0cb8\u0c97\u0cca\u0cb3\u0cbf\u0cb8\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 <1>\u0c97\u0cbf\u0c9f\u0ccd\u200c\u0cb9\u0cac\u0ccd \u0cab\u0ccd\u0cb2\u0cc7\u0cb5\u0cb0\u0ccd\u0ca1\u0ccd \u0cae\u0cbe\u0cb0\u0ccd\u0c95\u0ccd\u200c\u0ca1\u0ccc\u0ca8\u0ccd</1> \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0cac\u0cb3\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u0cab\u0ccb\u0c9f\u0ccb URL"},"firstName":{"label":"\u0cae\u0cc6\u0cc2\u0ca6\u0cb2 \u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"lastName":{"label":"\u0c95\u0cc6\u0cc2\u0ca8\u0cc6\u0caf \u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"subtitle":{"label":"\u0c89\u0caa\u0cb6\u0cc0\u0cb0\u0ccd\u0cb7\u0cbf\u0c95\u0cc6"},"address":{"label":"\u0cb5\u0cbf\u0cb3\u0cbe\u0cb8","line1":{"label":"\u0cb5\u0cbf\u0cb3\u0cbe\u0cb8 \u0cb8\u0cbe\u0cb2\u0cc1 1"},"line2":{"label":"\u0cb5\u0cbf\u0cb3\u0cbe\u0cb8 \u0cb8\u0cbe\u0cb2\u0cc1 2"},"line3":{"label":"\u0cb5\u0cbf\u0cb3\u0cbe\u0cb8 \u0cb8\u0cbe\u0cb2\u0cc1 3"}},"phone":{"label":"\u0ca6\u0cc2\u0cb0\u0cb5\u0cbe\u0ca3\u0cbf \u0cb8\u0c82\u0c96\u0ccd\u0caf\u0cc6"},"website":{"label":"\u0c9c\u0cbe\u0cb2\u0ca4\u0cbe\u0ca3"},"email":{"label":"\u0c87\u0cae\u0cc6\u0cd5\u0cb2\u0ccd \u0cb5\u0cbf\u0cb3\u0cbe\u0cb8"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0c89\u0ca6\u0ccd\u0ca6\u0cc7\u0cb6"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"role":{"label":"\u0cb8\u0ccd\u0ca5\u0cbe\u0ca8"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"major":{"label":"\u0c85\u0ca7\u0ccd\u0caf\u0caf\u0ca8"},"grade":{"label":"\u0c97\u0ccd\u0cb0\u0cc7\u0ca1\u0ccd"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0cb6\u0cc0\u0cb0\u0ccd\u0cb7\u0cbf\u0c95\u0cc6"},"subtitle":{"label":"\u0c89\u0caa\u0cb6\u0cc0\u0cb0\u0ccd\u0cb7\u0cbf\u0c95\u0cc6"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"subtitle":{"label":"\u0caa\u0ccd\u0cb0\u0cbe\u0ca7\u0cbf\u0c95\u0cbe\u0cb0"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"level":{"label":"\u0cae\u0c9f\u0ccd\u0c9f"},"rating":{"label":"\u0cb0\u0cc7\u0c9f\u0cbf\u0c82\u0c97\u0ccd"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0cb9\u0cc6\u0cb8\u0cb0\u0cc1"},"position":{"label":"\u0cb8\u0ccd\u0ca5\u0cbe\u0ca8"},"phone":{"label":"\u0ca6\u0cc2\u0cb0\u0cb5\u0cbe\u0ca3\u0cbf \u0cb8\u0c82\u0c96\u0ccd\u0caf\u0cc6"},"email":{"label":"\u0c87\u0cae\u0cc6\u0cd5\u0cb2\u0ccd \u0cb5\u0cbf\u0cb3\u0cbe\u0cb8"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0c95\u0cc0"},"value":{"label":"\u0cae\u0ccc\u0cb2\u0ccd\u0caf"}}')},function(e){e.exports=JSON.parse('{"title":"\u0c9f\u0cc6\u0c82\u0caa\u0ccd\u0cb2\u0cc7\u0c9f\u0ccd\u200c\u0c97\u0cb3\u0cc1"}')},function(e){e.exports=JSON.parse('{"title":"\u0cac\u0ca3\u0ccd\u0ca3\u0c97\u0cb3\u0cc1","colorOptions":"\u0cac\u0ca3\u0ccd\u0ca3 \u0c86\u0caf\u0ccd\u0c95\u0cc6\u0c97\u0cb3\u0cc1","primaryColor":"\u0caa\u0ccd\u0cb0\u0cbe\u0ca5\u0cae\u0cbf\u0c95 \u0cac\u0ca3\u0ccd\u0ca3","accentColor":"\u0ca6\u0ccd\u0cb5\u0cbf\u0ca4\u0cc0\u0caf\u0c95 \u0cac\u0ca3\u0ccd\u0ca3","clipboardCopyAction":"{{color}} \u0cac\u0ca3\u0ccd\u0ca3\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0c95\u0ccd\u0cb2\u0cbf\u0caa\u0ccd\u200c\u0cac\u0ccb\u0cb0\u0ccd\u0ca1\u0ccd\u200c\u0c97\u0cc6 \u0ca8\u0c95\u0cb2\u0cbf\u0cb8\u0cb2\u0cbe\u0c97\u0cbf\u0ca6\u0cc6."}')},function(e){e.exports=JSON.parse('{"title":"\u0cab\u0cbe\u0c82\u0c9f\u0ccd\u200c\u0c97\u0cb3\u0cc1","fontFamily":{"label":"\u0cab\u0cbe\u0c82\u0c9f\u0ccd \u0c95\u0cc1\u0c9f\u0cc1\u0c82\u0cac","helpText":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb8\u0cbf\u0cb8\u0ccd\u0c9f\u0c82\u0ca8\u0cb2\u0ccd\u0cb2\u0cbf \u0cb8\u0ccd\u0ca5\u0cbe\u0caa\u0cbf\u0cb8\u0cb2\u0cbe\u0ca6 \u0caf\u0cbe\u0cb5\u0cc1\u0ca6\u0cc7 \u0cab\u0cbe\u0c82\u0c9f\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0cac\u0cb3\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0c87\u0cb2\u0ccd\u0cb2\u0cbf \u0cab\u0cbe\u0c82\u0c9f\u0ccd\u200c\u0ca8 \u0cb9\u0cc6\u0cb8\u0cb0\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cae\u0cc2\u0ca6\u0cbf\u0cb8\u0cbf \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0cac\u0ccd\u0cb0\u0ccc\u0cb8\u0cb0\u0ccd \u0c85\u0ca6\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cbf\u0cae\u0c97\u0cbe\u0c97\u0cbf \u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6."}}')},function(e){e.exports=JSON.parse('{"title":"\u0c95\u0ccd\u0cb0\u0cbf\u0caf\u0cc6\u0c97\u0cb3\u0cc1","disclaimer":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0caa\u0cc1\u0ca8\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0ca6\u0cb2\u0ccd\u0cb2\u0cbf \u0ca8\u0cc0\u0cb5\u0cc1 \u0cae\u0cbe\u0ca1\u0cbf\u0ca6 \u0cac\u0ca6\u0cb2\u0cbe\u0cb5\u0ca3\u0cc6\u0c97\u0cb3\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cac\u0ccd\u0cb0\u0ccc\u0cb8\u0cb0\u0ccd\u200c\u0ca8 \u0cb8\u0ccd\u0ca5\u0cb3\u0cc0\u0caf \u0cb8\u0c82\u0c97\u0ccd\u0cb0\u0cb9\u0ca3\u0cc6\u0c97\u0cc6 \u0cb8\u0ccd\u0cb5\u0caf\u0c82\u0c9a\u0cbe\u0cb2\u0cbf\u0ca4\u0cb5\u0cbe\u0c97\u0cbf \u0c89\u0cb3\u0cbf\u0cb8\u0cb2\u0cbe\u0c97\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6. \u0caf\u0cbe\u0cb5\u0cc1\u0ca6\u0cc7 \u0ca1\u0cc7\u0c9f\u0cbe \u0cb9\u0cca\u0cb0\u0cac\u0cb0\u0cc1\u0cb5\u0cc1\u0ca6\u0cbf\u0cb2\u0ccd\u0cb2, \u0c86\u0ca6\u0ccd\u0ca6\u0cb0\u0cbf\u0c82\u0ca6 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cae\u0cbe\u0cb9\u0cbf\u0ca4\u0cbf\u0caf\u0cc1 \u0cb8\u0c82\u0caa\u0cc2\u0cb0\u0ccd\u0ca3\u0cb5\u0cbe\u0c97\u0cbf \u0cb8\u0cc1\u0cb0\u0c95\u0ccd\u0cb7\u0cbf\u0ca4\u0cb5\u0cbe\u0c97\u0cbf\u0ca6\u0cc6.","importExport":{"heading":"\u0c86\u0cae\u0ca6\u0cc1 / \u0cb0\u0cab\u0ccd\u0ca4\u0cc1","body":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0ca1\u0cc7\u0c9f\u0cbe\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 JSON \u0cb8\u0ccd\u0cb5\u0cb0\u0cc2\u0caa\u0ca6\u0cb2\u0ccd\u0cb2\u0cbf \u0c86\u0cae\u0ca6\u0cc1 \u0cae\u0cbe\u0ca1\u0cbf\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cac\u0cb9\u0cc1\u0ca6\u0cc1 \u0c85\u0ca5\u0cb5\u0cbe \u0cb0\u0cab\u0ccd\u0ca4\u0cc1 \u0cae\u0cbe\u0ca1\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0c87\u0ca6\u0cb0\u0cca\u0c82\u0ca6\u0cbf\u0c97\u0cc6, \u0ca8\u0cc0\u0cb5\u0cc1 \u0caf\u0cbe\u0cb5\u0cc1\u0ca6\u0cc7 \u0cb8\u0cbe\u0ca7\u0ca8\u0ca6\u0cbf\u0c82\u0ca6 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0caa\u0cc1\u0ca8\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb8\u0c82\u0caa\u0cbe\u0ca6\u0cbf\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0cae\u0cc1\u0ca6\u0ccd\u0cb0\u0cbf\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0ca8\u0c82\u0ca4\u0cb0\u0ca6 \u0cac\u0cb3\u0c95\u0cc6\u0c97\u0cbe\u0c97\u0cbf \u0c88 \u0cab\u0cc8\u0cb2\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0c89\u0cb3\u0cbf\u0cb8\u0cbf.","buttons":{"import":"\u0c86\u0cae\u0ca6\u0cc1","export":"\u0cb0\u0cab\u0ccd\u0ca4\u0cc1"}},"downloadResume":{"heading":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb0\u0cc7\u0cb8\u0cc1\u0cae\u0cc7 \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf","body":"\u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb0\u0cc7\u0cb8\u0cc1\u0cae\u0cc7\u0caf \u0caa\u0cbf\u0ca1\u0cbf\u0c8e\u0cab\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca4\u0c95\u0ccd\u0cb7\u0ca3 \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0c95\u0cc6\u0cb3\u0c97\u0cbf\u0ca8 \u0cac\u0c9f\u0ca8\u0ccd \u0c95\u0ccd\u0cb2\u0cbf\u0c95\u0ccd \u0cae\u0cbe\u0ca1\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0c89\u0ca4\u0ccd\u0ca4\u0cae \u0cab\u0cb2\u0cbf\u0ca4\u0cbe\u0c82\u0cb6\u0c97\u0cb3\u0cbf\u0c97\u0cbe\u0c97\u0cbf, \u0ca6\u0caf\u0cb5\u0cbf\u0c9f\u0ccd\u0c9f\u0cc1 \u0c87\u0ca4\u0ccd\u0ca4\u0cc0\u0c9a\u0cbf\u0ca8 Google Chrome \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0cac\u0cb3\u0cb8\u0cbf.","buttons":{"saveAsPdf":"\u0caa\u0cbf\u0ca1\u0cbf\u0c8e\u0cab\u0ccd \u0ca1\u0ccc\u0ca8\u0ccd\u200c\u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf"}},"loadDemoData":{"heading":"\u0ca1\u0cc6\u0cae\u0cca \u0ca1\u0cc7\u0c9f\u0cbe\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf","body":"\u0cb9\u0cca\u0cb8 \u0c96\u0cbe\u0cb2\u0cbf \u0caa\u0cc1\u0c9f\u0ca6\u0cca\u0c82\u0ca6\u0cbf\u0c97\u0cc6 \u0c8f\u0ca8\u0cc1 \u0cae\u0cbe\u0ca1\u0cac\u0cc7\u0c95\u0cc6\u0c82\u0ca6\u0cc1 \u0cb8\u0ccd\u0caa\u0cb7\u0ccd\u0c9f\u0cb5\u0cbe\u0c97\u0cbf\u0cb2\u0ccd\u0cb2\u0cb5\u0cc7? \u0cb0\u0cc7\u0cb8\u0ccd\u200c\u0cb8\u0cc1\u0cae\u0cc7 \u0cb9\u0cc7\u0c97\u0cc6 \u0c95\u0cbe\u0ca3\u0cac\u0cc7\u0c95\u0cc1 \u0c8e\u0c82\u0cac\u0cc1\u0ca6\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0ccb\u0ca1\u0cb2\u0cc1 \u0caa\u0cc2\u0cb0\u0ccd\u0cb5\u0cad\u0cbe\u0cb5\u0cbf \u0cae\u0ccc\u0cb2\u0ccd\u0caf\u0c97\u0cb3\u0cca\u0c82\u0ca6\u0cbf\u0c97\u0cc6 \u0c95\u0cc6\u0cb2\u0cb5\u0cc1 \u0ca1\u0cc6\u0cae\u0cca \u0ca1\u0cc7\u0c9f\u0cbe\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb2\u0ccb\u0ca1\u0ccd \u0cae\u0cbe\u0ca1\u0cbf \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0c85\u0cb2\u0ccd\u0cb2\u0cbf\u0c82\u0ca6 \u0cb8\u0c82\u0caa\u0cbe\u0ca6\u0ca8\u0cc6\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0caa\u0ccd\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0cbf\u0cb8\u0cac\u0cb9\u0cc1\u0ca6\u0cc1.","buttons":{"loadData":"\u0cb2\u0ccb\u0ca1\u0ccd \u0ca1\u0cc7\u0c9f\u0cbe"}},"reset":{"heading":"\u0c8e\u0cb2\u0ccd\u0cb2\u0cb5\u0ca8\u0ccd\u0ca8\u0cc2 \u0cae\u0cb0\u0cc1\u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0cb8\u0cbf!","body":"\u0c88 \u0c95\u0ccd\u0cb0\u0cbf\u0caf\u0cc6\u0caf\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0c8e\u0cb2\u0ccd\u0cb2\u0cbe \u0ca1\u0cc7\u0c9f\u0cbe\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cae\u0cb0\u0cc1\u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0cb8\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cac\u0ccd\u0cb0\u0ccc\u0cb8\u0cb0\u0ccd\u200c\u0ca8 \u0cb8\u0ccd\u0ca5\u0cb3\u0cc0\u0caf \u0cb8\u0c82\u0c97\u0ccd\u0cb0\u0cb9\u0ca3\u0cc6\u0c97\u0cc6 \u0cae\u0cbe\u0ca1\u0cbf\u0ca6 \u0cac\u0ccd\u0caf\u0cbe\u0c95\u0caa\u0ccd\u200c\u0c97\u0cb3\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca4\u0cc6\u0c97\u0cc6\u0ca6\u0cc1\u0cb9\u0cbe\u0c95\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6, \u0c86\u0ca6\u0ccd\u0ca6\u0cb0\u0cbf\u0c82\u0ca6 \u0ca8\u0cc0\u0cb5\u0cc1 \u0c8e\u0cb2\u0ccd\u0cb2\u0cb5\u0ca8\u0ccd\u0ca8\u0cc2 \u0cae\u0cb0\u0cc1\u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0cb8\u0cc1\u0cb5 \u0cae\u0cca\u0ca6\u0cb2\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cae\u0cbe\u0cb9\u0cbf\u0ca4\u0cbf\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0cb0\u0cab\u0ccd\u0ca4\u0cc1 \u0cae\u0cbe\u0ca1\u0cbf\u0ca6\u0ccd\u0ca6\u0cc0\u0cb0\u0cbf \u0c8e\u0c82\u0ca6\u0cc1 \u0c96\u0c9a\u0cbf\u0ca4\u0caa\u0ca1\u0cbf\u0cb8\u0cbf\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cbf.","buttons":{"reset":"\u0cae\u0cb0\u0cc1\u0cb9\u0cca\u0c82\u0ca6\u0cbf\u0cb8\u0cbf"}}}')},function(e){e.exports=JSON.parse('{"title":"\u0cb8\u0cc6\u0c9f\u0ccd\u0c9f\u0cbf\u0c82\u0c97\u0ccd\u0cb8\u0ccd","language":{"label":"\u0cad\u0cbe\u0cb7\u0cc6","helpText":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0cb8\u0ccd\u0cb5\u0c82\u0ca4 \u0cad\u0cbe\u0cb7\u0cc6\u0c97\u0cc6 \u0cad\u0cbe\u0cb7\u0cbe\u0c82\u0ca4\u0cb0\u0cbf\u0cb8\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0cb8\u0cb9\u0cbe\u0caf \u0cae\u0cbe\u0ca1\u0cb2\u0cc1 \u0cac\u0caf\u0cb8\u0cbf\u0ca6\u0cb0\u0cc6, \u0ca6\u0caf\u0cb5\u0cbf\u0c9f\u0ccd\u0c9f\u0cc1 <1>\u0c85\u0ca8\u0cc1\u0cb5\u0cbe\u0ca6 \u0ca6\u0cbe\u0c96\u0cb2\u0cc6</1> \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0ccb\u0ca1\u0cbf."}}')},function(e){e.exports=JSON.parse('{"title":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0cac\u0c97\u0ccd\u0c97\u0cc6","documentation":{"heading":"\u0ca6\u0cb8\u0ccd\u0ca4\u0cbe\u0cb5\u0cc7\u0c9c\u0ca8\u0ccd\u0ca8\u0cc1","body":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0cac\u0c97\u0ccd\u0c97\u0cc6 \u0c87\u0ca8\u0ccd\u0ca8\u0cb7\u0ccd\u0c9f\u0cc1 \u0ca4\u0cbf\u0cb3\u0cbf\u0ca6\u0cc1\u0c95\u0cca\u0cb3\u0ccd\u0cb3\u0cb2\u0cc1 \u0cac\u0caf\u0cb8\u0cc1\u0cb5\u0cbf\u0cb0\u0cbe? \u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd\u200c\u0c97\u0cc6 \u0cb9\u0cc7\u0c97\u0cc6 \u0c95\u0cca\u0ca1\u0cc1\u0c97\u0cc6 \u0ca8\u0cc0\u0ca1\u0cac\u0cc7\u0c95\u0cc1 \u0c8e\u0c82\u0cac\u0cc1\u0ca6\u0cb0 \u0c95\u0cc1\u0cb0\u0cbf\u0ca4\u0cc1 \u0cae\u0cbe\u0cb9\u0cbf\u0ca4\u0cbf \u0cac\u0cc7\u0c95\u0cc7? \u0cae\u0cc1\u0c82\u0ca6\u0cc6 \u0ca8\u0ccb\u0ca1\u0cac\u0cc7\u0ca1\u0cbf, \u0ca8\u0cbf\u0cae\u0c97\u0cbe\u0c97\u0cbf \u0cae\u0cbe\u0ca1\u0cbf\u0ca6 \u0cb8\u0cae\u0c97\u0ccd\u0cb0 \u0cae\u0cbe\u0cb0\u0ccd\u0c97\u0ca6\u0cb0\u0ccd\u0cb6\u0cbf \u0c87\u0ca6\u0cc6.","buttons":{"documentation":"\u0ca6\u0cb8\u0ccd\u0ca4\u0cbe\u0cb5\u0cc7\u0c9c\u0ca8\u0ccd\u0ca8\u0cc1"}},"bugOrFeatureRequest":{"heading":"\u0cb8\u0cae\u0cb8\u0ccd\u0caf\u0cc6? \u0cb9\u0cca\u0cb8 \u0c86\u0cb2\u0ccb\u0c9a\u0ca8\u0cc6?","body":"\u0caa\u0cc1\u0ca8\u0cb0\u0cbe\u0cb0\u0c82\u0cad\u0cb5\u0ca8\u0ccd\u0ca8\u0cc1 \u0cae\u0cbe\u0ca1\u0cc1\u0cb5\u0cc1\u0ca6\u0cb0\u0cbf\u0c82\u0ca6 \u0ca8\u0cbf\u0cae\u0ccd\u0cae \u0caa\u0ccd\u0cb0\u0c97\u0ca4\u0cbf\u0caf\u0ca8\u0ccd\u0ca8\u0cc1 \u0c8f\u0ca8\u0cbe\u0ca6\u0cb0\u0cc2 \u0ca4\u0ca1\u0cc6\u0caf\u0cc1\u0ca4\u0ccd\u0ca4\u0cc0\u0cb0\u0cbe? \u0ca4\u0cca\u0cb0\u0cc6\u0caf\u0ca6\u0c82\u0ca4\u0cb9 \u0ca4\u0cca\u0c82\u0ca6\u0cb0\u0cc6 \u0ca6\u0ccb\u0cb7 \u0c95\u0c82\u0ca1\u0cc1\u0cac\u0c82\u0ca6\u0cbf\u0ca6\u0cc6? GitHub \u0cb8\u0cae\u0cb8\u0ccd\u0caf\u0cc6\u0c97\u0cb3 \u0cb5\u0cbf\u0cad\u0cbe\u0c97\u0ca6\u0cb2\u0ccd\u0cb2\u0cbf \u0c87\u0ca6\u0cb0 \u0cac\u0c97\u0ccd\u0c97\u0cc6 \u0cae\u0cbe\u0ca4\u0ca8\u0cbe\u0ca1\u0cbf, \u0c85\u0ca5\u0cb5\u0cbe \u0c95\u0cc6\u0cb3\u0c97\u0cbf\u0ca8 \u0c95\u0ccd\u0cb0\u0cbf\u0caf\u0cc6\u0c97\u0cb3\u0ca8\u0ccd\u0ca8\u0cc1 \u0cac\u0cb3\u0cb8\u0cbf\u0c95\u0cca\u0c82\u0ca1\u0cc1 \u0ca8\u0ca8\u0c97\u0cc6 \u0cae\u0ca4\u0ccd\u0ca4\u0cc1 \u0c87\u0cae\u0cc7\u0cb2\u0ccd \u0c95\u0cb3\u0cc1\u0cb9\u0cbf\u0cb8\u0cbf.","buttons":{"raiseIssue":"\u0cb8\u0cae\u0cb8\u0ccd\u0caf\u0cc6\u0caf \u0cac\u0c97\u0ccd\u0c97\u0cc6 \u0ca4\u0cbf\u0cb3\u0cbf\u0cb8\u0cbf","sendEmail":"\u0c87\u0cae\u0cc7\u0cb2\u0ccd \u0c95\u0cb3\u0cc1\u0cb9\u0cbf\u0cb8\u0cbf"}},"sourceCode":{"heading":"\u0cb8\u0ccb\u0cb0\u0ccd\u0cb8\u0ccd \u0c95\u0ccb\u0ca1\u0ccd","body":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0c85\u0ca6\u0cb0 \u0cae\u0cc2\u0cb2\u0ca6\u0cbf\u0c82\u0ca6 \u0c9a\u0cb2\u0cbe\u0caf\u0cbf\u0cb8\u0cb2\u0cc1 \u0cac\u0caf\u0cb8\u0cc1\u0cb5\u0cbf\u0cb0\u0cbe? \u0c88 \u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd\u200c\u0ca8 \u0cae\u0cc1\u0c95\u0ccd\u0ca4-\u0cae\u0cc2\u0cb2 \u0c85\u0cad\u0cbf\u0cb5\u0cc3\u0ca6\u0ccd\u0ca7\u0cbf\u0c97\u0cc6 \u0c95\u0cca\u0ca1\u0cc1\u0c97\u0cc6 \u0ca8\u0cc0\u0ca1\u0cb2\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0cb8\u0cbf\u0ca6\u0ccd\u0ca7\u0cb0\u0cbf\u0ca6\u0ccd\u0ca6\u0cc0\u0cb0\u0cbe? \u0c95\u0cc6\u0cb3\u0c97\u0cbf\u0ca8 \u0cac\u0c9f\u0ca8\u0ccd \u0c95\u0ccd\u0cb2\u0cbf\u0c95\u0ccd \u0cae\u0cbe\u0ca1\u0cbf.","buttons":{"githubRepo":"\u0c97\u0cbf\u0c9f\u0ccd\u200c\u0cb9\u0cac\u0ccd \u0cb0\u0cbf\u0caa\u0ccb"}},"license":{"heading":"\u0caa\u0cb0\u0cb5\u0cbe\u0ca8\u0c97\u0cbf \u0cae\u0cbe\u0cb9\u0cbf\u0ca4\u0cbf","body":"\u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0c85\u0ca8\u0ccd\u0ca8\u0cc1 \u0c8e\u0c82\u0c90\u0c9f\u0cbf \u0caa\u0cb0\u0cb5\u0cbe\u0ca8\u0c97\u0cbf \u0c85\u0ca1\u0cbf\u0caf\u0cb2\u0ccd\u0cb2\u0cbf \u0ca8\u0cbf\u0caf\u0c82\u0ca4\u0ccd\u0cb0\u0cbf\u0cb8\u0cb2\u0cbe\u0c97\u0cc1\u0ca4\u0ccd\u0ca4\u0ca6\u0cc6, \u0c85\u0ca6\u0ca8\u0ccd\u0ca8\u0cc1 \u0ca8\u0cc0\u0cb5\u0cc1 \u0c95\u0cc6\u0cb3\u0c97\u0cc6 \u0c87\u0ca8\u0ccd\u0ca8\u0cb7\u0ccd\u0c9f\u0cc1 \u0c93\u0ca6\u0cac\u0cb9\u0cc1\u0ca6\u0cc1. \u0ca8\u0cc0\u0cb5\u0cc1 \u0cae\u0cc2\u0cb2 \u0cb2\u0cc7\u0c96\u0c95\u0cb0\u0cbf\u0c97\u0cc6 \u0c95\u0ccd\u0cb0\u0cc6\u0ca1\u0cbf\u0c9f\u0ccd \u0ca8\u0cc0\u0ca1\u0cbf\u0ca6\u0cb0\u0cc6 \u0c8e\u0cb2\u0ccd\u0cb2\u0cbf\u0caf\u0cbe\u0ca6\u0cb0\u0cc2 \u0c85\u0caa\u0ccd\u0cb2\u0cbf\u0c95\u0cc7\u0cb6\u0ca8\u0ccd \u0cac\u0cb3\u0cb8\u0cb2\u0cc1 \u0ca8\u0cbf\u0cae\u0c97\u0cc6 \u0c85\u0ca8\u0cc1\u0cae\u0ca4\u0cbf \u0c87\u0ca6\u0cc6.","buttons":{"mitLicense":"\u0c8e\u0c82\u0c90\u0c9f\u0cbf \u0caa\u0cb0\u0cb5\u0cbe\u0ca8\u0c97\u0cbf"}},"footer":{"credit":"<1>\u0c85\u0cae\u0cc3\u0ca4\u0ccd \u0caa\u0cbf\u0cb3\u0ccd\u0cb3\u0cc8</1> \u0c85\u0cb5\u0cb0\u0cbf\u0c82\u0ca6 \u0caa\u0ccd\u0cb0\u0cc0\u0ca4\u0cbf\u0caf\u0cbf\u0c82\u0ca6 \u0cae\u0cbe\u0ca1\u0cb2\u0ccd\u0caa\u0c9f\u0ccd\u0c9f\u0cbf\u0ca6\u0cc6","thanks":"\u0cb0\u0cbf\u0caf\u0cbe\u0c95\u0ccd\u0c9f\u0cbf\u0cb5\u0ccd \u0cb0\u0cc6\u0cb8\u0cc1\u0cae\u0cc7 \u0cac\u0cb3\u0cb8\u0cbf\u0ca6\u0ccd\u0ca6\u0c95\u0ccd\u0c95\u0cbe\u0c97\u0cbf \u0ca7\u0ca8\u0ccd\u0caf\u0cb5\u0cbe\u0ca6\u0c97\u0cb3\u0cc1!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"heading":{"placeholder":"Heading"},"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date","placeholder":"March 2018"},"endDate":{"label":"End Date","placeholder":"March 2022"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"}},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name","placeholder":"Jane"},"lastName":{"label":"Last Name","placeholder":"Doe"},"subtitle":{"label":"Subtitle","placeholder":"Full Stack Web Developer"},"address":{"line1":{"label":"Address Line 1","placeholder":"Palladium Complex"},"line2":{"label":"Address Line 2","placeholder":"140 E 14th St"},"line3":{"label":"Address Line 3","placeholder":"New York, NY 10003 USA"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective","placeholder":"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Amazon"},"role":{"label":"Role","placeholder":"Front-end Web Developer"},"description":{"placeholder":"You can write about what you specialized in while working at the company and what projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Harvard University"},"major":{"label":"Major","placeholder":"Masters in Computer Science"},"grade":{"label":"Grade"},"description":{"placeholder":"You can write about projects or special credit classes that you took while studying at this school."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title","placeholder":"Math & Science Olympiad"},"subtitle":{"label":"Subtitle","placeholder":"First Place, International Level"},"description":{"placeholder":"You can write about what qualities made you succeed in getting this award."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Dothraki"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Richard Hendricks"},"position":{"label":"Position","placeholder":"CEO, Pied Piper"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"},"description":{"placeholder":"You can write about how you and the reference contact worked together and which projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Date of Birth"},"value":{"label":"Value","placeholder":"6th August 1995"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Accent Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"printResume":{"heading":"Print Your Resume","body":"You can click on the button below to generate a PDF instantly. Alternatively, you can also use <1>Cmd/Ctrl + P</1> but it would have different effects.","buttons":{"export":"Export","print":"Print"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Wouldn\'t it be nice if there was a guide to setting it up on your local machine? Need information on how to contribute to the project? Look no further, there\'s comprehensive documentation made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"heading":{"placeholder":"Heading"},"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date","placeholder":"March 2018"},"endDate":{"label":"End Date","placeholder":"March 2022"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"}},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name","placeholder":"Jane"},"lastName":{"label":"Last Name","placeholder":"Doe"},"subtitle":{"label":"Subtitle","placeholder":"Full Stack Web Developer"},"address":{"line1":{"label":"Address Line 1","placeholder":"Palladium Complex"},"line2":{"label":"Address Line 2","placeholder":"140 E 14th St"},"line3":{"label":"Address Line 3","placeholder":"New York, NY 10003 USA"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective","placeholder":"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Amazon"},"role":{"label":"Role","placeholder":"Front-end Web Developer"},"description":{"placeholder":"You can write about what you specialized in while working at the company and what projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Harvard University"},"major":{"label":"Major","placeholder":"Masters in Computer Science"},"grade":{"label":"Grade"},"description":{"placeholder":"You can write about projects or special credit classes that you took while studying at this school."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title","placeholder":"Math & Science Olympiad"},"subtitle":{"label":"Subtitle","placeholder":"First Place, International Level"},"description":{"placeholder":"You can write about what qualities made you succeed in getting this award."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Dothraki"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Richard Hendricks"},"position":{"label":"Position","placeholder":"CEO, Pied Piper"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"},"description":{"placeholder":"You can write about how you and the reference contact worked together and which projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Date of Birth"},"value":{"label":"Value","placeholder":"6th August 1995"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Accent Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"printResume":{"heading":"Print Your Resume","body":"You can click on the button below to generate a PDF instantly. Alternatively, you can also use <1>Cmd/Ctrl + P</1> but it would have different effects.","buttons":{"export":"Export","print":"Print"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Wouldn\'t it be nice if there was a guide to setting it up on your local machine? Need information on how to contribute to the project? Look no further, there\'s comprehensive documentation made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Voeg {{- heading}} toe","startDate":{"label":"Startdatum"},"endDate":{"label":"Einddatum"},"description":{"label":"Beschrijving"}},"buttons":{"add":{"label":"Toevoegen"},"delete":{"label":"Verwijderen"}},"printDialog":{"heading":"Download je curriculum","quality":{"label":"Kwaliteit"},"printType":{"label":"Soort","types":{"unconstrained":"Geen limitaties","fitInA4":"Passend maken in A4","multiPageA4":"Multi-Pagina A4"}},"helpText":["Deze exportmethode maakt gebruik van HTML-canvas om de cv te converteren naar een afbeelding en deze af te drukken op een PDF, dit betekent dat het alle selectie/parsing mogelijkheden verliest.","Als dat belangrijk voor u is, probeer dan het Cmd/Ctrl + P of de print knop hieronder af te drukken. Het resultaat kan vari\xebren omdat de output afhankelijk is van de browser, maar het is bekend dat het het beste werkt op de nieuwste versie van Google Chrome."],"buttons":{"cancel":"Annuleren","saveAsPdf":"Opslaan als PDF"}},"panZoomAnimation":{"helpText":"Je kunt op elk moment op het artboard inzoomen om een beter zicht te krijgen op je curriculum."},"markdownHelpText":"U kunt <1>GitHub Flavored Markdown</1> gebruiken om dit gedeelte van de tekst op te maken."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Foto URL"},"firstName":{"label":"Voornaam"},"lastName":{"label":"Achternaam"},"subtitle":{"label":"Ondertitel"},"address":{"label":"Adres","line1":{"label":"Adresregel 1"},"line2":{"label":"Adresregel 2"},"line3":{"label":"Adresregel 3"}},"phone":{"label":"Telefoonnummer"},"website":{"label":"Website"},"email":{"label":"E-mailadres"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Doelstelling"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Naam"},"role":{"label":"Rol"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Naam"},"major":{"label":"Groot"},"grade":{"label":"Beoordeling"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Titel"},"subtitle":{"label":"Ondertitel"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Naam"},"subtitle":{"label":"Autoriteit"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Naam"},"level":{"label":"Taalvaardigheid"},"rating":{"label":"Waardering"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Naam"},"position":{"label":"Positie"},"phone":{"label":"Telefoonnummer"},"email":{"label":"E-mailadres"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Sleutel"},"value":{"label":"Waarde"}}')},function(e){e.exports=JSON.parse('{"title":"Sjablonen"}')},function(e){e.exports=JSON.parse('{"title":"Kleuren","colorOptions":"Kleuropties","primaryColor":"Hoofdkleur","accentColor":"Secundaire kleur","clipboardCopyAction":"{{color}} is naar het klembord gekopieerd."}')},function(e){e.exports=JSON.parse('{"title":"Lettertypes","fontFamily":{"label":"Lettertype Familie","helpText":"U kunt elk lettertype gebruiken dat ook op uw systeem is ge\xefnstalleerd. Voer hier gewoon de naam in van de lettertype familie en de browser zou het voor je laden."}}')},function(e){e.exports=JSON.parse('{"title":"Acties","disclaimer":"Veranderingen die u aanbrengt in uw curriculum worden automatisch bewaard in je browsers lokale opslag. Geen data wordt verstuurd, dus je informatie is helemaal veilig.","importExport":{"heading":"Importeren/Exporteren","body":"U kunt uw gegevens importeren of exporteren in JSON formaat. Hiermee kunt u uw CV op elk apparaat bewerken en afdrukken. Sla dit bestand op voor later gebruik.","buttons":{"import":"Importeren","export":"Exporteren"}},"downloadResume":{"heading":"Download je curriculum","body":"U kunt op de knop hieronder klikken om direct een PDF-versie van uw CV te downloaden. Gebruik de nieuwste versie van Google Chrome voor de beste resultaten.","buttons":{"saveAsPdf":"Opslaan als PDF"}},"loadDemoData":{"heading":"Laad Demo gegevens","body":"Onduidelijk wat te doen met een nieuwe lege pagina? Laad wat demogegevens om te zien hoe een curriculum eruit zou moeten zien en u kan meteen beginnen te bewerken.","buttons":{"loadData":"Gegevens laden"}},"reset":{"heading":"Reset alles!","body":"Deze actie zal al uw gegevens resetten en back-ups naar de lokale opslag van uw browser verwijderen dus zorg ervoor dat je je informatie hebt ge\xebxporteerd voordat je alles opnieuw instelt.","buttons":{"reset":"Resetten"}}}')},function(e){e.exports=JSON.parse('{"title":"Instellingen","language":{"label":"Taal","helpText":"Als u wilt helpen de app te vertalen in uw eigen taal, raadpleeg dan de <1>Vertalingsdocumentatie</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Over","documentation":{"heading":"Documentatie","body":"Wil je meer weten over de app? Heb je informatie nodig over hoe je kan bijdragen aan het project? Kijk niet verder, er is een uitgebreide handleiding gemaakt speciaal voor jou.","buttons":{"documentation":"Documentatie"}},"bugOrFeatureRequest":{"heading":"Fout opgemerkt? Functionaliteit aanvragen?","body":"Iets dat je voortgang verhindert om te hervatten of te hervatten? Heb je een vervelende bug gevonden die gewoon niet zal stoppen? Praat erover in de GitHub Issues sectie, of stuur mij een e-mail via de onderstaande acties.","buttons":{"raiseIssue":"Meld een probleem","sendEmail":"Stuur een e-mail"}},"sourceCode":{"heading":"Broncode","body":"Wil je het project uitvoeren vanuit de bron? Bent u een ontwikkelaar die bereid is bij te dragen aan de open-source ontwikkeling van dit project? Klik op de knop hieronder.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"Licentie informatie","body":"Het project valt onder de MIT-licentie, waarover je hieronder meer kunt lezen. In principe mag u het project overal gebruiken, mits u credits geeft aan de oorspronkelijke auteur.","buttons":{"mitLicense":"MIT Licentie"}},"footer":{"credit":"Gemaakt met liefde door <1>Amruth Pillai</1>","thanks":"Bedankt voor het gebruiken van Reactieve Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"heading":{"placeholder":"Heading"},"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date","placeholder":"March 2018"},"endDate":{"label":"End Date","placeholder":"March 2022"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"}},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name","placeholder":"Jane"},"lastName":{"label":"Last Name","placeholder":"Doe"},"subtitle":{"label":"Subtitle","placeholder":"Full Stack Web Developer"},"address":{"line1":{"label":"Address Line 1","placeholder":"Palladium Complex"},"line2":{"label":"Address Line 2","placeholder":"140 E 14th St"},"line3":{"label":"Address Line 3","placeholder":"New York, NY 10003 USA"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective","placeholder":"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Amazon"},"role":{"label":"Role","placeholder":"Front-end Web Developer"},"description":{"placeholder":"You can write about what you specialized in while working at the company and what projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Harvard University"},"major":{"label":"Major","placeholder":"Masters in Computer Science"},"grade":{"label":"Grade"},"description":{"placeholder":"You can write about projects or special credit classes that you took while studying at this school."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title","placeholder":"Math & Science Olympiad"},"subtitle":{"label":"Subtitle","placeholder":"First Place, International Level"},"description":{"placeholder":"You can write about what qualities made you succeed in getting this award."}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Dothraki"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name","placeholder":"Richard Hendricks"},"position":{"label":"Position","placeholder":"CEO, Pied Piper"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"},"description":{"placeholder":"You can write about how you and the reference contact worked together and which projects you were a part of."}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key","placeholder":"Date of Birth"},"value":{"label":"Value","placeholder":"6th August 1995"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Accent Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"printResume":{"heading":"Print Your Resume","body":"You can click on the button below to generate a PDF instantly. Alternatively, you can also use <1>Cmd/Ctrl + P</1> but it would have different effects.","buttons":{"export":"Export","print":"Print"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Wouldn\'t it be nice if there was a guide to setting it up on your local machine? Need information on how to contribute to the project? Look no further, there\'s comprehensive documentation made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Reactive Resume is a project by <1>Amruth Pillai</1>.","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Dodaj {{- heading}}","startDate":{"label":"Data rozpocz\u0119cia"},"endDate":{"label":"Data zako\u0144czenia"},"description":{"label":"Opis"}},"buttons":{"add":{"label":"Dodaj"},"delete":{"label":"Usu\u0144"}},"printDialog":{"heading":"Pobierz swoje CV","quality":{"label":"Jako\u015b\u0107"},"printType":{"label":"Rodzaj","types":{"unconstrained":"Nieograniczony","fitInA4":"Dopasuj do A4","multiPageA4":"Wielostronne A4"}},"helpText":["Ta metoda eksportu wykorzystuje HTML do konwersji CV jako obrazu i wydrukowania go w formacie PDF, co oznacza, \u017ce utraci on wszystkie mo\u017cliwo\u015bci zaznaczania / analizowania.","Je\u015bli jest to dla Ciebie wa\u017cne, spr\xf3buj wydrukowa\u0107 CV za pomoc\u0105 Ctrl + P lub przycisku drukowania poni\u017cej. Wynik mo\u017ce si\u0119 r\xf3\u017cni\u0107, poniewa\u017c wynik zale\u017cy od przegl\u0105darki, ale wiemy, \u017ce najlepiej dzia\u0142a w najnowszej wersji Google Chrome."],"buttons":{"cancel":"Anuluj","saveAsPdf":"Zapisz jako PDF"}},"panZoomAnimation":{"helpText":"Mo\u017cesz przesuwa\u0107 i powi\u0119ksza\u0107 obszar roboczy w dowolnym momencie, aby przyjrze\u0107 si\u0119 swojemu CV."},"markdownHelpText":"Mo\u017cesz u\u017cy\u0107 <1>GitHub Flavored Markdown</1> aby zmieni\u0107 styl tej sekcji tekstu."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Adres URL zdj\u0119cia"},"firstName":{"label":"Imi\u0119"},"lastName":{"label":"Nazwisko"},"subtitle":{"label":"Podtytu\u0142"},"address":{"label":"Adres","line1":{"label":"Wiersz adresu 1"},"line2":{"label":"Wiersz adresu 2"},"line3":{"label":"Wiersz adresu 3"}},"phone":{"label":"Telefon"},"website":{"label":"Strona WWW"},"email":{"label":"Adres e-mail"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Cel"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nazwa"},"role":{"label":"Stanowisko"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nazwa"},"major":{"label":"Kierunek"},"grade":{"label":"Stopie\u0144"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Tytu\u0142"},"subtitle":{"label":"Podtytu\u0142"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nazwa"},"subtitle":{"label":"Wydany przez"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nazwa"},"level":{"label":"Poziom"},"rating":{"label":"Ocena"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nazwisko"},"position":{"label":"Stanowisko"},"phone":{"label":"Numer telefonu"},"email":{"label":"Adres e-mail"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nazwa/Klucz"},"value":{"label":"Warto\u015b\u0107"}}')},function(e){e.exports=JSON.parse('{"title":"Szablony"}')},function(e){e.exports=JSON.parse('{"title":"Kolory","colorOptions":"Opcje kolor\xf3w","primaryColor":"Kolor podstawowy","accentColor":"Kolor dodatkowy","clipboardCopyAction":"{{color}} zosta\u0142 skopiowany do schowka."}')},function(e){e.exports=JSON.parse('{"title":"Czcionki","fontFamily":{"label":"Rodzina czcionek","helpText":"Mo\u017cesz tak\u017ce u\u017cy\u0107 dowolnej czcionki zainstalowanej w systemie. Wystarczy wpisa\u0107 tutaj nazw\u0119 rodziny czcionek, a przegl\u0105darka za\u0142aduje j\u0105 dla Ciebie."}}')},function(e){e.exports=JSON.parse('{"title":"Akcje","disclaimer":"Zmiany wprowadzone w CV zostan\u0105 automatycznie zapisane w lokalnej pami\u0119ci przegl\u0105darki. \u017badne dane nie s\u0105 pobierane, dlatego Twoje informacje s\u0105 ca\u0142kowicie bezpieczne.","importExport":{"heading":"Importuj/Eksportuj","body":"Mo\u017cesz importowa\u0107 lub eksportowa\u0107 swoje dane w formacie JSON. Dzi\u0119ki temu mo\u017cesz edytowa\u0107 i drukowa\u0107 swoje CV z dowolnego urz\u0105dzenia. Zapisz ten plik do p\xf3\u017aniejszego wykorzystania.","buttons":{"import":"Import","export":"Eksport"}},"downloadResume":{"heading":"Pobierz swoje CV","body":"Mo\u017cesz klikn\u0105\u0107 na poni\u017cszy przycisk, aby natychmiast pobra\u0107 wersj\u0119 PDF. Aby uzyska\u0107 najlepsze wyniki, u\u017cyj najnowszej wersji Google Chrome.","buttons":{"saveAsPdf":"Zapisz jako PDF"}},"loadDemoData":{"heading":"Wczytaj dane Demo","body":"Nie wiesz, co zrobi\u0107 z czyst\u0105 pust\u0105 stron\u0105? Za\u0142aduj niekt\xf3re dane demonstracyjne z wst\u0119pnie wype\u0142nionymi warto\u015bciami, aby zobaczy\u0107, jak powinno wygl\u0105da\u0107 CV, i mo\u017cesz rozpocz\u0105\u0107 edycj\u0119.","buttons":{"loadData":"Wczytaj dane"}},"reset":{"heading":"Zresetuj wszystko!","body":"Ta czynno\u015b\u0107 zresetuje wszystkie dane i usunie kopie zapasowe utworzone w lokalnej pami\u0119ci przegl\u0105darki, wi\u0119c upewnij si\u0119, \u017ce wyeksportowa\u0142e\u015b informacje przed zresetowaniem.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Ustawienia","language":{"label":"J\u0119zyk","helpText":"Je\u015bli chcesz pom\xf3c w t\u0142umaczeniu aplikacji na sw\xf3j j\u0119zyk, zapoznaj si\u0119 z <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"O aplikacji","documentation":{"heading":"Dokumentacja","body":"Chcesz dowiedzie\u0107 si\u0119 wi\u0119cej o aplikacji? Potrzebujesz informacji o tym, jak przyczyni\u0107 si\u0119 do rozwoju projektu? Przygotowali\u015bmy kompleksowy przewodnik stworzony tylko dla Ciebie.","buttons":{"documentation":"Dokumentacja"}},"bugOrFeatureRequest":{"heading":"B\u0142\u0105d? Potrzebujesz nowych funkcji?","body":"Co\u015b powstrzymuje twoje post\u0119py w tworzeniu CV? Znalaz\u0142e\u015b niezno\u015bny b\u0142\u0105d? Porozmawiaj o tym w sekcji Problemy na GitHub lub wy\u015blij mi e-mail, korzystaj\u0105c z poni\u017cszych dzia\u0142a\u0144.","buttons":{"raiseIssue":"Zg\u0142o\u015b problem","sendEmail":"Wy\u015blij email"}},"sourceCode":{"heading":"Kod \u017ar\xf3d\u0142owy","body":"Chcesz uruchomi\u0107 projekt ze \u017ar\xf3d\u0142a? Czy jeste\u015b programist\u0105, kt\xf3ry chce przyczyni\u0107 si\u0119 do rozwoju tego open projektu open source? Kliknij przycisk poni\u017cej.","buttons":{"githubRepo":"Repozytorium na GitHub"}},"license":{"heading":"Informacje o licencji","body":"Projekt podlega licencji MIT, o kt\xf3rej wi\u0119cej mo\u017cesz przeczyta\u0107 poni\u017cej. Zasadniczo mo\u017cesz korzysta\u0107 z projektu w dowolnym miejscu, pod warunkiem, \u017ce nie zmienisz autora projektu.","buttons":{"mitLicense":"Licencja MIT"}},"footer":{"credit":"Wykonane z Mi\u0142o\u015bci\u0105 przez <1>Amruth Pillai</1>","thanks":"Dzi\u0119kujemy za korzystanie z Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Adicionar {{- heading}}","startDate":{"label":"Data Inicial"},"endDate":{"label":"Data Final"},"description":{"label":"Descri\xe7\xe3o"}},"buttons":{"add":{"label":"Adicionar"},"delete":{"label":"Eliminar"}},"printDialog":{"heading":"Baixar Curriculum","quality":{"label":"Qualidade"},"printType":{"label":"Tipo","types":{"unconstrained":"Sem restri\xe7\xf5es","fitInA4":"Ajustar a A4","multiPageA4":"Multi-p\xe1ginas A4"}},"helpText":["Esse m\xe9todo de exporta\xe7\xe3o utiliza a tela HTML para converter o curr\xedculo em uma imagem e imprimi-lo em um PDF, o que significa que ele perder\xe1 todos os recursos de sele\xe7\xe3o / an\xe1lise.","Se isso for importante para voc\xea, tente imprimir o curr\xedculo usando Cmd / Ctrl + P ou o bot\xe3o de impress\xe3o abaixo. O resultado pode variar, pois a sa\xedda depende do navegador, mas \xe9 conhecido por funcionar melhor na vers\xe3o mais recente do Google Chrome."],"buttons":{"cancel":"Cancelar","saveAsPdf":"Salvar como PDF"}},"panZoomAnimation":{"helpText":"Voc\xea pode arrastar e dar zoom no quadro de trabalho a qualquer momento para ver mais detalhes do seu curriculum."},"markdownHelpText":"Voc\xea pode utilizar <1>GitHub Flavored Markdown</1> para estilizar esta se\xe7\xe3o."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL da foto"},"firstName":{"label":"Nome"},"lastName":{"label":"Sobrenome"},"subtitle":{"label":"Subt\xedtulo"},"address":{"label":"Endere\xe7o","line1":{"label":"Endere\xe7o linha 1"},"line2":{"label":"Endere\xe7o linha 2"},"line3":{"label":"Endere\xe7o linha 3"}},"phone":{"label":"Telefone"},"website":{"label":"Site"},"email":{"label":"Email"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objetivo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"role":{"label":"Cargo"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Institui\xe7\xe3o"},"major":{"label":"\xc1rea de estudo"},"grade":{"label":"Nota"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"T\xedtulo"},"subtitle":{"label":"Subt\xedtulo"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Nome"},"subtitle":{"label":"Autoria"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Nome"},"level":{"label":"N\xedvel"},"rating":{"label":"Nota"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Nome"},"position":{"label":"Posi\xe7\xe3o"},"phone":{"label":"Telefone"},"email":{"label":"Email"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Chave"},"value":{"label":"Valor"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Cores","colorOptions":"Op\xe7\xf5es de cores","primaryColor":"Cor principal","accentColor":"Cor secund\xe1ria","clipboardCopyAction":"A cor {{color}} foi copiada para \xe1rea de transfer\xeancia."}')},function(e){e.exports=JSON.parse('{"title":"Fontes","fontFamily":{"label":"Fam\xedlia de Fontes","helpText":"Voc\xea tamb\xe9m pode usar qualquer fonte que esteja instalada no seu sistema. Basta digitar o nome da fonte aqui e o navegador vai carreg\xe1-la para voc\xea."}}')},function(e){e.exports=JSON.parse('{"title":"A\xe7\xf5es","disclaimer":"As altera\xe7\xf5es que voc\xea faz no seu curriculum s\xe3o salvas automaticamente no armazenamento local do seu navegador. Nenhum dado \xe9 partilhado, por isso sua informa\xe7\xe3o est\xe1 completamente segura.","importExport":{"heading":"Importar/Exportar","body":"Voc\xea pode importar ou exportar seus dados no formato JSON. Sendo assim, \xe9 poss\xedvel editar ou imprimir seu curriculum em qualquer dispositivo. Salve este arquivo para us\xe1-lo posteriormente.","buttons":{"import":"Importar","export":"Exportar"}},"downloadResume":{"heading":"Baixe seu Curriculum","body":"Voc\xea pode clicar no bot\xe3o abaixo para baixar a vers\xe3o em PDF do seu curriculum. Para obter melhores resultados, por favor utilize a ver\xe3o mais recente do Google Chrome.","buttons":{"saveAsPdf":"Salvar como PDF"}},"loadDemoData":{"heading":"Carregar dados demonstrativos","body":"Na d\xfavida sobre o que fazer com uma p\xe1gina em branco? Carregue os dados demonstrativos com valores j\xe1 preenchidos para ver como o curriculum fica e a partir da\xed voc\xea pode come\xe7ar a editar.","buttons":{"loadData":"Carregar dados"}},"reset":{"heading":"Reiniciar tudo!","body":"Esta a\xe7\xe3o vai apagar todos os seus dados e remover os backups feitos no armazenamento local do seu navegador tamb\xe9m. Por favor, lembre de exportar as suas informa\xe7\xf5es antes de reiniciar tudo.","buttons":{"reset":"Reiniciar"}}}')},function(e){e.exports=JSON.parse('{"title":"Configura\xe7\xe3o","language":{"label":"Escolher idioma","helpText":"Se voc\xea gostaria de ajudar a traduzir esta aplica\xe7\xe3o para o seu idioma, por favor, consulte a <1>Documenta\xe7\xe3o de Tradu\xe7\xe3o</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Sobre","documentation":{"heading":"Documenta\xe7\xe3o","body":"Quer saber mais sobre a applica\xe7\xe3o? N\xe3o seria \xf3timo se houvesse um guia para configur\xe1-la em sua m\xe1quina? Precisa de informa\xe7\xe3o sobre como contribuir para o projeto? N\xe3o precisa procurar mais, aqui h\xe1 uma documenta\xe7\xe3o compreensiva para voc\xea.","buttons":{"documentation":"Documenta\xe7\xe3o"}},"bugOrFeatureRequest":{"heading":"Bug? Solicita\xe7\xe3o de nova funcionalidade?","body":"Algo impedindo voc\xea de progredir com um curriculum? Encontrou aquele erro chato e persistente? Fale sobre ele na se\xe7\xe3o de Issues no Github, ou me envie um email usando as seguintes a\xe7\xf5es.","buttons":{"raiseIssue":"Notificar um problema","sendEmail":"Enviar um e-mail"}},"sourceCode":{"heading":"C\xf3digo fonte","body":"Tem interesse em executar o c\xf3digo fonte deste projeto? Voc\xea \xe9 um desenvolvedor interessado em contribuir para o desenvolvimento open-source deste projeto? Click no bot\xe3o abaixo.","buttons":{"githubRepo":"Reposit\xf3rio Github"}},"license":{"heading":"Informa\xe7\xe3o da licen\xe7a","body":"O projeto \xe9 regido pela licen\xe7a MIT, a qual voc\xea pode ler mais sobre abaixo. Basicamente, voc\xea pode usar este projeto onde quiser desde que d\xea os cr\xe9ditos ao autor original.","buttons":{"mitLicense":"Licen\xe7a MIT"}},"footer":{"credit":"Projeto criado com amor por <1>Amruth Pillai</1>.","thanks":"Obrigado por usar Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c {{- heading}}","startDate":{"label":"\u0414\u0430\u0442\u0430 \u043d\u0430\u0447\u0430\u043b\u0430"},"endDate":{"label":"\u0414\u0430\u0442\u0430 \u043e\u043a\u043e\u043d\u0447\u0430\u043d\u0438\u044f"},"description":{"label":"\u041e\u043f\u0438\u0441\u0430\u043d\u0438\u0435"}},"buttons":{"add":{"label":"\u0414\u043e\u0431\u0430\u0432\u0438\u0442\u044c"},"delete":{"label":"\u0423\u0434\u0430\u043b\u0438\u0442\u044c"}},"printDialog":{"heading":"\u0421\u043a\u0430\u0447\u0430\u0442\u044c \u0440\u0435\u0437\u044e\u043c\u0435","quality":{"label":"\u041a\u0430\u0447\u0435\u0441\u0442\u0432\u043e"},"printType":{"label":"\u0422\u0438\u043f","types":{"unconstrained":"\u041d\u0435\u043e\u0433\u0440\u0430\u043d\u0438\u0447\u0435\u043d\u043d\u044b\u0439","fitInA4":"\u041f\u043e \u0440\u0430\u0437\u043c\u0435\u0440\u0443 A4","multiPageA4":"\u041c\u043d\u043e\u0433\u043e\u0441\u0442\u0440\u0430\u043d\u0438\u0447\u043d\u044b\u0439 A4"}},"helpText":["\u042d\u0442\u043e\u0442 \u043c\u0435\u0442\u043e\u0434 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0430 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u0435\u0442 HTML canvas \u0434\u043b\u044f \u043f\u0440\u0435\u043e\u0431\u0440\u0430\u0437\u043e\u0432\u0430\u043d\u0438\u044f \u0440\u0435\u0437\u044e\u043c\u0435 \u0432 \u0438\u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u0438\u0435 \u0438 \u043a\u043e\u043d\u0432\u0435\u0440\u0442\u0430\u0446\u0438\u044e \u0432 \u0444\u043e\u0440\u043c\u0430\u0442 PDF, \u0447\u0442\u043e \u043e\u0437\u043d\u0430\u0447\u0430\u0435\u0442, \u0447\u0442\u043e \u043e\u043d \u043f\u043e\u0442\u0435\u0440\u044f\u0435\u0442 \u0432\u0441\u0435 \u0432\u043e\u0437\u043c\u043e\u0436\u043d\u043e\u0441\u0442\u0438 \u0432\u044b\u0431\u043e\u0440\u0430/\u0441\u0438\u043d\u0442\u0430\u043a\u0441\u0438\u0447\u0435\u0441\u043a\u043e\u0433\u043e \u0430\u043d\u0430\u043b\u0438\u0437\u0430.","\u0415\u0441\u043b\u0438 \u044d\u0442\u043e \u0432\u0430\u0436\u043d\u043e \u0434\u043b\u044f \u0412\u0430\u0441, \u043f\u043e\u0436\u0430\u043b\u0443\u0439\u0441\u0442\u0430, \u043f\u043e\u043f\u0440\u043e\u0431\u0443\u0439\u0442\u0435 \u0440\u0430\u0441\u043f\u0435\u0447\u0430\u0442\u0430\u0442\u044c \u0440\u0435\u0437\u044e\u043c\u0435 \u0432\u043c\u0435\u0441\u0442\u043e \u044d\u0442\u043e\u0433\u043e, \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u044f Cmd / Ctrl + P \u0438\u043b\u0438 \u043a\u043d\u043e\u043f\u043a\u0443 \u043f\u0435\u0447\u0430\u0442\u0438 \u043d\u0438\u0436\u0435. \u0420\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442 \u043c\u043e\u0436\u0435\u0442 \u043e\u0442\u043b\u0438\u0447\u0430\u0442\u044c\u0441\u044f, \u043f\u043e\u0441\u043a\u043e\u043b\u044c\u043a\u0443 \u0440\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442 \u0437\u0430\u0432\u0438\u0441\u0438\u0442 \u043e\u0442 \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430, \u043d\u043e \u0438\u0437\u0432\u0435\u0441\u0442\u043d\u043e, \u0447\u0442\u043e \u043e\u043d \u043b\u0443\u0447\u0448\u0435 \u0432\u0441\u0435\u0433\u043e \u0440\u0430\u0431\u043e\u0442\u0430\u0435\u0442 \u043d\u0430 \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u0435\u0439 \u0432\u0435\u0440\u0441\u0438\u0438 Google Chrome."],"buttons":{"cancel":"\u041e\u0442\u043c\u0435\u043d\u0430","saveAsPdf":"\u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u0432 PDF"}},"panZoomAnimation":{"helpText":"\u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u043f\u0435\u0440\u0435\u043c\u0435\u0449\u0430\u0442\u044c \u0438 \u043c\u0430\u0441\u0448\u0442\u0430\u0431\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0441\u0432\u043e\u0451 \u0440\u0435\u0437\u044e\u043c\u0435 \u0447\u0442\u043e\u0431\u044b \u043f\u043e\u0431\u043b\u0438\u0436\u0435 \u0432\u0437\u0433\u043b\u044f\u043d\u0443\u0442\u044c \u043d\u0430 \u043d\u0435\u0433\u043e."},"markdownHelpText":"\u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c <1>GitHub Flavored Markdown</1> \u0437\u0434\u0435\u0441\u044c."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL \u0410\u0434\u0440\u0435\u0441 \u0444\u043e\u0442\u043e\u0433\u0440\u0430\u0444\u0438\u0438"},"firstName":{"label":"\u0418\u043c\u044f"},"lastName":{"label":"\u0424\u0430\u043c\u0438\u043b\u0438\u044f"},"subtitle":{"label":"\u041f\u043e\u0434\u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a"},"address":{"label":"\u0410\u0434\u0440\u0435\u0441","line1":{"label":"\u0410\u0434\u0440\u0435\u0441, \u0441\u0442\u0440\u043e\u043a\u0430 1"},"line2":{"label":"\u0410\u0434\u0440\u0435\u0441, \u0441\u0442\u0440\u043e\u043a\u0430 2"},"line3":{"label":"\u0410\u0434\u0440\u0435\u0441, \u0441\u0442\u0440\u043e\u043a\u0430 3"}},"phone":{"label":"\u041d\u043e\u043c\u0435\u0440 \u0442\u0435\u043b\u0435\u0444\u043e\u043d\u0430"},"website":{"label":"\u0412\u0435\u0431-\u0441\u0430\u0439\u0442"},"email":{"label":"E-mail \u0430\u0434\u0440\u0435\u0441"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0426\u0435\u043b\u044c"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0418\u043c\u044f"},"role":{"label":"\u0414\u043e\u043b\u0436\u043d\u043e\u0441\u0442\u044c"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0418\u043c\u044f"},"major":{"label":"\u041f\u0440\u0435\u0434\u043c\u0435\u0442"},"grade":{"label":"\u041a\u043b\u0430\u0441\u0441"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0417\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a"},"subtitle":{"label":"\u041f\u043e\u0434\u0437\u0430\u0433\u043e\u043b\u043e\u0432\u043e\u043a"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0418\u043c\u044f"},"subtitle":{"label":"\u0410\u0432\u0442\u043e\u0440"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0418\u043c\u044f"},"level":{"label":"\u0423\u0440\u043e\u0432\u0435\u043d\u044c"},"rating":{"label":"\u0420\u0435\u0439\u0442\u0438\u043d\u0433"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0418\u043c\u044f"},"position":{"label":"\u041f\u043e\u043b\u043e\u0436\u0435\u043d\u0438\u0435"},"phone":{"label":"\u041d\u043e\u043c\u0435\u0440 \u0442\u0435\u043b\u0435\u0444\u043e\u043d\u0430"},"email":{"label":"E-mail \u0430\u0434\u0440\u0435\u0441"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435"},"value":{"label":"\u0417\u043d\u0430\u0447\u0435\u043d\u0438\u0435"}}')},function(e){e.exports=JSON.parse('{"title":"\u0428\u0430\u0431\u043b\u043e\u043d\u044b"}')},function(e){e.exports=JSON.parse('{"title":"\u0426\u0432\u0435\u0442\u0430","colorOptions":"\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u0446\u0432\u0435\u0442\u0430","primaryColor":"\u0426\u0432\u0435\u0442 \u0442\u0435\u043a\u0441\u0442\u0430","accentColor":"\u041e\u0441\u043d\u043e\u0432\u043d\u043e\u0439 \u0446\u0432\u0435\u0442","clipboardCopyAction":"\u0426\u0432\u0435\u0442 {{color}} \u0431\u044b\u043b \u0441\u043a\u043e\u043f\u0438\u0440\u043e\u0432\u0430\u043d \u0432 \u0431\u0443\u0444\u0435\u0440 \u043e\u0431\u043c\u0435\u043d\u0430."}')},function(e){e.exports=JSON.parse('{"title":"\u0428\u0440\u0438\u0444\u0442\u044b","fontFamily":{"label":"\u0428\u0440\u0438\u0444\u0442","helpText":"\u0412\u044b \u0442\u0430\u043a\u0436\u0435 \u043c\u043e\u0436\u0435\u0442\u0435 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u043b\u044e\u0431\u043e\u0439 \u0448\u0440\u0438\u0444\u0442, \u0443\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u043d\u044b\u0439 \u0432 \u0432\u0430\u0448\u0435\u0439 \u0441\u0438\u0441\u0442\u0435\u043c\u0435. \u041f\u0440\u043e\u0441\u0442\u043e \u0432\u0432\u0435\u0434\u0438\u0442\u0435 \u0437\u0434\u0435\u0441\u044c \u0438\u043c\u044f \u0448\u0440\u0438\u0444\u0442\u0430, \u0438 \u0431\u0440\u0430\u0443\u0437\u0435\u0440 \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442 \u0435\u0433\u043e."}}')},function(e){e.exports=JSON.parse('{"title":"\u0414\u0435\u0439\u0441\u0442\u0432\u0438\u044f","disclaimer":"\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u044f, \u0432\u043d\u0435\u0441\u0435\u043d\u043d\u044b\u0435 \u0432 \u0432\u0430\u0448\u0435 \u0440\u0435\u0437\u044e\u043c\u0435, \u0441\u043e\u0445\u0440\u0430\u043d\u044f\u044e\u0442\u0441\u044f \u0430\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438 \u0432 \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u043e\u0435 \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0435 \u0432\u0430\u0448\u0435\u0433\u043e \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430. \u0414\u0430\u043d\u043d\u044b\u0435 \u043d\u0435 \u043e\u0442\u043f\u0440\u0430\u0432\u043b\u044f\u044e\u0442\u0441\u044f \u043d\u0430 \u0441\u0435\u0440\u0432\u0435\u0440\u0430, \u043f\u043e\u044d\u0442\u043e\u043c\u0443 \u0432\u0430\u0448\u0430 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u0432 \u0431\u0435\u0437\u043e\u043f\u0430\u0441\u043d\u043e\u0441\u0442\u0438.","importExport":{"heading":"\u0418\u043c\u043f\u043e\u0440\u0442/\u042d\u043a\u0441\u043f\u043e\u0440\u0442","body":"\u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u0438\u043c\u043f\u043e\u0440\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0438\u043b\u0438 \u044d\u043a\u0441\u043f\u043e\u0440\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0434\u0430\u043d\u043d\u044b\u0435 \u0432 \u0444\u043e\u0440\u043c\u0430\u0442\u0435 JSON. \u041f\u0440\u0438 \u044d\u0442\u043e\u043c \u0432\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u0442\u044c \u0438 \u0440\u0430\u0441\u043f\u0435\u0447\u0430\u0442\u0430\u0442\u044c \u0440\u0435\u0437\u044e\u043c\u0435 \u0441 \u043b\u044e\u0431\u043e\u0433\u043e \u0443\u0441\u0442\u0440\u043e\u0439\u0441\u0442\u0432\u0430. \u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u0435 \u044d\u0442\u043e\u0442 \u0444\u0430\u0439\u043b \u0434\u043b\u044f \u043f\u043e\u0441\u043b\u0435\u0434\u0443\u044e\u0449\u0435\u0433\u043e \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u044f.","buttons":{"import":"\u0418\u043c\u043f\u043e\u0440\u0442","export":"\u042d\u043a\u0441\u043f\u043e\u0440\u0442"}},"downloadResume":{"heading":"\u0421\u043a\u0430\u0447\u0430\u0442\u044c \u0440\u0435\u0437\u044e\u043c\u0435","body":"\u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u043d\u0430\u0436\u0430\u0442\u044c \u043d\u0430 \u043a\u043d\u043e\u043f\u043a\u0443 \u043d\u0438\u0436\u0435, \u0447\u0442\u043e\u0431\u044b \u0437\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044c PDF-\u0432\u0435\u0440\u0441\u0438\u044e \u0432\u0430\u0448\u0435\u0433\u043e \u0440\u0435\u0437\u044e\u043c\u0435. \u041b\u0443\u0447\u0448\u0435 \u0438\u0441\u043f\u043e\u043b\u0437\u043e\u0432\u0430\u0442\u044c \u043f\u043e\u0441\u043b\u0435\u0434\u043d\u044e\u044e \u0432\u0435\u0440\u0441\u0438\u044e Google Chrome.","buttons":{"saveAsPdf":"\u0421\u043e\u0445\u0440\u0430\u043d\u0438\u0442\u044c \u0432 PDF"}},"loadDemoData":{"heading":"\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044c \u0434\u0435\u043c\u043e-\u0434\u0430\u043d\u043d\u044b\u0435","body":"\u041d\u0435\u043f\u043e\u043d\u044f\u0442\u043d\u043e, \u0447\u0442\u043e \u0434\u0435\u043b\u0430\u0442\u044c \u0441 \u0447\u0438\u0441\u0442\u043e\u0439 \u0441\u0442\u0440\u0430\u043d\u0438\u0446\u0435\u0439? \u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u0435 \u0434\u0435\u043c\u043e, \u0447\u0442\u043e\u0431\u044b \u0443\u0432\u0438\u0434\u0435\u0442\u044c \u043f\u0440\u0438\u043c\u0435\u0440 \u0440\u0435\u0437\u044e\u043c\u0435, \u0438 \u0432\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u043d\u0430\u0447\u0430\u0442\u044c \u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435.","buttons":{"loadData":"\u0417\u0430\u0433\u0440\u0443\u0437\u0438\u0442\u044c \u0434\u0435\u043c\u043e"}},"reset":{"heading":"\u0421\u0431\u0440\u043e\u0441\u0438\u0442\u044c \u0432\u0441\u0435","body":"\u042d\u0442\u043e \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0435 \u0441\u0431\u0440\u043e\u0441\u0438\u0442 \u0432\u0441\u0435 \u0432\u0430\u0448\u0438 \u0434\u0430\u043d\u043d\u044b\u0435 \u0438 \u0443\u0434\u0430\u043b\u0438\u0442 \u0440\u0435\u0437\u0435\u0440\u0432\u043d\u044b\u0435 \u043a\u043e\u043f\u0438\u0438 \u043b\u043e\u043a\u0430\u043b\u044c\u043d\u043e\u0433\u043e \u0445\u0440\u0430\u043d\u0438\u043b\u0438\u0449\u0430 \u0432\u0430\u0448\u0435\u0433\u043e \u0431\u0440\u0430\u0443\u0437\u0435\u0440\u0430. \u043f\u043e\u044d\u0442\u043e\u043c\u0443 \u0443\u0431\u0435\u0434\u0438\u0442\u0435\u0441\u044c, \u0447\u0442\u043e \u0432\u044b \u0441\u043e\u0445\u0440\u0430\u043d\u0438\u043b\u0438 \u0432\u0430\u0448\u0443 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044e, \u043f\u0435\u0440\u0435\u0434 \u0441\u0431\u0440\u043e\u0441\u043e\u043c.","buttons":{"reset":"\u0421\u0431\u0440\u043e\u0441\u0438\u0442\u044c"}}}')},function(e){e.exports=JSON.parse('{"title":"\u041d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438","language":{"label":"\u042f\u0437\u044b\u043a","helpText":"\u0415\u0441\u043b\u0438 \u0432\u044b \u0445\u043e\u0442\u0438\u0442\u0435 \u043f\u043e\u043c\u043e\u0447\u044c \u043f\u0435\u0440\u0435\u0432\u0435\u0441\u0442\u0438 \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u0435 \u043d\u0430 \u0432\u0430\u0448 \u044f\u0437\u044b\u043a, \u043e\u0431\u0440\u0430\u0442\u0438\u0442\u0435\u0441\u044c \u043a <1>\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u0438 \u043f\u043e \u043f\u0435\u0440\u0435\u0432\u043e\u0434\u0443</1>."}}')},function(e){e.exports=JSON.parse('{"title":"\u041e \u043f\u0440\u043e\u0433\u0440\u0430\u043c\u043c\u0435","documentation":{"heading":"\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u044f","body":"\u0425\u043e\u0442\u0438\u0442\u0435 \u0443\u0437\u043d\u0430\u0442\u044c \u0431\u043e\u043b\u044c\u0448\u0435 \u043e \u043f\u0440\u0438\u043b\u043e\u0436\u0435\u043d\u0438\u0438? \u041d\u0443\u0436\u043d\u0430 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u0442\u043e\u043c, \u043a\u0430\u043a \u0432\u043d\u0435\u0441\u0442\u0438 \u0441\u0432\u043e\u0439 \u0432\u043a\u043b\u0430\u0434 \u0432 \u043f\u0440\u043e\u0435\u043a\u0442?","buttons":{"documentation":"\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430\u0446\u0438\u044f"}},"bugOrFeatureRequest":{"heading":"\u041e\u0448\u0438\u0431\u043a\u0430? \u0425\u043e\u0442\u0438\u0442\u0435 \u043f\u0440\u0435\u0434\u043b\u043e\u0436\u0438\u0442\u044c \u0444\u0443\u043d\u043a\u0446\u0438\u044e?","body":"\u0427\u0442\u043e-\u0442\u043e \u043c\u0435\u0448\u0430\u0435\u0442 \u0432\u0430\u043c \u0432 \u0441\u043e\u0437\u0434\u0430\u043d\u0438\u0438 \u0440\u0435\u0437\u044e\u043c\u0435? \u041d\u0430\u0448\u043b\u0438 \u043e\u0448\u0438\u0431\u043a\u0443? \u0420\u0430\u0441\u0441\u043a\u0430\u0436\u0438\u0442\u0435 \u043e\u0431 \u044d\u0442\u043e\u043c \u0432 \u0440\u0430\u0437\u0434\u0435\u043b\u0435 issues \u043d\u0430 GitHub \u0438\u043b\u0438 \u043e\u0442\u043f\u0440\u0430\u0432\u044c\u0442\u0435 \u043c\u043d\u0435 \u043f\u0438\u0441\u044c\u043c\u043e \u043f\u043e \u044d\u043b\u0435\u043a\u0442\u0440\u043e\u043d\u043d\u043e\u0439 \u043f\u043e\u0447\u0442\u0435, \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u0443\u044f \u043a\u043d\u043e\u043f\u043a\u0438 \u043d\u0438\u0436\u0435.","buttons":{"raiseIssue":"\u0421\u043e\u043e\u0431\u0449\u0438\u0442\u044c \u043e\u0431 \u043e\u0448\u0438\u0431\u043a\u0435","sendEmail":"\u041d\u0430\u043f\u0438\u0441\u0430\u0442\u044c \u043f\u0438\u0441\u044c\u043c\u043e"}},"sourceCode":{"heading":"\u0418\u0441\u0445\u043e\u0434\u043d\u044b\u0439 \u043a\u043e\u0434","body":"\u0425\u043e\u0442\u0438\u0442\u0435 \u0437\u0430\u043f\u0443\u0441\u0442\u0438\u0442\u044c \u043f\u0440\u043e\u0435\u043a\u0442 \u0438\u0437 \u0438\u0441\u0445\u043e\u0434\u043d\u043e\u0433\u043e \u043a\u043e\u0434\u0430? \u0412\u044b \u0445\u043e\u0442\u0438\u0442\u0435 \u0432\u043d\u0435\u0441\u0442\u0438 \u0432\u043a\u043b\u0430\u0434 \u0432 \u0440\u0430\u0437\u0440\u0430\u0431\u043e\u0442\u043a\u0443 \u044d\u0442\u043e\u0433\u043e \u043f\u0440\u043e\u0435\u043a\u0442\u0430? \u041d\u0430\u0436\u043c\u0438\u0442\u0435 \u043d\u0430 \u043a\u043d\u043e\u043f\u043a\u0443 \u043d\u0438\u0436\u0435.","buttons":{"githubRepo":"GitHub"}},"license":{"heading":"\u0418\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u044f \u043e \u043b\u0438\u0446\u0435\u043d\u0437\u0438\u0438","body":"\u041f\u0440\u043e\u0435\u043a\u0442 \u0443\u043f\u0440\u0430\u0432\u043b\u044f\u0435\u0442\u0441\u044f \u0432 \u0441\u043e\u043e\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u0438 \u0441 \u043b\u0438\u0446\u0435\u043d\u0437\u0438\u0435\u0439 MIT, \u043e \u043a\u043e\u0442\u043e\u0440\u043e\u0439 \u0432\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u043f\u0440\u043e\u0447\u0438\u0442\u0430\u0442\u044c \u043d\u0438\u0436\u0435. \u0412\u044b \u043c\u043e\u0436\u0435\u0442\u0435 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u044c \u043f\u0440\u043e\u0435\u043a\u0442 \u0432 \u043b\u044e\u0431\u043e\u043c \u043c\u0435\u0441\u0442\u0435 \u043f\u0440\u0438 \u0443\u0441\u043b\u043e\u0432\u0438\u0438, \u0447\u0442\u043e \u0432\u044b \u0443\u043a\u0430\u0436\u0435\u0442\u0435 \u0430\u0432\u0442\u043e\u0440\u0430 \u043f\u0440\u043e\u0435\u043a\u0442\u0430.","buttons":{"mitLicense":"\u041b\u0438\u0446\u0435\u043d\u0437\u0438\u044f MIT"}},"footer":{"credit":"\u0421\u0434\u0435\u043b\u0430\u043d\u043e \u0441 \u043b\u044e\u0431\u043e\u0432\u044c\u044e <1>\u0410\u043c\u0440\u0443\u0442 \u041f\u0438\u043b\u043b\u0430\u0439</1>","thanks":"\u0421\u043f\u0430\u0441\u0438\u0431\u043e \u0437\u0430 \u0438\u0441\u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u043d\u0438\u0435 Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"{{- heading}} \u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd","startDate":{"label":"\u0ba4\u0bca\u0b9f\u0b95\u0bcd\u0b95 \u0ba4\u0bc7\u0ba4\u0bbf"},"endDate":{"label":"\u0b95\u0b9f\u0bc8\u0b9a\u0bbf \u0ba4\u0bc7\u0ba4\u0bbf"},"description":{"label":"\u0bb5\u0bbf\u0bb3\u0b95\u0bcd\u0b95\u0bae\u0bcd"}},"buttons":{"add":{"label":"\u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95"},"delete":{"label":"\u0b85\u0bb4\u0bbf"}},"printDialog":{"heading":"\u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0b9f\u0bb5\u0bc1\u0ba9\u0bcd\u0bb2\u0bcb\u0b9f\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bb5\u0bc1\u0bae\u0bcd","quality":{"label":"\u0ba4\u0bb0\u0bae\u0bcd"},"printType":{"label":"\u0bb5\u0b95\u0bc8","types":{"unconstrained":"\u0b85\u0bb3\u0bb5\u0bc1 \u0b87\u0bb2\u0bcd\u0bb2\u0bc7","fitInA4":"A4 \u0b87\u0bb2\u0bcd \u0b9a\u0bc7\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd","multiPageA4":"\u0bae\u0bb2\u0bcd\u0b9f\u0bbf-\u0baa\u0bc7\u0b9c\u0bcd A4"}},"helpText":["\u0b87\u0ba8\u0bcd\u0ba4 \u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf \u0bae\u0bc1\u0bb1\u0bc8 HTML \u0b95\u0bc7\u0ba9\u0bcd\u0bb5\u0bbe\u0bb8\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bbf \u0bb5\u0bbf\u0ba3\u0bcd\u0ba3\u0baa\u0bcd\u0baa\u0ba4\u0bcd\u0ba4\u0bc8 \u0b92\u0bb0\u0bc1 \u0baa\u0b9f\u0bae\u0bbe\u0b95 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0bbf PDF \u0b87\u0bb2\u0bcd \u0b85\u0b9a\u0bcd\u0b9a\u0bbf\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1, \u0b85\u0ba4\u0bbe\u0bb5\u0ba4\u0bc1 \u0b87\u0ba4\u0bc1 \u0ba4\u0bc7\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bcd / \u0baa\u0bbe\u0b95\u0bc1\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bae\u0bcd \u0ba4\u0bbf\u0bb1\u0ba9\u0bcd\u0b95\u0bb3\u0bc8 \u0b87\u0bb4\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bcd.","\u0b87\u0ba4\u0bc1 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0bae\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0baf\u0bae\u0bcd \u0b8e\u0ba9\u0bcd\u0bb1\u0bbe\u0bb2\u0bcd, \u0ba4\u0baf\u0bb5\u0bc1\u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bc1 Cmd/Ctrl + P \u0b85\u0bb2\u0bcd\u0bb2\u0ba4\u0bc1 \u0b95\u0bc0\u0bb4\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3 \u0b85\u0b9a\u0bcd\u0b9a\u0bc1 \u0baa\u0bca\u0ba4\u0bcd\u0ba4\u0bbe\u0ba9\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bbf \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0b85\u0b9a\u0bcd\u0b9a\u0bbf\u0b9f \u0bae\u0bc1\u0baf\u0bb1\u0bcd\u0b9a\u0bbf\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd. \u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc0\u0b9f\u0bc1 \u0b89\u0bb2\u0bbe\u0bb5\u0bbf \u0b9a\u0bbe\u0bb0\u0bcd\u0ba8\u0bcd\u0ba4\u0bc1 \u0b87\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0ba4\u0bbe\u0bb2\u0bcd \u0bae\u0bc1\u0b9f\u0bbf\u0bb5\u0bc1 \u0bae\u0bbe\u0bb1\u0bc1\u0baa\u0b9f\u0bb2\u0bbe\u0bae\u0bcd, \u0b86\u0ba9\u0bbe\u0bb2\u0bcd \u0b87\u0ba4\u0bc1 Google Chrome \u0b87\u0ba9\u0bcd \u0b9a\u0bae\u0bc0\u0baa\u0ba4\u0bcd\u0ba4\u0bbf\u0baf \u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bbf\u0bb2\u0bcd \u0b9a\u0bbf\u0bb1\u0baa\u0bcd\u0baa\u0bbe\u0b95 \u0b9a\u0bc6\u0baf\u0bb2\u0bcd\u0baa\u0b9f\u0bc1\u0bae\u0bcd \u0b8e\u0ba9\u0bcd\u0bb1\u0bc1 \u0b85\u0bb1\u0bbf\u0baf\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1."],"buttons":{"cancel":"\u0bb0\u0ba4\u0bcd\u0ba4\u0bc1\u0b9a\u0bc6\u0baf\u0bcd","saveAsPdf":"PDF \u0b86\u0b95 \u0b9a\u0bc7\u0bae\u0bbf\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd"}},"panZoomAnimation":{"helpText":"\u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0ba8\u0bc6\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bae\u0bbe\u0b95\u0baa\u0bcd \u0baa\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b8e\u0ba8\u0bcd\u0ba4 \u0ba8\u0bc7\u0bb0\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bc1\u0bae\u0bcd \u0b86\u0bb0\u0bcd\u0b9f\u0bcd\u0baa\u0bcb\u0bb0\u0bcd\u0b9f\u0bc8\u0b9a\u0bcd \u0b9a\u0bc1\u0bb1\u0bcd\u0bb1\u0bbf \u0baa\u0bc6\u0bb0\u0bbf\u0ba4\u0bbe\u0b95\u0bcd\u0b95\u0bb2\u0bbe\u0bae\u0bcd."},"markdownHelpText":"\u0b89\u0bb0\u0bc8\u0baf\u0bbf\u0ba9\u0bcd \u0b87\u0ba8\u0bcd\u0ba4 \u0baa\u0b95\u0bc1\u0ba4\u0bbf\u0baf\u0bc8 \u0bb5\u0b9f\u0bbf\u0bb5\u0bae\u0bc8\u0b95\u0bcd\u0b95 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd <1>GitHub Flavored Markdown </ 1> \u0b90\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb2\u0bbe\u0bae\u0bcd."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u0baa\u0bc1\u0b95\u0bc8\u0baa\u0bcd\u0baa\u0b9f\u0bae\u0bcd URL"},"firstName":{"label":"\u0bae\u0bc1\u0ba4\u0bb2\u0bcd \u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"lastName":{"label":"\u0b95\u0b9f\u0bc8\u0b9a\u0bbf \u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"subtitle":{"label":"\u0ba4\u0bc1\u0ba3\u0bc8\u0ba4\u0bcd \u0ba4\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bc1"},"address":{"label":"\u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf","line1":{"label":"\u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf \u0bb5\u0bb0\u0bbf 1"},"line2":{"label":"\u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf \u0bb5\u0bb0\u0bbf 2"},"line3":{"label":"\u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf \u0bb5\u0bb0\u0bbf 3"}},"phone":{"label":"\u0ba4\u0bc6\u0bbe\u0bb2\u0bc8\u0baa\u0bc7\u0b9a\u0bbf \u0b8e\u0ba3\u0bcd"},"website":{"label":"\u0bb5\u0bc6\u0baa\u0bcd\u0b9a\u0bc8\u0b9f\u0bcd"},"email":{"label":"\u0b88\u0bae\u0bc6\u0baf\u0bbf\u0bb2\u0bcd \u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u0ba8\u0bcb\u0b95\u0bcd\u0b95\u0bae\u0bcd"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"role":{"label":"\u0baa\u0b99\u0bcd\u0b95\u0bc1"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"major":{"label":"\u0bae\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0baf"},"grade":{"label":"\u0ba4\u0bb0\u0bae\u0bcd"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0ba4\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bc1"},"subtitle":{"label":"\u0ba4\u0bc1\u0ba3\u0bc8\u0ba4\u0bcd \u0ba4\u0bb2\u0bc8\u0baa\u0bcd\u0baa\u0bc1"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"subtitle":{"label":"\u0b85\u0ba4\u0bbf\u0b95\u0bbe\u0bb0\u0bae\u0bcd"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"level":{"label":"\u0ba8\u0bbf\u0bb2\u0bc8"},"rating":{"label":"\u0bae\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc0\u0b9f\u0bc1"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u0baa\u0bc6\u0baf\u0bb0\u0bcd"},"position":{"label":"\u0ba8\u0bbf\u0bb2\u0bc8"},"phone":{"label":"\u0ba4\u0bc6\u0bbe\u0bb2\u0bc8\u0baa\u0bc7\u0b9a\u0bbf \u0b8e\u0ba3\u0bcd"},"email":{"label":"\u0b88\u0bae\u0bc6\u0baf\u0bbf\u0bb2\u0bcd \u0bae\u0bc1\u0b95\u0bb5\u0bb0\u0bbf"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u0b9a\u0bbe\u0bb5\u0bbf"},"value":{"label":"\u0bae\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc1"}}')},function(e){e.exports=JSON.parse('{"title":"\u0b9f\u0bc6\u0bae\u0bcd\u0baa\u0bcd\u0bb3\u0b9f\u0bcd\u0bb8\u0bcd"}')},function(e){e.exports=JSON.parse('{"title":"\u0bb5\u0ba3\u0bcd\u0ba3\u0b99\u0bcd\u0b95\u0bb3\u0bcd","colorOptions":"\u0bb5\u0ba3\u0bcd\u0ba3 \u0bb5\u0bbf\u0bb0\u0bc1\u0baa\u0bcd\u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bcd","primaryColor":"\u0bae\u0bc1\u0ba4\u0ba9\u0bcd\u0bae\u0bc8 \u0ba8\u0bbf\u0bb1\u0bae\u0bcd","accentColor":"\u0b87\u0bb0\u0ba3\u0bcd\u0b9f\u0bbe\u0bae\u0bcd \u0ba8\u0bbf\u0bb1\u0bae\u0bcd","clipboardCopyAction":"{{color}} \u0b95\u0bbf\u0bb3\u0bbf\u0baa\u0bcd\u0baa\u0bcb\u0bb0\u0bcd\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0ba8\u0b95\u0bb2\u0bc6\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0ba4\u0bc1."}')},function(e){e.exports=JSON.parse('{"title":"\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bb3\u0bcd","fontFamily":{"label":"\u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0baa\u0bc7\u0bae\u0bbf\u0bb2\u0bbf","helpText":"\u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b95\u0ba3\u0bbf\u0ba9\u0bbf\u0baf\u0bbf\u0bb2\u0bcd \u0ba8\u0bbf\u0bb1\u0bc1\u0bb5\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b8e\u0ba8\u0bcd\u0ba4 \u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1\u0bb5\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb2\u0bbe\u0bae\u0bcd. \u0b8e\u0bb4\u0bc1\u0ba4\u0bcd\u0ba4\u0bc1\u0bb0\u0bc1 \u0baa\u0bc7\u0bae\u0bbf\u0bb2\u0bbf \u0baa\u0bc6\u0baf\u0bb0\u0bc8 \u0b87\u0b99\u0bcd\u0b95\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3\u0bbf\u0b9f\u0bb5\u0bc1\u0bae\u0bcd, \u0b89\u0bb2\u0bbe\u0bb5\u0bbf \u0b85\u0ba4\u0bc8 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bbe\u0b95 \u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd."}}')},function(e){e.exports=JSON.parse('{"title":"\u0b9a\u0bc6\u0baf\u0bb2\u0bcd\u0b95\u0bb3\u0bcd","disclaimer":"\u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0b87\u0bb2\u0bcd \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0ba4 \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b89\u0bb2\u0bbe\u0bb5\u0bbf\u0baf\u0bbf\u0ba9\u0bcd \u0b89\u0bb3\u0bcd\u0bb3\u0bc2\u0bb0\u0bcd \u0b9a\u0bc7\u0bae\u0bbf\u0baa\u0bcd\u0baa\u0b95\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bcd \u0ba4\u0bbe\u0ba9\u0bbe\u0b95\u0bb5\u0bc7 \u0b9a\u0bc7\u0bae\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0bae\u0bcd. \u0ba4\u0bb0\u0bb5\u0bc1 \u0b8e\u0ba4\u0bc1\u0bb5\u0bc1\u0bae\u0bcd \u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc7\u0bb1\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8, \u0b8e\u0ba9\u0bb5\u0bc7 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0ba4\u0b95\u0bb5\u0bb2\u0bcd \u0bae\u0bc1\u0bb1\u0bcd\u0bb1\u0bbf\u0bb2\u0bc1\u0bae\u0bcd \u0baa\u0bbe\u0ba4\u0bc1\u0b95\u0bbe\u0baa\u0bcd\u0baa\u0bbe\u0ba9\u0ba4\u0bc1.","importExport":{"heading":"\u0b87\u0bb1\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0ba4\u0bbf/\u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf","body":"\u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0ba4\u0bb0\u0bb5\u0bc8 JSON \u0bb5\u0b9f\u0bbf\u0bb5\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bcd \u0b87\u0bb1\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0ba4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bb2\u0bbe\u0bae\u0bcd \u0b85\u0bb2\u0bcd\u0bb2\u0ba4\u0bc1 \u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bb2\u0bbe\u0bae\u0bcd. \u0b87\u0ba4\u0ba9\u0bcd \u0bae\u0bc2\u0bb2\u0bae\u0bcd, \u0b8e\u0ba8\u0bcd\u0ba4\u0bb5\u0bca\u0bb0\u0bc1 \u0b9a\u0bbe\u0ba4\u0ba9\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1\u0bae\u0bcd \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bb5\u0bbf\u0ba3\u0bcd\u0ba3\u0baa\u0bcd\u0baa\u0ba4\u0bcd\u0ba4\u0bc8 \u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0bb2\u0bbe\u0bae\u0bcd \u0bae\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd \u0b85\u0b9a\u0bcd\u0b9a\u0bbf\u0b9f\u0bb2\u0bbe\u0bae\u0bcd. \u0baa\u0bbf\u0ba9\u0bcd\u0ba9\u0bb0\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4 \u0b87\u0ba8\u0bcd\u0ba4 \u0b95\u0bcb\u0baa\u0bcd\u0baa\u0bc8 \u0b9a\u0bc7\u0bae\u0bbf\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd.","buttons":{"import":"\u0b87\u0bb1\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0ba4\u0bbf","export":"\u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf"}},"downloadResume":{"heading":"\u0b9f\u0bb5\u0bc1\u0ba9\u0bcd\u0bb2\u0bcb\u0b9f\u0bcd \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7","body":"\u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 PDF \u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc8 \u0b9f\u0bb5\u0bc1\u0ba9\u0bcd\u0bb2\u0bcb\u0b9f\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf \u0b95\u0bc0\u0bb4\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3 \u0baa\u0bca\u0ba4\u0bcd\u0ba4\u0bbe\u0ba9\u0bc8\u0b95\u0bcd \u0b95\u0bbf\u0bb3\u0bbf\u0b95\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0bb2\u0bbe\u0bae\u0bcd. \u0b9a\u0bbf\u0bb1\u0ba8\u0bcd\u0ba4 \u0bae\u0bc1\u0b9f\u0bbf\u0bb5\u0bc1\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bc1, Google Chrome \u0b87\u0ba9\u0bcd \u0b9a\u0bae\u0bc0\u0baa\u0ba4\u0bcd\u0ba4\u0bbf\u0baf \u0baa\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb5\u0bc1\u0bae\u0bcd.","buttons":{"saveAsPdf":"PDF \u0b8e\u0ba9 \u0b9a\u0bc7\u0bae\u0bbf\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd"}},"loadDemoData":{"heading":"\u0b9f\u0bc6\u0bae\u0bcb \u0ba4\u0b95\u0bb5\u0bb2\u0bcd\u0b95\u0bb3\u0bcd \u0b8f\u0bb1\u0bcd\u0bb1\u0bb5\u0bc1\u0bae\u0bcd","body":"\u0baa\u0bc1\u0ba4\u0bbf\u0baf \u0bb5\u0bc6\u0bb1\u0bcd\u0bb1\u0bc1 \u0baa\u0b95\u0bcd\u0b95\u0ba4\u0bcd\u0ba4\u0bc1\u0b9f\u0ba9\u0bcd \u0b8e\u0ba9\u0bcd\u0ba9 \u0b9a\u0bc6\u0baf\u0bcd\u0bb5\u0ba4\u0bc1 \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf\u0ba4\u0bcd\u0ba4\u0bc1 \u0ba4\u0bc6\u0bb3\u0bbf\u0bb5\u0bbe\u0b95 \u0ba4\u0bc6\u0bb0\u0bbf\u0baf\u0bb5\u0bbf\u0bb2\u0bcd\u0bb2\u0bc8\u0baf\u0bbe? \u0b92\u0bb0\u0bc1 \u0bb0\u0bc6\u0b9a\u0bc1\u0bae\u0bc7 \u0b8e\u0bb5\u0bcd\u0bb5\u0bbe\u0bb1\u0bc1 \u0b87\u0bb0\u0bc1\u0b95\u0bcd\u0b95 \u0bb5\u0bc7\u0ba3\u0bcd\u0b9f\u0bc1\u0bae\u0bcd \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bc8\u0b95\u0bcd \u0b95\u0bbe\u0ba3 \u0b9a\u0bbf\u0bb2 \u0b9f\u0bc6\u0bae\u0bcb \u0ba4\u0bb0\u0bb5\u0bc8 \u0bae\u0bc1\u0ba9\u0bcd \u0bae\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bbf\u0b9f\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0bae\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bc1\u0b9f\u0ba9\u0bcd \u0b8f\u0bb1\u0bcd\u0bb1\u0bb5\u0bc1\u0bae\u0bcd, \u0b85\u0b99\u0bcd\u0b95\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1 \u0ba4\u0bbf\u0bb0\u0bc1\u0ba4\u0bcd\u0ba4\u0ba4\u0bcd \u0ba4\u0bca\u0b9f\u0b99\u0bcd\u0b95\u0bb2\u0bbe\u0bae\u0bcd.","buttons":{"loadData":"\u0ba4\u0b95\u0bb5\u0bb2\u0bcd\u0b95\u0bb3\u0bcd \u0b8f\u0bb1\u0bcd\u0bb1\u0bb5\u0bc1\u0bae\u0bcd"}},"reset":{"heading":"\u0b8e\u0bb2\u0bcd\u0bb2\u0bbe\u0bb5\u0bb1\u0bcd\u0bb1\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0bae\u0bbe\u0bb1\u0bcd\u0bb1\u0bb5\u0bc1\u0bae\u0bcd!","body":"\u0b87\u0ba8\u0bcd\u0ba4 \u0ba8\u0b9f\u0bb5\u0b9f\u0bbf\u0b95\u0bcd\u0b95\u0bc8 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b8e\u0bb2\u0bcd\u0bb2\u0bbe \u0ba4\u0bb0\u0bb5\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8\u0ba4\u0bcd\u0ba4\u0bc1, \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b89\u0bb2\u0bbe\u0bb5\u0bbf\u0baf\u0bbf\u0ba9\u0bcd \u0b89\u0bb3\u0bcd\u0bb3\u0bc2\u0bb0\u0bcd \u0b9a\u0bc7\u0bae\u0bbf\u0baa\u0bcd\u0baa\u0b95\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b95\u0bbe\u0baa\u0bcd\u0baa\u0bc1\u0baa\u0bcd\u0baa\u0bbf\u0bb0\u0ba4\u0bbf\u0b95\u0bb3\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0b85\u0b95\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0bcd, \u0b8e\u0ba9\u0bb5\u0bc7 \u0b8e\u0bb2\u0bcd\u0bb2\u0bbe\u0bb5\u0bb1\u0bcd\u0bb1\u0bc8\u0baf\u0bc1\u0bae\u0bcd \u0bae\u0bc0\u0b9f\u0bcd\u0b9f\u0bae\u0bc8\u0baa\u0bcd\u0baa\u0ba4\u0bb1\u0bcd\u0b95\u0bc1 \u0bae\u0bc1\u0ba9\u0bcd\u0baa\u0bc1 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0ba4\u0b95\u0bb5\u0bb2\u0bc8 \u0b8f\u0bb1\u0bcd\u0bb1\u0bc1\u0bae\u0ba4\u0bbf \u0b9a\u0bc6\u0baf\u0bcd\u0ba4\u0bc1\u0bb3\u0bcd\u0bb3\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bcd \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bc8 \u0b89\u0bb1\u0bc1\u0ba4\u0bbf\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bb5\u0bc1\u0bae\u0bcd.","buttons":{"reset":"\u0bae\u0bbe\u0bb1\u0bcd\u0bb1"}}}')},function(e){e.exports=JSON.parse('{"title":"\u0b85\u0bae\u0bc8\u0baa\u0bcd\u0baa\u0bc1\u0b95\u0bb3\u0bcd","language":{"label":"\u0bae\u0bca\u0bb4\u0bbf","helpText":"\u0baa\u0baf\u0ba9\u0bcd\u0baa\u0bbe\u0b9f\u0bcd\u0b9f\u0bc8 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b9a\u0bca\u0ba8\u0bcd\u0ba4 \u0bae\u0bca\u0bb4\u0bbf\u0baf\u0bbf\u0bb2\u0bcd \u0bae\u0bca\u0bb4\u0bbf\u0baa\u0bc6\u0baf\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0b89\u0ba4\u0bb5 \u0bb5\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bbf\u0ba9\u0bbe\u0bb2\u0bcd, <1>Translation Documentation</1> \u0baa\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95\u0bb5\u0bc1\u0bae\u0bcd."}}')},function(e){e.exports=JSON.parse('{"title":"\u0baa\u0bb1\u0bcd\u0bb1\u0bbf","documentation":{"heading":"\u0b9f\u0bbe\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bc6\u0ba3\u0bcd\u0b9f\u0bb7\u0ba9\u0bcd","body":"\u0baa\u0baf\u0ba9\u0bcd\u0baa\u0bbe\u0b9f\u0bcd\u0b9f\u0bc8\u0baa\u0bcd \u0baa\u0bb1\u0bcd\u0bb1\u0bbf \u0bae\u0bc7\u0bb2\u0bc1\u0bae\u0bcd \u0b85\u0bb1\u0bbf\u0baf \u0bb5\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bc1\u0b95\u0bbf\u0bb1\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bbe? \u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0ba4\u0bcd\u0ba4\u0bbf\u0bb1\u0bcd\u0b95\u0bc1 \u0b8e\u0bb5\u0bcd\u0bb5\u0bbe\u0bb1\u0bc1 \u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bbf\u0baa\u0bcd\u0baa\u0ba4\u0bc1 \u0b8e\u0ba9\u0bcd\u0baa\u0ba4\u0bc1 \u0b95\u0bc1\u0bb1\u0bbf\u0ba4\u0bcd\u0ba4 \u0ba4\u0b95\u0bb5\u0bb2\u0bcd \u0ba4\u0bc7\u0bb5\u0bc8\u0baf\u0bbe? \u0bae\u0bc7\u0bb2\u0bc1\u0bae\u0bcd \u0baa\u0bbe\u0bb0\u0bcd\u0b95\u0bcd\u0b95 \u0bb5\u0bc7\u0ba3\u0bcd\u0b9f\u0bbe\u0bae\u0bcd, \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bc1\u0b95\u0bcd\u0b95\u0bbe\u0b95 \u0b89\u0bb0\u0bc1\u0bb5\u0bbe\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f \u0b92\u0bb0\u0bc1 \u0bb5\u0bbf\u0bb0\u0bbf\u0bb5\u0bbe\u0ba9 \u0bb5\u0bb4\u0bbf\u0b95\u0bbe\u0b9f\u0bcd\u0b9f\u0bbf \u0b87\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0bb1\u0ba4\u0bc1.","buttons":{"documentation":"\u0b9f\u0bbe\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bc6\u0ba3\u0bcd\u0b9f\u0bb7\u0ba9\u0bcd"}},"bugOrFeatureRequest":{"heading":"\u0baa\u0bc1\u0b95\u0bcd? \u0baa\u0bbf\u0b9f\u0bcd\u0b9f\u0bc1\u0bb1\u0bc7 \u0bb0\u0bc6\u0b83\u0b89\u0b8e\u0bb8\u0bcd\u0ba4\u0bcd?","body":"\u0bae\u0bb1\u0bc1\u0ba4\u0bca\u0b9f\u0b95\u0bcd\u0b95\u0bae\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0bb5\u0ba4\u0bbf\u0bb2\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1 \u0b89\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bae\u0bc1\u0ba9\u0bcd\u0ba9\u0bc7\u0bb1\u0bcd\u0bb1\u0ba4\u0bcd\u0ba4\u0bc8 \u0b8f\u0ba4\u0bc7\u0ba9\u0bc1\u0bae\u0bcd \u0ba4\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bbf\u0bb1\u0ba4\u0bbe? \u0bb5\u0bc6\u0bb3\u0bbf\u0baf\u0bc7\u0bb1\u0bbe\u0ba4 \u0b92\u0bb0\u0bc1 \u0ba4\u0bca\u0bb2\u0bcd\u0bb2\u0bc8 \u0baa\u0bbf\u0bb4\u0bc8 \u0b95\u0bbf\u0b9f\u0bc8\u0ba4\u0bcd\u0ba4\u0ba4\u0bbe? \u0b95\u0bbf\u0b9f\u0bcd\u0bb9\u0baa\u0bcd \u0b9a\u0bbf\u0b95\u0bcd\u0b95\u0bb2\u0bcd\u0b95\u0bb3\u0bcd \u0baa\u0bbf\u0bb0\u0bbf\u0bb5\u0bbf\u0bb2\u0bcd \u0b87\u0ba4\u0bc8\u0baa\u0bcd \u0baa\u0bb1\u0bcd\u0bb1\u0bbf \u0baa\u0bc7\u0b9a\u0bc1\u0b99\u0bcd\u0b95\u0bb3\u0bcd, \u0b85\u0bb2\u0bcd\u0bb2\u0ba4\u0bc1 \u0b95\u0bc0\u0bb4\u0bc7\u0baf\u0bc1\u0bb3\u0bcd\u0bb3 \u0b9a\u0bc6\u0baf\u0bb2\u0bcd\u0b95\u0bb3\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bbf \u0b8e\u0ba9\u0b95\u0bcd\u0b95\u0bc1\u0bae\u0bcd \u0bae\u0bbf\u0ba9\u0bcd\u0ba9\u0b9e\u0bcd\u0b9a\u0bb2\u0bcd \u0b85\u0ba9\u0bc1\u0baa\u0bcd\u0baa\u0bb5\u0bc1\u0bae\u0bcd.","buttons":{"raiseIssue":" \u0b9a\u0bbf\u0b95\u0bcd\u0b95\u0bb2\u0bc8  \u0b95\u0bbe\u0b9f\u0bcd\u0b9f\u0bc1\\n","sendEmail":"\u0b88\u0bae\u0bc6\u0baf\u0bbf\u0bb2\u0bcd \u0b85\u0ba9\u0bc1\u0baa\u0bcd\u0baa\u0bc1\u0b95"}},"sourceCode":{"heading":"\u0bae\u0bc2\u0bb2 \u0b95\u0bbe\u0b9f\u0bc7","body":"\u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0ba4\u0bcd\u0ba4\u0bc8 \u0b85\u0ba4\u0ba9\u0bcd \u0bae\u0bc2\u0bb2\u0ba4\u0bcd\u0ba4\u0bbf\u0bb2\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1 \u0b87\u0baf\u0b95\u0bcd\u0b95 \u0bb5\u0bbf\u0bb0\u0bc1\u0bae\u0bcd\u0baa\u0bc1\u0b95\u0bbf\u0bb1\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bbe? \u0b87\u0ba8\u0bcd\u0ba4 \u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0ba4\u0bcd\u0ba4\u0bbf\u0ba9\u0bcd \u0ba4\u0bbf\u0bb1\u0ba8\u0bcd\u0ba4 \u0bae\u0bc2\u0bb2 \u0bae\u0bc7\u0bae\u0bcd\u0baa\u0bbe\u0b9f\u0bcd\u0b9f\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0baa\u0b99\u0bcd\u0b95\u0bb3\u0bbf\u0b95\u0bcd\u0b95 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b92\u0bb0\u0bc1 \u0b9f\u0bc6\u0bb5\u0bb2\u0baa\u0bcd\u0baa\u0bb0\u0bbe? \u0b95\u0bc0\u0bb4\u0bc7 \u0b89\u0bb3\u0bcd\u0bb3 \u0baa\u0bca\u0ba4\u0bcd\u0ba4\u0bbe\u0ba9\u0bc8\u0b95\u0bcd \u0b95\u0bbf\u0bb3\u0bbf\u0b95\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0b95.","buttons":{"githubRepo":"\u0b95\u0bc1\u0ba4\u0bc1\u0baa\u0bcd \u0bb0\u0bc6\u0baa\u0bcb"}},"license":{"heading":"\u0b89\u0bb0\u0bbf\u0bae\u0ba4\u0bcd \u0ba4\u0b95\u0bb5\u0bb2\u0bcd","body":"\u0b87\u0ba8\u0bcd\u0ba4 \u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0bae\u0bcd MIT \u0b89\u0bb0\u0bbf\u0bae\u0ba4\u0bcd\u0ba4\u0bbf\u0ba9\u0bcd \u0b95\u0bc0\u0bb4\u0bcd \u0ba8\u0bbf\u0bb0\u0bcd\u0bb5\u0b95\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0b95\u0bbf\u0bb1\u0ba4\u0bc1, \u0b85\u0ba4\u0bc8 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0b95\u0bc0\u0bb4\u0bc7 \u0bae\u0bc7\u0bb2\u0bc1\u0bae\u0bcd \u0baa\u0b9f\u0bbf\u0b95\u0bcd\u0b95\u0bb2\u0bbe\u0bae\u0bcd. \u0b85\u0b9f\u0bbf\u0baa\u0bcd\u0baa\u0b9f\u0bc8\u0baf\u0bbf\u0bb2\u0bcd, \u0b85\u0b9a\u0bb2\u0bcd \u0b86\u0b9a\u0bbf\u0bb0\u0bbf\u0baf\u0bb0\u0bc1\u0b95\u0bcd\u0b95\u0bc1 \u0ba8\u0bc0\u0b99\u0bcd\u0b95\u0bb3\u0bcd \u0bb5\u0bb0\u0bb5\u0bc1\u0b95\u0bb3\u0bc8 \u0bb5\u0bb4\u0b99\u0bcd\u0b95\u0bbf\u0ba9\u0bbe\u0bb2\u0bcd, \u0b8e\u0b99\u0bcd\u0b95\u0bbf\u0bb0\u0bc1\u0ba8\u0bcd\u0ba4\u0bc1\u0bae\u0bcd \u0ba4\u0bbf\u0b9f\u0bcd\u0b9f\u0ba4\u0bcd\u0ba4\u0bc8\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4 \u0b85\u0ba9\u0bc1\u0bae\u0ba4\u0bbf\u0b95\u0bcd\u0b95\u0baa\u0bcd\u0baa\u0b9f\u0bc1\u0bb5\u0bc0\u0bb0\u0bcd\u0b95\u0bb3\u0bcd.","buttons":{"mitLicense":"MIT \u0bb2\u0bbf\u0b9a\u0bc6\u0ba9\u0bcd\u0bb8\u0bcd"}},"footer":{"credit":"<1>Amruth Pillai<1>\u0baf\u0bbe\u0bb2\u0bcd \u0b85\u0ba9\u0bcd\u0baa\u0bbe\u0bb2\u0bcd \u0b9a\u0bc6\u0baf\u0bcd\u0baf\u0baa\u0bcd\u0baa\u0b9f\u0bcd\u0b9f\u0ba4\u0bc1","thanks":"Reactive Resume \u0b90\u0baa\u0bcd \u0baa\u0baf\u0ba9\u0bcd\u0baa\u0b9f\u0bc1\u0ba4\u0bcd\u0ba4\u0bbf\u0baf\u0ba4\u0bb1\u0bcd\u0b95\u0bc1 \u0ba8\u0ba9\u0bcd\u0bb1\u0bbf!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Add {{- heading}}","startDate":{"label":"Start Date"},"endDate":{"label":"End Date"},"description":{"label":"Description"}},"buttons":{"add":{"label":"Add"},"delete":{"label":"Delete"}},"printDialog":{"heading":"Download Your Resume","quality":{"label":"Quality"},"printType":{"label":"Type","types":{"unconstrained":"Unconstrained","fitInA4":"Fit in A4","multiPageA4":"Multi-Page A4"}},"helpText":["This export method makes use of HTML canvas to convert the resume to an image and print it on a PDF, which means it will lose all selecting/parsing capabilities.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"Download Your Resume","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u0414\u043e\u0434\u0430\u0442\u0438 {{- heading}}","startDate":{"label":"\u0414\u0430\u0442\u0430 \u043f\u043e\u0447\u0430\u0442\u043a\u0443"},"endDate":{"label":"\u0414\u0430\u0442\u0430 \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u043d\u044f"},"description":{"label":"\u041e\u043f\u0438\u0441"}},"buttons":{"add":{"label":"\u0414\u043e\u0434\u0430\u0442\u0438"},"delete":{"label":"Delete"}},"printDialog":{"heading":"\u0417\u0430\u0432\u0430\u043d\u0442\u0430\u0436\u0438\u0442\u0438 \u0440\u0435\u0437\u044e\u043c\u0435","quality":{"label":"\u042f\u043a\u0456\u0441\u0442\u044c"},"printType":{"label":"\u0422\u0438\u043f","types":{"unconstrained":"\u041d\u0435 \u0432\u0438\u0437\u043d\u0430\u0447\u0435\u043d\u043e","fitInA4":"\u0412\u043c\u0456\u0441\u0442\u0438\u0442\u0438 \u0432 A4","multiPageA4":"Multi-Page A4"}},"helpText":["\u0426\u0435\u0439 \u043c\u0435\u0442\u043e\u0434 \u0435\u043a\u0441\u043f\u043e\u0440\u0442\u0443 \u0432\u0438\u043a\u043e\u0440\u0438\u0441\u0442\u043e\u0432\u0443\u0454 HTML canvas \u0434\u043b\u044f \u043f\u0435\u0440\u0435\u0442\u0432\u043e\u0440\u0435\u043d\u043d\u044f \u0440\u0435\u0437\u044e\u043c\u0435 \u0432 \u0437\u043e\u0431\u0440\u0430\u0436\u0435\u043d\u043d\u044f \u0442\u0430 \u0434\u0440\u0443\u043a\u0443\u0432\u0430\u043d\u043d\u044f \u0439\u043e\u0433\u043e \u043d\u0430 PDF, \u0446\u0435 \u0437\u043d\u0430\u0447\u0438\u0442\u044c, \u0449\u043e \u0432\u0456\u043d \u0432\u0442\u0440\u0430\u0442\u0438\u0442\u044c \u0432\u0441\u0456 \u043c\u043e\u0436\u043b\u0438\u0432\u043e\u0441\u0442\u0456 \u0432\u0456\u0434\u0431\u043e\u0440\u0443/\u043f\u0430\u0440\u0441\u0456\u043d\u0433\u0443.","If that is important to you, please try printing the resume instead, using Cmd/Ctrl + P or the print button below. The result may vary as the output is browser dependent, but it is known to work best on the latest version of Google Chrome."],"buttons":{"cancel":"Cancel","saveAsPdf":"Save as PDF"}},"panZoomAnimation":{"helpText":"You can pan and zoom around the artboard at any time to get a closer look at your resume."},"markdownHelpText":"You can use <1>GitHub Flavored Markdown</1> to style this section of the text."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"Photo URL"},"firstName":{"label":"First Name"},"lastName":{"label":"Last Name"},"subtitle":{"label":"Subtitle"},"address":{"label":"Address","line1":{"label":"Address Line 1"},"line2":{"label":"Address Line 2"},"line3":{"label":"Address Line 3"}},"phone":{"label":"Phone Number"},"website":{"label":"Website"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"Objective"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"role":{"label":"Role"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"major":{"label":"Major"},"grade":{"label":"Grade"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Title"},"subtitle":{"label":"Subtitle"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Name"},"subtitle":{"label":"Authority"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Name"},"level":{"label":"Level"},"rating":{"label":"Rating"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"Name"},"position":{"label":"Position"},"phone":{"label":"Phone Number"},"email":{"label":"Email Address"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"Key"},"value":{"label":"Value"}}')},function(e){e.exports=JSON.parse('{"title":"Templates"}')},function(e){e.exports=JSON.parse('{"title":"Colors","colorOptions":"Color Options","primaryColor":"Primary Color","accentColor":"Secondary Color","clipboardCopyAction":"{{color}} has been copied to the clipboard."}')},function(e){e.exports=JSON.parse('{"title":"Fonts","fontFamily":{"label":"Font Family","helpText":"You can use any font that is installed on your system as well. Just enter the name of the font family here and the browser would load it up for you."}}')},function(e){e.exports=JSON.parse('{"title":"Actions","disclaimer":"Changes you make to your resume are saved automatically to your browser\'s local storage. No data gets out, hence your information is completely secure.","importExport":{"heading":"Import/Export","body":"You can import or export your data in JSON format. With this, you can edit and print your resume from any device. Save this file for later use.","buttons":{"import":"Import","export":"Export"}},"downloadResume":{"heading":"\u0417\u0430\u0432\u0430\u043d\u0442\u0430\u0436\u0438\u0442\u0438 \u0440\u0435\u0437\u044e\u043c\u0435","body":"You can click on the button below to download a PDF version of your resume instantly. For best results, please use the latest version of Google Chrome.","buttons":{"saveAsPdf":"Save as PDF"}},"loadDemoData":{"heading":"Load Demo Data","body":"Unclear on what to do with a fresh blank page? Load some demo data with prepopulated values to see how a resume should look and you can start editing from there.","buttons":{"loadData":"Load Data"}},"reset":{"heading":"Reset Everything!","body":"This action will reset all your data and remove backups made to your browser\'s local storage as well, so please make sure you have exported your information before you reset everything.","buttons":{"reset":"Reset"}}}')},function(e){e.exports=JSON.parse('{"title":"Settings","language":{"label":"Language","helpText":"If you would like to help translate the app into your own language, please refer to the <1>Translation Documentation</1>."}}')},function(e){e.exports=JSON.parse('{"title":"About","documentation":{"heading":"Documentation","body":"Want to know more about the app? Need information on how to contribute to the project? Look no further, there\'s a comprehensive guide made just for you.","buttons":{"documentation":"Documentation"}},"bugOrFeatureRequest":{"heading":"Bug? Feature Request?","body":"Something halting your progress from making a resume? Found a pesky bug that just won\'t quit? Talk about it on the GitHub Issues section, or send me and email using the actions below.","buttons":{"raiseIssue":"Raise an Issue","sendEmail":"Send an Email"}},"sourceCode":{"heading":"Source Code","body":"Want to run the project from its source? Are you a developer willing to contribute to the open-source development of this project? Click the button below.","buttons":{"githubRepo":"GitHub Repo"}},"license":{"heading":"License Information","body":"The project is governed under the MIT License, which you can read more about below. Basically, you are allowed to use the project anywhere provided you give credits to the original author.","buttons":{"mitLicense":"MIT License"}},"footer":{"credit":"Made with Love by <1>Amruth Pillai</1>","thanks":"Thank you for using Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"Th\xeam {{- heading}}","startDate":{"label":"B\u1eaft \u0111\u1ea7u"},"endDate":{"label":"K\u1ebft th\xfac"},"description":{"label":"M\xf4 t\u1ea3"}},"buttons":{"add":{"label":"Th\xeam"},"delete":{"label":"X\xf3a"}},"printDialog":{"heading":"T\u1ea3i B\u1ea3n tr\xedch ngang v\u1ec1","quality":{"label":"Ch\u1ea5t l\u01b0\u1ee3ng"},"printType":{"label":"Ki\u1ec3u","types":{"unconstrained":"T\u1ef1 do","fitInA4":"V\u1eeba trang A4","multiPageA4":"Nhi\u1ec1u trang A4"}},"helpText":["Ph\u01b0\u01a1ng ph\xe1p xu\u1ea5t n\xe0y x\u1eed d\u1ee5ng HTML canvas \u0111\u1ec3 chuy\u1ec3n d\u1ea1ng b\u1ea3n tr\xedch ngang th\xe0nh m\u1ed9t \u1ea3nh r\u1ed3i in n\xf3 tr\xean m\u1ed9t PDF, \u0111i\u1ec1u n\xe0y c\xf3 ngh\u0129a l\xe0 n\xf3 s\u1ebd m\u1ea5t t\u1ea5t c\u1ea3 c\xe1c t\xednh n\u0103ng ch\u1ecdn l\u1ef1a (selecting)/ph\xe2n t\xedch (parsing).","N\u1ebfu vi\u1ec7c c\xf3 c\xe1c t\xednh n\u0103ng \u0111\xf3 quan tr\u1ecdng \u0111\u1ed1i v\u1edbi b\u1ea1n, h\xe3y th\u1eed in b\u1ea3n tr\xedch ngang, b\u1eb1ng ph\xedm Cmd/Ctrl + P ho\u1eb7c n\xfat in d\u01b0\u1edbi \u0111\xe2y. K\u1ebft qu\u1ea3 c\xf3 th\u1ec3 sai kh\xe1c v\xec \u0111\u1ea7u ra ph\u1ee5 thu\u1ed9c v\xe0o tr\xecnh duy\u1ec7t, nh\u01b0ng n\xf3 s\u1ebd ho\u1ea1t \u0111\u1ed9ng t\u1ed1t nh\u1ea5t tr\xean phi\xean b\u1ea3n Google Chrome m\u1edbi nh\u1ea5t."],"buttons":{"cancel":"H\u1ee7y b\u1ecf","saveAsPdf":"L\u01b0u th\xe0nh PDF"}},"panZoomAnimation":{"helpText":"B\u1ea1n c\xf3 th\u1ec3 k\xe9o v\xe0 thu ph\xf3ng b\u1ea3n thi\u1ebft k\u1ebf b\u1ea5t k\xec l\xfac n\xe0o \u0111\u1ec3 xem b\u1ea3n tr\xedch ngang c\u1ee7a m\xecnh r\xf5 h\u01a1n."},"markdownHelpText":"B\u1ea1n c\xf3 th\u1ec3 d\xf9ng <1>c\xfa ph\xe1p Markdown c\u1ee7a GitHub</1> \u0111\u1ec3 t\u1ea1o ki\u1ec3u cho ch\u1eef \u1edf ph\u1ea7n n\xe0y."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"URL \u1ea2nh"},"firstName":{"label":"T\xean"},"lastName":{"label":"H\u1ecd"},"subtitle":{"label":"Ch\u1ee9c danh"},"address":{"label":"\u0110\u1ecba ch\u1ec9","line1":{"label":"\u0110\u1ecba ch\u1ec9, D\xf2ng 1"},"line2":{"label":"\u0110\u1ecba ch\u1ec9, D\xf2ng 2"},"line3":{"label":"\u0110\u1ecba ch\u1ec9, D\xf2ng 3"}},"phone":{"label":"S\u1ed1 \u0110i\u1ec7n tho\u1ea1i"},"website":{"label":"Trang web"},"email":{"label":"\u0110\u1ecba ch\u1ec9 Email"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"M\u1ee5c ti\xeau"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"T\xean"},"role":{"label":"V\u1ecb tr\xed"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"T\xean"},"major":{"label":"Ng\xe0nh"},"grade":{"label":"\u0110i\u1ec3m"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"Ti\xeau \u0111\u1ec1"},"subtitle":{"label":"Ti\xeau \u0111\u1ec1 ph\u1ee5"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"T\xean"},"subtitle":{"label":"Trao b\u1edfi"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"T\xean"},"level":{"label":"Tr\xecnh \u0111\u1ed9"},"rating":{"label":"\u0110\xe1nh gi\xe1"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"T\xean"},"position":{"label":"Ch\u1ee9c v\u1ee5"},"phone":{"label":"S\u1ed1 \u0110i\u1ec7n tho\u1ea1i"},"email":{"label":"\u0110\u1ecba ch\u1ec9 Email"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"T\xean tr\u01b0\u1eddng"},"value":{"label":"N\u1ed9i dung tr\u01b0\u1eddng"}}')},function(e){e.exports=JSON.parse('{"title":"Ki\u1ec3u m\u1eabu"}')},function(e){e.exports=JSON.parse('{"title":"M\xe0u s\u1eafc","colorOptions":"L\u1ef1a ch\u1ecdn M\xe0u","primaryColor":"M\xe0u Ch\xednh","accentColor":"M\xe0u Ph\u1ee5","clipboardCopyAction":"{{color}} \u0111\xe3 \u0111\u01b0\u1ee3c ch\xe9p v\xe0o b\u1ea3ng nh\xe1p (clipboard)."}')},function(e){e.exports=JSON.parse('{"title":"Ph\xf4ng ch\u1eef","fontFamily":{"label":"H\u1ecd Ph\xf4ng ch\u1eef","helpText":"B\u1ea1n c\u0169ng c\xf3 th\u1ec3 d\xf9ng b\u1ea5t k\xec ph\xf4ng n\xe0o \u0111\xe3 c\xe0i tr\xean m\xe1y c\u1ee7a b\u1ea1n. Ch\u1ec9 c\u1ea7n \u0111i\u1ec1n t\xean c\u1ee7a h\u1ecd ph\xf4ng \u0111\xf3 v\xe0o \u0111\xe2y v\xe0 tr\xecnh duy\u1ec7t s\u1ebd t\u1ea3i n\xf3 l\xean cho b\u1ea1n."}}')},function(e){e.exports=JSON.parse('{"title":"Thao t\xe1c","disclaimer":"Nh\u1eefng g\xec b\u1ea1n thay \u0111\u1ed5i trong b\u1ea3n tr\xedch ngang c\u1ee7a m\xecnh \u0111\u01b0\u1ee3c t\u1ef1 \u0111\u1ed9ng l\u01b0u v\xe0o v\xf9ng l\u01b0u tr\u1eef c\u1ee5c b\u1ed9 (local storage) c\u1ee7a tr\xecnh duy\u1ec7t. Kh\xf4ng ch\xfat d\u1eef li\u1ec7u n\xe0o tho\xe1t ra ngo\xe0i, n\xean th\xf4ng tin c\u1ee7a b\u1ea1n ho\xe0n to\xe0n \u0111\u01b0\u1ee3c b\u1ea3o v\u1ec7.","importExport":{"heading":"Nh\u1eadp/Xu\u1ea5t","body":"B\u1ea1n c\xf3 th\u1ec3 nh\u1eadp ho\u1eb7c xu\u1ea5t d\u1eef li\u1ec7u c\u1ee7a m\xecnh \u1edf d\u1ea1ng JSON. B\u1eb1ng c\xe1ch n\xe0y, b\u1ea1n c\xf3 th\u1ec3 ch\u1ec9nh s\u1eeda v\xe0 in b\u1ea3n tr\xedch ngang c\u1ee7a m\xecnh tr\xean b\u1ea5t k\xec thi\u1ebft b\u1ecb n\xe0o. L\u01b0u t\u1ec7p n\xe0y l\u1ea1i \u0111\u1ec3 d\xf9ng v\u1ec1 sau.","buttons":{"import":"Nh\u1eadp","export":"Xu\u1ea5t"}},"downloadResume":{"heading":"T\u1ea3i B\u1ea3n tr\xedch ngang v\u1ec1","body":"B\u1ea1n c\xf3 th\u1ec3 \u1ea5n n\xfat d\u01b0\u1edbi \u0111\xe2y \u0111\u1ec3 t\u1ea3i v\u1ec1 ngay b\u1ea3n tr\xedch ngang \u1edf d\u1ea1ng PDF. \u0110\u1ec3 c\xf3 k\u1ebft qu\u1ea3 t\u1ed1t nh\u1ea5t, h\xe3y x\u1eed d\u1ee5ng Google Chrome b\u1ea3n m\u1edbi nh\u1ea5t.","buttons":{"saveAsPdf":"L\u01b0u th\xe0nh PDF"}},"loadDemoData":{"heading":"T\u1ea3i D\u1eef li\u1ec7u Minh h\u1ecda","body":"Kh\xf4ng r\xf5 n\xean l\xe0m g\xec v\u1edbi m\u1ed9t trang tr\u1ed1ng tr\u01a1n? T\u1ea3i v\xe0i d\u1eef li\u1ec7u minh h\u1ecda v\u1edbi c\xe1c gi\xe1 tr\u1ecb \u0111\u1eb7t s\u1eb5n \u0111\u1ec3 th\u1ea5y m\u1ed9t b\u1ea3n tr\xedch ngang tr\xf4ng th\u1ebf n\xe0o v\xe0 t\u1eeb \u0111\xf3 b\u1ea1n c\xf3 th\u1ec3 b\u1eaft \u0111\u1ea7u ch\u1ec9nh s\u1eeda.","buttons":{"loadData":"T\u1ea3i D\u1eef li\u1ec7u"}},"reset":{"heading":"\u0110\u1eb7t l\u1ea1i T\u1ea5t c\u1ea3!","body":"Thao t\xe1c n\xe0y s\u1ebd \u0111\u1eb7t l\u1ea1i t\u1ea5t c\u1ea3 d\u1eef li\u1ec7u c\u1ee7a b\u1ea1n v\xe0 x\xf3a c\u1ea3 c\xe1c b\u1ea3n sao l\u01b0u v\xf9ng l\u01b0u tr\u1eef c\u1ee5c b\u1ed9 (local storage) c\u1ee7a tr\xecnh duy\u1ec7t, n\xean h\xe3y ch\u1eafc ch\u1eafn l\xe0 b\u1ea1n \u0111\xe3 xu\u1ea5t th\xf4ng tin ra tr\u01b0\u1edbc khi \u0111\u1eb7t l\u1ea1i m\u1ecdi th\u1ee9.","buttons":{"reset":"\u0110\u1eb7t l\u1ea1i"}}}')},function(e){e.exports=JSON.parse('{"title":"C\xe0i \u0111\u1eb7t","language":{"label":"Ng\xf4n ng\u1eef","helpText":"N\u1ebfu b\u1ea1n mu\u1ed1n gi\xfap d\u1ecbch \u1ee9ng d\u1ee5ng n\xe0y sang ng\xf4n ng\u1eef c\u1ee7a m\xecnh, xin h\xe3y tra c\u1ee9u <1>H\u01b0\u1edbng d\u1eabn D\u1ecbch thu\u1eadt</1>."}}')},function(e){e.exports=JSON.parse('{"title":"Gi\u1edbi thi\u1ec7u","documentation":{"heading":"T\xe0i li\u1ec7u h\u01b0\u1edbng d\u1eabn","body":"Mu\u1ed1n bi\u1ebft th\xeam v\u1ec1 \u1ee9ng d\u1ee5ng n\xe0y? C\u1ea7n th\xf4ng tin v\u1ec1 c\xe1ch \u0111\xf3ng g\xf3p cho d\u1ef1 \xe1n? Kh\xf4ng ph\u1ea3i t\xecm th\xeam n\u1eefa, c\xf3 m\u1ed9t h\u01b0\u1edbng d\u1eabn \u0111\u1ea7y \u0111\u1ee7 d\xe0nh cho b\u1ea1n.","buttons":{"documentation":"T\xe0i li\u1ec7u h\u01b0\u1edbng d\u1eabn"}},"bugOrFeatureRequest":{"heading":"L\u1ed7i? \u0110\u1ec1 xu\u1ea5t T\xednh n\u0103ng?","body":"C\xf3 g\xec \u0111\xf3 g\xe2y c\u1ea3n tr\u1edf vi\u1ec7c b\u1ea1n l\xe0m b\u1ea3n tr\xedch ngang? T\xecm ra m\u1ed9t l\u1ed7i phi\u1ec1n h\xe0 dai d\u1eb3ng? N\xf3i v\u1ec1 chuy\u1ec7n \u0111\xf3 \u1edf m\u1ee5c Issues tr\xean GitHub, ho\u1eb7c g\u1eedi cho t\xf4i m\u1ed9t email, b\u1eb1ng c\xe1c thao t\xe1c d\u01b0\u1edbi \u0111\xe2y.","buttons":{"raiseIssue":"\u0110\u01b0a ra V\u1ea5n \u0111\u1ec1","sendEmail":"G\u1eedi Email"}},"sourceCode":{"heading":"M\xe3 Ngu\u1ed3n","body":"Mu\u1ed1n ch\u1ea1y d\u1ef1 \xe1n t\u1eeb m\xe3 ngu\u1ed3n? L\xe0 m\u1ed9t l\u1eadp tr\xecnh vi\xean mu\u1ed1n \u0111\xf3ng g\xf3p cho vi\u1ec7c ph\xe1t tri\u1ec3n ngu\u1ed3n m\u1edf c\u1ee7a d\u1ef1 \xe1n n\xe0y? \u1ea4n n\xfat d\u01b0\u1edbi \u0111\xe2y.","buttons":{"githubRepo":"Kho tr\xean GitHub"}},"license":{"heading":"Th\xf4ng tin Gi\u1ea5y ph\xe9p","body":"D\u1ef1 \xe1n n\xe0y \u0111\u01b0\u1ee3c \u0111i\u1ec1u ch\u1ec9nh b\u1edfi Gi\u1ea5y ph\xe9p MIT, b\u1ea1n c\xf3 th\u1ec3 \u0111\u1ecdc th\xeam v\u1ec1 n\xf3 d\u01b0\u1edbi \u0111\xe2y. V\u1ec1 c\u01a1 b\u1ea3n, b\u1ea1n \u0111\u01b0\u1ee3c s\u1eed d\u1ee5ng d\u1ef1 \xe1n n\xe0y \u1edf b\u1ea5t k\xec \u0111\xe2u, ch\u1ec9 c\u1ea7n b\u1ea1n c\xf4ng nh\u1eadn t\xe1c gi\u1ea3 g\u1ed1c.","buttons":{"mitLicense":"Gi\u1ea5y ph\xe9p MIT"}},"footer":{"credit":"\u0110\u01b0\u1ee3c t\u1ea1o ra, b\u1eb1ng T\xecnh c\u1ea3m, b\u1edfi <1>Amruth Pillai</1>.","thanks":"C\u1ea3m \u01a1n b\u1ea1n \u0111\xe3 x\u1eed d\u1ee5ng Reactive Resume!"}}')},function(e){e.exports=JSON.parse('{"item":{"add":"\u6dfb\u52a0 {{- heading}}","startDate":{"label":"\u5f00\u59cb\u65e5\u671f"},"endDate":{"label":"\u7ed3\u675f\u65e5\u671f"},"description":{"label":"\u8bf4\u660e"}},"buttons":{"add":{"label":"\u6dfb\u52a0"},"delete":{"label":"\u5220\u9664"}},"printDialog":{"heading":"\u4e0b\u8f7d\u7b80\u5386","quality":{"label":"\u54c1\u8d28"},"printType":{"label":"\u7c7b\u578b","types":{"unconstrained":"\u4e0d\u53d7\u9650\u5236","fitInA4":"\u9002\u5408A4","multiPageA4":"\u591a\u9875A4"}},"helpText":["\u6b64\u5bfc\u51fa\u65b9\u6cd5\u4f7f\u7528 HTML \u753b\u5e03\u5c06\u6062\u590d\u8f6c\u6362\u4e3a\u56fe\u50cf\u5e76\u6253\u5370\u5230 PDF \u4e0a\u3002 \u8fd9\u610f\u5473\u7740\u5b83\u5c06\u5931\u53bb\u6240\u6709\u9009\u62e9/\u89e3\u6790\u80fd\u529b\u3002","\u5982\u679c\u8fd9\u5bf9\u60a8\u5f88\u91cd\u8981\uff0c\u8bf7\u5c1d\u8bd5\u6253\u5370\u7eed\u7248\uff0c\u800c\u4e0d\u662f\u4f7f\u7528 Cmd/Ctrl + P \u6216\u4e0b\u9762\u7684\u6253\u5370\u6309\u94ae\u3002 \u7ed3\u679c\u53ef\u80fd\u4f1a\u56e0\u4e3a\u8f93\u51fa\u4f9d\u8d56\u4e8e\u6d4f\u89c8\u5668\u800c\u6709\u6240\u6539\u53d8\uff0c\u4f46\u5b83\u5df2\u77e5\u6700\u9002\u5408\u6700\u65b0\u7248\u672c\u7684Google Chrome\u3002"],"buttons":{"cancel":"\u53d6\u6d88","saveAsPdf":"\u4fdd\u5b58\u4e3a PDF"}},"panZoomAnimation":{"helpText":"\u60a8\u53ef\u4ee5\u968f\u65f6\u5728\u753b\u677f\u4e0a\u5e73\u79fb\u548c\u7f29\u653e\uff0c\u4ee5\u66f4\u4ed4\u7ec6\u5730\u67e5\u770b\u7b80\u5386\u3002"},"markdownHelpText":"\u4f60\u53ef\u4ee5\u4f7f\u7528 <1>GitHub \u503e\u5411\u7684 Markdown</1> \u6765\u7f8e\u5316\u8fd9\u90e8\u5206\u6587\u5b57."}')},function(e){e.exports=JSON.parse('{"photoUrl":{"label":"\u7167\u7247\u94fe\u63a5"},"firstName":{"label":"\u540d"},"lastName":{"label":"\u59d3"},"subtitle":{"label":"\u804c\u4f4d"},"address":{"label":"\u5730\u5740","line1":{"label":"\u5730\u5740\u680f 1"},"line2":{"label":"\u5730\u5740\u680f 2"},"line3":{"label":"\u5730\u5740\u680f 3"}},"phone":{"label":"\u7535\u8bdd\u53f7\u7801"},"website":{"label":"\u7f51\u7ad9"},"email":{"label":"\u90ae\u7bb1\u5730\u5740"}}')},function(e){e.exports=JSON.parse('{"objective":{"label":"\u6c42\u804c\u610f\u5411"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u516c\u53f8"},"role":{"label":"\u804c\u4f4d"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u5b66\u6821"},"major":{"label":"\u4e3b\u4fee\u8bfe\u7a0b"},"grade":{"label":"\u5b66\u5206"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u540d\u79f0"},"subtitle":{"label":"\u5956\u9879"}}')},function(e){e.exports=JSON.parse('{"title":{"label":"\u540d\u79f0"},"subtitle":{"label":"\u9881\u53d1\u673a\u6784"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u540d\u79f0"},"level":{"label":"\u7ea7\u522b"},"rating":{"label":"\u7b49\u7ea7"}}')},function(e){e.exports=JSON.parse('{"name":{"label":"\u540d\u5b57"},"position":{"label":"\u804c\u4f4d"},"phone":{"label":"\u7535\u8bdd\u53f7\u7801"},"email":{"label":"\u90ae\u7bb1\u5730\u5740"}}')},function(e){e.exports=JSON.parse('{"key":{"label":"\u540d\u79f0"},"value":{"label":"\u5185\u5bb9"}}')},function(e){e.exports=JSON.parse('{"title":"\u6a21\u677f"}')},function(e){e.exports=JSON.parse('{"title":"\u989c\u8272","colorOptions":"\u989c\u8272\u9009\u9879","primaryColor":"\u4e3b\u8272\u8c03","accentColor":"\u8f85\u52a9\u989c\u8272","clipboardCopyAction":"{{color}} \u5df2\u88ab\u590d\u5236\u5230\u526a\u8d34\u677f\u3002"}')},function(e){e.exports=JSON.parse('{"title":"\u5b57\u4f53","fontFamily":{"label":"\u5b57\u4f53\u5e93","helpText":"\u60a8\u4e5f\u53ef\u4ee5\u4f7f\u7528\u5b89\u88c5\u5728\u60a8\u7684\u7cfb\u7edf\u4e0a\u7684\u4efb\u4f55\u5b57\u4f53\u3002 \u53ea\u9700\u5728\u6b64\u8f93\u5165\u5b57\u4f53\u5e93\u7684\u540d\u5b57\uff0c\u6d4f\u89c8\u5668\u5c06\u4e3a\u60a8\u52a0\u8f7d\u5b83\u3002"}}')},function(e){e.exports=JSON.parse('{"title":"\u64cd\u4f5c","disclaimer":"\u60a8\u5bf9\u60a8\u7684\u7b80\u5386\u6240\u4f5c\u7684\u66f4\u6539\u5c06\u81ea\u52a8\u4fdd\u5b58\u5230\u60a8\u7684\u6d4f\u89c8\u5668\u672c\u5730\u5b58\u50a8\u3002\u6ca1\u6709\u6570\u636e\u6d41\u51fa\uff0c\u56e0\u6b64\u60a8\u7684\u4fe1\u606f\u662f\u5b8c\u5168\u5b89\u5168\u7684\u3002","importExport":{"heading":"\u5bfc\u5165/\u5bfc\u51fa","body":"\u60a8\u53ef\u4ee5\u4ee5 JSON \u683c\u5f0f\u5bfc\u5165\u6216\u5bfc\u51fa\u60a8\u7684\u6570\u636e\u3002 \u8fd9\u6837\uff0c\u60a8\u53ef\u4ee5\u4ece\u4efb\u4f55\u8bbe\u5907\u4e0a\u7f16\u8f91\u548c\u6253\u5370\u60a8\u7684\u7b80\u5386\u3002\u4fdd\u5b58\u6b64\u6587\u4ef6\u4f9b\u4ee5\u540e\u4f7f\u7528\u3002","buttons":{"import":"\u5bfc\u5165","export":"\u5bfc\u51fa"}},"downloadResume":{"heading":"\u4e0b\u8f7d\u7b80\u5386","body":"\u60a8\u53ef\u4ee5\u5355\u51fb\u4e0b\u9762\u7684\u6309\u94ae\u7acb\u5373\u4e0b\u8f7d\u7b80\u5386\u7684PDF\u7248\u672c\u3002 \u4e3a\u4e86\u83b7\u5f97\u6700\u4f73\u6548\u679c\uff0c\u8bf7\u4f7f\u7528\u6700\u65b0\u7248\u672c\u7684Google Chrome\u3002","buttons":{"saveAsPdf":"\u4fdd\u5b58\u4e3a PDF"}},"loadDemoData":{"heading":"\u52a0\u8f7d\u6a21\u7248\u6570\u636e","body":"\u4e0d\u6e05\u695a\u7528\u65b0\u7684\u7a7a\u767d\u9875\u9762\u505a\u4ec0\u4e48\uff1f\u52a0\u8f7d\u4e00\u4e9b\u5e26\u6709\u9884\u7f6e\u503c\u7684\u865a\u62df\u6570\u636e\u6765\u67e5\u770b\u7b80\u5386\u7684\u683c\u5f0f\uff0c\u60a8\u53ef\u4ee5\u4ece\u90a3\u91cc\u5f00\u59cb\u7f16\u8f91\u3002","buttons":{"loadData":"\u8f7d\u5165\u6570\u636e"}},"reset":{"heading":"\u5168\u90e8\u91cd\u7f6e!","body":"\u6b64\u64cd\u4f5c\u5c06\u91cd\u7f6e\u60a8\u6240\u6709\u7684\u6570\u636e\u5e76\u5220\u9664\u6d4f\u89c8\u5668\u672c\u5730\u5b58\u50a8\u5907\u4efd\uff0c\u8bf7\u786e\u4fdd\u60a8\u5728\u91cd\u5236\u524d\u5df2\u7ecf\u5bfc\u51fa\u60a8\u7684\u4fe1\u606f\u3002","buttons":{"reset":"\u91cd\u7f6e"}}}')},function(e){e.exports=JSON.parse('{"title":"\u8bbe\u7f6e","language":{"label":"\u8bed\u8a00","helpText":"\u5982\u679c\u60a8\u5e0c\u671b\u5e2e\u52a9\u5c06\u5e94\u7528\u7a0b\u5e8f\u7ffb\u8bd1\u6210\u60a8\u81ea\u5df1\u7684\u8bed\u8a00\uff0c\u8bf7\u53c2\u9605<1>\u7ffb\u8bd1\u6587\u6863</1>\u3002"}}')},function(e){e.exports=JSON.parse('{"title":"\u5173\u4e8e\u6211\u4eec","documentation":{"heading":"\u6587\u6863","body":"\u60f3\u8fdb\u4e00\u6b65\u4e86\u89e3\u8be5\u5e94\u7528\u7a0b\u5e8f\uff1f \u9700\u8981\u6709\u5173\u5982\u4f55\u4e3a\u8be5\u9879\u76ee\u505a\u51fa\u8d21\u732e\u7684\u4fe1\u606f\uff1f \u522b\u65e0\u6240\u6c42\uff0c\u8fd9\u91cc\u6709\u4e13\u95e8\u4e3a\u60a8\u51c6\u5907\u7684\u7efc\u5408\u6307\u5357\u3002","buttons":{"documentation":"\u6587\u6863"}},"bugOrFeatureRequest":{"heading":"\u9519\u8bef\uff1f\u529f\u80fd\u8bf7\u6c42\uff1f","body":"\u5728\u4f60\u5199\u7b80\u5386\u7684\u65f6\u5019\u53d1\u73b0\u9047\u5230\u56f0\u96be\uff1f\u53d1\u73b0\u4e86\u4e00\u4e2a\u70e6\u4eba\u7684\u95ee\u9898\uff1f \u4f60\u53ef\u4ee5\u4e0aGithub\u95ee\u9898\u7248\u6765\u8c08\u8bba\u8fd9\u4e2a\u95ee\u9898\uff0c\u6216\u8005\u901a\u8fc7\u4e0b\u9762\u7684\u6309\u94ae\u6765\u7ed9\u6211\u53d1\u9001\u7535\u5b50\u90ae\u4ef6\u3002","buttons":{"raiseIssue":"\u63d0\u4ea4\u6539\u8fdb","sendEmail":"\u53d1\u9001\u90ae\u4ef6"}},"sourceCode":{"heading":"\u6e90\u7801","body":"\u60f3\u8981\u672c\u5730\u8fd0\u884c\u8fd9\u4e2a\u9879\u76ee\uff1f \u4f60\u662f\u4e0d\u662f\u4e00\u4e2a\u5f00\u53d1\u8005\u60f3\u8981\u4e3a\u8fd9\u4e2a\u5f00\u6e90\u9879\u76ee\u505a\u51fa\u8d21\u732e\uff1f\u8bf7\u70b9\u51fb\u4e0b\u9762\u7684\u6309\u94ae\u3002","buttons":{"githubRepo":"GitHub \u9879\u76ee"}},"license":{"heading":"\u6388\u6743\u7533\u660e","body":"\u8be5\u9879\u76ee\u53d7MIT\u8bb8\u53ef\u7684\u7ba1\u7406\uff0c\u60a8\u53ef\u4ee5\u5728\u4e0b\u9762\u9605\u8bfb\u66f4\u591a\u4fe1\u606f\u3002 \u57fa\u672c\u4e0a\uff0c\u53ea\u8981\u60a8\u5411\u539f\u4f5c\u8005\u63d0\u4f9b\u611f\u8c22\uff0c\u60a8\u5c31\u53ef\u4ee5\u5728\u4efb\u4f55\u5730\u65b9\u4f7f\u7528\u8be5\u9879\u76ee\u3002","buttons":{"mitLicense":"MIT \u6388\u6743\u8bb8\u53ef"}},"footer":{"credit":"<1>Amruth Pillai</1> \u7528\u7231\u5236\u9020","thanks":"\u611f\u8c22\u60a8\u4f7f\u7528 Reactive Resume\uff01"}}')},,,,function(e){e.exports=JSON.parse('{"data":{"jsonld":{"@context":["https://jsonldresume.github.io/skill/context.json",{"gender":{"@id":"schema:gender","@type":"@vocab"},"skill:classOfAward":{"@id":"skill:classOfAward","@type":"@vocab"},"skill:securityClearance":{"@id":"skill:securityClearance","@type":"@vocab"},"category":{"@id":"schema:category","@type":"@vocab"},"dayOfWeek":{"@id":"schema:dayOfWeek","@type":"@vocab"}}],"@graph":[{"@type":"skill:Resume","@id":"_:9c9dc430-987a-4bf8-9b7e-326705d1723e?resume"},{"@type":"Person","@id":"nancyontheweb.com","givenName":[{"@value":"Nancy","@language":"en"}],"additionalName":[],"familyName":[{"@value":"Jackson","@language":"en"}],"description":"Full-stack Developer","honorificPrefix":[],"birthDate":"","birthPlace":{},"nationality":[],"height":{},"weight":{},"address":[],"hasCredential":[{"@type":"EducationalOccupationalCredential","educationalLevel":"","@id":"_:hasCredential__0","credentialCategory":"degree","dateCreated":"Aug 2002","abstract":"","aggregateRating":{"@id":"_:hasCredential__0#gpa","@type":"aggregateRating","name":"GPA","ratingValue":"7.2 CGPA","itemReviewed":{"@id":"_:hasCredential__0"}},"about":{"@id":"_:hasCredential__0#program","@type":"EducationalOccupationalProgram","startDate":"Sep 2001","endDate":"Aug 2002"}},{"@type":"EducationalOccupationalCredential","educationalLevel":"","@id":"_:hasCredential__1","credentialCategory":"degree","dateCreated":"Aug 2001","abstract":"","aggregateRating":{"@id":"_:hasCredential__1#gpa","@type":"aggregateRating","name":"GPA","ratingValue":"8.4 CGPA","itemReviewed":{"@id":"_:hasCredential__1"}},"about":{"@id":"_:hasCredential__1#program","@type":"EducationalOccupationalProgram","startDate":"Sep 1997","endDate":"Aug 2001"}}]}]},"profile":{"heading":"Profile","photo":"https://i.imgur.com/Icr472Z.jpg","firstName":"Nancy","lastName":"Jackson","subtitle":"Customer Sales Representative","address":{"line1":"3879 Gateway Avenue","line2":"Bakersfield,","line3":"California, USA"},"phone":"******-808-4188","website":"nancyontheweb.com","email":"<EMAIL>"},"address":{"heading":"Address","enable":true},"contacts":{"heading":"Contacts","enable":true},"objective":{"enable":true,"heading":"Professional Objective","body":"To obtain a job within my chosen field that will challenge me and allow me to use my education, skills and past experiences in a way that is mutually beneficial to both myself and my employer and allow for future growth and advancement."},"work":{"enable":true,"heading":"Work Experience","items":[{"id":"a208ec03-76e3-4428-ac5b-e17c3de4ac18","title":"On Point Electronics, NYC, NY","role":"Customer Service Representative","start":"Jan 2013","end":"July 2018","description":"- Organized customer information and account data for business planning and customer service purposes.\\n- Created excel spreadsheets to track customer data and perform intense reconciliation process.\\n- Received 97% positive customer survey results.\\n- Speed on calls was 10% above team average.  \\n**Key Achievement:** Designed and executed an automatized system for following up with customers, increasing customer retention by 22%.","enabled":true,"enable":true},{"id":"bd8649f2-42d1-4424-acaf-a02c08c3322c","title":"Excelsior Communications, NYC, NY","role":"Customer Service Representative","start":"Oct 2009","end":"Dec 2012","description":"- Worked as a full time customer service rep in a high volume call center.\\n- Received \\"Associate of the Month\\" award six times.\\n- Chosen as an example for other associates in trainings.  \\n**Key Achievement:** Received Customer Appreciation bonus in three of four years.","enabled":true,"enable":true},{"id":"dde47711-a7a6-424f-9751-73483a0ef4ed","title":"Pizza Hut, Newark, NJ","role":"Waitress","start":"Aug 2005","end":"Sep 2009","description":"- Worked passionately in customer service in a high volume restaurant.\\n- Completed the FAST customer service training class.\\n- Maintained a high tip average thanks to consistent customer satisfaction.","enabled":true,"enable":true}]},"education":{"enable":true,"heading":"Education","items":[{"id":"624f32ab-2d78-4052-86ad-1354fd41d754","name":"The City College of New York, NYC, NY","major":"MS in Computer Science","start":"Sep 2001","end":"Aug 2002","grade":"7.2 CGPA","description":"","enabled":true,"enable":true},{"id":"71a9852f-ed14-4281-bff2-4db9a2275978","name":"University of California, Berkeley, CA","major":"BS in Computer Science","start":"Sep 1997","end":"Aug 2001","grade":"8.4 CGPA","description":"","enabled":true,"enable":true}]},"awards":{"enable":true,"heading":"Honors & Awards","items":[{"id":"121f0976-18cb-4e46-921d-0e156b6bf7fb","title":"Cast Member of a Musical - Oklahoma","subtitle":"Winter, 2007","description":"","enable":true},{"id":"e5f27346-72ad-4d4f-bab3-726a111e4932","title":"Class Representative to ASB","subtitle":"Fall, 2008","description":"","enable":true},{"id":"f71ba9bc-8c14-46b5-99dd-e1333e9aceb9","title":"Most Improved - Varsity Soccer","subtitle":"Fall, 2007","description":"","enable":true}]},"certifications":{"enable":true,"heading":"Certifications","items":[{"id":"e5170d99-b21d-4131-a7dc-26a4670037f5","title":"CCNP","subtitle":"Cisco Systems","description":"","enable":true},{"id":"788e4042-9ecb-40c5-849d-7688b4e23888","title":"VCP6-DCV","subtitle":"VMWare","description":"","enable":true},{"id":"97a1a8d9-3c03-47fb-93ab-e84f864ffe17","title":"DCUCI 642-999","subtitle":"Cisco Systems","description":"","enable":true}]},"skills":{"enable":true,"heading":"Skills","items":[{"id":"2562d78a-3459-4370-8604-c81b00738db1","skill":"Customer Service Expertise"},{"id":"58c31587-9770-4522-a34c-f5ad92fe33e5","skill":"High-Volume Call Center"},{"id":"7aa9a4b1-a2bb-4bcd-8711-b66c0d246971","skill":"Team Leader/Problem Solver"},{"id":"e7fd33e8-5d77-462d-8115-5be57f52832e","skill":"Call Center Management"},{"id":"7bad2af1-c24d-4e01-b68b-be01cfa784ce","skill":"Teambuilding & Training"},{"id":"64fe1710-c2d1-4f53-922e-a5d751eee967","skill":"Continuous Improvement"}]},"memberships":{"enable":true,"heading":"Memberships","items":[{"id":"dd2efad7-e900-4384-bdc0-b2ab5f62bb71","hobby":"Poetry"},{"id":"96023eb7-8c93-4b1d-b581-b8fc4107351a","hobby":"Travelling"},{"id":"7e5a6168-9cbe-4fe6-b9b9-43a47d8bb15a","hobby":"Beatboxing"},{"id":"dd7f4ffd-9c16-4dbf-8968-1165b9e30db8","hobby":"Sketching"}]},"languages":{"enable":true,"heading":"Languages","items":[{"id":"9d34cfcb-c9f0-4d25-ab27-cf81652dd1d0","key":"English (US)","value":5,"enable":true,"level":"","rating":5},{"id":"3511a86b-7ea9-44ac-8144-6acc7f3bd54f","key":"Spanish","value":4,"enable":true,"rating":4},{"id":"d1e17542-f7cc-473a-aa0e-978765907454","key":"Japanese","value":4,"enable":true,"level":"N4","rating":2},{"id":"b1e8442a-7059-4c6f-8a9c-415383133b0e","key":"German","value":3,"enable":true,"level":"B1","rating":0}]},"references":{"enable":true,"heading":"References","items":[{"id":"ba3662e6-29cb-4a03-9766-b3618d1621f3","name":"Lorraine Beasley","position":"Head of HR, Carson Logistics","phone":"******-808-4188","email":"<EMAIL>","description":"","enable":true},{"id":"62fd3293-0e93-4242-882b-ae19b7865fef","name":"Mikhail Nabakov","position":"Assistant Manager, Bullseye","phone":"******-808-4188","email":"<EMAIL>","description":"","enable":true},{"id":"eaab2e32-8591-497c-8676-d122cf3a4798","name":"Katherine Rose","position":"CEO , DownToPlay","phone":"******-808-4188","email":"<EMAIL>","description":"","enable":true}]},"extras":{"enable":true,"heading":"Additional Information","items":[{"id":"3834a270-2c01-4105-b670-80863c955347","key":"Skype","value":"@NancyJack5436","enable":true},{"id":"b0c4fd85-cfda-421e-bd31-008b9aad1dfe","key":"Hometown","value":"New Jersey, NY","enable":true},{"id":"7f0a4971-9770-4ca7-b135-2b0ccd867879","key":"Membership","value":"Playing Soccer & Guitar","enable":true},{"id":"e17552a2-e7e9-4605-8145-795e2b62c30e","key":"Valid Work Visas","value":"US, UK, EU","enable":true}]}},"theme":{"layout":"castform","font":{"family":"Montserrat"},"colors":{"background":"#ffffff","primary":"#212121","accent":"#f44336"}}}')},,,,,,function(e,t,a){e.exports=a.p+"static/media/preview.a5fc2f27.png"},function(e,t,a){e.exports=a.p+"static/media/preview.f1f46a82.png"},function(e,t,a){e.exports=a.p+"static/media/preview.27f7a093.png"},function(e,t,a){e.exports=a.p+"static/media/preview.115df124.png"},function(e,t,a){e.exports=a.p+"static/media/preview.93d6587b.png"},function(e,t,a){e.exports=a.p+"static/media/preview.3186944c.png"},,,,function(e,t,a){e.exports=a.p+"static/media/panzoom.e912bae3.mp4"},,,,function(e,t,a){e.exports=a(850)},,,,,,function(e,t,a){},function(e,t,a){},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(e,t,a){"use strict";a.r(t);var n=a(0),o=a.n(n),l=a(15),r=a.n(l),i=a(27),s=(a(654),a(60)),c=a(81),u=a(12),d=a(647),m=a(83),p=m,b=a(84),h=a(85),g=a(86),f=a(87),y=a(88),v=a(89),x=a(90),N=a(91),E=a(92),w={profile:b,objective:h,work:g,education:f,awards:y,certifications:v,languages:x,references:N,extras:E},k=a(93),S=a(94),O=a(95),C=a(96),j=a(97),D=a(98),A={templates:k,colors:S,fonts:O,actions:C,settings:j,about:D},T={app:p,leftSidebar:w,rightSidebar:A},J=a(99),P=J,R=a(100),F=a(101),L=a(102),z=a(103),I=a(104),M=a(105),H=a(106),G=a(107),q=a(108),Y={profile:R,objective:F,work:L,education:z,awards:I,certifications:M,languages:H,references:G,extras:q},W=a(109),U=a(110),_=a(111),V=a(112),B=a(113),K=a(114),Z={templates:W,colors:U,fonts:_,actions:V,settings:B,about:K},Q={app:P,leftSidebar:Y,rightSidebar:Z},X=a(115),$=X,ee=a(116),te=a(117),ae=a(118),ne=a(119),oe=a(120),le=a(121),re=a(122),ie=a(123),se=a(124),ce={profile:ee,objective:te,work:ae,education:ne,awards:oe,certifications:le,languages:re,references:ie,extras:se},ue=a(125),de=a(126),me=a(127),pe=a(128),be=a(129),he=a(130),ge={templates:ue,colors:de,fonts:me,actions:pe,settings:be,about:he},fe={app:$,leftSidebar:ce,rightSidebar:ge},ye=a(131),ve=ye,xe=a(132),Ne=a(133),Ee=a(134),we=a(135),ke=a(136),Se=a(137),Oe=a(138),Ce=a(139),je=a(140),De={profile:xe,objective:Ne,work:Ee,education:we,awards:ke,certifications:Se,languages:Oe,references:Ce,extras:je},Ae=a(141),Te=a(142),Je=a(143),Pe=a(144),Re=a(145),Fe=a(146),Le={templates:Ae,colors:Te,fonts:Je,actions:Pe,settings:Re,about:Fe},ze={app:ve,leftSidebar:De,rightSidebar:Le},Ie=a(147),Me=Ie,He=a(148),Ge=a(149),qe=a(150),Ye=a(151),We=a(152),Ue=a(153),_e=a(154),Ve=a(155),Be=a(156),Ke={profile:He,objective:Ge,work:qe,education:Ye,awards:We,certifications:Ue,languages:_e,references:Ve,extras:Be},Ze=a(157),Qe=a(158),Xe=a(159),$e=a(160),et=a(161),tt=a(162),at={templates:Ze,colors:Qe,fonts:Xe,actions:$e,settings:et,about:tt},nt={app:Me,leftSidebar:Ke,rightSidebar:at},ot=a(163),lt=ot,rt=a(164),it=a(165),st=a(166),ct=a(167),ut=a(168),dt=a(169),mt=a(170),pt=a(171),bt=a(172),ht={profile:rt,objective:it,work:st,education:ct,awards:ut,certifications:dt,languages:mt,references:pt,extras:bt},gt=a(173),ft=a(174),yt=a(175),vt=a(176),xt=a(177),Nt=a(178),Et={templates:gt,colors:ft,fonts:yt,actions:vt,settings:xt,about:Nt},wt={app:lt,leftSidebar:ht,rightSidebar:Et},kt=a(179),St=kt,Ot=a(180),Ct=a(181),jt=a(182),Dt=a(183),At=a(184),Tt=a(185),Jt=a(186),Pt=a(187),Rt=a(188),Ft={profile:Ot,objective:Ct,work:jt,education:Dt,awards:At,certifications:Tt,languages:Jt,references:Pt,extras:Rt},Lt=a(189),zt=a(190),It=a(191),Mt=a(192),Ht=a(193),Gt=a(194),qt={templates:Lt,colors:zt,fonts:It,actions:Mt,settings:Ht,about:Gt},Yt={app:St,leftSidebar:Ft,rightSidebar:qt},Wt=a(195),Ut=Wt,_t=a(196),Vt=a(197),Bt=a(198),Kt=a(199),Zt=a(200),Qt=a(201),Xt=a(202),$t=a(203),ea=a(204),ta={profile:_t,objective:Vt,work:Bt,education:Kt,awards:Zt,certifications:Qt,languages:Xt,references:$t,extras:ea},aa=a(205),na=a(206),oa=a(207),la=a(208),ra=a(209),ia=a(210),sa={templates:aa,colors:na,fonts:oa,actions:la,settings:ra,about:ia},ca={app:Ut,leftSidebar:ta,rightSidebar:sa},ua=a(211),da=ua,ma=a(212),pa=a(213),ba=a(214),ha=a(215),ga=a(216),fa=a(217),ya=a(218),va=a(219),xa=a(220),Na={profile:ma,objective:pa,work:ba,education:ha,awards:ga,certifications:fa,languages:ya,references:va,extras:xa},Ea=a(221),wa=a(222),ka=a(223),Sa=a(224),Oa=a(225),Ca=a(226),ja={templates:Ea,colors:wa,fonts:ka,actions:Sa,settings:Oa,about:Ca},Da={app:da,leftSidebar:Na,rightSidebar:ja},Aa=a(227),Ta=Aa,Ja=a(228),Pa=a(229),Ra=a(230),Fa=a(231),La=a(232),za=a(233),Ia=a(234),Ma=a(235),Ha=a(236),Ga={profile:Ja,objective:Pa,work:Ra,education:Fa,awards:La,certifications:za,languages:Ia,references:Ma,extras:Ha},qa=a(237),Ya=a(238),Wa=a(239),Ua=a(240),_a=a(241),Va=a(242),Ba={templates:qa,colors:Ya,fonts:Wa,actions:Ua,settings:_a,about:Va},Ka={app:Ta,leftSidebar:Ga,rightSidebar:Ba},Za=a(243),Qa=Za,Xa=a(244),$a=a(245),en=a(246),tn=a(247),an=a(248),nn=a(249),on=a(250),ln=a(251),rn=a(252),sn={profile:Xa,objective:$a,work:en,education:tn,awards:an,certifications:nn,languages:on,references:ln,extras:rn},cn=a(253),un=a(254),dn=a(255),mn=a(256),pn=a(257),bn=a(258),hn={templates:cn,colors:un,fonts:dn,actions:mn,settings:pn,about:bn},gn={app:Qa,leftSidebar:sn,rightSidebar:hn},fn=a(259),yn=fn,vn=a(260),xn=a(261),Nn=a(262),En=a(263),wn=a(264),kn=a(265),Sn=a(266),On=a(267),Cn=a(268),jn={profile:vn,objective:xn,work:Nn,education:En,awards:wn,certifications:kn,languages:Sn,references:On,extras:Cn},Dn=a(269),An=a(270),Tn=a(271),Jn=a(272),Pn=a(273),Rn=a(274),Fn={templates:Dn,colors:An,fonts:Tn,actions:Jn,settings:Pn,about:Rn},Ln={app:yn,leftSidebar:jn,rightSidebar:Fn},zn=a(275),In=zn,Mn=a(276),Hn=a(277),Gn=a(278),qn=a(279),Yn=a(280),Wn=a(281),Un=a(282),_n=a(283),Vn=a(284),Bn={profile:Mn,objective:Hn,work:Gn,education:qn,awards:Yn,certifications:Wn,languages:Un,references:_n,extras:Vn},Kn=a(285),Zn=a(286),Qn=a(287),Xn=a(288),$n=a(289),eo=a(290),to={templates:Kn,colors:Zn,fonts:Qn,actions:Xn,settings:$n,about:eo},ao={app:In,leftSidebar:Bn,rightSidebar:to},no=a(291),oo=no,lo=a(292),ro=a(293),io=a(294),so=a(295),co=a(296),uo=a(297),mo=a(298),po=a(299),bo=a(300),ho={profile:lo,objective:ro,work:io,education:so,awards:co,certifications:uo,languages:mo,references:po,extras:bo},go=a(301),fo=a(302),yo=a(303),vo=a(304),xo=a(305),No=a(306),Eo={templates:go,colors:fo,fonts:yo,actions:vo,settings:xo,about:No},wo={app:oo,leftSidebar:ho,rightSidebar:Eo},ko=a(307),So=ko,Oo=a(308),Co=a(309),jo=a(310),Do=a(311),Ao=a(312),To=a(313),Jo=a(314),Po=a(315),Ro=a(316),Fo={profile:Oo,objective:Co,work:jo,education:Do,awards:Ao,certifications:To,languages:Jo,references:Po,extras:Ro},Lo=a(317),zo=a(318),Io=a(319),Mo=a(320),Ho=a(321),Go=a(322),qo={templates:Lo,colors:zo,fonts:Io,actions:Mo,settings:Ho,about:Go},Yo={app:So,leftSidebar:Fo,rightSidebar:qo},Wo=a(323),Uo=Wo,_o=a(324),Vo=a(325),Bo=a(326),Ko=a(327),Zo=a(328),Qo=a(329),Xo=a(330),$o=a(331),el=a(332),tl={profile:_o,objective:Vo,work:Bo,education:Ko,awards:Zo,certifications:Qo,languages:Xo,references:$o,extras:el},al=a(333),nl=a(334),ol=a(335),ll=a(336),rl=a(337),il=a(338),sl={templates:al,colors:nl,fonts:ol,actions:ll,settings:rl,about:il},cl={app:Uo,leftSidebar:tl,rightSidebar:sl},ul=a(339),dl=ul,ml=a(340),pl=a(341),bl=a(342),hl=a(343),gl=a(344),fl=a(345),yl=a(346),vl=a(347),xl=a(348),Nl={profile:ml,objective:pl,work:bl,education:hl,awards:gl,certifications:fl,languages:yl,references:vl,extras:xl},El=a(349),wl=a(350),kl=a(351),Sl=a(352),Ol=a(353),Cl=a(354),jl={templates:El,colors:wl,fonts:kl,actions:Sl,settings:Ol,about:Cl},Dl={app:dl,leftSidebar:Nl,rightSidebar:jl},Al=a(355),Tl=Al,Jl=a(356),Pl=a(357),Rl=a(358),Fl=a(359),Ll=a(360),zl=a(361),Il=a(362),Ml=a(363),Hl=a(364),Gl={profile:Jl,objective:Pl,work:Rl,education:Fl,awards:Ll,certifications:zl,languages:Il,references:Ml,extras:Hl},ql=a(365),Yl=a(366),Wl=a(367),Ul=a(368),_l=a(369),Vl=a(370),Bl={templates:ql,colors:Yl,fonts:Wl,actions:Ul,settings:_l,about:Vl},Kl={app:Tl,leftSidebar:Gl,rightSidebar:Bl},Zl=a(371),Ql=Zl,Xl=a(372),$l=a(373),er=a(374),tr=a(375),ar=a(376),nr=a(377),or=a(378),lr=a(379),rr=a(380),ir={profile:Xl,objective:$l,work:er,education:tr,awards:ar,certifications:nr,languages:or,references:lr,extras:rr},sr=a(381),cr=a(382),ur=a(383),dr=a(384),mr=a(385),pr=a(386),br={templates:sr,colors:cr,fonts:ur,actions:dr,settings:mr,about:pr},hr={app:Ql,leftSidebar:ir,rightSidebar:br},gr=a(387),fr=gr,yr=a(388),vr=a(389),xr=a(390),Nr=a(391),Er=a(392),wr=a(393),kr=a(394),Sr=a(395),Or=a(396),Cr={profile:yr,objective:vr,work:xr,education:Nr,awards:Er,certifications:wr,languages:kr,references:Sr,extras:Or},jr=a(397),Dr=a(398),Ar=a(399),Tr=a(400),Jr=a(401),Pr=a(402),Rr={templates:jr,colors:Dr,fonts:Ar,actions:Tr,settings:Jr,about:Pr},Fr={app:fr,leftSidebar:Cr,rightSidebar:Rr},Lr=a(403),zr=Lr,Ir=a(404),Mr=a(405),Hr=a(406),Gr=a(407),qr=a(408),Yr=a(409),Wr=a(410),Ur=a(411),_r=a(412),Vr={profile:Ir,objective:Mr,work:Hr,education:Gr,awards:qr,certifications:Yr,languages:Wr,references:Ur,extras:_r},Br=a(413),Kr=a(414),Zr=a(415),Qr=a(416),Xr=a(417),$r=a(418),ei={templates:Br,colors:Kr,fonts:Zr,actions:Qr,settings:Xr,about:$r},ti={app:zr,leftSidebar:Vr,rightSidebar:ei},ai=a(419),ni=ai,oi=a(420),li=a(421),ri=a(422),ii=a(423),si=a(424),ci=a(425),ui=a(426),di=a(427),mi=a(428),pi={profile:oi,objective:li,work:ri,education:ii,awards:si,certifications:ci,languages:ui,references:di,extras:mi},bi=a(429),hi=a(430),gi=a(431),fi=a(432),yi=a(433),vi=a(434),xi={templates:bi,colors:hi,fonts:gi,actions:fi,settings:yi,about:vi},Ni={app:ni,leftSidebar:pi,rightSidebar:xi},Ei=a(435),wi=Ei,ki=a(436),Si=a(437),Oi=a(438),Ci=a(439),ji=a(440),Di=a(441),Ai=a(442),Ti=a(443),Ji=a(444),Pi={profile:ki,objective:Si,work:Oi,education:Ci,awards:ji,certifications:Di,languages:Ai,references:Ti,extras:Ji},Ri=a(445),Fi=a(446),Li=a(447),zi=a(448),Ii=a(449),Mi=a(450),Hi={templates:Ri,colors:Fi,fonts:Li,actions:zi,settings:Ii,about:Mi},Gi={app:wi,leftSidebar:Pi,rightSidebar:Hi},qi=a(451),Yi=qi,Wi=a(452),Ui=a(453),_i=a(454),Vi=a(455),Bi=a(456),Ki=a(457),Zi=a(458),Qi=a(459),Xi=a(460),$i={profile:Wi,objective:Ui,work:_i,education:Vi,awards:Bi,certifications:Ki,languages:Zi,references:Qi,extras:Xi},es=a(461),ts=a(462),as=a(463),ns=a(464),os=a(465),ls=a(466),rs={templates:es,colors:ts,fonts:as,actions:ns,settings:os,about:ls},is={app:Yi,leftSidebar:$i,rightSidebar:rs},ss=a(467),cs=ss,us={af:T,ar:Q,as:fe,ca:ze,cs:nt,da:wt,de:Yt,el:ca,en:Da,es:Ka,fi:gn,fr:Ln,he:ao,hi:wo,hu:Yo,it:cl,ja:Dl,kn:Kl,ko:hr,ml:Fr,mr:ti,nl:Ni,no:Gi,pa:is,pl:{app:cs,leftSidebar:{profile:a(468),objective:a(469),work:a(470),education:a(471),awards:a(472),certifications:a(473),languages:a(474),references:a(475),extras:a(476)},rightSidebar:{templates:a(477),colors:a(478),fonts:a(479),actions:a(480),settings:a(481),about:a(482)}},pt:{app:a(483),leftSidebar:{profile:a(484),objective:a(485),work:a(486),education:a(487),awards:a(488),certifications:a(489),languages:a(490),references:a(491),extras:a(492)},rightSidebar:{templates:a(493),colors:a(494),fonts:a(495),actions:a(496),settings:a(497),about:a(498)}},ro:{app:a(499),leftSidebar:{profile:a(500),objective:a(501),work:a(502),education:a(503),awards:a(504),certifications:a(505),languages:a(506),references:a(507),extras:a(508)},rightSidebar:{templates:a(509),colors:a(510),fonts:a(511),actions:a(512),settings:a(513),about:a(514)}},ru:{app:a(515),leftSidebar:{profile:a(516),objective:a(517),work:a(518),education:a(519),awards:a(520),certifications:a(521),languages:a(522),references:a(523),extras:a(524)},rightSidebar:{templates:a(525),colors:a(526),fonts:a(527),actions:a(528),settings:a(529),about:a(530)}},sv:{app:a(531),leftSidebar:{profile:a(532),objective:a(533),work:a(534),education:a(535),awards:a(536),certifications:a(537),languages:a(538),references:a(539),extras:a(540)},rightSidebar:{templates:a(541),colors:a(542),fonts:a(543),actions:a(544),settings:a(545),about:a(546)}},ta:{app:a(547),leftSidebar:{profile:a(548),objective:a(549),work:a(550),education:a(551),awards:a(552),certifications:a(553),languages:a(554),references:a(555),extras:a(556)},rightSidebar:{templates:a(557),colors:a(558),fonts:a(559),actions:a(560),settings:a(561),about:a(562)}},tr:{app:a(563),leftSidebar:{profile:a(564),objective:a(565),work:a(566),education:a(567),awards:a(568),certifications:a(569),languages:a(570),references:a(571),extras:a(572)},rightSidebar:{templates:a(573),colors:a(574),fonts:a(575),actions:a(576),settings:a(577),about:a(578)}},uk:{app:a(579),leftSidebar:{profile:a(580),objective:a(581),work:a(582),education:a(583),awards:a(584),certifications:a(585),languages:a(586),references:a(587),extras:a(588)},rightSidebar:{templates:a(589),colors:a(590),fonts:a(591),actions:a(592),settings:a(593),about:a(594)}},vi:{app:a(595),leftSidebar:{profile:a(596),objective:a(597),work:a(598),education:a(599),awards:a(600),certifications:a(601),languages:a(602),references:a(603),extras:a(604)},rightSidebar:{templates:a(605),colors:a(606),fonts:a(607),actions:a(608),settings:a(609),about:a(610)}},zh:{app:a(611),leftSidebar:{profile:a(612),objective:a(613),work:a(614),education:a(615),awards:a(616),certifications:a(617),languages:a(618),references:a(619),extras:a(620)},rightSidebar:{templates:a(621),colors:a(622),fonts:a(623),actions:a(624),settings:a(625),about:a(626)}}},ds=[{code:"ar",name:"Arabic (\u0639\u0631\u0628\u0649)"},{code:"zh",name:"Chinese (\u4e2d\u6587)"},{code:"da",name:"Danish (Dansk)"},{code:"nl",name:"Dutch (Nederlands)"},{code:"en",name:"English (US)"},{code:"fr",name:"French (Fran\xe7ais)"},{code:"de",name:"German (Deutsche)"},{code:"he",name:"Hebrew (\u05e2\u05d1\u05e8\u05d9\u05ea)"},{code:"hi",name:"Hindi (\u0939\u093f\u0928\u094d\u0926\u0940)"},{code:"it",name:"Italian (Italiano)"},{code:"kn",name:"Kannada (\u0c95\u0ca8\u0ccd\u0ca8\u0ca1)"},{code:"pl",name:"Polish (Polskie)"},{code:"pt",name:"Portuguese (Portugu\xeas)"},{code:"ru",name:"Russian (\u0440\u0443\u0441\u0441\u043a\u0438\u0439)"},{code:"es",name:"Spanish (Espa\xf1ol)"},{code:"ta",name:"Tamil (\u0ba4\u0bae\u0bbf\u0bb4\u0bcd)"},{code:"vi",name:"Vietnamese (Ti\u1ebfng Vi\u1ec7t)"}];s.a.use(d.a).use(c.a).use(u.e).init({resources:us,lng:"en",fallbackLng:"en",ns:["app","leftSidebar","rightSidebar"],defaultNS:"app"});s.a,a(655),a(656);var ms=Boolean("localhost"===window.location.hostname||"[::1]"===window.location.hostname||window.location.hostname.match(/^127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/));function ps(e,t){navigator.serviceWorker.register(e).then((function(e){e.onupdatefound=function(){var a=e.installing;null!=a&&(a.onstatechange=function(){"installed"===a.state&&(navigator.serviceWorker.controller?(console.log("New content is available and will be used when all tabs for this page are closed. See https://bit.ly/CRA-PWA."),t&&t.onUpdate&&t.onUpdate(e)):(console.log("Content is cached for offline use."),t&&t.onSuccess&&t.onSuccess(e)))})}})).catch((function(e){console.error("Error during service worker registration:",e)}))}var bs=a(2),hs=a(4),gs=a(22),fs=a.n(gs),ys=a(5),vs=a.n(ys),xs=a(629),Ns=a.n(xs),Es=a(630),ws=a(56),ks=a.n(ws),Ss=a(57),Os=function(e,t,a){var n=e.findIndex((function(e){return e.id===t.id})),o=n+a;if(!(o<0||o===e.length)){var l=[n,o].sort((function(e,t){return e-t}));e.splice(l[0],2,e[l[1]],e[l[0]])}},Cs=function(e){e=e.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i,(function(e,t,a,n){return t+t+a+a+n+n}));var t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);return t?{r:parseInt(t[1],16),g:parseInt(t[2],16),b:parseInt(t[3],16)}:null},js=function(e){return e({type:"save_data"})},Ds=function(e,t,a){e({type:"add_item",payload:{key:t,value:a}}),js(e)},As=null,Ts=function(e,t,a,n){if(!As)return new Promise((function(o){t.current.autoCenter(1),t.current.reset(),As=setTimeout((function(){ks()(e.current,{scale:5,useCORS:!0,allowTaint:!0}).then((function(e){var t=e.toDataURL("image/jpeg",a/100),l=new Ss({orientation:"portrait",unit:"px",format:"unconstrained"===n?[e.width,e.height]:"a4"}),r=l.internal.pageSize.getWidth(),i=l.internal.pageSize.getHeight(),s=r/e.width,c=i/e.height,u=s>c?c:s,d=e.width*u,m=e.height*u,p=0,b=0;"unconstrained"!==n&&(p=(r-d)/2,b=(i-m)/2),l.addImage(t,"JPEG",p,b,d,m,null,"SLOW"),l.save("RxResume_".concat(Date.now(),".pdf")),As=null,o()}))}),250)}))},Js=null,Ps=function(e,t,a){if(!Js)return new Promise((function(n){t.current.autoCenter(1),t.current.reset(),Js=setTimeout((function(){ks()(e.current,{scale:5,useCORS:!0,allowTaint:!0}).then((function(e){var t=e.toDataURL("image/jpeg",a/100),o=new Ss({orientation:"portrait",unit:"px",format:"a4"}),l=o.internal.pageSize.getHeight(),r=o.internal.pageSize.getWidth(),i=e.height*r/e.width,s=0,c=i;for(o.addImage(t,"JPEG",0,s,r,i),c-=l;c>=0;)s=c-i,o.addPage(),o.addImage(t,"JPEG",0,s,r,i),c-=l;o.save("RxResume_".concat(Date.now(),".pdf")),Js=null,n()}))}),250)}))},Rs={data:{jsonld:{"@context":["https://jsonldresume.github.io/skill/context.json",{gender:{"@id":"schema:gender","@type":"@vocab"},"skill:classOfAward":{"@id":"skill:classOfAward","@type":"@vocab"},"skill:securityClearance":{"@id":"skill:securityClearance","@type":"@vocab"},category:{"@id":"schema:category","@type":"@vocab"},dayOfWeek:{"@id":"schema:dayOfWeek","@type":"@vocab"}}],"@graph":[{"@type":"skill:Resume"},{"@type":"Person",givenName:[{"@language":"en","@value":""}],familyName:[{"@language":"en","@value":""}],address:[]}]},profile:{heading:"Profile",photo:"",firstName:"",lastName:"",subtitle:"",address:{line1:"",line2:"",line3:""},phone:"",website:"",email:""},contacts:{enable:!0,heading:"Contacts"},address:{enable:!0,heading:"Address"},objective:{enable:!0,heading:"Objective",body:""},work:{enable:!0,heading:"Work Experience",items:[]},education:{enable:!0,heading:"Education",items:[]},awards:{enable:!0,heading:"Honors & Awards",items:[]},certifications:{enable:!0,heading:"Certifications",items:[]},skills:{enable:!0,heading:"Skills",items:[]},memberships:{enable:!0,heading:"Memberships",items:[]},languages:{enable:!0,heading:"Languages",items:[]},references:{enable:!0,heading:"References",items:[]},extras:{enable:!0,heading:"Personal Information",items:[]}},theme:{layout:"Onyx",font:{family:""},colors:{background:"#ffffff",primary:"#212121",accent:"#f44336"}},settings:{language:"en"}},Fs=function(e,t){var a,n=t.type,o=t.payload,l=JSON.parse(JSON.stringify(e));switch(n){case"migrate_section":return vs()(Object(hs.a)({},l),"data.".concat(o.key),o.value);case"add_item":return(a=fs()(Object(hs.a)({},l),"".concat(o.key),[])).push(o.value),vs()(Object(hs.a)({},l),"".concat(o.key),a);case"delete_item":return a=fs()(Object(hs.a)({},l),"".concat(o.key),[]),Ns()(a,(function(e){return e.id===o.value.id})),vs()(Object(hs.a)({},l),"".concat(o.key),a);case"move_item_up":return a=fs()(Object(hs.a)({},l),"".concat(o.key),[]),Os(a,o.value,-1),vs()(Object(hs.a)({},l),"".concat(o.key),a);case"move_item_down":return a=fs()(Object(hs.a)({},l),"".concat(o.key),[]),Os(a,o.value,1),vs()(Object(hs.a)({},l),"".concat(o.key),a);case"on_input":return vs()(Object(hs.a)({},l),o.key,o.value);case"save_data":return localStorage.setItem("state",JSON.stringify(l)),l;case"import_data":if(null===o)return Rs;for(var r=0,i=Object.keys(Rs.data);r<i.length;r++){var s=i[r];s in o.data||(o.data[s]=Rs.data[s])}return Object(hs.a)({},l,{},o);case"load_demo_data":return Object(hs.a)({},l,{},Es);case"reset":return Rs;default:return l}},Ls=Object(n.createContext)(Rs),zs=Ls.Provider,Is=function(e){var t=e.children,a=Object(n.useReducer)(Fs,Rs),l=Object(bs.a)(a,2),r=l[0],i=l[1];return o.a.createElement(zs,{value:{state:r,dispatch:i}},t)},Ms=(Ls.Consumer,Ls),Hs=o.a.createContext(null),Gs=Hs.Provider,qs=function(e){var t=e.children,a=Object(n.useState)(null),l=Object(bs.a)(a,2),r=l[0],i=l[1],s=Object(n.useState)(null),c=Object(bs.a)(s,2),u=c[0],d=c[1],m=Object(n.useState)(!1),p=Object(bs.a)(m,2),b=p[0],h=p[1];return o.a.createElement(Gs,{value:{pageRef:r,setPageRef:i,panZoomRef:u,setPanZoomRef:d,isPrintDialogOpen:b,setPrintDialogOpen:h}},t)},Ys=(Hs.Consumer,Hs),Ws=a(853),Us=a(631),_s=a(1),Vs=function(e){var t=e.className,a=e.label,n=e.value,l=e.onChange,r=e.options,i=e.optionItem;return o.a.createElement("div",{className:"flex flex-col mb-2 "+t,style:{display:"contents"}},a&&o.a.createElement("label",{className:"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2"},a),o.a.createElement("div",{className:"inline-flex relative w-full bg-gray-200 text-gray-800 rounded py-3 px-4 leading-tight focus:outline-none"},o.a.createElement("select",{className:"block appearance-none w-full bg-gray-200 text-gray-800 focus:outline-none",value:n,onChange:function(e){return l(e.target.value)}},r.map(i)),o.a.createElement("div",{className:"pointer-events-none absolute inset-y-0 right-0 flex justify-center items-center px-2 bg-gray-200"},o.a.createElement("i",{className:"material-icons"},"expand_more"))))},Bs=function(e){var t=e.tabs,a=e.currentTab,n=e.setCurrentTab,l=function(e){var o=t.findIndex((function(e){return e.key===a}));e<0&&o>0&&n(t[o-1].key),e>0&&o<t.length-1&&n(t[o+1].key)};return o.a.createElement("div",{className:"mx-4 mb-6 flex items-center"},o.a.createElement("div",{className:"flex mr-1 cursor-pointer select-none text-gray-600 hover:text-gray-800",onClick:function(){return l(-1)}},o.a.createElement("i",{className:"material-icons"},"chevron_left")),o.a.createElement(Vs,{className:"mb-6",label:"",placeholder:"",value:a,onChange:function(e){n(e)},options:t,optionItem:function(e,t){return o.a.createElement("option",{key:e.key,value:e.key},e.name||"Tab")}}),o.a.createElement("div",{className:"flex ml-1 cursor-pointer select-none text-gray-600 hover:text-gray-800",onClick:function(){return l(1)}},o.a.createElement("i",{className:"material-icons"},"chevron_right")))},Ks=a(632),Zs=a(633),Qs=a(646),Xs=a(648),$s=["en","fr","it","de","ar"],ec=function(e){Object(Xs.a)(a,e);var t=Object(Qs.a)(a);function a(){var e;Object(Ks.a)(this,a);for(var n=arguments.length,l=new Array(n),r=0;r<n;r++)l[r]=arguments[r];return(e=t.call.apply(t,[this].concat(l))).state={editingLanguage:"en"},e.handleMultiTextChange=function(t,a){var n=e.props.value;for(e.props.value&&Array.isArray(e.props.value)||(n=[]);_s.size(n)<=a;)n.push("");n[a]=t,e.props.onChange(n)},e.initAllValues=function(t){var a=e.props.value;e.props.value&&Array.isArray(e.props.value)||(a=[{"@language":t,"@value":""}]);var n=a.findIndex((function(e){return e["@language"]===t}));if(n<0){var o={"@language":t,"@value":""};a.push(o),e.props.onChange(a)}return n=a.findIndex((function(e){return e["@language"]===t}))},e.handleLanguageChange=function(t){e.initAllValues(t),e.setState({editingLanguage:t})},e.handleTextChange=function(t,a){var n=e.initAllValues(t),o=e.props.value;o[n]["@value"]=a,e.props.onChange(o)},e.MultiItem=function(t,a){return o.a.createElement("div",{key:"holder_"+a,style:{display:"flex"}},o.a.createElement("input",{className:"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500",type:e.props.type,disabled:e.props.disabled,value:e.props.value[a],onChange:function(t){return e.handleMultiTextChange(t.target.value,a)},placeholder:e.props.placeholder,key:"input_"+a}),_s.size(e.props.value)<=1?"":o.a.createElement("button",{type:"button",onClick:function(){_s.pullAt(e.props.value,a),e.props.onChange(e.props.value)},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded",key:"button_"+a},o.a.createElement("div",{className:"flex items-center",key:"removeHolder_"+a},o.a.createElement("i",{className:"material-icons font-bold text-base",key:"remove_"+a},"remove"))))},e}return Object(Zs.a)(a,[{key:"render",value:function(){var e=this;return o.a.createElement("div",{className:"w-full flex flex-col ".concat(this.props.className)},this.props.label&&o.a.createElement("label",{className:"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2"},this.props.label),"multilang"===this.props.type?o.a.createElement("div",{style:{display:"flex"}},o.a.createElement("select",{value:this.state.editingLanguage,onChange:function(t){return e.handleLanguageChange(t.target.value)}},function(){var e=[];return $s.forEach((function(t){e.push(o.a.createElement("option",{key:t,value:t},t))})),e}()),o.a.createElement("input",{className:"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500",type:this.props.type,disabled:this.props.disabled,value:this.props.value[this.props.value.findIndex((function(t){return t["@language"]===e.state.editingLanguage}))>=0?this.props.value.findIndex((function(t){return t["@language"]===e.state.editingLanguage})):0]["@value"],onChange:function(t){return e.handleTextChange(e.state.editingLanguage,t.target.value)},placeholder:this.props.placeholder})):"multitext"===this.props.type?o.a.createElement("div",null,this.props.value.map(this.MultiItem),o.a.createElement("div",{key:"holder_main",style:{display:"flex"}},o.a.createElement("button",{type:"button",onClick:function(){e.props.value.push(""),e.props.onChange(e.props.value)},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded",key:"button_main"},o.a.createElement("div",{className:"flex items-center",key:"addHolder_main"},o.a.createElement("i",{className:"material-icons font-bold text-base",key:"add_main"},"add"))))):o.a.createElement("input",{className:"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500",type:this.props.type,disabled:this.props.disabled,value:this.props.value,onChange:function(t){return e.props.onChange(t.target.value)},placeholder:this.props.placeholder}))}}]),a}(o.a.Component),tc=function(e){var t=e.data,a=e.onChange,n=Object(Ws.a)("leftSidebar").t,l=function(e,n,o){var l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=_s.get(t,e+"."+n,null);null===i&&("string"===typeof o||"number"===typeof o?_s.set(t,e+"."+n,""):"object"===typeof o&&(Array.isArray(o)?_s.set(t,e+"."+n,[]):_s.set(t,e+"."+n,{}))),a("data."+e+"."+n,o),r&&a("data."+e+'["@id"]',r),l&&a("data."+e+'["@type"]',l)};return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",placeholder:"Heading",value:t.profile.heading,onChange:function(e){return a("data.profile.heading",e)}}),o.a.createElement("hr",{className:"my-6"}),o.a.createElement(ec,{className:"mb-6",label:n("profile.photoUrl.label"),placeholder:"https://i.imgur.com/...",value:_s.get(t,'jsonld["@graph"][1].image.contentUrl',""),onChange:function(e){l('jsonld["@graph"][1].image',"contentUrl",e,"ImageObject","_:#image")}}),o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},o.a.createElement(ec,{className:"mb-6",label:n("profile.firstName.label"),placeholder:"Jane",value:_s.get(t,"jsonld['@graph'][1].givenName",""),onChange:function(e){return l('jsonld["@graph"][1]',"givenName",e)},type:"multilang"}),o.a.createElement(ec,{className:"mb-6",label:n("profile.lastName.label"),placeholder:"Doe",value:_s.get(t,"jsonld['@graph'][1].familyName",""),onChange:function(e){return l('jsonld["@graph"][1]',"familyName",e)},type:"multilang"})),o.a.createElement(ec,{className:"mb-6",label:n("profile.subtitle.label"),placeholder:"Full-Stack Web Developer",value:_s.get(t,'jsonld["@graph"][1].description',""),onChange:function(e){l('jsonld["@graph"][1]',"description",e)}}),o.a.createElement("hr",{className:"my-6"}),o.a.createElement(ec,{className:"mb-6",label:n("profile.website.label"),placeholder:"janedoe.me",value:_s.get(t,'jsonld["@graph"][1].sameAs',[]),onChange:function(e){return l('jsonld["@graph"][1]',"sameAs",e)},AddItem:function(){},type:"multitext"}))},ac=a(855),nc=function(e){var t=e.checked,a=e.onChange,n=e.icon,l=void 0===n?"check":n,r=e.size,i=void 0===r?"2rem":r;return o.a.createElement("div",{className:"relative bg-white border-2 rounded border-gray-400 hover:border-gray-500 flex flex-shrink-0 justify-center items-center mr-2 focus-within:border-blue-500 cursor-pointer",style:{width:i,height:i}},o.a.createElement("input",{type:"checkbox",style:{width:i,height:i},className:"opacity-0 absolute cursor-pointer z-20",checked:t,onChange:function(e){return a(e.target.checked)}}),o.a.createElement("i",{className:"absolute material-icons ".concat(t?"opacity-100":"opacity-0"," text-sm text-gray-800")},l))},oc=function(e){var t=e.dispatch,a=e.first,n=e.identifier,l=e.item,r=e.last,i=e.onChange,s=e.type,c=e.enableAction,u=Object(Ws.a)().t;return o.a.createElement("div",{className:"flex justify-between"},o.a.createElement("div",{className:"flex items-center"},c?c(n,l,i):o.a.createElement(nc,{size:"2.25rem",checked:l.enable,onChange:function(e){i("".concat(n,"enable"),e)}}),o.a.createElement("button",{type:"button",onClick:function(){return function(e,t,a){e({type:"delete_item",payload:{key:t,value:a}}),js(e)}(t,s,l)},className:"ml-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"delete"),o.a.createElement("span",{className:"text-sm"},u("buttons.delete.label"))))),o.a.createElement("div",{className:"flex"},!a&&o.a.createElement("button",{type:"button",onClick:function(){return function(e,t,a){e({type:"move_item_up",payload:{key:t,value:a}}),js(e)}(t,s,l)},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded mr-2"},o.a.createElement("div",{className:"flex items-center"},o.a.createElement("i",{className:"material-icons font-bold text-base"},"arrow_upward"))),!r&&o.a.createElement("button",{type:"button",onClick:function(){return function(e,t,a){e({type:"move_item_down",payload:{key:t,value:a}}),js(e)}(t,s,l)},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded"},o.a.createElement("div",{className:"flex items-center"},o.a.createElement("i",{className:"material-icons font-bold text-base"},"arrow_downward")))))},lc=function(e){var t=e.onSubmit,a=Object(Ws.a)().t;return o.a.createElement("div",null,o.a.createElement("button",{type:"button",onClick:t,className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"add"),o.a.createElement("span",{className:"text-sm"},a("buttons.add.label")))))},rc=function(e){var t=e.title,a=e.heading,n=e.isOpen,l=e.setOpen,r=Object(Ws.a)().t;return o.a.createElement("div",{className:"flex justify-between items-center cursor-pointer",onClick:function(){return l(!n)}},o.a.createElement("h6",{className:"text-sm font-medium"},"undefined"===typeof a?t:r("item.add",{heading:a})),o.a.createElement("i",{className:"material-icons"},n?"expand_less":"expand_more"))},ic=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=Object(Ws.a)(["leftSidebar","app"]).t;return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",label:r("address.streetAddress.label"),placeholder:"20 Malvin Dr",value:t.streetAddress,onChange:function(e){return a("".concat(l,"streetAddress"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("address.addressLocality.label"),placeholder:"Toronto",value:t.addressLocality,onChange:function(e){return a("".concat(l,"addressLocality"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("address.addressRegion.label"),placeholder:"ON",value:t.addressRegion,onChange:function(e){return a("".concat(l,"addressRegion"),e)}}),o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},o.a.createElement(ec,{className:"mb-6",label:r("address.addressCountry.label"),placeholder:"Canada",value:t.addressCountry,onChange:function(e){return a("".concat(l,"addressCountry"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("address.postalCode.label"),placeholder:"H1H 0H0",value:t.postalCode,onChange:function(e){return a("".concat(l,"postalCode"),e)}})),o.a.createElement(ec,{className:"mb-6",label:r("address.sameAs.label"),placeholder:"Google Map Url of address",value:t.sameAs,onChange:function(e){return a("".concat(l,"sameAs"),e)}}))},sc=function(e){var t=e.heading,a=e.dispatch,l="_:"+Object(ac.a)(),r=Object(n.useState)(!1),i=Object(bs.a)(r,2),s=i[0],c=i[1],u=Object(n.useState)({"@id":l,"@type":"PostalAddress",hoursAvailable:{"@id":l+"#hoursAvailable","@type":"OpeningHoursSpecification",validThrough:"2099-01-01"},addressCountry:"",streetAddress:"",addressRegion:"",addressLocality:"",postalCode:"",contactType:"",sameAs:""}),d=Object(bs.a)(u,2),m=d[0],p=d[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:c,isOpen:s}),o.a.createElement("div",{className:"mt-6 ".concat(s?"block":"hidden")},o.a.createElement(ic,{item:m,onChange:function(e,t){return p(vs()(Object(hs.a)({},m),e,t))}}),o.a.createElement(lc,{onSubmit:function(){var e="_:"+Object(ac.a)();""!==m.addressCountry&&(Ds(a,'data.jsonld["@graph"][1].address',m),p({"@id":e,"@type":"PostalAddress",hoursAvailable:{"@id":e+"#hoursAvailable","@type":"OpeningHoursSpecification",validThrough:"2099-01-01"},addressCountry:"",streetAddress:"",addressRegion:"",addressLocality:"",postalCode:"",contactType:"",sameAs:""}),c(!1))}})))},cc=function(e,t,a){return o.a.createElement(nc,{size:"2.25rem",checked:t&&t.hoursAvailable&&t.hoursAvailable.validThrough&&Date.parse(t.hoursAvailable.validThrough)-Date.parse(new Date)>0,onChange:function(t){var n="1900-01-01";t&&(n="2099-01-01"),a("".concat(e,"hoursAvailable.validThrough"),n)}})},uc=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].address['.concat(a,"].");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:t.streetAddress||t.addressCountry,setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(ic,{item:t,onChange:l,identifier:p}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][1].address",enableAction:cc})))},dc=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:t.address.enable,onChange:function(e){return a("data.address.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:t.address.heading,onChange:function(e){return a("data.address.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),t.jsonld["@graph"][1].address&&t.jsonld["@graph"][1].address.map((function(e,n){return o.a.createElement(uc,{dispatch:l,first:0===n,index:n,item:e,key:e["@id"],last:n===t.jsonld["@graph"][1].address.length-1,onChange:a})})),o.a.createElement(sc,{heading:t.address.heading,dispatch:l}))},mc=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=Object(Ws.a)(["leftSidebar","app"]).t;return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",label:r("profile.phone.label"),placeholder:"+1 (999)999-9999",value:t.telephone,onChange:function(e){return a("".concat(l,"telephone"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("profile.email.label"),placeholder:"<EMAIL>",value:t.email,onChange:function(e){return a("".concat(l,"email"),e)}}),o.a.createElement(Vs,{className:"mb-6",label:r("profile.contactType.label"),placeholder:"Only preferred is shown on resume",value:t.contactType,onChange:function(e){return a("".concat(l,"contactType"),e)},options:["Preferred","Emergency","Other"],optionItem:function(e,t){return o.a.createElement("option",{key:e,value:e},e)}}),o.a.createElement(ec,{className:"mb-6",label:r("profile.contacts.description"),placeholder:"Description",value:t.description,onChange:function(e){return a("".concat(l,"description"),e)}}))},pc=function(e){var t=e.heading,a=e.dispatch,l="_:"+Object(ac.a)(),r=Object(n.useState)(!1),i=Object(bs.a)(r,2),s=i[0],c=i[1],u=Object(n.useState)({"@id":l,"@type":"ContactPoint",description:"",contactType:"Preferred",email:"",telephone:""}),d=Object(bs.a)(u,2),m=d[0],p=d[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:c,isOpen:s}),o.a.createElement("div",{className:"mt-6 ".concat(s?"block":"hidden")},o.a.createElement(mc,{item:m,onChange:function(e,t){return p(vs()(Object(hs.a)({},m),e,t))}}),o.a.createElement(lc,{onSubmit:function(){var e="_:"+Object(ac.a)();""!==m.contactType&&(Ds(a,'data.jsonld["@graph"][1].contactPoint',m),p({"@id":e,"@type":"ContactPoint",description:"",contactType:"Preferred",email:"",telephone:""}),c(!1))}})))},bc=function(e,t,a){return o.a.createElement(o.a.Fragment,null)},hc=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].contactPoint['.concat(a,"].");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:t.contactType||t.telephone,setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(mc,{item:t,onChange:l,identifier:p}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][1].contactPoint",enableAction:bc})))},gc=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:t.contacts.enable,onChange:function(e){return a("data.contacts.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:t.contacts.heading,onChange:function(e){return a("data.contacts.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),t.jsonld["@graph"][1].contactPoint&&t.jsonld["@graph"][1].contactPoint.filter((function(e){return"Preferred"===e.contactType})).map((function(e,n){return o.a.createElement(hc,{dispatch:l,first:0===n,index:n,item:e,key:e["@id"],last:n===t.jsonld["@graph"][1].contactPoint.length-1,onChange:a})})),o.a.createElement(pc,{heading:t.contacts.heading,dispatch:l}))},fc=a(854),yc=function(e){var t=e.className;return o.a.createElement("div",{className:t},o.a.createElement("p",{className:"text-gray-800 text-xs"},o.a.createElement(fc.a,{i18nKey:"markdownHelpText"},"You can use",o.a.createElement("a",{className:"text-blue-600 hover:underline",target:"_blank",rel:"noopener noreferrer",href:"https://github.com/adam-p/markdown-here/wiki/Markdown-Cheatsheet"},"GitHub Flavored Markdown"),"to style this section of text.")))},vc=function(e){var t=e.label,a=e.placeholder,n=e.value,l=e.onChange,r=e.className,i=e.rows,s=void 0===i?5:i;return o.a.createElement("div",{className:"w-full flex flex-col ".concat(r)},o.a.createElement("label",{className:"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2"},t),o.a.createElement("textarea",{className:"appearance-none block leading-7 w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 focus:outline-none focus:bg-white focus:border-gray-500",rows:s,value:n,onChange:function(e){return l(e.target.value)},placeholder:a}),o.a.createElement(yc,{className:"mt-2"}))},xc=function(e){var t=e.item,a=e.onChange;t||(t={});var n=Object(Ws.a)(["leftSidebar","app"]).t;return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",label:n("address.addressLocality.label"),placeholder:"Toronto",value:_s.get(t,"addressLocality",""),onChange:function(e){return a("addressLocality",e)}}),o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},o.a.createElement(ec,{className:"mb-6",label:n("address.addressRegion.label"),placeholder:"ON",value:_s.get(t,"addressRegion",""),onChange:function(e){return a("addressRegion",e)}}),o.a.createElement(ec,{className:"mb-6",label:n("address.addressCountry.label"),placeholder:"Canada",value:_s.get(t,"addressCountry",""),onChange:function(e){return a("addressCountry",e)}})))},Nc=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=e.index,i=void 0===r?0:r,s=Object(Ws.a)(["leftSidebar","app"]).t;return o.a.createElement("div",null,0===i?o.a.createElement(vc,{rows:"15",className:"mb-4",label:s("objective.objective.label"),value:_s.get(t,"description",""),placeholder:"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector.",onChange:function(e){return a("".concat(l,"description"),e)}}):o.a.createElement(o.a.Fragment,null),o.a.createElement(ec,{className:"mb-6",label:s("objective.availabilityStarts.label"),placeholder:"2022-01-01",value:_s.get(t,"availabilityStarts",""),onChange:function(e){return a("".concat(l,"availabilityStarts"),e)}}),o.a.createElement(ec,{className:"mb-6",label:s("objective.availabilityEnds.label"),placeholder:"2022-12-01",item:_s.get(t,"availabilityEnds",""),onChange:function(e){return a("".concat(l,"availabilityEnds"),e)}}),o.a.createElement(xc,{item:_s.get(t,"availableAtOrFrom.address",{}),onChange:function(e,n){return function(e,n,o){if(e&&n){var r=_s.get(o,"address",{});r["@type"]||(r["@type"]="PostalAddress",r["@id"]=t["@id"]+"_availableAtOrFrom_address"),r[n]=e,_s.set(o,"address",r),o["@type"]||(o["@type"]="Place",o["@id"]=t["@id"]+"_availableAtOrFrom"),a("".concat(l,"availableAtOrFrom"),o)}}(n,e,t.availableAtOrFrom)}}))},Ec=function(e){var t=e.heading,a=e.dispatch,l=e.size,r="_:"+Object(ac.a)(),i=Object(n.useState)(!1),s=Object(bs.a)(i,2),c=s[0],u=s[1],d=Object(n.useState)({"@id":r,"@type":"Demand",description:"",availabilityStarts:"",availabilityEnds:"",availableAtOrFrom:{},deliveryLeadTime:{}}),m=Object(bs.a)(d,2),p=m[0],b=m[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:u,isOpen:c}),o.a.createElement("div",{className:"mt-6 ".concat(c?"block":"hidden")},o.a.createElement(Nc,{item:p,onChange:function(e,t){return b(vs()(Object(hs.a)({},p),e,t))},index:l}),o.a.createElement(lc,{onSubmit:function(){var e="_:"+Object(ac.a)();""===p.description&&p.availableAtOrFrom===[]||(Ds(a,'data.jsonld["@graph"][1].seeks',p),b({"@id":e,"@type":"Demand",description:"",availabilityStarts:"",availabilityEnds:"",availableAtOrFrom:{},deliveryLeadTime:{}}),u(!1))}})))},wc=function(e,t,a){return o.a.createElement(nc,{size:"2.25rem",checked:t&&t.availabilityEnds&&Date.parse(t.availabilityEnds)-Date.parse(new Date)>0,onChange:function(t){var n="1900-01-01";t&&(n="2099-01-01"),a("".concat(e,"availabilityEnds"),n)}})},kc=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].seeks['.concat(a,"].");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:t.availableAtOrFrom.address.addressCountry||t.description.substring(0,10)+"...",setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(Nc,{item:t,onChange:l,identifier:p,index:a}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][1].seeks",enableAction:wc})))},Sc=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:t.objective.enable,onChange:function(e){return a("data.objective.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:t.objective.heading,onChange:function(e){return a("data.objective.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),t.jsonld["@graph"][1].seeks&&t.jsonld["@graph"][1].seeks.map((function(e,n){return o.a.createElement(kc,{dispatch:l,first:0===n,index:n,item:e,key:e["@id"],last:n===t.jsonld["@graph"][1].seeks.length-1,onChange:a})})),o.a.createElement(Ec,{heading:t.objective.heading,dispatch:l,size:_s.size(t.jsonld["@graph"][1].seeks)}))},Oc=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=Object(Ws.a)(["leftSidebar","app"]).t,i=function(e,n,o){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,s=e;n&&(s=s+"."+n);var c=_s.get(t,s,null);null===c&&("string"===typeof o||"number"===typeof o?_s.set(t,s,""):"object"===typeof o&&(Array.isArray(o)?_s.set(t,s,[]):_s.set(t,s,{}))),a(l+s,o),i&&a("".concat(l)+e+'["@id"]',i),r&&a("".concat(l)+e+'["@type"]',r)};return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",label:r("work.name.label"),placeholder:"Amazon",value:_s.get(t,"subjectOf.organizer.name",""),onChange:function(e){return a("".concat(l,"subjectOf.organizer.name"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("work.role.label"),placeholder:"Full-Stack Web Developer",value:_s.get(t,"roleName",""),onChange:function(e){return a("".concat(l,"roleName"),e)}}),o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},o.a.createElement(ec,{className:"mb-6",label:r("app:item.startDate.label"),placeholder:"2019-01-01",value:_s.get(t,"startDate",""),onChange:function(e){return a("".concat(l,"startDate"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("app:item.endDate.label"),placeholder:"2020-01-01",value:_s.get(t,"endDate",""),onChange:function(e){return a("".concat(l,"endDate"),e)}})),o.a.createElement(ec,{className:"mb-6",label:r("work.responsibilities.label"),placeholder:"Preparing project plans",value:_s.get(t,"hasOccupation.responsibilities",[]),onChange:function(e){return i("hasOccupation","responsibilities",e)},AddItem:function(){},type:"multitext"}),o.a.createElement(ec,{className:"mb-6",label:r("work.skills.label"),placeholder:"Project Management",value:_s.get(t,"hasOccupation.skills",[]),onChange:function(e){return i("hasOccupation","skills",e)},AddItem:function(){},type:"multitext"}),o.a.createElement(vc,{rows:"5",className:"mb-6",label:r("app:item.description.label"),value:_s.get(t,"description",""),onChange:function(e){return a("".concat(l,"description"),e)}}))},Cc=function(){var e=Object(ac.a)();return{"@type":"EmployeeRole","@id":"_:"+e+"#enable",hasOccupation:{"@id":"_:"+e+"#hasOccupation","@type":"Occupation",name:"",skills:[],responsibilities:[]},subjectOf:{"@type":"BusinessEvent",id:"_:"+e+"#subjectOf",organizer:{"@type":"Organization",id:"_:"+e+"#subjectOf#organizer",name:""}},roleName:"",startDate:"",endDate:"",description:""}},jc=function(e){var t=e.heading,a=e.dispatch,l=Object(n.useState)(!1),r=Object(bs.a)(l,2),i=r[0],s=r[1],c=Object(n.useState)(Cc()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:s,isOpen:i}),o.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},o.a.createElement(Oc,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),o.a.createElement(lc,{onSubmit:function(){""!==d.roleName&&(Ds(a,"data.jsonld['@graph'][1].hasOccupation",d),m(Cc()),s(!1))}})))},Dc=function(e,t,a){return o.a.createElement(o.a.Fragment,null)},Ac=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].hasOccupation['.concat(a,"].");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:t.roleName+" "+t.subjectOf.organizer.name,setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(Oc,{item:t,onChange:l,identifier:p}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][1].hasOccupation",enableAction:Dc})))},Tc=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:t.work.enable,onChange:function(e){return a("data.work.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:t.work.heading,onChange:function(e){return a("data.work.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),_s.get(t.jsonld["@graph"][1],"hasOccupation",[]).map((function(e,n){return o.a.createElement(Ac,{dispatch:l,first:0===n,index:n,item:e,key:_s.get(e,"@id","item"),last:n===_s.size(t.jsonld["@graph"][1].hasOccupation)-1,onChange:a})})),o.a.createElement(jc,{heading:t.work.heading,dispatch:l}))},Jc=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=Object(Ws.a)(["leftSidebar","app"]).t;return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",label:r("education.name.label"),placeholder:"Harvard University",value:_s.get(t,"about.provider.name",""),onChange:function(e){return a("".concat(l,"about.provider.name"),e)}}),o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},o.a.createElement(Vs,{className:"mb-6",label:r("education.type.label"),placeholder:"Certificate type",value:_s.get(t,"credentialCategory",""),onChange:function(e){return a("".concat(l,"credentialCategory"),e)},options:["Degree","Certificate","Badge"],optionItem:function(e,t){return o.a.createElement("option",{key:e,value:e},e)}}),o.a.createElement(ec,{className:"mb-6",label:r("education.major.degree"),placeholder:"Masters of Science",value:_s.get(t,"educationalLevel",""),onChange:function(e){return a("".concat(l,"educationalLevel"),e)}})),o.a.createElement(ec,{className:"mb-6",label:r("education.major.label"),placeholder:"Computer Science",value:_s.get(t,"about.educationalCredentialAwarded",""),onChange:function(e){return a("".concat(l,"about.educationalCredentialAwarded"),e)}}),o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},o.a.createElement(ec,{className:"mb-6",label:r("education.grade.label"),placeholder:"3.7",value:_s.get(t,"aggregateRating.ratingValue",""),onChange:function(e){return a("".concat(l,"aggregateRating.ratingValue"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("education.maxGrade.label"),placeholder:"4",value:_s.get(t,"aggregateRating.bestRating",""),onChange:function(e){return a("".concat(l,"aggregateRating.bestRating"),e)}})),o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},o.a.createElement(ec,{className:"mb-6",label:r("app:item.startDate.label"),placeholder:"2018-01-01",value:_s.get(t,"about.startDate",""),onChange:function(e){return a("".concat(l,"about.startDate"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("app:item.endDate.label"),placeholder:"2020-01-01",value:_s.get(t,"about.endDate",""),onChange:function(e){return a("".concat(l,"about.endDate"),e)}})),o.a.createElement(ec,{className:"mb-6",label:r("work.skills.label"),placeholder:"Project Management",value:_s.get(t,"teaches",[]),onChange:function(e){return function(e,n,o){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,s=e;n&&(s=s+"."+n);var c=_s.get(t,s,null);null===c&&("string"===typeof o||"number"===typeof o?_s.set(t,s,""):"object"===typeof o&&(Array.isArray(o)?_s.set(t,s,[]):_s.set(t,s,{}))),a(l+s,o),i&&a("".concat(l)+e+'["@id"]',i),r&&a("".concat(l)+e+'["@type"]',r)}("teaches","",e)},AddItem:function(){},type:"multitext"}),o.a.createElement(vc,{rows:"5",className:"mb-6",label:r("app:item.description.label"),value:_s.get(t,"abstract",""),onChange:function(e){return a("".concat(l,"abstract"),e)}}))},Pc=function(){var e=Object(ac.a)();return{"@type":"EducationalOccupationalCredential","@id":"_:"+e+"#enable",aggregateRating:{"@id":"_:"+e+"#aggregateRating","@type":"aggregateRating",bestRating:"",ratingValue:"",name:"GPA",itemReviewed:{"@id":"_:"+e+"#enable"}},credentialCategory:"degree",educationalLevel:"",abstract:"",teaches:[],about:{"@id":"_:"+e+"#about","@type":"EducationalOccupationalProgram",educationalCredentialAwarded:"",startDate:"",endDate:"",provider:{"@id":"_:"+e+"#about#provider","@type":"CollegeOrUniversity",name:""}}}},Rc=function(e){var t=e.heading,a=e.dispatch,l=Object(n.useState)(!1),r=Object(bs.a)(l,2),i=r[0],s=r[1],c=Object(n.useState)(Pc()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:s,isOpen:i}),o.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},o.a.createElement(Jc,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),o.a.createElement(lc,{onSubmit:function(){""!==d.educationalLevel&&(Ds(a,'data.jsonld["@graph"][1].hasCredential',d),m(Pc()),s(!1))}})))},Fc=function(e,t,a){return o.a.createElement(o.a.Fragment,null)},Lc=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p="data.jsonld['@graph'][1].hasCredential[".concat(a,"].");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:_s.get(t,"educationalLevel",""),setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(Jc,{item:t,onChange:l,identifier:p}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][1].hasCredential",enableAction:Fc})))},zc=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:t.education.enable,onChange:function(e){return a("data.education.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:t.education.heading,onChange:function(e){return a("data.education.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),_s.get(t.jsonld["@graph"][1],"hasCredential",[]).map((function(e,n){return o.a.createElement(Lc,{item:e,key:_s.get(e,"@id","item"),index:n,onChange:a,dispatch:l,first:0===n,last:n===_s.size(_s.get(t.jsonld["@graph"][1],"hasCredential",[]))-1})})),o.a.createElement(Rc,{heading:t.education.heading,dispatch:l}))},Ic=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=e.altidentifier,i=void 0===r?"":r,s=Object(Ws.a)(["leftSidebar","app"]).t;return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",label:s("awards.title.label"),placeholder:"Code For Good Hackathon",value:_s.get(t,"skill:title",""),onChange:function(e){a("".concat(l,"['skill:title']"),e),""!==i&&a("".concat(i),e)}}),o.a.createElement(ec,{className:"mb-6",label:s("awards.subtitle.label"),placeholder:"First Place, National Level",value:_s.get(t,"skill:nativeLabel",""),onChange:function(e){return a("".concat(l,"['skill:nativeLabel']"),e)}}),o.a.createElement(vc,{className:"mb-6",label:s("app:item.description.label"),value:t.description,onChange:function(e){return a("".concat(l,"description"),e)}}))},Mc=function(){return{"@type":"skill:Award","@id":"_:"+Object(ac.a)()+"#enable","skill:title":"","skill:nativeLabel":"",description:""}},Hc=function(e){var t=e.heading,a=e.dispatch,l=Object(n.useState)(!1),r=Object(bs.a)(l,2),i=r[0],s=r[1],c=Object(n.useState)(Mc()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:s,isOpen:i}),o.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},o.a.createElement(Ic,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),o.a.createElement(lc,{onSubmit:function(){""!==_s.get(d,"skill:title","")&&(Ds(a,'data.jsonld["@graph"][0]["award"]',d),Ds(a,'data.jsonld["@graph"][1]["award"]',_s.get(d,"skill:title","")),m(Mc()),s(!1))}})))},Gc=function(e,t,a){return o.a.createElement(o.a.Fragment,null)},qc=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][0].award['.concat(a,"]."),b='data.jsonld["@graph"][1].award['.concat(a,"]");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:t["skill:title"],setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(Ic,{item:t,onChange:l,identifier:p,altidentifier:b}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][0].award",enableAction:Gc})))},Yc=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:t.awards.enable,onChange:function(e){return a("data.awards.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:t.awards.heading,onChange:function(e){return a("data.awards.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),_s.get(t.jsonld["@graph"][0],"award",[]).map((function(e,n){return o.a.createElement(qc,{item:e,key:_s.get(e,"@id","main"),index:n,onChange:a,dispatch:l,first:0===n,last:n===_s.size(_s.get(t.jsonld["@graph"][0],"award",[]))-1})})),o.a.createElement(Hc,{heading:t.awards.heading,dispatch:l}))},Wc=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=Object(Ws.a)("leftSidebar").t;return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",label:r("extras.key.label"),placeholder:"Date of Birth",value:_s.get(t,"propertyID",""),onChange:function(e){return a("".concat(l,"propertyID"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("extras.value.label"),placeholder:"6th August 1995",value:_s.get(t,"value",""),onChange:function(e){return a("".concat(l,"value"),e)}}))},Uc=function(){return{"@type":"PropertyValue","@id":"_:Extras_"+Object(ac.a)(),propertyID:"",value:""}},_c=function(e){var t=e.heading,a=e.dispatch,l=Object(n.useState)(!1),r=Object(bs.a)(l,2),i=r[0],s=r[1],c=Object(n.useState)(Uc()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:s,isOpen:i}),o.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},o.a.createElement(Wc,{item:d,onChange:function(e,t){return m((function(a){return vs()(Object(hs.a)({},a),e,t)}))}}),o.a.createElement(lc,{onSubmit:function(){""!==_s.get(d,"propertyID","")&&""!==_s.get(d,"value","")&&(Ds(a,'data.jsonld["@graph"][1].identifier',d),m(Uc()),s(!1))}})))},Vc=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].identifier['.concat(a,"].");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:_s.get(t,"propertyID",""),setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(Wc,{item:t,onChange:l,identifier:p}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][1].identifier"})))},Bc=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:t.extras.enable,onChange:function(e){return a("data.extras.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:t.extras.heading,onChange:function(e){return a("data.extras.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),_s.get(t.jsonld["@graph"][1],"identifier",[]).map((function(e,n){return o.a.createElement(Vc,{item:e,key:_s.get(e,"@id","main"),index:n,onChange:a,dispatch:l,first:0===n,last:n===_s.get(t.jsonld["@graph"][1],"identifier",[]).length-1})})),o.a.createElement(_c,{heading:t.extras.heading,dispatch:l}))},Kc=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=Object(Ws.a)("leftSidebar").t;return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",label:r("languages.key.label"),placeholder:"English",value:_s.get(t,"name",""),onChange:function(e){return a("".concat(l,"name"),e)}}))},Zc=function(){return{"@type":"Language","@id":"_:"+Object(ac.a)(),name:""}},Qc=function(e){var t=e.heading,a=e.dispatch,l=Object(n.useState)(!1),r=Object(bs.a)(l,2),i=r[0],s=r[1],c=Object(n.useState)(Zc()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:s,isOpen:i}),o.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},o.a.createElement(Kc,{item:d,onChange:function(e,t){return m((function(a){return vs()(Object(hs.a)({},a),e,t)}))}}),o.a.createElement(lc,{onSubmit:function(){""!==_s.get(d,"name","")&&(Ds(a,'data.jsonld["@graph"][1].knowsLanguage',d),m(Zc()),s(!1))}})))},Xc=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].knowsLanguage['.concat(a,"].");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:_s.get(t,"name",""),setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(Kc,{item:t,onChange:l,identifier:p}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][1].knowsLanguage"})))},$c=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return Object(n.useEffect)((function(){"languages"in t||(l({type:"migrate_section",payload:{key:"languages",value:{enable:!1,heading:"Languages"}}}),l({type:"save_data"}))}),[t,l]),"languages"in t&&o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:t.languages.enable,onChange:function(e){return a("data.languages.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:t.languages.heading,onChange:function(e){return a("data.languages.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),_s.get(t.jsonld["@graph"][1],"knowsLanguage",[]).map((function(e,n){return o.a.createElement(Xc,{item:e,key:_s.get(e,"@id","item"),index:n,onChange:a,dispatch:l,first:0===n,last:n===_s.size(_s.get(t.jsonld["@graph"][1],"knowsLanguage",[]))-1})})),o.a.createElement(Qc,{heading:t.languages.heading,dispatch:l}))},eu=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=Object(Ws.a)(["leftSidebar","app"]).t;return o.a.createElement("div",null,o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},o.a.createElement(ec,{className:"mb-6",label:r("references.name.label"),placeholder:"Richard Hendricks",value:_s.get(t,"interactionType.participant.givenName",""),onChange:function(e){return a("".concat(l,"interactionType.participant.givenName"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("references.familyName.label"),placeholder:"Richard Hendricks",value:_s.get(t,"interactionType.participant.familyName",""),onChange:function(e){return a("".concat(l,"interactionType.participant.familyName"),e)}})),o.a.createElement(ec,{className:"mb-6",label:r("references.position.label"),placeholder:"CEO, Pied Piper",value:_s.get(t,"interactionType.participant.jobTitle",""),onChange:function(e){return a("".concat(l,"interactionType.participant.jobTitle"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("references.phone.label"),placeholder:"****** 754 3010",value:_s.get(t,"interactionType.participant.telephone",""),onChange:function(e){return a("".concat(l,"interactionType.participant.telephone"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("references.email.label"),placeholder:"<EMAIL>",value:_s.get(t,"interactionType.participant.email",""),onChange:function(e){return a("".concat(l,"interactionType.participant.email"),e)}}),o.a.createElement(vc,{rows:"5",className:"mb-6",label:r("app:item.description.label"),value:_s.get(t,"result[0].reviewRating.ratingExplanation",""),onChange:function(e){return a("".concat(l,"result[0].reviewRating.ratingExplanation"),e)}}))},tu=function(){var e=Object(ac.a)();return{"@id":"_:Reference#"+e,"@type":"InteractionCounter",disambiguatingDescription:"Reference",interactionType:{"@id":"_:Reference#"+e+"#interactionType","@type":"AssessAction",participant:{"@id":"_:Reference#"+e+"#interactionType#participant","@type":"Person"},result:[{"@id":"_:Reference#"+e+"#result","@type":"Review",itemReviewed:{},reviewAspect:[],reviewRating:{"@id":"_:Reference#"+e+"#result#reviewRating","@type":"Rating",ratingValue:"5",bestRating:"5",ratingExplanation:""}}]}}},au=function(e){var t=e.heading,a=e.dispatch,l=Object(n.useState)(!1),r=Object(bs.a)(l,2),i=r[0],s=r[1],c=Object(n.useState)(tu()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:s,isOpen:i}),o.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},o.a.createElement(eu,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),o.a.createElement(lc,{onSubmit:function(){""!==_s.get(d,"interactionType.participant.givenName","")&&(Ds(a,'data.jsonld["@graph"][1].interactionStatistic',d),m(tu()),s(!1))}})))},nu=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p='data.jsonld["@graph"][1].interactionStatistic['.concat(a,"].");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:_s.get(t,"interactionType.participant.givenName","")+" "+_s.get(t,"interactionType.participant.familyName",""),setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(eu,{item:t,onChange:l,identifier:p}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][1].interactionStatistic"})))},ou=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return Object(n.useEffect)((function(){"references"in t||(l({type:"migrate_section",payload:{key:"references",value:{enable:!1,heading:"References"}}}),l({type:"save_data"}))}),[t,l]),"references"in t&&o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"mb-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:t.references.enable,onChange:function(e){return a("data.references.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:t.references.heading,onChange:function(e){return a("data.references.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),_s.get(t.jsonld["@graph"][1],"interactionStatistic",[]).filter((function(e){return"Reference"===_s.get(e,"disambiguatingDescription","")})).map((function(e,n){return o.a.createElement(nu,{item:e,key:_s.get(e,"@id","main"),index:n,onChange:a,dispatch:l,first:0===n,last:n===_s.get(t.jsonld["@graph"][1],"interactionStatistic",[]).filter((function(e){return"Reference"===_s.get(e,"disambiguatingDescription","")})).length-1})})),o.a.createElement(au,{heading:t.references.heading,dispatch:l}))},lu=function(e){var t=e.item,a=e.onChange,n=e.identifier,l=void 0===n?"":n,r=Object(Ws.a)(["leftSidebar","app"]).t;return o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-6",label:r("membership.programName.label"),placeholder:"Salsa Dance Class",value:_s.get(t,"memberOf.programName",""),onChange:function(e){return a("".concat(l,"memberOf.programName"),e)}}),o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4"},o.a.createElement(ec,{className:"mb-6",label:r("membership.startDate.label"),placeholder:"2019-01-01",value:_s.get(t,"startDate",""),onChange:function(e){return a("".concat(l,"startDate"),e)}}),o.a.createElement(ec,{className:"mb-6",label:r("membership.endDate.label"),placeholder:"2020-01-01",value:_s.get(t,"endDate",""),onChange:function(e){return a("".concat(l,"endDate"),e)}})),o.a.createElement(ec,{className:"mb-6",label:r("membership.roleName.label"),placeholder:"VIP member",value:_s.get(t,"roleName",""),onChange:function(e){return a("".concat(l,"roleName"),e)}}))},ru=function(){var e=Object(ac.a)();return{"@id":"_:"+e+"#enable","@type":"Role",startDate:"",endDate:"",roleName:"member",memberOf:{"@id":"_:"+e+"#memberOf","@type":"ProgramMembership",url:"",programName:"",description:""}}},iu=function(e){var t=e.heading,a=e.dispatch,l=Object(n.useState)(!1),r=Object(bs.a)(l,2),i=r[0],s=r[1],c=Object(n.useState)(ru()),u=Object(bs.a)(c,2),d=u[0],m=u[1];return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{heading:t,setOpen:s,isOpen:i}),o.a.createElement("div",{className:"mt-6 ".concat(i?"block":"hidden")},o.a.createElement(lu,{item:d,onChange:function(e,t){return m(vs()(Object(hs.a)({},d),e,t))}}),o.a.createElement(lc,{onSubmit:function(){""!==_s.get(d,"roleName","")&&(Ds(a,'data.jsonld["@graph"][1].memberOf',d),m(ru()),s(!1))}})))},su=function(e,t,a){return o.a.createElement(o.a.Fragment,null)},cu=function(e){var t=e.item,a=e.index,l=e.onChange,r=e.dispatch,i=e.first,s=e.last,c=Object(n.useState)(!1),u=Object(bs.a)(c,2),d=u[0],m=u[1],p="data.jsonld['@graph'][1].memberOf[".concat(a,"].");return o.a.createElement("div",{className:"my-4 border border-gray-200 rounded p-5"},o.a.createElement(rc,{title:_s.get(t,"memberOf.programName",""),setOpen:m,isOpen:d}),o.a.createElement("div",{className:"mt-6 ".concat(d?"block":"hidden")},o.a.createElement(lu,{item:t,onChange:l,identifier:p}),o.a.createElement(oc,{dispatch:r,first:i,identifier:p,item:t,last:s,onChange:l,type:"data.jsonld['@graph'][1].memberOf",enableAction:su})))},uu=function(e){var t=e.data,a=e.onChange,l=Object(n.useContext)(Ms).dispatch;return o.a.createElement(o.a.Fragment,null,o.a.createElement("div",{className:"my-6 grid grid-cols-6 items-center"},o.a.createElement("div",{className:"col-span-1"},o.a.createElement(nc,{checked:_s.get(t,"Memberships.enable",!0),onChange:function(e){return a("data.Memberships.enable",e)}})),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{placeholder:"Heading",value:_s.get(t,"Memberships.heading",""),onChange:function(e){return a("data.Memberships.heading",e)}}))),o.a.createElement("hr",{className:"my-6"}),_s.get(t.jsonld["@graph"][1],"memberOf",[]).map((function(e,n){return o.a.createElement(cu,{item:e,key:_s.get(e,"@id","item"),index:n,onChange:a,dispatch:l,first:0===n,last:n===_s.size(_s.get(t.jsonld["@graph"][1],"memberOf",[]))-1})})),o.a.createElement(iu,{heading:_s.get(t,"Memberships.heading",""),dispatch:l}))},du=function(){var e=Object(n.useContext)(Ms),t=e.state,a=e.dispatch,l=t.data,r=[{key:"profile",name:_s.get(l,"profile.heading","Profile")},{key:"address",name:_s.get(l,"address.headin","Address")},{key:"contacts",name:_s.get(l,"contacts.heading","Contacts")},{key:"objective",name:_s.get(l,"objective.heading","Objective")},{key:"work",name:_s.get(l,"work.heading","Work")},{key:"education",name:_s.get(l,"education.heading","Education")},{key:"awards",name:_s.get(l,"awards.heading","Awards")},{key:"memberships",name:_s.get(l,"memberships.heading","Memberships")},{key:"languages",name:_s.get(l,"languages.heading","Languages")},{key:"references",name:_s.get(l,"references.heading","References")},{key:"extras",name:_s.get(l,"extras.heading","Extras")}],i=Object(n.useState)(r[0].key),s=Object(bs.a)(i,2),c=s[0],u=s[1],d=function(e,t){a({type:"on_input",payload:{key:e,value:t}}),a({type:"save_data"})};return o.a.createElement("div",{id:"leftSidebar",className:"animated slideInLeft z-10 py-6 h-screen bg-white col-span-1 shadow-2xl overflow-y-scroll"},o.a.createElement(Bs,{tabs:r,currentTab:c,setCurrentTab:u}),o.a.createElement("div",{className:"px-6"},function(){switch(c){case"profile":return o.a.createElement(tc,{data:l,onChange:d});case"address":return o.a.createElement(dc,{data:l,onChange:d});case"contacts":return o.a.createElement(gc,{data:l,onChange:d});case"objective":return o.a.createElement(Sc,{data:l,onChange:d});case"work":return o.a.createElement(Tc,{data:l,onChange:d});case"education":return o.a.createElement(zc,{data:l,onChange:d});case"awards":return o.a.createElement(Yc,{data:l,onChange:d});case"memberships":return o.a.createElement(uu,{data:l,onChange:d});case"languages":return o.a.createElement($c,{data:l,onChange:d});case"references":return o.a.createElement(ou,{data:l,onChange:d});case"extras":return o.a.createElement(Bc,{data:l,onChange:d});default:return null}}()))},mu=a(3),pu=a.n(mu),bu=function(){var e=Object(n.useContext)(Ms).state,t=e.data,a=e.theme,l=function(){return t.profile.photo&&o.a.createElement("img",{className:"rounded object-cover mr-4",src:t.profile.photo,alt:"Resume Photograph",style:{width:"120px",height:"120px"}})},r=function(){return o.a.createElement("div",null,o.a.createElement("h1",{className:"font-bold text-4xl",style:{color:a.colors.accent}},t.profile.firstName," ",t.profile.lastName),o.a.createElement("h6",{className:"font-medium text-sm"},t.profile.subtitle),o.a.createElement("div",{className:"flex flex-col mt-4 text-xs"},o.a.createElement("span",null,t.profile.address.line1),o.a.createElement("span",null,t.profile.address.line2),o.a.createElement("span",null,t.profile.address.line3)))},i=function(e){var t=e.icon,n=e.value,l=e.link,r=void 0===l?"#":l;return n&&o.a.createElement("div",{className:"flex items-center my-3"},o.a.createElement("span",{className:"material-icons text-lg mr-2",style:{color:a.colors.accent}},t),o.a.createElement("a",{href:r},o.a.createElement("span",{className:"font-medium break-all"},n)))},s=function(e){var t=e.title;return o.a.createElement("h6",{className:"text-xs font-bold uppercase mt-6 mb-2",style:{color:a.colors.accent}},t)},c=function(){return t.objective&&t.objective.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.objective.heading}),o.a.createElement(pu.a,{className:"text-sm",source:t.objective.body}))},u=function(e){return o.a.createElement("div",{key:e.id,className:"mt-3"},o.a.createElement("div",{className:"flex justify-between"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.role)),o.a.createElement("span",{className:"text-xs font-medium"},"(",e.start," - ",e.end,")")),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},d=function(){return t.work&&t.work.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.work.heading}),t.work.items.filter((function(e){return e.enable})).map(u))},m=function(e){return o.a.createElement("div",{key:e.id,className:"mt-3"},o.a.createElement("div",{className:"flex justify-between"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold"},e.name),o.a.createElement("p",{className:"text-xs"},e.major)),o.a.createElement("div",{className:"flex flex-col items-end"},o.a.createElement("span",{className:"text-sm font-bold"},e.grade),o.a.createElement("span",{className:"text-xs font-medium"},"(",e.start," - ",e.end,")"))),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},p=function(){return t.education&&t.education.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.education.heading}),t.education.items.filter((function(e){return e.enable})).map(m))},b=function(e){return o.a.createElement("div",{key:e.id,className:"mt-3"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},h=function(){return t.awards&&t.awards.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.awards.heading}),t.awards.items.filter((function(e){return e.enable})).map(b))},g=function(e){return o.a.createElement("div",{key:e.id,className:"mt-3"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},f=function(){return t.certifications&&t.certifications.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.certifications.heading}),t.certifications.items.filter((function(e){return e.enable})).map(g))},y=function(e){return o.a.createElement("span",{key:e.id,className:"text-xs rounded-full px-3 py-1 font-medium my-2 mr-2",style:{backgroundColor:a.colors.primary,color:a.colors.background}},e.hobby)},v=function(){return t.memberships&&t.memberships.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.memberships.heading}),o.a.createElement("div",{className:"mt-1 flex flex-wrap"},t.memberships.items.map(y)))},x=function(e){return o.a.createElement("span",{key:e.id,className:"text-xs rounded-full px-3 py-1 font-medium my-2 mr-2",style:{backgroundColor:a.colors.primary,color:a.colors.background}},e.skill)},N=function(){return t.skills&&t.skills.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.skills.heading}),o.a.createElement("div",{className:"mt-1 flex flex-wrap"},t.skills.items.map(x)))},E=function(e){return o.a.createElement("div",{key:e.id,className:"grid grid-cols-2 items-center py-2"},o.a.createElement("h6",{className:"text-sm font-medium"},e.key),o.a.createElement("div",{className:"flex"},e.level&&o.a.createElement("div",{className:"font-bold text-sm mr-2"},e.level),0!==e.rating&&o.a.createElement("div",{className:"flex"},Array.from(Array(e.rating)).map((function(e,t){return o.a.createElement("i",{key:t,className:"material-icons text-lg",style:{color:a.colors.accent}},"star")})))))},w=function(){return t.languages&&t.languages.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.languages.heading}),o.a.createElement("div",{className:"w-3/4"},t.languages.items.filter((function(e){return e.enable})).map(E)))},k=function(e){return o.a.createElement("div",{key:e.id,className:"flex flex-col"},o.a.createElement("h6",{className:"text-sm font-medium"},e.name),o.a.createElement("span",{className:"text-xs"},e.position),o.a.createElement("span",{className:"text-xs"},e.phone),o.a.createElement("span",{className:"text-xs"},e.email),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},S=function(){return t.references&&t.references.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.references.heading}),o.a.createElement("div",{className:"grid grid-cols-3 gap-6"},t.references.items.filter((function(e){return e.enable})).map(k)))},O=function(e){return o.a.createElement("tr",{key:e.id},o.a.createElement("td",{className:"border font-medium px-4 py-2 text-sm"},e.key),o.a.createElement("td",{className:"border px-4 py-2 text-sm"},e.value))},C=function(){return t.extras&&t.extras.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.extras.heading}),o.a.createElement("table",{className:"table-auto"},o.a.createElement("tbody",null,t.extras.items.filter((function(e){return e.enable})).map(O))))};return o.a.createElement("div",{className:"p-10",style:{fontFamily:a.font.family,backgroundColor:a.colors.background,color:a.colors.primary}},o.a.createElement("div",{className:"grid grid-cols-4 items-center"},o.a.createElement("div",{className:"col-span-3 flex items-center"},o.a.createElement(l,null),o.a.createElement(r,null)),o.a.createElement("div",{className:"col-span-1 text-xs"},o.a.createElement(i,{icon:"phone",value:t.profile.phone,link:"tel:".concat(t.profile.phone)}),o.a.createElement(i,{icon:"language",value:t.profile.website,link:"http://".concat(t.profile.website)}),o.a.createElement(i,{icon:"email",value:t.profile.email,link:"mailto:".concat(t.profile.email)}),o.a.createElement(i,{icon:"location_on",value:t.profile.address.line3}))),o.a.createElement("hr",{className:"my-6"}),o.a.createElement(c,null),o.a.createElement(d,null),o.a.createElement(p,null),o.a.createElement("div",{className:"grid grid-cols-2 gap-6"},o.a.createElement(h,null),o.a.createElement(f,null)),o.a.createElement("div",{className:"grid grid-cols-2 gap-6"},o.a.createElement(N,null),o.a.createElement(v,null)),o.a.createElement(S,null),o.a.createElement("div",{className:"grid grid-cols-2 gap-6"},o.a.createElement(C,null),o.a.createElement(w,null)))},hu=a(636),gu=a.n(hu).a,fu=bu,yu=function(){var e=Object(n.useContext)(Ms).state,t=e.data,a=e.theme,l=function(){return""!==t.profile.photo&&o.a.createElement("div",{className:"self-center col-span-4"},o.a.createElement("img",{className:"w-48 h-48 rounded-full mx-auto object-cover",src:t.profile.photo,alt:""}))},r=function(){return o.a.createElement("div",{className:"h-48 rounded flex flex-col justify-center",style:{backgroundColor:a.colors.accent,color:a.colors.background}},o.a.createElement("div",{className:"flex flex-col justify-center mx-8 my-6"},o.a.createElement("h1",{className:"text-3xl font-bold leading-tight"},t.profile.firstName," ",t.profile.lastName),o.a.createElement("div",{className:"text-sm font-medium tracking-wide"},t.profile.subtitle),o.a.createElement("hr",{className:"my-4 opacity-50"}),o.a.createElement(pu.a,{className:"text-sm",source:t.objective.body})))},i=function(e){var t=e.icon,n=e.value,l=e.link,r=void 0===l?"#":l;return n&&o.a.createElement("div",{className:"flex items-center my-3"},o.a.createElement("span",{className:"material-icons text-lg mr-2",style:{color:a.colors.accent}},t),o.a.createElement("a",{href:r},o.a.createElement("span",{className:"font-medium break-all"},n)))},s=function(e){var t=e.title;return o.a.createElement("div",{className:"mb-2 border-b-2 pb-1 font-bold uppercase tracking-wide text-sm",style:{color:a.colors.accent,borderColor:a.colors.accent}},t)},c=function(e){return o.a.createElement("span",{key:e.id,className:"leading-none rounded-lg text-sm font-medium bg-gray-300 py-3 my-1 px-4"},e.skill)},u=function(){return t.skills&&t.skills.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.skills.heading}),o.a.createElement("div",{className:"flex flex-col mb-6"},t.skills.items.map(c)))},d=function(e){return o.a.createElement("span",{key:e.id,className:"leading-none rounded-lg text-sm font-medium bg-gray-300 py-3 my-1 px-4"},e.hobby)},m=function(){return t.hobbies&&t.hobbies.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.hobbies.heading}),o.a.createElement("div",{className:"flex flex-col mb-6"},t.hobbies.items.map(d)))},p=function(e){return o.a.createElement("div",{key:e.id,className:"flex flex-col"},o.a.createElement("h6",{className:"text-sm font-medium"},e.name),o.a.createElement("span",{className:"text-xs"},e.position),o.a.createElement("span",{className:"text-xs"},e.phone),o.a.createElement("span",{className:"text-xs"},e.email),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},b=function(){return t.references&&t.references.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.references.heading}),o.a.createElement("div",{className:"grid grid-cols-2 gap-2 mb-6"},t.references.items.filter((function(e){return e.enable})).map(p)))},h=function(e){return o.a.createElement("div",{key:e.id,className:"grid grid-cols-2 items-center py-2"},o.a.createElement("h6",{className:"text-sm font-medium"},e.key),o.a.createElement("div",{className:"flex"},e.level&&o.a.createElement("div",{className:"font-bold text-sm mr-2"},e.level),0!==e.rating&&o.a.createElement("div",{className:"flex"},Array.from(Array(e.rating)).map((function(e,t){return o.a.createElement("i",{key:t,className:"material-icons text-lg",style:{color:a.colors.accent}},"star")})))))},g=function(){return t.languages&&t.languages.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.languages.heading}),o.a.createElement("div",{className:"mb-6"},t.languages.items.filter((function(e){return e.enable})).map(h)))},f=function(e){return o.a.createElement("div",{key:e.id,className:"text-sm my-1"},o.a.createElement("h6",{className:"text-xs font-bold"},e.key),o.a.createElement("h6",{className:""},e.value))},y=function(){return t.extras&&t.extras.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.extras.heading}),o.a.createElement("div",{className:"grid grid-cols-2"},t.extras.items.filter((function(e){return e.enable})).map(f)))},v=function(e){return o.a.createElement("div",{key:e.id,className:"mb-3"},o.a.createElement("div",{className:"flex justify-between items-center"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.role)),o.a.createElement("span",{className:"text-xs font-medium"},"(",e.start," - ",e.end,")")),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},x=function(){return t.work&&t.work.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.work.heading}),o.a.createElement("div",{className:"flex flex-col mb-4"},t.work.items.filter((function(e){return e.enable})).map(v)))},N=function(e){return o.a.createElement("div",{key:e.id,className:"mb-2"},o.a.createElement("div",{className:"flex justify-between items-center"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold"},e.name),o.a.createElement("p",{className:"text-xs"},e.major)),o.a.createElement("div",{className:"flex flex-col text-right items-end"},o.a.createElement("span",{className:"text-sm font-bold",style:{color:a.colors.accent}},e.grade),o.a.createElement("span",{className:"text-xs font-medium"},"(",e.start," - ",e.end,")"))),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},E=function(){return t.education&&t.education.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.education.heading}),o.a.createElement("div",{className:"flex flex-col mb-4"},t.education.items.filter((function(e){return e.enable})).map(N)))},w=function(e){return o.a.createElement("div",{key:e.id,className:"mb-2"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},k=function(){return t.awards&&t.awards.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.awards.heading}),o.a.createElement("div",{className:"flex flex-col mb-2"},t.awards.items.filter((function(e){return e.enable})).map(w)))},S=function(e){return o.a.createElement("div",{key:e.id,className:"mb-3"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},O=function(){return t.certifications&&t.certifications.enable&&o.a.createElement("div",null,o.a.createElement(s,{title:t.certifications.heading}),o.a.createElement("div",{className:"flex flex-col mb-2"},t.certifications.items.filter((function(e){return e.enable})).map(S)))};return o.a.createElement("div",{className:"p-10",style:{fontFamily:a.font.family,backgroundColor:a.colors.background,color:a.colors.primary}},o.a.createElement("div",{className:"grid grid-cols-12 col-gap-6 row-gap-8"},o.a.createElement(l,null),o.a.createElement("div",{className:"".concat(""!==t.profile.photo?"col-span-8":"col-span-12")},o.a.createElement(r,null)),o.a.createElement("div",{className:"col-span-4 overflow-hidden"},o.a.createElement("div",{className:"text-sm mb-6"},o.a.createElement(i,{icon:"phone",value:t.profile.phone,link:"tel:".concat(t.profile.phone)}),o.a.createElement(i,{icon:"language",value:t.profile.website,link:"http://".concat(t.profile.website)}),o.a.createElement(i,{icon:"email",value:t.profile.email,link:"mailto:".concat(t.profile.email)}),o.a.createElement(i,{icon:"location_on",value:t.profile.address.line3})),o.a.createElement(u,null),o.a.createElement(m,null),o.a.createElement(g,null),o.a.createElement(O,null)),o.a.createElement("div",{className:"col-span-8"},o.a.createElement(x,null),o.a.createElement(E,null),o.a.createElement(k,null),o.a.createElement(b,null),o.a.createElement(y,null))))},vu=a(637),xu=a.n(vu).a,Nu=yu,Eu=function(){var e=Object(n.useContext)(Ms).state,t=e.data,a=e.theme,l=Cs(a.colors.accent)||{},r=l.r,i=l.g,s=l.b,c=function(){return""!==t.profile.photo&&o.a.createElement("img",{className:"w-24 h-24 rounded-full mr-4 object-cover border-4",style:{borderColor:a.colors.background},src:t.profile.photo,alt:"Resume Photograph"})},u=function(){return o.a.createElement("div",null,o.a.createElement("h1",{className:"text-2xl font-bold leading-tight"},t.profile.firstName),o.a.createElement("h1",{className:"text-2xl font-bold leading-tight"},t.profile.lastName),o.a.createElement("div",{className:"text-xs font-medium mt-2"},t.profile.subtitle))},d=function(e){var t=e.icon,n=e.value,l=e.link,r=void 0===l?"#":l;return n&&o.a.createElement("div",{className:"flex items-center mb-3"},o.a.createElement("div",{className:"w-5 h-5 rounded-full flex justify-center items-center mr-2",style:{backgroundColor:a.colors.background}},o.a.createElement("i",{className:"flex justify-center items-center material-icons text-xs",style:{color:a.colors.accent}},t)),o.a.createElement("a",{href:r},o.a.createElement("span",{className:"text-sm font-medium break-all"},n)))},m=function(e){var t=e.title;return o.a.createElement("h6",{className:"font-bold text-xs uppercase tracking-wide mb-2"},t)},p=function(){return t.objective&&t.objective.enable&&o.a.createElement("div",{className:"flex flex-col justify-center items-start mb-6"},o.a.createElement(m,{title:t.objective.heading}),o.a.createElement(pu.a,{className:"text-sm",source:t.objective.body}))},b=function(e){return o.a.createElement("li",{key:e.id,className:"text-sm py-1"},e.skill)},h=function(){return t.skills&&t.skills.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(m,{title:t.skills.heading}),o.a.createElement("ul",null,t.skills.items.map(b)))},g=function(e){return o.a.createElement("li",{key:e.id,className:"text-sm py-1"},e.hobby)},f=function(){return t.hobbies&&t.hobbies.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(m,{title:t.hobbies.heading}),o.a.createElement("ul",null,t.hobbies.items.map(g)))},y=function(e){return o.a.createElement("div",{key:e.id,className:"mb-3"},o.a.createElement("div",{className:"flex justify-between items-center"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold"},e.name,o.a.createElement("small",{className:"ml-2"},""!==e.start&&""!==e.end&&o.a.createElement("span",{className:"text-xs font-medium"},"(",e.start," - ",e.end,")"))),o.a.createElement("p",{className:"text-xs"},e.major)),o.a.createElement("div",{className:"flex flex-col text-right items-end"},o.a.createElement("span",{className:"text-sm font-bold",style:{color:a.colors.accent}},e.grade))),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},v=function(){return t.education&&t.education.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(m,{title:t.education.heading}),t.education.items.filter((function(e){return e.enable})).map(y))},x=function(e){return o.a.createElement("div",{key:e.id,className:"mb-3"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},N=function(){return t.certifications&&t.certifications.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(m,{title:t.certifications.heading}),t.certifications.items.filter((function(e){return e.enable})).map(x))},E=function(e){return o.a.createElement("div",{key:e.id,className:"mb-3"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},w=function(){return t.awards&&t.awards.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(m,{title:t.awards.heading}),t.awards.items.filter((function(e){return e.enable})).map(E))},k=function(e){return o.a.createElement("div",{key:e.id,className:"flex flex-col"},o.a.createElement("h6",{className:"text-sm font-medium"},e.name),o.a.createElement("span",{className:"text-xs"},e.position),o.a.createElement("span",{className:"text-xs"},e.phone),o.a.createElement("span",{className:"text-xs"},e.email),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},S=function(){return t.references&&t.references.enable&&o.a.createElement("div",null,o.a.createElement(m,{title:t.references.heading}),o.a.createElement("div",{className:"grid grid-cols-2 gap-6"},t.references.items.filter((function(e){return e.enable})).map(k)))},O=function(e){return o.a.createElement("div",{key:e.id,className:"mb-3"},o.a.createElement("div",{className:"flex justify-between items-center"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.role)),o.a.createElement("span",{className:"text-xs font-medium"},"(",e.start," - ",e.end,")")),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},C=function(){return t.work&&t.work.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(m,{title:t.work.heading}),t.work.items.filter((function(e){return e.enable})).map(O))},j=function(e){return o.a.createElement("div",{key:e.id,className:"grid grid-cols-2 items-center py-2"},o.a.createElement("h6",{className:"text-sm font-medium"},e.key),o.a.createElement("div",{className:"flex"},e.level&&o.a.createElement("div",{className:"font-bold text-sm mr-2"},e.level),0!==e.rating&&o.a.createElement("div",{className:"flex"},Array.from(Array(e.rating)).map((function(e,t){return o.a.createElement("i",{key:t,className:"material-icons text-lg",style:{color:a.colors.accent}},"star")})))))},D=function(){return t.languages&&t.languages.enable&&o.a.createElement("div",null,o.a.createElement(m,{title:t.languages.heading}),o.a.createElement("div",{className:"mb-6"},t.languages.items.filter((function(e){return e.enable})).map(j)))},A=function(e){return o.a.createElement("div",{key:e.id,className:"text-sm my-1"},o.a.createElement("h6",{className:"text-xs font-bold"},e.key),o.a.createElement("h6",null,e.value))},T=function(){return t.extras&&t.extras.enable&&o.a.createElement("div",null,o.a.createElement(m,{title:t.extras.heading}),o.a.createElement("div",{className:"grid grid-cols-2"},t.extras.items.filter((function(e){return e.enable})).map(A)))};return o.a.createElement("div",{style:{fontFamily:a.font.family,backgroundColor:a.colors.background,color:a.colors.primary}},o.a.createElement("div",{className:"grid grid-cols-12"},o.a.createElement("div",{className:"col-span-4 px-6 py-8",style:{backgroundColor:a.colors.accent,color:a.colors.background}},o.a.createElement("div",{className:"flex items-center"},o.a.createElement(c,null),o.a.createElement(u,null)),o.a.createElement("hr",{className:"w-1/4 my-5 opacity-50"}),o.a.createElement(d,{icon:"phone",value:t.profile.phone,link:"tel:".concat(t.profile.phone)}),o.a.createElement(d,{icon:"email",value:t.profile.email,link:"mailto:".concat(t.profile.email)}),o.a.createElement(d,{icon:"language",value:t.profile.website,link:"http://".concat(t.profile.website)}),o.a.createElement(d,{icon:"location_on",value:t.profile.address.line3})),o.a.createElement("div",{className:"col-span-8 px-6 py-8",style:{backgroundColor:"rgba(".concat(r,", ").concat(i,", ").concat(s,", 0.1)")}},o.a.createElement(p,null),o.a.createElement(T,null)),o.a.createElement("div",{className:"col-span-4 px-6 py-8",style:{backgroundColor:"rgba(".concat(r,", ").concat(i,", ").concat(s,", 0.1)")}},o.a.createElement(h,null),o.a.createElement(f,null),o.a.createElement(D,null),o.a.createElement(v,null),o.a.createElement(N,null)),o.a.createElement("div",{className:"col-span-8 px-6 py-8"},o.a.createElement(C,null),o.a.createElement(w,null),o.a.createElement(S,null))))},wu=a(638),ku=a.n(wu).a,Su=Eu,Ou=function(){var e=Object(n.useContext)(Ms).state,t=e.data,a=e.theme,l=function(){return""!==t.profile.photo&&o.a.createElement("div",{className:"mt-5 ml-5"},o.a.createElement("img",{className:"w-32 h-32 rounded-full",style:{borderWidth:6,borderColor:a.colors.background},src:t.profile.photo,alt:"Profile Photograph"}))},r=function(){return o.a.createElement("div",{className:"pt-5 px-5"},o.a.createElement("h1",{className:"text-2xl font-bold"},t.profile.firstName," ",t.profile.lastName),o.a.createElement("h5",null,t.profile.subtitle))},i=function(e){var t=e.title,a=e.light,n=void 0!==a&&a;return o.a.createElement("div",{className:"py-2 my-4 ".concat(n?"mx-5 border-t border-b border-gray-400":""),style:{backgroundColor:n?"":"rgba(0, 0, 0, 0.25)"}},o.a.createElement("h6",{className:"".concat(n?"":"pl-5"," font-semibold")},t))},s=function(){return o.a.createElement("div",{className:"px-5 my-2"},o.a.createElement("h6",{className:"text-xs font-bold"},"Address"),o.a.createElement("div",{className:"text-sm"},t.profile.address.line1),o.a.createElement("div",{className:"text-sm"},t.profile.address.line2),o.a.createElement("div",{className:"text-sm"},t.profile.address.line3))},c=function(e){var t=e.title,a=e.value,n=e.link,l=void 0===n?"#":n;return a&&o.a.createElement("div",{className:"px-5 my-2"},o.a.createElement("h6",{className:"text-xs font-bold"},t),o.a.createElement("a",{href:l},o.a.createElement("div",{className:"text-sm"},a)))},u=function(){return o.a.createElement("div",null,o.a.createElement(i,{title:t.profile.heading}),o.a.createElement(s,null),o.a.createElement(c,{title:"Phone",value:t.profile.phone,link:"tel:".concat(t.profile.phone)}),o.a.createElement(c,{title:"Email Address",value:t.profile.email,link:"mailto:".concat(t.profile.email)}),o.a.createElement(c,{title:"Website",value:t.profile.website,link:"http://".concat(t.profile.website)}))},d=function(e){return o.a.createElement("li",{key:e.id,className:"text-sm my-2"},e.skill)},m=function(){return t.skills&&t.skills.enable&&o.a.createElement("div",null,o.a.createElement(i,{title:t.skills.heading}),o.a.createElement("ul",{className:"list-none px-5"},t.skills.items.map(d)))},p=function(e){return o.a.createElement("li",{key:e.id,className:"text-sm my-2"},e.hobby)},b=function(){return t.memberships&&t.memberships.enable&&o.a.createElement("div",null,o.a.createElement(i,{title:t.memberships.heading}),o.a.createElement("ul",{className:"list-none px-5"},t.memberships.items.map(p)))},h=function(){return t.objective&&t.objective.enable&&o.a.createElement(pu.a,{className:"m-5 text-sm",source:t.objective.body})},g=function(e){return o.a.createElement("div",{key:e.id,className:"my-3 px-5"},o.a.createElement("div",{className:"flex justify-between"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.role)),o.a.createElement("span",{className:"text-xs font-medium"},"(",e.start," - ",e.end,")")),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},f=function(){return t.work&&t.work.enable&&o.a.createElement("div",null,o.a.createElement(i,{light:!0,title:t.work.heading}),t.work.items.filter((function(e){return e.enable})).map(g))},y=function(e){return o.a.createElement("div",{key:e.id,className:"flex flex-col"},o.a.createElement("h6",{className:"text-sm font-medium"},e.name),o.a.createElement("span",{className:"text-xs"},e.position),o.a.createElement("span",{className:"text-xs"},e.phone),o.a.createElement("span",{className:"text-xs"},e.email),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},v=function(){return t.references&&t.references.enable&&o.a.createElement("div",null,o.a.createElement(i,{light:!0,title:t.references.heading}),o.a.createElement("div",{className:"grid grid-cols-2 gap-6 px-5"},t.references.items.filter((function(e){return e.enable})).map(y)))},x=function(e){return o.a.createElement("div",{key:e.id,className:"flex flex-col my-2"},o.a.createElement("div",{className:"flex justify-between items-center"},o.a.createElement("h6",{className:"text-sm font-medium mb-1"},e.key),""!==e.level&&o.a.createElement("div",{className:"font-bold text-sm"},e.level)),0!==e.rating&&o.a.createElement("div",{className:"relative h-5"},o.a.createElement("div",{className:"absolute mb-1 inset-0",style:{backgroundColor:"rgba(0, 0, 0, 0.25)"}}),o.a.createElement("div",{className:"absolute mb-1 inset-0 rounded",style:{width:"".concat(20*e.rating,"%"),backgroundColor:"rgba(0, 0, 0, 0.3)"}})))},N=function(){return t.languages&&t.languages.enable&&o.a.createElement("div",null,o.a.createElement(i,{title:t.languages.heading}),o.a.createElement("div",{className:"px-5 mb-6"},t.languages.items.filter((function(e){return e.enable})).map(x)))},E=function(e){return o.a.createElement("div",{key:e.id,className:"my-3 px-5"},o.a.createElement("div",{className:"flex justify-between"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold"},e.name),o.a.createElement("p",{className:"text-xs"},e.major)),o.a.createElement("div",{className:"flex flex-col items-end"},o.a.createElement("span",{className:"text-sm font-bold"},e.grade),o.a.createElement("span",{className:"text-xs font-medium"},"(",e.start," - ",e.end,")"))),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},w=function(){return t.education&&t.education.enable&&o.a.createElement("div",null,o.a.createElement(i,{light:!0,title:t.education.heading}),t.education.items.filter((function(e){return e.enable})).map(E))},k=function(e){return o.a.createElement("div",{key:e.id,className:"my-3 px-5"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},S=function(){return t.awards&&t.awards.enable&&o.a.createElement("div",null,o.a.createElement(i,{light:!0,title:t.awards.heading}),t.awards.items.filter((function(e){return e.enable})).map(k))},O=function(e){return o.a.createElement("div",{key:e.id,className:"my-3 px-5"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},C=function(){return t.certifications&&t.certifications.enable&&o.a.createElement("div",null,o.a.createElement(i,{title:t.certifications.heading}),t.certifications.items.filter((function(e){return e.enable})).map(O))},j=function(e){return o.a.createElement("div",{key:e.id,className:"px-5 my-2"},o.a.createElement("h6",{className:"text-xs font-bold"},e.key),o.a.createElement("div",{className:"text-sm"},e.value))},D=function(){return t.extras&&t.extras.enable&&o.a.createElement("div",null,o.a.createElement(i,{light:!0,title:t.extras.heading}),t.extras.items.filter((function(e){return e.enable})).map(j))};return o.a.createElement("div",{style:{fontFamily:a.font.family,backgroundColor:a.colors.background,color:a.colors.primary}},o.a.createElement("div",{className:"grid grid-cols-12"},o.a.createElement("div",{className:"col-span-4",style:{color:a.colors.background,backgroundColor:a.colors.accent}},o.a.createElement(l,null),o.a.createElement(r,null),o.a.createElement(u,null),o.a.createElement(m,null),o.a.createElement(b,null),o.a.createElement(N,null),o.a.createElement(C,null)),o.a.createElement("div",{className:"col-span-8"},o.a.createElement(h,null),o.a.createElement(f,null),o.a.createElement(w,null),o.a.createElement(S,null),o.a.createElement(v,null),o.a.createElement(D,null))))},Cu=a(639),ju=a.n(Cu).a,Du=Ou,Au=function(){var e=Object(n.useContext)(Ms).state,t=e.data,a=e.theme,l=Cs(a.colors.accent)||{},r=l.r,i=l.g,s=l.b,c=function(){return""!==t.profile.photo&&o.a.createElement("img",{className:"w-40 h-40 rounded-full mx-auto",src:t.profile.photo,alt:"Resume Photograph"})},u=function(){return o.a.createElement("div",{className:"text-4xl font-bold leading-none"},o.a.createElement("h1",null,t.profile.firstName),o.a.createElement("h1",null,t.profile.lastName))},d=function(){return o.a.createElement("div",{className:"tracking-wide text-xs uppercase font-medium"},t.profile.subtitle)},m=function(e){var t=e.title,n=e.value;return n&&o.a.createElement("div",{className:"flex flex-col"},o.a.createElement("h6",{className:"text-xs font-bold",style:{color:a.colors.accent}},t),o.a.createElement("p",{className:"text-sm"},n))},p=function(){return o.a.createElement("div",{className:"w-full border-2 pl-4 pr-4 mb-6",style:{borderColor:a.colors.accent}},o.a.createElement("div",{className:"inline-block relative px-4",style:{top:"-.75em",color:a.colors.accent}},o.a.createElement("h2",{className:"flex"},o.a.createElement("i",{className:"material-icons"},"flare"))),o.a.createElement("div",{className:"grid grid-cols-1 row-gap-4"},o.a.createElement(m,{title:"Phone Number",value:t.profile.phone}),o.a.createElement(m,{title:"Email Address",value:t.profile.email}),o.a.createElement(m,{title:"Website",value:t.profile.website}),o.a.createElement("div",{className:"flex flex-col"},o.a.createElement("i",{className:"material-icons text-lg",style:{color:a.colors.accent}},"home"),o.a.createElement("p",{className:"text-sm"},t.profile.address.line1),o.a.createElement("p",{className:"text-sm"},t.profile.address.line2),o.a.createElement("p",{className:"text-sm"},t.profile.address.line3))))},b=function(e){var t=e.title;return o.a.createElement("h6",{className:"text-sm font-semibold uppercase pb-1 mb-2 border-b",style:{borderColor:a.colors.accent,color:a.colors.accent}},t)},h=function(){return t.objective.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.objective.heading}),o.a.createElement(pu.a,{className:"text-sm text-justify",source:t.objective.body}))},g=function(e){return o.a.createElement("div",{key:e.id,className:"mt-3"},o.a.createElement("div",{className:"flex justify-between"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold text-sm"},e.title),o.a.createElement("p",{className:"text-xs opacity-75 font-medium"},e.role," / ",e.start," - ",e.end))),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},f=function(){return t.work&&t.work.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.work.heading}),t.work.items.filter((function(e){return e.enable})).map(g))},y=function(e){return o.a.createElement("div",{key:e.id,className:"mt-3"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold text-xs"},e.name),o.a.createElement("p",{className:"text-xs opacity-75"},e.major),o.a.createElement("p",{className:"text-xs opacity-75"},e.start," - ",e.end)),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},v=function(){return t.education&&t.education.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.education.heading}),o.a.createElement("div",{className:"grid grid-cols-2 gap-4"},t.education.items.filter((function(e){return e.enable})).map(y)))},x=function(e){return o.a.createElement("div",{key:e.id,className:"mt-3 text-left"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},N=function(){return t.awards&&t.awards.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.awards.heading}),t.awards.items.filter((function(e){return e.enable})).map(x))},E=function(e){return o.a.createElement("div",{key:e.id,className:"mt-3 text-left"},o.a.createElement("h6",{className:"font-semibold"},e.title),o.a.createElement("p",{className:"text-xs"},e.subtitle),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},w=function(){return t.certifications&&t.certifications.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.certifications.heading}),t.certifications.items.filter((function(e){return e.enable})).map(E))},k=function(e){return o.a.createElement("li",{key:e.id,className:"text-xs font-medium"},e.skill)},S=function(){return t.skills&&t.skills.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.skills.heading}),o.a.createElement("ul",{className:"pt-2 grid grid-cols-2 gap-3"},t.skills.items.map(k)))},O=function(e){return o.a.createElement("li",{key:e.id,className:"text-xs font-medium"},e.hobby)},C=function(){return t.hobbies&&t.hobbies.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.hobbies.heading}),o.a.createElement("ul",{className:"pt-2 grid grid-cols-2 row-gap-3 text-left"},t.hobbies.items.map(O)))},j=function(e){return o.a.createElement("div",{key:e.id,className:"grid grid-cols-2 items-center py-2"},o.a.createElement("h6",{className:"text-xs font-medium text-left"},e.key),o.a.createElement("div",{className:"flex"},e.level&&o.a.createElement("div",{className:"font-bold text-sm mr-2"},e.level),0!==e.rating&&o.a.createElement("div",{className:"flex"},Array.from(Array(e.rating)).map((function(e,t){return o.a.createElement("i",{key:t,className:"material-icons text-lg",style:{color:a.colors.accent}},"star")})))))},D=function(){return t.languages&&t.languages.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.languages.heading}),o.a.createElement("div",{className:"w-3/4"},t.languages.items.filter((function(e){return e.enable})).map(j)))},A=function(e){return o.a.createElement("div",{key:e.id,className:"flex flex-col"},o.a.createElement("h6",{className:"text-sm font-medium"},e.name),o.a.createElement("span",{className:"text-xs"},e.position),o.a.createElement("span",{className:"text-xs"},e.phone),o.a.createElement("span",{className:"text-xs"},e.email),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:e.description}))},T=function(){return t.references&&t.references.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.references.heading}),o.a.createElement("div",{className:"grid grid-cols-3 gap-8"},t.references.items.filter((function(e){return e.enable})).map(A)))},J=function(e){return o.a.createElement("tr",{key:e.id},o.a.createElement("td",{className:"border font-medium px-4 py-2 text-xs"},e.key),o.a.createElement("td",{className:"border px-4 py-2 text-xs"},e.value))},P=function(){return t.extras&&t.extras.enable&&o.a.createElement("div",null,o.a.createElement(b,{title:t.extras.heading}),o.a.createElement("table",{className:"mt-4 w-2/3 table-auto"},o.a.createElement("tbody",null,t.extras.items.filter((function(e){return e.enable})).map(J))))};return o.a.createElement("div",{style:{fontFamily:a.font.family,backgroundColor:a.colors.background,color:a.colors.primary}},o.a.createElement("div",{className:"grid grid-cols-12"},o.a.createElement("div",{className:"h-full col-span-4 p-8 grid grid-cols-1 row-gap-4 text-center",style:{backgroundColor:"rgba(".concat(r,", ").concat(i,", ").concat(s,", 0.1)"),minHeight:"29.7cm"}},o.a.createElement("div",{className:"grid grid-cols-1 gap-2"},o.a.createElement(c,null),o.a.createElement(u,null),o.a.createElement(d,null)),o.a.createElement(p,null),o.a.createElement(h,null),o.a.createElement(C,null),o.a.createElement(D,null),o.a.createElement(w,null)),o.a.createElement("div",{className:"col-span-8 p-8 grid grid-cols-1 row-gap-4"},o.a.createElement(f,null),o.a.createElement(v,null),o.a.createElement(S,null),o.a.createElement(N,null),o.a.createElement(T,null),o.a.createElement(P,null))))},Tu=a(640),Ju=a.n(Tu).a,Pu=Au,Ru=a(16),Fu={header:{position:"absolute",left:0,right:0,zIndex:0,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"start",color:"white",backgroundColor:"#222",height:"160px",paddingLeft:"270px"},section:{marginTop:"167px",marginLeft:"20px"}},Lu=function(){var e=Object(n.useContext)(Ms).state,t=e.data,a=e.theme,l=Cs(a.colors.accent)||{},r=l.r,i=l.g,s=l.b,c=function(e){var t=e.title,a=e.className;return o.a.createElement("h5",{className:"my-2 text-md uppercase font-semibold tracking-wider pb-1 border-b-2 border-gray-800 ".concat(a)},t)},u=function(){return""!==_s.get(t,'jsonld["@graph"][1].image.contentUrl',"")&&o.a.createElement("div",{className:"relative z-40"},o.a.createElement("img",{className:"w-full object-cover object-center",src:_s.get(t,'jsonld["@graph"][1].image.contentUrl',""),alt:"Person Photograph",style:{height:"160px"}}))||o.a.createElement("div",{className:"relative z-40"},o.a.createElement("div",{style:{height:"160px"}}))},d=function(){return o.a.createElement("h6",{className:"text-lg tracking-wider uppercase"},t.jsonld["@graph"][1].givenName[1]?" ("+t.jsonld["@graph"][1].givenName.map((function(e,a){if(a>0&&e["@value"]){var n=e["@value"],o=t.jsonld["@graph"][1].familyName.findIndex((function(t){return t["@language"]===e["@language"]}));return o>=0&&t.jsonld["@graph"][1].familyName[o]&&t.jsonld["@graph"][1].familyName[o]["@value"]&&(n+=" "+t.jsonld["@graph"][1].familyName[o]["@value"]),n}return null})).filter((function(e){return null!=e})).join(", ")+")":"")},m=function(){return o.a.createElement("h1",{className:"tracking-wide uppercase font-semibold",style:{fontSize:"2.75em"}},Array.isArray(t.jsonld["@graph"][1].givenName)?t.jsonld["@graph"][1].givenName[0]["@value"]:t.jsonld["@graph"][1].givenName," ",Array.isArray(t.jsonld["@graph"][1].familyName)?t.jsonld["@graph"][1].familyName[0]["@value"]:t.jsonld["@graph"][1].familyName)},p=function(){return o.a.createElement("header",{style:Fu.header},o.a.createElement("div",{className:"ml-6"},o.a.createElement(m,null),o.a.createElement(d,null),o.a.createElement("h6",{className:"text-lg tracking-wider uppercase"},_s.get(t,'jsonld["@graph"][1].description',""))))},b=function(){return t.objective&&t.objective.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{title:t.objective.heading}),_s.get(t,'jsonld["@graph"][1].seeks',[]).map((function(e,t){return o.a.createElement(pu.a,{key:"objetive_"+t,className:"mr-10 text-sm",source:e.description})})),_s.get(t,'jsonld["@graph"][1].seeks',[]).map((function(e,t){return o.a.createElement("div",{key:"holder_"+t},o.a.createElement("p",{className:"text-xs text-gray-800",key:"p_"+t},(_s.get(e,"availableAtOrFrom.address.addressCountry",null)||_s.get(e,"availableAtOrFrom.address.addressRegion",null)||_s.get(e,"availableAtOrFrom.address.addressLocality",null),""),(_s.get(e,"availableAtOrFrom.address.addressLocality",null)?_s.get(e,"availableAtOrFrom.address.addressLocality","")+" ":"")+(_s.get(e,"availableAtOrFrom.address.addressRegion",null)?_s.get(e,"availableAtOrFrom.address.addressRegion","")+" ":"")+(_s.get(e,"availableAtOrFrom.address.addressCountry",null)?_s.get(e,"availableAtOrFrom.address.addressCountry",""):"")," | ",_s.get(e,"availabilityStarts",null)?_s.get(e,"availabilityStarts",""):""," ",_s.get(e,"availabilityEnds",null)?"- "+_s.get(e,"availabilityEnds",""):""))})))},h=function(e){var t=e.label,a=e.value;return a&&o.a.createElement("div",{className:"mb-3"},o.a.createElement("h6",{className:"text-xs font-bold"},t),o.a.createElement("p",{className:"text-sm"},a))},g=function(){return t.jsonld["@graph"][1].address&&t.jsonld["@graph"][1].address.length>0&&t.address.enable&&o.a.createElement("div",{className:"mb-6"},t.jsonld["@graph"][1].address.filter((function(e){return Date.parse(e.hoursAvailable.validThrough)-Date.parse(new Date)>0})).map(f))||""},f=function(e,a){return o.a.createElement("div",{className:"mb-3",key:_s.get(e,"@id","main")},0===a?o.a.createElement("h6",{className:"text-xs font-bold"},t.profile.address.heading||"Address"):"",o.a.createElement("p",{className:"text-sm"},e.streetAddress),o.a.createElement("p",{className:"text-sm"},e.addressLocality," ",e.addressRegion),o.a.createElement("p",{className:"text-sm"},e.addressCountry," ",e.postalCode))},y=function(){return t.contacts.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{title:"Contact",className:"mt-8 w-3/4 mx-auto"}),o.a.createElement(g,null),o.a.createElement(h,{label:"Phone",value:_s.get(_s.find(t.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"telephone","")}),o.a.createElement(h,{label:"Email Address",value:_s.get(_s.find(t.jsonld["@graph"][1].contactPoint,{contactType:"Preferred"}),"email","")}),o.a.createElement(h,{label:"Website",value:_s.get(t,'jsonld["@graph"][1].sameAs[0]',"")}))},v=function(e){return e&&o.a.createElement("p",{key:Object(ac.a)()},"| ",e," ")},x=function(e){var t=e.skills;return t&&t.length>0&&o.a.createElement("div",{className:"text-xs text-gray-800 flex"},t.filter((function(e){return""!==e})).map(v))},N=function(e){return e&&o.a.createElement("li",{className:"mt-2 text-sm",key:Object(ac.a)()},e)},E=function(e){var t=e.responsibilities;return t&&t.length>0&&o.a.createElement("ul",null,t.filter((function(e){return""!==e})).map(N))},w=function(e){return o.a.createElement("div",{key:_s.get(e,"@id","main"),className:"my-3 mr-10"},o.a.createElement("div",null,o.a.createElement("h6",{className:"font-semibold"},_s.get(e,"subjectOf.organizer.name","")),o.a.createElement("p",{className:"text-xs text-gray-800"},_s.get(e,"roleName","")," | ",_s.get(e,"startDate","")," - ",_s.get(e,"endDate",""))),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:_s.get(e,"description","")}),o.a.createElement(E,{responsibilities:_s.get(e,"hasOccupation.responsibilities",[])}),o.a.createElement(x,{skills:_s.get(e,"hasOccupation.skills",[])}))},k=function(){return _s.get(t,"jsonld['@graph'][1].hasOccupation",[]).length&&t.work.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{title:t.work.heading}),_s.get(t,"jsonld['@graph'][1].hasOccupation",[]).filter((function(e){return!_s.get(e,"@id","").endsWith("disable")})).map(w))},S=function(e){return o.a.createElement("div",{key:_s.get(e,"@id","main"),className:"my-3 mr-10"},o.a.createElement("h6",{className:"font-semibold"},_s.get(e,"about.provider.name","")),o.a.createElement("p",{className:"text-xs"},_s.get(e,"educationalLevel","")," ",_s.get(e,"about.educationalCredentialAwarded","")),o.a.createElement("div",{className:"text-xs"},_s.get(e,"about.startDate","")," - ",_s.get(e,"about.endDate","")),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:_s.get(e,"abstract","")}),o.a.createElement(x,{skills:_s.get(e,"teaches",[])}))},O=function(){return _s.get(t,"jsonld['@graph'][1].hasCredential",[]).length>0&&t.education.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{title:t.education.heading}),_s.get(t,"jsonld['@graph'][1].hasCredential",[]).filter((function(e){return!_s.get(e,"@id","").endsWith("disable")&&"degree"===_s.get(e,"credentialCategory","")})).map(S))},C=function(){return t.skills.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{title:"Skills",className:"w-3/4 mx-auto"}),o.a.createElement("ul",{className:"list-none text-sm"},function(){var e=_s.chain(_s.get(t,"jsonld['@graph'][1].hasOccupation",[])).map("skills").flatten(),a=_s.chain(_s.get(t,"jsonld['@graph'][0].award",[])).map("skill:assesses").flatten(),n=_s.chain(_s.get(t,"jsonld['@graph'][1].hasCredential",[])).map("teaches").flatten(),o=_s.chain(_s.get(t,"jsonld['@graph'][1].hasCredential",[])).map("about").flatten().map("hasCourse").flatten().map("teaches").flatten(),l=_s.chain(_s.get(t,"jsonld['@graph'][1].hasCredential",[])).map("about").map("workExample").flatten().map("hasPart").flatten().map("teaches").flatten(),r=_s.chain(_s.get(t,"jsonld['@graph'][1].interactionStatistic",[])).map("result").flatten().map("teaches").flatten(),i=_s.chain(_s.get(t,"jsonld['@graph'][1].interactionStatistic",[])).map("result").flatten().map("assesses").flatten();return[].concat(Object(Ru.a)(e),Object(Ru.a)(a),Object(Ru.a)(n),Object(Ru.a)(o),Object(Ru.a)(l),Object(Ru.a)(r),Object(Ru.a)(i))}().map((function(e){return o.a.createElement("li",{key:Object(ac.a)(),className:"my-2"},e)}))))},j=function(){return t.memberships.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{title:"Memberships",className:"w-3/4 mx-auto"}),o.a.createElement("ul",{className:"list-none text-sm"},_s.get(t.jsonld["@graph"][1],"memberOf",[]).map((function(e){return o.a.createElement("li",{key:_s.get(e,"@id",""),className:"my-2"},_s.get(e,"memberOf.programName",""))}))))},D=function(e){return o.a.createElement("div",{key:_s.get(e,"@id","main"),className:"flex flex-col"},o.a.createElement("h6",{className:"text-sm font-semibold"},_s.get(e,"interactionType.participant.givenName","")," ",_s.get(e,"interactionType.participant.familyName","")),o.a.createElement("span",{className:"text-sm"},_s.get(e,"interactionType.participant.jobTitle","")),o.a.createElement("span",{className:"text-sm"},_s.get(e,"interactionType.participant.telephone","")),o.a.createElement("span",{className:"text-sm"},_s.get(e,"interactionType.participant.email","")),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:_s.get(e,"result[0].reviewRating.ratingExplanation","")}))},A=function(){return t.references&&t.references.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{title:t.references.heading}),o.a.createElement("div",{className:"grid grid-cols-2 col-gap-4 row-gap-2"},_s.get(t.jsonld["@graph"][1],"interactionStatistic",[]).filter((function(e){return"Reference"===_s.get(e,"disambiguatingDescription","")})).map(D)))},T=function(e){return o.a.createElement("div",{key:_s.get(e,"@id",""),className:"grid grid-cols-2 items-center py-2"},o.a.createElement("h6",{className:"text-xs font-medium text-left"},_s.get(e,"name","")),o.a.createElement("div",{className:"flex"},e.level&&o.a.createElement("div",{className:"font-bold text-sm mr-2"},e.level),0!==e.rating&&o.a.createElement("div",{className:"flex"},Array.from(Array(e.rating)).map((function(e,t){return o.a.createElement("i",{key:t,className:"material-icons text-lg",style:{color:a.colors.accent}},"star")})))))},J=function(){return t.languages&&t.languages.enable&&_s.get(t,'jsonld["@graph"][1].knowsLanguage',[]).length>0&&o.a.createElement("div",{className:"w-3/4 mx-auto mb-6"},o.a.createElement(c,{title:t.languages.heading}),o.a.createElement("div",null,_s.get(t,'jsonld["@graph"][1].knowsLanguage',[]).filter((function(e){return""!==_s.get(e,"name","")})).map(T)))},P=function(e){return o.a.createElement("div",{key:_s.get(e,"@id","main"),className:"my-2"},o.a.createElement("h6",{className:"font-semibold"},_s.get(e,"skill:title","")),o.a.createElement("p",{className:"text-xs"},_s.get(e,"skill:nativeLabel","")),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:_s.get(e,"description","")}))},R=function(){return t.awards&&t.awards.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{light:!0,title:t.awards.heading}),_s.get(t.jsonld["@graph"][0],"award",[]).filter((function(e){return""!==e["skill:title"]})).map(P))},F=function(e){return o.a.createElement("div",{key:_s.get(e,"@id","main"),className:"my-2"},o.a.createElement("h6",{className:"font-semibold"},_s.get(e,"educationalLevel","")),o.a.createElement("p",{className:"text-xs"},_s.get(e,"about.educationalCredentialAwarded","")),o.a.createElement(pu.a,{className:"mt-2 text-sm",source:_s.get(e,"abstract","")}))},L=function(){return t.certifications&&t.certifications.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{title:t.certifications.heading,className:"w-3/4 mx-auto"}),_s.get(t,"jsonld['@graph'][1].hasCredential",[]).filter((function(e){return!_s.get(e,"@id","").endsWith("disable")&&"Degree"!==_s.get(e,"credentialCategory","")})).map(F))},z=function(e){return o.a.createElement("div",{key:_s.get(e,"@id","main"),className:"my-3"},o.a.createElement("h6",{className:"text-xs font-bold"},_s.get(e,"propertyID","")),o.a.createElement("div",{className:"text-sm"},_s.get(e,"value","")))},I=function(){return t.extras&&t.extras.enable&&o.a.createElement("div",{className:"mb-6"},o.a.createElement(c,{title:t.extras.heading,className:"w-3/4 mx-auto"}),_s.get(t.jsonld["@graph"][1],"identifier",[]).filter((function(e){return""!==e.value})).map(z))};return o.a.createElement("div",{style:{fontFamily:a.font.family,backgroundColor:a.colors.background,color:a.colors.primary}},o.a.createElement("div",{className:"grid grid-cols-12"},o.a.createElement("div",{className:"sidebar col-span-4 pb-8 ml-8 z-10 text-center",style:{backgroundColor:"rgba(".concat(r,", ").concat(i,", ").concat(s,", 0.1)")}},o.a.createElement(u,null),o.a.createElement(y,null),o.a.createElement(C,null),o.a.createElement(j,null),o.a.createElement(J,null),o.a.createElement(L,null),o.a.createElement(I,null)),o.a.createElement("div",{className:"col-span-8"},o.a.createElement(p,null),o.a.createElement("section",{className:"py-4",style:Fu.section},o.a.createElement(b,null),o.a.createElement(k,null),o.a.createElement(O,null),o.a.createElement(R,null),o.a.createElement(A,null)))))},zu=a(641),Iu=[{key:"onyx",name:"Onyx",component:fu,preview:gu},{key:"pikachu",name:"Pikachu",component:Nu,preview:xu},{key:"gengar",name:"Gengar",component:Su,preview:ku},{key:"castform",name:"Castform",component:Du,preview:ju},{key:"glalie",name:"Glalie",component:Pu,preview:Ju},{key:"celebi",name:"Celebi",component:Lu,preview:a.n(zu).a}],Mu=function(e){var t=e.theme,a=e.onChange;return o.a.createElement("div",{className:"grid grid-cols-2 gap-6"},Iu.map((function(e){return o.a.createElement("div",{key:e.key,className:"text-center",onClick:function(){return a("theme.layout",e.key)}},o.a.createElement("img",{className:"rounded cursor-pointer object-cover border shadow hover:shadow-md ".concat(t.layout.toLowerCase()===e.key?"border-gray-600 hover:border-gray-600":"border-transparent "," hover:border-gray-500 cursor-pointer"),src:e.preview,alt:e.name}),o.a.createElement("p",{className:"mt-1 text-sm font-medium"},e.name))})))},Hu=["#f44336","#E91E63","#9C27B0","#673AB7","#3F51B5","#2196F3","#03A9F4","#00BCD4","#009688","#4CAF50","#8BC34A","#CDDC39","#FFEB3B","#FFC107","#FF9800","#FF5722","#795548","#9E9E9E","#607D8B","#FAFAFA","#212121","#263238"],Gu=function(e){var t=e.theme,a=e.onChange,n=Object(Ws.a)("rightSidebar").t,l=function(e){!function(e){var t=document.createElement("textarea");t.style.position="fixed",t.style.top=0,t.style.left=0,t.style.width="2em",t.style.height="2em",t.style.padding=0,t.style.border="none",t.style.outline="none",t.style.boxShadow="none",t.style.background="transparent",t.value=e,document.body.appendChild(t),t.focus(),t.select();var a=document.execCommand("copy");document.body.removeChild(t)}(e),Object(i.a)(n("colors.clipboardCopyAction",{color:e}),{bodyClassName:"text-center text-gray-800 py-2"}),a("theme.colors.accent",e)};return o.a.createElement("div",null,o.a.createElement("div",{className:"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-4"},n("colors.colorOptions")),o.a.createElement("div",{className:"mb-6 grid grid-cols-8 col-gap-2 row-gap-3"},Hu.map((function(e){return o.a.createElement("div",{key:e,className:"cursor-pointer rounded-full border border-gray-200 h-6 w-6 hover:opacity-75",style:{backgroundColor:e},onClick:function(){return l(e)}})}))),o.a.createElement("hr",{className:"my-6"}),o.a.createElement("div",{className:"my-6 grid grid-cols-6 items-end"},o.a.createElement("div",{className:"rounded-full w-8 h-8 mb-2 border-2",style:{backgroundColor:t.colors.primary}}),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{label:n("colors.primaryColor"),placeholder:"#FFFFFF",value:t.colors.primary,onChange:function(e){return a("theme.colors.primary",e)}}))),o.a.createElement("div",{className:"my-6 grid grid-cols-6 items-end"},o.a.createElement("div",{className:"rounded-full w-8 h-8 mb-2 border-2",style:{backgroundColor:t.colors.accent}}),o.a.createElement("div",{className:"col-span-5"},o.a.createElement(ec,{label:n("colors.accentColor"),placeholder:"#FFFFFF",value:t.colors.accent,onChange:function(e){return a("theme.colors.accent",e)}}))))},qu=["Lato","Montserrat","Nunito","Open Sans","Raleway","Rubik","Source Sans Pro","Titillium Web","Ubuntu"],Yu=function(e){var t=e.theme,a=e.onChange,n=Object(Ws.a)("rightSidebar").t;return o.a.createElement("div",{className:"grid grid-cols-1 gap-6"},qu.map((function(e){return o.a.createElement("div",{key:e,style:{fontFamily:e},onClick:function(){return a("theme.font.family",e)},className:"w-full rounded border py-4 shadow text-xl text-center ".concat(t.font.family===e?"border-gray-500":"border-transparent"," hover:border-gray-400 cursor-pointer")},e)})),o.a.createElement("div",null,o.a.createElement(ec,{className:"mb-3",label:n("fonts.fontFamily.label"),placeholder:"Avenir Next",value:t.font.family,onChange:function(e){return a("theme.font.family",e)}}),o.a.createElement("p",{className:"text-gray-800 text-xs"},n("fonts.fontFamily.helpText"))))},Wu=a(642),Uu=a(643),_u=function(e){var t=e.data,a=(e.theme,e.dispatch),l=Object(n.useContext)(Ys).setPrintDialogOpen,r=Object(Ws.a)("rightSidebar").t,i=Object(n.useRef)(null);return o.a.createElement("div",null,o.a.createElement("div",{className:"shadow text-center text-sm p-5"},r("actions.disclaimer")),o.a.createElement("hr",{className:"my-6"}),o.a.createElement("div",{className:"shadow text-center p-5"},o.a.createElement("h6",{className:"font-bold text-sm mb-2"},r("actions.importExport.heading")),o.a.createElement("p",{className:"text-sm"},r("actions.importExport.body")),o.a.createElement("input",{ref:i,type:"file",className:"hidden",onChange:function(e){return function(e,t){var a=new FileReader;a.addEventListener("load",(function(){var e=JSON.parse(a.result);t({type:"import_data",payload:e}),t({type:"save_data"})})),a.readAsText(e.target.files[0])}(e,a)}}),o.a.createElement("a",{id:"downloadAnchor",className:"hidden"}),o.a.createElement("div",{className:"mt-4 grid grid-cols-2 col-gap-6"},o.a.createElement("button",{type:"button",onClick:function(){return i.current.click()},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"publish"),o.a.createElement("span",{className:"text-sm"},r("actions.importExport.buttons.import")))),o.a.createElement("button",{type:"button",onClick:function(){var e=_s.cloneDeep(t.jsonld),a='<script type="application/ld+json">'+JSON.stringify(e)+"<\/script>";_s.set(e["@graph"][1],"@context","http://schema.org/");var n=a+('<script type="application/ld+json">'+JSON.stringify(e["@graph"][1])+"<\/script>"),o=new Wu;o.file("script.js",n),o.file("resume.json",JSON.stringify(t)),o.generateAsync({type:"blob"}).then((function(e){Object(Uu.saveAs)(e,"jsonldresume.zip")}))},className:"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"get_app"),o.a.createElement("span",{className:"text-sm"},r("actions.importExport.buttons.export")))))),o.a.createElement("hr",{className:"my-6"}),o.a.createElement("div",{className:"shadow text-center p-5"},o.a.createElement("h6",{className:"font-bold text-sm mb-2"},r("actions.downloadResume.heading")),o.a.createElement("div",{className:"text-sm"},r("actions.downloadResume.body")),o.a.createElement("button",{type:"button",onClick:function(){return l(!0)},className:"mt-4 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"save"),o.a.createElement("span",{className:"text-sm"},r("actions.downloadResume.buttons.saveAsPdf"))))),o.a.createElement("hr",{className:"my-6"}),o.a.createElement("div",{className:"shadow text-center p-5"},o.a.createElement("h6",{className:"font-bold text-sm mb-2"},r("actions.loadDemoData.heading")),o.a.createElement("div",{className:"text-sm"},r("actions.loadDemoData.body")),o.a.createElement("button",{type:"button",onClick:function(){a({type:"load_demo_data"}),a({type:"save_data"})},className:"mt-4 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"flight_takeoff"),o.a.createElement("span",{className:"text-sm"},r("actions.loadDemoData.buttons.loadData"))))),o.a.createElement("hr",{className:"my-6"}),o.a.createElement("div",{className:"shadow text-center p-5"},o.a.createElement("h6",{className:"font-bold text-sm mb-2"},r("actions.reset.heading")),o.a.createElement("div",{className:"text-sm"},r("actions.reset.body")),o.a.createElement("button",{type:"button",onClick:function(){a({type:"reset"}),a({type:"save_data"})},className:"mt-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"refresh"),o.a.createElement("span",{className:"text-sm"},r("actions.reset.buttons.reset"))))))},Vu=function(){var e=Object(Ws.a)("rightSidebar").t;return o.a.createElement("div",null,o.a.createElement("div",{className:"shadow text-center p-5"},o.a.createElement("h6",{className:"font-bold text-sm mb-2"},e("about.documentation.heading")),o.a.createElement("div",{className:"text-sm"},e("about.documentation.body")),o.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://docs.jsonldresume.org/",className:"flex justify-center items-center mt-4 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"description"),o.a.createElement("span",{className:"text-sm"},e("about.documentation.buttons.documentation"))))),o.a.createElement("hr",{className:"my-5"}),o.a.createElement("div",{className:"shadow text-center p-5"},o.a.createElement("h6",{className:"font-bold text-sm mb-2"},e("about.bugOrFeatureRequest.heading")),o.a.createElement("div",{className:"text-sm"},e("about.bugOrFeatureRequest.body")),o.a.createElement("div",{className:"grid grid-cols-1"},o.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://github.com/AmruthPillai/Reactive-Resume/issues/new",className:"mt-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"bug_report"),o.a.createElement("span",{className:"text-sm"},e("about.bugOrFeatureRequest.buttons.raiseIssue")))),o.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"mailto:<EMAIL>?subject=Feature Request/Reporting a Bug in Reactive Resume: ",className:"mt-4 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"email"),o.a.createElement("span",{className:"text-sm"},e("about.bugOrFeatureRequest.buttons.sendEmail")))))),o.a.createElement("hr",{className:"my-5"}),o.a.createElement("div",{className:"shadow text-center p-5"},o.a.createElement("h6",{className:"font-bold text-sm mb-2"},e("about.sourceCode.heading")),o.a.createElement("div",{className:"text-sm"},e("about.sourceCode.body")),o.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://github.com/AmruthPillai/Reactive-Resume",className:"flex justify-center items-center mt-4 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"code"),o.a.createElement("span",{className:"text-sm"},e("about.sourceCode.buttons.githubRepo"))))),o.a.createElement("hr",{className:"my-5"}),o.a.createElement("div",{className:"shadow text-center p-5"},o.a.createElement("h6",{className:"font-bold text-sm mb-2"},e("about.license.heading")),o.a.createElement("div",{className:"text-sm"},e("about.license.body")),o.a.createElement("a",{target:"_blank",rel:"noopener noreferrer",href:"https://github.com/AmruthPillai/Reactive-Resume/blob/master/LICENSE",className:"flex justify-center items-center mt-4 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"gavel"),o.a.createElement("span",{className:"text-sm"},e("about.license.buttons.mitLicense"))))),o.a.createElement("div",{className:"mt-5"},o.a.createElement("p",{className:"text-xs font-gray-600 text-center"},o.a.createElement(fc.a,{t:e,i18nKey:"about.footer.credit"},"Made with Love by",o.a.createElement("a",{className:"font-bold hover:underline",href:"https://www.amruthpillai.com/",rel:"noopener noreferrer",target:"_blank"},"Amruth Pillai"))),o.a.createElement("p",{className:"text-xs font-gray-600 text-center"},e("about.footer.thanks"))))},Bu=function(e){var t=e.settings,a=e.onChange,n=Object(Ws.a)("rightSidebar").t;return o.a.createElement("div",null,o.a.createElement(Vs,{label:n("settings.language.label"),value:t.language,onChange:function(e){return a("settings.language",e)},options:ds,optionItem:function(e){return o.a.createElement("option",{key:e.code,value:e.code},e.name)}}),o.a.createElement("p",{className:"text-gray-800 text-xs"},o.a.createElement(fc.a,{t:n,i18nKey:"settings.language.helpText"},"If you would like to help translate the app into your own language, please refer the",o.a.createElement("a",{className:"text-blue-600 hover:underline",target:"_blank",rel:"noopener noreferrer",href:"https://docs.rxresu.me/translation/"},"Translation Documentation"),".")))},Ku=function(){var e=Object(Ws.a)("rightSidebar").t,t=Object(n.useContext)(Ms),a=t.state,l=t.dispatch,r=a.data,i=a.theme,s=a.settings,c=[{key:"templates",name:e("templates.title")},{key:"colors",name:e("colors.title")},{key:"fonts",name:e("fonts.title")},{key:"actions",name:e("actions.title")},{key:"settings",name:e("settings.title")},{key:"about",name:e("about.title")}],u=Object(n.useState)(c[0].key),d=Object(bs.a)(u,2),m=d[0],p=d[1],b=function(e,t){l({type:"on_input",payload:{key:e,value:t}}),l({type:"save_data"})};return o.a.createElement("div",{id:"rightSidebar",className:"animated slideInRight z-10 py-6 h-screen bg-white col-span-1 shadow-2xl overflow-y-scroll"},o.a.createElement(Bs,{tabs:c,currentTab:m,setCurrentTab:p}),o.a.createElement("div",{className:"px-6"},function(){switch(m){case c[0].key:return o.a.createElement(Mu,{theme:i,onChange:b});case c[1].key:return o.a.createElement(Gu,{theme:i,onChange:b});case c[2].key:return o.a.createElement(Yu,{theme:i,onChange:b});case c[3].key:return o.a.createElement(_u,{data:r,theme:i,dispatch:l});case c[4].key:return o.a.createElement(Bu,{settings:s,onChange:b});case c[5].key:return o.a.createElement(Vu,null);default:return null}}()))},Zu=function(){var e=Object(n.useContext)(Ys),t=e.panZoomRef,a=e.setPrintDialogOpen;return o.a.createElement("div",{id:"pageController",className:"absolute z-20 opacity-75 hover:opacity-100 transition-all duration-150"},o.a.createElement("div",{className:"text-2xl px-8 border border-gray-200 rounded-full bg-white flex justify-center items-center leading-none select-none"},o.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){return t.current.zoomIn(2)}},o.a.createElement("i",{className:"material-icons"},"zoom_in")),o.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){return t.current.zoomOut(2)}},o.a.createElement("i",{className:"material-icons"},"zoom_out")),o.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){t.current.autoCenter(1),t.current.reset(1)}},o.a.createElement("i",{className:"material-icons"},"center_focus_strong")),o.a.createElement("div",{className:"text-gray-400 p-3"},"|"),o.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){return window.print()}},o.a.createElement("i",{className:"material-icons"},"print")),o.a.createElement("div",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",onClick:function(){return a(!0)}},o.a.createElement("i",{className:"material-icons"},"save")),o.a.createElement("div",{className:"text-gray-400 p-3"},"|"),o.a.createElement("a",{className:"p-3 hover:bg-gray-200 cursor-pointer flex",href:"https://doc.jsonldresume.org/",target:"_blank",rel:"noopener noreferrer"},o.a.createElement("i",{className:"material-icons"},"help_outline"))))},Qu=a(59),Xu=a.n(Qu),$u=a(644),ed=function(){var e=Object(Ws.a)().t,t=Object(n.useContext)(Ys),a=t.pageRef,l=t.panZoomRef,r=t.isPrintDialogOpen,i=t.setPrintDialogOpen,s=[{key:"unconstrained",value:"".concat(e("printDialog.printType.types.unconstrained"))},{key:"fitInA4",value:"".concat(e("printDialog.printType.types.fitInA4"))},{key:"multiPageA4",value:"".concat(e("printDialog.printType.types.multiPageA4"))}],c=Object(n.useState)(80),u=Object(bs.a)(c,2),d=u[0],m=u[1],p=Object(n.useState)(s[0].key),b=Object(bs.a)(p,2),h=b[0],g=b[1];return o.a.createElement("div",{className:"absolute inset-0 transition-all duration-200 ease-in-out ".concat(r?"opacity-100 z-20":"opacity-0 z-0"),style:{backgroundColor:"rgba(0, 0, 0, 0.25)"},onClick:function(){i(!1)}},o.a.createElement("div",{className:"centered py-8 px-12 bg-white shadow-xl rounded w-full md:w-1/3",onClick:function(e){e.stopPropagation(),e.preventDefault()}},o.a.createElement("h5",{className:"mb-6 text-lg font-bold"},e("printDialog.heading")),o.a.createElement("h6",{className:"mb-1 text-sm font-medium"},e("printDialog.quality.label")),o.a.createElement("div",{className:"flex items-center"},o.a.createElement("input",{type:"range",className:"w-full h-4 my-2 rounded-full overflow-hidden appearance-none focus:outline-none bg-gray-400",value:d,onChange:function(e){return m(e.target.value)},min:"40",max:"100",step:"5"}),o.a.createElement("h6",{className:"font-medium pl-5"},d,"%")),o.a.createElement("h6",{className:"mt-4 mb-2 text-sm font-medium"},e("printDialog.printType.label")),o.a.createElement(Vs,{value:h,options:s,onChange:g,optionItem:function(e){return o.a.createElement("option",{key:e.key,value:e.key},e.value)}}),o.a.createElement("p",{className:"my-3 text-xs text-gray-600"},e("printDialog.helpText.0")),o.a.createElement("p",{className:"my-3 text-xs text-gray-600"},e("printDialog.helpText.1")),o.a.createElement("div",{className:"flex justify-between"},o.a.createElement("button",{type:"button",onClick:function(){i(!1)},className:"mt-6 border border-red-600 text-red-600 hover:bg-red-600 hover:text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"close"),o.a.createElement("span",{className:"text-sm"},e("printDialog.buttons.cancel")))),o.a.createElement("button",{type:"button",onClick:Object($u.a)(Xu.a.mark((function e(){return Xu.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,"multiPageA4"===h?Ps(a,l,d):Ts(a,l,d,h);case 2:i(!1);case 3:case"end":return e.stop()}}),e)}))),className:"mt-6 border border-gray-700 text-gray-700 hover:bg-gray-700 hover:text-white text-sm font-medium py-2 px-5 rounded"},o.a.createElement("div",{className:"flex justify-center items-center"},o.a.createElement("i",{className:"material-icons mr-2 font-bold text-base"},"save"),o.a.createElement("span",{className:"text-sm"},e("printDialog.buttons.saveAsPdf")))))))},td=a(645),ad=a.n(td),nd=function(){var e=Object(Ws.a)().t,t=Object(n.useState)(!1),a=Object(bs.a)(t,2),l=a[0],r=a[1];return Object(n.useEffect)((function(){setTimeout((function(){return r(!0)}),500),setTimeout((function(){return r(!1)}),3e3)}),[]),o.a.createElement("div",{className:"centered absolute inset-0 w-1/4 mt-24 transition-all duration-1000 ease-in-out ".concat(l?"opacity-100 z-20":"opacity-0 z-0")},o.a.createElement("div",{className:"px-12 rounded-lg shadow-2xl bg-white"},o.a.createElement("video",{src:ad.a,autoPlay:!0,muted:!0,loop:!0}),o.a.createElement("p",{className:"px-6 pb-6 text-sm text-gray-800 font-medium text-center"},e("panZoomAnimation.helpText"))))},od=function(){var e=Object(n.useRef)(null),t=Object(n.useRef)(null),a=Object(Ws.a)().i18n,l=Object(n.useContext)(Ms),r=l.state,i=l.dispatch,s=r.theme,c=r.settings,u=Object(n.useContext)(Ys),d=u.setPageRef,m=u.setPanZoomRef;return Object(n.useEffect)((function(){d(e),m(t),a.changeLanguage(c.language);var n=JSON.parse(localStorage.getItem("state"));i({type:"import_data",payload:n})}),[i,d,m,a,c.language]),o.a.createElement(n.Suspense,{fallback:"Loading..."},o.a.createElement("div",{className:"h-screen grid grid-cols-5 items-center"},o.a.createElement(du,null),o.a.createElement("div",{className:"relative z-10 h-screen overflow-hidden col-span-3 flex justify-center items-center"},o.a.createElement(Us.PanZoom,{ref:t,minZoom:"0.4",autoCenter:!0,autoCenterZoomLevel:.7,enableBoundingBox:!0,boundaryRatioVertical:.8,boundaryRatioHorizontal:.8,style:{outline:"none"}},o.a.createElement("div",{id:"page",ref:e,className:"shadow-2xl break-words"},Iu.find((function(e){return s.layout.toLowerCase()===e.key})).component())),o.a.createElement(Zu,null)),o.a.createElement("div",{id:"printPage",className:"break-words"},Iu.find((function(e){return s.layout.toLowerCase()===e.key})).component()),o.a.createElement(Ku,null),o.a.createElement(nd,null),o.a.createElement(ed,null)))};i.a.configure({autoClose:3e3,closeButton:!1,hideProgressBar:!0,position:i.a.POSITION.BOTTOM_RIGHT}),r.a.render(o.a.createElement(o.a.StrictMode,null,o.a.createElement(Is,null,o.a.createElement(qs,null,o.a.createElement(od,null)))),document.getElementById("root")),function(e){if("serviceWorker"in navigator){if(new URL(".",window.location.href).origin!==window.location.origin)return;window.addEventListener("load",(function(){var t="".concat(".","/service-worker.js");ms?(!function(e,t){fetch(e,{headers:{"Service-Worker":"script"}}).then((function(a){var n=a.headers.get("content-type");404===a.status||null!=n&&-1===n.indexOf("javascript")?navigator.serviceWorker.ready.then((function(e){e.unregister().then((function(){window.location.reload()}))})):ps(e,t)})).catch((function(){console.log("No internet connection found. App is running in offline mode.")}))}(t,e),navigator.serviceWorker.ready.then((function(){console.log("This web app is being served cache-first by a service worker. To learn more, visit https://bit.ly/CRA-PWA")}))):ps(t,e)}))}}()}]),[[649,1,2]]]);
//# sourceMappingURL=main.aaf9e78c.chunk.js.map