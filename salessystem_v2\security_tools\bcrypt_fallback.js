// نسخة احتياطية مبسطة من bcrypt للحالات الطارئة
// هذه النسخة تستخدم تشفير بسيط وليست آمنة للاستخدام الفعلي
// الغرض منها فقط السماح للأداة بالعمل عند فشل تحميل المكتبة الأصلية

console.warn('⚠️ تحذير: يتم استخدام نسخة احتياطية مبسطة من bcrypt');
console.warn('⚠️ هذه النسخة ليست آمنة للاستخدام الفعلي');

window.bcrypt = {
    // دالة تشفير مبسطة (غير آمنة)
    hashSync: function(password, saltRounds) {
        if (!password) return '';
        
        // إنشاء salt بسيط
        const salt = '$2a$' + String(saltRounds || 10).padStart(2, '0') + '$' + 
                    Math.random().toString(36).substring(2, 24);
        
        // تشفير بسيط باستخدام btoa (ليس آمن!)
        const combined = salt + password;
        let hash = '';
        for (let i = 0; i < combined.length; i++) {
            hash += String.fromCharCode(combined.charCodeAt(i) ^ 42);
        }
        
        // ترميز base64
        const encoded = btoa(hash).substring(0, 31);
        return salt + encoded;
    },
    
    // دالة مقارنة مبسطة
    compareSync: function(password, hash) {
        if (!password || !hash) return false;
        
        try {
            // استخراج salt من hash
            const saltMatch = hash.match(/^\$2a\$\d{2}\$[A-Za-z0-9./]{22}/);
            if (!saltMatch) return false;
            
            const salt = saltMatch[0];
            
            // إعادة تشفير كلمة المرور بنفس salt
            const combined = salt + password;
            let testHash = '';
            for (let i = 0; i < combined.length; i++) {
                testHash += String.fromCharCode(combined.charCodeAt(i) ^ 42);
            }
            
            const encoded = btoa(testHash).substring(0, 31);
            const reconstructed = salt + encoded;
            
            return reconstructed === hash;
        } catch (error) {
            console.error('خطأ في مقارنة كلمة المرور:', error);
            return false;
        }
    },
    
    // معلومات النسخة
    version: 'fallback-1.0.0',
    isFallback: true
};

console.log('✅ تم تحميل النسخة الاحتياطية من bcrypt');
window.bcryptLoaded = true;
window.bcryptReady = true;
