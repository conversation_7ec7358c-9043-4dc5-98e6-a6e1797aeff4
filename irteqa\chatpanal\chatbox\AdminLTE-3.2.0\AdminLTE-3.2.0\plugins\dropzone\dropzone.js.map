{"version": 3, "sources": ["../src/dropzone.js"], "names": ["Emitter", "event", "fn", "_callbacks", "push", "callbacks", "args", "callback", "apply", "arguments", "length", "i", "splice", "Dropzone", "prototype", "events", "defaultOptions", "url", "method", "withCredentials", "timeout", "parallelUploads", "uploadMultiple", "chunking", "forceChunking", "chunkSize", "parallelChunkUploads", "retryChunks", "retryChunksLimit", "maxFilesize", "paramName", "createImageThumbnails", "maxThumbnailFilesize", "thumbnailWidth", "thumbnailHeight", "thumbnail<PERSON><PERSON><PERSON>", "resizeWidth", "resizeHeight", "resizeMimeType", "resizeQuality", "resizeMethod", "filesizeBase", "maxFiles", "headers", "clickable", "ignoreHiddenFiles", "acceptedFiles", "acceptedMimeTypes", "autoProcessQueue", "autoQueue", "addRemoveLinks", "previewsContainer", "hiddenInputContainer", "capture", "renameFilename", "renameFile", "force<PERSON><PERSON><PERSON>", "dictDefaultMessage", "dictFallbackMessage", "dictFallbackText", "dictFileTooBig", "dictInvalidFileType", "dictResponseError", "dictCancelUpload", "dictUploadCanceled", "dictCancelUploadConfirmation", "dictRemoveFile", "dictRemoveFileConfirmation", "dictMaxFilesExceeded", "dictFileSizeUnits", "tb", "gb", "mb", "kb", "b", "init", "params", "files", "xhr", "chunk", "d<PERSON><PERSON>", "file", "upload", "uuid", "dzchunkindex", "index", "dztotalfilesize", "size", "dzchunksize", "options", "d<PERSON><PERSON><PERSON><PERSON>nkcount", "totalChunkCount", "dzchunkbyteoffset", "accept", "done", "chunksUploaded", "fallback", "messageElement", "element", "className", "getElementsByTagName", "child", "test", "createElement", "append<PERSON><PERSON><PERSON>", "span", "textContent", "innerText", "getFallbackForm", "resize", "width", "height", "info", "srcX", "srcY", "srcWidth", "srcHeight", "srcRatio", "Math", "min", "trgRatio", "Error", "trgWidth", "trgHeight", "transformFile", "type", "match", "resizeImage", "previewTemplate", "drop", "e", "classList", "remove", "dragstart", "dragend", "dragenter", "add", "dragover", "dragleave", "paste", "reset", "addedfile", "previewElement", "trim", "querySelectorAll", "node", "name", "innerHTML", "filesize", "_removeLink", "removeFileEvent", "preventDefault", "stopPropagation", "status", "UPLOADING", "confirm", "removeFile", "removeLink", "addEventListener", "removedfile", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "_updateMaxFilesReachedClass", "thumbnail", "dataUrl", "thumbnailElement", "alt", "src", "setTimeout", "error", "message", "errormultiple", "processing", "processingmultiple", "uploadprogress", "progress", "bytesSent", "nodeName", "value", "style", "totaluploadprogress", "sending", "sendingmultiple", "success", "successmultiple", "canceled", "emit", "canceledmultiple", "complete", "completemultiple", "maxfilesexceeded", "maxfilesreached", "queuecomplete", "addedfiles", "_thumbnailQueue", "_processingThumbnail", "target", "objects", "object", "key", "val", "el", "left", "version", "replace", "clickableElements", "listeners", "document", "querySelector", "nodeType", "dropzone", "instances", "elementOptions", "optionsForElement", "extend", "isBrowserSupported", "call", "getAttribute", "toUpperCase", "getExistingFallback", "getElement", "getElements", "filter", "accepted", "map", "getFilesWithStatus", "QUEUED", "ADDED", "tagName", "setAttribute", "contains", "setupHiddenFileInput", "hiddenFileInput", "visibility", "position", "top", "addFile", "URL", "window", "webkitURL", "eventName", "on", "updateTotalUploadProgress", "getAddedFiles", "getUploadingFiles", "getQueuedFiles", "noPropagation", "returnValue", "efct", "dataTransfer", "effectAllowed", "dropEffect", "for<PERSON>ach", "clickableElement", "evt", "elementInside", "click", "enable", "disable", "removeAllFiles", "undefined", "indexOf", "totalUploadProgress", "totalBytesSent", "totalBytes", "activeFiles", "getActiveFiles", "total", "n", "existingFallback", "form", "fieldsString", "_getParamName", "fields", "get<PERSON>allback", "elements", "elementListeners", "result", "listener", "removeEventListener", "removeEventListeners", "disabled", "cancelUpload", "setupEventListeners", "selectedSize", "<PERSON><PERSON><PERSON><PERSON>", "units", "unit", "cutoff", "pow", "round", "getAcceptedFiles", "items", "webkitGetAsEntry", "_addFilesFromItems", "handleFiles", "__guard__", "clipboardData", "x", "item", "entry", "isFile", "getAsFile", "isDirectory", "_addFilesFromDirectory", "kind", "directory", "path", "<PERSON><PERSON><PERSON><PERSON>", "createReader", "<PERSON><PERSON><PERSON><PERSON>", "__guard<PERSON><PERSON><PERSON>__", "console", "o", "log", "readEntries", "entries", "substring", "fullPath", "isValidFile", "uuidv4", "filename", "_renameFile", "chunked", "ceil", "_enqueueThumbnail", "_errorProcessing", "enqueueFile", "processQueue", "_processThumbnailQueue", "shift", "createThumbnail", "without", "cancelIfNecessary", "slice", "canvas", "resizedDataURL", "toDataURL", "ExifRestore", "restore", "dataURL", "dataURItoBlob", "fixOrientation", "fileReader", "FileReader", "onload", "createThumbnailFromUrl", "readAsDataURL", "crossOrigin", "img", "loadExif", "EXIF", "getData", "getTag", "orientation", "resizeInfo", "ctx", "getContext", "translate", "scale", "rotate", "PI", "drawImageIOSFix", "trgX", "trgY", "onerror", "processingLength", "queuedFiles", "processFiles", "processFile", "uploadFiles", "groupedFiles", "_getFilesWithXhr", "groupedFile", "CANCELED", "abort", "option", "_transformFiles", "transformedFiles", "transformedFile", "startedChunkCount", "chunks", "handleNextChunk", "chunkIndex", "start", "end", "dataBlock", "data", "webkitSlice", "retries", "_uploadData", "finishedChunkUpload", "allFinished", "SUCCESS", "_finished", "dataBlocks", "XMLHttpRequest", "resolveOption", "open", "_finishedUploading", "ontimeout", "_handleUploadError", "progressObj", "onprogress", "_updateFilesUploadProgress", "headerName", "headerValue", "setRequestHeader", "formData", "FormData", "additionalParams", "_getChunk", "append", "_addFormElementData", "submitRequest", "<PERSON><PERSON><PERSON><PERSON>", "input", "inputName", "inputType", "toLowerCase", "hasAttribute", "selected", "checked", "loaded", "fileProgress", "fileTotal", "fileBytesSent", "allFilesFinished", "response", "readyState", "responseType", "responseText", "getResponseHeader", "JSON", "parse", "warn", "send", "ERROR", "c", "r", "random", "v", "toString", "initClass", "camelize", "forElement", "autoDiscover", "discover", "dropzones", "checkElements", "blacklistedBrowsers", "capableBrowser", "File", "FileList", "Blob", "regex", "navigator", "userAgent", "dataURI", "byteString", "atob", "split", "mimeString", "ab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ia", "Uint8Array", "asc", "charCodeAt", "list", "rejectedItem", "str", "char<PERSON>t", "string", "div", "childNodes", "container", "els", "Array", "question", "rejected", "mimeType", "baseMimeType", "validType", "j<PERSON><PERSON><PERSON>", "each", "module", "exports", "ACCEPTED", "PROCESSING", "detectVerticalSquash", "iw", "naturalWidth", "ih", "naturalHeight", "drawImage", "getImageData", "sy", "ey", "py", "alpha", "ratio", "sx", "sw", "sh", "dx", "dy", "dw", "dh", "vertSquashRatio", "KEY_STR", "output", "chr1", "chr2", "chr3", "enc1", "enc2", "enc3", "enc4", "isNaN", "origFileBase64", "resizedFileBase64", "rawImage", "decode64", "segments", "slice2Segments", "image", "exifManipulation", "encode64", "exifArray", "getExifArray", "newImageArray", "insertExif", "<PERSON><PERSON><PERSON><PERSON>", "seg", "imageData", "buf", "separatePoint", "mae", "ato", "array", "concat", "rawImageArray", "head", "endPoint", "base64test", "exec", "contentLoaded", "win", "doc", "root", "documentElement", "rem", "pre", "poll", "doScroll", "createEventObject", "frameElement", "_autoDiscoverFunction", "transform", "obj", "methodName"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;AACA;IACMA,O;;;;;;;;;AACJ;uBACGC,K,EAAOC,E,EAAI;AACZ,WAAKC,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC,CADY,CAEZ;;AACA,UAAI,CAAC,KAAKA,UAAL,CAAgBF,KAAhB,CAAL,EAA6B;AAC3B,aAAKE,UAAL,CAAgBF,KAAhB,IAAyB,EAAzB;AACD;;AACD,WAAKE,UAAL,CAAgBF,KAAhB,EAAuBG,IAAvB,CAA4BF,EAA5B;;AACA,aAAO,IAAP;AACD;;;yBAGID,K,EAAgB;AACnB,WAAKE,UAAL,GAAkB,KAAKA,UAAL,IAAmB,EAArC;AACA,UAAIE,SAAS,GAAG,KAAKF,UAAL,CAAgBF,KAAhB,CAAhB;;AAEA,UAAII,SAAJ,EAAe;AAAA,0CAJFC,IAIE;AAJFA,UAAAA,IAIE;AAAA;;AAAA;AAAA;AAAA;;AAAA;AACb,+BAAqBD,SAArB,8HAAgC;AAAA,gBAAvBE,QAAuB;AAC9BA,YAAAA,QAAQ,CAACC,KAAT,CAAe,IAAf,EAAqBF,IAArB;AACD;AAHY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAId;;AAED,aAAO,IAAP;AACD,K,CAED;AACA;AACA;;;;wBACIL,K,EAAOC,E,EAAI;AACb,UAAI,CAAC,KAAKC,UAAN,IAAqBM,SAAS,CAACC,MAAV,KAAqB,CAA9C,EAAkD;AAChD,aAAKP,UAAL,GAAkB,EAAlB;AACA,eAAO,IAAP;AACD,OAJY,CAMb;;;AACA,UAAIE,SAAS,GAAG,KAAKF,UAAL,CAAgBF,KAAhB,CAAhB;;AACA,UAAI,CAACI,SAAL,EAAgB;AACd,eAAO,IAAP;AACD,OAVY,CAYb;;;AACA,UAAII,SAAS,CAACC,MAAV,KAAqB,CAAzB,EAA4B;AAC1B,eAAO,KAAKP,UAAL,CAAgBF,KAAhB,CAAP;AACA,eAAO,IAAP;AACD,OAhBY,CAkBb;;;AACA,WAAK,IAAIU,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGN,SAAS,CAACK,MAA9B,EAAsCC,CAAC,EAAvC,EAA2C;AACzC,YAAIJ,QAAQ,GAAGF,SAAS,CAACM,CAAD,CAAxB;;AACA,YAAIJ,QAAQ,KAAKL,EAAjB,EAAqB;AACnBG,UAAAA,SAAS,CAACO,MAAV,CAAiBD,CAAjB,EAAoB,CAApB;AACA;AACD;AACF;;AAED,aAAO,IAAP;AACD;;;;;;IAGGE,Q;;;;;;;gCACe;AAEjB;AACA,WAAKC,SAAL,CAAed,OAAf,GAAyBA,OAAzB;AAEA;;;;;;AAQA,WAAKc,SAAL,CAAeC,MAAf,GAAwB,CACtB,MADsB,EAEtB,WAFsB,EAGtB,SAHsB,EAItB,WAJsB,EAKtB,UALsB,EAMtB,WANsB,EAOtB,WAPsB,EAQtB,YARsB,EAStB,aATsB,EAUtB,WAVsB,EAWtB,OAXsB,EAYtB,eAZsB,EAatB,YAbsB,EActB,oBAdsB,EAetB,gBAfsB,EAgBtB,qBAhBsB,EAiBtB,SAjBsB,EAkBtB,iBAlBsB,EAmBtB,SAnBsB,EAoBtB,iBApBsB,EAqBtB,UArBsB,EAsBtB,kBAtBsB,EAuBtB,UAvBsB,EAwBtB,kBAxBsB,EAyBtB,OAzBsB,EA0BtB,kBA1BsB,EA2BtB,iBA3BsB,EA4BtB,eA5BsB,CAAxB;AAgCA,WAAKD,SAAL,CAAeE,cAAf,GAAgC;AAC9B;;;;;;AAMAC,QAAAA,GAAG,EAAE,IAPyB;;AAS9B;;;;AAIAC,QAAAA,MAAM,EAAE,MAbsB;;AAe9B;;;AAGAC,QAAAA,eAAe,EAAE,KAlBa;;AAoB9B;;;AAGAC,QAAAA,OAAO,EAAE,KAvBqB;;AAyB9B;;;;AAIAC,QAAAA,eAAe,EAAE,CA7Ba;;AA+B9B;;;;;;;AAOAC,QAAAA,cAAc,EAAE,KAtCc;;AAwC9B;;;;;;AAMAC,QAAAA,QAAQ,EAAE,KA9CoB;;AAgD9B;;;;;AAKAC,QAAAA,aAAa,EAAE,KArDe;;AAuD9B;;;AAGAC,QAAAA,SAAS,EAAE,OA1DmB;;AA4D9B;;;AAGAC,QAAAA,oBAAoB,EAAE,KA/DQ;;AAiE9B;;;AAGAC,QAAAA,WAAW,EAAE,KApEiB;;AAsE9B;;;AAGAC,QAAAA,gBAAgB,EAAE,CAzEY;;AA2E9B;;;;;AAKAC,QAAAA,WAAW,EAAE,GAhFiB;;AAkF9B;;;;;AAKAC,QAAAA,SAAS,EAAE,MAvFmB;;AAyF9B;;;AAGAC,QAAAA,qBAAqB,EAAE,IA5FO;;AA8F9B;;;AAGAC,QAAAA,oBAAoB,EAAE,EAjGQ;;AAmG9B;;;AAGAC,QAAAA,cAAc,EAAE,GAtGc;;AAwG9B;;;AAGAC,QAAAA,eAAe,EAAE,GA3Ga;;AA6G9B;;;;AAIAC,QAAAA,eAAe,EAAE,MAjHa;;AAmH9B;;;;;;;;AAQAC,QAAAA,WAAW,EAAE,IA3HiB;;AA6H9B;;;AAGAC,QAAAA,YAAY,EAAE,IAhIgB;;AAkI9B;;;;;AAKAC,QAAAA,cAAc,EAAE,IAvIc;;AAyI9B;;;AAGAC,QAAAA,aAAa,EAAE,GA5Ie;;AA8I9B;;;;AAIAC,QAAAA,YAAY,EAAE,SAlJgB;;AAoJ9B;;;;;;AAMAC,QAAAA,YAAY,EAAE,IA1JgB;;AA4J9B;;;AAGAC,QAAAA,QAAQ,EAAE,IA/JoB;;AAiK9B;;;;AAIAC,QAAAA,OAAO,EAAE,IArKqB;;AAuK9B;;;;;;;;AAQAC,QAAAA,SAAS,EAAE,IA/KmB;;AAiL9B;;;AAGAC,QAAAA,iBAAiB,EAAE,IApLW;;AAuL9B;;;;;;;;;;;AAWAC,QAAAA,aAAa,EAAE,IAlMe;;AAoM9B;;;;AAIAC,QAAAA,iBAAiB,EAAE,IAxMW;;AA0M9B;;;;;;;;;;AAUAC,QAAAA,gBAAgB,EAAE,IApNY;;AAsN9B;;;;AAIAC,QAAAA,SAAS,EAAE,IA1NmB;;AA4N9B;;;;;AAKAC,QAAAA,cAAc,EAAE,KAjOc;;AAmO9B;;;;;;AAMAC,QAAAA,iBAAiB,EAAE,IAzOW;;AA2O9B;;;;;;;AAOAC,QAAAA,oBAAoB,EAAE,MAlPQ;;AAoP9B;;;;;;;;AAQAC,QAAAA,OAAO,EAAE,IA5PqB;;AA8P9B;;;AAGAC,QAAAA,cAAc,EAAE,IAjQc;;AAmQ9B;;;;;AAKAC,QAAAA,UAAU,EAAE,IAxQkB;;AA0Q9B;;;;;;AAMAC,QAAAA,aAAa,EAAE,KAhRe;;AAkR9B;;;AAGAC,QAAAA,kBAAkB,EAAE,2BArRU;;AAuR9B;;;AAGAC,QAAAA,mBAAmB,EAAE,yDA1RS;;AA4R9B;;;;;AAKAC,QAAAA,gBAAgB,EAAE,iFAjSY;;AAmS9B;;;;AAIAC,QAAAA,cAAc,EAAE,sEAvSc;;AAyS9B;;;AAGAC,QAAAA,mBAAmB,EAAE,sCA5SS;;AA8S9B;;;;AAIAC,QAAAA,iBAAiB,EAAE,4CAlTW;;AAoT9B;;;AAGAC,QAAAA,gBAAgB,EAAE,eAvTY;;AAyT9B;;;AAGAC,QAAAA,kBAAkB,EAAE,kBA5TU;;AA8T9B;;;AAGAC,QAAAA,4BAA4B,EAAE,8CAjUA;;AAmU9B;;;AAGAC,QAAAA,cAAc,EAAE,aAtUc;;AAwU9B;;;AAGAC,QAAAA,0BAA0B,EAAE,IA3UE;;AA6U9B;;;;AAIAC,QAAAA,oBAAoB,EAAE,oCAjVQ;;AAmV9B;;;;AAIAC,QAAAA,iBAAiB,EAAE;AAACC,UAAAA,EAAE,EAAE,IAAL;AAAWC,UAAAA,EAAE,EAAE,IAAf;AAAqBC,UAAAA,EAAE,EAAE,IAAzB;AAA+BC,UAAAA,EAAE,EAAE,IAAnC;AAAyCC,UAAAA,CAAC,EAAE;AAA5C,SAvVW;;AAwV9B;;;;AAIAC,QAAAA,IA5V8B,kBA4VvB,CAAE,CA5VqB;;AA8V9B;;;;;;;;;;AAUAC,QAAAA,MAxW8B,kBAwWvBC,KAxWuB,EAwWhBC,GAxWgB,EAwWXC,KAxWW,EAwWJ;AACxB,cAAIA,KAAJ,EAAW;AACT,mBAAO;AACLC,cAAAA,MAAM,EAAED,KAAK,CAACE,IAAN,CAAWC,MAAX,CAAkBC,IADrB;AAELC,cAAAA,YAAY,EAAEL,KAAK,CAACM,KAFf;AAGLC,cAAAA,eAAe,EAAEP,KAAK,CAACE,IAAN,CAAWM,IAHvB;AAILC,cAAAA,WAAW,EAAE,KAAKC,OAAL,CAAahE,SAJrB;AAKLiE,cAAAA,iBAAiB,EAAEX,KAAK,CAACE,IAAN,CAAWC,MAAX,CAAkBS,eALhC;AAMLC,cAAAA,iBAAiB,EAAEb,KAAK,CAACM,KAAN,GAAc,KAAKI,OAAL,CAAahE;AANzC,aAAP;AAQD;AACF,SAnX6B;;AAqX9B;;;;;;;;;AASAoE,QAAAA,MA9X8B,kBA8XvBZ,IA9XuB,EA8XjBa,IA9XiB,EA8XX;AACjB,iBAAOA,IAAI,EAAX;AACD,SAhY6B;;AAkY9B;;;;;;AAMAC,QAAAA,cAAc,EAAE,wBAASd,IAAT,EAAea,IAAf,EAAqB;AAAEA,UAAAA,IAAI;AAAK,SAxYlB;;AA0Y9B;;;;;AAKAE,QAAAA,QA/Y8B,sBA+YnB;AACT;AACA,cAAIC,cAAJ;AACA,eAAKC,OAAL,CAAaC,SAAb,aAA4B,KAAKD,OAAL,CAAaC,SAAzC;AAHS;AAAA;AAAA;;AAAA;AAKT,kCAAkB,KAAKD,OAAL,CAAaE,oBAAb,CAAkC,KAAlC,CAAlB,mIAA4D;AAAA,kBAAnDC,KAAmD;;AAC1D,kBAAI,uBAAuBC,IAAvB,CAA4BD,KAAK,CAACF,SAAlC,CAAJ,EAAkD;AAChDF,gBAAAA,cAAc,GAAGI,KAAjB;AACAA,gBAAAA,KAAK,CAACF,SAAN,GAAkB,YAAlB,CAFgD,CAEhB;;AAChC;AACD;AACF;AAXQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAYT,cAAI,CAACF,cAAL,EAAqB;AACnBA,YAAAA,cAAc,GAAGpF,QAAQ,CAAC0F,aAAT,CAAuB,+CAAvB,CAAjB;AACA,iBAAKL,OAAL,CAAaM,WAAb,CAAyBP,cAAzB;AACD;;AAED,cAAIQ,IAAI,GAAGR,cAAc,CAACG,oBAAf,CAAoC,MAApC,EAA4C,CAA5C,CAAX;;AACA,cAAIK,IAAJ,EAAU;AACR,gBAAIA,IAAI,CAACC,WAAL,IAAoB,IAAxB,EAA8B;AAC5BD,cAAAA,IAAI,CAACC,WAAL,GAAmB,KAAKjB,OAAL,CAAa/B,mBAAhC;AACD,aAFD,MAEO,IAAI+C,IAAI,CAACE,SAAL,IAAkB,IAAtB,EAA4B;AACjCF,cAAAA,IAAI,CAACE,SAAL,GAAiB,KAAKlB,OAAL,CAAa/B,mBAA9B;AACD;AACF;;AAED,iBAAO,KAAKwC,OAAL,CAAaM,WAAb,CAAyB,KAAKI,eAAL,EAAzB,CAAP;AACD,SA1a6B;;AA6a9B;;;;;;;;;;;;AAYAC,QAAAA,MAzb8B,kBAybvB5B,IAzbuB,EAybjB6B,KAzbiB,EAybVC,MAzbU,EAybFvE,YAzbE,EAybY;AACxC,cAAIwE,IAAI,GAAG;AACTC,YAAAA,IAAI,EAAE,CADG;AAETC,YAAAA,IAAI,EAAE,CAFG;AAGTC,YAAAA,QAAQ,EAAElC,IAAI,CAAC6B,KAHN;AAITM,YAAAA,SAAS,EAAEnC,IAAI,CAAC8B;AAJP,WAAX;AAOA,cAAIM,QAAQ,GAAGpC,IAAI,CAAC6B,KAAL,GAAa7B,IAAI,CAAC8B,MAAjC,CARwC,CAUxC;;AACA,cAAKD,KAAK,IAAI,IAAV,IAAoBC,MAAM,IAAI,IAAlC,EAAyC;AACvCD,YAAAA,KAAK,GAAGE,IAAI,CAACG,QAAb;AACAJ,YAAAA,MAAM,GAAGC,IAAI,CAACI,SAAd;AACD,WAHD,MAGO,IAAKN,KAAK,IAAI,IAAd,EAAqB;AAC1BA,YAAAA,KAAK,GAAGC,MAAM,GAAGM,QAAjB;AACD,WAFM,MAEA,IAAKN,MAAM,IAAI,IAAf,EAAsB;AAC3BA,YAAAA,MAAM,GAAGD,KAAK,GAAGO,QAAjB;AACD,WAlBuC,CAoBxC;;;AACAP,UAAAA,KAAK,GAAGQ,IAAI,CAACC,GAAL,CAAST,KAAT,EAAgBE,IAAI,CAACG,QAArB,CAAR;AACAJ,UAAAA,MAAM,GAAGO,IAAI,CAACC,GAAL,CAASR,MAAT,EAAiBC,IAAI,CAACI,SAAtB,CAAT;AAEA,cAAII,QAAQ,GAAGV,KAAK,GAAGC,MAAvB;;AAEA,cAAKC,IAAI,CAACG,QAAL,GAAgBL,KAAjB,IAA4BE,IAAI,CAACI,SAAL,GAAiBL,MAAjD,EAA0D;AACxD;AACA,gBAAIvE,YAAY,KAAK,MAArB,EAA6B;AAC3B,kBAAI6E,QAAQ,GAAGG,QAAf,EAAyB;AACvBR,gBAAAA,IAAI,CAACI,SAAL,GAAiBnC,IAAI,CAAC8B,MAAtB;AACAC,gBAAAA,IAAI,CAACG,QAAL,GAAgBH,IAAI,CAACI,SAAL,GAAiBI,QAAjC;AACD,eAHD,MAGO;AACLR,gBAAAA,IAAI,CAACG,QAAL,GAAgBlC,IAAI,CAAC6B,KAArB;AACAE,gBAAAA,IAAI,CAACI,SAAL,GAAiBJ,IAAI,CAACG,QAAL,GAAgBK,QAAjC;AACD;AACF,aARD,MAQO,IAAIhF,YAAY,KAAK,SAArB,EAAgC;AACrC;AACA,kBAAI6E,QAAQ,GAAGG,QAAf,EAAyB;AACvBT,gBAAAA,MAAM,GAAGD,KAAK,GAAGO,QAAjB;AACD,eAFD,MAEO;AACLP,gBAAAA,KAAK,GAAGC,MAAM,GAAGM,QAAjB;AACD;AACF,aAPM,MAOA;AACL,oBAAM,IAAII,KAAJ,iCAAmCjF,YAAnC,OAAN;AACD;AACF;;AAEDwE,UAAAA,IAAI,CAACC,IAAL,GAAY,CAAChC,IAAI,CAAC6B,KAAL,GAAaE,IAAI,CAACG,QAAnB,IAA+B,CAA3C;AACAH,UAAAA,IAAI,CAACE,IAAL,GAAY,CAACjC,IAAI,CAAC8B,MAAL,GAAcC,IAAI,CAACI,SAApB,IAAiC,CAA7C;AAEAJ,UAAAA,IAAI,CAACU,QAAL,GAAgBZ,KAAhB;AACAE,UAAAA,IAAI,CAACW,SAAL,GAAiBZ,MAAjB;AAEA,iBAAOC,IAAP;AACD,SAhf6B;;AAkf9B;;;;;;;;;AASAY,QAAAA,aA3f8B,yBA2fhB3C,IA3fgB,EA2fVa,IA3fU,EA2fJ;AACxB,cAAI,CAAC,KAAKL,OAAL,CAAarD,WAAb,IAA4B,KAAKqD,OAAL,CAAapD,YAA1C,KAA2D4C,IAAI,CAAC4C,IAAL,CAAUC,KAAV,CAAgB,SAAhB,CAA/D,EAA2F;AACzF,mBAAO,KAAKC,WAAL,CAAiB9C,IAAjB,EAAuB,KAAKQ,OAAL,CAAarD,WAApC,EAAiD,KAAKqD,OAAL,CAAapD,YAA9D,EAA4E,KAAKoD,OAAL,CAAajD,YAAzF,EAAuGsD,IAAvG,CAAP;AACD,WAFD,MAEO;AACL,mBAAOA,IAAI,CAACb,IAAD,CAAX;AACD;AACF,SAjgB6B;;AAogB9B;;;;;;;;;;;;;;AAcA+C,QAAAA,eAAe,ssGAlhBe;AAkjB9B;AACA;;AAGA;;;;;;;;AAYA;AACAC,QAAAA,IAnkB8B,gBAmkBzBC,CAnkByB,EAmkBtB;AACN,iBAAO,KAAKhC,OAAL,CAAaiC,SAAb,CAAuBC,MAAvB,CAA8B,eAA9B,CAAP;AACD,SArkB6B;AAskB9BC,QAAAA,SAtkB8B,qBAskBpBH,CAtkBoB,EAskBjB,CACZ,CAvkB6B;AAwkB9BI,QAAAA,OAxkB8B,mBAwkBtBJ,CAxkBsB,EAwkBnB;AACT,iBAAO,KAAKhC,OAAL,CAAaiC,SAAb,CAAuBC,MAAvB,CAA8B,eAA9B,CAAP;AACD,SA1kB6B;AA2kB9BG,QAAAA,SA3kB8B,qBA2kBpBL,CA3kBoB,EA2kBjB;AACX,iBAAO,KAAKhC,OAAL,CAAaiC,SAAb,CAAuBK,GAAvB,CAA2B,eAA3B,CAAP;AACD,SA7kB6B;AA8kB9BC,QAAAA,QA9kB8B,oBA8kBrBP,CA9kBqB,EA8kBlB;AACV,iBAAO,KAAKhC,OAAL,CAAaiC,SAAb,CAAuBK,GAAvB,CAA2B,eAA3B,CAAP;AACD,SAhlB6B;AAilB9BE,QAAAA,SAjlB8B,qBAilBpBR,CAjlBoB,EAilBjB;AACX,iBAAO,KAAKhC,OAAL,CAAaiC,SAAb,CAAuBC,MAAvB,CAA8B,eAA9B,CAAP;AACD,SAnlB6B;AAqlB9BO,QAAAA,KArlB8B,iBAqlBxBT,CArlBwB,EAqlBrB,CACR,CAtlB6B;AAwlB9B;AACA;AACAU,QAAAA,KA1lB8B,mBA0lBtB;AACN,iBAAO,KAAK1C,OAAL,CAAaiC,SAAb,CAAuBC,MAAvB,CAA8B,YAA9B,CAAP;AACD,SA5lB6B;AA8lB9B;AACA;AACAS,QAAAA,SAhmB8B,qBAgmBpB5D,IAhmBoB,EAgmBd;AAAA;;AACd,cAAI,KAAKiB,OAAL,KAAiB,KAAK/C,iBAA1B,EAA6C;AAC3C,iBAAK+C,OAAL,CAAaiC,SAAb,CAAuBK,GAAvB,CAA2B,YAA3B;AACD;;AAED,cAAI,KAAKrF,iBAAT,EAA4B;AAC1B8B,YAAAA,IAAI,CAAC6D,cAAL,GAAsBjI,QAAQ,CAAC0F,aAAT,CAAuB,KAAKd,OAAL,CAAauC,eAAb,CAA6Be,IAA7B,EAAvB,CAAtB;AACA9D,YAAAA,IAAI,CAAC+C,eAAL,GAAuB/C,IAAI,CAAC6D,cAA5B,CAF0B,CAEkB;;AAE5C,iBAAK3F,iBAAL,CAAuBqD,WAAvB,CAAmCvB,IAAI,CAAC6D,cAAxC;AAJ0B;AAAA;AAAA;;AAAA;AAK1B,oCAAiB7D,IAAI,CAAC6D,cAAL,CAAoBE,gBAApB,CAAqC,gBAArC,CAAjB,mIAAyE;AAAA,oBAAhEC,IAAgE;AACvEA,gBAAAA,IAAI,CAACvC,WAAL,GAAmBzB,IAAI,CAACiE,IAAxB;AACD;AAPyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;AAQ1B,oCAAajE,IAAI,CAAC6D,cAAL,CAAoBE,gBAApB,CAAqC,gBAArC,CAAb,mIAAqE;AAAhEC,gBAAAA,IAAgE;AACnEA,gBAAAA,IAAI,CAACE,SAAL,GAAiB,KAAKC,QAAL,CAAcnE,IAAI,CAACM,IAAnB,CAAjB;AACD;AAVyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAY1B,gBAAI,KAAKE,OAAL,CAAavC,cAAjB,EAAiC;AAC/B+B,cAAAA,IAAI,CAACoE,WAAL,GAAmBxI,QAAQ,CAAC0F,aAAT,gFAA2F,KAAKd,OAAL,CAAavB,cAAxG,UAAnB;AACAe,cAAAA,IAAI,CAAC6D,cAAL,CAAoBtC,WAApB,CAAgCvB,IAAI,CAACoE,WAArC;AACD;;AAED,gBAAIC,eAAe,GAAG,SAAlBA,eAAkB,CAAApB,CAAC,EAAI;AACzBA,cAAAA,CAAC,CAACqB,cAAF;AACArB,cAAAA,CAAC,CAACsB,eAAF;;AACA,kBAAIvE,IAAI,CAACwE,MAAL,KAAgB5I,QAAQ,CAAC6I,SAA7B,EAAwC;AACtC,uBAAO7I,QAAQ,CAAC8I,OAAT,CAAiB,MAAI,CAAClE,OAAL,CAAaxB,4BAA9B,EAA4D;AAAA,yBAAM,MAAI,CAAC2F,UAAL,CAAgB3E,IAAhB,CAAN;AAAA,iBAA5D,CAAP;AACD,eAFD,MAEO;AACL,oBAAI,MAAI,CAACQ,OAAL,CAAatB,0BAAjB,EAA6C;AAC3C,yBAAOtD,QAAQ,CAAC8I,OAAT,CAAiB,MAAI,CAAClE,OAAL,CAAatB,0BAA9B,EAA0D;AAAA,2BAAM,MAAI,CAACyF,UAAL,CAAgB3E,IAAhB,CAAN;AAAA,mBAA1D,CAAP;AACD,iBAFD,MAEO;AACL,yBAAO,MAAI,CAAC2E,UAAL,CAAgB3E,IAAhB,CAAP;AACD;AACF;AACF,aAZD;;AAjB0B;AAAA;AAAA;;AAAA;AA+B1B,oCAAuBA,IAAI,CAAC6D,cAAL,CAAoBE,gBAApB,CAAqC,kBAArC,CAAvB,mIAAiF;AAAA,oBAAxEa,UAAwE;AAC9EA,gBAAAA,UAAU,CAACC,gBAAX,CAA4B,OAA5B,EAAqCR,eAArC;AACF;AAjCyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkC3B;AACF,SAxoB6B;AA2oB9B;AACAS,QAAAA,WA5oB8B,uBA4oBlB9E,IA5oBkB,EA4oBZ;AAChB,cAAIA,IAAI,CAAC6D,cAAL,IAAuB,IAAvB,IAA+B7D,IAAI,CAAC6D,cAAL,CAAoBkB,UAApB,IAAkC,IAArE,EAA2E;AACzE/E,YAAAA,IAAI,CAAC6D,cAAL,CAAoBkB,UAApB,CAA+BC,WAA/B,CAA2ChF,IAAI,CAAC6D,cAAhD;AACD;;AACD,iBAAO,KAAKoB,2BAAL,EAAP;AACD,SAjpB6B;AAmpB9B;AACA;AACAC,QAAAA,SArpB8B,qBAqpBpBlF,IArpBoB,EAqpBdmF,OArpBc,EAqpBL;AACvB,cAAInF,IAAI,CAAC6D,cAAT,EAAyB;AACvB7D,YAAAA,IAAI,CAAC6D,cAAL,CAAoBX,SAApB,CAA8BC,MAA9B,CAAqC,iBAArC;AADuB;AAAA;AAAA;;AAAA;AAEvB,oCAA6BnD,IAAI,CAAC6D,cAAL,CAAoBE,gBAApB,CAAqC,qBAArC,CAA7B,mIAA0F;AAAA,oBAAjFqB,gBAAiF;AACxFA,gBAAAA,gBAAgB,CAACC,GAAjB,GAAuBrF,IAAI,CAACiE,IAA5B;AACAmB,gBAAAA,gBAAgB,CAACE,GAAjB,GAAuBH,OAAvB;AACD;AALsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAOvB,mBAAOI,UAAU,CAAE;AAAA,qBAAMvF,IAAI,CAAC6D,cAAL,CAAoBX,SAApB,CAA8BK,GAA9B,CAAkC,kBAAlC,CAAN;AAAA,aAAF,EAAgE,CAAhE,CAAjB;AACD;AACF,SA/pB6B;AAiqB9B;AACA;AACAiC,QAAAA,KAnqB8B,iBAmqBxBxF,IAnqBwB,EAmqBlByF,OAnqBkB,EAmqBT;AACnB,cAAIzF,IAAI,CAAC6D,cAAT,EAAyB;AACvB7D,YAAAA,IAAI,CAAC6D,cAAL,CAAoBX,SAApB,CAA8BK,GAA9B,CAAkC,UAAlC;;AACA,gBAAK,OAAOkC,OAAP,KAAmB,QAApB,IAAiCA,OAAO,CAACD,KAA7C,EAAoD;AAClDC,cAAAA,OAAO,GAAGA,OAAO,CAACD,KAAlB;AACD;;AAJsB;AAAA;AAAA;;AAAA;AAKvB,oCAAiBxF,IAAI,CAAC6D,cAAL,CAAoBE,gBAApB,CAAqC,wBAArC,CAAjB,mIAAiF;AAAA,oBAAxEC,IAAwE;AAC/EA,gBAAAA,IAAI,CAACvC,WAAL,GAAmBgE,OAAnB;AACD;AAPsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQxB;AACF,SA7qB6B;AA+qB9BC,QAAAA,aA/qB8B,2BA+qBd,CACf,CAhrB6B;AAkrB9B;AACA;AACA;AACAC,QAAAA,UArrB8B,sBAqrBnB3F,IArrBmB,EAqrBb;AACf,cAAIA,IAAI,CAAC6D,cAAT,EAAyB;AACvB7D,YAAAA,IAAI,CAAC6D,cAAL,CAAoBX,SAApB,CAA8BK,GAA9B,CAAkC,eAAlC;;AACA,gBAAIvD,IAAI,CAACoE,WAAT,EAAsB;AACpB,qBAAOpE,IAAI,CAACoE,WAAL,CAAiBF,SAAjB,GAA6B,KAAK1D,OAAL,CAAa1B,gBAAjD;AACD;AACF;AACF,SA5rB6B;AA8rB9B8G,QAAAA,kBA9rB8B,gCA8rBT,CACpB,CA/rB6B;AAisB9B;AACA;AACA;AACAC,QAAAA,cApsB8B,0BAosBf7F,IApsBe,EAosBT8F,QApsBS,EAosBCC,SApsBD,EAosBY;AACxC,cAAI/F,IAAI,CAAC6D,cAAT,EAAyB;AAAA;AAAA;AAAA;;AAAA;AACvB,oCAAiB7D,IAAI,CAAC6D,cAAL,CAAoBE,gBAApB,CAAqC,0BAArC,CAAjB,mIAAmF;AAAA,oBAA1EC,IAA0E;AAC/EA,gBAAAA,IAAI,CAACgC,QAAL,KAAkB,UAAlB,GACKhC,IAAI,CAACiC,KAAL,GAAaH,QADlB,GAGK9B,IAAI,CAACkC,KAAL,CAAWrE,KAAX,aAAsBiE,QAAtB,MAHL;AAIH;AANsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOxB;AACF,SA7sB6B;AA+sB9B;AACA;AACAK,QAAAA,mBAjtB8B,iCAitBR,CACrB,CAltB6B;AAotB9B;AACA;AACA;AACAC,QAAAA,OAvtB8B,qBAutBpB,CACT,CAxtB6B;AA0tB9BC,QAAAA,eA1tB8B,6BA0tBZ,CAAE,CA1tBU;AA4tB9B;AACA;AACAC,QAAAA,OA9tB8B,mBA8tBtBtG,IA9tBsB,EA8tBhB;AACZ,cAAIA,IAAI,CAAC6D,cAAT,EAAyB;AACvB,mBAAO7D,IAAI,CAAC6D,cAAL,CAAoBX,SAApB,CAA8BK,GAA9B,CAAkC,YAAlC,CAAP;AACD;AACF,SAluB6B;AAouB9BgD,QAAAA,eApuB8B,6BAouBZ,CAAE,CApuBU;AAsuB9B;AACAC,QAAAA,QAvuB8B,oBAuuBrBxG,IAvuBqB,EAuuBf;AACb,iBAAO,KAAKyG,IAAL,CAAU,OAAV,EAAmBzG,IAAnB,EAAyB,KAAKQ,OAAL,CAAazB,kBAAtC,CAAP;AACD,SAzuB6B;AA2uB9B2H,QAAAA,gBA3uB8B,8BA2uBX,CAAE,CA3uBS;AA6uB9B;AACA;AACAC,QAAAA,QA/uB8B,oBA+uBrB3G,IA/uBqB,EA+uBf;AACb,cAAIA,IAAI,CAACoE,WAAT,EAAsB;AACpBpE,YAAAA,IAAI,CAACoE,WAAL,CAAiBF,SAAjB,GAA6B,KAAK1D,OAAL,CAAavB,cAA1C;AACD;;AACD,cAAIe,IAAI,CAAC6D,cAAT,EAAyB;AACvB,mBAAO7D,IAAI,CAAC6D,cAAL,CAAoBX,SAApB,CAA8BK,GAA9B,CAAkC,aAAlC,CAAP;AACD;AACF,SAtvB6B;AAwvB9BqD,QAAAA,gBAxvB8B,8BAwvBX,CAAE,CAxvBS;AA0vB9BC,QAAAA,gBA1vB8B,8BA0vBX,CAAE,CA1vBS;AA4vB9BC,QAAAA,eA5vB8B,6BA4vBZ,CAAE,CA5vBU;AA8vB9BC,QAAAA,aA9vB8B,2BA8vBd,CAAE,CA9vBY;AAgwB9BC,QAAAA,UAhwB8B,wBAgwBjB,CAAE;AAhwBe,OAAhC;AAowBA,WAAKnL,SAAL,CAAeoL,eAAf,GAAiC,EAAjC;AACA,WAAKpL,SAAL,CAAeqL,oBAAf,GAAsC,KAAtC;AACD,K,CAED;;;;2BACcC,M,EAAoB;AAAA,yCAATC,OAAS;AAATA,QAAAA,OAAS;AAAA;;AAChC,kCAAmBA,OAAnB,8BAA4B;AAAvB,YAAIC,MAAM,eAAV;;AACH,aAAK,IAAIC,GAAT,IAAgBD,MAAhB,EAAwB;AACtB,cAAIE,GAAG,GAAGF,MAAM,CAACC,GAAD,CAAhB;AACAH,UAAAA,MAAM,CAACG,GAAD,CAAN,GAAcC,GAAd;AACD;AACF;;AACD,aAAOJ,MAAP;AACD;;;AAED,oBAAYK,EAAZ,EAAgBhH,OAAhB,EAAyB;AAAA;;AAAA;;AACvB;AACA,QAAIO,QAAJ,EAAc0G,IAAd;AACA,UAAKxG,OAAL,GAAeuG,EAAf,CAHuB,CAIvB;;AACA,UAAKE,OAAL,GAAe9L,QAAQ,CAAC8L,OAAxB;AAEA,UAAK3L,cAAL,CAAoBgH,eAApB,GAAsC,MAAKhH,cAAL,CAAoBgH,eAApB,CAAoC4E,OAApC,CAA4C,MAA5C,EAAoD,EAApD,CAAtC;AAEA,UAAKC,iBAAL,GAAyB,EAAzB;AACA,UAAKC,SAAL,GAAiB,EAAjB;AACA,UAAKjI,KAAL,GAAa,EAAb,CAXuB,CAWN;;AAEjB,QAAI,OAAO,MAAKqB,OAAZ,KAAwB,QAA5B,EAAsC;AACpC,YAAKA,OAAL,GAAe6G,QAAQ,CAACC,aAAT,CAAuB,MAAK9G,OAA5B,CAAf;AACD,KAfsB,CAiBvB;;;AACA,QAAI,CAAC,MAAKA,OAAN,IAAkB,MAAKA,OAAL,CAAa+G,QAAb,IAAyB,IAA/C,EAAsD;AACpD,YAAM,IAAIxF,KAAJ,CAAU,2BAAV,CAAN;AACD;;AAED,QAAI,MAAKvB,OAAL,CAAagH,QAAjB,EAA2B;AACzB,YAAM,IAAIzF,KAAJ,CAAU,4BAAV,CAAN;AACD,KAxBsB,CA0BvB;;;AACA5G,IAAAA,QAAQ,CAACsM,SAAT,CAAmB/M,IAAnB,gCA3BuB,CA6BvB;;AACA,UAAK8F,OAAL,CAAagH,QAAb;AAEA,QAAIE,cAAc,GAAG,CAACV,IAAI,GAAG7L,QAAQ,CAACwM,iBAAT,CAA2B,MAAKnH,OAAhC,CAAR,KAAqD,IAArD,GAA4DwG,IAA5D,GAAmE,EAAxF;AAEA,UAAKjH,OAAL,GAAe5E,QAAQ,CAACyM,MAAT,CAAgB,EAAhB,EAAoB,MAAKtM,cAAzB,EAAyCoM,cAAzC,EAAyD3H,OAAO,IAAI,IAAX,GAAkBA,OAAlB,GAA4B,EAArF,CAAf,CAlCuB,CAoCvB;;AACA,QAAI,MAAKA,OAAL,CAAajC,aAAb,IAA8B,CAAC3C,QAAQ,CAAC0M,kBAAT,EAAnC,EAAkE;AAChE,+CAAO,MAAK9H,OAAL,CAAaO,QAAb,CAAsBwH,IAAtB,+BAAP;AACD,KAvCsB,CAyCvB;;;AACA,QAAI,MAAK/H,OAAL,CAAaxE,GAAb,IAAoB,IAAxB,EAA8B;AAC5B,YAAKwE,OAAL,CAAaxE,GAAb,GAAmB,MAAKiF,OAAL,CAAauH,YAAb,CAA0B,QAA1B,CAAnB;AACD;;AAED,QAAI,CAAC,MAAKhI,OAAL,CAAaxE,GAAlB,EAAuB;AACrB,YAAM,IAAIwG,KAAJ,CAAU,kBAAV,CAAN;AACD;;AAED,QAAI,MAAKhC,OAAL,CAAa3C,aAAb,IAA8B,MAAK2C,OAAL,CAAa1C,iBAA/C,EAAkE;AAChE,YAAM,IAAI0E,KAAJ,CAAU,oGAAV,CAAN;AACD;;AAED,QAAI,MAAKhC,OAAL,CAAanE,cAAb,IAA+B,MAAKmE,OAAL,CAAalE,QAAhD,EAA0D;AACxD,YAAM,IAAIkG,KAAJ,CAAU,mDAAV,CAAN;AACD,KAxDsB,CA0DvB;;;AACA,QAAI,MAAKhC,OAAL,CAAa1C,iBAAjB,EAAoC;AAClC,YAAK0C,OAAL,CAAa3C,aAAb,GAA6B,MAAK2C,OAAL,CAAa1C,iBAA1C;AACA,aAAO,MAAK0C,OAAL,CAAa1C,iBAApB;AACD,KA9DsB,CAgEvB;;;AACA,QAAI,MAAK0C,OAAL,CAAanC,cAAb,IAA+B,IAAnC,EAAyC;AACvC,YAAKmC,OAAL,CAAalC,UAAb,GAA0B,UAAA0B,IAAI;AAAA,eAAI,MAAKQ,OAAL,CAAanC,cAAb,CAA4BkK,IAA5B,gCAAuCvI,IAAI,CAACiE,IAA5C,EAAkDjE,IAAlD,CAAJ;AAAA,OAA9B;AACD;;AAED,UAAKQ,OAAL,CAAavE,MAAb,GAAsB,MAAKuE,OAAL,CAAavE,MAAb,CAAoBwM,WAApB,EAAtB;;AAEA,QAAI,CAAC1H,QAAQ,GAAG,MAAK2H,mBAAL,EAAZ,KAA2C3H,QAAQ,CAACgE,UAAxD,EAAoE;AAClE;AACAhE,MAAAA,QAAQ,CAACgE,UAAT,CAAoBC,WAApB,CAAgCjE,QAAhC;AACD,KA1EsB,CA4EvB;;;AACA,QAAI,MAAKP,OAAL,CAAatC,iBAAb,KAAmC,KAAvC,EAA8C;AAC5C,UAAI,MAAKsC,OAAL,CAAatC,iBAAjB,EAAoC;AAClC,cAAKA,iBAAL,GAAyBtC,QAAQ,CAAC+M,UAAT,CAAoB,MAAKnI,OAAL,CAAatC,iBAAjC,EAAoD,mBAApD,CAAzB;AACD,OAFD,MAEO;AACL,cAAKA,iBAAL,GAAyB,MAAK+C,OAA9B;AACD;AACF;;AAED,QAAI,MAAKT,OAAL,CAAa7C,SAAjB,EAA4B;AAC1B,UAAI,MAAK6C,OAAL,CAAa7C,SAAb,KAA2B,IAA/B,EAAqC;AACnC,cAAKiK,iBAAL,GAAyB,CAAC,MAAK3G,OAAN,CAAzB;AACD,OAFD,MAEO;AACL,cAAK2G,iBAAL,GAAyBhM,QAAQ,CAACgN,WAAT,CAAqB,MAAKpI,OAAL,CAAa7C,SAAlC,EAA6C,WAA7C,CAAzB;AACD;AACF;;AAGD,UAAK+B,IAAL;;AA9FuB;AA+FxB,G,CAGD;;;;;uCACmB;AACjB,aAAO,KAAKE,KAAL,CAAWiJ,MAAX,CAAkB,UAAC7I,IAAD;AAAA,eAAUA,IAAI,CAAC8I,QAAf;AAAA,OAAlB,EAA2CC,GAA3C,CAA+C,UAAC/I,IAAD;AAAA,eAAUA,IAAV;AAAA,OAA/C,CAAP;AACD,K,CAED;AACA;;;;uCACmB;AACjB,aAAO,KAAKJ,KAAL,CAAWiJ,MAAX,CAAkB,UAAC7I,IAAD;AAAA,eAAU,CAACA,IAAI,CAAC8I,QAAhB;AAAA,OAAlB,EAA4CC,GAA5C,CAAgD,UAAC/I,IAAD;AAAA,eAAUA,IAAV;AAAA,OAAhD,CAAP;AACD;;;uCAEkBwE,M,EAAQ;AACzB,aAAO,KAAK5E,KAAL,CAAWiJ,MAAX,CAAkB,UAAC7I,IAAD;AAAA,eAAUA,IAAI,CAACwE,MAAL,KAAgBA,MAA1B;AAAA,OAAlB,EAAoDuE,GAApD,CAAwD,UAAC/I,IAAD;AAAA,eAAUA,IAAV;AAAA,OAAxD,CAAP;AACD,K,CAED;;;;qCACiB;AACf,aAAO,KAAKgJ,kBAAL,CAAwBpN,QAAQ,CAACqN,MAAjC,CAAP;AACD;;;wCAEmB;AAClB,aAAO,KAAKD,kBAAL,CAAwBpN,QAAQ,CAAC6I,SAAjC,CAAP;AACD;;;oCAEe;AACd,aAAO,KAAKuE,kBAAL,CAAwBpN,QAAQ,CAACsN,KAAjC,CAAP;AACD,K,CAED;;;;qCACiB;AACf,aAAO,KAAKtJ,KAAL,CAAWiJ,MAAX,CAAkB,UAAC7I,IAAD;AAAA,eAAWA,IAAI,CAACwE,MAAL,KAAgB5I,QAAQ,CAAC6I,SAA1B,IAAyCzE,IAAI,CAACwE,MAAL,KAAgB5I,QAAQ,CAACqN,MAA5E;AAAA,OAAlB,EAAuGF,GAAvG,CAA2G,UAAC/I,IAAD;AAAA,eAAUA,IAAV;AAAA,OAA3G,CAAP;AACD,K,CAED;AACA;;;;2BACO;AAAA;;AACL;AACA,UAAI,KAAKiB,OAAL,CAAakI,OAAb,KAAyB,MAA7B,EAAqC;AACnC,aAAKlI,OAAL,CAAamI,YAAb,CAA0B,SAA1B,EAAqC,qBAArC;AACD;;AAED,UAAI,KAAKnI,OAAL,CAAaiC,SAAb,CAAuBmG,QAAvB,CAAgC,UAAhC,KAA+C,CAAC,KAAKpI,OAAL,CAAa8G,aAAb,CAA2B,aAA3B,CAApD,EAA+F;AAC7F,aAAK9G,OAAL,CAAaM,WAAb,CAAyB3F,QAAQ,CAAC0F,aAAT,sDAAmE,KAAKd,OAAL,CAAahC,kBAAhF,mBAAzB;AACD;;AAED,UAAI,KAAKoJ,iBAAL,CAAuBnM,MAA3B,EAAmC;AACjC,YAAI6N,oBAAoB,GAAG,SAAvBA,oBAAuB,GAAM;AAC/B,cAAI,MAAI,CAACC,eAAT,EAA0B;AACxB,YAAA,MAAI,CAACA,eAAL,CAAqBxE,UAArB,CAAgCC,WAAhC,CAA4C,MAAI,CAACuE,eAAjD;AACD;;AACD,UAAA,MAAI,CAACA,eAAL,GAAuBzB,QAAQ,CAACxG,aAAT,CAAuB,OAAvB,CAAvB;;AACA,UAAA,MAAI,CAACiI,eAAL,CAAqBH,YAArB,CAAkC,MAAlC,EAA0C,MAA1C;;AACA,cAAK,MAAI,CAAC5I,OAAL,CAAa/C,QAAb,KAA0B,IAA3B,IAAqC,MAAI,CAAC+C,OAAL,CAAa/C,QAAb,GAAwB,CAAjE,EAAqE;AACnE,YAAA,MAAI,CAAC8L,eAAL,CAAqBH,YAArB,CAAkC,UAAlC,EAA8C,UAA9C;AACD;;AACD,UAAA,MAAI,CAACG,eAAL,CAAqBrI,SAArB,GAAiC,iBAAjC;;AAEA,cAAI,MAAI,CAACV,OAAL,CAAa3C,aAAb,KAA+B,IAAnC,EAAyC;AACvC,YAAA,MAAI,CAAC0L,eAAL,CAAqBH,YAArB,CAAkC,QAAlC,EAA4C,MAAI,CAAC5I,OAAL,CAAa3C,aAAzD;AACD;;AACD,cAAI,MAAI,CAAC2C,OAAL,CAAapC,OAAb,KAAyB,IAA7B,EAAmC;AACjC,YAAA,MAAI,CAACmL,eAAL,CAAqBH,YAArB,CAAkC,SAAlC,EAA6C,MAAI,CAAC5I,OAAL,CAAapC,OAA1D;AACD,WAhB8B,CAkB/B;AACA;;;AACA,UAAA,MAAI,CAACmL,eAAL,CAAqBrD,KAArB,CAA2BsD,UAA3B,GAAwC,QAAxC;AACA,UAAA,MAAI,CAACD,eAAL,CAAqBrD,KAArB,CAA2BuD,QAA3B,GAAsC,UAAtC;AACA,UAAA,MAAI,CAACF,eAAL,CAAqBrD,KAArB,CAA2BwD,GAA3B,GAAiC,GAAjC;AACA,UAAA,MAAI,CAACH,eAAL,CAAqBrD,KAArB,CAA2BuB,IAA3B,GAAkC,GAAlC;AACA,UAAA,MAAI,CAAC8B,eAAL,CAAqBrD,KAArB,CAA2BpE,MAA3B,GAAoC,GAApC;AACA,UAAA,MAAI,CAACyH,eAAL,CAAqBrD,KAArB,CAA2BrE,KAA3B,GAAmC,GAAnC;AACAjG,UAAAA,QAAQ,CAAC+M,UAAT,CAAoB,MAAI,CAACnI,OAAL,CAAarC,oBAAjC,EAAuD,sBAAvD,EAA+EoD,WAA/E,CAA2F,MAAI,CAACgI,eAAhG;AACA,iBAAO,MAAI,CAACA,eAAL,CAAqB1E,gBAArB,CAAsC,QAAtC,EAAgD,YAAM;AAAA,gBACtDjF,KADsD,GAC7C,MAAI,CAAC2J,eADwC,CACtD3J,KADsD;;AAE3D,gBAAIA,KAAK,CAACnE,MAAV,EAAkB;AAAA;AAAA;AAAA;;AAAA;AAChB,sCAAiBmE,KAAjB,mIAAwB;AAAA,sBAAfI,IAAe;;AACtB,kBAAA,MAAI,CAAC2J,OAAL,CAAa3J,IAAb;AACD;AAHe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIjB;;AACD,YAAA,MAAI,CAACyG,IAAL,CAAU,YAAV,EAAwB7G,KAAxB;;AACA,mBAAO0J,oBAAoB,EAA3B;AACD,WATM,CAAP;AAUD,SArCD;;AAsCAA,QAAAA,oBAAoB;AACrB;;AAED,WAAKM,GAAL,GAAWC,MAAM,CAACD,GAAP,KAAe,IAAf,GAAsBC,MAAM,CAACD,GAA7B,GAAmCC,MAAM,CAACC,SAArD,CApDK,CAuDL;AACA;AACA;;AAzDK;AAAA;AAAA;;AAAA;AA0DL,+BAAsB,KAAKhO,MAA3B,wIAAmC;AAAA,cAA1BiO,SAA0B;AACjC,eAAKC,EAAL,CAAQD,SAAR,EAAmB,KAAKvJ,OAAL,CAAauJ,SAAb,CAAnB;AACD;AA5DI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AA8DL,WAAKC,EAAL,CAAQ,gBAAR,EAA0B;AAAA,eAAM,MAAI,CAACC,yBAAL,EAAN;AAAA,OAA1B;AAEA,WAAKD,EAAL,CAAQ,aAAR,EAAuB;AAAA,eAAM,MAAI,CAACC,yBAAL,EAAN;AAAA,OAAvB;AAEA,WAAKD,EAAL,CAAQ,UAAR,EAAoB,UAAAhK,IAAI;AAAA,eAAI,MAAI,CAACyG,IAAL,CAAU,UAAV,EAAsBzG,IAAtB,CAAJ;AAAA,OAAxB,EAlEK,CAoEL;;AACA,WAAKgK,EAAL,CAAQ,UAAR,EAAoB,UAAAhK,IAAI,EAAI;AAC1B,YAAK,MAAI,CAACkK,aAAL,GAAqBzO,MAArB,KAAgC,CAAjC,IAAwC,MAAI,CAAC0O,iBAAL,GAAyB1O,MAAzB,KAAoC,CAA5E,IAAmF,MAAI,CAAC2O,cAAL,GAAsB3O,MAAtB,KAAiC,CAAxH,EAA4H;AAC1H;AACA,iBAAO8J,UAAU,CAAE;AAAA,mBAAM,MAAI,CAACkB,IAAL,CAAU,eAAV,CAAN;AAAA,WAAF,EAAqC,CAArC,CAAjB;AACD;AACF,OALD;;AAQA,UAAI4D,aAAa,GAAG,SAAhBA,aAAgB,CAAUpH,CAAV,EAAa;AAC/BA,QAAAA,CAAC,CAACsB,eAAF;;AACA,YAAItB,CAAC,CAACqB,cAAN,EAAsB;AACpB,iBAAOrB,CAAC,CAACqB,cAAF,EAAP;AACD,SAFD,MAEO;AACL,iBAAOrB,CAAC,CAACqH,WAAF,GAAgB,KAAvB;AACD;AACF,OAPD,CA7EK,CAsFL;;;AACA,WAAKzC,SAAL,GAAiB,CACf;AACE5G,QAAAA,OAAO,EAAE,KAAKA,OADhB;AAEEnF,QAAAA,MAAM,EAAE;AACN,uBAAa,mBAAAmH,CAAC,EAAI;AAChB,mBAAO,MAAI,CAACwD,IAAL,CAAU,WAAV,EAAuBxD,CAAvB,CAAP;AACD,WAHK;AAIN,uBAAa,mBAAAA,CAAC,EAAI;AAChBoH,YAAAA,aAAa,CAACpH,CAAD,CAAb;AACA,mBAAO,MAAI,CAACwD,IAAL,CAAU,WAAV,EAAuBxD,CAAvB,CAAP;AACD,WAPK;AAQN,sBAAY,kBAAAA,CAAC,EAAI;AACf;AACA;AACA;AACA,gBAAIsH,IAAJ;;AACA,gBAAI;AACFA,cAAAA,IAAI,GAAGtH,CAAC,CAACuH,YAAF,CAAeC,aAAtB;AACD,aAFD,CAEE,OAAOjF,KAAP,EAAc,CACf;;AACDvC,YAAAA,CAAC,CAACuH,YAAF,CAAeE,UAAf,GAA6B,WAAWH,IAAZ,IAAsB,eAAeA,IAArC,GAA6C,MAA7C,GAAsD,MAAlF;AAEAF,YAAAA,aAAa,CAACpH,CAAD,CAAb;AACA,mBAAO,MAAI,CAACwD,IAAL,CAAU,UAAV,EAAsBxD,CAAtB,CAAP;AACD,WArBK;AAsBN,uBAAa,mBAAAA,CAAC,EAAI;AAChB,mBAAO,MAAI,CAACwD,IAAL,CAAU,WAAV,EAAuBxD,CAAvB,CAAP;AACD,WAxBK;AAyBN,kBAAQ,cAAAA,CAAC,EAAI;AACXoH,YAAAA,aAAa,CAACpH,CAAD,CAAb;AACA,mBAAO,MAAI,CAACD,IAAL,CAAUC,CAAV,CAAP;AACD,WA5BK;AA6BN,qBAAW,iBAAAA,CAAC,EAAI;AACd,mBAAO,MAAI,CAACwD,IAAL,CAAU,SAAV,EAAqBxD,CAArB,CAAP;AACD;AA/BK,SAFV,CAoCE;AACA;AACA;AACA;;AAvCF,OADe,CAAjB;AA4CA,WAAK2E,iBAAL,CAAuB+C,OAAvB,CAA+B,UAAAC,gBAAgB,EAAI;AACjD,eAAO,MAAI,CAAC/C,SAAL,CAAe1M,IAAf,CAAoB;AACzB8F,UAAAA,OAAO,EAAE2J,gBADgB;AAEzB9O,UAAAA,MAAM,EAAE;AACN,qBAAS,eAAA+O,GAAG,EAAI;AACd;AACA,kBAAKD,gBAAgB,KAAK,MAAI,CAAC3J,OAA3B,IAAyC4J,GAAG,CAAC1D,MAAJ,KAAe,MAAI,CAAClG,OAArB,IAAiCrF,QAAQ,CAACkP,aAAT,CAAuBD,GAAG,CAAC1D,MAA3B,EAAmC,MAAI,CAAClG,OAAL,CAAa8G,aAAb,CAA2B,aAA3B,CAAnC,CAA7E,EAA6J;AAC3J,gBAAA,MAAI,CAACwB,eAAL,CAAqBwB,KAArB,GAD2J,CAC7H;;AAC/B;;AACD,qBAAO,IAAP;AACD;AAPK;AAFiB,SAApB,CAAP;AAYD,OAbD;AAeA,WAAKC,MAAL;AAEA,aAAO,KAAKxK,OAAL,CAAad,IAAb,CAAkB6I,IAAlB,CAAuB,IAAvB,CAAP;AACD,K,CAED;;;;8BACU;AACR,WAAK0C,OAAL;AACA,WAAKC,cAAL,CAAoB,IAApB;;AACA,UAAI,KAAK3B,eAAL,IAAwB,IAAxB,GAA+B,KAAKA,eAAL,CAAqBxE,UAApD,GAAiEoG,SAArE,EAAgF;AAC9E,aAAK5B,eAAL,CAAqBxE,UAArB,CAAgCC,WAAhC,CAA4C,KAAKuE,eAAjD;AACA,aAAKA,eAAL,GAAuB,IAAvB;AACD;;AACD,aAAO,KAAKtI,OAAL,CAAagH,QAApB;AACA,aAAOrM,QAAQ,CAACsM,SAAT,CAAmBvM,MAAnB,CAA0BC,QAAQ,CAACsM,SAAT,CAAmBkD,OAAnB,CAA2B,IAA3B,CAA1B,EAA4D,CAA5D,CAAP;AACD;;;gDAG2B;AAC1B,UAAIC,mBAAJ;AACA,UAAIC,cAAc,GAAG,CAArB;AACA,UAAIC,UAAU,GAAG,CAAjB;AAEA,UAAIC,WAAW,GAAG,KAAKC,cAAL,EAAlB;;AAEA,UAAID,WAAW,CAAC/P,MAAhB,EAAwB;AAAA;AAAA;AAAA;;AAAA;AACtB,iCAAiB,KAAKgQ,cAAL,EAAjB,wIAAwC;AAAA,gBAA/BzL,IAA+B;AACtCsL,YAAAA,cAAc,IAAItL,IAAI,CAACC,MAAL,CAAY8F,SAA9B;AACAwF,YAAAA,UAAU,IAAIvL,IAAI,CAACC,MAAL,CAAYyL,KAA1B;AACD;AAJqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAKtBL,QAAAA,mBAAmB,GAAI,MAAMC,cAAP,GAAyBC,UAA/C;AACD,OAND,MAMO;AACLF,QAAAA,mBAAmB,GAAG,GAAtB;AACD;;AAED,aAAO,KAAK5E,IAAL,CAAU,qBAAV,EAAiC4E,mBAAjC,EAAsDE,UAAtD,EAAkED,cAAlE,CAAP;AACD,K,CAED;AACA;;;;kCACcK,C,EAAG;AACf,UAAI,OAAO,KAAKnL,OAAL,CAAa3D,SAApB,KAAkC,UAAtC,EAAkD;AAChD,eAAO,KAAK2D,OAAL,CAAa3D,SAAb,CAAuB8O,CAAvB,CAAP;AACD,OAFD,MAEO;AACL,yBAAU,KAAKnL,OAAL,CAAa3D,SAAvB,SAAmC,KAAK2D,OAAL,CAAanE,cAAb,cAAkCsP,CAAlC,SAAyC,EAA5E;AACD;AACF,K,CAED;AACA;;;;gCACY3L,I,EAAM;AAChB,UAAI,OAAO,KAAKQ,OAAL,CAAalC,UAApB,KAAmC,UAAvC,EAAmD;AACjD,eAAO0B,IAAI,CAACiE,IAAZ;AACD;;AACD,aAAO,KAAKzD,OAAL,CAAalC,UAAb,CAAwB0B,IAAxB,CAAP;AACD,K,CAED;AACA;AACA;AACA;;;;sCACkB;AAChB,UAAI4L,gBAAJ,EAAsBC,IAAtB;;AACA,UAAID,gBAAgB,GAAG,KAAKlD,mBAAL,EAAvB,EAAmD;AACjD,eAAOkD,gBAAP;AACD;;AAED,UAAIE,YAAY,GAAG,6BAAnB;;AACA,UAAI,KAAKtL,OAAL,CAAa9B,gBAAjB,EAAmC;AACjCoN,QAAAA,YAAY,iBAAU,KAAKtL,OAAL,CAAa9B,gBAAvB,SAAZ;AACD;;AACDoN,MAAAA,YAAY,0CAAgC,KAAKC,aAAL,CAAmB,CAAnB,CAAhC,gBAA0D,KAAKvL,OAAL,CAAanE,cAAb,GAA8B,qBAA9B,GAAsD8O,SAAhH,uDAAZ;AAEA,UAAIa,MAAM,GAAGpQ,QAAQ,CAAC0F,aAAT,CAAuBwK,YAAvB,CAAb;;AACA,UAAI,KAAK7K,OAAL,CAAakI,OAAb,KAAyB,MAA7B,EAAqC;AACnC0C,QAAAA,IAAI,GAAGjQ,QAAQ,CAAC0F,aAAT,0BAAwC,KAAKd,OAAL,CAAaxE,GAArD,yDAAmG,KAAKwE,OAAL,CAAavE,MAAhH,gBAAP;AACA4P,QAAAA,IAAI,CAACtK,WAAL,CAAiByK,MAAjB;AACD,OAHD,MAGO;AACL;AACA,aAAK/K,OAAL,CAAamI,YAAb,CAA0B,SAA1B,EAAqC,qBAArC;AACA,aAAKnI,OAAL,CAAamI,YAAb,CAA0B,QAA1B,EAAoC,KAAK5I,OAAL,CAAavE,MAAjD;AACD;;AACD,aAAO4P,IAAI,IAAI,IAAR,GAAeA,IAAf,GAAsBG,MAA7B;AACD,K,CAGD;AACA;AACA;;;;0CACsB;AACpB,UAAIC,WAAW,GAAG,SAAdA,WAAc,CAAUC,QAAV,EAAoB;AAAA;AAAA;AAAA;;AAAA;AACpC,iCAAeA,QAAf,wIAAyB;AAAA,gBAAhB1E,EAAgB;;AACvB,gBAAI,qBAAqBnG,IAArB,CAA0BmG,EAAE,CAACtG,SAA7B,CAAJ,EAA6C;AAC3C,qBAAOsG,EAAP;AACD;AACF;AALmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMrC,OAND;;AAQA,+BAAoB,CAAC,KAAD,EAAQ,MAAR,CAApB,4BAAqC;AAAhC,YAAI2B,OAAO,YAAX;AACH,YAAIpI,QAAJ;;AACA,YAAIA,QAAQ,GAAGkL,WAAW,CAAC,KAAKhL,OAAL,CAAaE,oBAAb,CAAkCgI,OAAlC,CAAD,CAA1B,EAAwE;AACtE,iBAAOpI,QAAP;AACD;AACF;AACF,K,CAGD;;;;0CACsB;AACpB,aAAO,KAAK8G,SAAL,CAAekB,GAAf,CAAmB,UAACoD,gBAAD;AAAA,eACrB,YAAM;AACL,cAAIC,MAAM,GAAG,EAAb;;AACA,eAAK,IAAIpR,KAAT,IAAkBmR,gBAAgB,CAACrQ,MAAnC,EAA2C;AACzC,gBAAIuQ,QAAQ,GAAGF,gBAAgB,CAACrQ,MAAjB,CAAwBd,KAAxB,CAAf;AACAoR,YAAAA,MAAM,CAACjR,IAAP,CAAYgR,gBAAgB,CAAClL,OAAjB,CAAyB4D,gBAAzB,CAA0C7J,KAA1C,EAAiDqR,QAAjD,EAA2D,KAA3D,CAAZ;AACD;;AACD,iBAAOD,MAAP;AACD,SAPD,EADsB;AAAA,OAAnB,CAAP;AASD,K,CAGD;;;;2CACuB;AACrB,aAAO,KAAKvE,SAAL,CAAekB,GAAf,CAAmB,UAACoD,gBAAD;AAAA,eACrB,YAAM;AACL,cAAIC,MAAM,GAAG,EAAb;;AACA,eAAK,IAAIpR,KAAT,IAAkBmR,gBAAgB,CAACrQ,MAAnC,EAA2C;AACzC,gBAAIuQ,QAAQ,GAAGF,gBAAgB,CAACrQ,MAAjB,CAAwBd,KAAxB,CAAf;AACAoR,YAAAA,MAAM,CAACjR,IAAP,CAAYgR,gBAAgB,CAAClL,OAAjB,CAAyBqL,mBAAzB,CAA6CtR,KAA7C,EAAoDqR,QAApD,EAA8D,KAA9D,CAAZ;AACD;;AACD,iBAAOD,MAAP;AACD,SAPD,EADsB;AAAA,OAAnB,CAAP;AASD,K,CAED;;;;8BACU;AAAA;;AACR,WAAKxE,iBAAL,CAAuB+C,OAAvB,CAA+B,UAAA1J,OAAO;AAAA,eAAIA,OAAO,CAACiC,SAAR,CAAkBC,MAAlB,CAAyB,cAAzB,CAAJ;AAAA,OAAtC;AACA,WAAKoJ,oBAAL;AACA,WAAKC,QAAL,GAAgB,IAAhB;AAEA,aAAO,KAAK5M,KAAL,CAAWmJ,GAAX,CAAe,UAAC/I,IAAD;AAAA,eAAU,MAAI,CAACyM,YAAL,CAAkBzM,IAAlB,CAAV;AAAA,OAAf,CAAP;AACD;;;6BAEQ;AACP,aAAO,KAAKwM,QAAZ;AACA,WAAK5E,iBAAL,CAAuB+C,OAAvB,CAA+B,UAAA1J,OAAO;AAAA,eAAIA,OAAO,CAACiC,SAAR,CAAkBK,GAAlB,CAAsB,cAAtB,CAAJ;AAAA,OAAtC;AACA,aAAO,KAAKmJ,mBAAL,EAAP;AACD,K,CAED;;;;6BACSpM,I,EAAM;AACb,UAAIqM,YAAY,GAAG,CAAnB;AACA,UAAIC,YAAY,GAAG,GAAnB;;AAEA,UAAItM,IAAI,GAAG,CAAX,EAAc;AACZ,YAAIuM,KAAK,GAAG,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,EAAyB,GAAzB,CAAZ;;AAEA,aAAK,IAAInR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmR,KAAK,CAACpR,MAA1B,EAAkCC,CAAC,EAAnC,EAAuC;AACrC,cAAIoR,IAAI,GAAGD,KAAK,CAACnR,CAAD,CAAhB;AACA,cAAIqR,MAAM,GAAG1K,IAAI,CAAC2K,GAAL,CAAS,KAAKxM,OAAL,CAAahD,YAAtB,EAAoC,IAAI9B,CAAxC,IAA6C,EAA1D;;AAEA,cAAI4E,IAAI,IAAIyM,MAAZ,EAAoB;AAClBJ,YAAAA,YAAY,GAAGrM,IAAI,GAAG+B,IAAI,CAAC2K,GAAL,CAAS,KAAKxM,OAAL,CAAahD,YAAtB,EAAoC,IAAI9B,CAAxC,CAAtB;AACAkR,YAAAA,YAAY,GAAGE,IAAf;AACA;AACD;AACF;;AAEDH,QAAAA,YAAY,GAAGtK,IAAI,CAAC4K,KAAL,CAAW,KAAKN,YAAhB,IAAgC,EAA/C,CAdY,CAcuC;AACpD;;AAED,+BAAkBA,YAAlB,uBAA2C,KAAKnM,OAAL,CAAapB,iBAAb,CAA+BwN,YAA/B,CAA3C;AACD,K,CAGD;;;;kDAC8B;AAC5B,UAAK,KAAKpM,OAAL,CAAa/C,QAAb,IAAyB,IAA1B,IAAoC,KAAKyP,gBAAL,GAAwBzR,MAAxB,IAAkC,KAAK+E,OAAL,CAAa/C,QAAvF,EAAkG;AAChG,YAAI,KAAKyP,gBAAL,GAAwBzR,MAAxB,KAAmC,KAAK+E,OAAL,CAAa/C,QAApD,EAA8D;AAC5D,eAAKgJ,IAAL,CAAU,iBAAV,EAA6B,KAAK7G,KAAlC;AACD;;AACD,eAAO,KAAKqB,OAAL,CAAaiC,SAAb,CAAuBK,GAAvB,CAA2B,sBAA3B,CAAP;AACD,OALD,MAKO;AACL,eAAO,KAAKtC,OAAL,CAAaiC,SAAb,CAAuBC,MAAvB,CAA8B,sBAA9B,CAAP;AACD;AACF;;;yBAGIF,C,EAAG;AACN,UAAI,CAACA,CAAC,CAACuH,YAAP,EAAqB;AACnB;AACD;;AACD,WAAK/D,IAAL,CAAU,MAAV,EAAkBxD,CAAlB,EAJM,CAMN;AACA;;AACA,UAAIrD,KAAK,GAAG,EAAZ;;AACA,WAAK,IAAIlE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGuH,CAAC,CAACuH,YAAF,CAAe5K,KAAf,CAAqBnE,MAAzC,EAAiDC,CAAC,EAAlD,EAAsD;AACpDkE,QAAAA,KAAK,CAAClE,CAAD,CAAL,GAAWuH,CAAC,CAACuH,YAAF,CAAe5K,KAAf,CAAqBlE,CAArB,CAAX;AACD;;AAED,WAAK+K,IAAL,CAAU,YAAV,EAAwB7G,KAAxB,EAbM,CAeN;;AACA,UAAIA,KAAK,CAACnE,MAAV,EAAkB;AAAA,YACX0R,KADW,GACFlK,CAAC,CAACuH,YADA,CACX2C,KADW;;AAEhB,YAAIA,KAAK,IAAIA,KAAK,CAAC1R,MAAf,IAA0B0R,KAAK,CAAC,CAAD,CAAL,CAASC,gBAAT,IAA6B,IAA3D,EAAkE;AAChE;AACA,eAAKC,kBAAL,CAAwBF,KAAxB;AACD,SAHD,MAGO;AACL,eAAKG,WAAL,CAAiB1N,KAAjB;AACD;AACF;AACF;;;0BAEKqD,C,EAAG;AACP,UAAIsK,SAAS,CAACtK,CAAC,IAAI,IAAL,GAAYA,CAAC,CAACuK,aAAd,GAA8BrC,SAA/B,EAA0C,UAAAsC,CAAC;AAAA,eAAIA,CAAC,CAACN,KAAN;AAAA,OAA3C,CAAT,IAAoE,IAAxE,EAA8E;AAC5E;AACD;;AAED,WAAK1G,IAAL,CAAU,OAAV,EAAmBxD,CAAnB;AALO,UAMFkK,KANE,GAMOlK,CAAC,CAACuK,aANT,CAMFL,KANE;;AAQP,UAAIA,KAAK,CAAC1R,MAAV,EAAkB;AAChB,eAAO,KAAK4R,kBAAL,CAAwBF,KAAxB,CAAP;AACD;AACF;;;gCAGWvN,K,EAAO;AAAA;AAAA;AAAA;;AAAA;AACjB,+BAAgBA,KAAhB,wIAAuB;AAAA,cAAfI,IAAe;AACrB,eAAK2J,OAAL,CAAa3J,IAAb;AACD;AAHgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIlB,K,CAED;AACA;;;;uCACmBmN,K,EAAO;AAAA;;AACxB,aAAQ,YAAM;AACZ,YAAIf,MAAM,GAAG,EAAb;AADY;AAAA;AAAA;;AAAA;AAEZ,iCAAiBe,KAAjB,wIAAwB;AAAA,gBAAfO,IAAe;AACtB,gBAAIC,KAAJ;;AACA,gBAAKD,IAAI,CAACN,gBAAL,IAAyB,IAA1B,KAAoCO,KAAK,GAAGD,IAAI,CAACN,gBAAL,EAA5C,CAAJ,EAA0E;AACxE,kBAAIO,KAAK,CAACC,MAAV,EAAkB;AAChBxB,gBAAAA,MAAM,CAACjR,IAAP,CAAY,MAAI,CAACwO,OAAL,CAAa+D,IAAI,CAACG,SAAL,EAAb,CAAZ;AACD,eAFD,MAEO,IAAIF,KAAK,CAACG,WAAV,EAAuB;AAC5B;AACA1B,gBAAAA,MAAM,CAACjR,IAAP,CAAY,MAAI,CAAC4S,sBAAL,CAA4BJ,KAA5B,EAAmCA,KAAK,CAAC1J,IAAzC,CAAZ;AACD,eAHM,MAGA;AACLmI,gBAAAA,MAAM,CAACjR,IAAP,CAAYgQ,SAAZ;AACD;AACF,aATD,MASO,IAAIuC,IAAI,CAACG,SAAL,IAAkB,IAAtB,EAA4B;AACjC,kBAAKH,IAAI,CAACM,IAAL,IAAa,IAAd,IAAwBN,IAAI,CAACM,IAAL,KAAc,MAA1C,EAAmD;AACjD5B,gBAAAA,MAAM,CAACjR,IAAP,CAAY,MAAI,CAACwO,OAAL,CAAa+D,IAAI,CAACG,SAAL,EAAb,CAAZ;AACD,eAFD,MAEO;AACLzB,gBAAAA,MAAM,CAACjR,IAAP,CAAYgQ,SAAZ;AACD;AACF,aANM,MAMA;AACLiB,cAAAA,MAAM,CAACjR,IAAP,CAAYgQ,SAAZ;AACD;AACF;AAtBW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAuBZ,eAAOiB,MAAP;AACD,OAxBM,EAAP;AAyBD,K,CAGD;;;;2CACuB6B,S,EAAWC,I,EAAM;AAAA;;AACtC,UAAIC,SAAS,GAAGF,SAAS,CAACG,YAAV,EAAhB;;AAEA,UAAIC,YAAY,GAAG,SAAfA,YAAe,CAAA7I,KAAK;AAAA,eAAI8I,eAAe,CAACC,OAAD,EAAU,KAAV,EAAiB,UAAAC,CAAC;AAAA,iBAAIA,CAAC,CAACC,GAAF,CAAMjJ,KAAN,CAAJ;AAAA,SAAlB,CAAnB;AAAA,OAAxB;;AAEA,UAAIkJ,WAAW,GAAG,SAAdA,WAAc,GAAM;AACtB,eAAOP,SAAS,CAACO,WAAV,CAAsB,UAAAC,OAAO,EAAI;AAClC,cAAIA,OAAO,CAAClT,MAAR,GAAiB,CAArB,EAAwB;AAAA;AAAA;AAAA;;AAAA;AACtB,qCAAkBkT,OAAlB,wIAA2B;AAAA,oBAAlBhB,KAAkB;;AACzB,oBAAIA,KAAK,CAACC,MAAV,EAAkB;AAChBD,kBAAAA,KAAK,CAAC3N,IAAN,CAAW,UAAAA,IAAI,EAAI;AACjB,wBAAI,MAAI,CAACQ,OAAL,CAAa5C,iBAAb,IAAmCoC,IAAI,CAACiE,IAAL,CAAU2K,SAAV,CAAoB,CAApB,EAAuB,CAAvB,MAA8B,GAArE,EAA2E;AACzE;AACD;;AACD5O,oBAAAA,IAAI,CAAC6O,QAAL,aAAmBX,IAAnB,cAA2BlO,IAAI,CAACiE,IAAhC;AACA,2BAAO,MAAI,CAAC0F,OAAL,CAAa3J,IAAb,CAAP;AACD,mBAND;AAOD,iBARD,MAQO,IAAI2N,KAAK,CAACG,WAAV,EAAuB;AAC5B,kBAAA,MAAI,CAACC,sBAAL,CAA4BJ,KAA5B,YAAsCO,IAAtC,cAA8CP,KAAK,CAAC1J,IAApD;AACD;AACF,eAbqB,CAetB;AACA;AACA;;AAjBsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAkBtByK,YAAAA,WAAW;AACZ;;AACD,iBAAO,IAAP;AACD,SAtBE,EAuBDL,YAvBC,CAAP;AAwBD,OAzBD;;AA2BA,aAAOK,WAAW,EAAlB;AACD,K,CAGD;AACA;AACA;AACA;AACA;AACA;;;;2BACO1O,I,EAAMa,I,EAAM;AACjB,UAAI,KAAKL,OAAL,CAAa5D,WAAb,IAA4BoD,IAAI,CAACM,IAAL,GAAa,KAAKE,OAAL,CAAa5D,WAAb,GAA2B,IAA3B,GAAkC,IAA/E,EAAsF;AACpF,eAAOiE,IAAI,CAAC,KAAKL,OAAL,CAAa7B,cAAb,CAA4BgJ,OAA5B,CAAoC,cAApC,EAAoDtF,IAAI,CAAC4K,KAAL,CAAWjN,IAAI,CAACM,IAAL,GAAY,IAAZ,GAAmB,KAA9B,IAAuC,GAA3F,EAAgGqH,OAAhG,CAAwG,iBAAxG,EAA2H,KAAKnH,OAAL,CAAa5D,WAAxI,CAAD,CAAX;AACD,OAFD,MAEO,IAAI,CAAChB,QAAQ,CAACkT,WAAT,CAAqB9O,IAArB,EAA2B,KAAKQ,OAAL,CAAa3C,aAAxC,CAAL,EAA6D;AAClE,eAAOgD,IAAI,CAAC,KAAKL,OAAL,CAAa5B,mBAAd,CAAX;AACD,OAFM,MAEA,IAAK,KAAK4B,OAAL,CAAa/C,QAAb,IAAyB,IAA1B,IAAoC,KAAKyP,gBAAL,GAAwBzR,MAAxB,IAAkC,KAAK+E,OAAL,CAAa/C,QAAvF,EAAkG;AACvGoD,QAAAA,IAAI,CAAC,KAAKL,OAAL,CAAarB,oBAAb,CAAkCwI,OAAlC,CAA0C,cAA1C,EAA0D,KAAKnH,OAAL,CAAa/C,QAAvE,CAAD,CAAJ;AACA,eAAO,KAAKgJ,IAAL,CAAU,kBAAV,EAA8BzG,IAA9B,CAAP;AACD,OAHM,MAGA;AACL,eAAO,KAAKQ,OAAL,CAAaI,MAAb,CAAoB2H,IAApB,CAAyB,IAAzB,EAA+BvI,IAA/B,EAAqCa,IAArC,CAAP;AACD;AACF;;;4BAEOb,I,EAAM;AAAA;;AACZA,MAAAA,IAAI,CAACC,MAAL,GAAc;AACZC,QAAAA,IAAI,EAAEtE,QAAQ,CAACmT,MAAT,EADM;AAEZjJ,QAAAA,QAAQ,EAAE,CAFE;AAGZ;AACA;AACA4F,QAAAA,KAAK,EAAE1L,IAAI,CAACM,IALA;AAMZyF,QAAAA,SAAS,EAAE,CANC;AAOZiJ,QAAAA,QAAQ,EAAE,KAAKC,WAAL,CAAiBjP,IAAjB,CAPE;AAQZkP,QAAAA,OAAO,EAAE,KAAK1O,OAAL,CAAalE,QAAb,KAA0B,KAAKkE,OAAL,CAAajE,aAAb,IAA8ByD,IAAI,CAACM,IAAL,GAAY,KAAKE,OAAL,CAAahE,SAAjF,CARG;AASZkE,QAAAA,eAAe,EAAE2B,IAAI,CAAC8M,IAAL,CAAUnP,IAAI,CAACM,IAAL,GAAY,KAAKE,OAAL,CAAahE,SAAnC;AATL,OAAd;AAWA,WAAKoD,KAAL,CAAWzE,IAAX,CAAgB6E,IAAhB;AAEAA,MAAAA,IAAI,CAACwE,MAAL,GAAc5I,QAAQ,CAACsN,KAAvB;AAEA,WAAKzC,IAAL,CAAU,WAAV,EAAuBzG,IAAvB;;AAEA,WAAKoP,iBAAL,CAAuBpP,IAAvB;;AAEA,aAAO,KAAKY,MAAL,CAAYZ,IAAZ,EAAkB,UAAAwF,KAAK,EAAI;AAChC,YAAIA,KAAJ,EAAW;AACTxF,UAAAA,IAAI,CAAC8I,QAAL,GAAgB,KAAhB;;AACA,UAAA,MAAI,CAACuG,gBAAL,CAAsB,CAACrP,IAAD,CAAtB,EAA8BwF,KAA9B,EAFS,CAE6B;;AACvC,SAHD,MAGO;AACLxF,UAAAA,IAAI,CAAC8I,QAAL,GAAgB,IAAhB;;AACA,cAAI,MAAI,CAACtI,OAAL,CAAaxC,SAAjB,EAA4B;AAC1B,YAAA,MAAI,CAACsR,WAAL,CAAiBtP,IAAjB;AACD,WAJI,CAIH;;AACH;;AACD,eAAO,MAAI,CAACiF,2BAAL,EAAP;AACD,OAXM,CAAP;AAYD,K,CAGD;;;;iCACarF,K,EAAO;AAAA;AAAA;AAAA;;AAAA;AAClB,+BAAiBA,KAAjB,wIAAwB;AAAA,cAAfI,IAAe;AACtB,eAAKsP,WAAL,CAAiBtP,IAAjB;AACD;AAHiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAIlB,aAAO,IAAP;AACD;;;gCAEWA,I,EAAM;AAAA;;AAChB,UAAKA,IAAI,CAACwE,MAAL,KAAgB5I,QAAQ,CAACsN,KAA1B,IAAqClJ,IAAI,CAAC8I,QAAL,KAAkB,IAA3D,EAAkE;AAChE9I,QAAAA,IAAI,CAACwE,MAAL,GAAc5I,QAAQ,CAACqN,MAAvB;;AACA,YAAI,KAAKzI,OAAL,CAAazC,gBAAjB,EAAmC;AACjC,iBAAOwH,UAAU,CAAE;AAAA,mBAAM,MAAI,CAACgK,YAAL,EAAN;AAAA,WAAF,EAA8B,CAA9B,CAAjB,CADiC,CACkB;AACpD;AACF,OALD,MAKO;AACL,cAAM,IAAI/M,KAAJ,CAAU,kFAAV,CAAN;AACD;AACF;;;sCAEiBxC,I,EAAM;AAAA;;AACtB,UAAI,KAAKQ,OAAL,CAAa1D,qBAAb,IAAsCkD,IAAI,CAAC4C,IAAL,CAAUC,KAAV,CAAgB,SAAhB,CAAtC,IAAqE7C,IAAI,CAACM,IAAL,IAAc,KAAKE,OAAL,CAAazD,oBAAb,GAAoC,IAApC,GAA2C,IAAlI,EAA0I;AACxI,aAAKkK,eAAL,CAAqB9L,IAArB,CAA0B6E,IAA1B;;AACA,eAAOuF,UAAU,CAAE;AAAA,iBAAM,MAAI,CAACiK,sBAAL,EAAN;AAAA,SAAF,EAAwC,CAAxC,CAAjB,CAFwI,CAE3E;AAC9D;AACF;;;6CAEwB;AAAA;;AACvB,UAAI,KAAKtI,oBAAL,IAA8B,KAAKD,eAAL,CAAqBxL,MAArB,KAAgC,CAAlE,EAAsE;AACpE;AACD;;AAED,WAAKyL,oBAAL,GAA4B,IAA5B;;AACA,UAAIlH,IAAI,GAAG,KAAKiH,eAAL,CAAqBwI,KAArB,EAAX;;AACA,aAAO,KAAKC,eAAL,CAAqB1P,IAArB,EAA2B,KAAKQ,OAAL,CAAaxD,cAAxC,EAAwD,KAAKwD,OAAL,CAAavD,eAArE,EAAsF,KAAKuD,OAAL,CAAatD,eAAnG,EAAoH,IAApH,EAA0H,UAAAiI,OAAO,EAAI;AAC1I,QAAA,OAAI,CAACsB,IAAL,CAAU,WAAV,EAAuBzG,IAAvB,EAA6BmF,OAA7B;;AACA,QAAA,OAAI,CAAC+B,oBAAL,GAA4B,KAA5B;AACA,eAAO,OAAI,CAACsI,sBAAL,EAAP;AACD,OAJM,CAAP;AAKD,K,CAGD;;;;+BACWxP,I,EAAM;AACf,UAAIA,IAAI,CAACwE,MAAL,KAAgB5I,QAAQ,CAAC6I,SAA7B,EAAwC;AACtC,aAAKgI,YAAL,CAAkBzM,IAAlB;AACD;;AACD,WAAKJ,KAAL,GAAa+P,OAAO,CAAC,KAAK/P,KAAN,EAAaI,IAAb,CAApB;AAEA,WAAKyG,IAAL,CAAU,aAAV,EAAyBzG,IAAzB;;AACA,UAAI,KAAKJ,KAAL,CAAWnE,MAAX,KAAsB,CAA1B,EAA6B;AAC3B,eAAO,KAAKgL,IAAL,CAAU,OAAV,CAAP;AACD;AACF,K,CAED;;;;mCACemJ,iB,EAAmB;AAChC;AACA,UAAIA,iBAAiB,IAAI,IAAzB,EAA+B;AAC7BA,QAAAA,iBAAiB,GAAG,KAApB;AACD;;AAJ+B;AAAA;AAAA;;AAAA;AAKhC,+BAAiB,KAAKhQ,KAAL,CAAWiQ,KAAX,EAAjB,wIAAqC;AAAA,cAA5B7P,IAA4B;;AACnC,cAAKA,IAAI,CAACwE,MAAL,KAAgB5I,QAAQ,CAAC6I,SAA1B,IAAwCmL,iBAA5C,EAA+D;AAC7D,iBAAKjL,UAAL,CAAgB3E,IAAhB;AACD;AACF;AAT+B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAUhC,aAAO,IAAP;AACD,K,CAED;AACA;AACA;;;;gCACYA,I,EAAM6B,K,EAAOC,M,EAAQvE,Y,EAAcjC,Q,EAAU;AAAA;;AACvD,aAAO,KAAKoU,eAAL,CAAqB1P,IAArB,EAA2B6B,KAA3B,EAAkCC,MAAlC,EAA0CvE,YAA1C,EAAwD,IAAxD,EAA8D,UAAC4H,OAAD,EAAU2K,MAAV,EAAqB;AACxF,YAAIA,MAAM,IAAI,IAAd,EAAoB;AAClB;AACA,iBAAOxU,QAAQ,CAAC0E,IAAD,CAAf;AACD,SAHD,MAGO;AAAA,cACA3C,cADA,GACkB,OAAI,CAACmD,OADvB,CACAnD,cADA;;AAEL,cAAIA,cAAc,IAAI,IAAtB,EAA4B;AAC1BA,YAAAA,cAAc,GAAG2C,IAAI,CAAC4C,IAAtB;AACD;;AACD,cAAImN,cAAc,GAAGD,MAAM,CAACE,SAAP,CAAiB3S,cAAjB,EAAiC,OAAI,CAACmD,OAAL,CAAalD,aAA9C,CAArB;;AACA,cAAKD,cAAc,KAAK,YAApB,IAAsCA,cAAc,KAAK,WAA7D,EAA2E;AACzE;AACA0S,YAAAA,cAAc,GAAGE,WAAW,CAACC,OAAZ,CAAoBlQ,IAAI,CAACmQ,OAAzB,EAAkCJ,cAAlC,CAAjB;AACD;;AACD,iBAAOzU,QAAQ,CAACM,QAAQ,CAACwU,aAAT,CAAuBL,cAAvB,CAAD,CAAf;AACD;AACF,OAhBM,CAAP;AAiBD;;;oCAEe/P,I,EAAM6B,K,EAAOC,M,EAAQvE,Y,EAAc8S,c,EAAgB/U,Q,EAAU;AAAA;;AAC3E,UAAIgV,UAAU,GAAG,IAAIC,UAAJ,EAAjB;;AAEAD,MAAAA,UAAU,CAACE,MAAX,GAAoB,YAAM;AAExBxQ,QAAAA,IAAI,CAACmQ,OAAL,GAAeG,UAAU,CAAClE,MAA1B,CAFwB,CAIxB;;AACA,YAAIpM,IAAI,CAAC4C,IAAL,KAAc,eAAlB,EAAmC;AACjC,cAAItH,QAAQ,IAAI,IAAhB,EAAsB;AACpBA,YAAAA,QAAQ,CAACgV,UAAU,CAAClE,MAAZ,CAAR;AACD;;AACD;AACD;;AAED,eAAO,OAAI,CAACqE,sBAAL,CAA4BzQ,IAA5B,EAAkC6B,KAAlC,EAAyCC,MAAzC,EAAiDvE,YAAjD,EAA+D8S,cAA/D,EAA+E/U,QAA/E,CAAP;AACD,OAbD;;AAeA,aAAOgV,UAAU,CAACI,aAAX,CAAyB1Q,IAAzB,CAAP;AACD;;;2CAEsBA,I,EAAM6B,K,EAAOC,M,EAAQvE,Y,EAAc8S,c,EAAgB/U,Q,EAAUqV,W,EAAa;AAAA;;AAC/F;AACA;AACA,UAAIC,GAAG,GAAG9I,QAAQ,CAACxG,aAAT,CAAuB,KAAvB,CAAV;;AAEA,UAAIqP,WAAJ,EAAiB;AACfC,QAAAA,GAAG,CAACD,WAAJ,GAAkBA,WAAlB;AACD;;AAEDC,MAAAA,GAAG,CAACJ,MAAJ,GAAa,YAAM;AACjB,YAAIK,QAAQ,GAAG,kBAAAvV,QAAQ;AAAA,iBAAIA,QAAQ,CAAC,CAAD,CAAZ;AAAA,SAAvB;;AACA,YAAK,OAAOwV,IAAP,KAAgB,WAAhB,IAA+BA,IAAI,KAAK,IAAzC,IAAkDT,cAAtD,EAAsE;AACpEQ,UAAAA,QAAQ,GAAG,kBAAAvV,QAAQ;AAAA,mBACfwV,IAAI,CAACC,OAAL,CAAaH,GAAb,EAAkB,YAAY;AAC5B,qBAAOtV,QAAQ,CAACwV,IAAI,CAACE,MAAL,CAAY,IAAZ,EAAkB,aAAlB,CAAD,CAAf;AACD,aAFD,CADe;AAAA,WAAnB;AAKD;;AAED,eAAOH,QAAQ,CAAC,UAAAI,WAAW,EAAI;AAC7BjR,UAAAA,IAAI,CAAC6B,KAAL,GAAa+O,GAAG,CAAC/O,KAAjB;AACA7B,UAAAA,IAAI,CAAC8B,MAAL,GAAc8O,GAAG,CAAC9O,MAAlB;;AAEA,cAAIoP,UAAU,GAAG,OAAI,CAAC1Q,OAAL,CAAaoB,MAAb,CAAoB2G,IAApB,CAAyB,OAAzB,EAA+BvI,IAA/B,EAAqC6B,KAArC,EAA4CC,MAA5C,EAAoDvE,YAApD,CAAjB;;AAEA,cAAIuS,MAAM,GAAGhI,QAAQ,CAACxG,aAAT,CAAuB,QAAvB,CAAb;AACA,cAAI6P,GAAG,GAAGrB,MAAM,CAACsB,UAAP,CAAkB,IAAlB,CAAV;AAEAtB,UAAAA,MAAM,CAACjO,KAAP,GAAeqP,UAAU,CAACzO,QAA1B;AACAqN,UAAAA,MAAM,CAAChO,MAAP,GAAgBoP,UAAU,CAACxO,SAA3B;;AAEA,cAAIuO,WAAW,GAAG,CAAlB,EAAqB;AACnBnB,YAAAA,MAAM,CAACjO,KAAP,GAAeqP,UAAU,CAACxO,SAA1B;AACAoN,YAAAA,MAAM,CAAChO,MAAP,GAAgBoP,UAAU,CAACzO,QAA3B;AACD;;AAED,kBAAQwO,WAAR;AACE,iBAAK,CAAL;AACE;AACAE,cAAAA,GAAG,CAACE,SAAJ,CAAcvB,MAAM,CAACjO,KAArB,EAA4B,CAA5B;AACAsP,cAAAA,GAAG,CAACG,KAAJ,CAAU,CAAC,CAAX,EAAc,CAAd;AACA;;AACF,iBAAK,CAAL;AACE;AACAH,cAAAA,GAAG,CAACE,SAAJ,CAAcvB,MAAM,CAACjO,KAArB,EAA4BiO,MAAM,CAAChO,MAAnC;AACAqP,cAAAA,GAAG,CAACI,MAAJ,CAAWlP,IAAI,CAACmP,EAAhB;AACA;;AACF,iBAAK,CAAL;AACE;AACAL,cAAAA,GAAG,CAACE,SAAJ,CAAc,CAAd,EAAiBvB,MAAM,CAAChO,MAAxB;AACAqP,cAAAA,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAC,CAAd;AACA;;AACF,iBAAK,CAAL;AACE;AACAH,cAAAA,GAAG,CAACI,MAAJ,CAAW,MAAMlP,IAAI,CAACmP,EAAtB;AACAL,cAAAA,GAAG,CAACG,KAAJ,CAAU,CAAV,EAAa,CAAC,CAAd;AACA;;AACF,iBAAK,CAAL;AACE;AACAH,cAAAA,GAAG,CAACI,MAAJ,CAAW,MAAMlP,IAAI,CAACmP,EAAtB;AACAL,cAAAA,GAAG,CAACE,SAAJ,CAAc,CAAd,EAAiB,CAACvB,MAAM,CAACjO,KAAzB;AACA;;AACF,iBAAK,CAAL;AACE;AACAsP,cAAAA,GAAG,CAACI,MAAJ,CAAW,MAAMlP,IAAI,CAACmP,EAAtB;AACAL,cAAAA,GAAG,CAACE,SAAJ,CAAcvB,MAAM,CAAChO,MAArB,EAA6B,CAACgO,MAAM,CAACjO,KAArC;AACAsP,cAAAA,GAAG,CAACG,KAAJ,CAAU,CAAC,CAAX,EAAc,CAAd;AACA;;AACF,iBAAK,CAAL;AACE;AACAH,cAAAA,GAAG,CAACI,MAAJ,CAAW,CAAC,GAAD,GAAOlP,IAAI,CAACmP,EAAvB;AACAL,cAAAA,GAAG,CAACE,SAAJ,CAAc,CAACvB,MAAM,CAAChO,MAAtB,EAA8B,CAA9B;AACA;AApCJ,WAjB6B,CAwD7B;;;AACA2P,UAAAA,eAAe,CAACN,GAAD,EAAMP,GAAN,EAAWM,UAAU,CAAClP,IAAX,IAAmB,IAAnB,GAA0BkP,UAAU,CAAClP,IAArC,GAA4C,CAAvD,EAA0DkP,UAAU,CAACjP,IAAX,IAAmB,IAAnB,GAA0BiP,UAAU,CAACjP,IAArC,GAA4C,CAAtG,EAAyGiP,UAAU,CAAChP,QAApH,EAA8HgP,UAAU,CAAC/O,SAAzI,EAAoJ+O,UAAU,CAACQ,IAAX,IAAmB,IAAnB,GAA0BR,UAAU,CAACQ,IAArC,GAA4C,CAAhM,EAAmMR,UAAU,CAACS,IAAX,IAAmB,IAAnB,GAA0BT,UAAU,CAACS,IAArC,GAA4C,CAA/O,EAAkPT,UAAU,CAACzO,QAA7P,EAAuQyO,UAAU,CAACxO,SAAlR,CAAf;AAEA,cAAIwC,SAAS,GAAG4K,MAAM,CAACE,SAAP,CAAiB,WAAjB,CAAhB;;AAEA,cAAI1U,QAAQ,IAAI,IAAhB,EAAsB;AACpB,mBAAOA,QAAQ,CAAC4J,SAAD,EAAY4K,MAAZ,CAAf;AACD;AACF,SAhEc,CAAf;AAiED,OA3ED;;AA6EA,UAAIxU,QAAQ,IAAI,IAAhB,EAAsB;AACpBsV,QAAAA,GAAG,CAACgB,OAAJ,GAActW,QAAd;AACD;;AAED,aAAOsV,GAAG,CAACtL,GAAJ,GAAUtF,IAAI,CAACmQ,OAAtB;AACD,K,CAGD;;;;mCACe;AAAA,UACR/T,eADQ,GACW,KAAKoE,OADhB,CACRpE,eADQ;AAEb,UAAIyV,gBAAgB,GAAG,KAAK1H,iBAAL,GAAyB1O,MAAhD;AACA,UAAIC,CAAC,GAAGmW,gBAAR,CAHa,CAKb;;AACA,UAAIA,gBAAgB,IAAIzV,eAAxB,EAAyC;AACvC;AACD;;AAED,UAAI0V,WAAW,GAAG,KAAK1H,cAAL,EAAlB;;AAEA,UAAI,EAAE0H,WAAW,CAACrW,MAAZ,GAAqB,CAAvB,CAAJ,EAA+B;AAC7B;AACD;;AAED,UAAI,KAAK+E,OAAL,CAAanE,cAAjB,EAAiC;AAC/B;AACA,eAAO,KAAK0V,YAAL,CAAkBD,WAAW,CAACjC,KAAZ,CAAkB,CAAlB,EAAsBzT,eAAe,GAAGyV,gBAAxC,CAAlB,CAAP;AACD,OAHD,MAGO;AACL,eAAOnW,CAAC,GAAGU,eAAX,EAA4B;AAC1B,cAAI,CAAC0V,WAAW,CAACrW,MAAjB,EAAyB;AACvB;AACD,WAHyB,CAGxB;;;AACF,eAAKuW,WAAL,CAAiBF,WAAW,CAACrC,KAAZ,EAAjB;AACA/T,UAAAA,CAAC;AACF;AACF;AACF,K,CAGD;;;;gCACYsE,I,EAAM;AAChB,aAAO,KAAK+R,YAAL,CAAkB,CAAC/R,IAAD,CAAlB,CAAP;AACD,K,CAGD;;;;iCACaJ,K,EAAO;AAAA;AAAA;AAAA;;AAAA;AAClB,+BAAiBA,KAAjB,wIAAwB;AAAA,cAAfI,IAAe;AACtBA,UAAAA,IAAI,CAAC2F,UAAL,GAAkB,IAAlB,CADsB,CACE;;AACxB3F,UAAAA,IAAI,CAACwE,MAAL,GAAc5I,QAAQ,CAAC6I,SAAvB;AAEA,eAAKgC,IAAL,CAAU,YAAV,EAAwBzG,IAAxB;AACD;AANiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAQlB,UAAI,KAAKQ,OAAL,CAAanE,cAAjB,EAAiC;AAC/B,aAAKoK,IAAL,CAAU,oBAAV,EAAgC7G,KAAhC;AACD;;AAED,aAAO,KAAKqS,WAAL,CAAiBrS,KAAjB,CAAP;AACD;;;qCAGgBC,G,EAAK;AACpB,UAAID,KAAJ;AACA,aAAOA,KAAK,GAAI,KAAKA,KAAL,CAAWiJ,MAAX,CAAkB,UAAC7I,IAAD;AAAA,eAAUA,IAAI,CAACH,GAAL,KAAaA,GAAvB;AAAA,OAAlB,EAA8CkJ,GAA9C,CAAkD,UAAC/I,IAAD;AAAA,eAAUA,IAAV;AAAA,OAAlD,CAAhB;AACD,K,CAGD;AACA;AACA;AACA;;;;iCACaA,I,EAAM;AACjB,UAAIA,IAAI,CAACwE,MAAL,KAAgB5I,QAAQ,CAAC6I,SAA7B,EAAwC;AACtC,YAAIyN,YAAY,GAAG,KAAKC,gBAAL,CAAsBnS,IAAI,CAACH,GAA3B,CAAnB;;AADsC;AAAA;AAAA;;AAAA;AAEtC,iCAAwBqS,YAAxB,wIAAsC;AAAA,gBAA7BE,WAA6B;AACpCA,YAAAA,WAAW,CAAC5N,MAAZ,GAAqB5I,QAAQ,CAACyW,QAA9B;AACD;AAJqC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAKtC,YAAI,OAAOrS,IAAI,CAACH,GAAZ,KAAoB,WAAxB,EAAqC;AACnCG,UAAAA,IAAI,CAACH,GAAL,CAASyS,KAAT;AACD;;AAPqC;AAAA;AAAA;;AAAA;AAQtC,iCAAwBJ,YAAxB,wIAAsC;AAAA,gBAA7BE,YAA6B;AACpC,iBAAK3L,IAAL,CAAU,UAAV,EAAsB2L,YAAtB;AACD;AAVqC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAWtC,YAAI,KAAK5R,OAAL,CAAanE,cAAjB,EAAiC;AAC/B,eAAKoK,IAAL,CAAU,kBAAV,EAA8ByL,YAA9B;AACD;AAEF,OAfD,MAeO,IAAIlS,IAAI,CAACwE,MAAL,KAAgB5I,QAAQ,CAACsN,KAAzB,IAAkClJ,IAAI,CAACwE,MAAL,KAAgB5I,QAAQ,CAACqN,MAA/D,EAAuE;AAC5EjJ,QAAAA,IAAI,CAACwE,MAAL,GAAc5I,QAAQ,CAACyW,QAAvB;AACA,aAAK5L,IAAL,CAAU,UAAV,EAAsBzG,IAAtB;;AACA,YAAI,KAAKQ,OAAL,CAAanE,cAAjB,EAAiC;AAC/B,eAAKoK,IAAL,CAAU,kBAAV,EAA8B,CAACzG,IAAD,CAA9B;AACD;AACF;;AAED,UAAI,KAAKQ,OAAL,CAAazC,gBAAjB,EAAmC;AACjC,eAAO,KAAKwR,YAAL,EAAP;AACD;AACF;;;kCAEagD,M,EAAiB;AAC7B,UAAI,OAAOA,MAAP,KAAkB,UAAtB,EAAkC;AAAA,2CADXlX,IACW;AADXA,UAAAA,IACW;AAAA;;AAChC,eAAOkX,MAAM,CAAChX,KAAP,CAAa,IAAb,EAAmBF,IAAnB,CAAP;AACD;;AACD,aAAOkX,MAAP;AACD;;;+BAEUvS,I,EAAM;AAAE,aAAO,KAAKiS,WAAL,CAAiB,CAACjS,IAAD,CAAjB,CAAP;AAAkC;;;gCAEzCJ,K,EAAO;AAAA;;AACjB,WAAK4S,eAAL,CAAqB5S,KAArB,EAA4B,UAAC6S,gBAAD,EAAsB;AAChD,YAAI7S,KAAK,CAAC,CAAD,CAAL,CAASK,MAAT,CAAgBiP,OAApB,EAA6B;AAC3B;AAEA;AACA;AACA,cAAIlP,IAAI,GAAGJ,KAAK,CAAC,CAAD,CAAhB;AACA,cAAI8S,eAAe,GAAGD,gBAAgB,CAAC,CAAD,CAAtC;AACA,cAAIE,iBAAiB,GAAG,CAAxB;AAEA3S,UAAAA,IAAI,CAACC,MAAL,CAAY2S,MAAZ,GAAqB,EAArB;;AAEA,cAAIC,eAAe,GAAG,SAAlBA,eAAkB,GAAM;AAC1B,gBAAIC,UAAU,GAAG,CAAjB,CAD0B,CAG1B;;AACA,mBAAO9S,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBE,UAAnB,MAAmC3H,SAA1C,EAAqD;AACnD2H,cAAAA,UAAU;AACX,aANyB,CAQ1B;;;AACA,gBAAIA,UAAU,IAAI9S,IAAI,CAACC,MAAL,CAAYS,eAA9B,EAA+C;AAE/CiS,YAAAA,iBAAiB;AAEjB,gBAAII,KAAK,GAAGD,UAAU,GAAG,OAAI,CAACtS,OAAL,CAAahE,SAAtC;AACA,gBAAIwW,GAAG,GAAG3Q,IAAI,CAACC,GAAL,CAASyQ,KAAK,GAAG,OAAI,CAACvS,OAAL,CAAahE,SAA9B,EAAyCwD,IAAI,CAACM,IAA9C,CAAV;AAEA,gBAAI2S,SAAS,GAAG;AACdhP,cAAAA,IAAI,EAAE,OAAI,CAAC8H,aAAL,CAAmB,CAAnB,CADQ;AAEdmH,cAAAA,IAAI,EAAER,eAAe,CAACS,WAAhB,GAA8BT,eAAe,CAACS,WAAhB,CAA4BJ,KAA5B,EAAmCC,GAAnC,CAA9B,GAAwEN,eAAe,CAAC7C,KAAhB,CAAsBkD,KAAtB,EAA6BC,GAA7B,CAFhE;AAGdhE,cAAAA,QAAQ,EAAEhP,IAAI,CAACC,MAAL,CAAY+O,QAHR;AAId8D,cAAAA,UAAU,EAAEA;AAJE,aAAhB;AAOA9S,YAAAA,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBE,UAAnB,IAAiC;AAC/B9S,cAAAA,IAAI,EAAEA,IADyB;AAE/BI,cAAAA,KAAK,EAAE0S,UAFwB;AAG/BG,cAAAA,SAAS,EAAEA,SAHoB;AAGT;AACtBzO,cAAAA,MAAM,EAAE5I,QAAQ,CAAC6I,SAJc;AAK/BqB,cAAAA,QAAQ,EAAE,CALqB;AAM/BsN,cAAAA,OAAO,EAAE,CANsB,CAMpB;;AANoB,aAAjC;;AAUA,YAAA,OAAI,CAACC,WAAL,CAAiBzT,KAAjB,EAAwB,CAACqT,SAAD,CAAxB;AACD,WAlCD;;AAoCAjT,UAAAA,IAAI,CAACC,MAAL,CAAYqT,mBAAZ,GAAkC,UAACxT,KAAD,EAAW;AAC3C,gBAAIyT,WAAW,GAAG,IAAlB;AACAzT,YAAAA,KAAK,CAAC0E,MAAN,GAAe5I,QAAQ,CAAC4X,OAAxB,CAF2C,CAI3C;;AACA1T,YAAAA,KAAK,CAACmT,SAAN,GAAkB,IAAlB,CAL2C,CAM3C;;AACAnT,YAAAA,KAAK,CAACD,GAAN,GAAY,IAAZ;;AAEA,iBAAK,IAAInE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsE,IAAI,CAACC,MAAL,CAAYS,eAAhC,EAAiDhF,CAAC,EAAlD,EAAuD;AACrD,kBAAIsE,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,MAA0ByP,SAA9B,EAAyC;AACvC,uBAAO0H,eAAe,EAAtB;AACD;;AACD,kBAAI7S,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,EAAsB8I,MAAtB,KAAiC5I,QAAQ,CAAC4X,OAA9C,EAAuD;AACrDD,gBAAAA,WAAW,GAAG,KAAd;AACD;AACF;;AAED,gBAAIA,WAAJ,EAAiB;AACf,cAAA,OAAI,CAAC/S,OAAL,CAAaM,cAAb,CAA4Bd,IAA5B,EAAkC,YAAM;AACtC,gBAAA,OAAI,CAACyT,SAAL,CAAe7T,KAAf,EAAsB,EAAtB,EAA0B,IAA1B;AACD,eAFD;AAGD;AACF,WAvBD;;AAyBA,cAAI,OAAI,CAACY,OAAL,CAAa/D,oBAAjB,EAAuC;AACrC,iBAAK,IAAIf,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsE,IAAI,CAACC,MAAL,CAAYS,eAAhC,EAAiDhF,CAAC,EAAlD,EAAsD;AACpDmX,cAAAA,eAAe;AAChB;AACF,WAJD,MAKK;AACHA,YAAAA,eAAe;AAChB;AACF,SAhFD,MAgFO;AACL,cAAIa,UAAU,GAAG,EAAjB;;AACA,eAAK,IAAIhY,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGkE,KAAK,CAACnE,MAA1B,EAAkCC,GAAC,EAAnC,EAAuC;AACrCgY,YAAAA,UAAU,CAAChY,GAAD,CAAV,GAAgB;AACduI,cAAAA,IAAI,EAAE,OAAI,CAAC8H,aAAL,CAAmBrQ,GAAnB,CADQ;AAEdwX,cAAAA,IAAI,EAAET,gBAAgB,CAAC/W,GAAD,CAFR;AAGdsT,cAAAA,QAAQ,EAAEpP,KAAK,CAAClE,GAAD,CAAL,CAASuE,MAAT,CAAgB+O;AAHZ,aAAhB;AAKD;;AACD,UAAA,OAAI,CAACqE,WAAL,CAAiBzT,KAAjB,EAAwB8T,UAAxB;AACD;AACF,OA5FD;AA6FD,K,CAED;;;;8BACU1T,I,EAAMH,G,EAAK;AACnB,WAAK,IAAInE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsE,IAAI,CAACC,MAAL,CAAYS,eAAhC,EAAiDhF,CAAC,EAAlD,EAAsD;AACpD,YAAIsE,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,MAA0ByP,SAA1B,IAAuCnL,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,EAAsBmE,GAAtB,KAA8BA,GAAzE,EAA8E;AAC5E,iBAAOG,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,CAAP;AACD;AACF;AACF,K,CAED;AACA;AACA;;;;gCACYkE,K,EAAO8T,U,EAAY;AAAA;;AAC7B,UAAI7T,GAAG,GAAG,IAAI8T,cAAJ,EAAV,CAD6B,CAG7B;;AAH6B;AAAA;AAAA;;AAAA;AAI7B,+BAAiB/T,KAAjB,wIAAwB;AAAA,cAAfI,IAAe;AACtBA,UAAAA,IAAI,CAACH,GAAL,GAAWA,GAAX;AACD;AAN4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAO7B,UAAID,KAAK,CAAC,CAAD,CAAL,CAASK,MAAT,CAAgBiP,OAApB,EAA6B;AAC3B;AACAtP,QAAAA,KAAK,CAAC,CAAD,CAAL,CAASK,MAAT,CAAgB2S,MAAhB,CAAuBc,UAAU,CAAC,CAAD,CAAV,CAAcZ,UAArC,EAAiDjT,GAAjD,GAAuDA,GAAvD;AACD;;AAED,UAAI5D,MAAM,GAAG,KAAK2X,aAAL,CAAmB,KAAKpT,OAAL,CAAavE,MAAhC,EAAwC2D,KAAxC,CAAb;AACA,UAAI5D,GAAG,GAAG,KAAK4X,aAAL,CAAmB,KAAKpT,OAAL,CAAaxE,GAAhC,EAAqC4D,KAArC,CAAV;AACAC,MAAAA,GAAG,CAACgU,IAAJ,CAAS5X,MAAT,EAAiBD,GAAjB,EAAsB,IAAtB,EAd6B,CAgB7B;;AACA6D,MAAAA,GAAG,CAAC1D,OAAJ,GAAc,KAAKyX,aAAL,CAAmB,KAAKpT,OAAL,CAAarE,OAAhC,EAAyCyD,KAAzC,CAAd,CAjB6B,CAmB7B;;AACAC,MAAAA,GAAG,CAAC3D,eAAJ,GAAsB,CAAC,CAAC,KAAKsE,OAAL,CAAatE,eAArC;;AAGA2D,MAAAA,GAAG,CAAC2Q,MAAJ,GAAa,UAAAvN,CAAC,EAAI;AAChB,QAAA,OAAI,CAAC6Q,kBAAL,CAAwBlU,KAAxB,EAA+BC,GAA/B,EAAoCoD,CAApC;AACD,OAFD;;AAIApD,MAAAA,GAAG,CAACkU,SAAJ,GAAgB,YAAM;AACpB,QAAA,OAAI,CAACC,kBAAL,CAAwBpU,KAAxB,EAA+BC,GAA/B,mCAA8D,OAAI,CAACW,OAAL,CAAarE,OAA3E;AACD,OAFD;;AAIA0D,MAAAA,GAAG,CAAC+R,OAAJ,GAAc,YAAM;AAClB,QAAA,OAAI,CAACoC,kBAAL,CAAwBpU,KAAxB,EAA+BC,GAA/B;AACD,OAFD,CA/B6B,CAmC7B;;;AACA,UAAIoU,WAAW,GAAGpU,GAAG,CAACI,MAAJ,IAAc,IAAd,GAAqBJ,GAAG,CAACI,MAAzB,GAAkCJ,GAApD;;AACAoU,MAAAA,WAAW,CAACC,UAAZ,GAAyB,UAACjR,CAAD;AAAA,eAAO,OAAI,CAACkR,0BAAL,CAAgCvU,KAAhC,EAAuCC,GAAvC,EAA4CoD,CAA5C,CAAP;AAAA,OAAzB;;AAEA,UAAIvF,OAAO,GAAG;AACZ,kBAAU,kBADE;AAEZ,yBAAiB,UAFL;AAGZ,4BAAoB;AAHR,OAAd;;AAMA,UAAI,KAAK8C,OAAL,CAAa9C,OAAjB,EAA0B;AACxB9B,QAAAA,QAAQ,CAACyM,MAAT,CAAgB3K,OAAhB,EAAyB,KAAK8C,OAAL,CAAa9C,OAAtC;AACD;;AAED,WAAK,IAAI0W,UAAT,IAAuB1W,OAAvB,EAAgC;AAC9B,YAAI2W,WAAW,GAAG3W,OAAO,CAAC0W,UAAD,CAAzB;;AACA,YAAIC,WAAJ,EAAiB;AACfxU,UAAAA,GAAG,CAACyU,gBAAJ,CAAqBF,UAArB,EAAiCC,WAAjC;AACD;AACF;;AAED,UAAIE,QAAQ,GAAG,IAAIC,QAAJ,EAAf,CAxD6B,CA0D7B;;AACA,UAAI,KAAKhU,OAAL,CAAab,MAAjB,EAAyB;AACvB,YAAI8U,gBAAgB,GAAG,KAAKjU,OAAL,CAAab,MAApC;;AACA,YAAI,OAAO8U,gBAAP,KAA4B,UAAhC,EAA4C;AAC1CA,UAAAA,gBAAgB,GAAGA,gBAAgB,CAAClM,IAAjB,CAAsB,IAAtB,EAA4B3I,KAA5B,EAAmCC,GAAnC,EAAwCD,KAAK,CAAC,CAAD,CAAL,CAASK,MAAT,CAAgBiP,OAAhB,GAA0B,KAAKwF,SAAL,CAAe9U,KAAK,CAAC,CAAD,CAApB,EAAyBC,GAAzB,CAA1B,GAA0D,IAAlG,CAAnB;AACD;;AAED,aAAK,IAAIyH,GAAT,IAAgBmN,gBAAhB,EAAkC;AAChC,cAAIxO,KAAK,GAAGwO,gBAAgB,CAACnN,GAAD,CAA5B;AACAiN,UAAAA,QAAQ,CAACI,MAAT,CAAgBrN,GAAhB,EAAqBrB,KAArB;AACD;AACF,OArE4B,CAuE7B;;;AAvE6B;AAAA;AAAA;;AAAA;AAwE7B,+BAAiBrG,KAAjB,wIAAwB;AAAA,cAAfI,KAAe;AACtB,eAAKyG,IAAL,CAAU,SAAV,EAAqBzG,KAArB,EAA2BH,GAA3B,EAAgC0U,QAAhC;AACD;AA1E4B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AA2E7B,UAAI,KAAK/T,OAAL,CAAanE,cAAjB,EAAiC;AAC/B,aAAKoK,IAAL,CAAU,iBAAV,EAA6B7G,KAA7B,EAAoCC,GAApC,EAAyC0U,QAAzC;AACD;;AAGD,WAAKK,mBAAL,CAAyBL,QAAzB,EAhF6B,CAmF7B;AACA;;;AACA,WAAK,IAAI7Y,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGgY,UAAU,CAACjY,MAA/B,EAAuCC,CAAC,EAAxC,EAA4C;AAC1C,YAAIuX,SAAS,GAAGS,UAAU,CAAChY,CAAD,CAA1B;AACA6Y,QAAAA,QAAQ,CAACI,MAAT,CAAgB1B,SAAS,CAAChP,IAA1B,EAAgCgP,SAAS,CAACC,IAA1C,EAAgDD,SAAS,CAACjE,QAA1D;AACD;;AAED,WAAK6F,aAAL,CAAmBhV,GAAnB,EAAwB0U,QAAxB,EAAkC3U,KAAlC;AACD,K,CAGD;;;;oCACgBA,K,EAAOiB,I,EAAM;AAAA;;AAC3B,UAAI4R,gBAAgB,GAAG,EAAvB,CAD2B,CAE3B;;AACA,UAAIqC,WAAW,GAAG,CAAlB;;AAH2B,iCAIlBpZ,CAJkB;AAKzB,QAAA,OAAI,CAAC8E,OAAL,CAAamC,aAAb,CAA2B4F,IAA3B,CAAgC,OAAhC,EAAsC3I,KAAK,CAAClE,CAAD,CAA3C,EAAgD,UAACgX,eAAD,EAAqB;AACnED,UAAAA,gBAAgB,CAAC/W,CAAD,CAAhB,GAAsBgX,eAAtB;;AACA,cAAI,EAAEoC,WAAF,KAAkBlV,KAAK,CAACnE,MAA5B,EAAoC;AAClCoF,YAAAA,IAAI,CAAC4R,gBAAD,CAAJ;AACD;AACF,SALD;AALyB;;AAI3B,WAAK,IAAI/W,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGkE,KAAK,CAACnE,MAA1B,EAAkCC,CAAC,EAAnC,EAAuC;AAAA,cAA9BA,CAA8B;AAOtC;AACF,K,CAED;;;;wCACoB6Y,Q,EAAU;AAC5B;AACA,UAAI,KAAKtT,OAAL,CAAakI,OAAb,KAAyB,MAA7B,EAAqC;AAAA;AAAA;AAAA;;AAAA;AACnC,iCAAkB,KAAKlI,OAAL,CAAa8C,gBAAb,CAA8B,iCAA9B,CAAlB,wIAAoF;AAAA,gBAA3EgR,KAA2E;AAClF,gBAAIC,SAAS,GAAGD,KAAK,CAACvM,YAAN,CAAmB,MAAnB,CAAhB;AACA,gBAAIyM,SAAS,GAAGF,KAAK,CAACvM,YAAN,CAAmB,MAAnB,CAAhB;AACA,gBAAIyM,SAAJ,EAAeA,SAAS,GAAGA,SAAS,CAACC,WAAV,EAAZ,CAHmE,CAKlF;;AACA,gBAAI,OAAOF,SAAP,KAAqB,WAArB,IAAoCA,SAAS,KAAK,IAAtD,EAA4D;;AAE5D,gBAAKD,KAAK,CAAC5L,OAAN,KAAkB,QAAnB,IAAgC4L,KAAK,CAACI,YAAN,CAAmB,UAAnB,CAApC,EAAoE;AAClE;AADkE;AAAA;AAAA;;AAAA;AAElE,uCAAmBJ,KAAK,CAACvU,OAAzB,wIAAkC;AAAA,sBAAzB+R,MAAyB;;AAChC,sBAAIA,MAAM,CAAC6C,QAAX,EAAqB;AACnBb,oBAAAA,QAAQ,CAACI,MAAT,CAAgBK,SAAhB,EAA2BzC,MAAM,CAACtM,KAAlC;AACD;AACF;AANiE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOnE,aAPD,MAOO,IAAI,CAACgP,SAAD,IAAeA,SAAS,KAAK,UAAd,IAA4BA,SAAS,KAAK,OAAzD,IAAqEF,KAAK,CAACM,OAA/E,EAAwF;AAC7Fd,cAAAA,QAAQ,CAACI,MAAT,CAAgBK,SAAhB,EAA2BD,KAAK,CAAC9O,KAAjC;AACD;AACF;AAnBkC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAoBpC;AACF,K,CAED;AACA;;;;+CAC2BrG,K,EAAOC,G,EAAKoD,C,EAAG;AACxC,UAAI6C,QAAJ;;AACA,UAAI,OAAO7C,CAAP,KAAa,WAAjB,EAA8B;AAC5B6C,QAAAA,QAAQ,GAAI,MAAM7C,CAAC,CAACqS,MAAT,GAAmBrS,CAAC,CAACyI,KAAhC;;AAEA,YAAI9L,KAAK,CAAC,CAAD,CAAL,CAASK,MAAT,CAAgBiP,OAApB,EAA6B;AAC3B,cAAIlP,IAAI,GAAGJ,KAAK,CAAC,CAAD,CAAhB,CAD2B,CAE3B;;AACA,cAAIE,KAAK,GAAG,KAAK4U,SAAL,CAAe1U,IAAf,EAAqBH,GAArB,CAAZ;;AACAC,UAAAA,KAAK,CAACgG,QAAN,GAAiBA,QAAjB;AACAhG,UAAAA,KAAK,CAAC4L,KAAN,GAAczI,CAAC,CAACyI,KAAhB;AACA5L,UAAAA,KAAK,CAACiG,SAAN,GAAkB9C,CAAC,CAACqS,MAApB;AACA,cAAIC,YAAY,GAAG,CAAnB;AAAA,cAAsBC,SAAtB;AAAA,cAAiCC,aAAjC;AACAzV,UAAAA,IAAI,CAACC,MAAL,CAAY6F,QAAZ,GAAuB,CAAvB;AACA9F,UAAAA,IAAI,CAACC,MAAL,CAAYyL,KAAZ,GAAoB,CAApB;AACA1L,UAAAA,IAAI,CAACC,MAAL,CAAY8F,SAAZ,GAAwB,CAAxB;;AACA,eAAK,IAAIrK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsE,IAAI,CAACC,MAAL,CAAYS,eAAhC,EAAiDhF,CAAC,EAAlD,EAAsD;AACpD,gBAAIsE,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,MAA0ByP,SAA1B,IAAuCnL,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,EAAsBoK,QAAtB,KAAmCqF,SAA9E,EAAyF;AACvFnL,cAAAA,IAAI,CAACC,MAAL,CAAY6F,QAAZ,IAAwB9F,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,EAAsBoK,QAA9C;AACA9F,cAAAA,IAAI,CAACC,MAAL,CAAYyL,KAAZ,IAAqB1L,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,EAAsBgQ,KAA3C;AACA1L,cAAAA,IAAI,CAACC,MAAL,CAAY8F,SAAZ,IAAyB/F,IAAI,CAACC,MAAL,CAAY2S,MAAZ,CAAmBlX,CAAnB,EAAsBqK,SAA/C;AACD;AACF;;AACD/F,UAAAA,IAAI,CAACC,MAAL,CAAY6F,QAAZ,GAAuB9F,IAAI,CAACC,MAAL,CAAY6F,QAAZ,GAAuB9F,IAAI,CAACC,MAAL,CAAYS,eAA1D;AACD,SAnBD,MAmBO;AAAA;AAAA;AAAA;;AAAA;AACL,mCAAiBd,KAAjB,wIAAwB;AAAA,kBAAfI,MAAe;AACtBA,cAAAA,MAAI,CAACC,MAAL,CAAY6F,QAAZ,GAAuBA,QAAvB;AACA9F,cAAAA,MAAI,CAACC,MAAL,CAAYyL,KAAZ,GAAoBzI,CAAC,CAACyI,KAAtB;AACA1L,cAAAA,MAAI,CAACC,MAAL,CAAY8F,SAAZ,GAAwB9C,CAAC,CAACqS,MAA1B;AACD;AALI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAMN;;AA5B2B;AAAA;AAAA;;AAAA;AA6B5B,iCAAiB1V,KAAjB,wIAAwB;AAAA,gBAAfI,MAAe;AACtB,iBAAKyG,IAAL,CAAU,gBAAV,EAA4BzG,MAA5B,EAAkCA,MAAI,CAACC,MAAL,CAAY6F,QAA9C,EAAwD9F,MAAI,CAACC,MAAL,CAAY8F,SAApE;AACD;AA/B2B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgC7B,OAhCD,MAgCO;AACL;AAEA,YAAI2P,gBAAgB,GAAG,IAAvB;AAEA5P,QAAAA,QAAQ,GAAG,GAAX;AALK;AAAA;AAAA;;AAAA;AAOL,iCAAiBlG,KAAjB,wIAAwB;AAAA,gBAAfI,MAAe;;AACtB,gBAAKA,MAAI,CAACC,MAAL,CAAY6F,QAAZ,KAAyB,GAA1B,IAAmC9F,MAAI,CAACC,MAAL,CAAY8F,SAAZ,KAA0B/F,MAAI,CAACC,MAAL,CAAYyL,KAA7E,EAAqF;AACnFgK,cAAAA,gBAAgB,GAAG,KAAnB;AACD;;AACD1V,YAAAA,MAAI,CAACC,MAAL,CAAY6F,QAAZ,GAAuBA,QAAvB;AACA9F,YAAAA,MAAI,CAACC,MAAL,CAAY8F,SAAZ,GAAwB/F,MAAI,CAACC,MAAL,CAAYyL,KAApC;AACD,WAbI,CAeL;;AAfK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAgBL,YAAIgK,gBAAJ,EAAsB;AACpB;AACD;;AAlBI;AAAA;AAAA;;AAAA;AAoBL,iCAAiB9V,KAAjB,wIAAwB;AAAA,gBAAfI,MAAe;AACtB,iBAAKyG,IAAL,CAAU,gBAAV,EAA4BzG,MAA5B,EAAkC8F,QAAlC,EAA4C9F,MAAI,CAACC,MAAL,CAAY8F,SAAxD;AACD;AAtBI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAuBN;AAEF;;;uCAGkBnG,K,EAAOC,G,EAAKoD,C,EAAG;AAChC,UAAI0S,QAAJ;;AAEA,UAAI/V,KAAK,CAAC,CAAD,CAAL,CAAS4E,MAAT,KAAoB5I,QAAQ,CAACyW,QAAjC,EAA2C;AACzC;AACD;;AAED,UAAIxS,GAAG,CAAC+V,UAAJ,KAAmB,CAAvB,EAA0B;AACxB;AACD;;AAED,UAAK/V,GAAG,CAACgW,YAAJ,KAAqB,aAAtB,IAAyChW,GAAG,CAACgW,YAAJ,KAAqB,MAAlE,EAA2E;AACzEF,QAAAA,QAAQ,GAAG9V,GAAG,CAACiW,YAAf;;AAEA,YAAIjW,GAAG,CAACkW,iBAAJ,CAAsB,cAAtB,KAAyC,CAAClW,GAAG,CAACkW,iBAAJ,CAAsB,cAAtB,EAAsC3K,OAAtC,CAA8C,kBAA9C,CAA9C,EAAiH;AAC/G,cAAI;AACFuK,YAAAA,QAAQ,GAAGK,IAAI,CAACC,KAAL,CAAWN,QAAX,CAAX;AACD,WAFD,CAEE,OAAOnQ,KAAP,EAAc;AACdvC,YAAAA,CAAC,GAAGuC,KAAJ;AACAmQ,YAAAA,QAAQ,GAAG,oCAAX;AACD;AACF;AACF;;AAED,WAAKxB,0BAAL,CAAgCvU,KAAhC;;AAEA,UAAI,EAAE,OAAOC,GAAG,CAAC2E,MAAX,IAAqB3E,GAAG,CAAC2E,MAAJ,GAAa,GAApC,CAAJ,EAA8C;AAC5C,aAAKwP,kBAAL,CAAwBpU,KAAxB,EAA+BC,GAA/B,EAAoC8V,QAApC;AACD,OAFD,MAEO;AACL,YAAI/V,KAAK,CAAC,CAAD,CAAL,CAASK,MAAT,CAAgBiP,OAApB,EAA6B;AAC3BtP,UAAAA,KAAK,CAAC,CAAD,CAAL,CAASK,MAAT,CAAgBqT,mBAAhB,CAAoC,KAAKoB,SAAL,CAAe9U,KAAK,CAAC,CAAD,CAApB,EAAyBC,GAAzB,CAApC;AACD,SAFD,MAEO;AACL,eAAK4T,SAAL,CAAe7T,KAAf,EAAsB+V,QAAtB,EAAgC1S,CAAhC;AACD;AACF;AACF;;;uCAEkBrD,K,EAAOC,G,EAAK8V,Q,EAAU;AACvC,UAAI/V,KAAK,CAAC,CAAD,CAAL,CAAS4E,MAAT,KAAoB5I,QAAQ,CAACyW,QAAjC,EAA2C;AACzC;AACD;;AAED,UAAIzS,KAAK,CAAC,CAAD,CAAL,CAASK,MAAT,CAAgBiP,OAAhB,IAA2B,KAAK1O,OAAL,CAAa9D,WAA5C,EAAyD;AACvD,YAAIoD,KAAK,GAAG,KAAK4U,SAAL,CAAe9U,KAAK,CAAC,CAAD,CAApB,EAAyBC,GAAzB,CAAZ;;AACA,YAAIC,KAAK,CAACsT,OAAN,KAAkB,KAAK5S,OAAL,CAAa7D,gBAAnC,EAAqD;AACnD,eAAK0W,WAAL,CAAiBzT,KAAjB,EAAwB,CAACE,KAAK,CAACmT,SAAP,CAAxB;;AACA;AACD,SAHD,MAGO;AACL1E,UAAAA,OAAO,CAAC2H,IAAR,CAAa,0CAAb;AACD;AACF;;AAbsC;AAAA;AAAA;;AAAA;AAevC,+BAAiBtW,KAAjB,wIAAwB;AAAA,cAAfI,IAAe;;AACtB,eAAKqP,gBAAL,CAAsBzP,KAAtB,EAA6B+V,QAAQ,IAAI,KAAKnV,OAAL,CAAa3B,iBAAb,CAA+B8I,OAA/B,CAAuC,gBAAvC,EAAyD9H,GAAG,CAAC2E,MAA7D,CAAzC,EAA+G3E,GAA/G;AACD;AAjBsC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBxC;;;kCAEaA,G,EAAK0U,Q,EAAU3U,K,EAAO;AAClCC,MAAAA,GAAG,CAACsW,IAAJ,CAAS5B,QAAT;AACD,K,CAED;AACA;;;;8BACU3U,K,EAAOkW,Y,EAAc7S,C,EAAG;AAAA;AAAA;AAAA;;AAAA;AAChC,+BAAiBrD,KAAjB,wIAAwB;AAAA,cAAfI,IAAe;AACtBA,UAAAA,IAAI,CAACwE,MAAL,GAAc5I,QAAQ,CAAC4X,OAAvB;AACA,eAAK/M,IAAL,CAAU,SAAV,EAAqBzG,IAArB,EAA2B8V,YAA3B,EAAyC7S,CAAzC;AACA,eAAKwD,IAAL,CAAU,UAAV,EAAsBzG,IAAtB;AACD;AAL+B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAMhC,UAAI,KAAKQ,OAAL,CAAanE,cAAjB,EAAiC;AAC/B,aAAKoK,IAAL,CAAU,iBAAV,EAA6B7G,KAA7B,EAAoCkW,YAApC,EAAkD7S,CAAlD;AACA,aAAKwD,IAAL,CAAU,kBAAV,EAA8B7G,KAA9B;AACD;;AAED,UAAI,KAAKY,OAAL,CAAazC,gBAAjB,EAAmC;AACjC,eAAO,KAAKwR,YAAL,EAAP;AACD;AACF,K,CAED;AACA;;;;qCACiB3P,K,EAAO6F,O,EAAS5F,G,EAAK;AAAA;AAAA;AAAA;;AAAA;AACpC,+BAAiBD,KAAjB,wIAAwB;AAAA,cAAfI,IAAe;AACtBA,UAAAA,IAAI,CAACwE,MAAL,GAAc5I,QAAQ,CAACwa,KAAvB;AACA,eAAK3P,IAAL,CAAU,OAAV,EAAmBzG,IAAnB,EAAyByF,OAAzB,EAAkC5F,GAAlC;AACA,eAAK4G,IAAL,CAAU,UAAV,EAAsBzG,IAAtB;AACD;AALmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAMpC,UAAI,KAAKQ,OAAL,CAAanE,cAAjB,EAAiC;AAC/B,aAAKoK,IAAL,CAAU,eAAV,EAA2B7G,KAA3B,EAAkC6F,OAAlC,EAA2C5F,GAA3C;AACA,aAAK4G,IAAL,CAAU,kBAAV,EAA8B7G,KAA9B;AACD;;AAED,UAAI,KAAKY,OAAL,CAAazC,gBAAjB,EAAmC;AACjC,eAAO,KAAKwR,YAAL,EAAP;AACD;AACF;;;6BAEe;AACd,aAAO,uCAAuC5H,OAAvC,CAA+C,OAA/C,EAAwD,UAAS0O,CAAT,EAAY;AACzE,YAAIC,CAAC,GAAGjU,IAAI,CAACkU,MAAL,KAAgB,EAAhB,GAAqB,CAA7B;AAAA,YAAgCC,CAAC,GAAGH,CAAC,KAAK,GAAN,GAAYC,CAAZ,GAAiBA,CAAC,GAAG,GAAJ,GAAU,GAA/D;AACA,eAAOE,CAAC,CAACC,QAAF,CAAW,EAAX,CAAP;AACD,OAHM,CAAP;AAID;;;;EA3oEoB1b,O;;AA6oEvBa,QAAQ,CAAC8a,SAAT;AAGA9a,QAAQ,CAAC8L,OAAT,GAAmB,OAAnB,C,CAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA9L,QAAQ,CAAC4E,OAAT,GAAmB,EAAnB,C,CAGA;;AACA5E,QAAQ,CAACwM,iBAAT,GAA6B,UAAUnH,OAAV,EAAmB;AAC9C;AACA,MAAIA,OAAO,CAACuH,YAAR,CAAqB,IAArB,CAAJ,EAAgC;AAC9B,WAAO5M,QAAQ,CAAC4E,OAAT,CAAiBmW,QAAQ,CAAC1V,OAAO,CAACuH,YAAR,CAAqB,IAArB,CAAD,CAAzB,CAAP;AACD,GAFD,MAEO;AACL,WAAO2C,SAAP;AACD;AACF,CAPD,C,CAUA;;;AACAvP,QAAQ,CAACsM,SAAT,GAAqB,EAArB,C,CAEA;;AACAtM,QAAQ,CAACgb,UAAT,GAAsB,UAAU3V,OAAV,EAAmB;AACvC,MAAI,OAAOA,OAAP,KAAmB,QAAvB,EAAiC;AAC/BA,IAAAA,OAAO,GAAG6G,QAAQ,CAACC,aAAT,CAAuB9G,OAAvB,CAAV;AACD;;AACD,MAAI,CAACA,OAAO,IAAI,IAAX,GAAkBA,OAAO,CAACgH,QAA1B,GAAqCkD,SAAtC,KAAoD,IAAxD,EAA8D;AAC5D,UAAM,IAAI3I,KAAJ,CAAU,gNAAV,CAAN;AACD;;AACD,SAAOvB,OAAO,CAACgH,QAAf;AACD,CARD,C,CAWA;;;AACArM,QAAQ,CAACib,YAAT,GAAwB,IAAxB,C,CAEA;;AACAjb,QAAQ,CAACkb,QAAT,GAAoB,YAAY;AAC9B,MAAIC,SAAJ;;AACA,MAAIjP,QAAQ,CAAC/D,gBAAb,EAA+B;AAC7BgT,IAAAA,SAAS,GAAGjP,QAAQ,CAAC/D,gBAAT,CAA0B,WAA1B,CAAZ;AACD,GAFD,MAEO;AACLgT,IAAAA,SAAS,GAAG,EAAZ,CADK,CAEL;;AACA,QAAIC,aAAa,GAAG,SAAhBA,aAAgB,CAAA9K,QAAQ;AAAA,aACvB,YAAM;AACL,YAAIE,MAAM,GAAG,EAAb;AADK;AAAA;AAAA;;AAAA;AAEL,iCAAeF,QAAf,wIAAyB;AAAA,gBAAhB1E,EAAgB;;AACvB,gBAAI,qBAAqBnG,IAArB,CAA0BmG,EAAE,CAACtG,SAA7B,CAAJ,EAA6C;AAC3CkL,cAAAA,MAAM,CAACjR,IAAP,CAAY4b,SAAS,CAAC5b,IAAV,CAAeqM,EAAf,CAAZ;AACD,aAFD,MAEO;AACL4E,cAAAA,MAAM,CAACjR,IAAP,CAAYgQ,SAAZ;AACD;AACF;AARI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AASL,eAAOiB,MAAP;AACD,OAVD,EADwB;AAAA,KAA5B;;AAaA4K,IAAAA,aAAa,CAAClP,QAAQ,CAAC3G,oBAAT,CAA8B,KAA9B,CAAD,CAAb;AACA6V,IAAAA,aAAa,CAAClP,QAAQ,CAAC3G,oBAAT,CAA8B,MAA9B,CAAD,CAAb;AACD;;AAED,SAAQ,YAAM;AACZ,QAAIiL,MAAM,GAAG,EAAb;AADY;AAAA;AAAA;;AAAA;AAEZ,6BAAqB2K,SAArB,wIAAgC;AAAA,YAAvB9O,QAAuB;;AAC9B;AACA,YAAIrM,QAAQ,CAACwM,iBAAT,CAA2BH,QAA3B,MAAyC,KAA7C,EAAoD;AAClDmE,UAAAA,MAAM,CAACjR,IAAP,CAAY,IAAIS,QAAJ,CAAaqM,QAAb,CAAZ;AACD,SAFD,MAEO;AACLmE,UAAAA,MAAM,CAACjR,IAAP,CAAYgQ,SAAZ;AACD;AACF;AATW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAUZ,WAAOiB,MAAP;AACD,GAXM,EAAP;AAYD,CApCD,C,CAuCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAxQ,QAAQ,CAACqb,mBAAT,GAA+B,CAC7B;AACA,gDAF6B,CAA/B,C,CAMA;;AACArb,QAAQ,CAAC0M,kBAAT,GAA8B,YAAY;AACxC,MAAI4O,cAAc,GAAG,IAArB;;AAEA,MAAIrN,MAAM,CAACsN,IAAP,IAAetN,MAAM,CAAC0G,UAAtB,IAAoC1G,MAAM,CAACuN,QAA3C,IAAuDvN,MAAM,CAACwN,IAA9D,IAAsExN,MAAM,CAAC2K,QAA7E,IAAyF1M,QAAQ,CAACC,aAAtG,EAAqH;AACnH,QAAI,EAAE,eAAeD,QAAQ,CAACxG,aAAT,CAAuB,GAAvB,CAAjB,CAAJ,EAAmD;AACjD4V,MAAAA,cAAc,GAAG,KAAjB;AACD,KAFD,MAEO;AACL;AADK;AAAA;AAAA;;AAAA;AAEL,+BAAkBtb,QAAQ,CAACqb,mBAA3B,wIAAgD;AAAA,cAAvCK,KAAuC;;AAC9C,cAAIA,KAAK,CAACjW,IAAN,CAAWkW,SAAS,CAACC,SAArB,CAAJ,EAAqC;AACnCN,YAAAA,cAAc,GAAG,KAAjB;AACA;AACD;AACF;AAPI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQN;AACF,GAZD,MAYO;AACLA,IAAAA,cAAc,GAAG,KAAjB;AACD;;AAED,SAAOA,cAAP;AACD,CApBD;;AAsBAtb,QAAQ,CAACwU,aAAT,GAAyB,UAAUqH,OAAV,EAAmB;AAC1C;AACA;AACA,MAAIC,UAAU,GAAGC,IAAI,CAACF,OAAO,CAACG,KAAR,CAAc,GAAd,EAAmB,CAAnB,CAAD,CAArB,CAH0C,CAK1C;;AACA,MAAIC,UAAU,GAAGJ,OAAO,CAACG,KAAR,CAAc,GAAd,EAAmB,CAAnB,EAAsBA,KAAtB,CAA4B,GAA5B,EAAiC,CAAjC,EAAoCA,KAApC,CAA0C,GAA1C,EAA+C,CAA/C,CAAjB,CAN0C,CAQ1C;;AACA,MAAIE,EAAE,GAAG,IAAIC,WAAJ,CAAgBL,UAAU,CAACjc,MAA3B,CAAT;AACA,MAAIuc,EAAE,GAAG,IAAIC,UAAJ,CAAeH,EAAf,CAAT;;AACA,OAAK,IAAIpc,CAAC,GAAG,CAAR,EAAWsX,GAAG,GAAG0E,UAAU,CAACjc,MAA5B,EAAoCyc,GAAG,GAAG,KAAKlF,GAApD,EAAyDkF,GAAG,GAAGxc,CAAC,IAAIsX,GAAR,GAActX,CAAC,IAAIsX,GAA/E,EAAoFkF,GAAG,GAAGxc,CAAC,EAAJ,GAASA,CAAC,EAAjG,EAAqG;AACnGsc,IAAAA,EAAE,CAACtc,CAAD,CAAF,GAAQgc,UAAU,CAACS,UAAX,CAAsBzc,CAAtB,CAAR;AACD,GAbyC,CAe1C;;;AACA,SAAO,IAAI2b,IAAJ,CAAS,CAACS,EAAD,CAAT,EAAe;AAAClV,IAAAA,IAAI,EAAEiV;AAAP,GAAf,CAAP;AACD,CAjBD,C,CAmBA;;;AACA,IAAMlI,OAAO,GAAG,SAAVA,OAAU,CAACyI,IAAD,EAAOC,YAAP;AAAA,SAAwBD,IAAI,CAACvP,MAAL,CAAY,UAAC6E,IAAD;AAAA,WAAUA,IAAI,KAAK2K,YAAnB;AAAA,GAAZ,EAA6CtP,GAA7C,CAAiD,UAAC2E,IAAD;AAAA,WAAUA,IAAV;AAAA,GAAjD,CAAxB;AAAA,CAAhB,C,CAEA;;;AACA,IAAMiJ,QAAQ,GAAG,SAAXA,QAAW,CAAA2B,GAAG;AAAA,SAAIA,GAAG,CAAC3Q,OAAJ,CAAY,YAAZ,EAA0B,UAAA9E,KAAK;AAAA,WAAIA,KAAK,CAAC0V,MAAN,CAAa,CAAb,EAAgB9P,WAAhB,EAAJ;AAAA,GAA/B,CAAJ;AAAA,CAApB,C,CAEA;;;AACA7M,QAAQ,CAAC0F,aAAT,GAAyB,UAAUkX,MAAV,EAAkB;AACzC,MAAIC,GAAG,GAAG3Q,QAAQ,CAACxG,aAAT,CAAuB,KAAvB,CAAV;AACAmX,EAAAA,GAAG,CAACvU,SAAJ,GAAgBsU,MAAhB;AACA,SAAOC,GAAG,CAACC,UAAJ,CAAe,CAAf,CAAP;AACD,CAJD,C,CAMA;;;AACA9c,QAAQ,CAACkP,aAAT,GAAyB,UAAU7J,OAAV,EAAmB0X,SAAnB,EAA8B;AACrD,MAAI1X,OAAO,KAAK0X,SAAhB,EAA2B;AACzB,WAAO,IAAP;AACD,GAHoD,CAGnD;;;AACF,SAAQ1X,OAAO,GAAGA,OAAO,CAAC8D,UAA1B,EAAuC;AACrC,QAAI9D,OAAO,KAAK0X,SAAhB,EAA2B;AACzB,aAAO,IAAP;AACD;AACF;;AACD,SAAO,KAAP;AACD,CAVD;;AAaA/c,QAAQ,CAAC+M,UAAT,GAAsB,UAAUnB,EAAV,EAAcvD,IAAd,EAAoB;AACxC,MAAIhD,OAAJ;;AACA,MAAI,OAAOuG,EAAP,KAAc,QAAlB,EAA4B;AAC1BvG,IAAAA,OAAO,GAAG6G,QAAQ,CAACC,aAAT,CAAuBP,EAAvB,CAAV;AACD,GAFD,MAEO,IAAIA,EAAE,CAACQ,QAAH,IAAe,IAAnB,EAAyB;AAC9B/G,IAAAA,OAAO,GAAGuG,EAAV;AACD;;AACD,MAAIvG,OAAO,IAAI,IAAf,EAAqB;AACnB,UAAM,IAAIuB,KAAJ,oBAAuByB,IAAvB,+EAAN;AACD;;AACD,SAAOhD,OAAP;AACD,CAXD;;AAcArF,QAAQ,CAACgN,WAAT,GAAuB,UAAUgQ,GAAV,EAAe3U,IAAf,EAAqB;AAC1C,MAAIuD,EAAJ,EAAQ0E,QAAR;;AACA,MAAI0M,GAAG,YAAYC,KAAnB,EAA0B;AACxB3M,IAAAA,QAAQ,GAAG,EAAX;;AACA,QAAI;AAAA;AAAA;AAAA;;AAAA;AACF,+BAAW0M,GAAX,wIAAgB;AAAXpR,UAAAA,EAAW;AACd0E,UAAAA,QAAQ,CAAC/Q,IAAT,CAAc,KAAKwN,UAAL,CAAgBnB,EAAhB,EAAoBvD,IAApB,CAAd;AACD;AAHC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIH,KAJD,CAIE,OAAOhB,CAAP,EAAU;AACViJ,MAAAA,QAAQ,GAAG,IAAX;AACD;AACF,GATD,MASO,IAAI,OAAO0M,GAAP,KAAe,QAAnB,EAA6B;AAClC1M,IAAAA,QAAQ,GAAG,EAAX;AADkC;AAAA;AAAA;;AAAA;AAElC,6BAAWpE,QAAQ,CAAC/D,gBAAT,CAA0B6U,GAA1B,CAAX,wIAA2C;AAAtCpR,QAAAA,EAAsC;AACzC0E,QAAAA,QAAQ,CAAC/Q,IAAT,CAAcqM,EAAd;AACD;AAJiC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKnC,GALM,MAKA,IAAIoR,GAAG,CAAC5Q,QAAJ,IAAgB,IAApB,EAA0B;AAC/BkE,IAAAA,QAAQ,GAAG,CAAC0M,GAAD,CAAX;AACD;;AAED,MAAK1M,QAAQ,IAAI,IAAb,IAAsB,CAACA,QAAQ,CAACzQ,MAApC,EAA4C;AAC1C,UAAM,IAAI+G,KAAJ,oBAAuByB,IAAvB,gGAAN;AACD;;AAED,SAAOiI,QAAP;AACD,CAzBD,C,CA2BA;AACA;AACA;AACA;;;AACAtQ,QAAQ,CAAC8I,OAAT,GAAmB,UAAUoU,QAAV,EAAoBhQ,QAApB,EAA8BiQ,QAA9B,EAAwC;AACzD,MAAIlP,MAAM,CAACnF,OAAP,CAAeoU,QAAf,CAAJ,EAA8B;AAC5B,WAAOhQ,QAAQ,EAAf;AACD,GAFD,MAEO,IAAIiQ,QAAQ,IAAI,IAAhB,EAAsB;AAC3B,WAAOA,QAAQ,EAAf;AACD;AACF,CAND,C,CAQA;AACA;AACA;;;AACAnd,QAAQ,CAACkT,WAAT,GAAuB,UAAU9O,IAAV,EAAgBnC,aAAhB,EAA+B;AACpD,MAAI,CAACA,aAAL,EAAoB;AAClB,WAAO,IAAP;AACD,GAHmD,CAGlD;;;AACFA,EAAAA,aAAa,GAAGA,aAAa,CAAC+Z,KAAd,CAAoB,GAApB,CAAhB;AAEA,MAAIoB,QAAQ,GAAGhZ,IAAI,CAAC4C,IAApB;AACA,MAAIqW,YAAY,GAAGD,QAAQ,CAACrR,OAAT,CAAiB,OAAjB,EAA0B,EAA1B,CAAnB;AAPoD;AAAA;AAAA;;AAAA;AASpD,2BAAsB9J,aAAtB,wIAAqC;AAAA,UAA5Bqb,SAA4B;AACnCA,MAAAA,SAAS,GAAGA,SAAS,CAACpV,IAAV,EAAZ;;AACA,UAAIoV,SAAS,CAACX,MAAV,CAAiB,CAAjB,MAAwB,GAA5B,EAAiC;AAC/B,YAAIvY,IAAI,CAACiE,IAAL,CAAUiR,WAAV,GAAwB9J,OAAxB,CAAgC8N,SAAS,CAAChE,WAAV,EAAhC,EAAyDlV,IAAI,CAACiE,IAAL,CAAUxI,MAAV,GAAmByd,SAAS,CAACzd,MAAtF,MAAkG,CAAC,CAAvG,EAA0G;AACxG,iBAAO,IAAP;AACD;AACF,OAJD,MAIO,IAAI,QAAQ4F,IAAR,CAAa6X,SAAb,CAAJ,EAA6B;AAClC;AACA,YAAID,YAAY,KAAKC,SAAS,CAACvR,OAAV,CAAkB,OAAlB,EAA2B,EAA3B,CAArB,EAAqD;AACnD,iBAAO,IAAP;AACD;AACF,OALM,MAKA;AACL,YAAIqR,QAAQ,KAAKE,SAAjB,EAA4B;AAC1B,iBAAO,IAAP;AACD;AACF;AACF;AAzBmD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AA2BpD,SAAO,KAAP;AACD,CA5BD,C,CA8BA;;;AACA,IAAI,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,KAAK,IAAhD,EAAsD;AACpDA,EAAAA,MAAM,CAACle,EAAP,CAAUgN,QAAV,GAAqB,UAAUzH,OAAV,EAAmB;AACtC,WAAO,KAAK4Y,IAAL,CAAU,YAAY;AAC3B,aAAO,IAAIxd,QAAJ,CAAa,IAAb,EAAmB4E,OAAnB,CAAP;AACD,KAFM,CAAP;AAGD,GAJD;AAKD;;AAGD,IAAI,OAAO6Y,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,KAAK,IAAhD,EAAsD;AACpDA,EAAAA,MAAM,CAACC,OAAP,GAAiB1d,QAAjB;AACD,CAFD,MAEO;AACLiO,EAAAA,MAAM,CAACjO,QAAP,GAAkBA,QAAlB;AACD,C,CAGD;;;AACAA,QAAQ,CAACsN,KAAT,GAAiB,OAAjB;AAEAtN,QAAQ,CAACqN,MAAT,GAAkB,QAAlB,C,CACA;AACA;;AACArN,QAAQ,CAAC2d,QAAT,GAAoB3d,QAAQ,CAACqN,MAA7B;AAEArN,QAAQ,CAAC6I,SAAT,GAAqB,WAArB;AACA7I,QAAQ,CAAC4d,UAAT,GAAsB5d,QAAQ,CAAC6I,SAA/B,C,CAA0C;;AAE1C7I,QAAQ,CAACyW,QAAT,GAAoB,UAApB;AACAzW,QAAQ,CAACwa,KAAT,GAAiB,OAAjB;AACAxa,QAAQ,CAAC4X,OAAT,GAAmB,SAAnB;AAGA;;;;;;;AAQA;AACA;AACA;;AACA,IAAIiG,oBAAoB,GAAG,SAAvBA,oBAAuB,CAAU7I,GAAV,EAAe;AACxC,MAAI8I,EAAE,GAAG9I,GAAG,CAAC+I,YAAb;AACA,MAAIC,EAAE,GAAGhJ,GAAG,CAACiJ,aAAb;AACA,MAAI/J,MAAM,GAAGhI,QAAQ,CAACxG,aAAT,CAAuB,QAAvB,CAAb;AACAwO,EAAAA,MAAM,CAACjO,KAAP,GAAe,CAAf;AACAiO,EAAAA,MAAM,CAAChO,MAAP,GAAgB8X,EAAhB;AACA,MAAIzI,GAAG,GAAGrB,MAAM,CAACsB,UAAP,CAAkB,IAAlB,CAAV;AACAD,EAAAA,GAAG,CAAC2I,SAAJ,CAAclJ,GAAd,EAAmB,CAAnB,EAAsB,CAAtB;;AAPwC,0BAQ3BO,GAAG,CAAC4I,YAAJ,CAAiB,CAAjB,EAAoB,CAApB,EAAuB,CAAvB,EAA0BH,EAA1B,CAR2B;AAAA,MAQnC1G,IARmC,qBAQnCA,IARmC,EAWxC;;;AACA,MAAI8G,EAAE,GAAG,CAAT;AACA,MAAIC,EAAE,GAAGL,EAAT;AACA,MAAIM,EAAE,GAAGN,EAAT;;AACA,SAAOM,EAAE,GAAGF,EAAZ,EAAgB;AACd,QAAIG,KAAK,GAAGjH,IAAI,CAAE,CAACgH,EAAE,GAAG,CAAN,IAAW,CAAZ,GAAiB,CAAlB,CAAhB;;AAEA,QAAIC,KAAK,KAAK,CAAd,EAAiB;AACfF,MAAAA,EAAE,GAAGC,EAAL;AACD,KAFD,MAEO;AACLF,MAAAA,EAAE,GAAGE,EAAL;AACD;;AAEDA,IAAAA,EAAE,GAAID,EAAE,GAAGD,EAAN,IAAa,CAAlB;AACD;;AACD,MAAII,KAAK,GAAIF,EAAE,GAAGN,EAAlB;;AAEA,MAAIQ,KAAK,KAAK,CAAd,EAAiB;AACf,WAAO,CAAP;AACD,GAFD,MAEO;AACL,WAAOA,KAAP;AACD;AACF,CAjCD,C,CAmCA;AACA;;;AACA,IAAI3I,eAAe,GAAG,SAAlBA,eAAkB,CAAUN,GAAV,EAAeP,GAAf,EAAoByJ,EAApB,EAAwBL,EAAxB,EAA4BM,EAA5B,EAAgCC,EAAhC,EAAoCC,EAApC,EAAwCC,EAAxC,EAA4CC,EAA5C,EAAgDC,EAAhD,EAAoD;AACxE,MAAIC,eAAe,GAAGnB,oBAAoB,CAAC7I,GAAD,CAA1C;AACA,SAAOO,GAAG,CAAC2I,SAAJ,CAAclJ,GAAd,EAAmByJ,EAAnB,EAAuBL,EAAvB,EAA2BM,EAA3B,EAA+BC,EAA/B,EAAmCC,EAAnC,EAAuCC,EAAvC,EAA2CC,EAA3C,EAA+CC,EAAE,GAAGC,eAApD,CAAP;AACD,CAHD,C,CAMA;AACA;AACA;;;IACM3K,W;;;;;;;;;gCACe;AACjB,WAAK4K,OAAL,GAAe,mEAAf;AACD;;;6BAEe9F,K,EAAO;AACrB,UAAI+F,MAAM,GAAG,EAAb;AACA,UAAIC,IAAI,GAAG5P,SAAX;AACA,UAAI6P,IAAI,GAAG7P,SAAX;AACA,UAAI8P,IAAI,GAAG,EAAX;AACA,UAAIC,IAAI,GAAG/P,SAAX;AACA,UAAIgQ,IAAI,GAAGhQ,SAAX;AACA,UAAIiQ,IAAI,GAAGjQ,SAAX;AACA,UAAIkQ,IAAI,GAAG,EAAX;AACA,UAAI3f,CAAC,GAAG,CAAR;;AACA,aAAO,IAAP,EAAa;AACXqf,QAAAA,IAAI,GAAGhG,KAAK,CAACrZ,CAAC,EAAF,CAAZ;AACAsf,QAAAA,IAAI,GAAGjG,KAAK,CAACrZ,CAAC,EAAF,CAAZ;AACAuf,QAAAA,IAAI,GAAGlG,KAAK,CAACrZ,CAAC,EAAF,CAAZ;AACAwf,QAAAA,IAAI,GAAGH,IAAI,IAAI,CAAf;AACAI,QAAAA,IAAI,GAAI,CAACJ,IAAI,GAAG,CAAR,KAAc,CAAf,GAAqBC,IAAI,IAAI,CAApC;AACAI,QAAAA,IAAI,GAAI,CAACJ,IAAI,GAAG,EAAR,KAAe,CAAhB,GAAsBC,IAAI,IAAI,CAArC;AACAI,QAAAA,IAAI,GAAGJ,IAAI,GAAG,EAAd;;AACA,YAAIK,KAAK,CAACN,IAAD,CAAT,EAAiB;AACfI,UAAAA,IAAI,GAAIC,IAAI,GAAG,EAAf;AACD,SAFD,MAEO,IAAIC,KAAK,CAACL,IAAD,CAAT,EAAiB;AACtBI,UAAAA,IAAI,GAAG,EAAP;AACD;;AACDP,QAAAA,MAAM,GAAGA,MAAM,GAAG,KAAKD,OAAL,CAAatC,MAAb,CAAoB2C,IAApB,CAAT,GAAqC,KAAKL,OAAL,CAAatC,MAAb,CAAoB4C,IAApB,CAArC,GAAiE,KAAKN,OAAL,CAAatC,MAAb,CAAoB6C,IAApB,CAAjE,GAA6F,KAAKP,OAAL,CAAatC,MAAb,CAAoB8C,IAApB,CAAtG;AACAN,QAAAA,IAAI,GAAIC,IAAI,GAAIC,IAAI,GAAG,EAAvB;AACAC,QAAAA,IAAI,GAAIC,IAAI,GAAIC,IAAI,GAAIC,IAAI,GAAG,EAA/B;;AACA,YAAI,EAAE3f,CAAC,GAAGqZ,KAAK,CAACtZ,MAAZ,CAAJ,EAAyB;AACvB;AACD;AACF;;AACD,aAAOqf,MAAP;AACD;;;4BAEcS,c,EAAgBC,iB,EAAmB;AAChD,UAAI,CAACD,cAAc,CAAC1Y,KAAf,CAAqB,yBAArB,CAAL,EAAsD;AACpD,eAAO2Y,iBAAP;AACD;;AACD,UAAIC,QAAQ,GAAG,KAAKC,QAAL,CAAcH,cAAc,CAAC5T,OAAf,CAAuB,yBAAvB,EAAkD,EAAlD,CAAd,CAAf;AACA,UAAIgU,QAAQ,GAAG,KAAKC,cAAL,CAAoBH,QAApB,CAAf;AACA,UAAII,KAAK,GAAG,KAAKC,gBAAL,CAAsBN,iBAAtB,EAAyCG,QAAzC,CAAZ;AACA,8CAAiC,KAAKI,QAAL,CAAcF,KAAd,CAAjC;AACD;;;qCAEuBL,iB,EAAmBG,Q,EAAU;AACnD,UAAIK,SAAS,GAAG,KAAKC,YAAL,CAAkBN,QAAlB,CAAhB;AACA,UAAIO,aAAa,GAAG,KAAKC,UAAL,CAAgBX,iBAAhB,EAAmCQ,SAAnC,CAApB;AACA,UAAII,OAAO,GAAG,IAAInE,UAAJ,CAAeiE,aAAf,CAAd;AACA,aAAOE,OAAP;AACD;;;iCAEmBT,Q,EAAU;AAC5B,UAAIU,GAAG,GAAGlR,SAAV;AACA,UAAIsC,CAAC,GAAG,CAAR;;AACA,aAAOA,CAAC,GAAGkO,QAAQ,CAAClgB,MAApB,EAA4B;AAC1B4gB,QAAAA,GAAG,GAAGV,QAAQ,CAAClO,CAAD,CAAd;;AACA,YAAK4O,GAAG,CAAC,CAAD,CAAH,KAAW,GAAZ,GAAoBA,GAAG,CAAC,CAAD,CAAH,KAAW,GAAnC,EAAyC;AACvC,iBAAOA,GAAP;AACD;;AACD5O,QAAAA,CAAC;AACF;;AACD,aAAO,EAAP;AACD;;;+BAEiB+N,iB,EAAmBQ,S,EAAW;AAC9C,UAAIM,SAAS,GAAGd,iBAAiB,CAAC7T,OAAlB,CAA0B,yBAA1B,EAAqD,EAArD,CAAhB;AACA,UAAI4U,GAAG,GAAG,KAAKb,QAAL,CAAcY,SAAd,CAAV;AACA,UAAIE,aAAa,GAAGD,GAAG,CAACnR,OAAJ,CAAY,GAAZ,EAAiB,CAAjB,CAApB;AACA,UAAIqR,GAAG,GAAGF,GAAG,CAAC1M,KAAJ,CAAU,CAAV,EAAa2M,aAAb,CAAV;AACA,UAAIE,GAAG,GAAGH,GAAG,CAAC1M,KAAJ,CAAU2M,aAAV,CAAV;AACA,UAAIG,KAAK,GAAGF,GAAZ;AACAE,MAAAA,KAAK,GAAGA,KAAK,CAACC,MAAN,CAAaZ,SAAb,CAAR;AACAW,MAAAA,KAAK,GAAGA,KAAK,CAACC,MAAN,CAAaF,GAAb,CAAR;AACA,aAAOC,KAAP;AACD;;;mCAEqBE,a,EAAe;AACnC,UAAIC,IAAI,GAAG,CAAX;AACA,UAAInB,QAAQ,GAAG,EAAf;;AACA,aAAO,IAAP,EAAa;AACX,YAAIlgB,MAAJ;;AACA,YAAKohB,aAAa,CAACC,IAAD,CAAb,KAAwB,GAAzB,GAAiCD,aAAa,CAACC,IAAI,GAAG,CAAR,CAAb,KAA4B,GAAjE,EAAuE;AACrE;AACD;;AACD,YAAKD,aAAa,CAACC,IAAD,CAAb,KAAwB,GAAzB,GAAiCD,aAAa,CAACC,IAAI,GAAG,CAAR,CAAb,KAA4B,GAAjE,EAAuE;AACrEA,UAAAA,IAAI,IAAI,CAAR;AACD,SAFD,MAEO;AACLrhB,UAAAA,MAAM,GAAIohB,aAAa,CAACC,IAAI,GAAG,CAAR,CAAb,GAA0B,GAA3B,GAAkCD,aAAa,CAACC,IAAI,GAAG,CAAR,CAAxD;AACA,cAAIC,QAAQ,GAAGD,IAAI,GAAGrhB,MAAP,GAAgB,CAA/B;AACA,cAAI4gB,GAAG,GAAGQ,aAAa,CAAChN,KAAd,CAAoBiN,IAApB,EAA0BC,QAA1B,CAAV;AACApB,UAAAA,QAAQ,CAACxgB,IAAT,CAAckhB,GAAd;AACAS,UAAAA,IAAI,GAAGC,QAAP;AACD;;AACD,YAAID,IAAI,GAAGD,aAAa,CAACphB,MAAzB,EAAiC;AAC/B;AACD;AACF;;AACD,aAAOkgB,QAAP;AACD;;;6BAEe5G,K,EAAO;AACrB,UAAI+F,MAAM,GAAG,EAAb;AACA,UAAIC,IAAI,GAAG5P,SAAX;AACA,UAAI6P,IAAI,GAAG7P,SAAX;AACA,UAAI8P,IAAI,GAAG,EAAX;AACA,UAAIC,IAAI,GAAG/P,SAAX;AACA,UAAIgQ,IAAI,GAAGhQ,SAAX;AACA,UAAIiQ,IAAI,GAAGjQ,SAAX;AACA,UAAIkQ,IAAI,GAAG,EAAX;AACA,UAAI3f,CAAC,GAAG,CAAR;AACA,UAAI6gB,GAAG,GAAG,EAAV,CAVqB,CAWrB;;AACA,UAAIS,UAAU,GAAG,qBAAjB;;AACA,UAAIA,UAAU,CAACC,IAAX,CAAgBlI,KAAhB,CAAJ,EAA4B;AAC1BxG,QAAAA,OAAO,CAAC2H,IAAR,CAAa,wJAAb;AACD;;AACDnB,MAAAA,KAAK,GAAGA,KAAK,CAACpN,OAAN,CAAc,qBAAd,EAAqC,EAArC,CAAR;;AACA,aAAO,IAAP,EAAa;AACXuT,QAAAA,IAAI,GAAG,KAAKL,OAAL,CAAazP,OAAb,CAAqB2J,KAAK,CAACwD,MAAN,CAAa7c,CAAC,EAAd,CAArB,CAAP;AACAyf,QAAAA,IAAI,GAAG,KAAKN,OAAL,CAAazP,OAAb,CAAqB2J,KAAK,CAACwD,MAAN,CAAa7c,CAAC,EAAd,CAArB,CAAP;AACA0f,QAAAA,IAAI,GAAG,KAAKP,OAAL,CAAazP,OAAb,CAAqB2J,KAAK,CAACwD,MAAN,CAAa7c,CAAC,EAAd,CAArB,CAAP;AACA2f,QAAAA,IAAI,GAAG,KAAKR,OAAL,CAAazP,OAAb,CAAqB2J,KAAK,CAACwD,MAAN,CAAa7c,CAAC,EAAd,CAArB,CAAP;AACAqf,QAAAA,IAAI,GAAIG,IAAI,IAAI,CAAT,GAAeC,IAAI,IAAI,CAA9B;AACAH,QAAAA,IAAI,GAAI,CAACG,IAAI,GAAG,EAAR,KAAe,CAAhB,GAAsBC,IAAI,IAAI,CAArC;AACAH,QAAAA,IAAI,GAAI,CAACG,IAAI,GAAG,CAAR,KAAc,CAAf,GAAoBC,IAA3B;AACAkB,QAAAA,GAAG,CAACphB,IAAJ,CAAS4f,IAAT;;AACA,YAAIK,IAAI,KAAK,EAAb,EAAiB;AACfmB,UAAAA,GAAG,CAACphB,IAAJ,CAAS6f,IAAT;AACD;;AACD,YAAIK,IAAI,KAAK,EAAb,EAAiB;AACfkB,UAAAA,GAAG,CAACphB,IAAJ,CAAS8f,IAAT;AACD;;AACDF,QAAAA,IAAI,GAAIC,IAAI,GAAIC,IAAI,GAAG,EAAvB;AACAC,QAAAA,IAAI,GAAIC,IAAI,GAAIC,IAAI,GAAIC,IAAI,GAAG,EAA/B;;AACA,YAAI,EAAE3f,CAAC,GAAGqZ,KAAK,CAACtZ,MAAZ,CAAJ,EAAyB;AACvB;AACD;AACF;;AACD,aAAO8gB,GAAP;AACD;;;;;;AAEHtM,WAAW,CAACyG,SAAZ;AAGA;;;;;;;;;;;;;AAcA;AACA;;AACA,IAAIwG,aAAa,GAAG,SAAhBA,aAAgB,CAAUC,GAAV,EAAeliB,EAAf,EAAmB;AACrC,MAAI4F,IAAI,GAAG,KAAX;AACA,MAAI6I,GAAG,GAAG,IAAV;AACA,MAAI0T,GAAG,GAAGD,GAAG,CAACrV,QAAd;AACA,MAAIuV,IAAI,GAAGD,GAAG,CAACE,eAAf;AACA,MAAI/Z,GAAG,GAAI6Z,GAAG,CAACvY,gBAAJ,GAAuB,kBAAvB,GAA4C,aAAvD;AACA,MAAI0Y,GAAG,GAAIH,GAAG,CAACvY,gBAAJ,GAAuB,qBAAvB,GAA+C,aAA1D;AACA,MAAI2Y,GAAG,GAAIJ,GAAG,CAACvY,gBAAJ,GAAuB,EAAvB,GAA4B,IAAvC;;AACA,MAAInF,IAAI,GAAG,SAAPA,IAAO,CAAUuD,CAAV,EAAa;AACtB,QAAKA,CAAC,CAACL,IAAF,KAAW,kBAAZ,IAAoCwa,GAAG,CAACxH,UAAJ,KAAmB,UAA3D,EAAwE;AACtE;AACD;;AACD,KAAE3S,CAAC,CAACL,IAAF,KAAW,MAAX,GAAoBua,GAApB,GAA0BC,GAA5B,EAAkCG,GAAlC,EAAuCC,GAAG,GAAGva,CAAC,CAACL,IAA/C,EAAqDlD,IAArD,EAA2D,KAA3D;;AACA,QAAI,CAACmB,IAAD,KAAUA,IAAI,GAAG,IAAjB,CAAJ,EAA4B;AAC1B,aAAO5F,EAAE,CAACsN,IAAH,CAAQ4U,GAAR,EAAala,CAAC,CAACL,IAAF,IAAUK,CAAvB,CAAP;AACD;AACF,GARD;;AAUA,MAAIwa,IAAI,GAAG,SAAPA,IAAO,GAAY;AACrB,QAAI;AACFJ,MAAAA,IAAI,CAACK,QAAL,CAAc,MAAd;AACD,KAFD,CAEE,OAAOza,CAAP,EAAU;AACVsC,MAAAA,UAAU,CAACkY,IAAD,EAAO,EAAP,CAAV;AACA;AACD;;AACD,WAAO/d,IAAI,CAAC,MAAD,CAAX;AACD,GARD;;AAUA,MAAI0d,GAAG,CAACxH,UAAJ,KAAmB,UAAvB,EAAmC;AACjC,QAAIwH,GAAG,CAACO,iBAAJ,IAAyBN,IAAI,CAACK,QAAlC,EAA4C;AAC1C,UAAI;AACFhU,QAAAA,GAAG,GAAG,CAACyT,GAAG,CAACS,YAAX;AACD,OAFD,CAEE,OAAOpY,KAAP,EAAc,CACf;;AACD,UAAIkE,GAAJ,EAAS;AACP+T,QAAAA,IAAI;AACL;AACF;;AACDL,IAAAA,GAAG,CAAC7Z,GAAD,CAAH,CAASia,GAAG,GAAG,kBAAf,EAAmC9d,IAAnC,EAAyC,KAAzC;AACA0d,IAAAA,GAAG,CAAC7Z,GAAD,CAAH,CAASia,GAAG,GAAG,kBAAf,EAAmC9d,IAAnC,EAAyC,KAAzC;AACA,WAAOyd,GAAG,CAAC5Z,GAAD,CAAH,CAASia,GAAG,GAAG,MAAf,EAAuB9d,IAAvB,EAA6B,KAA7B,CAAP;AACD;AACF,CA1CD,C,CA6CA;;;AACA9D,QAAQ,CAACiiB,qBAAT,GAAiC,YAAY;AAC3C,MAAIjiB,QAAQ,CAACib,YAAb,EAA2B;AACzB,WAAOjb,QAAQ,CAACkb,QAAT,EAAP;AACD;AACF,CAJD;;AAKAoG,aAAa,CAACrT,MAAD,EAASjO,QAAQ,CAACiiB,qBAAlB,CAAb;;AAEA,SAAStQ,SAAT,CAAmBtH,KAAnB,EAA0B6X,SAA1B,EAAqC;AACnC,SAAQ,OAAO7X,KAAP,KAAiB,WAAjB,IAAgCA,KAAK,KAAK,IAA3C,GAAmD6X,SAAS,CAAC7X,KAAD,CAA5D,GAAsEkF,SAA7E;AACD;;AACD,SAASmD,eAAT,CAAyByP,GAAzB,EAA8BC,UAA9B,EAA0CF,SAA1C,EAAqD;AACnD,MAAI,OAAOC,GAAP,KAAe,WAAf,IAA8BA,GAAG,KAAK,IAAtC,IAA8C,OAAOA,GAAG,CAACC,UAAD,CAAV,KAA2B,UAA7E,EAAyF;AACvF,WAAOF,SAAS,CAACC,GAAD,EAAMC,UAAN,CAAhB;AACD,GAFD,MAEO;AACL,WAAO7S,SAAP;AACD;AACF", "sourcesContent": ["/*\n *\n * More info at [www.dropzonejs.com](http://www.dropzonejs.com)\n *\n * Copyright (c) 2012, <PERSON><PERSON>\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in\n * all copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n * THE SOFTWARE.\n *\n */\n\n\n// The Emitter class provides the ability to call `.on()` on Dropzone to listen\n// to events.\n// It is strongly based on component's emitter class, and I removed the\n// functionality because of the dependency hell with different frameworks.\nclass Emitter {\n  // Add an event listener for given event\n  on(event, fn) {\n    this._callbacks = this._callbacks || {};\n    // Create namespace for this event\n    if (!this._callbacks[event]) {\n      this._callbacks[event] = [];\n    }\n    this._callbacks[event].push(fn);\n    return this;\n  }\n\n\n  emit(event, ...args) {\n    this._callbacks = this._callbacks || {};\n    let callbacks = this._callbacks[event];\n\n    if (callbacks) {\n      for (let callback of callbacks) {\n        callback.apply(this, args);\n      }\n    }\n\n    return this;\n  }\n\n  // Remove event listener for given event. If fn is not provided, all event\n  // listeners for that event will be removed. If neither is provided, all\n  // event listeners will be removed.\n  off(event, fn) {\n    if (!this._callbacks || (arguments.length === 0)) {\n      this._callbacks = {};\n      return this;\n    }\n\n    // specific event\n    let callbacks = this._callbacks[event];\n    if (!callbacks) {\n      return this;\n    }\n\n    // remove all handlers\n    if (arguments.length === 1) {\n      delete this._callbacks[event];\n      return this;\n    }\n\n    // remove specific handler\n    for (let i = 0; i < callbacks.length; i++) {\n      let callback = callbacks[i];\n      if (callback === fn) {\n        callbacks.splice(i, 1);\n        break;\n      }\n    }\n\n    return this;\n  }\n}\n\nclass Dropzone extends Emitter {\n  static initClass() {\n\n    // Exposing the emitter class, mainly for tests\n    this.prototype.Emitter = Emitter;\n\n    /*\n     This is a list of all available events you can register on a dropzone object.\n\n     You can register an event handler like this:\n\n     dropzone.on(\"dragEnter\", function() { });\n\n     */\n    this.prototype.events = [\n      \"drop\",\n      \"dragstart\",\n      \"dragend\",\n      \"dragenter\",\n      \"dragover\",\n      \"dragleave\",\n      \"addedfile\",\n      \"addedfiles\",\n      \"removedfile\",\n      \"thumbnail\",\n      \"error\",\n      \"errormultiple\",\n      \"processing\",\n      \"processingmultiple\",\n      \"uploadprogress\",\n      \"totaluploadprogress\",\n      \"sending\",\n      \"sendingmultiple\",\n      \"success\",\n      \"successmultiple\",\n      \"canceled\",\n      \"canceledmultiple\",\n      \"complete\",\n      \"completemultiple\",\n      \"reset\",\n      \"maxfilesexceeded\",\n      \"maxfilesreached\",\n      \"queuecomplete\"\n    ];\n\n\n    this.prototype.defaultOptions = {\n      /**\n       * Has to be specified on elements other than form (or when the form\n       * doesn't have an `action` attribute). You can also\n       * provide a function that will be called with `files` and\n       * must return the url (since `v3.12.0`)\n       */\n      url: null,\n\n      /**\n       * Can be changed to `\"put\"` if necessary. You can also provide a function\n       * that will be called with `files` and must return the method (since `v3.12.0`).\n       */\n      method: \"post\",\n\n      /**\n       * Will be set on the XHRequest.\n       */\n      withCredentials: false,\n\n      /**\n       * The timeout for the XHR requests in milliseconds (since `v4.4.0`).\n       */\n      timeout: 30000,\n\n      /**\n       * How many file uploads to process in parallel (See the\n       * Enqueuing file uploads* documentation section for more info)\n       */\n      parallelUploads: 2,\n\n      /**\n       * Whether to send multiple files in one request. If\n       * this it set to true, then the fallback file input element will\n       * have the `multiple` attribute as well. This option will\n       * also trigger additional events (like `processingmultiple`). See the events\n       * documentation section for more information.\n       */\n      uploadMultiple: false,\n\n      /**\n       * Whether you want files to be uploaded in chunks to your server. This can't be\n       * used in combination with `uploadMultiple`.\n       *\n       * See [chunksUploaded](#config-chunksUploaded) for the callback to finalise an upload.\n       */\n      chunking: false,\n\n      /**\n       * If `chunking` is enabled, this defines whether **every** file should be chunked,\n       * even if the file size is below chunkSize. This means, that the additional chunk\n       * form data will be submitted and the `chunksUploaded` callback will be invoked.\n       */\n      forceChunking: false,\n\n      /**\n       * If `chunking` is `true`, then this defines the chunk size in bytes.\n       */\n      chunkSize: 2000000,\n\n      /**\n       * If `true`, the individual chunks of a file are being uploaded simultaneously.\n       */\n      parallelChunkUploads: false,\n\n      /**\n       * Whether a chunk should be retried if it fails.\n       */\n      retryChunks: false,\n\n      /**\n       * If `retryChunks` is true, how many times should it be retried.\n       */\n      retryChunksLimit: 3,\n\n      /**\n       * If not `null` defines how many files this Dropzone handles. If it exceeds,\n       * the event `maxfilesexceeded` will be called. The dropzone element gets the\n       * class `dz-max-files-reached` accordingly so you can provide visual feedback.\n       */\n      maxFilesize: 256,\n\n      /**\n       * The name of the file param that gets transferred.\n       * **NOTE**: If you have the option  `uploadMultiple` set to `true`, then\n       * Dropzone will append `[]` to the name.\n       */\n      paramName: \"file\",\n\n      /**\n       * Whether thumbnails for images should be generated\n       */\n      createImageThumbnails: true,\n\n      /**\n       * In MB. When the filename exceeds this limit, the thumbnail will not be generated.\n       */\n      maxThumbnailFilesize: 10,\n\n      /**\n       * If `null`, the ratio of the image will be used to calculate it.\n       */\n      thumbnailWidth: 120,\n\n      /**\n       * The same as `thumbnailWidth`. If both are null, images will not be resized.\n       */\n      thumbnailHeight: 120,\n\n      /**\n       * How the images should be scaled down in case both, `thumbnailWidth` and `thumbnailHeight` are provided.\n       * Can be either `contain` or `crop`.\n       */\n      thumbnailMethod: 'crop',\n\n      /**\n       * If set, images will be resized to these dimensions before being **uploaded**.\n       * If only one, `resizeWidth` **or** `resizeHeight` is provided, the original aspect\n       * ratio of the file will be preserved.\n       *\n       * The `options.transformFile` function uses these options, so if the `transformFile` function\n       * is overridden, these options don't do anything.\n       */\n      resizeWidth: null,\n\n      /**\n       * See `resizeWidth`.\n       */\n      resizeHeight: null,\n\n      /**\n       * The mime type of the resized image (before it gets uploaded to the server).\n       * If `null` the original mime type will be used. To force jpeg, for example, use `image/jpeg`.\n       * See `resizeWidth` for more information.\n       */\n      resizeMimeType: null,\n\n      /**\n       * The quality of the resized images. See `resizeWidth`.\n       */\n      resizeQuality: 0.8,\n\n      /**\n       * How the images should be scaled down in case both, `resizeWidth` and `resizeHeight` are provided.\n       * Can be either `contain` or `crop`.\n       */\n      resizeMethod: 'contain',\n\n      /**\n       * The base that is used to calculate the filesize. You can change this to\n       * 1024 if you would rather display kibibytes, mebibytes, etc...\n       * 1024 is technically incorrect, because `1024 bytes` are `1 kibibyte` not `1 kilobyte`.\n       * You can change this to `1024` if you don't care about validity.\n       */\n      filesizeBase: 1000,\n\n      /**\n       * Can be used to limit the maximum number of files that will be handled by this Dropzone\n       */\n      maxFiles: null,\n\n      /**\n       * An optional object to send additional headers to the server. Eg:\n       * `{ \"My-Awesome-Header\": \"header value\" }`\n       */\n      headers: null,\n\n      /**\n       * If `true`, the dropzone element itself will be clickable, if `false`\n       * nothing will be clickable.\n       *\n       * You can also pass an HTML element, a CSS selector (for multiple elements)\n       * or an array of those. In that case, all of those elements will trigger an\n       * upload when clicked.\n       */\n      clickable: true,\n\n      /**\n       * Whether hidden files in directories should be ignored.\n       */\n      ignoreHiddenFiles: true,\n\n\n      /**\n       * The default implementation of `accept` checks the file's mime type or\n       * extension against this list. This is a comma separated list of mime\n       * types or file extensions.\n       *\n       * Eg.: `image/*,application/pdf,.psd`\n       *\n       * If the Dropzone is `clickable` this option will also be used as\n       * [`accept`](https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept)\n       * parameter on the hidden file input as well.\n       */\n      acceptedFiles: null,\n\n      /**\n       * **Deprecated!**\n       * Use acceptedFiles instead.\n       */\n      acceptedMimeTypes: null,\n\n      /**\n       * If false, files will be added to the queue but the queue will not be\n       * processed automatically.\n       * This can be useful if you need some additional user input before sending\n       * files (or if you want want all files sent at once).\n       * If you're ready to send the file simply call `myDropzone.processQueue()`.\n       *\n       * See the [enqueuing file uploads](#enqueuing-file-uploads) documentation\n       * section for more information.\n       */\n      autoProcessQueue: true,\n\n      /**\n       * If false, files added to the dropzone will not be queued by default.\n       * You'll have to call `enqueueFile(file)` manually.\n       */\n      autoQueue: true,\n\n      /**\n       * If `true`, this will add a link to every file preview to remove or cancel (if\n       * already uploading) the file. The `dictCancelUpload`, `dictCancelUploadConfirmation`\n       * and `dictRemoveFile` options are used for the wording.\n       */\n      addRemoveLinks: false,\n\n      /**\n       * Defines where to display the file previews – if `null` the\n       * Dropzone element itself is used. Can be a plain `HTMLElement` or a CSS\n       * selector. The element should have the `dropzone-previews` class so\n       * the previews are displayed properly.\n       */\n      previewsContainer: null,\n\n      /**\n       * This is the element the hidden input field (which is used when clicking on the\n       * dropzone to trigger file selection) will be appended to. This might\n       * be important in case you use frameworks to switch the content of your page.\n       *\n       * Can be a selector string, or an element directly.\n       */\n      hiddenInputContainer: \"body\",\n\n      /**\n       * If null, no capture type will be specified\n       * If camera, mobile devices will skip the file selection and choose camera\n       * If microphone, mobile devices will skip the file selection and choose the microphone\n       * If camcorder, mobile devices will skip the file selection and choose the camera in video mode\n       * On apple devices multiple must be set to false.  AcceptedFiles may need to\n       * be set to an appropriate mime type (e.g. \"image/*\", \"audio/*\", or \"video/*\").\n       */\n      capture: null,\n\n      /**\n       * **Deprecated**. Use `renameFile` instead.\n       */\n      renameFilename: null,\n\n      /**\n       * A function that is invoked before the file is uploaded to the server and renames the file.\n       * This function gets the `File` as argument and can use the `file.name`. The actual name of the\n       * file that gets used during the upload can be accessed through `file.upload.filename`.\n       */\n      renameFile: null,\n\n      /**\n       * If `true` the fallback will be forced. This is very useful to test your server\n       * implementations first and make sure that everything works as\n       * expected without dropzone if you experience problems, and to test\n       * how your fallbacks will look.\n       */\n      forceFallback: false,\n\n      /**\n       * The text used before any files are dropped.\n       */\n      dictDefaultMessage: \"Drop files here to upload\",\n\n      /**\n       * The text that replaces the default message text it the browser is not supported.\n       */\n      dictFallbackMessage: \"Your browser does not support drag'n'drop file uploads.\",\n\n      /**\n       * The text that will be added before the fallback form.\n       * If you provide a  fallback element yourself, or if this option is `null` this will\n       * be ignored.\n       */\n      dictFallbackText: \"Please use the fallback form below to upload your files like in the olden days.\",\n\n      /**\n       * If the filesize is too big.\n       * `{{filesize}}` and `{{maxFilesize}}` will be replaced with the respective configuration values.\n       */\n      dictFileTooBig: \"File is too big ({{filesize}}MiB). Max filesize: {{maxFilesize}}MiB.\",\n\n      /**\n       * If the file doesn't match the file type.\n       */\n      dictInvalidFileType: \"You can't upload files of this type.\",\n\n      /**\n       * If the server response was invalid.\n       * `{{statusCode}}` will be replaced with the servers status code.\n       */\n      dictResponseError: \"Server responded with {{statusCode}} code.\",\n\n      /**\n       * If `addRemoveLinks` is true, the text to be used for the cancel upload link.\n       */\n      dictCancelUpload: \"Cancel upload\",\n\n      /**\n       * The text that is displayed if an upload was manually canceled\n       */\n      dictUploadCanceled: \"Upload canceled.\",\n\n      /**\n       * If `addRemoveLinks` is true, the text to be used for confirmation when cancelling upload.\n       */\n      dictCancelUploadConfirmation: \"Are you sure you want to cancel this upload?\",\n\n      /**\n       * If `addRemoveLinks` is true, the text to be used to remove a file.\n       */\n      dictRemoveFile: \"Remove file\",\n\n      /**\n       * If this is not null, then the user will be prompted before removing a file.\n       */\n      dictRemoveFileConfirmation: null,\n\n      /**\n       * Displayed if `maxFiles` is st and exceeded.\n       * The string `{{maxFiles}}` will be replaced by the configuration value.\n       */\n      dictMaxFilesExceeded: \"You can not upload any more files.\",\n\n      /**\n       * Allows you to translate the different units. Starting with `tb` for terabytes and going down to\n       * `b` for bytes.\n       */\n      dictFileSizeUnits: {tb: \"TB\", gb: \"GB\", mb: \"MB\", kb: \"KB\", b: \"b\"},\n      /**\n       * Called when dropzone initialized\n       * You can add event listeners here\n       */\n      init() {},\n\n      /**\n       * Can be an **object** of additional parameters to transfer to the server, **or** a `Function`\n       * that gets invoked with the `files`, `xhr` and, if it's a chunked upload, `chunk` arguments. In case\n       * of a function, this needs to return a map.\n       *\n       * The default implementation does nothing for normal uploads, but adds relevant information for\n       * chunked uploads.\n       *\n       * This is the same as adding hidden input fields in the form element.\n       */\n      params(files, xhr, chunk) {\n        if (chunk) {\n          return {\n            dzuuid: chunk.file.upload.uuid,\n            dzchunkindex: chunk.index,\n            dztotalfilesize: chunk.file.size,\n            dzchunksize: this.options.chunkSize,\n            dztotalchunkcount: chunk.file.upload.totalChunkCount,\n            dzchunkbyteoffset: chunk.index * this.options.chunkSize\n          };\n        }\n      },\n\n      /**\n       * A function that gets a [file](https://developer.mozilla.org/en-US/docs/DOM/File)\n       * and a `done` function as parameters.\n       *\n       * If the done function is invoked without arguments, the file is \"accepted\" and will\n       * be processed. If you pass an error message, the file is rejected, and the error\n       * message will be displayed.\n       * This function will not be called if the file is too big or doesn't match the mime types.\n       */\n      accept(file, done) {\n        return done();\n      },\n\n      /**\n       * The callback that will be invoked when all chunks have been uploaded for a file.\n       * It gets the file for which the chunks have been uploaded as the first parameter,\n       * and the `done` function as second. `done()` needs to be invoked when everything\n       * needed to finish the upload process is done.\n       */\n      chunksUploaded: function(file, done) { done(); },\n\n      /**\n       * Gets called when the browser is not supported.\n       * The default implementation shows the fallback input field and adds\n       * a text.\n       */\n      fallback() {\n        // This code should pass in IE7... :(\n        let messageElement;\n        this.element.className = `${this.element.className} dz-browser-not-supported`;\n\n        for (let child of this.element.getElementsByTagName(\"div\")) {\n          if (/(^| )dz-message($| )/.test(child.className)) {\n            messageElement = child;\n            child.className = \"dz-message\"; // Removes the 'dz-default' class\n            break;\n          }\n        }\n        if (!messageElement) {\n          messageElement = Dropzone.createElement(\"<div class=\\\"dz-message\\\"><span></span></div>\");\n          this.element.appendChild(messageElement);\n        }\n\n        let span = messageElement.getElementsByTagName(\"span\")[0];\n        if (span) {\n          if (span.textContent != null) {\n            span.textContent = this.options.dictFallbackMessage;\n          } else if (span.innerText != null) {\n            span.innerText = this.options.dictFallbackMessage;\n          }\n        }\n\n        return this.element.appendChild(this.getFallbackForm());\n      },\n\n\n      /**\n       * Gets called to calculate the thumbnail dimensions.\n       *\n       * It gets `file`, `width` and `height` (both may be `null`) as parameters and must return an object containing:\n       *\n       *  - `srcWidth` & `srcHeight` (required)\n       *  - `trgWidth` & `trgHeight` (required)\n       *  - `srcX` & `srcY` (optional, default `0`)\n       *  - `trgX` & `trgY` (optional, default `0`)\n       *\n       * Those values are going to be used by `ctx.drawImage()`.\n       */\n      resize(file, width, height, resizeMethod) {\n        let info = {\n          srcX: 0,\n          srcY: 0,\n          srcWidth: file.width,\n          srcHeight: file.height\n        };\n\n        let srcRatio = file.width / file.height;\n\n        // Automatically calculate dimensions if not specified\n        if ((width == null) && (height == null)) {\n          width = info.srcWidth;\n          height = info.srcHeight;\n        } else if ((width == null)) {\n          width = height * srcRatio;\n        } else if ((height == null)) {\n          height = width / srcRatio;\n        }\n\n        // Make sure images aren't upscaled\n        width = Math.min(width, info.srcWidth);\n        height = Math.min(height, info.srcHeight);\n\n        let trgRatio = width / height;\n\n        if ((info.srcWidth > width) || (info.srcHeight > height)) {\n          // Image is bigger and needs rescaling\n          if (resizeMethod === 'crop') {\n            if (srcRatio > trgRatio) {\n              info.srcHeight = file.height;\n              info.srcWidth = info.srcHeight * trgRatio;\n            } else {\n              info.srcWidth = file.width;\n              info.srcHeight = info.srcWidth / trgRatio;\n            }\n          } else if (resizeMethod === 'contain') {\n            // Method 'contain'\n            if (srcRatio > trgRatio) {\n              height = width / srcRatio;\n            } else {\n              width = height * srcRatio;\n            }\n          } else {\n            throw new Error(`Unknown resizeMethod '${resizeMethod}'`);\n          }\n        }\n\n        info.srcX = (file.width - info.srcWidth) / 2;\n        info.srcY = (file.height - info.srcHeight) / 2;\n\n        info.trgWidth = width;\n        info.trgHeight = height;\n\n        return info;\n      },\n\n      /**\n       * Can be used to transform the file (for example, resize an image if necessary).\n       *\n       * The default implementation uses `resizeWidth` and `resizeHeight` (if provided) and resizes\n       * images according to those dimensions.\n       *\n       * Gets the `file` as the first parameter, and a `done()` function as the second, that needs\n       * to be invoked with the file when the transformation is done.\n       */\n      transformFile(file, done) {\n        if ((this.options.resizeWidth || this.options.resizeHeight) && file.type.match(/image.*/)) {\n          return this.resizeImage(file, this.options.resizeWidth, this.options.resizeHeight, this.options.resizeMethod, done);\n        } else {\n          return done(file);\n        }\n      },\n\n\n      /**\n       * A string that contains the template used for each dropped\n       * file. Change it to fulfill your needs but make sure to properly\n       * provide all elements.\n       *\n       * If you want to use an actual HTML element instead of providing a String\n       * as a config option, you could create a div with the id `tpl`,\n       * put the template inside it and provide the element like this:\n       *\n       *     document\n       *       .querySelector('#tpl')\n       *       .innerHTML\n       *\n       */\n      previewTemplate: `\\\n<div class=\"dz-preview dz-file-preview\">\n  <div class=\"dz-image\"><img data-dz-thumbnail /></div>\n  <div class=\"dz-details\">\n    <div class=\"dz-size\"><span data-dz-size></span></div>\n    <div class=\"dz-filename\"><span data-dz-name></span></div>\n  </div>\n  <div class=\"dz-progress\"><span class=\"dz-upload\" data-dz-uploadprogress></span></div>\n  <div class=\"dz-error-message\"><span data-dz-errormessage></span></div>\n  <div class=\"dz-success-mark\">\n    <svg width=\"54px\" height=\"54px\" viewBox=\"0 0 54 54\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" xmlns:sketch=\"http://www.bohemiancoding.com/sketch/ns\">\n      <title>Check</title>\n      <defs></defs>\n      <g id=\"Page-1\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\" sketch:type=\"MSPage\">\n        <path d=\"M23.5,31.8431458 L17.5852419,25.9283877 C16.0248253,24.3679711 13.4910294,24.366835 11.9289322,25.9289322 C10.3700136,27.4878508 10.3665912,30.0234455 11.9283877,31.5852419 L20.4147581,40.0716123 C20.5133999,40.1702541 20.6159315,40.2626649 20.7218615,40.3488435 C22.2835669,41.8725651 24.794234,41.8626202 26.3461564,40.3106978 L43.3106978,23.3461564 C44.8771021,21.7797521 44.8758057,19.2483887 43.3137085,17.6862915 C41.7547899,16.1273729 39.2176035,16.1255422 37.6538436,17.6893022 L23.5,31.8431458 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z\" id=\"Oval-2\" stroke-opacity=\"0.198794158\" stroke=\"#747474\" fill-opacity=\"0.816519475\" fill=\"#FFFFFF\" sketch:type=\"MSShapeGroup\"></path>\n      </g>\n    </svg>\n  </div>\n  <div class=\"dz-error-mark\">\n    <svg width=\"54px\" height=\"54px\" viewBox=\"0 0 54 54\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" xmlns:sketch=\"http://www.bohemiancoding.com/sketch/ns\">\n      <title>Error</title>\n      <defs></defs>\n      <g id=\"Page-1\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\" sketch:type=\"MSPage\">\n        <g id=\"Check-+-Oval-2\" sketch:type=\"MSLayerGroup\" stroke=\"#747474\" stroke-opacity=\"0.198794158\" fill=\"#FFFFFF\" fill-opacity=\"0.816519475\">\n          <path d=\"M32.6568542,29 L38.3106978,23.3461564 C39.8771021,21.7797521 39.8758057,19.2483887 38.3137085,17.6862915 C36.7547899,16.1273729 34.2176035,16.1255422 32.6538436,17.6893022 L27,23.3431458 L21.3461564,17.6893022 C19.7823965,16.1255422 17.2452101,16.1273729 15.6862915,17.6862915 C14.1241943,19.2483887 14.1228979,21.7797521 15.6893022,23.3461564 L21.3431458,29 L15.6893022,34.6538436 C14.1228979,36.2202479 14.1241943,38.7516113 15.6862915,40.3137085 C17.2452101,41.8726271 19.7823965,41.8744578 21.3461564,40.3106978 L27,34.6568542 L32.6538436,40.3106978 C34.2176035,41.8744578 36.7547899,41.8726271 38.3137085,40.3137085 C39.8758057,38.7516113 39.8771021,36.2202479 38.3106978,34.6538436 L32.6568542,29 Z M27,53 C41.3594035,53 53,41.3594035 53,27 C53,12.6405965 41.3594035,1 27,1 C12.6405965,1 1,12.6405965 1,27 C1,41.3594035 12.6405965,53 27,53 Z\" id=\"Oval-2\" sketch:type=\"MSShapeGroup\"></path>\n        </g>\n      </g>\n    </svg>\n  </div>\n</div>\\\n`,\n\n      // END OPTIONS\n      // (Required by the dropzone documentation parser)\n\n\n      /*\n       Those functions register themselves to the events on init and handle all\n       the user interface specific stuff. Overwriting them won't break the upload\n       but can break the way it's displayed.\n       You can overwrite them if you don't like the default behavior. If you just\n       want to add an additional event handler, register it on the dropzone object\n       and don't overwrite those options.\n       */\n\n\n\n\n      // Those are self explanatory and simply concern the DragnDrop.\n      drop(e) {\n        return this.element.classList.remove(\"dz-drag-hover\");\n      },\n      dragstart(e) {\n      },\n      dragend(e) {\n        return this.element.classList.remove(\"dz-drag-hover\");\n      },\n      dragenter(e) {\n        return this.element.classList.add(\"dz-drag-hover\");\n      },\n      dragover(e) {\n        return this.element.classList.add(\"dz-drag-hover\");\n      },\n      dragleave(e) {\n        return this.element.classList.remove(\"dz-drag-hover\");\n      },\n\n      paste(e) {\n      },\n\n      // Called whenever there are no files left in the dropzone anymore, and the\n      // dropzone should be displayed as if in the initial state.\n      reset() {\n        return this.element.classList.remove(\"dz-started\");\n      },\n\n      // Called when a file is added to the queue\n      // Receives `file`\n      addedfile(file) {\n        if (this.element === this.previewsContainer) {\n          this.element.classList.add(\"dz-started\");\n        }\n\n        if (this.previewsContainer) {\n          file.previewElement = Dropzone.createElement(this.options.previewTemplate.trim());\n          file.previewTemplate = file.previewElement; // Backwards compatibility\n\n          this.previewsContainer.appendChild(file.previewElement);\n          for (var node of file.previewElement.querySelectorAll(\"[data-dz-name]\")) {\n            node.textContent = file.name;\n          }\n          for (node of file.previewElement.querySelectorAll(\"[data-dz-size]\")) {\n            node.innerHTML = this.filesize(file.size);\n          }\n\n          if (this.options.addRemoveLinks) {\n            file._removeLink = Dropzone.createElement(`<a class=\"dz-remove\" href=\"javascript:undefined;\" data-dz-remove>${this.options.dictRemoveFile}</a>`);\n            file.previewElement.appendChild(file._removeLink);\n          }\n\n          let removeFileEvent = e => {\n            e.preventDefault();\n            e.stopPropagation();\n            if (file.status === Dropzone.UPLOADING) {\n              return Dropzone.confirm(this.options.dictCancelUploadConfirmation, () => this.removeFile(file));\n            } else {\n              if (this.options.dictRemoveFileConfirmation) {\n                return Dropzone.confirm(this.options.dictRemoveFileConfirmation, () => this.removeFile(file));\n              } else {\n                return this.removeFile(file);\n              }\n            }\n          };\n\n          for (let removeLink of file.previewElement.querySelectorAll(\"[data-dz-remove]\")) {\n             removeLink.addEventListener(\"click\", removeFileEvent);\n          }\n        }\n      },\n\n\n      // Called whenever a file is removed.\n      removedfile(file) {\n        if (file.previewElement != null && file.previewElement.parentNode != null) {\n          file.previewElement.parentNode.removeChild(file.previewElement);\n        }\n        return this._updateMaxFilesReachedClass();\n      },\n\n      // Called when a thumbnail has been generated\n      // Receives `file` and `dataUrl`\n      thumbnail(file, dataUrl) {\n        if (file.previewElement) {\n          file.previewElement.classList.remove(\"dz-file-preview\");\n          for (let thumbnailElement of file.previewElement.querySelectorAll(\"[data-dz-thumbnail]\")) {\n            thumbnailElement.alt = file.name;\n            thumbnailElement.src = dataUrl;\n          }\n\n          return setTimeout((() => file.previewElement.classList.add(\"dz-image-preview\")), 1);\n        }\n      },\n\n      // Called whenever an error occurs\n      // Receives `file` and `message`\n      error(file, message) {\n        if (file.previewElement) {\n          file.previewElement.classList.add(\"dz-error\");\n          if ((typeof message !== \"String\") && message.error) {\n            message = message.error;\n          }\n          for (let node of file.previewElement.querySelectorAll(\"[data-dz-errormessage]\")) {\n            node.textContent = message;\n          }\n        }\n      },\n\n      errormultiple() {\n      },\n\n      // Called when a file gets processed. Since there is a cue, not all added\n      // files are processed immediately.\n      // Receives `file`\n      processing(file) {\n        if (file.previewElement) {\n          file.previewElement.classList.add(\"dz-processing\");\n          if (file._removeLink) {\n            return file._removeLink.innerHTML = this.options.dictCancelUpload;\n          }\n        }\n      },\n\n      processingmultiple() {\n      },\n\n      // Called whenever the upload progress gets updated.\n      // Receives `file`, `progress` (percentage 0-100) and `bytesSent`.\n      // To get the total number of bytes of the file, use `file.size`\n      uploadprogress(file, progress, bytesSent) {\n        if (file.previewElement) {\n          for (let node of file.previewElement.querySelectorAll(\"[data-dz-uploadprogress]\")) {\n              node.nodeName === 'PROGRESS' ?\n                  (node.value = progress)\n                  :\n                  (node.style.width = `${progress}%`)\n          }\n        }\n      },\n\n      // Called whenever the total upload progress gets updated.\n      // Called with totalUploadProgress (0-100), totalBytes and totalBytesSent\n      totaluploadprogress() {\n      },\n\n      // Called just before the file is sent. Gets the `xhr` object as second\n      // parameter, so you can modify it (for example to add a CSRF token) and a\n      // `formData` object to add additional information.\n      sending() {\n      },\n\n      sendingmultiple() {},\n\n      // When the complete upload is finished and successful\n      // Receives `file`\n      success(file) {\n        if (file.previewElement) {\n          return file.previewElement.classList.add(\"dz-success\");\n        }\n      },\n\n      successmultiple() {},\n\n      // When the upload is canceled.\n      canceled(file) {\n        return this.emit(\"error\", file, this.options.dictUploadCanceled);\n      },\n\n      canceledmultiple() {},\n\n      // When the upload is finished, either with success or an error.\n      // Receives `file`\n      complete(file) {\n        if (file._removeLink) {\n          file._removeLink.innerHTML = this.options.dictRemoveFile;\n        }\n        if (file.previewElement) {\n          return file.previewElement.classList.add(\"dz-complete\");\n        }\n      },\n\n      completemultiple() {},\n\n      maxfilesexceeded() {},\n\n      maxfilesreached() {},\n\n      queuecomplete() {},\n\n      addedfiles() {}\n    };\n\n\n    this.prototype._thumbnailQueue = [];\n    this.prototype._processingThumbnail = false;\n  }\n\n  // global utility\n  static extend(target, ...objects) {\n    for (let object of objects) {\n      for (let key in object) {\n        let val = object[key];\n        target[key] = val;\n      }\n    }\n    return target;\n  }\n\n  constructor(el, options) {\n    super();\n    let fallback, left;\n    this.element = el;\n    // For backwards compatibility since the version was in the prototype previously\n    this.version = Dropzone.version;\n\n    this.defaultOptions.previewTemplate = this.defaultOptions.previewTemplate.replace(/\\n*/g, \"\");\n\n    this.clickableElements = [];\n    this.listeners = [];\n    this.files = []; // All files\n\n    if (typeof this.element === \"string\") {\n      this.element = document.querySelector(this.element);\n    }\n\n    // Not checking if instance of HTMLElement or Element since IE9 is extremely weird.\n    if (!this.element || (this.element.nodeType == null)) {\n      throw new Error(\"Invalid dropzone element.\");\n    }\n\n    if (this.element.dropzone) {\n      throw new Error(\"Dropzone already attached.\");\n    }\n\n    // Now add this dropzone to the instances.\n    Dropzone.instances.push(this);\n\n    // Put the dropzone inside the element itself.\n    this.element.dropzone = this;\n\n    let elementOptions = (left = Dropzone.optionsForElement(this.element)) != null ? left : {};\n\n    this.options = Dropzone.extend({}, this.defaultOptions, elementOptions, options != null ? options : {});\n\n    // If the browser failed, just call the fallback and leave\n    if (this.options.forceFallback || !Dropzone.isBrowserSupported()) {\n      return this.options.fallback.call(this);\n    }\n\n    // @options.url = @element.getAttribute \"action\" unless @options.url?\n    if (this.options.url == null) {\n      this.options.url = this.element.getAttribute(\"action\");\n    }\n\n    if (!this.options.url) {\n      throw new Error(\"No URL provided.\");\n    }\n\n    if (this.options.acceptedFiles && this.options.acceptedMimeTypes) {\n      throw new Error(\"You can't provide both 'acceptedFiles' and 'acceptedMimeTypes'. 'acceptedMimeTypes' is deprecated.\");\n    }\n\n    if (this.options.uploadMultiple && this.options.chunking) {\n      throw new Error('You cannot set both: uploadMultiple and chunking.');\n    }\n\n    // Backwards compatibility\n    if (this.options.acceptedMimeTypes) {\n      this.options.acceptedFiles = this.options.acceptedMimeTypes;\n      delete this.options.acceptedMimeTypes;\n    }\n\n    // Backwards compatibility\n    if (this.options.renameFilename != null) {\n      this.options.renameFile = file => this.options.renameFilename.call(this, file.name, file);\n    }\n\n    this.options.method = this.options.method.toUpperCase();\n\n    if ((fallback = this.getExistingFallback()) && fallback.parentNode) {\n      // Remove the fallback\n      fallback.parentNode.removeChild(fallback);\n    }\n\n    // Display previews in the previewsContainer element or the Dropzone element unless explicitly set to false\n    if (this.options.previewsContainer !== false) {\n      if (this.options.previewsContainer) {\n        this.previewsContainer = Dropzone.getElement(this.options.previewsContainer, \"previewsContainer\");\n      } else {\n        this.previewsContainer = this.element;\n      }\n    }\n\n    if (this.options.clickable) {\n      if (this.options.clickable === true) {\n        this.clickableElements = [this.element];\n      } else {\n        this.clickableElements = Dropzone.getElements(this.options.clickable, \"clickable\");\n      }\n    }\n\n\n    this.init();\n  }\n\n\n  // Returns all files that have been accepted\n  getAcceptedFiles() {\n    return this.files.filter((file) => file.accepted).map((file) => file);\n  }\n\n  // Returns all files that have been rejected\n  // Not sure when that's going to be useful, but added for completeness.\n  getRejectedFiles() {\n    return this.files.filter((file) => !file.accepted).map((file) => file);\n  }\n\n  getFilesWithStatus(status) {\n    return this.files.filter((file) => file.status === status).map((file) => file);\n  }\n\n  // Returns all files that are in the queue\n  getQueuedFiles() {\n    return this.getFilesWithStatus(Dropzone.QUEUED);\n  }\n\n  getUploadingFiles() {\n    return this.getFilesWithStatus(Dropzone.UPLOADING);\n  }\n\n  getAddedFiles() {\n    return this.getFilesWithStatus(Dropzone.ADDED);\n  }\n\n  // Files that are either queued or uploading\n  getActiveFiles() {\n    return this.files.filter((file) => (file.status === Dropzone.UPLOADING) || (file.status === Dropzone.QUEUED)).map((file) => file);\n  }\n\n  // The function that gets called when Dropzone is initialized. You\n  // can (and should) setup event listeners inside this function.\n  init() {\n    // In case it isn't set already\n    if (this.element.tagName === \"form\") {\n      this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n    }\n\n    if (this.element.classList.contains(\"dropzone\") && !this.element.querySelector(\".dz-message\")) {\n      this.element.appendChild(Dropzone.createElement(`<div class=\"dz-default dz-message\"><span>${this.options.dictDefaultMessage}</span></div>`));\n    }\n\n    if (this.clickableElements.length) {\n      let setupHiddenFileInput = () => {\n        if (this.hiddenFileInput) {\n          this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);\n        }\n        this.hiddenFileInput = document.createElement(\"input\");\n        this.hiddenFileInput.setAttribute(\"type\", \"file\");\n        if ((this.options.maxFiles === null) || (this.options.maxFiles > 1)) {\n          this.hiddenFileInput.setAttribute(\"multiple\", \"multiple\");\n        }\n        this.hiddenFileInput.className = \"dz-hidden-input\";\n\n        if (this.options.acceptedFiles !== null) {\n          this.hiddenFileInput.setAttribute(\"accept\", this.options.acceptedFiles);\n        }\n        if (this.options.capture !== null) {\n          this.hiddenFileInput.setAttribute(\"capture\", this.options.capture);\n        }\n\n        // Not setting `display=\"none\"` because some browsers don't accept clicks\n        // on elements that aren't displayed.\n        this.hiddenFileInput.style.visibility = \"hidden\";\n        this.hiddenFileInput.style.position = \"absolute\";\n        this.hiddenFileInput.style.top = \"0\";\n        this.hiddenFileInput.style.left = \"0\";\n        this.hiddenFileInput.style.height = \"0\";\n        this.hiddenFileInput.style.width = \"0\";\n        Dropzone.getElement(this.options.hiddenInputContainer, 'hiddenInputContainer').appendChild(this.hiddenFileInput);\n        return this.hiddenFileInput.addEventListener(\"change\", () => {\n          let {files} = this.hiddenFileInput;\n          if (files.length) {\n            for (let file of files) {\n              this.addFile(file);\n            }\n          }\n          this.emit(\"addedfiles\", files);\n          return setupHiddenFileInput();\n        });\n      };\n      setupHiddenFileInput();\n    }\n\n    this.URL = window.URL !== null ? window.URL : window.webkitURL;\n\n\n    // Setup all event listeners on the Dropzone object itself.\n    // They're not in @setupEventListeners() because they shouldn't be removed\n    // again when the dropzone gets disabled.\n    for (let eventName of this.events) {\n      this.on(eventName, this.options[eventName]);\n    }\n\n    this.on(\"uploadprogress\", () => this.updateTotalUploadProgress());\n\n    this.on(\"removedfile\", () => this.updateTotalUploadProgress());\n\n    this.on(\"canceled\", file => this.emit(\"complete\", file));\n\n    // Emit a `queuecomplete` event if all files finished uploading.\n    this.on(\"complete\", file => {\n      if ((this.getAddedFiles().length === 0) && (this.getUploadingFiles().length === 0) && (this.getQueuedFiles().length === 0)) {\n        // This needs to be deferred so that `queuecomplete` really triggers after `complete`\n        return setTimeout((() => this.emit(\"queuecomplete\")), 0);\n      }\n    });\n\n\n    let noPropagation = function (e) {\n      e.stopPropagation();\n      if (e.preventDefault) {\n        return e.preventDefault();\n      } else {\n        return e.returnValue = false;\n      }\n    };\n\n    // Create the listeners\n    this.listeners = [\n      {\n        element: this.element,\n        events: {\n          \"dragstart\": e => {\n            return this.emit(\"dragstart\", e);\n          },\n          \"dragenter\": e => {\n            noPropagation(e);\n            return this.emit(\"dragenter\", e);\n          },\n          \"dragover\": e => {\n            // Makes it possible to drag files from chrome's download bar\n            // http://stackoverflow.com/questions/19526430/drag-and-drop-file-uploads-from-chrome-downloads-bar\n            // Try is required to prevent bug in Internet Explorer 11 (SCRIPT65535 exception)\n            let efct;\n            try {\n              efct = e.dataTransfer.effectAllowed;\n            } catch (error) {\n            }\n            e.dataTransfer.dropEffect = ('move' === efct) || ('linkMove' === efct) ? 'move' : 'copy';\n\n            noPropagation(e);\n            return this.emit(\"dragover\", e);\n          },\n          \"dragleave\": e => {\n            return this.emit(\"dragleave\", e);\n          },\n          \"drop\": e => {\n            noPropagation(e);\n            return this.drop(e);\n          },\n          \"dragend\": e => {\n            return this.emit(\"dragend\", e);\n          }\n        }\n\n        // This is disabled right now, because the browsers don't implement it properly.\n        // \"paste\": (e) =>\n        //   noPropagation e\n        //   @paste e\n      }\n    ];\n\n    this.clickableElements.forEach(clickableElement => {\n      return this.listeners.push({\n        element: clickableElement,\n        events: {\n          \"click\": evt => {\n            // Only the actual dropzone or the message element should trigger file selection\n            if ((clickableElement !== this.element) || ((evt.target === this.element) || Dropzone.elementInside(evt.target, this.element.querySelector(\".dz-message\")))) {\n              this.hiddenFileInput.click(); // Forward the click\n            }\n            return true;\n          }\n        }\n      });\n    });\n\n    this.enable();\n\n    return this.options.init.call(this);\n  }\n\n  // Not fully tested yet\n  destroy() {\n    this.disable();\n    this.removeAllFiles(true);\n    if (this.hiddenFileInput != null ? this.hiddenFileInput.parentNode : undefined) {\n      this.hiddenFileInput.parentNode.removeChild(this.hiddenFileInput);\n      this.hiddenFileInput = null;\n    }\n    delete this.element.dropzone;\n    return Dropzone.instances.splice(Dropzone.instances.indexOf(this), 1);\n  }\n\n\n  updateTotalUploadProgress() {\n    let totalUploadProgress;\n    let totalBytesSent = 0;\n    let totalBytes = 0;\n\n    let activeFiles = this.getActiveFiles();\n\n    if (activeFiles.length) {\n      for (let file of this.getActiveFiles()) {\n        totalBytesSent += file.upload.bytesSent;\n        totalBytes += file.upload.total;\n      }\n      totalUploadProgress = (100 * totalBytesSent) / totalBytes;\n    } else {\n      totalUploadProgress = 100;\n    }\n\n    return this.emit(\"totaluploadprogress\", totalUploadProgress, totalBytes, totalBytesSent);\n  }\n\n  // @options.paramName can be a function taking one parameter rather than a string.\n  // A parameter name for a file is obtained simply by calling this with an index number.\n  _getParamName(n) {\n    if (typeof this.options.paramName === \"function\") {\n      return this.options.paramName(n);\n    } else {\n      return `${this.options.paramName}${this.options.uploadMultiple ? `[${n}]` : \"\"}`;\n    }\n  }\n\n  // If @options.renameFile is a function,\n  // the function will be used to rename the file.name before appending it to the formData\n  _renameFile(file) {\n    if (typeof this.options.renameFile !== \"function\") {\n      return file.name;\n    }\n    return this.options.renameFile(file);\n  }\n\n  // Returns a form that can be used as fallback if the browser does not support DragnDrop\n  //\n  // If the dropzone is already a form, only the input field and button are returned. Otherwise a complete form element is provided.\n  // This code has to pass in IE7 :(\n  getFallbackForm() {\n    let existingFallback, form;\n    if (existingFallback = this.getExistingFallback()) {\n      return existingFallback;\n    }\n\n    let fieldsString = \"<div class=\\\"dz-fallback\\\">\";\n    if (this.options.dictFallbackText) {\n      fieldsString += `<p>${this.options.dictFallbackText}</p>`;\n    }\n    fieldsString += `<input type=\"file\" name=\"${this._getParamName(0)}\" ${this.options.uploadMultiple ? 'multiple=\"multiple\"' : undefined } /><input type=\"submit\" value=\"Upload!\"></div>`;\n\n    let fields = Dropzone.createElement(fieldsString);\n    if (this.element.tagName !== \"FORM\") {\n      form = Dropzone.createElement(`<form action=\"${this.options.url}\" enctype=\"multipart/form-data\" method=\"${this.options.method}\"></form>`);\n      form.appendChild(fields);\n    } else {\n      // Make sure that the enctype and method attributes are set properly\n      this.element.setAttribute(\"enctype\", \"multipart/form-data\");\n      this.element.setAttribute(\"method\", this.options.method);\n    }\n    return form != null ? form : fields;\n  }\n\n\n  // Returns the fallback elements if they exist already\n  //\n  // This code has to pass in IE7 :(\n  getExistingFallback() {\n    let getFallback = function (elements) {\n      for (let el of elements) {\n        if (/(^| )fallback($| )/.test(el.className)) {\n          return el;\n        }\n      }\n    };\n\n    for (let tagName of [\"div\", \"form\"]) {\n      var fallback;\n      if (fallback = getFallback(this.element.getElementsByTagName(tagName))) {\n        return fallback;\n      }\n    }\n  }\n\n\n  // Activates all listeners stored in @listeners\n  setupEventListeners() {\n    return this.listeners.map((elementListeners) =>\n        (() => {\n          let result = [];\n          for (let event in elementListeners.events) {\n            let listener = elementListeners.events[event];\n            result.push(elementListeners.element.addEventListener(event, listener, false));\n          }\n          return result;\n        })());\n  }\n\n\n  // Deactivates all listeners stored in @listeners\n  removeEventListeners() {\n    return this.listeners.map((elementListeners) =>\n        (() => {\n          let result = [];\n          for (let event in elementListeners.events) {\n            let listener = elementListeners.events[event];\n            result.push(elementListeners.element.removeEventListener(event, listener, false));\n          }\n          return result;\n        })());\n  }\n\n  // Removes all event listeners and cancels all files in the queue or being processed.\n  disable() {\n    this.clickableElements.forEach(element => element.classList.remove(\"dz-clickable\"));\n    this.removeEventListeners();\n    this.disabled = true;\n\n    return this.files.map((file) => this.cancelUpload(file));\n  }\n\n  enable() {\n    delete this.disabled;\n    this.clickableElements.forEach(element => element.classList.add(\"dz-clickable\"));\n    return this.setupEventListeners();\n  }\n\n  // Returns a nicely formatted filesize\n  filesize(size) {\n    let selectedSize = 0;\n    let selectedUnit = \"b\";\n\n    if (size > 0) {\n      let units = ['tb', 'gb', 'mb', 'kb', 'b'];\n\n      for (let i = 0; i < units.length; i++) {\n        let unit = units[i];\n        let cutoff = Math.pow(this.options.filesizeBase, 4 - i) / 10;\n\n        if (size >= cutoff) {\n          selectedSize = size / Math.pow(this.options.filesizeBase, 4 - i);\n          selectedUnit = unit;\n          break;\n        }\n      }\n\n      selectedSize = Math.round(10 * selectedSize) / 10; // Cutting of digits\n    }\n\n    return `<strong>${selectedSize}</strong> ${this.options.dictFileSizeUnits[selectedUnit]}`;\n  }\n\n\n  // Adds or removes the `dz-max-files-reached` class from the form.\n  _updateMaxFilesReachedClass() {\n    if ((this.options.maxFiles != null) && (this.getAcceptedFiles().length >= this.options.maxFiles)) {\n      if (this.getAcceptedFiles().length === this.options.maxFiles) {\n        this.emit('maxfilesreached', this.files);\n      }\n      return this.element.classList.add(\"dz-max-files-reached\");\n    } else {\n      return this.element.classList.remove(\"dz-max-files-reached\");\n    }\n  }\n\n\n  drop(e) {\n    if (!e.dataTransfer) {\n      return;\n    }\n    this.emit(\"drop\", e);\n\n    // Convert the FileList to an Array\n    // This is necessary for IE11\n    let files = [];\n    for (let i = 0; i < e.dataTransfer.files.length; i++) {\n      files[i] = e.dataTransfer.files[i];\n    }\n\n    this.emit(\"addedfiles\", files);\n\n    // Even if it's a folder, files.length will contain the folders.\n    if (files.length) {\n      let {items} = e.dataTransfer;\n      if (items && items.length && (items[0].webkitGetAsEntry != null)) {\n        // The browser supports dropping of folders, so handle items instead of files\n        this._addFilesFromItems(items);\n      } else {\n        this.handleFiles(files);\n      }\n    }\n  }\n\n  paste(e) {\n    if (__guard__(e != null ? e.clipboardData : undefined, x => x.items) == null) {\n      return;\n    }\n\n    this.emit(\"paste\", e);\n    let {items} = e.clipboardData;\n\n    if (items.length) {\n      return this._addFilesFromItems(items);\n    }\n  }\n\n\n  handleFiles(files) {\n    for(let file of files) {\n      this.addFile(file);\n    }\n  }\n\n  // When a folder is dropped (or files are pasted), items must be handled\n  // instead of files.\n  _addFilesFromItems(items) {\n    return (() => {\n      let result = [];\n      for (let item of items) {\n        var entry;\n        if ((item.webkitGetAsEntry != null) && (entry = item.webkitGetAsEntry())) {\n          if (entry.isFile) {\n            result.push(this.addFile(item.getAsFile()));\n          } else if (entry.isDirectory) {\n            // Append all files from that directory to files\n            result.push(this._addFilesFromDirectory(entry, entry.name));\n          } else {\n            result.push(undefined);\n          }\n        } else if (item.getAsFile != null) {\n          if ((item.kind == null) || (item.kind === \"file\")) {\n            result.push(this.addFile(item.getAsFile()));\n          } else {\n            result.push(undefined);\n          }\n        } else {\n          result.push(undefined);\n        }\n      }\n      return result;\n    })();\n  }\n\n\n  // Goes through the directory, and adds each file it finds recursively\n  _addFilesFromDirectory(directory, path) {\n    let dirReader = directory.createReader();\n\n    let errorHandler = error => __guardMethod__(console, 'log', o => o.log(error));\n\n    var readEntries = () => {\n      return dirReader.readEntries(entries => {\n            if (entries.length > 0) {\n              for (let entry of entries) {\n                if (entry.isFile) {\n                  entry.file(file => {\n                    if (this.options.ignoreHiddenFiles && (file.name.substring(0, 1) === '.')) {\n                      return;\n                    }\n                    file.fullPath = `${path}/${file.name}`;\n                    return this.addFile(file);\n                  });\n                } else if (entry.isDirectory) {\n                  this._addFilesFromDirectory(entry, `${path}/${entry.name}`);\n                }\n              }\n\n              // Recursively call readEntries() again, since browser only handle\n              // the first 100 entries.\n              // See: https://developer.mozilla.org/en-US/docs/Web/API/DirectoryReader#readEntries\n              readEntries();\n            }\n            return null;\n          }\n          , errorHandler);\n    };\n\n    return readEntries();\n  }\n\n\n  // If `done()` is called without argument the file is accepted\n  // If you call it with an error message, the file is rejected\n  // (This allows for asynchronous validation)\n  //\n  // This function checks the filesize, and if the file.type passes the\n  // `acceptedFiles` check.\n  accept(file, done) {\n    if (this.options.maxFilesize && file.size > (this.options.maxFilesize * 1024 * 1024)) {\n      return done(this.options.dictFileTooBig.replace(\"{{filesize}}\", Math.round(file.size / 1024 / 10.24) / 100).replace(\"{{maxFilesize}}\", this.options.maxFilesize));\n    } else if (!Dropzone.isValidFile(file, this.options.acceptedFiles)) {\n      return done(this.options.dictInvalidFileType);\n    } else if ((this.options.maxFiles != null) && (this.getAcceptedFiles().length >= this.options.maxFiles)) {\n      done(this.options.dictMaxFilesExceeded.replace(\"{{maxFiles}}\", this.options.maxFiles));\n      return this.emit(\"maxfilesexceeded\", file);\n    } else {\n      return this.options.accept.call(this, file, done);\n    }\n  }\n\n  addFile(file) {\n    file.upload = {\n      uuid: Dropzone.uuidv4(),\n      progress: 0,\n      // Setting the total upload size to file.size for the beginning\n      // It's actual different than the size to be transmitted.\n      total: file.size,\n      bytesSent: 0,\n      filename: this._renameFile(file),\n      chunked: this.options.chunking && (this.options.forceChunking || file.size > this.options.chunkSize),\n      totalChunkCount: Math.ceil(file.size / this.options.chunkSize)\n    };\n    this.files.push(file);\n\n    file.status = Dropzone.ADDED;\n\n    this.emit(\"addedfile\", file);\n\n    this._enqueueThumbnail(file);\n\n    return this.accept(file, error => {\n      if (error) {\n        file.accepted = false;\n        this._errorProcessing([file], error); // Will set the file.status\n      } else {\n        file.accepted = true;\n        if (this.options.autoQueue) {\n          this.enqueueFile(file);\n        } // Will set .accepted = true\n      }\n      return this._updateMaxFilesReachedClass();\n    });\n  }\n\n\n  // Wrapper for enqueueFile\n  enqueueFiles(files) {\n    for (let file of files) {\n      this.enqueueFile(file);\n    }\n    return null;\n  }\n\n  enqueueFile(file) {\n    if ((file.status === Dropzone.ADDED) && (file.accepted === true)) {\n      file.status = Dropzone.QUEUED;\n      if (this.options.autoProcessQueue) {\n        return setTimeout((() => this.processQueue()), 0); // Deferring the call\n      }\n    } else {\n      throw new Error(\"This file can't be queued because it has already been processed or was rejected.\");\n    }\n  }\n\n  _enqueueThumbnail(file) {\n    if (this.options.createImageThumbnails && file.type.match(/image.*/) && (file.size <= (this.options.maxThumbnailFilesize * 1024 * 1024))) {\n      this._thumbnailQueue.push(file);\n      return setTimeout((() => this._processThumbnailQueue()), 0); // Deferring the call\n    }\n  }\n\n  _processThumbnailQueue() {\n    if (this._processingThumbnail || (this._thumbnailQueue.length === 0)) {\n      return;\n    }\n\n    this._processingThumbnail = true;\n    let file = this._thumbnailQueue.shift();\n    return this.createThumbnail(file, this.options.thumbnailWidth, this.options.thumbnailHeight, this.options.thumbnailMethod, true, dataUrl => {\n      this.emit(\"thumbnail\", file, dataUrl);\n      this._processingThumbnail = false;\n      return this._processThumbnailQueue();\n    });\n  }\n\n\n  // Can be called by the user to remove a file\n  removeFile(file) {\n    if (file.status === Dropzone.UPLOADING) {\n      this.cancelUpload(file);\n    }\n    this.files = without(this.files, file);\n\n    this.emit(\"removedfile\", file);\n    if (this.files.length === 0) {\n      return this.emit(\"reset\");\n    }\n  }\n\n  // Removes all files that aren't currently processed from the list\n  removeAllFiles(cancelIfNecessary) {\n    // Create a copy of files since removeFile() changes the @files array.\n    if (cancelIfNecessary == null) {\n      cancelIfNecessary = false;\n    }\n    for (let file of this.files.slice()) {\n      if ((file.status !== Dropzone.UPLOADING) || cancelIfNecessary) {\n        this.removeFile(file);\n      }\n    }\n    return null;\n  }\n\n  // Resizes an image before it gets sent to the server. This function is the default behavior of\n  // `options.transformFile` if `resizeWidth` or `resizeHeight` are set. The callback is invoked with\n  // the resized blob.\n  resizeImage(file, width, height, resizeMethod, callback) {\n    return this.createThumbnail(file, width, height, resizeMethod, true, (dataUrl, canvas) => {\n      if (canvas == null) {\n        // The image has not been resized\n        return callback(file);\n      } else {\n        let {resizeMimeType} = this.options;\n        if (resizeMimeType == null) {\n          resizeMimeType = file.type;\n        }\n        let resizedDataURL = canvas.toDataURL(resizeMimeType, this.options.resizeQuality);\n        if ((resizeMimeType === 'image/jpeg') || (resizeMimeType === 'image/jpg')) {\n          // Now add the original EXIF information\n          resizedDataURL = ExifRestore.restore(file.dataURL, resizedDataURL);\n        }\n        return callback(Dropzone.dataURItoBlob(resizedDataURL));\n      }\n    });\n  }\n\n  createThumbnail(file, width, height, resizeMethod, fixOrientation, callback) {\n    let fileReader = new FileReader;\n\n    fileReader.onload = () => {\n\n      file.dataURL = fileReader.result;\n\n      // Don't bother creating a thumbnail for SVG images since they're vector\n      if (file.type === \"image/svg+xml\") {\n        if (callback != null) {\n          callback(fileReader.result);\n        }\n        return;\n      }\n\n      return this.createThumbnailFromUrl(file, width, height, resizeMethod, fixOrientation, callback);\n    };\n\n    return fileReader.readAsDataURL(file);\n  }\n\n  createThumbnailFromUrl(file, width, height, resizeMethod, fixOrientation, callback, crossOrigin) {\n    // Not using `new Image` here because of a bug in latest Chrome versions.\n    // See https://github.com/enyo/dropzone/pull/226\n    let img = document.createElement(\"img\");\n\n    if (crossOrigin) {\n      img.crossOrigin = crossOrigin;\n    }\n\n    img.onload = () => {\n      let loadExif = callback => callback(1);\n      if ((typeof EXIF !== 'undefined' && EXIF !== null) && fixOrientation) {\n        loadExif = callback =>\n            EXIF.getData(img, function () {\n              return callback(EXIF.getTag(this, 'Orientation'));\n            })\n        ;\n      }\n\n      return loadExif(orientation => {\n        file.width = img.width;\n        file.height = img.height;\n\n        let resizeInfo = this.options.resize.call(this, file, width, height, resizeMethod);\n\n        let canvas = document.createElement(\"canvas\");\n        let ctx = canvas.getContext(\"2d\");\n\n        canvas.width = resizeInfo.trgWidth;\n        canvas.height = resizeInfo.trgHeight;\n\n        if (orientation > 4) {\n          canvas.width = resizeInfo.trgHeight;\n          canvas.height = resizeInfo.trgWidth;\n        }\n\n        switch (orientation) {\n          case 2:\n            // horizontal flip\n            ctx.translate(canvas.width, 0);\n            ctx.scale(-1, 1);\n            break;\n          case 3:\n            // 180° rotate left\n            ctx.translate(canvas.width, canvas.height);\n            ctx.rotate(Math.PI);\n            break;\n          case 4:\n            // vertical flip\n            ctx.translate(0, canvas.height);\n            ctx.scale(1, -1);\n            break;\n          case 5:\n            // vertical flip + 90 rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.scale(1, -1);\n            break;\n          case 6:\n            // 90° rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.translate(0, -canvas.width);\n            break;\n          case 7:\n            // horizontal flip + 90 rotate right\n            ctx.rotate(0.5 * Math.PI);\n            ctx.translate(canvas.height, -canvas.width);\n            ctx.scale(-1, 1);\n            break;\n          case 8:\n            // 90° rotate left\n            ctx.rotate(-0.5 * Math.PI);\n            ctx.translate(-canvas.height, 0);\n            break;\n        }\n\n        // This is a bugfix for iOS' scaling bug.\n        drawImageIOSFix(ctx, img, resizeInfo.srcX != null ? resizeInfo.srcX : 0, resizeInfo.srcY != null ? resizeInfo.srcY : 0, resizeInfo.srcWidth, resizeInfo.srcHeight, resizeInfo.trgX != null ? resizeInfo.trgX : 0, resizeInfo.trgY != null ? resizeInfo.trgY : 0, resizeInfo.trgWidth, resizeInfo.trgHeight);\n\n        let thumbnail = canvas.toDataURL(\"image/png\");\n\n        if (callback != null) {\n          return callback(thumbnail, canvas);\n        }\n      });\n    };\n\n    if (callback != null) {\n      img.onerror = callback;\n    }\n\n    return img.src = file.dataURL;\n  }\n\n\n  // Goes through the queue and processes files if there aren't too many already.\n  processQueue() {\n    let {parallelUploads} = this.options;\n    let processingLength = this.getUploadingFiles().length;\n    let i = processingLength;\n\n    // There are already at least as many files uploading than should be\n    if (processingLength >= parallelUploads) {\n      return;\n    }\n\n    let queuedFiles = this.getQueuedFiles();\n\n    if (!(queuedFiles.length > 0)) {\n      return;\n    }\n\n    if (this.options.uploadMultiple) {\n      // The files should be uploaded in one request\n      return this.processFiles(queuedFiles.slice(0, (parallelUploads - processingLength)));\n    } else {\n      while (i < parallelUploads) {\n        if (!queuedFiles.length) {\n          return;\n        } // Nothing left to process\n        this.processFile(queuedFiles.shift());\n        i++;\n      }\n    }\n  }\n\n\n  // Wrapper for `processFiles`\n  processFile(file) {\n    return this.processFiles([file]);\n  }\n\n\n  // Loads the file, then calls finishedLoading()\n  processFiles(files) {\n    for (let file of files) {\n      file.processing = true; // Backwards compatibility\n      file.status = Dropzone.UPLOADING;\n\n      this.emit(\"processing\", file);\n    }\n\n    if (this.options.uploadMultiple) {\n      this.emit(\"processingmultiple\", files);\n    }\n\n    return this.uploadFiles(files);\n  }\n\n\n  _getFilesWithXhr(xhr) {\n    let files;\n    return files = (this.files.filter((file) => file.xhr === xhr).map((file) => file));\n  }\n\n\n  // Cancels the file upload and sets the status to CANCELED\n  // **if** the file is actually being uploaded.\n  // If it's still in the queue, the file is being removed from it and the status\n  // set to CANCELED.\n  cancelUpload(file) {\n    if (file.status === Dropzone.UPLOADING) {\n      let groupedFiles = this._getFilesWithXhr(file.xhr);\n      for (let groupedFile of groupedFiles) {\n        groupedFile.status = Dropzone.CANCELED;\n      }\n      if (typeof file.xhr !== 'undefined') {\n        file.xhr.abort();\n      }\n      for (let groupedFile of groupedFiles) {\n        this.emit(\"canceled\", groupedFile);\n      }\n      if (this.options.uploadMultiple) {\n        this.emit(\"canceledmultiple\", groupedFiles);\n      }\n\n    } else if (file.status === Dropzone.ADDED || file.status === Dropzone.QUEUED) {\n      file.status = Dropzone.CANCELED;\n      this.emit(\"canceled\", file);\n      if (this.options.uploadMultiple) {\n        this.emit(\"canceledmultiple\", [file]);\n      }\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  resolveOption(option, ...args) {\n    if (typeof option === 'function') {\n      return option.apply(this, args);\n    }\n    return option;\n  }\n\n  uploadFile(file) { return this.uploadFiles([file]); }\n\n  uploadFiles(files) {\n    this._transformFiles(files, (transformedFiles) => {\n      if (files[0].upload.chunked) {\n        // This file should be sent in chunks!\n\n        // If the chunking option is set, we **know** that there can only be **one** file, since\n        // uploadMultiple is not allowed with this option.\n        let file = files[0];\n        let transformedFile = transformedFiles[0];\n        let startedChunkCount = 0;\n\n        file.upload.chunks = [];\n\n        let handleNextChunk = () => {\n          let chunkIndex = 0;\n\n          // Find the next item in file.upload.chunks that is not defined yet.\n          while (file.upload.chunks[chunkIndex] !== undefined) {\n            chunkIndex++;\n          }\n\n          // This means, that all chunks have already been started.\n          if (chunkIndex >= file.upload.totalChunkCount) return;\n\n          startedChunkCount++;\n\n          let start = chunkIndex * this.options.chunkSize;\n          let end = Math.min(start + this.options.chunkSize, file.size);\n\n          let dataBlock = {\n            name: this._getParamName(0),\n            data: transformedFile.webkitSlice ? transformedFile.webkitSlice(start, end) : transformedFile.slice(start, end),\n            filename: file.upload.filename,\n            chunkIndex: chunkIndex\n          };\n\n          file.upload.chunks[chunkIndex] = {\n            file: file,\n            index: chunkIndex,\n            dataBlock: dataBlock, // In case we want to retry.\n            status: Dropzone.UPLOADING,\n            progress: 0,\n            retries: 0 // The number of times this block has been retried.\n          };\n\n\n          this._uploadData(files, [dataBlock]);\n        };\n\n        file.upload.finishedChunkUpload = (chunk) => {\n          let allFinished = true;\n          chunk.status = Dropzone.SUCCESS;\n\n          // Clear the data from the chunk\n          chunk.dataBlock = null;\n          // Leaving this reference to xhr intact here will cause memory leaks in some browsers\n          chunk.xhr = null;\n\n          for (let i = 0; i < file.upload.totalChunkCount; i ++) {\n            if (file.upload.chunks[i] === undefined) {\n              return handleNextChunk();\n            }\n            if (file.upload.chunks[i].status !== Dropzone.SUCCESS) {\n              allFinished = false;\n            }\n          }\n\n          if (allFinished) {\n            this.options.chunksUploaded(file, () => {\n              this._finished(files, '', null);\n            });\n          }\n        };\n\n        if (this.options.parallelChunkUploads) {\n          for (let i = 0; i < file.upload.totalChunkCount; i++) {\n            handleNextChunk();\n          }\n        }\n        else {\n          handleNextChunk();\n        }\n      } else {\n        let dataBlocks = [];\n        for (let i = 0; i < files.length; i++) {\n          dataBlocks[i] = {\n            name: this._getParamName(i),\n            data: transformedFiles[i],\n            filename: files[i].upload.filename\n          };\n        }\n        this._uploadData(files, dataBlocks);\n      }\n    });\n  }\n\n  /// Returns the right chunk for given file and xhr\n  _getChunk(file, xhr) {\n    for (let i = 0; i < file.upload.totalChunkCount; i++) {\n      if (file.upload.chunks[i] !== undefined && file.upload.chunks[i].xhr === xhr) {\n        return file.upload.chunks[i];\n      }\n    }\n  }\n\n  // This function actually uploads the file(s) to the server.\n  // If dataBlocks contains the actual data to upload (meaning, that this could either be transformed\n  // files, or individual chunks for chunked upload).\n  _uploadData(files, dataBlocks) {\n    let xhr = new XMLHttpRequest();\n\n    // Put the xhr object in the file objects to be able to reference it later.\n    for (let file of files) {\n      file.xhr = xhr;\n    }\n    if (files[0].upload.chunked) {\n      // Put the xhr object in the right chunk object, so it can be associated later, and found with _getChunk\n      files[0].upload.chunks[dataBlocks[0].chunkIndex].xhr = xhr;\n    }\n\n    let method = this.resolveOption(this.options.method, files);\n    let url = this.resolveOption(this.options.url, files);\n    xhr.open(method, url, true);\n\n    // Setting the timeout after open because of IE11 issue: https://gitlab.com/meno/dropzone/issues/8\n    xhr.timeout = this.resolveOption(this.options.timeout, files);\n\n    // Has to be after `.open()`. See https://github.com/enyo/dropzone/issues/179\n    xhr.withCredentials = !!this.options.withCredentials;\n\n\n    xhr.onload = e => {\n      this._finishedUploading(files, xhr, e);\n    };\n\n    xhr.ontimeout = () => {\n      this._handleUploadError(files, xhr, `Request timedout after ${this.options.timeout} seconds`);\n    };\n\n    xhr.onerror = () => {\n      this._handleUploadError(files, xhr);\n    };\n\n    // Some browsers do not have the .upload property\n    let progressObj = xhr.upload != null ? xhr.upload : xhr;\n    progressObj.onprogress = (e) => this._updateFilesUploadProgress(files, xhr, e);\n\n    let headers = {\n      \"Accept\": \"application/json\",\n      \"Cache-Control\": \"no-cache\",\n      \"X-Requested-With\": \"XMLHttpRequest\",\n    };\n\n    if (this.options.headers) {\n      Dropzone.extend(headers, this.options.headers);\n    }\n\n    for (let headerName in headers) {\n      let headerValue = headers[headerName];\n      if (headerValue) {\n        xhr.setRequestHeader(headerName, headerValue);\n      }\n    }\n\n    let formData = new FormData();\n\n    // Adding all @options parameters\n    if (this.options.params) {\n      let additionalParams = this.options.params;\n      if (typeof additionalParams === 'function') {\n        additionalParams = additionalParams.call(this, files, xhr, files[0].upload.chunked ? this._getChunk(files[0], xhr) : null);\n      }\n\n      for (let key in additionalParams) {\n        let value = additionalParams[key];\n        formData.append(key, value);\n      }\n    }\n\n    // Let the user add additional data if necessary\n    for (let file of files) {\n      this.emit(\"sending\", file, xhr, formData);\n    }\n    if (this.options.uploadMultiple) {\n      this.emit(\"sendingmultiple\", files, xhr, formData);\n    }\n\n\n    this._addFormElementData(formData);\n\n\n    // Finally add the files\n    // Has to be last because some servers (eg: S3) expect the file to be the last parameter\n    for (let i = 0; i < dataBlocks.length; i++) {\n      let dataBlock = dataBlocks[i];\n      formData.append(dataBlock.name, dataBlock.data, dataBlock.filename);\n    }\n\n    this.submitRequest(xhr, formData, files);\n  }\n\n\n  // Transforms all files with this.options.transformFile and invokes done with the transformed files when done.\n  _transformFiles(files, done) {\n    let transformedFiles = [];\n    // Clumsy way of handling asynchronous calls, until I get to add a proper Future library.\n    let doneCounter = 0;\n    for (let i = 0; i < files.length; i++) {\n      this.options.transformFile.call(this, files[i], (transformedFile) => {\n        transformedFiles[i] = transformedFile;\n        if (++doneCounter === files.length) {\n          done(transformedFiles);\n        }\n      });\n    }\n  }\n\n  // Takes care of adding other input elements of the form to the AJAX request\n  _addFormElementData(formData) {\n    // Take care of other input elements\n    if (this.element.tagName === \"FORM\") {\n      for (let input of this.element.querySelectorAll(\"input, textarea, select, button\")) {\n        let inputName = input.getAttribute(\"name\");\n        let inputType = input.getAttribute(\"type\");\n        if (inputType) inputType = inputType.toLowerCase();\n\n        // If the input doesn't have a name, we can't use it.\n        if (typeof inputName === 'undefined' || inputName === null) continue;\n\n        if ((input.tagName === \"SELECT\") && input.hasAttribute(\"multiple\")) {\n          // Possibly multiple values\n          for (let option of input.options) {\n            if (option.selected) {\n              formData.append(inputName, option.value);\n            }\n          }\n        } else if (!inputType || (inputType !== \"checkbox\" && inputType !== \"radio\") || input.checked) {\n          formData.append(inputName, input.value);\n        }\n      }\n    }\n  }\n\n  // Invoked when there is new progress information about given files.\n  // If e is not provided, it is assumed that the upload is finished.\n  _updateFilesUploadProgress(files, xhr, e) {\n    let progress;\n    if (typeof e !== 'undefined') {\n      progress = (100 * e.loaded) / e.total;\n\n      if (files[0].upload.chunked) {\n        let file = files[0];\n        // Since this is a chunked upload, we need to update the appropriate chunk progress.\n        let chunk = this._getChunk(file, xhr);\n        chunk.progress = progress;\n        chunk.total = e.total;\n        chunk.bytesSent = e.loaded;\n        let fileProgress = 0, fileTotal, fileBytesSent;\n        file.upload.progress = 0;\n        file.upload.total = 0;\n        file.upload.bytesSent = 0;\n        for (let i = 0; i < file.upload.totalChunkCount; i++) {\n          if (file.upload.chunks[i] !== undefined && file.upload.chunks[i].progress !== undefined) {\n            file.upload.progress += file.upload.chunks[i].progress;\n            file.upload.total += file.upload.chunks[i].total;\n            file.upload.bytesSent += file.upload.chunks[i].bytesSent;\n          }\n        }\n        file.upload.progress = file.upload.progress / file.upload.totalChunkCount;\n      } else {\n        for (let file of files) {\n          file.upload.progress = progress;\n          file.upload.total = e.total;\n          file.upload.bytesSent = e.loaded;\n        }\n      }\n      for (let file of files) {\n        this.emit(\"uploadprogress\", file, file.upload.progress, file.upload.bytesSent);\n      }\n    } else {\n      // Called when the file finished uploading\n\n      let allFilesFinished = true;\n\n      progress = 100;\n\n      for (let file of files) {\n        if ((file.upload.progress !== 100) || (file.upload.bytesSent !== file.upload.total)) {\n          allFilesFinished = false;\n        }\n        file.upload.progress = progress;\n        file.upload.bytesSent = file.upload.total;\n      }\n\n      // Nothing to do, all files already at 100%\n      if (allFilesFinished) {\n        return;\n      }\n\n      for (let file of files) {\n        this.emit(\"uploadprogress\", file, progress, file.upload.bytesSent);\n      }\n    }\n\n  }\n\n\n  _finishedUploading(files, xhr, e) {\n    let response;\n\n    if (files[0].status === Dropzone.CANCELED) {\n      return;\n    }\n\n    if (xhr.readyState !== 4) {\n      return;\n    }\n\n    if ((xhr.responseType !== 'arraybuffer') && (xhr.responseType !== 'blob')) {\n      response = xhr.responseText;\n\n      if (xhr.getResponseHeader(\"content-type\") && ~xhr.getResponseHeader(\"content-type\").indexOf(\"application/json\")) {\n        try {\n          response = JSON.parse(response);\n        } catch (error) {\n          e = error;\n          response = \"Invalid JSON response from server.\";\n        }\n      }\n    }\n\n    this._updateFilesUploadProgress(files);\n\n    if (!(200 <= xhr.status && xhr.status < 300)) {\n      this._handleUploadError(files, xhr, response);\n    } else {\n      if (files[0].upload.chunked) {\n        files[0].upload.finishedChunkUpload(this._getChunk(files[0], xhr));\n      } else {\n        this._finished(files, response, e);\n      }\n    }\n  }\n\n  _handleUploadError(files, xhr, response) {\n    if (files[0].status === Dropzone.CANCELED) {\n      return;\n    }\n\n    if (files[0].upload.chunked && this.options.retryChunks) {\n      let chunk = this._getChunk(files[0], xhr);\n      if (chunk.retries++ < this.options.retryChunksLimit) {\n        this._uploadData(files, [chunk.dataBlock]);\n        return;\n      } else {\n        console.warn('Retried this chunk too often. Giving up.')\n      }\n    }\n\n    for (let file of files) {\n      this._errorProcessing(files, response || this.options.dictResponseError.replace(\"{{statusCode}}\", xhr.status), xhr);\n    }\n  }\n\n  submitRequest(xhr, formData, files) {\n    xhr.send(formData);\n  }\n\n  // Called internally when processing is finished.\n  // Individual callbacks have to be called in the appropriate sections.\n  _finished(files, responseText, e) {\n    for (let file of files) {\n      file.status = Dropzone.SUCCESS;\n      this.emit(\"success\", file, responseText, e);\n      this.emit(\"complete\", file);\n    }\n    if (this.options.uploadMultiple) {\n      this.emit(\"successmultiple\", files, responseText, e);\n      this.emit(\"completemultiple\", files);\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  // Called internally when processing is finished.\n  // Individual callbacks have to be called in the appropriate sections.\n  _errorProcessing(files, message, xhr) {\n    for (let file of files) {\n      file.status = Dropzone.ERROR;\n      this.emit(\"error\", file, message, xhr);\n      this.emit(\"complete\", file);\n    }\n    if (this.options.uploadMultiple) {\n      this.emit(\"errormultiple\", files, message, xhr);\n      this.emit(\"completemultiple\", files);\n    }\n\n    if (this.options.autoProcessQueue) {\n      return this.processQueue();\n    }\n  }\n\n  static uuidv4() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      let r = Math.random() * 16 | 0, v = c === 'x' ? r : (r & 0x3 | 0x8);\n      return v.toString(16);\n    });\n  };\n}\nDropzone.initClass();\n\n\nDropzone.version = \"5.5.0\";\n\n\n// This is a map of options for your different dropzones. Add configurations\n// to this object for your different dropzone elemens.\n//\n// Example:\n//\n//     Dropzone.options.myDropzoneElementId = { maxFilesize: 1 };\n//\n// To disable autoDiscover for a specific element, you can set `false` as an option:\n//\n//     Dropzone.options.myDisabledElementId = false;\n//\n// And in html:\n//\n//     <form action=\"/upload\" id=\"my-dropzone-element-id\" class=\"dropzone\"></form>\nDropzone.options = {};\n\n\n// Returns the options for an element or undefined if none available.\nDropzone.optionsForElement = function (element) {\n  // Get the `Dropzone.options.elementId` for this element if it exists\n  if (element.getAttribute(\"id\")) {\n    return Dropzone.options[camelize(element.getAttribute(\"id\"))];\n  } else {\n    return undefined;\n  }\n};\n\n\n// Holds a list of all dropzone instances\nDropzone.instances = [];\n\n// Returns the dropzone for given element if any\nDropzone.forElement = function (element) {\n  if (typeof element === \"string\") {\n    element = document.querySelector(element);\n  }\n  if ((element != null ? element.dropzone : undefined) == null) {\n    throw new Error(\"No Dropzone found for given element. This is probably because you're trying to access it before Dropzone had the time to initialize. Use the `init` option to setup any additional observers on your Dropzone.\");\n  }\n  return element.dropzone;\n};\n\n\n// Set to false if you don't want Dropzone to automatically find and attach to .dropzone elements.\nDropzone.autoDiscover = true;\n\n// Looks for all .dropzone elements and creates a dropzone for them\nDropzone.discover = function () {\n  let dropzones;\n  if (document.querySelectorAll) {\n    dropzones = document.querySelectorAll(\".dropzone\");\n  } else {\n    dropzones = [];\n    // IE :(\n    let checkElements = elements =>\n        (() => {\n          let result = [];\n          for (let el of elements) {\n            if (/(^| )dropzone($| )/.test(el.className)) {\n              result.push(dropzones.push(el));\n            } else {\n              result.push(undefined);\n            }\n          }\n          return result;\n        })()\n    ;\n    checkElements(document.getElementsByTagName(\"div\"));\n    checkElements(document.getElementsByTagName(\"form\"));\n  }\n\n  return (() => {\n    let result = [];\n    for (let dropzone of dropzones) {\n      // Create a dropzone unless auto discover has been disabled for specific element\n      if (Dropzone.optionsForElement(dropzone) !== false) {\n        result.push(new Dropzone(dropzone));\n      } else {\n        result.push(undefined);\n      }\n    }\n    return result;\n  })();\n};\n\n\n// Since the whole Drag'n'Drop API is pretty new, some browsers implement it,\n// but not correctly.\n// So I created a blacklist of userAgents. Yes, yes. Browser sniffing, I know.\n// But what to do when browsers *theoretically* support an API, but crash\n// when using it.\n//\n// This is a list of regular expressions tested against navigator.userAgent\n//\n// ** It should only be used on browser that *do* support the API, but\n// incorrectly **\n//\nDropzone.blacklistedBrowsers = [\n  // The mac os and windows phone version of opera 12 seems to have a problem with the File drag'n'drop API.\n  /opera.*(Macintosh|Windows Phone).*version\\/12/i\n];\n\n\n// Checks if the browser is supported\nDropzone.isBrowserSupported = function () {\n  let capableBrowser = true;\n\n  if (window.File && window.FileReader && window.FileList && window.Blob && window.FormData && document.querySelector) {\n    if (!(\"classList\" in document.createElement(\"a\"))) {\n      capableBrowser = false;\n    } else {\n      // The browser supports the API, but may be blacklisted.\n      for (let regex of Dropzone.blacklistedBrowsers) {\n        if (regex.test(navigator.userAgent)) {\n          capableBrowser = false;\n          continue;\n        }\n      }\n    }\n  } else {\n    capableBrowser = false;\n  }\n\n  return capableBrowser;\n};\n\nDropzone.dataURItoBlob = function (dataURI) {\n  // convert base64 to raw binary data held in a string\n  // doesn't handle URLEncoded DataURIs - see SO answer #6850276 for code that does this\n  let byteString = atob(dataURI.split(',')[1]);\n\n  // separate out the mime component\n  let mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];\n\n  // write the bytes of the string to an ArrayBuffer\n  let ab = new ArrayBuffer(byteString.length);\n  let ia = new Uint8Array(ab);\n  for (let i = 0, end = byteString.length, asc = 0 <= end; asc ? i <= end : i >= end; asc ? i++ : i--) {\n    ia[i] = byteString.charCodeAt(i);\n  }\n\n  // write the ArrayBuffer to a blob\n  return new Blob([ab], {type: mimeString});\n};\n\n// Returns an array without the rejected item\nconst without = (list, rejectedItem) => list.filter((item) => item !== rejectedItem).map((item) => item);\n\n// abc-def_ghi -> abcDefGhi\nconst camelize = str => str.replace(/[\\-_](\\w)/g, match => match.charAt(1).toUpperCase());\n\n// Creates an element from string\nDropzone.createElement = function (string) {\n  let div = document.createElement(\"div\");\n  div.innerHTML = string;\n  return div.childNodes[0];\n};\n\n// Tests if given element is inside (or simply is) the container\nDropzone.elementInside = function (element, container) {\n  if (element === container) {\n    return true;\n  } // Coffeescript doesn't support do/while loops\n  while ((element = element.parentNode)) {\n    if (element === container) {\n      return true;\n    }\n  }\n  return false;\n};\n\n\nDropzone.getElement = function (el, name) {\n  let element;\n  if (typeof el === \"string\") {\n    element = document.querySelector(el);\n  } else if (el.nodeType != null) {\n    element = el;\n  }\n  if (element == null) {\n    throw new Error(`Invalid \\`${name}\\` option provided. Please provide a CSS selector or a plain HTML element.`);\n  }\n  return element;\n};\n\n\nDropzone.getElements = function (els, name) {\n  let el, elements;\n  if (els instanceof Array) {\n    elements = [];\n    try {\n      for (el of els) {\n        elements.push(this.getElement(el, name));\n      }\n    } catch (e) {\n      elements = null;\n    }\n  } else if (typeof els === \"string\") {\n    elements = [];\n    for (el of document.querySelectorAll(els)) {\n      elements.push(el);\n    }\n  } else if (els.nodeType != null) {\n    elements = [els];\n  }\n\n  if ((elements == null) || !elements.length) {\n    throw new Error(`Invalid \\`${name}\\` option provided. Please provide a CSS selector, a plain HTML element or a list of those.`);\n  }\n\n  return elements;\n};\n\n// Asks the user the question and calls accepted or rejected accordingly\n//\n// The default implementation just uses `window.confirm` and then calls the\n// appropriate callback.\nDropzone.confirm = function (question, accepted, rejected) {\n  if (window.confirm(question)) {\n    return accepted();\n  } else if (rejected != null) {\n    return rejected();\n  }\n};\n\n// Validates the mime type like this:\n//\n// https://developer.mozilla.org/en-US/docs/HTML/Element/input#attr-accept\nDropzone.isValidFile = function (file, acceptedFiles) {\n  if (!acceptedFiles) {\n    return true;\n  } // If there are no accepted mime types, it's OK\n  acceptedFiles = acceptedFiles.split(\",\");\n\n  let mimeType = file.type;\n  let baseMimeType = mimeType.replace(/\\/.*$/, \"\");\n\n  for (let validType of acceptedFiles) {\n    validType = validType.trim();\n    if (validType.charAt(0) === \".\") {\n      if (file.name.toLowerCase().indexOf(validType.toLowerCase(), file.name.length - validType.length) !== -1) {\n        return true;\n      }\n    } else if (/\\/\\*$/.test(validType)) {\n      // This is something like a image/* mime type\n      if (baseMimeType === validType.replace(/\\/.*$/, \"\")) {\n        return true;\n      }\n    } else {\n      if (mimeType === validType) {\n        return true;\n      }\n    }\n  }\n\n  return false;\n};\n\n// Augment jQuery\nif (typeof jQuery !== 'undefined' && jQuery !== null) {\n  jQuery.fn.dropzone = function (options) {\n    return this.each(function () {\n      return new Dropzone(this, options);\n    });\n  };\n}\n\n\nif (typeof module !== 'undefined' && module !== null) {\n  module.exports = Dropzone;\n} else {\n  window.Dropzone = Dropzone;\n}\n\n\n// Dropzone file status codes\nDropzone.ADDED = \"added\";\n\nDropzone.QUEUED = \"queued\";\n// For backwards compatibility. Now, if a file is accepted, it's either queued\n// or uploading.\nDropzone.ACCEPTED = Dropzone.QUEUED;\n\nDropzone.UPLOADING = \"uploading\";\nDropzone.PROCESSING = Dropzone.UPLOADING; // alias\n\nDropzone.CANCELED = \"canceled\";\nDropzone.ERROR = \"error\";\nDropzone.SUCCESS = \"success\";\n\n\n/*\n\n Bugfix for iOS 6 and 7\n Source: http://stackoverflow.com/questions/11929099/html5-canvas-drawimage-ratio-bug-ios\n based on the work of https://github.com/stomita/ios-imagefile-megapixel\n\n */\n\n// Detecting vertical squash in loaded image.\n// Fixes a bug which squash image vertically while drawing into canvas for some images.\n// This is a bug in iOS6 devices. This function from https://github.com/stomita/ios-imagefile-megapixel\nlet detectVerticalSquash = function (img) {\n  let iw = img.naturalWidth;\n  let ih = img.naturalHeight;\n  let canvas = document.createElement(\"canvas\");\n  canvas.width = 1;\n  canvas.height = ih;\n  let ctx = canvas.getContext(\"2d\");\n  ctx.drawImage(img, 0, 0);\n  let {data} = ctx.getImageData(1, 0, 1, ih);\n\n\n  // search image edge pixel position in case it is squashed vertically.\n  let sy = 0;\n  let ey = ih;\n  let py = ih;\n  while (py > sy) {\n    let alpha = data[((py - 1) * 4) + 3];\n\n    if (alpha === 0) {\n      ey = py;\n    } else {\n      sy = py;\n    }\n\n    py = (ey + sy) >> 1;\n  }\n  let ratio = (py / ih);\n\n  if (ratio === 0) {\n    return 1;\n  } else {\n    return ratio;\n  }\n};\n\n// A replacement for context.drawImage\n// (args are for source and destination).\nvar drawImageIOSFix = function (ctx, img, sx, sy, sw, sh, dx, dy, dw, dh) {\n  let vertSquashRatio = detectVerticalSquash(img);\n  return ctx.drawImage(img, sx, sy, sw, sh, dx, dy, dw, dh / vertSquashRatio);\n};\n\n\n// Based on MinifyJpeg\n// Source: http://www.perry.cz/files/ExifRestorer.js\n// http://elicon.blog57.fc2.com/blog-entry-206.html\nclass ExifRestore {\n  static initClass() {\n    this.KEY_STR = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';\n  }\n\n  static encode64(input) {\n    let output = '';\n    let chr1 = undefined;\n    let chr2 = undefined;\n    let chr3 = '';\n    let enc1 = undefined;\n    let enc2 = undefined;\n    let enc3 = undefined;\n    let enc4 = '';\n    let i = 0;\n    while (true) {\n      chr1 = input[i++];\n      chr2 = input[i++];\n      chr3 = input[i++];\n      enc1 = chr1 >> 2;\n      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);\n      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);\n      enc4 = chr3 & 63;\n      if (isNaN(chr2)) {\n        enc3 = (enc4 = 64);\n      } else if (isNaN(chr3)) {\n        enc4 = 64;\n      }\n      output = output + this.KEY_STR.charAt(enc1) + this.KEY_STR.charAt(enc2) + this.KEY_STR.charAt(enc3) + this.KEY_STR.charAt(enc4);\n      chr1 = (chr2 = (chr3 = ''));\n      enc1 = (enc2 = (enc3 = (enc4 = '')));\n      if (!(i < input.length)) {\n        break;\n      }\n    }\n    return output;\n  }\n\n  static restore(origFileBase64, resizedFileBase64) {\n    if (!origFileBase64.match('data:image/jpeg;base64,')) {\n      return resizedFileBase64;\n    }\n    let rawImage = this.decode64(origFileBase64.replace('data:image/jpeg;base64,', ''));\n    let segments = this.slice2Segments(rawImage);\n    let image = this.exifManipulation(resizedFileBase64, segments);\n    return `data:image/jpeg;base64,${this.encode64(image)}`;\n  }\n\n  static exifManipulation(resizedFileBase64, segments) {\n    let exifArray = this.getExifArray(segments);\n    let newImageArray = this.insertExif(resizedFileBase64, exifArray);\n    let aBuffer = new Uint8Array(newImageArray);\n    return aBuffer;\n  }\n\n  static getExifArray(segments) {\n    let seg = undefined;\n    let x = 0;\n    while (x < segments.length) {\n      seg = segments[x];\n      if ((seg[0] === 255) & (seg[1] === 225)) {\n        return seg;\n      }\n      x++;\n    }\n    return [];\n  }\n\n  static insertExif(resizedFileBase64, exifArray) {\n    let imageData = resizedFileBase64.replace('data:image/jpeg;base64,', '');\n    let buf = this.decode64(imageData);\n    let separatePoint = buf.indexOf(255, 3);\n    let mae = buf.slice(0, separatePoint);\n    let ato = buf.slice(separatePoint);\n    let array = mae;\n    array = array.concat(exifArray);\n    array = array.concat(ato);\n    return array;\n  }\n\n  static slice2Segments(rawImageArray) {\n    let head = 0;\n    let segments = [];\n    while (true) {\n      var length;\n      if ((rawImageArray[head] === 255) & (rawImageArray[head + 1] === 218)) {\n        break;\n      }\n      if ((rawImageArray[head] === 255) & (rawImageArray[head + 1] === 216)) {\n        head += 2;\n      } else {\n        length = (rawImageArray[head + 2] * 256) + rawImageArray[head + 3];\n        let endPoint = head + length + 2;\n        let seg = rawImageArray.slice(head, endPoint);\n        segments.push(seg);\n        head = endPoint;\n      }\n      if (head > rawImageArray.length) {\n        break;\n      }\n    }\n    return segments;\n  }\n\n  static decode64(input) {\n    let output = '';\n    let chr1 = undefined;\n    let chr2 = undefined;\n    let chr3 = '';\n    let enc1 = undefined;\n    let enc2 = undefined;\n    let enc3 = undefined;\n    let enc4 = '';\n    let i = 0;\n    let buf = [];\n    // remove all characters that are not A-Z, a-z, 0-9, +, /, or =\n    let base64test = /[^A-Za-z0-9\\+\\/\\=]/g;\n    if (base64test.exec(input)) {\n      console.warn('There were invalid base64 characters in the input text.\\nValid base64 characters are A-Z, a-z, 0-9, \\'+\\', \\'/\\',and \\'=\\'\\nExpect errors in decoding.');\n    }\n    input = input.replace(/[^A-Za-z0-9\\+\\/\\=]/g, '');\n    while (true) {\n      enc1 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc2 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc3 = this.KEY_STR.indexOf(input.charAt(i++));\n      enc4 = this.KEY_STR.indexOf(input.charAt(i++));\n      chr1 = (enc1 << 2) | (enc2 >> 4);\n      chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);\n      chr3 = ((enc3 & 3) << 6) | enc4;\n      buf.push(chr1);\n      if (enc3 !== 64) {\n        buf.push(chr2);\n      }\n      if (enc4 !== 64) {\n        buf.push(chr3);\n      }\n      chr1 = (chr2 = (chr3 = ''));\n      enc1 = (enc2 = (enc3 = (enc4 = '')));\n      if (!(i < input.length)) {\n        break;\n      }\n    }\n    return buf;\n  }\n}\nExifRestore.initClass();\n\n\n/*\n * contentloaded.js\n *\n * Author: Diego Perini (diego.perini at gmail.com)\n * Summary: cross-browser wrapper for DOMContentLoaded\n * Updated: 20101020\n * License: MIT\n * Version: 1.2\n *\n * URL:\n * http://javascript.nwbox.com/ContentLoaded/\n * http://javascript.nwbox.com/ContentLoaded/MIT-LICENSE\n */\n\n// @win window reference\n// @fn function reference\nlet contentLoaded = function (win, fn) {\n  let done = false;\n  let top = true;\n  let doc = win.document;\n  let root = doc.documentElement;\n  let add = (doc.addEventListener ? \"addEventListener\" : \"attachEvent\");\n  let rem = (doc.addEventListener ? \"removeEventListener\" : \"detachEvent\");\n  let pre = (doc.addEventListener ? \"\" : \"on\");\n  var init = function (e) {\n    if ((e.type === \"readystatechange\") && (doc.readyState !== \"complete\")) {\n      return;\n    }\n    ((e.type === \"load\" ? win : doc))[rem](pre + e.type, init, false);\n    if (!done && (done = true)) {\n      return fn.call(win, e.type || e);\n    }\n  };\n\n  var poll = function () {\n    try {\n      root.doScroll(\"left\");\n    } catch (e) {\n      setTimeout(poll, 50);\n      return;\n    }\n    return init(\"poll\");\n  };\n\n  if (doc.readyState !== \"complete\") {\n    if (doc.createEventObject && root.doScroll) {\n      try {\n        top = !win.frameElement;\n      } catch (error) {\n      }\n      if (top) {\n        poll();\n      }\n    }\n    doc[add](pre + \"DOMContentLoaded\", init, false);\n    doc[add](pre + \"readystatechange\", init, false);\n    return win[add](pre + \"load\", init, false);\n  }\n};\n\n\n// As a single function to be able to write tests.\nDropzone._autoDiscoverFunction = function () {\n  if (Dropzone.autoDiscover) {\n    return Dropzone.discover();\n  }\n};\ncontentLoaded(window, Dropzone._autoDiscoverFunction);\n\nfunction __guard__(value, transform) {\n  return (typeof value !== 'undefined' && value !== null) ? transform(value) : undefined;\n}\nfunction __guardMethod__(obj, methodName, transform) {\n  if (typeof obj !== 'undefined' && obj !== null && typeof obj[methodName] === 'function') {\n    return transform(obj, methodName);\n  } else {\n    return undefined;\n  }\n}"], "file": "dropzone.js"}