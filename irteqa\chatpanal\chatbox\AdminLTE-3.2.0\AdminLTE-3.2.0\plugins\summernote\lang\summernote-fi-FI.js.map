{"version": 3, "file": "lang/summernote-fi-FI.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,YADF;AAEJC,QAAAA,MAAM,EAAE,aAFJ;AAGJC,QAAAA,SAAS,EAAE,aAHP;AAIJC,QAAAA,KAAK,EAAE,mBAJH;AAKJC,QAAAA,MAAM,EAAE,UALJ;AAMJC,QAAAA,IAAI,EAAE,gBANF;AAOJC,QAAAA,aAAa,EAAE,YAPX;AAQJC,QAAAA,SAAS,EAAE,YARP;AASJC,QAAAA,WAAW,EAAE,YATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,MAAM,EAAE,YAFH;AAGLC,QAAAA,UAAU,EAAE,aAHP;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,aAAa,EAAE,kBALV;AAMLC,QAAAA,SAAS,EAAE,oBANN;AAOLC,QAAAA,UAAU,EAAE,kBAPP;AAQLC,QAAAA,SAAS,EAAE,eARN;AASLC,QAAAA,YAAY,EAAE,oBATT;AAULC,QAAAA,WAAW,EAAE,eAVR;AAWLC,QAAAA,cAAc,EAAE,uBAXX;AAYLC,QAAAA,SAAS,EAAE,qBAZN;AAaLC,QAAAA,aAAa,EAAE,iBAbV;AAcLC,QAAAA,eAAe,EAAE,sBAdZ;AAeLC,QAAAA,eAAe,EAAE,uBAfZ;AAgBLC,QAAAA,oBAAoB,EAAE,iCAhBjB;AAiBLC,QAAAA,GAAG,EAAE,sBAjBA;AAkBLC,QAAAA,MAAM,EAAE,aAlBH;AAmBLC,QAAAA,QAAQ,EAAE;AAnBL,OAbA;AAkCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,gBAFN;AAGLnB,QAAAA,MAAM,EAAE,aAHH;AAILe,QAAAA,GAAG,EAAE,mBAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAlCA;AAyCPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,QADF;AAEJrB,QAAAA,MAAM,EAAE,cAFJ;AAGJsB,QAAAA,MAAM,EAAE,eAHJ;AAIJC,QAAAA,IAAI,EAAE,SAJF;AAKJC,QAAAA,aAAa,EAAE,mBALX;AAMJT,QAAAA,GAAG,EAAE,mBAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OAzCC;AAkDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,UADF;AAELC,QAAAA,WAAW,EAAE,wBAFR;AAGLC,QAAAA,WAAW,EAAE,wBAHR;AAILC,QAAAA,UAAU,EAAE,kCAJP;AAKLC,QAAAA,WAAW,EAAE,gCALR;AAMLC,QAAAA,MAAM,EAAE,aANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAlDA;AA4DPC,MAAAA,EAAE,EAAE;AACFlC,QAAAA,MAAM,EAAE;AADN,OA5DG;AA+DPmC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,UAFE;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,GAAG,EAAE,OAJA;AAKLC,QAAAA,EAAE,EAAE,WALC;AAMLC,QAAAA,EAAE,EAAE,WANC;AAOLC,QAAAA,EAAE,EAAE,WAPC;AAQLC,QAAAA,EAAE,EAAE,WARC;AASLC,QAAAA,EAAE,EAAE,WATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OA/DA;AA2EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,2BADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA3EA;AA+EPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,MADC;AAEPC,QAAAA,UAAU,EAAE,aAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OA/EF;AAoFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,SADF;AAETC,QAAAA,OAAO,EAAE,qBAFA;AAGTC,QAAAA,MAAM,EAAE,qBAHC;AAITC,QAAAA,IAAI,EAAE,kBAJG;AAKTC,QAAAA,MAAM,EAAE,SALC;AAMTC,QAAAA,KAAK,EAAE,gBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OApFJ;AA6FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,gBADH;AAELC,QAAAA,IAAI,EAAE,cAFD;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,cAJP;AAKLC,QAAAA,WAAW,EAAE,YALR;AAMLC,QAAAA,cAAc,EAAE,qBANX;AAOLC,QAAAA,KAAK,EAAE,SAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA7FA;AAuGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,eADH;AAERC,QAAAA,KAAK,EAAE,OAFC;AAGRC,QAAAA,cAAc,EAAE,kBAHR;AAIRC,QAAAA,MAAM,EAAE,UAJA;AAKRC,QAAAA,mBAAmB,EAAE,oBALb;AAMRC,QAAAA,aAAa,EAAE;AANP,OAvGH;AA+GPzB,MAAAA,IAAI,EAAE;AACJ,2BAAmB,eADf;AAEJ,gBAAQ,yBAFJ;AAGJ,gBAAQ,+BAHJ;AAIJ,eAAO,SAJH;AAKJ,iBAAS,sBALL;AAMJ,gBAAQ,YANJ;AAOJ,kBAAU,UAPN;AAQJ,qBAAa,aART;AASJ,yBAAiB,YATb;AAUJ,wBAAgB,wBAVZ;AAWJ,uBAAe,kBAXX;AAYJ,yBAAiB,SAZb;AAaJ,wBAAgB,gBAbZ;AAcJ,uBAAe,OAdX;AAeJ,+BAAuB,mCAfnB;AAgBJ,6BAAqB,iBAhBjB;AAiBJ,mBAAW,qBAjBP;AAkBJ,kBAAU,qBAlBN;AAmBJ,sBAAc,6BAnBV;AAoBJ,oBAAY,8BApBR;AAqBJ,oBAAY,8BArBR;AAsBJ,oBAAY,8BAtBR;AAuBJ,oBAAY,8BAvBR;AAwBJ,oBAAY,8BAxBR;AAyBJ,oBAAY,8BAzBR;AA0BJ,gCAAwB,kBA1BpB;AA2BJ,2BAAmB;AA3Bf,OA/GC;AA4IP0B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA5IF;AAgJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,eADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAhJN;AADiB,GAA5B;AAuJD,CAxJD,EAwJGC,MAxJH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-fi-FI.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'fi-FI': {\n      font: {\n        bold: 'Lihavointi',\n        italic: 'Kursivointi',\n        underline: 'Alleviivaus',\n        clear: '<PERSON><PERSON>je<PERSON><PERSON> muotoilu',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        strikethrough: 'Y<PERSON><PERSON><PERSON><PERSON>',\n        subscript: '<PERSON><PERSON><PERSON>',\n        superscript: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n        size: 'Kir<PERSON><PERSON><PERSON>',\n      },\n      image: {\n        image: '<PERSON><PERSON>',\n        insert: 'Lisää kuva',\n        resizeFull: '<PERSON><PERSON> leveys',\n        resizeHalf: 'Puolik<PERSON> leveys',\n        resizeQuarter: 'Neljäsosa leveys',\n        floatLeft: 'Si<PERSON>ita vasemmalle',\n        floatRight: '<PERSON><PERSON>ita oikealle',\n        floatNone: 'Ei sijoitusta',\n        shapeRounded: 'Muoto: Pyöristetty',\n        shapeCircle: 'Muoto: Ympyrä',\n        shapeThumbnail: 'Muoto: Esikatselukuva',\n        shapeNone: 'Muoto: Ei muotoilua',\n        dragImageHere: 'Vedä kuva tähän',\n        selectFromFiles: 'Valitse tiedostoista',\n        maximumFileSize: '<PERSON><PERSON><PERSON><PERSON> tiedosto koko',\n        maximumFileSizeError: 'Maksimi tiedosto koko ylitetty.',\n        url: 'URL-osoitteen mukaan',\n        remove: 'Poista kuva',\n        original: 'Alkuperäinen',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Linkki videoon',\n        insert: 'Lisää video',\n        url: 'Videon URL-osoite',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion tai Youku)',\n      },\n      link: {\n        link: 'Linkki',\n        insert: 'Lisää linkki',\n        unlink: 'Poista linkki',\n        edit: 'Muokkaa',\n        textToDisplay: 'Näytettävä teksti',\n        url: 'Linkin URL-osoite',\n        openInNewWindow: 'Avaa uudessa ikkunassa',\n      },\n      table: {\n        table: 'Taulukko',\n        addRowAbove: 'Lisää rivi yläpuolelle',\n        addRowBelow: 'Lisää rivi alapuolelle',\n        addColLeft: 'Lisää sarake vasemmalle puolelle',\n        addColRight: 'Lisää sarake oikealle puolelle',\n        delRow: 'Poista rivi',\n        delCol: 'Poista sarake',\n        delTable: 'Poista taulukko',\n      },\n      hr: {\n        insert: 'Lisää vaakaviiva',\n      },\n      style: {\n        style: 'Tyyli',\n        p: 'Normaali',\n        blockquote: 'Lainaus',\n        pre: 'Koodi',\n        h1: 'Otsikko 1',\n        h2: 'Otsikko 2',\n        h3: 'Otsikko 3',\n        h4: 'Otsikko 4',\n        h5: 'Otsikko 5',\n        h6: 'Otsikko 6',\n      },\n      lists: {\n        unordered: 'Luettelomerkitty luettelo',\n        ordered: 'Numeroitu luettelo',\n      },\n      options: {\n        help: 'Ohje',\n        fullscreen: 'Koko näyttö',\n        codeview: 'HTML-näkymä',\n      },\n      paragraph: {\n        paragraph: 'Kappale',\n        outdent: 'Pienennä sisennystä',\n        indent: 'Suurenna sisennystä',\n        left: 'Tasaa vasemmalle',\n        center: 'Keskitä',\n        right: 'Tasaa oikealle',\n        justify: 'Tasaa',\n      },\n      color: {\n        recent: 'Viimeisin väri',\n        more: 'Lisää värejä',\n        background: 'Korostusväri',\n        foreground: 'Tekstin väri',\n        transparent: 'Läpinäkyvä',\n        setTransparent: 'Aseta läpinäkyväksi',\n        reset: 'Palauta',\n        resetToDefault: 'Palauta oletusarvoksi',\n      },\n      shortcut: {\n        shortcuts: 'Pikanäppäimet',\n        close: 'Sulje',\n        textFormatting: 'Tekstin muotoilu',\n        action: 'Toiminto',\n        paragraphFormatting: 'Kappaleen muotoilu',\n        documentStyle: 'Asiakirjan tyyli',\n      },\n      help: {\n        'insertParagraph': 'Lisää kappale',\n        'undo': 'Kumoa viimeisin komento',\n        'redo': 'Tee uudelleen kumottu komento',\n        'tab': 'Sarkain',\n        'untab': 'Sarkainmerkin poisto',\n        'bold': 'Lihavointi',\n        'italic': 'Kursiivi',\n        'underline': 'Alleviivaus',\n        'strikethrough': 'Yliviivaus',\n        'removeFormat': 'Poista asetetut tyylit',\n        'justifyLeft': 'Tasaa vasemmalle',\n        'justifyCenter': 'Keskitä',\n        'justifyRight': 'Tasaa oikealle',\n        'justifyFull': 'Tasaa',\n        'insertUnorderedList': 'Luettelomerkillä varustettu lista',\n        'insertOrderedList': 'Numeroitu lista',\n        'outdent': 'Pienennä sisennystä',\n        'indent': 'Suurenna sisennystä',\n        'formatPara': 'Muuta kappaleen formaatti p',\n        'formatH1': 'Muuta kappaleen formaatti H1',\n        'formatH2': 'Muuta kappaleen formaatti H2',\n        'formatH3': 'Muuta kappaleen formaatti H3',\n        'formatH4': 'Muuta kappaleen formaatti H4',\n        'formatH5': 'Muuta kappaleen formaatti H5',\n        'formatH6': 'Muuta kappaleen formaatti H6',\n        'insertHorizontalRule': 'Lisää vaakaviiva',\n        'linkDialog.show': 'Lisää linkki',\n      },\n      history: {\n        undo: 'Kumoa',\n        redo: 'Toista',\n      },\n      specialChar: {\n        specialChar: 'ERIKOISMERKIT',\n        select: 'Valitse erikoismerkit',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}