<?php
/**
 * ملف لاختبار صفحة الملف الشخصي
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$test_results = [];
$errors = [];

// اختبار 1: فحص وجود ملف الملف الشخصي
$test_results['profile_file_exists'] = file_exists(__DIR__ . '/profile.php');

// اختبار 2: فحص بنية جدول المستخدمين
try {
    $stmt = $main_db->prepare("DESCRIBE users");
    $stmt->execute();
    $result = $stmt->get_result();
    $columns = [];
    while ($row = $result->fetch_assoc()) {
        $columns[] = $row['Field'];
    }
    
    $required_columns = ['id', 'username', 'full_name', 'email', 'password', 'created_at'];
    $test_results['user_table_structure'] = [];
    foreach ($required_columns as $column) {
        $test_results['user_table_structure'][$column] = in_array($column, $columns);
    }
    
    $test_results['all_columns_exist'] = !in_array(false, $test_results['user_table_structure']);
    
} catch (Exception $e) {
    $errors[] = "خطأ في فحص بنية جدول المستخدمين: " . $e->getMessage();
    $test_results['user_table_structure'] = [];
    $test_results['all_columns_exist'] = false;
}

// اختبار 3: فحص بيانات المستخدم الحالي
try {
    $stmt = $main_db->prepare("SELECT id, username, full_name, email, created_at FROM users WHERE id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user_data = $result->fetch_assoc();
    
    $test_results['user_data_available'] = ($user_data !== null);
    $test_results['user_data'] = $user_data;
    
} catch (Exception $e) {
    $errors[] = "خطأ في جلب بيانات المستخدم: " . $e->getMessage();
    $test_results['user_data_available'] = false;
    $test_results['user_data'] = null;
}

// اختبار 4: فحص الروابط في الهيدر
$header_content = file_get_contents(__DIR__ . '/includes/header.php');
$test_results['profile_link_in_header'] = strpos($header_content, 'profile.php') !== false;

// اختبار 5: فحص الترجمات المطلوبة
$ar_translations = require __DIR__ . '/languages/ar/lang.php';
$en_translations = require __DIR__ . '/languages/en/lang.php';

$required_translations = ['profile', 'full_name', 'email', 'current_password', 'new_password', 'confirm_password'];
$test_results['translations'] = [];
foreach ($required_translations as $key) {
    $test_results['translations'][$key] = [
        'ar' => isset($ar_translations[$key]),
        'en' => isset($en_translations[$key])
    ];
}

// حساب النتيجة الإجمالية
$total_score = 0;
$max_score = 0;

// نقاط وجود الملف (20 نقطة)
if ($test_results['profile_file_exists']) $total_score += 20;
$max_score += 20;

// نقاط بنية الجدول (30 نقطة)
if ($test_results['all_columns_exist']) $total_score += 30;
$max_score += 30;

// نقاط بيانات المستخدم (20 نقطة)
if ($test_results['user_data_available']) $total_score += 20;
$max_score += 20;

// نقاط الرابط في الهيدر (10 نقاط)
if ($test_results['profile_link_in_header']) $total_score += 10;
$max_score += 10;

// نقاط الترجمات (20 نقطة)
$translation_score = 0;
foreach ($test_results['translations'] as $translation) {
    if ($translation['ar'] && $translation['en']) {
        $translation_score += 20 / count($required_translations);
    }
}
$total_score += $translation_score;
$max_score += 20;

$overall_percentage = ($total_score / $max_score) * 100;

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار صفحة الملف الشخصي</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات الاختبار</h5>
        <p>هذا الاختبار يتحقق من جاهزية صفحة الملف الشخصي:</p>
        <ul>
            <li>وجود ملف الملف الشخصي</li>
            <li>بنية جدول المستخدمين</li>
            <li>توفر بيانات المستخدم</li>
            <li>الروابط والترجمات</li>
        </ul>
    </div>
    
    <!-- النتيجة الإجمالية -->
    <div class="card mb-4">
        <div class="card-header <?php echo $overall_percentage >= 90 ? 'bg-success' : ($overall_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?> text-white">
            <h4 class="mb-0">
                <i class="fas fa-chart-pie"></i>
                النتيجة الإجمالية: <?php echo number_format($overall_percentage, 1); ?>%
            </h4>
        </div>
        <div class="card-body">
            <div class="progress mb-3" style="height: 30px;">
                <div class="progress-bar <?php echo $overall_percentage >= 90 ? 'bg-success' : ($overall_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?>" 
                     style="width: <?php echo $overall_percentage; ?>%">
                    <?php echo number_format($total_score, 1); ?>/<?php echo $max_score; ?> (<?php echo number_format($overall_percentage, 1); ?>%)
                </div>
            </div>
            
            <?php if ($overall_percentage >= 90): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ممتاز! صفحة الملف الشخصي جاهزة للاستخدام
                </div>
            <?php elseif ($overall_percentage >= 70): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> جيد! الصفحة تعمل مع بعض المشاكل البسيطة
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i> يحتاج إلى إصلاح! هناك مشاكل تحتاج إلى حل
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <?php if (!empty($errors)): ?>
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">
                <i class="fas fa-times-circle"></i>
                أخطاء حدثت أثناء الاختبار
            </h5>
        </div>
        <div class="card-body">
            <ul class="list-group">
                <?php foreach ($errors as $error): ?>
                <li class="list-group-item list-group-item-danger">
                    <i class="fas fa-times text-danger"></i> <?php echo htmlspecialchars($error); ?>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
    
    <div class="row">
        <!-- اختبار الملفات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header <?php echo $test_results['profile_file_exists'] ? 'bg-success' : 'bg-danger'; ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-code"></i>
                        الملفات (20/20)
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>ملف الملف الشخصي:</span>
                        <span class="badge <?php echo $test_results['profile_file_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['profile_file_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>رابط في الهيدر:</span>
                        <span class="badge <?php echo $test_results['profile_link_in_header'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['profile_link_in_header'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار قاعدة البيانات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header <?php echo $test_results['all_columns_exist'] ? 'bg-success' : 'bg-danger'; ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-database"></i>
                        قاعدة البيانات (30/30)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($test_results['user_table_structure'])): ?>
                        <?php foreach ($test_results['user_table_structure'] as $column => $exists): ?>
                        <div class="d-flex justify-content-between mb-2">
                            <span><?php echo $column; ?>:</span>
                            <span class="badge <?php echo $exists ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $exists ? '✓' : '✗'; ?>
                            </span>
                        </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="alert alert-danger">لا يمكن فحص بنية الجدول</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- بيانات المستخدم -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header <?php echo $test_results['user_data_available'] ? 'bg-success' : 'bg-danger'; ?> text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-user"></i>
                        بيانات المستخدم (20/20)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($test_results['user_data_available'] && $test_results['user_data']): ?>
                        <table class="table table-sm">
                            <tr>
                                <td><strong>المعرف:</strong></td>
                                <td><?php echo htmlspecialchars($test_results['user_data']['id']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>اسم المستخدم:</strong></td>
                                <td><?php echo htmlspecialchars($test_results['user_data']['username']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>الاسم الكامل:</strong></td>
                                <td><?php echo htmlspecialchars($test_results['user_data']['full_name']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td><?php echo htmlspecialchars($test_results['user_data']['email']); ?></td>
                            </tr>
                        </table>
                    <?php else: ?>
                        <div class="alert alert-danger">لا يمكن جلب بيانات المستخدم</div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- الترجمات -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-language"></i>
                        الترجمات (<?php echo number_format($translation_score, 1); ?>/20)
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($test_results['translations'] as $key => $translation): ?>
                    <div class="mb-2">
                        <strong><?php echo $key; ?>:</strong>
                        <div class="d-flex justify-content-between">
                            <span>عربي:</span>
                            <span class="badge <?php echo $translation['ar'] ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $translation['ar'] ? '✓' : '✗'; ?>
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>إنجليزي:</span>
                            <span class="badge <?php echo $translation['en'] ? 'bg-success' : 'bg-danger'; ?>">
                                <?php echo $translation['en'] ? '✓' : '✗'; ?>
                            </span>
                        </div>
                    </div>
                    <hr>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الإجراءات -->
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">الإجراءات والاختبارات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6>اختبار الصفحة:</h6>
                    <div class="d-grid gap-2">
                        <?php if ($test_results['profile_file_exists']): ?>
                        <a href="profile.php" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-user-circle"></i> فتح الملف الشخصي
                        </a>
                        <?php else: ?>
                        <button class="btn btn-outline-secondary btn-sm" disabled>
                            <i class="fas fa-times"></i> الملف غير موجود
                        </button>
                        <?php endif; ?>
                        
                        <a href="test_profile_page.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-redo"></i> إعادة الاختبار
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>إدارة المستخدم:</h6>
                    <div class="d-grid gap-2">
                        <a href="register.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-user-plus"></i> تسجيل مستخدم جديد
                        </a>
                        <a href="logout.php" class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>العودة:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="test_all_fixes.php" class="btn btn-info btn-sm">
                            <i class="fas fa-clipboard-check"></i> الاختبار الشامل
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
