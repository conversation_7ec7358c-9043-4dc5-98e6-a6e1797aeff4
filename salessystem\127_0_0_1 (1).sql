-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: 15 يونيو 2025 الساعة 04:22
-- إصدار الخادم: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `sales_system`
--
CREATE DATABASE IF NOT EXISTS `u193708811_salesbeta` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `u193708811_salesbeta`;
--
-- Database: `sales_system_main`
--
CREATE DATABASE IF NOT EXISTS `sales_system_main` DEFAULT CHARACTER SET utf8 COLLATE utf8_unicode_ci;
USE `sales_system_main`;

-- --------------------------------------------------------

--
-- بنية الجدول `activity_log`
--

CREATE TABLE `activity_log` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_type` enum('user','admin') DEFAULT 'user',
  `action` varchar(100) NOT NULL,
  `table_name` varchar(50) DEFAULT NULL,
  `record_id` int(11) DEFAULT NULL,
  `old_data` text DEFAULT NULL,
  `new_data` text DEFAULT NULL,
  `description` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `activity_log`
--

INSERT INTO `activity_log` (`id`, `user_id`, `user_type`, `action`, `table_name`, `record_id`, `old_data`, `new_data`, `description`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 1, 'user', 'user_login', 'users', 1, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 01:34:11'),
(2, 1, 'user', 'user_login', 'users', 1, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 01:53:20'),
(3, 1, 'admin', 'admin_created', NULL, NULL, NULL, NULL, 'تم إنشاء المدير الافتراضي تلقائياً', '::1', NULL, '2025-06-13 02:15:13'),
(4, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:15:32'),
(5, 1, 'admin', 'sale_create', 'sales', 2, NULL, '{\"invoice_number\":\"INV-20250613-684B8A4F027AD\",\"total_amount\":6670}', 'إنشاء فاتورة مبيعات جديدة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:17:51'),
(6, NULL, 'user', 'user_register', 'users', 2, NULL, '{\"username\":\"user03\",\"full_name\":\"user03\",\"email\":\"<EMAIL>\"}', 'تسجيل مستخدم جديد', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:18:39'),
(7, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:18:50'),
(8, 1, 'admin', 'user_login', 'users', 1, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:22:19'),
(9, NULL, 'user', 'admin_login_failed', 'admins', NULL, NULL, '{\"username\":\"alomari\"}', 'محاولة تسجيل دخول بحساب غير موجود', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:22:57'),
(10, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:23:04'),
(11, 1, 'admin', 'admin_logout', 'admins', 1, NULL, NULL, 'تسجيل خروج المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:27:14'),
(12, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:27:21'),
(13, 1, 'admin', 'admin_logout', 'admins', 1, NULL, NULL, 'تسجيل خروج المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:30:06'),
(14, 1, 'user', 'user_login', 'users', 1, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 02:56:44'),
(15, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:23:25'),
(16, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:28:20'),
(17, 1, 'admin', 'user_status_changed', 'users', 2, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:33:55'),
(18, 1, 'admin', 'user_login', 'users', 2, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:34:45'),
(19, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:35:50'),
(20, 1, 'admin', 'user_status_changed', 'users', 2, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:40:36'),
(21, 1, 'admin', 'user_status_changed', 'users', 2, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:40:43'),
(22, 1, 'admin', 'user_status_changed', 'users', 2, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:40:48'),
(23, 1, 'admin', 'user_status_changed', 'users', 2, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:44:41'),
(24, 1, 'admin', 'user_login', 'users', 2, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:47:59'),
(25, 1, 'admin', 'user_status_changed', 'users', 2, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 03:57:26'),
(26, 1, 'admin', 'user_status_changed', 'users', 2, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 04:32:38'),
(27, 1, 'admin', 'user_status_changed', 'users', 2, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 04:32:45'),
(28, 1, 'admin', 'user_deleted', 'users', 2, NULL, NULL, 'حذف المستخدم وقاعدة بياناته', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 04:46:39'),
(29, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 05:07:04'),
(30, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 05:07:34'),
(31, 1, 'admin', 'user_register', 'users', 3, NULL, '{\"username\":\"user03\",\"full_name\":\"user03\",\"email\":\"<EMAIL>\"}', 'تسجيل مستخدم جديد', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 05:42:38'),
(32, 1, 'admin', 'user_login', 'users', 3, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 05:42:42'),
(33, 1, 'admin', 'sale_create', 'sales', 1, NULL, '{\"invoice_number\":\"INV-20250613-684BBAC4A74D5\",\"total_amount\":25300}', 'إنشاء فاتورة مبيعات جديدة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 05:44:36'),
(34, 1, 'admin', 'user_status_changed', 'users', 3, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 05:58:55'),
(35, 1, 'admin', 'user_status_changed', 'users', 3, NULL, NULL, 'تغيير حالة المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 05:59:08'),
(36, 1, 'admin', 'password_reset', 'users', 3, NULL, NULL, 'تم إعادة تعيين كلمة مرور المستخدم ID: 3', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 05:59:33'),
(37, 1, 'admin', 'password_reset', 'users', 3, NULL, NULL, 'تم إعادة تعيين كلمة مرور المستخدم ID: 3', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 05:59:45'),
(38, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 18:54:32'),
(39, 1, 'admin', 'user_login', 'users', 1, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:11:35'),
(40, 1, 'admin', 'purchase_create', 'purchases', 3, NULL, '{\"invoice_number\":\"PUR-20250613-684C7C1D725E7\",\"total_amount\":6670}', 'إنشاء فاتورة مشتريات جديدة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:29:33'),
(41, 1, 'admin', 'purchase_create', 'purchases', 4, NULL, '{\"invoice_number\":\"PUR-20250613-684C7C2CB3453\",\"total_amount\":6670}', 'إنشاء فاتورة مشتريات جديدة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:29:48'),
(42, 1, 'admin', 'purchase_create', 'purchases', 5, NULL, '{\"invoice_number\":\"PUR-20250613-684C7C7CA9F65\",\"total_amount\":13340}', 'إنشاء فاتورة مشتريات جديدة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:31:08'),
(43, 1, 'admin', 'sale_create', 'sales', 3, NULL, '{\"invoice_number\":\"INV-20250613-684C7CDB97841\",\"total_amount\":17595}', 'إنشاء فاتورة مبيعات جديدة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:32:43'),
(44, 1, 'admin', 'purchase_create', 'purchases', 6, NULL, '{\"invoice_number\":\"PUR-20250613-684C7D0E84255\",\"total_amount\":17250}', 'إنشاء فاتورة مشتريات جديدة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:33:34'),
(45, 1, 'admin', 'sale_create', 'sales', 4, NULL, '{\"invoice_number\":\"INV-20250613-684C7D5240D58\",\"total_amount\":63250}', 'إنشاء فاتورة مبيعات جديدة', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:34:42'),
(46, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:36:27'),
(47, 1, 'admin', 'password_reset', 'users', 3, NULL, NULL, 'تم إعادة تعيين كلمة مرور المستخدم ID: 3', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:37:21'),
(48, 1, 'admin', 'user_login', 'users', 3, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:37:42'),
(49, 3, 'user', 'user_login', 'users', 3, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:41:16'),
(50, 3, 'user', 'user_login', 'users', 3, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:45:28'),
(51, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:45:43'),
(52, 1, 'admin', 'admin_created', 'admins', 2, NULL, NULL, 'تم إنشاء مدير جديد: مؤسسة العمري', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:46:59'),
(53, 2, 'admin', 'admin_login', 'admins', 2, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:55:29'),
(54, 2, 'admin', 'admin_logout', 'admins', 2, NULL, NULL, 'تسجيل خروج المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:56:37'),
(55, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 19:56:44'),
(56, 1, 'admin', 'user_login', 'users', 1, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 20:28:07'),
(57, 1, 'user', 'user_login', 'users', 1, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-13 23:15:46'),
(58, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-14 05:11:46'),
(59, 1, 'admin', 'product_create', 'products', 5, NULL, '{\"name\":\"\\u0645\\u0648\\u0633\\u0633\\u0629 \\u0627\\u0645\\u0627\\u0645 \\u0635\\u0627\\u0628\\u0631\",\"price\":5500,\"tax_rate\":15}', 'إضافة منتج جديد', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-14 05:41:22'),
(60, 1, 'user', 'user_login', 'users', 1, NULL, NULL, 'تسجيل دخول المستخدم', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-14 21:00:31'),
(61, 1, 'user', 'customer_create', 'customers', 4, NULL, '{\"name\":\"smartwebkeys\",\"customer_type\":\"supplier\"}', 'إضافة مورد جديد', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 00:01:16'),
(62, 1, 'admin', 'admin_login', 'admins', 1, NULL, NULL, 'تسجيل دخول المدير', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-15 01:26:42');

-- --------------------------------------------------------

--
-- بنية الجدول `admins`
--

CREATE TABLE `admins` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `permissions` text DEFAULT NULL,
  `is_super_admin` tinyint(1) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `admins`
--

INSERT INTO `admins` (`id`, `username`, `password`, `full_name`, `email`, `phone`, `permissions`, `is_super_admin`, `is_active`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$uNQ5.zKrHOk3KM60NckwBOSGfVXN1sM5jFhk0uBkiUgA5WBYqk6Pe', 'المدير العام', '<EMAIL>', NULL, '{\"manage_users\":true,\"view_all_data\":true,\"manage_system\":true,\"view_reports\":true,\"manage_admins\":true}', 1, 1, '2025-06-15 01:26:42', '2025-06-13 02:15:13', '2025-06-15 01:26:42'),
(2, 'admin2', '$2y$10$Af.NopeUSDj7bw7gF7IboOiyxHeJBrPqmdHJgI8BnZU84U6YCbyvS', 'مؤسسة العمري', '<EMAIL>', NULL, '[\"view_all_data\",\"view_reports\"]', 0, 1, '2025-06-13 19:55:29', '2025-06-13 19:46:59', '2025-06-13 19:55:29');

-- --------------------------------------------------------

--
-- بنية الجدول `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `full_name`, `email`, `phone`, `is_active`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'alomari', '$2y$10$HbAeqrdoBxGarMxdQN3sMeTsY3dfRKlnaKF6OTyb7k4Xwe0oKeyOG', 'مؤسسة العمري', '<EMAIL>', NULL, 1, '2025-06-14 21:00:31', '2025-06-13 00:09:34', '2025-06-14 21:00:31'),
(3, 'user03', '$2y$10$bCa6Pz2VOtzAUitcusRW8ua4JJa6l9NMlXT6B0.BhOdvv5wezJzs.', 'user03', '<EMAIL>', NULL, 1, '2025-06-13 19:45:28', '2025-06-13 05:42:37', '2025-06-13 19:45:28');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `activity_log`
--
ALTER TABLE `activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `user_type` (`user_type`),
  ADD KEY `action` (`action`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_admin_username` (`username`),
  ADD UNIQUE KEY `idx_admin_email` (`email`),
  ADD KEY `idx_admin_active` (`is_active`),
  ADD KEY `idx_admin_super` (`is_super_admin`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `idx_username` (`username`),
  ADD UNIQUE KEY `idx_email` (`email`),
  ADD KEY `idx_active` (`is_active`),
  ADD KEY `idx_created` (`created_at`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `activity_log`
--
ALTER TABLE `activity_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=63;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;
--
-- Database: `sales_system_user_1`
--
CREATE DATABASE IF NOT EXISTS `sales_system_user_1` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `sales_system_user_1`;

-- --------------------------------------------------------

--
-- بنية الجدول `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `customers`
--

INSERT INTO `customers` (`id`, `name`, `phone`, `email`, `tax_number`, `address`, `customer_type`, `created_at`, `updated_at`) VALUES
(1, 'موسسة امام صابر', '055555555555', '<EMAIL>', '30301000000000', '', 'customer', '2025-06-13 00:09:58', '2025-06-13 00:09:58'),
(3, 'محمد ygd', '0120000000', '<EMAIL>', '*********', '', 'customer', '2025-06-13 19:31:44', '2025-06-13 19:31:44'),
(4, 'smartwebkeys', '966597291422', '<EMAIL>', '3555888800', '', 'supplier', '2025-06-15 00:01:16', '2025-06-15 00:01:16');

-- --------------------------------------------------------

--
-- بنية الجدول `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
  `stock_quantity` decimal(10,2) DEFAULT 0.00,
  `unit` varchar(50) DEFAULT 'قطعة',
  `barcode` varchar(100) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `products`
--

INSERT INTO `products` (`id`, `name`, `description`, `price`, `tax_rate`, `stock_quantity`, `unit`, `barcode`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'تلفونات', NULL, 5800.00, 15.00, 0.00, 'قطعة', NULL, NULL, 1, '2025-06-13 00:11:08', '2025-06-13 00:11:08'),
(2, 'جوال', NULL, 13500.00, 15.00, 0.00, 'قطعة', NULL, NULL, 1, '2025-06-13 19:32:10', '2025-06-13 19:32:10'),
(3, 'شواحن', NULL, 0.00, 15.00, 0.00, 'قطعة', NULL, NULL, 1, '2025-06-13 19:33:21', '2025-06-13 19:33:21'),
(4, 'كمبيوتر', NULL, 0.00, 15.00, 0.00, 'قطعة', NULL, NULL, 1, '2025-06-13 19:34:24', '2025-06-13 19:34:24'),
(5, 'موسسة امام صابر', '', 5500.00, 15.00, 0.00, 'قطعة', NULL, 'الكترونيا', 1, '2025-06-14 05:41:22', '2025-06-14 05:41:22');

-- --------------------------------------------------------

--
-- بنية الجدول `purchases`
--

CREATE TABLE `purchases` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `supplier_name` varchar(255) DEFAULT NULL,
  `supplier_phone` varchar(20) DEFAULT NULL,
  `supplier_email` varchar(255) DEFAULT NULL,
  `supplier_tax_number` varchar(50) DEFAULT NULL,
  `invoice_number` varchar(50) NOT NULL,
  `date` date NOT NULL,
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `payment_method` varchar(50) DEFAULT 'نقدي',
  `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `purchases`
--

INSERT INTO `purchases` (`id`, `customer_id`, `supplier_name`, `supplier_phone`, `supplier_email`, `supplier_tax_number`, `invoice_number`, `date`, `subtotal`, `tax_amount`, `total_amount`, `discount_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`) VALUES
(1, 2, NULL, NULL, NULL, NULL, 'PUR-20250613-684B6D51A82FD', '2025-06-13', 10000.00, 1500.00, 11500.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 00:14:09', '2025-06-13 00:14:09'),
(2, 1, NULL, NULL, NULL, NULL, 'PUR-20250613-684C77FC0B2D8', '2025-06-13', 5800.00, 870.00, 6670.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 19:11:56', '2025-06-13 19:11:56'),
(3, 1, NULL, NULL, NULL, NULL, 'PUR-20250613-684C7C1D725E7', '2025-06-13', 5800.00, 870.00, 6670.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 19:29:33', '2025-06-13 19:29:33'),
(4, 1, NULL, NULL, NULL, NULL, 'PUR-20250613-684C7C2CB3453', '2025-06-13', 5800.00, 870.00, 6670.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 19:29:48', '2025-06-13 19:29:48'),
(5, 1, NULL, NULL, NULL, NULL, 'PUR-20250613-684C7C7CA9F65', '2025-06-13', 11600.00, 1740.00, 13340.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 19:31:08', '2025-06-13 19:31:08'),
(6, 0, NULL, NULL, NULL, NULL, 'PUR-20250613-684C7D0E84255', '2025-06-13', 15000.00, 2250.00, 17250.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 19:33:34', '2025-06-13 19:33:34');

-- --------------------------------------------------------

--
-- بنية الجدول `purchase_items`
--

CREATE TABLE `purchase_items` (
  `id` int(11) NOT NULL,
  `purchase_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_name` varchar(255) NOT NULL DEFAULT '',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
  `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `purchase_items`
--

INSERT INTO `purchase_items` (`id`, `purchase_id`, `product_id`, `product_name`, `quantity`, `unit_price`, `price`, `tax_rate`, `tax_amount`, `total_price`, `discount_amount`, `created_at`) VALUES
(1, 3, 1, 'تلفونات', 1.00, 5800.00, 0.00, 15.00, 870.00, 6670.00, 0.00, '2025-06-13 19:29:33'),
(2, 4, 1, 'تلفونات', 1.00, 5800.00, 0.00, 15.00, 870.00, 6670.00, 0.00, '2025-06-13 19:29:48'),
(3, 5, 1, 'تلفونات', 1.00, 5800.00, 0.00, 15.00, 870.00, 6670.00, 0.00, '2025-06-13 19:31:08'),
(4, 5, 1, 'تلفونات', 1.00, 5800.00, 0.00, 15.00, 870.00, 6670.00, 0.00, '2025-06-13 19:31:08'),
(5, 6, 3, 'شواحن', 1.00, 15000.00, 0.00, 15.00, 2250.00, 17250.00, 0.00, '2025-06-13 19:33:34');

-- --------------------------------------------------------

--
-- بنية الجدول `sales`
--

CREATE TABLE `sales` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `invoice_number` varchar(50) NOT NULL,
  `date` date NOT NULL,
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `payment_method` varchar(50) DEFAULT 'نقدي',
  `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `sales`
--

INSERT INTO `sales` (`id`, `customer_id`, `invoice_number`, `date`, `subtotal`, `tax_amount`, `total_amount`, `discount_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`) VALUES
(1, 1, 'INV-20250613-684B6CD574D20', '2025-06-13', 11600.00, 1740.00, 13340.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 00:12:05', '2025-06-13 00:12:05'),
(2, 1, 'INV-20250613-684B8A4F027AD', '2025-06-13', 5800.00, 870.00, 6670.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 02:17:51', '2025-06-13 02:17:51'),
(3, 3, 'INV-20250613-684C7CDB97841', '2025-06-13', 15300.00, 2295.00, 17595.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 19:32:43', '2025-06-13 19:32:43'),
(4, 1, 'INV-20250613-684C7D5240D58', '2025-06-13', 55000.00, 8250.00, 63250.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 19:34:42', '2025-06-13 19:34:42'),
(5, 1, 'S20250614071101248', '2025-06-14', 13500.00, 2025.00, 15525.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-14 05:11:01', '2025-06-14 05:11:01'),
(6, 1, 'S20250614231132236', '2025-06-14', 5800.00, 870.00, 6670.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-14 21:11:32', '2025-06-14 21:11:32'),
(7, 1, 'S20250615014034591', '2025-06-15', 19300.00, 2895.00, 22195.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-14 23:40:34', '2025-06-14 23:40:34');

-- --------------------------------------------------------

--
-- بنية الجدول `sale_items`
--

CREATE TABLE `sale_items` (
  `id` int(11) NOT NULL,
  `sale_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_name` varchar(255) NOT NULL DEFAULT '',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
  `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `sale_items`
--

INSERT INTO `sale_items` (`id`, `sale_id`, `product_id`, `product_name`, `quantity`, `unit_price`, `tax_rate`, `tax_amount`, `total_price`, `discount_amount`, `created_at`) VALUES
(3, 1, 1, '', 1.00, 5800.00, 15.00, 870.00, 6670.00, 0.00, '2025-06-13 00:12:12'),
(4, 1, 1, '', 1.00, 5800.00, 15.00, 870.00, 6670.00, 0.00, '2025-06-13 00:12:12'),
(5, 2, 1, '', 1.00, 5800.00, 15.00, 870.00, 6670.00, 0.00, '2025-06-13 02:17:51'),
(6, 3, 2, '', 1.00, 15300.00, 15.00, 2295.00, 17595.00, 0.00, '2025-06-13 19:32:43'),
(7, 4, 4, '', 1.00, 55000.00, 15.00, 8250.00, 63250.00, 0.00, '2025-06-13 19:34:42'),
(8, 5, 2, '', 1.00, 13500.00, 15.00, 2025.00, 15525.00, 0.00, '2025-06-14 05:11:01'),
(9, 6, 1, '', 1.00, 5800.00, 15.00, 870.00, 6670.00, 0.00, '2025-06-14 21:11:32'),
(10, 7, 2, '', 1.00, 13500.00, 15.00, 2025.00, 15525.00, 0.00, '2025-06-14 23:40:34'),
(11, 7, 1, '', 1.00, 5800.00, 15.00, 870.00, 6670.00, 0.00, '2025-06-14 23:40:34');

-- --------------------------------------------------------

--
-- بنية الجدول `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','number','boolean','email','url','textarea') DEFAULT 'text',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_required` tinyint(1) DEFAULT 0,
  `default_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_required`, `default_value`, `created_at`, `updated_at`) VALUES
(1, 'company_name', 'اسم الشركة', 'text', 'company', 'اسم الشركة أو المؤسسة', 1, 'شركتي', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(2, 'company_address', 'عنوان الشركة', 'textarea', 'company', 'العنوان الكامل للشركة', 1, 'المملكة العربية السعودية', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(3, 'company_phone', '0501234567', 'text', 'company', 'رقم هاتف الشركة', 1, '0501234567', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(4, 'company_email', '<EMAIL>', 'email', 'company', 'البريد الإلكتروني للشركة', 0, '<EMAIL>', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(5, 'company_website', 'https://www.company.com', 'url', 'company', 'موقع الشركة الإلكتروني', 0, 'https://www.company.com', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(6, 'company_tax_number', '*********', 'text', 'company', 'الرقم الضريبي للشركة', 0, '*********', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(7, 'default_language', 'ar', 'text', 'general', 'اللغة الافتراضية للنظام', 1, 'ar', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(8, 'default_currency', 'ريال سعودي', 'text', 'general', 'العملة الافتراضية', 1, 'ريال سعودي', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(9, 'currency_symbol', 'ر.س', 'text', 'general', 'رمز العملة', 1, 'ر.س', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(10, 'currency_code', 'SAR', 'text', 'general', 'كود العملة', 1, 'SAR', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(11, 'decimal_places', '2', 'number', 'general', 'عدد الخانات العشرية', 1, '2', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(12, 'date_format', 'Y-m-d', 'text', 'general', 'تنسيق التاريخ', 1, 'Y-m-d', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(13, 'time_format', 'H:i:s', 'text', 'general', 'تنسيق الوقت', 1, 'H:i:s', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(14, 'timezone', 'Asia/Riyadh', 'text', 'general', 'المنطقة الزمنية', 1, 'Asia/Riyadh', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(15, 'tax_enabled', 'true', 'boolean', 'tax', 'تفعيل نظام الضريبة', 1, 'true', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(16, 'default_tax_rate', '15', 'number', 'tax', 'نسبة الضريبة الافتراضية', 1, '15', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(17, 'tax_number_required', 'false', 'boolean', 'tax', 'إجبارية الرقم الضريبي', 0, 'false', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(18, 'tax_inclusive_pricing', 'false', 'boolean', 'tax', 'الأسعار شاملة الضريبة', 0, 'false', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(19, 'invoice_prefix', 'INV-', 'text', 'invoice', 'بادئة رقم الفاتورة', 1, 'INV-', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(20, 'invoice_number_length', '6', 'number', 'invoice', 'طول رقم الفاتورة', 1, '6', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(21, 'invoice_footer', 'شكراً لتعاملكم معنا', 'textarea', 'invoice', 'تذييل الفاتورة', 0, 'شكراً لتعاملكم معنا', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(22, 'auto_invoice_number', 'true', 'boolean', 'invoice', 'ترقيم الفواتير تلقائياً', 1, 'true', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(23, 'items_per_page', '20', 'number', 'system', 'عدد العناصر في الصفحة', 1, '20', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(24, 'backup_enabled', 'true', 'boolean', 'system', 'تفعيل النسخ الاحتياطي', 0, 'true', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(25, 'maintenance_mode', 'false', 'boolean', 'system', 'وضع الصيانة', 0, 'false', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(26, 'debug_mode', 'false', 'boolean', 'system', 'وضع التطوير', 0, 'false', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(27, 'session_timeout', '3600', 'number', 'security', 'مهلة انتهاء الجلسة (بالثواني)', 1, '3600', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(28, 'password_min_length', '6', 'number', 'security', 'الحد الأدنى لطول كلمة المرور', 1, '6', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(29, 'login_attempts', '5', 'number', 'security', 'عدد محاولات تسجيل الدخول المسموحة', 1, '5', '2025-06-13 00:09:34', '2025-06-13 00:09:34'),
(30, 'account_lockout_time', '900', 'number', 'security', 'مدة قفل الحساب (بالثواني)', 1, '900', '2025-06-13 00:09:34', '2025-06-13 00:09:34');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_customer_name` (`name`),
  ADD KEY `idx_customer_email` (`email`),
  ADD KEY `idx_customer_phone` (`phone`),
  ADD KEY `idx_customer_tax_number` (`tax_number`),
  ADD KEY `idx_customer_type` (`customer_type`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_product_name` (`name`),
  ADD KEY `idx_product_barcode` (`barcode`),
  ADD KEY `idx_product_category` (`category`),
  ADD KEY `idx_product_active` (`is_active`),
  ADD KEY `idx_category` (`category`);

--
-- Indexes for table `purchases`
--
ALTER TABLE `purchases`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_invoice_number` (`invoice_number`),
  ADD KEY `idx_purchases_date` (`date`),
  ADD KEY `idx_purchases_customer` (`customer_id`),
  ADD KEY `idx_purchases_supplier` (`supplier_name`),
  ADD KEY `idx_purchases_payment_status` (`payment_status`);

--
-- Indexes for table `purchase_items`
--
ALTER TABLE `purchase_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_purchase_id` (`purchase_id`),
  ADD KEY `idx_product_id` (`product_id`);

--
-- Indexes for table `sales`
--
ALTER TABLE `sales`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_invoice_number` (`invoice_number`),
  ADD KEY `idx_sales_date` (`date`),
  ADD KEY `idx_sales_customer` (`customer_id`),
  ADD KEY `idx_sales_payment_status` (`payment_status`);

--
-- Indexes for table `sale_items`
--
ALTER TABLE `sale_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_sale_id` (`sale_id`),
  ADD KEY `idx_product_id` (`product_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_settings_category` (`category`),
  ADD KEY `idx_settings_type` (`setting_type`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `purchases`
--
ALTER TABLE `purchases`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `purchase_items`
--
ALTER TABLE `purchase_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `sales`
--
ALTER TABLE `sales`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `sale_items`
--
ALTER TABLE `sale_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;
--
-- Database: `sales_system_user_3`
--
CREATE DATABASE IF NOT EXISTS `sales_system_user_3` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
USE `sales_system_user_3`;

-- --------------------------------------------------------

--
-- بنية الجدول `customers`
--

CREATE TABLE `customers` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `customers`
--

INSERT INTO `customers` (`id`, `name`, `phone`, `email`, `tax_number`, `address`, `created_at`, `updated_at`) VALUES
(1, 'مؤسسة افاق المستقبل', '5977961171', '<EMAIL>', '3555888800', 'الرياض', '2025-06-13 05:43:04', '2025-06-13 05:43:04'),
(2, 'smartwebkeys', '966597291422', '<EMAIL>', '30300000000000', 'الدمام', '2025-06-13 05:43:30', '2025-06-13 05:43:30');

-- --------------------------------------------------------

--
-- بنية الجدول `products`
--

CREATE TABLE `products` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
  `stock_quantity` decimal(10,2) DEFAULT 0.00,
  `unit` varchar(50) DEFAULT 'قطعة',
  `barcode` varchar(100) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `products`
--

INSERT INTO `products` (`id`, `name`, `description`, `price`, `tax_rate`, `stock_quantity`, `unit`, `barcode`, `category`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'جوالات', NULL, 22000.00, 15.00, 0.00, 'قطعة', NULL, NULL, 1, '2025-06-13 05:44:29', '2025-06-13 05:44:29'),
(2, 'جوالات', NULL, 20000.00, 15.00, 0.00, 'قطعة', NULL, NULL, 1, '2025-06-13 05:45:33', '2025-06-13 05:45:33');

-- --------------------------------------------------------

--
-- بنية الجدول `purchases`
--

CREATE TABLE `purchases` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `supplier_name` varchar(255) DEFAULT NULL,
  `supplier_phone` varchar(20) DEFAULT NULL,
  `supplier_email` varchar(255) DEFAULT NULL,
  `supplier_tax_number` varchar(50) DEFAULT NULL,
  `invoice_number` varchar(50) NOT NULL,
  `date` date NOT NULL,
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `payment_method` varchar(50) DEFAULT 'نقدي',
  `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `purchases`
--

INSERT INTO `purchases` (`id`, `customer_id`, `supplier_name`, `supplier_phone`, `supplier_email`, `supplier_tax_number`, `invoice_number`, `date`, `subtotal`, `tax_amount`, `total_amount`, `discount_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`) VALUES
(1, 1, NULL, NULL, NULL, NULL, 'PUR-20250613-684BBB185EACB', '2025-06-13', 20000.00, 3000.00, 23000.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 05:46:00', '2025-06-13 05:46:00');

-- --------------------------------------------------------

--
-- بنية الجدول `purchase_items`
--

CREATE TABLE `purchase_items` (
  `id` int(11) NOT NULL,
  `purchase_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_name` varchar(255) NOT NULL DEFAULT '',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
  `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- بنية الجدول `sales`
--

CREATE TABLE `sales` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `invoice_number` varchar(50) NOT NULL,
  `date` date NOT NULL,
  `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `payment_method` varchar(50) DEFAULT 'نقدي',
  `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `sales`
--

INSERT INTO `sales` (`id`, `customer_id`, `invoice_number`, `date`, `subtotal`, `tax_amount`, `total_amount`, `discount_amount`, `payment_method`, `payment_status`, `notes`, `created_at`, `updated_at`) VALUES
(1, 2, 'INV-20250613-684BBAC4A74D5', '2025-06-13', 22000.00, 3300.00, 25300.00, 0.00, 'نقدي', 'unpaid', '', '2025-06-13 05:44:36', '2025-06-13 05:44:36');

-- --------------------------------------------------------

--
-- بنية الجدول `sale_items`
--

CREATE TABLE `sale_items` (
  `id` int(11) NOT NULL,
  `sale_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_name` varchar(255) NOT NULL DEFAULT '',
  `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
  `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
  `discount_amount` decimal(10,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- إرجاع أو استيراد بيانات الجدول `sale_items`
--

INSERT INTO `sale_items` (`id`, `sale_id`, `product_id`, `product_name`, `quantity`, `unit_price`, `tax_rate`, `tax_amount`, `total_price`, `discount_amount`, `created_at`) VALUES
(1, 1, 1, '', 1.00, 22000.00, 15.00, 3300.00, 25300.00, 0.00, '2025-06-13 05:44:36');

-- --------------------------------------------------------

--
-- بنية الجدول `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','number','boolean','email','url','textarea') DEFAULT 'text',
  `category` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `is_required` tinyint(1) DEFAULT 0,
  `default_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- إرجاع أو استيراد بيانات الجدول `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_required`, `default_value`, `created_at`, `updated_at`) VALUES
(1, 'company_name', 'اسم الشركة', 'text', 'company', 'اسم الشركة أو المؤسسة', 1, 'شركتي', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(2, 'company_address', 'عنوان الشركة', 'textarea', 'company', 'العنوان الكامل للشركة', 1, 'المملكة العربية السعودية', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(3, 'company_phone', '0501234567', 'text', 'company', 'رقم هاتف الشركة', 1, '0501234567', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(4, 'company_email', '<EMAIL>', 'email', 'company', 'البريد الإلكتروني للشركة', 0, '<EMAIL>', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(5, 'company_website', 'https://www.company.com', 'url', 'company', 'موقع الشركة الإلكتروني', 0, 'https://www.company.com', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(6, 'company_tax_number', '*********', 'text', 'company', 'الرقم الضريبي للشركة', 0, '*********', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(7, 'default_language', 'ar', 'text', 'general', 'اللغة الافتراضية للنظام', 1, 'ar', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(8, 'default_currency', 'ريال سعودي', 'text', 'general', 'العملة الافتراضية', 1, 'ريال سعودي', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(9, 'currency_symbol', 'ر.س', 'text', 'general', 'رمز العملة', 1, 'ر.س', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(10, 'currency_code', 'SAR', 'text', 'general', 'كود العملة', 1, 'SAR', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(11, 'decimal_places', '2', 'number', 'general', 'عدد الخانات العشرية', 1, '2', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(12, 'date_format', 'Y-m-d', 'text', 'general', 'تنسيق التاريخ', 1, 'Y-m-d', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(13, 'time_format', 'H:i:s', 'text', 'general', 'تنسيق الوقت', 1, 'H:i:s', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(14, 'timezone', 'Asia/Riyadh', 'text', 'general', 'المنطقة الزمنية', 1, 'Asia/Riyadh', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(15, 'tax_enabled', 'true', 'boolean', 'tax', 'تفعيل نظام الضريبة', 1, 'true', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(16, 'default_tax_rate', '15', 'number', 'tax', 'نسبة الضريبة الافتراضية', 1, '15', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(17, 'tax_number_required', 'false', 'boolean', 'tax', 'إجبارية الرقم الضريبي', 0, 'false', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(18, 'tax_inclusive_pricing', 'false', 'boolean', 'tax', 'الأسعار شاملة الضريبة', 0, 'false', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(19, 'invoice_prefix', 'INV-', 'text', 'invoice', 'بادئة رقم الفاتورة', 1, 'INV-', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(20, 'invoice_number_length', '6', 'number', 'invoice', 'طول رقم الفاتورة', 1, '6', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(21, 'invoice_footer', 'شكراً لتعاملكم معنا', 'textarea', 'invoice', 'تذييل الفاتورة', 0, 'شكراً لتعاملكم معنا', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(22, 'auto_invoice_number', 'true', 'boolean', 'invoice', 'ترقيم الفواتير تلقائياً', 1, 'true', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(23, 'items_per_page', '20', 'number', 'system', 'عدد العناصر في الصفحة', 1, '20', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(24, 'backup_enabled', 'true', 'boolean', 'system', 'تفعيل النسخ الاحتياطي', 0, 'true', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(25, 'maintenance_mode', 'false', 'boolean', 'system', 'وضع الصيانة', 0, 'false', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(26, 'debug_mode', 'false', 'boolean', 'system', 'وضع التطوير', 0, 'false', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(27, 'session_timeout', '3600', 'number', 'security', 'مهلة انتهاء الجلسة (بالثواني)', 1, '3600', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(28, 'password_min_length', '6', 'number', 'security', 'الحد الأدنى لطول كلمة المرور', 1, '6', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(29, 'login_attempts', '5', 'number', 'security', 'عدد محاولات تسجيل الدخول المسموحة', 1, '5', '2025-06-13 05:42:38', '2025-06-13 05:42:38'),
(30, 'account_lockout_time', '900', 'number', 'security', 'مدة قفل الحساب (بالثواني)', 1, '900', '2025-06-13 05:42:38', '2025-06-13 05:42:38');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_customer_name` (`name`),
  ADD KEY `idx_customer_email` (`email`),
  ADD KEY `idx_customer_phone` (`phone`),
  ADD KEY `idx_customer_tax_number` (`tax_number`);

--
-- Indexes for table `products`
--
ALTER TABLE `products`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_product_name` (`name`),
  ADD KEY `idx_product_barcode` (`barcode`),
  ADD KEY `idx_product_category` (`category`),
  ADD KEY `idx_product_active` (`is_active`);

--
-- Indexes for table `purchases`
--
ALTER TABLE `purchases`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_invoice_number` (`invoice_number`),
  ADD KEY `idx_purchases_date` (`date`),
  ADD KEY `idx_purchases_customer` (`customer_id`),
  ADD KEY `idx_purchases_supplier` (`supplier_name`),
  ADD KEY `idx_purchases_payment_status` (`payment_status`);

--
-- Indexes for table `purchase_items`
--
ALTER TABLE `purchase_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_purchase_id` (`purchase_id`),
  ADD KEY `idx_product_id` (`product_id`);

--
-- Indexes for table `sales`
--
ALTER TABLE `sales`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_invoice_number` (`invoice_number`),
  ADD KEY `idx_sales_date` (`date`),
  ADD KEY `idx_sales_customer` (`customer_id`),
  ADD KEY `idx_sales_payment_status` (`payment_status`);

--
-- Indexes for table `sale_items`
--
ALTER TABLE `sale_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_sale_id` (`sale_id`),
  ADD KEY `idx_product_id` (`product_id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_settings_category` (`category`),
  ADD KEY `idx_settings_type` (`setting_type`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `products`
--
ALTER TABLE `products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `purchases`
--
ALTER TABLE `purchases`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `purchase_items`
--
ALTER TABLE `purchase_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sales`
--
ALTER TABLE `sales`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `sale_items`
--
ALTER TABLE `sale_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
