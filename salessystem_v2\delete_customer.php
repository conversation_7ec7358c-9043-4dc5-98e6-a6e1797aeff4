<?php
/**
 * حذف عميل مع معالجة محسنة للأخطاء
 */

// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once __DIR__.'/config/init.php';
require_once __DIR__.'/includes/functions.php';
redirectIfNotLoggedIn();

try {
    $db = getCurrentUserDB();

    if ($db === null || $db->connect_error) {
        $_SESSION['error'] = "خطأ في الاتصال بقاعدة البيانات";
        header("Location: customers.php");
        exit();
    }

    if (!isset($_GET['id']) || empty($_GET['id'])) {
        $_SESSION['error'] = "معرف العميل مطلوب";
        header("Location: customers.php");
        exit();
    }

    $customer_id = intval($_GET['id']);

    if ($customer_id <= 0) {
        $_SESSION['error'] = "معرف العميل غير صحيح";
        header("Location: customers.php");
        exit();
    }

    // التحقق من وجود العميل أولاً
    $check_stmt = $db->prepare("SELECT id, name FROM customers WHERE id = ?");
    if (!$check_stmt) {
        $_SESSION['error'] = "خطأ في إعداد استعلام التحقق: " . $db->error;
        header("Location: customers.php");
        exit();
    }

    $check_stmt->bind_param("i", $customer_id);
    $check_stmt->execute();
    $result = $check_stmt->get_result();

    if ($result->num_rows === 0) {
        $check_stmt->close();
        $_SESSION['error'] = "العميل غير موجود";
        header("Location: customers.php");
        exit();
    }

    $customer = $result->fetch_assoc();
    $customer_name = $customer['name'];
    $check_stmt->close();

    // التحقق من وجود مبيعات مرتبطة بالعميل
    $sales_stmt = $db->prepare("SELECT COUNT(id) as sales_count FROM sales WHERE customer_id = ?");
    if (!$sales_stmt) {
        $_SESSION['error'] = "خطأ في إعداد استعلام المبيعات: " . $db->error;
        header("Location: customers.php");
        exit();
    }

    $sales_stmt->bind_param("i", $customer_id);
    $sales_stmt->execute();
    $sales_result = $sales_stmt->get_result();
    $sales_data = $sales_result->fetch_assoc();
    $sales_count = $sales_data['sales_count'];
    $sales_stmt->close();

    if ($sales_count > 0) {
        $_SESSION['error'] = "لا يمكن حذف العميل '$customer_name' لأنه مرتبط بـ $sales_count فاتورة مبيعات";
        header("Location: customers.php");
        exit();
    }

    // التحقق من وجود مشتريات مرتبطة بالعميل (إذا كان هناك جدول للمشتريات يحتوي على customer_id)
    $purchases_stmt = $db->prepare("SELECT COUNT(id) as purchases_count FROM purchases WHERE supplier_name = ?");
    if ($purchases_stmt) {
        $purchases_stmt->bind_param("s", $customer_name);
        $purchases_stmt->execute();
        $purchases_result = $purchases_stmt->get_result();
        $purchases_data = $purchases_result->fetch_assoc();
        $purchases_count = $purchases_data['purchases_count'];
        $purchases_stmt->close();

        if ($purchases_count > 0) {
            $_SESSION['warning'] = "تحذير: العميل '$customer_name' مرتبط بـ $purchases_count فاتورة شراء كمورد";
        }
    }

    // حذف العميل
    $delete_stmt = $db->prepare("DELETE FROM customers WHERE id = ?");
    if (!$delete_stmt) {
        $_SESSION['error'] = "خطأ في إعداد استعلام الحذف: " . $db->error;
        header("Location: customers.php");
        exit();
    }

    $delete_stmt->bind_param("i", $customer_id);

    if ($delete_stmt->execute()) {
        if ($delete_stmt->affected_rows > 0) {
            $_SESSION['success'] = "تم حذف العميل '$customer_name' بنجاح";
        } else {
            $_SESSION['error'] = "لم يتم حذف أي عميل. قد يكون العميل محذوف مسبقاً";
        }
    } else {
        $_SESSION['error'] = "حدث خطأ أثناء حذف العميل: " . $delete_stmt->error;
    }

    $delete_stmt->close();

} catch (mysqli_sql_exception $e) {
    $_SESSION['error'] = "خطأ في قاعدة البيانات: " . $e->getMessage();
    error_log("Database error in delete_customer.php: " . $e->getMessage());
} catch (Exception $e) {
    $_SESSION['error'] = "حدث خطأ غير متوقع: " . $e->getMessage();
    error_log("General error in delete_customer.php: " . $e->getMessage());
}

header("Location: customers.php");
exit();
?>