*{
    margin: 0;
    font-family: "Gill Sans Extrabold", sans-serif;
}
body{
    width: 100%;
    height: 1000px;
    background-color: #f8f9fa!important;}
.dbody{
    width: 90%;
    height: 100%;
    margin: auto;
    margin-top: 30px;
}
.ulbdy{
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: 0;
}
.icrd{
width: 200px;
height: 300px;
background: rgb(241, 241, 241);
border-radius: 20px;
border: 1px solid rgb(218, 218, 218);
margin: 15px;
box-shadow: 0 6px 24px 0 rgb(0 0 0 / 8%);
overflow: hidden;
transition: all 0.2s ease;
}
h3{
    position: absolute;
    margin: 10px;
    z-index: 1;
    border: 2px solid #8ceb50;
    padding: 0 6px 0;
    border-radius: 5px;
    box-shadow: 1px 3px 2px 0px rgb(0 0 0 / 68%);
    background-color: rgba(255, 255, 255, 0);
}
.icrd .idimg{
    width: 200px;
height:300px;
object-fit: cover;
transition:transform 0.5s ease;
}
.idimg img{
width: 100%;
height:100%;
object-fit: cover;
}

.isam{
    position: sticky;
    bottom: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.719);
    box-shadow: 0px -10px 15px 3px rgb(0 0 0 / 25%);
    visibility: hidden;
    transition: transform 0.5s ease;
    padding: 5px;height: 100px;
    transform: translateY(40px);
}
.icrd:hover .isam{
    visibility: visible;
    transform: translateY(0px);}
    
.icrd:hover{
    box-shadow: 0 6px 24px 0 rgba(0, 0, 0, 0.158);
    border: 1px solid rgb(255, 255, 255);
}
.icrd:hover .idimg{
transform: scale(1.08);
}
.bopcard{
    width: 100%;
}
.opcard{
    width: 500px;
    height: 300px;
    background:linear-gradient(135deg, rgb(255 255 255 / 42%), rgba(255, 255, 255, 0));
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 20px;
    border:2px solid rgba(255, 255, 255, 0.18);
    box-shadow: 0px 1px 15px 6px rgba(56, 56, 56, 0.171);
}
.bopcard .opcard{
    margin: auto;
    margin-bottom: 30px;
    margin-top: 30px; 
}