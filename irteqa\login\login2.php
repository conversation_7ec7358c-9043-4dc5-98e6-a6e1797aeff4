
<?php
session_start(); // بدء الجلسة

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST["username"];
    $password = $_POST["password"];

    // تنفيذ استعلام SQL للتحقق من صحة اسم المستخدم وكلمة المرور
    // يجب تعديل المعلومات الخاصة بالاتصال بقاعدة البيانات هنا
    $db_host = "localhost";
    $db_user = "root";
    $db_pass = "";
    $db_name = "signupai";

    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

    if ($conn->connect_error) {
        die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    $query = "SELECT * FROM users WHERE username = '$username'";
    $result = $conn->query($query);

    if ($result->num_rows == 1) {
        $row = $result->fetch_assoc();

        if (password_verify($password, $row["password"])) {
            // تم تسجيل الدخول بنجاح
            $_SESSION["username"] = $username; // تخزين اسم المستخدم في الجلسة
            header("Location: home.php");
            exit; // توقف تنفيذ السكريبت بعد التوجيه
        } else {
            // كلمة المرور غير صحيحة
            echo "كلمة المرور غير صحيحة.";
        }
    } else {
        // اسم المستخدم غير موجود
        echo "اسم المستخدم غير موجود.";
    }

    $conn->close();
}

// إذا وصلت هنا، فإن تسجيل الدخول فشل، يمكنك عرض نموذج تسجيل الدخول مرة أخرى
include "login_form.html";
?>