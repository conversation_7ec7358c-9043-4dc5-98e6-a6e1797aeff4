<!DOCTYPE html>
<html lang="en">
  <head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة الطلبات</title>
<link rel="stylesheet" href="style/foundation.min.css">
<link rel="stylesheet" href="style/jquery.dataTables.min.css">
<script src="js/jquery-3.3.1.min.js"></script>

<style>
*{font-family: &#39;<PERSON><PERSON><PERSON>&#39;, sans-serif;}
  body {
  display:block;
  width:100%;
    text-align: center;
    font-weight: bold;
}
  div.aa{
    padding:5px ;
  box-shadow: 3px 5px 3px 0 rgba(85, 85, 85, 0.2), 0 6px 20px 0 rgba(125, 125, 125, 0.19);
  background-color: rgb(244, 244, 244);
  border: 1px solid #ccc;
  border-radius: 10px 0px 10px 10px;
  width: 97%;
  height: 150px;
  align-items: center;
  float:right;
  margin-right: 20px;
  }
  li a.activ {
    color: white;
    background-color: #04aa30;}
  input[type=text] {
  text-align: right;
  float:right;
  width: 20%;
  padding: 12px 20px;
  margin: 5px;
  box-sizing: border-box;
  border-radius: 10px;
  display:block;
  box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%);
}
input[type=submit]{
  color:white;
  float:right;
width: 10%;
margin:8px;
height: 50px;
border:none;
border-radius: 20px;
background-color:#04aa30;
box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%); */
}
input[type=submit]:hover {
background-color: #62a874;
}
 h1{
 font-size:  2.5em;
margin: 0px 0px 0px 30px;
 }
 .box{
  display: flex;
    height: 99px;
    height: 100px;
    direction: rtl;
 }

.form-select{
  display:block;
border: 0px;
text-align:center;
font-size: 20px;
width: 90px;
padding: 5px 20px;
margin: 10px;
border-radius: 10px;
float: right;
box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%);
}
.box select{
border:none;
margin:5px;
}

body {
  display:block;
  width:100%;
    text-align: center;
    font-weight: bold;
}

#fantasyTable_filter{
  border-radius:10px;
  direction: rtl;
}
#myform{
  float:right;
  display: inline-flex;
}
table {
  border-radius:6px;
    width: 100%;
	border-collapse: collapse;
  counter-reset: row-num-1;
  zoom:1.5;
}
#fantasyTable tr .header, #fantasyTable tr:hover {
  background-color: #f1f1f1;
}
/*table tr {
  counter-increment: row-num;
}
table tr td:last-child::after {
    content: counter(row-num) "";
}*/
#fantasyTable {
	width: 100%;
	border-collapse: collapse;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
  float:right;
  font: icon;
}
#tabh{
  margin:10px;
}
#fantasyTable th {
	background: #D6D6D6;
	font-size: 1rem;
	font-weight: 700;
	color: #474443;
	border-bottom: solid thin #C1C1C1;
	border-right: solid thin #C1C1C1;
	border-top: solid thin #D6D6D6;
	border-left: solid thin #D6D6D6;
}
#fantasyTable td {
	padding: 2px;
	font-size: .75rem;
	font-weight: 400;
	color: #474443;
	border-bottom: solid thin #C1C1C1;
	border-right: solid thin #C1C1C1;
	border-top: solid thin #D6D6D6;
	border-left: solid thin #D6D6D6;
}

/*controls odd rows*/
#fantasyTable tr:nth-child(odd) {
	background: #FFFFFF;
	border-bottom: solid thin #C1C1C1;
	border-right: solid thin #C1C1C1;
	border-top: solid thin #D6D6D6;
	border-left: solid thin #D6D6D6;
}
/*controls even rows*/
#fantasyTable tr:nth-child(even) {
	background: #FFFFFF;
	border-bottom: solid thin #C1C1C1;
	border-right: solid thin #C1C1C1;
	border-top: solid thin #D6D6D6;
	border-left: solid thin #D6D6D6;
}
#fantasyTable td:nth-child(2) {
	color: #04aa30;
	text-align:center;
	font-weight: 700;
  font-size: 1.2rem;
  width: 40px;
}
/*controls column 1*/
#fantasyTable td:nth-child(1) {
	background: #FFFFFF;
	color: #0082D6;
	font-size: 1rem;
	font-weight: 700;
	text-align: center;
}
/*controls column 1 header*/
#fantasyTable th:nth-child(1) {
	text-align: center;
	border-left: solid 1px #FFFFFF;
}
/*controls column 2*/
#fantasyTable td:nth-child(3) {
	text-transform: uppercase;
  font-weight: 700;
  font-size: 1.5rem;
  color: #c82b2b;
  text-align: center;
}
#fantasyTable td:nth-child(4) {
	text-transform: uppercase;
  font-weight: 700;
  font-size: 1rem;
  text-align:center;
}
#fantasyTable td:nth-child(5) {
	text-transform: uppercase;
  font-weight: 700;
  font-size: 1rem;
}
/*controls column 5*/
#fantasyTable td:nth-child(6) {
	background: #ffffff;
	color: #000000;
	font-weight: 700;
  font-size: 1rem;
}
#fantasyTable td:nth-child(7) {
	background: #ffffff;
	color: #000000;
	font-weight: 700;
  font-size: 1rem;
}
/*controls column head 5*/
#fantasyTable th:nth-child(6) {
	background: #D6D6D6;
}
/*controls column 6*/
#fantasyTable td:nth-child(8) {
	background: #EDEDED;
	color: #0082D6;
	text-align:center;
	font-weight: 700;
  font-size: 1.5rem;
}
/*controls column head 6*/
#fantasyTable th:nth-child(8) {
	background: #D6D6D6;
	text-align: right;
}
 
#fantasyTable th:nth-child(9) {
	background: #D6D6D6;
}
#fantasyTable td:nth-child(9) {
	align-items:center;
}
.pointLeader img {
	float: left;
	padding-right: 20px;
	font-weight: 900;
}
.pointLeader p {
	font-size: .75rem;
	text-align: left
}
.pointLeader h4 {
	font-weight: 900;
	text-align: left
}
.align-center {
	text-align: center
}
.learnMore {
	padding: 0 0 0 10px
}
.md-label {
	background: #BABABA
}
.md-label-orange {
	background: #f78d2c;
	padding: 0.3rem 0.5rem;
	border-radius: 0;
	color: white
}
.md-caps {
	text-transform: uppercase
}
.card {
	border: solid thin #F5F5F5;
	padding: 2rem 1rem;
}
.callout {
	border: solid thin #F5F5F5;
	background-color: #D6D6D6;
	border-radius: 5px;
	padding: .5rem;
	width: auto;
}
.md-black {
	font-weight: 900;
}
.linethrough {
	text-align: center;
	position: relative;
	z-index: 1;
}
.linethrough:before {
	border-top: 2px solid #dfdfdf;
	content: "";
	margin: 0 auto; /* this centers the line to the full width specified */
	position: absolute; /* positioning must be absolute here, and relative positioning must be applied to the parent */
	top: 15px;
	left: 0;
	right: 0;
	bottom: 0;
	width: 95%;
	z-index: -1;
}
.linethrough span {
	/* to hide the lines from behind the text, you have to set the background color the same as the container */ 
	background: #FFFFFF;
	padding: 0 15px;
}
.max-width-50 {
	max-width: 50%
}
/*controls responsive button for opening hidden columns*/
table.dataTable.dtr-inline.collapsed>tbody>tr>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr>th:first-child:before {
	background-color: #0d82fb;
	border: none;
  font-size:12px
}
/* give space between search bar and table */
.dataTables_filter {
	margin-bottom: 20px
}
.dataTables_filter input {
    text-align: center;zoom: 1.5;
}
label {
    font-size: 1.55rem;}
@media screen and (max-width: 39.9375em) {
/* give space between search bar and table */
.dataTables_wrapper .dataTables_length {
    display: none;
}
body{
	display: inline-table;
	zoom:0.58;
}
} 
@media screen and (max-width:420px) {
    body{
        zoom:.6;
        display: inline-table;
    }
h1 {
    margin:10px;
    font-size: 20px;
    font-weight: bold;
}
label{
    font-size: 16px;
       text-align: right;
}
.dataTables_wrapper {
    position: relative;
    zoom: .2;
}
.dataTables_filter input {
    width:50%;
    margin-left: 13.5em;
    zoom:1;
    font-size: 16px;
    height:250px;
    padding:5px;
}


}
</style><script>
  window.console = window.console || function(t) {};
</script><script>
  if (document.location.search.match(/type=embed/gi)) {
    window.parent.postMessage("resize", "*");
  }
</script><style type="text/css"> /*
 * contextMenu.js v 1.4.0
 * Author: Sudhanshu Yadav
 * s-yadav.github.com
 * Copyright (c) 2013 Sudhanshu Yadav.
 * Dual licensed under the MIT and GPL licenses
**/
</style>
</head>
<body>
  <?php include('head.php');?>
  <div class="aa" >
   <h1>مؤسسات أبوسعود</h1>
   <div id="myform">
        <form class="box" action="index.php" method="POST">
          <input type="submit" value="إرسال" name="send"/>
              <input type="text" id="serv" name="serv" placeholder="الخدمة">
              <input type="text" id="fname" name="fname" placeholder="الإسم" >
              <input class="inpu" type="text" id="call1" name="calll" placeholder="الهاتف" style="width: 20%;">
         <select class="form-select" aria-label="Default select example" style="text-align: center;font-size:14px;" name="who">
            <option selected>الصفة</option>
            <option value="منفذ">منفذ</option>
            <option value="وسيط">وسيط</option>
            <option value="عميل">عميل</option>
              <input type="text" id="sell" name="sell" placeholder="المبلغ" style="width:10%;">
        </form>
        <div></div>
        <?php 
        include('indcon.php');
        
$sql = "INSERT INTO orderss(sell,who,calll,fname,serv) VALUES ('$sell','$who','$calll','$fname','$serv')";
if (isset($_POST['send'])){

if(empty($serv)){
  echo '';

}
elseif(mysqli_query($conn, $sql)){ 
  
    }else{
      echo 'Error: ' . mysqli_error($conn);    }
      
}

?>
</div>
<div id="tabh">
      <!-- End Example Code -->
    <table id="fantasyTable" class="display responsive no-wrap order-column dataTable no-footer dtr-inline" role="grid" style="width: 100%;text-align: right;zoom:1.2">
<?php include ('mdlfrm.php');?>
  <thead>
  <tr role="row">
  
    <th class="sorting" tabindex="0" aria-controls="fantasyTable" rowspan="1" colspan="1" style="width: 100px;" aria-sort="ascending" aria-label="Rank: activate to sort column descending">التأريخ</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 40px;text-align: center;" aria-label="State">الإجراء</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 30px;text-align:center;" aria-label="Customer">المبلغ(ريال)</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 40px;text-align:center;" aria-label="Customer">الصفة</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 60px;text-align: center;" aria-label="City">الهاتف</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 188px;text-align: center;" aria-label="State">إسم العميل</th>
    <th class="sorting" tabindex="0" aria-controls="fantasyTable" rowspan="1" colspan="1" style="width: 150px;text-align: center;" aria-label="Total Points: activate to sort column ascending">الخدمة</th>
    <th class="sorting" tabindex="0" aria-controls="fantasyTable" rowspan="1" colspan="1" style="width: 40px;text-align:center;" aria-label="Entries: activate to sort column ascending">الرقم</th>
    <th class="sorting" tabindex="0" aria-controls="fantasyTable" rowspan="1" colspan="1" style="width: 40px;text-align:center;" aria-label="Entries: activate to sort column ascending">تعديل</th>
  </tr>
 </thead>
 <?php
 include('indcon.php');
// إستيراد معلومات المرضى من قاعدة البيانات
$sql = "SELECT * FROM orderss";
$result = mysqli_query($conn,$sql);
$num=1;
if ($result){
    while($row = mysqli_fetch_assoc($result)){
        echo "<tr><td>" . $row['date'] . "</td><td>" . $row['injaz'] . "</td><td>" . $row['sell'] . "</td><td>" . $row['who'] . "</td><td>" . $row['calll'] . "</td><td>" . $row['fname'] . "</td><td>" . $row['serv'] . "</td><td>" . $num++ . "</td><td>" . '<a  href="?user_edit='.$row['id'].'" ><img src="img/sett.png" style="width:25px;"></a>' . "</td></tr>";
      }
    echo "</table>";
    
}
else{
    echo "يوجد خطا ماء";
}
?>

</div>
</script>
<script type="text/javascript">
    $(window).on('load',function(){
        $('#edit').modal('show');
    });
</script>
	
	
<script>
function goBack() {
    window.history.back();
}
</script>


<script>
$('#edit').modal({
backdrop: 'static',
keyboard: false
})
</script>	

<script src="js/jquery.dataTables.min.js"></script>
      <script id="rendered-js">
// ENTER INFO INTO SEARCH BAR TO SEE FUNCTION. CLICK RANK TO SEE RANKING. PAGINATION ON BOTTOM.

// DATATABLES.NET

$(document).ready(function () {

  jQuery(function () {
    $('#fantasyTable').DataTable({
      info: false,
      responsive: true,
      columnDefs: [
      { responsivePriority: 1, targets: 0 },
      { responsivePriority: 2, targets: -1 },
      { responsivePriority: 3, targets: -2 },
      {
        "targets": [1, 2, 3],
        "orderable": false }] });
  });
});
//# sourceURL=pen.js احديث جافا
if ( window.history.replaceState ) {
  window.history.replaceState( null, null, window.location.href );
}
    </script>

<?php

?>
<h1 style="color:#04aa30;text-shadow: 0 1px 10px rgb(138 194 173);font-weight: bold;">بقية البيانات في الصفحات التالية</h1>
    </body>
</html>