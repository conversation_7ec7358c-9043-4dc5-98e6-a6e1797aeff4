!function(h){"use strict";var i=function(i,o,r){var u,s,v,c,t={},m=0,a=0,l={sensitivity:7,interval:100,timeout:0};function f(e){u=e.clientX,s=e.clientY}function e(e){return a&&(a=clearTimeout(a)),i.removeEventListener("mousemove",f),1!==m&&(v=e.clientX,c=e.clientY,i.addEventListener("mousemove",f),a=setTimeout(function(){!function e(t,n){if(a&&(a=clearTimeout(a)),Math.abs(v-u)+Math.abs(c-s)<l.sensitivity)return m=1,o?o.call(t,n):null;v=u,c=s,a=setTimeout(function(){e(t,n)},l.interval)}(i,e)},l.interval)),this}function n(n){return a&&(a=clearTimeout(a)),i.removeEventListener("mousemove",f),1===m&&(a=setTimeout(function(){var e,t;e=i,t=n,a&&(a=clearTimeout(a)),m=0,r&&r.call(e,t)},l.timeout)),this}return t.options=function(e){return l=h.extend({},l,e),t},t.remove=function(){i.removeEventListener("mouseover",e),i.removeEventListener("mouseleave",n)},i.addEventListener("mouseover",e),i.addEventListener("mouseleave",n),t};h.fn.hoverIntent=function(e,t,n){return this.each(function(){i(this,e,t).options(n||{})})}}(jQuery);