<?php
/**
 * أداة إضافة حماية CSRF للنماذج
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// معالجة طلب إضافة حماية CSRF
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_csrf_protection'])) {
    $files_updated = 0;
    $errors = [];
    
    try {
        // 1. إنشاء ملف CSRF helper
        $csrf_helper_content = '<?php
/**
 * مساعد حماية CSRF
 */

class CSRFProtection {
    
    /**
     * إنشاء CSRF token
     */
    public static function generateToken() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION["csrf_token"])) {
            $_SESSION["csrf_token"] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION["csrf_token"];
    }
    
    /**
     * الحصول على CSRF token
     */
    public static function getToken() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        return isset($_SESSION["csrf_token"]) ? $_SESSION["csrf_token"] : self::generateToken();
    }
    
    /**
     * التحقق من CSRF token
     */
    public static function validateToken($token) {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (!isset($_SESSION["csrf_token"]) || empty($token)) {
            return false;
        }
        
        return hash_equals($_SESSION["csrf_token"], $token);
    }
    
    /**
     * إنشاء حقل CSRF مخفي للنماذج
     */
    public static function getHiddenField() {
        $token = self::getToken();
        return "<input type=\"hidden\" name=\"csrf_token\" value=\"" . htmlspecialchars($token) . "\">";
    }
    
    /**
     * التحقق من CSRF token في POST request
     */
    public static function validateRequest() {
        if ($_SERVER["REQUEST_METHOD"] === "POST") {
            $token = isset($_POST["csrf_token"]) ? $_POST["csrf_token"] : "";
            
            if (!self::validateToken($token)) {
                http_response_code(403);
                die("CSRF token validation failed. Request blocked for security.");
            }
        }
    }
    
    /**
     * تجديد CSRF token
     */
    public static function regenerateToken() {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        $_SESSION["csrf_token"] = bin2hex(random_bytes(32));
        return $_SESSION["csrf_token"];
    }
}
';
        
        $csrf_helper_file = __DIR__ . '/includes/csrf_protection.php';
        if (file_put_contents($csrf_helper_file, $csrf_helper_content)) {
            $files_updated++;
        } else {
            $errors[] = "فشل في إنشاء ملف csrf_protection.php";
        }
        
        // 2. قائمة الملفات التي تحتوي على نماذج
        $form_files = [
            'add_customer.php',
            'add_sale.php', 
            'add_purchase.php',
            'edit_customer.php',
            'edit_sale.php',
            'edit_purchase.php',
            'login.php',
            'register.php',
            'profile.php',
            'settings.php'
        ];
        
        foreach ($form_files as $file) {
            $filepath = __DIR__ . '/' . $file;
            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                $original_content = $content;
                
                // إضافة require للـ CSRF protection في بداية الملف
                if (strpos($content, 'csrf_protection.php') === false) {
                    $content = str_replace(
                        'require_once __DIR__ . \'/includes/functions.php\';',
                        'require_once __DIR__ . \'/includes/functions.php\';' . PHP_EOL . 'require_once __DIR__ . \'/includes/csrf_protection.php\';',
                        $content
                    );
                }
                
                // إضافة التحقق من CSRF في بداية معالجة POST
                if (preg_match('/if\s*\(\s*\$_SERVER\s*\[\s*["\']REQUEST_METHOD["\']\s*\]\s*===?\s*["\']POST["\']\s*\)/', $content)) {
                    $csrf_check = 'CSRFProtection::validateRequest();' . PHP_EOL . '    ';
                    
                    if (strpos($content, 'CSRFProtection::validateRequest') === false) {
                        $content = preg_replace(
                            '/(if\s*\(\s*\$_SERVER\s*\[\s*["\']REQUEST_METHOD["\']\s*\]\s*===?\s*["\']POST["\']\s*\)\s*\{)/',
                            '$1' . PHP_EOL . '    ' . $csrf_check,
                            $content
                        );
                    }
                }
                
                // إضافة CSRF token للنماذج
                if (preg_match('/<form[^>]*method\s*=\s*["\']post["\'][^>]*>/i', $content)) {
                    if (strpos($content, 'csrf_token') === false) {
                        $content = preg_replace(
                            '/(<form[^>]*method\s*=\s*["\']post["\'][^>]*>)/i',
                            '$1' . PHP_EOL . '                        <?php echo CSRFProtection::getHiddenField(); ?>',
                            $content
                        );
                    }
                }
                
                // حفظ الملف إذا تم تعديله
                if ($content !== $original_content) {
                    if (file_put_contents($filepath, $content)) {
                        $files_updated++;
                    } else {
                        $errors[] = "فشل في تحديث ملف $file";
                    }
                }
            }
        }
        
        // 3. تحديث ajax_handler.php
        $ajax_file = __DIR__ . '/ajax_handler.php';
        if (file_exists($ajax_file)) {
            $content = file_get_contents($ajax_file);
            $original_content = $content;
            
            // إضافة require للـ CSRF protection
            if (strpos($content, 'csrf_protection.php') === false) {
                $content = str_replace(
                    'require_once __DIR__ . \'/includes/functions.php\';',
                    'require_once __DIR__ . \'/includes/functions.php\';' . PHP_EOL . 'require_once __DIR__ . \'/includes/csrf_protection.php\';',
                    $content
                );
            }
            
            // إضافة التحقق من CSRF
            if (strpos($content, 'CSRFProtection::validateRequest') === false) {
                $csrf_validation = '
// التحقق من CSRF token
CSRFProtection::validateRequest();
';
                $content = str_replace(
                    '// التحقق من تسجيل الدخول',
                    $csrf_validation . PHP_EOL . '// التحقق من تسجيل الدخول',
                    $content
                );
            }
            
            if ($content !== $original_content) {
                if (file_put_contents($ajax_file, $content)) {
                    $files_updated++;
                } else {
                    $errors[] = "فشل في تحديث ملف ajax_handler.php";
                }
            }
        }
        
        // 4. إنشاء JavaScript helper للـ CSRF
        $csrf_js_content = '/**
 * مساعد CSRF للـ JavaScript
 */

// إضافة CSRF token لجميع طلبات AJAX
$(document).ready(function() {
    // الحصول على CSRF token من meta tag
    var csrfToken = $(\'meta[name="csrf-token"]\').attr(\'content\');
    
    if (csrfToken) {
        // إضافة CSRF token لجميع طلبات AJAX
        $.ajaxSetup({
            beforeSend: function(xhr, settings) {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.crossDomain) {
                    xhr.setRequestHeader("X-CSRFToken", csrfToken);
                }
            }
        });
        
        // إضافة CSRF token لجميع النماذج تلقائياً
        $(\'form[method="post"]\').each(function() {
            if (!$(this).find(\'input[name="csrf_token"]\').length) {
                $(this).append(\'<input type="hidden" name="csrf_token" value="\' + csrfToken + \'">\');
            }
        });
    }
});

// دالة للحصول على CSRF token
function getCSRFToken() {
    return $(\'meta[name="csrf-token"]\').attr(\'content\');
}

// دالة لإضافة CSRF token لطلب AJAX
function addCSRFToAjax(data) {
    var token = getCSRFToken();
    if (token) {
        data.csrf_token = token;
    }
    return data;
}
';
        
        $csrf_js_file = __DIR__ . '/assets/js/csrf-protection.js';
        if (file_put_contents($csrf_js_file, $csrf_js_content)) {
            $files_updated++;
        } else {
            $errors[] = "فشل في إنشاء ملف csrf-protection.js";
        }
        
        // 5. تحديث header.php لإضافة meta tag للـ CSRF token
        $header_file = __DIR__ . '/includes/header.php';
        if (file_exists($header_file)) {
            $content = file_get_contents($header_file);
            $original_content = $content;
            
            // إضافة meta tag للـ CSRF token
            if (strpos($content, 'csrf-token') === false) {
                $csrf_meta = '    <meta name="csrf-token" content="<?php echo isset($_SESSION[\'csrf_token\']) ? $_SESSION[\'csrf_token\'] : \'\'; ?>">';
                $content = str_replace(
                    '<meta charset="utf-8">',
                    '<meta charset="utf-8">' . PHP_EOL . $csrf_meta,
                    $content
                );
            }
            
            // إضافة JavaScript file
            if (strpos($content, 'csrf-protection.js') === false) {
                $csrf_script = '    <script src="assets/js/csrf-protection.js"></script>';
                $content = str_replace(
                    '</body>',
                    $csrf_script . PHP_EOL . '</body>',
                    $content
                );
            }
            
            if ($content !== $original_content) {
                if (file_put_contents($header_file, $content)) {
                    $files_updated++;
                } else {
                    $errors[] = "فشل في تحديث ملف header.php";
                }
            }
        }
        
        if ($files_updated > 0) {
            $_SESSION['success'] = "تم تحديث $files_updated ملف بحماية CSRF";
        } else {
            $_SESSION['info'] = "لا توجد ملفات تحتاج لتحديث";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء إضافة حماية CSRF: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt"></i>
                        إضافة حماية CSRF
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> حول حماية CSRF</h6>
                        <p>Cross-Site Request Forgery (CSRF) هو نوع من الهجمات حيث يتم خداع المستخدم لتنفيذ إجراءات غير مرغوب فيها على موقع ويب موثوق به.</p>
                        <p>هذه الأداة ستقوم بإضافة حماية CSRF شاملة لجميع النماذج والطلبات في النظام.</p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">الميزات المضافة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> CSRF tokens لجميع النماذج</li>
                                        <li><i class="fas fa-check text-success"></i> التحقق التلقائي من التوكن</li>
                                        <li><i class="fas fa-check text-success"></i> حماية طلبات AJAX</li>
                                        <li><i class="fas fa-check text-success"></i> تجديد التوكن دورياً</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">الملفات المتأثرة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-file text-info"></i> جميع ملفات النماذج</li>
                                        <li><i class="fas fa-file text-info"></i> ajax_handler.php</li>
                                        <li><i class="fas fa-file text-info"></i> header.php</li>
                                        <li><i class="fas fa-file text-info"></i> ملفات JavaScript</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من إضافة حماية CSRF لجميع النماذج؟')">
                        <div class="text-center">
                            <button type="submit" name="add_csrf_protection" class="btn btn-success btn-lg">
                                <i class="fas fa-shield-alt"></i> إضافة حماية CSRF
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4">
                        <h6>كيف تعمل حماية CSRF:</h6>
                        <ol>
                            <li><strong>إنشاء التوكن:</strong> يتم إنشاء توكن فريد لكل جلسة</li>
                            <li><strong>إضافة للنماذج:</strong> يتم إضافة التوكن كحقل مخفي في كل نموذج</li>
                            <li><strong>التحقق:</strong> يتم التحقق من التوكن عند استلام الطلب</li>
                            <li><strong>الرفض:</strong> يتم رفض الطلبات التي لا تحتوي على توكن صحيح</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
