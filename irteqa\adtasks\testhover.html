<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Card with Hover Button</title>
<style>
  /* استيلات الكارد */
  .card {
    width: 300px;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
    position: relative;
  }

  .card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }

  /* زر الإظهار */
  .btn {
    position: absolute;
    bottom: -30px;
    right: 20px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
    opacity: 0; /* يكون غير مرئي بشكل افتراضي */
    transition: opacity 0.3s ease;
  }

  /* ظهور الزر عند تحويل الماوس على الكارد */
  .card:hover .btn {
    opacity: 1;
  }
</style>
</head>
<body>

<div class="card">
  <h2>عنوان الكارد</h2>
  <p>محتوى الكارد. يمكنك وضع أي شيء هنا.</p>
  <button class="btn">زر مخفي</button>
</div>

</body>
</html>
