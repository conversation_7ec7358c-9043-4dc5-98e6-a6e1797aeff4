/*! elementor - v3.11.5 - 14-03-2023 */
@import "//fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap";
@import "//fonts.googleapis.com/css2?family=DM%20Sans&display=swap";
@import "//fonts.googleapis.com/css2?family=Source%20Serif%20Pro&display=swap";
:root {
  --color-box-shadow-color: rgba(0, 0, 0, 0.05);
}

.eps-theme-dark {
  --color-box-shadow-color: rgba(0, 0, 0, 0.1);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@media screen and (min-width: 480px) {
  .text-start-sm {
    text-align: start;
  }
}
@media screen and (min-width: 480px) {
  .text-center-sm {
    text-align: center;
  }
}
@media screen and (min-width: 480px) {
  .text-end-sm {
    text-align: end;
  }
}
@media screen and (min-width: 768px) {
  .text-start-md {
    text-align: start;
  }
}
@media screen and (min-width: 768px) {
  .text-center-md {
    text-align: center;
  }
}
@media screen and (min-width: 768px) {
  .text-end-md {
    text-align: end;
  }
}
@media screen and (min-width: 1025px) {
  .text-start-lg {
    text-align: start;
  }
}
@media screen and (min-width: 1025px) {
  .text-center-lg {
    text-align: center;
  }
}
@media screen and (min-width: 1025px) {
  .text-end-lg {
    text-align: end;
  }
}
@media screen and (min-width: 1440px) {
  .text-start-xl {
    text-align: start;
  }
}
@media screen and (min-width: 1440px) {
  .text-center-xl {
    text-align: center;
  }
}
@media screen and (min-width: 1440px) {
  .text-end-xl {
    text-align: end;
  }
}
@media screen and (min-width: 1600px) {
  .text-start-xxl {
    text-align: start;
  }
}
@media screen and (min-width: 1600px) {
  .text-center-xxl {
    text-align: center;
  }
}
@media screen and (min-width: 1600px) {
  .text-end-xxl {
    text-align: end;
  }
}
@keyframes eps-animation-pop {
  from {
    transform: scale(0.75);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
/**
TODO: The molecules, atoms and such generics should be at top level, so the styles will be not depended on the order.
EG: '../../../app/assets/styles/generic'.
Auto-import is designed for CSS that not dependent on the loading order.
 */
.eps-button {
  display: inline-flex;
  --button-line-height: 16px;
  --button-padding-y: 0.5em;
  --button-padding-x: 1.5em;
  --button-primary-background-color: #39b54a;
  --button-primary-hover-background-color: #33a242;
  --button-primary-active-background-color: #35a945;
  --button-primary-color: #fff;
  --button-secondary-background-color: #a4afb7;
  --button-secondary-hover-background-color: #96a2ac;
  --button-secondary-active-background-color: #9ba7b0;
  --button-secondary-color: #fff;
  --button-danger-background-color: #b01b1b;
  --button-danger-hover-background-color: #9a1818;
  --button-danger-active-background-color: #a31919;
  --button-danger-color: #fff;
  --button-cta-background-color: #93003F;
  --button-cta-hover-background-color: #7a0034;
  --button-cta-active-background-color: #840038;
  --button-cta-color: #fff;
  --button-link-background-color: #58d0f5;
  --button-link-hover-background-color: #40c9f4;
  --button-link-active-background-color: #4accf4;
  --button-link-color: #fff;
  --button-disabled-background-color: #c2cbd2;
  --button-disabled-hover-background-color: #b3bec7;
  --button-disabled-active-background-color: #b9c3cc;
  --button-disabled-color: #fff;
  color: var(--button-background-color, currentColor);
  font-size: var(--button-font-size, inherit);
  font-weight: 500;
  line-height: var(--button-line-height);
  cursor: pointer;
}
.eps-button:active {
  --button-background-color: var(--button-active-background-color, transparent);
}
.eps-button:hover {
  --button-background-color: var(--button-hover-background-color);
}
.eps-theme-dark .eps-button {
  --button-primary-background-color: #39b54a;
  --button-primary-color: #fff;
  --button-primary-hover-background-color: #33a242;
  --button-primary-active-background-color: #35a945;
  --button-secondary-background-color: #b4b5b7;
  --button-secondary-color: #fff;
  --button-secondary-hover-background-color: #a7a8ab;
  --button-secondary-active-background-color: #acadb0;
  --button-cta-background-color: #93003F;
  --button-cta-hover-background-color: #7a0034;
  --button-cta-active-background-color: #840038;
  --button-cta-color: #fff;
  --button-link-background-color: #58d0f5;
  --button-link-hover-background-color: #40c9f4;
  --button-link-active-background-color: #4accf4;
  --button-link-color: #fff;
  --button-disabled-background-color: #64666a;
  --button-disabled-hover-background-color: #58595d;
  --button-disabled-active-background-color: #5d5e62;
  --button-disabled-color: #fff;
}
.eps-button--contained {
  color: var(--button-color);
  padding: var(--button-padding-y) var(--button-padding-x);
  background-color: var(--button-background-color, transparent);
  border: 1px solid var(--button-background-color);
}
.eps-button--contained:hover {
  color: var(--button-color);
}
.eps-button--outlined {
  display: block;
  padding: var(--button-padding-y) var(--button-padding-x);
  border: 1px solid var(--button-background-color);
}
.eps-button--contained, .eps-button--outlined {
  border-radius: 0.1875rem;
}
.eps-button--underlined {
  text-decoration: underline;
}
.eps-button--sm {
  --button-font-size: 0.75rem;
  --button-line-height: 14px;
}
.eps-button--lg {
  --button-font-size: 0.9375rem;
  --button-line-height: 18px;
}
.eps-button--primary {
  --button-color: var(--button-primary-color);
  --button-background-color: var(--button-primary-background-color);
  --button-hover-background-color: var(--button-primary-hover-background-color);
  --button-active-background-color: var(--button-primary-active-background-color);
}
.eps-button--secondary {
  --button-color: var(--button-secondary-color);
  --button-background-color: var(--button-secondary-background-color);
  --button-hover-background-color: var(--button-secondary-hover-background-color);
  --button-active-background-color: var(--button-secondary-active-background-color);
}
.eps-button--danger {
  --button-color: var(--button-danger-color);
  --button-background-color: var(--button-danger-background-color);
  --button-hover-background-color: var(--button-danger-hover-background-color);
  --button-active-background-color: var(--button-danger-active-background-color);
}
.eps-button--cta {
  --button-color: var(--button-cta-color);
  --button-background-color: var(--button-cta-background-color);
  --button-hover-background-color: var(--button-cta-hover-background-color);
  --button-active-background-color: var(--button-cta-active-background-color);
}
.eps-button--link {
  --button-color: var(--button-link-color);
  --button-background-color: var(--button-link-background-color);
  --button-hover-background-color: var(--button-link-hover-background-color);
  --button-active-background-color: var(--button-link-active-background-color);
}
.eps-button--disabled, .eps-button[disabled] {
  --button-color: var(--button-disabled-color);
  --button-background-color: var(--button-disabled-background-color);
  --button-hover-background-color: var(--button-disabled-hover-background-color);
  --button-active-background-color: var(--button-disabled-active-background-color);
  cursor: default;
}

:root {
  --app-background-color: #f1f3f5;
  --app-box-shadow-color: rgba(var(--box-shadow-color, rgba(0, 0, 0, 0.15)), 0.2);
  --app-header-background-color: #fff;
  --app-header-color: #495157;
  --app-sidebar-background-color: rgba(255, 255, 255, 0.5);
  --app-header-buttons-separator-color: #d5dadf;
  --app-header-buttons-color: #a4afb7;
  --app-lightbox-background-color: rgba(0, 0, 0, 0.8);
}

.eps-theme-dark {
  --app-background-color: #34383c;
  --app-box-shadow-color: rgba(var(--box-shadow-color, rgba(0, 0, 0, 0.15)), 0.2);
  --app-header-background-color: #26292C;
  --app-header-color: #e0e1e3;
  --app-sidebar-background-color: #34383c;
  --app-header-buttons-separator-color: #64666a;
  --app-header-buttons-color: #b4b5b7;
  --app-lightbox-background-color: rgba(0, 0, 0, 0.8);
}

:root {
  --text-muted: #d5dadf;
  --disabled: #c2cbd2;
  --light: #fff;
  --dark: #000;
  --info: #58d0f5;
  --cta: #93003F;
  --danger: #b01b1b;
  --success: #39b54a;
  --warning: #fcb92c;
  --body-color: #6d7882;
  --link-color: #58d0f5;
  --link-hover-color: #10bcf1;
  --hr-color: #d5dadf;
  --box-shadow-color: theme-colors(dark);
  --display-1-color: #495157;
  --display-2-color: #495157;
  --display-3-color: #6d7882;
  --display-4-color: #495157;
  --h1-color: #6d7882;
  --h2-color: #6d7882;
  --h3-color: #495157;
  --h4-color: #495157;
  --h5-color: #495157;
  --h6-color: #495157;
  --text-base-color: #495157;
  --text-xl-color: #495157;
  --text-lg-color: #495157;
  --text-sm-color: #495157;
  --text-xs-color: #495157;
  --text-xxs-color: #495157;
  --gray-800: #495157;
  --gray-700: #556068;
  --gray-600: #6d7882;
  --gray-500: #a4afb7;
  --gray-400: #c2cbd2;
  --gray-300: #d5dadf;
  --gray-200: #f1f3f5;
  --gray-100: #fcfcfc;
}

.eps-theme-dark {
  --text-muted: #7d7e82;
  --disabled: #64666a;
  --light: #fff;
  --dark: #000;
  --info: #58d0f5;
  --accent: #58d0f5;
  --danger: #F84343;
  --cta: #93003F;
  --success: #39b54a;
  --warning: #fcb92c;
  --body-color: #e0e1e3;
  --body-bg: #fff;
  --link-color: #58d0f5;
  --link-hover-color: #10bcf1;
  --hr-color: #4c4f56;
  --box-shadow-color: rgba(0, 0, 0, 0.15);
  --display-1-color: #e0e1e3;
  --display-2-color: #e0e1e3;
  --display-3-color: #e0e1e3;
  --display-4-color: #e0e1e3;
  --h1-color: #e0e1e3;
  --h2-color: #e0e1e3;
  --h3-color: #e0e1e3;
  --h4-color: #e0e1e3;
  --h5-color: #e0e1e3;
  --h6-color: #e0e1e3;
  --text-base-color: #b4b5b7;
  --text-xl-color: #b4b5b7;
  --text-lg-color: #b4b5b7;
  --text-sm-color: #b4b5b7;
  --text-xs-color: #b4b5b7;
  --text-xxs-color: #b4b5b7;
  --gray-800: #26292C;
  --gray-700: #34383c;
  --gray-600: #404349;
  --gray-500: #4c4f56;
  --gray-400: #64666a;
  --gray-300: #7d7e82;
  --gray-200: #b4b5b7;
  --gray-100: #e0e1e3;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: sans-serif;
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

article, aside, figcaption, figure, footer, header, hgroup, main, nav, section {
  display: block;
}

body {
  margin: 0;
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--body-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--body-bg);
}

::-moz-selection {
  background-color: #58d0f5;
  color: #fff;
}

::selection {
  background-color: #58d0f5;
  color: #fff;
}

[tabindex="-1"]:focus:not(:focus-visible) {
  outline: 0 !important;
}

h1, h2, h3, h4, h5, h6 {
  font-size: 100%;
  margin: 0;
  padding: 0;
  line-height: inherit;
  font-weight: normal;
}

p {
  margin-top: 0;
}

b,
strong {
  font-weight: 700;
}

small {
  font-size: 80%;
}

a {
  --eps-link-color: $eps-link-color;
  color: var(--eps-link-color);
  background-color: transparent;
}
a, a:active, a:hover, a:focus {
  text-decoration: none;
}
a:focus, a:hover {
  --eps-link-color: $eps-link-hover-color;
  text-decoration: none;
}

a:not([href]) {
  color: inherit;
  text-decoration: none;
}
a:not([href]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: monospace;
  font-size: 1em;
}

figure {
  margin: 0 0 0;
}

img {
  vertical-align: middle;
  border-style: none;
}

svg {
  overflow: hidden;
  vertical-align: middle;
}

button {
  border-radius: 0;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
     -moz-appearance: button;
          appearance: button;
}

button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

button::-moz-focus-inner,
[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type=radio],
input[type=checkbox] {
  box-sizing: border-box;
  padding: 0;
}

textarea {
  overflow: auto;
  resize: vertical;
}

[hidden] {
  display: none !important;
}

hr {
  border: 0 none;
  border-bottom: 1px solid var(--hr-color);
}

.eps-display-1 {
  font-size: 1.85rem;
  color: var(--display-1-color);
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.eps-display-2 {
  font-size: 1.85rem;
  color: var(--display-2-color);
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.eps-display-3 {
  font-size: 1.85rem;
  color: var(--display-3-color);
  margin-top: 0;
  margin-bottom: 1.25rem;
}

.eps-display-4 {
  font-size: 1.85rem;
  color: var(--display-4-color);
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

h1,
.eps-h1 {
  font-size: 1.625rem;
  line-height: 1;
  color: var(--h1-color);
  margin-bottom: 1.25rem;
  font-weight: 500;
}

h2,
.eps-h2 {
  font-size: 1.25rem;
  line-height: 1.2;
  color: var(--h2-color);
  margin-top: 0;
  margin-bottom: 1.25rem;
  font-weight: 500;
}

h3,
.eps-h3 {
  font-size: 1rem;
  line-height: 1.2;
  color: var(--h3-color);
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

h4,
.eps-h4 {
  font-size: 0.9375rem;
  color: var(--h4-color);
  margin-top: 0;
  margin-bottom: 0.5rem;
}

h5,
.eps-h5 {
  font-size: 0.875rem;
  color: var(--h5-color);
  margin-top: 0;
  margin-bottom: 0.5rem;
}

h6,
.eps-h6 {
  font-size: 0.875rem;
  color: var(--h-6-color);
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.eps-text-xxs {
  font-size: 0.75rem;
  line-height: 1.2;
  color: var(--text-xxs-color);
  font-weight: 400;
}

.eps-text-xs {
  font-size: 0.75rem;
  line-height: 1.5;
  color: var(--text-xs-color);
  font-weight: 400;
}

.eps-text {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-base-color);
  font-weight: 400;
}

.eps-text-sm {
  font-size: 0.8125rem;
  line-height: 1.5;
  color: var(--text-sm-color);
  font-weight: 400;
}

.eps-text-lg {
  font-size: 0.9375rem;
  line-height: 1.5;
  color: var(--text-lg-color);
  font-weight: 400;
}

.eps-text-xl {
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-xl-color);
  font-weight: 400;
}

.video-wrapper {
  position: relative;
  padding-bottom: 56.25%;
  height: 0;
}
.video-wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.eps-separator {
  margin-bottom: 2.75rem;
}

.eps-theme-dark {
  --e-app-back-button-color: #b4b5b7;
}

.back-button, .e-app-back-button {
  --button-background-color: var(--e-app-back-button-color, #a4afb7);
  margin-bottom: 1.5rem;
}
.back-button .eps-icon, .e-app-back-button .eps-icon {
  -webkit-margin-end: 0.3125rem;
          margin-inline-end: 0.3125rem;
}

.eps-theme-dark {
  --input-border-color: --hr-color;
}

.eps-input {
  border: 1px solid var(--hr-color);
  border-radius: 0.1875rem;
  background: transparent;
  color: inherit;
  height: 1.875rem;
  padding: 0 0.3125rem;
}
.eps-input--block {
  width: 100%;
}

.eps-app {
  display: flex;
  height: 100vh;
  flex-direction: column;
  color: var(--body-color);
  background-color: var(--app-background-color);
  position: absolute;
  border-radius: 0;
  box-shadow: 2px 8px 23px 3px var(--color-box-shadow-color);
  overflow: hidden;
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  width: 100%;
  max-width: 100%;
}
.eps-app__lightbox {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  height: 100%;
  width: 100%;
  background-color: var(--app-lightbox-background-color);
  z-index: 1040;
  bottom: 0;
  left: 0;
}
.eps-app__header {
  flex-shrink: 0;
  font-size: 0.9375rem;
  color: var(--app-header-color);
  background-color: var(--app-header-background-color);
  box-shadow: 0 4px 10px var(--color-box-shadow-color);
  position: relative;
  z-index: 3;
  height: 3.125rem;
  padding: 0 1rem;
}
.eps-app__header-buttons {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
  font-size: 1.125rem;
}
.eps-app__header-btn {
  --button-background-color: var(--app-header-buttons-color);
  -webkit-padding-start: 1rem;
          padding-inline-start: 1rem;
  font-size: 1.125rem;
  line-height: 1.25rem;
}
.eps-app__header-btn:first-child {
  -webkit-border-start: 1px solid var(--app-header-buttons-separator-color);
          border-inline-start: 1px solid var(--app-header-buttons-separator-color);
}
.eps-app__header-btn:not(:first-child) {
  -webkit-padding-end: 1rem;
          padding-inline-end: 1rem;
}
.eps-app__logo-title-wrapper {
  display: flex;
  align-items: center;
}
.eps-app__logo {
  display: block;
  width: 1.75rem;
  height: 1.75rem;
  line-height: 1.75rem;
  text-align: center;
  font-size: calc(0.4 * 1.75rem);
  border-radius: 50%;
  color: #fff;
  background-color: #93003F;
}
.eps-app__logo:not(:last-child) {
  -webkit-margin-end: 0.625rem;
          margin-inline-end: 0.625rem;
}
.eps-app__title {
  font-size: 0.9375rem;
  font-weight: 700;
  text-transform: uppercase;
  margin-bottom: 0;
}
.eps-app__main {
  display: flex;
  overflow: hidden;
  flex-grow: 1;
}
.eps-app__sidebar {
  background-color: var(--app-sidebar-background-color);
  padding-top: 1.25rem;
  width: 30%;
  max-width: 17.1875rem;
  flex-grow: 0;
  overflow-y: auto;
  box-shadow: 2px 8px 23px 3px var(--color-box-shadow-color);
  z-index: 4;
}
.eps-app__content {
  flex-grow: 1;
  position: relative;
  padding: 2.75rem;
  height: 100%;
  overflow-y: auto;
}

.e-app-upload-file__input {
  display: none;
}

.e-app-drop-zone {
  --e-app-drop-zone-icon-color: #c2cbd2;
  --e-app-drop-zone-text-color: #a4afb7;
  --e-app-drop-zone-secondary-text-color: #6d7882;
}
.e-app-drop-zone__icon {
  margin-bottom: 2.75rem;
  color: var(--e-app-drop-zone-icon-color);
  font-size: 60px;
}
.e-app-drop-zone__text {
  color: var(--e-app-drop-zone-text-color);
}
.e-app-drop-zone__secondary-text {
  color: var(--e-app-drop-zone-secondary-text-color);
}

.eps-theme-dark .e-app-drop-zone {
  --e-app-drop-zone-icon-color: #c2cbd2;
  --e-app-drop-zone-text-color: #b4b5b7;
  --e-app-drop-zone-secondary-text-color: #e0e1e3;
}

:root {
  --info-toggle-color: #d5dadf;
  --info-toggle-hover-color: #a4afb7;
}

.eps-theme-dark {
  --placeholder-filter: invert(0.8) sepia(1) saturate(0.2) hue-rotate(180deg) contrast(1.25) brightness(1.2);
  --info-toggle-color: #64666a;
  --info-toggle-hover-color: #b4b5b7;
}

.e-site-part .eps-card__image {
  filter: var(--placeholder-filter, none);
}
.e-site-part__info-toggle {
  color: var(--info-toggle-color);
}
.e-site-part__info-toggle:hover {
  --info-toggle-color: var(--info-toggle-hover-color);
}

.e-site-editor__header {
  margin-bottom: 2.75rem;
  border-bottom: 1px solid var(--hr-color);
}

:root {
  --e-elementor-loader-container-background-color: #f1f3f5;
  --e-elementor-loader-background-color: rgba(255, 255, 255, 0.9);
}

.eps-theme-dark {
  --e-elementor-loader-container-background-color: #34383c;
  --e-elementor-loader-background-color: #4c4f56;
}

.elementor-loading {
  background-color: var(--e-elementor-loader-container-background-color);
  height: 100vh;
}

.elementor-loader-wrapper {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.elementor-loader {
  border-radius: 50%;
  padding: 40px;
  height: 150px;
  width: 150px;
  background-color: var(--e-elementor-loader-background-color);
  box-sizing: border-box;
  box-shadow: 2px 2px 20px 4px rgba(0, 0, 0, 0.02);
}

.elementor-loader-boxes {
  height: 100%;
  width: 100%;
  position: relative;
}

.elementor-loader-box {
  position: absolute;
  background-color: #d5dadf;
  animation: load 1.8s linear infinite;
}
.elementor-loader-box:nth-of-type(1) {
  width: 20%;
  height: 100%;
  left: 0;
  top: 0;
}
.elementor-loader-box:not(:nth-of-type(1)) {
  right: 0;
  height: 20%;
  width: 60%;
}
.elementor-loader-box:nth-of-type(2) {
  top: 0;
  animation-delay: calc( 1.8s / 4 * -1 );
}
.elementor-loader-box:nth-of-type(3) {
  top: 40%;
  animation-delay: calc( 1.8s / 4 * -2 );
}
.elementor-loader-box:nth-of-type(4) {
  bottom: 0;
  animation-delay: calc( 1.8s / 4 * -3 );
}

.elementor-loading-title {
  color: #a4afb7;
  text-align: center;
  text-transform: uppercase;
  margin-top: 30px;
  letter-spacing: 7px;
  text-indent: 7px;
  font-size: 10px;
  width: 100%;
}

@keyframes load {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}
.eps-menu__title {
  margin-top: 2.75rem;
  margin-bottom: 1rem;
}

.e-app-import {
  --e-app-import-back-to-library-color: #a4afb7;
  padding-bottom: 1.25rem;
}
.e-app-import__drop-zone {
  margin-top: 1.25rem;
}
.e-app-import__back-to-library {
  color: var(--e-app-import-back-to-library-color);
  margin-bottom: 1.5rem;
}
.e-app-import__back-to-library > i {
  -webkit-margin-end: 0.5rem;
          margin-inline-end: 0.5rem;
}

.eps-theme-dark .e-app-import {
  --e-app-import-back-to-library-color: #b4b5b7;
}

.e-site-editor__promotion-overlay__link {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  text-decoration: none;
}
.e-site-editor__promotion-overlay__icon {
  font-size: 1.25rem;
  color: #fff;
  margin-bottom: 1rem;
}

.e-app-import-export-wizard-step {
  --e-app-import-export-wizard-step-icon-color: #c2cbd2;
  --e-app-import-export-wizard-step-text-color: #a4afb7;
  --e-app-import-export-wizard-step-bottom-text-color: #a4afb7;
  height: 100%;
  position: relative;
  text-align: center;
}
.e-app-import-export-wizard-step__media-container {
  height: 140px;
  margin: 5.5rem 0 2.75rem;
}
.e-app-import-export-wizard-step__icon {
  color: var(--e-app-import-export-wizard-step-icon-color);
  font-size: 50px;
}
.e-app-import-export-wizard-step__icon.eicon-loading {
  font-size: 1.85rem;
}
.e-app-import-export-wizard-step__heading {
  margin-bottom: 1.5rem;
}
.e-app-import-export-wizard-step__description, .e-app-import-export-wizard-step__info {
  color: var(--e-app-import-export-wizard-step-text-color);
}
.e-app-import-export-wizard-step__info {
  margin-top: 1.5rem;
}
.e-app-import-export-wizard-step__content {
  text-align: initial;
  margin-bottom: 1.25rem;
}
.e-app-import-export-wizard-step__notice {
  display: block;
  position: sticky;
  top: 100%; /* Will prevent text overlapping when window height is too short. */
  color: var(--e-app-import-export-wizard-step-bottom-text-color);
  font-style: italic;
  margin-bottom: 0;
}

.eps-theme-dark .e-app-import-export-wizard-step {
  --e-app-import-export-wizard-step-icon-color: #64666a;
  --e-app-import-export-wizard-step-text-color: #b4b5b7;
  --e-app-import-export-wizard-step-bottom-text-color: #b4b5b7;
}

.e-app-import-export-page-header {
  --e-app-import-export-page-header-border-bottom-color: #d5dadf;
  --e-app-import-export-page-header-heading-color: #6d7882;
  --e-app-import-export-page-header-description-color: #a4afb7;
  border-bottom: 1px solid var(--e-app-import-export-page-header-border-bottom-color);
  margin-bottom: 2.75rem;
}
.e-app-import-export-page-header__content-wrapper {
  max-width: 645px;
}
.e-app-import-export-page-header__heading {
  color: var(--e-app-import-export-page-header-heading-color);
}
.e-app-import-export-page-header__description {
  color: var(--e-app-import-export-page-header-description-color);
  margin-top: 1.25rem;
}

.eps-theme-dark .e-app-import-export-page-header {
  --e-app-import-export-page-header-border-bottom-color: #4c4f56;
  --e-app-import-export-page-header-heading-color: #e0e1e3;
  --e-app-import-export-page-header-description-color: #e0e1e3;
}

.e-app-export-kit-content {
  --e-app-export-kit-content-title-color: #556068;
  --e-app-export-kit-content-description-color: #6d7882;
}
.e-app-export-kit-content__checkbox {
  flex-shrink: 0;
  -webkit-margin-end: 0.75rem;
          margin-inline-end: 0.75rem;
}
.e-app-export-kit-content__title {
  color: var(--e-app-export-kit-content-title-color);
}
.e-app-export-kit-content__description {
  color: var(--e-app-export-kit-content-description-color);
  -webkit-margin-end: 1.25rem;
          margin-inline-end: 1.25rem;
}
.e-app-export-kit-content__notice {
  margin-top: 1rem;
}
.e-app-export-kit-content__small-notice {
  font-style: italic;
  color: var(--e-app-export-kit-content-description-color);
}

.eps-theme-dark .e-app-export-kit-content {
  --e-app-export-kit-content-title-color: #e0e1e3;
  --e-app-export-kit-content-description-color: #b4b5b7;
}

.e-app-wizard-footer {
  --e-app-wizard-footer-border-color: #d5dadf;
  padding: 0.5rem;
}
.e-app-wizard-footer__separator {
  border-top: 1px solid var(--e-app-wizard-footer-border-color);
}

.eps-theme-dark .e-app-wizard-footer {
  --e-app-wizard-footer-border-color: #4c4f56;
}

.e-app-export-templates-features__locked {
  --e-app-export-templates-features-locked-color: #a4afb7;
  color: var(--e-app-export-templates-features-locked-color);
}

.eps-theme-dark .e-app-export-templates-features__locked {
  --e-app-export-templates-features-locked-color: #7d7e82;
}

:root {
  --color-box-shadow-color: rgba(0, 0, 0, 0.05);
}

.eps-theme-dark {
  --color-box-shadow-color: rgba(0, 0, 0, 0.1);
}

:root {
  --card-background-color: rgba(255, 255, 255, 0.5);
  --card-background-color-hover: #fff;
  --card-box-shadow: 0 4px 10px var(--color-box-shadow-color);
  --card-header-footer-border: 1px solid #f1f3f5;
  --card-header-footer-active-border: 2px solid #e2e6ea;
  --card-headline-color: #6d7882;
  --card-figure-background-color: #f1f3f5;
  --card-image-overlay-background-color: rgba(0, 0, 0, 0.2);
}

.eps-theme-dark {
  --card-background-color: #404349;
  --card-background-color-hover: rgba(76, 79, 86, 0.8);
  --card-box-shadow: 0 3px 6px var(--color-box-shadow-color);
  --card-header-footer-border: 1px solid #34383c;
  --card-header-footer-active-border: 1px solid #26292C;
  --card-headline-color: #e0e1e3;
  --card-figure-background-color: #34383c;
  --card-image-overlay-background-color: rgba(52, 56, 60, 0.5);
}

.eps-card {
  background-color: var(--card-background-color);
  box-shadow: 0 4px 10px var(--color-box-shadow-color);
  border-radius: 0.1875rem;
  transition: 0.3s;
  font-size: 0.75rem;
  /*
   todo: TBD: Optionally remove headline styling in favor of a global atom depending on variation needs
  */
}
.eps-card__header {
  padding: 0.625rem;
  border-bottom: var(--card-header-footer-border);
  min-height: 2.5rem;
  display: flex;
  align-items: center;
}
.eps-card__header--padding {
  padding: var(--eps-card-header-padding);
}
.eps-card__headline {
  color: var(--card-headline-color);
  margin-bottom: 0;
  font-weight: 500;
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.eps-card__body {
  padding: 0.625rem 0.625rem;
}
.eps-card__body--padding {
  padding: var(--eps-card-body-padding);
}
.eps-card__figure {
  background-color: var(--card-figure-background-color);
  position: relative;
  padding-bottom: var(--card-image-aspect-ratio, 95.6%);
  overflow: hidden;
  height: 0;
}
.eps-card__image {
  width: 100%;
  -o-object-fit: contain;
     object-fit: contain;
  -o-object-position: top;
     object-position: top;
  position: absolute;
  top: 0;
  left: 0;
}
.eps-card__image-overlay {
  position: absolute;
  top: 0;
  background-color: var(--card-image-overlay-background-color);
  z-index: 1;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: 0.3s;
}
.eps-card__image-overlay:hover {
  opacity: 1;
}
.eps-card__footer {
  padding: 0.625rem;
  border-top: var(--card-header-footer-border);
  font-size: 0.6875rem;
}
.eps-card__footer--padding {
  padding: var(--eps-card-footer-padding);
}
.eps-card:hover {
  background-color: var(--card-background-color-hover);
}

:root {
  --menu-item-color: #6d7882;
  --menu-item-color-hover: #556068;
  --menu-item-background-color-active: #fff;
  --menu-item-icon-color: #a4afb7;
  --menu-item-action-button-color: #d5dadf;
}

.eps-theme-dark {
  --menu-item-color: #e0e1e3;
  --menu-item-color-hover: #e0e1e3;
  --menu-item-background-color-active: #404349;
  --menu-item-icon-color: #b4b5b7;
  --menu-item-action-button-color: #64666a;
}

.eps-menu-item {
  display: flex;
  align-items: center;
  position: relative;
  transition: 0.3s;
  --action-button-opacity: 0;
}
.eps-menu-item::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  height: 100%;
  width: var(--menu-item-pointer-width);
  background-color: #58d0f5;
}
.eps-menu-item:hover {
  --action-button-opacity: 1;
}
.eps-menu-item:hover, .eps-menu-item--active {
  --menu-item-color: var(--menu-item-color-hover);
  --eps-link-color: var(--menu-item-color-hover);
  --menu-item-icon-color: #58d0f5;
}
.eps-menu-item--active {
  background-color: var(--menu-item-background-color-active);
}
.eps-menu-item__link {
  padding: 0.5rem 1.875rem;
  min-height: 2.75rem;
  font-size: 0.75rem;
  line-height: 1.2;
  flex-grow: 1;
  display: flex;
  align-items: center;
  color: var(--menu-item-color);
  --eps-link-color: var(--menu-item-color);
}
.eps-menu-item__link:not(:last-child) {
  -webkit-padding-end: 0;
          padding-inline-end: 0;
}
.eps-menu-item__link .eps-icon {
  font-size: 1.125rem;
  color: var(--menu-item-icon-color);
  -webkit-margin-end: 0.75rem;
          margin-inline-end: 0.75rem;
}
.eps-menu-item__action-button {
  color: var(--menu-item-action-button-color);
  opacity: var(--action-button-opacity);
  padding: 0.625rem;
  transition: 0.3s;
  -webkit-margin-end: 1.25rem;
          margin-inline-end: 1.25rem;
}
.eps-menu-item--active {
  --menu-item-pointer-width: 0.3125rem;
  box-shadow: 0 3px 6px var(--color-box-shadow-color);
}
.eps-menu-item--active .eps-menu-item__link .eps-icon {
  color: #58d0f5;
}

.eps-grid-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
.eps-grid-container--no-wrap {
  flex-wrap: nowrap;
}
.eps-grid-container--wrap-reverse {
  flex-wrap: wrap-reverse;
}
.eps-grid-container--spacing {
  --grid-row-gutter: calc(-1 * calc(var(--grid-spacing-gutter) * (0.625rem / 10)));
  width: var(--grid-spacing-width);
  margin: var(--grid-row-gutter);
}
.eps-grid-container--spacing > .eps-grid-item {
  padding: var(--grid-spacing-gutter);
}
.eps-grid--direction-row {
  flex-direction: row;
}
.eps-grid--direction-row-reverse {
  flex-direction: row-reverse;
}
.eps-grid--direction-column {
  flex-direction: column;
}
.eps-grid--direction-column-reverse {
  flex-direction: column-reverse;
}
.eps-grid--justify-stretch {
  justify-content: stretch;
}
.eps-grid--justify-start {
  justify-content: flex-start;
}
.eps-grid--justify-center {
  justify-content: center;
}
.eps-grid--justify-end {
  justify-content: flex-end;
}
.eps-grid--justify-space-between {
  justify-content: space-between;
}
.eps-grid--justify-space-around {
  justify-content: space-around;
}
.eps-grid--justify-space-evenly {
  justify-content: space-evenly;
}
.eps-grid--align-content-stretch {
  align-content: stretch;
}
.eps-grid--align-content-start {
  align-content: flex-start;
}
.eps-grid--align-content-center {
  align-content: center;
}
.eps-grid--align-content-end {
  align-content: flex-end;
}
.eps-grid--align-content-space-between {
  align-content: space-between;
}
.eps-grid--align-items-start {
  align-items: flex-start;
}
.eps-grid--align-items-center {
  align-items: center;
}
.eps-grid--align-items-end {
  align-items: flex-end;
}
.eps-grid--align-items-baseline {
  align-items: baseline;
}
.eps-grid--align-items-stretch {
  align-items: stretch;
}

.eps-grid-item--zero-min-width {
  min-width: 0;
}

@media screen and (min-width: 480px) {
  .eps-grid-item-sm {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
@media screen and (min-width: 768px) {
  .eps-grid-item-md {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
@media screen and (min-width: 1025px) {
  .eps-grid-item-lg {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
@media screen and (min-width: 1440px) {
  .eps-grid-item-xl {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
@media screen and (min-width: 1600px) {
  .eps-grid-item-xxl {
    flex-grow: 1;
    max-width: 100%;
    flex-basis: 0;
  }
}
.eps-grid-item-xs-1 {
  flex-grow: 0;
  max-width: calc( 1 / 12 * 100% );
  flex-basis: calc( 1 / 12 * 100% );
}

.eps-grid-item-xs-2 {
  flex-grow: 0;
  max-width: calc( 2 / 12 * 100% );
  flex-basis: calc( 2 / 12 * 100% );
}

.eps-grid-item-xs-3 {
  flex-grow: 0;
  max-width: calc( 3 / 12 * 100% );
  flex-basis: calc( 3 / 12 * 100% );
}

.eps-grid-item-xs-4 {
  flex-grow: 0;
  max-width: calc( 4 / 12 * 100% );
  flex-basis: calc( 4 / 12 * 100% );
}

.eps-grid-item-xs-5 {
  flex-grow: 0;
  max-width: calc( 5 / 12 * 100% );
  flex-basis: calc( 5 / 12 * 100% );
}

.eps-grid-item-xs-6 {
  flex-grow: 0;
  max-width: calc( 6 / 12 * 100% );
  flex-basis: calc( 6 / 12 * 100% );
}

.eps-grid-item-xs-7 {
  flex-grow: 0;
  max-width: calc( 7 / 12 * 100% );
  flex-basis: calc( 7 / 12 * 100% );
}

.eps-grid-item-xs-8 {
  flex-grow: 0;
  max-width: calc( 8 / 12 * 100% );
  flex-basis: calc( 8 / 12 * 100% );
}

.eps-grid-item-xs-9 {
  flex-grow: 0;
  max-width: calc( 9 / 12 * 100% );
  flex-basis: calc( 9 / 12 * 100% );
}

.eps-grid-item-xs-10 {
  flex-grow: 0;
  max-width: calc( 10 / 12 * 100% );
  flex-basis: calc( 10 / 12 * 100% );
}

.eps-grid-item-xs-11 {
  flex-grow: 0;
  max-width: calc( 11 / 12 * 100% );
  flex-basis: calc( 11 / 12 * 100% );
}

.eps-grid-item-xs-12 {
  flex-grow: 0;
  max-width: calc( 12 / 12 * 100% );
  flex-basis: calc( 12 / 12 * 100% );
}

@media screen and (min-width: 480px) {
  .eps-grid-item-sm-1 {
    flex-grow: 0;
    max-width: calc( 1 / 12 * 100% );
    flex-basis: calc( 1 / 12 * 100% );
  }
  .eps-grid-item-sm-2 {
    flex-grow: 0;
    max-width: calc( 2 / 12 * 100% );
    flex-basis: calc( 2 / 12 * 100% );
  }
  .eps-grid-item-sm-3 {
    flex-grow: 0;
    max-width: calc( 3 / 12 * 100% );
    flex-basis: calc( 3 / 12 * 100% );
  }
  .eps-grid-item-sm-4 {
    flex-grow: 0;
    max-width: calc( 4 / 12 * 100% );
    flex-basis: calc( 4 / 12 * 100% );
  }
  .eps-grid-item-sm-5 {
    flex-grow: 0;
    max-width: calc( 5 / 12 * 100% );
    flex-basis: calc( 5 / 12 * 100% );
  }
  .eps-grid-item-sm-6 {
    flex-grow: 0;
    max-width: calc( 6 / 12 * 100% );
    flex-basis: calc( 6 / 12 * 100% );
  }
  .eps-grid-item-sm-7 {
    flex-grow: 0;
    max-width: calc( 7 / 12 * 100% );
    flex-basis: calc( 7 / 12 * 100% );
  }
  .eps-grid-item-sm-8 {
    flex-grow: 0;
    max-width: calc( 8 / 12 * 100% );
    flex-basis: calc( 8 / 12 * 100% );
  }
  .eps-grid-item-sm-9 {
    flex-grow: 0;
    max-width: calc( 9 / 12 * 100% );
    flex-basis: calc( 9 / 12 * 100% );
  }
  .eps-grid-item-sm-10 {
    flex-grow: 0;
    max-width: calc( 10 / 12 * 100% );
    flex-basis: calc( 10 / 12 * 100% );
  }
  .eps-grid-item-sm-11 {
    flex-grow: 0;
    max-width: calc( 11 / 12 * 100% );
    flex-basis: calc( 11 / 12 * 100% );
  }
  .eps-grid-item-sm-12 {
    flex-grow: 0;
    max-width: calc( 12 / 12 * 100% );
    flex-basis: calc( 12 / 12 * 100% );
  }
}
@media screen and (min-width: 768px) {
  .eps-grid-item-md-1 {
    flex-grow: 0;
    max-width: calc( 1 / 12 * 100% );
    flex-basis: calc( 1 / 12 * 100% );
  }
  .eps-grid-item-md-2 {
    flex-grow: 0;
    max-width: calc( 2 / 12 * 100% );
    flex-basis: calc( 2 / 12 * 100% );
  }
  .eps-grid-item-md-3 {
    flex-grow: 0;
    max-width: calc( 3 / 12 * 100% );
    flex-basis: calc( 3 / 12 * 100% );
  }
  .eps-grid-item-md-4 {
    flex-grow: 0;
    max-width: calc( 4 / 12 * 100% );
    flex-basis: calc( 4 / 12 * 100% );
  }
  .eps-grid-item-md-5 {
    flex-grow: 0;
    max-width: calc( 5 / 12 * 100% );
    flex-basis: calc( 5 / 12 * 100% );
  }
  .eps-grid-item-md-6 {
    flex-grow: 0;
    max-width: calc( 6 / 12 * 100% );
    flex-basis: calc( 6 / 12 * 100% );
  }
  .eps-grid-item-md-7 {
    flex-grow: 0;
    max-width: calc( 7 / 12 * 100% );
    flex-basis: calc( 7 / 12 * 100% );
  }
  .eps-grid-item-md-8 {
    flex-grow: 0;
    max-width: calc( 8 / 12 * 100% );
    flex-basis: calc( 8 / 12 * 100% );
  }
  .eps-grid-item-md-9 {
    flex-grow: 0;
    max-width: calc( 9 / 12 * 100% );
    flex-basis: calc( 9 / 12 * 100% );
  }
  .eps-grid-item-md-10 {
    flex-grow: 0;
    max-width: calc( 10 / 12 * 100% );
    flex-basis: calc( 10 / 12 * 100% );
  }
  .eps-grid-item-md-11 {
    flex-grow: 0;
    max-width: calc( 11 / 12 * 100% );
    flex-basis: calc( 11 / 12 * 100% );
  }
  .eps-grid-item-md-12 {
    flex-grow: 0;
    max-width: calc( 12 / 12 * 100% );
    flex-basis: calc( 12 / 12 * 100% );
  }
}
@media screen and (min-width: 1025px) {
  .eps-grid-item-lg-1 {
    flex-grow: 0;
    max-width: calc( 1 / 12 * 100% );
    flex-basis: calc( 1 / 12 * 100% );
  }
  .eps-grid-item-lg-2 {
    flex-grow: 0;
    max-width: calc( 2 / 12 * 100% );
    flex-basis: calc( 2 / 12 * 100% );
  }
  .eps-grid-item-lg-3 {
    flex-grow: 0;
    max-width: calc( 3 / 12 * 100% );
    flex-basis: calc( 3 / 12 * 100% );
  }
  .eps-grid-item-lg-4 {
    flex-grow: 0;
    max-width: calc( 4 / 12 * 100% );
    flex-basis: calc( 4 / 12 * 100% );
  }
  .eps-grid-item-lg-5 {
    flex-grow: 0;
    max-width: calc( 5 / 12 * 100% );
    flex-basis: calc( 5 / 12 * 100% );
  }
  .eps-grid-item-lg-6 {
    flex-grow: 0;
    max-width: calc( 6 / 12 * 100% );
    flex-basis: calc( 6 / 12 * 100% );
  }
  .eps-grid-item-lg-7 {
    flex-grow: 0;
    max-width: calc( 7 / 12 * 100% );
    flex-basis: calc( 7 / 12 * 100% );
  }
  .eps-grid-item-lg-8 {
    flex-grow: 0;
    max-width: calc( 8 / 12 * 100% );
    flex-basis: calc( 8 / 12 * 100% );
  }
  .eps-grid-item-lg-9 {
    flex-grow: 0;
    max-width: calc( 9 / 12 * 100% );
    flex-basis: calc( 9 / 12 * 100% );
  }
  .eps-grid-item-lg-10 {
    flex-grow: 0;
    max-width: calc( 10 / 12 * 100% );
    flex-basis: calc( 10 / 12 * 100% );
  }
  .eps-grid-item-lg-11 {
    flex-grow: 0;
    max-width: calc( 11 / 12 * 100% );
    flex-basis: calc( 11 / 12 * 100% );
  }
  .eps-grid-item-lg-12 {
    flex-grow: 0;
    max-width: calc( 12 / 12 * 100% );
    flex-basis: calc( 12 / 12 * 100% );
  }
}
@media screen and (min-width: 1440px) {
  .eps-grid-item-xl-1 {
    flex-grow: 0;
    max-width: calc( 1 / 12 * 100% );
    flex-basis: calc( 1 / 12 * 100% );
  }
  .eps-grid-item-xl-2 {
    flex-grow: 0;
    max-width: calc( 2 / 12 * 100% );
    flex-basis: calc( 2 / 12 * 100% );
  }
  .eps-grid-item-xl-3 {
    flex-grow: 0;
    max-width: calc( 3 / 12 * 100% );
    flex-basis: calc( 3 / 12 * 100% );
  }
  .eps-grid-item-xl-4 {
    flex-grow: 0;
    max-width: calc( 4 / 12 * 100% );
    flex-basis: calc( 4 / 12 * 100% );
  }
  .eps-grid-item-xl-5 {
    flex-grow: 0;
    max-width: calc( 5 / 12 * 100% );
    flex-basis: calc( 5 / 12 * 100% );
  }
  .eps-grid-item-xl-6 {
    flex-grow: 0;
    max-width: calc( 6 / 12 * 100% );
    flex-basis: calc( 6 / 12 * 100% );
  }
  .eps-grid-item-xl-7 {
    flex-grow: 0;
    max-width: calc( 7 / 12 * 100% );
    flex-basis: calc( 7 / 12 * 100% );
  }
  .eps-grid-item-xl-8 {
    flex-grow: 0;
    max-width: calc( 8 / 12 * 100% );
    flex-basis: calc( 8 / 12 * 100% );
  }
  .eps-grid-item-xl-9 {
    flex-grow: 0;
    max-width: calc( 9 / 12 * 100% );
    flex-basis: calc( 9 / 12 * 100% );
  }
  .eps-grid-item-xl-10 {
    flex-grow: 0;
    max-width: calc( 10 / 12 * 100% );
    flex-basis: calc( 10 / 12 * 100% );
  }
  .eps-grid-item-xl-11 {
    flex-grow: 0;
    max-width: calc( 11 / 12 * 100% );
    flex-basis: calc( 11 / 12 * 100% );
  }
  .eps-grid-item-xl-12 {
    flex-grow: 0;
    max-width: calc( 12 / 12 * 100% );
    flex-basis: calc( 12 / 12 * 100% );
  }
}
@media screen and (min-width: 1600px) {
  .eps-grid-item-xxl-1 {
    flex-grow: 0;
    max-width: calc( 1 / 12 * 100% );
    flex-basis: calc( 1 / 12 * 100% );
  }
  .eps-grid-item-xxl-2 {
    flex-grow: 0;
    max-width: calc( 2 / 12 * 100% );
    flex-basis: calc( 2 / 12 * 100% );
  }
  .eps-grid-item-xxl-3 {
    flex-grow: 0;
    max-width: calc( 3 / 12 * 100% );
    flex-basis: calc( 3 / 12 * 100% );
  }
  .eps-grid-item-xxl-4 {
    flex-grow: 0;
    max-width: calc( 4 / 12 * 100% );
    flex-basis: calc( 4 / 12 * 100% );
  }
  .eps-grid-item-xxl-5 {
    flex-grow: 0;
    max-width: calc( 5 / 12 * 100% );
    flex-basis: calc( 5 / 12 * 100% );
  }
  .eps-grid-item-xxl-6 {
    flex-grow: 0;
    max-width: calc( 6 / 12 * 100% );
    flex-basis: calc( 6 / 12 * 100% );
  }
  .eps-grid-item-xxl-7 {
    flex-grow: 0;
    max-width: calc( 7 / 12 * 100% );
    flex-basis: calc( 7 / 12 * 100% );
  }
  .eps-grid-item-xxl-8 {
    flex-grow: 0;
    max-width: calc( 8 / 12 * 100% );
    flex-basis: calc( 8 / 12 * 100% );
  }
  .eps-grid-item-xxl-9 {
    flex-grow: 0;
    max-width: calc( 9 / 12 * 100% );
    flex-basis: calc( 9 / 12 * 100% );
  }
  .eps-grid-item-xxl-10 {
    flex-grow: 0;
    max-width: calc( 10 / 12 * 100% );
    flex-basis: calc( 10 / 12 * 100% );
  }
  .eps-grid-item-xxl-11 {
    flex-grow: 0;
    max-width: calc( 11 / 12 * 100% );
    flex-basis: calc( 11 / 12 * 100% );
  }
  .eps-grid-item-xxl-12 {
    flex-grow: 0;
    max-width: calc( 12 / 12 * 100% );
    flex-basis: calc( 12 / 12 * 100% );
  }
}
:root {
  --menu-title-color: #6d7882;
}

.eps-theme-dark {
  --menu-title-color: #6d7882;
}

.eps-menu ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.eps-menu ul li {
  display: flex;
}
.eps-menu__title {
  padding: 0.5rem 1.875rem;
  font-size: 0.6875rem;
  line-height: 1.2;
  text-transform: uppercase;
  font-weight: normal;
  color: var(--menu-title-color);
}

:root {
  --eps-modal-background-color: #fff;
  --eps-modal-header-background-color: #58d0f5;
}

.eps-theme-dark {
  --eps-modal-background-color: #34383c;
  --eps-modal-header-background-color: #58d0f5;
}

/** ----------------------------------------------------------------
	EPS Modal
---------------------------------------------------------------- */
.eps-modal {
  max-width: 43.75rem;
  background: var(--eps-modal-background-color);
  border-radius: 0.1875rem;
  animation: eps-animation-pop 0.15s cubic-bezier(0.57, 0.53, 0.71, 1.47) forwards;
}
.eps-modal__overlay {
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  display: flex;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  z-index: 1030;
}
.eps-modal__header {
  font-size: 0.875rem;
  background: var(--eps-modal-header-background-color);
  height: 2.75rem;
  padding: 0.625rem 1rem;
  border-radius: 0.1875rem;
}
.eps-modal__header, .eps-modal__header .title {
  color: #fff;
}
.eps-modal__icon {
  -webkit-margin-end: 0.625rem;
          margin-inline-end: 0.625rem;
}
.eps-modal__body {
  padding: 1.875rem;
}
.eps-modal__tip, .eps-modal .eps-tip {
  -webkit-padding-start: 0.75rem;
          padding-inline-start: 0.75rem;
  -webkit-border-start: 3px solid #58d0f5;
          border-inline-start: 3px solid #58d0f5;
}
.eps-modal__tip:not(:last-child), .eps-modal .eps-tip:not(:last-child) {
  margin-bottom: 1.875rem;
}
.eps-modal__tip:not(:first-child), .eps-modal .eps-tip:not(:first-child) {
  margin-top: 1.875rem;
}
.eps-modal__section:not(:first-child) {
  margin-top: 1.875rem;
}
.eps-modal__close-wrapper {
  -webkit-padding-start: 1rem;
          padding-inline-start: 1rem;
  -webkit-border-start: solid 1px #fff;
          border-inline-start: solid 1px #fff;
}

.eps-add-new-button {
  display: inline-flex;
  --eps-add-new-button-size: 1.5rem;
  line-height: var(--eps-add-new-button-size);
  cursor: pointer;
}
.eps-add-new-button .eps-icon {
  background-color: #58d0f5;
  color: #fff;
  width: var(--eps-add-new-button-size);
  height: var(--eps-add-new-button-size);
  border-radius: 100%;
  font-size: calc(var(--eps-add-new-button-size) * 0.75);
  text-align: center;
  line-height: var(--eps-add-new-button-size);
}
.eps-add-new-button span:not(.sr-only) {
  -webkit-margin-start: 0.625rem;
          margin-inline-start: 0.625rem;
  font-weight: 500;
}
.eps-add-new-button--sm {
  --eps-add-new-button-size: 1rem;
}

:root {
  --select2-selection-background-color: #fff;
  --select2-selection-color: #6d7882;
  --select2-selection-border-color: #d5dadf;
  --select2-selection-opened-focused-border-color: #f1f3f5;
  --select2-single-selection-rendered-color: #6d7882;
  --select2-default-single-selection-background-color: #fff;
  --select2-default-single-selection-border-color: #d5dadf;
  --select2-default-multiple-selection-background-color: #fff;
  --select2-default-multiple-selection-choice-background-color: #f1f3f5;
  --select2-default-multiple-selection-choice-color: #6d7882;
  --select2-default-multiple-selection-choice-border-color: #f1f3f5;
  --select2-default-multiple-selection-choice-remove-color: #a4afb7;
  --select2-default-multiple-selection-choice-remove-hover-color: #6d7882;
  --select2-default-results-selected-option-background-color: #fff;
  --select2-default-results-selected-option-color: #6d7882;
  --select2-default-results-highlighted-option-background-color: #5897fb;
  --select2-default-results-highlighted-option-color: #fff;
  --select2-results-selected-option-background-color: #5897fb;
  --select2-results-selected-option-color: #fff;
  --select2-dropdown-background-color: #fff;
  --select2-dropdown-border-color: #d5dadf;
}

.eps-theme-dark {
  --select2-selection-background-color: #34383c;
  --select2-selection-color: #e0e1e3;
  --select2-selection-border-color: #64666a;
  --select2-selection-opened-focused-border-color: #7d7e82;
  --select2-single-selection-rendered-color: #e0e1e3;
  --select2-default-single-selection-background-color: #34383c;
  --select2-default-single-selection-border-color: #4c4f56;
  --select2-default-multiple-selection-background-color: #34383c;
  --select2-default-multiple-selection-choice-background-color: #4c4f56;
  --select2-default-multiple-selection-choice-color: #e0e1e3;
  --select2-default-multiple-selection-choice-border-color: #4c4f56;
  --select2-default-multiple-selection-choice-remove-color: #b4b5b7;
  --select2-default-multiple-selection-choice-remove-hover-color: #e0e1e3;
  --select2-default-results-selected-option-background-color: #34383c;
  --select2-default-results-selected-option-color: #e0e1e3;
  --select2-default-results-highlighted-option-background-color: #4c4f56;
  --select2-default-results-highlighted-option-color: #e0e1e3;
  --select2-results-selected-option-background-color: #4c4f56;
  --select2-results-selected-option-color: #e0e1e3;
  --select2-dropdown-background-color: #34383c;
  --select2-dropdown-border-color: #64666a;
}

.select2-container:not(.select2-container--open):not(.select2-container--focus) .select2-selection--single,
.select2-container:not(.select2-container--open):not(.select2-container--focus) .select2-selection--multiple {
  background-color: var(--select2-selection-background-color);
  color: var(--select2-selection-color);
  border-color: var(--select2-selection-border-color);
}
.select2-container.select2-container--open .select2-selection--single,
.select2-container.select2-container--open .select2-selection--multiple, .select2-container.select2-container--focus .select2-selection--single,
.select2-container.select2-container--focus .select2-selection--multiple {
  border-color: var(--select2-selection-opened-focused-border-color);
}
.select2-container.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: var(--select2-single-selection-rendered-color);
}
.select2-container--default .select2-selection--single {
  background-color: var(--select2-default-single-selection-background-color);
  border-color: var(--select2-default-single-selection-border-color);
}
.select2-container--default .select2-selection--multiple {
  background-color: var(--select2-default-multiple-selection-background-color);
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
  background-color: var(--select2-default-multiple-selection-choice-background-color);
  color: var(--select2-default-multiple-selection-choice-color);
  border-color: var(--select2-default-multiple-selection-choice-border-color);
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
  color: var(--select2-default-multiple-selection-choice-remove-color);
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
  color: var(--select2-default-multiple-selection-choice-remove-hover-color);
}
.select2-container--default .select2-results__option[aria-selected] {
  background-color: var(--select2-default-results-selected-option-background-color);
  color: var(--select2-default-results-selected-option-color);
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--select2-default-results-highlighted-option-background-color);
  color: var(--select2-default-results-highlighted-option-color);
}
.select2-container .select2-results__option[aria-selected=true] {
  background-color: var(--select2-results-selected-option-background-color);
  color: var(--select2-results-selected-option-color);
}
.select2-container .select2-dropdown {
  background-color: var(--select2-dropdown-background-color);
  border-color: var(--select2-dropdown-border-color);
}

.eps-notice {
  --eps-box-notice-color: #a4afb7;
  --eps-box-notice-background-color: #fcfcfc;
  padding: 0.625rem 1rem;
  box-shadow: 0 2px 3px 1px var(--color-box-shadow-color);
  background-color: var(--eps-box-notice-background-color);
}
.eps-notice-semantic {
  -webkit-border-start: 3px solid var(--eps-notice-semantic-color);
          border-inline-start: 3px solid var(--eps-notice-semantic-color);
}
.eps-notice-semantic .eps-notice__icon {
  color: var(--eps-notice-semantic-color);
  font-size: 1rem;
  -webkit-margin-end: 0.75rem;
          margin-inline-end: 0.75rem;
}
.eps-notice--warning {
  --eps-notice-semantic-color: #fcb92c;
}
.eps-notice--danger {
  --eps-notice-semantic-color: #b01b1b;
}
.eps-notice--info {
  --eps-notice-semantic-color: #58d0f5;
}
.eps-notice__text {
  margin: 0;
  padding: 0;
  color: var(--eps-box-notice-color);
  font-style: italic;
}
.eps-notice__button-container {
  flex-shrink: 0;
  margin-left: 1.25rem;
  width: auto;
}

.eps-theme-dark .eps-notice {
  --eps-box-notice-color: #b4b5b7;
  --eps-box-notice-background-color: #404349;
}

.eps-list {
  --eps-list-item-separated-border-color: #f1f3f5;
  padding: 0;
  margin: 0;
  list-style-type: none;
}
.eps-list--padding {
  padding: var(--eps-list-padding);
}
.eps-list__item {
  padding: 0;
}
.eps-list__item--padding {
  padding: var(--eps-list-item-padding);
}
.eps-list--separated .eps-list__item:not(:last-child) {
  border-bottom: 1px solid var(--eps-list-item-separated-border-color);
}

.eps-theme-dark .eps-list {
  --eps-list-item-separated-border-color: #34383c;
}

:root {
  --popover-background-color: #fff;
  --popover-item-color: #6d7882;
  --popover-item-hover-color: #556068;
  --popover-item-danger-hover-color: #b01b1b;
  --popover-item-background-color: #fff;
  --popover-box-shadow-color: rgba(0, 0, 0, 0.15);
  --popover-box-shadow-size: 0px 1px 20px;
  --popover-arrow-color: #fff;
}

.eps-theme-dark {
  --popover-background-color: #4c4f56;
  --popover-item-color: #fff;
  --popover-item-hover-color: #e0e1e3;
  --popover-item-danger-hover-color: #F84343;
  --popover-item-background-color: #4c4f56;
  --popover-box-shadow-color: rgba(0, 0, 0, 0.15);
  --popover-box-shadow-size: 0px 1px 20px;
  --popover-arrow-color: #4c4f56;
}

.eps-popover {
  padding: 10px 0;
  background-color: var(--popover-background-color);
  box-shadow: var(--popover-box-shadow-size) var(--popover-box-shadow-color);
  list-style: none;
  display: flex;
  flex-direction: column;
  min-width: 120px;
  border-radius: 0.1875rem;
  position: absolute;
  z-index: 1050;
  margin-top: 9px;
  transform: translateX(-50%);
  left: 0.25rem;
}
.eps-popover__background {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1030;
}
.eps-popover__container {
  position: relative;
}
.eps-popover::before {
  content: "";
  display: block;
  position: absolute;
  width: 16px;
  height: 9px;
  margin: 0 0.1875rem 9px;
  top: -9px;
  left: 50%;
  transform: translateX(-50%);
  border-color: transparent;
  border-style: solid;
  border-width: 0 calc(16px / 2) 9px calc(16px / 2);
  border-bottom-color: var(--popover-arrow-color);
}
.eps-popover__item {
  padding: 0.3125rem 1rem;
  background-color: var(--popover-item-background-color);
  color: var(--popover-item-color);
  font-size: 0.6875rem;
  font-weight: 500;
  line-height: 0.8125rem;
  width: 100%;
  align-items: center;
  cursor: pointer;
}
.eps-popover__item:hover {
  color: var(--popover-item-hover-color);
}
.eps-popover__item--danger:hover {
  color: var(--popover-item-danger-hover-color);
}
.eps-popover__item .eps-icon {
  font-size: inherit;
  -webkit-margin-end: 0.3125rem;
          margin-inline-end: 0.3125rem;
}

.eps-css-grid {
  display: grid;
  grid-template-columns: repeat(var(--eps-grid-columns, auto-fill), minmax(var(--eps-grid-col-min-width, 1fr), var(--eps-grid-col-max-width, 1fr)));
  grid-gap: var(--eps-grid-spacing);
}

.eps-box {
  --eps-box-background-color: #fff;
  --eps-box-color: #495157;
  --eps-box-input-color: #495157;
  padding: 0;
  border-radius: 0.1875rem;
  background-color: var(--eps-box-background-color);
  color: var(--eps-box-color);
}
.eps-box--padding {
  padding: var(--eps-box-padding);
}
.eps-box > input {
  width: 100%;
  outline: 0;
  border: 0;
  background-color: var(--eps-box-background-color);
  color: var(--eps-box-input-color);
}

.eps-theme-dark .eps-box {
  --eps-box-background-color: #404349;
  --eps-box-color: #e0e1e3;
  --eps-box-input-color: #e0e1e3;
}

:root {
  --checkbox-border-color: #d5dadf;
  --checkbox-hover-border-color: #c7cdd4;
  --checkbox-active-border-color: #e3e7ea;
  --checkbox-background-color: #fff;
  --checkbox-checked-background-color: #39b54a;
  --checkbox-checked-hover-background-color: #33a242;
  --checkbox-checked-active-background-color: #44c455;
  --checkbox-checked-disabled-background-color: #c2cbd2;
  --checkbox-indicator-color: #fff;
  --checkbox-error-background-color: #b01b1b;
}

.eps-theme-dark {
  --checkbox-background-color: transparent;
}

.eps-checkbox {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.1875rem;
  width: 15px;
  height: 15px;
  outline: 0;
  background-color: var(--checkbox-background-color);
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border: 1px solid var(--checkbox-border-color);
}
.eps-checkbox::after {
  display: inline-block;
  margin-bottom: calc(0.25 / 2 * 100%);
  content: " ";
  width: 3px;
  height: 6px;
  transform: rotate(45deg);
}
.eps-checkbox:hover {
  --checkbox-border-color: var(--checkbox-hover-border-color);
}
.eps-checkbox:active {
  --checkbox-border-color: var(--checkbox-active-border-color);
}
.eps-checkbox:checked {
  --checkbox-background-color: var(--checkbox-checked-background-color);
  --checkbox-border-color: var(--checkbox-checked-background-color);
}
.eps-checkbox:checked::after {
  border: solid #fff;
  border-width: 0 1px 1px 0;
}
.eps-checkbox:checked:hover {
  --checkbox-background-color: var(--checkbox-checked-hover-background-color);
  --checkbox-border-color: var(--checkbox-checked-hover-background-color);
}
.eps-checkbox:checked:active {
  --checkbox-background-color: var(--checkbox-checked-active-background-color);
  --checkbox-border-color: var(--checkbox-checked-active-background-color);
}
.eps-checkbox:checked:disabled {
  --checkbox-background-color: var(--checkbox-checked-disabled-background-color);
  --checkbox-border-color: var(--checkbox-checked-disabled-background-color);
}
.eps-checkbox--rounded {
  border-radius: 50%;
}
.eps-checkbox--indeterminate {
  --checkbox-background-color: var(--checkbox-checked-background-color);
  --checkbox-border-color: var(--checkbox-checked-background-color);
}
.eps-checkbox--indeterminate::after {
  display: inline-block;
  margin-bottom: 0;
  content: " ";
  width: 7px;
  height: 0;
  transform: rotate(0deg);
  border-top: 1px solid #fff;
}
.eps-checkbox--error::before, .eps-checkbox--error::after, .eps-checkbox--error:checked::before, .eps-checkbox--error:checked::after {
  display: inline-block;
  margin-bottom: 0;
  content: " ";
  width: 7px;
  height: 0;
  border: solid #fff;
  border-width: 1px 0 0 0;
  position: absolute;
}
.eps-checkbox--error::before, .eps-checkbox--error:checked::before {
  transform: rotate(45deg);
}
.eps-checkbox--error::after, .eps-checkbox--error:checked::after {
  transform: rotate(-45deg);
}
.eps-checkbox--error, .eps-checkbox--error:hover, .eps-checkbox--error:checked, .eps-checkbox--error:checked:hover {
  --checkbox-background-color: var(--checkbox-error-background-color);
  --checkbox-border-color: var(--checkbox-error-background-color);
}

:root {
  --e-app-drag-drop-background-color: #fff;
  --e-app-drag-drop-outline-color: #d5dadf;
}

.eps-theme-dark {
  --e-app-drag-drop-background-color: #404349;
  --e-app-drag-drop-outline-color: #7d7e82;
}

.e-app-drag-drop {
  background-color: var(--e-app-drag-drop-background-color);
  outline: 2px dashed var(--e-app-drag-drop-outline-color);
  outline-offset: -0.75rem;
  margin-bottom: 1.5rem;
  padding: 4.125rem;
  text-align: center;
}
.e-app-drag-drop--drag-over {
  outline-color: #58d0f5;
}

.eps-dialog {
  border-radius: 3px;
  width: 375px;
}
.eps-dialog__close-button {
  position: absolute;
  top: -2.75rem;
  right: -2.75rem;
  margin-top: 0.625rem;
  margin-right: 0.625rem;
  z-index: 1040;
  font-size: 1.25rem;
  color: #fff;
}
.eps-dialog__content {
  padding: 1.5rem 1.875rem 1rem;
  font-size: 0.75rem;
}
.eps-dialog__title, .eps-dialog__text {
  text-align: center;
}
.eps-dialog__buttons {
  display: flex;
}
.eps-dialog__button {
  flex: 1;
  border-top: 1px solid var(--hr-color);
  line-height: 2.75rem;
  text-align: center;
  justify-content: center;
}
.eps-dialog__button:last-child:not(:first-child) {
  -webkit-border-start: 1px solid var(--hr-color);
          border-inline-start: 1px solid var(--hr-color);
}

.e-app__popover {
  display: none;
  position: absolute;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
  border-radius: 6px;
  padding: 20px;
  width: -moz-fit-content;
  width: fit-content;
  z-index: 999;
  background-color: #ffffff;
}
.e-app__popover:before {
  content: "";
  position: absolute;
  top: -16px;
  right: var(--popover-arrow-offset-end, 22px);
  border: 8px solid transparent;
  border-bottom-color: #fff;
}

.eps-inline-link {
  color: var(--eps-inline-link-color);
  background-color: initial;
  border: 0;
  padding: 0;
}
.eps-inline-link--color-primary {
  --eps-inline-link-color: #39b54a;
}
.eps-inline-link--color-secondary {
  --eps-inline-link-color: #c2cbd2;
}
.eps-inline-link--color-danger {
  --eps-inline-link-color: #b01b1b;
}
.eps-inline-link--color-cta {
  --eps-inline-link-color: #93003F;
}
.eps-inline-link--color-link {
  --eps-inline-link-color: #58d0f5;
}
.eps-inline-link--color-disabled {
  --eps-inline-link-color: #c2cbd2;
}
.eps-inline-link--underline-hover:hover, .eps-inline-link--underline-always, .eps-inline-link--underline-always:hover {
  text-decoration: underline;
}
.eps-inline-link--italic {
  font-style: italic;
}
.eps-inline-link, .eps-inline-link:focus {
  outline: none;
}

.eps-text-field {
  --eps-text-field-color: #6d7882;
  --eps-text-field-background-color: #fff;
  --eps-text-field-placeholder-color: #a4afb7;
  --eps-text-field-outlined-border-color: #d5dadf;
  --eps-text-field-outlined-focus-border-color: #c2cbd2;
  width: 100%;
  color: var(--eps-text-field-color);
  background-color: var(--eps-text-field-background-color);
  border: 0;
  outline: none;
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.eps-text-field--outlined {
  border-radius: 0.1875rem;
  border: 1px solid var(--eps-text-field-outlined-border-color);
  padding: 0.625rem;
}
.eps-text-field--outlined:focus {
  border-color: var(--eps-text-field-outlined-focus-border-color);
}
.eps-text-field::-moz-placeholder {
  color: var(--eps-text-field-placeholder-color);
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.eps-text-field::placeholder {
  color: var(--eps-text-field-placeholder-color);
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  font-size: 0.875rem;
  font-weight: 400;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.eps-theme-dark .eps-text-field {
  --eps-text-field-color: #b4b5b7;
  --eps-text-field-background-color: #34383c;
  --eps-text-field-placeholder-color: #7d7e82;
  --eps-text-field-outlined-border-color: #64666a;
  --eps-text-field-outlined-focus-border-color: #7d7e82;
}

.e-app-import-export-content-layout {
  display: flex;
  justify-content: center;
  height: 100%;
}
.e-app-import-export-content-layout__container {
  flex-basis: 1075px;
}

.e-app-export-complete__kit-content-title {
  margin: 2.75rem 0 0.625rem;
}

.e-app-export-kit-content {
  --e-app-export-kit-content-title-color: #556068;
  --e-app-export-kit-content-description-color: #6d7882;
}
.e-app-export-kit-content__checkbox {
  flex-shrink: 0;
  -webkit-margin-end: 0.75rem;
          margin-inline-end: 0.75rem;
}
.e-app-export-kit-content__title {
  color: var(--e-app-export-kit-content-title-color);
}
.e-app-export-kit-content__description {
  color: var(--e-app-export-kit-content-description-color);
  -webkit-margin-end: 1.25rem;
          margin-inline-end: 1.25rem;
}
.e-app-export-kit-content__notice {
  margin-top: 1rem;
}
.e-app-export-kit-content__small-notice {
  font-style: italic;
  color: var(--e-app-export-kit-content-description-color);
}

.eps-theme-dark .e-app-export-kit-content {
  --e-app-export-kit-content-title-color: #e0e1e3;
  --e-app-export-kit-content-description-color: #b4b5b7;
}

.e-app-import-export-kit-data {
  --e-app-import-export-kit-data-site-area-color: #556068;
  --e-app-import-export-kit-data-included-color: #a4afb7;
}
.e-app-import-export-kit-data__site-area, .e-app-import-export-kit-data__included {
  margin-bottom: 0;
}
.e-app-import-export-kit-data__site-area {
  color: var(--e-app-import-export-kit-data-site-area-color);
  font-weight: bold;
}
.e-app-import-export-kit-data__included {
  color: var(--e-app-import-export-kit-data-included-color);
}

.eps-theme-dark .e-app-import-export-kit-data {
  --e-app-import-export-kit-data-site-area-color: #b4b5b7;
  --e-app-import-export-kit-data-included-color: #7d7e82;
}

.e-app-import-resolver {
  --e-app-import-resolver-panel-header-background-color: #fff;
  --e-app-import-resolver-panel-body-background-color: rgba(255, 255, 255, 0.5);
  --e-app-import-resolver-conflicts-asset-border-color: #c2cbd2;
  --e-app-import-resolver-conflicts-asset-inactive-color: #a4afb7;
  padding-bottom: 1.25rem;
}
.e-app-import-resolver__notice {
  margin-bottom: 1.25rem;
}
.e-app-import-resolver__panel, .e-app-import-resolver__panel:hover {
  background-color: initial;
}
.e-app-import-resolver__panel-header {
  background-color: var(--e-app-import-resolver-panel-header-background-color);
}
.e-app-import-resolver__panel-body {
  background-color: var(--e-app-import-resolver-panel-body-background-color);
}
.e-app-import-resolver-conflicts__container {
  box-shadow: 0 2px 3px 1px var(--color-box-shadow-color);
}
.e-app-import-resolver-conflicts__checkbox {
  flex-shrink: 0;
  -webkit-margin-end: 0.75rem;
          margin-inline-end: 0.75rem;
}
.e-app-import-resolver-conflicts__title {
  line-height: 1;
}
.e-app-import-resolver-conflicts__asset:not(:first-child) {
  -webkit-border-start: 2px solid var(--e-app-import-resolver-conflicts-asset-border-color);
          border-inline-start: 2px solid var(--e-app-import-resolver-conflicts-asset-border-color);
  -webkit-padding-start: 1rem;
          padding-inline-start: 1rem;
  -webkit-margin-start: 1rem;
          margin-inline-start: 1rem;
}
.e-app-import-resolver-conflicts__asset:not(.active) {
  color: var(--e-app-import-resolver-conflicts-asset-inactive-color);
}
.e-app-import-resolver-conflicts__edit-template {
  -webkit-margin-start: 0.3125rem;
          margin-inline-start: 0.3125rem;
}

.eps-theme-dark .e-app-import-resolver {
  --e-app-import-resolver-panel-header-background-color: #4c4f56;
  --e-app-import-resolver-panel-body-background-color: rgba(0, 0, 0, 0.05);
  --e-app-import-resolver-conflicts-asset-border-color: #64666a;
  --e-app-import-resolver-conflicts-asset-inactive-color: #7d7e82;
}

.eps-panel {
  --eps-panel-header-background-color: #fff;
  --eps-panel-body-background-color: rgba(255, 255, 255, 0.5);
}
.eps-panel, .eps-panel:hover {
  background-color: initial;
}
.eps-panel__header {
  background-color: var(--eps-panel-header-background-color);
  border-radius: 0.1875rem;
}
.eps-panel__body {
  background-color: var(--eps-panel-body-background-color);
  border-radius: 0 0 0.1875rem 0.1875rem;
}

.eps-theme-dark .eps-panel {
  --eps-panel-header-background-color: #404349;
  --eps-panel-body-background-color: rgba(64, 67, 73, 0.5);
}

.e-app-export-kit {
  padding-bottom: 1.25rem;
}
.e-app-export-kit-information {
  margin-top: 1.25rem;
}
.e-app-export-kit-information__field-header {
  margin-bottom: 0.625rem;
}
.e-app-export-kit-information__label {
  margin: 0;
}
.e-app-export-kit-information__info-icon {
  -webkit-margin-start: 0.625rem;
          margin-inline-start: 0.625rem;
}
.e-app-export-kit-info-modal__icon {
  -webkit-padding-start: 0.625rem;
          padding-inline-start: 0.625rem;
}
.e-app-export-kit-info-modal__heading {
  margin-bottom: 1.25rem;
}

.e-app-import-export-info-modal__section:not(:first-child) {
  margin-top: 1.875rem;
}
.e-app-import-export-info-modal__heading {
  margin-bottom: 1.25rem;
}

:root {
  --eps-badge-background-color: #fff;
}

.eps-theme-dark {
  --eps-badge-background-color: #404349;
}

.eps-badge {
  display: inline-block;
  background: var(--eps-badge-background-color);
  padding: 0 0.5rem;
  line-height: 1.8;
  box-shadow: 0 3px 6px var(--color-box-shadow-color);
  border-radius: 4px;
  font-size: 0.75rem;
}
.eps-badge--sm {
  font-size: 0.625rem;
  border-radius: 3px;
  padding: 0 0.3125rem;
  line-height: 1.5;
}

.eps-collapse__title {
  cursor: pointer;
  padding: 0.3125rem 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background: transparent;
  border: none;
  color: inherit;
}
.eps-collapse__title:focus {
  outline: none;
}
.eps-collapse__icon {
  transition: all 150ms;
  transform: rotate(0deg);
}
.eps-collapse__content {
  margin-top: 0.625rem;
  display: none;
}
.eps-collapse[data-open] .eps-collapse__content {
  display: block;
}
.eps-collapse[data-open] .eps-collapse__icon {
  transform: rotate(90deg);
}

.e-kit-library-bottom-promotion {
  --e-kit-library-bottom-promotion-color: tints(600);
}

.eps-theme-dark .e-kit-library-bottom-promotion {
  --e-kit-library-bottom-promotion-color: dark-tints(400);
}

.e-kit-library-bottom-promotion {
  width: 100%;
  text-align: center;
  margin-top: 1.875rem;
  color: var(--e-kit-library-bottom-promotion-color);
}

.e-kit-library__error-screen {
  margin-top: 2.75rem;
}
.e-kit-library__error-screen-title {
  margin-top: 2.75rem;
  margin-bottom: 0;
}
.e-kit-library__error-screen-description {
  margin-top: 1.5rem;
  color: #a4afb7;
  text-align: center;
  max-width: 520px;
}

.e-kit-library__kit-favorite-actions {
  padding: 0.3125rem;
  transition: 0.3s all;
  border-radius: 4px;
}
.e-kit-library__kit-favorite-actions--active {
  color: #b01b1b;
}
.e-kit-library__kit-favorite-actions--loading {
  opacity: 50%;
  cursor: default;
}
.e-kit-library__kit-favorite-actions:hover {
  background-color: rgba(176, 27, 27, 0.1);
}

.e-kit-library__filter-indication {
  margin-top: 1.5rem;
  display: flex;
  align-items: center;
}
.e-kit-library__filter-indication-text {
  margin-bottom: 0;
  display: flex;
  align-items: center;
}
.e-kit-library__filter-indication-badge {
  margin-left: 0.3125rem;
  display: flex;
  align-items: center;
}
.e-kit-library__filter-indication-badge-remove {
  margin-left: 0.3125rem;
  font-size: 0.875rem;
}
.e-kit-library__filter-indication-button {
  margin-left: 1.5rem;
}

#eps-app-header-btn-apply,
#eps-app-header-btn-promotion,
#eps-app-header-btn-connect {
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}

.e-kit-library__apply-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.3125rem;
}

.e-kit-library__kit-item {
  --e-kit-library-kit-item-overlay-promotion-button-background-color: #fcfcfc;
}

.eps-theme-dark .e-kit-library__kit-item {
  --e-kit-library-kit-item-overlay-promotion-button-background-color: #404349;
}

.e-kit-library__kit-item-overlay {
  height: 100%;
}
.e-kit-library__kit-item-overlay > *:first-child {
  flex: 1;
}
.e-kit-library__kit-item-overlay-overview-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  height: 100%;
  width: 100%;
}
.e-kit-library__kit-item-overlay-overview-button > i {
  font-size: 2rem;
  margin-bottom: 5px;
}
.e-kit-library__kit-item-overlay-overview-button > span {
  font-size: 0.9rem;
}
.e-kit-library__kit-item-overlay-promotion-button {
  display: flex;
  width: 100%;
  background: white;
  align-items: center;
  justify-content: center;
  padding: 10px;
  font-size: 13px;
  color: #93003F;
  background: var(--e-kit-library-kit-item-overlay-promotion-button-background-color);
}
.e-kit-library__kit-item-overlay-promotion-button > * {
  margin: 0 3px;
}
.e-kit-library__kit-item-subscription-plan-badge {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0.3125rem;
  color: #fff;
  text-transform: uppercase;
}

:root {
  --e-kit-library-header-back-border: 1px solid #f1f3f5;
  --e-kit-library-header-back-color: #a4afb7;
}

.eps-theme-dark {
  --e-kit-library-header-back-border: 1px solid #64666a;
  --e-kit-library-header-back-color: #b4b5b7;
}

.e-kit-library__header-back {
  color: var(--e-kit-library-header-back-color);
  padding-right: 1.25rem;
  padding-left: 0.3125rem;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  border-right: var(--e-kit-library-header-back-border);
}
.e-kit-library__header-back-container {
  flex: 1;
  height: 100%;
}
.e-kit-library__header-back .eps-icon {
  transform: rotate(0deg);
}

.e-kit-library__page-loader {
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  font-size: 1.85rem;
  color: #a4afb7;
}

.eps-search-input {
  --eps-search-input-background-color: #fcfcfc;
  --eps-search-input-background-color-focus: #fff;
  --eps-search-input-color: #556068;
  --eps-search-input-placeholder-color: #a4afb7;
}

.eps-theme-dark .eps-search-input {
  --eps-search-input-background-color: #404349;
  --eps-search-input-background-color-focus: #4c4f56;
  --eps-search-input-color: #b4b5b7;
  --eps-search-input-placeholder-color: #b4b5b7;
}

.eps-search-input {
  width: 100%;
  font-size: 0.9375rem;
  padding: 0.625rem 2.75rem;
  border: none;
  background: var(--eps-search-input-background-color);
  outline: none;
  color: var(--eps-search-input-color);
  line-height: 1;
  height: 2.75rem;
}
.eps-search-input--sm {
  font-size: 0.8125rem;
  padding: 0.5rem 1.875rem;
}
.eps-search-input:focus {
  background: var(--eps-search-input-background-color-focus);
}
.eps-search-input::-moz-placeholder {
  color: var(--eps-search-input-placeholder-color);
  font-style: italic;
}
.eps-search-input::placeholder {
  color: var(--eps-search-input-placeholder-color);
  font-style: italic;
}
.eps-search-input__container {
  position: relative;
}
.eps-search-input__icon {
  font-size: 1.25rem;
  padding: 0.625rem;
  color: #a4afb7;
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.eps-search-input__icon--sm {
  font-size: 0.75rem;
}
.eps-search-input__clear-icon {
  font-size: 0.875rem;
  padding: 0.625rem;
  color: #a4afb7;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.eps-search-input__clear-icon--sm {
  font-size: 0.6875rem;
}

:root {
  --eps-sort-select-select-background-color: #fcfcfc;
  --eps-sort-select-select-color: #556068;
  --eps-sort-select-button-background-color: #fcfcfc;
  --eps-sort-select-button-border: 1px solid #f1f3f5;
}

.eps-theme-dark {
  --eps-sort-select-select-background-color: #404349;
  --eps-sort-select-select-color: #b4b5b7;
  --eps-sort-select-button-background-color: #404349;
  --eps-sort-select-button-border: 1px solid #26292C;
}

.eps-sort-select {
  width: 100%;
  font-size: 0.9375rem;
  display: flex;
}
.eps-sort-select__select-wrapper {
  flex: 1;
  position: relative;
}
.eps-sort-select__select-wrapper::after {
  content: "\e8ad";
  font-family: eicons;
  position: absolute;
  right: 0.625rem;
  top: 0;
  bottom: 0;
  color: #a4afb7;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}
.eps-sort-select__select {
  width: 100%;
  padding: 0.625rem 0.625rem;
  border: none;
  background: var(--eps-sort-select-select-background-color);
  outline: none;
  color: var(--eps-sort-select-select-color);
  line-height: 1;
  cursor: pointer;
  height: 2.75rem;
  -moz-appearance: none;
       appearance: none;
  -webkit-appearance: none;
  border-radius: 0;
}
.eps-sort-select__button {
  padding: 0.75rem 0.75rem;
  background: var(--eps-sort-select-button-background-color);
  border-left: var(--eps-sort-select-button-border);
  line-height: 1;
  color: #a4afb7;
}

.e-kit-library__tags-filter {
  --e-kit-library-tags-filter-list-search-background-color: #fff;
}

.eps-theme-dark .e-kit-library__tags-filter {
  --e-kit-library-tags-filter-list-search-background-color: #404349;
}

.e-kit-library__tags-filter {
  margin-top: 2.75rem;
}
.e-kit-library__tags-filter-list {
  margin-bottom: 2.75rem;
}
.e-kit-library__tags-filter-list .eps-collapse__title {
  padding-right: 1.875rem;
  padding-left: 1.875rem;
  text-transform: uppercase;
}
.e-kit-library__tags-filter-list .eps-collapse__content {
  margin: 0.3125rem 1.875rem;
}
.e-kit-library__tags-filter-list-container {
  max-height: 250px;
  overflow: auto;
}
.e-kit-library__tags-filter-list-search {
  margin-bottom: 0.625rem;
}
.e-kit-library__tags-filter-list-search .eps-search-input {
  background: var(--e-kit-library-tags-filter-list-search-background-color);
}
.e-kit-library__tags-filter-list-item {
  padding: 0.625rem 0;
  display: flex;
  align-items: center;
  font-weight: 500;
}
.e-kit-library__tags-filter-list-item input {
  margin-right: 0.3125rem;
}

.e-kit-library #eps-app-header-btn-refetch {
  padding: 0;
}

.e-kit-library-header-info-modal-container {
  margin-bottom: 2.75rem;
}

.e-kit-library__tooltip {
  padding: 5px 12px;
  color: #ffffff;
  background-color: #26292C;
  font-size: 10px;
}
.e-kit-library__tooltip:before {
  border-bottom-color: #26292C;
}

.e-kit-library__index-layout-container {
  flex-grow: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}
.e-kit-library__index-layout-top-area {
  padding: 1.875rem 2.75rem;
  position: sticky;
  top: -1px;
  width: 100%;
  z-index: 2;
  background-color: var(--app-background-color);
  gap: 1.5rem;
}
.e-kit-library__index-layout-top-area-search, .e-kit-library__index-layout-top-area-sort {
  min-width: 265px;
}
.e-kit-library__index-layout-top-area-search {
  flex: 1;
}
.e-kit-library__index-layout-main {
  padding-top: 0;
  padding-bottom: 1.5rem;
  overflow-y: hidden;
  height: auto;
  flex: 1 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.e-kit-library__tags-filter {
  --e-kit-library-item-sidebar-header-title-color: #6d7882;
  --e-kit-library-item-sidebar-description-color: #6d7882;
  --e-kit-library-item-information-text-color: #6d7882;
}

.eps-theme-dark .e-kit-library__tags-filter {
  --e-kit-library-item-sidebar-header-title-color: #b4b5b7;
  --e-kit-library-item-sidebar-description-color: #b4b5b7;
  --e-kit-library-item-information-text-color: #b4b5b7;
}

.e-kit-library__item-sidebar {
  padding: 1.5rem 1.875rem;
}
.e-kit-library__item-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.e-kit-library__item-sidebar-header-title {
  color: var(--e-kit-library-item-sidebar-header-title-color);
  margin-bottom: 0;
}
.e-kit-library__item-sidebar-thumbnail {
  margin-top: 1.5rem;
  box-shadow: 0 4px 10px var(--color-box-shadow-color);
}
.e-kit-library__item-sidebar-description {
  margin-top: 1.5rem;
  color: var(--e-kit-library-item-sidebar-description-color);
}
.e-kit-library__item-sidebar-collapse-tags {
  margin-top: 2.75rem;
}
.e-kit-library__item-sidebar-collapse-info {
  margin-top: 1.875rem;
}
.e-kit-library__item-sidebar-tags-container {
  gap: 0.625rem;
}
.e-kit-library__item-information-text {
  font-size: 0.8125rem;
  color: var(--e-kit-library-item-information-text-color);
  margin-bottom: 0.3125rem;
}

.e-kit-library {
  --e-kit-library-content-overview-group-title-color: #6d7882;
}

.eps-theme-dark .e-kit-library {
  --e-kit-library-content-overview-group-title-color: #b4b5b7;
}

.e-kit-library__content-overview-group-item {
  margin-bottom: 2.75rem;
}
.e-kit-library__content-overview-group-title {
  margin-bottom: 1.875rem;
  color: var(--e-kit-library-content-overview-group-title-color);
}

.e-kit-library__preview-responsive-controls {
  width: auto;
}
.e-kit-library__preview-responsive-controls-item {
  margin: 0 0.3125rem;
  color: #a4afb7;
  padding: 0.3125rem;
}
.e-kit-library__preview-responsive-controls-item:hover {
  background: rgba(88, 208, 245, 0.1);
  border-radius: 3px;
  transition: all 0.3s;
}
.e-kit-library__preview-responsive-controls-item--active {
  color: #58d0f5;
}

.e-kit-library__preview-loader {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
}
.e-kit-library__preview-iframe {
  border: none;
  transition: all 0.3s;
  box-shadow: 0 4px 10px var(--color-box-shadow-color);
}
.e-kit-library__preview-iframe-container {
  overflow-y: auto;
  position: relative;
  z-index: 1;
}

.e-app-collapse {
  --e-app-collapse-icon-color: #6d7882;
}
.e-app-collapse-toggle {
  position: relative;
}
.e-app-collapse-toggle--active {
  cursor: pointer;
}
.e-app-collapse-toggle__icon {
  color: var(--e-app-collapse-icon-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  position: absolute;
  top: 50%;
  right: var(--e-app-collapse-toggle-icon-spacing);
  transform: translateY(-50%);
}
.e-app-collapse-toggle__icon:before {
  transition: all 0.2s linear;
}
.e-app-collapse-content {
  display: none;
}
.e-app-collapse--opened .e-app-collapse-toggle__icon:before {
  transform: rotate(-180deg);
}
.e-app-collapse--opened .e-app-collapse-content {
  display: block;
}

[dir=rtl] .e-app-collapse-toggle__icon {
  right: initial;
  left: var(--e-app-collapse-toggle-icon-spacing);
}

.eps-theme-dark .e-app-collapse {
  --e-app-collapse-icon-color: #b4b5b7;
}

.e-app-import-plugins {
  --e-app-import-plugins-selection-section-heading-color: #6d7882;
  padding-bottom: 1.5rem;
}
.e-app-import-plugins__section {
  margin-top: 1.875rem;
}
.e-app-import-plugins__section-heading {
  color: var(--e-app-import-plugins-selection-section-heading-color);
  margin-bottom: 1rem;
}
.e-app-import-plugins__versions-notice {
  margin-bottom: 0.75rem;
}

.eps-theme-dark .e-app-import-plugins {
  --e-app-import-plugins-selection-section-heading-color: #b4b5b7;
}

.eps-table {
  --eps-table-body-color: #556068;
  --eps-table-body-background-color: #fff;
  border-spacing: 0 2px;
  table-layout: fixed;
  width: 100%;
}
.eps-table__checkboxes-column {
  width: 1.875rem;
}
.eps-table__checkbox {
  display: flex;
  flex-shrink: 0;
}
.eps-table__cell {
  padding: 1rem;
}
.eps-table__head .eps-table__cell {
  text-align: start;
}
.eps-table__body .eps-table__row {
  background-color: var(--eps-table-body-background-color);
}
.eps-table__body .eps-table__cell:first-child {
  border-radius: 0.1875rem 0 0 0.1875rem;
}
.eps-table__body .eps-table__cell:last-child {
  border-radius: 0 0.1875rem 0.1875rem 0;
}
.eps-table--selection .eps-table__cell:first-child {
  -webkit-padding-end: 0;
          padding-inline-end: 0;
}

.eps-theme-dark .eps-table {
  --eps-table-body-color: #b4b5b7;
  --eps-table-body-background-color: #404349;
}

[dir=rtl] .eps-table__body [dir=rtl] .eps-table__cell:first-child {
  border-radius: 0 0.1875rem 0.1875rem 0;
}
[dir=rtl] .eps-table__body [dir=rtl] .eps-table__cell:last-child {
  border-radius: 0.1875rem 0 0 0.1875rem;
}

.e-app-import-plugins-pro-banner {
  --e-app-import-plugins-pro-banner-heading-color: #556068;
  --e-app-import-plugins-pro-banner-description-color: #6d7882;
  margin-bottom: 1.875rem;
}
.e-app-import-plugins-pro-banner__heading {
  color: var(--e-app-import-plugins-pro-banner-heading-color);
  margin-bottom: 0.625rem;
}
.e-app-import-plugins-pro-banner__description {
  color: var(--e-app-import-plugins-pro-banner-description-color);
  margin-bottom: 0;
}

.eps-theme-dark .e-app-import-plugins-pro-banner {
  --e-app-import-plugins-pro-banner-heading-color: #7d7e82;
  --e-app-import-plugins-pro-banner-description-color: #b4b5b7;
}

.e-app-export-plugins {
  padding-bottom: 1.25rem;
}

.e-app-import-content {
  padding-bottom: 1.25rem;
}
.e-app-import-content__plugins-notice {
  margin-bottom: 1.25rem;
}

.e-app-import-plugins-activation__installing-plugins {
  padding: 0.625rem 0;
}
.e-app-import-plugins-activation__plugin-name {
  -webkit-margin-start: 0.5rem;
          margin-inline-start: 0.5rem;
}
.e-app-import-plugins-activation__plugin-status-item {
  margin-bottom: 0.75rem;
}

.e-app-import-export-plugins-table__cell-content {
  margin-bottom: 0;
  text-transform: capitalize;
}

.e-app-import-export-loader {
  --e-app-import-export-loader-color: #c2cbd2;
  color: var(--e-app-import-export-loader-color);
  font-size: 50px;
}
.e-app-import-export-loader.eicon-loading {
  font-size: 1.85rem;
}
.e-app-import-export-loader--absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}

.eps-theme-dark .e-app-import-export-loader {
  --e-app-import-export-loader-color: #64666a;
}

.e-app-import-export-message-banner {
  --e-app-import-export-message-banner-heading-color: #556068;
  --e-app-import-export-message-banner-description-color: #6d7882;
  margin-bottom: 1.875rem;
}
.e-app-import-export-message-banner__heading {
  color: var(--e-app-import-export-message-banner-heading-color);
  margin-bottom: 0.625rem;
}
.e-app-import-export-message-banner__description {
  color: var(--e-app-import-export-message-banner-description-color);
  margin-bottom: 0;
}

.eps-theme-dark .e-app-import-export-message-banner {
  --e-app-import-export-message-banner-heading-color: #7d7e82;
  --e-app-import-export-message-banner-description-color: #b4b5b7;
}

.e-app-import-connect-pro-notice {
  margin-bottom: 1.25rem;
}

.e-app-import-failed-plugins-notice {
  margin-bottom: 1.25rem;
}

.e-onboarding {
  font-family: "DM Sans", "Roboto", sans-serif;
}
.e-onboarding .eps-app__main {
  justify-content: center;
  overflow-y: auto;
}
.e-onboarding__content {
  max-width: 1135px;
  padding: initial;
  margin: 2.75rem;
  overflow-y: initial;
}
.e-onboarding__checkbox-label {
  display: flex;
  line-height: 18px;
  margin-bottom: 27px;
}
.e-onboarding__checkbox-input {
  -webkit-margin-end: 14px;
          margin-inline-end: 14px;
  width: 16px;
  height: 16px;
  border-color: #69727D;
  border-radius: 2px;
}
.e-onboarding__checkbox-input:checked {
  background-color: #525861;
}
.e-onboarding__checkbox-input:checked::after {
  margin-bottom: 15%;
  width: 6px;
  height: 9px;
  border-width: 0 2px 2px 0;
}
.e-onboarding__feature-list {
  margin-bottom: 40px;
}
.e-onboarding__text-input {
  font-size: 14px;
  width: 325px;
  padding: 12px 16px;
  color: #69727D;
  border: 1px solid #9EA5AD;
}
.e-onboarding__text-input:focus-visible {
  outline: initial;
  border: 1px solid #3A3F45;
}
.e-onboarding__text-input::-moz-placeholder {
  color: #C2C7CC;
}
.e-onboarding__text-input::placeholder {
  color: #C2C7CC;
}
.e-onboarding__footnote {
  margin-top: 24px;
  width: 325px;
  text-align: center;
}
.e-onboarding__footnote a {
  text-decoration: underline;
  color: #3A3F45;
}

#e-app ~ #__wp-uploader-id-2 .media-modal {
  max-width: 1024px;
  max-height: 768px;
  margin: auto;
}

.e-onboarding__page-content {
  margin-bottom: 70px;
}
.e-onboarding__page-content-start {
  max-width: 675px;
  text-align: start;
  flex-basis: 555px;
  align-self: start;
}
.e-onboarding__page-content-end {
  min-width: 440px;
  max-width: 540px;
}
.e-onboarding__page-content-end img {
  width: 100%;
}
.e-onboarding__page-content-section-title {
  font-family: "DM Serif Display", serif;
  font-size: 36px;
  font-weight: 700;
  color: #0C0D0E;
}
.e-onboarding__page-content-section-text {
  font-size: 18px;
  color: #3A3F45;
}

.e-onboarding__header-logo .eps-app__logo {
  background-color: #0C0D0E;
  color: #ffffff;
  width: 1.3rem;
  height: 1.3rem;
  line-height: 1.3rem;
  font-size: 0.48rem;
}
.e-onboarding__header-logo .eps-app__logo:not(:last-child) {
  -webkit-margin-end: 7px;
          margin-inline-end: 7px;
}
.e-onboarding__header-logo img {
  width: 104px;
}
.e-onboarding__header .eps-app__header-btn {
  display: flex;
  align-items: center;
  font-size: 13px;
}
.e-onboarding__header .eps-app__header-btn .eps-icon:not(:last-child) {
  -webkit-margin-end: 7px;
          margin-inline-end: 7px;
}
.e-onboarding__header .eps-button {
  color: #0C0D0E;
}
.e-onboarding__header .eps-button__go-pro-btn {
  background-color: #871A40;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 700;
  font-size: 12px;
  transition: 0.5s;
}
.e-onboarding__header .eps-button__go-pro-btn:hover {
  background: #b22254;
}

.e-onboarding__go-pro {
  width: 288px;
  font-size: 12px;
}
.e-onboarding__go-pro-title {
  font-size: 18px;
  font-weight: 700;
  color: #871A40;
}
.e-onboarding__go-pro-cta {
  display: inline-block;
  color: #871A40;
  padding: 9px;
  border: 1px solid #871A40;
}
.e-onboarding__go-pro-cta.e-onboarding__button {
  font-size: 14px;
}
.e-onboarding__go-pro-paragraph:not(:last-child) {
  margin-bottom: 20px;
}
.e-onboarding__go-pro-already-have {
  text-decoration: underline;
}

.e-onboarding__progress-bar {
  display: flex;
  justify-content: center;
  margin-bottom: 125px;
}
.e-onboarding__progress-bar-item {
  display: flex;
  align-items: center;
  color: #69727D;
  font-size: 0.75rem;
}
.e-onboarding__progress-bar-item-icon {
  display: inline-block;
  font-family: "DM Serif Display", serif;
  text-align: center;
  width: 1.1rem;
  height: 1.1rem;
  line-height: 1rem;
  font-size: 0.75rem;
  font-weight: bold;
  border-radius: 50%;
  border: 1px solid #69727D;
  -webkit-margin-end: 8px;
          margin-inline-end: 8px;
  flex-shrink: 0;
}
.e-onboarding__progress-bar-item:not(:last-child) {
  -webkit-margin-end: 22px;
          margin-inline-end: 22px;
}
.e-onboarding__progress-bar-item:not(:last-child):after {
  font-family: "eicons";
  -webkit-margin-start: 22px;
          margin-inline-start: 22px;
  content: "\e89e";
}
.e-onboarding__progress-bar-item--active {
  color: #040080;
}
.e-onboarding__progress-bar-item--active .e-onboarding__progress-bar-item-icon, .e-onboarding__progress-bar-item--completed .e-onboarding__progress-bar-item-icon {
  color: #ffffff;
  border-color: #040080;
  background-color: #040080;
}
.e-onboarding__progress-bar-item--skipped, .e-onboarding__progress-bar-item--completed {
  cursor: pointer;
}
.e-onboarding__progress-bar-item--skipped .e-onboarding__progress-bar-item-icon {
  color: #ffffff;
  background-color: #69727D;
}

.e-onboarding__button {
  font-size: 18px;
  cursor: pointer;
}
.e-onboarding__button-action {
  color: #46F2B6;
  background-color: #040080;
  width: 325px;
  padding: 15px 15px;
  text-align: center;
}
.e-onboarding__button-skip {
  color: #6A727C;
  padding: 15px 29px;
}
.e-onboarding__button--disabled {
  pointer-events: none;
  background-color: #D5D8DC;
  color: #9EA5AD;
}
.e-onboarding__button--disabled:hover {
  cursor: progress;
}
.e-onboarding__button--processing {
  pointer-events: none;
  filter: brightness(90%);
}

.e-onboarding__card {
  border: 1px solid #3A3F45;
  border-radius: 8px;
  padding: 40px 40px;
  background-color: #ffffff;
  cursor: pointer;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.e-onboarding__card-image, .e-onboarding__card-text {
  width: 345px;
}
.e-onboarding__card-image {
  margin-bottom: 44px;
}
.e-onboarding__card-text {
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  color: #0C0D0E;
}
.e-onboarding__card:hover {
  box-shadow: 4px 4px 0 0 #000000;
}
.e-onboarding__card:active {
  box-shadow: initial;
}

.e-onboarding__checklist {
  -webkit-padding-start: 0;
          padding-inline-start: 0;
}
.e-onboarding__checklist-item {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-bottom: 12px;
}
.e-onboarding__checklist-item .eicon-check-circle {
  -webkit-margin-end: 9px;
          margin-inline-end: 9px;
  font-size: 16px;
  font-weight: 600;
}

.e-onboarding__notice {
  display: inline-block;
  padding: 12px 14px;
  margin-bottom: 16px;
  color: #3A3F45;
}
.e-onboarding__notice--error {
  background-color: #FDE8EC;
}
.e-onboarding__notice--error i {
  font-size: 16px;
  color: #B92136;
}
.e-onboarding__notice--success {
  background-color: #ECF9F2;
}
.e-onboarding__notice--success i {
  color: #117740;
}
.e-onboarding__notice i {
  -webkit-margin-end: 14px;
          margin-inline-end: 14px;
}
.e-onboarding__notice-empty-spacer {
  height: 61px;
}

.e-onboarding__page-account .e-onboarding__checkbox-label {
  margin-top: 50px;
}

.e-onboarding__action-button-text {
  -webkit-margin-start: 10px;
          margin-inline-start: 10px;
}

.e-onboarding__site-name-input {
  margin-top: 80px;
}

.e-onboarding__page-siteLogo .e-app-upload-file__button {
  color: #040080;
  border-color: #040080;
}
.e-onboarding__drop-zone {
  margin-top: 3rem;
}
.e-onboarding .eps-display-3 {
  font-size: 16px;
  margin-bottom: 0.5rem;
}
.e-onboarding .e-app-upload-file__button {
  max-width: 198px;
  margin: 0 auto;
}
.e-onboarding .e-app-drop-zone__secondary-text {
  font-size: 14px;
}
.e-onboarding__logo-container {
  position: relative;
  margin-top: 3rem;
  height: 220px;
  width: -moz-fit-content;
  width: fit-content;
  display: flex;
  align-items: center;
  min-width: 220px;
}
.e-onboarding__logo-container img {
  height: auto;
  max-height: 100%;
  width: auto;
}
.e-onboarding__logo-remove {
  position: absolute;
  right: 8px;
  top: 5px;
}
.e-onboarding__logo-remove i {
  font-size: 16px;
  width: 15px;
}
.e-onboarding__logo-remove:hover {
  cursor: pointer;
}
.e-onboarding__is-uploading {
  visibility: hidden;
}

.e-onboarding__page-goodToGo .e-onboarding__page-content-section-title,
.e-onboarding__page-goodToGo .e-onboarding__page-content-section-text {
  text-align: center;
}
.e-onboarding__cards-grid.e-onboarding__page-content {
  margin-top: 48px;
  margin-bottom: 48px;
}
.e-onboarding__cards-grid .e-onboarding__card {
  max-width: 555px;
}
.e-onboarding__cards-grid .e-onboarding__card:not(:last-child) {
  -webkit-margin-end: 20px;
          margin-inline-end: 20px;
}
.e-onboarding__good-to-go-footer {
  justify-content: end;
}

.e-onboarding__upload-pro {
  flex-direction: row;
  justify-content: center;
  font-family: "DM Sans", "Roboto", sans-serif;
  text-align: center;
}
.e-onboarding__upload-pro .eps-app__content {
  overflow-y: hidden;
  max-width: 1113px;
  padding: 30px;
}
.e-onboarding__upload-pro-drop-zone {
  margin-bottom: 24px;
}
.e-onboarding__upload-pro-drop-zone h1 {
  font-family: "Source Serif Pro", "Roboto", sans-serif;
}
.e-onboarding__upload-pro-drop-zone .e-app-drag-drop {
  padding: 48px;
}
.e-onboarding__upload-pro-drop-zone .eps-display-3,
.e-onboarding__upload-pro-drop-zone .e-app-drop-zone__text,
.e-onboarding__upload-pro-drop-zone .e-app-drop-zone__secondary-text {
  margin-bottom: 12px;
}
.e-onboarding__upload-pro-drop-zone .e-app-upload-file__button {
  background-color: #040080;
  color: #46F2B6;
  padding: 14px 40px;
  border-color: #040080;
}
.e-onboarding__upload-pro-get-file {
  font-size: 12px;
  margin-top: 24px;
}
.e-onboarding__upload-pro-get-file a {
  text-decoration: underline;
}
.e-onboarding__upload-pro .e-onboarding__notice {
  margin-bottom: 0;
}
/*# sourceMappingURL=app.css.map */