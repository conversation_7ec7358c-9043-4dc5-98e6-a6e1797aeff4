<?php
/**
 * معالج طباعة التقرير
 *
 * يستقبل هذا الملف محتوى التقرير من النموذج ويخزنه في الجلسة
 * ثم يعيد توجيه المستخدم إلى صفحة الطباعة
 */

// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التأكد من ضبط ترميز UTF-8 قبل أي إخراج
header('Content-Type: text/html; charset=utf-8');

redirectIfNotLoggedIn();

// التحقق من وجود محتوى التقرير
if (isset($_POST['report_content'])) {
    // تخزين محتوى التقرير في الجلسة
    $_SESSION['report_content'] = $_POST['report_content'];

    // تحضير معلمات URL
    $params = [];

    if (isset($_POST['report_title'])) {
        // استخدام عنوان التقرير من ملف اللغة بدلاً من النص المرسل
        // لتجنب مشاكل الترميز
        $report_type = isset($_POST['report_type']) ? $_POST['report_type'] : 'summary';

        switch ($report_type) {
            case 'sales_details':
                $params['title'] = urlencode(__('sales_details'));
                break;
            case 'purchases_details':
                $params['title'] = urlencode(__('purchases_details'));
                break;
            case 'top_products':
                $params['title'] = urlencode(__('top_products'));
                break;
            case 'top_customers':
                $params['title'] = urlencode(__('top_customers'));
                break;
            default:
                $params['title'] = urlencode(__('reports'));
                break;
        }
    }

    if (isset($_POST['start_date'])) {
        $params['start_date'] = $_POST['start_date'];
    }

    if (isset($_POST['end_date'])) {
        $params['end_date'] = $_POST['end_date'];
    }

    if (isset($_POST['report_type'])) {
        $params['report_type'] = $_POST['report_type'];
    }

    if (isset($_POST['customer_id'])) {
        $params['customer_id'] = intval($_POST['customer_id']);
    }

    // بناء URL مع المعلمات
    $url = 'print_report.php';
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }

    // إعادة التوجيه إلى صفحة الطباعة
    header("Location: $url");
    exit();
} else {
    // إذا لم يتم تقديم محتوى التقرير، إعادة التوجيه إلى صفحة التقارير
    $_SESSION['error'] = __('no_report_content');
    header("Location: reports.php");
    exit();
}
