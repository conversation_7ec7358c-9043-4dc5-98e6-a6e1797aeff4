<?php
/**
 * ملف لإضافة عمود البريد الإلكتروني إلى جدول العملاء الموجود
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

$update_results = [];
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_email_column'])) {
    try {
        // فحص وجود جدول العملاء
        $check_table = $db->query("SHOW TABLES LIKE 'customers'");
        if (!$check_table || $check_table->num_rows == 0) {
            $errors[] = "جدول العملاء غير موجود. يرجى إنشاء الجدول أولاً.";
        } else {
            // فحص وجود عمود البريد الإلكتروني
            $check_column = $db->query("SHOW COLUMNS FROM customers LIKE 'email'");
            
            if ($check_column && $check_column->num_rows > 0) {
                $update_results[] = "عمود البريد الإلكتروني موجود بالفعل";
            } else {
                // إضافة عمود البريد الإلكتروني
                $add_column_sql = "ALTER TABLE customers ADD COLUMN email VARCHAR(100) DEFAULT NULL AFTER phone";
                
                if ($db->query($add_column_sql)) {
                    $update_results[] = "تم إضافة عمود البريد الإلكتروني بنجاح";
                    
                    // إضافة فهرس على البريد الإلكتروني لتحسين الأداء
                    $index_sql = "ALTER TABLE customers ADD INDEX idx_customer_email (email)";
                    if ($db->query($index_sql)) {
                        $update_results[] = "تم إضافة فهرس على البريد الإلكتروني";
                    } else {
                        $errors[] = "فشل في إضافة الفهرس: " . $db->error;
                    }
                } else {
                    $errors[] = "فشل في إضافة عمود البريد الإلكتروني: " . $db->error;
                }
            }
        }
        
        if (empty($errors)) {
            $_SESSION['success'] = "تم تحديث جدول العملاء بنجاح!";
        } else {
            $_SESSION['error'] = "حدثت بعض الأخطاء أثناء التحديث";
        }
        
    } catch (Exception $e) {
        $errors[] = "حدث خطأ أثناء تحديث الجدول: " . $e->getMessage();
        $_SESSION['error'] = "فشل في تحديث جدول العملاء";
    }
}

// فحص حالة الجدول الحالية
$table_status = [];
try {
    $check_table = $db->query("SHOW TABLES LIKE 'customers'");
    $table_status['exists'] = ($check_table && $check_table->num_rows > 0);
    
    if ($table_status['exists']) {
        // فحص بنية الجدول
        $structure = $db->query("DESCRIBE customers");
        $table_status['columns'] = [];
        while ($row = $structure->fetch_assoc()) {
            $table_status['columns'][$row['Field']] = $row;
        }
        
        // فحص وجود عمود البريد الإلكتروني
        $table_status['has_email'] = isset($table_status['columns']['email']);
        
        // عدد العملاء
        $count_result = $db->query("SELECT COUNT(*) as count FROM customers");
        $table_status['count'] = $count_result ? $count_result->fetch_assoc()['count'] : 0;
        
        // عدد العملاء الذين لديهم بريد إلكتروني
        if ($table_status['has_email']) {
            $email_count_result = $db->query("SELECT COUNT(*) as count FROM customers WHERE email IS NOT NULL AND email != ''");
            $table_status['email_count'] = $email_count_result ? $email_count_result->fetch_assoc()['count'] : 0;
        }
    }
} catch (Exception $e) {
    $table_status['error'] = $e->getMessage();
}

displayMessages();
?>

<div class="container mt-4">
    <h2>إضافة عمود البريد الإلكتروني لجدول العملاء</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات التحديث</h5>
        <p>هذه الأداة تقوم بإضافة عمود البريد الإلكتروني إلى جدول العملاء الموجود. سيتم:</p>
        <ul>
            <li>فحص وجود جدول العملاء</li>
            <li>التحقق من وجود عمود البريد الإلكتروني</li>
            <li>إضافة العمود إذا لم يكن موجوداً</li>
            <li>إضافة فهرس لتحسين الأداء</li>
        </ul>
    </div>
    
    <?php if (!empty($update_results)): ?>
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-check-circle"></i>
                التحديثات المطبقة
            </h5>
        </div>
        <div class="card-body">
            <ul class="list-group">
                <?php foreach ($update_results as $result): ?>
                <li class="list-group-item">
                    <i class="fas fa-check text-success"></i> <?php echo htmlspecialchars($result); ?>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
    
    <?php if (!empty($errors)): ?>
    <div class="card mb-4">
        <div class="card-header bg-danger text-white">
            <h5 class="mb-0">
                <i class="fas fa-times-circle"></i>
                أخطاء حدثت أثناء التحديث
            </h5>
        </div>
        <div class="card-body">
            <ul class="list-group">
                <?php foreach ($errors as $error): ?>
                <li class="list-group-item list-group-item-danger">
                    <i class="fas fa-times text-danger"></i> <?php echo htmlspecialchars($error); ?>
                </li>
                <?php endforeach; ?>
            </ul>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- حالة الجدول الحالية -->
    <div class="card mb-4">
        <div class="card-header <?php echo isset($table_status['exists']) && $table_status['exists'] ? 'bg-success' : 'bg-warning'; ?> text-white">
            <h5 class="mb-0">
                <i class="fas fa-database"></i>
                حالة جدول العملاء
            </h5>
        </div>
        <div class="card-body">
            <?php if (isset($table_status['error'])): ?>
                <div class="alert alert-danger">
                    خطأ في فحص الجدول: <?php echo htmlspecialchars($table_status['error']); ?>
                </div>
            <?php elseif (isset($table_status['exists']) && $table_status['exists']): ?>
                <div class="row">
                    <div class="col-md-6">
                        <h6>معلومات الجدول:</h6>
                        <ul>
                            <li><strong>الحالة:</strong> <span class="badge bg-success">موجود</span></li>
                            <li><strong>عدد العملاء:</strong> <?php echo $table_status['count']; ?></li>
                            <li><strong>عدد الأعمدة:</strong> <?php echo count($table_status['columns']); ?></li>
                            <li><strong>عمود البريد الإلكتروني:</strong> 
                                <span class="badge <?php echo $table_status['has_email'] ? 'bg-success' : 'bg-warning'; ?>">
                                    <?php echo $table_status['has_email'] ? 'موجود' : 'غير موجود'; ?>
                                </span>
                            </li>
                            <?php if ($table_status['has_email'] && isset($table_status['email_count'])): ?>
                            <li><strong>عملاء لديهم بريد إلكتروني:</strong> <?php echo $table_status['email_count']; ?></li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>الأعمدة الموجودة:</h6>
                        <div class="d-flex flex-wrap">
                            <?php foreach ($table_status['columns'] as $column_name => $column_info): ?>
                                <span class="badge <?php echo $column_name == 'email' ? 'bg-primary' : 'bg-info'; ?> me-1 mb-1">
                                    <?php echo $column_name; ?>
                                    <?php if ($column_name == 'email'): ?>
                                        <i class="fas fa-envelope ms-1"></i>
                                    <?php endif; ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> جدول العملاء غير موجود
                    <br>
                    <a href="fix_customer_table.php" class="btn btn-primary btn-sm mt-2">إنشاء الجدول</a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- نموذج التحديث -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">إضافة عمود البريد الإلكتروني</h5>
        </div>
        <div class="card-body">
            <?php if (isset($table_status['has_email']) && $table_status['has_email']): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> عمود البريد الإلكتروني موجود بالفعل في الجدول
                </div>
            <?php else: ?>
                <form method="POST">
                    <div class="alert alert-warning">
                        <strong>تحذير:</strong> سيتم تعديل بنية قاعدة البيانات. تأكد من وجود نسخة احتياطية قبل المتابعة.
                    </div>
                    
                    <p>سيتم إضافة عمود البريد الإلكتروني مع الخصائص التالية:</p>
                    <ul>
                        <li><strong>نوع البيانات:</strong> VARCHAR(100)</li>
                        <li><strong>القيمة الافتراضية:</strong> NULL</li>
                        <li><strong>الموقع:</strong> بعد عمود الهاتف</li>
                        <li><strong>فهرس:</strong> سيتم إضافة فهرس لتحسين الأداء</li>
                    </ul>
                    
                    <button type="submit" name="add_email_column" class="btn btn-primary" onclick="return confirm('هل أنت متأكد من إضافة عمود البريد الإلكتروني؟')">
                        <i class="fas fa-plus"></i> إضافة عمود البريد الإلكتروني
                    </button>
                </form>
            <?php endif; ?>
            
            <div class="mt-3">
                <a href="test_customer_fields_match.php" class="btn btn-success">
                    <i class="fas fa-check-double"></i> اختبار تطابق الحقول
                </a>
                
                <a href="test_ajax_customer.php" class="btn btn-info">
                    <i class="fas fa-vial"></i> اختبار إضافة العميل
                </a>
                
                <a href="customers.php" class="btn btn-warning">
                    <i class="fas fa-users"></i> قائمة العملاء
                </a>
                
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-home"></i> العودة للصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
