{"version": 3, "file": "lang/summernote-id-ID.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,OADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,aAHP;AAIJC,QAAAA,KAAK,EAAE,gBAJH;AAKJC,QAAAA,MAAM,EAAE,aALJ;AAMJC,QAAAA,IAAI,EAAE,eANF;AAOJC,QAAAA,aAAa,EAAE,OAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,aATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,MAAM,EAAE,iBAFH;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,aAAa,EAAE,YALV;AAMLC,QAAAA,SAAS,EAAE,WANN;AAOLC,QAAAA,UAAU,EAAE,YAPP;AAQLC,QAAAA,SAAS,EAAE,gBARN;AASLC,QAAAA,YAAY,EAAE,mBATT;AAULC,QAAAA,WAAW,EAAE,gBAVR;AAWLC,QAAAA,cAAc,EAAE,mBAXX;AAYLC,QAAAA,SAAS,EAAE,mBAZN;AAaLC,QAAAA,aAAa,EAAE,0BAbV;AAcLC,QAAAA,SAAS,EAAE,2BAdN;AAeLC,QAAAA,eAAe,EAAE,0BAfZ;AAgBLC,QAAAA,eAAe,EAAE,wBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,oCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,YAlBA;AAmBLC,QAAAA,MAAM,EAAE,cAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,YAFN;AAGLpB,QAAAA,MAAM,EAAE,gBAHH;AAILgB,QAAAA,GAAG,EAAE,cAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,QADF;AAEJtB,QAAAA,MAAM,EAAE,eAFJ;AAGJuB,QAAAA,MAAM,EAAE,cAHJ;AAIJC,QAAAA,IAAI,EAAE,MAJF;AAKJC,QAAAA,aAAa,EAAE,eALX;AAMJT,QAAAA,GAAG,EAAE,eAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,yBAFR;AAGLC,QAAAA,WAAW,EAAE,0BAHR;AAILC,QAAAA,UAAU,EAAE,yBAJP;AAKLC,QAAAA,WAAW,EAAE,0BALR;AAMLC,QAAAA,MAAM,EAAE,aANH;AAOLC,QAAAA,MAAM,EAAE,aAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,MADF;AAELC,QAAAA,CAAC,EAAE,GAFE;AAGLC,QAAAA,UAAU,EAAE,SAHP;AAILC,QAAAA,GAAG,EAAE,MAJA;AAKLC,QAAAA,EAAE,EAAE,WALC;AAMLC,QAAAA,EAAE,EAAE,WANC;AAOLC,QAAAA,EAAE,EAAE,WAPC;AAQLC,QAAAA,EAAE,EAAE,WARC;AASLC,QAAAA,EAAE,EAAE,WATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,YADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,UAAU,EAAE,aAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,SAFA;AAGTC,QAAAA,MAAM,EAAE,QAHC;AAITC,QAAAA,IAAI,EAAE,WAJG;AAKTC,QAAAA,MAAM,EAAE,aALC;AAMTC,QAAAA,KAAK,EAAE,YANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,gBADH;AAELC,QAAAA,IAAI,EAAE,cAFD;AAGLC,QAAAA,UAAU,EAAE,aAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,WAAW,EAAE,YALR;AAMLC,QAAAA,cAAc,EAAE,mBANX;AAOLC,QAAAA,KAAK,EAAE,YAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,cADH;AAERC,QAAAA,KAAK,EAAE,OAFC;AAGRC,QAAAA,cAAc,EAAE,aAHR;AAIRC,QAAAA,MAAM,EAAE,MAJA;AAKRC,QAAAA,mBAAmB,EAAE,iBALb;AAMRC,QAAAA,aAAa,EAAE,cANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,oBADf;AAEJ,gBAAQ,4BAFJ;AAGJ,gBAAQ,8BAHJ;AAIJ,eAAO,KAJH;AAKJ,iBAAS,OALL;AAMJ,gBAAQ,yBANJ;AAOJ,kBAAU,0BAPN;AAQJ,qBAAa,6BART;AASJ,yBAAiB,iCATb;AAUJ,wBAAgB,kBAVZ;AAWJ,uBAAe,gBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,iBAbZ;AAcJ,uBAAe,sBAdX;AAeJ,+BAAuB,6BAfnB;AAgBJ,6BAAqB,0BAhBjB;AAiBJ,mBAAW,8BAjBP;AAkBJ,kBAAU,6BAlBN;AAmBJ,sBAAc,oDAnBV;AAoBJ,oBAAY,qDApBR;AAqBJ,oBAAY,qDArBR;AAsBJ,oBAAY,qDAtBR;AAuBJ,oBAAY,qDAvBR;AAwBJ,oBAAY,qDAxBR;AAyBJ,oBAAY,qDAzBR;AA0BJ,gCAAwB,2BA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,iBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-id-ID.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'id-ID': {\n      font: {\n        bold: '<PERSON><PERSON>',\n        italic: 'Miring',\n        underline: 'Garis bawah',\n        clear: '<PERSON><PERSON><PERSON><PERSON> gaya',\n        height: '<PERSON><PERSON><PERSON> baris',\n        name: '<PERSON><PERSON>',\n        strikethrough: '<PERSON><PERSON>',\n        subscript: 'Subscript',\n        superscript: 'Superscript',\n        size: 'Ukuran font',\n      },\n      image: {\n        image: 'Gambar',\n        insert: 'Sisipkan gambar',\n        resizeFull: 'Ukuran penuh',\n        resizeHalf: 'Ukuran 50%',\n        resizeQuarter: 'Ukuran 25%',\n        floatLeft: 'Rata kiri',\n        floatRight: 'Rata kanan',\n        floatNone: 'Tanpa perataan',\n        shapeRounded: 'Bentuk: Membundar',\n        shapeCircle: 'Bentuk: Bundar',\n        shapeThumbnail: 'Bentuk: Thumbnail',\n        shapeNone: 'Bentuk: Tidak ada',\n        dragImageHere: 'Tarik gambar ke area ini',\n        dropImage: 'Letakkan gambar atau teks',\n        selectFromFiles: 'Pilih gambar dari berkas',\n        maximumFileSize: 'Ukuran maksimal berkas',\n        maximumFileSizeError: 'Ukuran maksimal berkas terlampaui.',\n        url: 'URL gambar',\n        remove: 'Hapus Gambar',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Link video',\n        insert: 'Sisipkan video',\n        url: 'Tautan video',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion atau Youku)',\n      },\n      link: {\n        link: 'Tautan',\n        insert: 'Tambah tautan',\n        unlink: 'Hapus tautan',\n        edit: 'Edit',\n        textToDisplay: 'Tampilan teks',\n        url: 'Tautan tujuan',\n        openInNewWindow: 'Buka di jendela baru',\n      },\n      table: {\n        table: 'Tabel',\n        addRowAbove: 'Tambahkan baris ke atas',\n        addRowBelow: 'Tambahkan baris ke bawah',\n        addColLeft: 'Tambahkan kolom ke kiri',\n        addColRight: 'Tambahkan kolom ke kanan',\n        delRow: 'Hapus baris',\n        delCol: 'Hapus kolom',\n        delTable: 'Hapus tabel',\n      },\n      hr: {\n        insert: 'Masukkan garis horizontal',\n      },\n      style: {\n        style: 'Gaya',\n        p: 'p',\n        blockquote: 'Kutipan',\n        pre: 'Kode',\n        h1: 'Heading 1',\n        h2: 'Heading 2',\n        h3: 'Heading 3',\n        h4: 'Heading 4',\n        h5: 'Heading 5',\n        h6: 'Heading 6',\n      },\n      lists: {\n        unordered: 'Pencacahan',\n        ordered: 'Penomoran',\n      },\n      options: {\n        help: 'Bantuan',\n        fullscreen: 'Layar penuh',\n        codeview: 'Kode HTML',\n      },\n      paragraph: {\n        paragraph: 'Paragraf',\n        outdent: 'Outdent',\n        indent: 'Indent',\n        left: 'Rata kiri',\n        center: 'Rata tengah',\n        right: 'Rata kanan',\n        justify: 'Rata kanan kiri',\n      },\n      color: {\n        recent: 'Warna sekarang',\n        more: 'Selengkapnya',\n        background: 'Warna latar',\n        foreground: 'Warna font',\n        transparent: 'Transparan',\n        setTransparent: 'Atur transparansi',\n        reset: 'Atur ulang',\n        resetToDefault: 'Kembalikan kesemula',\n      },\n      shortcut: {\n        shortcuts: 'Jalan pintas',\n        close: 'Tutup',\n        textFormatting: 'Format teks',\n        action: 'Aksi',\n        paragraphFormatting: 'Format paragraf',\n        documentStyle: 'Gaya dokumen',\n        extraKeys: 'Shortcut tambahan',\n      },\n      help: {\n        'insertParagraph': 'Tambahkan paragraf',\n        'undo': 'Urungkan perintah terakhir',\n        'redo': 'Kembalikan perintah terakhir',\n        'tab': 'Tab',\n        'untab': 'Untab',\n        'bold': 'Mengaktifkan gaya tebal',\n        'italic': 'Mengaktifkan gaya italic',\n        'underline': 'Mengaktifkan gaya underline',\n        'strikethrough': 'Mengaktifkan gaya strikethrough',\n        'removeFormat': 'Hapus semua gaya',\n        'justifyLeft': 'Atur rata kiri',\n        'justifyCenter': 'Atur rata tengah',\n        'justifyRight': 'Atur rata kanan',\n        'justifyFull': 'Atur rata kiri-kanan',\n        'insertUnorderedList': 'Nyalakan urutan tanpa nomor',\n        'insertOrderedList': 'Nyalakan urutan bernomor',\n        'outdent': 'Outdent di paragraf terpilih',\n        'indent': 'Indent di paragraf terpilih',\n        'formatPara': 'Ubah format gaya tulisan terpilih menjadi paragraf',\n        'formatH1': 'Ubah format gaya tulisan terpilih menjadi Heading 1',\n        'formatH2': 'Ubah format gaya tulisan terpilih menjadi Heading 2',\n        'formatH3': 'Ubah format gaya tulisan terpilih menjadi Heading 3',\n        'formatH4': 'Ubah format gaya tulisan terpilih menjadi Heading 4',\n        'formatH5': 'Ubah format gaya tulisan terpilih menjadi Heading 5',\n        'formatH6': 'Ubah format gaya tulisan terpilih menjadi Heading 6',\n        'insertHorizontalRule': 'Masukkan garis horizontal',\n        'linkDialog.show': 'Tampilkan Link Dialog',\n      },\n      history: {\n        undo: 'Kembali',\n        redo: 'Ulang',\n      },\n      specialChar: {\n        specialChar: 'KARAKTER KHUSUS',\n        select: 'Pilih karakter khusus',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}