<?php
/**
 * صفحة تفاصيل المستخدم للمدير
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('view_all_data')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

global $main_db;

$user_id = intval($_GET['id'] ?? 0);

// جلب معلومات المستخدم
$user_info = null;
if ($user_id > 0) {
    $stmt = $main_db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $user_info = $stmt->get_result()->fetch_assoc();
    $stmt->close();
}

if (!$user_info) {
    $_SESSION['error'] = 'المستخدم غير موجود';
    header("Location: admin_users.php");
    exit();
}

// جلب إحصائيات نشاط المستخدم
$activity_stats = $main_db->query("SELECT 
    COUNT(*) as total_activities,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_activities,
    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_activities,
    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) as month_activities
    FROM activity_log 
    WHERE user_id = $user_id AND user_type = 'user'")->fetch_assoc();

// جلب أحدث الأنشطة
$recent_activities = $main_db->query("SELECT * FROM activity_log 
    WHERE user_id = $user_id AND user_type = 'user' 
    ORDER BY created_at DESC 
    LIMIT 10");

// الاتصال بقاعدة بيانات المستخدم للحصول على الإحصائيات
$user_db_name = "sales_system_user_" . $user_id;
$user_stats = [
    'sales_count' => 0,
    'purchases_count' => 0,
    'customers_count' => 0,
    'total_sales_amount' => 0,
    'total_purchases_amount' => 0
];

$db_exists = $main_db->query("SHOW DATABASES LIKE '$user_db_name'");
if ($db_exists && $db_exists->num_rows > 0) {
    $user_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, $user_db_name);
    if (!$user_db->connect_error) {
        // إحصائيات المبيعات
        $sales_result = $user_db->query("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM sales");
        if ($sales_result) {
            $sales_data = $sales_result->fetch_assoc();
            $user_stats['sales_count'] = $sales_data['count'];
            $user_stats['total_sales_amount'] = $sales_data['total'];
        }
        
        // إحصائيات المشتريات
        $purchases_result = $user_db->query("SELECT COUNT(*) as count, COALESCE(SUM(total_amount), 0) as total FROM purchases");
        if ($purchases_result) {
            $purchases_data = $purchases_result->fetch_assoc();
            $user_stats['purchases_count'] = $purchases_data['count'];
            $user_stats['total_purchases_amount'] = $purchases_data['total'];
        }
        
        // إحصائيات العملاء
        $customers_result = $user_db->query("SELECT COUNT(*) as count FROM customers");
        if ($customers_result) {
            $customers_data = $customers_result->fetch_assoc();
            $user_stats['customers_count'] = $customers_data['count'];
        }
        
        $user_db->close();
    }
}

require_once __DIR__ . '/includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" href="admin_users.php">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_activity.php">
                            <i class="fas fa-history me-2"></i>سجل العمليات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الشاملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_financial_reports.php">
                            <i class="fas fa-chart-line me-2"></i>التقارير المالية
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-user me-2 text-primary"></i>تفاصيل المستخدم
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a href="admin_users.php" class="btn btn-sm btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-1"></i>العودة للمستخدمين
                    </a>
                    <div class="btn-group me-2">
                        <a href="admin_invoice_details.php?user_id=<?php echo $user_id; ?>" class="btn btn-sm btn-info">
                            <i class="fas fa-file-invoice me-1"></i>عرض الفواتير
                        </a>
                    </div>
                </div>
            </div>

            <!-- معلومات المستخدم الأساسية -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card shadow">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-user-circle me-2"></i>المعلومات الشخصية
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">الاسم الكامل</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($user_info['full_name']); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">اسم المستخدم</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($user_info['username']); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">البريد الإلكتروني</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($user_info['email']); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">رقم الهاتف</label>
                                    <div class="fw-bold"><?php echo htmlspecialchars($user_info['phone'] ?? 'غير محدد'); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">تاريخ التسجيل</label>
                                    <div class="fw-bold"><?php echo date('Y-m-d H:i', strtotime($user_info['created_at'])); ?></div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">آخر تسجيل دخول</label>
                                    <div class="fw-bold">
                                        <?php echo $user_info['last_login'] ? date('Y-m-d H:i', strtotime($user_info['last_login'])) : 'لم يسجل دخول بعد'; ?>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">حالة الحساب</label>
                                    <div>
                                        <span class="badge <?php echo $user_info['is_active'] ? 'bg-success' : 'bg-danger'; ?>">
                                            <?php echo $user_info['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label text-muted">اللغة المفضلة</label>
                                    <div class="fw-bold"><?php echo $user_info['preferred_language'] === 'ar' ? 'العربية' : 'الإنجليزية'; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4">
                    <div class="card shadow">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-pie me-2"></i>إحصائيات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>إجمالي النشاط</span>
                                    <strong><?php echo number_format($activity_stats['total_activities']); ?></strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>نشاط اليوم</span>
                                    <strong><?php echo number_format($activity_stats['today_activities']); ?></strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>نشاط الأسبوع</span>
                                    <strong><?php echo number_format($activity_stats['week_activities']); ?></strong>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>نشاط الشهر</span>
                                    <strong><?php echo number_format($activity_stats['month_activities']); ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات البيانات -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        فواتير المبيعات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($user_stats['sales_count']); ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo number_format($user_stats['total_sales_amount'], 2); ?> ر.س
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        فواتير المشتريات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($user_stats['purchases_count']); ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo number_format($user_stats['total_purchases_amount'], 2); ?> ر.س
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-truck fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        العملاء
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($user_stats['customers_count']); ?>
                                    </div>
                                    <div class="text-xs text-muted">
                                        عميل مسجل
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-friends fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        صافي الربح
                                    </div>
                                    <?php $profit = $user_stats['total_sales_amount'] - $user_stats['total_purchases_amount']; ?>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($profit, 2); ?> ر.س
                                    </div>
                                    <div class="text-xs text-muted">
                                        <?php echo $profit >= 0 ? 'ربح' : 'خسارة'; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أحدث الأنشطة -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">أحدث الأنشطة</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>العملية</th>
                                    <th>الجدول</th>
                                    <th>الوصف</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($recent_activities->num_rows > 0): ?>
                                    <?php while ($activity = $recent_activities->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo date('Y-m-d H:i:s', strtotime($activity['created_at'])); ?></td>
                                        <td>
                                            <code><?php echo htmlspecialchars($activity['action']); ?></code>
                                        </td>
                                        <td>
                                            <?php if ($activity['table_name']): ?>
                                                <span class="badge bg-secondary"><?php echo htmlspecialchars($activity['table_name']); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($activity['description'] ?? ''); ?></td>
                                        <td><?php echo htmlspecialchars($activity['ip_address'] ?? ''); ?></td>
                                    </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="5" class="text-center text-muted">لا توجد أنشطة مسجلة</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </main>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.sidebar {
    min-height: 100vh;
}
.nav-link.active {
    background-color: #495057 !important;
}
</style>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
