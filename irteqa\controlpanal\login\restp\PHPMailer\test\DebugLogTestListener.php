<?php

/**
 * PHPMailer - language file tests.
 *
 * PHP version 5.5.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR>
 * <AUTHOR>
 * @copyright 2010 - 2020 <PERSON>
 * @copyright 2004 - 2009 <PERSON>
 * @copyright 2020 Juliette Reinders Folmer
 * @license   http://www.gnu.org/copyleft/lesser.html GNU Lesser General Public License
 */

namespace PHPMailer\Test;

use PHPUnit\Framework\TestListener;
use Yoast\PHPUnitPolyfills\TestListeners\TestListenerDefaultImplementation;

class DebugLogTestListener implements TestListener
{
    use TestListenerDefaultImplementation;

    private static $debugLog = '';

    public function add_error($test, $e, $time)
    {
        echo self::$debugLog;
    }

    public function add_failure($test, $e, $time)
    {
        echo self::$debugLog;
    }

    public function start_test($test)
    {
        self::$debugLog = '';
    }

    public static function debugLog($str)
    {
        self::$debugLog .= $str . PHP_EOL;
    }
}
