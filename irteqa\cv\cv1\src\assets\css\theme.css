/*! - - - - - - - - - - - - - -  Theme Styles - - - - - - - - - - - - - - - - */
/* - - - - - - - - - - - - - - - Components - - - - - - - - - - - - - - - - - -
  1. General
  2. Typography
  3. Home
  4. Navigation
  5. About
  6. Video
  7. Resume
  8. Skill
  9. Chart
  10. Portfolio
  11. Facts
  12. Blog
  13. Hire
  14. Contact
  15. Footer
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
 */

html, body {
  overflow-x: hidden;
}

body {
  background-color: #fff;
  color:        #282828;
  /* font-family:  'Roboto', 'sans-serif'; */
  font-family:  'sans-serif';
  font-size:    14px;
  font-weight:  300;
  /* font-display: swap; */
  line-height:  28px;
  -webkit-text-size-adjust:   100%;
  -webkit-font-smoothing:     antialiased !important;
  -webkit-overflow-scrolling: touch;
}

/* ---------------------------------------------- */
/* Link style */
/* ---------------------------------------------- */
a {
  color: #68c3a3;
}

  a
, a > * {
  cursor:   pointer;
  outline:  none;
  text-decoration: none;
}

  a:focus
, a:hover {
  color:    #333;
  outline:  none;
  text-decoration: none;
}


/* ---------------------------------------------- */
/* Transition elements */
/* ---------------------------------------------- */
  .navbar a
, .form-control {
  -webkit-transition: all 0.4s ease-in-out 0s;
     -moz-transition: all 0.4s ease-in-out 0s;
      -ms-transition: all 0.4s ease-in-out 0s;
       -o-transition: all 0.4s ease-in-out 0s;
          transition: all 0.4s ease-in-out 0s;
}

  a
, .btn {
  -webkit-transition: all 0.125s ease-in-out 0s;
     -moz-transition: all 0.125s ease-in-out 0s;
      -ms-transition: all 0.125s ease-in-out 0s;
       -o-transition: all 0.125s ease-in-out 0s;
          transition: all 0.125s ease-in-out 0s;
}


/* ---------------------------------------------- */
/* Reset box-shadow */
/* ---------------------------------------------- */

  .btn
, .form-control
, .form-control:hover
, .form-control:focus
, .navbar-custom .dropdown-menu {
  -webkit-box-shadow: none;
          box-shadow: none;
}


/* ---------------------------------------------- */
/* Typography */
/* ---------------------------------------------- */

h1, h2, h3, h4, h5, h6 {
  font-weight:    700;
  margin:         0;
  text-transform: uppercase;
}

h1 {
  font-size:    34px;
  line-height:  34px;
}
h2 {
  font-size:    30px;
  line-height:  30px;
}
h3 {
  font-size:    24px;
  line-height:  24px;
}
h4 {
  font-size:    18px;
}
h5 {
  font-size:    14px;
}
h6 {
  font-size:    12px;
}


/* ---------------------------------------------- */
/* FORM STYLES */
/* ---------------------------------------------- */
.form-control {
  background-color: #f7f7f7;
  border:           1px solid #e8e8e8;
  border-radius:    1;
  box-shadow:       none;
  height:           45px;
}

.form-control:focus {
  border-color: #68c3a3;
          box-shadow: 0 0 5px rgba(0, 0, 0, 0.6);
  -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, 0.6);
}


/* ---------------------------------------------- */
/* Alert Style */
/* ---------------------------------------------- */
/* .alert {
  padding:          10px 15px;
  margin-top:       20px;
  margin-bottom:    20px;
  border-radius:    0;
}

.alert strong {
  display:  inline-block !important;
  margin:   0 !important;
} */


/* ---------------------------------------------- */
/* BUTTON STYLES */
/* ---------------------------------------------- */

/* common-btn */
.btn {
  border:           0;
  border-bottom:    3px solid;
  font-weight:      700;
  line-height:      28px;
  padding:          9px 22px;
  text-transform:   uppercase;
}

/* large-btn */
  .btn-lg
, .btn-group-lg > .btn {
    border-radius:  4px;
    font-size:      14px;
    padding:        14px 20px;
}

/* btn-primary */
.btn-primary {
  background-color: #68c3a3;
  border-color:     rgba(51, 51, 51, 0.15);
}

  /* .btn-primary:hover
, .btn-primary:focus
, .btn-primary.focus
, .btn-primary:active
, .btn-primary.active
, .open > .dropdown-toggle.btn-primary {
  background-color: #4eb28f;
  border-color:     #4eb28f;
  color:            #fff;
} */

/* btn-info */
/* .btn-info {
  background-color: #52b3d9;
  border-color:     rgba(51, 51, 51, 0.15);
} */

  /* .btn-info:hover
, .btn-info:focus
, .btn-info.focus
, .btn-info:active
, .btn-info.active
, .open > .dropdown-toggle.btn-info {
  background-color: #4aa0c2;
  border-color:     #4aa0c2;
  color:            #fff;
} */

/* btn-default */
/* .btn-default {
  background:   transparent;
  border:       1px solid #fff;
  color:        #fff;
} */

  /* .btn-default:hover
, .btn-default:focus
, .btn-default.focus
, .btn-default:active
, .btn-default.active {
  background-color: #fff;
  border-color:     #fff;
  color:            #68c3a3;
} */

button:focus {
  outline: none;
}


/* ---------------------------------------------- */
/* IMAGES */
/* ---------------------------------------------- */
img {
  width: 100%;
}

hr {
  margin-top:    15px;
  margin-bottom: 15px;
}

.no-padding {
  padding: 0;
}


/* ---------------------------------------------- */
/* LIST STYLE */
/* ---------------------------------------------- */
ul, ol {
  margin:       0;
  padding:      0;
  list-style:   none;
}

ul.list-check {
}

ul.list-check li {
  position:     relative;
  font-size:    18px;
  line-height:  32px;
}

ul.list-check li::before {
}

/*ul.list-check li::before {
  color: #68c3a3;
  font-family:  fontawesome;
  font-weight:  400;
  content:      "\f00c";
  margin-right: 10px;
}*/


/* === MAIN === */
.section-padding {
  padding: 130px 0 130px;
}


/* ---------------------------------------------- */
/* SECTION TITLE */
/* ---------------------------------------------- */
.section-title {
  color:        #2c3e50;
  font-size:    50px;
  line-height:  50px;
  margin-bottom: 80px;
  position:     relative;
  text-align:   center;
}

.section-title::after {
  background: url("../img/section-divider.webp") no-repeat 0 0;
  bottom:   -40px;
  content:  "";
  height:   15px;
  left:     0;
  margin:   0 auto;
  position: absolute;
  right:    0;
  width:    113px;
}


/* ---------------------------------------------- */
/* HOME */
/* ---------------------------------------------- */
@media (max-width : 640px) {
  .tt-fullHeight{
    height: 560px !important;
  }
}

#home {
  background: url("../img/home2.webp") no-repeat center center;
  background-color: #222;
  background-size: cover;
  -webkit-background-attachment: fixed;
          background-attachment: fixed;
  padding: 0;
  position: relative;
}
#home:before {
  content: "";
  background-color: rgba(26, 30, 35, 0.85);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.intro {
  position: absolute;
  width: 100%;
  top: 50%;
  left: 0;
  text-align: center;
  -webkit-transform: translate(0%, -50%);
     -moz-transform: translate(0%, -50%);
      -ms-transform: translate(0%, -50%);
       -o-transform: translate(0%, -50%);
          transform: translate(0%, -50%);
  padding: 0 15px;
}

.intro h1 {
  color:            #52b3d9;
  font-size:        44px;
  line-height:      44px;
  margin-bottom:    16px;
}
.intro h1 span.first {
  color: #52b3d9;
}
.intro h1 span {
  color: #68c3a3;
}
.intro p {
  color:         #f7f7f7;
  margin-bottom: 25px;
}

.intro-sub {
  color:            #fff;
  font-size:        24px;
  line-height:      32px;
  font-weight:      700;
  text-transform:   uppercase;
  display:          block;
  margin-bottom:    20px;
}

/* ---------------------------------------------- */
/* SOCIAL ICONS */
/* ---------------------------------------------- */
.social-icons {
  margin-top: 20px;
}
.social-icons a i {
  position:     relative;
  color:        #fff;
  font-size:    18px;
  margin:       0 7px;
  line-height:  40px;
  text-align:   center;
  width:        40px;
  height:       40px;
}

.social-icons a i::before {
  position: relative;
  z-index:  1;
}

.social-icons a i::after {
  position:         absolute;
  content:          "";
  left:             0;
  top:              0;
  width:            40px;
  height:           40px;
  border:           1px solid #fff;
  border-radius:    3px;
  -webkit-transform:    rotate(45deg);
     -moz-transform:    rotate(45deg);
      -ms-transform:    rotate(45deg);
       -o-transform:    rotate(45deg);
          transform:    rotate(45deg);
  -webkit-transition:   all 300ms;
     -moz-transition:   all 300ms;
       -o-transition:   all 300ms;
          transition:   all 300ms;
}

.social-icons a i:hover::after {
  background:   #68c3a3;
  border:       1px solid transparent;
  z-index:      0;
}


/* ---------------------------------------------- */
/* NAVIGATION */
/* ---------------------------------------------- */
.header {
  border-bottom: 1px solid #eee;
  position: relative;
  width: 100%;
  padding-bottom: 1px;
  background: #fff;
  z-index: 100 !important;
}

.header::after {
  border-bottom:1px solid #eee;
  width: 100%;
  position: absolute;
  content: "";
  height: 2px;
  bottom: 1px;
  left: 0;
}

.navbar-custom {
  border: 0;
  border-radius: 0;
  margin: 0;
  text-transform: uppercase;
  font-family: 'Roboto Condensed', sans-serif;
}

  .navbar-custom
, .navbar-custom .dropdown-menu {
  background: #fff;
  padding: 0;
}

.navbar-custom ul li a span {
  margin-left:  5px;
}

.navbar-custom .dropdown-menu {
  background-color: #fff;
  border:           1px solid #ddd;
  border-top:       2px solid #68c3a3;
  border-radius:    0;
  border-bottom:    3px double #ddd;
  display:          block;
  margin-top:       10px;
  opacity:          0;
  visibility:       hidden;
  -webkit-transition:   all 400ms;
      -moz-transition:  all 400ms;
        -o-transition:  all 400ms;
            transition: all 400ms;
}

.navbar-right .dropdown-menu {
  right: auto;
  left:  0;
}
.navbar-custom .navbar-nav > li:hover .dropdown-menu {
  display:    block;
  opacity:    1;
  visibility: visible;
  margin-top: -2px;
}

.navbar-custom .navbar-nav > li > a {
  color:          #282828;
  font-size:      14px;
  font-weight:    700;
  padding-top:    20px;
  padding-bottom: 20px;
  text-transform: uppercase;
}

.navbar-custom .dropdown-menu > li {
  border-bottom: 1px solid #F0F0F0;
}

.navbar-custom .dropdown-menu > li:last-child {
  border-bottom: 0;
}

.navbar-custom .dropdown-menu > li > a {
  padding:     10px 20px;
  color:       #282828;
  font-size:   14px;
  font-weight: 700;
}

  .navbar-custom .nav li.active
, .navbar-custom .nav li a:hover
, .navbar-custom .nav li a:focus
, .navbar-custom .navbar-nav > li.active > a {
  background: none;
  outline:    0;
  color:      #68c3a3;
}

.navbar-custom .navbar-brand {
  font-weight: 700;
  font-size:   18px;
}

  .navbar-custom .nav .open > a
, .navbar-custom .dropdown-menu > li > a:hover
, .navbar-custom .dropdown-menu > li > a:focus {
  background: #f5f5f5;
}

.navbar-custom .navbar-toggle .icon-bar {
  background: #222;
}


/* ---------------------------------------------- */
/* About Section */
/* ---------------------------------------------- */
.short-info {
  margin-bottom: 40px;
}

.short-info h3 {
  margin-bottom: 30px;
}

.short-info ul {
  margin-top: 10px;
  display:inline-block;
}
.short-info ul li {
  /* width: 50%; */
  float: left;
}

.my-signature {
  margin-bottom: 45px;
}
.my-signature img {
  width: inherit;
}

/* download-button */
.download-button a {
  margin-right: 20px;
  color: #fff;
}
.download-button a i {
  margin-right: 10px;
}

.biography {
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border:1px solid #eee;
  position: relative;
}
.biography::after {
  content: "";
  border-bottom: 1px solid #eee;
  position: absolute;
  bottom: -3px;
  width: 100%;
  border-radius: 10px;
  height: 20px;
}
.biography ul {
  padding:25px 20px 30px;
}
.biography ul li {
  padding:5px 0;
  font-size: 16px;
  border-bottom:1px solid #eee;
}
.biography ul li:last-child {
  border-bottom:0;
}

.myphoto {
  position: relative;
  background: url("../img/photo-frame.webp") bottom center no-repeat;
  background-size: contain;
}
.myphoto img {
  position: relative;
  z-index: -1;
}


/* ---------------------------------------------- /*
 *  Resume Section */
/* ---------------------------------------------- */

/* === Resume Timeline === */
.resume-title h3 {
  text-align: center;
}
.resume-section > div > div {
  margin-bottom: 60px;
}
.resume-section > div > div:nth-of-type(2) {
  margin-bottom: 0;
}
.resume-title {
  margin-bottom: 30px;
}
.timeline {
  position: relative;
  padding: 0;
  list-style: none;
}

.resume {
  position: relative;
}

.resume:before {
  content: "";
  position: absolute;
  top: -8px;
  width: 10px;
  height: 10px;
  background-color: #e1e1e1;
  left: 50%;
  margin-left: -6px;
  -webkit-transform: rotate(45deg);
     -moz-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
       -o-transform: rotate(45deg);
          transform: rotate(45deg);
}

.resume:after {
  content: "";
  position: absolute;
  bottom: -8px;
  width: 10px;
  height: 10px;
  background-color: #e1e1e1;
  left: 50%;
  margin-left: -6px;
  -webkit-transform: rotate(45deg);
     -moz-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
       -o-transform: rotate(45deg);
          transform: rotate(45deg);
}

.timeline:before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  margin-left: -1.5px;
  background-color: #e1e1e1;
}

.timeline>li {
  position: relative;
  margin-bottom: 50px;
  min-height: 50px;
}

.timeline>li:before,
.timeline>li:after {
  content: " ";
  display: table;
}

.timeline>li:after {
  clear: both;
}

.timeline>li .timeline-panel {
  float: left;
  position: relative;
  width: 100%;
}

.timeline-content {
  padding: 5px 20px 0 20px;
}

.timeline > li .timeline-panel:before {
  right: auto;
  left: -15px;
  border-right-width: 15px;
  border-left-width: 0;
}

.timeline>li .timeline-panel:after {
  right: auto;
  left: -14px;
  border-right-width: 14px;
  border-left-width: 0;
}

.timeline>li .posted-date {
  position: relative;
  text-align: center;
  background-color: #fff;
  left: 20px;
}

.timeline>li .posted-date .month {
  font-size: 18px;
  line-height: 20px;
  font-weight: 700;
  display: block;
  color: #2c3e50;
  top: 0;
  float: left;
}

.timeline>li.timeline-inverted>.timeline-panel {
  float: right;
  text-align: left;
}

.timeline>li.timeline-inverted>.timeline-panel:before {
  right: auto;
  left: -15px;
  border-right-width: 15px;
  border-left-width: 0;
}

.timeline>li.timeline-inverted>.timeline-panel:after {
  right: auto;
  left: -14px;
  border-right-width: 14px;
  border-left-width: 0;
}

.timeline>li:last-child {
  margin-bottom: 0;
}

.timeline-heading h3 {
  font-size: 20px;
  line-height: 28px;
}
.timeline-heading span {
  display: block;
  font-weight: 700;
  margin-bottom: 20px;
}
.timeline-heading span a {
  text-decoration: underline;
}


@media(min-width:992px) {

  .timeline:before {
    left: 50%;
  }

  .timeline>li {
    margin-bottom: 15px;
    min-height: 100px;
  }

  .timeline>li .posted-date {
    position: absolute;
    text-align: center;
    background-color: #fff;
  }

  .timeline > li .timeline-panel {
    float: left;
    width: 43%;
    /*text-align: right;*/
    text-align: left;
    border: 1px solid #eee;
    position: relative;
  }

  .timeline-content {
    position: relative;
    padding: 24px;
    background-color: #fff;
    -webkit-transition: all 400ms;
       -moz-transition: all 400ms;
         -o-transition: all 400ms;
            transition: all 400ms;
  }

  .timeline-content::after {
    border: 1px solid #eee;
    width: 100%;
    position: absolute;
    content: "";
    bottom: -3px;
    height:10px;
    left: 0;
    z-index: -1;
  }

  .timeline>li .timeline-content:hover {
    background: #68c3a3;
    color: #fff;
  }

  .timeline>li .timeline-panel::before,
  .timeline>li.timeline-inverted .timeline-panel::before {
    content: "";
    top: 25px;
    position: absolute;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
  }
  .timeline>li .timeline-panel::before {
    left: 100%;
    border-left: 12px solid #ECECEC;
    -webkit-transition:all 400ms;
    -moz-transition:all 400ms;
    -o-transition:all 400ms;
    transition: all 400ms;
  }
  .timeline>li .timeline-panel:hover::before {
    border-left: 12px solid #68c3a3;
  }

  .timeline>li.timeline-inverted .timeline-panel::before {
    right: 100%;
    border-right: 12px solid #ECECEC;
    -webkit-transition:all 400ms;
    -moz-transition:all 400ms;
    -o-transition:all 400ms;
    transition: all 400ms;
  }
  .timeline>li.timeline-inverted .timeline-panel:hover::before {
    border-right: 12px solid #68c3a3;
    border-left: 0;
  }

  .timeline>li .timeline-panel::after,
  .timeline>li.timeline-inverted .timeline-panel::after {
    content: "";
    top: 25px;
    position: absolute;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
  }
  .timeline>li .timeline-panel::after {
    left: 100%;
    margin-left: -2px;
    border-left: 12px solid #FFFFFF;
    -webkit-transition:all 400ms;
    -moz-transition:all 400ms;
    -o-transition:all 400ms;
    transition: all 400ms;
  }
  .timeline>li .timeline-panel:hover::after {
    border-left: 12px solid #68c3a3;
  }

  .timeline>li.timeline-inverted .timeline-panel::after {
    right: 100%;
    margin-right: -2px;
    border-right: 12px solid #FFFFFF;
    -webkit-transition: all 400ms;
       -moz-transition: all 400ms;
         -o-transition: all 400ms;
            transition: all 400ms;
  }
  .timeline>li.timeline-inverted .timeline-panel:hover::after {
    border-right: 12px solid #68c3a3;
    border-left: 0;
  }

  .timeline>li .posted-date {
    left: 50%;
    width: 120px;
    margin-left: -60px;
    margin-top: 28px;
  }
  .timeline>li .posted-date .month {
    float: none;
  }

}


/* ---------------------------------------------- /*
 *  Skill Section */
/* ---------------------------------------------- */
.skills-section {
  background-color: #1a1e23;
  color: #fff;
}
.skills-section .section-title {
  color: #fff;
  margin-bottom: 110px;
}
.skills-section .section-title h2 {
  color: #fff;
}
.skill-title {
  width: 45%;
  float: left;
}
.skill-title h3 {
  font-size: 18px;
  font-weight: 400;
  margin-top: -6px;
  text-transform: capitalize;
}


/* ---------------------------------------------- /*
 *  progress-bar */
/* ---------------------------------------------- */
.skill-progress {
  position: relative;
}

.progress {
  height: 10px;
  margin-bottom: 40px;
  background-color: #3a4149;
  border-radius: 3px;
          box-shadow: 0 0 1px rgba(0, 0, 0, 0.6);
  -webkit-box-shadow: 0 0 1px rgba(0, 0, 0, 0.6);
  width: 45%;
}

.progress-bar {
  background-color: #68c3a3;
}

.progress .progress-bar.six-sec-ease-in-out {
  -webkit-transition: width 6s ease-in-out;
     -moz-transition: width 6s ease-in-out;
       -o-transition: width 6s ease-in-out;
          transition: width 6s ease-in-out;
}

.skill-chart h3 {
  margin-top: 35px;
  margin-bottom: 35px;
}

/* .our-progress {
  margin:10px 0 30px;
}

.our-progress h3 {
  font-weight: 400;
  margin-bottom: 5px;
} */

.progress-percent {
  margin-left: auto;
  margin-right: auto;
}


/* ---------------------------------------------- /*
 *  Chart CSS */
/* ---------------------------------------------- */
.chart {
  position: relative;
  display: inline-block;
  width: 140px;
  height: 140px;
  margin: 40px auto;
  text-align: center;
}
.chart canvas {
  position:absolute;
  top:0;
  left:0;
}
.percent {
  display: inline-block;
  margin: auto;
  line-height: 140px;
  font-size: 25px;
  font-weight: 500;
  text-align: center;
  z-index: 2;
}

.percent:after {
  content: '%';
}

.chart-text span {
  font-size: 18px;
  font-weight: 400;
  text-transform: capitalize;
  display: block;
  margin-top: 20px;
}

.percent:after {
  content:      '%';
  font-size:    .8em;
  margin-left:  0.1em;
}
/* .angular {margin-top:100px;} */
/* .angular .chart {margin-top:0;} */
/* .chart .chart-text h3{font-size:22px;} */


/* ---------------------------------------------- */
/* Portfolio Section                              */
/* ---------------------------------------------- */
.portfolio-section {
  background-color: #f7f7f7;
}
.portfolio-item {
  padding-right: 5px;
  padding-left: 5px;
  max-height: 235px;
  min-height: 235px;
}
.portfolio-bg {
  background: #fff;
  padding: 5px;
  margin-bottom: 20px;
  border: 1px solid #eee;
  border-bottom: 3px double #eee;
  min-height: 180px;
}
  .portfolio-bg img
, .portfolio-bg video {
  max-height: 180px;
  min-height: 180px;
  height: 180px;
}
/* .portfolio-bg picture {
  max-height: 180px;
  min-height: 180px;
  height: 180px;
} */
.portfolio-bg video {
  display: block;
  object-fit: fill;
  object-position: 50% 50%;
  width: 100%;
}
.portfolio {
  position: relative;
  overflow: hidden;
}
/* .portfolio picture { */
  /* display: block; */
  /* max-height: 180px; */
  /* min-height: 180px; */
  /* height: 180px; */
/* } */
/* .portfolio video { */
  /* display: block; */
  /* max-height: 180px; */
  /* min-height: 180px; */
  /* height: 180px; */
  /* object-fit: fill; */
  /* object-position: 50% 50%; */
  /* width: 100%; */
/* } */

/* ---------------------------------------------- */
/* Shuffle Filter                                 */
/* ---------------------------------------------- */
#filter {
  margin: 0 0 35px;
  text-align: center;
}

#filter li a {
  color: #68c3a3;
  display: block;
  text-transform: uppercase;
  padding: 0 15px;
  border-radius: 4px;
  transition: inherit;
}

#filter li a.active,
#filter li a:hover {
  position: relative;
  background: #68c3a3;
  color: #fff;
  border-bottom: 3px solid rgba(0, 0, 0, 0.15);
}

#filter li a.active::after,
#filter li a:hover::after {
  position: absolute;
  content: "";
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 10px solid rgba(104, 195, 163, 1);
  bottom: -9px;
  left: 50%;
  margin-left: -8px;
}

.tt-overlay {
  position: absolute;
  background-color: rgba(26, 30, 31, 0.85);
  width: 100%;
  height: 100%;
  opacity: 0;
}

.portfolio-info,
.links,
.links a i,
.links a i::after,
.tt-overlay {
  -webkit-transition: all 400ms;
     -moz-transition: all 400ms;
       -o-transition: all 400ms;
          transition: all 400ms;
}

.links {
  width: 90px;
  height: 40px;
  position: absolute;
  top: 40%;
  left: 50%;
  margin-top: -20px;
  margin-left: -45px;
  opacity: 0;
  -webkit-transform: translate(0,-50px);
     -moz-transform: translate(0,-50px);
      -ms-transform: translate(0,-50px);
       -o-transform: translate(0,-50px);
          transform: translate(0,-50px);
  z-index: 80;
}

.links a {
  z-index: 100;
}

.links a i {
  position: relative;
  color: #68c3a3;
  font-size: 16px;
  margin: 0 13px;
  z-index: 90;
}

.links a:hover i {
  color:  #fff;
}
.links a i::after {
  position: absolute;
  content: "";
  left: -7px;
  top: -7px;
  width: 30px;
  height: 30px;
  border: 1px solid #68c3a3;
  z-index: -1;
  border-radius: 3px;
  -webkit-transform: rotate(45deg);
     -moz-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
       -o-transform: rotate(45deg);
          transform: rotate(45deg);
}
.links a:hover i::after {
  background-color: #68c3a3;
}

.portfolio-info {
  position: absolute;
  bottom: -100%;
  left: 0;
  background-color: #68c3a3;
  color: #fff;
  height: 45px;
  width: 100%;
  text-align: center;
}

.portfolio:hover .tt-overlay,
.portfolio:hover .links {
  opacity: 1;
  -webkit-transform: translate(0,0);
     -moz-transform: translate(0,0);
      -ms-transform: translate(0,0);
       -o-transform: translate(0,0);
          transform: translate(0,0);
}
.portfolio:hover .portfolio-info{
  bottom: 0;
}
.portfolio-info h3 {
  font-size:    14px;
  font-weight:  700;
  line-height:  45px;
  margin:       0;
}


/* ---------------------------------------------- */
/* Facts Section */
/* ---------------------------------------------- */
.facts-section {
  /* background: url("../img/video-bg.jpg") no-repeat center center; */
  background: url("../img/video-bg.webp") no-repeat center center;
  background-attachment: fixed;
  background-size: cover;
  color: #fff;
}

.tt-overlay-bg {
  background: rgba(26, 30, 35, 0.85);
  padding: 110px 0;
}

.count-wrap i {
  font-size: 64px;
  margin-bottom: 25px;
}
.count-wrap h3 {
  font-size:      50px;
  line-height:    28px;
  margin-bottom:  20px;
}
.count-wrap p {
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}


/* ---------------------------------------------- */
/* Contact Section */
/* ---------------------------------------------- */
/* .contact-section {} */

.contact-section strong {
  display:        block;
  text-transform: uppercase;
}
.contact-form strong {
  margin-bottom: 40px;
}

.contact-form textarea.form-control {
  height: 115px;
}

.contact-form .btn {
  margin-top: 15px;
}

.contact-section div > i {
  float:        left;
  font-size:    45px;
  margin-right: 20px;
}

.contact-section address,
.contact-section .contact-number {
  font-weight: 400;
  line-height: 30px;
  margin-bottom: 45px;
  overflow: hidden;
}


/* ---------------------------------------------- /*
 *  MAP */
/* ---------------------------------------------- */
.location-map {
  padding: 4px;
  border: 1px solid #eee;
  border-bottom: 3px double #eee;
}
.map-canvas {
  height: 380px;
}


/* ---------------------------------------------- /*
 *  Footer Wrapper */
/* ---------------------------------------------- */
.footer-wrapper {
  background: #1a1e23;
  padding: 36px 0;
  color: #fff;
}
.copyright p {
  margin: 0;
}


/* ---------------------------------------------- /*
 *  Scroll to top */
/* ---------------------------------------------- */
.scroll-up {
  position: fixed;
  display: none;
  bottom: 1.8em;
  right: 1.8em;
  z-index: 999;
}

.scroll-up a {
  background-color: #68c3a3;
  display: block;
  width: 28px;
  height: 28px;
  text-align: center;
  color: #fff;
  font-size: 14px;
  line-height: 28px;
}

.scroll-up a:hover,
.scroll-up a:active {
  background-color: rgba(235, 235, 235, .8);
  color: #222;
}


/* ---------------------------------------------- /*
 *  Mouse animate icon */
/* ---------------------------------------------- */
.mouse-icon {
  position: absolute;
  left: 50%;
  bottom: 40px;
  border: 2px solid #fff;
  border-radius: 16px;
  height: 50px;
  width: 30px;
  margin-left: -17px;
  display: block;
  z-index: 10;
}

.mouse-icon .wheel {
  -webkit-animation-name: drop;
  -webkit-animation-duration: 1s;
  -webkit-animation-timing-function: linear;
  -webkit-animation-delay: 0s;
  -webkit-animation-iteration-count: infinite;
  -webkit-animation-play-state: running;
  animation-name: drop;
  animation-duration: 1s;
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-iteration-count: infinite;
  animation-play-state: running;
}

.mouse-icon .wheel {
  position: relative;
  border-radius: 10px;
  background: #fff;
  width: 4px;
  height: 10px;
  top: 4px;
  margin-left: auto;
  margin-right: auto;
}

@-webkit-keyframes drop {
  0%   { top:5px;  opacity: 0;}
  30%  { top:10px; opacity: 1;}
  100% { top:25px; opacity: 0;}
}

@keyframes drop {
  0%   { top:5px;  opacity: 0;}
  30%  { top:10px; opacity: 1;}
  100% { top:25px; opacity: 0;}
}


/* ---------------------------------------------- /*
 *  Preloader */
/* ---------------------------------------------- */
#tt-preloader {
  background: #fff;
  bottom: 0;
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 9999;
}

#pre-status,
.preload-placeholder {
  background-image: url("../img/preload/preloader-cogs.gif");
  background-position: center;
  background-repeat: no-repeat;
  height: 200px;
  left: 50%;
  margin: -100px 0 0 -100px;
  position: absolute;
  top: 50%;
  width: 200px;
}

.preload-placeholder {
  background: none;
  left: 0;
  margin: 0;
  text-align: center;
  top: 65%;
}


/* ---------------------------------------------- */
/* About Section */
/* ---------------------------------------------- */
.short-info {
  margin-bottom: 40px;
}

.short-info h3 {
  margin-bottom: 30px;
}
