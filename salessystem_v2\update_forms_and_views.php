<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث النماذج وصفحات العرض - salessystem_v2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .update-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 50px auto;
            max-width: 1000px;
            overflow: hidden;
        }
        .update-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .update-content {
            padding: 40px;
        }
        .update-section {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .file-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin: 2px;
        }
        .status-updated {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-needs-update {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-new {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <?php
    require_once __DIR__.'/config/init.php';
    ?>
    
    <div class="container">
        <div class="update-container">
            <div class="update-header">
                <h1><i class="fas fa-sync-alt"></i> تحديث النماذج وصفحات العرض</h1>
                <p class="mb-0">تحديث جميع النماذج والصفحات للتوافق مع التعديلات الأخيرة</p>
            </div>
            
            <div class="update-content">
                <!-- ملخص التحديثات -->
                <div class="update-section">
                    <h3><i class="fas fa-info-circle"></i> ملخص التحديثات المطلوبة</h3>
                    
                    <div class="alert alert-info">
                        <h5>التعديلات الأخيرة التي تحتاج تحديث النماذج:</h5>
                        <ul>
                            <li><strong>ربط user_id:</strong> جميع الجداول تحتوي الآن على عمود user_id</li>
                            <li><strong>البادئات:</strong> أسماء الجداول تستخدم بادئة المستخدم</li>
                            <li><strong>الدوال الجديدة:</strong> insertWithUserId() و updateWithUserId()</li>
                            <li><strong>الأمان:</strong> فلترة تلقائية بـ user_id في جميع العمليات</li>
                            <li><strong>نوع العميل:</strong> إضافة حقل customer_type (عميل/مورد)</li>
                        </ul>
                    </div>
                </div>

                <!-- حالة الملفات -->
                <div class="update-section">
                    <h3><i class="fas fa-files-o"></i> حالة ملفات النماذج والصفحات</h3>
                    
                    <?php
                    $files_status = [
                        'العملاء والموردين' => [
                            'customers.php' => 'updated',
                            'add_customer.php' => 'updated', 
                            'save_customer.php' => 'updated',
                            'get_customer.php' => 'updated'
                        ],
                        'المنتجات' => [
                            'products.php' => 'updated',
                            'save_product.php' => 'updated',
                            'get_product.php' => 'needs_update'
                        ],
                        'المبيعات' => [
                            'sales.php' => 'needs_update',
                            'add_sale.php' => 'needs_update',
                            'save_sale.php' => 'needs_update',
                            'view_sale.php' => 'needs_update'
                        ],
                        'المشتريات' => [
                            'purchases.php' => 'needs_update',
                            'add_purchase.php' => 'needs_update',
                            'save_purchase.php' => 'needs_update',
                            'view_purchase.php' => 'needs_update'
                        ],
                        'التقارير' => [
                            'reports.php' => 'needs_update',
                            'financial_report.php' => 'needs_update'
                        ],
                        'الإعدادات' => [
                            'settings.php' => 'needs_update',
                            'profile.php' => 'needs_update'
                        ]
                    ];

                    foreach ($files_status as $section => $files) {
                        echo "<h5>$section:</h5>";
                        echo "<div class='mb-3'>";
                        
                        foreach ($files as $file => $status) {
                            $status_class = '';
                            $status_text = '';
                            $icon = '';
                            
                            switch ($status) {
                                case 'updated':
                                    $status_class = 'status-updated';
                                    $status_text = 'محدث';
                                    $icon = 'fas fa-check';
                                    break;
                                case 'needs_update':
                                    $status_class = 'status-needs-update';
                                    $status_text = 'يحتاج تحديث';
                                    $icon = 'fas fa-exclamation-triangle';
                                    break;
                                case 'new':
                                    $status_class = 'status-new';
                                    $status_text = 'جديد';
                                    $icon = 'fas fa-plus';
                                    break;
                            }
                            
                            echo "<span class='file-status $status_class'>";
                            echo "<i class='$icon'></i> $file - $status_text";
                            echo "</span>";
                        }
                        
                        echo "</div>";
                    }
                    ?>
                </div>

                <!-- التحديثات المطبقة -->
                <div class="update-section">
                    <h3><i class="fas fa-check-circle"></i> التحديثات المطبقة</h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>ملفات العملاء والموردين:</h5>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    customers.php
                                    <span class="badge bg-success rounded-pill">محدث</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    add_customer.php
                                    <span class="badge bg-success rounded-pill">محدث</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    save_customer.php
                                    <span class="badge bg-success rounded-pill">محدث</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    get_customer.php
                                    <span class="badge bg-success rounded-pill">محدث</span>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>ملفات المنتجات:</h5>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    products.php
                                    <span class="badge bg-success rounded-pill">محدث</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    save_product.php
                                    <span class="badge bg-success rounded-pill">محدث</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    get_product.php
                                    <span class="badge bg-warning rounded-pill">يحتاج تحديث</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- التحديثات المطلوبة -->
                <div class="update-section">
                    <h3><i class="fas fa-tasks"></i> التحديثات المطلوبة</h3>
                    
                    <div class="alert alert-warning">
                        <h5>الملفات التي تحتاج تحديث:</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>المبيعات:</h6>
                                <ul>
                                    <li>sales.php - تحديث الاستعلامات</li>
                                    <li>add_sale.php - تحديث النموذج</li>
                                    <li>save_sale.php - استخدام الدوال الجديدة</li>
                                    <li>view_sale.php - تحديث العرض</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>المشتريات:</h6>
                                <ul>
                                    <li>purchases.php - تحديث الاستعلامات</li>
                                    <li>add_purchase.php - تحديث النموذج</li>
                                    <li>save_purchase.php - استخدام الدوال الجديدة</li>
                                    <li>view_purchase.php - تحديث العرض</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إرشادات التحديث -->
                <div class="update-section">
                    <h3><i class="fas fa-book"></i> إرشادات التحديث</h3>
                    
                    <div class="accordion" id="updateGuide">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingOne">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                    تحديث صفحات العرض
                                </button>
                            </h2>
                            <div id="collapseOne" class="accordion-collapse collapse show" data-bs-parent="#updateGuide">
                                <div class="accordion-body">
                                    <ol>
                                        <li>استبدال <code>FROM table_name</code> بـ <code>FROM `{getUserTableName('table', $username)}`</code></li>
                                        <li>إضافة فلترة <code>user_id = ?</code> لجميع الاستعلامات</li>
                                        <li>استخدام <code>$_SESSION['user_id']</code> في المعاملات</li>
                                        <li>تحديث استعلامات العد والجلب</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingTwo">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                    تحديث نماذج الإدخال
                                </button>
                            </h2>
                            <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#updateGuide">
                                <div class="accordion-body">
                                    <ol>
                                        <li>استبدال <code>INSERT INTO table</code> بـ <code>insertWithUserId('table', $data)</code></li>
                                        <li>استبدال <code>UPDATE table</code> بـ <code>updateWithUserId('table', $data, $where)</code></li>
                                        <li>إزالة إدراج <code>user_id</code> يدوياً (يتم تلقائياً)</li>
                                        <li>تحديث التحقق من التكرار ليشمل <code>user_id</code></li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="headingThree">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                    تحديث ملفات AJAX
                                </button>
                            </h2>
                            <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#updateGuide">
                                <div class="accordion-body">
                                    <ol>
                                        <li>إضافة فحص <code>user_id</code> في جميع العمليات</li>
                                        <li>استخدام <code>getUserTableName()</code> للجداول</li>
                                        <li>تحديث رسائل الاستجابة</li>
                                        <li>إضافة معالجة الأخطاء المحسنة</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="text-center mt-4">
                    <a href="test_user_id_linking.php" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-user-tag"></i> اختبار ربط user_id
                    </a>
                    <a href="test_user_tables.php" class="btn btn-info btn-lg me-3">
                        <i class="fas fa-link"></i> اختبار ربط الجداول
                    </a>
                    <a href="test_system.php" class="btn btn-success btn-lg me-3">
                        <i class="fas fa-chart-line"></i> تقرير النظام
                    </a>
                    <a href="index_safe.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-home"></i> الصفحة الرئيسية
                    </a>
                </div>

                <!-- معلومات إضافية -->
                <div class="mt-4 text-center text-muted">
                    <hr>
                    <p class="mb-0">
                        <small>
                            تحديث النماذج وصفحات العرض - salessystem_v2 | 
                            تاريخ التحديث: <?php echo date('Y-m-d H:i:s'); ?>
                        </small>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
