<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<style>
@import url("https://fonts.googleapis.com/css?family=DM+Sans:400,500,700&display=swap");

* { box-sizing: border-box; }

body {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: var(--app-bg);
  font-family: 'DM Sans', sans-serif;
  --text-dark: #1a2f45;
  --text-light: #445e78;
  --chat-box-dark: #1b243b;
  --chat-box-light: #fff;
  --button-text: #fff;
  --toggle-ball: #fff;
  --list-item-active: #e5effb;
  --number-bg: #6895A3;
  --message-box-1: #fff;
  --message-box-2: #1a233b;
  --chat-input: #060415;
  --border-light: #8187a2;
  --info-box-color-1: #004dfc;
  --info-box-color-2: #00ba9d;
  --info-box-color-3: #715fc2;
  --info-box-color-4: #ff562d;
  --info-icon-bg-1: #b1c7fc;
  --info-icon-bg-2: #c6f0ea;
  --info-icon-bg-3: #d9d3ff;
  --info-icon-bg-4: #ffe1d4;
  --app-bg: #060415;
  --box-color: #12172d;
  --box-border:  #477eff;
  --button-bg: #477eff;
  --text-dark: rgba(255,255,255,1);
  --text-light: rgba(255,255,255,.6);
  --info-box-1: rgba(160, 186, 242, 0.5);
  --info-box-2: rgba(168, 240, 229, 0.55);
  --info-box-3: rgba(194, 168, 240, 0.34);
  --info-box-4: rgba(240, 185, 168, 0.34);
  --toggle-bg: #477eff;
  --toggle-bg-off: #6895a3;
  --message-box-2: #477eff;
  --message-box-1: #576c99;
  --logo-path-1: #477eff;
  --logo-path-2: #576c99;
  --box-shadow: rgba( 18, 23, 45 ,.6) 0px 8px 24px;
  --scrollbar-thumb: linear-gradient(to bottom, rgba(43, 88, 118, .8), rgba(78, 67, 118, .8));

  &[data-theme="indigo"], &[data-theme='pink'] {
    --app-bg: #fff;
    --box-color: #f5f8fc;
    --box-border: #e7edf5;
    --text-dark: #1a2f45;
    --text-light: #445e78;
    --chat-box-dark: #1b243b;
    --chat-box-light: #fff;
    --button-bg: #004dfc;
    --button-text: #fff;
    --toggle-bg: #004dfc;
    --toggle-bg-off: #6895A3;
    --toggle-ball: #fff;
    --logo-path-1: #A0C9E1;
    --logo-path-2: #18689C;
    --list-item-active: #e5effb;
    --number-bg: #6895A3;
    --message-box-1: #fff;
    --message-box-2: #1a233b;
    --chat-input: #f5f8fc;
    --border-light: #e5e6eb;
    --info-box-1: rgba( 217, 228, 252 ,1);
    --info-box-2: rgba( 226, 246, 243 ,1);
    --info-box-3: #f7f3ff;
    --info-box-4: #fff1e9;
    --box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    --info-box-3: #b3a5ce;
    --info-box-4: #ffceb3;
    --scrollbar-thumb: linear-gradient(to top, rgba(131, 164, 212, .5), rgb(182, 188 ,255 , 0.4));
  }
  
  &[data-theme='pink'] {
    --box-color: #f8f1f1;
    --button-bg: #1a233b;
    --logo-path-1:  #e8d5db;
    --logo-path-2:  #6895A3;
    --toggle-bg: #1a233b;
  }
  
  &[data-theme='navy-dark'] {
    --app-bg: #060415;
    --box-color: #12172d;
    --box-border:  #477eff;
    --button-bg: #477eff;
    --toggle-bg: #477eff;
    --toggle-bg-off: #6895a3;
    --message-box-2: #477eff;
    --message-box-1: #576c99;
    --chat-input: #060415;
    --border-light: #8187a2;
  }
  
  &[data-theme='navy-dark'],
  &[data-theme='dark'] {
    --text-dark: rgba(255,255,255,1);
    --text-light: rgba(255,255,255,.6);
    --info-box-1: rgba(160, 186, 242, 0.5);
    --info-box-2: rgba(168, 240, 229, 0.55);
    --info-box-3: rgba(194, 168, 240, 0.34);
    --info-box-4: rgba(240, 185, 168, 0.34);
    --logo-path-1: #477eff;
    --logo-path-2: #576c99;
    --scrollbar-thumb: linear-gradient(to bottom, rgba(43, 88, 118, .8), rgba(78, 67, 118, .8));
  }
  
  &[data-theme='dark'] {
    --app-bg: #040508;
    --box-color: #131a24;
    --box-border:  #131a24;
    --button-bg: #1e2b4a;
    --toggle-bg: #477eff;
    --toggle-bg-off: #6895a3;
    --message-box-2: #1e2b4a;
    --message-box-1: #576c99;
    --chat-input: #040508;
    --border-light:  #040508;
  }
}

.app-container {
  display: flex;
  height: 100%;
  width: 100%;
  padding: 24px;
  max-width: 1800px;
}

.app-left {
  flex-basis: 320px;
  flex-shrink: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  max-width: 480px;
  margin-right: 24px;
  overflow-y: auto;
}

.app-left-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: var(--app-bg);
  
  h1 {
    margin: 0;
    font-size: 20px;
    color: var(--text-dark);
    margin-left: 12px;
  }
}

.app-logo svg {
  width: 40px;
  height: 40px;
  
  .path-1 { fill: var(--logo-path-1); }
  .path-2 { fill: var(--logo-path-2); }
}

.app-profile-box {
  border: 1px solid var(--box-border);
  background-color: var(--box-color);
  display: flex;
  align-items: center;
  flex-direction: column;
  padding: 16px;
  border-radius: 10px;
  margin-bottom: 24px;
  
  img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
  }
  
  &-name {
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
    color: var(--text-dark);
    margin: 4px 0;
    display: flex;
    align-items: center;
  }
  
  &-title {
    font-size: 12px;
    line-height: 16px;
    color: var(--text-light);
    margin: 0;
  }
}

.switch-status {
  display: flex;
  align-items: center;
  margin-top: 12px;
}

.switch-status input {
  opacity: 0;
  position: absolute;
  width: 0;
  height: 0;
  
  &:checked + label {
    background-color: var(--toggle-bg);
    
    &:before {
      left: 12px;
    }
  }
  
   &:checked ~ .toggle-offline {
     display: none;
   }
  
  &:checked ~ .toggle-online {
     display: inline-block;
   }
}

.toggle-text { user-select: none; color: var(--text-light); }

.toggle-online { display: none; }
.toggle-offline { display: inline-block; }

.label-toggle {
  background-color: var(--toggle-bg-off);
  border-radius: 24px;
  width: 24px;
  height: 14px;
  display: inline-block;
  margin-right: 4px;
  position: relative;
  transition: 0.2s;
  cursor: pointer;
  
  &:before {
    content: '';
    position: absolute;
    width: 10px;
    height: 10px;
    left: 2px;
    top: 50%;
    transform: translatey(-50%);
    border-radius: 50%;
    background-color: var(--toggle-ball);
    transition: all 0.2s ease;
  }
}

.toggle-text {
  font-size: 12px;
  line-height: 16px;
}

.app-setting {
  padding: 0;
  border: none;
  background-color: transparent;
  color: var(--text-dark);
  margin-left: 4px;
}

.chat-list {
  padding: 0;
  list-style: none;
  height: 0px;
  overflow: hidden;
  transition: .4s ease-in;
  display: none;
  opacity: 0;
  
  &.active {
    display: block;
    height: auto;
    max-height: auto;
    opacity: 1;
  }
  
  &-item {
    transition: .2s;
    padding: 8px 14px;
    margin: 0;
    border-radius: 6px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    position: relative;
    cursor: pointer;
    
    &.active {
      background-color: var(--box-color);
      
      span {
        color: var(--text-dark);
        font-weight: 700;
      }
    }
    
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 8px;
    }
    
    &:hover { background-color: var(--box-color); }
  }
  
  &-name {
    display: inline-block;
    font-size: 14px;
    line-height: 16px;
    font-weight: 500;
    color: var(--text-light);
  }
  
  &-header {
    font-weight: 700;
    line-height: 24px;
    font-size: 16px;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 40px;
    
    .c-number {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--button-bg);
      color: #fff;
      font-weight: 500;
      font-size: 12px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      margin-left: 8px;
    }
  }
}

.list-header-arrow {
  margin-left: auto;
  transform: rotate(180deg);
  transition: .2s ease;
  
  .chat-list-header.active & {
    transform: rotate(0deg);
  }
}

.app-main {
  flex: 3;
  height: 100%;
  background-color: var(--box-color);
  border-radius: 10px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  transition: .2s;
  
  &-header {
    position: sticky;
    top: 0;
    background-color: var(--box-color);
    border: 1px solid var(--box-border);
  }
}

.chat-wrapper {
  overflow: auto;
}

.chat-input {
  border: none;
  outline: none;
  height: 32px;
  flex: 1;
  margin-right: 4px;
  background-color: var(--chat-input);
  color: var(--text-dark);
  
  &:placeholder {
    color: var(--text-light);
    font-size: 12px;
  }
  
  &-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--box-shadow);
    margin-top: auto;
    border-radius: 6px;
    padding: 12px;
    background-color: var(--chat-input);
  }
}

.input-wrapper {
  border: 1px solid var(--border-light);
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  flex: 1;
  overflow: hidden;
  padding: 0 6px 0 12px;
  justify-content: space-between;
  margin: 0 8px;
  background-color: var(--chat-input);
}

.emoji-btn {
  border: none;
  background-color: transparent;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 4px;
  color: #ffca3e;
}

.chat-send-btn {
  height: 32px;
  color: #fff;
  background-color: var(--button-bg);
  border: none;
  border-radius: 4px;
  padding: 0 32px 0 10px;
  font-size: 12px;
  background-position: center right 8px;
  background-repeat: no-repeat;
  background-size: 14px;
  line-height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cdefs/%3E%3Cpath fill='%23fff' d='M481.508 210.336L68.414 38.926c-17.403-7.222-37.064-4.045-51.309 8.287C2.86 59.547-3.098 78.551 1.558 96.808L38.327 241h180.026c8.284 0 15.001 6.716 15.001 15.001 0 8.284-6.716 15.001-15.001 15.001H38.327L1.558 415.193c-4.656 18.258 1.301 37.262 15.547 49.595 14.274 12.357 33.937 15.495 51.31 8.287l413.094-171.409C500.317 293.862 512 276.364 512 256.001s-11.683-37.862-30.492-45.665z'/%3E%3C/svg%3E");
}

.chat-attachment-btn {
  border: none;
  padding: 0;
  background-color: transparent;
  color: var(--text-light);
  display: flex;
  align-items: center;
  opacity: 0.7;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  padding-bottom: 20px;
  
  &.reverse {
    justify-content: flex-end;
    
    .message-box {
      background-color: var(--message-box-2);
      color: #fff;
      
      &-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
      }
    }
    
    .message-pp {
      order: 2;
    }
  }
}

.message-pp {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}

.message-box {
  background-color: var(--message-box-1);
  box-shadow: var(--box-shadow);
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
  line-height: 16px;
  width: auto;
  max-width: 80%;
  color: var(--text-dark);
  
  &-wrapper {
    margin: 0 12px;
    
    span {
      font-size: 10px;
      line-height: 16px;
      color: var(--text-light);
      opacity: 0.6;
    }
  }
}

.app-right {
  flex-basis: 320px;
  flex-shrink: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-left: 24px;
  transition: .2s;
  overflow: auto;
  
  .app-profile-box img { margin-bottom: 16px; }
  
  .app-profile-box-title {
    width: 100%;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    
    svg {
      width: 16px;
      margin-right: 6px;
    }
  }
}

.archive-btn {
  color: #fff;
  height: 32px;
  margin-top: 16px;
  border-radius: 4px;
  background-color: var(--button-bg);
  display: flex;
  align-items: center;
  font-size: 14px;
  border: none;
  
  svg { margin-left: 6px; }
}

.app-activity-box {
  border-radius: 10px;
  padding: 16px 16px 8px 16px;
  border: 1px solid var(--box-border);
  background-color: var(--box-color);
}

.activity-info-boxes {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.activity-info-box {
  width: 48%;
  border-radius: 10px;
  padding: 10px;
  margin-bottom: 8px;
  display: flex;
  
  &.time {
    background-color: var(--info-box-1);
    color: var(--info-box-color-1);
    .info-icon-wrapper { background-color: var(--info-icon-bg-1);}
  }
  
  &.atendee {
    background-color: var(--info-box-2);
    color: var(--info-box-color-2);
    .info-icon-wrapper { background-color: var(--info-icon-bg-2);}
  }
  
  &.meeting {
    background-color: var(--info-box-3);
    color: var(--info-box-color-3);
    .info-icon-wrapper { background-color: var(--info-icon-bg-3);}
  }
  
  &.reject {
    background-color: var(--info-box-4);
    color: var(--info-box-color-4);
    .info-icon-wrapper { background-color: var(--info-icon-bg-4);}
  }
}

.info-icon-wrapper {
  border-radius: 50%;
  margin-right: 4px;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    width: 16px;
    height: 16px;
  }
}

.info-text-wrapper span {
  display: block;
}

.info-text-upper {
  font-size: 14px;
  font-weight: 700;
}

.info-text-bottom {
  font-size: 10px;
  color: var(--text-light);
}

.activity-info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 4px;
  margin-top: 16px;
}

.info-header-bold {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  color: var(--text-dark);
}

.info-header-light {
  color: var(--text-light);
  font-size: 12px;
  line-height: 24px;
}

.activity-days-wrapper {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
}

.day {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  height: 64px;
  
  &.current .chart {
    opacity: 1;
    transform: scale(1.2);
    transform-origin: bottom;
  }
  
  &:first-child {
    .chart { height: 20%; }
  }
  
  &:nth-child(3) .chart { height: 100%; }
  
  .chart {
    border-radius: 6px;
    height: 50%;
    width: 6px;
    background-color: var(--button-bg);
    opacity: 0.5;
    position: relative;
    
    &:before {
      content: '';
      position: absolute;
      background-color: var(--button-bg);
      width: 6px;
      height: 6px;
      border-radius: 50%;
      top: calc(100% + 4px);
    }
  }
  
  span {
    margin-top: 20px;
    display: block;
    font-size: 10px;
    color: var(--text-light);
  }
}

.app-right-bottom {
  position: fixed;
  bottom: 0;
  right: 24px;
  z-index: 1;
  background-color: var(--app-bg);
  display: flex;
  justify-content: flex-end;
  margin-top: auto;
  padding-bottom: 10px;
}

.app-theme-selector {
  background-color: var(--box-color);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--box-border);
  display: flex;
  justify-content: flex-end;
  width: auto;
  padding: 10px;
  border-radius: 6px;
}

.theme-color {
  width: 24px;
  height: 24px;
  margin-left: 4px;
  border: none;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  color: #fff;
  cursor: pointer;
  
  &.active {
    border: 1px solid rgba(  71, 126, 255 ,1);
    box-shadow: 0 0 0 3px rgba( 71, 126, 255  ,.2);
  }
  
  svg {
    width: 14px;
    height: 14px;
  }
  
  &.indigo { background-color: #18689C; }
  &.pink { background-color:  #e8d5db; }
  &.dark { background-color: #060415; }
  &.navy-dark { background-color: #192734; color: #fff; }
}

.open-left, .open-right {
  position: absolute;
  padding: 0;
  display: none;
  width: 20px;
  height: 20px;
  background-color: var(--box-color);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--box-border);
  border-radius: 4px;
  top: 24px;
}

.open-left {
  left: 0;
}

.open-right {
  right: 0;
}

::-webkit-scrollbar {
    width: 8px;
}
 
::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 4px rgba(0,0,0,0.1);
    border-radius: 10px;
}
 
::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: var(--scrollbar-thumb);
}

@media screen and (max-width: 1025px) {
  .app-left {
    transform: translateX(-100%);
    position: absolute;
    opacity: 0;
    top: 0;
    z-index: 2;
    height: 100%;
    width: 100%;
    transition: all 300ms cubic-bezier(0.19, 1, 0.56, 1);
    
    &.open {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .open-left { display: block; }
}

@media screen and (max-width: 680px) {
  .app-right {
    transform: translateX(100%);
    position: absolute;
    opacity: 0;
    top: 0;
    z-index: 2;
    height: 100%;
    width: 100%;
    transition: all 300ms cubic-bezier(0.19, 1, 0.56, 1);
    
    &.open {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .open-right { display: block; }
  
  .app-theme-selector {
    position: fixed;
    bottom: 0;
  }
  
  .app-main {
    height: calc(100% - 48px);
  }
}
</style>
<body>
    <div class="app-container">
        <div class="app-left">
          <div class="app-left-header">
            <div class="app-logo">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                <defs/>
                <path class="path-1" fill="#3eb798" d="M448 193.108h-32v80c0 44.176-35.824 80-80 80H192v32c0 35.344 28.656 64 64 64h96l69.76 58.08c6.784 5.648 16.88 4.736 22.528-2.048A16.035 16.035 0 00448 494.868v-45.76c35.344 0 64-28.656 64-64v-128c0-35.344-28.656-64-64-64z" opacity=".4"/>
                <path class="path-2" fill="#3eb798" d="M320 1.108H64c-35.344 0-64 28.656-64 64v192c0 35.344 28.656 64 64 64v61.28c0 8.832 7.168 16 16 16a16 16 0 0010.4-3.84l85.6-73.44h144c35.344 0 64-28.656 64-64v-192c0-35.344-28.656-64-64-64zm-201.44 182.56a22.555 22.555 0 01-4.8 4 35.515 35.515 0 01-5.44 3.04 42.555 42.555 0 01-6.08 1.76 28.204 28.204 0 01-6.24.64c-17.68 0-32-14.32-32-32-.336-17.664 13.712-32.272 31.376-32.608 2.304-.048 4.608.16 6.864.608a42.555 42.555 0 016.08 1.76c1.936.8 3.76 1.808 5.44 3.04a27.78 27.78 0 014.8 3.84 32.028 32.028 0 019.44 23.36 31.935 31.935 0 01-9.44 22.56zm96 0a31.935 31.935 0 01-22.56 9.44c-2.08.24-4.16.24-6.24 0a42.555 42.555 0 01-6.08-1.76 35.515 35.515 0 01-5.44-3.04 29.053 29.053 0 01-4.96-4 32.006 32.006 0 01-9.28-23.2 27.13 27.13 0 010-6.24 42.555 42.555 0 011.76-6.08c.8-1.936 1.808-3.76 3.04-5.44a37.305 37.305 0 013.84-4.96 37.305 37.305 0 014.96-3.84 25.881 25.881 0 015.44-3.04 42.017 42.017 0 016.72-2.4c17.328-3.456 34.176 7.808 37.632 25.136.448 2.256.656 4.56.608 6.864 0 8.448-3.328 16.56-9.28 22.56h-.16zm96 0a22.555 22.555 0 01-4.8 4 35.515 35.515 0 01-5.44 3.04 42.555 42.555 0 01-6.08 1.76 28.204 28.204 0 01-6.24.64c-17.68 0-32-14.32-32-32-.336-17.664 13.712-32.272 31.376-32.608 2.304-.048 4.608.16 6.864.608a42.555 42.555 0 016.08 1.76c1.936.8 3.76 1.808 5.44 3.04a27.78 27.78 0 014.8 3.84 32.028 32.028 0 019.44 23.36 31.935 31.935 0 01-9.44 22.56z"/>
              </svg>
            </div>
            <h1>QuickChat</h1>
          </div>
          <div class="app-profile-box">
            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=2250&q=80" alt="profile">
            <div class="app-profile-box-name">
              Pam Beesly Halpert
              <button class="app-setting">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="feather feather-settings" viewBox="0 0 24 24">
                  <defs/>
                  <circle cx="12" cy="12" r="3"/>
                  <path d="M19.4 15a1.65 1.65 0 00.33 1.82l.06.06a2 2 0 010 2.83 2 2 0 01-2.83 0l-.06-.06a1.65 1.65 0 00-1.82-.33 1.65 1.65 0 00-1 1.51V21a2 2 0 01-2 2 2 2 0 01-2-2v-.09A1.65 1.65 0 009 19.4a1.65 1.65 0 00-1.82.33l-.06.06a2 2 0 01-2.83 0 2 2 0 010-2.83l.06-.06a1.65 1.65 0 00.33-1.82 1.65 1.65 0 00-1.51-1H3a2 2 0 01-2-2 2 2 0 012-2h.09A1.65 1.65 0 004.6 9a1.65 1.65 0 00-.33-1.82l-.06-.06a2 2 0 010-2.83 2 2 0 012.83 0l.06.06a1.65 1.65 0 001.82.33H9a1.65 1.65 0 001-1.51V3a2 2 0 012-2 2 2 0 012 2v.09a1.65 1.65 0 001 1.51 1.65 1.65 0 001.82-.33l.06-.06a2 2 0 012.83 0 2 2 0 010 2.83l-.06.06a1.65 1.65 0 00-.33 1.82V9a1.65 1.65 0 001.51 1H21a2 2 0 012 2 2 2 0 01-2 2h-.09a1.65 1.65 0 00-1.51 1z"/>
                </svg>
              </button>
            </div>
            <p class="app-profile-box-title">UI Designer</p>
            <div class="switch-status">
              <input type="checkbox" name="switchStatus" id="switchStatus" checked>
              <label class="label-toggle" for="switchStatus"></label>
              <span class="toggle-text toggle-online">Online</span>
              <span class="toggle-text toggle-offline">Offline</span>
            </div>
          </div>
          <div class="chat-list-wrapper">
            <div class="chat-list-header">Active Conversations <span class="c-number">4</span>
              <svg class="list-header-arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" class="feather feather-chevron-up" viewBox="0 0 24 24">
                <defs/>
                <path d="M18 15l-6-6-6 6"/>
              </svg>
            </div>
            <ul class="chat-list active">
              <li class="chat-list-item active">
                <img src="https://images.unsplash.com/photo-1587080266227-677cc2a4e76e?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=934&q=80" alt="chat">
                <span class="chat-list-name">Dwight Schrute</span>
              </li>
              <li class="chat-list-item">
                <img src="https://images.unsplash.com/photo-1566465559199-50c6d9c81631?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=934&q=80" alt="chat">
                <span class="chat-list-name">Andy Bernard</span>
              </li>
              <li class="chat-list-item">
                <img src="https://images.unsplash.com/photo-1562788869-4ed32648eb72?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=2552&q=80" alt="chat">
                <span class="chat-list-name">Michael Scott</span>
              </li>
              <li class="chat-list-item">
                <img src="https://images.unsplash.com/photo-1604004555489-723a93d6ce74?ixlib=rb-1.2.1&ixid=*******************************************%3D&auto=format&fit=crop&w=934&q=80" alt="chat">
                <span class="chat-list-name">Holy Flax</span>
              </li>
              <li class="chat-list-item">
                <img src="https://images.unsplash.com/photo-1583864697784-a0efc8379f70?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1yZWxhdGVkfDE1fHx8ZW58MHx8fA%3D%3D&auto=format&fit=crop&w=800&q=60" alt="chat">
                <span class="chat-list-name">Jim Halpert</span>
              </li>
            </ul>
          </div>
          <div class="chat-list-wrapper">
            <div class="chat-list-header active">Achived Conversations <span class="c-number">3</span>
              <svg class="list-header-arrow" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="3" class="feather feather-chevron-up" viewBox="0 0 24 24">
                <defs/>
                <path d="M18 15l-6-6-6 6"/>
              </svg>
            </div>
            <ul class="chat-list">
              <li class="chat-list-item">
                <img src="https://images.unsplash.com/photo-1542042457485-a4c7afd74cd5?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=1270&q=80" alt="chat">
                <span class="chat-list-name">Toby Flenderson</span>
              </li>
              <li class="chat-list-item">
                <img src="https://images.unsplash.com/photo-1477118476589-bff2c5c4cfbb?ixid=MXwxMjA3fDB8MHxzZWFyY2h8MjV8fHdvbWFufGVufDB8fDB8&ixlib=rb-1.2.1&auto=format&fit=crop&w=900&q=60" alt="chat">
                <span class="chat-list-name">Kelly Kapoor</span>
              </li>
              <li class="chat-list-item">
                <img src="https://images.unsplash.com/photo-1528763380143-65b3ac89a3ff?ixlib=rb-1.2.1&ixid=MXwxMjA3fDB8MHxwaG90by1yZWxhdGVkfDExfHx8ZW58MHx8fA%3D%3D&auto=format&fit=crop&w=900&q=60" alt="chat">
                <span class="chat-list-name">Roy Andersson</span>
              </li>
            </ul>
          </div>
        </div>
        <div class="app-main">
          <div class="chat-wrapper">
            <div class="message-wrapper reverse">
              <img class="message-pp" src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=2550&q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                 Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur
                </div>
                <span>9h ago</span>
              </div>
            </div>
            <div class="message-wrapper reverse">
              <img class="message-pp" src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=2550&q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                  Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                </div>
                <span>9h ago</span>
              </div>
            </div>
            <div class="message-wrapper">
              <img class="message-pp" src="https://images.unsplash.com/photo-1587080266227-677cc2a4e76e?ixid=*******************************************%3D&amp;ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=934&amp;q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                  Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit, sed quia consequuntur magni dolores eos qui ratione voluptatem sequi nesciunt. Neque porro quisquam est, qui dolorem ipsum quia dolor sit amet, consectetur
                </div>
                <span>9h ago</span>
              </div>
            </div>
            <div class="message-wrapper">
              <img class="message-pp" src="https://images.unsplash.com/photo-1587080266227-677cc2a4e76e?ixid=*******************************************%3D&amp;ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=934&amp;q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit 
                </div>
                <span>9h ago</span>
              </div>
            </div>
            <div class="message-wrapper reverse">
              <img class="message-pp" src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=2550&q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                  Lorem ipsum dolor sit amet
                </div>
                <span>9h ago</span>
              </div>
            </div>
            <div class="message-wrapper reverse">
              <img class="message-pp" src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=2550&q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                  Lorem ipsum dolor
                </div>
                <span>9h ago</span>
              </div>
            </div>
            <div class="message-wrapper">
              <img class="message-pp" src="https://images.unsplash.com/photo-1587080266227-677cc2a4e76e?ixid=*******************************************%3D&amp;ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=934&amp;q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit
                </div>
                <span>9h ago</span>
              </div>
            </div>
            <div class="message-wrapper">
              <img class="message-pp" src="https://images.unsplash.com/photo-1587080266227-677cc2a4e76e?ixid=*******************************************%3D&amp;ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=934&amp;q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit
                </div>
                <span>9h ago</span>
              </div>
            </div>
            <div class="message-wrapper">
              <img class="message-pp" src="https://images.unsplash.com/photo-1587080266227-677cc2a4e76e?ixid=*******************************************%3D&amp;ixlib=rb-1.2.1&amp;auto=format&amp;fit=crop&amp;w=934&amp;q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                  Lorem ipsum dolor sit amet
                </div>
                <span>9h ago</span>
              </div>
            </div>
          <div class="message-wrapper reverse">
              <img class="message-pp" src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=2550&q=80" alt="profile-pic">
              <div class="message-box-wrapper">
                <div class="message-box">
                  Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
                </div>
                <span>9h ago</span>
              </div>
            </div>
          </div>
          <div class="chat-input-wrapper">
            <button class="chat-attachment-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="feather feather-paperclip" viewBox="0 0 24 24">
                <defs/>
                <path d="M21.44 11.05l-9.19 9.19a6 6 0 01-8.49-8.49l9.19-9.19a4 4 0 015.66 5.66l-9.2 9.19a2 2 0 01-2.83-2.83l8.49-8.48"/>
              </svg>
            </button>
            <div class="input-wrapper">
              <input type="text" class="chat-input" placeholder="Enter your message here">
              <button class="emoji-btn">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="feather feather-smile" viewBox="0 0 24 24">
                <defs/>
                <circle cx="12" cy="12" r="10"/>
                <path d="M8 14s1.5 2 4 2 4-2 4-2M9 9h.01M15 9h.01"/>
              </svg>
            </button>
            </div>
            <button class="chat-send-btn">Send</button>
          </div>
        </div>
        <div class="app-right">
          <div class="app-profile-box">
            <img src="https://images.unsplash.com/photo-1587080266227-677cc2a4e76e?ixid=*******************************************%3D&ixlib=rb-1.2.1&auto=format&fit=crop&w=934&q=80" alt="profile">
            <p class="app-profile-box-title name"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-user"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>Dwight Scrute</p>
            <p class="app-profile-box-title mail"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-mail"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/><polyline points="22,6 12,13 2,6"/></svg><EMAIL></p>
            <button class="archive-btn">Archive<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="feather feather-archive" viewBox="0 0 24 24">
        <defs/>
        <path d="M21 8v13H3V8M1 3h22v5H1zM10 12h4"/>
      </svg></button>
          </div>
          <div class="app-activity-box">
            <div class="activity-info-boxes">
              <div class="activity-info-box time">
                <div class="info-icon-wrapper">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="feather feather-clock" viewBox="0 0 24 24">
                    <defs/>
                    <circle cx="12" cy="12" r="10"/>
                    <path d="M12 6v6l4 2"/>
                  </svg>
                </div>
                <div class="info-text-wrapper">
                  <span class="info-text-upper">13h</span>
                  <span class="info-text-bottom">Time</span>
                </div>
              </div>
              <div class="activity-info-box atendee">
                <div class="info-icon-wrapper">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="feather feather-users" viewBox="0 0 24 24">
                    <defs/>
                    <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2"/>
                    <circle cx="9" cy="7" r="4"/>
                    <path d="M23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75"/>
                  </svg>
                </div>
                <div class="info-text-wrapper">
                  <span class="info-text-upper">32</span>
                  <span class="info-text-bottom">Atendeed</span>
                </div>
              </div>
              <div class="activity-info-box meeting">
                <div class="info-icon-wrapper">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="feather feather-calendar" viewBox="0 0 24 24">
                    <defs/>
                    <rect width="18" height="18" x="3" y="4" rx="2" ry="2"/>
                    <path d="M16 2v4M8 2v4M3 10h18"/>
                  </svg>
                </div>
                <div class="info-text-wrapper">
                  <span class="info-text-upper">122</span>
                  <span class="info-text-bottom">Meetings</span>
                </div>
              </div>
              <div class="activity-info-box reject">
                <div class="info-icon-wrapper">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" class="feather feather-x-square" viewBox="0 0 24 24">
                    <defs/>
                    <rect width="18" height="18" x="3" y="3" rx="2" ry="2"/>
                    <path d="M9 9l6 6M15 9l-6 6"/>
                  </svg>
                </div>
                <div class="info-text-wrapper">
                  <span class="info-text-upper">12</span>
                  <span class="info-text-bottom">Rejected</span>
                </div>
              </div>
            </div>
            <div class="activity-info-header">
              <span class=
      "info-header-bold">Current Week</span>
               <span class=
      "info-header-light">Activity</span>
            </div>
            <div class="activity-days-wrapper">
              <div class="day">
                <div class="chart"></div>
                <span>MON</span>
              </div>
              <div class="day">
                <div class="chart"></div>
                <span>TUE</span>
              </div>
              <div class="day current">
                <div class="chart"></div>
                <span>WED</span>
              </div>
              <div class="day">
                <div class="chart"></div>
                <span>THU</span>
              </div>
              <div class="day">
                <div class="chart"></div>
                <span>FRI</span>
              </div>
              <div class="day">
                <div class="chart"></div>
                <span>SAT</span>
              </div>
              <div class="day">
                <div class="chart"></div>
                <span>SUN</span>
              </div>
            </div>
          </div>
          <div class="app-right-bottom">
            <div class="app-theme-selector">
            <button class="theme-color indigo" data-color="indigo">
              <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512" title="Indigo">
                <defs/>
                <path fill="#fff" d="M451.648.356c-25.777 2.712-56.79 19.872-94.811 52.46-68.786 58.958-149.927 160.756-202.185 234-38.158-5.951-78.375 10.368-102.187 40.133C8.758 381.584 45.347 430.34 4.12 473.811c-7.179 7.569-4.618 20.005 4.98 24.114 67.447 28.876 153.664 10.879 194.109-31.768 24.718-26.063 38.167-64.54 31.411-100.762 72.281-55.462 172.147-140.956 228.7-211.885 31.316-39.277 47.208-70.872 48.584-96.59C513.759 22.273 486.87-3.346 451.648.356zM181.443 445.511c-27.362 28.85-87.899 45.654-141.767 31.287 30.12-48.043 4.229-91.124 36.214-131.106 26.246-32.808 79.034-41.993 109.709-11.317 35.839 35.843 19.145 86.566-4.156 111.136zm3.07-148.841c7.354-10.167 18.887-25.865 33.29-44.659l49.22 49.224c-18.125 14.906-33.263 26.86-43.077 34.494-8.842-15.879-22.526-30.108-39.433-39.059zM481.948 55.316c-3.368 63.004-143.842 186.021-191.797 226.621l-53.785-53.79c39.458-49.96 155.261-191.312 218.422-197.954 16.851-1.775 28.03 8.858 27.16 25.123z"/>
              </svg>
            </button>
            <button class="theme-color pink" data-color="pink" title="Pink">
              <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512" >
                <defs/>
                <path fill="#fff" d="M451.648.356c-25.777 2.712-56.79 19.872-94.811 52.46-68.786 58.958-149.927 160.756-202.185 234-38.158-5.951-78.375 10.368-102.187 40.133C8.758 381.584 45.347 430.34 4.12 473.811c-7.179 7.569-4.618 20.005 4.98 24.114 67.447 28.876 153.664 10.879 194.109-31.768 24.718-26.063 38.167-64.54 31.411-100.762 72.281-55.462 172.147-140.956 228.7-211.885 31.316-39.277 47.208-70.872 48.584-96.59C513.759 22.273 486.87-3.346 451.648.356zM181.443 445.511c-27.362 28.85-87.899 45.654-141.767 31.287 30.12-48.043 4.229-91.124 36.214-131.106 26.246-32.808 79.034-41.993 109.709-11.317 35.839 35.843 19.145 86.566-4.156 111.136zm3.07-148.841c7.354-10.167 18.887-25.865 33.29-44.659l49.22 49.224c-18.125 14.906-33.263 26.86-43.077 34.494-8.842-15.879-22.526-30.108-39.433-39.059zM481.948 55.316c-3.368 63.004-143.842 186.021-191.797 226.621l-53.785-53.79c39.458-49.96 155.261-191.312 218.422-197.954 16.851-1.775 28.03 8.858 27.16 25.123z"/>
              </svg>
            </button>
            <button class="theme-color navy-dark active" data-color="navy-dark" title="Navy Dark">
              <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512">
                <defs/>
                <path fill="#fff" d="M451.648.356c-25.777 2.712-56.79 19.872-94.811 52.46-68.786 58.958-149.927 160.756-202.185 234-38.158-5.951-78.375 10.368-102.187 40.133C8.758 381.584 45.347 430.34 4.12 473.811c-7.179 7.569-4.618 20.005 4.98 24.114 67.447 28.876 153.664 10.879 194.109-31.768 24.718-26.063 38.167-64.54 31.411-100.762 72.281-55.462 172.147-140.956 228.7-211.885 31.316-39.277 47.208-70.872 48.584-96.59C513.759 22.273 486.87-3.346 451.648.356zM181.443 445.511c-27.362 28.85-87.899 45.654-141.767 31.287 30.12-48.043 4.229-91.124 36.214-131.106 26.246-32.808 79.034-41.993 109.709-11.317 35.839 35.843 19.145 86.566-4.156 111.136zm3.07-148.841c7.354-10.167 18.887-25.865 33.29-44.659l49.22 49.224c-18.125 14.906-33.263 26.86-43.077 34.494-8.842-15.879-22.526-30.108-39.433-39.059zM481.948 55.316c-3.368 63.004-143.842 186.021-191.797 226.621l-53.785-53.79c39.458-49.96 155.261-191.312 218.422-197.954 16.851-1.775 28.03 8.858 27.16 25.123z"/>
              </svg>
            </button>
            <button class="theme-color dark" data-color="dark" title="Dark">
              <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512">
                <defs/>
                <path fill="currentColor" d="M451.648.356c-25.777 2.712-56.79 19.872-94.811 52.46-68.786 58.958-149.927 160.756-202.185 234-38.158-5.951-78.375 10.368-102.187 40.133C8.758 381.584 45.347 430.34 4.12 473.811c-7.179 7.569-4.618 20.005 4.98 24.114 67.447 28.876 153.664 10.879 194.109-31.768 24.718-26.063 38.167-64.54 31.411-100.762 72.281-55.462 172.147-140.956 228.7-211.885 31.316-39.277 47.208-70.872 48.584-96.59C513.759 22.273 486.87-3.346 451.648.356zM181.443 445.511c-27.362 28.85-87.899 45.654-141.767 31.287 30.12-48.043 4.229-91.124 36.214-131.106 26.246-32.808 79.034-41.993 109.709-11.317 35.839 35.843 19.145 86.566-4.156 111.136zm3.07-148.841c7.354-10.167 18.887-25.865 33.29-44.659l49.22 49.224c-18.125 14.906-33.263 26.86-43.077 34.494-8.842-15.879-22.526-30.108-39.433-39.059zM481.948 55.316c-3.368 63.004-143.842 186.021-191.797 226.621l-53.785-53.79c39.458-49.96 155.261-191.312 218.422-197.954 16.851-1.775 28.03 8.858 27.16 25.123z"/>
              </svg>
            </button>
          </div>
          </div>
        </div>
        <div class="app-right-bottom">
            <div class="app-theme-selector">
            <button class="theme-color indigo" data-color="indigo">
              <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512" title="Indigo">
                <defs/>
                <path fill="#fff" d="M451.648.356c-25.777 2.712-56.79 19.872-94.811 52.46-68.786 58.958-149.927 160.756-202.185 234-38.158-5.951-78.375 10.368-102.187 40.133C8.758 381.584 45.347 430.34 4.12 473.811c-7.179 7.569-4.618 20.005 4.98 24.114 67.447 28.876 153.664 10.879 194.109-31.768 24.718-26.063 38.167-64.54 31.411-100.762 72.281-55.462 172.147-140.956 228.7-211.885 31.316-39.277 47.208-70.872 48.584-96.59C513.759 22.273 486.87-3.346 451.648.356zM181.443 445.511c-27.362 28.85-87.899 45.654-141.767 31.287 30.12-48.043 4.229-91.124 36.214-131.106 26.246-32.808 79.034-41.993 109.709-11.317 35.839 35.843 19.145 86.566-4.156 111.136zm3.07-148.841c7.354-10.167 18.887-25.865 33.29-44.659l49.22 49.224c-18.125 14.906-33.263 26.86-43.077 34.494-8.842-15.879-22.526-30.108-39.433-39.059zM481.948 55.316c-3.368 63.004-143.842 186.021-191.797 226.621l-53.785-53.79c39.458-49.96 155.261-191.312 218.422-197.954 16.851-1.775 28.03 8.858 27.16 25.123z"/>
              </svg>
            </button>
            <button class="theme-color pink" data-color="pink" title="Pink">
              <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512" >
                <defs/>
                <path fill="#fff" d="M451.648.356c-25.777 2.712-56.79 19.872-94.811 52.46-68.786 58.958-149.927 160.756-202.185 234-38.158-5.951-78.375 10.368-102.187 40.133C8.758 381.584 45.347 430.34 4.12 473.811c-7.179 7.569-4.618 20.005 4.98 24.114 67.447 28.876 153.664 10.879 194.109-31.768 24.718-26.063 38.167-64.54 31.411-100.762 72.281-55.462 172.147-140.956 228.7-211.885 31.316-39.277 47.208-70.872 48.584-96.59C513.759 22.273 486.87-3.346 451.648.356zM181.443 445.511c-27.362 28.85-87.899 45.654-141.767 31.287 30.12-48.043 4.229-91.124 36.214-131.106 26.246-32.808 79.034-41.993 109.709-11.317 35.839 35.843 19.145 86.566-4.156 111.136zm3.07-148.841c7.354-10.167 18.887-25.865 33.29-44.659l49.22 49.224c-18.125 14.906-33.263 26.86-43.077 34.494-8.842-15.879-22.526-30.108-39.433-39.059zM481.948 55.316c-3.368 63.004-143.842 186.021-191.797 226.621l-53.785-53.79c39.458-49.96 155.261-191.312 218.422-197.954 16.851-1.775 28.03 8.858 27.16 25.123z"/>
              </svg>
            </button>
            <button class="theme-color navy-dark active" data-color="navy-dark" title="Navy Dark">
              <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512">
                <defs/>
                <path fill="#fff" d="M451.648.356c-25.777 2.712-56.79 19.872-94.811 52.46-68.786 58.958-149.927 160.756-202.185 234-38.158-5.951-78.375 10.368-102.187 40.133C8.758 381.584 45.347 430.34 4.12 473.811c-7.179 7.569-4.618 20.005 4.98 24.114 67.447 28.876 153.664 10.879 194.109-31.768 24.718-26.063 38.167-64.54 31.411-100.762 72.281-55.462 172.147-140.956 228.7-211.885 31.316-39.277 47.208-70.872 48.584-96.59C513.759 22.273 486.87-3.346 451.648.356zM181.443 445.511c-27.362 28.85-87.899 45.654-141.767 31.287 30.12-48.043 4.229-91.124 36.214-131.106 26.246-32.808 79.034-41.993 109.709-11.317 35.839 35.843 19.145 86.566-4.156 111.136zm3.07-148.841c7.354-10.167 18.887-25.865 33.29-44.659l49.22 49.224c-18.125 14.906-33.263 26.86-43.077 34.494-8.842-15.879-22.526-30.108-39.433-39.059zM481.948 55.316c-3.368 63.004-143.842 186.021-191.797 226.621l-53.785-53.79c39.458-49.96 155.261-191.312 218.422-197.954 16.851-1.775 28.03 8.858 27.16 25.123z"/>
              </svg>
            </button>
            <button class="theme-color dark" data-color="dark" title="Dark">
              <svg xmlns="http://www.w3.org/2000/svg" width="512" height="512" viewBox="0 0 512 512">
                <defs/>
                <path fill="currentColor" d="M451.648.356c-25.777 2.712-56.79 19.872-94.811 52.46-68.786 58.958-149.927 160.756-202.185 234-38.158-5.951-78.375 10.368-102.187 40.133C8.758 381.584 45.347 430.34 4.12 473.811c-7.179 7.569-4.618 20.005 4.98 24.114 67.447 28.876 153.664 10.879 194.109-31.768 24.718-26.063 38.167-64.54 31.411-100.762 72.281-55.462 172.147-140.956 228.7-211.885 31.316-39.277 47.208-70.872 48.584-96.59C513.759 22.273 486.87-3.346 451.648.356zM181.443 445.511c-27.362 28.85-87.899 45.654-141.767 31.287 30.12-48.043 4.229-91.124 36.214-131.106 26.246-32.808 79.034-41.993 109.709-11.317 35.839 35.843 19.145 86.566-4.156 111.136zm3.07-148.841c7.354-10.167 18.887-25.865 33.29-44.659l49.22 49.224c-18.125 14.906-33.263 26.86-43.077 34.494-8.842-15.879-22.526-30.108-39.433-39.059zM481.948 55.316c-3.368 63.004-143.842 186.021-191.797 226.621l-53.785-53.79c39.458-49.96 155.261-191.312 218.422-197.954 16.851-1.775 28.03 8.858 27.16 25.123z"/>
              </svg>
            </button>
          </div>
          </div>
      </div>
</body>
<script>
let title = document.querySelectorAll(".chat-list-header");
let totalHeight = 0;

for(let i = 0; i < title.length; i++){
  let totalHeight = 0;
  title[i].addEventListener("click", function(){
    let result = this.nextElementSibling;
    let activeSibling = this.nextElementSibling.classList.contains('active');
    this.classList.toggle('active');
    result.classList.toggle("active");
    if(!activeSibling) {
      for( i= 0; i < result.children.length; i++) {
        totalHeight = totalHeight +  result.children[i].scrollHeight + 40;
      }
    } else {
      totalHeight = 0;
    }
    result.style.maxHeight =  totalHeight + "px";
  });
}

const themeColors = document.querySelectorAll('.theme-color');

themeColors.forEach(themeColor => {
  themeColor.addEventListener('click', (e) => {
    themeColors.forEach(c => c.classList.remove('active'));
    const theme = themeColor.getAttribute('data-color');
    document.body.setAttribute('data-theme', theme);
    themeColor.classList.add('active');
  });
});
</script>
</html>