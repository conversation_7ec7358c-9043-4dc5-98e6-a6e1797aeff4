{"version": 3, "file": "lang/summernote-en-US.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;;;;;;ACVA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCtBA;WACA;WACA;WACA;WACA;WACA,iCAAiC,WAAW;WAC5C;WACA;;;;;WCPA;WACA;WACA;WACA;WACA,yCAAyC,wCAAwC;WACjF;WACA;WACA;;;;;WCPA;;;;;WCAA;WACA;WACA;WACA,uDAAuD,iBAAiB;WACxE;WACA,gDAAgD,aAAa;WAC7D;;;;;;;;;;ACNA;AAEAA,0DAAA,GAAeA,0DAAA,IAAgB;AAC7BE,EAAAA,IAAI,EAAE;AADuB,CAA/B;AAIAF,oDAAA,CAASA,+DAAT,EAA4B;AAC1B,WAAS;AACPI,IAAAA,IAAI,EAAE;AACJC,MAAAA,IAAI,EAAE,MADF;AAEJC,MAAAA,MAAM,EAAE,QAFJ;AAGJC,MAAAA,SAAS,EAAE,WAHP;AAIJC,MAAAA,KAAK,EAAE,mBAJH;AAKJC,MAAAA,MAAM,EAAE,aALJ;AAMJC,MAAAA,IAAI,EAAE,aANF;AAOJC,MAAAA,aAAa,EAAE,eAPX;AAQJC,MAAAA,SAAS,EAAE,WARP;AASJC,MAAAA,WAAW,EAAE,aATT;AAUJC,MAAAA,IAAI,EAAE,WAVF;AAWJC,MAAAA,QAAQ,EAAE;AAXN,KADC;AAcPC,IAAAA,KAAK,EAAE;AACLA,MAAAA,KAAK,EAAE,SADF;AAELC,MAAAA,MAAM,EAAE,cAFH;AAGLC,MAAAA,UAAU,EAAE,aAHP;AAILC,MAAAA,UAAU,EAAE,aAJP;AAKLC,MAAAA,aAAa,EAAE,gBALV;AAMLC,MAAAA,UAAU,EAAE,eANP;AAOLC,MAAAA,SAAS,EAAE,YAPN;AAQLC,MAAAA,UAAU,EAAE,aARP;AASLC,MAAAA,SAAS,EAAE,cATN;AAULC,MAAAA,YAAY,EAAE,gBAVT;AAWLC,MAAAA,WAAW,EAAE,eAXR;AAYLC,MAAAA,cAAc,EAAE,kBAZX;AAaLC,MAAAA,SAAS,EAAE,aAbN;AAcLC,MAAAA,aAAa,EAAE,yBAdV;AAeLC,MAAAA,SAAS,EAAE,oBAfN;AAgBLC,MAAAA,eAAe,EAAE,mBAhBZ;AAiBLC,MAAAA,eAAe,EAAE,mBAjBZ;AAkBLC,MAAAA,oBAAoB,EAAE,6BAlBjB;AAmBLC,MAAAA,GAAG,EAAE,WAnBA;AAoBLC,MAAAA,MAAM,EAAE,cApBH;AAqBLC,MAAAA,QAAQ,EAAE;AArBL,KAdA;AAqCPC,IAAAA,KAAK,EAAE;AACLA,MAAAA,KAAK,EAAE,OADF;AAELC,MAAAA,SAAS,EAAE,YAFN;AAGLrB,MAAAA,MAAM,EAAE,cAHH;AAILiB,MAAAA,GAAG,EAAE,WAJA;AAKLK,MAAAA,SAAS,EAAE;AALN,KArCA;AA4CPC,IAAAA,IAAI,EAAE;AACJA,MAAAA,IAAI,EAAE,MADF;AAEJvB,MAAAA,MAAM,EAAE,aAFJ;AAGJwB,MAAAA,MAAM,EAAE,QAHJ;AAIJC,MAAAA,IAAI,EAAE,MAJF;AAKJC,MAAAA,aAAa,EAAE,iBALX;AAMJT,MAAAA,GAAG,EAAE,kCAND;AAOJU,MAAAA,eAAe,EAAE,oBAPb;AAQJC,MAAAA,WAAW,EAAE;AART,KA5CC;AAsDPC,IAAAA,KAAK,EAAE;AACLA,MAAAA,KAAK,EAAE,OADF;AAELC,MAAAA,WAAW,EAAE,eAFR;AAGLC,MAAAA,WAAW,EAAE,eAHR;AAILC,MAAAA,UAAU,EAAE,iBAJP;AAKLC,MAAAA,WAAW,EAAE,kBALR;AAMLC,MAAAA,MAAM,EAAE,YANH;AAOLC,MAAAA,MAAM,EAAE,eAPH;AAQLC,MAAAA,QAAQ,EAAE;AARL,KAtDA;AAgEPC,IAAAA,EAAE,EAAE;AACFrC,MAAAA,MAAM,EAAE;AADN,KAhEG;AAmEPsC,IAAAA,KAAK,EAAE;AACLA,MAAAA,KAAK,EAAE,OADF;AAELC,MAAAA,CAAC,EAAE,QAFE;AAGLC,MAAAA,UAAU,EAAE,OAHP;AAILC,MAAAA,GAAG,EAAE,MAJA;AAKLC,MAAAA,EAAE,EAAE,UALC;AAMLC,MAAAA,EAAE,EAAE,UANC;AAOLC,MAAAA,EAAE,EAAE,UAPC;AAQLC,MAAAA,EAAE,EAAE,UARC;AASLC,MAAAA,EAAE,EAAE,UATC;AAULC,MAAAA,EAAE,EAAE;AAVC,KAnEA;AA+EPC,IAAAA,KAAK,EAAE;AACLC,MAAAA,SAAS,EAAE,gBADN;AAELC,MAAAA,OAAO,EAAE;AAFJ,KA/EA;AAmFPC,IAAAA,OAAO,EAAE;AACPC,MAAAA,IAAI,EAAE,MADC;AAEPC,MAAAA,UAAU,EAAE,aAFL;AAGPC,MAAAA,QAAQ,EAAE;AAHH,KAnFF;AAwFPC,IAAAA,SAAS,EAAE;AACTA,MAAAA,SAAS,EAAE,WADF;AAETC,MAAAA,OAAO,EAAE,SAFA;AAGTC,MAAAA,MAAM,EAAE,QAHC;AAITC,MAAAA,IAAI,EAAE,YAJG;AAKTC,MAAAA,MAAM,EAAE,cALC;AAMTC,MAAAA,KAAK,EAAE,aANE;AAOTC,MAAAA,OAAO,EAAE;AAPA,KAxFJ;AAiGPC,IAAAA,KAAK,EAAE;AACLC,MAAAA,MAAM,EAAE,cADH;AAELC,MAAAA,IAAI,EAAE,YAFD;AAGLC,MAAAA,UAAU,EAAE,kBAHP;AAILC,MAAAA,UAAU,EAAE,YAJP;AAKLC,MAAAA,WAAW,EAAE,aALR;AAMLC,MAAAA,cAAc,EAAE,iBANX;AAOLC,MAAAA,KAAK,EAAE,OAPF;AAQLC,MAAAA,cAAc,EAAE,kBARX;AASLC,MAAAA,QAAQ,EAAE;AATL,KAjGA;AA4GPC,IAAAA,QAAQ,EAAE;AACRC,MAAAA,SAAS,EAAE,oBADH;AAERC,MAAAA,KAAK,EAAE,OAFC;AAGRC,MAAAA,cAAc,EAAE,iBAHR;AAIRC,MAAAA,MAAM,EAAE,QAJA;AAKRC,MAAAA,mBAAmB,EAAE,sBALb;AAMRC,MAAAA,aAAa,EAAE,gBANP;AAORC,MAAAA,SAAS,EAAE;AAPH,KA5GH;AAqHP3B,IAAAA,IAAI,EAAE;AACJ,gBAAU,QADN;AAEJ,yBAAmB,kBAFf;AAGJ,cAAQ,uBAHJ;AAIJ,cAAQ,uBAJJ;AAKJ,aAAO,KALH;AAMJ,eAAS,OANL;AAOJ,cAAQ,kBAPJ;AAQJ,gBAAU,oBARN;AASJ,mBAAa,uBATT;AAUJ,uBAAiB,2BAVb;AAWJ,sBAAgB,eAXZ;AAYJ,qBAAe,gBAZX;AAaJ,uBAAiB,kBAbb;AAcJ,sBAAgB,iBAdZ;AAeJ,qBAAe,gBAfX;AAgBJ,6BAAuB,uBAhBnB;AAiBJ,2BAAqB,qBAjBjB;AAkBJ,iBAAW,8BAlBP;AAmBJ,gBAAU,6BAnBN;AAoBJ,oBAAc,sDApBV;AAqBJ,kBAAY,sCArBR;AAsBJ,kBAAY,sCAtBR;AAuBJ,kBAAY,sCAvBR;AAwBJ,kBAAY,sCAxBR;AAyBJ,kBAAY,sCAzBR;AA0BJ,kBAAY,sCA1BR;AA2BJ,8BAAwB,wBA3BpB;AA4BJ,yBAAmB;AA5Bf,KArHC;AAmJP4B,IAAAA,OAAO,EAAE;AACPC,MAAAA,IAAI,EAAE,MADC;AAEPC,MAAAA,IAAI,EAAE;AAFC,KAnJF;AAuJPC,IAAAA,WAAW,EAAE;AACXA,MAAAA,WAAW,EAAE,oBADF;AAEXC,MAAAA,MAAM,EAAE;AAFG,KAvJN;AA2JPC,IAAAA,MAAM,EAAE;AACNC,MAAAA,WAAW,EAAE;AADP;AA3JD;AADiB,CAA5B", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///external umd \"jQuery\"", "webpack:///webpack/bootstrap", "webpack:///webpack/runtime/compat get default export", "webpack:///webpack/runtime/define property getters", "webpack:///webpack/runtime/hasOwnProperty shorthand", "webpack:///webpack/runtime/make namespace object", "webpack:///./src/lang/summernote-en-US.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"jQuery\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"jQuery\"], factory);\n\telse {\n\t\tvar a = typeof exports === 'object' ? factory(require(\"jQuery\")) : factory(root[\"jQuery\"]);\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function(__WEBPACK_EXTERNAL_MODULE__1145__) {\nreturn ", "module.exports = __WEBPACK_EXTERNAL_MODULE__1145__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "import $ from 'jquery';\n\n$.summernote = $.summernote || {\n  lang: {},\n};\n\n$.extend($.summernote.lang, {\n  'en-US': {\n    font: {\n      bold: 'Bold',\n      italic: 'Italic',\n      underline: 'Underline',\n      clear: 'Remove Font Style',\n      height: 'Line Height',\n      name: 'Font Family',\n      strikethrough: 'Strikethrough',\n      subscript: 'Subscript',\n      superscript: 'Superscript',\n      size: 'Font Size',\n      sizeunit: 'Font Size Unit',\n    },\n    image: {\n      image: 'Picture',\n      insert: 'Insert Image',\n      resizeFull: 'Resize full',\n      resizeHalf: 'Resize half',\n      resizeQuarter: 'Resize quarter',\n      resizeNone: 'Original size',\n      floatLeft: 'Float Left',\n      floatRight: 'Float Right',\n      floatNone: 'Remove float',\n      shapeRounded: 'Shape: Rounded',\n      shapeCircle: 'Shape: Circle',\n      shapeThumbnail: 'Shape: Thumbnail',\n      shapeNone: 'Shape: None',\n      dragImageHere: 'Drag image or text here',\n      dropImage: 'Drop image or Text',\n      selectFromFiles: 'Select from files',\n      maximumFileSize: 'Maximum file size',\n      maximumFileSizeError: 'Maximum file size exceeded.',\n      url: 'Image URL',\n      remove: 'Remove Image',\n      original: 'Original',\n    },\n    video: {\n      video: 'Video',\n      videoLink: 'Video Link',\n      insert: 'Insert Video',\n      url: 'Video URL',\n      providers: '(YouTube, Google Drive, Vimeo, Vine, Instagram, DailyMotion, Youku, Peertube)',\n    },\n    link: {\n      link: 'Link',\n      insert: 'Insert Link',\n      unlink: 'Unlink',\n      edit: 'Edit',\n      textToDisplay: 'Text to display',\n      url: 'To what URL should this link go?',\n      openInNewWindow: 'Open in new window',\n      useProtocol: 'Use default protocol',\n    },\n    table: {\n      table: 'Table',\n      addRowAbove: 'Add row above',\n      addRowBelow: 'Add row below',\n      addColLeft: 'Add column left',\n      addColRight: 'Add column right',\n      delRow: 'Delete row',\n      delCol: 'Delete column',\n      delTable: 'Delete table',\n    },\n    hr: {\n      insert: 'Insert Horizontal Rule',\n    },\n    style: {\n      style: 'Style',\n      p: 'Normal',\n      blockquote: 'Quote',\n      pre: 'Code',\n      h1: 'Header 1',\n      h2: 'Header 2',\n      h3: 'Header 3',\n      h4: 'Header 4',\n      h5: 'Header 5',\n      h6: 'Header 6',\n    },\n    lists: {\n      unordered: 'Unordered list',\n      ordered: 'Ordered list',\n    },\n    options: {\n      help: 'Help',\n      fullscreen: 'Full Screen',\n      codeview: 'Code View',\n    },\n    paragraph: {\n      paragraph: 'Paragraph',\n      outdent: 'Outdent',\n      indent: 'Indent',\n      left: 'Align left',\n      center: 'Align center',\n      right: 'Align right',\n      justify: 'Justify full',\n    },\n    color: {\n      recent: 'Recent Color',\n      more: 'More Color',\n      background: 'Background Color',\n      foreground: 'Text Color',\n      transparent: 'Transparent',\n      setTransparent: 'Set transparent',\n      reset: 'Reset',\n      resetToDefault: 'Reset to default',\n      cpSelect: 'Select',\n    },\n    shortcut: {\n      shortcuts: 'Keyboard shortcuts',\n      close: 'Close',\n      textFormatting: 'Text formatting',\n      action: 'Action',\n      paragraphFormatting: 'Paragraph formatting',\n      documentStyle: 'Document Style',\n      extraKeys: 'Extra keys',\n    },\n    help: {\n      'escape': 'Escape',\n      'insertParagraph': 'Insert Paragraph',\n      'undo': 'Undo the last command',\n      'redo': 'Redo the last command',\n      'tab': 'Tab',\n      'untab': 'Untab',\n      'bold': 'Set a bold style',\n      'italic': 'Set a italic style',\n      'underline': 'Set a underline style',\n      'strikethrough': 'Set a strikethrough style',\n      'removeFormat': 'Clean a style',\n      'justifyLeft': 'Set left align',\n      'justifyCenter': 'Set center align',\n      'justifyRight': 'Set right align',\n      'justifyFull': 'Set full align',\n      'insertUnorderedList': 'Toggle unordered list',\n      'insertOrderedList': 'Toggle ordered list',\n      'outdent': 'Outdent on current paragraph',\n      'indent': 'Indent on current paragraph',\n      'formatPara': 'Change current block\\'s format as a paragraph(P tag)',\n      'formatH1': 'Change current block\\'s format as H1',\n      'formatH2': 'Change current block\\'s format as H2',\n      'formatH3': 'Change current block\\'s format as H3',\n      'formatH4': 'Change current block\\'s format as H4',\n      'formatH5': 'Change current block\\'s format as H5',\n      'formatH6': 'Change current block\\'s format as H6',\n      'insertHorizontalRule': 'Insert horizontal rule',\n      'linkDialog.show': 'Show Link Dialog',\n    },\n    history: {\n      undo: 'Undo',\n      redo: 'Redo',\n    },\n    specialChar: {\n      specialChar: 'SPECIAL CHARACTERS',\n      select: 'Select Special characters',\n    },\n    output: {\n      noSelection: 'No Selection Made!',\n    },\n  },\n});\n"], "names": ["$", "summernote", "lang", "extend", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "sizeunit", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "resizeNone", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "output", "noSelection"], "sourceRoot": ""}