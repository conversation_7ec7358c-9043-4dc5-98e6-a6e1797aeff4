-- ===================================================================
-- مثال عملي لجداول مستخدم محدد - salessystem_v2
-- اسم المستخدم: testuser
-- معرف المستخدم: 1
-- تاريخ الإنشاء: 2024-12-19
-- ===================================================================

-- استخدام قاعدة البيانات التشغيلية
USE `u193708811_operations`;

-- ===================================================================
-- جداول المستخدم: testuser (ID: 1)
-- ===================================================================

-- جدول العملاء للمستخدم testuser
CREATE TABLE IF NOT EXISTS `testuser_customers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL DEFAULT 1,
    `name` varchar(255) NOT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `tax_number` varchar(50) DEFAULT NULL,
    `address` text DEFAULT NULL,
    `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_name` (`name`),
    KEY `idx_email` (`email`),
    KEY `idx_phone` (`phone`),
    KEY `idx_customer_type` (`customer_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المنتجات للمستخدم testuser
CREATE TABLE IF NOT EXISTS `testuser_products` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL DEFAULT 1,
    `name` varchar(255) NOT NULL,
    `description` text DEFAULT NULL,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
    `category` varchar(100) DEFAULT NULL,
    `stock_quantity` decimal(10,2) DEFAULT 0.00,
    `unit` varchar(50) DEFAULT 'قطعة',
    `barcode` varchar(100) DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_name` (`name`),
    KEY `idx_category` (`category`),
    KEY `idx_barcode` (`barcode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المبيعات للمستخدم testuser
CREATE TABLE IF NOT EXISTS `testuser_sales` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL DEFAULT 1,
    `invoice_number` varchar(50) NOT NULL,
    `date` date NOT NULL,
    `customer_id` int(11) DEFAULT NULL,
    `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
    `notes` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    UNIQUE KEY `idx_invoice_number` (`invoice_number`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_date` (`date`),
    KEY `idx_payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول المشتريات للمستخدم testuser
CREATE TABLE IF NOT EXISTS `testuser_purchases` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL DEFAULT 1,
    `invoice_number` varchar(50) NOT NULL,
    `date` date NOT NULL,
    `customer_id` int(11) DEFAULT NULL,
    `supplier_name` varchar(255) DEFAULT NULL,
    `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
    `notes` text DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    UNIQUE KEY `idx_invoice_number` (`invoice_number`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_date` (`date`),
    KEY `idx_payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول عناصر المبيعات للمستخدم testuser
CREATE TABLE IF NOT EXISTS `testuser_sale_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL DEFAULT 1,
    `sale_id` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `product_name` varchar(255) NOT NULL DEFAULT '',
    `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
    `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_sale_id` (`sale_id`),
    KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول عناصر المشتريات للمستخدم testuser
CREATE TABLE IF NOT EXISTS `testuser_purchase_items` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL DEFAULT 1,
    `purchase_id` int(11) NOT NULL,
    `product_id` int(11) NOT NULL,
    `product_name` varchar(255) NOT NULL DEFAULT '',
    `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
    `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
    `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_purchase_id` (`purchase_id`),
    KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ===================================================================
-- بيانات تجريبية للمستخدم testuser
-- ===================================================================

-- إدراج عملاء تجريبيين
INSERT INTO `testuser_customers` (`user_id`, `name`, `phone`, `email`, `customer_type`) VALUES
(1, 'عميل تجريبي 1', '0500000001', '<EMAIL>', 'customer'),
(1, 'عميل تجريبي 2', '0500000002', '<EMAIL>', 'customer'),
(1, 'مورد تجريبي 1', '0500000003', '<EMAIL>', 'supplier'),
(1, 'مورد تجريبي 2', '0500000004', '<EMAIL>', 'supplier');

-- إدراج منتجات تجريبية
INSERT INTO `testuser_products` (`user_id`, `name`, `description`, `price`, `tax_rate`, `category`) VALUES
(1, 'منتج تجريبي 1', 'وصف المنتج التجريبي الأول', 100.00, 15.00, 'إلكترونيات'),
(1, 'منتج تجريبي 2', 'وصف المنتج التجريبي الثاني', 250.00, 15.00, 'أجهزة'),
(1, 'منتج تجريبي 3', 'وصف المنتج التجريبي الثالث', 75.50, 15.00, 'اكسسوارات'),
(1, 'منتج تجريبي 4', 'وصف المنتج التجريبي الرابع', 500.00, 15.00, 'أجهزة');

-- إدراج فاتورة مبيعات تجريبية
INSERT INTO `testuser_sales` (`user_id`, `invoice_number`, `date`, `customer_id`, `subtotal`, `tax_amount`, `total_amount`, `payment_status`, `notes`) VALUES
(1, 'SALE20241219001', '2024-12-19', 1, 350.00, 52.50, 402.50, 'paid', 'فاتورة مبيعات تجريبية');

-- إدراج عناصر فاتورة المبيعات التجريبية
INSERT INTO `testuser_sale_items` (`user_id`, `sale_id`, `product_id`, `product_name`, `quantity`, `unit_price`, `price`, `tax_rate`, `tax_amount`, `total_price`) VALUES
(1, 1, 1, 'منتج تجريبي 1', 2.00, 100.00, 100.00, 15.00, 30.00, 230.00),
(1, 1, 2, 'منتج تجريبي 2', 1.00, 250.00, 250.00, 15.00, 37.50, 287.50);

-- إدراج فاتورة مشتريات تجريبية
INSERT INTO `testuser_purchases` (`user_id`, `invoice_number`, `date`, `customer_id`, `supplier_name`, `subtotal`, `tax_amount`, `total_amount`, `payment_status`, `notes`) VALUES
(1, 'PUR20241219001', '2024-12-19', 3, 'مورد تجريبي 1', 575.50, 86.33, 661.83, 'paid', 'فاتورة مشتريات تجريبية');

-- إدراج عناصر فاتورة المشتريات التجريبية
INSERT INTO `testuser_purchase_items` (`user_id`, `purchase_id`, `product_id`, `product_name`, `quantity`, `unit_price`, `price`, `tax_rate`, `tax_amount`, `total_price`) VALUES
(1, 1, 3, 'منتج تجريبي 3', 3.00, 75.50, 75.50, 15.00, 33.98, 260.48),
(1, 1, 4, 'منتج تجريبي 4', 1.00, 500.00, 500.00, 15.00, 75.00, 575.00);

-- ===================================================================
-- استعلامات مفيدة للاختبار
-- ===================================================================

/*
-- عرض جميع العملاء للمستخدم testuser
SELECT * FROM `testuser_customers` WHERE `user_id` = 1;

-- عرض جميع المنتجات للمستخدم testuser
SELECT * FROM `testuser_products` WHERE `user_id` = 1;

-- عرض جميع فواتير المبيعات للمستخدم testuser
SELECT s.*, c.name as customer_name 
FROM `testuser_sales` s 
LEFT JOIN `testuser_customers` c ON s.customer_id = c.id AND c.user_id = s.user_id 
WHERE s.user_id = 1;

-- عرض جميع فواتير المشتريات للمستخدم testuser
SELECT p.*, c.name as supplier_name 
FROM `testuser_purchases` p 
LEFT JOIN `testuser_customers` c ON p.customer_id = c.id AND c.user_id = p.user_id 
WHERE p.user_id = 1;

-- عرض تفاصيل فاتورة مبيعات مع العناصر
SELECT s.invoice_number, s.date, c.name as customer_name, 
       si.product_name, si.quantity, si.unit_price, si.total_price
FROM `testuser_sales` s
LEFT JOIN `testuser_customers` c ON s.customer_id = c.id AND c.user_id = s.user_id
LEFT JOIN `testuser_sale_items` si ON s.id = si.sale_id AND si.user_id = s.user_id
WHERE s.user_id = 1 AND s.id = 1;

-- إحصائيات المبيعات للمستخدم testuser
SELECT 
    COUNT(*) as total_invoices,
    SUM(subtotal) as total_subtotal,
    SUM(tax_amount) as total_tax,
    SUM(total_amount) as total_amount
FROM `testuser_sales` 
WHERE user_id = 1;
*/
