<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>السندات</title>
</head>

<style>
*{
    zoom:0.99;
}
#frmsnd{
 height: 710px   
}
.linli{
    text-align-last: justify;
}


    .bodyfrm {
    font-weight: bold;
}
    input{
        border:1px dotted rgb(156, 156, 156);
        border-right: none;
    border-left: none;
    border-top: none;
    font-size: 25px;
    color: #260c73;
    
    }
    .hdr{
        margin: 5px;
        align-items: center;text-align-last: center;
        margin: 5px;
    font-weight: bold;
    font-variant-caps: all-petite-caps;

    }
    .hddt{
        justify-content: space-between;
    }
    label{
        font-size: 25px;
        width: 600px;
        margin-top: 20px;
        
    }
    .downit {
    display: flex;
    /* margin-left: 157px; */
    justify-content: space-between;
    margin-top: 40px;
}
button{
    color:white;
  float:right;
width: 70px;
margin:5px;
height: 40px;
border:none;
border-radius: 20px;
background-color:#04aa30;
box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%);}
@media screen and (max-width: 39.9375em) {
/* give space between search bar and table */
.dataTables_wrapper .dataTables_length {
    display: none;
}
body{
height:400px;
	zoom:1;
}
} 
@media screen and (max-width:420px) {
    body{
        zoom:.6;
        display: inline-table;
    }
h1 {
    margin:10px;
    font-size: 20px;
    font-weight: bold;
}
label{
    font-size: 16px;
       text-align: right;
}
.dataTables_wrapper {
    position: relative;
    zoom: .2;
}
.dataTables_filter input {
    width:50%;
    margin-left: 13.5em;
    zoom:1;
    font-size: 16px;
    height:250px;
    padding:5px;
}


}
   @media screen and (max-width: 39.9375em) {
#frmsnd{
	height: 850px;
	zoom:0.2365;
}
button{
     zoom:0.8;
    }
}
   @media screen and (max-width:420px) {
#frmsnd{
		height: 850px;
	zoom:0.2365;
}
button{
     zoom:0.8;
    }
}
@media print {
    #frmsnd{ 
        height: 800px;
        zoom:0.597;
    }
    button{
     display:none;
     zoom:0.5;
    }
    }
    
</style>
<body >

    <div id="sanad" style="margin:0;">
    <div id="frmsnd" style="border:solid 2px; padding:70px;direction: rtl;border-radius: 40px;margin-right: 0px;width: 100%px;">
        <form  action="" class="frmsnd" >
            <h1 class="hdr" style="font-size: 63px;margin: -25px;color: #184d9b;">ســـــند قبـــــض</h1>
            <h1 class="hdr" style="text-decoration-line: underline;color: #184d9b;">Receipt Voucher</h1>
            <div class="bodyfrm">
            <div style="display: grid;margin:10px">
            <label style="margin-right: 10px;" for="">ريال .R.S</label>
            <input type="text" style="border:solid 1px ; width:7%;height: 30px;background: rgb(221, 246, 255);"></div>
            <div class="hddt" style="margin:30px 0px 30px 0px"><div><label for="">التأريخ :</label><input style="width:15%" type="text"></div><div><label for="">الموافق:</label><input style="width:15%;" type="text"></div></div>
            <div class="linli"><label for="">إستلمنا من السيد/السادة:</label><input type="text" style="width:70% ;"><label for="">:Receiveed From Mrs</label></div>
            <div class="linli" style="margin-top:20px;"><label for="">مبلغ وقدره:</label><input style="width:86.3%;" type="text"><label for="">:Amount</label></div>
            <div class="linli" style="margin-top:20px;"><label for="">نقدا شيك رقم:</label><input style="width:34%;" type="text"><label for="">:Cash/Cheque No.</label><label for="">على بنك:</label><input style="width:34%"  type="text"><label for="">:Bank</label></div>
            <div class="linli" style="margin-top:20px;"><label for="">وذلك مقابل:</label><input style="width:87.5%;" type="text"><label for="">:Being</label></div>
            <div class="downit"><div><label for="">المدير:</label><input type="text"><label for="">:Manager</label></div><div><label for="">المستلم:</label><input type="text"><label for="">:Receiver</label></div></div>
            <div style="text-align:center; margin-top: 100px;"><img hidden src="on.png"alt="Paris" style="width:44px;"><h2><input type="submit" name="ktm" value="الختم" style="background:none"></h2></div>
        </form>
    </div>
</div>
    <div style="margin:50px">
<button style="background:#7e1717"  type="button" onclick="goBack()" class="btn btn-danger" data-dismiss="modal">رجوع</button>
<button  onclick="window.print()" type="button" value="" name="print">إضافة</button>
<button style="background:#958eff"  type="button" onclick="goBack()" class="btn btn-danger" data-dismiss="modal">تعديل</button>
<button  style="background:#b4b4b4"onclick="window.print()" type="button" value="" name="print">طباعة</button>
</div>
</div>
</body>
<script>
    function goBack() {
        window.history.back();
    }
    </script>
    <script type='application/javascript'>
    function myFunction(){

var prtContent = document.getElementById("sanad");
var WinPrint = window.open('', '', 'letf=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0');
WinPrint.document.write(prtContent.innerHTML);
WinPrint.document.close();WinPrint.focus();
WinPrint.print();
WinPrint.close();
}
</script>
</html>