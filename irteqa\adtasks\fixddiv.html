<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Floating Form</title>
<style>
  #floatingForm {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #f0f0f0;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    z-index: 9999; /* تأكد من عرض النموذج فوق العناصر الأخرى */
  }
</style>
</head>
<body>

<button id="showFormButton">عرض النموذج</button>

<div id="floatingForm">
  <form>
    <h2>هذا النموذج عائم</h2>
    <!-- إضافة حقول النموذج هنا -->
  </form>
</div>

<script>
document.getElementById("showFormButton").addEventListener("click", function() {
  var floatingForm = document.getElementById("floatingForm");
  floatingForm.style.display = "block";
});

// التحقق من حالة العنصر عند تحميل الصفحة
document.addEventListener("DOMContentLoaded", function() {
  var floatingForm = document.getElementById("floatingForm");
  var isFormVisible = localStorage.getItem("isFormVisible");

  // إذا كانت النافذة مرئية، قم بإعادة عرضها
  if (isFormVisible === "true") {
    floatingForm.style.display = "block";
  }
});

// الحفاظ على حالة النموذج في الذاكرة المحلية عند إخفائه أو عرضه
window.addEventListener("beforeunload", function() {
  var floatingForm = document.getElementById("floatingForm");
  if (floatingForm.style.display === "block") {
    localStorage.setItem("isFormVisible", "true");
  } else {
    localStorage.setItem("isFormVisible", "false");
  }
});
</script>

</body>
</html>
