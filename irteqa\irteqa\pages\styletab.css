div.aa{
    padding:5px ;
  box-shadow: 3px 5px 3px 0 rgba(85, 85, 85, 0.2), 0 6px 20px 0 rgba(125, 125, 125, 0.19);
  background-color: rgb(244, 244, 244);
  border: 1px solid #ccc;
  border-radius: 10px 0px 10px 10px;
  width: 97%;
  height: 150px;
  align-items: center;
  float:right;
  margin-right: 20px;
  }
  li a.activ {
    color: white;
    background-color: #04aa30;}
    
  input[type=text] {
    height: 37px;
  text-align: right;
  float:right;
  width: 20%;
  padding: 12px 20px;
  margin: 5px;
  box-sizing: border-box;
  border-radius: 10px;
  border: none;
  display:block;
  box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%);
}
input[type=submit]{
  color:white;
  float:right;
width: 10%;
margin:8px;
height: 40px;
border:none;
border-radius: 20px;
background-color:#04aa30;
box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%); 
}
input[type=submit]:hover {
background-color: #62a874;
}
 h1{
 font-size:  2.5em;
margin: 0px 0px 0px 30px;
 }
 .box{
  display: flex;
    height: 99px;
    height: 100px;
    direction: rtl;
 }

.form-select{
  display:block;
border: 0px;
text-align:center;
font-size: 20px;
height: 40px;
width: 10%;
padding: 5px 20px;
margin: 10px;
border-radius: 10px;
float: right;
box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%);
}
.box select{
border:none;
margin:5px;
}

body {
  display:block;
  width:100%;
    text-align: center;
    font-weight: bold;
}
div hr{
  margin:20px 20px;
}