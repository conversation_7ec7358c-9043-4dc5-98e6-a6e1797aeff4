
    
    <?php 
    session_start(); // بدء الجلسة

    include('lgcon.php');

    if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST['uname'];
    $password = $_POST['pword'];

    if ($conn->connect_error) {
        die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    $query = "SELECT * FROM userst WHERE uname = '$username'";
    $result = $conn->query($query);

    if ($result->num_rows == 1) {
        $row = $result->fetch_assoc();

        if (password_verify($password, $row["pword"])) {
            // تم تسجيل الدخول بنجاح
            $_SESSION["uname"] = $username; // تخزين اسم المستخدم في الجلسة

            require_once 'BrowserDetection.php';

            require_once 'lib/phpUserAgent.php';
require_once 'lib/phpUserAgentStringParser.php';
require_once 'lib/UserInformation.php';


// Create a user agent
$userAgent = new phpUserAgent();

            $conn = new mysqli("localhost", "root", "", "login_history");
            // استخراج معلومات المتصفح ونظام التشغيل
            $browserDetection = new BrowserDetection();
            $user_agent = $_SERVER['HTTP_USER_AGENT'];
           // $browser = $browserDetection->getBrowser($user_agent);
             $operating_system= $_SERVER['HTTP_USER_AGENT'] . "\n\n" ;
            $browser = $userAgent->getBrowserName(). " ".$userAgent->getBrowserVersion();
            //$operating_system = $browserDetection->getOperatingSystem($user_agent);
            
            // استخراج معلومات تسجيل الدخول من النموذج
            $username = $_POST['uname'];
            $password = $_POST['pword'];
            $login_time = date("Y-m-d H:i:s");
            $ip_address = $_SERVER['REMOTE_ADDR'];
            
            // تنفيذ الاستعلام لتسجيل الدخول
            $sql = "INSERT INTO login_records (username, login_time, ip_address, operating_system, browser) VALUES ('$username', '$login_time', '$ip_address', '$operating_system', '$browser')";
            
            if ($conn->query($sql) === TRUE) {
                echo "تم تسجيل الدخول بنجاح.";
            } else {
                echo "خطأ في تسجيل الدخول: " . $conn->error;
            }

            header("Location: home.php");

            exit; // توقف تنفيذ السكريبت بعد التوجيه
        } else {
            // كلمة المرور غير صحيحة
            echo "<h4><label>- كلمة المرور غير صحيحة.";
        }
    } else {
        // اسم المستخدم غير موجود
        echo "<h4><label>- اسم المستخدم غير موجود.";
    }



$conn->close();





  /* 
    $sql = "INSERT INTO userst(uname,pword) VALUES ('$username','$password')";
if (isset($_POST['login']));
{

if(empty($username)){
  echo '';

}
elseif(mysqli_query($conn, $sql)){ 

    }else{
      echo 'Error: ' . mysqli_error($conn);    }
    
}*/

}
   /* if(isset($_POST['login'])){
        $getusername=$_POST['username'];
        $getpassword=$_POST['password'];
        if($username === $getusername && $password === $getpassword){
            echo "تم تسجيل الدخول";

        }else{
            echo "كلمة مرور او اسم المستخدم غير صحيح";
        }
    }*/

    
// إذا وصلت هنا، فإن تسجيل الدخول فشل، يمكنك عرض نموذج تسجيل الدخول مرة أخرى
include "login_form.html";

    ?>
</div>
