/*reset.css*/
/* RESETS */
*,
*::before,
*::after {
    box-sizing: border-box;
}

*:focus {
    outline: 3px dashed #228bec;
}

html {
    font: 62.5% / 1.15 sans-serif;
}

h1,
h2 {
    margin-bottom: 0;
}

ul {
    list-style: none;
    padding: 0;
}

button {
    border: none;
    margin: 0;
    padding: 0;
    width: auto;
    overflow: visible;
    background: transparent;
    color: inherit;
    font: inherit;
    line-height: normal;
    -webkit-font-smoothing: inherit;
    -moz-osx-font-smoothing: inherit;
    -webkit-appearance: none;
}

button::-moz-focus-inner {
    border: 0;
}

button,
input,
optgroup,
select,
textarea {
    font-family: inherit;
    font-size: 100%;
    line-height: 1.15;
    margin: 0;
}

button,
input {
    /* 1 */
    overflow: visible;
}

input[type="text"] {
    border-radius: 0;
}

body {
    width: 100%;
    max-width: 68rem;
    margin: 0 auto;
    font: 1.6rem/1.25 "Helvetica Neue", Helvetica, Arial, sans-serif;
    background-color: #f5f5f5;
    color: #4d4d4d;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
}

@media screen and (min-width: 620px) {
    body {
        font-size: 1.9rem;
        line-height: 1.31579;
    }
}

/*END RESETS*/