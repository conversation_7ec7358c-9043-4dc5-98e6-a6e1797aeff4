

.wrapper {
/*
  width: 500px;
  height: 500px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 5px;
  background-image: linear-gradient(to top, #accbee 0%, #e7f0fd 100%);
  overflow: hidden;
*/
}

.wave {
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0%;
    right: -4%;
    border-radius: 35%;
    background: rgba(255, 255, 255, .75);
    animation: wave 15s infinite linear;
    z-index: 0;
    background: #141E30;  /* fallback for old browsers */
background: -webkit-linear-gradient(to right, #243B55, #141E30);  /* Chrome 10-25, Safari 5.1-6 */
background: linear-gradient(to right, #243B55, #141E30); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */

}

@keyframes wave {
  from { transform: rotate(0deg);}
  from { transform: rotate(360deg);}
}


.settings_content{
        z-index: 999;
    position: absolute;
}


.triggered_menu {
    background: none;
}


.the_trigger_for_menu {
        background: #141E30;
    background: -webkit-linear-gradient(to right, #243B55, #141E30);
    background: linear-gradient(to right, #243B55, #141E30);
    width: 60px;
    height: 60px;
    line-height: 60px;
    cursor: pointer;
}


.dashicon_for_trigger_menu{
        width: 100% !important;
    height: 100% !important;
    line-height: 40px !important;
    font-size: 25px !important;
}

#buildCVbtn , .btn_for_print_out , .style_cv , .save_cv_in_profile , .edit_cv{
        background-color: #7872f6;
        border-radius: 50%;
/*
    height: 60px;
    width: 60px;
    text-align: center;
    position: absolute;
    right: 10px;
    bottom: 1px;
*/

}

#buildCVbtn:hover , .btn_for_print_out:hover, .style_cv:hover, .save_cv_in_profile:hover, .edit_cv:hover{
        background-color:#51168C;

}


.photo_of_user img{
        float: right;
    width: 220px;
}

.input_FullName , .printFullAddress, .printContactEmail , .input_title_cv , .text_textareas{
    background-color: transparent;
    border: none;
    outline: none;
        width: 90%;

}

.input_FullName {
    font-size: 3.5rem;
    font-weight: bold;
    line-height: 1.2;
    background-color: transparent;
    border: none;
    outline: none;
    
}

.printContactEmail {
        color: #00f;

}


.container-fluid textarea {
        resize: none;
    overflow: hidden;
}


.input_title_cv {
    font-size: 2rem;
    margin-bottom: .5rem;
    font-family: inherit;
    font-weight: 500;
    line-height: 1.2;
    color: inherit;
    height: 50px;
}

.resume-content {
        width: 80%;

}

/*
html,body,div,span,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,abbr,address,cite,code,del,dfn,em,img,ins,kbd,q,samp,small,strong,sub,sup,var,b,i,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,figcaption,figure,footer,header,hgroup,menu,nav,section,summary,time,mark,audio,video {
border:0;
font:inherit;
font-size:100%;
margin:0;
padding:0;
vertical-align:baseline;
}
*/

article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section {
display:block;
}

/*html, body {background: #181818; font-family: 'Lato', helvetica, arial, sans-serif; font-size: 16px; color: #222;}*/

.clear {clear: both;}

.instaFade p {
	font-size: 1em;
	line-height: 1.4em;
	margin-bottom: 20px;
	color: #444;
}

#cv {
	width: 90%;
	max-width: 800px;
	background: #f3f3f3;
	margin: 30px auto;
}

.mainDetails {
	padding: 25px 35px;
	border-bottom: 2px solid #cf8a05;
	background: #ededed;
}

#name h1 {
	font-size: 2.5em;
	font-weight: 700;
	font-family: 'Rokkitt', Helvetica, Arial, sans-serif;
	margin-bottom: -6px;
}

#name h2 {
	font-size: 2em;
	margin-left: 2px;
	font-family: 'Rokkitt', Helvetica, Arial, sans-serif;
}

#mainArea {
	padding: 0 40px;
}

#headshot {
	width: 12.5%;
	float: left;
	margin-right: 30px;
}

#headshot img {
	width: 100%;
	height: auto;
	-webkit-border-radius: 50px;
	border-radius: 50px;
}

#name {
	float: left;
}

#contactDetails {
	float: right;
}

#contactDetails ul {
	list-style-type: none;
	font-size: 0.9em;
	margin-top: 2px;
}

#contactDetails ul li {
	margin-bottom: 3px;
	color: #444;
}

#contactDetails ul li a, a[href^=tel] {
	color: #444; 
	text-decoration: none;
	-webkit-transition: all .3s ease-in;
	-moz-transition: all .3s ease-in;
	-o-transition: all .3s ease-in;
	-ms-transition: all .3s ease-in;
	transition: all .3s ease-in;
}

#contactDetails ul li a:hover { 
	color: #cf8a05;
}


.instaFade section {
	border-top: 1px solid #dedede;
	padding: 20px 0 0;
}

.instaFade section:first-child {
	border-top: 0;
}

.instaFade section:last-child {
	padding: 20px 0 10px;
}

.sectionTitle {
	float: left;
	width: 25%;
}

.sectionContent {
	float: right;
	width: 72.5%;
}

.sectionTitle h1 {
	font-family: 'Rokkitt', Helvetica, Arial, sans-serif;
	font-style: italic;
	font-size: 1.5em;
	color: #cf8a05;
}

.sectionContent h2 {
	font-family: 'Rokkitt', Helvetica, Arial, sans-serif;
	font-size: 1.5em;
	margin-bottom: -2px;
}

.subDetails {
	font-size: 0.8em;
	font-style: italic;
	margin-bottom: 3px;
}

.keySkills {
	list-style-type: none;
	-moz-column-count:3;
	-webkit-column-count:3;
	column-count:3;
	margin-bottom: 20px;
	font-size: 1em;
	color: #444;
}

.keySkills ul li {
	margin-bottom: 3px;
}

@media all and (min-width: 602px) and (max-width: 800px) {
	#headshot {
		display: none;
	}
	
	.keySkills {
	-moz-column-count:2;
	-webkit-column-count:2;
	column-count:2;
	}
}

@media all and (max-width: 601px) {
	#cv {
		width: 95%;
		margin: 10px auto;
		min-width: 280px;
	}
	
	#headshot {
		display: none;
	}
	
	#name, #contactDetails {
		float: none;
		width: 100%;
		text-align: center;
	}
	
	.sectionTitle, .sectionContent {
		float: none;
		width: 100%;
	}
	
	.sectionTitle {
		margin-left: -2px;
		font-size: 1.25em;
	}
	
	.keySkills {
		-moz-column-count:2;
		-webkit-column-count:2;
		column-count:2;
	}
}

@media all and (max-width: 480px) {
	.mainDetails {
		padding: 15px 15px;
	}
	
	section {
		padding: 15px 0 0;
	}
	
	#mainArea {
		padding: 0 25px;
	}

	
	.keySkills {
	-moz-column-count:1;
	-webkit-column-count:1;
	column-count:1;
	}
	
	#name h1 {
		line-height: .8em;
		margin-bottom: 4px;
	}
}

/*
@media print {
    #cv {
        width: 100%;
    }
}

@-webkit-keyframes reset {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 0;
	}
}

@-webkit-keyframes fade-in {
	0% {
		opacity: 0;
	}
	40% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}

@-moz-keyframes reset {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 0;
	}
}

@-moz-keyframes fade-in {
	0% {
		opacity: 0;
	}
	40% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}

@keyframes reset {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 0;
	}
}

@keyframes fade-in {
	0% {
		opacity: 0;
	}
	40% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
*/

/*
.instaFade {
    -webkit-animation-name: reset, fade-in;
    -webkit-animation-duration: 1.5s;
    -webkit-animation-timing-function: ease-in;
	
	-moz-animation-name: reset, fade-in;
    -moz-animation-duration: 1.5s;
    -moz-animation-timing-function: ease-in;
	
	animation-name: reset, fade-in;
    animation-duration: 1.5s;
    animation-timing-function: ease-in;
}
*/

/*
.quickFade {
    -webkit-animation-name: reset, fade-in;
    -webkit-animation-duration: 2.5s;
    -webkit-animation-timing-function: ease-in;
	
	-moz-animation-name: reset, fade-in;
    -moz-animation-duration: 2.5s;
    -moz-animation-timing-function: ease-in;
	
	animation-name: reset, fade-in;
    animation-duration: 2.5s;
    animation-timing-function: ease-in;
}
 
.delayOne {
	-webkit-animation-delay: 0, .5s;
	-moz-animation-delay: 0, .5s;
	animation-delay: 0, .5s;
}

.delayTwo {
	-webkit-animation-delay: 0, 1s;
	-moz-animation-delay: 0, 1s;
	animation-delay: 0, 1s;
}

.delayThree {
	-webkit-animation-delay: 0, 1.5s;
	-moz-animation-delay: 0, 1.5s;
	animation-delay: 0, 1.5s;
}

.delayFour {
	-webkit-animation-delay: 0, 2s;
	-moz-animation-delay: 0, 2s;
	animation-delay: 0, 2s;
}

.delayFive {
	-webkit-animation-delay: 0, 2.5s;
	-moz-animation-delay: 0, 2.5s;
	animation-delay: 0, 2.5s;
}
*/



.hide_instaFade { 
display: none;
}

.cv_templates{
    width: min-content;
    position: absolute;
    float: right;
    margin-right: -5px;
    margin-top: 2px;
    right: 0px;

}
.cv_template{
    margin-top: 5px;
    background-color: #fff;
    color: #343a40;
    border-radius: 50%;

}

.cv_template:hover {
    color: #fff;
    background-color: #51168C;
}


.settings_content {
        z-index: 999;
    position: absolute;
    top: 90px;
    right: 160px;
}



/*%%%%%%%%%%%%%%%%1*/

/*
.cv_template_tabcontent {
        width: 80%;
    height: auto;
        float: right;

}
*/


.cv_2_inputs {
        background-color: transparent;
    border: none;
    outline: none;
    width: 90%;
}

.mainDetails #name {
        width: 50%;

}


.mainDetails #contactDetails {
        width: 25%;

}

.cv_2_titles {
        font-family: 'Rokkitt', Helvetica, Arial, sans-serif;
    font-style: italic;
    font-size: 1.5em;
    color: #cf8a05;
    font-weight: inherit;
}




/*start CV 3 */



#CV_three .msg { padding: 10px; background: #222; position: relative; }
#CV_three .msg h1 { color: #fff;  }
#CV_three .msg a { margin-left: 20px; background: #408814; color: white; padding: 4px 8px; text-decoration: none; }
#CV_three .msg a:hover { background: #266400; }

/* //-- yui-grids style overrides -- */
/*body { font-family: Georgia; color: #444; }*/
#CV_three #inner { padding: 10px 80px; margin: 80px auto; background: #f5f5f5; border: solid #666; border-width: 8px 0 2px 0; }
#CV_three .yui-gf { margin-bottom: 2em; padding-bottom: 2em; border-bottom: 1px solid #ccc; }

/* //-- header, body, footer -- */
#CV_three #hd { margin: 2.5em 0 3em 0; padding-bottom: 1.5em; border-bottom: 1px solid #ccc }
#CV_three #hd h2 { text-transform: uppercase; letter-spacing: 2px; }
#CV_three #bd,#CV_three #ft { margin-bottom: 2em; }

/* //-- footer -- */
#CV_three #ft { padding: 1em 0 5em 0; font-size: 92%; border-top: 1px solid #ccc; text-align: center; }
#CV_three #ft p { margin-bottom: 0; text-align: center;   }

/* //-- core typography and style -- */
#CV_three #hd h1 { font-size: 48px; text-transform: uppercase; letter-spacing: 3px; }
#CV_three h2 { font-size: 152% }
#CV_three h3, h4 { font-size: 122%; }
#CV_three h1,#CV_three h2,#CV_three h3,#CV_three h4 { color: #333; }
#CV_three p { font-size: 100%; line-height: 18px; padding-right: 3em; }
#CV_three a { color: #990003 }
#CV_three a:hover { text-decoration: none; }
#CV_three strong { font-weight: bold; }
#CV_three li { line-height: 24px; border-bottom: 1px solid #ccc; }
#CV_three p.enlarge { font-size: 144%; padding-right: 6.5em; line-height: 24px; }
#CV_three p.enlarge span { color: #000 }
#CV_three .contact-info { margin-top: 7px; }
#CV_three .first h2 { font-style: italic; }
#CV_three .last { border-bottom: 0 }


/* //-- section styles -- */

#CV_three a#pdf { display: block; float: left; background: #666; color: white; padding: 6px 50px 6px 12px; margin-bottom: 6px; text-decoration: none;  }
#CV_three a#pdf:hover { background: #222; }

#CV_three .job { position: relative; margin-bottom: 1em; padding-bottom: 1em; border-bottom: 1px solid #ccc; }
#CV_three .job h4 { position: absolute; top: 0.35em; right: 0 }
#CV_three .job p { margin: 0.75em 0 3em 0; }

#CV_three .last { border: none; }
#CV_three .skills-list {  }
#CV_three .skills-list ul { margin: 0; }
#CV_three .skills-list li { margin: 3px 0; padding: 3px 0; }
#CV_three .skills-list li span { font-size: 152%; display: block; margin-bottom: -2px; padding: 0 }
#CV_three .talent { width: 32%; float: left }
#CV_three .talent h2 { margin-bottom: 6px; }

#CV_three #srt-ttab { margin-bottom: 100px; text-align: center;  }
#CV_three #srt-ttab img.last { margin-top: 20px }

/* --// override to force 1/8th width grids -- */
#CV_three .yui-gf .yui-u{width:80.2%;}
#CV_three .yui-gf div.first{width:12.3%;}
#CV_three {    width: 73.076em;
        margin: auto;

}

#hd:after, #bd:after, #ft:after, .yui-g:after, .yui-gb:after, .yui-gc:after, .yui-gd:after, .yui-ge:after, .yui-gf:after {
    content: ".";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
}

.yui-gb .yui-u, .yui-g .yui-gb .yui-u, .yui-gb .yui-g, .yui-gb .yui-gb, .yui-gb .yui-gc, .yui-gb .yui-gd, .yui-gb .yui-ge, .yui-gb .yui-gf, .yui-gc .yui-u, .yui-gc .yui-g, .yui-gd .yui-u {
    width: 32%;
    margin-left: 1.99%;
}


.yui-g .yui-gb div.first, .yui-gb div.first, .yui-gc div.first, .yui-gd div.first {
    margin-left: 0;
}


.yui-u {
        margin: 0px;
    float: right;
}


.yui-u.first {
        margin: 0;
    float: left;
        width: 60%;

}

.cv_templates_on_page_load .cv_template {
        border-radius: 0px;
    margin: 5px;
}

.cv_templates_on_page_load .active {
       color: #fff;
    background-color: #51168C; 
}


#cv3_name {
        font-size: 48px;
    text-transform: uppercase;
    letter-spacing: 3px;
        color: #333;
    letter-spacing: 1px;
}

#cv3_Job{
        text-transform: uppercase;
    letter-spacing: 2px;
        font-style: italic;
    color: #333;

}

.cv_3_sub_titles{
        color: #333;
    font-style: italic;
/*    font-size: 130%;*/
    font-weight: normal;

}

#cv3_talent{
        color: #333;
    font-weight: normal;
    font-size: 100%;

}

#CV_three textarea { height: 80px;}

.capture_all {
    padding: 35px;
}

.text_for_jobsss{
        width: 80%;

}

.cv3_last_inputs {
        width: auto;
    float: left;
        width: 33.3333%;

}

.cv3_last_inputs_span{
        margin: auto;
    display: inline-block;
    width: 100%;
}




.cv_page_explained .angela {
    left: 75px;
}
.cv_page_explained .angela .cv_explain_label{
/*        position: absolute;*/
    font-family: 'Indie Flower', cursive;
/*    font-weight: 600;*/
    padding: 10px;
        transform: rotate(-20deg);
    left: 75px;
}
.cv_page_explained .samantha {
right: 75px;
}

.cv_page_explained .samantha .cv_explain_label {
/*        position: absolute;*/
    font-family: 'Indie Flower', cursive;
/*    font-weight: 600;*/
    padding: 10px;
    transform: rotate(-30deg);
}


.cv_page_explained .angela svg {
        transform: rotateX(0deg) rotateY(0deg) rotateZ(-156deg);
overflow: unset;
}

.cv_page_explained .samantha svg {
    transform: rotateX(0deg) rotateY(180deg) rotateZ(121deg);
    overflow: unset;

}

.cv_page_explained .angela svg g {
    transform: scale(3.5);

}

.cv_page_explained .samantha svg g {
    transform: scale(3.5);
}


.cv_page_explained .samantha , .cv_page_explained .angela {
    position: absolute;
    z-index: 999999;
    color: #333c4e;
    font-size: 35px;
    padding: 10px;
    top: 230px;
/*        overflow: hidden;*/
    text-align: center;

}
.cv_page_explained .samantha {
    top: 50vh;
    right: 0;

}

.footer {
    display: none;
}

.capture_all {
        width: auto;

}

.cv_template_tabcontent {
        padding: 10px;

}

.cv_page_notice{
        text-align: center;
    display: block;
    font-size: larger;
    font-weight: bold;
}

/*

.template_4_img{
    background: url(../imgs/cv11.png) no-repeat top;
        display: block;
    height: 1000px;
    background-size: contain;

}
*/



/*template 4 START */
/**********
/* Grille - 960.gs
**********/
   .template_4_img .container_16 {
        max-width: 960px;
        width: 92%;
        margin: 0 auto;
    }
    .template_4_img .grid_6,
    .template_4_img .grid_8,
    .template_4_img .grid_10,
    .template_4_img .grid_16 {
        display:inline;
        float: left;
        position: relative;
        margin-left: 1%;
        margin-right: 1%;
    }

   .template_4_img  .container_16 .grid_6 {
        width:35.5%;
    }

   .template_4_img  .container_16 .grid_8 {
        width:48.0%;
    }

   .template_4_img  .container_16 .grid_10 {
        width:60.5%;
    }

    .template_4_img .container_16 .grid_16 {
        width:98.0%;
    }

  .template_4_img   .clearfix:after {
        clear: both;
        content: ' ';
        display: block;
        font-size: 0;
        line-height: 0;
        visibility: hidden;
        width: 0;
        height: 0;
    }

    .template_4_img .clearfix {
        display: inline-block;
    }

    * html .template_4_img .clearfix {
        height: 1%;
    }

   .template_4_img  .clearfix {
        display: block;
    }



/**********
/* Header
**********/
	.template_4_img header {
		background:#fff;
		border-bottom:4px solid #33a4c9;
		padding: 19px 0;
		overflow: hidden;
	}
	
	.template_4_img header figure {
		border: 3px solid #eee;
		border-radius: 78px;
		float: right;
		height: 156px;
		width: 156px;
	}
	
	.template_4_img header figure img {
		border-radius: 75px;
		height: 150px;
		position: relative;
		width: 150px;
	}
	
	.template_4_img header hgroup {
		float: left;
		padding: 42px 0;
	}
	
	.template_4_img header hgroup h1 {
		color:#555;
		font-family:'DroidSansBold', Arial, sans-serif;
	}
	
	.template_4_img header hgroup h2 {
		color:#cecece;
		font-style: italic;
		text-shadow: 1px 1px 0 rgba(0,0,0,.3);
	}

/************
/* Contact
************/
	.template_4_img ::-webkit-input-placeholder  { color:#fff; }
	.template_4_img input:-moz-placeholder { color:#fff; }
	.template_4_img .placeholder { color: #fff; }

	.template_4_img .contactform {
		background-color: #33a4c9;
		color: #fff;
		display: none;
	}
	
	.template_4_img .contactform > div {
		padding: 21px 0;
	}
	
	.template_4_img .contactform input[type=text], .contactform input[type=email], .contactform textarea {
		background: #2983a0;
		border: 1px solid #226d85;
		border-radius: 2px;
		color: #fff;
		max-width: 100%;
		opacity: .7;
		padding: 10px 10px 9px 10px;
		margin-top: 0;
		position: relative;
		vertical-align: top;
		width: 100%;
	}
	
	.template_4_img .contactform input[type=text], .contactform input[type=email] {
		margin-bottom: 21px;
	}
	
	.template_4_img .contactform textarea {
		font-family: 'DroidSansRegular', Arial, sans-serif;
		font-size: 14px;
		height: 148px;
	}
	
	.template_4_img .contactform input[type=text]:focus, .contactform input[type=email]:focus, .contactform textarea:focus {
		border: 1px solid #226d85;
		opacity: 1;
		outline: none;
		-webkit-transition: opacity 1s;
	}
	
	.template_4_img .contactform input[type=submit] {
		background: #ff9f39;
		border: 1px solid #ff9f39;
		border-radius: 2px;
		color: #fff;
		cursor: pointer;
		font-weight: bold;
		font-size: 14px;
		margin: 0;
		padding: 10px 10px 9px 10px;
		width: 100%;
	}
	
	.template_4_img .contactform input.error, .contactform textarea.error {
		border: 1px solid red;
	}
	
	.template_4_img .contactform label.error {
		display: none !important;
	}

	/***********************
	/* Message du formulaire
	***********************/
		.template_4_img .messageform { 
			display: none; /* Affiché via jQuery */ 
			border-radius: 2px; 
			font-size: 14px;
			font-weight: bold;
			line-height: 17px;
			padding:10px 10px 9px 10px; 
			margin: 0;
			text-align: center;
			width: 100%; 
		}
		.template_4_img .envoi-valid, .envoi-error { 
			display: inline-block; 
		 }
		.template_4_img .envoi-valid { 
			background-color: #31D869;
			border:1px solid #108D3A; 
			color: #108D3A;
		}
		.template_4_img .envoi-error { 
			background-color: #FF5839;
			border:1px solid #A62913; 
			color: #A62913;
		}
		.template_4_img .envoi-error a {
			color: #A62913;
			text-decoration: underline;
		}
		

/***************
/* Corps - Main
***************/
	.template_4_img section[role=main] {
		padding: 21px 0;
		overflow: hidden;
	}
	
	.template_4_img section[role=main] > div {
		margin-top: 63px;
	}
	
	.template_4_img section[role=main] h3 {
		border-bottom: 1px solid #e1e1e1;
		padding: 0 0 20px 60px;
		margin-top: 0px;
		position: relative;
 	}
	
/*
	.template_4_img section[role=main] h3:after, h3:before {
        content: "\f110";
        display: block;
		height: 50px;
		left: 0;
		position: absolute;
		top: -15px;
		width: 50px;
	}
	
	.template_4_img section[role=main] h3:before {
		background-color: #33a4c9;
		border-radius: 25px;
                content: "\f110";

	}
*/
	
		/***************
		/* Pictos titres
		***************/
		
/*
		.template_4_img section[role=main] h3:after {
			background:url('../img/pictos-titre.png') no-repeat 11px 10px;
		}
		
		.template_4_img section[role=main] .competences h3:after {
			background-position: 11px -34px;
		}
	
		.template_4_img section[role=main] .experiences h3:after {
			background-position: 11px -86px;
		}
		
		.template_4_img section[role=main] .formations h3:after {
			background-position: 10px -139px;
		}
		
		.template_4_img section[role=main] .loisirs h3:after {
			background-position: 10px -250px;
		}
		
		.template_4_img section[role=main] .contact h3:after {
			background-position: 11px -194px;
		}
*/
	
	
	.template_4_img .experiences ul, .formations ul {
		margin-left: 60px;
	}
	
	.template_4_img section[role=main] h4 {
		margin-bottom: 0px;
	}
	
	.template_4_img section[role=main] h4 strong {
		color: #147393;
		font-weight: normal;
	}
	
	.template_4_img .experiences li, .formations li {
		margin-bottom: 63px;
	}
	
	.template_4_img .experiences li:last-child, .formations li:last-child {
		margin-bottom: 21px;
	}
	
	.template_4_img .experiences li p, .formations li p {
		margin-left: 30px;
	}
	
	/**********************************************
	/* Affichage des compétences sous forme de tags
	**********************************************/
	.template_4_img .competences .tags li {
		background-color:#33a4c9;
		border:1px solid #1b91b7;
		border-radius: 15px;
		color:#fff;
		display: inline-block;
		line-height: 13px;
		margin: 0 7px 17px 0;
		padding: 5px 10px;
	}
	
	/************************************************
	/* Affichage des compétences sous forme de barres
	*************************************************/
	.template_4_img .competences .barres li {
		margin-bottom: 21px;
		position: relative;
	}
	
	.template_4_img .competences .barres li:after {
		background: #e1e1e1;
		bottom: -4px;
		content: ' ';
		display: block;
		height: 5px;
		position: absolute;
		width:100%;
	}
	
	.template_4_img .competences .barres li span {
		background: #33a4c9;
		bottom: -4px;
		content: ' ';
		display: block;
		height: 5px;
		left: 0;
		position: absolute;
		width: inherit;
		z-index: 1;
	}
	
	
	/***********
	/* Pictos
	***********/
	.template_4_img .lieu, .template_4_img .dates, .template_4_img .phone, .template_4_img .mail, .template_4_img .site, .template_4_img .form, .template_4_img .twitter, .template_4_img .facebook, .template_4_img .dribbble, .template_4_img .skype {
		background: url('../img/pictos-gris.png') no-repeat;
		padding-left: 25px;
		margin: 0 10px 1px 10px;
	}
	
	.template_4_img .lieu {
		background-position: 0 -2px;
		color: #999;
	}
	
	.template_4_img .dates {
		background-position: 0 -22px;
		color: #999;
	}
	
	.template_4_img .phone {
		background-position: 0 -40px;
	}
	
	.template_4_img .mail {
		background-position: 0 -58px;
	}
	
	.template_4_img .site {
		background-position: 0 -80px;
	}
	
	.template_4_img .form {
		background-position: 0 -100px;
	}
	
	.template_4_img .twitter {
		background-position: 0 -122px;
	}
	
	.template_4_img .facebook {
		background-position: 0 -140px;
	}
	
	.template_4_img .dribbble {
		background-position: 0 -160px;
	}
	
	.template_4_img .skype {
		background-position: 0 -180px;
	}



/*start template 555555555555555555555555555555555555555555555555555555555555555555*/

/*@charset "UTF-8";*/
/* RESET */
/* ----------------------------------------- */
    .template_55555 div,.template_55555  span, .template_55555 applet, .template_55555 object,.template_55555  iframe,
    .template_55555 h1, .template_55555 h2,.template_55555  h3, .template_55555 h4, .template_55555 h5, .template_55555 h6, .template_55555 p, .template_55555 blockquote, .template_55555 pre, .template_55555 a, .template_55555 abbr, .template_55555 acronym, .template_55555 address, .template_55555 big, .template_55555 code,
    .template_55555 del, .template_55555 dfn, .template_55555 em, .template_55555 font, .template_55555 img, .template_55555 ins, .template_55555 kbd, .template_55555 q, .template_55555 s, .template_55555 samp,
    .template_55555 small, .template_55555 strike, .template_55555 strong, .template_55555 tt, .template_55555 var,
    .template_55555 dl, .template_55555 dt, .template_55555 dd, .template_55555 ol, .template_55555 ul, .template_55555 li,
    .template_55555 fieldset, .template_55555 form, .template_55555 label, .template_55555 legend,
    .template_55555 table, .template_55555 caption, .template_55555 tbody, .template_55555 tfoot, .template_55555 thead, .template_55555 tr, .template_55555 th, .template_55555 td {
        margin: 0;
        padding: 0;
        border: 0;
        outline: 0;
        font-weight: inherit;
        font-style: inherit;
        font-size: 100%;
        font-family: inherit;
        vertical-align: baseline;
    }
    .template_55555 :focus {
        outline: 0;
    }
    .template_55555  {
        line-height: 1;
    }
/*     tables still need 'cellspacing="0"' in the markup */
    .template_55555 table {
        border-collapse: separate;
        border-spacing: 0;
    }
   .template_55555  caption,.template_55555  th,.template_55555  td {
        text-align: left;
        font-weight: normal;
    }
    .template_55555 blockquote:before,.template_55555  blockquote:after,
    .template_55555 q:before,.template_55555  q:after {
        content: "";
    }
.template_55555 blockquote, q {
	quotes: "" "";
}

.template_55555 .clear {
	clear:both;
}


/* LAYOUT */
/* ----------------------------------------- */
/*

.template_55555 {
	font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
	background-color: #e1e0db;
	background-image: url(../images/bg.jpg);
	background-repeat: repeat;
	font-size:13px;
	line-height: 17px;
	color:#000;
}
*/

.template_55555 .contact {
	background-image: none;
	background-color:#e9e9e9;
}

.template_55555 a, .template_55555 a:visited, .template_55555 a:active {
	color:#7b90b5;
	text-decoration: none;
}

.template_55555 a:hover {
	color:#7b90b5;
	text-decoration: underline;
}

/* DOSSIER BACKGROUND */
/* ----------------------------------------- */

.template_55555 #wrapper {
	width:969px;
	margin:0 auto;
	margin-top:60px;
	margin-bottom:100px;
}

/*
    .template_55555 .wrapper-top {
        width:969px;
        height:19px;
        margin:0 auto;
        background-image:url(../imgs/btop.jpg);
        background-repeat:no-repeat;
    }

    .template_55555 .wrapper-mid {
        width:969px;
        margin:0 auto;
        background-image:url(../imgs/bmid.jpg);
        background-repeat: repeat-y;
        padding-bottom:40px;
    }

    .template_55555 .wrapper-bottom {
        width:969px;
        height:22px;
        padding:0;
        margin:0 auto;
        background-image:url(../imgs/bbottom.jpg);
        background-repeat:no-repeat;
    }
*/

/* PAPER BACKGROUND */
/* ----------------------------------------- */

    .template_55555 #paper {
        width:828px;
        margin:0 auto;
    }

/*
   .template_55555  .paper-top {
        width:828px;
        margin:0 auto;
        background-image:url(../imgs/ptop.jpg);
        background-repeat:no-repeat;
        height:126px;
    }
*/

   .template_55555  #paper-mid {
        width:828px;
        margin:0 auto;
/*        background-image:url(../imgs/pmid.jpg);*/
        background-repeat:repeat-y;
        display:block;
        padding:0;
        overflow: hidden;
        padding-bottom:75px;
        padding-top:7px;
           background-color: #fff;

    }

/*
   .template_55555  .paper-bottom {
        width:828px;
        height:18px;
        margin:0 auto;
        background-image:url(../imgs/pbottom.jpg);
        background-repeat:no-repeat;
    }
*/

    /* ENTRIES */
    /* ----------------------------------------- */

   .template_55555  .entry {
        width:720px;
        display: block;
        padding-top:55px;
        clear: both;
        margin-left:4px;
    }

    .template_55555 h1  {
        width:250px;
        font-size:42px;
        color:#1b4491;
        margin-bottom:3px;
        clear:both;
    }

    .template_55555 h1 span {
        font-size:21px;
        display:block;
        color:#1b4491;
        margin-top:-5px;
    }

   .template_55555  h2 {
        width:180px;
        height: 23px;
        text-align: right;
        float:left;
        padding:0;
        margin:0;
        clear:both;
        font-size:22px;
        color:#1b4491;
        margin-bottom:-12px;
    }

   .template_55555  p {
        width:500px;
        margin-left:40px;
        float:right;	
    }

   .template_55555  p img {
        float:right;
        margin-left:20px;
        padding-left:10px;
    }

    .template_55555 div.content {
        clear: both;
        padding:0;
        margin:0;
        overflow: hidden;
        display:block;
        padding-top:32px;
    }

   .template_55555  h3 {
        width:180px;
        text-align: right;
        float:left;
        padding:0;
        clear:both;
        font-size: 13px;
        color:#7b90b5;
    }

   .template_55555  h4 {
        width:180px;
        text-align: right;
        float:left;
        padding:0;
        clear:both;
        font-size: 11px;
        color:#7b90b5;
    }

   .template_55555  h5 {
        width:180px;
        text-align: right;
        float:left;
        padding:0;
        clear:both;
        font-size: 10px;
        color:#7b90b5;
    }

    .template_55555 h6 {
        width:180px;
        text-align: right;
        float:left;
        padding:0;
        clear:both;
        font-size: 9px;
        color:#7b90b5;
    }

    .template_55555 em {
        font-family: Georgia, "Times New Roman", serif;
        font-style: italic;
        color:#777777;
        font-size:12px;
        display: block;
        padding-top:3px;
    }


    .template_55555 ol {
        float:right;
        width:500px;
        margin-left:40px;
        list-style: decimal;

    }

    .template_55555 ol li {
        width:500px;
        list-style-type: decimal;
        list-style-position: inside;
    }

    .template_55555 dl {
        float:right;
        width:500px;
        margin-left:40px;

    }

   .template_55555  dt {
        float:left;
        display: block;
        width:500px;
            color:#7b90b5;
    }

    .template_55555 dd {
        float:left;
        display: block;
        width:480px;
        text-indent:20px;
    }

    .template_55555 ul.unordered {
        float:right;
        width:500px;
        margin-left:40px;

    }

   .template_55555  ul.unordered li {
        margin:0;
        padding:0;
        float:left;
        display: block;
        width:500px;
        background-image:url(../imgs/bullet.gif);
        background-repeat: no-repeat;
        padding-left:10px;
        background-position: 0 .5em;
        margin-top:0;
    }

    .template_55555 ul.info {
        float:right;
        width:500px;
        margin-left:40px;
        list-style-type: none; 
    }

    .template_55555 ul.info li {
        margin:0;
        padding:0;
        float:left;
        display: block;
        width:500px;
        background-image:url(../imgs/bullet.gif);
        background-repeat: no-repeat;
        padding-left:10px;
        background-position: 0 .5em;
        margin-top:12px;
    }

    .template_55555 ul.skills {
        margin:0;
        padding:0;
        float:right;
        width:500px;
        margin-left:40px;
        margin-top:-6px;
        list-style-type: none;

    }

    .template_55555 ul.skills li {
        margin:0;
        padding:0;
        float:left;
        width:156px;
        background-image:url(../imgs/bullet.gif);
        background-repeat: no-repeat;
        padding-left:10px;
        background-position: 0 .5em;
        margin-top:6px;
    }

    .template_55555 ul.works {
        margin:0;
        padding:0;
        float:right;
        width:500px;
        margin-left:40px;
        list-style-type: none;
    }

   .template_55555  ul.works li {
        margin:0;
        padding:0;
        float:left;
    }

    .template_55555 ul.works li img {
        padding:4px;
        background-color:#fff;
        width:95px;
        height:95px;
        border:1px solid #bdc7da;
        margin-right:20px;
        margin-bottom:16px;
    }

   .template_55555  ul.works li img:hover {
        border:1px solid #1b4491;
        cursor: pointer;
    }

    .template_55555 img.portrait {
        background-image:url(../imgs/frame.jpg);
        background-repeat: no-repeat;
        width:109px;
        height:109px;
        padding-top:20px;
        padding-left:11px;
        padding-right:11px;
        padding-bottom:36px;
        margin-left:50px;
        float:left;

    }


    /* SELF INFORMATION */
    /* ----------------------------------------- */

   .template_55555  .self {
        width:250px;
        float:left;
        padding-top:11px;
        margin-left:38px;
        margin-bottom:15px;
    }

   .template_55555  .self ul {
        padding-top: 10px;
    }

    .template_55555 .self ul li.ad {
        background-image:url(../imgs/icn-ad.gif);
    }

    .template_55555 .self ul li.mail {
        background-image:url(../imgs/icn-mail.gif);
    }

    .template_55555 .self ul li.tel {
        background-image:url(../imgs/icn-tel.gif);
    }

    .template_55555 .self ul li.web {
        background-image:url(../imgs/icn-web.gif);
    }

    .template_55555 .self ul li {
        background-repeat: no-repeat;
        padding-left:26px;
        background-position: 0 .1em;
        height:25px;
        display:block;
        margin-top:-2px;
    }

    /* SOCIAL ICONS */
    /* ----------------------------------------- */

    .template_55555 .social {
        width:135px;
        float:right;
        padding-top:10px;
    }

    .template_55555 .social ul {
        list-style: none;
    }

   .template_55555  .social ul li {
        float:left;
        width:21px;
        height:24px;
        margin:0;
        padding:0;
        margin-left:6px;
    }

    /* TIPSY */
    /* ----------------------------------------- */

    .template_55555 .tipsy { padding: 5px; font-size: 10px; background-repeat: no-repeat;  background-image: url(../imgs/tipsy.gif); }
    .template_55555 .tipsy-inner { padding: 5px 8px 4px 8px; background-color: #080626; color: #fff; max-width: 200px; text-align: center; }
   .template_55555  .tipsy-inner { -moz-border-radius:3px; -webkit-border-radius:3px; }
    .template_55555 .tipsy-north { background-position: top center; }
    .template_55555 .tipsy-south { background-position: bottom center; margin-bottom:20px; }
    .template_55555 .tipsy-east { background-position: right center; }
    .template_55555 .tipsy-west { background-position: left center; }


    /* COLORBOX */
    /* ----------------------------------------- */

   .template_55555  #colorbox, .template_55555 #cboxOverlay,.template_55555  #cboxWrapper{position:absolute; top:0; left:0; z-index:9999; overflow:hidden;}
   .template_55555  #cboxOverlay{position:fixed; width:100%; height:100%;}
   .template_55555  #cboxMiddleLeft, #cboxBottomLeft{clear:left;}
   .template_55555  #cboxContent{position:relative; overflow:hidden;}
    .template_55555 #cboxLoadedContent{overflow:auto;}
   .template_55555  #cboxLoadedContent iframe{display:block; width:100%; height:100%; border:0;}
    .template_55555 #cboxTitle{margin:0; display:none;}
    .template_55555 #cboxLoadingOverlay, #cboxLoadingGraphic{position:absolute; top:0; left:0; width:100%;}
    .template_55555 #cboxPrevious, .template_55555 #cboxNext,.template_55555  #cboxClose, .template_55555 #cboxSlideshow{cursor:pointer;}

    .template_55555 #cboxOverlay{background:#666;}

   .template_55555  #colorBox{}
   .template_55555  #cboxTopLeft{width:25px; height:25px; background:url(../imgs/border1.png) 0 0 no-repeat;}
   .template_55555  #cboxTopCenter{height:25px; background:url(../imgs/border1.png) 0 -50px repeat-x;}
    .template_55555 #cboxTopRight{width:25px; height:25px; background:url(../imgs/border1.png) -25px 0 no-repeat;}
    .template_55555 #cboxBottomLeft{width:25px; height:25px; background:url(../imgs/border1.png) 0 -25px no-repeat;}
    .template_55555 #cboxBottomCenter{height:25px; background:url(../imgs/border1.png) 0 -75px repeat-x;}
   .template_55555  #cboxBottomRight{width:25px; height:25px; background:url(../imgs/border1.png) -25px -25px no-repeat;}
   .template_55555  #cboxMiddleLeft{width:25px; background:url(../imgs/border2.png) 0 0 repeat-y;}
   .template_55555  #cboxMiddleRight{width:25px; background:url(../imgs/border2.png) -25px 0 repeat-y;}
   .template_55555  #cboxContent{background:#fff;}
   .template_55555  #cboxLoadedContent{margin-bottom:20px;}
   .template_55555  #cboxTitle{position:absolute; bottom:0px; left:0; text-align:center; width:100%; color:#1b4491;}
   .template_55555  #cboxCurrent{position:absolute; bottom:0px; left:100px; color:#000;}
    .template_55555 #cboxSlideshow{position:absolute; bottom:0px; right:42px; color:#444;}
   .template_55555  #cboxPrevious{position:absolute; bottom:0px; left:0; color:#444;}
   .template_55555  #cboxNext{position:absolute; bottom:0px; left:63px; color:#444;}
    .template_55555 #cboxLoadingOverlay{background:url(../images/loading.gif) 5px 5px no-repeat #fff;}
   .template_55555  #cboxClose{position:absolute; bottom:0; right:0; display:block; color:#444;}



    /* ELEMENTS */
    /* ----------------------------------------- */

    .template_55555 table {
        width: 500px;
        margin-left:40px;
        float:right;
        border-collapse: collapse;
        border-bottom:1px dashed #999;
        border-top:1px dashed #999;
    }

    .template_55555 table td {
        padding: 10px;
        padding-top:0;
        border-right:1px dashed #999;
    }

    .template_55555 table td:hover {
        background-color: #f2f2f2;
    }

    .template_55555 table th {
        padding: 10px;
        padding-top:0;
        color:#7b90b5;
        text-align: left;
        border-right:1px dashed #999;
    }

    .template_55555 table td.center {
        text-align: center;
    }

   .template_55555  table td.last {
        background: none;
    }

    .template_55555 table th.center {
        text-align: center;
    }

   .template_55555  table th.last {
        background: none;
    }

    .template_55555 table tr {
        border-left:1px dashed #999;
        border-top:1px dashed #999;
    }

   .template_55555  table tr.caption {
        border-left:1px dashed #999;
    }

    .template_55555 blockquote {
        border-left:3px solid #7b90b5;
        padding-left:8px;
        width:489px;
        margin-left:40px;
        float:right;
    }

    .template_55555 abbr, .template_55555 acronym {
        border-bottom:1px dashed #7b90b5;
    }

    .template_55555 strong {
        font-weight: bold;
    }

   .template_55555  small {
        font-size:70%;
    }

   .template_55555  big {
        font-size:1.2em;
    }


    /* CONTACT */

    .template_55555 fieldset, .template_55555 legend {
        float:right;
        width:500px;
        padding:0;
        margin:0;
        margin-left:40px;
    }

    .template_55555 h1.warn {
        color:#1b4491;
        font-size:18px;
        margin-top:30px;
        padding:50px;
        text-align: center;
    }

    .template_55555 #contactform {
        width:320px;
        height:370px;
        margin:0 auto;
        padding:50px;
        overflow: hidden;
    }

    .template_55555 label {
        float:left;
        width:40px;
        height:28px;
        margin-bottom:10px;
        clear:both;
        color:#1b4491;
    }

    .template_55555 input {
        width:228px;
        float:right;
        height:18px;
        margin-bottom:10px;
        border-top:1px solid #c8c8c8;
        border-left:1px solid #c8c8c8;
        border-right:1px solid #dadada;
        border-bottom:1px solid #f2f2f2;
        background-color: #e4e4e4;
        font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
        font-size: 13px;
        padding:5px;
        outline: none;
    }

    .template_55555 input:focus {
        background-color: #dddddd;
        border-top:1px solid #c8c8c8;
        border-left:1px solid #c8c8c8;
        border-right:1px solid #dadada;
        border-bottom:1px solid #f2f2f2;
    }

    .template_55555 textarea {
        border-top:1px solid #c8c8c8;
        border-left:1px solid #c8c8c8;
        border-right:1px solid #dadada;
        border-bottom:1px solid #f2f2f2;
        background-color: #e4e4e4;
        width:228px;
        float:right;
        margin-bottom:10px;
        font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
        font-size: 13px;
        padding:5px;
        outline: none;
    }

    .template_55555 textarea:focus {
        background-color: #dddddd;
        border-top:1px solid #c8c8c8;
        border-left:1px solid #c8c8c8;
        border-right:1px solid #dadada;
        border-bottom:1px solid #f2f2f2;
    }

    .template_55555 .submit-button {
        background: transparent url(../imgs/submit.jpg) no-repeat 0 0;
        display: block;
        clear: both;
        width:90px;
        height: 33px; 
        border:none;
        float:left;
        margin-left: 80px;
        text-indent: -9999px;
        outline: 0;
        cursor: pointer;
        padding:0;
    }

    .template_55555 .submit-button:hover {
        background: transparent url(../imgs/submit.jpg) no-repeat 0 -33px;
        outline: 0;
        border: none;
    }

    .template_55555 label.error {
        clear: both;
        padding:0;
        margin-top:-5px;
        color:#7b90b5;
        font-size: 1.0em;
        font-style: italic;
        font-weight: normal;
        margin-left:80px;
        width:238px;
    }

.template_55555 {
      margin: auto;
/*    display: flex;  */
}

.template_55555 .wrapper-mid {
      margin: auto;

}

.circle_8 {
        position: absolute;
    background-color: #33a4c9;
    color: #fff;
    left: 0px;
    text-align: center;
    padding: 10px;
    line-height: 20px;
    border-radius: 50%;
}


/* $$$$$$$$$$$#############&&&&&&&&&&&&&&&&&&%%%%%%%%% end CV $$$$$$$$$$$#############&&&&&&&&&&&&&&&&&&%%%%%%%%% */


.html2canvas_thing {
    position: fixed;
/*    background-color: #333c4e;*/
    bottom: 20px;
    right: 20px;
}

.html2canvas_thing .the_image {
    display: none;
}

.css-1oz4tqn {
    width: 50px;
    text-align: center;
}

/*
.triggered_menu{
    display: none;
}
*/

.dashicon_for_trigger_menu{
    color: #fff;
    font-size: 35px;
    text-align: center;
    width: 100%;
    line-height: 50px;
}

.hide_triggered_menu{
    display: none;
}

.the_trigger_for_menu{
    border-radius: 50%;
    padding: 10px;
    z-index: 9999;
    position: relative;
    -webkit-transition: all 0.2s ease-in-out ;
    -moz-transition: all 0.2s ease-in-out ;
    -o-transition: all 0.2s ease-in-out ;

}


.triggered_menu{
       position: absolute;
    top: -50vh;
    right: -10vh;
    background-color: rgba(0,0,0,.6);
    width: 50vw;
    height: 50vw;
    border-radius: 50%;
    padding: 20vh;
    color: #fff; 
        -webkit-transition: all 0.2s ease-in-out ;
    -moz-transition: all 0.2s ease-in-out ;
    -o-transition: all 0.2s ease-in-out ;
}
