# دليل إصلاح مشاكل قاعدة البيانات

## نظرة عامة

تم إنشاء مجموعة شاملة من أدوات إصلاح قاعدة البيانات لحل جميع المشاكل المتعلقة بالاتصال والجداول في نظام المبيعات.

## الأدوات المتاحة

### 1. الإصلاح السريع (quick_database_fix.php)
**الاستخدام:** للإصلاح السريع والأساسي
- إنشاء قواعد البيانات المفقودة
- إنشاء الجداول الأساسية
- إضافة بيانات تجريبية
- فحص الاتصالات الأساسية

**متى تستخدمه:** عند بدء استخدام النظام لأول مرة أو عند حدوث مشاكل بسيطة

### 2. أداة الإصلاح الشاملة (database_repair_tool.php)
**الاستخدام:** للإصلاح المتقدم والشامل
- فحص شامل لجميع الجداول
- إصلاح البيانات التالفة
- إضافة الأعمدة المفقودة
- إصلاح الفهارس
- إضافة الإعدادات الافتراضية

**متى تستخدمه:** عند حدوث مشاكل معقدة أو بعد تحديث النظام

### 3. فحص صحة قاعدة البيانات (database_health_check.php)
**الاستخدام:** للتشخيص والمراقبة
- تقييم صحة قاعدة البيانات
- اكتشاف المشاكل المحتملة
- تقديم توصيات للتحسين
- مراقبة الأداء

**متى تستخدمه:** للفحص الدوري أو قبل تطبيق الإصلاحات

### 4. فحص الجداول (check_tables.php)
**الاستخدام:** للفحص الأساسي للجداول
- التحقق من وجود الجداول
- عرض بنية الجداول
- إحصائيات البيانات

**متى تستخدمه:** للفحص السريع لحالة الجداول

## خطوات الإصلاح الموصى بها

### للمشاكل البسيطة:
1. قم بتشغيل `quick_database_fix.php`
2. تحقق من النتائج
3. اختبر النظام

### للمشاكل المعقدة:
1. قم بتشغيل `database_health_check.php` للتشخيص
2. قم بتشغيل `database_repair_tool.php` للإصلاح الشامل
3. قم بتشغيل `database_health_check.php` مرة أخرى للتأكد
4. اختبر النظام

### للفحص الدوري:
1. قم بتشغيل `database_health_check.php` شهرياً
2. راجع التوصيات وطبقها حسب الحاجة

## المشاكل الشائعة وحلولها

### 1. "فشل الاتصال بقاعدة البيانات"
**الحل:**
- تأكد من تشغيل خادم MySQL
- تحقق من إعدادات الاتصال في `config/db_config.php`
- قم بتشغيل `quick_database_fix.php`

### 2. "جداول مفقودة"
**الحل:**
- قم بتشغيل `database_repair_tool.php`
- أو استخدم `quick_database_fix.php` للإصلاح السريع

### 3. "أعمدة مفقودة في الجداول"
**الحل:**
- قم بتشغيل `database_repair_tool.php`
- سيتم إضافة الأعمدة المفقودة تلقائياً

### 4. "بيانات تالفة أو يتيمة"
**الحل:**
- قم بتشغيل `database_repair_tool.php`
- سيتم تنظيف البيانات التالفة

### 5. "بطء في الأداء"
**الحل:**
- قم بتشغيل `database_health_check.php`
- راجع توصيات الأداء
- قم بتشغيل `database_repair_tool.php` لإضافة الفهارس

## التحسينات المطبقة

### 1. تحسين دوال الاتصال
- إضافة آلية إعادة المحاولة
- تحسين معالجة الأخطاء
- إضافة timeout للاستعلامات
- تنظيف النتائج المتبقية

### 2. معالجة الأخطاء المحسنة
- تسجيل مفصل للأخطاء
- رسائل خطأ واضحة للمستخدم
- إعادة توجيه آمنة عند الأخطاء

### 3. فحص سلامة البيانات
- اكتشاف البيانات المكررة
- إصلاح البيانات اليتيمة
- التحقق من سلامة المراجع

### 4. تحسين الأداء
- إضافة فهارس للأعمدة المهمة
- تحسين الاستعلامات
- مراقبة حجم الجداول

## الملفات المحسنة

### ملفات الإعداد:
- `config/init.php` - دوال اتصال محسنة
- `config/db_config.php` - إعدادات قاعدة البيانات
- `includes/functions.php` - دوال مساعدة محسنة

### ملفات الإصلاح:
- `database_repair_tool.php` - أداة الإصلاح الشاملة
- `quick_database_fix.php` - الإصلاح السريع
- `database_health_check.php` - فحص الصحة
- `check_tables.php` - فحص الجداول
- `fix_database_issues.php` - إصلاح ملفات النظام

### ملفات النظام المحسنة:
- `index.php` - صفحة رئيسية محسنة
- جميع ملفات النظام تم تحسين معالجة قاعدة البيانات فيها

## نصائح للصيانة

### 1. النسخ الاحتياطي
- قم بعمل نسخة احتياطية قبل تطبيق أي إصلاحات
- استخدم أدوات النسخ الاحتياطي المدمجة في النظام

### 2. المراقبة الدورية
- قم بتشغيل فحص الصحة شهرياً
- راقب سجلات الأخطاء بانتظام

### 3. التحديثات
- حافظ على تحديث النظام
- طبق الإصلاحات الأمنية فوراً

### 4. الأداء
- راقب حجم قاعدة البيانات
- أرشف البيانات القديمة عند الحاجة

## الدعم الفني

إذا واجهت مشاكل لا يمكن حلها بالأدوات المتاحة:

1. تحقق من سجلات الأخطاء في `/logs/`
2. قم بتشغيل جميع أدوات التشخيص
3. احفظ تقارير الأخطاء
4. تواصل مع فريق الدعم الفني

## إصدار الأدوات
- الإصدار: 2.0
- تاريخ الإنشاء: 2024
- آخر تحديث: اليوم

---

**ملاحظة مهمة:** قم دائماً بعمل نسخة احتياطية من قاعدة البيانات قبل تطبيق أي إصلاحات.
