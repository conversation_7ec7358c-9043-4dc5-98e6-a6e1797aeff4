<?php
/**
 * ملف لإضافة بيانات تجريبية للنظام
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_sample_data'])) {
    try {
        // إضافة عملاء تجريبيين
        $customers_data = [
            ['name' => 'شركة الأمل للتجارة', 'phone' => '0501234567', 'email' => '<EMAIL>', 'address' => 'الرياض، المملكة العربية السعودية'],
            ['name' => 'مؤسسة النور للمقاولات', 'phone' => '0507654321', 'email' => '<EMAIL>', 'address' => 'جدة، المملكة العربية السعودية'],
            ['name' => 'شركة الفجر للتوريدات', 'phone' => '0509876543', 'email' => '<EMAIL>', 'address' => 'الدمام، المملكة العربية السعودية']
        ];

        foreach ($customers_data as $customer) {
            $check_customer = $db->prepare("SELECT id FROM customers WHERE name = ?");
            $check_customer->bind_param("s", $customer['name']);
            $check_customer->execute();
            $result = $check_customer->get_result();
            
            if ($result->num_rows == 0) {
                $stmt = $db->prepare("INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)");
                $stmt->bind_param("ssss", $customer['name'], $customer['phone'], $customer['email'], $customer['address']);
                $stmt->execute();
            }
        }

        // إضافة منتجات تجريبية
        $products_data = [
            ['name' => 'جهاز كمبيوتر محمول', 'price' => 2500.00, 'tax_rate' => 15.00],
            ['name' => 'طابعة ليزر', 'price' => 800.00, 'tax_rate' => 15.00],
            ['name' => 'شاشة كمبيوتر 24 بوصة', 'price' => 600.00, 'tax_rate' => 15.00],
            ['name' => 'لوحة مفاتيح لاسلكية', 'price' => 150.00, 'tax_rate' => 15.00],
            ['name' => 'فأرة لاسلكية', 'price' => 80.00, 'tax_rate' => 15.00],
            ['name' => 'كاميرا ويب HD', 'price' => 200.00, 'tax_rate' => 15.00]
        ];

        foreach ($products_data as $product) {
            $check_product = $db->prepare("SELECT id FROM products WHERE name = ?");
            $check_product->bind_param("s", $product['name']);
            $check_product->execute();
            $result = $check_product->get_result();
            
            if ($result->num_rows == 0) {
                $stmt = $db->prepare("INSERT INTO products (name, price, tax_rate) VALUES (?, ?, ?)");
                $stmt->bind_param("sdd", $product['name'], $product['price'], $product['tax_rate']);
                $stmt->execute();
            }
        }

        // إضافة مشتريات تجريبية
        $purchases_data = [
            [
                'customer_id' => 1,
                'date' => '2024-01-15',
                'items' => [
                    ['product_id' => 1, 'quantity' => 2, 'unit_price' => 2500.00, 'tax_rate' => 15.00],
                    ['product_id' => 2, 'quantity' => 1, 'unit_price' => 800.00, 'tax_rate' => 15.00]
                ]
            ],
            [
                'customer_id' => 2,
                'date' => '2024-01-20',
                'items' => [
                    ['product_id' => 3, 'quantity' => 3, 'unit_price' => 600.00, 'tax_rate' => 15.00],
                    ['product_id' => 4, 'quantity' => 5, 'unit_price' => 150.00, 'tax_rate' => 15.00]
                ]
            ],
            [
                'customer_id' => 3,
                'date' => date('Y-m-d'), // اليوم
                'items' => [
                    ['product_id' => 5, 'quantity' => 10, 'unit_price' => 80.00, 'tax_rate' => 15.00],
                    ['product_id' => 6, 'quantity' => 2, 'unit_price' => 200.00, 'tax_rate' => 15.00]
                ]
            ]
        ];

        foreach ($purchases_data as $purchase) {
            // حساب المجاميع
            $subtotal = 0;
            $tax_amount = 0;
            
            foreach ($purchase['items'] as $item) {
                $item_subtotal = $item['quantity'] * $item['unit_price'];
                $item_tax = $item_subtotal * ($item['tax_rate'] / 100);
                $subtotal += $item_subtotal;
                $tax_amount += $item_tax;
            }
            
            $total_amount = $subtotal + $tax_amount;
            $invoice_number = 'PUR-' . date('Ymd') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
            
            // إدراج فاتورة المشتريات
            $stmt = $db->prepare("INSERT INTO purchases (customer_id, invoice_number, date, subtotal, tax_amount, total_amount, notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $notes = 'فاتورة مشتريات تجريبية';
            $stmt->bind_param("issddds", $purchase['customer_id'], $invoice_number, $purchase['date'], $subtotal, $tax_amount, $total_amount, $notes);
            $stmt->execute();
            
            $purchase_id = $stmt->insert_id;
            
            // إدراج عناصر المشتريات
            foreach ($purchase['items'] as $item) {
                $item_subtotal = $item['quantity'] * $item['unit_price'];
                $item_tax = $item_subtotal * ($item['tax_rate'] / 100);
                $item_total = $item_subtotal + $item_tax;
                
                // الحصول على اسم المنتج
                $product_stmt = $db->prepare("SELECT name FROM products WHERE id = ?");
                $product_stmt->bind_param("i", $item['product_id']);
                $product_stmt->execute();
                $product_result = $product_stmt->get_result();
                $product_name = $product_result->fetch_assoc()['name'];
                
                $item_stmt = $db->prepare("INSERT INTO purchase_items (purchase_id, product_id, product_name, quantity, price, tax_rate, tax_amount, total_price) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $item_stmt->bind_param("iisidddd", $purchase_id, $item['product_id'], $product_name, $item['quantity'], $item['unit_price'], $item['tax_rate'], $item_tax, $item_total);
                $item_stmt->execute();
            }
        }

        // إضافة مبيعات تجريبية
        $sales_data = [
            [
                'customer_id' => 1,
                'date' => '2024-01-16',
                'items' => [
                    ['product_id' => 1, 'quantity' => 1, 'unit_price' => 2800.00, 'tax_rate' => 15.00],
                    ['product_id' => 4, 'quantity' => 2, 'unit_price' => 180.00, 'tax_rate' => 15.00]
                ]
            ],
            [
                'customer_id' => 2,
                'date' => '2024-01-25',
                'items' => [
                    ['product_id' => 3, 'quantity' => 2, 'unit_price' => 700.00, 'tax_rate' => 15.00],
                    ['product_id' => 5, 'quantity' => 3, 'unit_price' => 100.00, 'tax_rate' => 15.00]
                ]
            ],
            [
                'customer_id' => 3,
                'date' => date('Y-m-d'), // اليوم
                'items' => [
                    ['product_id' => 2, 'quantity' => 1, 'unit_price' => 950.00, 'tax_rate' => 15.00],
                    ['product_id' => 6, 'quantity' => 1, 'unit_price' => 250.00, 'tax_rate' => 15.00]
                ]
            ]
        ];

        foreach ($sales_data as $sale) {
            // حساب المجاميع
            $subtotal = 0;
            $tax_amount = 0;
            
            foreach ($sale['items'] as $item) {
                $item_subtotal = $item['quantity'] * $item['unit_price'];
                $item_tax = $item_subtotal * ($item['tax_rate'] / 100);
                $subtotal += $item_subtotal;
                $tax_amount += $item_tax;
            }
            
            $total_amount = $subtotal + $tax_amount;
            $invoice_number = 'SAL-' . date('Ymd') . '-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT);
            
            // إدراج فاتورة المبيعات
            $stmt = $db->prepare("INSERT INTO sales (customer_id, invoice_number, date, subtotal, tax_amount, total_amount, notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $notes = 'فاتورة مبيعات تجريبية';
            $stmt->bind_param("issddds", $sale['customer_id'], $invoice_number, $sale['date'], $subtotal, $tax_amount, $total_amount, $notes);
            $stmt->execute();
            
            $sale_id = $stmt->insert_id;
            
            // إدراج عناصر المبيعات
            foreach ($sale['items'] as $item) {
                $item_subtotal = $item['quantity'] * $item['unit_price'];
                $item_tax = $item_subtotal * ($item['tax_rate'] / 100);
                $item_total = $item_subtotal + $item_tax;
                
                $item_stmt = $db->prepare("INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, tax_rate, tax_amount, total_price) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $item_stmt->bind_param("iiddddd", $sale_id, $item['product_id'], $item['quantity'], $item['unit_price'], $item['tax_rate'], $item_tax, $item_total);
                $item_stmt->execute();
            }
        }

        $_SESSION['success'] = "تم إضافة البيانات التجريبية بنجاح!";
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء إضافة البيانات التجريبية: " . $e->getMessage();
    }
}

displayMessages();
?>

<div class="container mt-4">
    <h2>إضافة بيانات تجريبية</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات</h5>
        <p>هذه الصفحة تسمح لك بإضافة بيانات تجريبية للنظام لاختبار الوظائف. ستتم إضافة:</p>
        <ul>
            <li>3 عملاء تجريبيين</li>
            <li>6 منتجات تجريبية</li>
            <li>3 فواتير مشتريات تجريبية</li>
            <li>3 فواتير مبيعات تجريبية</li>
        </ul>
    </div>
    
    <form method="POST">
        <button type="submit" name="add_sample_data" class="btn btn-primary" onclick="return confirm('هل أنت متأكد من إضافة البيانات التجريبية؟')">
            <i class="fas fa-plus"></i> إضافة البيانات التجريبية
        </button>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> العودة للصفحة الرئيسية
        </a>
    </form>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
