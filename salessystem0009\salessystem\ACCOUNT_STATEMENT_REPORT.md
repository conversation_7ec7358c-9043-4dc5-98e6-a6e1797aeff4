# تقرير كشف الحساب الشامل

## 🏦 نظام كشف الحساب البنكي

تم إضافة تقرير جديد يشبه كشف الحساب البنكي يعرض جميع المعاملات المالية (مبيعات ومشتريات) في قائمة موحدة مرتبة زمنياً مع حساب الرصيد التراكمي.

## 🔧 المميزات المطبقة

### **1. عرض شامل للمعاملات:**

#### **دمج المبيعات والمشتريات:**
```sql
-- استعلام المبيعات
SELECT 
    s.id,
    s.invoice_number,
    s.date,
    s.total_amount,
    s.payment_status,
    c.name as customer_name,
    'sale' as transaction_type,
    'مبيعات' as transaction_type_ar
FROM sales s
LEFT JOIN customers c ON s.customer_id = c.id

-- استعلام المشتريات
SELECT 
    p.id,
    p.invoice_number,
    p.date,
    p.total_amount,
    p.payment_status,
    COALESCE(c.name, p.supplier_name, 'غير محدد') as customer_name,
    'purchase' as transaction_type,
    'مشتريات' as transaction_type_ar
FROM purchases p
LEFT JOIN customers c ON p.customer_id = c.id
```

#### **ترتيب زمني موحد:**
```php
// ترتيب المعاملات حسب التاريخ
usort($account_transactions, function($a, $b) {
    return strtotime($a['date']) - strtotime($b['date']);
});
```

### **2. حساب الرصيد التراكمي:**

#### **منطق الحساب:**
```php
$running_balance = 0;
foreach ($account_transactions as &$transaction) {
    if ($transaction['transaction_type'] == 'sale') {
        $running_balance += $transaction['total_amount'];  // المبيعات تزيد الرصيد
    } else {
        $running_balance -= $transaction['total_amount'];  // المشتريات تقلل الرصيد
    }
    $transaction['running_balance'] = $running_balance;
}
```

### **3. واجهة تشبه كشف الحساب البنكي:**

#### **ملخص الرصيد العلوي مع الضرائب:**
```html
<div class="row g-0 border-bottom">
    <div class="col-md-2 bg-light p-3 border-end">
        <h6 class="text-success">إجمالي المبيعات</h6>
        <h5 class="text-success">[المبلغ] ر.س</h5>
    </div>
    <div class="col-md-2 bg-light p-3 border-end">
        <h6 class="text-danger">إجمالي المشتريات</h6>
        <h5 class="text-danger">[المبلغ] ر.س</h5>
    </div>
    <div class="col-md-2 bg-light p-3 border-end">
        <h6 class="text-success/danger">صافي الرصيد</h6>
        <h5 class="text-success/danger">[المبلغ] ر.س</h5>
    </div>
    <div class="col-md-2 bg-light p-3 border-end">
        <h6 class="text-primary">ضريبة المبيعات</h6>
        <h5 class="text-primary">[المبلغ] ر.س</h5>
    </div>
    <div class="col-md-2 bg-light p-3 border-end">
        <h6 class="text-warning">ضريبة المشتريات</h6>
        <h5 class="text-warning">[المبلغ] ر.س</h5>
    </div>
    <div class="col-md-2 bg-light p-3">
        <h6 class="text-success/danger">صافي الضريبة</h6>
        <h5 class="text-success/danger">[المبلغ] ر.س</h5>
    </div>
</div>
```

#### **جدول المعاملات مع الضرائب:**
```html
<table class="table table-hover">
    <thead class="table-dark">
        <tr>
            <th>التاريخ</th>
            <th>رقم الفاتورة</th>
            <th>نوع المعاملة</th>
            <th>العميل/المورد</th>
            <th>حالة الدفع</th>
            <th>مدين</th>        <!-- المشتريات (بدون ضريبة) -->
            <th>دائن</th>         <!-- المبيعات (بدون ضريبة) -->
            <th>ض.مدين</th>       <!-- ضريبة المشتريات -->
            <th>ض.دائن</th>        <!-- ضريبة المبيعات -->
            <th>الرصيد</th>       <!-- الرصيد التراكمي -->
            <th>إجراءات</th>
        </tr>
    </thead>
</table>
```

## 💰 نظام الضرائب المتقدم

### **حسابات الضرائب:**

#### **ضريبة المبيعات:**
- **اللون**: أزرق (`text-primary`)
- **المعنى**: الضريبة المحصلة من العملاء
- **الحساب**: مجموع ضرائب جميع فواتير المبيعات
- **التأثير**: تزيد من الالتزامات الضريبية

#### **ضريبة المشتريات:**
- **اللون**: برتقالي (`text-warning`)
- **المعنى**: الضريبة المدفوعة للموردين
- **الحساب**: مجموع ضرائب جميع فواتير المشتريات
- **التأثير**: تقلل من الالتزامات الضريبية

#### **صافي الضريبة:**
```php
$net_tax = $sales_tax - $purchases_tax;

// إذا كان موجب: يجب دفع ضريبة للحكومة
// إذا كان سالب: يحق استرداد ضريبة من الحكومة
```

### **عرض الضرائب في الجدول:**

#### **أعمدة الضرائب:**
- **ض.مدين**: ضريبة المشتريات (برتقالي)
- **ض.دائن**: ضريبة المبيعات (أزرق)
- **عرض منفصل**: لسهولة المتابعة والمراجعة

#### **الإجماليات:**
```html
<tfoot class="table-dark">
    <tr>
        <th>إجمالي ضريبة المشتريات</th>
        <th>إجمالي ضريبة المبيعات</th>
        <th>صافي الضريبة</th>
    </tr>
</tfoot>
```

## 🎨 التصميم والألوان

### **نظام الألوان المحدث:**

#### **حسب نوع المعاملة:**
- **المبيعات**:
  - لون أخضر (`text-success`, `bg-success`)
  - أيقونة سهم لأعلى (`fa-arrow-up`)
  - صفوف بخلفية فاتحة (`table-light`)

- **المشتريات**:
  - لون أحمر (`text-danger`, `bg-danger`)
  - أيقونة سهم لأسفل (`fa-arrow-down`)
  - صفوف بخلفية صفراء (`table-warning`)

#### **حسب نوع الضريبة:**
- **ضريبة المبيعات**:
  - لون أزرق (`text-primary`)
  - معنى: ضريبة محصلة (دخل)
  - تظهر في عمود "ض.دائن"

- **ضريبة المشتريات**:
  - لون برتقالي (`text-warning`)
  - معنى: ضريبة مدفوعة (خصم)
  - تظهر في عمود "ض.مدين"

#### **حسب حالة الدفع:**
```php
switch ($payment_status) {
    case 'paid':
        $status_class = 'bg-success';
        $status_text = 'مدفوع';
        break;
    case 'unpaid':
        $status_class = 'bg-danger';
        $status_text = 'غير مدفوع';
        break;
    case 'partial':
        $status_class = 'bg-warning';
        $status_text = 'مدفوع جزئياً';
        break;
}
```

#### **حسب الرصيد:**
- **رصيد موجب**: أخضر (`text-success`)
- **رصيد سالب**: أحمر (`text-danger`)

### **الأيقونات المميزة:**
- **العملاء**: `fa-user`
- **الموردين**: `fa-truck`
- **المبيعات**: `fa-arrow-up`
- **المشتريات**: `fa-arrow-down`
- **عرض**: `fa-eye`
- **طباعة**: `fa-print`

## 📊 المعلومات المعروضة

### **في الملخص العلوي:**
1. **إجمالي المبيعات** - مجموع جميع فواتير المبيعات (شامل الضريبة)
2. **إجمالي المشتريات** - مجموع جميع فواتير المشتريات (شامل الضريبة)
3. **صافي الرصيد** - الفرق بين المبيعات والمشتريات
4. **ضريبة المبيعات** - إجمالي ضرائب المبيعات المحصلة (أزرق)
5. **ضريبة المشتريات** - إجمالي ضرائب المشتريات المدفوعة (برتقالي)
6. **صافي الضريبة** - الفرق بين ضرائب المبيعات والمشتريات

### **في جدول المعاملات:**
1. **التاريخ** - تاريخ الفاتورة
2. **رقم الفاتورة** - مع شارة ملونة حسب النوع
3. **نوع المعاملة** - مبيعات أو مشتريات مع أيقونة
4. **العميل/المورد** - اسم الطرف الآخر
5. **حالة الدفع** - مدفوع/غير مدفوع/جزئي
6. **مدين** - مبلغ المشتريات بدون ضريبة (أحمر)
7. **دائن** - مبلغ المبيعات بدون ضريبة (أخضر)
8. **ض.مدين** - ضريبة المشتريات (برتقالي)
9. **ض.دائن** - ضريبة المبيعات (أزرق)
10. **الرصيد** - الرصيد التراكمي (شامل الضريبة)
11. **إجراءات** - عرض وطباعة

## 🔍 خيارات الفلترة

### **الفلاتر المتاحة:**
1. **الفترة الزمنية** - من تاريخ إلى تاريخ
2. **العميل المحدد** - عرض معاملات عميل واحد فقط
3. **نوع التقرير** - "كشف حساب شامل"

### **مثال على الاستخدام:**
```
- الفترة: 01/01/2024 - 31/12/2024
- العميل: شركة الأمل التجارية
- النتيجة: جميع معاملات هذا العميل في السنة
```

### **مثال كشف حساب مع الضرائب:**
```
التاريخ | النوع    | دائن  | مدين  | ض.دائن | ض.مدين | الرصيد
01/01  | مبيعات  | 4348  | -     | 652    | -      | 5000
05/01  | مشتريات | -     | 1739  | -      | 261    | 3000
10/01  | مبيعات  | 2609  | -     | 391    | -      | 6000
15/01  | مشتريات | -     | 870   | -      | 130    | 5000

الملخص:
- إجمالي المبيعات: 8000 ر.س
- إجمالي المشتريات: 3000 ر.س
- صافي الرصيد: 5000 ر.س
- ضريبة المبيعات: 1043 ر.س
- ضريبة المشتريات: 391 ر.س
- صافي الضريبة: 652 ر.س (مستحق للحكومة)
```

## 📈 الفوائد المحققة

### **للمحاسبة:**
- ✅ **رؤية شاملة** لجميع المعاملات المالية
- ✅ **تتبع الرصيد** بشكل تراكمي ومستمر
- ✅ **سهولة المراجعة** والتدقيق المحاسبي
- ✅ **تحديد الأرصدة** المدينة والدائنة

### **لإدارة العملاء:**
- ✅ **متابعة حساب العميل** بشكل مفصل
- ✅ **تحديد المديونيات** والمستحقات
- ✅ **تاريخ كامل** للتعاملات مع كل عميل
- ✅ **حالة المدفوعات** لكل فاتورة

### **للتقارير المالية:**
- ✅ **كشف حساب احترافي** يشبه البنوك
- ✅ **ملخص مالي** في أعلى التقرير
- ✅ **إجماليات دقيقة** في أسفل الجدول
- ✅ **تصدير وطباعة** سهلة

## 🖨️ إمكانيات الطباعة والتصدير

### **أزرار الإجراءات:**
- **عرض الفاتورة** - رابط لصفحة تفاصيل الفاتورة
- **طباعة الفاتورة** - فتح الفاتورة في نافذة جديدة للطباعة
- **طباعة التقرير** - طباعة كشف الحساب كاملاً

### **تنسيق الطباعة:**
- **خط واضح** ومقروء (14px)
- **ألوان مناسبة** للطباعة
- **تخطيط منظم** مع حدود واضحة
- **معلومات كاملة** في كل صفحة

## 🔧 التقنيات المستخدمة

### **قاعدة البيانات:**
- **استعلامات متقدمة** لدمج الجداول
- **ترتيب زمني** للمعاملات
- **حسابات تراكمية** للأرصدة
- **فلترة ديناميكية** حسب المعايير

### **واجهة المستخدم:**
- **Bootstrap 5** للتصميم المتجاوب
- **Font Awesome** للأيقونات
- **CSS مخصص** للألوان والتنسيق
- **JavaScript** للتفاعل

### **الأمان:**
- **تحقق من الصلاحيات** قبل العرض
- **حماية من SQL Injection** مع Prepared Statements
- **تشفير البيانات** الحساسة
- **تسجيل العمليات** في سجل النشاطات

## ✅ الخلاصة

تم تطوير تقرير كشف الحساب الشامل بنجاح:

### **الإنجازات:**
1. **دمج المبيعات والمشتريات** في قائمة موحدة
2. **حساب الرصيد التراكمي** لكل معاملة
3. **واجهة احترافية** تشبه كشف الحساب البنكي
4. **فلترة متقدمة** حسب التاريخ والعميل
5. **نظام ضرائب متكامل** مع حسابات دقيقة
6. **ملخص ضريبي شامل** في أعلى التقرير

### **النتائج:**
- ✅ **رؤية شاملة** لجميع المعاملات المالية والضريبية
- ✅ **تتبع دقيق** للأرصدة والمديونيات والضرائب
- ✅ **حسابات ضريبية** دقيقة ومفصلة
- ✅ **تقرير احترافي** قابل للطباعة والتصدير
- ✅ **سهولة الاستخدام** والفهم للمحاسبين

### **المميزات الرئيسية:**
- **ترتيب زمني** للمعاملات
- **ألوان مميزة** لكل نوع معاملة وضريبة
- **رصيد تراكمي** محدث باستمرار
- **ملخص مالي وضريبي** شامل في الأعلى
- **أعمدة ضرائب منفصلة** لسهولة المراجعة
- **حسابات ضريبية تلقائية** دقيقة
- **إجراءات سريعة** لكل فاتورة

**النتيجة: تقرير كشف حساب شامل واحترافي يوفر رؤية كاملة للوضع المالي مع إمكانيات فلترة وطباعة متقدمة!** 🎉
