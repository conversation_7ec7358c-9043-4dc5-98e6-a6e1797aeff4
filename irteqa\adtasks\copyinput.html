<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخ النص في مربع النص</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
        }
        input {
            width: 200px;
            height: 30px;
            margin: 20px auto;
            padding: 10px;
            resize: none;
            font-size: 2em;
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>

<input id="myText"></input>
<button onclick="copyText()">نسخ النص</button>

<script>
    function copyText() {
        var copyText = document.getElementById("myText");
        copyText.select();
        copyText.setSelectionRange(0, 99999); /* For mobile devices */
        document.execCommand("copy");
        alert("تم نسخ النص: " + copyText.value);
    }
</script>

</body>
</html>
