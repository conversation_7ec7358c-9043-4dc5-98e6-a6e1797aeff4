import Onyx, { Image as OnyxPreview } from './onyx';
import Pikachu, { Image as PikachuPreview } from './pikachu';
import Gengar, { Image as GengarPreview } from './gengar';
import Castform, { Image as CastformPreview } from './castform';
import Glalie, { Image as GlaliePreview } from './glalie';
import Celebi, { Image as CelebiPreview } from './celebi';

export default [
  {
    key: 'onyx',
    name: 'Onyx',
    component: Onyx,
    preview: OnyxPreview,
  },
  {
    key: 'pikachu',
    name: '<PERSON><PERSON><PERSON>',
    component: <PERSON><PERSON>chu,
    preview: PikachuPreview,
  },
  {
    key: 'gengar',
    name: 'Gengar',
    component: Gengar,
    preview: GengarPreview,
  },
  {
    key: 'castform',
    name: 'Castform',
    component: Castform,
    preview: CastformPreview,
  },
  {
    key: 'glalie',
    name: 'Glal<PERSON>',
    component: Glalie,
    preview: GlaliePreview,
  },
  {
    key: 'celebi',
    name: '<PERSON><PERSON><PERSON>',
    component: <PERSON><PERSON><PERSON>,
    preview: CelebiPreview,
  },
];
