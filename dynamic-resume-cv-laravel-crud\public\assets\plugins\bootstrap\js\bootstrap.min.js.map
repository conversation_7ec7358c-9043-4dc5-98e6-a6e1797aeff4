{"version": 3, "sources": ["../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/data.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/util/scrollbar.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "names": ["TRANSITION_END", "getSelector", "element", "selector", "getAttribute", "hrefAttribute", "includes", "startsWith", "split", "trim", "getSelectorFromElement", "document", "querySelector", "getElementFromSelector", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "object", "j<PERSON>y", "nodeType", "getElement", "length", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "window", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "getUidEvent", "uid", "getEvent", "<PERSON><PERSON><PERSON><PERSON>", "events", "delegationSelector", "Object", "values", "find", "event", "<PERSON><PERSON><PERSON><PERSON>", "normalizeParameters", "originalTypeEvent", "delegationFunction", "delegation", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "call", "this", "handlers", "previousFunction", "replace", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "keys", "on", "one", "inNamespace", "isNamespace", "elementEvent", "slice", "keyHandlers", "trigger", "args", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "key", "defineProperty", "get", "preventDefault", "elementMap", "Map", "Data", "set", "instance", "instanceMap", "size", "console", "error", "Array", "from", "remove", "delete", "normalizeData", "value", "toString", "JSON", "parse", "decodeURIComponent", "_unused", "normalizeDataKey", "chr", "toLowerCase", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "prototype", "match", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "static", "getInstance", "VERSION", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "getOrCreateInstance", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "map", "join", "el", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "endCallback", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLID", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "KEY_TO_DIRECTION", "ArrowLeft", "ArrowRight", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "clearTimeout", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "eventName", "_orderToDirection", "isCycling", "directionalClassName", "orderClassName", "_isAnimated", "SELECTOR_ACTIVE", "clearInterval", "carousel", "slideIndex", "carousels", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "style", "scrollSize", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "selectorElements", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "offset", "boundary", "reference", "display", "popperConfig", "autoClose", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "destroy", "update", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "createPopper", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "placement", "modifiers", "options", "enabled", "_selectMenuItem", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "dataApiKeydownHandler", "clearMenus", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "clientWidth", "innerWidth", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "reset", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "overflow", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "setProperty", "actualValue", "removeProperty", "callBack", "sel", "EVENT_MOUSEDOWN", "className", "rootElement", "clickCallback", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "TAB_NAV_BACKWARD", "trapElement", "autofocus", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "elements", "shift<PERSON>ey", "EVENT_HIDDEN", "EVENT_SHOW", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "htmlElement", "handleUpdate", "scrollTop", "modalBody", "_triggerBackdropTransition", "currentTarget", "_resetAdjustments", "isModalOverflowing", "scrollHeight", "clientHeight", "initialOverflowY", "overflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "EVENT_HIDE_PREVENTED", "scroll", "<PERSON><PERSON><PERSON>", "blur", "position", "uriAttributes", "SAFE_URL_PATTERN", "DATA_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "nodeName", "nodeValue", "attributeRegex", "some", "regex", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "div", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "extraClass", "template", "content", "html", "sanitize", "sanitizeFn", "allowList", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "innerHTML", "_maybeSanitize", "text", "entries", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "sanitizeHtml", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "title", "delay", "container", "fallbackPlacements", "customClass", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "_initializeOnDelegatedTarget", "click", "_isWithActiveTrigger", "_enter", "_leave", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "ownerDocument", "_getTipElement", "previousHoverState", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "isShown", "_getDelegateConfig", "attachment", "phase", "state", "triggers", "eventIn", "eventOut", "_fixTitle", "originalTitle", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "Popover", "_getContent", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "rootMargin", "smoothScroll", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "hash", "height", "offsetTop", "scrollTo", "top", "threshold", "_getR<PERSON><PERSON><PERSON><PERSON>", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "id", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "_activateParents", "listGroup", "item", "activeNodes", "node", "spy", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "SELECTOR_INNER_ELEM", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "_onInteraction", "isInteracting"], "mappings": ";;;;;mlBASMA,EAAiB,gBAuBjBC,EAAcC,IAClB,IAAIC,EAAWD,EAAQE,aAAa,kBAEpC,IAAKD,GAAyB,MAAbA,EAAkB,CACjC,IAAIE,EAAgBH,EAAQE,aAAa,QAMzC,IAAKC,IAAmBA,EAAcC,SAAS,OAASD,EAAcE,WAAW,KAC/E,OAAO,KAILF,EAAcC,SAAS,OAASD,EAAcE,WAAW,OAC3DF,EAAiB,IAAGA,EAAcG,MAAM,KAAK,MAG/CL,EAAWE,GAAmC,MAAlBA,EAAwBA,EAAcI,OAAS,KAG7E,OAAON,GAGHO,EAAyBR,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAIC,GACKQ,SAASC,cAAcT,GAAYA,EAGrC,MAGHU,EAAyBX,IAC7B,MAAMC,EAAWF,EAAYC,GAE7B,OAAOC,EAAWQ,SAASC,cAAcT,GAAY,MA0BjDW,EAAuBZ,IAC3BA,EAAQa,cAAc,IAAIC,MAAMhB,KAG5BiB,EAAYC,MACXA,GAA4B,iBAAXA,UAIO,IAAlBA,EAAOC,SAChBD,EAASA,EAAO,SAGgB,IAApBA,EAAOE,UAGjBC,EAAaH,GAEbD,EAAUC,GACLA,EAAOC,OAASD,EAAO,GAAKA,EAGf,iBAAXA,GAAuBA,EAAOI,OAAS,EACzCX,SAASC,cAAcM,GAGzB,KAGHK,EAAYrB,IAChB,IAAKe,EAAUf,IAAgD,IAApCA,EAAQsB,iBAAiBF,OAClD,OAAO,EAGT,MAAMG,EAAgF,YAA7DC,iBAAiBxB,GAASyB,iBAAiB,cAE9DC,EAAgB1B,EAAQ2B,QAAQ,uBAEtC,IAAKD,EACH,OAAOH,EAGT,GAAIG,IAAkB1B,EAAS,CAC7B,MAAM4B,EAAU5B,EAAQ2B,QAAQ,WAChC,GAAIC,GAAWA,EAAQC,aAAeH,EACpC,OAAO,EAGT,GAAgB,OAAZE,EACF,OAAO,EAIX,OAAOL,GAGHO,EAAa9B,IACZA,GAAWA,EAAQkB,WAAaa,KAAKC,gBAItChC,EAAQiC,UAAUC,SAAS,mBAIC,IAArBlC,EAAQmC,SACVnC,EAAQmC,SAGVnC,EAAQoC,aAAa,aAAoD,UAArCpC,EAAQE,aAAa,aAG5DmC,EAAiBrC,IACrB,IAAKS,SAAS6B,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxBvC,EAAQwC,YAA4B,CAC7C,MAAMC,EAAOzC,EAAQwC,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,KAG7C,OAAIzC,aAAmB0C,WACd1C,EAIJA,EAAQ6B,WAINQ,EAAerC,EAAQ6B,YAHrB,MAMLc,EAAO,OAUPC,EAAS5C,IACbA,EAAQ6C,cAGJC,EAAY,IACZC,OAAOC,SAAWvC,SAASwC,KAAKb,aAAa,qBACxCW,OAAOC,OAGT,KAGHE,EAA4B,GAmB5BC,EAAQ,IAAuC,QAAjC1C,SAAS6B,gBAAgBc,IAEvCC,EAAqBC,IAnBAC,IAAAA,EAAAA,EAoBN,KACjB,MAAMC,EAAIV,IAEV,GAAIU,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,mBA7BQ,YAAxBpD,SAASuD,YAENd,EAA0B9B,QAC7BX,SAASwD,iBAAiB,oBAAoB,KAC5C,IAAK,MAAMV,KAAYL,EACrBK,OAKNL,EAA0BgB,KAAKX,IAE/BA,KAuBEY,EAAUZ,IACU,mBAAbA,GACTA,KAIEa,EAAyB,CAACb,EAAUc,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAH,EAAQZ,GAIV,MACMgB,EA/LiCvE,CAAAA,IACvC,IAAKA,EACH,OAAO,EAIT,IAAIwE,mBAAEA,EAAFC,gBAAsBA,GAAoB1B,OAAOvB,iBAAiBxB,GAEtE,MAAM0E,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBlE,MAAM,KAAK,GACnDmE,EAAkBA,EAAgBnE,MAAM,KAAK,GAnFf,KAqFtBqE,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GAkLgBK,CAAiCT,GADlC,EAGxB,IAAIU,GAAS,EAEb,MAAMC,EAAU,EAAGC,OAAAA,MACbA,IAAWZ,IAIfU,GAAS,EACTV,EAAkBa,oBAAoBpF,EAAgBkF,GACtDb,EAAQZ,KAGVc,EAAkBJ,iBAAiBnE,EAAgBkF,GACnDG,YAAW,KACJJ,GACHnE,EAAqByD,KAEtBE,IAYCa,EAAuB,CAACC,EAAMC,EAAeC,EAAeC,KAChE,MAAMC,EAAaJ,EAAKjE,OACxB,IAAIsE,EAAQL,EAAKM,QAAQL,GAIzB,OAAe,IAAXI,GACMH,GAAiBC,EAAiBH,EAAKI,EAAa,GAAKJ,EAAK,IAGxEK,GAASH,EAAgB,GAAK,EAE1BC,IACFE,GAASA,EAAQD,GAAcA,GAG1BJ,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIJ,EAAOD,EAAa,OC1SjDM,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WAOF,SAASC,EAAYzG,EAAS0G,GAC5B,OAAQA,GAAQ,GAAEA,MAAQP,OAAiBnG,EAAQmG,UAAYA,IAGjE,SAASQ,EAAS3G,GAChB,MAAM0G,EAAMD,EAAYzG,GAKxB,OAHAA,EAAQmG,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,GAqCvB,SAASE,EAAYC,EAAQ7B,EAAS8B,EAAqB,MACzD,OAAOC,OAAOC,OAAOH,GAClBI,MAAKC,GAASA,EAAMC,kBAAoBnC,GAAWkC,EAAMJ,qBAAuBA,IAGrF,SAASM,EAAoBC,EAAmBrC,EAASsC,GACvD,MAAMC,EAAgC,iBAAZvC,EACpBmC,EAAkBI,EAAaD,EAAqBtC,EAC1D,IAAIwC,EAAYC,EAAaJ,GAM7B,OAJKd,EAAamB,IAAIF,KACpBA,EAAYH,GAGP,CAACE,EAAYJ,EAAiBK,GAGvC,SAASG,EAAW3H,EAASqH,EAAmBrC,EAASsC,EAAoBM,GAC3E,GAAiC,iBAAtBP,IAAmCrH,EAC5C,OAUF,GAPKgF,IACHA,EAAUsC,EACVA,EAAqB,MAKnBD,KAAqBjB,EAAc,CACrC,MAAMyB,EAAejE,GACZ,SAAUsD,GACf,IAAKA,EAAMY,eAAkBZ,EAAMY,gBAAkBZ,EAAMa,iBAAmBb,EAAMa,eAAe7F,SAASgF,EAAMY,eAChH,OAAOlE,EAAGoE,KAAKC,KAAMf,IAKvBI,EACFA,EAAqBO,EAAaP,GAElCtC,EAAU6C,EAAa7C,GAI3B,MAAOuC,EAAYJ,EAAiBK,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAC3FT,EAASF,EAAS3G,GAClBkI,EAAWrB,EAAOW,KAAeX,EAAOW,GAAa,IACrDW,EAAmBvB,EAAYsB,EAAUf,EAAiBI,EAAavC,EAAU,MAEvF,GAAImD,EAGF,YAFAA,EAAiBP,OAASO,EAAiBP,QAAUA,GAKvD,MAAMlB,EAAMD,EAAYU,EAAiBE,EAAkBe,QAAQrC,EAAgB,KAC7EnC,EAAK2D,EA/Eb,SAAoCvH,EAASC,EAAU2D,GACrD,OAAO,SAASoB,EAAQkC,GACtB,MAAMmB,EAAcrI,EAAQsI,iBAAiBrI,GAE7C,IAAK,IAAIgF,OAAEA,GAAWiC,EAAOjC,GAAUA,IAAWgD,KAAMhD,EAASA,EAAOpD,WACtE,IAAK,MAAM0G,KAAcF,EACvB,GAAIE,IAAetD,EAUnB,OANAiC,EAAMa,eAAiB9C,EAEnBD,EAAQ4C,QACVY,EAAaC,IAAIzI,EAASkH,EAAMwB,KAAMzI,EAAU2D,GAG3CA,EAAG+E,MAAM1D,EAAQ,CAACiC,KAgE7B0B,CAA2B5I,EAASgF,EAASsC,GA5FjD,SAA0BtH,EAAS4D,GACjC,OAAO,SAASoB,EAAQkC,GAOtB,OANAA,EAAMa,eAAiB/H,EAEnBgF,EAAQ4C,QACVY,EAAaC,IAAIzI,EAASkH,EAAMwB,KAAM9E,GAGjCA,EAAG+E,MAAM3I,EAAS,CAACkH,KAqF1B2B,CAAiB7I,EAASgF,GAE5BpB,EAAGkD,mBAAqBS,EAAavC,EAAU,KAC/CpB,EAAGuD,gBAAkBA,EACrBvD,EAAGgE,OAASA,EACZhE,EAAGuC,SAAWO,EACdwB,EAASxB,GAAO9C,EAEhB5D,EAAQiE,iBAAiBuD,EAAW5D,EAAI2D,GAG1C,SAASuB,EAAc9I,EAAS6G,EAAQW,EAAWxC,EAAS8B,GAC1D,MAAMlD,EAAKgD,EAAYC,EAAOW,GAAYxC,EAAS8B,GAE9ClD,IAIL5D,EAAQkF,oBAAoBsC,EAAW5D,EAAImF,QAAQjC,WAC5CD,EAAOW,GAAW5D,EAAGuC,WAG9B,SAAS6C,EAAyBhJ,EAAS6G,EAAQW,EAAWyB,GAC5D,MAAMC,EAAoBrC,EAAOW,IAAc,GAE/C,IAAK,MAAM2B,KAAcpC,OAAOqC,KAAKF,GACnC,GAAIC,EAAW/I,SAAS6I,GAAY,CAClC,MAAM/B,EAAQgC,EAAkBC,GAChCL,EAAc9I,EAAS6G,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,qBAK7E,SAASW,EAAaP,GAGpB,OADAA,EAAQA,EAAMkB,QAAQpC,EAAgB,IAC/BI,EAAac,IAAUA,EAGhC,MAAMsB,EAAe,CACnBa,GAAGrJ,EAASkH,EAAOlC,EAASsC,GAC1BK,EAAW3H,EAASkH,EAAOlC,EAASsC,GAAoB,IAG1DgC,IAAItJ,EAASkH,EAAOlC,EAASsC,GAC3BK,EAAW3H,EAASkH,EAAOlC,EAASsC,GAAoB,IAG1DmB,IAAIzI,EAASqH,EAAmBrC,EAASsC,GACvC,GAAiC,iBAAtBD,IAAmCrH,EAC5C,OAGF,MAAOuH,EAAYJ,EAAiBK,GAAaJ,EAAoBC,EAAmBrC,EAASsC,GAC3FiC,EAAc/B,IAAcH,EAC5BR,EAASF,EAAS3G,GAClBwJ,EAAcnC,EAAkBhH,WAAW,KAEjD,QAA+B,IAApB8G,EAAiC,CAE1C,IAAKN,IAAWA,EAAOW,GACrB,OAIF,YADAsB,EAAc9I,EAAS6G,EAAQW,EAAWL,EAAiBI,EAAavC,EAAU,MAIpF,GAAIwE,EACF,IAAK,MAAMC,KAAgB1C,OAAOqC,KAAKvC,GACrCmC,EAAyBhJ,EAAS6G,EAAQ4C,EAAcpC,EAAkBqC,MAAM,IAIpF,MAAMR,EAAoBrC,EAAOW,IAAc,GAC/C,IAAK,MAAMmC,KAAe5C,OAAOqC,KAAKF,GAAoB,CACxD,MAAMC,EAAaQ,EAAYvB,QAAQnC,EAAe,IAEtD,IAAKsD,GAAelC,EAAkBjH,SAAS+I,GAAa,CAC1D,MAAMjC,EAAQgC,EAAkBS,GAChCb,EAAc9I,EAAS6G,EAAQW,EAAWN,EAAMC,gBAAiBD,EAAMJ,uBAK7E8C,QAAQ5J,EAASkH,EAAO2C,GACtB,GAAqB,iBAAV3C,IAAuBlH,EAChC,OAAO,KAGT,MAAMwD,EAAIV,IAIV,IAAIgH,EAAc,KACdC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EALH/C,IADFO,EAAaP,IAQZ1D,IACjBsG,EAActG,EAAE1C,MAAMoG,EAAO2C,GAE7BrG,EAAExD,GAAS4J,QAAQE,GACnBC,GAAWD,EAAYI,uBACvBF,GAAkBF,EAAYK,gCAC9BF,EAAmBH,EAAYM,sBAGjC,MAAMC,EAAM,IAAIvJ,MAAMoG,EAAO,CAAE6C,QAAAA,EAASO,YAAY,IAGpD,QAAoB,IAATT,EACT,IAAK,MAAMU,KAAOxD,OAAOqC,KAAKS,GAC5B9C,OAAOyD,eAAeH,EAAKE,EAAK,CAC9BE,IAAG,IACMZ,EAAKU,KAkBpB,OAZIN,GACFI,EAAIK,iBAGFV,GACFhK,EAAQa,cAAcwJ,GAGpBA,EAAIJ,kBAAoBH,GAC1BA,EAAYY,iBAGPL,IChTLM,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAI9K,EAASuK,EAAKQ,GACXJ,EAAWjD,IAAI1H,IAClB2K,EAAWG,IAAI9K,EAAS,IAAI4K,KAG9B,MAAMI,EAAcL,EAAWF,IAAIzK,GAI9BgL,EAAYtD,IAAI6C,IAA6B,IAArBS,EAAYC,KAMzCD,EAAYF,IAAIP,EAAKQ,GAJnBG,QAAQC,MAAO,+EAA8EC,MAAMC,KAAKL,EAAY5B,QAAQ,QAOhIqB,IAAG,CAACzK,EAASuK,IACPI,EAAWjD,IAAI1H,IACV2K,EAAWF,IAAIzK,GAASyK,IAAIF,IAG9B,KAGTe,OAAOtL,EAASuK,GACd,IAAKI,EAAWjD,IAAI1H,GAClB,OAGF,MAAMgL,EAAcL,EAAWF,IAAIzK,GAEnCgL,EAAYO,OAAOhB,GAGM,IAArBS,EAAYC,MACdN,EAAWY,OAAOvL,KC5CxB,SAASwL,EAAcC,GACrB,GAAc,SAAVA,EACF,OAAO,EAGT,GAAc,UAAVA,EACF,OAAO,EAGT,GAAIA,IAAU9G,OAAO8G,GAAOC,WAC1B,OAAO/G,OAAO8G,GAGhB,GAAc,KAAVA,GAA0B,SAAVA,EAClB,OAAO,KAGT,GAAqB,iBAAVA,EACT,OAAOA,EAGT,IACE,OAAOE,KAAKC,MAAMC,mBAAmBJ,IACrC,MAAMK,GACN,OAAOL,GAIX,SAASM,EAAiBxB,GACxB,OAAOA,EAAInC,QAAQ,UAAU4D,GAAQ,IAAGA,EAAIC,kBAG9C,MAAMC,EAAc,CAClBC,iBAAiBnM,EAASuK,EAAKkB,GAC7BzL,EAAQoM,aAAc,WAAUL,EAAiBxB,KAAQkB,IAG3DY,oBAAoBrM,EAASuK,GAC3BvK,EAAQsM,gBAAiB,WAAUP,EAAiBxB,OAGtDgC,kBAAkBvM,GAChB,IAAKA,EACH,MAAO,GAGT,MAAMwM,EAAa,GACbC,EAAS1F,OAAOqC,KAAKpJ,EAAQ0M,SAASC,QAAOpC,GAAOA,EAAIlK,WAAW,QAAUkK,EAAIlK,WAAW,cAElG,IAAK,MAAMkK,KAAOkC,EAAQ,CACxB,IAAIG,EAAUrC,EAAInC,QAAQ,MAAO,IACjCwE,EAAUA,EAAQC,OAAO,GAAGZ,cAAgBW,EAAQlD,MAAM,EAAGkD,EAAQxL,QACrEoL,EAAWI,GAAWpB,EAAcxL,EAAQ0M,QAAQnC,IAGtD,OAAOiC,GAGTM,iBAAgB,CAAC9M,EAASuK,IACjBiB,EAAcxL,EAAQE,aAAc,WAAU6L,EAAiBxB,QCpD1E,MAAMwC,EAEOC,qBACT,MAAO,GAGEC,yBACT,MAAO,GAGEvJ,kBACT,MAAM,IAAIwJ,MAAM,uEAGlBC,WAAWC,GAIT,OAHAA,EAASnF,KAAKoF,gBAAgBD,GAC9BA,EAASnF,KAAKqF,kBAAkBF,GAChCnF,KAAKsF,iBAAiBH,GACfA,EAGTE,kBAAkBF,GAChB,OAAOA,EAGTC,gBAAgBD,EAAQpN,GACtB,MAAMwN,EAAazM,EAAUf,GAAWkM,EAAYY,iBAAiB9M,EAAS,UAAY,GAE1F,MAAO,IACFiI,KAAKwF,YAAYT,WACM,iBAAfQ,EAA0BA,EAAa,MAC9CzM,EAAUf,GAAWkM,EAAYK,kBAAkBvM,GAAW,MAC5C,iBAAXoN,EAAsBA,EAAS,IAI9CG,iBAAiBH,EAAQM,EAAczF,KAAKwF,YAAYR,aACtD,IAAK,MAAMU,KAAY5G,OAAOqC,KAAKsE,GAAc,CAC/C,MAAME,EAAgBF,EAAYC,GAC5BlC,EAAQ2B,EAAOO,GACfE,EAAY9M,EAAU0K,GAAS,UJzCrCzK,OADSA,EI0C+CyK,GJxClD,GAAEzK,IAGL+F,OAAO+G,UAAUpC,SAAS1D,KAAKhH,GAAQ+M,MAAM,eAAe,GAAG9B,cIuClE,IAAK,IAAI+B,OAAOJ,GAAeK,KAAKJ,GAClC,MAAM,IAAIK,UACP,GAAEjG,KAAKwF,YAAY/J,KAAKyK,0BAA0BR,qBAA4BE,yBAAiCD,OJ9C3G5M,IAAAA,GKUf,MAAMoN,UAAsBrB,EAC1BU,YAAYzN,EAASoN,GACnBiB,SAEArO,EAAUmB,EAAWnB,MAKrBiI,KAAKqG,SAAWtO,EAChBiI,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAE/BvC,EAAKC,IAAI7C,KAAKqG,SAAUrG,KAAKwF,YAAYe,SAAUvG,OAIrDwG,UACE5D,EAAKS,OAAOrD,KAAKqG,SAAUrG,KAAKwF,YAAYe,UAC5ChG,EAAaC,IAAIR,KAAKqG,SAAUrG,KAAKwF,YAAYiB,WAEjD,IAAK,MAAMC,KAAgB5H,OAAO6H,oBAAoB3G,MACpDA,KAAK0G,GAAgB,KAIzBE,eAAetL,EAAUvD,EAAS8O,GAAa,GAC7C1K,EAAuBb,EAAUvD,EAAS8O,GAG5C3B,WAAWC,GAIT,OAHAA,EAASnF,KAAKoF,gBAAgBD,EAAQnF,KAAKqG,UAC3ClB,EAASnF,KAAKqF,kBAAkBF,GAChCnF,KAAKsF,iBAAiBH,GACfA,EAIS2B,mBAAC/O,GACjB,OAAO6K,EAAKJ,IAAItJ,EAAWnB,GAAUiI,KAAKuG,UAGlBO,2BAAC/O,EAASoN,EAAS,IAC3C,OAAOnF,KAAK+G,YAAYhP,IAAY,IAAIiI,KAAKjI,EAA2B,iBAAXoN,EAAsBA,EAAS,MAGnF6B,qBACT,MApDY,cAuDHT,sBACT,MAAQ,MAAKvG,KAAKvE,OAGTgL,uBACT,MAAQ,IAAGzG,KAAKuG,WAGFO,iBAACtL,GACf,MAAQ,GAAEA,IAAOwE,KAAKyG,aCtE1B,MAAMQ,EAAuB,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAc,gBAAeF,EAAUT,YACvCjL,EAAO0L,EAAUzL,KAEvB8E,EAAaa,GAAG5I,SAAU4O,EAAa,qBAAoB5L,OAAU,SAAUyD,GAK7E,GAJI,CAAC,IAAK,QAAQ9G,SAAS6H,KAAKqH,UAC9BpI,EAAMwD,iBAGJ5I,EAAWmG,MACb,OAGF,MAAMhD,EAAStE,EAAuBsH,OAASA,KAAKtG,QAAS,IAAG8B,KAC/C0L,EAAUI,oBAAoBtK,GAGtCmK,SCEb,MAAMI,UAAcpB,EAEP1K,kBACT,MAhBS,QAoBX+L,QAGE,GAFmBjH,EAAaoB,QAAQ3B,KAAKqG,SAjB5B,kBAmBFrE,iBACb,OAGFhC,KAAKqG,SAASrM,UAAUqJ,OApBJ,QAsBpB,MAAMwD,EAAa7G,KAAKqG,SAASrM,UAAUC,SAvBvB,QAwBpB+F,KAAK4G,gBAAe,IAAM5G,KAAKyH,mBAAmBzH,KAAKqG,SAAUQ,GAInEY,kBACEzH,KAAKqG,SAAShD,SACd9C,EAAaoB,QAAQ3B,KAAKqG,SA/BR,mBAgClBrG,KAAKwG,UAIeM,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOJ,EAAMD,oBAAoBtH,MAEvC,GAAsB,iBAAXmF,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQnF,WASnBiH,EAAqBM,EAAO,SAM5BnM,EAAmBmM,GCrEnB,MAMMM,EAAuB,4BAO7B,MAAMC,UAAe3B,EAER1K,kBACT,MAhBS,SAoBXsM,SAEE/H,KAAKqG,SAASlC,aAAa,eAAgBnE,KAAKqG,SAASrM,UAAU+N,OAjB7C,WAqBFjB,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOG,EAAOR,oBAAoBtH,MAEzB,WAAXmF,GACFwC,EAAKxC,SAUb5E,EAAaa,GAAG5I,SAlCc,2BAkCkBqP,GAAsB5I,IACpEA,EAAMwD,iBAEN,MAAMuF,EAAS/I,EAAMjC,OAAOtD,QAAQmO,GACvBC,EAAOR,oBAAoBU,GAEnCD,YAOP3M,EAAmB0M,GCxDnB,MAAMG,EAAiB,CACrBjJ,KAAI,CAAChH,EAAUD,EAAUS,SAAS6B,kBACzB,GAAG6N,UAAUC,QAAQtC,UAAUxF,iBAAiBN,KAAKhI,EAASC,IAGvEoQ,QAAO,CAACpQ,EAAUD,EAAUS,SAAS6B,kBAC5B8N,QAAQtC,UAAUpN,cAAcsH,KAAKhI,EAASC,GAGvDqQ,SAAQ,CAACtQ,EAASC,IACT,GAAGkQ,UAAUnQ,EAAQsQ,UAAU3D,QAAO4D,GAASA,EAAMC,QAAQvQ,KAGtEwQ,QAAQzQ,EAASC,GACf,MAAMwQ,EAAU,GAChB,IAAIC,EAAW1Q,EAAQ6B,WAAWF,QAAQ1B,GAE1C,KAAOyQ,GACLD,EAAQvM,KAAKwM,GACbA,EAAWA,EAAS7O,WAAWF,QAAQ1B,GAGzC,OAAOwQ,GAGTE,KAAK3Q,EAASC,GACZ,IAAI2Q,EAAW5Q,EAAQ6Q,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQvQ,GACnB,MAAO,CAAC2Q,GAGVA,EAAWA,EAASC,uBAGtB,MAAO,IAGTC,KAAK9Q,EAASC,GACZ,IAAI6Q,EAAO9Q,EAAQ+Q,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQvQ,GACf,MAAO,CAAC6Q,GAGVA,EAAOA,EAAKC,mBAGd,MAAO,IAGTC,kBAAkBhR,GAChB,MAAMiR,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAC,KAAIjR,GAAa,GAAEA,2BAAiCkR,KAAK,KAE3D,OAAOlJ,KAAKhB,KAAKgK,EAAYjR,GAAS2M,QAAOyE,IAAOtP,EAAWsP,IAAO/P,EAAU+P,OCnD9EpE,EAAU,CACdqE,aAAc,KACdC,cAAe,KACfC,YAAa,MAGTtE,EAAc,CAClBoE,aAAc,kBACdC,cAAe,kBACfC,YAAa,mBAOf,MAAMC,UAAczE,EAClBU,YAAYzN,EAASoN,GACnBiB,QACApG,KAAKqG,SAAWtO,EAEXA,GAAYwR,EAAMC,gBAIvBxJ,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAC/BnF,KAAKyJ,QAAU,EACfzJ,KAAK0J,sBAAwB5I,QAAQhG,OAAO6O,cAC5C3J,KAAK4J,eAII7E,qBACT,OAAOA,EAGEC,yBACT,OAAOA,EAGEvJ,kBACT,MArDS,QAyDX+K,UACEjG,EAAaC,IAAIR,KAAKqG,SAzDR,aA6DhBwD,OAAO5K,GACAe,KAAK0J,sBAMN1J,KAAK8J,wBAAwB7K,KAC/Be,KAAKyJ,QAAUxK,EAAM8K,SANrB/J,KAAKyJ,QAAUxK,EAAM+K,QAAQ,GAAGD,QAUpCE,KAAKhL,GACCe,KAAK8J,wBAAwB7K,KAC/Be,KAAKyJ,QAAUxK,EAAM8K,QAAU/J,KAAKyJ,SAGtCzJ,KAAKkK,eACLhO,EAAQ8D,KAAKsG,QAAQgD,aAGvBa,MAAMlL,GACJe,KAAKyJ,QAAUxK,EAAM+K,SAAW/K,EAAM+K,QAAQ7Q,OAAS,EACrD,EACA8F,EAAM+K,QAAQ,GAAGD,QAAU/J,KAAKyJ,QAGpCS,eACE,MAAME,EAAYzM,KAAK0M,IAAIrK,KAAKyJ,SAEhC,GAAIW,GAlFgB,GAmFlB,OAGF,MAAME,EAAYF,EAAYpK,KAAKyJ,QAEnCzJ,KAAKyJ,QAAU,EAEVa,GAILpO,EAAQoO,EAAY,EAAItK,KAAKsG,QAAQ+C,cAAgBrJ,KAAKsG,QAAQ8C,cAGpEQ,cACM5J,KAAK0J,uBACPnJ,EAAaa,GAAGpB,KAAKqG,SAxGA,wBAwG6BpH,GAASe,KAAK6J,OAAO5K,KACvEsB,EAAaa,GAAGpB,KAAKqG,SAxGF,sBAwG6BpH,GAASe,KAAKiK,KAAKhL,KAEnEe,KAAKqG,SAASrM,UAAUuQ,IAvGG,mBAyG3BhK,EAAaa,GAAGpB,KAAKqG,SAhHD,uBAgH6BpH,GAASe,KAAK6J,OAAO5K,KACtEsB,EAAaa,GAAGpB,KAAKqG,SAhHF,sBAgH6BpH,GAASe,KAAKmK,MAAMlL,KACpEsB,EAAaa,GAAGpB,KAAKqG,SAhHH,qBAgH6BpH,GAASe,KAAKiK,KAAKhL,MAItE6K,wBAAwB7K,GACtB,OAAOe,KAAK0J,wBAjHS,QAiHiBzK,EAAMuL,aAlHrB,UAkHyDvL,EAAMuL,aAItE1D,qBAChB,MAAO,iBAAkBtO,SAAS6B,iBAAmBoQ,UAAUC,eAAiB,GCnHpF,MASMC,EAAa,OACbC,GAAa,OACbC,GAAiB,OACjBC,GAAkB,QAGlBC,GAAc,mBAQdC,GAAsB,WACtBC,GAAoB,SAepBC,GAAmB,CACvBC,UAAkBL,GAClBM,WAAmBP,IAGf9F,GAAU,CACdsG,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGF1G,GAAc,CAClBqG,SAAU,mBACVC,SAAU,UACVE,KAAM,mBACND,MAAO,mBACPE,MAAO,UACPC,KAAM,WAOR,MAAMC,WAAiBxF,EACrBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAK4L,UAAY,KACjB5L,KAAK6L,eAAiB,KACtB7L,KAAK8L,YAAa,EAClB9L,KAAK+L,aAAe,KACpB/L,KAAKgM,aAAe,KAEpBhM,KAAKiM,mBAAqBhE,EAAeG,QAzCjB,uBAyC8CpI,KAAKqG,UAC3ErG,KAAKkM,qBAEDlM,KAAKsG,QAAQkF,OAASR,IACxBhL,KAAKmM,QAKEpH,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA9FS,WAkGXoN,OACE7I,KAAKoM,OAAOzB,GAGd0B,mBAIO7T,SAAS8T,QAAUlT,EAAU4G,KAAKqG,WACrCrG,KAAK6I,OAITH,OACE1I,KAAKoM,OAAOxB,IAGdW,QACMvL,KAAK8L,YACPnT,EAAqBqH,KAAKqG,UAG5BrG,KAAKuM,iBAGPJ,QACEnM,KAAKuM,iBACLvM,KAAKwM,kBAELxM,KAAK4L,UAAYa,aAAY,IAAMzM,KAAKqM,mBAAmBrM,KAAKsG,QAAQ+E,UAG1EqB,oBACO1M,KAAKsG,QAAQkF,OAIdxL,KAAK8L,WACPvL,EAAac,IAAIrB,KAAKqG,SAAU0E,IAAY,IAAM/K,KAAKmM,UAIzDnM,KAAKmM,SAGPQ,GAAGlP,GACD,MAAMmP,EAAQ5M,KAAK6M,YACnB,GAAIpP,EAAQmP,EAAMzT,OAAS,GAAKsE,EAAQ,EACtC,OAGF,GAAIuC,KAAK8L,WAEP,YADAvL,EAAac,IAAIrB,KAAKqG,SAAU0E,IAAY,IAAM/K,KAAK2M,GAAGlP,KAI5D,MAAMqP,EAAc9M,KAAK+M,cAAc/M,KAAKgN,cAC5C,GAAIF,IAAgBrP,EAClB,OAGF,MAAMwP,EAAQxP,EAAQqP,EAAcnC,EAAaC,GAEjD5K,KAAKoM,OAAOa,EAAOL,EAAMnP,IAG3B+I,UACMxG,KAAKgM,cACPhM,KAAKgM,aAAaxF,UAGpBJ,MAAMI,UAIRnB,kBAAkBF,GAEhB,OADAA,EAAO+H,gBAAkB/H,EAAOkG,SACzBlG,EAGT+G,qBACMlM,KAAKsG,QAAQgF,UACf/K,EAAaa,GAAGpB,KAAKqG,SApKJ,uBAoK6BpH,GAASe,KAAKmN,SAASlO,KAG5C,UAAvBe,KAAKsG,QAAQiF,QACfhL,EAAaa,GAAGpB,KAAKqG,SAvKD,0BAuK6B,IAAMrG,KAAKuL,UAC5DhL,EAAaa,GAAGpB,KAAKqG,SAvKD,0BAuK6B,IAAMrG,KAAK0M,uBAG1D1M,KAAKsG,QAAQmF,OAASlC,EAAMC,eAC9BxJ,KAAKoN,0BAITA,0BACE,IAAK,MAAMC,KAAOpF,EAAejJ,KAhKX,qBAgKmCgB,KAAKqG,UAC5D9F,EAAaa,GAAGiM,EAhLI,yBAgLmBpO,GAASA,EAAMwD,mBAGxD,MAqBM6K,EAAc,CAClBlE,aAAc,IAAMpJ,KAAKoM,OAAOpM,KAAKuN,kBAAkB1C,KACvDxB,cAAe,IAAMrJ,KAAKoM,OAAOpM,KAAKuN,kBAAkBzC,KACxDxB,YAxBkB,KACS,UAAvBtJ,KAAKsG,QAAQiF,QAYjBvL,KAAKuL,QACDvL,KAAK+L,cACPyB,aAAaxN,KAAK+L,cAGpB/L,KAAK+L,aAAe7O,YAAW,IAAM8C,KAAK0M,qBAjNjB,IAiN+D1M,KAAKsG,QAAQ+E,aASvGrL,KAAKgM,aAAe,IAAIzC,EAAMvJ,KAAKqG,SAAUiH,GAG/CH,SAASlO,GACP,GAAI,kBAAkB+G,KAAK/G,EAAMjC,OAAOqK,SACtC,OAGF,MAAMiD,EAAYY,GAAiBjM,EAAMqD,KACrCgI,IACFrL,EAAMwD,iBACNzC,KAAKoM,OAAOpM,KAAKuN,kBAAkBjD,KAIvCyC,cAAchV,GACZ,OAAOiI,KAAK6M,YAAYnP,QAAQ3F,GAGlC0V,2BAA2BhQ,GACzB,IAAKuC,KAAKiM,mBACR,OAGF,MAAMyB,EAAkBzF,EAAeG,QA1NnB,UA0N4CpI,KAAKiM,oBAErEyB,EAAgB1T,UAAUqJ,OAAO4H,IACjCyC,EAAgBrJ,gBAAgB,gBAEhC,MAAMsJ,EAAqB1F,EAAeG,QAAS,sBAAqB3K,MAAWuC,KAAKiM,oBAEpF0B,IACFA,EAAmB3T,UAAUuQ,IAAIU,IACjC0C,EAAmBxJ,aAAa,eAAgB,SAIpDqI,kBACE,MAAMzU,EAAUiI,KAAK6L,gBAAkB7L,KAAKgN,aAE5C,IAAKjV,EACH,OAGF,MAAM6V,EAAkBlR,OAAOmR,SAAS9V,EAAQE,aAAa,oBAAqB,IAElF+H,KAAKsG,QAAQ+E,SAAWuC,GAAmB5N,KAAKsG,QAAQ4G,gBAG1Dd,OAAOa,EAAOlV,EAAU,MACtB,GAAIiI,KAAK8L,WACP,OAGF,MAAMzO,EAAgB2C,KAAKgN,aACrBc,EAASb,IAAUtC,EACnBoD,EAAchW,GAAWoF,EAAqB6C,KAAK6M,YAAaxP,EAAeyQ,EAAQ9N,KAAKsG,QAAQoF,MAE1G,GAAIqC,IAAgB1Q,EAClB,OAGF,MAAM2Q,EAAmBhO,KAAK+M,cAAcgB,GAEtCE,EAAeC,GACZ3N,EAAaoB,QAAQ3B,KAAKqG,SAAU6H,EAAW,CACpDrO,cAAekO,EACfzD,UAAWtK,KAAKmO,kBAAkBlB,GAClC7J,KAAMpD,KAAK+M,cAAc1P,GACzBsP,GAAIqB,IAMR,GAFmBC,EA5RF,qBA8RFjM,iBACb,OAGF,IAAK3E,IAAkB0Q,EAGrB,OAGF,MAAMK,EAAYtN,QAAQd,KAAK4L,WAC/B5L,KAAKuL,QAELvL,KAAK8L,YAAa,EAElB9L,KAAKyN,2BAA2BO,GAChChO,KAAK6L,eAAiBkC,EAEtB,MAAMM,EAAuBP,EAnSR,sBADF,oBAqSbQ,EAAiBR,EAnSH,qBACA,qBAoSpBC,EAAY/T,UAAUuQ,IAAI+D,GAE1B3T,EAAOoT,GAEP1Q,EAAcrD,UAAUuQ,IAAI8D,GAC5BN,EAAY/T,UAAUuQ,IAAI8D,GAa1BrO,KAAK4G,gBAXoB,KACvBmH,EAAY/T,UAAUqJ,OAAOgL,EAAsBC,GACnDP,EAAY/T,UAAUuQ,IAAIU,IAE1B5N,EAAcrD,UAAUqJ,OAAO4H,GAAmBqD,EAAgBD,GAElErO,KAAK8L,YAAa,EAElBmC,EAAalD,MAGuB1N,EAAe2C,KAAKuO,eAEtDH,GACFpO,KAAKmM,QAIToC,cACE,OAAOvO,KAAKqG,SAASrM,UAAUC,SAlUV,SAqUvB+S,aACE,OAAO/E,EAAeG,QA9TGoG,wBA8T2BxO,KAAKqG,UAG3DwG,YACE,OAAO5E,EAAejJ,KAnUJ,iBAmUwBgB,KAAKqG,UAGjDkG,iBACMvM,KAAK4L,YACP6C,cAAczO,KAAK4L,WACnB5L,KAAK4L,UAAY,MAIrB2B,kBAAkBjD,GAChB,OAAIpP,IACKoP,IAAcO,GAAiBD,GAAaD,EAG9CL,IAAcO,GAAiBF,EAAaC,GAGrDuD,kBAAkBlB,GAChB,OAAI/R,IACK+R,IAAUrC,GAAaC,GAAiBC,GAG1CmC,IAAUrC,GAAaE,GAAkBD,GAI5B/D,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOgE,GAASrE,oBAAoBtH,KAAMmF,GAEhD,GAAsB,iBAAXA,GAKX,GAAsB,iBAAXA,EAAqB,CAC9B,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,WATLwC,EAAKgF,GAAGxH,OAmBhB5E,EAAaa,GAAG5I,SAjYc,6BAeF,uCAkXyC,SAAUyG,GAC7E,MAAMjC,EAAStE,EAAuBsH,MAEtC,IAAKhD,IAAWA,EAAOhD,UAAUC,SAAS+Q,IACxC,OAGF/L,EAAMwD,iBAEN,MAAMiM,EAAW/C,GAASrE,oBAAoBtK,GACxC2R,EAAa3O,KAAK/H,aAAa,oBAErC,OAAI0W,GACFD,EAAS/B,GAAGgC,QACZD,EAAShC,qBAIyC,SAAhDzI,EAAYY,iBAAiB7E,KAAM,UACrC0O,EAAS7F,YACT6F,EAAShC,sBAIXgC,EAAShG,YACTgG,EAAShC,wBAGXnM,EAAaa,GAAGtG,OA9Za,6BA8ZgB,KAC3C,MAAM8T,EAAY3G,EAAejJ,KA9YR,6BAgZzB,IAAK,MAAM0P,KAAYE,EACrBjD,GAASrE,oBAAoBoH,MAQjCtT,EAAmBuQ,IClcnB,MAWMkD,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aASxBlH,GAAuB,8BAEvB9C,GAAU,CACdgD,QAAQ,EACRiH,OAAQ,MAGJhK,GAAc,CAClB+C,OAAQ,UACRiH,OAAQ,kBAOV,MAAMC,WAAiB9I,EACrBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAKkP,kBAAmB,EACxBlP,KAAKmP,cAAgB,GAErB,MAAMC,EAAanH,EAAejJ,KAAK6I,IAEvC,IAAK,MAAMwH,KAAQD,EAAY,CAC7B,MAAMpX,EAAWO,EAAuB8W,GAClCC,EAAgBrH,EAAejJ,KAAKhH,GACvC0M,QAAO6K,GAAgBA,IAAiBvP,KAAKqG,WAE/B,OAAbrO,GAAqBsX,EAAcnW,QACrC6G,KAAKmP,cAAclT,KAAKoT,GAI5BrP,KAAKwP,sBAEAxP,KAAKsG,QAAQ0I,QAChBhP,KAAKyP,0BAA0BzP,KAAKmP,cAAenP,KAAK0P,YAGtD1P,KAAKsG,QAAQyB,QACf/H,KAAK+H,SAKEhD,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA9ES,WAkFXsM,SACM/H,KAAK0P,WACP1P,KAAK2P,OAEL3P,KAAK4P,OAITA,OACE,GAAI5P,KAAKkP,kBAAoBlP,KAAK0P,WAChC,OAGF,IAAIG,EAAiB,GASrB,GANI7P,KAAKsG,QAAQ0I,SACfa,EAAiB7P,KAAK8P,uBA9EH,wCA+EhBpL,QAAO3M,GAAWA,IAAYiI,KAAKqG,WACnC4C,KAAIlR,GAAWkX,GAAS3H,oBAAoBvP,EAAS,CAAEgQ,QAAQ,OAGhE8H,EAAe1W,QAAU0W,EAAe,GAAGX,iBAC7C,OAIF,GADmB3O,EAAaoB,QAAQ3B,KAAKqG,SAvG7B,oBAwGDrE,iBACb,OAGF,IAAK,MAAM+N,KAAkBF,EAC3BE,EAAeJ,OAGjB,MAAMK,EAAYhQ,KAAKiQ,gBAEvBjQ,KAAKqG,SAASrM,UAAUqJ,OAAOyL,IAC/B9O,KAAKqG,SAASrM,UAAUuQ,IAAIwE,IAE5B/O,KAAKqG,SAAS6J,MAAMF,GAAa,EAEjChQ,KAAKyP,0BAA0BzP,KAAKmP,eAAe,GACnDnP,KAAKkP,kBAAmB,EAExB,MAYMiB,EAAc,SADSH,EAAU,GAAG9J,cAAgB8J,EAAUvO,MAAM,KAG1EzB,KAAK4G,gBAdY,KACf5G,KAAKkP,kBAAmB,EAExBlP,KAAKqG,SAASrM,UAAUqJ,OAAO0L,IAC/B/O,KAAKqG,SAASrM,UAAUuQ,IAAIuE,GAAqBD,IAEjD7O,KAAKqG,SAAS6J,MAAMF,GAAa,GAEjCzP,EAAaoB,QAAQ3B,KAAKqG,SAjIX,uBAuIarG,KAAKqG,UAAU,GAC7CrG,KAAKqG,SAAS6J,MAAMF,GAAc,GAAEhQ,KAAKqG,SAAS8J,OAGpDR,OACE,GAAI3P,KAAKkP,mBAAqBlP,KAAK0P,WACjC,OAIF,GADmBnP,EAAaoB,QAAQ3B,KAAKqG,SA/I7B,oBAgJDrE,iBACb,OAGF,MAAMgO,EAAYhQ,KAAKiQ,gBAEvBjQ,KAAKqG,SAAS6J,MAAMF,GAAc,GAAEhQ,KAAKqG,SAAS+J,wBAAwBJ,OAE1ErV,EAAOqF,KAAKqG,UAEZrG,KAAKqG,SAASrM,UAAUuQ,IAAIwE,IAC5B/O,KAAKqG,SAASrM,UAAUqJ,OAAOyL,GAAqBD,IAEpD,IAAK,MAAMlN,KAAW3B,KAAKmP,cAAe,CACxC,MAAMpX,EAAUW,EAAuBiJ,GAEnC5J,IAAYiI,KAAK0P,SAAS3X,IAC5BiI,KAAKyP,0BAA0B,CAAC9N,IAAU,GAI9C3B,KAAKkP,kBAAmB,EASxBlP,KAAKqG,SAAS6J,MAAMF,GAAa,GAEjChQ,KAAK4G,gBATY,KACf5G,KAAKkP,kBAAmB,EACxBlP,KAAKqG,SAASrM,UAAUqJ,OAAO0L,IAC/B/O,KAAKqG,SAASrM,UAAUuQ,IAAIuE,IAC5BvO,EAAaoB,QAAQ3B,KAAKqG,SA1KV,wBA+KYrG,KAAKqG,UAAU,GAG/CqJ,SAAS3X,EAAUiI,KAAKqG,UACtB,OAAOtO,EAAQiC,UAAUC,SAAS4U,IAIpCxJ,kBAAkBF,GAGhB,OAFAA,EAAO4C,OAASjH,QAAQqE,EAAO4C,QAC/B5C,EAAO6J,OAAS9V,EAAWiM,EAAO6J,QAC3B7J,EAGT8K,gBACE,OAAOjQ,KAAKqG,SAASrM,UAAUC,SAtLL,uBAEhB,QACC,SAsLbuV,sBACE,IAAKxP,KAAKsG,QAAQ0I,OAChB,OAGF,MAAM3G,EAAWrI,KAAK8P,uBAAuBjI,IAE7C,IAAK,MAAM9P,KAAWsQ,EAAU,CAC9B,MAAMgI,EAAW3X,EAAuBX,GAEpCsY,GACFrQ,KAAKyP,0BAA0B,CAAC1X,GAAUiI,KAAK0P,SAASW,KAK9DP,uBAAuB9X,GACrB,MAAMqQ,EAAWJ,EAAejJ,KA3MA,6BA2MiCgB,KAAKsG,QAAQ0I,QAE9E,OAAO/G,EAAejJ,KAAKhH,EAAUgI,KAAKsG,QAAQ0I,QAAQtK,QAAO3M,IAAYsQ,EAASlQ,SAASJ,KAGjG0X,0BAA0Ba,EAAcC,GACtC,GAAKD,EAAanX,OAIlB,IAAK,MAAMpB,KAAWuY,EACpBvY,EAAQiC,UAAU+N,OAvNK,aAuNyBwI,GAChDxY,EAAQoM,aAAa,gBAAiBoM,GAKpBzJ,uBAAC3B,GACrB,MAAMmB,EAAU,GAKhB,MAJsB,iBAAXnB,GAAuB,YAAYa,KAAKb,KACjDmB,EAAQyB,QAAS,GAGZ/H,KAAK0H,MAAK,WACf,MAAMC,EAAOsH,GAAS3H,oBAAoBtH,KAAMsG,GAEhD,GAAsB,iBAAXnB,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UAUb5E,EAAaa,GAAG5I,SA1Pc,6BA0PkBqP,IAAsB,SAAU5I,IAEjD,MAAzBA,EAAMjC,OAAOqK,SAAoBpI,EAAMa,gBAAmD,MAAjCb,EAAMa,eAAeuH,UAChFpI,EAAMwD,iBAGR,MAAMzK,EAAWO,EAAuByH,MAClCwQ,EAAmBvI,EAAejJ,KAAKhH,GAE7C,IAAK,MAAMD,KAAWyY,EACpBvB,GAAS3H,oBAAoBvP,EAAS,CAAEgQ,QAAQ,IAASA,YAQ7D3M,EAAmB6T,IChRnB,MAAMxT,GAAO,WAOPgV,GAAe,UACfC,GAAiB,YAOjBC,GAAwB,6BACxBC,GAA0B,+BAG1B/B,GAAkB,OAOlBhH,GAAuB,4DACvBgJ,GAA8B,GAAEhJ,UAChCiJ,GAAgB,iBAKhBC,GAAgB7V,IAAU,UAAY,YACtC8V,GAAmB9V,IAAU,YAAc,UAC3C+V,GAAmB/V,IAAU,aAAe,eAC5CgW,GAAsBhW,IAAU,eAAiB,aACjDiW,GAAkBjW,IAAU,aAAe,cAC3CkW,GAAiBlW,IAAU,cAAgB,aAI3C6J,GAAU,CACdsM,OAAQ,CAAC,EAAG,GACZC,SAAU,kBACVC,UAAW,SACXC,QAAS,UACTC,aAAc,KACdC,WAAW,GAGP1M,GAAc,CAClBqM,OAAQ,0BACRC,SAAU,mBACVC,UAAW,0BACXC,QAAS,SACTC,aAAc,yBACdC,UAAW,oBAOb,MAAMC,WAAiBxL,EACrBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAK4R,QAAU,KACf5R,KAAK6R,QAAU7R,KAAKqG,SAASzM,WAC7BoG,KAAK8R,MAAQ7J,EAAeG,QAAQ0I,GAAe9Q,KAAK6R,SACxD7R,KAAK+R,UAAY/R,KAAKgS,gBAIbjN,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,OAAOA,GAITsM,SACE,OAAO/H,KAAK0P,WAAa1P,KAAK2P,OAAS3P,KAAK4P,OAG9CA,OACE,GAAI/V,EAAWmG,KAAKqG,WAAarG,KAAK0P,WACpC,OAGF,MAAM7P,EAAgB,CACpBA,cAAeG,KAAKqG,UAKtB,IAFkB9F,EAAaoB,QAAQ3B,KAAKqG,SAxF5B,mBAwFkDxG,GAEpDmC,iBAAd,CAUA,GANAhC,KAAKiS,gBAMD,iBAAkBzZ,SAAS6B,kBAAoB2F,KAAK6R,QAAQnY,QAnFxC,eAoFtB,IAAK,MAAM3B,IAAW,GAAGmQ,UAAU1P,SAASwC,KAAKqN,UAC/C9H,EAAaa,GAAGrJ,EAAS,YAAa2C,GAI1CsF,KAAKqG,SAAS6L,QACdlS,KAAKqG,SAASlC,aAAa,iBAAiB,GAE5CnE,KAAK8R,MAAM9X,UAAUuQ,IAAIsE,IACzB7O,KAAKqG,SAASrM,UAAUuQ,IAAIsE,IAC5BtO,EAAaoB,QAAQ3B,KAAKqG,SA9GT,oBA8GgCxG,IAGnD8P,OACE,GAAI9V,EAAWmG,KAAKqG,YAAcrG,KAAK0P,WACrC,OAGF,MAAM7P,EAAgB,CACpBA,cAAeG,KAAKqG,UAGtBrG,KAAKmS,cAActS,GAGrB2G,UACMxG,KAAK4R,SACP5R,KAAK4R,QAAQQ,UAGfhM,MAAMI,UAGR6L,SACErS,KAAK+R,UAAY/R,KAAKgS,gBAClBhS,KAAK4R,SACP5R,KAAK4R,QAAQS,SAKjBF,cAActS,GAEZ,IADkBU,EAAaoB,QAAQ3B,KAAKqG,SAjJ5B,mBAiJkDxG,GACpDmC,iBAAd,CAMA,GAAI,iBAAkBxJ,SAAS6B,gBAC7B,IAAK,MAAMtC,IAAW,GAAGmQ,UAAU1P,SAASwC,KAAKqN,UAC/C9H,EAAaC,IAAIzI,EAAS,YAAa2C,GAIvCsF,KAAK4R,SACP5R,KAAK4R,QAAQQ,UAGfpS,KAAK8R,MAAM9X,UAAUqJ,OAAOwL,IAC5B7O,KAAKqG,SAASrM,UAAUqJ,OAAOwL,IAC/B7O,KAAKqG,SAASlC,aAAa,gBAAiB,SAC5CF,EAAYG,oBAAoBpE,KAAK8R,MAAO,UAC5CvR,EAAaoB,QAAQ3B,KAAKqG,SArKR,qBAqKgCxG,IAGpDqF,WAAWC,GAGT,GAAgC,iBAFhCA,EAASiB,MAAMlB,WAAWC,IAERoM,YAA2BzY,EAAUqM,EAAOoM,YACV,mBAA3CpM,EAAOoM,UAAUnB,sBAGxB,MAAM,IAAInK,UAAW,GAAExK,GAAKyK,+GAG9B,OAAOf,EAGT8M,gBACE,QAAsB,IAAXK,EACT,MAAM,IAAIrM,UAAU,gEAGtB,IAAIsM,EAAmBvS,KAAKqG,SAEG,WAA3BrG,KAAKsG,QAAQiL,UACfgB,EAAmBvS,KAAK6R,QACf/Y,EAAUkH,KAAKsG,QAAQiL,WAChCgB,EAAmBrZ,EAAW8G,KAAKsG,QAAQiL,WACA,iBAA3BvR,KAAKsG,QAAQiL,YAC7BgB,EAAmBvS,KAAKsG,QAAQiL,WAGlC,MAAME,EAAezR,KAAKwS,mBAC1BxS,KAAK4R,QAAUU,EAAOG,aAAaF,EAAkBvS,KAAK8R,MAAOL,GAGnE/B,WACE,OAAO1P,KAAK8R,MAAM9X,UAAUC,SAAS4U,IAGvC6D,gBACE,MAAMC,EAAiB3S,KAAK6R,QAE5B,GAAIc,EAAe3Y,UAAUC,SAtMN,WAuMrB,OAAOkX,GAGT,GAAIwB,EAAe3Y,UAAUC,SAzMJ,aA0MvB,OAAOmX,GAGT,GAAIuB,EAAe3Y,UAAUC,SA5MA,iBA6M3B,MA7LsB,MAgMxB,GAAI0Y,EAAe3Y,UAAUC,SA/ME,mBAgN7B,MAhMyB,SAoM3B,MAAM2Y,EAAkF,QAA1ErZ,iBAAiByG,KAAK8R,OAAOtY,iBAAiB,iBAAiBlB,OAE7E,OAAIqa,EAAe3Y,UAAUC,SA1NP,UA2Nb2Y,EAAQ5B,GAAmBD,GAG7B6B,EAAQ1B,GAAsBD,GAGvCe,gBACE,OAAkD,OAA3ChS,KAAKqG,SAAS3M,QAzND,WA4NtBmZ,aACE,MAAMxB,OAAEA,GAAWrR,KAAKsG,QAExB,MAAsB,iBAAX+K,EACFA,EAAOhZ,MAAM,KAAK4Q,KAAIzF,GAAS9G,OAAOmR,SAASrK,EAAO,MAGzC,mBAAX6N,EACFyB,GAAczB,EAAOyB,EAAY9S,KAAKqG,UAGxCgL,EAGTmB,mBACE,MAAMO,EAAwB,CAC5BC,UAAWhT,KAAK0S,gBAChBO,UAAW,CAAC,CACVzX,KAAM,kBACN0X,QAAS,CACP5B,SAAUtR,KAAKsG,QAAQgL,WAG3B,CACE9V,KAAM,SACN0X,QAAS,CACP7B,OAAQrR,KAAK6S,iBAcnB,OARI7S,KAAK+R,WAAsC,WAAzB/R,KAAKsG,QAAQkL,WACjCvN,EAAYC,iBAAiBlE,KAAK8R,MAAO,SAAU,UACnDiB,EAAsBE,UAAY,CAAC,CACjCzX,KAAM,cACN2X,SAAS,KAIN,IACFJ,KACsC,mBAA9B/S,KAAKsG,QAAQmL,aAA8BzR,KAAKsG,QAAQmL,aAAasB,GAAyB/S,KAAKsG,QAAQmL,cAI1H2B,iBAAgB9Q,IAAEA,EAAFtF,OAAOA,IACrB,MAAM4P,EAAQ3E,EAAejJ,KAzQF,8DAyQ+BgB,KAAK8R,OAAOpN,QAAO3M,GAAWqB,EAAUrB,KAE7F6U,EAAMzT,QAMXgE,EAAqByP,EAAO5P,EAAQsF,IAAQoO,IAAiB9D,EAAMzU,SAAS6E,IAASkV,QAIjEpL,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOgK,GAASrK,oBAAoBtH,KAAMmF,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,SAIQ2B,kBAAC7H,GAChB,GA5TuB,IA4TnBA,EAAM+I,QAAiD,UAAf/I,EAAMwB,MA/TtC,QA+T0DxB,EAAMqD,IAC1E,OAGF,MAAM+Q,EAAcpL,EAAejJ,KAAK6R,IAExC,IAAK,MAAM9I,KAAUsL,EAAa,CAChC,MAAMC,EAAU3B,GAAS5K,YAAYgB,GACrC,IAAKuL,IAAyC,IAA9BA,EAAQhN,QAAQoL,UAC9B,SAGF,MAAM6B,EAAetU,EAAMsU,eACrBC,EAAeD,EAAapb,SAASmb,EAAQxB,OACnD,GACEyB,EAAapb,SAASmb,EAAQjN,WACC,WAA9BiN,EAAQhN,QAAQoL,YAA2B8B,GACb,YAA9BF,EAAQhN,QAAQoL,WAA2B8B,EAE5C,SAIF,GAAIF,EAAQxB,MAAM7X,SAASgF,EAAMjC,UAA4B,UAAfiC,EAAMwB,MAtV1C,QAsV8DxB,EAAMqD,KAAoB,qCAAqC0D,KAAK/G,EAAMjC,OAAOqK,UACvJ,SAGF,MAAMxH,EAAgB,CAAEA,cAAeyT,EAAQjN,UAE5B,UAAfpH,EAAMwB,OACRZ,EAAcuH,WAAanI,GAG7BqU,EAAQnB,cAActS,IAIEiH,6BAAC7H,GAI3B,MAAMwU,EAAU,kBAAkBzN,KAAK/G,EAAMjC,OAAOqK,SAC9CqM,EA1WS,WA0WOzU,EAAMqD,IACtBqR,EAAkB,CAAClD,GAAcC,IAAgBvY,SAAS8G,EAAMqD,KAEtE,IAAKqR,IAAoBD,EACvB,OAGF,GAAID,IAAYC,EACd,OAGFzU,EAAMwD,iBAEN,MAAMmR,EAAkB3L,EAAeG,QAAQP,GAAsB5I,EAAMa,eAAelG,YACpFkJ,EAAW6O,GAASrK,oBAAoBsM,GAE9C,GAAID,EAIF,OAHA1U,EAAM4U,kBACN/Q,EAAS8M,YACT9M,EAASsQ,gBAAgBnU,GAIvB6D,EAAS4M,aACXzQ,EAAM4U,kBACN/Q,EAAS6M,OACTiE,EAAgB1B,UAStB3R,EAAaa,GAAG5I,SAAUoY,GAAwB/I,GAAsB8J,GAASmC,uBACjFvT,EAAaa,GAAG5I,SAAUoY,GAAwBE,GAAea,GAASmC,uBAC1EvT,EAAaa,GAAG5I,SAAUmY,GAAsBgB,GAASoC,YACzDxT,EAAaa,GAAG5I,SApYc,6BAoYkBmZ,GAASoC,YACzDxT,EAAaa,GAAG5I,SAAUmY,GAAsB9I,IAAsB,SAAU5I,GAC9EA,EAAMwD,iBACNkP,GAASrK,oBAAoBtH,MAAM+H,YAOrC3M,EAAmBuW,IC3anB,MAAMqC,GAAyB,oDACzBC,GAA0B,cAC1BC,GAAmB,gBACnBC,GAAkB,eAMxB,MAAMC,GACJ5O,cACExF,KAAKqG,SAAW7N,SAASwC,KAI3BqZ,WAEE,MAAMC,EAAgB9b,SAAS6B,gBAAgBka,YAC/C,OAAO5W,KAAK0M,IAAIvP,OAAO0Z,WAAaF,GAGtC3E,OACE,MAAM8E,EAAQzU,KAAKqU,WACnBrU,KAAK0U,mBAEL1U,KAAK2U,sBAAsB3U,KAAKqG,SAAU6N,IAAkBU,GAAmBA,EAAkBH,IAEjGzU,KAAK2U,sBAAsBX,GAAwBE,IAAkBU,GAAmBA,EAAkBH,IAC1GzU,KAAK2U,sBAAsBV,GAAyBE,IAAiBS,GAAmBA,EAAkBH,IAG5GI,QACE7U,KAAK8U,wBAAwB9U,KAAKqG,SAAU,YAC5CrG,KAAK8U,wBAAwB9U,KAAKqG,SAAU6N,IAC5ClU,KAAK8U,wBAAwBd,GAAwBE,IACrDlU,KAAK8U,wBAAwBb,GAAyBE,IAGxDY,gBACE,OAAO/U,KAAKqU,WAAa,EAI3BK,mBACE1U,KAAKgV,sBAAsBhV,KAAKqG,SAAU,YAC1CrG,KAAKqG,SAAS6J,MAAM+E,SAAW,SAGjCN,sBAAsB3c,EAAUkd,EAAe5Z,GAC7C,MAAM6Z,EAAiBnV,KAAKqU,WAW5BrU,KAAKoV,2BAA2Bpd,GAVHD,IAC3B,GAAIA,IAAYiI,KAAKqG,UAAYvL,OAAO0Z,WAAazc,EAAQwc,YAAcY,EACzE,OAGFnV,KAAKgV,sBAAsBjd,EAASmd,GACpC,MAAMN,EAAkB9Z,OAAOvB,iBAAiBxB,GAASyB,iBAAiB0b,GAC1End,EAAQmY,MAAMmF,YAAYH,EAAgB,GAAE5Z,EAASoB,OAAOC,WAAWiY,YAM3EI,sBAAsBjd,EAASmd,GAC7B,MAAMI,EAAcvd,EAAQmY,MAAM1W,iBAAiB0b,GAC/CI,GACFrR,EAAYC,iBAAiBnM,EAASmd,EAAeI,GAIzDR,wBAAwB9c,EAAUkd,GAahClV,KAAKoV,2BAA2Bpd,GAZHD,IAC3B,MAAMyL,EAAQS,EAAYY,iBAAiB9M,EAASmd,GAEtC,OAAV1R,GAKJS,EAAYG,oBAAoBrM,EAASmd,GACzCnd,EAAQmY,MAAMmF,YAAYH,EAAe1R,IALvCzL,EAAQmY,MAAMqF,eAAeL,MAWnCE,2BAA2Bpd,EAAUwd,GACnC,GAAI1c,EAAUd,GACZwd,EAASxd,QAIX,IAAK,MAAMyd,KAAOxN,EAAejJ,KAAKhH,EAAUgI,KAAKqG,UACnDmP,EAASC,IC7Ff,MAEM5G,GAAkB,OAClB6G,GAAmB,wBAEnB3Q,GAAU,CACd4Q,UAAW,iBACXvc,WAAW,EACXyN,YAAY,EACZ+O,YAAa,OACbC,cAAe,MAGX7Q,GAAc,CAClB2Q,UAAW,SACXvc,UAAW,UACXyN,WAAY,UACZ+O,YAAa,mBACbC,cAAe,mBAOjB,MAAMC,WAAiBhR,EACrBU,YAAYL,GACViB,QACApG,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAC/BnF,KAAK+V,aAAc,EACnB/V,KAAKqG,SAAW,KAIPtB,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA3CS,WA+CXmU,KAAKtU,GACH,IAAK0E,KAAKsG,QAAQlN,UAEhB,YADA8C,EAAQZ,GAIV0E,KAAKgW,UAEL,MAAMje,EAAUiI,KAAKiW,cACjBjW,KAAKsG,QAAQO,YACflM,EAAO5C,GAGTA,EAAQiC,UAAUuQ,IAAIsE,IAEtB7O,KAAKkW,mBAAkB,KACrBha,EAAQZ,MAIZqU,KAAKrU,GACE0E,KAAKsG,QAAQlN,WAKlB4G,KAAKiW,cAAcjc,UAAUqJ,OAAOwL,IAEpC7O,KAAKkW,mBAAkB,KACrBlW,KAAKwG,UACLtK,EAAQZ,OARRY,EAAQZ,GAYZkL,UACOxG,KAAK+V,cAIVxV,EAAaC,IAAIR,KAAKqG,SAAUqP,IAEhC1V,KAAKqG,SAAShD,SACdrD,KAAK+V,aAAc,GAIrBE,cACE,IAAKjW,KAAKqG,SAAU,CAClB,MAAM8P,EAAW3d,SAAS4d,cAAc,OACxCD,EAASR,UAAY3V,KAAKsG,QAAQqP,UAC9B3V,KAAKsG,QAAQO,YACfsP,EAASnc,UAAUuQ,IAjGH,QAoGlBvK,KAAKqG,SAAW8P,EAGlB,OAAOnW,KAAKqG,SAGdhB,kBAAkBF,GAGhB,OADAA,EAAOyQ,YAAc1c,EAAWiM,EAAOyQ,aAChCzQ,EAGT6Q,UACE,GAAIhW,KAAK+V,YACP,OAGF,MAAMhe,EAAUiI,KAAKiW,cACrBjW,KAAKsG,QAAQsP,YAAYS,OAAOte,GAEhCwI,EAAaa,GAAGrJ,EAAS2d,IAAiB,KACxCxZ,EAAQ8D,KAAKsG,QAAQuP,kBAGvB7V,KAAK+V,aAAc,EAGrBG,kBAAkB5a,GAChBa,EAAuBb,EAAU0E,KAAKiW,cAAejW,KAAKsG,QAAQO,aCjItE,MAEMJ,GAAa,gBAMb6P,GAAmB,WAEnBvR,GAAU,CACdwR,YAAa,KACbC,WAAW,GAGPxR,GAAc,CAClBuR,YAAa,UACbC,UAAW,WAOb,MAAMC,WAAkB3R,EACtBU,YAAYL,GACViB,QACApG,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAC/BnF,KAAK0W,WAAY,EACjB1W,KAAK2W,qBAAuB,KAInB5R,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA1CS,YA8CXmb,WACM5W,KAAK0W,YAIL1W,KAAKsG,QAAQkQ,WACfxW,KAAKsG,QAAQiQ,YAAYrE,QAG3B3R,EAAaC,IAAIhI,SAAUiO,IAC3BlG,EAAaa,GAAG5I,SArDG,wBAqDsByG,GAASe,KAAK6W,eAAe5X,KACtEsB,EAAaa,GAAG5I,SArDO,4BAqDsByG,GAASe,KAAK8W,eAAe7X,KAE1Ee,KAAK0W,WAAY,GAGnBK,aACO/W,KAAK0W,YAIV1W,KAAK0W,WAAY,EACjBnW,EAAaC,IAAIhI,SAAUiO,KAI7BoQ,eAAe5X,GACb,MAAMsX,YAAEA,GAAgBvW,KAAKsG,QAE7B,GAAIrH,EAAMjC,SAAWxE,UAAYyG,EAAMjC,SAAWuZ,GAAeA,EAAYtc,SAASgF,EAAMjC,QAC1F,OAGF,MAAMga,EAAW/O,EAAec,kBAAkBwN,GAE1B,IAApBS,EAAS7d,OACXod,EAAYrE,QACHlS,KAAK2W,uBAAyBL,GACvCU,EAASA,EAAS7d,OAAS,GAAG+Y,QAE9B8E,EAAS,GAAG9E,QAIhB4E,eAAe7X,GApFD,QAqFRA,EAAMqD,MAIVtC,KAAK2W,qBAAuB1X,EAAMgY,SAAWX,GAxFzB,YCFxB,MAQMY,GAAgB,kBAChBC,GAAc,gBAOdC,GAAkB,aAElBvI,GAAkB,OAClBwI,GAAoB,eAOpBtS,GAAU,CACdoR,UAAU,EACV7K,UAAU,EACV4G,OAAO,GAGHlN,GAAc,CAClBmR,SAAU,mBACV7K,SAAU,UACV4G,MAAO,WAOT,MAAMoF,WAAcnR,EAClBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAKuX,QAAUtP,EAAeG,QAxBV,gBAwBmCpI,KAAKqG,UAC5DrG,KAAKwX,UAAYxX,KAAKyX,sBACtBzX,KAAK0X,WAAa1X,KAAK2X,uBACvB3X,KAAK0P,UAAW,EAChB1P,KAAKkP,kBAAmB,EACxBlP,KAAK4X,WAAa,IAAIxD,GAEtBpU,KAAKkM,qBAIInH,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAlES,QAsEXsM,OAAOlI,GACL,OAAOG,KAAK0P,SAAW1P,KAAK2P,OAAS3P,KAAK4P,KAAK/P,GAGjD+P,KAAK/P,GACCG,KAAK0P,UAAY1P,KAAKkP,kBAIR3O,EAAaoB,QAAQ3B,KAAKqG,SAAU8Q,GAAY,CAChEtX,cAAAA,IAGYmC,mBAIdhC,KAAK0P,UAAW,EAChB1P,KAAKkP,kBAAmB,EAExBlP,KAAK4X,WAAWjI,OAEhBnX,SAASwC,KAAKhB,UAAUuQ,IAAI6M,IAE5BpX,KAAK6X,gBAEL7X,KAAKwX,UAAU5H,MAAK,IAAM5P,KAAK8X,aAAajY,MAG9C8P,OACO3P,KAAK0P,WAAY1P,KAAKkP,mBAIT3O,EAAaoB,QAAQ3B,KAAKqG,SAlG5B,iBAoGFrE,mBAIdhC,KAAK0P,UAAW,EAChB1P,KAAKkP,kBAAmB,EACxBlP,KAAK0X,WAAWX,aAEhB/W,KAAKqG,SAASrM,UAAUqJ,OAAOwL,IAE/B7O,KAAK4G,gBAAe,IAAM5G,KAAK+X,cAAc/X,KAAKqG,SAAUrG,KAAKuO,iBAGnE/H,UACE,IAAK,MAAMwR,IAAe,CAACld,OAAQkF,KAAKuX,SACtChX,EAAaC,IAAIwX,EAvHJ,aA0HfhY,KAAKwX,UAAUhR,UACfxG,KAAK0X,WAAWX,aAChB3Q,MAAMI,UAGRyR,eACEjY,KAAK6X,gBAIPJ,sBACE,OAAO,IAAI3B,GAAS,CAClB1c,UAAW0H,QAAQd,KAAKsG,QAAQ6P,UAChCtP,WAAY7G,KAAKuO,gBAIrBoJ,uBACE,OAAO,IAAIlB,GAAU,CACnBF,YAAavW,KAAKqG,WAItByR,aAAajY,GAENrH,SAASwC,KAAKf,SAAS+F,KAAKqG,WAC/B7N,SAASwC,KAAKqb,OAAOrW,KAAKqG,UAG5BrG,KAAKqG,SAAS6J,MAAMsB,QAAU,QAC9BxR,KAAKqG,SAAShC,gBAAgB,eAC9BrE,KAAKqG,SAASlC,aAAa,cAAc,GACzCnE,KAAKqG,SAASlC,aAAa,OAAQ,UACnCnE,KAAKqG,SAAS6R,UAAY,EAE1B,MAAMC,EAAYlQ,EAAeG,QAxIT,cAwIsCpI,KAAKuX,SAC/DY,IACFA,EAAUD,UAAY,GAGxBvd,EAAOqF,KAAKqG,UAEZrG,KAAKqG,SAASrM,UAAUuQ,IAAIsE,IAa5B7O,KAAK4G,gBAXsB,KACrB5G,KAAKsG,QAAQ4L,OACflS,KAAK0X,WAAWd,WAGlB5W,KAAKkP,kBAAmB,EACxB3O,EAAaoB,QAAQ3B,KAAKqG,SApKX,iBAoKkC,CAC/CxG,cAAAA,MAIoCG,KAAKuX,QAASvX,KAAKuO,eAG7DrC,qBACE3L,EAAaa,GAAGpB,KAAKqG,SA1KM,4BA0K2BpH,IACpD,GApLa,WAoLTA,EAAMqD,IAIV,OAAItC,KAAKsG,QAAQgF,UACfrM,EAAMwD,sBACNzC,KAAK2P,aAIP3P,KAAKoY,gCAGP7X,EAAaa,GAAGtG,OA1LE,mBA0LoB,KAChCkF,KAAK0P,WAAa1P,KAAKkP,kBACzBlP,KAAK6X,mBAITtX,EAAaa,GAAGpB,KAAKqG,SA/LI,0BA+L2BpH,IAC9CA,EAAMjC,SAAWiC,EAAMoZ,gBAIG,WAA1BrY,KAAKsG,QAAQ6P,SAKbnW,KAAKsG,QAAQ6P,UACfnW,KAAK2P,OALL3P,KAAKoY,iCAUXL,aACE/X,KAAKqG,SAAS6J,MAAMsB,QAAU,OAC9BxR,KAAKqG,SAASlC,aAAa,eAAe,GAC1CnE,KAAKqG,SAAShC,gBAAgB,cAC9BrE,KAAKqG,SAAShC,gBAAgB,QAC9BrE,KAAKkP,kBAAmB,EAExBlP,KAAKwX,UAAU7H,MAAK,KAClBnX,SAASwC,KAAKhB,UAAUqJ,OAAO+T,IAC/BpX,KAAKsY,oBACLtY,KAAK4X,WAAW/C,QAChBtU,EAAaoB,QAAQ3B,KAAKqG,SAAU6Q,OAIxC3I,cACE,OAAOvO,KAAKqG,SAASrM,UAAUC,SA1NX,QA6NtBme,6BAEE,GADkB7X,EAAaoB,QAAQ3B,KAAKqG,SAxOlB,0BAyOZrE,iBACZ,OAGF,MAAMuW,EAAqBvY,KAAKqG,SAASmS,aAAehgB,SAAS6B,gBAAgBoe,aAC3EC,EAAmB1Y,KAAKqG,SAAS6J,MAAMyI,UAEpB,WAArBD,GAAiC1Y,KAAKqG,SAASrM,UAAUC,SAASod,MAIjEkB,IACHvY,KAAKqG,SAAS6J,MAAMyI,UAAY,UAGlC3Y,KAAKqG,SAASrM,UAAUuQ,IAAI8M,IAC5BrX,KAAK4G,gBAAe,KAClB5G,KAAKqG,SAASrM,UAAUqJ,OAAOgU,IAC/BrX,KAAK4G,gBAAe,KAClB5G,KAAKqG,SAAS6J,MAAMyI,UAAYD,IAC/B1Y,KAAKuX,WACPvX,KAAKuX,SAERvX,KAAKqG,SAAS6L,SAOhB2F,gBACE,MAAMU,EAAqBvY,KAAKqG,SAASmS,aAAehgB,SAAS6B,gBAAgBoe,aAC3EtD,EAAiBnV,KAAK4X,WAAWvD,WACjCuE,EAAoBzD,EAAiB,EAE3C,GAAIyD,IAAsBL,EAAoB,CAC5C,MAAM7S,EAAWxK,IAAU,cAAgB,eAC3C8E,KAAKqG,SAAS6J,MAAMxK,GAAa,GAAEyP,MAGrC,IAAKyD,GAAqBL,EAAoB,CAC5C,MAAM7S,EAAWxK,IAAU,eAAiB,cAC5C8E,KAAKqG,SAAS6J,MAAMxK,GAAa,GAAEyP,OAIvCmD,oBACEtY,KAAKqG,SAAS6J,MAAM2I,YAAc,GAClC7Y,KAAKqG,SAAS6J,MAAM4I,aAAe,GAIfhS,uBAAC3B,EAAQtF,GAC7B,OAAOG,KAAK0H,MAAK,WACf,MAAMC,EAAO2P,GAAMhQ,oBAAoBtH,KAAMmF,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQtF,QASnBU,EAAaa,GAAG5I,SA3Sc,0BAUD,4BAiSyC,SAAUyG,GAC9E,MAAMjC,EAAStE,EAAuBsH,MAElC,CAAC,IAAK,QAAQ7H,SAAS6H,KAAKqH,UAC9BpI,EAAMwD,iBAGRlC,EAAac,IAAIrE,EAAQma,IAAY4B,IAC/BA,EAAU/W,kBAKdzB,EAAac,IAAIrE,EAAQka,IAAc,KACjC9d,EAAU4G,OACZA,KAAKkS,cAMX,MAAM8G,EAAc/Q,EAAeG,QAzTf,eA0ThB4Q,GACF1B,GAAMvQ,YAAYiS,GAAarJ,OAGpB2H,GAAMhQ,oBAAoBtK,GAElC+K,OAAO/H,SAGdiH,EAAqBqQ,IAMrBlc,EAAmBkc,ICzVnB,MAOMzI,GAAkB,OAClBoK,GAAqB,UACrBC,GAAoB,SAEpBC,GAAgB,kBAKhBC,GAAwB,6BACxBlC,GAAgB,sBAOhBnS,GAAU,CACdoR,UAAU,EACV7K,UAAU,EACV+N,QAAQ,GAGJrU,GAAc,CAClBmR,SAAU,mBACV7K,SAAU,UACV+N,OAAQ,WAOV,MAAMC,WAAkBnT,EACtBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAK0P,UAAW,EAChB1P,KAAKwX,UAAYxX,KAAKyX,sBACtBzX,KAAK0X,WAAa1X,KAAK2X,uBACvB3X,KAAKkM,qBAIInH,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA5DS,YAgEXsM,OAAOlI,GACL,OAAOG,KAAK0P,SAAW1P,KAAK2P,OAAS3P,KAAK4P,KAAK/P,GAGjD+P,KAAK/P,GACCG,KAAK0P,UAISnP,EAAaoB,QAAQ3B,KAAKqG,SA5D5B,oBA4DkD,CAAExG,cAAAA,IAEtDmC,mBAIdhC,KAAK0P,UAAW,EAChB1P,KAAKwX,UAAU5H,OAEV5P,KAAKsG,QAAQ+S,SAChB,IAAIjF,IAAkBzE,OAGxB3P,KAAKqG,SAASlC,aAAa,cAAc,GACzCnE,KAAKqG,SAASlC,aAAa,OAAQ,UACnCnE,KAAKqG,SAASrM,UAAUuQ,IAAI0O,IAY5BjZ,KAAK4G,gBAVoB,KAClB5G,KAAKsG,QAAQ+S,QAChBrZ,KAAK0X,WAAWd,WAGlB5W,KAAKqG,SAASrM,UAAUuQ,IAAIsE,IAC5B7O,KAAKqG,SAASrM,UAAUqJ,OAAO4V,IAC/B1Y,EAAaoB,QAAQ3B,KAAKqG,SAnFX,qBAmFkC,CAAExG,cAAAA,MAGfG,KAAKqG,UAAU,IAGvDsJ,OACO3P,KAAK0P,WAIQnP,EAAaoB,QAAQ3B,KAAKqG,SA7F5B,qBA+FFrE,mBAIdhC,KAAK0X,WAAWX,aAChB/W,KAAKqG,SAASkT,OACdvZ,KAAK0P,UAAW,EAChB1P,KAAKqG,SAASrM,UAAUuQ,IAAI2O,IAC5BlZ,KAAKwX,UAAU7H,OAcf3P,KAAK4G,gBAZoB,KACvB5G,KAAKqG,SAASrM,UAAUqJ,OAAOwL,GAAiBqK,IAChDlZ,KAAKqG,SAAShC,gBAAgB,cAC9BrE,KAAKqG,SAAShC,gBAAgB,QAEzBrE,KAAKsG,QAAQ+S,SAChB,IAAIjF,IAAkBS,QAGxBtU,EAAaoB,QAAQ3B,KAAKqG,SAAU6Q,MAGAlX,KAAKqG,UAAU,KAGvDG,UACExG,KAAKwX,UAAUhR,UACfxG,KAAK0X,WAAWX,aAChB3Q,MAAMI,UAIRiR,sBACE,MAUMre,EAAY0H,QAAQd,KAAKsG,QAAQ6P,UAEvC,OAAO,IAAIL,GAAS,CAClBH,UAlJsB,qBAmJtBvc,UAAAA,EACAyN,YAAY,EACZ+O,YAAa5V,KAAKqG,SAASzM,WAC3Bic,cAAezc,EAjBK,KACU,WAA1B4G,KAAKsG,QAAQ6P,SAKjBnW,KAAK2P,OAJHpP,EAAaoB,QAAQ3B,KAAKqG,SAAU+S,KAeK,OAI/CzB,uBACE,OAAO,IAAIlB,GAAU,CACnBF,YAAavW,KAAKqG,WAItB6F,qBACE3L,EAAaa,GAAGpB,KAAKqG,SAvJM,gCAuJ2BpH,IAtKvC,WAuKTA,EAAMqD,MAILtC,KAAKsG,QAAQgF,SAKlBtL,KAAK2P,OAJHpP,EAAaoB,QAAQ3B,KAAKqG,SAAU+S,QASpBtS,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAO2R,GAAUhS,oBAAoBtH,KAAMmF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQnF,WASnBO,EAAaa,GAAG5I,SA5Lc,8BAGD,gCAyLyC,SAAUyG,GAC9E,MAAMjC,EAAStE,EAAuBsH,MAMtC,GAJI,CAAC,IAAK,QAAQ7H,SAAS6H,KAAKqH,UAC9BpI,EAAMwD,iBAGJ5I,EAAWmG,MACb,OAGFO,EAAac,IAAIrE,EAAQka,IAAc,KAEjC9d,EAAU4G,OACZA,KAAKkS,WAKT,MAAM8G,EAAc/Q,EAAeG,QAAQ+Q,IACvCH,GAAeA,IAAgBhc,GACjCsc,GAAUvS,YAAYiS,GAAarJ,OAGxB2J,GAAUhS,oBAAoBtK,GACtC+K,OAAO/H,SAGdO,EAAaa,GAAGtG,OAvOa,8BAuOgB,KAC3C,IAAK,MAAM9C,KAAYiQ,EAAejJ,KAAKma,IACzCG,GAAUhS,oBAAoBtP,GAAU4X,UAI5CrP,EAAaa,GAAGtG,OA/NM,uBA+NgB,KACpC,IAAK,MAAM/C,KAAWkQ,EAAejJ,KAAK,gDACG,UAAvCzF,iBAAiBxB,GAASyhB,UAC5BF,GAAUhS,oBAAoBvP,GAAS4X,UAK7C1I,EAAqBqS,IAMrBle,EAAmBke,ICjRnB,MAAMG,GAAgB,IAAIlb,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUImb,GAAmB,iEAOnBC,GAAmB,qIAEnBC,GAAmB,CAACC,EAAWC,KACnC,MAAMC,EAAgBF,EAAUG,SAAShW,cAEzC,OAAI8V,EAAqB3hB,SAAS4hB,IAC5BN,GAAcha,IAAIsa,IACbjZ,QAAQ4Y,GAAiB1T,KAAK6T,EAAUI,YAAcN,GAAiB3T,KAAK6T,EAAUI,YAO1FH,EAAqBpV,QAAOwV,GAAkBA,aAA0BnU,SAC5EoU,MAAKC,GAASA,EAAMpU,KAAK+T,MAGjBM,GAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAlCP,kBAmC7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHhO,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDiO,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IC/DAlX,GAAU,CACdmX,WAAY,GACZC,SAAU,cACVC,QAAS,GACTC,MAAM,EACNC,UAAU,EACVC,WAAY,KACZC,UAAWnC,IAGPrV,GAAc,CAClBkX,WAAY,oBACZC,SAAU,SACVC,QAAS,SACTC,KAAM,UACNC,SAAU,UACVC,WAAY,kBACZC,UAAW,UAGPC,GAAqB,CACzBzkB,SAAU,mBACV0kB,MAAO,kCAOT,MAAMC,WAAwB7X,EAC5BU,YAAYL,GACViB,QACApG,KAAKsG,QAAUtG,KAAKkF,WAAWC,GAItBJ,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MA/CS,kBAmDXmhB,aACE,OAAO9d,OAAOC,OAAOiB,KAAKsG,QAAQ8V,SAC/BnT,KAAI9D,GAAUnF,KAAK6c,yBAAyB1X,KAC5CT,OAAO5D,SAGZgc,aACE,OAAO9c,KAAK4c,aAAazjB,OAAS,EAGpC4jB,cAAcX,GAGZ,OAFApc,KAAKgd,cAAcZ,GACnBpc,KAAKsG,QAAQ8V,QAAU,IAAKpc,KAAKsG,QAAQ8V,WAAYA,GAC9Cpc,KAGTid,SACE,MAAMC,EAAkB1kB,SAAS4d,cAAc,OAC/C8G,EAAgBC,UAAYnd,KAAKod,eAAepd,KAAKsG,QAAQ6V,UAE7D,IAAK,MAAOnkB,EAAUqlB,KAASve,OAAOwe,QAAQtd,KAAKsG,QAAQ8V,SACzDpc,KAAKud,YAAYL,EAAiBG,EAAMrlB,GAG1C,MAAMmkB,EAAWe,EAAgB7U,SAAS,GACpC6T,EAAalc,KAAK6c,yBAAyB7c,KAAKsG,QAAQ4V,YAM9D,OAJIA,GACFC,EAASniB,UAAUuQ,OAAO2R,EAAW7jB,MAAM,MAGtC8jB,EAIT7W,iBAAiBH,GACfiB,MAAMd,iBAAiBH,GACvBnF,KAAKgd,cAAc7X,EAAOiX,SAG5BY,cAAcQ,GACZ,IAAK,MAAOxlB,EAAUokB,KAAYtd,OAAOwe,QAAQE,GAC/CpX,MAAMd,iBAAiB,CAAEtN,SAAAA,EAAU0kB,MAAON,GAAWK,IAIzDc,YAAYpB,EAAUC,EAASpkB,GAC7B,MAAMylB,EAAkBxV,EAAeG,QAAQpQ,EAAUmkB,GAEpDsB,KAILrB,EAAUpc,KAAK6c,yBAAyBT,IAOpCtjB,EAAUsjB,GACZpc,KAAK0d,sBAAsBxkB,EAAWkjB,GAAUqB,GAI9Czd,KAAKsG,QAAQ+V,KACfoB,EAAgBN,UAAYnd,KAAKod,eAAehB,GAIlDqB,EAAgBE,YAAcvB,EAd5BqB,EAAgBpa,UAiBpB+Z,eAAeI,GACb,OAAOxd,KAAKsG,QAAQgW,SDzDjB,SAAsBsB,EAAYpB,EAAWqB,GAClD,IAAKD,EAAWzkB,OACd,OAAOykB,EAGT,GAAIC,GAAgD,mBAArBA,EAC7B,OAAOA,EAAiBD,GAG1B,MACME,GADY,IAAIhjB,OAAOijB,WACKC,gBAAgBJ,EAAY,aACxD5G,EAAW,GAAG9O,UAAU4V,EAAgB9iB,KAAKqF,iBAAiB,MAEpE,IAAK,MAAMtI,KAAWif,EAAU,CAC9B,MAAMiH,EAAclmB,EAAQiiB,SAAShW,cAErC,IAAKlF,OAAOqC,KAAKqb,GAAWrkB,SAAS8lB,GAAc,CACjDlmB,EAAQsL,SAER,SAGF,MAAM6a,EAAgB,GAAGhW,UAAUnQ,EAAQwM,YACrC4Z,EAAoB,GAAGjW,OAAOsU,EAAU,MAAQ,GAAIA,EAAUyB,IAAgB,IAEpF,IAAK,MAAMpE,KAAaqE,EACjBtE,GAAiBC,EAAWsE,IAC/BpmB,EAAQsM,gBAAgBwV,EAAUG,UAKxC,OAAO8D,EAAgB9iB,KAAKmiB,UCyBKiB,CAAaZ,EAAKxd,KAAKsG,QAAQkW,UAAWxc,KAAKsG,QAAQiW,YAAciB,EAGtGX,yBAAyBW,GACvB,MAAsB,mBAARA,EAAqBA,EAAIxd,MAAQwd,EAGjDE,sBAAsB3lB,EAAS0lB,GAC7B,GAAIzd,KAAKsG,QAAQ+V,KAGf,OAFAoB,EAAgBN,UAAY,QAC5BM,EAAgBpH,OAAOte,GAIzB0lB,EAAgBE,YAAc5lB,EAAQ4lB,aCxI1C,MACMU,GAAwB,IAAI9f,IAAI,CAAC,WAAY,YAAa,eAE1D+f,GAAkB,OAElBzP,GAAkB,OAGlB0P,GAAkB,SAElBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAehBC,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAO5jB,IAAU,OAAS,QAC1B6jB,OAAQ,SACRC,KAAM9jB,IAAU,QAAU,QAGtB6J,GAAU,CACdka,WAAW,EACX9C,SAAU,+GAIVxa,QAAS,cACTud,MAAO,GACPC,MAAO,EACP9C,MAAM,EACNrkB,UAAU,EACVgb,UAAW,MACX3B,OAAQ,CAAC,EAAG,GACZ+N,WAAW,EACXC,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/C/N,SAAU,kBACVgO,YAAa,GACbhD,UAAU,EACVC,WAAY,KACZC,UAAWnC,GACX5I,aAAc,MAGVzM,GAAc,CAClBia,UAAW,UACX9C,SAAU,SACV+C,MAAO,4BACPvd,QAAS,SACTwd,MAAO,kBACP9C,KAAM,UACNrkB,SAAU,mBACVgb,UAAW,oBACX3B,OAAQ,0BACR+N,UAAW,2BACXC,mBAAoB,QACpB/N,SAAU,mBACVgO,YAAa,oBACbhD,SAAU,UACVC,WAAY,kBACZC,UAAW,SACX/K,aAAc,0BAOhB,MAAM8N,WAAgBpZ,EACpBX,YAAYzN,EAASoN,GACnB,QAAsB,IAAXmN,EACT,MAAM,IAAIrM,UAAU,+DAGtBG,MAAMrO,EAASoN,GAGfnF,KAAKwf,YAAa,EAClBxf,KAAKyf,SAAW,EAChBzf,KAAK0f,YAAa,EAClB1f,KAAK2f,eAAiB,GACtB3f,KAAK4R,QAAU,KACf5R,KAAK4f,iBAAmB,KAGxB5f,KAAK6f,IAAM,KAEX7f,KAAK8f,gBAII/a,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAnHS,UAuHXskB,SACE/f,KAAKwf,YAAa,EAGpBQ,UACEhgB,KAAKwf,YAAa,EAGpBS,gBACEjgB,KAAKwf,YAAcxf,KAAKwf,WAG1BzX,OAAO9I,GACL,GAAKe,KAAKwf,WAAV,CAIA,GAAIvgB,EAAO,CACT,MAAMqU,EAAUtT,KAAKkgB,6BAA6BjhB,GAUlD,OARAqU,EAAQqM,eAAeQ,OAAS7M,EAAQqM,eAAeQ,WAEnD7M,EAAQ8M,uBACV9M,EAAQ+M,SAER/M,EAAQgN,UAMRtgB,KAAK0P,WACP1P,KAAKsgB,SAIPtgB,KAAKqgB,UAGP7Z,UACEgH,aAAaxN,KAAKyf,UAElBlf,EAAaC,IAAIR,KAAKqG,SAAS3M,QAAQ6kB,IAAiBC,GAAkBxe,KAAKugB,mBAE3EvgB,KAAK6f,KACP7f,KAAK6f,IAAIxc,SAGXrD,KAAKwgB,iBACLpa,MAAMI,UAGRoJ,OACE,GAAoC,SAAhC5P,KAAKqG,SAAS6J,MAAMsB,QACtB,MAAM,IAAIvM,MAAM,uCAGlB,IAAMjF,KAAKygB,mBAAoBzgB,KAAKwf,WAClC,OAGF,MAAMzG,EAAYxY,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UAjKxD,SAmKTwS,GADatmB,EAAe4F,KAAKqG,WACLrG,KAAKqG,SAASsa,cAActmB,iBAAiBJ,SAAS+F,KAAKqG,UAE7F,GAAI0S,EAAU/W,mBAAqB0e,EACjC,OAGF,MAAMb,EAAM7f,KAAK4gB,iBAEjB5gB,KAAKqG,SAASlC,aAAa,mBAAoB0b,EAAI5nB,aAAa,OAEhE,MAAMmnB,UAAEA,GAAcpf,KAAKsG,QAmB3B,GAjBKtG,KAAKqG,SAASsa,cAActmB,gBAAgBJ,SAAS+F,KAAK6f,OAC7DT,EAAU/I,OAAOwJ,GACjBtf,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UA/KpC,cAkLflO,KAAK4R,QACP5R,KAAK4R,QAAQS,SAEbrS,KAAKiS,cAAc4N,GAGrBA,EAAI7lB,UAAUuQ,IAAIsE,IAMd,iBAAkBrW,SAAS6B,gBAC7B,IAAK,MAAMtC,IAAW,GAAGmQ,UAAU1P,SAASwC,KAAKqN,UAC/C9H,EAAaa,GAAGrJ,EAAS,YAAa2C,GAe1CsF,KAAK4G,gBAXY,KACf,MAAMia,EAAqB7gB,KAAK0f,WAEhC1f,KAAK0f,YAAa,EAClBnf,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UAzMvC,UA2MV2S,GACF7gB,KAAKsgB,WAIqBtgB,KAAK6f,IAAK7f,KAAKuO,eAG/CoB,OACE,IAAK3P,KAAK0P,WACR,OAIF,GADkBnP,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UA3NxD,SA4NDlM,iBACZ,OAGF,MAAM6d,EAAM7f,KAAK4gB,iBAKjB,GAJAf,EAAI7lB,UAAUqJ,OAAOwL,IAIjB,iBAAkBrW,SAAS6B,gBAC7B,IAAK,MAAMtC,IAAW,GAAGmQ,UAAU1P,SAASwC,KAAKqN,UAC/C9H,EAAaC,IAAIzI,EAAS,YAAa2C,GAI3CsF,KAAK2f,eAAL,OAAqC,EACrC3f,KAAK2f,eAAL,OAAqC,EACrC3f,KAAK2f,eAAL,OAAqC,EACrC3f,KAAK0f,YAAa,EAiBlB1f,KAAK4G,gBAfY,KACX5G,KAAKogB,yBAIJpgB,KAAK0f,YACRG,EAAIxc,SAGNrD,KAAKqG,SAAShC,gBAAgB,oBAC9B9D,EAAaoB,QAAQ3B,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UAzPtC,WA2PflO,KAAKwgB,oBAGuBxgB,KAAK6f,IAAK7f,KAAKuO,eAG/C8D,SACMrS,KAAK4R,SACP5R,KAAK4R,QAAQS,SAKjBoO,iBACE,OAAO3f,QAAQd,KAAK8gB,aAGtBF,iBAKE,OAJK5gB,KAAK6f,MACR7f,KAAK6f,IAAM7f,KAAK+gB,kBAAkB/gB,KAAKghB,2BAGlChhB,KAAK6f,IAGdkB,kBAAkB3E,GAChB,MAAMyD,EAAM7f,KAAKihB,oBAAoB7E,GAASa,SAG9C,IAAK4C,EACH,OAAO,KAGTA,EAAI7lB,UAAUqJ,OAAOib,GAAiBzP,IAEtCgR,EAAI7lB,UAAUuQ,IAAK,MAAKvK,KAAKwF,YAAY/J,aAEzC,MAAMylB,ErB7SKC,CAAAA,IACb,GACEA,GAAUxjB,KAAKyjB,MAnBH,IAmBSzjB,KAAK0jB,gBACnB7oB,SAAS8oB,eAAeH,IAEjC,OAAOA,GqBwSSI,CAAOvhB,KAAKwF,YAAY/J,MAAMgI,WAQ5C,OANAoc,EAAI1b,aAAa,KAAM+c,GAEnBlhB,KAAKuO,eACPsR,EAAI7lB,UAAUuQ,IAAI+T,IAGbuB,EAGT2B,WAAWpF,GACT,IAAIqF,GAAU,EACVzhB,KAAK6f,MACP4B,EAAUzhB,KAAK0P,WACf1P,KAAK6f,IAAIxc,SACTrD,KAAK6f,IAAM,MAGb7f,KAAKwgB,iBACLxgB,KAAK6f,IAAM7f,KAAK+gB,kBAAkB3E,GAE9BqF,GACFzhB,KAAK4P,OAITqR,oBAAoB7E,GAalB,OAZIpc,KAAK4f,iBACP5f,KAAK4f,iBAAiB7C,cAAcX,GAEpCpc,KAAK4f,iBAAmB,IAAIjD,GAAgB,IACvC3c,KAAKsG,QAGR8V,QAAAA,EACAF,WAAYlc,KAAK6c,yBAAyB7c,KAAKsG,QAAQgZ,eAIpDtf,KAAK4f,iBAGdoB,yBACE,MAAO,CACL,iBAA0BhhB,KAAK8gB,aAInCA,YACE,OAAO9gB,KAAKsG,QAAQ4Y,MAItBgB,6BAA6BjhB,GAC3B,OAAOe,KAAKwF,YAAY8B,oBAAoBrI,EAAMa,eAAgBE,KAAK0hB,sBAGzEnT,cACE,OAAOvO,KAAKsG,QAAQ2Y,WAAcjf,KAAK6f,KAAO7f,KAAK6f,IAAI7lB,UAAUC,SAASqkB,IAG5E5O,WACE,OAAO1P,KAAK6f,KAAO7f,KAAK6f,IAAI7lB,UAAUC,SAAS4U,IAGjDoD,cAAc4N,GACZ,MAAM7M,EAA8C,mBAA3BhT,KAAKsG,QAAQ0M,UACpChT,KAAKsG,QAAQ0M,UAAUjT,KAAKC,KAAM6f,EAAK7f,KAAKqG,UAC5CrG,KAAKsG,QAAQ0M,UACT2O,EAAahD,GAAc3L,EAAU9M,eAC3ClG,KAAK4R,QAAUU,EAAOG,aAAazS,KAAKqG,SAAUwZ,EAAK7f,KAAKwS,iBAAiBmP,IAG/E9O,aACE,MAAMxB,OAAEA,GAAWrR,KAAKsG,QAExB,MAAsB,iBAAX+K,EACFA,EAAOhZ,MAAM,KAAK4Q,KAAIzF,GAAS9G,OAAOmR,SAASrK,EAAO,MAGzC,mBAAX6N,EACFyB,GAAczB,EAAOyB,EAAY9S,KAAKqG,UAGxCgL,EAGTwL,yBAAyBW,GACvB,MAAsB,mBAARA,EAAqBA,EAAIzd,KAAKC,KAAKqG,UAAYmX,EAG/DhL,iBAAiBmP,GACf,MAAM5O,EAAwB,CAC5BC,UAAW2O,EACX1O,UAAW,CACT,CACEzX,KAAM,OACN0X,QAAS,CACPmM,mBAAoBrf,KAAKsG,QAAQ+Y,qBAGrC,CACE7jB,KAAM,SACN0X,QAAS,CACP7B,OAAQrR,KAAK6S,eAGjB,CACErX,KAAM,kBACN0X,QAAS,CACP5B,SAAUtR,KAAKsG,QAAQgL,WAG3B,CACE9V,KAAM,QACN0X,QAAS,CACPnb,QAAU,IAAGiI,KAAKwF,YAAY/J,eAGlC,CACED,KAAM,kBACN2X,SAAS,EACTyO,MAAO,aACPjmB,GAAIgM,IAGF3H,KAAK4gB,iBAAiBzc,aAAa,wBAAyBwD,EAAKka,MAAM7O,eAM/E,MAAO,IACFD,KACsC,mBAA9B/S,KAAKsG,QAAQmL,aAA8BzR,KAAKsG,QAAQmL,aAAasB,GAAyB/S,KAAKsG,QAAQmL,cAI1HqO,gBACE,MAAMgC,EAAW9hB,KAAKsG,QAAQ3E,QAAQtJ,MAAM,KAE5C,IAAK,MAAMsJ,KAAWmgB,EACpB,GAAgB,UAAZngB,EACFpB,EAAaa,GAAGpB,KAAKqG,SAAUrG,KAAKwF,YAAY0I,UA5apC,SA4a4DlO,KAAKsG,QAAQtO,UAAUiH,GAASe,KAAK+H,OAAO9I,UAC/G,GApbU,WAobN0C,EAA4B,CACrC,MAAMogB,EAAUpgB,IAAY8c,GAC1Bze,KAAKwF,YAAY0I,UA5aF,cA6aflO,KAAKwF,YAAY0I,UA/aL,WAgbR8T,EAAWrgB,IAAY8c,GAC3Bze,KAAKwF,YAAY0I,UA9aF,cA+aflO,KAAKwF,YAAY0I,UAjbJ,YAmbf3N,EAAaa,GAAGpB,KAAKqG,SAAU0b,EAAS/hB,KAAKsG,QAAQtO,UAAUiH,IAC7D,MAAMqU,EAAUtT,KAAKkgB,6BAA6BjhB,GAClDqU,EAAQqM,eAA8B,YAAf1gB,EAAMwB,KAAqBie,GAAgBD,KAAiB,EACnFnL,EAAQ+M,YAEV9f,EAAaa,GAAGpB,KAAKqG,SAAU2b,EAAUhiB,KAAKsG,QAAQtO,UAAUiH,IAC9D,MAAMqU,EAAUtT,KAAKkgB,6BAA6BjhB,GAClDqU,EAAQqM,eAA8B,aAAf1gB,EAAMwB,KAAsBie,GAAgBD,IACjEnL,EAAQjN,SAASpM,SAASgF,EAAMY,eAElCyT,EAAQgN,YAKdtgB,KAAKugB,kBAAoB,KACnBvgB,KAAKqG,UACPrG,KAAK2P,QAITpP,EAAaa,GAAGpB,KAAKqG,SAAS3M,QAAQ6kB,IAAiBC,GAAkBxe,KAAKugB,mBAE1EvgB,KAAKsG,QAAQtO,SACfgI,KAAKsG,QAAU,IACVtG,KAAKsG,QACR3E,QAAS,SACT3J,SAAU,IAGZgI,KAAKiiB,YAITA,YACE,MAAM/C,EAAQlf,KAAKsG,QAAQ4b,cAEtBhD,IAIAlf,KAAKqG,SAASpO,aAAa,eAAkB+H,KAAKqG,SAASsX,aAC9D3d,KAAKqG,SAASlC,aAAa,aAAc+a,GAG3Clf,KAAKqG,SAAShC,gBAAgB,UAGhCgc,SACMrgB,KAAK0P,YAAc1P,KAAK0f,WAC1B1f,KAAK0f,YAAa,GAIpB1f,KAAK0f,YAAa,EAElB1f,KAAKmiB,aAAY,KACXniB,KAAK0f,YACP1f,KAAK4P,SAEN5P,KAAKsG,QAAQ6Y,MAAMvP,OAGxB0Q,SACMtgB,KAAKogB,yBAITpgB,KAAK0f,YAAa,EAElB1f,KAAKmiB,aAAY,KACVniB,KAAK0f,YACR1f,KAAK2P,SAEN3P,KAAKsG,QAAQ6Y,MAAMxP,OAGxBwS,YAAYplB,EAASqlB,GACnB5U,aAAaxN,KAAKyf,UAClBzf,KAAKyf,SAAWviB,WAAWH,EAASqlB,GAGtChC,uBACE,OAAOthB,OAAOC,OAAOiB,KAAK2f,gBAAgBxnB,UAAS,GAGrD+M,WAAWC,GACT,MAAMkd,EAAiBpe,EAAYK,kBAAkBtE,KAAKqG,UAE1D,IAAK,MAAMic,KAAiBxjB,OAAOqC,KAAKkhB,GAClChE,GAAsB5e,IAAI6iB,WACrBD,EAAeC,GAW1B,OAPAnd,EAAS,IACJkd,KACmB,iBAAXld,GAAuBA,EAASA,EAAS,IAEtDA,EAASnF,KAAKoF,gBAAgBD,GAC9BA,EAASnF,KAAKqF,kBAAkBF,GAChCnF,KAAKsF,iBAAiBH,GACfA,EAGTE,kBAAkBF,GAoBhB,OAnBAA,EAAOia,WAAiC,IAArBja,EAAOia,UAAsB5mB,SAASwC,KAAO9B,EAAWiM,EAAOia,WAEtD,iBAAjBja,EAAOga,QAChBha,EAAOga,MAAQ,CACbvP,KAAMzK,EAAOga,MACbxP,KAAMxK,EAAOga,QAIjBha,EAAO+c,cAAgBliB,KAAKqG,SAASpO,aAAa,UAAY,GAC9DkN,EAAO+Z,MAAQlf,KAAK6c,yBAAyB1X,EAAO+Z,QAAU/Z,EAAO+c,cACzC,iBAAjB/c,EAAO+Z,QAChB/Z,EAAO+Z,MAAQ/Z,EAAO+Z,MAAMzb,YAGA,iBAAnB0B,EAAOiX,UAChBjX,EAAOiX,QAAUjX,EAAOiX,QAAQ3Y,YAG3B0B,EAGTuc,qBACE,MAAMvc,EAAS,GAEf,IAAK,MAAM7C,KAAOtC,KAAKsG,QACjBtG,KAAKwF,YAAYT,QAAQzC,KAAStC,KAAKsG,QAAQhE,KACjD6C,EAAO7C,GAAOtC,KAAKsG,QAAQhE,IAO/B,OAAO6C,EAGTqb,iBACMxgB,KAAK4R,UACP5R,KAAK4R,QAAQQ,UACbpS,KAAK4R,QAAU,MAKG9K,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAO4X,GAAQjY,oBAAoBtH,KAAMmF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UASX/J,EAAmBmkB,IC5nBnB,MAKMxa,GAAU,IACXwa,GAAQxa,QACXiO,UAAW,QACX3B,OAAQ,CAAC,EAAG,GACZ1P,QAAS,QACTya,QAAS,GACTD,SAAU,+IAONnX,GAAc,IACfua,GAAQva,YACXoX,QAAS,kCAOX,MAAMmG,WAAgBhD,GAETxa,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAtCS,UA0CXglB,iBACE,OAAOzgB,KAAK8gB,aAAe9gB,KAAKwiB,cAIlCxB,yBACE,MAAO,CACL,kBAAkBhhB,KAAK8gB,YACvB,gBAAoB9gB,KAAKwiB,eAI7BA,cACE,OAAOxiB,KAAK6c,yBAAyB7c,KAAKsG,QAAQ8V,SAI9BtV,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAO4a,GAAQjb,oBAAoBtH,KAAMmF,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UASX/J,EAAmBmnB,IC9EnB,MAMME,GAAe,qBAIfxX,GAAoB,SAGpByX,GAAwB,SASxB3d,GAAU,CACdsM,OAAQ,KACRsR,WAAY,eACZC,cAAc,EACd5lB,OAAQ,MAGJgI,GAAc,CAClBqM,OAAQ,gBACRsR,WAAY,SACZC,aAAc,UACd5lB,OAAQ,WAOV,MAAM6lB,WAAkB1c,EACtBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAGfnF,KAAK8iB,aAAe,IAAIngB,IACxB3C,KAAK+iB,oBAAsB,IAAIpgB,IAC/B3C,KAAKgjB,aAA6D,YAA9CzpB,iBAAiByG,KAAKqG,UAAUsS,UAA0B,KAAO3Y,KAAKqG,SAC1FrG,KAAKijB,cAAgB,KACrBjjB,KAAKkjB,UAAY,KACjBljB,KAAKmjB,oBAAsB,CACzBC,gBAAiB,EACjBC,gBAAiB,GAEnBrjB,KAAKsjB,UAIIve,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAnES,YAuEX6nB,UACEtjB,KAAKujB,mCACLvjB,KAAKwjB,2BAEDxjB,KAAKkjB,UACPljB,KAAKkjB,UAAUO,aAEfzjB,KAAKkjB,UAAYljB,KAAK0jB,kBAGxB,IAAK,MAAMC,KAAW3jB,KAAK+iB,oBAAoBhkB,SAC7CiB,KAAKkjB,UAAUU,QAAQD,GAI3Bnd,UACExG,KAAKkjB,UAAUO,aACfrd,MAAMI,UAIRnB,kBAAkBF,GAIhB,OAFAA,EAAOnI,OAAS9D,EAAWiM,EAAOnI,SAAWxE,SAASwC,KAE/CmK,EAGTqe,2BACOxjB,KAAKsG,QAAQsc,eAKlBriB,EAAaC,IAAIR,KAAKsG,QAAQtJ,OAAQylB,IAEtCliB,EAAaa,GAAGpB,KAAKsG,QAAQtJ,OAAQylB,GAAaC,IAAuBzjB,IACvE,MAAM4kB,EAAoB7jB,KAAK+iB,oBAAoBvgB,IAAIvD,EAAMjC,OAAO8mB,MACpE,GAAID,EAAmB,CACrB5kB,EAAMwD,iBACN,MAAMjI,EAAOwF,KAAKgjB,cAAgBloB,OAC5BipB,EAASF,EAAkBG,UAAYhkB,KAAKqG,SAAS2d,UAC3D,GAAIxpB,EAAKypB,SAEP,YADAzpB,EAAKypB,SAAS,CAAEC,IAAKH,IAKvBvpB,EAAK0d,UAAY6L,OAKvBL,kBACE,MAAMxQ,EAAU,CACd1Y,KAAMwF,KAAKgjB,aACXmB,UAAW,CAAC,GAAK,GAAK,GACtBxB,WAAY3iB,KAAKokB,kBAGnB,OAAO,IAAIC,sBAAqB/G,GAAWtd,KAAKskB,kBAAkBhH,IAAUpK,GAI9EoR,kBAAkBhH,GAChB,MAAMiH,EAAgB7H,GAAS1c,KAAK8iB,aAAatgB,IAAK,IAAGka,EAAM1f,OAAOwnB,MAChE5N,EAAW8F,IACf1c,KAAKmjB,oBAAoBC,gBAAkB1G,EAAM1f,OAAOgnB,UACxDhkB,KAAKykB,SAASF,EAAc7H,KAGxB2G,GAAmBrjB,KAAKgjB,cAAgBxqB,SAAS6B,iBAAiB6d,UAClEwM,EAAkBrB,GAAmBrjB,KAAKmjB,oBAAoBE,gBACpErjB,KAAKmjB,oBAAoBE,gBAAkBA,EAE3C,IAAK,MAAM3G,KAASY,EAAS,CAC3B,IAAKZ,EAAMiI,eAAgB,CACzB3kB,KAAKijB,cAAgB,KACrBjjB,KAAK4kB,kBAAkBL,EAAc7H,IAErC,SAGF,MAAMmI,EAA2BnI,EAAM1f,OAAOgnB,WAAahkB,KAAKmjB,oBAAoBC,gBAEpF,GAAIsB,GAAmBG,GAGrB,GAFAjO,EAAS8F,IAEJ2G,EACH,YAOCqB,GAAoBG,GACvBjO,EAAS8F,IAMf0H,iBACE,OAAOpkB,KAAKsG,QAAQ+K,OAAU,GAAErR,KAAKsG,QAAQ+K,oBAAsBrR,KAAKsG,QAAQqc,WAGlFY,mCACEvjB,KAAK8iB,aAAe,IAAIngB,IACxB3C,KAAK+iB,oBAAsB,IAAIpgB,IAE/B,MAAMmiB,EAAc7c,EAAejJ,KAAK0jB,GAAuB1iB,KAAKsG,QAAQtJ,QAE5E,IAAK,MAAM+nB,KAAUD,EAAa,CAEhC,IAAKC,EAAOjB,MAAQjqB,EAAWkrB,GAC7B,SAGF,MAAMlB,EAAoB5b,EAAeG,QAAQ2c,EAAOjB,KAAM9jB,KAAKqG,UAG/DjN,EAAUyqB,KACZ7jB,KAAK8iB,aAAajgB,IAAIkiB,EAAOjB,KAAMiB,GACnC/kB,KAAK+iB,oBAAoBlgB,IAAIkiB,EAAOjB,KAAMD,KAKhDY,SAASznB,GACHgD,KAAKijB,gBAAkBjmB,IAI3BgD,KAAK4kB,kBAAkB5kB,KAAKsG,QAAQtJ,QACpCgD,KAAKijB,cAAgBjmB,EACrBA,EAAOhD,UAAUuQ,IAAIU,IACrBjL,KAAKglB,iBAAiBhoB,GAEtBuD,EAAaoB,QAAQ3B,KAAKqG,SA7MN,wBA6MgC,CAAExG,cAAe7C,KAGvEgoB,iBAAiBhoB,GAEf,GAAIA,EAAOhD,UAAUC,SA9MQ,iBA+M3BgO,EAAeG,QApMY,mBAoMsBpL,EAAOtD,QArMpC,cAsMjBM,UAAUuQ,IAAIU,SAInB,IAAK,MAAMga,KAAahd,EAAeO,QAAQxL,EA/MnB,qBAkN1B,IAAK,MAAMkoB,KAAQjd,EAAeS,KAAKuc,EA9MhB,sDA+MrBC,EAAKlrB,UAAUuQ,IAAIU,IAKzB2Z,kBAAkB5V,GAChBA,EAAOhV,UAAUqJ,OAAO4H,IAExB,MAAMka,EAAcld,EAAejJ,KAAM,gBAAgDgQ,GACzF,IAAK,MAAMoW,KAAQD,EACjBC,EAAKprB,UAAUqJ,OAAO4H,IAKJnE,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOkb,GAAUvb,oBAAoBtH,KAAMmF,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UASX5E,EAAaa,GAAGtG,OA9Pa,8BA8PgB,KAC3C,IAAK,MAAMuqB,KAAOpd,EAAejJ,KA1PT,0BA2PtB6jB,GAAUvb,oBAAoB+d,MAQlCjqB,EAAmBynB,IC/QnB,MAYMyC,GAAiB,YACjBC,GAAkB,aAClB9U,GAAe,UACfC,GAAiB,YAEjBzF,GAAoB,SACpBqT,GAAkB,OAClBzP,GAAkB,OAWlBhH,GAAuB,2EACvB2d,GAAuB,gHAAqB3d,KAQlD,MAAM4d,WAAYtf,EAChBX,YAAYzN,GACVqO,MAAMrO,GACNiI,KAAK6R,QAAU7R,KAAKqG,SAAS3M,QAfN,uCAiBlBsG,KAAK6R,UAOV7R,KAAK0lB,sBAAsB1lB,KAAK6R,QAAS7R,KAAK2lB,gBAE9CplB,EAAaa,GAAGpB,KAAKqG,SA5CF,kBA4C2BpH,GAASe,KAAKmN,SAASlO,MAI5DxD,kBACT,MA1DS,MA8DXmU,OACE,MAAMgW,EAAY5lB,KAAKqG,SACvB,GAAIrG,KAAK6lB,cAAcD,GACrB,OAIF,MAAME,EAAS9lB,KAAK+lB,iBAEdC,EAAYF,EAChBvlB,EAAaoB,QAAQmkB,EApEP,cAoE2B,CAAEjmB,cAAe+lB,IAC1D,KAEgBrlB,EAAaoB,QAAQikB,EArEvB,cAqE8C,CAAE/lB,cAAeimB,IAEjE9jB,kBAAqBgkB,GAAaA,EAAUhkB,mBAI1DhC,KAAKimB,YAAYH,EAAQF,GACzB5lB,KAAKkmB,UAAUN,EAAWE,IAI5BI,UAAUnuB,EAASouB,GACjB,IAAKpuB,EACH,OAGFA,EAAQiC,UAAUuQ,IAAIU,IAEtBjL,KAAKkmB,UAAUxtB,EAAuBX,IAEtC,MAAM8O,EAAa9O,EAAQiC,UAAUC,SAASqkB,IAmB9Cte,KAAK4G,gBAlBY,KACXC,GACF9O,EAAQiC,UAAUuQ,IAAIsE,IAGa,QAAjC9W,EAAQE,aAAa,UAIzBF,EAAQma,QACRna,EAAQsM,gBAAgB,YACxBtM,EAAQoM,aAAa,iBAAiB,GACtCnE,KAAKomB,gBAAgBruB,GAAS,GAC9BwI,EAAaoB,QAAQ5J,EAtGN,eAsG4B,CACzC8H,cAAesmB,OAIWpuB,EAAS8O,GAGzCof,YAAYluB,EAASouB,GACnB,IAAKpuB,EACH,OAGFA,EAAQiC,UAAUqJ,OAAO4H,IACzBlT,EAAQwhB,OAERvZ,KAAKimB,YAAYvtB,EAAuBX,IAExC,MAAM8O,EAAa9O,EAAQiC,UAAUC,SAASqkB,IAgB9Cte,KAAK4G,gBAfY,KACXC,GACF9O,EAAQiC,UAAUqJ,OAAOwL,IAGU,QAAjC9W,EAAQE,aAAa,UAIzBF,EAAQoM,aAAa,iBAAiB,GACtCpM,EAAQoM,aAAa,WAAY,MACjCnE,KAAKomB,gBAAgBruB,GAAS,GAC9BwI,EAAaoB,QAAQ5J,EAvIL,gBAuI4B,CAAE8H,cAAesmB,OAGjCpuB,EAAS8O,GAGzCsG,SAASlO,GACP,IAAM,CAACqmB,GAAgBC,GAAiB9U,GAAcC,IAAgBvY,SAAS8G,EAAMqD,KACnF,OAGFrD,EAAM4U,kBACN5U,EAAMwD,iBACN,MAAMqL,EAAS,CAACyX,GAAiB7U,IAAgBvY,SAAS8G,EAAMqD,KAC1D+jB,EAAoBlpB,EAAqB6C,KAAK2lB,eAAejhB,QAAO3M,IAAY8B,EAAW9B,KAAWkH,EAAMjC,OAAQ8Q,GAAQ,GAE9HuY,GACFZ,GAAIne,oBAAoB+e,GAAmBzW,OAI/C+V,eACE,OAAO1d,EAAejJ,KAAKwmB,GAAqBxlB,KAAK6R,SAGvDkU,iBACE,OAAO/lB,KAAK2lB,eAAe3mB,MAAKsJ,GAAStI,KAAK6lB,cAAcvd,MAAW,KAGzEod,sBAAsB1W,EAAQ3G,GAC5BrI,KAAKsmB,yBAAyBtX,EAAQ,OAAQ,WAE9C,IAAK,MAAM1G,KAASD,EAClBrI,KAAKumB,6BAA6Bje,GAItCie,6BAA6Bje,GAC3BA,EAAQtI,KAAKwmB,iBAAiBle,GAC9B,MAAMme,EAAWzmB,KAAK6lB,cAAcvd,GAC9Boe,EAAY1mB,KAAK2mB,iBAAiBre,GACxCA,EAAMnE,aAAa,gBAAiBsiB,GAEhCC,IAAcpe,GAChBtI,KAAKsmB,yBAAyBI,EAAW,OAAQ,gBAG9CD,GACHne,EAAMnE,aAAa,WAAY,MAGjCnE,KAAKsmB,yBAAyBhe,EAAO,OAAQ,OAG7CtI,KAAK4mB,mCAAmCte,GAG1Cse,mCAAmCte,GACjC,MAAMtL,EAAStE,EAAuB4P,GAEjCtL,IAILgD,KAAKsmB,yBAAyBtpB,EAAQ,OAAQ,YAE1CsL,EAAMkc,IACRxkB,KAAKsmB,yBAAyBtpB,EAAQ,kBAAoB,IAAGsL,EAAMkc,OAIvE4B,gBAAgBruB,EAAS8uB,GACvB,MAAMH,EAAY1mB,KAAK2mB,iBAAiB5uB,GACxC,IAAK2uB,EAAU1sB,UAAUC,SAjMN,YAkMjB,OAGF,MAAM8N,EAAS,CAAC/P,EAAU2d,KACxB,MAAM5d,EAAUkQ,EAAeG,QAAQpQ,EAAU0uB,GAC7C3uB,GACFA,EAAQiC,UAAU+N,OAAO4N,EAAWkR,IAIxC9e,EA1M6B,mBA0MIkD,IACjClD,EA1M2B,iBA0MI8G,IAC/B9G,EA1M2B,iBA0MIkD,IAC/Byb,EAAUviB,aAAa,gBAAiB0iB,GAG1CP,yBAAyBvuB,EAAS8hB,EAAWrW,GACtCzL,EAAQoC,aAAa0f,IACxB9hB,EAAQoM,aAAa0V,EAAWrW,GAIpCqiB,cAAcxW,GACZ,OAAOA,EAAKrV,UAAUC,SAASgR,IAIjCub,iBAAiBnX,GACf,OAAOA,EAAK9G,QAAQid,IAAuBnW,EAAOpH,EAAeG,QAAQod,GAAqBnW,GAIhGsX,iBAAiBtX,GACf,OAAOA,EAAK3V,QA3NO,gCA2NoB2V,EAInBvI,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAO8d,GAAIne,oBAAoBtH,MAErC,GAAsB,iBAAXmF,EAAX,CAIA,QAAqByC,IAAjBD,EAAKxC,IAAyBA,EAAO/M,WAAW,MAAmB,gBAAX+M,EAC1D,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,UASX5E,EAAaa,GAAG5I,SAxQc,eAwQkBqP,IAAsB,SAAU5I,GAC1E,CAAC,IAAK,QAAQ9G,SAAS6H,KAAKqH,UAC9BpI,EAAMwD,iBAGJ5I,EAAWmG,OAIfylB,GAAIne,oBAAoBtH,MAAM4P,UAMhCrP,EAAaa,GAAGtG,OArRa,eAqRgB,KAC3C,IAAK,MAAM/C,KAAWkQ,EAAejJ,KA/PF,iGAgQjCymB,GAAIne,oBAAoBvP,MAO5BqD,EAAmBqqB,ICxSnB,MAcMqB,GAAkB,OAClBjY,GAAkB,OAClBoK,GAAqB,UAErBjU,GAAc,CAClBia,UAAW,UACX8H,SAAU,UACV5H,MAAO,UAGHpa,GAAU,CACdka,WAAW,EACX8H,UAAU,EACV5H,MAAO,KAOT,MAAM6H,WAAc7gB,EAClBX,YAAYzN,EAASoN,GACnBiB,MAAMrO,EAASoN,GAEfnF,KAAKyf,SAAW,KAChBzf,KAAKinB,sBAAuB,EAC5BjnB,KAAKknB,yBAA0B,EAC/BlnB,KAAK8f,gBAII/a,qBACT,OAAOA,GAGEC,yBACT,OAAOA,GAGEvJ,kBACT,MAtDS,QA0DXmU,OACoBrP,EAAaoB,QAAQ3B,KAAKqG,SAjD5B,iBAmDFrE,mBAIdhC,KAAKmnB,gBAEDnnB,KAAKsG,QAAQ2Y,WACfjf,KAAKqG,SAASrM,UAAUuQ,IAvDN,QAiEpBvK,KAAKqG,SAASrM,UAAUqJ,OAAOyjB,IAC/BnsB,EAAOqF,KAAKqG,UACZrG,KAAKqG,SAASrM,UAAUuQ,IAAIsE,GAAiBoK,IAE7CjZ,KAAK4G,gBAXY,KACf5G,KAAKqG,SAASrM,UAAUqJ,OAAO4V,IAC/B1Y,EAAaoB,QAAQ3B,KAAKqG,SA9DX,kBAgEfrG,KAAKonB,uBAOuBpnB,KAAKqG,SAAUrG,KAAKsG,QAAQ2Y,YAG5DtP,OACO3P,KAAKyhB,YAIQlhB,EAAaoB,QAAQ3B,KAAKqG,SAlF5B,iBAoFFrE,mBAUdhC,KAAKqG,SAASrM,UAAUuQ,IAAI0O,IAC5BjZ,KAAK4G,gBAPY,KACf5G,KAAKqG,SAASrM,UAAUuQ,IAAIuc,IAC5B9mB,KAAKqG,SAASrM,UAAUqJ,OAAO4V,GAAoBpK,IACnDtO,EAAaoB,QAAQ3B,KAAKqG,SA1FV,qBA8FYrG,KAAKqG,SAAUrG,KAAKsG,QAAQ2Y,aAG5DzY,UACExG,KAAKmnB,gBAEDnnB,KAAKyhB,WACPzhB,KAAKqG,SAASrM,UAAUqJ,OAAOwL,IAGjCzI,MAAMI,UAGRib,UACE,OAAOzhB,KAAKqG,SAASrM,UAAUC,SAAS4U,IAK1CuY,qBACOpnB,KAAKsG,QAAQygB,WAId/mB,KAAKinB,sBAAwBjnB,KAAKknB,0BAItClnB,KAAKyf,SAAWviB,YAAW,KACzB8C,KAAK2P,SACJ3P,KAAKsG,QAAQ6Y,SAGlBkI,eAAepoB,EAAOqoB,GACpB,OAAQroB,EAAMwB,MACZ,IAAK,YACL,IAAK,WACHT,KAAKinB,qBAAuBK,EAC5B,MACF,IAAK,UACL,IAAK,WACHtnB,KAAKknB,wBAA0BI,EAMnC,GAAIA,EAEF,YADAtnB,KAAKmnB,gBAIP,MAAMpZ,EAAc9O,EAAMY,cACtBG,KAAKqG,WAAa0H,GAAe/N,KAAKqG,SAASpM,SAAS8T,IAI5D/N,KAAKonB,qBAGPtH,gBACEvf,EAAaa,GAAGpB,KAAKqG,SAhKA,sBAgK2BpH,GAASe,KAAKqnB,eAAepoB,GAAO,KACpFsB,EAAaa,GAAGpB,KAAKqG,SAhKD,qBAgK2BpH,GAASe,KAAKqnB,eAAepoB,GAAO,KACnFsB,EAAaa,GAAGpB,KAAKqG,SAhKF,oBAgK2BpH,GAASe,KAAKqnB,eAAepoB,GAAO,KAClFsB,EAAaa,GAAGpB,KAAKqG,SAhKD,qBAgK2BpH,GAASe,KAAKqnB,eAAepoB,GAAO,KAGrFkoB,gBACE3Z,aAAaxN,KAAKyf,UAClBzf,KAAKyf,SAAW,KAII3Y,uBAAC3B,GACrB,OAAOnF,KAAK0H,MAAK,WACf,MAAMC,EAAOqf,GAAM1f,oBAAoBtH,KAAMmF,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBwC,EAAKxC,GACd,MAAM,IAAIc,UAAW,oBAAmBd,MAG1CwC,EAAKxC,GAAQnF,kBAUrBiH,EAAqB+f,IAMrB5rB,EAAmB4rB,ICrMJ,CACbzf,MAAAA,EACAO,OAAAA,EACA6D,SAAAA,GACAsD,SAAAA,GACA0C,SAAAA,GACA2F,MAAAA,GACAgC,UAAAA,GACAiJ,QAAAA,GACAM,UAAAA,GACA4C,IAAAA,GACAuB,MAAAA,GACAzH,QAAAA", "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n// Shoutout AngusCroll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector\n}\n\nconst getSelectorFromElement = element => {\n  const selector = getSelector(element)\n\n  if (selector) {\n    return document.querySelector(selector) ? selector : null\n  }\n\n  return null\n}\n\nconst getElementFromSelector = element => {\n  const selector = getSelector(element)\n\n  return selector ? document.querySelector(selector) : null\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(object)\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = callback => {\n  if (typeof callback === 'function') {\n    callback()\n  }\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getElementFromSelector,\n  getjQuery,\n  getNextActiveElement,\n  getSelectorFromElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction getUidEvent(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getEvent(element) {\n  const uid = getUidEvent(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    event.delegateTarget = element\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        event.delegateTarget = target\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, handler, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.originalHandler === handler && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const delegation = typeof handler === 'string'\n  const originalHandler = delegation ? delegationFunction : handler\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [delegation, originalHandler, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  if (!handler) {\n    handler = delegationFunction\n    delegationFunction = null\n  }\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    if (delegationFunction) {\n      delegationFunction = wrapFunction(delegationFunction)\n    } else {\n      handler = wrapFunction(handler)\n    }\n  }\n\n  const [delegation, originalHandler, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n  const events = getEvent(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, originalHandler, delegation ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = getUidEvent(originalHandler, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = delegation ?\n    bootstrapDelegationHandler(element, handler, delegationFunction) :\n    bootstrapHandler(element, handler)\n\n  fn.delegationSelector = delegation ? handler : null\n  fn.originalHandler = originalHandler\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, delegation)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const handlerKey of Object.keys(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      const event = storeElementEvent[handlerKey]\n      removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [delegation, originalHandler, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getEvent(element)\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof originalHandler !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!events || !events[typeEvent]) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, originalHandler, delegation ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    const storeElementEvent = events[typeEvent] || {}\n    for (const keyHandlers of Object.keys(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        const event = storeElementEvent[keyHandlers]\n        removeHandler(element, events, typeEvent, event.originalHandler, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = new Event(event, { bubbles, cancelable: true })\n\n    // merge custom information in our event\n    if (typeof args !== 'undefined') {\n      for (const key of Object.keys(args)) {\n        Object.defineProperty(evt, key, {\n          get() {\n            return args[key]\n          }\n        })\n      }\n    }\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isElement, toType } from './index'\nimport Manipulator from '../dom/manipulator'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const property of Object.keys(configTypes)) {\n      const expectedTypes = configTypes[property]\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport { executeAfterTransition, getElement } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Config from './util/config'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.2.0-beta1'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { getElementFromSelector, isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible } from '../util/index'\n\n/**\n * Constants\n */\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Config from './config'\nimport EventHandler from '../dom/event-handler'\nimport { execute } from './index'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  leftCallback: null,\n  rightCallback: null,\n  endCallback: null\n}\n\nconst DefaultType = {\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)',\n  endCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport Swipe from './util/swipe'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)',\n  keyboard: 'boolean',\n  ride: '(boolean|string)',\n  pause: '(string|boolean)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // todo: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getElementFromSelector,\n  getSelectorFromElement,\n  reflow\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  toggle: true,\n  parent: null\n}\n\nconst DefaultType = {\n  toggle: 'boolean',\n  parent: '(null|element)'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  const selector = getSelectorFromElement(this)\n  const selectorElements = SelectorEngine.find(selector)\n\n  for (const element of selectorElements) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  offset: [0, 2],\n  boundary: 'clippingParents',\n  reference: 'toggle',\n  display: 'dynamic',\n  popperConfig: null,\n  autoClose: true\n}\n\nconst DefaultType = {\n  offset: '(array|string|function)',\n  boundary: '(string|element)',\n  reference: '(string|element|object)',\n  display: 'string',\n  popperConfig: '(null|object|function)',\n  autoClose: '(boolean|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    this._menu = SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // todo:v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    const getToggleButton = SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode)\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Manipulator from '../dom/manipulator'\nimport { isElement } from './index'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport { execute, executeAfterTransition, getElement, reflow } from './index'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  isAnimated: false,\n  rootElement: 'body', // give the choice to place backdrop under different elements\n  clickCallback: null\n}\n\nconst DefaultType = {\n  className: 'string',\n  isVisible: 'boolean',\n  isAnimated: 'boolean',\n  rootElement: '(element|string)',\n  clickCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  trapElement: null, // The element to trap focus inside of\n  autofocus: true\n}\n\nconst DefaultType = {\n  trapElement: 'element',\n  autofocus: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, isRTL, isVisible, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport ScrollBarHelper from './util/scrollbar'\nimport BaseComponent from './base-component'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  focus: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  focus: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    for (const htmlElement of [window, this._dialog]) {\n      EventHandler.off(htmlElement, EVENT_KEY)\n    }\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        event.preventDefault()\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_CLICK_DISMISS, event => {\n      if (event.target !== event.currentTarget) { // click is inside modal-dialog\n        return\n      }\n\n      if (this._config.backdrop === 'static') {\n        this._triggerBackdropTransition()\n        return\n      }\n\n      if (this._config.backdrop) {\n        this.hide()\n      }\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport {\n  defineJQueryPlugin,\n  getElementFromSelector,\n  isDisabled,\n  isVisible\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport SelectorEngine from './dom/selector-engine'\nimport Backdrop from './util/backdrop'\nimport FocusTrap from './util/focustrap'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (!this._config.keyboard) {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\n/**\n * A pattern that recognizes a commonly useful subset of URLs that are safe.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst SAFE_URL_PATTERN = /^(?:(?:https?|mailto|ftp|tel|file|sms):|[^#&/:?]*(?:[#/?]|$))/i\n\n/**\n * A pattern that matches safe data URLs. Only matches image, video and audio types.\n *\n * Shoutout to Angular https://github.com/angular/angular/blob/12.2.x/packages/core/src/sanitization/url_sanitizer.ts\n */\nconst DATA_URL_PATTERN = /^data:(?:image\\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\\/(?:mpeg|mp4|ogg|webm)|audio\\/(?:mp3|oga|ogg|opus));base64,[\\d+/a-z]+=*$/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue) || DATA_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  div: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer'\nimport { getElement, isElement } from '../util/index'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  extraClass: '',\n  template: '<div></div>',\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist\n}\n\nconst DefaultType = {\n  extraClass: '(string|function)',\n  template: 'string',\n  content: 'object',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object'\n}\n\nconst DefaultContentType = {\n  selector: '(string|element)',\n  entry: '(string|element|function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg(this) : arg\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport { defineJQueryPlugin, findShadowRoot, getElement, getUID, isRTL, noop } from './util/index'\nimport { DefaultAllowlist } from './util/sanitizer'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport BaseComponent from './base-component'\nimport TemplateFactory from './util/template-factory'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  animation: true,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n              '<div class=\"tooltip-arrow\"></div>' +\n              '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  trigger: 'hover focus',\n  title: '',\n  delay: 0,\n  html: false,\n  selector: false,\n  placement: 'top',\n  offset: [0, 0],\n  container: false,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  boundary: 'clippingParents',\n  customClass: '',\n  sanitize: true,\n  sanitizeFn: null,\n  allowList: DefaultAllowlist,\n  popperConfig: null\n}\n\nconst DefaultType = {\n  animation: 'boolean',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string',\n  delay: '(number|object)',\n  html: 'boolean',\n  selector: '(string|boolean)',\n  placement: '(string|function)',\n  offset: '(array|string|function)',\n  container: '(string|element|boolean)',\n  fallbackPlacements: 'array',\n  boundary: '(string|element)',\n  customClass: '(string|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  allowList: 'object',\n  popperConfig: '(null|object|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = false\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle(event) {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (event) {\n      const context = this._initializeOnDelegatedTarget(event)\n\n      context._activeTrigger.click = !context._activeTrigger.click\n\n      if (context._isWithActiveTrigger()) {\n        context._enter()\n      } else {\n        context._leave()\n      }\n\n      return\n    }\n\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this.tip) {\n      this.tip.remove()\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    if (this._popper) {\n      this._popper.update()\n    } else {\n      this._createPopper(tip)\n    }\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      const previousHoverState = this._isHovered\n\n      this._isHovered = false\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (previousHoverState) {\n        this._leave()\n      }\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = false\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        tip.remove()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n\n      this._disposePopper()\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // todo: remove this check on v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // todo: on v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    let isShown = false\n    if (this.tip) {\n      isShown = this._isShown()\n      this.tip.remove()\n      this.tip = null\n    }\n\n    this._disposePopper()\n    this.tip = this._createTipElement(content)\n\n    if (isShown) {\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._config.title\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = typeof this._config.placement === 'function' ?\n      this._config.placement.call(this, tip, this._element) :\n      this._config.placement\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    this._popper = Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return typeof arg === 'function' ? arg.call(this._element) : arg\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...(typeof this._config.popperConfig === 'function' ? this._config.popperConfig(defaultBsPopperConfig) : this._config.popperConfig)\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => this.toggle(event))\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._config.selector) {\n      this._config = {\n        ...this._config,\n        trigger: 'manual',\n        selector: ''\n      }\n    } else {\n      this._fixTitle()\n    }\n  }\n\n  _fixTitle() {\n    const title = this._config.originalTitle\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    config.originalTitle = this._element.getAttribute('title') || ''\n    config.title = this._resolvePossibleFunction(config.title) || config.originalTitle\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const key in this._config) {\n      if (this.constructor.Default[key] !== this._config[key]) {\n        config[key] = this._config[key]\n      }\n    }\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin } from './util/index'\nimport Tooltip from './tooltip'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  placement: 'right',\n  offset: [0, 8],\n  trigger: 'click',\n  content: '',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n              '<div class=\"popover-arrow\"></div>' +\n              '<h3 class=\"popover-header\"></h3>' +\n              '<div class=\"popover-body\"></div>' +\n            '</div>'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElement, isDisabled, isVisible } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: [0.1, 0.5, 1],\n      rootMargin: this._getRootMargin()\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n  _getRootMargin() {\n    return this._config.offset ? `${this._config.offset}px 0px -30%` : this._config.rootMargin\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(anchor.hash, this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(anchor.hash, anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, getElementFromSelector, getNextActiveElement, isDisabled } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport BaseComponent from './base-component'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst SELECTOR_DROPDOWN_ITEM = '.dropdown-item'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = ':not(.dropdown-toggle)'\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // todo:v6: could be only `tab`\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // todo: should Throw exception on v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    const complete = () => {\n      if (isAnimated) { // todo: maybe is redundant\n        element.classList.add(CLASS_NAME_SHOW)\n      }\n\n      if (element.getAttribute('role') !== 'tab') {\n        return\n      }\n\n      element.focus()\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, isAnimated)\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const isAnimated = element.classList.contains(CLASS_NAME_FADE)\n    const complete = () => {\n      if (isAnimated) { // todo maybe is redundant\n        element.classList.remove(CLASS_NAME_SHOW)\n      }\n\n      if (element.getAttribute('role') !== 'tab') {\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, isAnimated)\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n    const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n    const nextActiveElement = getNextActiveElement(this._getChildren().filter(element => !isDisabled(element)), event.target, isNext, true)\n\n    if (nextActiveElement) {\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `#${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    toggle(SELECTOR_DROPDOWN_ITEM, CLASS_NAME_ACTIVE)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { defineJQueryPlugin, reflow } from './util/index'\nimport EventHandler from './dom/event-handler'\nimport BaseComponent from './base-component'\nimport { enableDismissTrigger } from './util/component-functions'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        this._hasMouseInteraction = isInteracting\n        break\n      case 'focusin':\n      case 'focusout':\n        this._hasKeyboardInteraction = isInteracting\n        break\n      default:\n        break\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap (v5.2.0-beta1): index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert'\nimport But<PERSON> from './src/button'\nimport Carousel from './src/carousel'\nimport Collapse from './src/collapse'\nimport Dropdown from './src/dropdown'\nimport Modal from './src/modal'\nimport Offcanvas from './src/offcanvas'\nimport Popover from './src/popover'\nimport ScrollSpy from './src/scrollspy'\nimport Tab from './src/tab'\nimport Toast from './src/toast'\nimport Tooltip from './src/tooltip'\n\nexport default {\n  <PERSON><PERSON>,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"]}