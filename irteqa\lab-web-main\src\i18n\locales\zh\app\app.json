{"item": {"add": "添加 {{- heading}}", "startDate": {"label": "开始日期"}, "endDate": {"label": "结束日期"}, "description": {"label": "说明"}}, "buttons": {"add": {"label": "添加"}, "delete": {"label": "删除"}}, "printDialog": {"heading": "下载简历", "quality": {"label": "品质"}, "printType": {"label": "类型", "types": {"unconstrained": "不受限制", "fitInA4": "适合A4", "multiPageA4": "多页A4"}}, "helpText": ["此导出方法使用 HTML 画布将恢复转换为图像并打印到 PDF 上。 这意味着它将失去所有选择/解析能力。", "如果这对您很重要，请尝试打印续版，而不是使用 Cmd/Ctrl + P 或下面的打印按钮。 结果可能会因为输出依赖于浏览器而有所改变，但它已知最适合最新版本的Google Chrome。"], "buttons": {"cancel": "取消", "saveAsPdf": "保存为 PDF"}}, "panZoomAnimation": {"helpText": "您可以随时在画板上平移和缩放，以更仔细地查看简历。"}, "markdownHelpText": "你可以使用 <1>GitHub 倾向的 Markdown</1> 来美化这部分文字."}