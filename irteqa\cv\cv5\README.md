# ResumeForge - Fully frontend Vue.js based resume maker

ResumeForge is a vue.js based resume maker with easy to use UI to create awesome resumes. It is Privacy Friendly. Everything is stored in your own browser when you save and reloaded when you open ResumeForge next time you want to update your resume. You can download a backup for the resume in json file.


[![Netlify Status](https://api.netlify.com/api/v1/badges/c23094b0-601e-446d-9cdc-da4a9f13d0c7/deploy-status?branch=master)](https://app.netlify.com/sites/resumeforge/deploys) [![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## Features

- Easy to use UI
- Live previews
- Preview whole resume
- Cross platform
- Privacy friendly


## Tech Stack
- Vue.js
- HTML/CSS
- Bootstrap
- Javascript


## Roadmap

- [X] Adding Browser Fonts
- [ ] Drag/Drop template creation
- [ ] Improve Preview
- [ ] Refactoring
- [ ] ATS scanner Friendly

## Run Locally

Clone the project

```bash
  git clone https://github.com/zainaftab44/ResumeMaker.git
```

Go to the project directory

Install dependencies

```bash
  npm install
```

Start the server

```bash
  npm run serve
```
You need [Vue2 installed](https://v2.vuejs.org/v2/guide/installation.html)
### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).


## Contributing

Contributions are always welcome!

All your contributions are handled as MIT License. When you create a pull request or update the documentation, we assume you agreed this clause.
