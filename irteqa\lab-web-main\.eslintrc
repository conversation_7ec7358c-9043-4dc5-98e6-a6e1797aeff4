{"parser": "babel-es<PERSON>", "parserOptions": {"sourceType": "module", "allowImportExportEverywhere": false, "codeFrame": false}, "extends": ["airbnb", "plugin:react/recommended", "prettier", "prettier/react"], "env": {"browser": true, "jest": true}, "rules": {"jsx-a11y/no-static-element-interactions": 0, "jsx-a11y/click-events-have-key-events": 0, "jsx-a11y/label-has-associated-control": 0, "react/jsx-filename-extension": 0, "react/no-array-index-key": 0, "no-restricted-syntax": 0, "no-param-reassign": 0, "react/prop-types": 0, "no-plusplus": 0}}