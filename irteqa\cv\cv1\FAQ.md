<p align="center">
  <a href="https://github.com/tbaltrushaitis/cv/releases"><img src="https://img.shields.io/github/release/tbaltrushaitis/cv.svg?style=flat" alt="GitHub release"></a>
  <a href="https://github.com/tbaltrushaitis/cv/blob/master/LICENSE"><img src="https://img.shields.io/badge/license-MIT-green.svg?style=flat" alt="License"></a>
  <!--/ <img src="https://img.shields.io/david/tbaltrushaitis/cv.svg" alt="Dependencies"> /-->
</p>

<p align="center">
  <h1 align="center">CV, Resume and Portfolio website template</h1>
</p>

<p align="center">
  <a href="http://bit.ly/tomascv?ref=readme">
    <img src="assets/img/cv-demo-01.gif" max-width="640px" max-height="360px" alt="Modern CV, Resume and Portfolio website template" />
  </a>
</p>
<!--/
/-->

<!-- # Best-in-Class modern CV, Resume and Portfolio # -->

:mortar_board: Best in Class modern CV, Resume and Portfolio website template.
All-in-One-Page site with simple and fully customizable builder.

`CV` `resume` `portfolio` `template` `portfolio-website` `CV-template` `IT` `resume-template` `resume-website`

---

## FAQ ##

### What is a CV? ###
- CV stands for "**Curriculum Vitae**"
- Curriculum vitae is Latin for "**Life Story**"
- A CV is a formatted version of **one's master experience document**

### What to Include in a CV ###
A CV should at least include the following:
- Personal information
- Work experience
- Skills
- Education
- Personal profile and interests
- References

#### Personal Information ####
Personal information should include name, address, telephone, and email. I will suggest you put this information at the top of your CV and make it look like a letterhead.

#### Work Experience ####
List what you have done - most recent work experience first.
Include a short job description and your responsibilities.
Make sure your work experience is on the first page of your CV. This outlines your skills and selling points.

#### Skills ####
Skills are best described with a list.
List your skills - most important and relevant first.

#### Education ####
Education is best described with a list.
List what you have studied - most recent education first.
> Don't forget subject options, special project, courses, or diplomas.

#### References ####
List only a few names - like a teacher from your place of study, and a superior from a work situation - and make sure they can easily be reached and are willing to give you a good reference.

#### Personal Profile ####
Personal profile should include additional information about your age, status, interests and other relevant information that can produce a positive picture of your character. Put this in the last paragraph of your CV.

---

:scorpius:

[CV]: http://bit.ly/tomascv?ref_domain=github.com&ref_section=docs&ref_file=FAQ
