<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل مستخدم جديد</title>
    <link rel="stylesheet" href="styles/style.css">
</head> 
<body>
<a class="sninl" for="" href="login.php">تسجيل الدخول</a>

    <div class="snupf">
    <form action="signup.php"  method="POST" class="snup">
        <img src="img/offbtn.png" alt="user"style="width:150px;">
        <input name="fname"  type="text" placeholder="الاسم الأول"value="<?php echo isset($_POST['fname']) ? $_POST['fname'] : ''; ?>">
        <input name="lname" type="text" placeholder="إسم العائلة" value="<?php echo isset($_POST['lname']) ? $_POST['lname'] : ''; ?>" ><br>
        <input name="email" type="text" placeholder="الايميل" style=" width: auto;" value="<?php echo isset($_POST['email']) ? $_POST['email'] : ''; ?>"><br>
        <input name="uname" type="text" placeholder="اسم المستخدم" style=" width: auto;" value="<?php echo isset($_POST['uname']) ? $_POST['uname'] : ''; ?>"><br>
        <input name="fword" type="password" placeholder="كلمة المرور" value="<?php echo isset($_POST['fword']) ? $_POST['fword'] : ''; ?>">
        <input name="pword" type="password" placeholder="تأكيد كلمة المرور" value="<?php echo isset($_POST['pword']) ? $_POST['pword'] : ''; ?>">
        <button name="login" type="submit">تسجيل</button> 
    </form>
    <?php 
    include('lgcon.php');
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $fname = $_POST['fname'];
    $lname = $_POST['lname'];
    $email = $_POST['email'];
    $uname = $_POST['uname'];
    $pword = $_POST['pword'];
    $fword=$_POST['fword'];
    $hash = password_hash($pword, PASSWORD_DEFAULT);
    $sql = "INSERT INTO userst(fname,lname,email,uname,pword) VALUES ('$fname','$lname','$email','$uname','$hash')";
   $fpword= $fword !== $pword;
    if($fpword){
        echo"<h4><label>- كلمات المرور غير متطابقة","<br>";
} elseif (isset($_POST['login']));
{
    
 $empty  = empty($fname&&$uname&&$pword&&$fword);
if($empty){
    echo"<h4><label>- رجاءاً تعبئة البيانات المطلوبة";
}

else{
try{if($fword == $pword){
    mysqli_query($conn, $sql);
        echo"<h4>تم التسجيل بنجاح";
 //       header("Location: login_form.html");
 echo '<script>';
        echo 'setTimeout(function(){ window.location.href = "login_form.html"; }, 1000);';
        echo '</script>';
} 
}
catch(mysqli_sql_exception){
    echo"<h4><label>- اسم المستخدم غير متاح جرب اسم مستخدم اخر","<br>";
}

}

}
}
    ?>
    <?php
    mysqli_close($conn);
    
    ?>
</div>

<script>
if ( window.history.replaceState ) {
  window.history.replaceState( null, null, window.location.href );
}
</script>
</body>
</html>
   
