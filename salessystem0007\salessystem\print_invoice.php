<?php
require_once 'config/init.php';
redirectIfNotLoggedIn();

if (!isset($_GET['id']) || !isset($_GET['type'])) {
    header("Location: index.php");
    exit();
}

$id = intval($_GET['id']);
$type = $_GET['type'];

$db = getCurrentUserDB();

if ($type === 'sale') {
    // جلب بيانات فاتورة المبيعات
    $stmt = $db->prepare("SELECT s.*, c.name AS customer_name, c.address AS customer_address, 
                         c.phone AS customer_phone, c.tax_number AS customer_tax_number
                         FROM sales s
                         LEFT JOIN customers c ON s.customer_id = c.id
                         WHERE s.id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $invoice = $result->fetch_assoc();
    
    if (!$invoice) {
        die("فاتورة المبيعات غير موجودة");
    }
    
    // جلب عناصر الفاتورة
    $items = $db->query("SELECT si.*, p.name AS product_name 
                        FROM sale_items si
                        JOIN products p ON si.product_id = p.id
                        WHERE si.sale_id = $id");
    
    $title = "فاتورة مبيعات";
} elseif ($type === 'purchase') {
    // جلب بيانات فاتورة المشتريات
    $stmt = $db->prepare("SELECT * FROM purchases WHERE id = ?");
    $stmt->bind_param("i", $id);
    $stmt->execute();
    $result = $stmt->get_result();
    $invoice = $result->fetch_assoc();
    
    if (!$invoice) {
        die("فاتورة المشتريات غير موجودة");
    }
    
    // جلب عناصر الفاتورة
    $items = $db->query("SELECT * FROM purchase_items WHERE purchase_id = $id");
    
    $title = "فاتورة مشتريات";
} else {
    die("نوع الفاتورة غير صحيح");
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $title; ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .invoice-box {
            max-width: 800px;
            margin: auto;
            padding: 20px;
            border: 1px solid #eee;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
        }
        .header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .company-info {
            text-align: right;
        }
        .invoice-info {
            text-align: left;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: right;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        .total-row {
            font-weight: bold;
        }
        .notes {
            margin-top: 30px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        @media print {
            body {
                padding: 0;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-box">
        <div class="header">
            <div class="company-info">
                <h2>شركة المبيعات</h2>
                <p>العنوان: المدينة، الحي، الشارع</p>
                <p>الهاتف: 0*********</p>
                <p>الرقم الضريبي: *********</p>
            </div>
            <div class="invoice-info">
                <h2><?php echo $title; ?></h2>
                <p><strong>رقم الفاتورة:</strong> <?php echo $invoice['invoice_number']; ?></p>
                <p><strong>التاريخ:</strong> <?php echo $invoice['date']; ?></p>
            </div>
        </div>
        
        <div class="client-info">
            <?php if ($type === 'sale'): ?>
            <h3>معلومات العميل:</h3>
            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($invoice['customer_name'] ?? 'N/A'); ?></p>
            <p><strong>الهاتف:</strong> <?php echo htmlspecialchars($invoice['customer_phone'] ?? 'N/A'); ?></p>
            <p><strong>الرقم الضريبي:</strong> <?php echo htmlspecialchars($invoice['customer_tax_number'] ?? 'N/A'); ?></p>
            <p><strong>العنوان:</strong> <?php echo htmlspecialchars($invoice['customer_address'] ?? 'N/A'); ?></p>
            <?php else: ?>
            <h3>معلومات المورد:</h3>
            <p><strong>الاسم:</strong> <?php echo htmlspecialchars($invoice['supplier_name']); ?></p>
            <?php endif; ?>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th>#</th>
                    <th>المنتج</th>
                    <th>الكمية</th>
                    <th>السعر</th>
                    <th>الضريبة %</th>
                    <th>قيمة الضريبة</th>
                    <th>المجموع</th>
                </tr>
            </thead>
            <tbody>
                <?php 
                $counter = 1;
                while ($item = $items->fetch_assoc()): 
                    $item_subtotal = $item['quantity'] * $item['unit_price'];
                ?>
                <tr>
                    <td><?php echo $counter++; ?></td>
                    <td><?php echo htmlspecialchars($item['product_name'] ?? $item['product_name']); ?></td>
                    <td><?php echo $item['quantity']; ?></td>
                    <td><?php echo number_format($item['unit_price'], 2); ?> ر.س</td>
                    <td><?php echo number_format($item['tax_rate'], 2); ?>%</td>
                    <td><?php echo number_format($item['tax_amount'], 2); ?> ر.س</td>
                    <td><?php echo number_format($item['total_price'], 2); ?> ر.س</td>
                </tr>
                <?php endwhile; ?>
            </tbody>
            <tfoot>
                <tr class="total-row">
                    <td colspan="5"></td>
                    <td>المجموع الفرعي:</td>
                    <td><?php echo number_format($invoice['subtotal'], 2); ?> ر.س</td>
                </tr>
                <tr class="total-row">
                    <td colspan="5"></td>
                    <td>الضريبة:</td>
                    <td><?php echo number_format($invoice['tax_amount'], 2); ?> ر.س</td>
                </tr>
                <tr class="total-row">
                    <td colspan="5"></td>
                    <td>الإجمالي:</td>
                    <td><?php echo number_format($invoice['total_amount'], 2); ?> ر.س</td>
                </tr>
            </tfoot>
        </table>
        
        <?php if (!empty($invoice['notes'])): ?>
        <div class="notes">
            <h4>ملاحظات:</h4>
            <p><?php echo htmlspecialchars($invoice['notes']); ?></p>
        </div>
        <?php endif; ?>
        
        <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()" class="btn btn-primary">طباعة الفاتورة</button>
        </div>
    </div>
</body>
</html>