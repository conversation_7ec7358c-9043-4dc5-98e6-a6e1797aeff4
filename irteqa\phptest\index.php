<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
  <!--
    <form action="index.php" method="GET">
        <input type="text" name="name">
        <input type="text" name="pword">
        <input type="submit">
    </form>
    <br>
    <br>
<form action="index.php" method="GET" class="form">
    <input type="number" name="number1" id="">
    <input type="number" name="number2" id="">
    <input type="submit" value="reslt">
</form>
<br>
<br>
<br>
<form action="index.php" method="GET" class="form">
    <input type="number" name="number1" id="">
    <input type="number" name="number2" id="">
    <input type="text" name="op" id="">
    <input type="submit" value="reslt">
</form>
<form action="index.php" method="GET" class="">
<input type="text" name="xname" id="">
<input type="submit">
</form>
<br>
<br>
<form action="index.php" method="GET" class="">
<input type="text" name="name" id="">
<input type="submit">
</form>-->
    <?php
/*
//functions 1
function hello(){
    $user="nour";
    if($user==="nour"){
    echo "hello " . $user;
}else{
    echo"bye " . $user;
}
}
hello();*/
/*
//function 2
function hello($x){
    $user=$x;
    if($user==="nour"){
    echo "hello " . $user;
}else{
    echo"bye " . $user;
}
}
hello("anas");
echo"<br>";
hello("nour");
*/
/*
//function 3
function add($x,$y){
    return $x+$y;}
    echo add(5,10);
*/
/*
// for 1
for ($i=0; $i <= 5 ; $i++) { 
    echo $i + 10 . "<br>";
}
*/
/*
//foreach 
$family=["nour","majed","anas"];
foreach($family as $member){
    echo "$member <br>";
}*/

/*
// associative array =>
$xname=$_GET["xname"];
$family=["designer" => "noor" ,
 "programmer" => "majed" ,
  "gamer" => "anas"];
echo $family[$xname];
*/

/*
// associative array => =>
$name=$_GET["name"];
$score=["nour"=>["score"=>"90","grade"=>"A"],
"majed"=>["score"=>"80","grade"=>"C"],
"anas"=>["score"=>"60","grade"=>"F"],
];
echo "score: " . $score[$name]["score"];
echo "<br>";
echo "grade: " . $score[$name]["grade"];
*/


/*
// array []- array
$family=array("father","mather","prather");
$family[2]="love";
$family[3]="sister";
echo $family[2];
echo "<br>";
echo $family[3];
echo "<br>";
echo count($family);
*/

// 2 stattment &&=and ||=or
/*
$uname="ishag";
$email="<EMAIL>";

if($uname!=="ihag" && $email==="<EMAIL>"){
    echo "welcom";
}*/


/*
//calculater element value
$number1=$_GET["number1"];
$numner2=$_GET["number2"];
$op=$_GET["op"];
if(empty($number1)){
    $result="number one empty";  
}elseif(empty($number2)){
    $result="number tow empyt";
} elseif(empty($op)){
        $result="op empty";
    }else{
if($op === "+"){
    $result = $_GET["number1"] + $_GET["number2"];
}elseif($op === "-"){
    $result=$_GET["number1"] - $_GET["number2"];
}
if($op === "*"){
    $result = $_GET["number1"] * $_GET["number2"];
}elseif($op === "/"){
    $result=$_GET["number1"] / $_GET["number2"];

}}
echo "<br>";
echo "<h1>$result";
*/


// if stattment
/*
$num1 = $_GET["number1"];
$num2 = $_GET["number2"];

if($num1 > $num2){
    echo "number beg";
    if($num1 === 20){
        echo "<br>";
        echo "number 20";
    }
}elseif($num1 < $num2){
    echo "number yang";
}else{
    echo "nunbers equls";
}
*/

//clculet start
//echo $_GET["number1"] + $_GET["number2"];


 /*   //form start
echo $_GET["name"];
echo $_GET["pword"];
//namepwodr//
echo "<br>";
echo $_REQUEST["name"];
echo $_REQUEST["pword"];
//=namepword post for self//
echo "<br>";*/


    //string start
    /*
    $string="Dr Ishag";
    echo $string;//Ir Ishag
    echo "<br>";
    echo strtolower($string);//dr ishag
   echo "<br>";
    echo strtoupper($string);//DR ISHAG
    echo "<br>";
    echo strlen($string);//8
    echo "<br>";
    echo $string[3];//i
    echo "<br>";
     $string[3] = "e";//mr eshag
    echo $string ;
    echo "<br>";
   echo str_replace("Dr","Mr",$string);//Mr eshag
   echo "<br>";
  echo substr($string,3,4);//esha
  echo "<br>-----------------";
  //intger start
  echo "<br>";
  echo 20*50;//1000
  echo "<br>";
  echo pow(5,3);//125;
  echo "<br>";
  echo 11% 3;//2
  echo "<br>";
  $num = 5 ;
  $num *=5;
  echo $num;//25
echo "<br>";
$num=5;
$num++;
echo $num;//6
echo "<br>";
echo max(3,11);//11
echo "<br>";
echo min(3,11);//3
echo "<br>-----------";
*/

    ?>
</body>
</html>