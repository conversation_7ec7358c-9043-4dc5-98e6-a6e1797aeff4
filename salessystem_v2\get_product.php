<?php
/**
 * جلب بيانات منتج للتعديل
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// تعيين نوع المحتوى
header('Content-Type: application/json');

// التحقق من وجود معرف المنتج
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'معرف المنتج غير صحيح']);
    exit();
}

$product_id = intval($_GET['id']);

// الحصول على اتصال قاعدة البيانات
$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    echo json_encode(['success' => false, 'message' => 'فشل الاتصال بقاعدة البيانات']);
    exit();
}

try {
    // جلب بيانات المنتج
    $stmt = $db->prepare("SELECT * FROM products WHERE id = ?");
    $stmt->bind_param("i", $product_id);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'المنتج غير موجود']);
        exit();
    }
    
    $product = $result->fetch_assoc();
    $stmt->close();
    
    // إرجاع البيانات
    echo json_encode([
        'success' => true,
        'product' => [
            'id' => $product['id'],
            'name' => $product['name'],
            'description' => $product['description'],
            'price' => floatval($product['price']),
            'tax_rate' => floatval($product['tax_rate']),
            'category' => $product['category']
        ]
    ]);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
} finally {
    if (isset($db)) {
        $db->close();
    }
}
?>
