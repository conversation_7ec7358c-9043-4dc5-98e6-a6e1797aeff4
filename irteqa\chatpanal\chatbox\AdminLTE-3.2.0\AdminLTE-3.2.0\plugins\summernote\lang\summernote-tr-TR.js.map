{"version": 3, "file": "lang/summernote-tr-TR.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,OADF;AAEJC,QAAAA,MAAM,EAAE,QAFJ;AAGJC,QAAAA,SAAS,EAAE,aAHP;AAIJC,QAAAA,KAAK,EAAE,SAJH;AAKJC,QAAAA,MAAM,EAAE,kBALJ;AAMJC,QAAAA,IAAI,EAAE,WANF;AAOJC,QAAAA,aAAa,EAAE,aAPX;AAQJC,QAAAA,SAAS,EAAE,WARP;AASJC,QAAAA,WAAW,EAAE,WATT;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,MAAM,EAAE,YAFH;AAGLC,QAAAA,UAAU,EAAE,eAHP;AAILC,QAAAA,UAAU,EAAE,WAJP;AAKLC,QAAAA,aAAa,EAAE,WALV;AAMLC,QAAAA,SAAS,EAAE,aANN;AAOLC,QAAAA,UAAU,EAAE,aAPP;AAQLC,QAAAA,SAAS,EAAE,mBARN;AASLC,QAAAA,YAAY,EAAE,2BATT;AAULC,QAAAA,WAAW,EAAE,cAVR;AAWLC,QAAAA,cAAc,EAAE,gBAXX;AAYLC,QAAAA,SAAS,EAAE,YAZN;AAaLC,QAAAA,aAAa,EAAE,mBAbV;AAcLC,QAAAA,SAAS,EAAE,0BAdN;AAeLC,QAAAA,eAAe,EAAE,aAfZ;AAgBLC,QAAAA,eAAe,EAAE,uBAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,+BAjBjB;AAkBLC,QAAAA,GAAG,EAAE,kBAlBA;AAmBLC,QAAAA,MAAM,EAAE,eAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,kBAFN;AAGLpB,QAAAA,MAAM,EAAE,YAHH;AAILgB,QAAAA,GAAG,EAAE,mBAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,UADF;AAEJtB,QAAAA,MAAM,EAAE,eAFJ;AAGJuB,QAAAA,MAAM,EAAE,mBAHJ;AAIJC,QAAAA,IAAI,EAAE,oBAJF;AAKJC,QAAAA,aAAa,EAAE,mBALX;AAMJT,QAAAA,GAAG,EAAE,kBAND;AAOJU,QAAAA,eAAe,EAAE,mBAPb;AAQJC,QAAAA,WAAW,EAAE;AART,OA1CC;AAoDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,mBAFR;AAGLC,QAAAA,WAAW,EAAE,kBAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,iBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,YAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OApDA;AA8DPC,MAAAA,EAAE,EAAE;AACFpC,QAAAA,MAAM,EAAE;AADN,OA9DG;AAiEPqC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,CAAC,EAAE,GAFE;AAGLC,QAAAA,UAAU,EAAE,QAHP;AAILC,QAAAA,GAAG,EAAE,WAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAjEA;AA6EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,sBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA7EA;AAiFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,QADC;AAEPC,QAAAA,UAAU,EAAE,WAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAjFF;AAsFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,UADF;AAETC,QAAAA,OAAO,EAAE,iBAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,aAJG;AAKTC,QAAAA,MAAM,EAAE,eALC;AAMTC,QAAAA,KAAK,EAAE,aANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OAtFJ;AA+FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,UADH;AAELC,QAAAA,IAAI,EAAE,iBAFD;AAGLC,QAAAA,UAAU,EAAE,iBAHP;AAILC,QAAAA,UAAU,EAAE,YAJP;AAKLC,QAAAA,WAAW,EAAE,WALR;AAMLC,QAAAA,cAAc,EAAE,mBANX;AAOLC,QAAAA,KAAK,EAAE,SAPF;AAQLC,QAAAA,cAAc,EAAE,wBARX;AASLC,QAAAA,QAAQ,EAAE;AATL,OA/FA;AA0GPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,YADH;AAERC,QAAAA,KAAK,EAAE,OAFC;AAGRC,QAAAA,cAAc,EAAE,oBAHR;AAIRC,QAAAA,MAAM,EAAE,OAJA;AAKRC,QAAAA,mBAAmB,EAAE,wBALb;AAMRC,QAAAA,aAAa,EAAE,OANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OA1GH;AAmHP3B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,gBADf;AAEJ,gBAAQ,sBAFJ;AAGJ,gBAAQ,oBAHJ;AAIJ,eAAO,mBAJH;AAKJ,iBAAS,mBALL;AAMJ,gBAAQ,6BANJ;AAOJ,kBAAU,8BAPN;AAQJ,qBAAa,oCART;AASJ,yBAAiB,oCATb;AAUJ,wBAAgB,0BAVZ;AAWJ,uBAAe,qBAXX;AAYJ,yBAAiB,gBAZb;AAaJ,wBAAgB,qBAbZ;AAcJ,uBAAe,8BAdX;AAeJ,+BAAuB,4BAfnB;AAgBJ,6BAAqB,sBAhBjB;AAiBJ,mBAAW,sCAjBP;AAkBJ,kBAAU,sCAlBN;AAmBJ,sBAAc,sDAnBV;AAoBJ,oBAAY,uDApBR;AAqBJ,oBAAY,uDArBR;AAsBJ,oBAAY,uDAtBR;AAuBJ,oBAAY,uDAvBR;AAwBJ,oBAAY,uDAxBR;AAyBJ,oBAAY,uDAzBR;AA0BJ,gCAAwB,mBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAnHC;AAgJP4B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,SADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OAhJF;AAoJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,kBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AApJN;AADiB,GAA5B;AA2JD,CA5JD,EA4JGC,MA5JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-tr-TR.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'tr-TR': {\n      font: {\n        bold: 'Kalın',\n        italic: '<PERSON><PERSON><PERSON>',\n        underline: 'Altı çizili',\n        clear: 'Temizle',\n        height: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n        name: '<PERSON><PERSON><PERSON>',\n        strikethrough: '<PERSON>st<PERSON> çizili',\n        subscript: 'Alt Simge',\n        superscript: 'Üst Simge',\n        size: 'Yazı tipi boyutu',\n      },\n      image: {\n        image: 'Resim',\n        insert: 'Resim ekle',\n        resizeFull: 'Orjinal boyut',\n        resizeHalf: '1/2 boyut',\n        resizeQuarter: '1/4 boyut',\n        floatLeft: 'Sola hizala',\n        floatRight: 'Sağa hizala',\n        floatNone: 'Hizal<PERSON>yı kaldır',\n        shapeRounded: 'Şekil: Yuvarlatılmış Köşe',\n        shapeCircle: 'Şekil: Daire',\n        shapeThumbnail: 'Şekil: <PERSON>.<PERSON>',\n        shapeNone: 'Şekil: Yok',\n        dragImageHere: '<PERSON><PERSON><PERSON> s<PERSON>',\n        dropImage: '<PERSON>si<PERSON> veya metni bırakın',\n        selectFromFiles: '<PERSON><PERSON>a seçin',\n        maximumFileSize: 'Maksimum dosya boyutu',\n        maximumFileSizeError: 'Maksimum dosya boyutu aşıldı.',\n        url: 'Resim bağlantısı',\n        remove: 'Resimi Kaldır',\n        original: 'Original',\n      },\n      video: {\n        video: 'Video',\n        videoLink: 'Video bağlantısı',\n        insert: 'Video ekle',\n        url: 'Video bağlantısı?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion veya Youku)',\n      },\n      link: {\n        link: 'Bağlantı',\n        insert: 'Bağlantı ekle',\n        unlink: 'Bağlantıyı kaldır',\n        edit: 'Bağlantıyı düzenle',\n        textToDisplay: 'Görüntülemek için',\n        url: 'Bağlantı adresi?',\n        openInNewWindow: 'Yeni pencerede aç',\n        useProtocol: \"Varsayılan protokolü kullan\",\n      },\n      table: {\n        table: 'Tablo',\n        addRowAbove: 'Yukarı satır ekle',\n        addRowBelow: 'Aşağı satır ekle',\n        addColLeft: 'Sola sütun ekle',\n        addColRight: 'Sağa sütun ekle',\n        delRow: 'Satırı sil',\n        delCol: 'Sütunu sil',\n        delTable: 'Tabloyu sil',\n      },\n      hr: {\n        insert: 'Yatay çizgi ekle',\n      },\n      style: {\n        style: 'Biçim',\n        p: 'p',\n        blockquote: 'Alıntı',\n        pre: 'Önbiçimli',\n        h1: 'Başlık 1',\n        h2: 'Başlık 2',\n        h3: 'Başlık 3',\n        h4: 'Başlık 4',\n        h5: 'Başlık 5',\n        h6: 'Başlık 6',\n      },\n      lists: {\n        unordered: 'Madde işaretli liste',\n        ordered: 'Numaralı liste',\n      },\n      options: {\n        help: 'Yardım',\n        fullscreen: 'Tam ekran',\n        codeview: 'HTML Kodu',\n      },\n      paragraph: {\n        paragraph: 'Paragraf',\n        outdent: 'Girintiyi artır',\n        indent: 'Girintiyi azalt',\n        left: 'Sola hizala',\n        center: 'Ortaya hizala',\n        right: 'Sağa hizala',\n        justify: 'Yasla',\n      },\n      color: {\n        recent: 'Son renk',\n        more: 'Daha fazla renk',\n        background: 'Arka plan rengi',\n        foreground: 'Yazı rengi',\n        transparent: 'Seffaflık',\n        setTransparent: 'Şeffaflığı ayarla',\n        reset: 'Sıfırla',\n        resetToDefault: 'Varsayılanlara sıfırla',\n        cpSelect: 'Seç',\n      },\n      shortcut: {\n        shortcuts: 'Kısayollar',\n        close: 'Kapat',\n        textFormatting: 'Yazı biçimlendirme',\n        action: 'Eylem',\n        paragraphFormatting: 'Paragraf biçimlendirme',\n        documentStyle: 'Biçim',\n        extraKeys: 'İlave anahtarlar',\n      },\n      help: {\n        'insertParagraph': 'Paragraf ekler',\n        'undo': 'Son komudu geri alır',\n        'redo': 'Son komudu yineler',\n        'tab': 'Girintiyi artırır',\n        'untab': 'Girintiyi azaltır',\n        'bold': 'Kalın yazma stilini ayarlar',\n        'italic': 'İtalik yazma stilini ayarlar',\n        'underline': 'Altı çizgili yazma stilini ayarlar',\n        'strikethrough': 'Üstü çizgili yazma stilini ayarlar',\n        'removeFormat': 'Biçimlendirmeyi temizler',\n        'justifyLeft': 'Yazıyı sola hizalar',\n        'justifyCenter': 'Yazıyı ortalar',\n        'justifyRight': 'Yazıyı sağa hizalar',\n        'justifyFull': 'Yazıyı her iki tarafa yazlar',\n        'insertUnorderedList': 'Madde işaretli liste ekler',\n        'insertOrderedList': 'Numaralı liste ekler',\n        'outdent': 'Aktif paragrafın girintisini azaltır',\n        'indent': 'Aktif paragrafın girintisini artırır',\n        'formatPara': 'Aktif bloğun biçimini paragraf (p) olarak değiştirir',\n        'formatH1': 'Aktif bloğun biçimini başlık 1 (h1) olarak değiştirir',\n        'formatH2': 'Aktif bloğun biçimini başlık 2 (h2) olarak değiştirir',\n        'formatH3': 'Aktif bloğun biçimini başlık 3 (h3) olarak değiştirir',\n        'formatH4': 'Aktif bloğun biçimini başlık 4 (h4) olarak değiştirir',\n        'formatH5': 'Aktif bloğun biçimini başlık 5 (h5) olarak değiştirir',\n        'formatH6': 'Aktif bloğun biçimini başlık 6 (h6) olarak değiştirir',\n        'insertHorizontalRule': 'Yatay çizgi ekler',\n        'linkDialog.show': 'Bağlantı ayar kutusunu gösterir',\n      },\n      history: {\n        undo: 'Geri al',\n        redo: 'Yinele',\n      },\n      specialChar: {\n        specialChar: 'ÖZEL KARAKTERLER',\n        select: 'Özel Karakterleri seçin',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "subscript", "superscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "useProtocol", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "cpSelect", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}