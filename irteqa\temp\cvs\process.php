<?php
// process.php

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $skill = $_POST["skill"];
    $skill_level = $_POST["skill_level"];

    // قراءة البيانات المخزنة إن وجدت
    $skills_data = json_decode(file_get_contents('data.json'), true);

    // إضافة المهارة ومستوى المهارة الجديد إلى المصفوفة
    $skills_data[] = array("skill" => $skill, "skill_level" => $skill_level);

    // تحويل المصفوفة إلى نص JSON
    $json_data = json_encode($skills_data);

    // كتابة البيانات إلى ملف
    file_put_contents('data.json', $json_data);
}

// إعادة توجيه المستخدم إلى صفحة الإدخال بعد المعالجة
header("Location: form.html");
exit;
?>
