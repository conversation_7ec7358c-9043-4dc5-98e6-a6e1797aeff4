<html lang="en-US"><head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <title>test's Blog! – Just another WordPress site</title>
<meta name="robots" content="max-image-preview:large">
<link rel="dns-prefetch" href="//stats.wp.com">
<link rel="alternate" type="application/rss+xml" title="test's Blog! » Feed" href="http://127.0.0.1:82/wordpress/feed/">
<link rel="alternate" type="application/rss+xml" title="test's Blog! » Comments Feed" href="http://127.0.0.1:82/wordpress/comments/feed/">
<script type="text/javascript">
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/14.0.0\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/14.0.0\/svg\/","svgExt":".svg","source":{"concatemoji":"http:\/\/127.0.0.1:82\/wordpress\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.3.1"}};
/*! This file is auto-generated */
!function(i,n){var o,s,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),r=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===r[t]})}function u(e,t,n){switch(t){case"flag":return n(e,"\ud83c\udff3\ufe0f\u200d\u26a7\ufe0f","\ud83c\udff3\ufe0f\u200b\u26a7\ufe0f")?!1:!n(e,"\ud83c\uddfa\ud83c\uddf3","\ud83c\uddfa\u200b\ud83c\uddf3")&&!n(e,"\ud83c\udff4\udb40\udc67\udb40\udc62\udb40\udc65\udb40\udc6e\udb40\udc67\udb40\udc7f","\ud83c\udff4\u200b\udb40\udc67\u200b\udb40\udc62\u200b\udb40\udc65\u200b\udb40\udc6e\u200b\udb40\udc67\u200b\udb40\udc7f");case"emoji":return!n(e,"\ud83e\udef1\ud83c\udffb\u200d\ud83e\udef2\ud83c\udfff","\ud83e\udef1\ud83c\udffb\u200b\ud83e\udef2\ud83c\udfff")}return!1}function f(e,t,n){var r="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?new OffscreenCanvas(300,150):i.createElement("canvas"),a=r.getContext("2d",{willReadFrequently:!0}),o=(a.textBaseline="top",a.font="600 32px Arial",{});return e.forEach(function(e){o[e]=t(a,e,n)}),o}function t(e){var t=i.createElement("script");t.src=e,t.defer=!0,i.head.appendChild(t)}"undefined"!=typeof Promise&&(o="wpEmojiSettingsSupports",s=["flag","emoji"],n.supports={everything:!0,everythingExceptFlag:!0},e=new Promise(function(e){i.addEventListener("DOMContentLoaded",e,{once:!0})}),new Promise(function(t){var n=function(){try{var e=JSON.parse(sessionStorage.getItem(o));if("object"==typeof e&&"number"==typeof e.timestamp&&(new Date).valueOf()<e.timestamp+604800&&"object"==typeof e.supportTests)return e.supportTests}catch(e){}return null}();if(!n){if("undefined"!=typeof Worker&&"undefined"!=typeof OffscreenCanvas&&"undefined"!=typeof URL&&URL.createObjectURL&&"undefined"!=typeof Blob)try{var e="postMessage("+f.toString()+"("+[JSON.stringify(s),u.toString(),p.toString()].join(",")+"));",r=new Blob([e],{type:"text/javascript"}),a=new Worker(URL.createObjectURL(r),{name:"wpTestEmojiSupports"});return void(a.onmessage=function(e){c(n=e.data),a.terminate(),t(n)})}catch(e){}c(n=f(s,u,p))}t(n)}).then(function(e){for(var t in e)n.supports[t]=e[t],n.supports.everything=n.supports.everything&&n.supports[t],"flag"!==t&&(n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&n.supports[t]);n.supports.everythingExceptFlag=n.supports.everythingExceptFlag&&!n.supports.flag,n.DOMReady=!1,n.readyCallback=function(){n.DOMReady=!0}}).then(function(){return e}).then(function(){var e;n.supports.everything||(n.readyCallback(),(e=n.source||{}).concatemoji?t(e.concatemoji):e.wpemoji&&e.twemoji&&(t(e.twemoji),t(e.wpemoji)))}))}((window,document),window._wpemojiSettings);
</script>
<style type="text/css">
img.wp-smiley,
img.emoji {
display: inline !important;
border: none !important;
box-shadow: none !important;
height: 1em !important;
width: 1em !important;
margin: 0 0.07em !important;
vertical-align: -0.1em !important;
background: none !important;
padding: 0 !important;
}
</style>
<link rel="stylesheet" id="wp-block-library-css" href="http://127.0.0.1:82/wordpress/wp-includes/css/dist/block-library/style.min.css?ver=6.3.1" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/wc-blocks.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-active-filters-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/active-filters.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-add-to-cart-form-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/add-to-cart-form.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-packages-style-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/packages-style.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-all-products-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/all-products.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-all-reviews-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/all-reviews.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-attribute-filter-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/attribute-filter.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-breadcrumbs-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/breadcrumbs.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-catalog-sorting-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/catalog-sorting.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-customer-account-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/customer-account.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-featured-category-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/featured-category.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-featured-product-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/featured-product.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-mini-cart-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/mini-cart.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-price-filter-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/price-filter.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-add-to-cart-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-add-to-cart.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-button-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-button.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-categories-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-categories.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-image-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-image.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-image-gallery-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-image-gallery.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-query-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-query.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-results-count-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-results-count.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-reviews-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-reviews.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-sale-badge-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-sale-badge.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-search-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-search.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-sku-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-sku.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-stock-indicator-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-stock-indicator.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-summary-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-summary.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-title-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-title.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-rating-filter-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/rating-filter.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-reviews-by-category-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/reviews-by-category.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-reviews-by-product-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/reviews-by-product.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-product-details-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/product-details.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-single-product-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/single-product.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-stock-filter-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/stock-filter.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-cart-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/cart.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-checkout-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/checkout.css?ver=10.6.6" type="text/css" media="all">
<link rel="stylesheet" id="wc-blocks-style-mini-cart-contents-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/packages/woocommerce-blocks/build/mini-cart-contents.css?ver=10.6.6" type="text/css" media="all">
<style id="classic-theme-styles-inline-css" type="text/css">
/*! This file is auto-generated */
.wp-block-button__link{color:#fff;background-color:#32373c;border-radius:9999px;box-shadow:none;text-decoration:none;padding:calc(.667em + 2px) calc(1.333em + 2px);font-size:1.125em}.wp-block-file__button{background:#32373c;color:#fff;text-decoration:none}
</style>
<style id="global-styles-inline-css" type="text/css">
body{--wp--preset--color--black: #000000;--wp--preset--color--cyan-bluish-gray: #abb8c3;--wp--preset--color--white: #ffffff;--wp--preset--color--pale-pink: #f78da7;--wp--preset--color--vivid-red: #cf2e2e;--wp--preset--color--luminous-vivid-orange: #ff6900;--wp--preset--color--luminous-vivid-amber: #fcb900;--wp--preset--color--light-green-cyan: #7bdcb5;--wp--preset--color--vivid-green-cyan: #00d084;--wp--preset--color--pale-cyan-blue: #8ed1fc;--wp--preset--color--vivid-cyan-blue: #0693e3;--wp--preset--color--vivid-purple: #9b51e0;--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg,rgba(6,147,227,1) 0%,rgb(155,81,224) 100%);--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg,rgb(122,220,180) 0%,rgb(0,208,130) 100%);--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg,rgba(252,185,0,1) 0%,rgba(255,105,0,1) 100%);--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg,rgba(255,105,0,1) 0%,rgb(207,46,46) 100%);--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg,rgb(238,238,238) 0%,rgb(169,184,195) 100%);--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg,rgb(74,234,220) 0%,rgb(151,120,209) 20%,rgb(207,42,186) 40%,rgb(238,44,130) 60%,rgb(251,105,98) 80%,rgb(254,248,76) 100%);--wp--preset--gradient--blush-light-purple: linear-gradient(135deg,rgb(255,206,236) 0%,rgb(152,150,240) 100%);--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg,rgb(254,205,165) 0%,rgb(254,45,45) 50%,rgb(107,0,62) 100%);--wp--preset--gradient--luminous-dusk: linear-gradient(135deg,rgb(255,203,112) 0%,rgb(199,81,192) 50%,rgb(65,88,208) 100%);--wp--preset--gradient--pale-ocean: linear-gradient(135deg,rgb(255,245,203) 0%,rgb(182,227,212) 50%,rgb(51,167,181) 100%);--wp--preset--gradient--electric-grass: linear-gradient(135deg,rgb(202,248,128) 0%,rgb(113,206,126) 100%);--wp--preset--gradient--midnight: linear-gradient(135deg,rgb(2,3,129) 0%,rgb(40,116,252) 100%);--wp--preset--font-size--small: 13px;--wp--preset--font-size--medium: 20px;--wp--preset--font-size--large: 36px;--wp--preset--font-size--x-large: 42px;--wp--preset--spacing--20: 0.44rem;--wp--preset--spacing--30: 0.67rem;--wp--preset--spacing--40: 1rem;--wp--preset--spacing--50: 1.5rem;--wp--preset--spacing--60: 2.25rem;--wp--preset--spacing--70: 3.38rem;--wp--preset--spacing--80: 5.06rem;--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);}:where(.is-layout-flex){gap: 0.5em;}:where(.is-layout-grid){gap: 0.5em;}body .is-layout-flow > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}body .is-layout-flow > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}body .is-layout-flow > .aligncenter{margin-left: auto !important;margin-right: auto !important;}body .is-layout-constrained > .alignleft{float: left;margin-inline-start: 0;margin-inline-end: 2em;}body .is-layout-constrained > .alignright{float: right;margin-inline-start: 2em;margin-inline-end: 0;}body .is-layout-constrained > .aligncenter{margin-left: auto !important;margin-right: auto !important;}body .is-layout-constrained > :where(:not(.alignleft):not(.alignright):not(.alignfull)){max-width: var(--wp--style--global--content-size);margin-left: auto !important;margin-right: auto !important;}body .is-layout-constrained > .alignwide{max-width: var(--wp--style--global--wide-size);}body .is-layout-flex{display: flex;}body .is-layout-flex{flex-wrap: wrap;align-items: center;}body .is-layout-flex > *{margin: 0;}body .is-layout-grid{display: grid;}body .is-layout-grid > *{margin: 0;}:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}.has-black-color{color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-color{color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-color{color: var(--wp--preset--color--white) !important;}.has-pale-pink-color{color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-color{color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-color{color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-color{color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-color{color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-color{color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-color{color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-color{color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-color{color: var(--wp--preset--color--vivid-purple) !important;}.has-black-background-color{background-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-background-color{background-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-background-color{background-color: var(--wp--preset--color--white) !important;}.has-pale-pink-background-color{background-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-background-color{background-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-background-color{background-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-background-color{background-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-background-color{background-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-background-color{background-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-background-color{background-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-background-color{background-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-background-color{background-color: var(--wp--preset--color--vivid-purple) !important;}.has-black-border-color{border-color: var(--wp--preset--color--black) !important;}.has-cyan-bluish-gray-border-color{border-color: var(--wp--preset--color--cyan-bluish-gray) !important;}.has-white-border-color{border-color: var(--wp--preset--color--white) !important;}.has-pale-pink-border-color{border-color: var(--wp--preset--color--pale-pink) !important;}.has-vivid-red-border-color{border-color: var(--wp--preset--color--vivid-red) !important;}.has-luminous-vivid-orange-border-color{border-color: var(--wp--preset--color--luminous-vivid-orange) !important;}.has-luminous-vivid-amber-border-color{border-color: var(--wp--preset--color--luminous-vivid-amber) !important;}.has-light-green-cyan-border-color{border-color: var(--wp--preset--color--light-green-cyan) !important;}.has-vivid-green-cyan-border-color{border-color: var(--wp--preset--color--vivid-green-cyan) !important;}.has-pale-cyan-blue-border-color{border-color: var(--wp--preset--color--pale-cyan-blue) !important;}.has-vivid-cyan-blue-border-color{border-color: var(--wp--preset--color--vivid-cyan-blue) !important;}.has-vivid-purple-border-color{border-color: var(--wp--preset--color--vivid-purple) !important;}.has-vivid-cyan-blue-to-vivid-purple-gradient-background{background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;}.has-light-green-cyan-to-vivid-green-cyan-gradient-background{background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;}.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;}.has-luminous-vivid-orange-to-vivid-red-gradient-background{background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;}.has-very-light-gray-to-cyan-bluish-gray-gradient-background{background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;}.has-cool-to-warm-spectrum-gradient-background{background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;}.has-blush-light-purple-gradient-background{background: var(--wp--preset--gradient--blush-light-purple) !important;}.has-blush-bordeaux-gradient-background{background: var(--wp--preset--gradient--blush-bordeaux) !important;}.has-luminous-dusk-gradient-background{background: var(--wp--preset--gradient--luminous-dusk) !important;}.has-pale-ocean-gradient-background{background: var(--wp--preset--gradient--pale-ocean) !important;}.has-electric-grass-gradient-background{background: var(--wp--preset--gradient--electric-grass) !important;}.has-midnight-gradient-background{background: var(--wp--preset--gradient--midnight) !important;}.has-small-font-size{font-size: var(--wp--preset--font-size--small) !important;}.has-medium-font-size{font-size: var(--wp--preset--font-size--medium) !important;}.has-large-font-size{font-size: var(--wp--preset--font-size--large) !important;}.has-x-large-font-size{font-size: var(--wp--preset--font-size--x-large) !important;}
.wp-block-navigation a:where(:not(.wp-element-button)){color: inherit;}
:where(.wp-block-post-template.is-layout-flex){gap: 1.25em;}:where(.wp-block-post-template.is-layout-grid){gap: 1.25em;}
:where(.wp-block-columns.is-layout-flex){gap: 2em;}:where(.wp-block-columns.is-layout-grid){gap: 2em;}
.wp-block-pullquote{font-size: 1.5em;line-height: 1.6;}
</style>
<link rel="stylesheet" id="woocommerce-layout-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/assets/css/woocommerce-layout.css?ver=8.0.3" type="text/css" media="all">
<link rel="stylesheet" id="woocommerce-smallscreen-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/assets/css/woocommerce-smallscreen.css?ver=8.0.3" type="text/css" media="only screen and (max-width: 768px)">
<link rel="stylesheet" id="woocommerce-general-css" href="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/assets/css/woocommerce.css?ver=8.0.3" type="text/css" media="all">
<style id="woocommerce-inline-inline-css" type="text/css">
.woocommerce form .form-row .required { visibility: visible; }
</style>
<link rel="stylesheet" id="owl-carousel-min-css" href="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/vendors/css/owl.carousel.min.css?ver=6.3.1" type="text/css" media="all">
<link rel="stylesheet" id="font-awesome-css" href="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/vendors/css/font-awesome.min.css?ver=6.3.1" type="text/css" media="all">
<link rel="stylesheet" id="animate-css" href="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/vendors/css/animate.css?ver=6.3.1" type="text/css" media="all">
<link rel="stylesheet" id="cosmobit-core-css" href="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/css/core.css?ver=6.3.1" type="text/css" media="all">
<link rel="stylesheet" id="cosmobit-theme-css" href="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/css/themes.css?ver=6.3.1" type="text/css" media="all">
<link rel="stylesheet" id="cosmobit-woocommerce-css" href="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/css/woo-styles.css?ver=6.3.1" type="text/css" media="all">
<link rel="stylesheet" id="cosmobit-style-css" href="http://127.0.0.1:82/wordpress/wp-content/themes/auru/style.css?ver=6.3.1" type="text/css" media="all">
<style id="cosmobit-style-inline-css" type="text/css">
.dt__pagetitle:before {
                background-image: -moz-linear-gradient(0deg,var(--dt-sec-color) 0,rgba(14, 34, 88, 0.9) 100%);
                background-image: -webkit-linear-gradient(0deg,var(--dt-sec-color) 0,rgba(14, 34, 88, 0.9) 100%);
            }
.dt-container {
                    max-width: 1252px;
                }

</style>
<link rel="stylesheet" id="cosmobit-google-fonts-css" href="http://127.0.0.1:82/wordpress/wp-content/fonts/cfd09dfa1be2d3ed8be0b0afab43c476.css" type="text/css" media="all">
<link rel="stylesheet" id="auru-parent-theme-style-css" href="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/style.css?ver=6.3.1" type="text/css" media="all">
<link rel="stylesheet" id="auru-child-theme-style-css" href="http://127.0.0.1:82/wordpress/wp-content/themes/auru/style.css?ver=6.3.1" type="text/css" media="all">
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-includes/js/dist/vendor/wp-polyfill-inert.min.js?ver=3.1.2" id="wp-polyfill-inert-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-includes/js/dist/vendor/regenerator-runtime.min.js?ver=0.13.11" id="regenerator-runtime-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-includes/js/dist/vendor/wp-polyfill.min.js?ver=3.15.0" id="wp-polyfill-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-includes/js/dist/hooks.min.js?ver=c6aec9a8d4e5a5d543a1" id="wp-hooks-js"></script>
<script type="text/javascript" src="https://stats.wp.com/w.js?ver=202335" id="woo-tracks-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-includes/js/jquery/jquery.min.js?ver=3.7.0" id="jquery-core-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1" id="jquery-migrate-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/vendors/js/owl.carousel.min.js?ver=1" id="owl-carousel-js"></script>
<link rel="https://api.w.org/" href="http://127.0.0.1:82/wordpress/wp-json/"><link rel="EditURI" type="application/rsd+xml" title="RSD" href="http://127.0.0.1:82/wordpress/xmlrpc.php?rsd">
<meta name="generator" content="WordPress 6.3.1">
<meta name="generator" content="WooCommerce 8.0.3">
<noscript><style>.woocommerce-product-gallery{ opacity: 1 !important; }</style></noscript>
    <style type="text/css">
        body  h4.site-title,
    body  p.site-description {
        color: #161C2D;
    }
    </style>
    <script src="http://127.0.0.1:82/wordpress/wp-includes/js/wp-emoji-release.min.js?ver=6.3.1" defer=""></script></head>
<body class="home blog btn--effect-five menu__active-one theme-cosmobit woocommerce-js">

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#content">Skip to content</a>

<!--=== / Start: As--Header (Topbar + Navbar (Mobile Menu)) / === -->
<header id="dt__header" class="dt__header header--thirteen">
<div class="dt__header-inner">
    <div class="dt__header-navwrapper" style="min-height: 121px;">
        <div class="dt__header-navwrapperinner">
            <!--=== / Start: As--Navbar / === -->
            <div class="dt__navbar dt-d-none dt-d-lg-block">
                <div class="dt__navbar-wrapper is--sticky">
                    <div class="dt-container">
                        <div class="dt-row">
                            <div class="dt-col-2 dt-my-auto">									
                                <div class="site--logo">
                                    <div class="site--logoinner">
                                                    <a href="http://127.0.0.1:82/wordpress/" class="site--title">
            <h4 class="site-title">
                test's Blog!				</h4>
        </a>	
                            <p class="site-description">Just another WordPress site</p>
                                            </div>
                                </div>
                            </div>
                            <div class="dt-col-10 dt-my-auto">
                                <div class="dt__header-topbar dt-d-lg-block dt-d-none">
                                                                        </div>
                                <div class="dt__header-mid dt-d-none">
                                                                            <ul class="dt__navbar-list-right">
                                                                                </ul>	
                                </div>	
                                <div class="dt__navbar-menu">
                                    <nav class="dt__navbar-nav">
                                                                                </nav>
                                    <div class="dt__navbar-right">
                                        <ul class="dt__navbar-list-right">
                                                <li class="dt__navbar-cart-item">
    <a href="javascript:void(0);" class="dt__navbar-cart-icon">
        <span class="cart--icon">
                                <strong class="cart-count">0</strong>
                        </span>
    </a>
    <div class="dt__navbar-shopcart">
        <div class="widget_shopping_cart">
<div class="widget_shopping_cart_content">

<p class="woocommerce-mini-cart__empty-message">No products in the cart.</p>

</div>
</div>

    </div>
</li>
                                                <li class="dt__navbar-search-item">
<button class="dt__navbar-search-toggle"><i class="fa fa-search" aria-hidden="true"></i></button>
<div class="dt__search search--header">
    <form method="get" class="dt__search-form" action="http://127.0.0.1:82/wordpress/" aria-label="search again">
        <label for="dt__search-form-1">
            <span class="screen-reader-text">Search for:</span>
            <input type="search" id="dt__search-form-1" class="dt__search-field" placeholder="search Here" value="" name="s">
        </label>
        <button type="submit" class="dt__search-submit search-submit"><i class="fa fa-search" aria-hidden="true"></i></button>
    </form>
    <button type="button" class="dt__search-close"><i class="fa fa-angle-up" aria-hidden="true"></i></button>
</div>
</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="logo-bg"></div>
                </div>
            </div>
            <!--=== / End: As--Navbar / === -->
            <!--=== / Start: As--Mobile Menu / === -->
            <div class="dt__mobilenav is--sticky dt-d-lg-none">
                <div class="dt__mobilenav-topbar">                              
                    <button type="button" class="dt__mobilenav-topbar-toggle" style="display: none;"><i class="fa fa-angle-double-down" aria-hidden="true"></i></button>
                    <div class="dt__mobilenav-topbar-content">
                        <div class="dt-container">
                            <div class="dt-row">
                                <div class="dt-col-12">
                                                                        </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="dt-container">
                    <div class="dt-row">
                        <div class="dt-col-12">
                            <div class="dt__mobilenav-menu">
                                <div class="dt__mobilenav-logo">
                                    <div class="site--logo">
                                                    <a href="http://127.0.0.1:82/wordpress/" class="site--title">
            <h4 class="site-title">
                test's Blog!				</h4>
        </a>	
                            <p class="site-description">Just another WordPress site</p>
                                            </div>
                                </div>
                                <div class="dt__mobilenav-toggles">
                                    <div class="dt__mobilenav-right">
                                        <ul class="dt__navbar-list-right"><li class="dt__navbar-cart-item">
    <a href="javascript:void(0);" class="dt__navbar-cart-icon">
        <span class="cart--icon">
                                <strong class="cart-count">0</strong>
                        </span>
    </a>
    <div class="dt__navbar-shopcart">
        <div class="widget_shopping_cart">
<div class="widget_shopping_cart_content">

<p class="woocommerce-mini-cart__empty-message">No products in the cart.</p>

</div>
</div>

    </div>
</li>
                                            <li class="dt__navbar-search-item">
<button class="dt__navbar-search-toggle"><i class="fa fa-search" aria-hidden="true"></i></button>
<div class="dt__search search--header">
    <form method="get" class="dt__search-form" action="http://127.0.0.1:82/wordpress/" aria-label="search again">
        <label for="dt__search-form-1">
            <span class="screen-reader-text">Search for:</span>
            <input type="search" id="dt__search-form-1" class="dt__search-field" placeholder="search Here" value="" name="s">
        </label>
        <button type="submit" class="dt__search-submit search-submit"><i class="fa fa-search" aria-hidden="true"></i></button>
    </form>
    <button type="button" class="dt__search-close"><i class="fa fa-angle-up" aria-hidden="true"></i></button>
</div>
</li>
                                                                                        </ul>
                                    </div>
                                    <div class="dt__mobilenav-mainmenu">
                                        <button type="button" class="hamburger dt__mobilenav-mainmenu-toggle">
                                            <span></span>
                                            <span></span>
                                            <span></span>
                                        </button>
                                        <div class="dt__mobilenav-mainmenu-content">
                                            <div class="off--layer"></div>
                                            <div class="dt__mobilenav-mainmenu-inner">
                                                <button type="button" class="dt__header-closemenu site--close"></button>
                                                                                                </div>
                                        </div>
                                    </div>                                        
                                </div>                                    
                            </div>
                        </div>
                    </div>
                </div>        
            </div>
            <!--=== / End: As--Mobile Menu / === -->
        </div>
    </div>
</div>
</header><section class="dt__pagetitle" style="background-image: url('http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/images/pagetitle_bg.jpg');">
<div class="dt-container">
    <div class="dt__pagetitle-content">
        <div class="dt__pagetitle-wrapper">
            <div class="title">
                <h1></h1>				</div>
            <ul class="dt__pagetitle-breadcrumb">
                <li class="breadcrumb-item"><a href="http://127.0.0.1:82/wordpress">Home</a></li><li class="breadcrumb-item active"></li>				</ul>
        </div>
    </div>
</div>
</section>
    
<div id="content" class="cosmobit-content">
<section class="dt__posts dt__posts--one dt-py-default">
<div class="dt-container">
    <div class="dt-row dt-g-5">
            
            <div class="dt-col-lg-8 dt-col-md-12 dt-col-12 wow fadeInUp" style="visibility: visible; animation-name: fadeInUp;">
            
                                <div id="post-1" class="dt__post dt-mb-4 post-1 post type-post status-publish format-standard hentry category-uncategorized">
    <div class="dt__post-outer">
    <div class="dt__post-top-meta">
        <ul class="top-meta-list">
            <li><div class="dt__post-category"><span class="before-icon fa fa-folder-o" aria-hidden="true"></span><a href="http://127.0.0.1:82/wordpress/category/uncategorized/" rel="category tag">Uncategorized</a></div></li>
        </ul>
    </div>
    <div class="dt__post-entry">
        <h5 class="dt__post-title"><a href="http://127.0.0.1:82/wordpress/2023/09/03/hello-world/" rel="bookmark">Hello world!</a></h5> 
        
<p>Welcome to WordPress. This is your first post. Edit or delete it, then start writing!</p>
    </div>
    <div class="dt__post-bottom-meta">
        <ul class="bottom-meta-list">
            <li>
                <div class="dt__post-author">
                    <a href="http://127.0.0.1:82/wordpress/author/test/">
                        <span class="dt__post-author-img">
                            <i class="fa fa-user-o" aria-hidden="true"></i>
                        </span>
                        <span class="dt__post-author-name">test</span>
                    </a>
                </div>
            </li>
        </ul>
        <ul class="bottom-meta-list">
            <li><div class="dt__post-date"><a href="http://127.0.0.1:82/wordpress/2023/09/"><i class="fa fa-calendar" aria-hidden="true"></i> Sep, Sun, 2023</a></div></li>
        </ul>
    </div>
</div>
</div>			</div>
        <div class="dt-col-lg-4 dt-col-md-12 dt-col-12">
<div class="dt_widget-area">
    <aside id="block-5" class="widget widget_block"><div class="wp-block-group is-layout-flow wp-block-group-is-layout-flow"><div class="wp-block-group__inner-container"><h2 class="wp-block-heading">Archives</h2><ul class="wp-block-archives-list wp-block-archives">	<li><a href="http://127.0.0.1:82/wordpress/2023/09/">September 2023</a></li>
</ul></div></div></aside><aside id="block-6" class="widget widget_block"><div class="wp-block-group is-layout-flow wp-block-group-is-layout-flow"><div class="wp-block-group__inner-container"><h2 class="wp-block-heading">Categories</h2><ul class="wp-block-categories-list wp-block-categories">	<li class="cat-item cat-item-1"><a href="http://127.0.0.1:82/wordpress/category/uncategorized/">Uncategorized</a>
</li>
</ul></div></div></aside>	</div>
</div>		</div>
</div>
</section>
</div></div>
<footer id="dt__footer" class="dt__footer dt__footer--one">
        <div class="dt__footer-middle">
        <div class="dt-container">
            <div class="dt-row dt-g-4">
                                                                                            </div>
        </div>
    </div>
        <div class="dt__footer-copyright">
        <div class="dt-container">
            <div class="dt-row dt-g-4 dt-mt-md-0">
                <div class="dt-col-md-4 dt-col-sm-6 dt-text-sm-left dt-text-center">
                        
                </div>
                <div class="dt-col-md-4 dt-col-sm-6 dt-text-sm-center dt-text-center">
                                                <div class="dt__footer-copyright-text">
                            Copyright © 2023 test's Blog! | Powered by <a href="https://desertthemes.com/" target="_blank">Desert Themes</a>							</div>
                        
                </div>
                <div class="dt-col-md-4 dt-col-sm-6 dt-text-sm-right dt-text-center">
                    <div class="widget widget_nav_menu">
                        <div class="menu-copyright-menu-container">
                                                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>
    
        <button type="button" class="dt__uptop"><i class="fa fa-angle-up" aria-hidden="true"></i></button>
    <script type="text/javascript">
    (function () {
        var c = document.body.className;
        c = c.replace(/woocommerce-no-js/, 'woocommerce-js');
        document.body.className = c;
    })();
</script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/assets/js/jquery-blockui/jquery.blockUI.min.js?ver=2.7.0-wc.8.0.3" id="jquery-blockui-js"></script>
<script type="text/javascript" id="wc-add-to-cart-js-extra">
/* <![CDATA[ */
var wc_add_to_cart_params = {"ajax_url":"\/wordpress\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/wordpress\/?wc-ajax=%%endpoint%%","i18n_view_cart":"View cart","cart_url":"http:\/\/127.0.0.1:82\/wordpress\/cart\/","is_cart":"","cart_redirect_after_add":"no"};
/* ]]> */
</script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/assets/js/frontend/add-to-cart.min.js?ver=8.0.3" id="wc-add-to-cart-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/assets/js/js-cookie/js.cookie.min.js?ver=2.1.4-wc.8.0.3" id="js-cookie-js"></script>
<script type="text/javascript" id="woocommerce-js-extra">
/* <![CDATA[ */
var woocommerce_params = {"ajax_url":"\/wordpress\/wp-admin\/admin-ajax.php","wc_ajax_url":"\/wordpress\/?wc-ajax=%%endpoint%%"};
/* ]]> */
</script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-content/plugins/woocommerce/assets/js/frontend/woocommerce.min.js?ver=8.0.3" id="woocommerce-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/vendors/js/wow.min.js?ver=6.3.1" id="wow-min-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/js/theme.js?ver=6.3.1" id="cosmobit-theme-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-content/themes/cosmobit/assets/js/custom.js?ver=6.3.1" id="cosmobit-custom-js-js"></script>
<script type="text/javascript" src="http://127.0.0.1:82/wordpress/wp-content/themes/auru/assets/vendors/js/anime.min.js?ver=6.3.1" id="jquery-anime-js"></script>


</body></html>