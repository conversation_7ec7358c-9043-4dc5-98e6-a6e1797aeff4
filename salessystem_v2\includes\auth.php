<?php
require_once '../config/init.php';

// معالجة تسجيل الدخول
if (isset($_POST['login'])) {
    $username = trim($_POST['username']);
    $password = $_POST['password'];
    
    // التحقق من بيانات المستخدم
    $stmt = $main_db->prepare("SELECT id, password FROM users WHERE username = ?");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $stmt->store_result();
    
    if ($stmt->num_rows == 1) {
        $stmt->bind_result($user_id, $hashed_password);
        $stmt->fetch();
        
        if (password_verify($password, $hashed_password)) {
            $_SESSION['user_id'] = $user_id;
            $_SESSION['username'] = $username;

            // تحديث آخر تسجيل دخول
            $update_stmt = $main_db->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $update_stmt->bind_param("i", $user_id);
            $update_stmt->execute();
            $update_stmt->close();

            // التأكد من وجود جداول المستخدم وإنشاؤها إذا لم تكن موجودة
            if (!userTableExists('customers', $username)) {
                createUserTables($username);
            }

            // تسجيل العملية
            logActivity('user_login', 'users', $user_id, null, null, 'تسجيل دخول المستخدم');

            header("Location: ../index.php");
            exit();
        }
    }
    
    $_SESSION['error'] = __('invalid_credentials');
    header("Location: ../login.php");
    exit();
}

// معالجة التسجيل
if (isset($_POST['register'])) {
    $username = trim($_POST['username']);
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
    $full_name = trim($_POST['full_name']);
    $email = trim($_POST['email']);
    
    // التحقق من عدم وجود مستخدم بنفس الاسم
    $stmt = $main_db->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->bind_param("ss", $username, $email);
    $stmt->execute();
    $stmt->store_result();
    
    if ($stmt->num_rows > 0) {
        $_SESSION['error'] = __('username_or_email_exists');
        header("Location: ../register.php");
        exit();
    }
    
    // إضافة المستخدم الجديد
    $stmt = $main_db->prepare("INSERT INTO users (username, password, full_name, email) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $username, $password, $full_name, $email);
    
    if ($stmt->execute()) {
        $user_id = $stmt->insert_id;

        // إنشاء جداول المستخدم الجديد في قاعدة بيانات العمليات
        createUserTables($username);

        // تسجيل العملية
        logActivity('user_register', 'users', $user_id, null, ['username' => $username, 'full_name' => $full_name, 'email' => $email], 'تسجيل مستخدم جديد');

        $_SESSION['success'] = __('register_success');
        header("Location: ../login.php");
        exit();
    } else {
        $_SESSION['error'] = __('register_failed');
        header("Location: ../register.php");
        exit();
    }
}

// دالة محسنة لإنشاء جداول المستخدم الجديد (تستدعي الدالة من db_config.php)
function createUserDatabase($user_id) {
    // هذه الدالة للتوافق مع النظام القديم
    // الآن نستخدم createUserTables بدلاً منها
    if (isset($_SESSION['username'])) {
        return createUserTables($_SESSION['username']);
    }
    return false;
}
?>
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT DEFAULT NULL,
        invoice_number VARCHAR(50) NOT NULL,
        date DATE NOT NULL,
        subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        discount_amount DECIMAL(10,2) DEFAULT 0.00,
        payment_method VARCHAR(50) DEFAULT 'نقدي',
        payment_status ENUM('paid','unpaid','partial') DEFAULT 'unpaid',
        notes TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY idx_invoice_number (invoice_number),
        INDEX idx_sales_date (date),
        INDEX idx_sales_customer (customer_id),
        INDEX idx_sales_payment_status (payment_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

    CREATE TABLE IF NOT EXISTS sale_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sale_id INT NOT NULL,
        product_id INT NOT NULL,
        product_name VARCHAR(255) NOT NULL DEFAULT '',
        quantity DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        tax_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00,
        tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        discount_amount DECIMAL(10,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_sale_id (sale_id),
        INDEX idx_product_id (product_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

    CREATE TABLE IF NOT EXISTS purchases (
        id INT AUTO_INCREMENT PRIMARY KEY,
        customer_id INT DEFAULT NULL,
        supplier_name VARCHAR(255) DEFAULT NULL,
        supplier_phone VARCHAR(20) DEFAULT NULL,
        supplier_email VARCHAR(255) DEFAULT NULL,
        supplier_tax_number VARCHAR(50) DEFAULT NULL,
        invoice_number VARCHAR(50) NOT NULL,
        date DATE NOT NULL,
        subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        discount_amount DECIMAL(10,2) DEFAULT 0.00,
        payment_method VARCHAR(50) DEFAULT 'نقدي',
        payment_status ENUM('paid','unpaid','partial') DEFAULT 'unpaid',
        notes TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY idx_invoice_number (invoice_number),
        INDEX idx_purchases_date (date),
        INDEX idx_purchases_customer (customer_id),
        INDEX idx_purchases_supplier (supplier_name),
        INDEX idx_purchases_payment_status (payment_status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

    CREATE TABLE IF NOT EXISTS purchase_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        purchase_id INT NOT NULL,
        product_id INT NOT NULL,
        product_name VARCHAR(255) NOT NULL DEFAULT '',
        quantity DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        tax_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00,
        tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        total_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
        discount_amount DECIMAL(10,2) DEFAULT 0.00,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_purchase_id (purchase_id),
        INDEX idx_product_id (product_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

    CREATE TABLE settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT DEFAULT NULL,
        setting_type ENUM('text', 'number', 'boolean', 'email', 'url', 'textarea') DEFAULT 'text',
        category VARCHAR(50) DEFAULT 'general',
        description TEXT DEFAULT NULL,
        is_required BOOLEAN DEFAULT FALSE,
        default_value TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_settings_category (category),
        INDEX idx_settings_type (setting_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    CREATE TABLE IF NOT EXISTS admins (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100) NOT NULL,
        role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
        permissions JSON DEFAULT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_admins_username (username),
        INDEX idx_admins_email (email),
        INDEX idx_admins_role (role),
        INDEX idx_admins_active (is_active)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

    CREATE TABLE IF NOT EXISTS activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT DEFAULT NULL,
        user_id INT DEFAULT NULL,
        action VARCHAR(100) NOT NULL,
        description TEXT DEFAULT NULL,
        ip_address VARCHAR(45) DEFAULT NULL,
        user_agent TEXT DEFAULT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_activity_admin (admin_id),
        INDEX idx_activity_user (user_id),
        INDEX idx_activity_action (action),
        INDEX idx_activity_date (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
    
    // تنفيذ استعلامات متعددة
    $queries = explode(';', $tables_sql);
    foreach ($queries as $query) {
        if (trim($query) != '') {
            $main_db->query($query);
        }
    }

    // تحديث الجداول الموجودة - إضافة الأعمدة المفقودة
    $update_queries = [
        // تحديث جدول products - إضافة عمود categories
        "ALTER TABLE products ADD COLUMN IF NOT EXISTS category VARCHAR(100) DEFAULT NULL AFTER name",

        // تحديث جدول customers - إضافة عمود email
        "ALTER TABLE customers ADD COLUMN IF NOT EXISTS email VARCHAR(255) DEFAULT NULL AFTER phone",

        // تحديث جدول sale_items - إضافة عمود product_name
        "ALTER TABLE sale_items ADD COLUMN IF NOT EXISTS product_name VARCHAR(255) NOT NULL DEFAULT '' AFTER product_id",

        // تحديث جدول purchase_items - إضافة الأعمدة المفقودة
        "ALTER TABLE purchase_items ADD COLUMN IF NOT EXISTS product_name VARCHAR(255) NOT NULL DEFAULT '' AFTER product_id",
        "ALTER TABLE purchase_items ADD COLUMN IF NOT EXISTS unit_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 AFTER quantity",

        // إنشاء جدول admins إذا لم يكن موجوداً في قاعدة البيانات الرئيسية
        "CREATE TABLE IF NOT EXISTS " . MAIN_DB_NAME . ".admins (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('super_admin', 'admin', 'moderator') DEFAULT 'admin',
            permissions JSON DEFAULT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_admins_username (username),
            INDEX idx_admins_email (email),
            INDEX idx_admins_role (role),
            INDEX idx_admins_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci",

        // إنشاء جدول activity_log إذا لم يكن موجوداً في قاعدة البيانات الرئيسية
        "CREATE TABLE IF NOT EXISTS " . MAIN_DB_NAME . ".activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_id INT DEFAULT NULL,
            user_id INT DEFAULT NULL,
            action VARCHAR(100) NOT NULL,
            description TEXT DEFAULT NULL,
            ip_address VARCHAR(45) DEFAULT NULL,
            user_agent TEXT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_activity_admin (admin_id),
            INDEX idx_activity_user (user_id),
            INDEX idx_activity_action (action),
            INDEX idx_activity_date (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci"
    ];

    // تنفيذ استعلامات التحديث
    foreach ($update_queries as $update_query) {
        try {
            $main_db->query($update_query);
        } catch (Exception $e) {
            // تجاهل الأخطاء إذا كان العمود موجود بالفعل
            error_log("Database update warning: " . $e->getMessage());
        }
    }

    // إنشاء مستخدم إدارة افتراضي إذا لم يكن موجوداً
    try {
        $check_admin = $main_db->query("SELECT COUNT(*) as count FROM " . MAIN_DB_NAME . ".admins");
        if ($check_admin) {
            $admin_count = $check_admin->fetch_assoc()['count'];
            if ($admin_count == 0) {
                $default_admin_password = password_hash('admin123', PASSWORD_DEFAULT);
                $insert_admin = $main_db->prepare("INSERT INTO " . MAIN_DB_NAME . ".admins (username, email, password, full_name, role) VALUES (?, ?, ?, ?, ?)");
                $username = 'admin';
                $email = '<EMAIL>';
                $full_name = 'مدير النظام';
                $role = 'super_admin';
                $insert_admin->bind_param("sssss", $username, $email, $default_admin_password, $full_name, $role);
                $insert_admin->execute();
                $insert_admin->close();
            }
        }
    } catch (Exception $e) {
        error_log("Error creating default admin: " . $e->getMessage());
    }

    // إضافة البيانات الافتراضية للإعدادات
    $default_settings = [
        // إعدادات الشركة
        ['company_name', 'اسم الشركة', 'text', 'company', 'اسم الشركة أو المؤسسة', true, 'شركتي'],
        ['company_address', 'عنوان الشركة', 'textarea', 'company', 'العنوان الكامل للشركة', true, 'المملكة العربية السعودية'],
        ['company_phone', '0501234567', 'text', 'company', 'رقم هاتف الشركة', true, '0501234567'],
        ['company_email', '<EMAIL>', 'email', 'company', 'البريد الإلكتروني للشركة', false, '<EMAIL>'],
        ['company_website', 'https://www.company.com', 'url', 'company', 'موقع الشركة الإلكتروني', false, 'https://www.company.com'],
        ['company_tax_number', '*********', 'text', 'company', 'الرقم الضريبي للشركة', false, '*********'],

        // الإعدادات العامة
        ['default_language', 'ar', 'text', 'general', 'اللغة الافتراضية للنظام', true, 'ar'],
        ['default_currency', 'ريال سعودي', 'text', 'general', 'العملة الافتراضية', true, 'ريال سعودي'],
        ['currency_symbol', 'ر.س', 'text', 'general', 'رمز العملة', true, 'ر.س'],
        ['currency_code', 'SAR', 'text', 'general', 'كود العملة', true, 'SAR'],
        ['decimal_places', '2', 'number', 'general', 'عدد الخانات العشرية', true, '2'],
        ['date_format', 'Y-m-d', 'text', 'general', 'تنسيق التاريخ', true, 'Y-m-d'],
        ['time_format', 'H:i:s', 'text', 'general', 'تنسيق الوقت', true, 'H:i:s'],
        ['timezone', 'Asia/Riyadh', 'text', 'general', 'المنطقة الزمنية', true, 'Asia/Riyadh'],

        // إعدادات الضريبة
        ['tax_enabled', 'true', 'boolean', 'tax', 'تفعيل نظام الضريبة', true, 'true'],
        ['default_tax_rate', '15', 'number', 'tax', 'نسبة الضريبة الافتراضية', true, '15'],
        ['tax_number_required', 'false', 'boolean', 'tax', 'إجبارية الرقم الضريبي', false, 'false'],
        ['tax_inclusive_pricing', 'false', 'boolean', 'tax', 'الأسعار شاملة الضريبة', false, 'false'],

        // إعدادات الفواتير
        ['invoice_prefix', 'INV-', 'text', 'invoice', 'بادئة رقم الفاتورة', true, 'INV-'],
        ['invoice_number_length', '6', 'number', 'invoice', 'طول رقم الفاتورة', true, '6'],
        ['invoice_footer', 'شكراً لتعاملكم معنا', 'textarea', 'invoice', 'تذييل الفاتورة', false, 'شكراً لتعاملكم معنا'],
        ['auto_invoice_number', 'true', 'boolean', 'invoice', 'ترقيم الفواتير تلقائياً', true, 'true'],

        // إعدادات النظام
        ['items_per_page', '20', 'number', 'system', 'عدد العناصر في الصفحة', true, '20'],
        ['backup_enabled', 'true', 'boolean', 'system', 'تفعيل النسخ الاحتياطي', false, 'true'],
        ['maintenance_mode', 'false', 'boolean', 'system', 'وضع الصيانة', false, 'false'],
        ['debug_mode', 'false', 'boolean', 'system', 'وضع التطوير', false, 'false'],

        // إعدادات الأمان
        ['session_timeout', '3600', 'number', 'security', 'مهلة انتهاء الجلسة (بالثواني)', true, '3600'],
        ['password_min_length', '6', 'number', 'security', 'الحد الأدنى لطول كلمة المرور', true, '6'],
        ['login_attempts', '5', 'number', 'security', 'عدد محاولات تسجيل الدخول المسموحة', true, '5'],
        ['account_lockout_time', '900', 'number', 'security', 'مدة قفل الحساب (بالثواني)', true, '900']
    ];

    // إضافة الإعدادات الافتراضية
    $stmt = $main_db->prepare("INSERT INTO settings (setting_key, setting_value, setting_type, category, description, is_required, default_value) VALUES (?, ?, ?, ?, ?, ?, ?)");
    if ($stmt) {
        foreach ($default_settings as $setting) {
            $stmt->bind_param("sssssss", $setting[0], $setting[1], $setting[2], $setting[3], $setting[4], $setting[5], $setting[6]);
            $stmt->execute();
        }
        $stmt->close();
    }

    // العودة إلى قاعدة البيانات الرئيسية
    $main_db->select_db(MAIN_DB_NAME);
}
?>