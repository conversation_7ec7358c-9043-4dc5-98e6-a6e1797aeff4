<!DOCTYPE html>
<html>
<head>
    <title>إدخال البيانات باستخدام AJAX</title>
    <!-- تضمين مكتبة jQuery -->
    <script src="js/jquery.min.js"></script>
</head>
<body>

<!-- نموذج إدخال البيانات -->
<form id="myForm">
    <input type="text" id="name" name="name" placeholder="الاسم"><br>
    <input type="text" id="email" name="email" placeholder="البريد الإلكتروني"><br>
    <button type="button" onclick="submitForm()">إرسال</button>
</form>

<!-- مكان عرض رد السيرفر -->
<div id="response"></div>

<script>
// دالة لإرسال البيانات باستخدام AJAX
function submitForm() {
    var formData = $("#myForm").serialize(); // جمع بيانات النموذج
    
    $.ajax({
        type: "POST",
        url: "http://127.0.0.1:5500/adtasks/process.php",
        data: "{}",
        contentType: "application/jsonp; charset=utf-8",
        success: function(data) {
            $("#response").html(data); // عرض رد السيرفر في العنصر ذو الهوية "response"
        }
    });
}
jQuery.support.cors = true;
</script>

</body>
</html>
