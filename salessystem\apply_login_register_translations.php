<?php
/**
 * أداة تطبيق ترجمات صفحات الدخول والتسجيل
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// معالجة طلب التطبيق
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_login_register'])) {
    $files_updated = 0;
    $replacements_made = 0;
    $errors = [];
    
    try {
        // تحميل ملفات الترجمة
        $ar_file = __DIR__ . '/languages/ar/lang.php';
        $ar_translations = file_exists($ar_file) ? require $ar_file : [];
        
        // إنشاء خريطة عكسية للترجمات (من النص العربي إلى المفتاح)
        $arabic_to_key_map = [];
        foreach ($ar_translations as $key => $arabic_text) {
            $arabic_to_key_map[$arabic_text] = $key;
        }
        
        // قائمة الملفات المراد تحديثها
        $files_to_update = ['login.php', 'register.php', 'includes/auth.php'];
        
        foreach ($files_to_update as $filename) {
            $filepath = __DIR__ . '/' . $filename;
            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                $original_content = $content;
                $file_replacements = 0;
                
                // استبدال النصوص العربية بدالة الترجمة
                foreach ($arabic_to_key_map as $arabic_text => $key) {
                    $before_count = substr_count($content, $arabic_text);
                    
                    if ($before_count > 0) {
                        // استبدال في محتوى HTML
                        $content = str_replace('>' . $arabic_text . '<', '><?php echo __(\'' . $key . '\'); ?><', $content);
                        
                        // استبدال في النصوص المقتبسة
                        $content = str_replace('"' . $arabic_text . '"', '"<?php echo __(\'' . $key . '\'); ?>"', $content);
                        $content = str_replace("'" . $arabic_text . "'", "'<?php echo __('$key'); ?>'", $content);
                        
                        // استبدال في الخصائص
                        $content = str_replace('placeholder="' . $arabic_text . '"', 'placeholder="<?php echo __(\'' . $key . '\'); ?>"', $content);
                        $content = str_replace('title="' . $arabic_text . '"', 'title="<?php echo __(\'' . $key . '\'); ?>"', $content);
                        $content = str_replace('value="' . $arabic_text . '"', 'value="<?php echo __(\'' . $key . '\'); ?>"', $content);
                        $content = str_replace('alt="' . $arabic_text . '"', 'alt="<?php echo __(\'' . $key . '\'); ?>"', $content);
                        
                        // استبدال في رسائل JavaScript
                        $content = str_replace('alert("' . $arabic_text . '")', 'alert("<?php echo __(\'' . $key . '\'); ?>")', $content);
                        $content = str_replace("alert('" . $arabic_text . "')", "alert('<?php echo __('$key'); ?>')", $content);
                        
                        // استبدال في رسائل PHP
                        $content = str_replace('$_SESSION[\'success\'] = "' . $arabic_text . '"', '$_SESSION[\'success\'] = __(\'' . $key . '\')', $content);
                        $content = str_replace('$_SESSION[\'error\'] = "' . $arabic_text . '"', '$_SESSION[\'error\'] = __(\'' . $key . '\')', $content);
                        $content = str_replace('$_SESSION["success"] = "' . $arabic_text . '"', '$_SESSION["success"] = __(\'' . $key . '\')', $content);
                        $content = str_replace('$_SESSION["error"] = "' . $arabic_text . '"', '$_SESSION["error"] = __(\'' . $key . '\')', $content);
                        
                        // استبدال في التعليقات
                        $content = str_replace('// ' . $arabic_text, '// <?php echo __(\'' . $key . '\'); ?>', $content);
                        $content = str_replace('/* ' . $arabic_text . ' */', '/* <?php echo __(\'' . $key . '\'); ?> */', $content);
                        $content = str_replace(' * ' . $arabic_text, ' * <?php echo __(\'' . $key . '\'); ?>', $content);
                        
                        // استبدال في الأخطاء والاستثناءات
                        $content = str_replace('$errors[] = "' . $arabic_text . '"', '$errors[] = __(\'' . $key . '\')', $content);
                        $content = str_replace('throw new Exception("' . $arabic_text . '")', 'throw new Exception(__(\'' . $key . '\'))', $content);
                        
                        // استبدال في القيم الافتراضية لقاعدة البيانات
                        $content = str_replace("DEFAULT '" . $arabic_text . "'", "DEFAULT '" . $arabic_text . "'", $content); // نبقي القيم الافتراضية كما هي
                        $content = str_replace("ENUM('" . $arabic_text . "'", "ENUM('" . $arabic_text . "'", $content); // نبقي قيم ENUM كما هي
                        
                        // استبدال في مصفوفات الإعدادات
                        $content = str_replace("'" . $arabic_text . "'", "'" . $arabic_text . "'", $content); // نبقي قيم الإعدادات كما هي في المصفوفات
                        
                        $after_count = substr_count($content, $arabic_text);
                        $file_replacements += ($before_count - $after_count);
                    }
                }
                
                // حفظ الملف إذا تم تعديله
                if ($content !== $original_content) {
                    if (file_put_contents($filepath, $content)) {
                        $files_updated++;
                        $replacements_made += $file_replacements;
                    } else {
                        $errors[] = "فشل في حفظ ملف $filename";
                    }
                }
            }
        }
        
        if ($files_updated > 0) {
            $_SESSION['success'] = "تم تحديث $files_updated ملف وإجراء $replacements_made استبدال في صفحات الدخول والتسجيل";
        } else {
            $_SESSION['info'] = "لا توجد نصوص تحتاج استبدال في صفحات الدخول والتسجيل";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء تطبيق ترجمات صفحات الدخول والتسجيل: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-gradient-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-shield"></i>
                        تطبيق ترجمات صفحات الدخول والتسجيل
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> حول الأداة</h6>
                        <p>هذه الأداة تقوم بتطبيق جميع الترجمات الموجودة في ملفات اللغة على صفحات الدخول والتسجيل.</p>
                        <ul>
                            <li><strong>3 ملفات رئيسية</strong>: login.php، register.php، includes/auth.php</li>
                            <li><strong>استبدال متقدم</strong>: HTML، JavaScript، PHP، التعليقات، رسائل الأخطاء</li>
                            <li><strong>معالجة خاصة</strong>: القيم الافتراضية لقاعدة البيانات والإعدادات</li>
                            <li><strong>أمان عالي</strong>: فحص وتأكيد قبل التطبيق</li>
                        </ul>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">أنواع الاستبدال المتقدم</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-code text-primary"></i> <strong>محتوى HTML</strong>: <code>&gt;النص&lt;</code></li>
                                        <li><i class="fas fa-quote-right text-success"></i> <strong>النصوص المقتبسة</strong>: <code>"النص"</code></li>
                                        <li><i class="fas fa-tags text-info"></i> <strong>خصائص HTML</strong>: <code>placeholder="النص"</code></li>
                                        <li><i class="fas fa-code text-warning"></i> <strong>رسائل JavaScript</strong>: <code>alert("النص")</code></li>
                                        <li><i class="fas fa-server text-danger"></i> <strong>رسائل PHP</strong>: <code>$_SESSION['success']</code></li>
                                        <li><i class="fas fa-comment text-secondary"></i> <strong>التعليقات</strong>: <code>// النص</code></li>
                                        <li><i class="fas fa-exclamation-triangle text-warning"></i> <strong>الأخطاء</strong>: <code>$errors[] = "النص"</code></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">الملفات المشمولة</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <h6><i class="fas fa-sign-in-alt text-primary"></i> login.php</h6>
                                            <ul class="small">
                                                <li>عنوان الصفحة وحقول الإدخال</li>
                                                <li>أزرار العمل وروابط التنقل</li>
                                            </ul>
                                            
                                            <h6><i class="fas fa-user-plus text-success"></i> register.php</h6>
                                            <ul class="small">
                                                <li>نموذج التسجيل والحقول</li>
                                                <li>رسائل التوجيه والروابط</li>
                                            </ul>
                                            
                                            <h6><i class="fas fa-shield-alt text-warning"></i> includes/auth.php</h6>
                                            <ul class="small">
                                                <li>رسائل المعالجة والأخطاء</li>
                                                <li>تعليقات الكود والوظائف</li>
                                                <li>الإعدادات الافتراضية</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6>أمثلة على الاستبدال المتقدم:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger">قبل التطبيق:</h6>
                                    <pre class="bg-light p-3"><code>&lt;div class="card-header"&gt;تسجيل الدخول&lt;/div&gt;
&lt;label&gt;اسم المستخدم&lt;/label&gt;
$_SESSION['error'] = "اسم المستخدم أو كلمة المرور غير صحيحة";
// معالجة تسجيل الدخول</code></pre>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">بعد التطبيق:</h6>
                                    <pre class="bg-light p-3"><code>&lt;div class="card-header"&gt;&lt;?php echo __('login'); ?&gt;&lt;/div&gt;
&lt;label&gt;&lt;?php echo __('username'); ?&gt;&lt;/label&gt;
$_SESSION['error'] = __('invalid_username_password');
// &lt;?php echo __('process_login'); ?&gt;</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم</h6>
                        <p><strong>هذه الأداة ستقوم بتعديل ملفات نظام المصادقة مباشرة!</strong></p>
                        <ul class="mb-0">
                            <li><strong>عمل نسخة احتياطية إجباري</strong>: انسخ login.php، register.php، includes/auth.php</li>
                            <li><strong>تأكد من وجود الترجمات</strong>: استخدم أداة ترجمة صفحات الدخول والتسجيل أولاً</li>
                            <li><strong>اختبر بعد التطبيق</strong>: تأكد من عمل نظام المصادقة</li>
                            <li><strong>لا يمكن التراجع</strong>: التغييرات دائمة ولا يمكن إلغاؤها</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> ملاحظة خاصة</h6>
                        <p>هذه الأداة تحافظ على القيم الافتراضية في قاعدة البيانات والإعدادات كما هي لضمان عدم كسر النظام.</p>
                        <ul class="mb-0">
                            <li>قيم ENUM في الجداول تبقى كما هي</li>
                            <li>القيم الافتراضية للحقول تبقى كما هي</li>
                            <li>مصفوفات الإعدادات تبقى كما هي</li>
                        </ul>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0"><i class="fas fa-list-ol"></i> خطوات التطبيق الموصى بها</h6>
                                </div>
                                <div class="card-body">
                                    <ol>
                                        <li><strong>عمل نسخة احتياطية</strong>: انسخ login.php، register.php، includes/auth.php</li>
                                        <li><strong>تطبيق ترجمة صفحات الدخول والتسجيل</strong>: استخدم الأداة المخصصة أولاً</li>
                                        <li><strong>فحص الترجمات</strong>: تأكد من وجود جميع الترجمات في ملفات اللغة</li>
                                        <li><strong>تطبيق هذه الأداة</strong>: اضغط الزر أدناه</li>
                                        <li><strong>اختبار نظام المصادقة</strong>: جرب تسجيل الدخول والتسجيل</li>
                                        <li><strong>مراجعة النتائج</strong>: تأكد من عمل الترجمات بشكل صحيح</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" onsubmit="return confirm('⚠️ تحذير شديد: هذه العملية ستعدل ملفات نظام المصادقة مباشرة!\n\nهل أنت متأكد من:\n✅ عمل نسخة احتياطية\n✅ تطبيق ترجمة صفحات الدخول والتسجيل أولاً\n✅ فهم أن التغييرات دائمة\n\nاكتب \"نعم\" للمتابعة') && prompt('للتأكيد، اكتب \"تطبيق\" بالضبط:') === 'تطبيق'">
                        <div class="text-center">
                            <button type="submit" name="apply_login_register" class="btn btn-gradient-dark btn-lg">
                                <i class="fas fa-user-shield"></i> تطبيق ترجمات صفحات الدخول والتسجيل
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4">
                        <h6>أدوات مساعدة:</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <a href="translate_login_register_pages.php" class="btn btn-secondary w-100 mb-2">
                                    <i class="fas fa-sign-in-alt"></i> ترجمة أولاً
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="scan_arabic_terms.php" class="btn btn-info w-100 mb-2">
                                    <i class="fas fa-search-plus"></i> فحص المصطلحات
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="review_translations.php" class="btn btn-success w-100 mb-2">
                                    <i class="fas fa-check"></i> مراجعة الترجمات
                                </a>
                            </div>
                            <div class="col-md-3">
                                <a href="system_tools.php" class="btn btn-dark w-100 mb-2">
                                    <i class="fas fa-tools"></i> أدوات النظام
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
