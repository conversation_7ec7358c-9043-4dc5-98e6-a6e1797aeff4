<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نموذج إدخال البيانات - السيرة الذاتية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ccc;
            border-radius: 3px;
            box-sizing: border-box;
        }
        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        input[type="submit"]:hover {
            background-color: #45a049;
        }
        hr {
            margin-top: 20px;
            margin-bottom: 20px;
            border: 0;
            border-top: 1px solid #ccc;
        }
        .centered {
            text-align: center;
        }
        .right-aligned {
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="centered">السيرة الذاتية</h2>
        <h3 class="centered">المعلومات الشخصية</h3>
        <form action="display.php" method="post">
            <label for="fullname" class="right-aligned">الاسم الكامل:</label>
            <input type="text" id="fullname" name="fullname" required>
            <label for="fullname" class="right-aligned">السجل المدني:</label>
            <input type="text" id="idnum" name="idnum" required>
            <label for="fullname" class="right-aligned">تأريخ الميلاد:</label>
            <input type="text" id="bday" name="bday" required>
            <label for="email" class="right-aligned">البريد الإلكتروني:</label>
            <input type="text" id="email" name="email" required>
            <label for="phone" class="right-aligned">رقم الهاتف:</label>
            <input type="text" id="phone" name="phone" class="right-aligned">
            <label for="address" class="right-aligned">العنوان:</label>
            <input type="text" id="address" name="address" rows="4" class="right-aligned"></input>
            <hr>
            <h3 class="centered">التعليم</h3>
            <input type="text" id="education" name="education[]" rows="4"></input>
            <hr>
            <h3 class="centered">الدورات</h3>
            <div id="courses-container">
                <input type="text" name="courses[]" placeholder="اسم الدورة">
                <label for="start_date_courses">تاريخ البداية:</label>
                <input type="date" id="start_date_courses" name="start_date_courses[]">
                <label for="end_date_courses">تاريخ النهاية:</label>
                <input type="date" id="end_date_courses" name="end_date_courses[]">
            </div>
            <button type="button" onclick="addMore('courses-container')"> + </button>
            <hr>
            <h3 class="centered">الخبرات العملية</h3>
            <div id="experience-container">
                <input type="text" name="experience[]" placeholder="اسم الخبرة">
                <label for="start_date_experience">تاريخ البداية:</label>
                <input type="date" id="start_date_experience" name="start_date_experience[]">
                <label for="end_date_experience">تاريخ النهاية:</label>
                <input type="date" id="end_date_experience" name="end_date_experience[]">
            </div>
            <button type="button" onclick="addMore('experience-container')"> + </button>
            <hr>
            <h3 class="centered">المهارات</h3>
            <div id="skills-container">
                <input type="text" name="skills[]" placeholder="المهارة">
            </div>
            <button type="button" onclick="addSkill()"> + </button>
            <hr>
            <input type="submit" value="إرسال" class="centered">
        </form>
    </div>

    <script>
        function addSkill() {
            var container = document.getElementById('skills-container');
            var newInput = document.createElement('input');
            var deleteskill = document.createElement('button'); // Add delete button
            deleteskill.textContent = ' - ';
            deleteskill.onclick = function() { // Add functionality to delete button
                container.removeChild(newInput);
                container.removeChild(deleteskill);
            };
            newInput.setAttribute('type', 'text');
            newInput.setAttribute('name', 'skills[]');
            newInput.setAttribute('placeholder', 'المهارة');
            container.appendChild(newInput);
            container.appendChild(deleteskill);
        }
        function addMore(containerId) {
            var container = document.getElementById(containerId);
            var newInput = document.createElement('input');
            var startDateInput = document.createElement('input');
            var endDateInput = document.createElement('input');
            var deleteButton = document.createElement('button'); // Add delete button
            deleteButton.textContent = ' - ';
            deleteButton.onclick = function() { // Add functionality to delete button
                container.removeChild(newInput);
                container.removeChild(startDateInput);
                container.removeChild(endDateInput);
                container.removeChild(deleteButton);
            };
            newInput.setAttribute('type', 'text');
            newInput.setAttribute('name', containerId.replace("-container", "") + "[]");
            newInput.setAttribute('placeholder', 'العنصر الجديد');
            startDateInput.setAttribute('type', 'date');
            startDateInput.setAttribute('name', 'start_date_' + containerId.replace("-container", "") + '[]');
            endDateInput.setAttribute('type', 'date');
            endDateInput.setAttribute('name', 'end_date_' + containerId.replace("-container", "") + '[]');
            container.appendChild(newInput);
            container.appendChild(startDateInput);
            container.appendChild(endDateInput);
            container.appendChild(deleteButton); // Append delete button
        }
    </script>
</body>
</html>
