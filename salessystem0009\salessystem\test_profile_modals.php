<?php
/**
 * ملف لاختبار النوافذ المنبثقة في صفحة الملف الشخصي
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$test_results = [];

// اختبار 1: فحص وجود ملف الملف الشخصي
$profile_file = __DIR__ . '/profile.php';
$test_results['profile_file_exists'] = file_exists($profile_file);

if ($test_results['profile_file_exists']) {
    $profile_content = file_get_contents($profile_file);
    
    // اختبار 2: فحص وجود النوافذ المنبثقة
    $test_results['edit_modal_exists'] = strpos($profile_content, 'editProfileModal') !== false;
    $test_results['password_modal_exists'] = strpos($profile_content, 'changePasswordModal') !== false;
    
    // اختبار 3: فحص وجود أزرار فتح النوافذ
    $test_results['edit_button_exists'] = strpos($profile_content, 'data-bs-target="#editProfileModal"') !== false;
    $test_results['password_button_exists'] = strpos($profile_content, 'data-bs-target="#changePasswordModal"') !== false;
    
    // اختبار 4: فحص وجود JavaScript للنوافذ المنبثقة
    $test_results['modal_js_exists'] = strpos($profile_content, 'resetEditForm') !== false;
    $test_results['password_strength_js'] = strpos($profile_content, 'password-strength') !== false;
    
    // اختبار 5: فحص وجود نماذج منفصلة
    $test_results['edit_form_exists'] = strpos($profile_content, 'editProfileForm') !== false;
    $test_results['password_form_exists'] = strpos($profile_content, 'changePasswordForm') !== false;
} else {
    $test_results['edit_modal_exists'] = false;
    $test_results['password_modal_exists'] = false;
    $test_results['edit_button_exists'] = false;
    $test_results['password_button_exists'] = false;
    $test_results['modal_js_exists'] = false;
    $test_results['password_strength_js'] = false;
    $test_results['edit_form_exists'] = false;
    $test_results['password_form_exists'] = false;
}

// اختبار 6: فحص ملف CSS للنوافذ المنبثقة
$css_file = __DIR__ . '/assets/css/profile.css';
$test_results['css_file_exists'] = file_exists($css_file);

if ($test_results['css_file_exists']) {
    $css_content = file_get_contents($css_file);
    $test_results['modal_css_exists'] = strpos($css_content, '.modal-content') !== false;
    $test_results['password_strength_css'] = strpos($css_content, '.password-strength') !== false;
} else {
    $test_results['modal_css_exists'] = false;
    $test_results['password_strength_css'] = false;
}

// اختبار 7: فحص بيانات المستخدم
try {
    $stmt = $main_db->prepare("SELECT id, username, full_name, email FROM users WHERE id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();
    $user_data = $result->fetch_assoc();
    $test_results['user_data_available'] = ($user_data !== null);
} catch (Exception $e) {
    $test_results['user_data_available'] = false;
}

// حساب النتيجة الإجمالية
$total_tests = 11;
$passed_tests = 0;

foreach ($test_results as $result) {
    if ($result === true) {
        $passed_tests++;
    }
}

$success_percentage = ($passed_tests / $total_tests) * 100;

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار النوافذ المنبثقة للملف الشخصي</h2>
    
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> معلومات الاختبار</h5>
        <p>هذا الاختبار يتحقق من جاهزية النوافذ المنبثقة لتعديل الملف الشخصي:</p>
        <ul>
            <li>نافذة تعديل البيانات الأساسية</li>
            <li>نافذة تغيير كلمة المرور</li>
            <li>JavaScript والتفاعلات</li>
            <li>التنسيقات والمظهر</li>
        </ul>
    </div>
    
    <!-- النتيجة الإجمالية -->
    <div class="card mb-4">
        <div class="card-header <?php echo $success_percentage >= 90 ? 'bg-success' : ($success_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?> text-white">
            <h4 class="mb-0">
                <i class="fas fa-chart-pie"></i>
                النتيجة الإجمالية: <?php echo number_format($success_percentage, 1); ?>%
            </h4>
        </div>
        <div class="card-body">
            <div class="progress mb-3" style="height: 30px;">
                <div class="progress-bar <?php echo $success_percentage >= 90 ? 'bg-success' : ($success_percentage >= 70 ? 'bg-warning' : 'bg-danger'); ?>" 
                     style="width: <?php echo $success_percentage; ?>%">
                    <?php echo $passed_tests; ?>/<?php echo $total_tests; ?> (<?php echo number_format($success_percentage, 1); ?>%)
                </div>
            </div>
            
            <?php if ($success_percentage >= 90): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ممتاز! النوافذ المنبثقة جاهزة للاستخدام
                </div>
            <?php elseif ($success_percentage >= 70): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> جيد! النوافذ تعمل مع بعض المشاكل البسيطة
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i> يحتاج إلى إصلاح! هناك مشاكل تحتاج إلى حل
                </div>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="row">
        <!-- اختبار الملفات الأساسية -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-code"></i>
                        الملفات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>ملف الملف الشخصي:</span>
                        <span class="badge <?php echo $test_results['profile_file_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['profile_file_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>ملف CSS:</span>
                        <span class="badge <?php echo $test_results['css_file_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['css_file_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>بيانات المستخدم:</span>
                        <span class="badge <?php echo $test_results['user_data_available'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['user_data_available'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار النوافذ المنبثقة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-window-restore"></i>
                        النوافذ المنبثقة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>نافذة تعديل البيانات:</span>
                        <span class="badge <?php echo $test_results['edit_modal_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['edit_modal_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>نافذة تغيير كلمة المرور:</span>
                        <span class="badge <?php echo $test_results['password_modal_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['password_modal_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>تنسيقات النوافذ:</span>
                        <span class="badge <?php echo $test_results['modal_css_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['modal_css_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار الأزرار والنماذج -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-mouse-pointer"></i>
                        الأزرار والنماذج
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>زر تعديل البيانات:</span>
                        <span class="badge <?php echo $test_results['edit_button_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['edit_button_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>زر تغيير كلمة المرور:</span>
                        <span class="badge <?php echo $test_results['password_button_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['password_button_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>نموذج تعديل البيانات:</span>
                        <span class="badge <?php echo $test_results['edit_form_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['edit_form_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>نموذج تغيير كلمة المرور:</span>
                        <span class="badge <?php echo $test_results['password_form_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['password_form_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- اختبار JavaScript -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-code"></i>
                        JavaScript والتفاعلات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>دوال النوافذ المنبثقة:</span>
                        <span class="badge <?php echo $test_results['modal_js_exists'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['modal_js_exists'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>مؤشر قوة كلمة المرور:</span>
                        <span class="badge <?php echo $test_results['password_strength_js'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['password_strength_js'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>تنسيقات مؤشر القوة:</span>
                        <span class="badge <?php echo $test_results['password_strength_css'] ? 'bg-success' : 'bg-danger'; ?>">
                            <?php echo $test_results['password_strength_css'] ? '✓' : '✗'; ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- معاينة مباشرة -->
    <?php if ($test_results['profile_file_exists']): ?>
    <div class="card mb-4">
        <div class="card-header bg-secondary text-white">
            <h5 class="mb-0">
                <i class="fas fa-eye"></i>
                معاينة مباشرة
            </h5>
        </div>
        <div class="card-body">
            <p>يمكنك اختبار النوافذ المنبثقة مباشرة:</p>
            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <a href="profile.php" class="btn btn-primary" target="_blank">
                    <i class="fas fa-user-circle"></i> فتح الملف الشخصي
                </a>
                <button type="button" class="btn btn-info" onclick="openTestModal()">
                    <i class="fas fa-vial"></i> اختبار النوافذ هنا
                </button>
            </div>
        </div>
    </div>
    <?php endif; ?>
    
    <!-- الإجراءات -->
    <div class="card">
        <div class="card-header bg-dark text-white">
            <h5 class="mb-0">الإجراءات والأدوات</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <h6>اختبار الصفحة:</h6>
                    <div class="d-grid gap-2">
                        <a href="profile.php" class="btn btn-outline-primary btn-sm" target="_blank">
                            <i class="fas fa-user-circle"></i> الملف الشخصي
                        </a>
                        <a href="test_profile_modals.php" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-redo"></i> إعادة الاختبار
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>اختبارات أخرى:</h6>
                    <div class="d-grid gap-2">
                        <a href="test_profile_page.php" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-user-check"></i> اختبار الملف الشخصي
                        </a>
                        <a href="test_all_fixes.php" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-clipboard-check"></i> الاختبار الشامل
                        </a>
                    </div>
                </div>
                <div class="col-md-4">
                    <h6>العودة:</h6>
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function openTestModal() {
    alert('لاختبار النوافذ المنبثقة، يرجى فتح صفحة الملف الشخصي من الرابط أعلاه.');
}
</script>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
