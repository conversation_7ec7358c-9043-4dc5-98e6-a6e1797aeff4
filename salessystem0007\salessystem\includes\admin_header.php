<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المدير - نظام إدارة المبيعات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom Admin Styles -->
    <style>
        :root {
            /* نظام ألوان مرموق وأنيق - الوضع الفاتح */
            --admin-navy: #1e293b;
            --admin-slate: #334155;
            --admin-gray: #64748b;
            --admin-silver: #94a3b8;
            --admin-light: #f1f5f9;
            --admin-white: #ffffff;

            /* ألوان التفاعل الراقية */
            --admin-royal-blue: #2563eb;
            --admin-emerald: #059669;
            --admin-amber: #d97706;
            --admin-rose: #e11d48;
            --admin-cyan: #0891b2;
            --admin-violet: #7c3aed;

            /* تدرجات أنيقة */
            --admin-gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            --admin-gradient-success: linear-gradient(135deg, #059669 0%, #047857 100%);
            --admin-gradient-warning: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            --admin-gradient-danger: linear-gradient(135deg, #e11d48 0%, #be123c 100%);

            /* الخلفيات الراقية - الوضع الفاتح */
            --admin-bg-main: #f8fafc;
            --admin-bg-card: #ffffff;
            --admin-bg-sidebar: #1e293b;
            --admin-bg-navbar: rgba(255, 255, 255, 0.95);
            --admin-bg-header: #f8fafc;

            /* ألوان الشريط الجانبي - الوضع الفاتح */
            --sidebar-bg: #0f172a;
            --sidebar-text-normal: #94a3b8;
            --sidebar-text-hover: #cbd5e1;
            --sidebar-text-active: #ffffff;
            --sidebar-bg-hover: rgba(71, 85, 105, 0.3);
            --sidebar-bg-active: rgba(37, 99, 235, 0.3);
            --sidebar-border-hover: rgba(148, 163, 184, 0.5);
            --sidebar-border-active: #2563eb;
            --sidebar-icon-normal: #64748b;
            --sidebar-icon-hover: #94a3b8;
            --sidebar-icon-active: #60a5fa;

            /* الحدود الأنيقة */
            --admin-border-color: #e2e8f0;
            --admin-border-light: #f1f5f9;

            /* ظلال راقية */
            --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

            /* الحدود الأنيقة */
            --admin-border-radius-sm: 6px;
            --admin-border-radius: 8px;
            --admin-border-radius-lg: 12px;
            --admin-border-radius-xl: 16px;

            /* المسافات المتوازنة */
            --admin-spacing-xs: 0.25rem;
            --admin-spacing-sm: 0.5rem;
            --admin-spacing-md: 0.75rem;
            --admin-spacing-lg: 1rem;
            --admin-spacing-xl: 1.5rem;
            --admin-spacing-2xl: 2rem;

            /* الانتقالات السلسة */
            --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --admin-transition-fast: all 0.15s ease-out;
        }

        /* الوضع الداكن */
        [data-theme="dark"] {
            --admin-bg-main: #0f172a;
            --admin-bg-card: #1e293b;
            --admin-bg-sidebar: #020617;
            --admin-bg-navbar: rgba(30, 41, 59, 0.95);
            --admin-bg-header: #1e293b;

            --admin-navy: #f1f5f9;
            --admin-slate: #e2e8f0;
            --admin-gray: #cbd5e1;
            --admin-silver: #94a3b8;

            --admin-border-color: #334155;
            --admin-border-light: #1e293b;

            --sidebar-bg: #020617;
            --sidebar-text-normal: #64748b;
            --sidebar-text-hover: #94a3b8;
            --sidebar-text-active: #ffffff;
            --sidebar-bg-hover: rgba(71, 85, 105, 0.2);
            --sidebar-bg-active: rgba(37, 99, 235, 0.25);
            --sidebar-border-hover: rgba(148, 163, 184, 0.3);
            --sidebar-border-active: #3b82f6;
            --sidebar-icon-normal: #475569;
            --sidebar-icon-hover: #64748b;
            --sidebar-icon-active: #60a5fa;

            --admin-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
            --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
            --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--admin-bg-main);
            min-height: 100vh;
            font-size: 14px;
            line-height: 1.6;
            color: var(--admin-slate);
            overflow-x: hidden;
            margin: 0;
            padding: 0;
            font-weight: 400;
        }

        /* الشريط العلوي الأنيق */
        .admin-navbar {
            background: var(--admin-bg-navbar) !important;
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid var(--admin-border-color);
            box-shadow: var(--admin-shadow-md);
            position: sticky;
            top: 0;
            z-index: 1000;
            transition: var(--admin-transition);
            padding: 0.75rem 0;
        }

        .admin-navbar .navbar-brand {
            font-weight: 700;
            color: var(--admin-navy) !important;
            font-size: 1.375rem;
            display: flex;
            align-items: center;
            gap: var(--admin-spacing-md);
            letter-spacing: -0.025em;
        }

        .admin-navbar .navbar-brand i {
            background: var(--admin-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 1.5rem;
        }

        /* تحسينات الشريط العلوي للوضع الداكن */
        [data-theme="dark"] .admin-navbar {
            background: var(--admin-bg-navbar) !important;
            border-bottom-color: var(--admin-border-color);
        }

        [data-theme="dark"] .admin-navbar .navbar-brand {
            color: var(--admin-navy) !important;
        }

        .admin-navbar .nav-link {
            color: #6b7280 !important;
            transition: var(--admin-transition-base);
            font-weight: 500;
            border-radius: var(--admin-border-radius-sm);
            margin: 0 var(--admin-spacing-xs);
            padding: var(--admin-spacing-sm) var(--admin-spacing-md) !important;
            position: relative;
            overflow: hidden;
        }

        .admin-navbar .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--admin-gradient-primary);
            opacity: 0;
            transition: var(--admin-transition-base);
            border-radius: var(--admin-border-radius-sm);
        }

        .admin-navbar .nav-link:hover {
            color: white !important;
            transform: translateY(-1px);
        }

        .admin-navbar .nav-link:hover::before {
            opacity: 1;
        }

        .admin-navbar .nav-link span {
            position: relative;
            z-index: 1;
        }

        /* القوائم المنسدلة المحسنة */
        .dropdown-menu {
            border: none;
            box-shadow: var(--admin-shadow-xl);
            border-radius: var(--admin-border-radius);
            backdrop-filter: blur(20px) saturate(180%);
            background: rgba(255, 255, 255, 0.98);
            padding: var(--admin-spacing-sm);
            margin-top: var(--admin-spacing-sm);
            min-width: 220px;
        }

        .dropdown-header {
            font-weight: 600;
            color: var(--admin-primary);
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            padding: var(--admin-spacing-sm) var(--admin-spacing-md);
        }

        .dropdown-item {
            transition: var(--admin-transition-base);
            border-radius: var(--admin-border-radius-sm);
            margin: var(--admin-spacing-xs) 0;
            padding: var(--admin-spacing-sm) var(--admin-spacing-md);
            font-weight: 500;
            color: #4b5563;
            position: relative;
            overflow: hidden;
        }

        .dropdown-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--admin-gradient-primary);
            transition: var(--admin-transition-base);
            z-index: -1;
        }

        .dropdown-item:hover {
            color: white;
            transform: translateX(4px);
            box-shadow: var(--admin-shadow-md);
        }

        .dropdown-item:hover::before {
            left: 0;
        }

        .dropdown-divider {
            margin: var(--admin-spacing-sm) 0;
            border-color: rgba(229, 231, 235, 0.5);
        }

        /* الشريط الجانبي المحسن */
        .sidebar {
            background: var(--sidebar-bg);
            border-right: 1px solid rgba(71, 85, 105, 0.3);
            min-height: calc(100vh - 76px);
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 76px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: rgba(71, 85, 105, 0.5) transparent;
            width: 240px;
            flex-shrink: 0;
            padding: 16px 0;
        }

        .sidebar::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(148, 163, 184, 0.5);
        }

        /* تحسينات شاملة للقائمة الجانبية */
        .sidebar .nav-item {
            margin-bottom: 4px;
        }

        .sidebar .nav-link span {
            font-weight: inherit;
            letter-spacing: 0.025em;
            line-height: 1.4;
        }

        /* عنوان القائمة الجانبية */
        .sidebar .sidebar-brand {
            padding: 20px 20px 30px;
            border-bottom: 1px solid rgba(71, 85, 105, 0.3);
            margin-bottom: 16px;
        }

        .sidebar .sidebar-brand h5 {
            color: var(--sidebar-text-active);
            font-weight: 600;
            font-size: 16px;
            margin: 0;
            text-align: center;
        }

        /* تأثيرات خاصة للقائمة الجانبية */
        .sidebar .nav-link:focus {
            outline: none;
            box-shadow: 0 0 0 2px var(--sidebar-border-active);
        }

        /* تحسين الوضع الداكن للقائمة الجانبية */
        [data-theme="dark"] .sidebar {
            border-right-color: rgba(71, 85, 105, 0.4);
        }

        [data-theme="dark"] .sidebar .sidebar-brand {
            border-bottom-color: rgba(71, 85, 105, 0.4);
        }

        [data-theme="dark"] .sidebar::-webkit-scrollbar-thumb {
            background: rgba(71, 85, 105, 0.5);
        }

        [data-theme="dark"] .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(71, 85, 105, 0.7);
        }

        /* روابط الشريط الجانبي المحسنة */
        .sidebar .nav-link {
            color: var(--sidebar-text-normal) !important;
            padding: 14px 20px;
            border-radius: 8px;
            margin: 4px 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 14px;
            position: relative;
            text-decoration: none;
            border: 1px solid transparent;
            background: transparent;
        }

        .sidebar .nav-link:hover {
            color: var(--sidebar-text-hover) !important;
            background: var(--sidebar-bg-hover);
            text-decoration: none;
            border-color: var(--sidebar-border-hover);
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .sidebar .nav-link.active {
            color: var(--sidebar-text-active) !important;
            background: var(--sidebar-bg-active);
            border-color: var(--sidebar-border-active);
            font-weight: 600;
            transform: translateX(4px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
        }

        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 16px;
            transition: all 0.3s ease;
            color: var(--sidebar-icon-normal) !important;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar .nav-link:hover i {
            color: var(--sidebar-icon-hover) !important;
            transform: scale(1.1);
        }

        .sidebar .nav-link.active i {
            color: var(--sidebar-icon-active) !important;
            transform: scale(1.1);
        }

        /* عنوان القسم في الشريط الجانبي */
        .sidebar .nav-section {
            padding: var(--admin-spacing-lg) var(--admin-spacing-lg) var(--admin-spacing-sm);
            margin-top: var(--admin-spacing-lg);
            border-top: 1px solid rgba(229, 231, 235, 0.5);
        }

        .sidebar .nav-section:first-child {
            border-top: none;
            margin-top: 0;
        }

        .sidebar .nav-section-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: #9ca3af;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: var(--admin-spacing-sm);
        }

        /* البطاقات الأنيقة */
        .card {
            border: 1px solid rgba(148, 163, 184, 0.2);
            box-shadow: var(--admin-shadow-sm);
            border-radius: var(--admin-border-radius-lg);
            background: var(--admin-bg-card);
            transition: var(--admin-transition);
            overflow: hidden;
            margin-bottom: var(--admin-spacing-xl);
            position: relative;
        }

        /* تحسين حدود البطاقات للوضع الداكن */
        [data-theme="dark"] .card {
            border-color: rgba(148, 163, 184, 0.3);
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--admin-gradient-primary);
            opacity: 0;
            transition: var(--admin-transition);
        }

        .card:hover {
            box-shadow: var(--admin-shadow-lg);
            transform: translateY(-2px);
        }

        .card:hover::before {
            opacity: 1;
        }

        .card-header {
            background: var(--admin-bg-header);
            color: var(--admin-navy);
            border-bottom: 1px solid var(--admin-border-color);
            border-radius: var(--admin-border-radius-lg) var(--admin-border-radius-lg) 0 0 !important;
            font-weight: 600;
            font-size: 15px;
            padding: var(--admin-spacing-lg) var(--admin-spacing-xl);
        }

        .card-header.bg-primary {
            background: var(--admin-gradient-primary) !important;
            color: white;
        }

        .card-header.bg-success {
            background: var(--admin-gradient-success) !important;
            color: white;
        }

        .card-header.bg-warning {
            background: var(--admin-gradient-warning) !important;
            color: white;
        }

        .card-header.bg-danger {
            background: var(--admin-gradient-danger) !important;
            color: white;
        }

        .card-header.bg-info {
            background: linear-gradient(135deg, var(--admin-cyan) 0%, #0e7490 100%) !important;
            color: white;
        }

        .card-header.bg-secondary {
            background: linear-gradient(135deg, var(--admin-gray) 0%, var(--admin-slate) 100%) !important;
            color: white;
        }

        .card-header.bg-light {
            background: var(--admin-bg-header) !important;
            color: var(--admin-navy);
        }

        .card-header.bg-dark {
            background: linear-gradient(135deg, var(--admin-navy) 0%, var(--admin-slate) 100%) !important;
            color: white;
        }

        .card-body {
            padding: var(--admin-spacing-xl);
        }

        .card-footer {
            background: var(--admin-bg-header);
            border-top: 1px solid var(--admin-border-color);
            padding: var(--admin-spacing-lg) var(--admin-spacing-xl);
        }

        .text-primary {
            color: var(--admin-royal-blue) !important;
        }

        /* الأزرار الأنيقة */
        .btn {
            border-radius: var(--admin-border-radius);
            font-weight: 600;
            transition: var(--admin-transition);
            border: 1px solid transparent;
            padding: var(--admin-spacing-sm) var(--admin-spacing-lg);
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: var(--admin-spacing-sm);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: var(--admin-transition-fast);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-sm {
            padding: var(--admin-spacing-xs) var(--admin-spacing-md);
            font-size: 13px;
            border-radius: var(--admin-border-radius-sm);
        }

        .btn-lg {
            padding: var(--admin-spacing-md) var(--admin-spacing-xl);
            font-size: 16px;
            border-radius: var(--admin-border-radius-lg);
        }

        .btn-primary {
            background: var(--admin-gradient-primary);
            color: white;
            border: none;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-success {
            background: var(--admin-gradient-success);
            color: white;
            border: none;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #047857 0%, #065f46 100%);
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-warning {
            background: var(--admin-gradient-warning);
            color: white;
            border: none;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #b45309 0%, #92400e 100%);
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-danger {
            background: var(--admin-gradient-danger);
            color: white;
            border: none;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #be123c 0%, #9f1239 100%);
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, var(--admin-cyan) 0%, #0e7490 100%);
            color: white;
            border: none;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #0e7490 0%, #155e75 100%);
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--admin-gray) 0%, var(--admin-slate) 100%);
            color: white;
            border: none;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, var(--admin-slate) 0%, var(--admin-navy) 100%);
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-outline-primary {
            background: transparent;
            color: var(--admin-royal-blue);
            border: 2px solid var(--admin-royal-blue);
        }

        .btn-outline-primary:hover {
            background: var(--admin-gradient-primary);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
        }

        .btn-outline-secondary {
            background: transparent;
            color: var(--admin-gray);
            border: 2px solid var(--admin-gray);
        }

        .btn-outline-secondary:hover {
            background: linear-gradient(135deg, var(--admin-gray) 0%, var(--admin-slate) 100%);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
        }

        .btn-outline-danger {
            background: transparent;
            color: var(--admin-rose);
            border: 2px solid var(--admin-rose);
        }

        .btn-outline-danger:hover {
            background: var(--admin-gradient-danger);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--admin-shadow-lg);
        }

        /* الجداول البسيطة مع إطارات رمادية */
        .table {
            font-size: 14px;
            margin-bottom: 0;
            background: var(--admin-bg-card);
            color: var(--admin-slate);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: var(--admin-border-radius);
            overflow: hidden;
        }

        .table-bordered {
            border: 1px solid rgba(148, 163, 184, 0.2);
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid rgba(148, 163, 184, 0.15);
        }

        .table th {
            background: var(--admin-bg-header);
            color: var(--admin-navy);
            font-weight: 600;
            font-size: 14px;
            padding: 12px 16px;
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
            border-right: 1px solid rgba(148, 163, 184, 0.15);
        }

        .table th:last-child {
            border-right: none;
        }

        .table td {
            padding: 12px 16px;
            border-bottom: 1px solid rgba(148, 163, 184, 0.15);
            border-right: 1px solid rgba(148, 163, 184, 0.1);
            vertical-align: middle;
            color: var(--admin-slate);
            background: var(--admin-bg-card);
        }

        .table td:last-child {
            border-right: none;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        .table-hover tbody tr:hover {
            background: rgba(148, 163, 184, 0.1) !important;
        }

        [data-theme="dark"] .table-hover tbody tr:hover {
            background: rgba(148, 163, 184, 0.15) !important;
        }

        /* تحسين الإطارات للوضع الداكن */
        [data-theme="dark"] .table {
            border-color: rgba(148, 163, 184, 0.3);
        }

        [data-theme="dark"] .table th {
            border-bottom-color: rgba(148, 163, 184, 0.3);
            border-right-color: rgba(148, 163, 184, 0.2);
        }

        [data-theme="dark"] .table td {
            border-bottom-color: rgba(148, 163, 184, 0.2);
            border-right-color: rgba(148, 163, 184, 0.15);
        }

        [data-theme="dark"] .table-bordered {
            border-color: rgba(148, 163, 184, 0.3);
        }

        [data-theme="dark"] .table-bordered th,
        [data-theme="dark"] .table-bordered td {
            border-color: rgba(148, 163, 184, 0.2);
        }

        /* النماذج الأنيقة */
        .form-control {
            border: 2px solid var(--admin-border-color);
            border-radius: var(--admin-border-radius);
            padding: var(--admin-spacing-md) var(--admin-spacing-lg);
            font-size: 14px;
            transition: var(--admin-transition);
            background: var(--admin-bg-card);
            color: var(--admin-slate);
        }

        .form-control:focus {
            border-color: var(--admin-royal-blue);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
            outline: none;
            background: var(--admin-bg-card);
        }

        .form-select {
            border: 2px solid var(--admin-border-color);
            border-radius: var(--admin-border-radius);
            padding: var(--admin-spacing-md) var(--admin-spacing-lg);
            font-size: 14px;
            background: var(--admin-bg-card);
            color: var(--admin-slate);
        }

        .form-label {
            font-weight: 600;
            font-size: 14px;
            color: var(--admin-navy);
            margin-bottom: var(--admin-spacing-sm);
        }

        /* تحسينات الوضع الداكن للنماذج */
        [data-theme="dark"] .form-control,
        [data-theme="dark"] .form-select {
            background: var(--admin-bg-card);
            color: var(--admin-slate);
            border-color: var(--admin-border-color);
        }

        [data-theme="dark"] .form-control:focus {
            background: var(--admin-bg-card);
            color: var(--admin-slate);
        }

        /* الشارات الأنيقة */
        .badge {
            font-size: 12px;
            font-weight: 600;
            padding: 6px 12px;
            border-radius: var(--admin-border-radius);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .bg-success {
            background: var(--admin-gradient-success) !important;
        }

        .bg-danger {
            background: var(--admin-gradient-danger) !important;
        }

        .bg-warning {
            background: var(--admin-gradient-warning) !important;
        }

        .bg-info {
            background: linear-gradient(135deg, var(--admin-cyan) 0%, #0e7490 100%) !important;
        }

        .bg-secondary {
            background: linear-gradient(135deg, var(--admin-gray) 0%, var(--admin-slate) 100%) !important;
        }

        .bg-primary {
            background: var(--admin-gradient-primary) !important;
        }

        /* التنبيهات الأنيقة */
        .alert {
            border: none;
            border-radius: var(--admin-border-radius-lg);
            padding: var(--admin-spacing-lg) var(--admin-spacing-xl);
            font-size: 14px;
            margin-bottom: var(--admin-spacing-xl);
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            width: 4px;
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(4, 120, 87, 0.05) 100%);
            color: var(--admin-emerald);
            border: 1px solid rgba(5, 150, 105, 0.2);
        }

        .alert-success::before {
            background: var(--admin-gradient-success);
        }

        .alert-danger {
            background: linear-gradient(135deg, rgba(225, 29, 72, 0.1) 0%, rgba(190, 18, 60, 0.05) 100%);
            color: var(--admin-rose);
            border: 1px solid rgba(225, 29, 72, 0.2);
        }

        .alert-danger::before {
            background: var(--admin-gradient-danger);
        }

        .alert-warning {
            background: linear-gradient(135deg, rgba(217, 119, 6, 0.1) 0%, rgba(180, 83, 9, 0.05) 100%);
            color: var(--admin-amber);
            border: 1px solid rgba(217, 119, 6, 0.2);
        }

        .alert-warning::before {
            background: var(--admin-gradient-warning);
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(8, 145, 178, 0.1) 0%, rgba(14, 116, 144, 0.05) 100%);
            color: var(--admin-cyan);
            border: 1px solid rgba(8, 145, 178, 0.2);
        }

        .alert-info::before {
            background: linear-gradient(135deg, var(--admin-cyan) 0%, #0e7490 100%);
        }

        /* تحسينات إضافية أنيقة */
        .border-left-primary {
            border-left: 4px solid var(--admin-royal-blue) !important;
        }

        .border-left-success {
            border-left: 4px solid var(--admin-emerald) !important;
        }

        .border-left-warning {
            border-left: 4px solid var(--admin-amber) !important;
        }

        .border-left-danger {
            border-left: 4px solid var(--admin-rose) !important;
        }

        .border-left-info {
            border-left: 4px solid var(--admin-cyan) !important;
        }

        .text-muted {
            color: var(--admin-silver) !important;
        }

        .text-success {
            color: var(--admin-emerald) !important;
        }

        .text-danger {
            color: var(--admin-rose) !important;
        }

        .text-warning {
            color: var(--admin-amber) !important;
        }

        .text-info {
            color: var(--admin-cyan) !important;
        }

        .text-primary {
            color: var(--admin-royal-blue) !important;
        }

        .text-secondary {
            color: var(--admin-gray) !important;
        }

        /* تأثيرات خاصة */
        .hover-lift {
            transition: var(--admin-transition);
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-lg);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .gradient-text {
            background: var(--admin-gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 60px;
                left: -100%;
                width: 250px;
                height: calc(100vh - 60px);
                z-index: 999;
                transition: var(--admin-transition);
            }

            .sidebar.show {
                left: 0;
            }

            .card-body {
                padding: var(--admin-spacing-md);
            }

            .btn {
                font-size: 12px;
                padding: 6px var(--admin-spacing-sm);
            }
        }

        .btn-success {
            background: var(--admin-gradient-success);
            color: white;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-warning {
            background: var(--admin-gradient-warning);
            color: var(--admin-primary);
            box-shadow: var(--admin-shadow-md);
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-lg);
            color: var(--admin-primary);
        }

        .btn-info {
            background: var(--admin-gradient-success);
            color: white;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-danger {
            background: var(--admin-gradient-danger);
            color: white;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
            box-shadow: var(--admin-shadow-md);
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-lg);
            color: white;
        }

        .btn-outline-primary {
            background: transparent;
            color: var(--admin-accent);
            border: 2px solid var(--admin-accent);
            box-shadow: none;
        }

        .btn-outline-primary:hover {
            background: var(--admin-gradient-primary);
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-lg);
        }

        .btn-outline-secondary {
            background: transparent;
            color: #6b7280;
            border: 2px solid #6b7280;
            box-shadow: none;
        }

        .btn-outline-secondary:hover {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-lg);
        }

        /* الإشعارات المحسنة */
        .alert {
            border: none;
            border-radius: var(--admin-border-radius-lg);
            box-shadow: var(--admin-shadow-lg);
            backdrop-filter: blur(20px) saturate(180%);
            padding: var(--admin-spacing-lg);
            margin: var(--admin-spacing-md);
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: currentColor;
        }

        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            color: #166534;
            border: 1px solid rgba(34, 197, 94, 0.2);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: #991b1b;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            color: #92400e;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .alert-info {
            background: rgba(59, 130, 246, 0.1);
            color: #1e40af;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .alert-dismissible .btn-close {
            background: none;
            border: none;
            opacity: 0.7;
            transition: var(--admin-transition-base);
        }

        .alert-dismissible .btn-close:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        /* الشارات المحسنة */
        .badge {
            border-radius: var(--admin-border-radius-sm);
            font-weight: 600;
            padding: var(--admin-spacing-xs) var(--admin-spacing-sm);
            font-size: 0.75rem;
            letter-spacing: 0.025em;
        }

        .badge.bg-primary {
            background: var(--admin-gradient-primary) !important;
        }

        .badge.bg-success {
            background: var(--admin-gradient-success) !important;
        }

        .badge.bg-warning {
            background: var(--admin-gradient-warning) !important;
            color: var(--admin-primary) !important;
        }

        .badge.bg-danger {
            background: var(--admin-gradient-danger) !important;
        }

        .badge.bg-info {
            background: var(--admin-gradient-success) !important;
        }

        .badge.bg-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
        }

        .badge-counter {
            position: absolute;
            top: -6px;
            right: -6px;
            font-size: 0.65rem;
            min-width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
            box-shadow: var(--admin-shadow-md);
        }

        @keyframes pulse {
            0% { transform: scale(1); box-shadow: var(--admin-shadow-md); }
            50% { transform: scale(1.15); box-shadow: var(--admin-shadow-lg); }
            100% { transform: scale(1); box-shadow: var(--admin-shadow-md); }
        }

        .table {
            border-radius: var(--admin-border-radius);
            overflow: hidden;
        }

        .table thead th {
            background: var(--admin-gradient);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table-hover tbody tr:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: scale(1.01);
            transition: all 0.3s ease;
        }

        .border-left-primary {
            border-left: 4px solid var(--admin-primary) !important;
        }

        .border-left-success {
            border-left: 4px solid var(--admin-success) !important;
        }

        .border-left-info {
            border-left: 4px solid var(--admin-info) !important;
        }

        .border-left-warning {
            border-left: 4px solid var(--admin-warning) !important;
        }

        /* المودالات المحسنة */
        .modal-backdrop {
            backdrop-filter: blur(8px);
            background: rgba(0, 0, 0, 0.4);
        }

        .modal-content {
            border: none;
            border-radius: var(--admin-border-radius-xl);
            box-shadow: var(--admin-shadow-2xl);
            backdrop-filter: blur(20px) saturate(180%);
            background: rgba(255, 255, 255, 0.98);
            overflow: hidden;
        }

        .modal-header {
            background: var(--admin-gradient-primary);
            color: white;
            border-bottom: none;
            border-radius: var(--admin-border-radius-xl) var(--admin-border-radius-xl) 0 0;
            padding: var(--admin-spacing-xl);
        }

        .modal-title {
            font-weight: 700;
            font-size: 1.25rem;
        }

        .modal-body {
            padding: var(--admin-spacing-xl);
        }

        .modal-footer {
            background: rgba(248, 250, 252, 0.8);
            border-top: 1px solid rgba(229, 231, 235, 0.5);
            padding: var(--admin-spacing-xl);
        }

        .btn-close {
            background: none;
            border: none;
            color: white;
            opacity: 0.8;
            transition: var(--admin-transition-base);
        }

        .btn-close:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        /* تحسينات إضافية */
        .border-left-primary {
            border-left: 4px solid var(--admin-accent) !important;
        }

        .border-left-success {
            border-left: 4px solid var(--admin-success) !important;
        }

        .border-left-info {
            border-left: 4px solid var(--admin-info) !important;
        }

        .border-left-warning {
            border-left: 4px solid var(--admin-warning) !important;
        }

        .border-left-danger {
            border-left: 4px solid var(--admin-danger) !important;
        }

        /* تحسينات النصوص */
        .text-primary {
            color: var(--admin-primary) !important;
        }

        .text-muted {
            color: #6b7280 !important;
        }

        .fw-bold {
            font-weight: 700 !important;
        }

        .fw-semibold {
            font-weight: 600 !important;
        }

        /* تحسينات الخلفيات */
        .bg-light {
            background: var(--admin-bg-secondary) !important;
        }

        .bg-dark {
            background: var(--admin-bg-dark) !important;
        }

        /* شريط التقدم */
        .progress {
            border-radius: var(--admin-border-radius);
            background: rgba(229, 231, 235, 0.5);
            overflow: hidden;
        }

        .progress-bar {
            background: var(--admin-gradient-primary);
            transition: var(--admin-transition-base);
        }

        /* التحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 76px;
                left: -100%;
                width: 280px;
                height: calc(100vh - 76px);
                transition: var(--admin-transition-base);
                z-index: 1000;
                box-shadow: var(--admin-shadow-2xl);
            }

            .sidebar.show {
                left: 0;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: var(--admin-transition-base);
            }

            .sidebar-overlay.show {
                opacity: 1;
                visibility: visible;
            }

            .card {
                margin-bottom: var(--admin-spacing-lg);
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .table-responsive {
                font-size: 0.875rem;
            }
        }

        /* تأثيرات التحميل المحسنة */
        .loading {
            position: relative;
            overflow: hidden;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* تأثيرات الظهور */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in-right {
            animation: slideInRight 0.5s ease-out;
        }

        @keyframes slideInRight {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        .slide-out-right {
            animation: slideOutRight 0.5s ease-out;
        }

        @keyframes slideOutRight {
            from { opacity: 1; transform: translateX(0); }
            to { opacity: 0; transform: translateX(100%); }
        }

        /* الأفاتار */
        .avatar-sm {
            width: 32px;
            height: 32px;
        }

        .avatar-initial {
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.875rem;
            color: white;
            background: var(--admin-gradient-primary);
        }

        /* تحسينات الشريط العلوي */
        .navbar-toggler {
            padding: var(--admin-spacing-sm);
            border: none;
        }

        .navbar-toggler:focus {
            box-shadow: none;
        }

        /* تحسينات إضافية للأداء */
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .card, .btn, .alert, .modal-content {
            will-change: transform;
        }

        /* تأثيرات تفاعلية إضافية */
        .hover-lift {
            transition: var(--admin-transition-base);
        }

        .hover-lift:hover {
            transform: translateY(-2px);
            box-shadow: var(--admin-shadow-lg);
        }

        .hover-scale {
            transition: var(--admin-transition-base);
        }

        .hover-scale:hover {
            transform: scale(1.05);
        }

        /* تحسينات الأيقونات */
        .icon-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--admin-gradient-primary);
            color: white;
            font-size: 1.2rem;
        }

        .icon-square {
            width: 40px;
            height: 40px;
            border-radius: var(--admin-border-radius);
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--admin-gradient-primary);
            color: white;
            font-size: 1.2rem;
        }

        /* تحسينات الإحصائيات */
        .stat-card {
            background: white;
            border-radius: var(--admin-border-radius-lg);
            padding: var(--admin-spacing-xl);
            box-shadow: var(--admin-shadow-sm);
            border: 1px solid rgba(229, 231, 235, 0.8);
            transition: var(--admin-transition-base);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--admin-gradient-primary);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--admin-shadow-lg);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 800;
            color: var(--admin-primary);
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stat-change {
            font-size: 0.75rem;
            font-weight: 600;
        }

        .stat-change.positive {
            color: var(--admin-success);
        }

        .stat-change.negative {
            color: var(--admin-danger);
        }

        /* تحسينات الطباعة */
        @media print {
            .sidebar, .admin-navbar, .btn, .dropdown, .modal {
                display: none !important;
            }

            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                break-inside: avoid;
            }

            .card-header {
                background: #f8f9fa !important;
                color: #000 !important;
            }

            body {
                background: white !important;
            }
        }

        /* النماذج المحسنة */
        .form-control, .form-select {
            border-radius: var(--admin-border-radius);
            border: 2px solid rgba(229, 231, 235, 0.8);
            transition: var(--admin-transition-base);
            padding: var(--admin-spacing-md);
            font-weight: 500;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--admin-accent);
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
            background: white;
            transform: translateY(-1px);
        }

        .form-label {
            font-weight: 600;
            color: var(--admin-primary);
            margin-bottom: var(--admin-spacing-sm);
            font-size: 0.875rem;
        }

        .form-group {
            margin-bottom: var(--admin-spacing-lg);
        }

        .input-group {
            border-radius: var(--admin-border-radius);
            overflow: hidden;
        }

        .input-group-text {
            background: var(--admin-gradient-primary);
            color: white;
            border: none;
            font-weight: 600;
        }

        /* الجداول المحسنة */
        .table {
            border-radius: var(--admin-border-radius-lg);
            overflow: hidden;
            box-shadow: var(--admin-shadow-sm);
            background: white;
        }

        .table thead th {
            background: var(--admin-gradient-primary);
            color: white;
            border: none;
            font-weight: 700;
            padding: var(--admin-spacing-lg);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .table tbody td {
            padding: var(--admin-spacing-lg);
            border-color: rgba(229, 231, 235, 0.5);
            vertical-align: middle;
        }

        .table-hover tbody tr {
            transition: var(--admin-transition-base);
        }

        .table-hover tbody tr:hover {
            background: rgba(99, 102, 241, 0.05);
            transform: scale(1.01);
            box-shadow: var(--admin-shadow-sm);
        }

        .table-dark thead th {
            background: var(--admin-bg-dark);
        }

        .table-responsive {
            border-radius: var(--admin-border-radius-lg);
            box-shadow: var(--admin-shadow-sm);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 56px;
                left: -100%;
                width: 250px;
                height: calc(100vh - 56px);
                transition: left 0.3s ease;
                z-index: 1000;
            }

            .sidebar.show {
                left: 0;
            }

            .card {
                margin-bottom: 1rem;
            }
        }

        /* تأثيرات التحميل */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        /* زر التبديل بين الوضعين - في الشريط العلوي */
        .theme-toggle {
            background: transparent;
            border: none;
            color: var(--admin-slate);
            padding: 8px;
            border-radius: var(--admin-border-radius-sm);
            transition: var(--admin-transition);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }

        .theme-toggle:hover {
            background: rgba(37, 99, 235, 0.1);
            color: var(--admin-royal-blue);
        }

        .theme-toggle i {
            font-size: 16px;
            transition: var(--admin-transition);
        }

        [data-theme="dark"] .theme-toggle {
            color: var(--admin-silver);
        }

        [data-theme="dark"] .theme-toggle:hover {
            background: rgba(37, 99, 235, 0.2);
            color: var(--admin-royal-blue);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل العلوي المحسن -->
    <nav class="navbar navbar-expand-lg admin-navbar" id="adminNavbar">
        <div class="container-fluid">
            <a class="navbar-brand" href="admin_dashboard.php">
                <i class="fas fa-shield-alt"></i>
                <span>لوحة تحكم المدير</span>
            </a>

            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-bars text-primary"></i>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="admin_dashboard.php">
                            <span><i class="fas fa-tachometer-alt me-1"></i>الرئيسية</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_users.php">
                            <span><i class="fas fa-users me-1"></i>المستخدمين</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_reports.php">
                            <span><i class="fas fa-chart-bar me-1"></i>التقارير</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_financial_reports.php">
                            <span><i class="fas fa-chart-line me-1"></i>المالية</span>
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- عرض الوقت -->
                    <li class="nav-item d-none d-lg-block">
                        <span class="nav-link text-muted" id="current-time"></span>
                    </li>

                    <!-- زر التبديل بين الوضعين -->
                    <li class="nav-item">
                        <button class="theme-toggle nav-link" onclick="toggleTheme()" id="themeToggle" title="تبديل الوضع">
                            <i class="fas fa-moon" id="themeIcon"></i>
                        </button>
                    </li>

                    <!-- إشعارات محسنة -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle position-relative" href="#" id="alertsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell fs-5"></i>
                            <span class="badge bg-danger badge-counter">3</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <h6 class="dropdown-header">
                                <i class="fas fa-bell me-2"></i>الإشعارات الحديثة
                            </h6>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-user-plus text-success"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-semibold">مستخدم جديد</div>
                                        <div class="small text-muted">تم تسجيل مستخدم جديد</div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <small class="text-muted">5 د</small>
                                    </div>
                                </div>
                            </a>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-exclamation-triangle text-warning"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-semibold">تحذير أمني</div>
                                        <div class="small text-muted">محاولة دخول مشبوهة</div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <small class="text-muted">15 د</small>
                                    </div>
                                </div>
                            </a>
                            <a class="dropdown-item" href="#">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-chart-line text-info"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <div class="fw-semibold">تقرير جديد</div>
                                        <div class="small text-muted">تقرير المبيعات الشهري</div>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <small class="text-muted">1 س</small>
                                    </div>
                                </div>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-center fw-semibold" href="admin_notifications.php">
                                عرض جميع الإشعارات
                            </a>
                        </div>
                    </li>

                    <!-- ملف المدير المحسن -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="avatar-sm me-2">
                                <div class="avatar-initial rounded-circle bg-primary">
                                    <?php echo strtoupper(substr($_SESSION['admin_full_name'] ?? 'A', 0, 1)); ?>
                                </div>
                            </div>
                            <span class="d-none d-lg-inline">
                                <?php echo htmlspecialchars($_SESSION['admin_full_name'] ?? 'المدير'); ?>
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <div class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm me-3">
                                        <div class="avatar-initial rounded-circle bg-primary">
                                            <?php echo strtoupper(substr($_SESSION['admin_full_name'] ?? 'A', 0, 1)); ?>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="fw-semibold"><?php echo htmlspecialchars($_SESSION['admin_full_name'] ?? 'المدير'); ?></div>
                                        <div class="small text-muted">@<?php echo htmlspecialchars($_SESSION['admin_username'] ?? ''); ?></div>
                                        <?php if ($_SESSION['admin_is_super'] ?? false): ?>
                                        <span class="badge bg-danger">مدير عام</span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="admin_profile.php">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a>
                            <a class="dropdown-item" href="admin_system.php">
                                <i class="fas fa-cogs me-2"></i>إعدادات النظام
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="index.php" target="_blank">
                                <i class="fas fa-external-link-alt me-2"></i>عرض النظام العادي
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-danger" href="admin_logout.php">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- عرض الرسائل -->
    <?php if (isset($_SESSION['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show m-3" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($_SESSION['success']); unset($_SESSION['success']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show m-3" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($_SESSION['error']); unset($_SESSION['error']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['warning'])): ?>
    <div class="alert alert-warning alert-dismissible fade show m-3" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($_SESSION['warning']); unset($_SESSION['warning']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['info'])): ?>
    <div class="alert alert-info alert-dismissible fade show m-3" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        <?php echo htmlspecialchars($_SESSION['info']); unset($_SESSION['info']); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    <?php endif; ?>



    <!-- Custom Admin JavaScript -->
    <script>
        // تحسينات تفاعلية متقدمة للوحة التحكم
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل المتدرج للبطاقات
            const cards = document.querySelectorAll('.card');
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const cardObserver = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        setTimeout(() => {
                            entry.target.classList.add('fade-in');
                        }, index * 100);
                        cardObserver.unobserve(entry.target);
                    }
                });
            }, observerOptions);

            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                cardObserver.observe(card);
            });

            // تحسين الشريط العلوي عند التمرير
            const navbar = document.getElementById('adminNavbar');
            let lastScrollTop = 0;

            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > 10) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }

                lastScrollTop = scrollTop;
            });

            // تحسين الأزرار مع تأثيرات متقدمة
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                // إضافة تأثير الموجة عند النقر
                button.addEventListener('click', function(e) {
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');

                    this.appendChild(ripple);

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // تأثير النقر للبطاقات
            const clickableCards = document.querySelectorAll('.card[data-href]');
            clickableCards.forEach(card => {
                card.style.cursor = 'pointer';
                card.addEventListener('click', function() {
                    window.location.href = this.dataset.href;
                });
            });

            // تحديث الوقت في الشريط العلوي
            function updateTime() {
                const now = new Date();
                const timeString = now.toLocaleString('ar-SA', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });

                const timeElement = document.getElementById('current-time');
                if (timeElement) {
                    timeElement.textContent = timeString;
                }
            }

            // تحديث الوقت كل دقيقة
            updateTime();
            setInterval(updateTime, 60000);

            // تأكيد العمليات الحساسة
            const dangerButtons = document.querySelectorAll('.btn-danger, .btn-warning');
            dangerButtons.forEach(button => {
                if (!button.hasAttribute('data-no-confirm')) {
                    button.addEventListener('click', function(e) {
                        if (!confirm('هل أنت متأكد من هذا الإجراء؟')) {
                            e.preventDefault();
                            return false;
                        }
                    });
                }
            });

            // تحسين الجداول - بسيط وهادئ
            const tables = document.querySelectorAll('.table');
            tables.forEach(table => {
                // إضافة تأثير hover بسيط للصفوف
                const rows = table.querySelectorAll('tbody tr');
                rows.forEach(row => {
                    row.addEventListener('mouseenter', function() {
                        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
                        this.style.backgroundColor = isDark ? 'rgba(148, 163, 184, 0.15)' : 'rgba(148, 163, 184, 0.1)';
                    });

                    row.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '';
                    });
                });
            });

            // تحسين النماذج
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input, select, textarea');
                inputs.forEach(input => {
                    input.addEventListener('focus', function() {
                        this.parentElement.style.transform = 'scale(1.02)';
                        this.parentElement.style.transition = 'transform 0.3s ease';
                    });

                    input.addEventListener('blur', function() {
                        this.parentElement.style.transform = 'scale(1)';
                    });
                });
            });

            // إضافة مؤشر التحميل للنماذج
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...';
                        submitBtn.disabled = true;
                    }
                });
            });



            // إضافة تأثيرات للإشعارات
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.animation = 'slideInRight 0.5s ease';

                // إخفاء تلقائي بعد 5 ثوان
                setTimeout(() => {
                    if (alert.parentElement) {
                        alert.style.animation = 'slideOutRight 0.5s ease';
                        setTimeout(() => {
                            if (alert.parentElement) {
                                alert.remove();
                            }
                        }, 500);
                    }
                }, 5000);
            });
        });

        // دوال مساعدة
        function showLoading(element) {
            element.classList.add('loading');
            element.style.pointerEvents = 'none';
        }

        function hideLoading(element) {
            element.classList.remove('loading');
            element.style.pointerEvents = 'auto';
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.top = '20px';
            notification.style.right = '20px';
            notification.style.zIndex = '9999';
            notification.style.minWidth = '300px';
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // إزالة تلقائية بعد 5 ثوان
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // إضافة CSS للرسوم المتحركة والتأثيرات
        const animationCSS = `
            @keyframes fadeInDown {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            @keyframes slideInRight {
                from { opacity: 0; transform: translateX(100%); }
                to { opacity: 1; transform: translateX(0); }
            }

            @keyframes slideOutRight {
                from { opacity: 1; transform: translateX(0); }
                to { opacity: 0; transform: translateX(100%); }
            }

            @keyframes rippleEffect {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            .ripple {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.6);
                transform: scale(0);
                animation: rippleEffect 0.6s linear;
                pointer-events: none;
            }

            .btn {
                position: relative;
                overflow: hidden;
            }

            .fade-in {
                opacity: 1 !important;
                transform: translateY(0) !important;
                transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1) !important;
            }

            .navbar.scrolled {
                backdrop-filter: blur(25px) saturate(200%);
                background: rgba(255, 255, 255, 0.98) !important;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }

            /* تحسينات إضافية للتفاعل */
            .card:hover .card-header {
                background: var(--admin-gradient-primary) !important;
                color: white !important;
            }

            .sidebar .nav-link:hover {
                background: var(--sidebar-bg-hover) !important;
                color: var(--sidebar-text-hover) !important;
                transform: translateX(2px);
            }

            .table-hover tbody tr:hover {
                background: rgba(148, 163, 184, 0.1) !important;
            }

            [data-theme="dark"] .table-hover tbody tr:hover {
                background: rgba(148, 163, 184, 0.15) !important;
            }

            /* تحسينات الشاشات الصغيرة */
            @media (max-width: 768px) {
                .fade-in {
                    animation-delay: 0s !important;
                }

                .card {
                    margin-bottom: 1rem;
                    transform: none !important;
                }

                .btn {
                    padding: 0.75rem 1.5rem;
                    font-size: 0.9rem;
                }
            }
        `;

        const style = document.createElement('style');
        style.textContent = animationCSS;
        document.head.appendChild(style);

        // نظام التبديل بين الوضع الداكن والفاتح
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            setTheme(newTheme);
        }

        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('admin-theme', theme);

            const themeIcon = document.getElementById('themeIcon');
            if (themeIcon) {
                if (theme === 'dark') {
                    themeIcon.className = 'fas fa-sun';
                    themeIcon.style.color = '#fbbf24';
                } else {
                    themeIcon.className = 'fas fa-moon';
                    themeIcon.style.color = '#64748b';
                }
            }
        }

        // تطبيق الوضع المحفوظ عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('admin-theme') || 'light';
            setTheme(savedTheme);
        });
    </script>
