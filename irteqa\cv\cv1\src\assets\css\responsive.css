/*! - - - - - -   Extra small devices (phones, less than 768px)   - - - - - - */
@media (max-width: 766px) {

  .intro h1 {
    font-size:    40px;
    line-height:  40px;
  }

  .social-icons ul {
    margin-left: 0;
  }

  /* .social-icons li {
    padding: 0;
  } */

  .social-icons a i {
    margin: 0 5px;
  }

  .mouse-icon {
    display: none;
  }

  .navbar-custom .navbar-brand,
  .navbar-custom .navbar-nav > li > a,
  .navbar-custom .navbar-nav .dropdown-menu > li > a {
    padding: 10px 15px;
  }

  .navbar-custom .dropdown-menu {
    margin-left: 20px;
  }

  /* about me section */
  .biography {
    margin-top:     20px;
    margin-bottom:  45px;
  }

  ul.list-check li {
    width: 100%;
  }

  .download-button .btn {
    margin-bottom: 20px;
  }

  /* resume */
  .resume:before,
  .resume:after {
    left: 1px;
  }

  /* skill section */
  .skill-title h3 {
    font-size: 12px;
  }

  /* facts section */
  .count-wrap i {
    font-size: 40px;
  }

  .count-wrap h3 {
    font-size:    30px;
    line-height:  16px;
  }

  .count-wrap p {
    font-size:    10px;
    font-weight:  400;
    line-height:  18px;
  }

  .count-wrap > div {
    margin-bottom: 40px;
  }

  #filter li {
    margin-bottom: 20px;
  }

  /* blog section */
  .blog-post-wrapper {
    margin-bottom: 40px;
  }

  /* hire us section */
  .hire-section h2 {
    line-height: 40px;
  }

  /* contact section */
  .contact-form {
    margin-bottom: 40px;
  }

  .contact-section div > i {
    float: none;
    margin-right: 0;
  }

  .center-xs {
    text-align: center;
  }

  .contact-section address,
  .contact-section .contact-number {
    font-size: 12px;
  }

  /* service */
  .service-wrap > div {
    margin-bottom: 100px;
  }

  .service-wrap > div:nth-child(4) {
    margin-bottom: 0;
  }

  /* HOME TWO */
  #homeTwo:before {
    width: 100%;
  }

  .personal-info {
    text-align: center;
    padding:    0 15px;
  }

  .personal-info h1 {
    font-size:    40px;
    line-height:  50px;
  }

  .personal-info .intro-sub {
    font-size:    18px;
    line-height:  20px;
  }

  .personal-info p {
    line-height:    20px;
    margin-bottom:  20px;
  }

  .personal-info .my-signature {
    display: none;
  }

  .personal-info .download-button a {
    padding:      5px 10px;
    font-size:    15px;
    margin-right: 0;
  }

  /* .timeline-heading */
  .timeline-heading h3.panel-title {
    font-size:    14px;
    line-height:  normal;
  }

  .timeline-heading span {
    font-size:    12px;
    font-weight:  600;
    line-height:  12px;
  }

  .timeline-heading span a.h4 {
    font-size:    14px;
    line-height:  20px;
  }

  .timeline>li .posted-date .month {
    font-size:    14px;
    line-height:  1.1;
  }

  .thumbnail {
    margin-bottom: 5px;
  }

}


/* Landscape Mobile */
@media only screen and (min-width: 480px) and (max-width: 767px) {

  .intro h1 {
    font-size:    60px;
    line-height:  80px;
  }

  .social-icons ul {
    margin-left: -5px;
  }

  .social-icons li {
    padding: 0 5px;
  }

  .social-icons a i {
    margin: 0 7px;
  }

  .mouse-icon {
    display: none;
  }

  .navbar-custom .navbar-brand,
  .navbar-custom .navbar-nav > li > a,
  .navbar-custom .navbar-nav .dropdown-menu > li > a {
    padding: 10px 15px;
  }

  .navbar-custom .dropdown-menu {
    margin-left: 20px;
  }

  /* about me section */
  .biography {
    margin-top:     20px;
    margin-bottom:  45px;
  }

  ul.list-check li {
    /* width: 50%; */
    width: 100%;
  }


  /* resume */
  .resume:before,
  .resume:after {
    left: 1px;
  }

  /* skill section */
  .skill-title h3 {
    font-size: 12px;
  }

  /* facts section */
  .count-wrap i {
    font-size: 64px;
  }

  .count-wrap h3 {
    font-size:    50px;
    line-height:  28px;
  }

  .count-wrap p {
    font-size:    18px;
    font-weight:  700;
    line-height:  28px;
  }

  .count-wrap > div {
    margin-bottom: 40px;
  }

  /* blog section */
  .blog-post-wrapper {
    margin-bottom: 40px;
  }

  /* hire us section */
  .hire-section h2 {
    line-height: 40px;
  }

  /* contact section */
  .contact-form {
    margin-bottom: 40px;
  }

  .contact-section .map-icon,
  .contact-section .mobile-icon {
    float: none;
  }
  .center-xs {
    text-align: center;
  }

  /* service */
  .service-wrap > div {
    margin-bottom: 100px;
  }

  .service-wrap > div:nth-child(4) {
    margin-bottom: 0;
  }

  /* HOME TWO */
  #homeTwo:before {
    width: 100%;
  }

  .personal-info {
    text-align: center;
    padding:    0 15px;
  }

  .personal-info h1 {
    font-size:    60px;
    line-height:  80px;
  }

  .personal-info .intro-sub {
    font-size:    24px;
    line-height:  32px;
  }

  .personal-info p {
    font-size:    14px;
    line-height:  28px;
  }

  .personal-info .my-signature {
    margin-bottom:  45px;
    display:        block;
  }

  .personal-info .download-button a {
    padding:      14px 20px;
    font-size:    14px;
    margin-right: 20px;
  }

}


/* Small devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991px) {

  .navbar-custom .navbar-brand {
    padding: 10px 15px;
  }

  .navbar-custom .navbar-nav > li > a,
  .navbar-custom .navbar-nav .dropdown-menu > li > a {
    font-size:  12px;
    padding:    15px;
  }

  /* about me section */
  .biography {
    margin-top:     20px;
    margin-bottom:  45px;
  }

  /* resume */
  .resume:before,
  .resume:after {
    left: 0px;
  }

  /* skill section */
  .skill-title h3 {
    font-size: 14px;
  }

  /* blog section */
  .blog-post-wrapper {
    margin-bottom: 40px;
  }

  .entry-meta ul li{
    padding: 0 5px;
  }

  /* contact section */
  .contact-form {
    margin-bottom: 40px;
  }

  .contact-section .map-icon,
  .contact-section .mobile-icon {
    float: left;
  }
  .center-xs {
    text-align: left;
  }

  .portfolio-item {
    min-height: 162px;
  }

  /* service */
  .service-wrap > div {
    margin-bottom: 100px;
  }

  .service-wrap > div:nth-child(3),
  .service-wrap > div:nth-child(4){
    margin-bottom: 0;
  }

  /* HOME TWO */
  #homeTwo:before {
    width: 100%;
  }

  .personal-info {
    text-align: center;
    padding:    0 15px;
  }

}


/* Medium devices (desktops, 992px and up) */
@media (min-width: 992px) and (max-width: 1199px) {

  .portfolio-item {
    min-height: 150px;
  }
  .download-button a {
    margin-right: 10px;
  }

}


/* Large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) and (max-width: 1279px) {

}


/* Large devices (large desktops, 1280px and up) */
@media screen and (min-width: 1280px) and (max-width: 1599px) {

}


/* Extra Large devices (large desktops, 1600px and up) */
@media screen and (min-width: 1600px) and (max-width: 1920px) {
  .personal-info {
    padding-right: 180px;
  }
}
