<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.3/jspdf.umd.min.js"></script>
    <title>السيرة الذاتية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            text-align: right;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            padding: 10px;
            border: 6px double;
            width: 1050px;
            
        }
        .cvtitle{
            display: flex;
    align-items: center;
    justify-content: center;
        }
        h2{            width: 170px;
            text-align: center;
            margin-top: 30px;
            margin-bottom: 10px;
            font-size: 2em;
            border: 2px solid black;
            padding: 5px;
            border-radius: 10px;
            background: #ededed;

        }
        .info {
            margin-bottom: 10px;
        }
        .info label {
            font-weight: bold;
        }
        hr {
            margin-top: 20px;
            margin-bottom: 20px;
            border: 0;
            border-top: 1px solid #ccc;
        }
        a {
            display: block;
            width: 70px;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            text-align: center;
            text-decoration: none;
            border-radius: 3px;
        }
        a:hover {
            background-color: #45a049;
        }
        .date {
            float: left;
            font-size: 10px;
        }
        .btns{
            position: absolute;
    top: 300px;
    right: 400px;
        }
        @media print {
            body{
                height: 1050px;
                border: 5px double black;
            }
            .container{
                border: none;
            }
            a {
                display: none;
            }
            @page {
    margin: 5mm; /* هامش الصفحة */ /* اطار الصفحة */
  }
        }
    </style>
</head>
<body >
    <div id="container" class="container">
        <div class="cvtitle">
        <h2>السيرة الذاتية</h2></div>
        <h3>المعلومات الشخصية:</h3>
        <div class="info">
            <label>الاسم الكامل:</label>
            <span><?php echo $_POST['fullname']; ?></span>
        </div>
        <div class="info">
            <label>السجل المدني:</label>
            <span><?php echo $_POST['idnum']; ?></span>
        </div>
        <div class="info">
            <label>تأريخ الميلاد:</label>
            <span><?php echo $_POST['bday']; ?></span>
        </div>
        <div class="info">
            <label>البريد الإلكتروني:</label>
            <span><?php echo $_POST['email']; ?></span>
        </div>
        <div class="info">
            <label>رقم الهاتف:</label>
            <span><?php echo $_POST['phone']; ?></span>
        </div>
        <div class="info">
            <label>العنوان:</label>
            <span><?php echo $_POST['address']; ?></span>
        </div>
        <hr>
        <div class="info">
            <h3>التعليم</h3>
            <?php 
                if (!empty($_POST['education']) && is_array($_POST['education'])) {
                    foreach ($_POST['education'] as $education) {
                        echo "<p>- $education</p>";
                    }
                }
            ?>
        </div>
        <hr>
        <div class="info">
            <h3>الدورات</h3>
            <?php 
                if (!empty($_POST['courses']) && is_array($_POST['courses'])) {
                    foreach ($_POST['courses'] as $index => $course) {
                        echo "<p>- $course <span class='date'>".$_POST['start_date_courses'][$index]." - ".$_POST['end_date_courses'][$index]."</span></p>";
                    }
                }
            ?>
        </div>
        <hr>
        <div class="info">
            <h3>الخبرات العملية</h3>
            <?php 
                if (!empty($_POST['experience']) && is_array($_POST['experience'])) {
                    foreach ($_POST['experience'] as $index => $exp) {
                        echo "<p>- $exp <span class='date'>".$_POST['start_date_experience'][$index]." - ".$_POST['end_date_experience'][$index]."</span></p>";
                    }
                }
            ?>
        </div>
        <hr>
        <div class="info">
            <h3>المهارات</h3>
            <?php 
                if (!empty($_POST['skills']) && is_array($_POST['skills'])) {
                    foreach ($_POST['skills'] as $skill) {
                        echo "<p>- $skill</p>";
                    }
                }
            ?>
        </div>
    </div>
    <div class="btns">
    <a style="background: #858585;" class="print-btn" onclick="window.print()">طباعة</a>
    <a id="download-pdf">تنزيل</a>
    <a style="background-color: #a54848;" href="#" class="back-btn" onclick="goBack()">العودة</a>
</div>
    <script>
// تعريف وظيفة JavaScript للانتقال إلى الصفحة السابقة
function goBack() {
  window.history.back();
} </script>
<script>
document.getElementById("download-pdf").addEventListener("click", function() {
    var pdf = new jsPDF(); // إنشاء ملف PDF

    // تحويل صفحة HTML إلى ملف PDF
    pdf.html(document.getElementById("container"), {
        callback: function(pdf) {
            pdf.save("document.pdf"); // حفظ الملف PDF
        }
    });
});
</script>

</body>
</html>
