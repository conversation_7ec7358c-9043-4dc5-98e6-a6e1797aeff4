<?php
/**
 * أداة اختبار أمان كلمات المرور المشفرة بـ bcrypt
 * هذه الأداة مخصصة للأغراض التعليمية واختبار الأمان فقط
 * 
 * تحذير: استخدم هذه الأداة فقط على كلمات المرور التي تملكها أو لديك إذن لاختبارها
 */

session_start();

// التحقق من الصلاحيات (يجب أن يكون المستخدم مدير)
if (!isset($_SESSION['admin_id'])) {
    die('غير مسموح - يجب تسجيل الدخول كمدير');
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة اختبار أمان bcrypt</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .card-header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: bold;
        }
        .btn-danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: bold;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .progress {
            height: 25px;
            border-radius: 15px;
            background-color: #e9ecef;
        }
        .progress-bar {
            border-radius: 15px;
            font-weight: bold;
        }
        .warning-box {
            background: linear-gradient(45deg, #ffa726, #ff7043);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        .result-box {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            border-left: 5px solid #28a745;
        }
        .stats-card {
            background: linear-gradient(45deg, #36d1dc, #5b86e5);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                
                <!-- تحذير أمني -->
                <div class="warning-box">
                    <h4><i class="fas fa-exclamation-triangle"></i> تحذير أمني مهم</h4>
                    <p class="mb-0">هذه الأداة مخصصة للأغراض التعليمية واختبار الأمان فقط. استخدمها فقط على كلمات المرور التي تملكها أو لديك إذن صريح لاختبارها.</p>
                </div>

                <div class="card">
                    <div class="card-header text-center">
                        <h3><i class="fas fa-shield-alt"></i> أداة اختبار أمان bcrypt</h3>
                        <p class="mb-0">اختبار قوة كلمات المرور المشفرة</p>
                    </div>
                    <div class="card-body">
                        
                        <!-- نموذج الاختبار -->
                        <form id="bcryptTestForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="bcrypt_hash" class="form-label">
                                            <i class="fas fa-key"></i> Hash المراد اختباره
                                        </label>
                                        <textarea class="form-control" id="bcrypt_hash" rows="3" 
                                                placeholder="$2y$10$example..." required></textarea>
                                        <small class="text-muted">أدخل bcrypt hash المراد اختبار قوته</small>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="test_method" class="form-label">
                                            <i class="fas fa-cogs"></i> طريقة الاختبار
                                        </label>
                                        <select class="form-control" id="test_method" required>
                                            <option value="common">كلمات المرور الشائعة</option>
                                            <option value="dictionary">قاموس كلمات المرور</option>
                                            <option value="brute_simple">القوة الغاشمة البسيطة</option>
                                            <option value="brute_advanced">القوة الغاشمة المتقدمة</option>
                                            <option value="pattern">الأنماط الشائعة</option>
                                            <option value="hybrid">الهجين (مختلط)</option>
                                            <option value="wordlist_extended">قاموس موسع</option>
                                            <option value="mask_attack">هجوم القناع</option>
                                            <option value="rule_based">قائم على القواعد</option>
                                            <option value="comprehensive">شامل (جميع الطرق)</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="max_attempts" class="form-label">
                                            <i class="fas fa-hashtag"></i> عدد المحاولات القصوى
                                        </label>
                                        <input type="number" class="form-control" id="max_attempts"
                                               value="1000000000000" min="1000" max="9999999999999">
                                        <small class="text-muted">يمكن استخدام حتى 9.9 تريليون محاولة</small>
                                    </div>
                                </div>
                            </div>

                            <!-- خيارات متقدمة -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="card" style="background: #f8f9fa;">
                                        <div class="card-header" style="background: #6c757d; color: white;">
                                            <h6 class="mb-0">
                                                <i class="fas fa-cogs"></i> خيارات متقدمة
                                                <button class="btn btn-sm btn-outline-light float-end" type="button"
                                                        data-bs-toggle="collapse" data-bs-target="#advancedOptions">
                                                    <i class="fas fa-chevron-down"></i>
                                                </button>
                                            </h6>
                                        </div>
                                        <div class="collapse" id="advancedOptions">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <div class="mb-3">
                                                            <label for="charset_type" class="form-label">
                                                                <i class="fas fa-font"></i> نوع الأحرف
                                                            </label>
                                                            <select class="form-control" id="charset_type">
                                                                <option value="basic">أساسي (a-z, 0-9)</option>
                                                                <option value="extended">موسع (+A-Z)</option>
                                                                <option value="special">خاص (+رموز)</option>
                                                                <option value="unicode">يونيكود (+عربي)</option>
                                                                <option value="custom">مخصص</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="mb-3">
                                                            <label for="min_length" class="form-label">
                                                                <i class="fas fa-ruler"></i> الطول الأدنى
                                                            </label>
                                                            <input type="number" class="form-control" id="min_length"
                                                                   value="1" min="1" max="20">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="mb-3">
                                                            <label for="max_length" class="form-label">
                                                                <i class="fas fa-ruler-horizontal"></i> الطول الأقصى
                                                            </label>
                                                            <input type="number" class="form-control" id="max_length"
                                                                   value="8" min="1" max="20">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="mb-3">
                                                            <label for="thread_count" class="form-label">
                                                                <i class="fas fa-microchip"></i> عدد الخيوط
                                                            </label>
                                                            <select class="form-control" id="thread_count">
                                                                <option value="1">خيط واحد</option>
                                                                <option value="2">خيطان</option>
                                                                <option value="4">4 خيوط</option>
                                                                <option value="8">8 خيوط</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="custom_charset" class="form-label">
                                                                <i class="fas fa-keyboard"></i> أحرف مخصصة
                                                            </label>
                                                            <input type="text" class="form-control" id="custom_charset"
                                                                   placeholder="abcd1234!@#$">
                                                            <small class="text-muted">يُستخدم عند اختيار "مخصص"</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="mask_pattern" class="form-label">
                                                                <i class="fas fa-mask"></i> نمط القناع
                                                            </label>
                                                            <input type="text" class="form-control" id="mask_pattern"
                                                                   placeholder="?l?l?l?d?d?d">
                                                            <small class="text-muted">?l=حرف صغير, ?u=حرف كبير, ?d=رقم, ?s=رمز</small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="use_common_substitutions">
                                                            <label class="form-check-label" for="use_common_substitutions">
                                                                استبدالات شائعة (a→@, e→3)
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="use_keyboard_patterns">
                                                            <label class="form-check-label" for="use_keyboard_patterns">
                                                                أنماط لوحة المفاتيح
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" id="use_date_patterns">
                                                            <label class="form-check-label" for="use_date_patterns">
                                                                أنماط التواريخ
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-3">
                                <button type="submit" class="btn btn-primary me-3">
                                    <i class="fas fa-play"></i> بدء الاختبار
                                </button>
                                <button type="button" class="btn btn-danger" id="stopTest" style="display: none;">
                                    <i class="fas fa-stop"></i> إيقاف الاختبار
                                </button>
                            </div>
                        </form>

                        <!-- شريط التقدم -->
                        <div id="progressContainer" style="display: none;" class="mt-4">
                            <div class="d-flex justify-content-between mb-2">
                                <span>التقدم:</span>
                                <span id="progressText">0%</span>
                            </div>
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     id="progressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2 text-center">
                                <small id="currentAttempt" class="text-muted"></small>
                            </div>
                        </div>

                        <!-- النتائج -->
                        <div id="resultsContainer" style="display: none;"></div>

                        <!-- إحصائيات -->
                        <div class="row mt-4">
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <h5><i class="fas fa-clock"></i></h5>
                                    <h4 id="timeElapsed">0s</h4>
                                    <small>الوقت المنقضي</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <h5><i class="fas fa-tachometer-alt"></i></h5>
                                    <h4 id="attemptsPerSecond">0</h4>
                                    <small>محاولة/ثانية</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stats-card">
                                    <h5><i class="fas fa-list-ol"></i></h5>
                                    <h4 id="totalAttempts">0</h4>
                                    <small>إجمالي المحاولات</small>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>

                <!-- معلومات تعليمية -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle"></i> معلومات حول bcrypt</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-rocket text-success"></i> طرق الاختبار المتقدمة:</h6>
                                <ul class="small">
                                    <li><strong>كلمات شائعة:</strong> +3 مليون كلمة</li>
                                    <li><strong>قاموس موسع:</strong> ملايين التركيبات</li>
                                    <li><strong>القوة الغاشمة المتقدمة:</strong> محسنة</li>
                                    <li><strong>هجوم القناع:</strong> أنماط محددة</li>
                                    <li><strong>قائم على القواعد:</strong> تحويلات ذكية</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-cogs text-primary"></i> خيارات متقدمة:</h6>
                                <ul class="small">
                                    <li><strong>أنواع أحرف:</strong> أساسي، موسع، خاص، يونيكود</li>
                                    <li><strong>أطوال متغيرة:</strong> 1-20 حرف</li>
                                    <li><strong>أنماط قناع:</strong> ?l?u?d?s</li>
                                    <li><strong>استبدالات شائعة:</strong> a→@, e→3</li>
                                    <li><strong>أنماط لوحة مفاتيح</strong></li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-shield-alt text-warning"></i> نصائح الأمان:</h6>
                                <ul class="small">
                                    <li>استخدم كلمات مرور طويلة (+12 حرف)</li>
                                    <li>امزج أحرف كبيرة وصغيرة وأرقام</li>
                                    <li>تجنب الكلمات الشائعة والأنماط</li>
                                    <li>استخدم رموز خاصة</li>
                                    <li>غير كلمات المرور بانتظام</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // فحص الاستئناف عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkForResume();
        });

        function checkForResume() {
            fetch('bcrypt_resume_checker.php?action=check_resume')
                .then(response => response.json())
                .then(data => {
                    if (data.can_resume) {
                        showResumeDialog(data.progress_data);
                    }
                })
                .catch(error => {
                    console.error('Error checking resume:', error);
                });
        }

        function showResumeDialog(progressData) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-play-circle"></i> استئناف الاختبار
                            </h5>
                        </div>
                        <div class="modal-body">
                            <p>تم العثور على اختبار سابق لم يكتمل:</p>
                            <ul>
                                <li><strong>الطريقة:</strong> ${getMethodName(progressData.method)}</li>
                                <li><strong>التقدم:</strong> ${progressData.percentage}%</li>
                                <li><strong>المحاولات:</strong> ${progressData.attempts.toLocaleString()} من ${progressData.max_attempts.toLocaleString()}</li>
                                <li><strong>الوقت المنقضي:</strong> ${formatTime(progressData.time_elapsed)}</li>
                            </ul>
                            <p>هل تريد استئناف هذا الاختبار أم بدء اختبار جديد؟</p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-success" onclick="resumeTest()">
                                <i class="fas fa-play"></i> استئناف
                            </button>
                            <button type="button" class="btn btn-warning" onclick="startNewTest()">
                                <i class="fas fa-plus"></i> اختبار جديد
                            </button>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                إلغاء
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // حفظ المرجع للاستخدام لاحقاً
            window.resumeModal = bootstrapModal;
            window.resumeModalElement = modal;
        }

        function resumeTest() {
            // إغلاق المودال
            if (window.resumeModal) {
                window.resumeModal.hide();
                document.body.removeChild(window.resumeModalElement);
            }

            // بدء فحص التقدم فوراً
            testRunning = true;
            testStarted = true;
            startTime = Date.now();

            // إظهار عناصر التقدم
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('stopTest').style.display = 'inline-block';
            document.querySelector('button[type="submit"]').style.display = 'none';

            // بدء المؤقت وفحص التقدم
            timerInterval = setInterval(updateTimer, 1000);
            progressInterval = setInterval(checkProgress, 1000); // فحص أسرع للاستئناف

            // فحص فوري للتقدم
            checkProgress();
        }

        function startNewTest() {
            // إغلاق المودال
            if (window.resumeModal) {
                window.resumeModal.hide();
                document.body.removeChild(window.resumeModalElement);
            }

            // مسح بيانات الاختبار السابق
            fetch('bcrypt_resume_checker.php?action=clear_progress')
                .then(response => response.json())
                .then(data => {
                    console.log('Previous test data cleared:', data);
                })
                .catch(error => {
                    console.error('Error clearing previous test:', error);
                });
        }

        function formatTime(seconds) {
            if (seconds < 60) {
                return seconds + 's';
            } else if (seconds < 3600) {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = Math.floor(seconds % 60);
                return `${minutes}m ${remainingSeconds}s`;
            } else {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return `${hours}h ${minutes}m`;
            }
        }
    </script>
    <script>
        let testRunning = false;
        let startTime = 0;
        let totalAttempts = 0;
        let timerInterval;
        let progressInterval;
        let testStarted = false;

        document.getElementById('bcryptTestForm').addEventListener('submit', function(e) {
            e.preventDefault();
            startTest();
        });

        document.getElementById('stopTest').addEventListener('click', function() {
            stopTest();
        });

        function startTest() {
            if (testRunning) return;

            const hash = document.getElementById('bcrypt_hash').value.trim();
            const method = document.getElementById('test_method').value;
            const maxAttempts = parseInt(document.getElementById('max_attempts').value);

            if (!hash) {
                alert('يرجى إدخال bcrypt hash');
                return;
            }

            // تحذير للأعداد الكبيرة جداً
            if (maxAttempts > 1000000000) {
                if (!confirm(`تحذير: اخترت ${maxAttempts.toLocaleString()} محاولة. هذا عدد كبير جداً وقد يستغرق وقتاً طويلاً جداً أو يستنزف موارد الخادم. هل تريد المتابعة؟`)) {
                    return;
                }
            }

            testRunning = true;
            testStarted = true;
            startTime = Date.now();
            totalAttempts = 0;

            // إظهار عناصر التقدم
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('stopTest').style.display = 'inline-block';
            document.querySelector('button[type="submit"]').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'none';

            // بدء المؤقت
            timerInterval = setInterval(updateTimer, 1000);

            // بدء فحص التقدم كل 3 ثواني
            progressInterval = setInterval(checkProgress, 3000);

            // جمع المعاملات المتقدمة
            const charsetType = document.getElementById('charset_type').value;
            const minLength = parseInt(document.getElementById('min_length').value);
            const maxLength = parseInt(document.getElementById('max_length').value);
            const threadCount = parseInt(document.getElementById('thread_count').value);
            const customCharset = document.getElementById('custom_charset').value;
            const maskPattern = document.getElementById('mask_pattern').value;
            const useSubstitutions = document.getElementById('use_common_substitutions').checked;
            const useKeyboardPatterns = document.getElementById('use_keyboard_patterns').checked;
            const useDatePatterns = document.getElementById('use_date_patterns').checked;

            // إرسال طلب AJAX
            const formData = new FormData();
            formData.append('action', 'test_bcrypt');
            formData.append('hash', hash);
            formData.append('method', method);
            formData.append('max_attempts', maxAttempts);
            formData.append('charset_type', charsetType);
            formData.append('min_length', minLength);
            formData.append('max_length', maxLength);
            formData.append('thread_count', threadCount);
            formData.append('custom_charset', customCharset);
            formData.append('mask_pattern', maskPattern);
            formData.append('use_substitutions', useSubstitutions);
            formData.append('use_keyboard_patterns', useKeyboardPatterns);
            formData.append('use_date_patterns', useDatePatterns);

            // إعداد timeout طويل للطلبات الكبيرة
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 دقائق timeout

            fetch('bcrypt_tester_backend.php', {
                method: 'POST',
                body: formData,
                signal: controller.signal
            })
            .then(response => {
                clearTimeout(timeoutId);
                return response.json();
            })
            .then(data => {
                testStarted = false;
                stopTest();
                showResults(data);
            })
            .catch(error => {
                clearTimeout(timeoutId);
                testStarted = false;
                stopTest();

                if (error.name === 'AbortError') {
                    console.log('Request was aborted due to timeout');
                    // الاختبار مستمر في الخلفية، استمر في فحص التقدم
                    showLongRunningMessage();
                } else {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء الاختبار: ' + error.message);
                }
            });

            // محاكاة التقدم
            simulateProgress(maxAttempts);
        }

        function stopTest() {
            testRunning = false;
            clearInterval(timerInterval);
            clearInterval(progressInterval);

            // إرسال طلب إيقاف للخادم
            if (testStarted) {
                fetch('bcrypt_progress_checker.php?action=stop_test')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Stop request sent:', data);
                    })
                    .catch(error => {
                        console.error('Error sending stop request:', error);
                    });
            }

            document.getElementById('stopTest').style.display = 'none';
            document.querySelector('button[type="submit"]').style.display = 'inline-block';
        }

        function checkProgress() {
            if (!testRunning) return;

            fetch('bcrypt_progress_checker.php?action=get_progress')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'running') {
                        // تحديث شريط التقدم
                        document.getElementById('progressBar').style.width = data.percentage + '%';
                        document.getElementById('progressText').textContent = data.percentage + '%';
                        document.getElementById('currentAttempt').textContent =
                            `المحاولة ${data.attempts.toLocaleString()} من ${data.max_attempts.toLocaleString()}`;

                        // تحديث الإحصائيات
                        totalAttempts = data.attempts;
                        document.getElementById('totalAttempts').textContent = data.attempts.toLocaleString();
                        document.getElementById('attemptsPerSecond').textContent = data.attempts_per_second.toLocaleString();

                        // إضافة معلومات إضافية
                        updateAdditionalInfo(data);

                    } else if (data.status === 'stalled') {
                        // الاختبار متوقف
                        showWarning('تحذير: الاختبار متوقف أو معلق. قد تحتاج لإعادة تشغيله.');

                    } else if (data.status === 'not_started') {
                        // لم يبدأ بعد - انتظار
                        console.log('Waiting for test to start...');
                    }
                })
                .catch(error => {
                    console.error('Error checking progress:', error);
                });
        }

        function updateAdditionalInfo(data) {
            // إضافة معلومات إضافية إذا لم تكن موجودة
            let additionalInfo = document.getElementById('additionalInfo');
            if (!additionalInfo) {
                additionalInfo = document.createElement('div');
                additionalInfo.id = 'additionalInfo';
                additionalInfo.className = 'mt-3 small text-muted';
                document.getElementById('progressContainer').appendChild(additionalInfo);
            }

            let infoHtml = `
                <div class="row">
                    <div class="col-md-3">
                        <strong>الذاكرة:</strong> ${data.memory_usage_mb} MB
                    </div>
                    <div class="col-md-3">
                        <strong>الطريقة:</strong> ${getMethodName(data.method)}
                    </div>
                    <div class="col-md-3">
                        <strong>الوقت المتبقي:</strong> ${data.estimated_time_remaining_formatted}
                    </div>
                    <div class="col-md-3">
                        <strong>كلمة حالية:</strong> ${data.current_password || 'غير محدد'}
                    </div>
                </div>
            `;

            additionalInfo.innerHTML = infoHtml;
        }

        function showWarning(message) {
            let warningDiv = document.getElementById('warningMessage');
            if (!warningDiv) {
                warningDiv = document.createElement('div');
                warningDiv.id = 'warningMessage';
                warningDiv.className = 'alert alert-warning mt-3';
                document.getElementById('progressContainer').appendChild(warningDiv);
            }

            warningDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        }

        function getMethodName(method) {
            const methods = {
                'common': 'كلمات شائعة',
                'dictionary': 'قاموس',
                'brute_simple': 'قوة غاشمة بسيطة',
                'brute_advanced': 'قوة غاشمة متقدمة',
                'pattern': 'أنماط',
                'hybrid': 'هجين',
                'wordlist_extended': 'قاموس موسع',
                'mask_attack': 'هجوم قناع',
                'rule_based': 'قائم على قواعد',
                'comprehensive': 'شامل'
            };
            return methods[method] || method;
        }

        function showLongRunningMessage() {
            const container = document.getElementById('resultsContainer');
            container.style.display = 'block';

            container.innerHTML = `
                <div class="alert alert-info">
                    <h5><i class="fas fa-clock"></i> اختبار طويل المدى</h5>
                    <p>الاختبار يستمر في الخلفية. سيتم تحديث التقدم تلقائياً.</p>
                    <p>يمكنك إغلاق هذه الصفحة والعودة لاحقاً لمتابعة التقدم.</p>
                    <div class="mt-3">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh"></i> تحديث الصفحة
                        </button>
                        <button class="btn btn-warning" onclick="forceStopTest()">
                            <i class="fas fa-stop"></i> إيقاف قسري
                        </button>
                    </div>
                </div>
            `;
        }

        function forceStopTest() {
            fetch('bcrypt_progress_checker.php?action=stop_test')
                .then(response => response.json())
                .then(data => {
                    alert('تم إرسال طلب الإيقاف. قد يستغرق بعض الوقت.');
                    location.reload();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('خطأ في إرسال طلب الإيقاف');
                });
        }

        function simulateProgress(maxAttempts) {
            let current = 0;
            const interval = setInterval(() => {
                if (!testRunning) {
                    clearInterval(interval);
                    return;
                }

                current += Math.random() * 50;
                if (current > maxAttempts) current = maxAttempts;

                const percentage = (current / maxAttempts) * 100;
                document.getElementById('progressBar').style.width = percentage + '%';
                document.getElementById('progressText').textContent = Math.round(percentage) + '%';
                document.getElementById('currentAttempt').textContent = `المحاولة ${Math.round(current).toLocaleString()} من ${maxAttempts.toLocaleString()}`;

                totalAttempts = Math.round(current);
                updateStats();

                if (current >= maxAttempts) {
                    clearInterval(interval);
                }
            }, 100);
        }

        function updateTimer() {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            document.getElementById('timeElapsed').textContent = elapsed + 's';
            updateStats();
        }

        function updateStats() {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const attemptsPerSecond = elapsed > 0 ? Math.round(totalAttempts / elapsed) : 0;
            
            document.getElementById('attemptsPerSecond').textContent = attemptsPerSecond.toLocaleString();
            document.getElementById('totalAttempts').textContent = totalAttempts.toLocaleString();
        }

        function showResults(data) {
            const container = document.getElementById('resultsContainer');
            container.style.display = 'block';

            let resultHtml = '<div class="result-box">';

            if (data.success && data.password_found) {
                resultHtml += `
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle"></i> تم العثور على كلمة المرور!</h5>
                        <p><strong>كلمة المرور:</strong> <code>${data.password}</code></p>
                        <p><strong>عدد المحاولات:</strong> ${data.attempts.toLocaleString()}</p>
                        <p><strong>الوقت المستغرق:</strong> ${data.time_taken}s</p>
                        <hr>
                        <p class="mb-0"><strong>تحذير:</strong> هذه كلمة مرور ضعيفة ويجب تغييرها فوراً!</p>
                    </div>
                `;
            } else {
                resultHtml += `
                    <div class="alert alert-success">
                        <h5><i class="fas fa-shield-alt"></i> كلمة المرور آمنة!</h5>
                        <p>لم يتم العثور على كلمة المرور بعد ${data.attempts.toLocaleString()} محاولة.</p>
                        <p><strong>الوقت المستغرق:</strong> ${data.time_taken}s</p>
                        <hr>
                        <p class="mb-0">كلمة المرور تبدو قوية ومقاومة للهجمات الأساسية.</p>
                    </div>
                `;
            }

            resultHtml += '</div>';
            container.innerHTML = resultHtml;
        }
    </script>
</body>
</html>
