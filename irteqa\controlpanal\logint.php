<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>login h</title>
</head>
<style>
    
    body{
        margin: 0;
    }
    .loginfo{
        width: 850px;
    border: 1px solid #adadad;
    margin:auto;
    height: 120px;
    border-radius: 10px;
    background: aliceblue;
    margin-bottom: 10px;
    block-size: auto;
    padding: 15px;
    font-size: x-large;
    
    }
    label{
        color: red;
    }
    p{
        margin: 3px;
    }
    td:nth-child(1) {
        width: 100px;
    }
    td:nth-child(2) {
        width: 250px;
    }
    td:nth-child(3) {
        width: 300px;
    }
    td:nth-child(4) {
        width: 200px;
    }
    section{
        margin-top: 50px;
        margin-left: 20px;
        margin-right: 20px;
    }
   div .osinfo{
    font-size: 20px;
    color: black;
    }
    h1{
        text-align: center;
    }
</style>
<section id="sctn">
        

<h1>سجل عمليات الدخول</h1>
        <?php
        $conn = new mysqli("localhost", "root", "", "login_history");
        $sql = "SELECT * FROM login_records";
$result = mysqli_query($conn,$sql);
$num=1;
if ($result){
    while($row = mysqli_fetch_assoc($result)){
        

        echo "<div class='loginfo'><table> " ;
echo "<tr><td><label>No: </label>" . $row['id'] . "</td><td><label>Name: </label>" . $row['username'] . "</td><td><label>Time: </label>" . $row['login_time'] . "</td><td><label>ip: </label>" . $row['ip_address'] . "</td></tr>";
//. "</td><td><label>Browser: </label>" . $row['browser']
    echo "</table>";
        echo "<div><label>System : </label><label class='osinfo'>" . $row['operating_system'] . "</label></div></div>";
        }}
        ?>
 </section> 
 </html>