<?php
// إعدادات أمان الجلسة
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // تعيين إلى 1 في HTTPS
ini_set('session.cookie_samesite', 'Strict');

// بدء الجلسة
session_start();

// تجديد معرف الجلسة دورياً لمنع session hijacking
if (!isset($_SESSION['last_regeneration'])) {
    $_SESSION['last_regeneration'] = time();
} elseif (time() - $_SESSION['last_regeneration'] > 300) { // كل 5 دقائق
    session_regenerate_id(true);
    $_SESSION['last_regeneration'] = time();
}

// تضمين ملف تكوين قاعدة البيانات
require_once 'db_config.php';

// تضمين نظام اللغات
require_once __DIR__ . '/../includes/language.php';

// دالة للتحقق من تسجيل الدخول مع فحوصات أمان إضافية
function isLoggedIn() {
    // فحص وجود معرف المستخدم
    if (!isset($_SESSION['user_id']) || empty($_SESSION['user_id'])) {
        return false;
    }

    // فحص انتهاء صلاحية الجلسة (4 ساعات)
    if (isset($_SESSION['login_time'])) {
        $session_lifetime = 4 * 60 * 60; // 4 ساعات بالثواني
        if (time() - $_SESSION['login_time'] > $session_lifetime) {
            // انتهت صلاحية الجلسة
            session_destroy();
            return false;
        }
    }

    // فحص IP Address للحماية من session hijacking
    if (isset($_SESSION['user_ip'])) {
        if ($_SESSION['user_ip'] !== $_SERVER['REMOTE_ADDR']) {
            // IP مختلف - احتمال session hijacking
            session_destroy();
            return false;
        }
    }

    // فحص User Agent للحماية الإضافية
    if (isset($_SESSION['user_agent'])) {
        if ($_SESSION['user_agent'] !== $_SERVER['HTTP_USER_AGENT']) {
            // User Agent مختلف - احتمال session hijacking
            session_destroy();
            return false;
        }
    }

    return true;
}

// إعادة توجيه المستخدم إذا لم يكن مسجل الدخول
function redirectIfNotLoggedIn() {
    if (!isLoggedIn()) {
        header("Location: login.php");
        exit();
    }
}

// الحصول على اتصال قاعدة بيانات المستخدم الحالي
function getCurrentUserDB() {
    if (!isLoggedIn()) {
        return null;
    }

    static $connection = null;

    if ($connection === null || $connection->ping() === false) {
        // إعادة الاتصال إذا كان الاتصال مغلقًا
        $connection = getUserDBConnection($_SESSION['user_id']);

        if ($connection) {
            // ضبط خيارات الاتصال
            $connection->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
            $connection->set_charset("utf8mb4");

            // تنظيف أي نتائج متبقية
            while ($connection->more_results()) {
                $connection->next_result();
                if ($result = $connection->store_result()) {
                    $result->free();
                }
            }
        }
    }

    return $connection;
}
?>