<?php
// بدء الجلسة
session_start();

// تضمين ملف تكوين قاعدة البيانات
require_once 'db_config.php';

// تضمين معالج الأخطاء المحسن
require_once __DIR__ . '/../enhanced_error_handler.php';

// تضمين نظام اللغات
require_once __DIR__ . '/../includes/language.php';

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// إعادة توجيه المستخدم إذا لم يكن مسجل الدخول
function redirectIfNotLoggedIn() {
    if (!isLoggedIn()) {
        header("Location: login.php");
        exit();
    }
}

// الحصول على اتصال قاعدة بيانات المستخدم الحالي مع معالجة شاملة للأخطاء
function getCurrentUserDB() {
    if (!isLoggedIn()) {
        error_log("محاولة الوصول لقاعدة البيانات بدون تسجيل دخول");
        return null;
    }

    static $connection = null;
    static $last_ping_time = 0;
    $current_time = time();

    // فحص الاتصال كل 30 ثانية فقط لتحسين الأداء
    $need_ping_check = ($current_time - $last_ping_time) > 30;

    if ($connection === null || ($need_ping_check && !$connection->ping())) {
        // إغلاق الاتصال القديم إذا كان موجوداً
        if ($connection !== null) {
            $connection->close();
            $connection = null;
        }

        try {
            // محاولة إنشاء اتصال جديد
            $connection = getUserDBConnection($_SESSION['user_id']);

            if ($connection && !$connection->connect_error) {
                // ضبط خيارات الاتصال
                $connection->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
                $connection->set_charset("utf8mb4");

                // ضبط timeout للاستعلامات
                $connection->options(MYSQLI_OPT_CONNECT_TIMEOUT, 10);
                $connection->options(MYSQLI_OPT_READ_TIMEOUT, 30);

                // تنظيف أي نتائج متبقية
                while ($connection->more_results()) {
                    $connection->next_result();
                    if ($result = $connection->store_result()) {
                        $result->free();
                    }
                }

                $last_ping_time = $current_time;
                error_log("تم إنشاء اتصال جديد بقاعدة بيانات المستخدم " . $_SESSION['user_id']);
            } else {
                error_log("فشل في إنشاء اتصال بقاعدة بيانات المستخدم: " . ($connection ? $connection->connect_error : "اتصال فارغ"));

                // محاولة إنشاء قاعدة البيانات إذا لم تكن موجودة
                $user_db_name = "sales_system_user_" . $_SESSION['user_id'];
                $temp_conn = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS);

                if (!$temp_conn->connect_error) {
                    $create_db_result = $temp_conn->query("CREATE DATABASE IF NOT EXISTS `$user_db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");

                    if ($create_db_result) {
                        error_log("تم إنشاء قاعدة بيانات المستخدم: $user_db_name");

                        // محاولة الاتصال مرة أخرى
                        $connection = getUserDBConnection($_SESSION['user_id']);
                        if ($connection && !$connection->connect_error) {
                            $connection->set_charset("utf8mb4");
                            error_log("تم الاتصال بقاعدة البيانات الجديدة بنجاح");
                        }
                    } else {
                        error_log("فشل في إنشاء قاعدة بيانات المستخدم: " . $temp_conn->error);
                    }
                }
                $temp_conn->close();

                return $connection;
            }
        } catch (Exception $e) {
            error_log("استثناء في getCurrentUserDB: " . $e->getMessage());
            $connection = null;
        }
    } elseif ($need_ping_check) {
        $last_ping_time = $current_time;
    }

    return $connection;
}

// دالة محسنة لإعادة تعيين اتصال قاعدة البيانات
function resetDBConnection($db) {
    if (!$db || $db->connect_error) {
        return getCurrentUserDB();
    }

    try {
        // تنظيف النتائج المتبقية
        while ($db->more_results()) {
            $db->next_result();
            if ($result = $db->store_result()) {
                $result->free();
            }
        }

        // فحص حالة الاتصال
        if (!$db->ping()) {
            error_log("فقدان الاتصال بقاعدة البيانات، جاري إعادة الاتصال...");
            return getCurrentUserDB();
        }

        return $db;
    } catch (Exception $e) {
        error_log("خطأ في resetDBConnection: " . $e->getMessage());
        return getCurrentUserDB();
    }
}

// دالة للتحقق من صحة قاعدة البيانات
function validateDatabaseConnection() {
    $db = getCurrentUserDB();

    if (!$db || $db->connect_error) {
        return [
            'status' => false,
            'error' => 'فشل الاتصال بقاعدة البيانات: ' . ($db ? $db->connect_error : 'اتصال فارغ')
        ];
    }

    // فحص الجداول الأساسية
    $required_tables = ['customers', 'products', 'sales', 'purchases'];
    $missing_tables = [];

    foreach ($required_tables as $table) {
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows == 0) {
            $missing_tables[] = $table;
        }
    }

    if (!empty($missing_tables)) {
        return [
            'status' => false,
            'error' => 'جداول مفقودة: ' . implode(', ', $missing_tables),
            'missing_tables' => $missing_tables
        ];
    }

    return ['status' => true, 'message' => 'قاعدة البيانات سليمة'];
}
?>