<?php
// بدء الجلسة
session_start();

// تضمين ملف تكوين قاعدة البيانات
require_once 'db_config.php';

// تضمين نظام اللغات
require_once __DIR__ . '/../includes/language.php';

// التحقق من وجود قاعدة البيانات الرئيسية وجدول المستخدمين
function ensureMainDatabase() {
    global $main_db;

    // فحص الاتصال بقاعدة البيانات الرئيسية
    if ($main_db->connect_error) {
        die("خطأ في الاتصال بقاعدة البيانات الرئيسية: " . $main_db->connect_error);
    }

    // فحص وجود جدول المستخدمين وإنشاؤه إذا لم يكن موجود
    $result = $main_db->query("SHOW TABLES LIKE 'users'");
    if (!$result || $result->num_rows == 0) {
        // إنشاء جدول المستخدمين
        $users_table_sql = "CREATE TABLE IF NOT EXISTS `users` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) NOT NULL,
            `email` varchar(100) NOT NULL UNIQUE,
            `phone` varchar(20) DEFAULT NULL,
            `is_active` tinyint(1) DEFAULT 1,
            `last_login` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_username` (`username`),
            UNIQUE KEY `idx_email` (`email`),
            KEY `idx_active` (`is_active`),
            KEY `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $main_db->query($users_table_sql);

        // إنشاء جدول المديرين
        $admins_table_sql = "CREATE TABLE IF NOT EXISTS `admins` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `username` varchar(50) NOT NULL UNIQUE,
            `password` varchar(255) NOT NULL,
            `full_name` varchar(100) NOT NULL,
            `email` varchar(100) NOT NULL UNIQUE,
            `phone` varchar(20) DEFAULT NULL,
            `permissions` JSON DEFAULT NULL,
            `is_super_admin` tinyint(1) DEFAULT 0,
            `is_active` tinyint(1) DEFAULT 1,
            `last_login` timestamp NULL DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_admin_username` (`username`),
            UNIQUE KEY `idx_admin_email` (`email`),
            KEY `idx_admin_active` (`is_active`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $main_db->query($admins_table_sql);

        // إنشاء جدول سجل العمليات
        $activity_log_sql = "CREATE TABLE IF NOT EXISTS `activity_log` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `user_id` int(11) DEFAULT NULL,
            `user_type` enum('user','admin') DEFAULT 'user',
            `action` varchar(100) NOT NULL,
            `table_name` varchar(50) DEFAULT NULL,
            `record_id` int(11) DEFAULT NULL,
            `old_data` JSON DEFAULT NULL,
            `new_data` JSON DEFAULT NULL,
            `description` text DEFAULT NULL,
            `ip_address` varchar(45) DEFAULT NULL,
            `user_agent` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_user_type` (`user_type`),
            KEY `idx_action` (`action`),
            KEY `idx_table_name` (`table_name`),
            KEY `idx_created` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $main_db->query($activity_log_sql);

        // إنشاء مدير افتراضي إذا لم يوجد
        $admin_count = $main_db->query("SELECT COUNT(*) as count FROM admins")->fetch_assoc()['count'];
        if ($admin_count == 0) {
            $default_admin_password = password_hash('admin123', PASSWORD_DEFAULT);
            $default_permissions = json_encode([
                'manage_users' => true,
                'view_all_data' => true,
                'manage_system' => true,
                'view_reports' => true,
                'manage_admins' => true
            ]);

            $main_db->query("INSERT INTO admins (username, password, full_name, email, permissions, is_super_admin)
                           VALUES ('admin', '$default_admin_password', 'المدير العام', '<EMAIL>', '$default_permissions', 1)");
        }
    }
}

// تأكد من وجود قاعدة البيانات الرئيسية
ensureMainDatabase();

// دالة للتحقق من تسجيل الدخول
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

// دالة للتحقق من تسجيل دخول المدير
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']);
}

// دالة للتحقق من صلاحيات المدير
function hasAdminPermission($permission) {
    if (!isAdminLoggedIn()) {
        return false;
    }

    if (isset($_SESSION['admin_is_super']) && $_SESSION['admin_is_super']) {
        return true;
    }

    $permissions = $_SESSION['admin_permissions'] ?? [];
    return isset($permissions[$permission]) && $permissions[$permission];
}

// دالة لتسجيل العمليات
function logActivity($action, $table_name = null, $record_id = null, $old_data = null, $new_data = null, $description = null) {
    global $main_db;

    $user_id = null;
    $user_type = 'user';

    if (isAdminLoggedIn()) {
        $user_id = $_SESSION['admin_id'];
        $user_type = 'admin';
    } elseif (isLoggedIn()) {
        $user_id = $_SESSION['user_id'];
        $user_type = 'user';
    }

    $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;

    $old_data_json = $old_data ? json_encode($old_data) : null;
    $new_data_json = $new_data ? json_encode($new_data) : null;

    $stmt = $main_db->prepare("INSERT INTO activity_log (user_id, user_type, action, table_name, record_id, old_data, new_data, description, ip_address, user_agent)
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    if ($stmt) {
        $stmt->bind_param("isssssssss", $user_id, $user_type, $action, $table_name, $record_id, $old_data_json, $new_data_json, $description, $ip_address, $user_agent);
        $stmt->execute();
        $stmt->close();
    }
}

// إعادة توجيه المستخدم إذا لم يكن مسجل الدخول
function redirectIfNotLoggedIn() {
    if (!isLoggedIn()) {
        header("Location: login.php");
        exit();
    }
}

// الحصول على اتصال قاعدة بيانات المستخدم الحالي مع إنشاء تلقائي
function getCurrentUserDB() {
    if (!isLoggedIn()) {
        return null;
    }

    static $connection = null;

    if ($connection === null || $connection->ping() === false) {
        // إنشاء قاعدة بيانات المستخدم إذا لم تكن موجودة
        $user_db_name = "sales_system_user_" . $_SESSION['user_id'];

        // الاتصال بالخادم لإنشاء قاعدة البيانات
        global $main_db;
        $main_db->query("CREATE DATABASE IF NOT EXISTS `$user_db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci");

        // إعادة الاتصال إذا كان الاتصال مغلقًا
        $connection = getUserDBConnection($_SESSION['user_id']);

        if ($connection) {
            // ضبط خيارات الاتصال
            $connection->options(MYSQLI_OPT_INT_AND_FLOAT_NATIVE, 1);
            $connection->set_charset("utf8mb4");

            // إنشاء الجداول إذا لم تكن موجودة
            createRequiredTables($connection);

            // تنظيف أي نتائج متبقية
            while ($connection->more_results()) {
                $connection->next_result();
                if ($result = $connection->store_result()) {
                    $result->free();
                }
            }
        }
    }

    return $connection;
}

// دالة لإنشاء الجداول المطلوبة
function createRequiredTables($db) {
    $tables = [
        'customers' => "CREATE TABLE IF NOT EXISTS `customers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `email` varchar(255) DEFAULT NULL,
            `tax_number` varchar(50) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_email` (`email`),
            KEY `idx_phone` (`phone`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'products' => "CREATE TABLE IF NOT EXISTS `products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
            `stock_quantity` decimal(10,2) DEFAULT 0.00,
            `unit` varchar(50) DEFAULT 'قطعة',
            `barcode` varchar(100) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_barcode` (`barcode`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'sales' => "CREATE TABLE IF NOT EXISTS `sales` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_date` (`date`),
            KEY `idx_payment_status` (`payment_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'purchases' => "CREATE TABLE IF NOT EXISTS `purchases` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `supplier_name` varchar(255) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_date` (`date`),
            KEY `idx_payment_status` (`payment_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'sale_items' => "CREATE TABLE IF NOT EXISTS `sale_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `sale_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_sale_id` (`sale_id`),
            KEY `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'purchase_items' => "CREATE TABLE IF NOT EXISTS `purchase_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `purchase_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_purchase_id` (`purchase_id`),
            KEY `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci"
    ];

    foreach ($tables as $table_name => $create_sql) {
        $db->query($create_sql);
    }
}
?>