<?php
// ملف ترحيل قاعدة البيانات للنظام الجديد

// دالة لترحيل بيانات المستخدم من النظام القديم إلى الجديد
function migrateUserData($user_id, $username) {
    global $main_db;
    
    // التحقق من وجود قاعدة بيانات المستخدم القديمة
    $old_db_name = "sales_system_user_" . $user_id;
    
    $check_old_db = $main_db->query("SHOW DATABASES LIKE '$old_db_name'");
    if (!$check_old_db || $check_old_db->num_rows == 0) {
        return false; // لا توجد بيانات قديمة للترحيل
    }
    
    // إنشاء جداول المستخدم الجديدة
    if (!createUserTables($username)) {
        return false;
    }
    
    $operations_db = getOperationsDB();
    if (!$operations_db) {
        return false;
    }
    
    $prefix = getUserTablePrefix($username);
    if (!$prefix) {
        return false;
    }
    
    // قائمة الجداول للترحيل
    $tables_to_migrate = [
        'customers' => 'customers',
        'products' => 'products', 
        'sales' => 'sales',
        'purchases' => 'purchases',
        'sale_items' => 'sale_items',
        'purchase_items' => 'purchase_items'
    ];
    
    $migrated_tables = [];
    $migration_errors = [];
    
    foreach ($tables_to_migrate as $old_table => $new_table) {
        try {
            // التحقق من وجود الجدول القديم
            $check_table = $main_db->query("SELECT 1 FROM `$old_db_name`.`$old_table` LIMIT 1");
            if (!$check_table) {
                continue; // الجدول غير موجود، تخطي
            }
            
            // نسخ البيانات
            $new_table_name = $prefix . $new_table;
            $copy_sql = "INSERT INTO `" . OPERATIONS_DB_NAME . "`.`$new_table_name` 
                        SELECT * FROM `$old_db_name`.`$old_table`";
            
            if ($operations_db->query($copy_sql)) {
                $migrated_tables[] = $old_table;
            } else {
                $migration_errors[] = "فشل في ترحيل جدول $old_table: " . $operations_db->error;
            }
            
        } catch (Exception $e) {
            $migration_errors[] = "خطأ في ترحيل جدول $old_table: " . $e->getMessage();
        }
    }
    
    return [
        'success' => count($migrated_tables) > 0,
        'migrated_tables' => $migrated_tables,
        'errors' => $migration_errors
    ];
}

// دالة لترحيل جميع المستخدمين
function migrateAllUsers() {
    global $main_db;
    
    $users_result = $main_db->query("SELECT id, username FROM users ORDER BY id");
    if (!$users_result) {
        return false;
    }
    
    $migration_results = [];
    
    while ($user = $users_result->fetch_assoc()) {
        $user_id = $user['id'];
        $username = $user['username'];
        
        $result = migrateUserData($user_id, $username);
        $migration_results[$username] = $result;
    }
    
    return $migration_results;
}

// دالة للتحقق من سلامة البيانات بعد الترحيل
function verifyMigration($username) {
    $operations_db = getOperationsDB();
    if (!$operations_db) {
        return false;
    }
    
    $prefix = getUserTablePrefix($username);
    if (!$prefix) {
        return false;
    }
    
    $tables_to_check = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
    $verification_results = [];
    
    foreach ($tables_to_check as $table) {
        $table_name = $prefix . $table;
        
        // فحص وجود الجدول
        $check_table = $operations_db->query("SHOW TABLES LIKE '$table_name'");
        if (!$check_table || $check_table->num_rows == 0) {
            $verification_results[$table] = [
                'exists' => false,
                'count' => 0,
                'structure_ok' => false
            ];
            continue;
        }
        
        // عدد السجلات
        $count_result = $operations_db->query("SELECT COUNT(*) as count FROM `$table_name`");
        $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
        
        // فحص هيكل الجدول
        $structure_result = $operations_db->query("DESCRIBE `$table_name`");
        $structure_ok = $structure_result && $structure_result->num_rows > 0;
        
        $verification_results[$table] = [
            'exists' => true,
            'count' => $count,
            'structure_ok' => $structure_ok
        ];
    }
    
    return $verification_results;
}

// دالة لإنشاء نسخة احتياطية قبل الترحيل
function createBackupBeforeMigration() {
    global $main_db;
    
    $backup_dir = __DIR__ . '/../backups';
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $backup_file = $backup_dir . '/backup_before_migration_' . date('Y-m-d_H-i-s') . '.sql';
    
    // هذه دالة مبسطة - في الواقع تحتاج لاستخدام mysqldump
    $tables_result = $main_db->query("SHOW TABLES");
    if (!$tables_result) {
        return false;
    }
    
    $backup_content = "-- نسخة احتياطية قبل الترحيل\n";
    $backup_content .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
    
    while ($table_row = $tables_result->fetch_array()) {
        $table_name = $table_row[0];
        
        // هيكل الجدول
        $create_table_result = $main_db->query("SHOW CREATE TABLE `$table_name`");
        if ($create_table_result) {
            $create_table = $create_table_result->fetch_array();
            $backup_content .= "\n-- هيكل الجدول $table_name\n";
            $backup_content .= $create_table[1] . ";\n\n";
        }
        
        // بيانات الجدول (عينة صغيرة فقط)
        $data_result = $main_db->query("SELECT * FROM `$table_name` LIMIT 10");
        if ($data_result && $data_result->num_rows > 0) {
            $backup_content .= "-- عينة من بيانات الجدول $table_name\n";
            while ($row = $data_result->fetch_assoc()) {
                $values = array_map(function($value) {
                    return "'" . addslashes($value) . "'";
                }, array_values($row));
                $backup_content .= "INSERT INTO `$table_name` VALUES (" . implode(', ', $values) . ");\n";
            }
            $backup_content .= "\n";
        }
    }
    
    return file_put_contents($backup_file, $backup_content) !== false ? $backup_file : false;
}

// دالة لتنظيف قواعد البيانات القديمة (بعد التأكد من نجاح الترحيل)
function cleanupOldDatabases() {
    global $main_db;
    
    // البحث عن قواعد البيانات القديمة
    $databases_result = $main_db->query("SHOW DATABASES LIKE 'sales_system_user_%'");
    if (!$databases_result) {
        return false;
    }
    
    $old_databases = [];
    while ($db_row = $databases_result->fetch_array()) {
        $old_databases[] = $db_row[0];
    }
    
    return $old_databases;
}

// دالة لإنشاء تقرير الترحيل
function generateMigrationReport($migration_results) {
    $report = "تقرير ترحيل البيانات\n";
    $report .= "=====================\n";
    $report .= "تاريخ الترحيل: " . date('Y-m-d H:i:s') . "\n\n";
    
    $total_users = count($migration_results);
    $successful_migrations = 0;
    
    foreach ($migration_results as $username => $result) {
        $report .= "المستخدم: $username\n";
        
        if ($result && $result['success']) {
            $successful_migrations++;
            $report .= "  الحالة: نجح الترحيل\n";
            $report .= "  الجداول المرحلة: " . implode(', ', $result['migrated_tables']) . "\n";
        } else {
            $report .= "  الحالة: فشل الترحيل\n";
            if ($result && isset($result['errors'])) {
                $report .= "  الأخطاء: " . implode(', ', $result['errors']) . "\n";
            }
        }
        $report .= "\n";
    }
    
    $report .= "الملخص:\n";
    $report .= "========\n";
    $report .= "إجمالي المستخدمين: $total_users\n";
    $report .= "الترحيل الناجح: $successful_migrations\n";
    $report .= "الترحيل الفاشل: " . ($total_users - $successful_migrations) . "\n";
    
    return $report;
}

// دالة لاختبار النظام الجديد
function testNewSystem() {
    $tests = [];
    
    // اختبار الاتصال بقواعد البيانات
    $tests['main_db_connection'] = $GLOBALS['main_db'] && !$GLOBALS['main_db']->connect_error;
    $tests['operations_db_connection'] = getOperationsDB() && !getOperationsDB()->connect_error;
    
    // اختبار دوال البادئة
    $test_username = 'test_user';
    $tests['prefix_function'] = !empty(getUserTablePrefix($test_username));
    $tests['table_name_function'] = !empty(getUserTableName('customers', $test_username));
    
    // اختبار إنشاء الجداول
    $tests['create_tables'] = createUserTables($test_username);
    
    // اختبار تحديث الاستعلامات
    $test_query = "SELECT * FROM customers";
    $updated_query = updateQueryWithUserPrefix($test_query, $test_username);
    $tests['query_update'] = $updated_query !== $test_query;
    
    return $tests;
}
?>
