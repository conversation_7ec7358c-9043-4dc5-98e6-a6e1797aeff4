<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        #chatbox {
            width: 300px;
            height: 400px;
            border: 1px solid #ccc;
            position: fixed;
            bottom: 10px;
            right: 10px;
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #chatIcon {
            position: fixed;
            bottom: 10px;
            right: 10px;
            cursor: pointer;
        }

        #chatFrame {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>

    <script>
        // إنشاء عناصر HTML باستخدام JavaScript
        var chatIcon = document.createElement('div');
        chatIcon.id = 'chatIcon';
        chatIcon.textContent = '💬 Chat';
        chatIcon.style.position = 'fixed';
        chatIcon.style.bottom = '10px';
        chatIcon.style.right = '10px';
        chatIcon.style.cursor = 'pointer';
        chatIcon.onclick = toggleChatbox;

        var chatbox = document.createElement('div');
        chatbox.id = 'chatbox';
        chatbox.style.width = '300px';
        chatbox.style.height = '400px';
        chatbox.style.border = '1px solid #ccc';
        chatbox.style.position = 'fixed';
        chatbox.style.bottom = '10px';
        chatbox.style.right = '10px';
        chatbox.style.display = 'none';
        chatbox.style.opacity = 0;
        chatbox.style.transition = 'opacity 0.3s ease';

        var chatFrame = document.createElement('iframe');
        chatFrame.id = 'chatFrame';
        chatFrame.src = 'http://localhost:8080/irteqa/chatapp2/users.php'; // رابط الصفحة التي تريد عرضها في Chatbox
        chatFrame.setAttribute('frameborder', '0');

        // إضافة العناصر إلى الصفحة
        document.body.appendChild(chatIcon);
        document.body.appendChild(chatbox);
        chatbox.appendChild(chatFrame);

        // دالة لتبديل عرض/إخفاء Chatbox
        function toggleChatbox() {
            if (chatbox.style.display === 'none' || chatbox.style.display === '') {
                chatbox.style.display = 'block';
                chatbox.style.opacity = 1;
            } else {
                chatbox.style.opacity = 0;
                setTimeout(function () {
                    chatbox.style.display = 'none';
                }, 300);
            }
        }
    </script>

</body>
</html>
