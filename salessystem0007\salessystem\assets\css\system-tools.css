/**
 * تنسيقات خاصة بصفحة أدوات النظام
 */

/* بطاقة أدوات النظام الرئيسية */
.system-tools-card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    overflow: hidden;
}

.system-tools-card .card-header {
    background: linear-gradient(135deg, #343a40 0%, #212529 100%);
    border-bottom: none;
    padding: 1.5rem;
}

.system-tools-card .card-body {
    padding: 2rem;
}

/* تنسيق التبويبات */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 10px 10px 0 0;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
    margin-right: 0.25rem;
    transition: all 0.3s ease;
    position: relative;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: #ffffff;
    border-color: #e9ecef #e9ecef #ffffff;
    border-width: 2px 2px 0 2px;
    border-style: solid;
    font-weight: 600;
}

.nav-tabs .nav-link i {
    margin-right: 0.5rem;
}

/* محتوى التبويبات */
.tab-content {
    background: #ffffff;
    border-radius: 0 0 15px 15px;
    padding: 2rem;
    min-height: 500px;
}

.tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* بطاقات الأدوات */
.tool-card {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.tool-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    transform: translateY(-3px);
}

.tool-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
}

.tool-content {
    flex: 1;
}

.tool-content h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tool-content p {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* بطاقات حالة النظام */
.card .card-header {
    font-weight: 600;
    border-bottom: none;
}

.card .card-body {
    padding: 1.5rem;
}

/* الشارات */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.75rem;
    border-radius: 15px;
}

/* وحدة التحكم */
.console-output {
    background: #000000 !important;
    color: #00ff00 !important;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

.console-output div {
    margin-bottom: 0.25rem;
}

.input-group .input-group-text {
    background: #343a40;
    color: #ffffff;
    border-color: #495057;
    font-family: monospace;
    font-weight: bold;
}

.input-group .form-control {
    font-family: monospace;
    background: #f8f9fa;
    border-color: #495057;
}

.input-group .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تحسينات للأزرار */
.btn {
    border-radius: 20px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* ألوان مخصصة للحالات */
.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%) !important;
}

.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

/* تحسينات للتصميم المتجاوب */
@media (max-width: 768px) {
    .system-tools-card .card-body {
        padding: 1rem;
    }
    
    .tab-content {
        padding: 1rem;
    }
    
    .nav-tabs .nav-link {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
    }
    
    .nav-tabs .nav-link i {
        display: none;
    }
    
    .tool-card {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }
    
    .tool-icon {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .console-output {
        height: 150px !important;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .nav-tabs {
        flex-wrap: wrap;
    }
    
    .nav-tabs .nav-link {
        flex: 1;
        text-align: center;
        margin-right: 0;
        margin-bottom: 0.25rem;
        border-radius: 10px;
    }
    
    .tool-card {
        margin-bottom: 1rem;
    }
    
    .btn-sm {
        width: 100%;
    }
}

/* تأثيرات خاصة */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.online {
    background: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.3);
    animation: pulse 2s infinite;
}

.status-indicator.offline {
    background: #dc3545;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* تحسينات للجداول */
.table {
    margin-bottom: 0;
}

.table td {
    vertical-align: middle;
    padding: 0.75rem;
}

.table-sm td {
    padding: 0.5rem;
}

/* تحسينات للتنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

/* تحسينات للأيقونات */
.fas, .far {
    margin-right: 0.5rem;
}

/* تحسينات للنصوص */
h5 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

h6 {
    color: #495057;
    font-weight: 600;
}

/* تحسينات للمسافات */
.mb-custom {
    margin-bottom: 2rem;
}

.mt-custom {
    margin-top: 2rem;
}
