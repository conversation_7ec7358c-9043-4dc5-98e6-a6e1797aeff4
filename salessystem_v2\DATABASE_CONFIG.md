# إعدادات قاعدة البيانات - salessystem_v2

## نظرة عامة
هذا الملف يوضح إعدادات قاعدة البيانات المحدثة للنسخة الثانية من نظام المبيعات.

## قواعد البيانات

### 1. قاعدة البيانات الرئيسية
- **الاسم:** `u193708811_system_main`
- **المستخدم:** `sales01`
- **كلمة المرور:** `dNz35nd5@`
- **الخادم:** `localhost`
- **الترميز:** `utf8mb4`

**الغرض:**
- تخزين بيانات المستخدمين
- تخزين بيانات المديرين
- سجل العمليات والأنشطة

**الجداول:**
```sql
users           -- المستخدمون
admins          -- المديرون  
activity_log    -- سجل العمليات
```

### 2. قاعدة بيانات العمليات
- **الاسم:** `u193708811_operations`
- **المستخدم:** `sales02`
- **كلمة المرور:** `dNz35nd5@`
- **الخادم:** `localhost`
- **الترميز:** `utf8mb4`

**الغرض:**
- تخزين بيانات العمليات لجميع المستخدمين
- جداول بادئة باسم المستخدم
- عزل بيانات كل مستخدم

**الجداول (مع البادئة):**
```sql
{username}_customers      -- عملاء المستخدم
{username}_products       -- منتجات المستخدم
{username}_sales          -- مبيعات المستخدم
{username}_purchases      -- مشتريات المستخدم
{username}_sale_items     -- عناصر المبيعات
{username}_purchase_items -- عناصر المشتريات
```

## أمثلة عملية

### مستخدم باسم "ahmed":
```sql
-- في قاعدة بيانات u193708811_operations
ahmed_customers      -- عملاء أحمد
ahmed_products       -- منتجات أحمد
ahmed_sales          -- مبيعات أحمد
ahmed_purchases      -- مشتريات أحمد
ahmed_sale_items     -- عناصر مبيعات أحمد
ahmed_purchase_items -- عناصر مشتريات أحمد
```

### مستخدم باسم "sara":
```sql
-- في قاعدة بيانات u193708811_operations
sara_customers       -- عملاء سارة
sara_products        -- منتجات سارة
sara_sales           -- مبيعات سارة
sara_purchases       -- مشتريات سارة
sara_sale_items      -- عناصر مبيعات سارة
sara_purchase_items  -- عناصر مشتريات سارة
```

## إعدادات الاتصال في PHP

### ملف التكوين (config/db_config.php):
```php
// قاعدة البيانات الرئيسية
define('MAIN_DB_HOST', 'localhost');
define('MAIN_DB_USER', 'sales01');
define('MAIN_DB_PASS', 'dNz35nd5@');
define('MAIN_DB_NAME', 'u193708811_system_main');

// قاعدة بيانات العمليات
define('OPERATIONS_DB_HOST', 'localhost');
define('OPERATIONS_DB_USER', 'sales02');
define('OPERATIONS_DB_PASS', 'dNz35nd5@');
define('OPERATIONS_DB_NAME', 'u193708811_operations');
```

### إنشاء الاتصالات:
```php
// الاتصال بقاعدة البيانات الرئيسية
$main_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, MAIN_DB_NAME);
$main_db->set_charset("utf8mb4");

// الاتصال بقاعدة بيانات العمليات
$operations_db = new mysqli(OPERATIONS_DB_HOST, OPERATIONS_DB_USER, OPERATIONS_DB_PASS, OPERATIONS_DB_NAME);
$operations_db->set_charset("utf8mb4");
```

## الصلاحيات المطلوبة

### المستخدم sales01:
```sql
-- صلاحيات قاعدة البيانات الرئيسية
GRANT SELECT, INSERT, UPDATE, DELETE ON u193708811_system_main.* TO 'sales01'@'localhost';
GRANT CREATE TEMPORARY TABLES ON u193708811_system_main.* TO 'sales01'@'localhost';
```

### المستخدم sales02:
```sql
-- صلاحيات قاعدة بيانات العمليات
GRANT ALL PRIVILEGES ON u193708811_operations.* TO 'sales02'@'localhost';
GRANT CREATE ON *.* TO 'sales02'@'localhost';
```

## أوامر إنشاء قواعد البيانات

### إنشاء قاعدة البيانات الرئيسية:
```sql
CREATE DATABASE IF NOT EXISTS `u193708811_system_main`
CHARACTER SET utf8mb4
COLLATE utf8mb4_general_ci;
```

### إنشاء قاعدة بيانات العمليات:
```sql
CREATE DATABASE IF NOT EXISTS `u193708811_operations`
CHARACTER SET utf8mb4
COLLATE utf8mb4_general_ci;
```

## هيكل الجداول

### جدول المستخدمين (في قاعدة البيانات الرئيسية):
```sql
CREATE TABLE `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL UNIQUE,
    `password` varchar(255) NOT NULL,
    `full_name` varchar(100) NOT NULL,
    `email` varchar(100) NOT NULL UNIQUE,
    `phone` varchar(20) DEFAULT NULL,
    `is_active` tinyint(1) DEFAULT 1,
    `last_login` timestamp NULL DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_username` (`username`),
    UNIQUE KEY `idx_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

### جدول العملاء (في قاعدة بيانات العمليات):
```sql
CREATE TABLE `{username}_customers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `tax_number` varchar(50) DEFAULT NULL,
    `address` text DEFAULT NULL,
    `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`),
    KEY `idx_customer_type` (`customer_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

## اختبار الاتصال

### اختبار قاعدة البيانات الرئيسية:
```php
$test_main = new mysqli('localhost', 'sales01', 'dNz35nd5@', 'u193708811_system_main');
if ($test_main->connect_error) {
    die('فشل الاتصال: ' . $test_main->connect_error);
} else {
    echo 'نجح الاتصال بقاعدة البيانات الرئيسية';
}
$test_main->close();
```

### اختبار قاعدة بيانات العمليات:
```php
$test_ops = new mysqli('localhost', 'sales02', 'dNz35nd5@', 'u193708811_operations');
if ($test_ops->connect_error) {
    die('فشل الاتصال: ' . $test_ops->connect_error);
} else {
    echo 'نجح الاتصال بقاعدة بيانات العمليات';
}
$test_ops->close();
```

## استكشاف الأخطاء

### أخطاء الاتصال الشائعة:

#### 1. خطأ المصادقة:
```
Access denied for user 'sales01'@'localhost'
```
**الحل:** تحقق من اسم المستخدم وكلمة المرور

#### 2. قاعدة البيانات غير موجودة:
```
Unknown database 'u193708811_operations'
```
**الحل:** قم بإنشاء قاعدة البيانات أولاً

#### 3. عدم وجود صلاحيات:
```
Access denied for user 'sales02'@'localhost' to database 'u193708811_operations'
```
**الحل:** تحقق من صلاحيات المستخدم

### أدوات التشخيص:
1. `test_connection.php` - اختبار الاتصال
2. `test_system.php` - اختبار النظام الكامل
3. `update_database.php` - إعداد قواعد البيانات

## النسخ الاحتياطي

### نسخ احتياطي لقاعدة البيانات الرئيسية:
```bash
mysqldump -u sales01 -p u193708811_system_main > backup_main.sql
```

### نسخ احتياطي لقاعدة بيانات العمليات:
```bash
mysqldump -u sales02 -p u193708811_operations > backup_operations.sql
```

### استعادة النسخة الاحتياطية:
```bash
mysql -u sales01 -p u193708811_system_main < backup_main.sql
mysql -u sales02 -p u193708811_operations < backup_operations.sql
```

## الأمان

### نصائح الأمان:
1. **كلمات مرور قوية:** استخدم كلمات مرور معقدة
2. **صلاحيات محدودة:** امنح الحد الأدنى من الصلاحيات المطلوبة
3. **النسخ الاحتياطي:** اعمل نسخ احتياطي منتظم
4. **المراقبة:** راقب سجل العمليات

### تشفير الاتصال:
```php
// تفعيل SSL (إذا كان متاحاً)
$main_db->ssl_set(null, null, null, null, null);
$main_db->real_connect(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, MAIN_DB_NAME, 3306, null, MYSQLI_CLIENT_SSL);
```

---

**تاريخ آخر تحديث:** 2024-12-19  
**الإصدار:** 2.0  
**المطور:** نظام المبيعات المحسن
