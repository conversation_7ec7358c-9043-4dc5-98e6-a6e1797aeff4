<?php
require_once 'config/init.php';

// صفحة تحديث قاعدة البيانات للنظام الجديد
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث قاعدة البيانات - salessystem_v2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .update-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        .step { margin-bottom: 15px; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-database"></i> تحديث قاعدة البيانات - salessystem_v2
                </h1>
                
                <!-- معلومات التحديث -->
                <div class="update-section">
                    <h3><i class="fas fa-info-circle"></i> معلومات التحديث</h3>
                    <div class="alert alert-info">
                        <h5><i class="fas fa-exclamation-triangle"></i> هام:</h5>
                        <p>سيتم تحديث النظام لاستخدام قاعدة البيانات الموجودة <strong>u193708811_system_main</strong> وإنشاء قاعدة بيانات جديدة للعمليات <strong>u193708811_system_operations</strong></p>
                    </div>
                </div>

                <!-- فحص قاعدة البيانات الحالية -->
                <div class="update-section">
                    <h3><i class="fas fa-search"></i> فحص قاعدة البيانات الحالية</h3>
                    
                    <?php
                    echo "<div class='step'>";
                    echo "<h5>1. فحص الاتصال بقاعدة البيانات الرئيسية:</h5>";
                    
                    if ($main_db && !$main_db->connect_error) {
                        echo "<p class='success'><i class='fas fa-check'></i> متصل بنجاح - " . MAIN_DB_NAME . "</p>";
                        
                        // فحص الجداول الموجودة
                        $tables_check = $main_db->query("SHOW TABLES");
                        if ($tables_check) {
                            $tables = [];
                            while ($row = $tables_check->fetch_array()) {
                                $tables[] = $row[0];
                            }
                            
                            echo "<p class='info'><i class='fas fa-table'></i> الجداول الموجودة (" . count($tables) . "):</p>";
                            echo "<ul class='list-group'>";
                            foreach ($tables as $table) {
                                echo "<li class='list-group-item d-flex justify-content-between align-items-center'>";
                                echo "<span>$table</span>";
                                
                                // عدد السجلات في كل جدول
                                $count_result = $main_db->query("SELECT COUNT(*) as count FROM `$table`");
                                if ($count_result) {
                                    $count = $count_result->fetch_assoc()['count'];
                                    echo "<span class='badge bg-primary rounded-pill'>$count سجل</span>";
                                }
                                echo "</li>";
                            }
                            echo "</ul>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال: " . ($main_db->connect_error ?? 'غير معروف') . "</p>";
                    }
                    echo "</div>";
                    ?>
                </div>

                <!-- فحص قاعدة بيانات العمليات -->
                <div class="update-section">
                    <h3><i class="fas fa-search"></i> فحص قاعدة بيانات العمليات</h3>

                    <?php
                    echo "<div class='step'>";
                    echo "<h5>2. فحص قاعدة بيانات العمليات:</h5>";

                    // التحقق من وجود قاعدة بيانات العمليات
                    $operations_db = getOperationsDB();
                    if ($operations_db && !$operations_db->connect_error) {
                        echo "<p class='success'><i class='fas fa-check'></i> قاعدة بيانات العمليات موجودة ومتصلة - " . OPERATIONS_DB_NAME . "</p>";

                        // فحص الجداول الموجودة
                        $tables_result = $operations_db->query("SHOW TABLES");
                        if ($tables_result && $tables_result->num_rows > 0) {
                            echo "<p class='info'><i class='fas fa-table'></i> عدد الجداول الموجودة: " . $tables_result->num_rows . "</p>";
                        } else {
                            echo "<p class='info'><i class='fas fa-info-circle'></i> قاعدة البيانات فارغة - جاهزة لإنشاء الجداول</p>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> قاعدة بيانات العمليات غير موجودة أو لا يمكن الاتصال بها</p>";
                        echo "<div class='alert alert-warning mt-3'>";
                        echo "<h6><i class='fas fa-exclamation-triangle'></i> يجب إنشاء قاعدة البيانات يدوياً:</h6>";
                        echo "<p><strong>اسم قاعدة البيانات:</strong> " . OPERATIONS_DB_NAME . "</p>";
                        echo "<p><strong>أمر SQL:</strong></p>";
                        echo "<code>CREATE DATABASE IF NOT EXISTS `" . OPERATIONS_DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;</code>";
                        echo "</div>";
                    }
                    echo "</div>";
                    ?>
                </div>

                <!-- ترحيل البيانات الموجودة -->
                <div class="update-section">
                    <h3><i class="fas fa-exchange-alt"></i> ترحيل البيانات الموجودة</h3>
                    
                    <?php
                    echo "<div class='step'>";
                    echo "<h5>3. ترحيل بيانات المستخدمين الموجودين:</h5>";
                    
                    // فحص المستخدمين الموجودين
                    $users_result = $main_db->query("SELECT id, username FROM users ORDER BY id");
                    if ($users_result && $users_result->num_rows > 0) {
                        echo "<p class='info'><i class='fas fa-users'></i> المستخدمون الموجودون:</p>";
                        echo "<div class='row'>";
                        
                        while ($user = $users_result->fetch_assoc()) {
                            $user_id = $user['id'];
                            $username = $user['username'];
                            
                            echo "<div class='col-md-6 mb-3'>";
                            echo "<div class='card'>";
                            echo "<div class='card-body'>";
                            echo "<h6 class='card-title'>$username (ID: $user_id)</h6>";
                            
                            // محاولة إنشاء جداول المستخدم
                            $create_result = createUserTables($username);
                            if ($create_result) {
                                echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء الجداول</p>";
                                
                                // عرض الجداول المنشأة
                                $user_tables = getUserTables($username);
                                echo "<small class='text-muted'>الجداول: " . implode(', ', $user_tables) . "</small>";
                            } else {
                                echo "<p class='error'><i class='fas fa-times'></i> فشل في إنشاء الجداول</p>";
                            }
                            
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                        
                        echo "</div>";
                    } else {
                        echo "<p class='warning'><i class='fas fa-exclamation-triangle'></i> لا توجد مستخدمون في النظام</p>";
                    }
                    echo "</div>";
                    ?>
                </div>

                <!-- اختبار النظام المحدث -->
                <div class="update-section">
                    <h3><i class="fas fa-test-tube"></i> اختبار النظام المحدث</h3>
                    
                    <?php
                    echo "<div class='step'>";
                    echo "<h5>4. اختبار دوال النظام الجديد:</h5>";
                    
                    // اختبار دوال البادئة
                    $test_username = 'test_user';
                    $prefix = getUserTablePrefix($test_username);
                    $table_name = getUserTableName('customers', $test_username);
                    
                    echo "<p class='info'><i class='fas fa-tag'></i> اختبار البادئة للمستخدم '$test_username':</p>";
                    echo "<ul>";
                    echo "<li>البادئة: <code>$prefix</code></li>";
                    echo "<li>اسم جدول العملاء: <code>$table_name</code></li>";
                    echo "</ul>";
                    
                    // اختبار تحديث الاستعلامات
                    $test_query = "SELECT * FROM customers WHERE name LIKE '%أحمد%'";
                    $updated_query = updateQueryWithUserPrefix($test_query, $test_username);
                    
                    echo "<p class='info'><i class='fas fa-code'></i> اختبار تحديث الاستعلامات:</p>";
                    echo "<div class='bg-light p-2 mb-2'>";
                    echo "<strong>الأصلي:</strong> <code>$test_query</code>";
                    echo "</div>";
                    echo "<div class='bg-success text-white p-2'>";
                    echo "<strong>المحدث:</strong> <code>$updated_query</code>";
                    echo "</div>";
                    
                    echo "</div>";
                    ?>
                </div>

                <!-- النتائج والخطوات التالية -->
                <div class="update-section">
                    <h3><i class="fas fa-flag-checkered"></i> النتائج والخطوات التالية</h3>
                    
                    <div class="alert alert-success">
                        <h5><i class="fas fa-check-circle"></i> تم التحديث بنجاح!</h5>
                        <p>تم تحديث النظام لاستخدام:</p>
                        <ul>
                            <li><strong>قاعدة البيانات الرئيسية:</strong> u193708811_system_main (موجودة)</li>
                            <li><strong>قاعدة بيانات العمليات:</strong> u193708811_system_operations (جديدة)</li>
                            <li><strong>نظام البادئات:</strong> جداول بادئة باسم المستخدم</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> الخطوات التالية:</h5>
                        <ol>
                            <li>اختبر تسجيل مستخدم جديد</li>
                            <li>تحقق من إنشاء الجداول تلقائياً</li>
                            <li>اختبر العمليات الأساسية (إضافة عملاء، منتجات، فواتير)</li>
                            <li>تأكد من عمل النظام بشكل صحيح</li>
                        </ol>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="update-section text-center">
                    <h3><i class="fas fa-tools"></i> أدوات التحكم</h3>
                    
                    <div class="btn-group" role="group">
                        <a href="test_system.php" class="btn btn-primary">
                            <i class="fas fa-test-tube"></i> اختبار النظام
                        </a>
                        <a href="index.php" class="btn btn-success">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="register.php" class="btn btn-info">
                            <i class="fas fa-user-plus"></i> تسجيل مستخدم جديد
                        </a>
                        <button onclick="location.reload()" class="btn btn-warning">
                            <i class="fas fa-refresh"></i> إعادة تحميل التحديث
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
