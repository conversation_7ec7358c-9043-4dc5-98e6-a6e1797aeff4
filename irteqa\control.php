<!DOCTYPE html>
<?php include ('conn2.php');?>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <title>لوحة المعلومات</title>
    <style>
      * {
    box-sizing: border-box;
    margin: 0;
    font-family: sans-serif;
}
      body {
  display:block;
  width:100%;
    text-align: center;
    font-weight: bold;
    background: #f5f7fb;
}
    div.aa{
    direction: rtl;
    padding: 3px;
    height: 330px;
    margin: auto;
    display: inline-table;
    border-radius:3px;
    width: 100%;
    padding: 0px 40px 0px 10px;
  }
  .card{
    border: 1px dashed #5d5d5d66;
    background-color: #fff;
    width: 98%;
    /* box-shadow: 3px 5px 3px 0 rgb(85 85 85 / 20%), 0 6px 20px 0 rgb(125 125 125 / 19%); */
    border-radius: 3px;
    padding: 3px;
    height: 120px;
    /* margin: auto; */
    transition: 00.4s;
    text-align: right;
    margin: 10px 5px 5px 5px;
    height: 150px;
    box-shadow:0 1px 10px rgb(169 169 169 / 33%);


}
.search{
  display: flex;
    direction: rtl;
    float: left;
}
.card h1{
  margin:4px;
  font-size: 30px;
    font-weight: bold;
    color: #737373;

}
.card .h1h1{
   font-size: 60px ;
}
h2{
  margin-left:25px;
  color: #737373;
}
h4{
    font-size: 20px;
    text-align: center;
}
.csearch{
  width: 200px;
  margin: 5px;
    height: 35px;
    border-radius: 10px;
    
}
.cntrlg{
  float:right;
  margin:2px;
  width: 24%;
}
h3{font-size: 25px;
    font-weight: bolder;
    color: #1a73e8;
    margin-right: 10px;
    margin: 10px;
}
label{
  float: right;
  margin: 10px;
}
.print{
  float: left;
    margin: 10px;

}
.hnum{
  text-align:center;font-size: 60px;color: #04AA6D;font-weight: bold;
}
.numdv{
  display: inline-flex;
}
.card:hover{
  background: #0c0c0c12;
}
.changed{
  width: 100%;
    height: 70px;
    background: #f5f7fb;
    display: inline-flex;
}
.cselect{
  width: 169px;
    float: right;
    margin-right: 10px;
    height: 35px;
    border-radius: 10px;
    margin: 5px;
    border: 1px solid #dbdbdb;
}
.dsearch{
  float: left;
}
.ones{
  margin-top: 0;
}
.ctools{
  background: whitesmoke;
    width: 98%;
    height: 50px;
    margin: auto;
    border-radius: 10px;
    box-shadow:0 1px 10px rgb(169 169 169 / 33%);
    display: inline-flex;
    border: 1px solid #cdcdcd;
}
input[type="text"],input[type="search"]{
  box-shadow:inset 0 1px 3px 2px rgb(169 169 169 / 33%);
  border: 1px solid #dbdbdb;
  border-width: 0;
}

@media screen and (max-width: 39.9375em) {
body{
	display: inline-table;
	zoom:0.50;
}
.card{
    float: inherit;
}
.card h1 {
    margin:2px;
    font-size: 30px;
    font-weight: bold;
}
.card .h1h1{
    font-size: 50px;
    
}
}
@media screen and (max-width:420px) {
    body{
    	display: inline-table;
	zoom:0.40;}
.card h1 {
    margin:2px;
    font-size: 15px;
    font-weight: bold;
}
.card .h1h1{
    font-size: 30px;
    
}
}
@media print{
  header{
    display: none;
  }
}
    </style>
</head>

<body>
<?php include('head.php');?><br>

 <div class="changed">
        <div class="ctools">
          <div class="dselect">
            <label for="" style="float: right;">الشهر</label>
          <select class="cselect" aria-label="Default select example" style="text-align: center;font-size:20px;" name="who">
            <option value="">الشهر الحالي</option>
            <option value="وسيط">أغسطس</option>
            <option value="عميل">يوليو</option>
          </div>
          <div class="dsearch">
          <input class="csearch" type="search" name="csearch" id="">
          <label for="">بحث</label>
        <a class="print" href=""style="float:left;">طباعة</a>
        </div>
        </div>
      </div>
    <section class="ones">
     
        <div class="aa" >

                <div class="cntrlg">
            <div class="card">
              <h1>العملاء</h1><h1 class="h1h1" style="text-align:center;color: #04AA6D;font-weight: bold;">
  <?php
  
  // u364709247_shoping 5dN53znd 127.0.0.1
include('conn2.php');
            $sql = $database->prepare("SELECT * FROM orderss where who='عميل'");
            $sql->execute();
            echo $sql->rowcount();
            ?></h1>
            </div>
            <div class="card"><h1>إجمالي مبلغ الخدمات</h1><h1 class="h1h1" style="text-align:center;color: #04AA6D;font-weight: bold;">

   <?php
include('conn2.php');
$conn = mysqli_connect('localhost','u364709247_shoping','5dN53znd','u364709247_shoping');
 $sql = $database->prepare("SELECT * FROM orderss");
   //sql sum all culm itm query
   $sql = "SELECT  SUM(sell) from orderss where who='عميل'";
   $result = $conn->query($sql);
   //display data on web page
   while($row = mysqli_fetch_array($result)){
       echo $row['SUM(sell)'];
       
      }
            ?><h2 style="text-align:left;
            margin-top: -42px;">ريال</h2></h1>
            </div>
            </div>
            <div class="cntrlg">
            <div class="card" style="height: 305px;"><h1>جاري الإنجاز</h1><h3>العملاء:</h3><div class="numdv"><h1 class="hnum" style="margin-right:20px">
  <?php
       include('conn2.php');
            $sql = $database->prepare("SELECT * FROM orderss where who='عميل'AND injaz='جاري الإنجاز'");
            $sql->execute();
            echo $sql->rowcount();
            ?></h1><h1 style="margin-right:100px" class="hnum">
<?php
include('conn2.php');

$sql = $database->prepare("SELECT * FROM orderss");
//sql sum all culm itm query
$sql = "SELECT  SUM(sell) from orderss where who='عميل' AND injaz='جاري الإنجاز'";
$result = $conn->query($sql);
//display data on web page
while($row = mysqli_fetch_array($result)){
    echo $row['SUM(sell)'];
   }
         ?><h4>ريال</h4></div><h3>الوسطاء:</h3><div class="numdv"><h1 class="hnum" style="margin-right:20px;    color: firebrick;">
         <?php
             include('conn2.php');
                   $sql = $database->prepare("SELECT * FROM orderss where ((who='وسيط' or who='منفذ') and injaz='جاري الإنجاز')");
                   $sql->execute();
                   echo $sql->rowcount();
                   ?></h1><h1 style="margin-right:100px;    color: firebrick;" class="hnum">
       <?php
       include('conn2.php');
       
       $sql = $database->prepare("SELECT * FROM orderss");
       //sql sum all culm itm query
       $sql = "SELECT  SUM(sell) from orderss where ((who='وسيط' or who='منفذ') and injaz='جاري الإنجاز')";
       $result = $conn->query($sql);
       //display data on web page
       while($row = mysqli_fetch_array($result)){
           echo $row['SUM(sell)'];
          }
                ?><h4>ريال</h4></div></div>
    </div>
    <div class="cntrlg">
            <div class="card" style="height: 305px;"><h1>تم اللإنجاز</h1><h3>العملاء:</h3><div class="numdv"><h1 class="hnum" style="margin-right:20px">
  <?php
       include('conn2.php');
            $sql = $database->prepare("SELECT * FROM orderss where ((who='عميل') and injaz='تم الإنجاز')");
            $sql->execute();
            echo $sql->rowcount();
            ?></h1><h1 style="margin-right:100px" class="hnum">
<?php
include('conn2.php');

$sql = $database->prepare("SELECT * FROM orderss");
//sql sum all culm itm query
$sql = "SELECT  SUM(sell) from orderss where who='عميل' and injaz='تم الإنجاز'";
$result = $conn->query($sql);
//display data on web page
while($row = mysqli_fetch_array($result)){
    echo $row['SUM(sell)'];
   }
         ?><h4>ريال</h4></div><h3>الوسطاء:</h3><div class="numdv"><h1 class="hnum" style="margin-right:20px;    color: firebrick;">
         <?php
              include('conn2.php');
                   $sql = $database->prepare("SELECT * FROM orderss where ((who='وسيط'or who='منفذ') AND injaz='تم الإنجاز')");
                   $sql->execute();
                   echo $sql->rowcount();
                   ?></h1><h1 style="margin-right:100px;    color: firebrick;" class="hnum">
       <?php
       include('conn2.php');
       
       $sql = $database->prepare("SELECT * FROM orderss");
       //sql sum all culm itm query
       $sql = "SELECT  SUM(sell) from orderss where ((who='وسيط'or who='منفذ') and injaz='تم الإنجاز')";
       $result = $conn->query($sql);
       //display data on web page
       while($row = mysqli_fetch_array($result)){
           echo $row['SUM(sell)'];
          }
                ?><h4>ريال</h4></div></div>
    </div>
            <div class="cntrlg">
            <div class="card"><h1>الوسطاء</h1><h1 class="h1h1" style="text-align:center; color: firebrick;font-weight: bold;">
  <?php
include('conn2.php');
            $sql = $database->prepare("SELECT * FROM orderss ً WHERE who='وسيط' or who='منفذ'");
            $sql->execute();
            echo $sql->rowcount();
            ?></h1>
            </div><div class="card" ><h1>إجمالي مبالغ الوسطاء</h1><h1 class="h1h1" style="text-align:center;color: firebrick;font-weight: bold;">

<?php
include('conn2.php');
$sql = $database->prepare("SELECT * FROM orderss");
   //sql sum all culm itm query
   $sql = "SELECT  SUM(sell) from orderss where who='وسيط' or who='منفذ'";
   $result = $conn->query($sql);
   //display data on web page
   while($row = mysqli_fetch_array($result)){
       echo $row['SUM(sell)'];
       
      }
         ?><h2 style="text-align:left;
         margin-top: -42px;">ريال</h2>
         <?php ?></h1>
            </div>
          </div>

        
        </div>
    </section>
</body>
</html>