# نظام المبيعات - النسخة الثانية (salessystem_v2)

## نظرة عامة
هذه هي النسخة المحسنة من نظام المبيعات مع تحسينات جوهرية في هيكل قاعدة البيانات وإدارة المستخدمين.

## التحسينات الرئيسية

### 1. نظام قواعد البيانات المحسن
- **قاعدتا بيانات منفصلتان:**
  - `u193708811_system_main`: للمستخدمين والمديرين (المستخدم: sales01)
  - `u193708811_operations`: للعمليات والجداول المشتركة (المستخدم: sales02)

### 2. نظام البادئات للجداول
- **بدلاً من قاعدة بيانات منفصلة لكل مستخدم:**
  - جداول بادئة باسم المستخدم في قاعدة بيانات واحدة
  - مثال: `ahmed_customers`, `sara_products`, `mohammed_sales`

### 3. الدوال الجديدة

#### دوال إدارة البادئات:
```php
getUserTablePrefix($username)     // الحصول على بادئة المستخدم
getUserTableName($table, $username) // الحصول على اسم الجدول مع البادئة
updateQueryWithUserPrefix($query, $username) // تحديث الاستعلام ليستخدم البادئة
```

#### دوال إدارة الجداول:
```php
createUserTables($username)       // إنشاء جداول المستخدم
userTableExists($table, $username) // فحص وجود جدول المستخدم
getUserTableCount($table, $username) // عدد السجلات في جدول المستخدم
getUserTables($username)          // قائمة جداول المستخدم
```

#### دوال الاستعلامات:
```php
executeUserQuery($db, $query, $username) // تنفيذ استعلام مع البادئة
prepareUserQuery($db, $query, $username) // تحضير استعلام مع البادئة
```

## هيكل قاعدة البيانات

### قاعدة البيانات الرئيسية (u193708811_system_main)
- `users` - بيانات المستخدمين
- `admins` - بيانات المديرين
- `activity_log` - سجل العمليات
- **المستخدم:** sales01
- **كلمة المرور:** dNz35nd5@

### قاعدة بيانات العمليات (u193708811_operations)
- `{username}_customers` - عملاء المستخدم
- `{username}_products` - منتجات المستخدم
- `{username}_sales` - مبيعات المستخدم
- `{username}_purchases` - مشتريات المستخدم
- `{username}_sale_items` - عناصر المبيعات
- `{username}_purchase_items` - عناصر المشتريات
- **المستخدم:** sales02
- **كلمة المرور:** dNz35nd5@

## مثال على الاستخدام

### تسجيل مستخدم جديد:
```php
// عند تسجيل مستخدم باسم "ahmed"
// يتم إنشاء الجداول التالية تلقائياً:
- ahmed_customers
- ahmed_products
- ahmed_sales
- ahmed_purchases
- ahmed_sale_items
- ahmed_purchase_items
```

### استعلام البيانات:
```php
// الاستعلام الأصلي:
$query = "SELECT * FROM customers WHERE name LIKE '%أحمد%'";

// يتم تحويله تلقائياً إلى:
$query = "SELECT * FROM ahmed_customers WHERE name LIKE '%أحمد%'";
```

## الملفات المحدثة

### ملفات التكوين:
- `config/db_config.php` - تكوين قواعد البيانات المحسن
- `config/init.php` - تهيئة النظام المحسن

### ملفات جديدة:
- `includes/database_helper.php` - دوال مساعدة لإدارة قاعدة البيانات
- `test_system.php` - صفحة اختبار النظام الجديد

### ملفات محدثة:
- `includes/auth.php` - تحديث عملية التسجيل والدخول

## المزايا الجديدة

### 1. الأداء المحسن:
- قاعدة بيانات واحدة بدلاً من عدة قواعد بيانات
- فهرسة أفضل وإدارة أكثر كفاءة

### 2. سهولة الإدارة:
- جميع البيانات في مكان واحد
- نسخ احتياطي أسهل
- صيانة مبسطة

### 3. المرونة:
- إمكانية ربط البيانات بين المستخدمين
- تقارير شاملة عبر النظام
- إدارة مركزية للبيانات

### 4. الأمان المحسن:
- فصل بيانات المستخدمين والمديرين
- تحكم أفضل في الصلاحيات
- تتبع شامل للعمليات

## كيفية الاستخدام

### 1. الوصول للنظام:
```
http://localhost:808/salessystem_v2/
```

### 2. اختبار النظام:
```
http://localhost:808/salessystem_v2/test_system.php
```

### 3. تسجيل مستخدم جديد:
```
http://localhost:808/salessystem_v2/register.php
```

## التوافق مع النظام القديم

### الدوال المحافظة:
- `getUserDBConnection()` - تعيد اتصال قاعدة بيانات العمليات
- `getCurrentUserDB()` - تعيد اتصال قاعدة بيانات العمليات
- `createUserDatabase()` - تستدعي `createUserTables()`

### الترقية التلقائية:
- النظام يتعرف على المستخدمين الموجودين
- إنشاء جداول تلقائي عند أول دخول
- لا حاجة لتعديل الكود الموجود

## الاختبار والتطوير

### اختبار النظام:
1. افتح `test_system.php`
2. تحقق من اتصال قواعد البيانات
3. اختبر إنشاء الجداول
4. تحقق من دوال البادئة

### إضافة مستخدم تجريبي:
1. اذهب إلى `register.php`
2. سجل مستخدم جديد
3. تحقق من إنشاء الجداول تلقائياً
4. اختبر العمليات الأساسية

## الدعم والصيانة

### تنظيف النظام:
```php
// حذف جداول مستخدم معين (للاختبار)
dropUserTables('username');
```

### فحص الجداول:
```php
// الحصول على قائمة جداول المستخدم
$tables = getUserTables('username');
```

### إحصائيات:
```php
// عدد العملاء للمستخدم
$count = getUserTableCount('customers', 'username');
```

## ملاحظات مهمة

1. **النسخ الاحتياطي:** تأكد من عمل نسخ احتياطي منتظم لقاعدتي البيانات
2. **الأمان:** استخدم كلمات مرور قوية للمديرين
3. **الأداء:** راقب أداء النظام مع زيادة عدد المستخدمين
4. **التحديثات:** اختبر أي تحديثات على نسخة تجريبية أولاً

## الدعم الفني

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى:
1. فحص ملفات السجل (logs)
2. استخدام صفحة الاختبار للتشخيص
3. التحقق من إعدادات قاعدة البيانات

---

**تاريخ الإنشاء:** 2024-12-19  
**الإصدار:** 2.0  
**المطور:** نظام المبيعات المحسن
