<?php
/**
 * ملف إصلاح شامل لجميع مشاكل قاعدة البيانات
 * يقوم بتطبيق جميع الإصلاحات المطلوبة على الملفات الأخرى
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
redirectIfNotLoggedIn();

// قائمة الملفات التي تحتاج إصلاح
$files_to_fix = [
    'add_sale.php',
    'add_purchase.php',
    'sales.php',
    'purchases.php',
    'customers.php',
    'reports.php',
    'edit_sale.php',
    'edit_purchase.php',
    'view_sale.php',
    'view_purchase.php'
];

$fixes_applied = [];
$errors = [];

// دالة لإصلاح ملف واحد
function fixDatabaseConnectionInFile($file_path) {
    global $fixes_applied, $errors;
    
    if (!file_exists($file_path)) {
        $errors[] = "الملف غير موجود: $file_path";
        return false;
    }
    
    $content = file_get_contents($file_path);
    $original_content = $content;
    $fixes_count = 0;
    
    // 1. إصلاح استدعاء getCurrentUserDB
    $old_pattern = '/\$db\s*=\s*getCurrentUserDB\(\);\s*if\s*\(\s*\$db\s*===\s*null\s*\|\|\s*\$db->connect_error\s*\)\s*\{[^}]*\}/';
    $new_replacement = '$db_validation = validateDatabaseConnection();
if (!$db_validation[\'status\']) {
    $_SESSION[\'error\'] = $db_validation[\'error\'];
    header("Location: index.php");
    exit();
}
$db = getCurrentUserDB();';
    
    if (preg_match($old_pattern, $content)) {
        $content = preg_replace($old_pattern, $new_replacement, $content);
        $fixes_count++;
    }
    
    // 2. إضافة فحص الجداول المفقودة
    $table_check_pattern = '/\$check_purchases_table\s*=\s*\$db->query\("SHOW TABLES LIKE \'purchases\'"\);/';
    if (preg_match($table_check_pattern, $content)) {
        $table_fix = '$check_purchases_table = $db->query("SHOW TABLES LIKE \'purchases\'");
if (!$check_purchases_table || $check_purchases_table->num_rows == 0) {
    $_SESSION[\'error\'] = \'جدول المشتريات مفقود. <a href="database_repair_tool.php">انقر هنا لإصلاح قاعدة البيانات</a>\';
    header("Location: index.php");
    exit();
}';
        $content = preg_replace($table_check_pattern, $table_fix, $content);
        $fixes_count++;
    }
    
    // 3. تحسين معالجة الأخطاء في الاستعلامات
    $query_pattern = '/if\s*\(\s*!\$([a-zA-Z_]+)\s*\)\s*\{[^}]*error_log[^}]*\}/';
    $query_replacement = 'if (!$1) {
    error_log("خطأ في الاستعلام: " . $db->error);
    $_SESSION[\'error\'] = \'خطأ في قاعدة البيانات. يرجى المحاولة مرة أخرى.\';
    header("Location: index.php");
    exit();
}';
    
    if (preg_match($query_pattern, $content)) {
        $content = preg_replace($query_pattern, $query_replacement, $content);
        $fixes_count++;
    }
    
    // 4. إضافة resetDBConnection بعد كل استعلام
    $stmt_close_pattern = '/\$([a-zA-Z_]+)->close\(\);/';
    $stmt_close_replacement = '$1->close();
$db = resetDBConnection($db);';
    
    $content = preg_replace($stmt_close_pattern, $stmt_close_replacement, $content);
    if ($content !== $original_content) {
        $fixes_count++;
    }
    
    // 5. إضافة try-catch للاستعلامات الحرجة
    $critical_queries = ['INSERT', 'UPDATE', 'DELETE'];
    foreach ($critical_queries as $query_type) {
        $pattern = '/(\$db->query\("' . $query_type . '[^"]*"\))/';
        $replacement = 'try {
    $1
} catch (Exception $e) {
    error_log("خطأ في ' . $query_type . ': " . $e->getMessage());
    $_SESSION[\'error\'] = \'خطأ في قاعدة البيانات: \' . $e->getMessage();
}';
        
        if (preg_match($pattern, $content)) {
            $content = preg_replace($pattern, $replacement, $content);
            $fixes_count++;
        }
    }
    
    // حفظ الملف إذا تم تطبيق إصلاحات
    if ($fixes_count > 0 && $content !== $original_content) {
        if (file_put_contents($file_path, $content)) {
            $fixes_applied[] = "تم إصلاح $file_path - $fixes_count إصلاح";
            return true;
        } else {
            $errors[] = "فشل في حفظ $file_path";
            return false;
        }
    }
    
    return true;
}

// تطبيق الإصلاحات على جميع الملفات
foreach ($files_to_fix as $file) {
    $file_path = __DIR__ . '/' . $file;
    fixDatabaseConnectionInFile($file_path);
}

// إنشاء ملف إعدادات محسن لقاعدة البيانات
$enhanced_db_config = '<?php
/**
 * إعدادات قاعدة البيانات المحسنة مع معالجة الأخطاء
 */

// إعدادات الاتصال
define(\'DB_RETRY_ATTEMPTS\', 3);
define(\'DB_RETRY_DELAY\', 1); // ثانية
define(\'DB_TIMEOUT\', 30);

// دالة محسنة للاتصال مع إعادة المحاولة
function createDatabaseConnection($host, $user, $pass, $db_name) {
    $attempts = 0;
    $connection = null;
    
    while ($attempts < DB_RETRY_ATTEMPTS) {
        try {
            $connection = new mysqli($host, $user, $pass, $db_name);
            
            if (!$connection->connect_error) {
                // ضبط الإعدادات
                $connection->set_charset("utf8mb4");
                $connection->options(MYSQLI_OPT_CONNECT_TIMEOUT, DB_TIMEOUT);
                $connection->options(MYSQLI_OPT_READ_TIMEOUT, DB_TIMEOUT);
                
                return $connection;
            }
        } catch (Exception $e) {
            error_log("محاولة اتصال فاشلة #" . ($attempts + 1) . ": " . $e->getMessage());
        }
        
        $attempts++;
        if ($attempts < DB_RETRY_ATTEMPTS) {
            sleep(DB_RETRY_DELAY);
        }
    }
    
    return null;
}

// دالة للتحقق من صحة الاستعلام
function executeQuery($db, $query, $params = []) {
    try {
        if (empty($params)) {
            $result = $db->query($query);
        } else {
            $stmt = $db->prepare($query);
            if ($stmt) {
                $stmt->bind_param(...$params);
                $stmt->execute();
                $result = $stmt->get_result();
                $stmt->close();
            } else {
                throw new Exception("فشل في تحضير الاستعلام: " . $db->error);
            }
        }
        
        if (!$result) {
            throw new Exception("فشل في تنفيذ الاستعلام: " . $db->error);
        }
        
        return $result;
    } catch (Exception $e) {
        error_log("خطأ في الاستعلام: " . $e->getMessage());
        throw $e;
    }
}
?>';

file_put_contents(__DIR__ . '/config/enhanced_db.php', $enhanced_db_config);
$fixes_applied[] = "تم إنشاء ملف الإعدادات المحسن";

require_once __DIR__ . '/includes/header.php';
?>

<div class="container mt-4">
    <div class="card">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                <i class="fas fa-wrench"></i>
                إصلاح مشاكل قاعدة البيانات
            </h4>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h2><?php echo count($fixes_applied); ?></h2>
                            <p class="mb-0">إصلاحات تم تطبيقها</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h2><?php echo count($errors); ?></h2>
                            <p class="mb-0">أخطاء</p>
                        </div>
                    </div>
                </div>
            </div>

            <?php if (!empty($fixes_applied)): ?>
            <div class="mt-4">
                <h5>الإصلاحات المطبقة:</h5>
                <ul class="list-group">
                    <?php foreach ($fixes_applied as $fix): ?>
                    <li class="list-group-item list-group-item-success">
                        <i class="fas fa-check"></i> <?php echo $fix; ?>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <?php if (!empty($errors)): ?>
            <div class="mt-4">
                <h5>الأخطاء:</h5>
                <ul class="list-group">
                    <?php foreach ($errors as $error): ?>
                    <li class="list-group-item list-group-item-danger">
                        <i class="fas fa-times"></i> <?php echo $error; ?>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <div class="mt-4 text-center">
                <a href="database_repair_tool.php" class="btn btn-primary">
                    <i class="fas fa-tools"></i>
                    تشغيل أداة الإصلاح الشاملة
                </a>
                <a href="index.php" class="btn btn-success ms-2">
                    <i class="fas fa-home"></i>
                    العودة للصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
