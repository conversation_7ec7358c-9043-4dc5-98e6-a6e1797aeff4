{"version": 3, "file": "lang/summernote-gl-ES.js", "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD;;ACVA,CAAC,UAASA,CAAT,EAAY;AACXA,EAAAA,CAAC,CAACC,MAAF,CAASD,CAAC,CAACE,UAAF,CAAaC,IAAtB,EAA4B;AAC1B,aAAS;AACPC,MAAAA,IAAI,EAAE;AACJC,QAAAA,IAAI,EAAE,SADF;AAEJC,QAAAA,MAAM,EAAE,SAFJ;AAGJC,QAAAA,SAAS,EAAE,WAHP;AAIJC,QAAAA,KAAK,EAAE,wBAJH;AAKJC,QAAAA,MAAM,EAAE,gBALJ;AAMJC,QAAAA,IAAI,EAAE,OANF;AAOJC,QAAAA,aAAa,EAAE,SAPX;AAQJC,QAAAA,WAAW,EAAE,aART;AASJC,QAAAA,SAAS,EAAE,WATP;AAUJC,QAAAA,IAAI,EAAE;AAVF,OADC;AAaPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,MAAM,EAAE,eAFH;AAGLC,QAAAA,UAAU,EAAE,iCAHP;AAILC,QAAAA,UAAU,EAAE,wBAJP;AAKLC,QAAAA,aAAa,EAAE,2BALV;AAMLC,QAAAA,SAAS,EAAE,mBANN;AAOLC,QAAAA,UAAU,EAAE,kBAPP;AAQLC,QAAAA,SAAS,EAAE,YARN;AASLC,QAAAA,YAAY,EAAE,mBATT;AAULC,QAAAA,WAAW,EAAE,gBAVR;AAWLC,QAAAA,cAAc,EAAE,cAXX;AAYLC,QAAAA,SAAS,EAAE,iBAZN;AAaLC,QAAAA,aAAa,EAAE,oCAbV;AAcLC,QAAAA,SAAS,EAAE,wBAdN;AAeLC,QAAAA,eAAe,EAAE,+BAfZ;AAgBLC,QAAAA,eAAe,EAAE,0BAhBZ;AAiBLC,QAAAA,oBAAoB,EAAE,wCAjBjB;AAkBLC,QAAAA,GAAG,EAAE,cAlBA;AAmBLC,QAAAA,MAAM,EAAE,gBAnBH;AAoBLC,QAAAA,QAAQ,EAAE;AApBL,OAbA;AAmCPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,SAAS,EAAE,kBAFN;AAGLpB,QAAAA,MAAM,EAAE,gBAHH;AAILgB,QAAAA,GAAG,EAAE,eAJA;AAKLK,QAAAA,SAAS,EAAE;AALN,OAnCA;AA0CPC,MAAAA,IAAI,EAAE;AACJA,QAAAA,IAAI,EAAE,SADF;AAEJtB,QAAAA,MAAM,EAAE,iBAFJ;AAGJuB,QAAAA,MAAM,EAAE,gBAHJ;AAIJC,QAAAA,IAAI,EAAE,QAJF;AAKJC,QAAAA,aAAa,EAAE,mBALX;AAMJT,QAAAA,GAAG,EAAE,gCAND;AAOJU,QAAAA,eAAe,EAAE;AAPb,OA1CC;AAmDPC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,OADF;AAELC,QAAAA,WAAW,EAAE,eAFR;AAGLC,QAAAA,WAAW,EAAE,eAHR;AAILC,QAAAA,UAAU,EAAE,iBAJP;AAKLC,QAAAA,WAAW,EAAE,kBALR;AAMLC,QAAAA,MAAM,EAAE,YANH;AAOLC,QAAAA,MAAM,EAAE,eAPH;AAQLC,QAAAA,QAAQ,EAAE;AARL,OAnDA;AA6DPC,MAAAA,EAAE,EAAE;AACFnC,QAAAA,MAAM,EAAE;AADN,OA7DG;AAgEPoC,MAAAA,KAAK,EAAE;AACLA,QAAAA,KAAK,EAAE,QADF;AAELC,QAAAA,CAAC,EAAE,QAFE;AAGLC,QAAAA,UAAU,EAAE,MAHP;AAILC,QAAAA,GAAG,EAAE,QAJA;AAKLC,QAAAA,EAAE,EAAE,UALC;AAMLC,QAAAA,EAAE,EAAE,UANC;AAOLC,QAAAA,EAAE,EAAE,UAPC;AAQLC,QAAAA,EAAE,EAAE,UARC;AASLC,QAAAA,EAAE,EAAE,UATC;AAULC,QAAAA,EAAE,EAAE;AAVC,OAhEA;AA4EPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,SAAS,EAAE,mBADN;AAELC,QAAAA,OAAO,EAAE;AAFJ,OA5EA;AAgFPC,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,OADC;AAEPC,QAAAA,UAAU,EAAE,mBAFL;AAGPC,QAAAA,QAAQ,EAAE;AAHH,OAhFF;AAqFPC,MAAAA,SAAS,EAAE;AACTA,QAAAA,SAAS,EAAE,WADF;AAETC,QAAAA,OAAO,EAAE,kBAFA;AAGTC,QAAAA,MAAM,EAAE,iBAHC;AAITC,QAAAA,IAAI,EAAE,mBAJG;AAKTC,QAAAA,MAAM,EAAE,kBALC;AAMTC,QAAAA,KAAK,EAAE,kBANE;AAOTC,QAAAA,OAAO,EAAE;AAPA,OArFJ;AA8FPC,MAAAA,KAAK,EAAE;AACLC,QAAAA,MAAM,EAAE,YADH;AAELC,QAAAA,IAAI,EAAE,YAFD;AAGLC,QAAAA,UAAU,EAAE,cAHP;AAILC,QAAAA,UAAU,EAAE,eAJP;AAKLC,QAAAA,WAAW,EAAE,cALR;AAMLC,QAAAA,cAAc,EAAE,yBANX;AAOLC,QAAAA,KAAK,EAAE,WAPF;AAQLC,QAAAA,cAAc,EAAE;AARX,OA9FA;AAwGPC,MAAAA,QAAQ,EAAE;AACRC,QAAAA,SAAS,EAAE,oBADH;AAERC,QAAAA,KAAK,EAAE,QAFC;AAGRC,QAAAA,cAAc,EAAE,kBAHR;AAIRC,QAAAA,MAAM,EAAE,QAJA;AAKRC,QAAAA,mBAAmB,EAAE,sBALb;AAMRC,QAAAA,aAAa,EAAE,qBANP;AAORC,QAAAA,SAAS,EAAE;AAPH,OAxGH;AAiHP1B,MAAAA,IAAI,EAAE;AACJ,2BAAmB,mBADf;AAEJ,gBAAQ,wBAFJ;AAGJ,gBAAQ,uBAHJ;AAIJ,eAAO,SAJH;AAKJ,iBAAS,qBALL;AAMJ,gBAAQ,2BANJ;AAOJ,kBAAU,2BAPN;AAQJ,qBAAa,6BART;AASJ,yBAAiB,2BATb;AAUJ,wBAAgB,eAVZ;AAWJ,uBAAe,mBAXX;AAYJ,yBAAiB,kBAZb;AAaJ,wBAAgB,kBAbZ;AAcJ,uBAAe,YAdX;AAeJ,+BAAuB,2BAfnB;AAgBJ,6BAAqB,wBAhBjB;AAiBJ,mBAAW,iCAjBP;AAkBJ,kBAAU,kCAlBN;AAmBJ,sBAAc,iDAnBV;AAoBJ,oBAAY,6BApBR;AAqBJ,oBAAY,6BArBR;AAsBJ,oBAAY,6BAtBR;AAuBJ,oBAAY,6BAvBR;AAwBJ,oBAAY,6BAxBR;AAyBJ,oBAAY,6BAzBR;AA0BJ,gCAAwB,yBA1BpB;AA2BJ,2BAAmB;AA3Bf,OAjHC;AA8IP2B,MAAAA,OAAO,EAAE;AACPC,QAAAA,IAAI,EAAE,UADC;AAEPC,QAAAA,IAAI,EAAE;AAFC,OA9IF;AAkJPC,MAAAA,WAAW,EAAE;AACXA,QAAAA,WAAW,EAAE,sBADF;AAEXC,QAAAA,MAAM,EAAE;AAFG;AAlJN;AADiB,GAA5B;AAyJD,CA1JD,EA0JGC,MA1JH", "sources": ["webpack:///webpack/universalModuleDefinition", "webpack:///./src/lang/summernote-gl-ES.js"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse {\n\t\tvar a = factory();\n\t\tfor(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];\n\t}\n})(self, function() {\nreturn ", "(function($) {\n  $.extend($.summernote.lang, {\n    'gl-ES': {\n      font: {\n        bold: 'Negrita',\n        italic: 'Curs<PERSON>',\n        underline: 'Subliñado',\n        clear: 'Quitar estilo de fonte',\n        height: 'Altura de liña',\n        name: '<PERSON><PERSON>',\n        strikethrough: 'Risca<PERSON>',\n        superscript: 'Superíndice',\n        subscript: 'Subíndice',\n        size: 'Tama<PERSON> da fonte',\n      },\n      image: {\n        image: 'Imaxe',\n        insert: 'Inserir imaxe',\n        resizeFull: 'Redimensionar a tamaño completo',\n        resizeHalf: 'Redimensionar á metade',\n        resizeQuarter: 'Redimensionar a un cuarto',\n        floatLeft: 'Flotar á esquerda',\n        floatRight: 'Flotar á dereita',\n        floatNone: 'Non flotar',\n        shapeRounded: 'Forma: Redondeado',\n        shapeCircle: 'Forma: Círculo',\n        shapeThumbnail: 'Forma: Marco',\n        shapeNone: 'Forma: Ningunha',\n        dragImageHere: 'Arrastrar unha imaxe ou texto aquí',\n        dropImage: 'Solta a imaxe ou texto',\n        selectFromFiles: 'Seleccionar desde os arquivos',\n        maximumFileSize: 'Tamaño máximo do arquivo',\n        maximumFileSizeError: 'Superaches o tamaño máximo do arquivo.',\n        url: 'URL da imaxe',\n        remove: 'Eliminar imaxe',\n        original: 'Original',\n      },\n      video: {\n        video: 'Vídeo',\n        videoLink: 'Ligazón do vídeo',\n        insert: 'Insertar vídeo',\n        url: 'URL do vídeo?',\n        providers: '(YouTube, Vimeo, Vine, Instagram, DailyMotion, o Youku)',\n      },\n      link: {\n        link: 'Ligazón',\n        insert: 'Inserir Ligazón',\n        unlink: 'Quitar Ligazón',\n        edit: 'Editar',\n        textToDisplay: 'Texto para amosar',\n        url: 'Cara a que URL leva a ligazón?',\n        openInNewWindow: 'Abrir nunha nova xanela',\n      },\n      table: {\n        table: 'Táboa',\n        addRowAbove: 'Add row above',\n        addRowBelow: 'Add row below',\n        addColLeft: 'Add column left',\n        addColRight: 'Add column right',\n        delRow: 'Delete row',\n        delCol: 'Delete column',\n        delTable: 'Delete table',\n      },\n      hr: {\n        insert: 'Inserir liña horizontal',\n      },\n      style: {\n        style: 'Estilo',\n        p: 'Normal',\n        blockquote: 'Cita',\n        pre: 'Código',\n        h1: 'Título 1',\n        h2: 'Título 2',\n        h3: 'Título 3',\n        h4: 'Título 4',\n        h5: 'Título 5',\n        h6: 'Título 6',\n      },\n      lists: {\n        unordered: 'Lista desordenada',\n        ordered: 'Lista ordenada',\n      },\n      options: {\n        help: 'Axuda',\n        fullscreen: 'Pantalla completa',\n        codeview: 'Ver código fonte',\n      },\n      paragraph: {\n        paragraph: 'Parágrafo',\n        outdent: 'Menos tabulación',\n        indent: 'Máis tabulación',\n        left: 'Aliñar á esquerda',\n        center: 'Aliñar ao centro',\n        right: 'Aliñar á dereita',\n        justify: 'Xustificar',\n      },\n      color: {\n        recent: 'Última cor',\n        more: 'Máis cores',\n        background: 'Cor de fondo',\n        foreground: 'Cor de fuente',\n        transparent: 'Transparente',\n        setTransparent: 'Establecer transparente',\n        reset: 'Restaurar',\n        resetToDefault: 'Restaurar por defecto',\n      },\n      shortcut: {\n        shortcuts: 'Atallos de teclado',\n        close: 'Pechar',\n        textFormatting: 'Formato de texto',\n        action: 'Acción',\n        paragraphFormatting: 'Formato de parágrafo',\n        documentStyle: 'Estilo de documento',\n        extraKeys: 'Teclas adicionais',\n      },\n      help: {\n        'insertParagraph': 'Inserir parágrafo',\n        'undo': 'Desfacer última acción',\n        'redo': 'Refacer última acción',\n        'tab': 'Tabular',\n        'untab': 'Eliminar tabulación',\n        'bold': 'Establecer estilo negrita',\n        'italic': 'Establecer estilo cursiva',\n        'underline': 'Establecer estilo subliñado',\n        'strikethrough': 'Establecer estilo riscado',\n        'removeFormat': 'Limpar estilo',\n        'justifyLeft': 'Aliñar á esquerda',\n        'justifyCenter': 'Aliñar ao centro',\n        'justifyRight': 'Aliñar á dereita',\n        'justifyFull': 'Xustificar',\n        'insertUnorderedList': 'Inserir lista desordenada',\n        'insertOrderedList': 'Inserir lista ordenada',\n        'outdent': 'Reducir tabulación do parágrafo',\n        'indent': 'Aumentar tabulación do parágrafo',\n        'formatPara': 'Mudar estilo do bloque a parágrafo (etiqueta P)',\n        'formatH1': 'Mudar estilo do bloque a H1',\n        'formatH2': 'Mudar estilo do bloque a H2',\n        'formatH3': 'Mudar estilo do bloque a H3',\n        'formatH4': 'Mudar estilo do bloque a H4',\n        'formatH5': 'Mudar estilo do bloque a H5',\n        'formatH6': 'Mudar estilo do bloque a H6',\n        'insertHorizontalRule': 'Inserir liña horizontal',\n        'linkDialog.show': 'Amosar panel ligazóns',\n      },\n      history: {\n        undo: 'Desfacer',\n        redo: 'Refacer',\n      },\n      specialChar: {\n        specialChar: 'CARACTERES ESPECIAIS',\n        select: 'Selecciona Caracteres especiais',\n      },\n    },\n  });\n})(jQuery);\n"], "names": ["$", "extend", "summernote", "lang", "font", "bold", "italic", "underline", "clear", "height", "name", "strikethrough", "superscript", "subscript", "size", "image", "insert", "resizeFull", "resizeHalf", "resizeQuarter", "floatLeft", "floatRight", "floatNone", "shapeRounded", "shapeCircle", "shapeThumbnail", "shapeNone", "dragImageHere", "dropImage", "selectFromFiles", "maximumFileSize", "maximumFileSizeError", "url", "remove", "original", "video", "videoLink", "providers", "link", "unlink", "edit", "textToDisplay", "openInNewWindow", "table", "addRowAbove", "addRowBelow", "addColLeft", "addColRight", "delRow", "delCol", "delTable", "hr", "style", "p", "blockquote", "pre", "h1", "h2", "h3", "h4", "h5", "h6", "lists", "unordered", "ordered", "options", "help", "fullscreen", "codeview", "paragraph", "outdent", "indent", "left", "center", "right", "justify", "color", "recent", "more", "background", "foreground", "transparent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset", "resetToDefault", "shortcut", "shortcuts", "close", "textFormatting", "action", "paragraphFormatting", "documentStyle", "extraKeys", "history", "undo", "redo", "specialChar", "select", "j<PERSON><PERSON><PERSON>"], "sourceRoot": ""}