<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $code = $_POST['code'];
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // يجب التحقق من تطابق كلمة المرور والتحقق من الرمز ووقت الانتهاء هنا

    if ($password === $confirm_password) {
        // يجب التحقق من صحة وقت الانتهاء هنا
        $currentTime = time();
        if ($expirationTime >= $currentTime) {
            // تحديث كلمة المرور في قاعدة البيانات هنا

            // يمكنك توجيه المستخدم إلى صفحة تأكيد إعادة تعيين كلمة المرور
        } else {
            echo 'انتهت صلاحية الرابط. يرجى طلب إعادة تعيين كلمة المرور مرة أخرى.';
        }
    } else {
        echo 'كلمة المرور وتأكيد كلمة المرور غير متطابقين.';
    }
} else {
    // إعادة التوجيه إلى صفحة آخرى إذا تم الوصول إلى هذه الصفحة بطريقة غير صحيحة
    header('Location: error_page.php');
    exit;
}
?>