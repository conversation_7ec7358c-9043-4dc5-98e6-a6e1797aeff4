-- إضافة عمود customer_id إلى جدول purchases
ALTER TABLE purchases ADD COLUMN customer_id INT NULL;

-- إنشاء مفتاح أجنبي يربط جدول purchases بجدول customers
ALTER TABLE purchases ADD CONSTRAINT fk_purchases_customer
FOREIGN KEY (customer_id) REFERENCES customers(id);

-- تحديث البيانات الموجودة (اختياري)
-- يمكنك تنفيذ هذا الاستعلام إذا كان لديك بيانات موجودة وتريد تحديثها
-- UPDATE purchases SET customer_id = (SELECT id FROM customers WHERE name = supplier_name LIMIT 1);

-- حذف عمود supplier_name (اختياري)
-- يمكنك تنفيذ هذا الاستعلام بعد التأكد من تحديث البيانات بشكل صحيح
-- ALTER TABLE purchases DROP COLUMN supplier_name;
