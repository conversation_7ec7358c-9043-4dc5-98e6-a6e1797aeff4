<?php
require_once 'config/init.php';

// صفحة اختبار النظام الجديد
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الجديد - salessystem_v2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-cogs"></i> اختبار النظام الجديد - salessystem_v2
                </h1>
                
                <!-- اختبار الاتصال بقواعد البيانات -->
                <div class="test-section">
                    <h3><i class="fas fa-database"></i> اختبار الاتصال بقواعد البيانات</h3>
                    
                    <?php
                    echo "<h5>1. قاعدة البيانات الرئيسية:</h5>";
                    if ($main_db && !$main_db->connect_error) {
                        echo "<p class='success'><i class='fas fa-check'></i> متصل بنجاح - " . MAIN_DB_NAME . "</p>";
                        
                        // فحص جدول المستخدمين
                        $users_check = $main_db->query("SHOW TABLES LIKE 'users'");
                        if ($users_check && $users_check->num_rows > 0) {
                            echo "<p class='success'><i class='fas fa-check'></i> جدول المستخدمين موجود</p>";
                            
                            // عدد المستخدمين
                            $users_count = $main_db->query("SELECT COUNT(*) as count FROM users");
                            if ($users_count) {
                                $count = $users_count->fetch_assoc()['count'];
                                echo "<p class='info'><i class='fas fa-users'></i> عدد المستخدمين: $count</p>";
                            }
                        } else {
                            echo "<p class='error'><i class='fas fa-times'></i> جدول المستخدمين غير موجود</p>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال: " . ($main_db->connect_error ?? 'غير معروف') . "</p>";
                    }
                    
                    echo "<h5>2. قاعدة بيانات العمليات:</h5>";
                    $operations_db = getOperationsDB();
                    if ($operations_db && !$operations_db->connect_error) {
                        echo "<p class='success'><i class='fas fa-check'></i> متصل بنجاح - " . OPERATIONS_DB_NAME . "</p>";
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل الاتصال: " . ($operations_db->connect_error ?? 'غير معروف') . "</p>";
                    }
                    ?>
                </div>

                <!-- اختبار دوال البادئة -->
                <div class="test-section">
                    <h3><i class="fas fa-tag"></i> اختبار دوال البادئة</h3>
                    
                    <?php
                    // اختبار مع أسماء مستخدمين مختلفة
                    $test_usernames = ['ahmed', 'sara', 'mohammed', 'fatima'];
                    
                    foreach ($test_usernames as $username) {
                        echo "<h5>اختبار المستخدم: $username</h5>";
                        
                        $prefix = getUserTablePrefix($username);
                        echo "<p class='info'><i class='fas fa-tag'></i> البادئة: <code>$prefix</code></p>";
                        
                        $table_name = getUserTableName('customers', $username);
                        echo "<p class='info'><i class='fas fa-table'></i> اسم جدول العملاء: <code>$table_name</code></p>";
                        
                        $table_name = getUserTableName('products', $username);
                        echo "<p class='info'><i class='fas fa-table'></i> اسم جدول المنتجات: <code>$table_name</code></p>";
                        
                        echo "<hr>";
                    }
                    ?>
                </div>

                <!-- اختبار إنشاء الجداول -->
                <div class="test-section">
                    <h3><i class="fas fa-plus-circle"></i> اختبار إنشاء الجداول</h3>
                    
                    <?php
                    $test_username = 'test_user';
                    echo "<h5>إنشاء جداول للمستخدم: $test_username</h5>";
                    
                    $result = createUserTables($test_username);
                    if ($result) {
                        echo "<p class='success'><i class='fas fa-check'></i> تم إنشاء الجداول بنجاح</p>";
                        
                        // فحص الجداول المنشأة
                        $tables = getUserTables($test_username);
                        echo "<p class='info'><i class='fas fa-list'></i> الجداول المنشأة:</p>";
                        echo "<ul>";
                        foreach ($tables as $table) {
                            echo "<li><code>$table</code></li>";
                        }
                        echo "</ul>";
                        
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> فشل في إنشاء الجداول</p>";
                    }
                    ?>
                </div>

                <!-- اختبار الاستعلامات -->
                <div class="test-section">
                    <h3><i class="fas fa-search"></i> اختبار الاستعلامات مع البادئة</h3>
                    
                    <?php
                    $test_username = 'test_user';
                    echo "<h5>اختبار تحديث الاستعلامات للمستخدم: $test_username</h5>";
                    
                    $original_queries = [
                        "SELECT * FROM customers WHERE name LIKE '%أحمد%'",
                        "INSERT INTO products (name, price) VALUES ('منتج تجريبي', 100.00)",
                        "UPDATE sales SET total_amount = 500.00 WHERE id = 1",
                        "DELETE FROM purchase_items WHERE purchase_id = 1"
                    ];
                    
                    foreach ($original_queries as $query) {
                        $updated_query = updateQueryWithUserPrefix($query, $test_username);
                        echo "<div class='mb-3'>";
                        echo "<p><strong>الاستعلام الأصلي:</strong></p>";
                        echo "<code class='d-block bg-light p-2'>$query</code>";
                        echo "<p><strong>الاستعلام المحدث:</strong></p>";
                        echo "<code class='d-block bg-success text-white p-2'>$updated_query</code>";
                        echo "</div>";
                    }
                    ?>
                </div>

                <!-- اختبار فحص الجداول -->
                <div class="test-section">
                    <h3><i class="fas fa-check-circle"></i> اختبار فحص وجود الجداول</h3>
                    
                    <?php
                    $test_username = 'test_user';
                    $tables_to_check = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
                    
                    echo "<h5>فحص جداول المستخدم: $test_username</h5>";
                    echo "<div class='row'>";
                    
                    foreach ($tables_to_check as $table) {
                        $exists = userTableExists($table, $test_username);
                        $count = getUserTableCount($table, $test_username);
                        
                        echo "<div class='col-md-4 mb-3'>";
                        echo "<div class='card'>";
                        echo "<div class='card-body text-center'>";
                        echo "<h6 class='card-title'>$table</h6>";
                        
                        if ($exists) {
                            echo "<p class='success'><i class='fas fa-check'></i> موجود</p>";
                            echo "<p class='info'>عدد السجلات: $count</p>";
                        } else {
                            echo "<p class='error'><i class='fas fa-times'></i> غير موجود</p>";
                        }
                        
                        echo "</div>";
                        echo "</div>";
                        echo "</div>";
                    }
                    
                    echo "</div>";
                    ?>
                </div>

                <!-- معلومات النظام -->
                <div class="test-section">
                    <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>إعدادات قاعدة البيانات:</h5>
                            <ul>
                                <li><strong>الخادم:</strong> <?php echo MAIN_DB_HOST; ?></li>
                                <li><strong>قاعدة البيانات الرئيسية:</strong> u193708811_system_main (المستخدم: sales01)</li>
                                <li><strong>قاعدة بيانات العمليات:</strong> u193708811_operations (المستخدم: sales02)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>معلومات الجلسة:</h5>
                            <ul>
                                <li><strong>حالة الجلسة:</strong> <?php echo session_status() === PHP_SESSION_ACTIVE ? 'نشطة' : 'غير نشطة'; ?></li>
                                <li><strong>معرف الجلسة:</strong> <?php echo session_id(); ?></li>
                                <li><strong>تسجيل الدخول:</strong> <?php echo isLoggedIn() ? 'مسجل' : 'غير مسجل'; ?></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="test-section text-center">
                    <h3><i class="fas fa-tools"></i> أدوات التحكم</h3>
                    
                    <div class="btn-group" role="group">
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="register.php" class="btn btn-success">
                            <i class="fas fa-user-plus"></i> تسجيل مستخدم جديد
                        </a>
                        <a href="login.php" class="btn btn-info">
                            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
                        </a>
                        <button onclick="location.reload()" class="btn btn-warning">
                            <i class="fas fa-refresh"></i> إعادة تحميل الاختبار
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
