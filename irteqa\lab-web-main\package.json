{"homepage": ".", "name": "jsonldresume", "version": "1.0.0", "private": true, "engines": {"node": "13.12.0", "npm": "6.14.4"}, "dependencies": {"@fullhuman/postcss-purgecss": "^2.3.0", "@testing-library/jest-dom": "^5.10.1", "@testing-library/react": "^10.4.1", "@testing-library/user-event": "^12.0.7", "@vuepress/plugin-google-analytics": "^1.5.2", "autoprefixer": "^9.8.3", "axios": "^0.21.1", "dayjs": "^1.10.4", "file-saver": "^2.0.5", "gh-pages": "^3.1.0", "html2canvas": "^1.0.0-rc.5", "i18next": "^19.5.1", "i18next-browser-languagedetector": "^5.0.0", "i18next-http-backend": "^1.0.22", "jspdf": "^1.5.3", "jszip": "^3.5.0", "lodash": "^4.17.20", "postcss-cli": "^7.1.1", "react": "^16.13.1", "react-dom": "^16.13.1", "react-easy-panzoom": "^0.4.4", "react-i18next": "^11.7.0", "react-icons": "^4.1.0", "react-markdown": "^4.3.1", "react-scripts": "3.4.1", "react-toastify": "^6.0.6", "tailwindcss": "^1.4.6", "uuid": "^8.2.0", "vuepress": "^1.5.2"}, "scripts": {"css": "postcss src/assets/tailwind/tailwind.src.css -o src/assets/tailwind/tailwind.css", "css:prod": "postcss src/assets/tailwind/tailwind.src.css -o src/assets/tailwind/tailwind.css --env production", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "prestart": "npm run css", "start": "react-scripts start", "prebuild": "npm run css:prod", "build": "NODE_ENV=production react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "predeploy": "npm run build && npm run docs:build", "deploy": "firebase deploy", "predeploy:app": "npm run build", "deploy:app": "firebase deploy --only hosting:app", "predeploy:docs": "npm run docs:build", "deploy:docs": "firebase deploy --only hosting:docs", "docker:dev": "docker-compose -f docker-compose-dev.yml up -d --build", "docker": "docker-compose up -d --build"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint-config-airbnb": "^18.2.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-import": "^2.21.2", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react-hooks": "^4.0.4", "eslint-plugin-react": "^7.20.0", "eslint": "^7.3.1"}}