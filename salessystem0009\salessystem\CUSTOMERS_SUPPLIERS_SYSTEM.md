# نظام العملاء والموردين المنفصل

## 🔧 التحديثات المطبقة على قاعدة البيانات

### **تحديث جدول العملاء في الأوامر الأساسية:**

#### **الحقول المضافة:**
```sql
-- حقل نوع العميل
`customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer'

-- حقل التحديث التلقائي
`updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP

-- فهرس نوع العميل
KEY `idx_customer_type` (`customer_type`)
```

#### **الهيكل الجديد:**
```sql
CREATE TABLE IF NOT EXISTS `customers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `tax_number` varchar(50) DEFAULT NULL,
    `address` text DEFAULT NULL,
    `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`),
    KEY `idx_email` (`email`),
    KEY `idx_phone` (`phone`),
    KEY `idx_customer_type` (`customer_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

### **التحديث التلقائي للجداول الموجودة:**

#### **إضافة الأعمدة المفقودة:**
```php
'customers' => [
    'customer_type' => "ALTER TABLE `customers` ADD COLUMN `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer' AFTER `address`",
    'updated_at' => "ALTER TABLE `customers` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`"
]
```

#### **إضافة الفهارس:**
```php
'customers' => [
    'idx_customer_type' => "ALTER TABLE `customers` ADD INDEX `idx_customer_type` (`customer_type`)"
]
```

## 📋 تحديثات صفحة العملاء

### **1. نظام التبويب:**

#### **تبويب العملاء والموردين:**
```html
<ul class="nav nav-tabs mb-3" id="customerTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <a class="nav-link <?php echo $customer_type === 'customer' ? 'active' : ''; ?>" 
           href="?type=customer">
            <i class="fas fa-user-friends me-2"></i>
            العملاء
        </a>
    </li>
    <li class="nav-item" role="presentation">
        <a class="nav-link <?php echo $customer_type === 'supplier' ? 'active' : ''; ?>" 
           href="?type=supplier">
            <i class="fas fa-truck me-2"></i>
            الموردين
        </a>
    </li>
</ul>
```

### **2. فلترة البيانات:**

#### **فلتر نوع العميل:**
```php
// فلتر نوع العميل
$where_conditions[] = "customer_type = ?";
$params[] = $customer_type;
$types .= 's';

// فلتر البحث
if (!empty($search)) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR phone LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $types .= 'sss';
}
```

### **3. واجهة محسنة:**

#### **عرض البيانات المحسن:**
- **روابط الهاتف**: `<a href="tel:...">`
- **روابط البريد**: `<a href="mailto:...">`
- **شارات للرقم الضريبي**: `<span class="badge bg-info">`
- **اختصار العنوان**: عرض 30 حرف مع "..."

#### **أزرار الإجراءات:**
```html
<div class="btn-group" role="group">
    <button type="button" class="btn btn-sm btn-outline-primary" 
            onclick="editCustomer(<?php echo $customer['id']; ?>)"
            title="تعديل">
        <i class="fas fa-edit"></i>
    </button>
    <button type="button" class="btn btn-sm btn-outline-danger" 
            onclick="deleteCustomer(...)"
            title="حذف">
        <i class="fas fa-trash"></i>
    </button>
</div>
```

## 🔄 ملفات النظام الجديدة

### **1. `save_customer.php` - حفظ العملاء والموردين:**

#### **المميزات:**
- **التحقق من البيانات** الشامل
- **منع التكرار** في الأسماء والبريد الإلكتروني
- **دعم العملاء والموردين** في نفس الملف
- **تسجيل العمليات** في سجل النشاطات

#### **التحقق من البيانات:**
```php
// التحقق من الاسم
if (empty($name)) {
    throw new Exception("الاسم مطلوب");
}

// التحقق من نوع العميل
if (!in_array($customer_type, ['customer', 'supplier'])) {
    throw new Exception("نوع العميل غير صحيح");
}

// التحقق من البريد الإلكتروني
if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    throw new Exception("البريد الإلكتروني غير صحيح");
}
```

### **2. `get_customer.php` - جلب بيانات العميل/المورد:**

#### **API للتعديل:**
```php
// إرجاع البيانات بصيغة JSON
echo json_encode([
    'success' => true,
    'customer' => [
        'id' => $customer['id'],
        'name' => $customer['name'],
        'phone' => $customer['phone'],
        'email' => $customer['email'],
        'tax_number' => $customer['tax_number'],
        'address' => $customer['address'],
        'customer_type' => $customer['customer_type']
    ]
]);
```

## 🎯 المميزات الجديدة

### **الفصل بين العملاء والموردين:**
- ✅ **تبويب منفصل** لكل نوع
- ✅ **فلترة تلقائية** حسب النوع
- ✅ **عدادات منفصلة** لكل نوع
- ✅ **بحث مستقل** في كل قسم

### **تحسينات الواجهة:**
- ✅ **أيقونات مميزة** (user-friends للعملاء، truck للموردين)
- ✅ **ألوان متناسقة** مع نظام الألوان العام
- ✅ **روابط تفاعلية** للهاتف والبريد
- ✅ **عرض محسن** للبيانات الطويلة

### **وظائف متقدمة:**
- ✅ **إضافة وتعديل** من نافذة منبثقة
- ✅ **حذف آمن** مع التحقق من الاستخدام
- ✅ **بحث متقدم** في جميع الحقول
- ✅ **ترقيم صفحات** مع الحفاظ على الفلاتر

## 📊 الفوائد المحققة

### **تحسين التنظيم:**
- **فصل واضح** بين العملاء والموردين
- **إدارة مستقلة** لكل نوع
- **بحث مخصص** لكل قسم
- **إحصائيات منفصلة** لكل نوع

### **تحسين تجربة المستخدم:**
- **تنقل سهل** بين الأقسام
- **واجهة بديهية** مع أيقونات واضحة
- **بحث سريع** وفعال
- **عرض منظم** للبيانات

### **تحسين الأداء:**
- **فهارس محسنة** للبحث السريع
- **استعلامات مُحسنة** مع فلترة النوع
- **ترقيم فعال** للصفحات الكبيرة
- **تحميل سريع** للبيانات

## 🔗 التكامل مع النظام

### **الفواتير:**
- **ربط العملاء** بفواتير المبيعات
- **ربط الموردين** بفواتير المشتريات
- **فلترة تلقائية** حسب نوع الفاتورة
- **عرض مناسب** في القوائم المنسدلة

### **التقارير:**
- **تقارير منفصلة** للعملاء والموردين
- **إحصائيات مفصلة** لكل نوع
- **تحليل الأداء** المستقل
- **مقارنات** بين الأنواع

### **الأمان:**
- **حماية من الحذف** إذا كان مستخدم في فواتير
- **التحقق من الصلاحيات** في جميع العمليات
- **تسجيل العمليات** في سجل النشاطات
- **حماية من SQL Injection**

## ✅ الخلاصة

تم تطوير نظام العملاء والموردين المنفصل بنجاح:

### **الإنجازات:**
1. **تحديث قاعدة البيانات** مع حقل نوع العميل
2. **نظام تبويب** للفصل بين العملاء والموردين
3. **واجهة محسنة** مع مميزات متقدمة
4. **تكامل شامل** مع باقي النظام

### **النتائج:**
- ✅ **إدارة منفصلة** للعملاء والموردين
- ✅ **واجهة بديهية** مع تبويب واضح
- ✅ **بحث وفلترة متقدمة** لكل نوع
- ✅ **تحديث تلقائي** لقاعدة البيانات

### **الوظائف المتاحة:**
- **العملاء**: إدارة عملاء المبيعات
- **الموردين**: إدارة موردي المشتريات
- **بحث مستقل**: في كل قسم على حدة
- **إحصائيات منفصلة**: لكل نوع

**النتيجة: نظام إدارة عملاء وموردين متكامل ومنفصل مع واجهة احترافية وتبويب واضح!** 🎉
