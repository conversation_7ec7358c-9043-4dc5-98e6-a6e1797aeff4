<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head> 
<style>
        body{
            background: aliceblue;
        }
        .form{
            box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
            position:absolute;
            top:50%;
            left:50%;
            transform:translate(-50%,-50%);
            width: 400px;
            height: 480px;
            background:rgba(0,0,0,.5);
            border-radius:15px;
            box-sizing:border-box;
            margin: 5px;
            direction: rtl;
        }
        .snup{
            margin-top: 90px;
            margin-right: 20px;
        }
        img[alt="user"]{
                position:absolute;
                top:-60px;
                left: 130px;
            }
            input[type="text"],[type="password"]{
                font-size: 20px;
                outline:none;
                border:none;
                width: 40%;
                height:35px;
                border-bottom:1px solid #cbcbcb;
                background:none;
                padding:5px;
                color:#fff;
                text-align:right;
                margin-left: 25px;
                margin-bottom: 25px;

                
            }
            input[type="text"]{
                top: auto;
    left: auto;
             
            
            }
            input[type="password"]{
                top:210px;
                left:50px;
            }
            ::placeholder{
                color:#fff;

            }
            button[type="submit"]{
                position:absolute;
                top: 425px;
                left: 140px;
                width:130px;
                height:35px;
                background:#ffffff;
                border:none;
                outline: none;
                border-radius:15px;
                font-size:20px;

            }
            button[type="submit"]:hover{
                background:#fff;
                color:#0077ff;
            }
h4{
    margin: auto;
    text-align: -webkit-center;
    color: chartreuse;
}
h4 label{
    color: orange;
}
    </style>
<body>
    <div class="form">
    <form action="signup.php"  method="POST" class="snup">
        <img src="img/offbtn.png" alt="user"style="width:150px;">
        <input name="fname"  type="text" placeholder="الاسم الأول"value="<?php echo isset($_POST['fname']) ? $_POST['fname'] : ''; ?>">
        <input name="lname" type="text" placeholder="إسم العائلة" value="<?php echo isset($_POST['lname']) ? $_POST['lname'] : ''; ?>" ><br>
        <input name="email" type="text" placeholder="الايميل" style=" width: auto;" value="<?php echo isset($_POST['email']) ? $_POST['email'] : ''; ?>"><br>
        <input name="uname" type="text" placeholder="اسم المستخدم" style=" width: auto;" value="<?php echo isset($_POST['uname']) ? $_POST['uname'] : ''; ?>"><br>
        <input name="fword" type="password" placeholder="كلمة المرور" value="<?php echo isset($_POST['fword']) ? $_POST['fword'] : ''; ?>">
        <input name="pword" type="password" placeholder="كلمة المرور" value="<?php echo isset($_POST['pword']) ? $_POST['pword'] : ''; ?>">
        <button name="login" type="submit">الدخول</button>
        <label for=""></label>
    </form>
    <?php 
    include('lgcon.php');
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $fname = $_POST['fname'];
    $lname = $_POST['lname'];
    $email = $_POST['email'];
    $uname = $_POST['uname'];
    $pword = $_POST['pword'];
    $fword=$_POST['fword'];
    $hash = password_hash($pword, PASSWORD_DEFAULT);
    $sql = "INSERT INTO userst(fname,lname,email,uname,pword) VALUES ('$fname','$lname','$email','$uname','$hash')";
   $fpword= $fword !== $pword;
    if($fpword){
        echo"<h4><label>- كلمات المرور غير متطابقة","<br>";
} elseif (isset($_POST['login']));
{
    
 $empty  = empty($fname&&$uname&&$pword&&$fword);
if($empty){
    echo"<h4><label>- رجاءاً تعبئة البيانات المطلوبة";
}

else{
try{if($fword == $pword){
    mysqli_query($conn, $sql);
        echo"<h4>تم التسجيل بنجاح";
} 
}
catch(mysqli_sql_exception){
    echo"<h4><label>- اسم المستخدم غير متاح جرب اسم مستخدم اخر","<br>";
}

}

}
    }
 //   echo"<h4><label>- كلمات المرور غير متطابقة","<br>";if($fword !== $pword){
 //   echo"<h4><label>- كلمات المرور غير متطابقة","<br>";}
/*
elseif(mysqli_query($conn, $sql)){
    echo"تم التسجيل بنجاح";
    }else{
      echo 'Error: ' . mysqli_error($conn);    }
      if($fword === $pword){
  */


    ?>
    <?php
    mysqli_close($conn);
    
    ?>
</div>

<script>
if ( window.history.replaceState ) {
  window.history.replaceState( null, null, window.location.href );
}
</script>
</body>
</html>
   
