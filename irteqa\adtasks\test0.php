<style>
  /* ستايلات للخلفية السوداء التي تعمل كطبقة تظليل */
  .overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* لون الخلفية مع شفافية */
    display: none;
    /* إخفاء الطبقة بشكل افتراضي */
    justify-content: center;
    align-items: center;
    z-index: 999;
    /* تحديد ترتيب الطبقة */
  }

  /* ستايلات لصندوق النافذة المنبثقة */
  .popup {
    width: 450px;
    height: 250px;
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
  }

  .newitem {
    padding: 10px;
    background: rgb(233, 233, 233);
    border-radius: 10px;
    border: 1px solid rgb(192, 192, 192);
    display: inline-grid;
    justify-content: center;
  }

  .newtitl {
    border: 1px solid #b1b1b1;
    padding: 20px;
    border-radius: 10px;
    margin-top: 25px;
    margin-bottom: 5px;
    place-self: center;
  }

  .sctntitl {
    background: #e9e9e9;
    position: relative;
    top: -60px;
    right: 0px;
    border: 1px solid #ababab;
    padding: 5px;
    border-radius: 10px;
    width: max-content;
    margin: 0;
  }

  .titlfrm {
    display: flex;
    align-items: center;
  }

  input[type="text"] {
    width: 300px;
    height: 30px;
    border-radius: 5px;
    border: 2px solid #c5c5c5;
    margin: 5px;
    font-weight: bold;
    font-size: 1.3em;
    padding: 5px;
  }

  input[type="button"] {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
    margin: 3px;
    font-weight: bold;
    font-size: 1.5em;
  }

  input[type="button"]:hover {
    background-color: #a0d9ff;
    color: rgb(71, 71, 71);
  }
</style>
<!-- الطبقة السوداء التي تعمل كظليل -->
<div class="overlay" id="overlay">
  <!-- صندوق النافذة المنبثقة -->
  <div class="popup">

    <div class="newtitl">
      <h1 class="sctntitl">إضافة قسم</h1>
      <form action="" method="post" class="titlfrm">
        <input type="text">
        <input type="button" value="أضف">
      </form>
    </div>
    <button onclick="closePopup()">إغلاق</button>
  </div>

  <script>
    // دالة لفتح النافذة المنبثقة
    function openPopup() {
      document.getElementById("overlay").style.display = "flex";
    }

    // دالة لإغلاق النافذة المنبثقة
    function closePopup() {
      document.getElementById("overlay").style.display = "none";
    }
  </script>