<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="http://fonts.googleapis.com/earlyaccess/droidarabicnaskh.css" rel="stylesheet" type="text/css" />
    <title>Document</title>
</head>
<style>

    body{
        font-family: 'Droid Arabic Naskh', 'Monda', sans-serif;
        direction: rtl;
    }
    .trev{
        margin: 10%;
        margin-top: 100px;
    float: right;
    }
    .reslt{
        border: dashed 2px;
    padding: 10px;
    margin: 20px;
    border-radius: 7px;
    }
    h3{
        margin: 0;
    }
   .sub{
        font-size: x-large;
    border: outset;
    }
</style>
<body>
    <div class="trev">
<form class="box" action="test.php" method="POST">
    <input type="text" name="text" value="" style="width: 250px;
    height: 25px;">
    <input class="sub" type="submit" value="إرسال" name="send"/>
</form>
<div class="reslt">
    <?php
   if (isset($_POST['send'])){
    echo "<h3>" ;
    echo strrev($_POST['text']);
    echo "<h3>" ;
echo bin2hex($_POST['text']);
echo "<h3>" ;
echo base64_encode($_POST['text']);
}
    ?></div>
    <div>
    <input type="text" name="title" class="form-control" placeholder="one" onkeyup='URLChange(this.value);'>
<input type="text" name="url_code" class="form-control" placeholder="two">
    </div>
    </div>
    <script>
        function URLChange(titlestr) {
  var url=titlestr.replace(/ /g," ");
  document.getElementsByName("url_code")[0].value=url;
}
    </script>
</body>
</html>