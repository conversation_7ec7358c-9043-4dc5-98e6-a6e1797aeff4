<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Range Input Example</title>
</head>
<style>
  #volume{
    width: 400px;
  }
  
</style>
<body>

  <label for="volume">Volume:</label>
  <input type="range" id="volume" name="volume" min="0" max="200" step="1" oninput="updateVolume(this.value)">
  <span id="volumeValue">100</span>

  <script>
    function updateVolume(value) {
      document.getElementById('volumeValue').innerText = value;
      // يمكنك أيضًا إضافة الشفرة هنا لاستخدام قيمة 'value' فيما تحتاج
    }
  </script>

</body>
</html>