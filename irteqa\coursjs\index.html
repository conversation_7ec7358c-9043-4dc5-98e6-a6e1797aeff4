<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <h1 id="noor">hello</h1>
  
    <h1 class="name2"> Hello Worled</h1>
    <h2>hello dr</h2>
<!--<script>
            myvar = "<?php echo "hello"; ?>";
            alert(myvar);
           </script>-->
           <script>
           // document.querySelector('#noor').style.color = "red"
           // document.querySelector('.name2').style.color = "red"
          
           /// const
         const noor = document.querySelector('#noor');
        // noor.style.color = "red"
        // noor.innerHTML = " bye"
        // setTimeout(()=>{
          //  noor.innerHTML = "welcome"
         //},2000)
         let x = 5 + 15;
         noor.innerHTML = x

           </script>
</body>
</html>