<template>
  <div id="preview" class="preview" style="width:70%; margin: 0 auto;text-align: justify; border: solid 1px;border-padding: 3px;min-height: 29.7cm;">
    <h1 style="margin-bottom:0">{{ data.profile.name }}</h1>
<div class=WordSection1>
<p class=MsoNormal><span lang=EN-US style='color:#666666'>&nbsp;</span></p>
<p class=MsoTitle><a name=h.ydgpbs5gi5fx></a><span lang=EN-US>Tanmay Naik</span></p>
<p class=MsoNormal><span lang=EN-US>&nbsp;</span></p>
<p class=ContactCxSpFirst><span lang=EN-US>27, China Gate 2, New CityLight Area, Surat</span></p>
<p class=ContactCxSpMiddle><span lang=EN-US>+91 9429368682</span></p>
<p class=ContactCxSpLast><span lang=EN-US><EMAIL></span></p>
<p class=MsoNormal><span lang=EN-US>&nbsp;</span></p>
<div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 5.0pt 0cm'><div style='border:none;border-bottom:solid windowtext 1.0pt;padding:0cm 0cm 1.0pt 0cm'>
<p class=MsoNormalCxSpFirst align=center style='2;border:none;
padding:0cm'><span lang=EN-US style='font-family:\"Georgia\",serif;color:#666666'>&nbsp;</span></p>
</div>
<p class=MsoNormalCxSpMiddle><b><span lang=EN-US style='font-family:\"Georgia\",serif;
color:#666666'>&nbsp;</span></b></p>
<p class=MsoNormalCxSpLast><b><span lang=EN-US style='font-family:\"Georgia\",serif;
color:#666666'>&nbsp;</span></b></p>
<p class=MsoNormalCxSpLast><b><span lang=EN-US style='font-family:\"Georgia\",serif;
color:#741B47;text-transform:uppercase;letter-spacing:2.0pt'>Objective</span></b></p>
<p class=MsoNormal><b><span lang=EN-US style='font-family:\"Georgia\",serif;
color:#741B47;text-transform:uppercase;letter-spacing:2.0pt'>        </span></b></p>
<p class=Normalwithleftindent><span lang=DE>I am seeking a company where I can use my experience and education to help the company meet and surpass its goals.
I want to share my high level of skills on most languages and work on real life problems.</span></p>
<br>
<p class=MsoNormalCxSpLast><b><span lang=EN-US style='font-family:\"Georgia\",serif;
color:#741B47;text-transform:uppercase;letter-spacing:2.0pt'>EDUCATION</span></b></p>
<p class=MsoListParagraphCxSpFirst><span lang=EN-US style='font-size:14.0pt;
line-height:115%;font-family:\"Wingdings 2\";color:#244A58;letter-spacing:3.0pt'>å<span
style='font:7.0pt \"Times New Roman\"'> </span></span><span lang=EN-US>12th HSC from Jeevan Bharti, Surat</span></p>
<p class=MsoListParagraphCxSpFirst><span lang=EN-US style='font-size:14.0pt;
line-height:115%;font-family:\"Wingdings 2\";color:#244A58;letter-spacing:3.0pt'>å<span
style='font:7.0pt \"Times New Roman\"'> </span></span><span lang=EN-US>B. Tech in Computer Engineering from DDIT, Nadiad</span></p>
<p class=MsoListParagraphCxSpFirst><span lang=EN-US style='font-size:14.0pt;
line-height:115%;font-family:\"Wingdings 2\";color:#244A58;letter-spacing:3.0pt'>å<span
style='font:7.0pt \"Times New Roman\"'> </span></span><span lang=EN-US>MS in Computer Science from MIT, Massachusetts</span></p>
        <br><p class=MsoNormalCxSpMiddle><b><span lang=EN-US style='font-family:\"Georgia\",serif;
color:#741B47;text-transform:uppercase;letter-spacing:2.0pt'>Experience</span></b></p>
<h2><span lang=EN-US>Assistant Software Developer at Infosys</span></h2><br>
<p class=MsoListParagraph><span lang=EN-US style='font-size:14.0pt;line-height:
115%;font-family:\"Wingdings 2\";color:#244A58;letter-spacing:3.0pt'>å<span
style='font:7.0pt \"Times New Roman\"'> </span></span><span lang=EN-US>Design, develop and maintain the operation of database-driven ASP .NET/C# Web applications, with a specific emphasis on usability, performance and scalability.</span></p>
<br>
<h2><span lang=EN-US>Web Developer at Cisco</span></h2><br>
<p class=MsoListParagraph><span lang=EN-US style='font-size:14.0pt;line-height:
115%;font-family:\"Wingdings 2\";color:#244A58;letter-spacing:3.0pt'>å<span
style='font:7.0pt \"Times New Roman\"'> </span></span><span lang=EN-US>Build the operations end of the organization's websites and keep them running smoothly.</span></p>
<br>
<p class=MsoNormalCxSpLast><b><span lang=EN-US style='font-family:\"Georgia\",serif;
color:#741B47;text-transform:uppercase;letter-spacing:2.0pt'>Skills</span></b></p>
<p class=MsoListParagraphCxSpFirst><span lang=EN-US style='font-size:14.0pt;
line-height:115%;font-family:\"Wingdings 2\";color:#244A58;letter-spacing:3.0pt'>å<span
style='font:7.0pt \"Times New Roman\"'> </span></span><span lang=EN-US>JavaEE applications development</span></p>
<p class=MsoListParagraphCxSpFirst><span lang=EN-US style='font-size:14.0pt;
line-height:115%;font-family:\"Wingdings 2\";color:#244A58;letter-spacing:3.0pt'>å<span
style='font:7.0pt \"Times New Roman\"'> </span></span><span lang=EN-US>Algorithms Design & Analysis expert</span></p>
<p class=MsoListParagraphCxSpFirst><span lang=EN-US style='font-size:14.0pt;
line-height:115%;font-family:\"Wingdings 2\";color:#244A58;letter-spacing:3.0pt'>å<span
style='font:7.0pt \"Times New Roman\"'> </span></span><span lang=EN-US>IIT-B Certified Linux programmer</span></p>
</div>
</div>
  </div>
</template>




<script>

export default {
  name: "Template",
  props: ["data"],
  methods: {
  },
  mounted() {
    (function() {
      var id_ = 'preview'
      var rows_ = document.querySelectorAll('#' + id_ + '> .draggable-element')
      var dragSrcEl_ = null
      var current = null

      function handleDragStart(e) {
        e.dataTransfer.effectAllowed = 'move'
        e.dataTransfer.setData('text/html', e.target.innerHTML)
        e.dataTransfer.dropEffect = "move"
        dragSrcEl_ = e.target

      }

      function handleDragOver(e) {
        if (typeof e.target.closest == "function")
          current = e.target.closest(".draggable-element")
      }
      function handleDrop(e) {
        if (current.classList[0] == "draggable-element") {
          dragSrcEl_.innerHTML = current.innerHTML
          current.innerHTML = e.dataTransfer.getData('text/html')
        }
      }

      [].forEach.call(rows_, function(row) {
        row.addEventListener('dragstart', handleDragStart, false)
        row.addEventListener('dragover', handleDragOver, false)
        row.addEventListener('dragend', handleDrop, false)
      })

    })()
  }
}
</script>


<style>
.preview {
  user-select: none;
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -o-user-select: none;
}

.preview > * {
  text-align: justify !important;
  line-height: 1.2 !important;
}
.preview > small {
  text-decoration: none !important;
  color: #808080 !important;
}
.preview > .sub-color {
  color: #808080 !important;
}
.preview > h3 {
  margin-top: 1.5em !important;
  margin-bottom: 0.5em !important;
}
.preview > body {
  size: 7in 9.25in !important;
  margin: 27mm 16mm 27mm 16mm !important;
}

li:before {
  content: "\2014\a0\a0";
}
li {
  list-style: none !important;
}
.pr-2 {
  padding-right: 5dp !important;
}
</style>