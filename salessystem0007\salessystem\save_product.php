<?php
/**
 * حفظ وتعديل المنتجات
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "طريقة إرسال غير صحيحة";
    header("Location: products.php");
    exit();
}

// الحصول على اتصال قاعدة البيانات
$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات";
    header("Location: products.php");
    exit();
}

try {
    // استلام البيانات
    $product_id = !empty($_POST['product_id']) ? intval($_POST['product_id']) : null;
    $name = trim($_POST['name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = floatval($_POST['price'] ?? 0);
    $tax_rate = floatval($_POST['tax_rate'] ?? 15);
    $category = trim($_POST['category'] ?? '');

    // التحقق من البيانات الأساسية
    if (empty($name)) {
        throw new Exception("اسم المنتج مطلوب");
    }

    if ($price < 0) {
        throw new Exception("السعر يجب أن يكون أكبر من أو يساوي صفر");
    }

    if ($tax_rate < 0 || $tax_rate > 100) {
        throw new Exception("نسبة الضريبة يجب أن تكون بين 0 و 100");
    }

    // التحقق من عدم تكرار اسم المنتج
    if ($product_id) {
        // في حالة التعديل
        $check_stmt = $db->prepare("SELECT id FROM products WHERE name = ? AND id != ?");
        $check_stmt->bind_param("si", $name, $product_id);
    } else {
        // في حالة الإضافة
        $check_stmt = $db->prepare("SELECT id FROM products WHERE name = ?");
        $check_stmt->bind_param("s", $name);
    }

    $check_stmt->execute();
    $existing_product = $check_stmt->get_result()->fetch_assoc();
    $check_stmt->close();

    if ($existing_product) {
        throw new Exception("يوجد منتج آخر بنفس الاسم");
    }

    if ($product_id) {
        // تعديل منتج موجود
        $stmt = $db->prepare("UPDATE products SET name = ?, description = ?, price = ?, tax_rate = ?, category = ?, updated_at = NOW() WHERE id = ?");
        $stmt->bind_param("ssddsi", $name, $description, $price, $tax_rate, $category, $product_id);
        
        if ($stmt->execute()) {
            logActivity('product_update', 'products', $product_id, null, [
                'name' => $name,
                'price' => $price,
                'tax_rate' => $tax_rate
            ], 'تعديل منتج');
            
            $_SESSION['success'] = "تم تعديل المنتج بنجاح";
        } else {
            throw new Exception("حدث خطأ أثناء تعديل المنتج");
        }
    } else {
        // إضافة منتج جديد
        $stmt = $db->prepare("INSERT INTO products (name, description, price, tax_rate, category, created_at, updated_at) VALUES (?, ?, ?, ?, ?, NOW(), NOW())");
        $stmt->bind_param("ssdds", $name, $description, $price, $tax_rate, $category);
        
        if ($stmt->execute()) {
            $new_product_id = $db->insert_id;
            
            logActivity('product_create', 'products', $new_product_id, null, [
                'name' => $name,
                'price' => $price,
                'tax_rate' => $tax_rate
            ], 'إضافة منتج جديد');
            
            $_SESSION['success'] = "تم إضافة المنتج بنجاح";
        } else {
            throw new Exception("حدث خطأ أثناء إضافة المنتج");
        }
    }

    $stmt->close();

} catch (Exception $e) {
    $_SESSION['error'] = "خطأ في حفظ المنتج: " . $e->getMessage();
} finally {
    if (isset($db)) {
        $db->close();
    }
}

header("Location: products.php");
exit();
?>
