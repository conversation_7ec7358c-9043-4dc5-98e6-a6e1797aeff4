<?php
session_start(); // استئناف الجلسة

if (!isset($_SESSION["uname"])) {
    // إذا لم يتم تسجيل الدخول، قم بتوجيه المستخدم إلى نموذج تسجيل الدخول
    header("Location: login_form.html");
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
  <head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إدارة الطلبات</title>
<link rel="stylesheet" href="style/foundation.min.css">
<link rel="stylesheet" href="style/jquery.dataTables.min.css">
<link rel="stylesheet" href="style/style.css">
<script src="js/jquery-3.3.1.min.js"></script>

<script>
  window.console = window.console || function(t) {};
</script><script>
  if (document.location.search.match(/type=embed/gi)) {
    window.parent.postMessage("resize", "*");
  }
</script><style type="text/css"> /*
 * contextMenu.js v 1.4.0
 * Author: Sudhanshu Yadav
 * s-yadav.github.com
 * Copyright (c) 2013 Sudhanshu Yadav.
 * Dual licensed under the MIT and GPL licenses
**/
</style>
</head>

<body>
  <?php include('head.php');?>
  
  <div class="aa" >
   <h1>طلب جديد</h1>
   <div id="myform">
        <form class="box" action="index.php" method="POST">
          <input type="submit" value="إرسال" name="send"/>
              <input type="text" id="serv" name="serv" placeholder="الخدمة">
              <input type="text" id="fname" name="fname" placeholder="الإسم" >
              <input class="inpu" type="text" id="call1" name="calll" placeholder="الهاتف" style="width: 20%;">
         <select class="form-select" aria-label="Default select example" style="text-align: center;font-size:20px;" name="who">
            <option value="منفذ">منفذ</option>
            <option value="وسيط">وسيط</option>
            <option value="عميل">عميل</option>
              <input type="text" id="sell" name="sell" placeholder="المبلغ" style="width:10%;">
        </form>
        <div></div>
        <?php 
        include('indcon.php');
        
$sql = "INSERT INTO orderss(sell,who,calll,fname,serv) VALUES ('$sell','$who','$calll','$fname','$serv')";
if (isset($_POST['send'])){

if(empty($serv)){
  echo '';

}
elseif(mysqli_query($conn, $sql)){ 
  
    }else{
      echo 'Error: ' . mysqli_error($conn);    }
      
}

?>
</div>
<div id="tabh">
      <!-- End Example Code -->
    <table id="fantasyTable" class="display responsive no-wrap order-column dataTable no-footer dtr-inline" role="grid" style="width: 100%;text-align: right;zoom:1.2">
<?php include ('mdlfrm.php');?>
  <thead>
  <tr role="row">
  
    <th class="sorting" tabindex="0" aria-controls="fantasyTable" rowspan="1" colspan="1" style="width: 100px;" aria-sort="ascending" aria-label="Rank: activate to sort column descending">التأريخ</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 40px;text-align: center;" aria-label="State">الإجراء</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 30px;text-align:center;" aria-label="Customer">المبلغ(ريال)</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 40px;text-align:center;" aria-label="Customer">الصفة</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 60px;text-align: center;" aria-label="City">الهاتف</th>
    <th class="sorting" rowspan="1" colspan="1" style="width: 188px;text-align: center;" aria-label="State">إسم العميل</th>
    <th class="sorting" tabindex="0" aria-controls="fantasyTable" rowspan="1" colspan="1" style="width: 150px;text-align: center;" aria-label="Total Points: activate to sort column ascending">الخدمة</th>
    <th class="sorting" tabindex="0" aria-controls="fantasyTable" rowspan="1" colspan="1" style="width: 40px;text-align:center;" aria-label="Entries: activate to sort column ascending">الرقم</th>
    <th class="sorting" tabindex="0" aria-controls="fantasyTable" rowspan="1" colspan="1" style="width: 20px;text-align:center;padding: 3px;" aria-label="Entries: activate to sort column ascending">تعديل</th>
  </tr>
 </thead>
 <?php
 include('indcon.php');
// إستيراد معلومات المرضى من قاعدة البيانات
$sql = "SELECT * FROM orderss";
$result = mysqli_query($conn,$sql);
$num=1;
if ($result){
    while($row = mysqli_fetch_assoc($result)){
        echo "<tr><td>" . $row['date'] . "</td><td>" . $row['injaz'] . "</td><td>" . $row['sell'] . "</td><td>" . $row['who'] . "</td><td>" . $row['calll'] . "</td><td>" . $row['fname'] . "</td><td>" . $row['serv'] . "</td><td>" . $num++ . "</td><td>" . '<a  href="?user_edit='.$row['id'].'" ><img src="img/sett.png" style="width:25px;"></a>' . "</td></tr>";
      }
    echo "</table>";
    
}
else{
    echo "يوجد خطا ماء";
}
?>

</div>
</script>
<script type="text/javascript">
    $(window).on('load',function(){
        $('#edit').modal('show');
    });
</script>
	
	
<script>
function goBack() {
    window.history.back();
}
</script>


<script>
$('#edit').modal({
backdrop: 'static',
keyboard: false
})
</script>	

<script src="js/jquery.dataTables.min.js"></script>
      <script id="rendered-js">
// ENTER INFO INTO SEARCH BAR TO SEE FUNCTION. CLICK RANK TO SEE RANKING. PAGINATION ON BOTTOM.

// DATATABLES.NET

$(document).ready(function () {

  jQuery(function () {
    $('#fantasyTable').DataTable({
      info: false,
      responsive: true,
      columnDefs: [
      { responsivePriority: 1, targets: 0 },
      { responsivePriority: 2, targets: -1 },
      { responsivePriority: 3, targets: -2 },
      {
        "targets": [1, 2, 3],
        "orderable": false }] });
  });
});
//# sourceURL=pen.js احديث جافا
if ( window.history.replaceState ) {
  window.history.replaceState( null, null, window.location.href );
}
    </script>

<?php

?>
<h1 style="color:#04aa30;text-shadow: 0 1px 10px rgb(138 194 173);font-weight: bold;">بقية البيانات في الصفحات التالية</h1>
    </body>
</html>