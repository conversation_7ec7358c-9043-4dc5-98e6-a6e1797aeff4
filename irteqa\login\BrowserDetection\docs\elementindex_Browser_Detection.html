<html>
<head>
<title>Package Browser_Detection Element Index</title>
<link rel="stylesheet" type="text/css" href="media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">Browser_Detection</td>
  </tr>
  <tr><td class="header_line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="classtrees_Browser_Detection.html" class="menu">class tree: Browser_Detection</a> ]
		  [ <a href="elementindex_Browser_Detection.html" class="menu">index: Browser_Detection</a> ]
		  	    [ <a href="elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="li_Browser_Detection.html">Browser_Detection</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="Browser_Detection/_BrowserDetection.php.html">		BrowserDetection.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="Browser_Detection/BrowserDetection.html">BrowserDetection</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<a name="top"></a>
<h1>Element index for package Browser_Detection</h1>
	[ <a href="elementindex_Browser_Detection.html#a">a</a> ]
	[ <a href="elementindex_Browser_Detection.html#b">b</a> ]
	[ <a href="elementindex_Browser_Detection.html#c">c</a> ]
	[ <a href="elementindex_Browser_Detection.html#d">d</a> ]
	[ <a href="elementindex_Browser_Detection.html#f">f</a> ]
	[ <a href="elementindex_Browser_Detection.html#g">g</a> ]
	[ <a href="elementindex_Browser_Detection.html#i">i</a> ]
	[ <a href="elementindex_Browser_Detection.html#m">m</a> ]
	[ <a href="elementindex_Browser_Detection.html#p">p</a> ]
	[ <a href="elementindex_Browser_Detection.html#r">r</a> ]
	[ <a href="elementindex_Browser_Detection.html#s">s</a> ]
	[ <a href="elementindex_Browser_Detection.html#v">v</a> ]
	[ <a href="elementindex_Browser_Detection.html#w">w</a> ]
	[ <a href="elementindex_Browser_Detection.html#_">_</a> ]

  <hr />
	<a name="_"></a>
	<div>
		<h2>_</h2>
		<dl>
							<dt><b>$_agent</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_agent">BrowserDetection::$_agent</a></dd>
							<dt><b>$_browserName</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_browserName">BrowserDetection::$_browserName</a></dd>
							<dt><b>$_compatibilityViewName</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_compatibilityViewName">BrowserDetection::$_compatibilityViewName</a></dd>
							<dt><b>$_compatibilityViewVer</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_compatibilityViewVer">BrowserDetection::$_compatibilityViewVer</a></dd>
							<dt><b>$_customBrowserDetection</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_customBrowserDetection">BrowserDetection::$_customBrowserDetection</a></dd>
							<dt><b>$_customPlatformDetection</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_customPlatformDetection">BrowserDetection::$_customPlatformDetection</a></dd>
							<dt><b>$_customRobotDetection</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_customRobotDetection">BrowserDetection::$_customRobotDetection</a></dd>
							<dt><b>$_is64bit</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_is64bit">BrowserDetection::$_is64bit</a></dd>
							<dt><b>$_isMobile</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_isMobile">BrowserDetection::$_isMobile</a></dd>
							<dt><b>$_isRobot</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_isRobot">BrowserDetection::$_isRobot</a></dd>
							<dt><b>$_platform</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_platform">BrowserDetection::$_platform</a></dd>
							<dt><b>$_platformVersion</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_platformVersion">BrowserDetection::$_platformVersion</a></dd>
							<dt><b>$_robotName</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_robotName">BrowserDetection::$_robotName</a></dd>
							<dt><b>$_robotVersion</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_robotVersion">BrowserDetection::$_robotVersion</a></dd>
							<dt><b>$_version</b></dt>
				<dd>in file BrowserDetection.php, variable <a href="Browser_Detection/BrowserDetection.html#var$_version">BrowserDetection::$_version</a></dd>
							<dt><b>__construct</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#method__construct">BrowserDetection::__construct()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;BrowserDetection class constructor.</dd>
							<dt><b>__toString</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#method__toString">BrowserDetection::__toString()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine how the class will react when it is treated like a string.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="a"></a>
	<div>
		<h2>a</h2>
		<dl>
							<dt><b>addCustomBrowserDetection</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodaddCustomBrowserDetection">BrowserDetection::addCustomBrowserDetection()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Dynamically add support for a new Web browser.</dd>
							<dt><b>addCustomPlatformDetection</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodaddCustomPlatformDetection">BrowserDetection::addCustomPlatformDetection()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Dynamically add support for a new platform.</dd>
							<dt><b>addCustomRobotDetection</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodaddCustomRobotDetection">BrowserDetection::addCustomRobotDetection()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Dynamically add support for a new robot.</dd>
							<dt><b>androidVerToStr</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodandroidVerToStr">BrowserDetection::androidVerToStr()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert the Android version numbers to the operating system name. For instance '1.6' returns 'Donut'.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="b"></a>
	<div>
		<h2>b</h2>
		<dl>
							<dt><b>BrowserDetection</b></dt>
				<dd>in file BrowserDetection.php, class <a href="Browser_Detection/BrowserDetection.html">BrowserDetection</a><br>&nbsp;&nbsp;&nbsp;&nbsp;The BrowserDetection class facilitates the identification of the user's environment such as Web browser, version,  platform and device type.</dd>
							<dt><b>BrowserDetection.php</b></dt>
				<dd>procedural page <a href="Browser_Detection/_BrowserDetection.php.html">BrowserDetection.php</a></dd>
							<dt><b>BROWSER_ANDROID</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_ANDROID">BrowserDetection::BROWSER_ANDROID</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_BLACKBERRY</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_BLACKBERRY">BrowserDetection::BROWSER_BLACKBERRY</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_CHROME</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_CHROME">BrowserDetection::BROWSER_CHROME</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_EDGE</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_EDGE">BrowserDetection::BROWSER_EDGE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_FIREBIRD</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_FIREBIRD">BrowserDetection::BROWSER_FIREBIRD</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_FIREFOX</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_FIREFOX">BrowserDetection::BROWSER_FIREFOX</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_ICAB</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_ICAB">BrowserDetection::BROWSER_ICAB</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_ICECAT</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_ICECAT">BrowserDetection::BROWSER_ICECAT</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_ICEWEASEL</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_ICEWEASEL">BrowserDetection::BROWSER_ICEWEASEL</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_IE</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_IE">BrowserDetection::BROWSER_IE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_IE_MOBILE</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_IE_MOBILE">BrowserDetection::BROWSER_IE_MOBILE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_KONQUEROR</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_KONQUEROR">BrowserDetection::BROWSER_KONQUEROR</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_LYNX</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_LYNX">BrowserDetection::BROWSER_LYNX</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_MOZILLA</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_MOZILLA">BrowserDetection::BROWSER_MOZILLA</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_MSNTV</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_MSNTV">BrowserDetection::BROWSER_MSNTV</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_NETSCAPE</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_NETSCAPE">BrowserDetection::BROWSER_NETSCAPE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_NOKIA</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_NOKIA">BrowserDetection::BROWSER_NOKIA</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_OPERA</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_OPERA">BrowserDetection::BROWSER_OPERA</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_OPERA_MINI</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_OPERA_MINI">BrowserDetection::BROWSER_OPERA_MINI</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_OPERA_MOBILE</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_OPERA_MOBILE">BrowserDetection::BROWSER_OPERA_MOBILE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_PHOENIX</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_PHOENIX">BrowserDetection::BROWSER_PHOENIX</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_SAFARI</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_SAFARI">BrowserDetection::BROWSER_SAFARI</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_SAMSUNG</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_SAMSUNG">BrowserDetection::BROWSER_SAMSUNG</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_TABLET_OS</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_TABLET_OS">BrowserDetection::BROWSER_TABLET_OS</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_UC</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_UC">BrowserDetection::BROWSER_UC</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
							<dt><b>BROWSER_UNKNOWN</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constBROWSER_UNKNOWN">BrowserDetection::BROWSER_UNKNOWN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the Web browser.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="c"></a>
	<div>
		<h2>c</h2>
		<dl>
							<dt><b>checkBrowser</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowser">BrowserDetection::checkBrowser()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine what is the browser used by the user.</dd>
							<dt><b>checkBrowserAndroid</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserAndroid">BrowserDetection::checkBrowserAndroid()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is the Android browser (based on the WebKit layout engine and coupled with Chrome's  JavaScript engine) or not.</dd>
							<dt><b>checkBrowserBlackBerry</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserBlackBerry">BrowserDetection::checkBrowserBlackBerry()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is the BlackBerry browser or not.</dd>
							<dt><b>checkBrowserChrome</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserChrome">BrowserDetection::checkBrowserChrome()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Chrome or not.</dd>
							<dt><b>checkBrowserCustom</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserCustom">BrowserDetection::checkBrowserCustom()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is among the custom browser rules or not. Rules are checked in the order they were  added.</dd>
							<dt><b>checkBrowserEdge</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserEdge">BrowserDetection::checkBrowserEdge()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Edge or not.</dd>
							<dt><b>checkBrowserFirebird</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserFirebird">BrowserDetection::checkBrowserFirebird()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Firebird or not. Firebird was the name of Firefox from version 0.6 to 0.7.1.</dd>
							<dt><b>checkBrowserFirefox</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserFirefox">BrowserDetection::checkBrowserFirefox()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Firefox or not.</dd>
							<dt><b>checkBrowserIcab</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserIcab">BrowserDetection::checkBrowserIcab()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is iCab or not.</dd>
							<dt><b>checkBrowserIceCat</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserIceCat">BrowserDetection::checkBrowserIceCat()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is GNU IceCat (formerly known as GNU IceWeasel) or not.</dd>
							<dt><b>checkBrowserIceWeasel</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserIceWeasel">BrowserDetection::checkBrowserIceWeasel()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is GNU IceWeasel (now know as GNU IceCat) or not.</dd>
							<dt><b>checkBrowserInternetExplorer</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserInternetExplorer">BrowserDetection::checkBrowserInternetExplorer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Internet Explorer or not.</dd>
							<dt><b>checkBrowserKonqueror</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserKonqueror">BrowserDetection::checkBrowserKonqueror()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Konqueror or not.</dd>
							<dt><b>checkBrowserLynx</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserLynx">BrowserDetection::checkBrowserLynx()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Lynx or not. It is the oldest web browser currently in general use and development.</dd>
							<dt><b>checkBrowserMozilla</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserMozilla">BrowserDetection::checkBrowserMozilla()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Mozilla or not.</dd>
							<dt><b>checkBrowserMsnTv</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserMsnTv">BrowserDetection::checkBrowserMsnTv()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is MSN TV (formerly WebTV) or not.</dd>
							<dt><b>checkBrowserNetscape</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserNetscape">BrowserDetection::checkBrowserNetscape()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Netscape or not. Official support for this browser ended on March 1st, 2008.</dd>
							<dt><b>checkBrowserNokia</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserNokia">BrowserDetection::checkBrowserNokia()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is a Nokia browser or not.</dd>
							<dt><b>checkBrowserOpera</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserOpera">BrowserDetection::checkBrowserOpera()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Opera or not.</dd>
							<dt><b>checkBrowserPhoenix</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserPhoenix">BrowserDetection::checkBrowserPhoenix()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Phoenix or not. Phoenix was the name of Firefox from version 0.1 to 0.5.</dd>
							<dt><b>checkBrowserSafari</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserSafari">BrowserDetection::checkBrowserSafari()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is Safari or not.</dd>
							<dt><b>checkBrowserSamsung</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserSamsung">BrowserDetection::checkBrowserSamsung()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is the Samsung Internet browser or not.</dd>
							<dt><b>checkBrowserUAWithVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserUAWithVersion">BrowserDetection::checkBrowserUAWithVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Test the user agent for a specific browser that use a &quot;Version&quot; string (like Safari and Opera). The user agent  should look like: &quot;Version/1.0 Browser name/123.456&quot; or &quot;Browser name/123.456 Version/1.0&quot;.</dd>
							<dt><b>checkBrowserUC</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckBrowserUC">BrowserDetection::checkBrowserUC()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is UC Browser or not.</dd>
							<dt><b>checkPlatform</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckPlatform">BrowserDetection::checkPlatform()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine the user's platform.</dd>
							<dt><b>checkPlatformCustom</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckPlatformCustom">BrowserDetection::checkPlatformCustom()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the platform is among the custom platform rules or not. Rules are checked in the order they were  added.</dd>
							<dt><b>checkPlatformVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckPlatformVersion">BrowserDetection::checkPlatformVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine the user's platform version.</dd>
							<dt><b>checkRobot</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckRobot">BrowserDetection::checkRobot()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if it's a robot crawling the page and find it's name and version.</dd>
							<dt><b>checkRobotBingbot</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckRobotBingbot">BrowserDetection::checkRobotBingbot()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the robot is the Bingbot crawler or not.</dd>
							<dt><b>checkRobotCustom</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckRobotCustom">BrowserDetection::checkRobotCustom()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the robot is among the custom robot rules or not. Rules are checked in the order they were added.</dd>
							<dt><b>checkRobotGooglebot</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckRobotGooglebot">BrowserDetection::checkRobotGooglebot()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the robot is the Googlebot crawler or not.</dd>
							<dt><b>checkRobotMsnBot</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckRobotMsnBot">BrowserDetection::checkRobotMsnBot()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the robot is the MSNBot crawler or not. In October 2010 it was replaced by the Bingbot robot.</dd>
							<dt><b>checkRobotSlurp</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckRobotSlurp">BrowserDetection::checkRobotSlurp()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the robot is the Yahoo! Slurp crawler or not.</dd>
							<dt><b>checkRobotW3CValidator</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckRobotW3CValidator">BrowserDetection::checkRobotW3CValidator()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the robot is the W3C Validator or not.</dd>
							<dt><b>checkRobotYahooMultimedia</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckRobotYahooMultimedia">BrowserDetection::checkRobotYahooMultimedia()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the robot is the Yahoo! multimedia crawler or not.</dd>
							<dt><b>checkSimpleBrowserUA</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckSimpleBrowserUA">BrowserDetection::checkSimpleBrowserUA()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Test the user agent for a specific browser where the browser name is immediately followed by the version number.</dd>
							<dt><b>checkSimpleRobot</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcheckSimpleRobot">BrowserDetection::checkSimpleRobot()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Test the user agent for a specific robot where the robot name is immediately followed by the version number.</dd>
							<dt><b>cleanVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcleanVersion">BrowserDetection::cleanVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Clean a version string from unwanted characters.</dd>
							<dt><b>compareVersions</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcompareVersions">BrowserDetection::compareVersions()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Compare two version number strings.</dd>
							<dt><b>containString</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodcontainString">BrowserDetection::containString()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Find if one or more substring is contained in a string.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="d"></a>
	<div>
		<h2>d</h2>
		<dl>
							<dt><b>detect</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methoddetect">BrowserDetection::detect()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Detect the user environment from the details in the user agent string.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="f"></a>
	<div>
		<h2>f</h2>
		<dl>
							<dt><b>findAndGetVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodfindAndGetVersion">BrowserDetection::findAndGetVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Test the user agent for a specific browser and extract it's version.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="g"></a>
	<div>
		<h2>g</h2>
		<dl>
							<dt><b>getIECompatibilityView</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodgetIECompatibilityView">BrowserDetection::getIECompatibilityView()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the name and version of the browser emulated in the compatibility view mode (if any). Since Internet  Explorer 8, IE can be put in compatibility mode to make websites that were created for older browsers, especially  IE 6 and 7, look better in IE 8+ which renders web pages closer to the standards and thus differently from those  older versions of IE.</dd>
							<dt><b>getLibVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodgetLibVersion">BrowserDetection::getLibVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Return the BrowserDetection class version.</dd>
							<dt><b>getName</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodgetName">BrowserDetection::getName()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the browser. All of the return values are class constants. You can compare them like this:  $myBrowserInstance-&gt;getName() == BrowserDetection::BROWSER_FIREFOX.</dd>
							<dt><b>getPlatform</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodgetPlatform">BrowserDetection::getPlatform()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the platform family on which the browser is run on (such as Windows, Apple, etc.). All of  the return values are class constants. You can compare them like this:  $myBrowserInstance-&gt;getPlatform() == BrowserDetection::PLATFORM_ANDROID.</dd>
							<dt><b>getPlatformVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodgetPlatformVersion">BrowserDetection::getPlatformVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the platform version on which the browser is run on. It can be returned as a string number like 'NT 6.3' or  as a name like 'Windows 8.1'. When returning version string numbers for Windows NT OS families the number is  prefixed by 'NT ' to differentiate from older Windows 3.x &amp; 9x release. At the moment only the Windows and  Android operating systems are supported.</dd>
							<dt><b>getRobotName</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodgetRobotName">BrowserDetection::getRobotName()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the name of the robot. All of the return values are class constants. You can compare them like this:  $myBrowserInstance-&gt;getRobotName() == BrowserDetection::ROBOT_GOOGLEBOT.</dd>
							<dt><b>getRobotVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodgetRobotVersion">BrowserDetection::getRobotVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the version of the robot.</dd>
							<dt><b>getUserAgent</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodgetUserAgent">BrowserDetection::getUserAgent()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the user agent value used by the class to determine the browser details.</dd>
							<dt><b>getVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodgetVersion">BrowserDetection::getVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the version of the browser.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="i"></a>
	<div>
		<h2>i</h2>
		<dl>
							<dt><b>iOSVerToStr</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodiOSVerToStr">BrowserDetection::iOSVerToStr()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert the iOS version numbers to the operating system name. For instance '2.0' returns 'iPhone OS 2.0'.</dd>
							<dt><b>is64bitPlatform</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodis64bitPlatform">BrowserDetection::is64bitPlatform()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is executed from a 64-bit platform. Keep in mind that not all platforms/browsers report  this and the result may not always be accurate.</dd>
							<dt><b>isChromeFrame</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodisChromeFrame">BrowserDetection::isChromeFrame()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser runs Google Chrome Frame (it's a plug-in designed for Internet Explorer 6+ based on the  open-source Chromium project - it's like a Chrome browser within IE).</dd>
							<dt><b>isInIECompatibilityView</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodisInIECompatibilityView">BrowserDetection::isInIECompatibilityView()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is in compatibility view or not. Since Internet Explorer 8, IE can be put in  compatibility mode to make websites that were created for older browsers, especially IE 6 and 7, look better in  IE 8+ which renders web pages closer to the standards and thus differently from those older versions of IE.</dd>
							<dt><b>isMobile</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodisMobile">BrowserDetection::isMobile()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is from a mobile device or not.</dd>
							<dt><b>isRobot</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodisRobot">BrowserDetection::isRobot()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Determine if the browser is a robot (Googlebot, Bingbot, Yahoo! Slurp...) or not.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="m"></a>
	<div>
		<h2>m</h2>
		<dl>
							<dt><b>macVerToStr</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodmacVerToStr">BrowserDetection::macVerToStr()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert the macOS version numbers to the operating system name. For instance '10.7' returns 'Mac OS X Lion'.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="p"></a>
	<div>
		<h2>p</h2>
		<dl>
							<dt><b>parseInt</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodparseInt">BrowserDetection::parseInt()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Get the integer value of a string variable.</dd>
							<dt><b>PLATFORM_ANDROID</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_ANDROID">BrowserDetection::PLATFORM_ANDROID</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_BLACKBERRY</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_BLACKBERRY">BrowserDetection::PLATFORM_BLACKBERRY</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_CHROME_OS</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_CHROME_OS">BrowserDetection::PLATFORM_CHROME_OS</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_FREEBSD</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_FREEBSD">BrowserDetection::PLATFORM_FREEBSD</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_IOS</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_IOS">BrowserDetection::PLATFORM_IOS</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_LINUX</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_LINUX">BrowserDetection::PLATFORM_LINUX</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_MACINTOSH</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_MACINTOSH">BrowserDetection::PLATFORM_MACINTOSH</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_NETBSD</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_NETBSD">BrowserDetection::PLATFORM_NETBSD</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_NOKIA</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_NOKIA">BrowserDetection::PLATFORM_NOKIA</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_OPENBSD</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_OPENBSD">BrowserDetection::PLATFORM_OPENBSD</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_OPENSOLARIS</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_OPENSOLARIS">BrowserDetection::PLATFORM_OPENSOLARIS</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_SYMBIAN</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_SYMBIAN">BrowserDetection::PLATFORM_SYMBIAN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_UNKNOWN</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_UNKNOWN">BrowserDetection::PLATFORM_UNKNOWN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_VERSION_UNKNOWN</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_VERSION_UNKNOWN">BrowserDetection::PLATFORM_VERSION_UNKNOWN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_WINDOWS</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_WINDOWS">BrowserDetection::PLATFORM_WINDOWS</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_WINDOWS_CE</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_WINDOWS_CE">BrowserDetection::PLATFORM_WINDOWS_CE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
							<dt><b>PLATFORM_WINDOWS_PHONE</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constPLATFORM_WINDOWS_PHONE">BrowserDetection::PLATFORM_WINDOWS_PHONE</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the platform on which the Web browser runs.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="r"></a>
	<div>
		<h2>r</h2>
		<dl>
							<dt><b>removeCustomBrowserDetection</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodremoveCustomBrowserDetection">BrowserDetection::removeCustomBrowserDetection()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Remove support for a previously added Web browser.</dd>
							<dt><b>removeCustomPlatformDetection</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodremoveCustomPlatformDetection">BrowserDetection::removeCustomPlatformDetection()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Remove support for a previously added platform.</dd>
							<dt><b>removeCustomRobotDetection</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodremoveCustomRobotDetection">BrowserDetection::removeCustomRobotDetection()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Remove support for a previously added robot.</dd>
							<dt><b>reset</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodreset">BrowserDetection::reset()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Reset all the properties of the class.</dd>
							<dt><b>ROBOT_BINGBOT</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constROBOT_BINGBOT">BrowserDetection::ROBOT_BINGBOT</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the robot.</dd>
							<dt><b>ROBOT_GOOGLEBOT</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constROBOT_GOOGLEBOT">BrowserDetection::ROBOT_GOOGLEBOT</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the robot.</dd>
							<dt><b>ROBOT_MSNBOT</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constROBOT_MSNBOT">BrowserDetection::ROBOT_MSNBOT</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the robot.</dd>
							<dt><b>ROBOT_SLURP</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constROBOT_SLURP">BrowserDetection::ROBOT_SLURP</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the robot.</dd>
							<dt><b>ROBOT_UNKNOWN</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constROBOT_UNKNOWN">BrowserDetection::ROBOT_UNKNOWN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the robot.</dd>
							<dt><b>ROBOT_VERSION_UNKNOWN</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constROBOT_VERSION_UNKNOWN">BrowserDetection::ROBOT_VERSION_UNKNOWN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the robot.</dd>
							<dt><b>ROBOT_W3CVALIDATOR</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constROBOT_W3CVALIDATOR">BrowserDetection::ROBOT_W3CVALIDATOR</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the robot.</dd>
							<dt><b>ROBOT_YAHOO_MM</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constROBOT_YAHOO_MM">BrowserDetection::ROBOT_YAHOO_MM</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Constant for the name of the robot.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="s"></a>
	<div>
		<h2>s</h2>
		<dl>
							<dt><b>safariBuildToSafariVer</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsafariBuildToSafariVer">BrowserDetection::safariBuildToSafariVer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert a Safari build number to a Safari version number.</dd>
							<dt><b>set64bit</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodset64bit">BrowserDetection::set64bit()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set if the browser is executed from a 64-bit platform.</dd>
							<dt><b>setBrowser</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsetBrowser">BrowserDetection::setBrowser()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the browser.</dd>
							<dt><b>setMobile</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsetMobile">BrowserDetection::setMobile()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the browser to be from a mobile device or not.</dd>
							<dt><b>setPlatform</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsetPlatform">BrowserDetection::setPlatform()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the platform on which the browser is on.</dd>
							<dt><b>setPlatformVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsetPlatformVersion">BrowserDetection::setPlatformVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the platform version on which the browser is on.</dd>
							<dt><b>setRobot</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsetRobot">BrowserDetection::setRobot()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the browser to be a robot (crawler) or not.</dd>
							<dt><b>setRobotName</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsetRobotName">BrowserDetection::setRobotName()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the name of the robot.</dd>
							<dt><b>setRobotVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsetRobotVersion">BrowserDetection::setRobotVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the version of the robot.</dd>
							<dt><b>setUserAgent</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsetUserAgent">BrowserDetection::setUserAgent()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the user agent to use with the class.</dd>
							<dt><b>setVersion</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodsetVersion">BrowserDetection::setVersion()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Set the version of the browser.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="v"></a>
	<div>
		<h2>v</h2>
		<dl>
							<dt><b>VERSION_UNKNOWN</b></dt>
				<dd>in file BrowserDetection.php, class constant <a href="Browser_Detection/BrowserDetection.html#constVERSION_UNKNOWN">BrowserDetection::VERSION_UNKNOWN</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Version unknown constant.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
  <hr />
	<a name="w"></a>
	<div>
		<h2>w</h2>
		<dl>
							<dt><b>webKitBuildToSafariVer</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodwebKitBuildToSafariVer">BrowserDetection::webKitBuildToSafariVer()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert a WebKit build number to a Safari version number.</dd>
							<dt><b>windowsNTVerToStr</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodwindowsNTVerToStr">BrowserDetection::windowsNTVerToStr()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert the Windows NT family version numbers to the operating system name. For instance '5.1' returns  'Windows XP'.</dd>
							<dt><b>windowsVerToStr</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodwindowsVerToStr">BrowserDetection::windowsVerToStr()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Convert the Windows 3.x &amp; 9x family version numbers to the operating system name. For instance '4.10.1998'  returns 'Windows 98'.</dd>
							<dt><b>wordPos</b></dt>
				<dd>in file BrowserDetection.php, method <a href="Browser_Detection/BrowserDetection.html#methodwordPos">BrowserDetection::wordPos()</a><br>&nbsp;&nbsp;&nbsp;&nbsp;Find the position of the first occurrence of a word in a string.</dd>
					</dl>
	</div>
	<a href="elementindex_Browser_Detection.html#top">top</a><br>
        <div class="credit">
		    <hr />
		    Documentation generated on Tue, 20 Sep 2022 23:35:12 -0400 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>