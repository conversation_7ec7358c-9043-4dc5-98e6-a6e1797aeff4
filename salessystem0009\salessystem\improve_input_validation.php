<?php
/**
 * أداة تحسين Input Validation
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// معالجة طلب تحسين Input Validation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['improve_validation'])) {
    $files_updated = 0;
    $errors = [];
    
    try {
        // 1. إنشاء ملف Input Validation helper
        $validation_helper_content = '<?php
/**
 * مساعد تحسين Input Validation
 */

class InputValidator {
    
    /**
     * تنظيف النص العام
     */
    public static function sanitizeText($input, $max_length = 255) {
        if (empty($input)) {
            return "";
        }
        
        $input = trim($input);
        $input = strip_tags($input);
        $input = htmlspecialchars($input, ENT_QUOTES, "UTF-8");
        
        if ($max_length > 0 && strlen($input) > $max_length) {
            $input = substr($input, 0, $max_length);
        }
        
        return $input;
    }
    
    /**
     * تنظيف الأرقام
     */
    public static function sanitizeNumber($input, $type = "int") {
        if (empty($input)) {
            return 0;
        }
        
        switch ($type) {
            case "int":
                return intval($input);
            case "float":
                return floatval($input);
            case "decimal":
                return number_format(floatval($input), 2, ".", "");
            default:
                return intval($input);
        }
    }
    
    /**
     * تحقق من البريد الإلكتروني
     */
    public static function validateEmail($email) {
        if (empty($email)) {
            return false;
        }
        
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * تنظيف البريد الإلكتروني
     */
    public static function sanitizeEmail($email) {
        if (empty($email)) {
            return "";
        }
        
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);
        return self::validateEmail($email) ? $email : "";
    }
    
    /**
     * تحقق من رقم الهاتف
     */
    public static function validatePhone($phone) {
        if (empty($phone)) {
            return false;
        }
        
        // إزالة جميع الأحرف غير الرقمية
        $phone = preg_replace("/[^0-9]/", "", $phone);
        
        // التحقق من طول الرقم (8-15 رقم)
        return strlen($phone) >= 8 && strlen($phone) <= 15;
    }
    
    /**
     * تنظيف رقم الهاتف
     */
    public static function sanitizePhone($phone) {
        if (empty($phone)) {
            return "";
        }
        
        // إزالة جميع الأحرف غير الرقمية والعلامات المسموحة
        $phone = preg_replace("/[^0-9+\-\s()]/", "", $phone);
        $phone = trim($phone);
        
        return self::validatePhone($phone) ? $phone : "";
    }
    
    /**
     * تحقق من التاريخ
     */
    public static function validateDate($date, $format = "Y-m-d") {
        if (empty($date)) {
            return false;
        }
        
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }
    
    /**
     * تنظيف التاريخ
     */
    public static function sanitizeDate($date, $format = "Y-m-d") {
        if (empty($date)) {
            return "";
        }
        
        return self::validateDate($date, $format) ? $date : "";
    }
    
    /**
     * تحقق من URL
     */
    public static function validateURL($url) {
        if (empty($url)) {
            return false;
        }
        
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * تنظيف URL
     */
    public static function sanitizeURL($url) {
        if (empty($url)) {
            return "";
        }
        
        $url = filter_var($url, FILTER_SANITIZE_URL);
        return self::validateURL($url) ? $url : "";
    }
    
    /**
     * تحقق من قوة كلمة المرور
     */
    public static function validatePassword($password, $min_length = 6) {
        if (empty($password)) {
            return ["valid" => false, "message" => "كلمة المرور مطلوبة"];
        }
        
        if (strlen($password) < $min_length) {
            return ["valid" => false, "message" => "كلمة المرور يجب أن تكون $min_length أحرف على الأقل"];
        }
        
        // فحص وجود أحرف وأرقام
        if (!preg_match("/[a-zA-Z]/", $password)) {
            return ["valid" => false, "message" => "كلمة المرور يجب أن تحتوي على أحرف"];
        }
        
        if (!preg_match("/[0-9]/", $password)) {
            return ["valid" => false, "message" => "كلمة المرور يجب أن تحتوي على أرقام"];
        }
        
        return ["valid" => true, "message" => "كلمة المرور قوية"];
    }
    
    /**
     * تنظيف النص الطويل (textarea)
     */
    public static function sanitizeTextarea($input, $max_length = 1000) {
        if (empty($input)) {
            return "";
        }
        
        $input = trim($input);
        $input = strip_tags($input, "<br><p><strong><em><u>");
        $input = htmlspecialchars($input, ENT_QUOTES, "UTF-8");
        
        if ($max_length > 0 && strlen($input) > $max_length) {
            $input = substr($input, 0, $max_length);
        }
        
        return $input;
    }
    
    /**
     * تحقق من الملفات المرفوعة
     */
    public static function validateUploadedFile($file, $allowed_types = [], $max_size = 2097152) {
        if (!isset($file["tmp_name"]) || empty($file["tmp_name"])) {
            return ["valid" => false, "message" => "لم يتم رفع ملف"];
        }
        
        // فحص حجم الملف
        if ($file["size"] > $max_size) {
            $max_mb = $max_size / 1024 / 1024;
            return ["valid" => false, "message" => "حجم الملف كبير جداً. الحد الأقصى $max_mb MB"];
        }
        
        // فحص نوع الملف
        if (!empty($allowed_types)) {
            $file_type = mime_content_type($file["tmp_name"]);
            if (!in_array($file_type, $allowed_types)) {
                return ["valid" => false, "message" => "نوع الملف غير مسموح"];
            }
        }
        
        // فحص امتداد الملف
        $extension = strtolower(pathinfo($file["name"], PATHINFO_EXTENSION));
        $allowed_extensions = ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"];
        
        if (!in_array($extension, $allowed_extensions)) {
            return ["valid" => false, "message" => "امتداد الملف غير مسموح"];
        }
        
        return ["valid" => true, "message" => "الملف صحيح"];
    }
    
    /**
     * تنظيف مصفوفة من البيانات
     */
    public static function sanitizeArray($array, $rules = []) {
        $sanitized = [];
        
        foreach ($array as $key => $value) {
            $key = self::sanitizeText($key, 50);
            
            if (isset($rules[$key])) {
                $rule = $rules[$key];
                switch ($rule["type"]) {
                    case "text":
                        $sanitized[$key] = self::sanitizeText($value, $rule["max_length"] ?? 255);
                        break;
                    case "number":
                        $sanitized[$key] = self::sanitizeNumber($value, $rule["number_type"] ?? "int");
                        break;
                    case "email":
                        $sanitized[$key] = self::sanitizeEmail($value);
                        break;
                    case "phone":
                        $sanitized[$key] = self::sanitizePhone($value);
                        break;
                    case "date":
                        $sanitized[$key] = self::sanitizeDate($value, $rule["format"] ?? "Y-m-d");
                        break;
                    case "url":
                        $sanitized[$key] = self::sanitizeURL($value);
                        break;
                    case "textarea":
                        $sanitized[$key] = self::sanitizeTextarea($value, $rule["max_length"] ?? 1000);
                        break;
                    default:
                        $sanitized[$key] = self::sanitizeText($value);
                }
            } else {
                $sanitized[$key] = self::sanitizeText($value);
            }
        }
        
        return $sanitized;
    }
    
    /**
     * تسجيل محاولة إدخال مشبوهة
     */
    public static function logSuspiciousInput($input, $reason) {
        $log_entry = date("Y-m-d H:i:s") . " - " . $_SERVER["REMOTE_ADDR"] . " - Suspicious input: " . $reason . " - " . substr($input, 0, 100) . PHP_EOL;
        
        $log_dir = __DIR__ . "/../logs";
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
        
        file_put_contents($log_dir . "/suspicious_input.log", $log_entry, FILE_APPEND | LOCK_EX);
    }
}
';
        
        $validation_helper_file = __DIR__ . '/includes/input_validator.php';
        if (file_put_contents($validation_helper_file, $validation_helper_content)) {
            $files_updated++;
        } else {
            $errors[] = "فشل في إنشاء ملف input_validator.php";
        }
        
        // 2. قائمة الملفات التي تحتاج تحسين
        $files_to_improve = [
            'add_customer.php',
            'add_sale.php',
            'add_purchase.php',
            'edit_customer.php',
            'edit_sale.php',
            'edit_purchase.php',
            'ajax_handler.php',
            'login.php',
            'register.php'
        ];
        
        foreach ($files_to_improve as $file) {
            $filepath = __DIR__ . '/' . $file;
            if (file_exists($filepath)) {
                $content = file_get_contents($filepath);
                $original_content = $content;
                
                // إضافة require للـ input validator
                if (strpos($content, 'input_validator.php') === false) {
                    $content = str_replace(
                        'require_once __DIR__ . \'/includes/functions.php\';',
                        'require_once __DIR__ . \'/includes/functions.php\';' . PHP_EOL . 'require_once __DIR__ . \'/includes/input_validator.php\';',
                        $content
                    );
                }
                
                // استبدال trim() بـ InputValidator::sanitizeText()
                $content = preg_replace(
                    '/trim\(\$_POST\[([\'"][^\'\"]+[\'"])\]\)/',
                    'InputValidator::sanitizeText($_POST[$1])',
                    $content
                );
                
                // استبدال intval() بـ InputValidator::sanitizeNumber()
                $content = preg_replace(
                    '/intval\(\$_POST\[([\'"][^\'\"]+[\'"])\]\)/',
                    'InputValidator::sanitizeNumber($_POST[$1], "int")',
                    $content
                );
                
                // استبدال floatval() بـ InputValidator::sanitizeNumber()
                $content = preg_replace(
                    '/floatval\(\$_POST\[([\'"][^\'\"]+[\'"])\]\)/',
                    'InputValidator::sanitizeNumber($_POST[$1], "float")',
                    $content
                );
                
                // حفظ الملف إذا تم تعديله
                if ($content !== $original_content) {
                    if (file_put_contents($filepath, $content)) {
                        $files_updated++;
                    } else {
                        $errors[] = "فشل في تحديث ملف $file";
                    }
                }
            }
        }
        
        if ($files_updated > 0) {
            $_SESSION['success'] = "تم تحديث $files_updated ملف بتحسينات Input Validation";
        } else {
            $_SESSION['info'] = "لا توجد ملفات تحتاج لتحديث";
        }
        
        if (!empty($errors)) {
            $_SESSION['warning'] = "بعض الأخطاء: " . implode(', ', $errors);
        }
        
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ أثناء تحسين Input Validation: " . $e->getMessage();
    }
    
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-filter"></i>
                        تحسين Input Validation
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> حول تحسين Input Validation</h6>
                        <p>Input Validation هو عملية التحقق من صحة وتنظيف البيانات المدخلة من المستخدمين لمنع الهجمات الأمنية.</p>
                        <p>هذه الأداة ستقوم بإضافة نظام تحقق وتنظيف شامل لجميع البيانات المدخلة.</p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">أنواع التحقق المضافة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check text-success"></i> تنظيف النصوص</li>
                                        <li><i class="fas fa-check text-success"></i> تحقق الأرقام</li>
                                        <li><i class="fas fa-check text-success"></i> تحقق البريد الإلكتروني</li>
                                        <li><i class="fas fa-check text-success"></i> تحقق أرقام الهاتف</li>
                                        <li><i class="fas fa-check text-success"></i> تحقق التواريخ</li>
                                        <li><i class="fas fa-check text-success"></i> تحقق كلمات المرور</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">الحماية المضافة</h6>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-shield-alt text-success"></i> منع SQL Injection</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> منع XSS</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> تنظيف HTML</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> تحديد طول النص</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> تسجيل المحاولات المشبوهة</li>
                                        <li><i class="fas fa-shield-alt text-success"></i> تحقق الملفات المرفوعة</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" onsubmit="return confirm('هل أنت متأكد من تحسين Input Validation؟')">
                        <div class="text-center">
                            <button type="submit" name="improve_validation" class="btn btn-info btn-lg">
                                <i class="fas fa-filter"></i> تحسين Input Validation
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4">
                        <h6>أمثلة على التحسينات:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>قبل التحسين</th>
                                        <th>بعد التحسين</th>
                                        <th>الفائدة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><code>trim($_POST['name'])</code></td>
                                        <td><code>InputValidator::sanitizeText($_POST['name'])</code></td>
                                        <td>تنظيف شامل + حماية XSS</td>
                                    </tr>
                                    <tr>
                                        <td><code>intval($_POST['id'])</code></td>
                                        <td><code>InputValidator::sanitizeNumber($_POST['id'])</code></td>
                                        <td>تحقق أفضل من الأرقام</td>
                                    </tr>
                                    <tr>
                                        <td><code>$_POST['email']</code></td>
                                        <td><code>InputValidator::sanitizeEmail($_POST['email'])</code></td>
                                        <td>تحقق صحة البريد الإلكتروني</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
