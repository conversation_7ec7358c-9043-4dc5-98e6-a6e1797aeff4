##  ------------------------------------------------------------------------  ##
##        Colors definition (from "Color Bash Prompt" HowTo)                  ##
##  ------------------------------------------------------------------------  ##

##  Normal Colors  ##
Black=[0;1;30m
Blue=[0;1;34m
Cyan=[0;1;36m
Gold=[38;5;178m
Gray=[0;1;90m
Green=[0;1;32m
Orange=[38;5;178m
Purple=[0;1;35m
Red=[0;1;31m
White=[0;1;37m
Yellow=[0;1;33m

##  Bold  ##
BBlack=[1;1;30m
BBlue=[1;1;34m
<PERSON>yan=[1;1;36m
BGray=[1;1;90m
BGreen=[1;1;32m
BP<PERSON>ple=[1;1;35m
BRed=[1;1;31m
BWhite=[1;1;37m
BY<PERSON>w=[1;1;33m

##  Background  ##
On_Black=[40m
On_Blue=[44m
On_Cyan=[46m
On_Gray=[90m
On_Green=[42m
On_Purple=[45m
On_Red=[41m
On_White=[47m
On_Yellow=[43m

##  Color Reset  ##
NC=[m

##  Helpers  ##
ALERT=$(BYellow)$(On_Red)

##  ------------------------------------------------------------------------  ##
