<?php
// تضمين مكتبة PHPMailer
//require 'PHPMailer/PHPMailerAutoload.php';
require __DIR__.'/PHPMailer/src/Exception.php';
require __DIR__.'/PHPMailer/src/PHPMailer.php';
require __DIR__.'/PHPMailer/src/SMTP.php';

# use "use" after include or require

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use PHPMailer\PHPMailer\SMTP;


if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = $_POST["email"];

    // الاتصال بقاعدة البيانات والتحقق من وجود البريد الإلكتروني
    // يجب تغيير معلومات الاتصال بقاعدة البيانات والاستعلام حسب الحاجة
    $servername = "localhost";
    $username = "root";
    $password = "";
    $dbname = "signupai";

    $conn = new mysqli($servername, $username, $password, $dbname);

    if ($conn->connect_error) {
        die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    // إنشاء رمز عشوائي للرابط
    $token = bin2hex(random_bytes(50));

    // تحديد مدة صلاحية الرابط (مثلاً 1 ساعة من الآن)
    $expiration = date('Y-m-d H:i:s', strtotime('+1 hour'));

    // حفظ معلومات إعادة تعيين كلمة المرور في قاعدة البيانات
    $stmt = $conn->prepare("INSERT INTO password_reset (email, token, expiration) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $email, $token, $expiration);
    $stmt->execute();
    $stmt->close();

    // إرسال البريد الإلكتروني مع الرابط
    $mail = new PHPMailer;

    $mail->isSMTP();
    $mail->Host = 'smtp.hostinger.com'; // يجب تعديله إلى مضيف البريد الصحيح
    $mail->SMTPAuth = true;
    $mail->Username = '<EMAIL>'; // يجب تعديله إلى اسم مستخدم البريد الصحيح
    $mail->Password = '5dN53znd@'; // يجب تعديله إلى كلمة المرور الصحيحة
    $mail->SMTPSecure = 'tls';
    $mail->Port = 465;

    $mail->setFrom('<EMAIL>', 'Your Name');
    $mail->addAddress($email);
    $mail->Subject = 'إعادة تعيين كلمة المرور';
    $mail->Body = 'يرجى النقر على الرابط التالي لإعادة تعيين كلمة المرور: ' . 
                  'http://example.com/reset_password.php?token=' . $token;

    if ($mail->send()) {
        header("Location: reset_password_success.html");
    } else {
        echo 'خطأ في إرسال البريد الإلكتروني: ' . $mail->ErrorInfo;
    }

    $conn->close();
}
?>
