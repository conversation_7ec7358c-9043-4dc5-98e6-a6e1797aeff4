{"name": "cv-application", "version": "0.1.0", "private": true, "homepage": "http://michalosman.github.io/cv-application", "dependencies": {"@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^11.2.5", "@testing-library/user-event": "^12.8.0", "react": "^17.0.1", "react-dom": "^17.0.1", "react-icons": "^4.2.0", "react-scripts": "4.0.3", "react-to-print": "^2.12.3", "styled-components": "^5.2.1", "uuid": "^8.3.2", "web-vitals": "^1.1.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "predeploy": "npm run build", "deploy": "gh-pages -d build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"gh-pages": "^3.1.0"}}