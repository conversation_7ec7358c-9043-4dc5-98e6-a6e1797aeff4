<?php
/**
 * ملف جلب بيانات المبيعات الشهرية
 *
 * يستخدم هذا الملف لجلب بيانات المبيعات الشهرية وإرجاعها بتنسيق JSON
 * يمكن تصفية البيانات حسب الفترة الزمنية والعميل
 * يتم استخدام هذا الملف من قبل صفحة التقارير لعرض الرسم البياني للمبيعات الشهرية
 */

// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once 'config/init.php';
redirectIfNotLoggedIn();

header('Content-Type: application/json');

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    echo json_encode(['error' => 'Database connection failed']);
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

// تحديد الفترة الزمنية
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-01-01'); // بداية السنة الحالية
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-12-31'); // نهاية السنة الحالية
$customer_id = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;

// بناء شروط الاستعلام
$date_condition = "WHERE date BETWEEN '$start_date' AND '$end_date'";
$customer_condition = $customer_id > 0 ? "AND customer_id = $customer_id" : "";

// تحديد ما إذا كانت الفترة الزمنية تمتد لأكثر من سنة
$start_year = date('Y', strtotime($start_date));
$end_year = date('Y', strtotime($end_date));
$group_by = ($end_year - $start_year) > 0 ? "YEAR(date), MONTH(date)" : "MONTH(date)";
$select_year = ($end_year - $start_year) > 0 ? "YEAR(date) as year," : "";

// جلب بيانات المبيعات الشهرية
$query = "
    SELECT
        $select_year
        MONTH(date) as month,
        SUM(total_amount) as total
    FROM sales
    $date_condition
    $customer_condition
    GROUP BY $group_by
    ORDER BY " . (($end_year - $start_year) > 0 ? "year, month" : "month");

$result = $db->query($query);

// التحقق من وجود أخطاء في الاستعلام
if (!$result) {
    echo json_encode([
        'error' => 'Database query error: ' . $db->error,
        'query' => $query
    ]);
    exit();
}

// تحديد أسماء الأشهر حسب اللغة المحددة
$months_ar = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
$months_en = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
$months = getCurrentLang() === 'ar' ? $months_ar : $months_en;

// إذا كانت الفترة الزمنية تمتد لأكثر من سنة، نستخدم تنسيق مختلف للتسميات
if ($end_year - $start_year > 0) {
    $labels = [];
    $values = [];

    while ($row = $result->fetch_assoc()) {
        $month_name = $months[$row['month'] - 1];
        $labels[] = $month_name . ' ' . $row['year'];
        $values[] = floatval($row['total']);
    }

    $data = [
        'labels' => $labels,
        'values' => $values
    ];
} else {
    // إذا كانت الفترة الزمنية ضمن سنة واحدة، نستخدم مصفوفة ثابتة للأشهر
    $data = [
        'labels' => $months,
        'values' => array_fill(0, 12, 0)
    ];

    while ($row = $result->fetch_assoc()) {
        $data['values'][$row['month'] - 1] = floatval($row['total']);
    }
}

echo json_encode($data);
?>