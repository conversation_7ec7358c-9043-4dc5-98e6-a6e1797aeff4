<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Input with Label Update</title>
</head>
<body>

<div class="container">
  <input type="text" id="inputField" placeholder="أدخل قيمة">
  <button onclick="updateLabel()">تحديث العنصر</button>
  <label id="outputLabel">
    <?php
    // تحقق من وجود القيمة في الوصلة وتحديث الـ label
    if (isset($_GET['inputValue'])) {
      echo "القيمة المدخلة: " . htmlspecialchars($_GET['inputValue']);
    }
    ?>
  </label>
</div>

<script>
  function updateLabel() {
    // الحصول على قيمة المدخل
    var inputValue = document.getElementById("inputField").value;

    // تحديث الوصلة لتضمين القيمة المدخلة
    window.location.href = "page.php?inputValue=" + encodeURIComponent(inputValue);
  }
</script>

</body>
</html>
