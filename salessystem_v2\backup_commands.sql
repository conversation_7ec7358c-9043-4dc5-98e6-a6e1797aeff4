-- ===================================================================
-- أوامر النسخ الاحتياطي والاستعادة - salessystem_v2
-- تاريخ الإنشاء: 2024-12-19
-- ===================================================================

-- ===================================================================
-- أوامر إنشاء قواعد البيانات
-- ===================================================================

-- إنشاء قاعدة البيانات الرئيسية
CREATE DATABASE IF NOT EXISTS `u193708811_system_main` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- إنشاء قاعدة البيانات التشغيلية
CREATE DATABASE IF NOT EXISTS `u193708811_operations` 
CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- ===================================================================
-- أوامر إنشاء المستخدمين
-- ===================================================================

-- إنشاء مستخدم قاعدة البيانات الرئيسية
CREATE USER IF NOT EXISTS 'sales01'@'localhost' IDENTIFIED BY 'dNz35nd5@';
GRANT ALL PRIVILEGES ON `u193708811_system_main`.* TO 'sales01'@'localhost';

-- إنشاء مستخدم قاعدة البيانات التشغيلية
CREATE USER IF NOT EXISTS 'sales02'@'localhost' IDENTIFIED BY 'dNz35nd5@';
GRANT ALL PRIVILEGES ON `u193708811_operations`.* TO 'sales02'@'localhost';

-- تطبيق الصلاحيات
FLUSH PRIVILEGES;

-- ===================================================================
-- أوامر النسخ الاحتياطي (تشغل من سطر الأوامر)
-- ===================================================================

/*
-- نسخ احتياطي كامل لقاعدة البيانات الرئيسية
mysqldump -u sales01 -p'dNz35nd5@' --single-transaction --routines --triggers u193708811_system_main > backup_main_$(date +%Y%m%d_%H%M%S).sql

-- نسخ احتياطي كامل لقاعدة البيانات التشغيلية
mysqldump -u sales02 -p'dNz35nd5@' --single-transaction --routines --triggers u193708811_operations > backup_operations_$(date +%Y%m%d_%H%M%S).sql

-- نسخ احتياطي لجداول مستخدم محدد فقط
mysqldump -u sales02 -p'dNz35nd5@' --single-transaction u193708811_operations testuser_customers testuser_products testuser_sales testuser_purchases testuser_sale_items testuser_purchase_items > backup_testuser_$(date +%Y%m%d_%H%M%S).sql

-- نسخ احتياطي مضغوط
mysqldump -u sales01 -p'dNz35nd5@' --single-transaction --routines --triggers u193708811_system_main | gzip > backup_main_$(date +%Y%m%d_%H%M%S).sql.gz

-- نسخ احتياطي للبيانات فقط (بدون هيكل الجداول)
mysqldump -u sales02 -p'dNz35nd5@' --no-create-info --single-transaction u193708811_operations > backup_data_only_$(date +%Y%m%d_%H%M%S).sql

-- نسخ احتياطي للهيكل فقط (بدون البيانات)
mysqldump -u sales02 -p'dNz35nd5@' --no-data --routines --triggers u193708811_operations > backup_structure_only_$(date +%Y%m%d_%H%M%S).sql
*/

-- ===================================================================
-- أوامر الاستعادة (تشغل من سطر الأوامر)
-- ===================================================================

/*
-- استعادة قاعدة البيانات الرئيسية
mysql -u sales01 -p'dNz35nd5@' u193708811_system_main < backup_main_20241219_120000.sql

-- استعادة قاعدة البيانات التشغيلية
mysql -u sales02 -p'dNz35nd5@' u193708811_operations < backup_operations_20241219_120000.sql

-- استعادة من ملف مضغوط
gunzip < backup_main_20241219_120000.sql.gz | mysql -u sales01 -p'dNz35nd5@' u193708811_system_main

-- استعادة جداول مستخدم محدد
mysql -u sales02 -p'dNz35nd5@' u193708811_operations < backup_testuser_20241219_120000.sql
*/

-- ===================================================================
-- استعلامات صيانة مفيدة
-- ===================================================================

-- عرض جميع قواعد البيانات
SHOW DATABASES;

-- عرض جميع الجداول في قاعدة البيانات التشغيلية
USE u193708811_operations;
SHOW TABLES;

-- عرض جداول مستخدم محدد
SHOW TABLES LIKE 'testuser_%';

-- عرض حجم قواعد البيانات
SELECT 
    table_schema AS 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema IN ('u193708811_system_main', 'u193708811_operations')
GROUP BY table_schema;

-- عرض حجم الجداول
SELECT 
    table_name AS 'Table',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.TABLES 
WHERE table_schema = 'u193708811_operations'
ORDER BY (data_length + index_length) DESC;

-- عرض عدد السجلات في كل جدول
SELECT 
    table_name AS 'Table',
    table_rows AS 'Rows'
FROM information_schema.TABLES 
WHERE table_schema = 'u193708811_operations'
ORDER BY table_rows DESC;

-- فحص سلامة الجداول
CHECK TABLE u193708811_operations.testuser_customers;
CHECK TABLE u193708811_operations.testuser_products;
CHECK TABLE u193708811_operations.testuser_sales;
CHECK TABLE u193708811_operations.testuser_purchases;

-- إصلاح الجداول (إذا لزم الأمر)
REPAIR TABLE u193708811_operations.testuser_customers;
REPAIR TABLE u193708811_operations.testuser_products;

-- تحسين الجداول
OPTIMIZE TABLE u193708811_operations.testuser_customers;
OPTIMIZE TABLE u193708811_operations.testuser_products;
OPTIMIZE TABLE u193708811_operations.testuser_sales;
OPTIMIZE TABLE u193708811_operations.testuser_purchases;

-- ===================================================================
-- استعلامات إحصائية مفيدة
-- ===================================================================

-- إحصائيات المستخدمين
USE u193708811_system_main;
SELECT 
    status,
    COUNT(*) as count
FROM users 
GROUP BY status;

-- إحصائيات المدراء
SELECT 
    role,
    status,
    COUNT(*) as count
FROM admins 
GROUP BY role, status;

-- إحصائيات النشاطات
SELECT 
    action,
    COUNT(*) as count
FROM activity_log 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY action
ORDER BY count DESC;

-- إحصائيات المبيعات لجميع المستخدمين
USE u193708811_operations;
SELECT 
    SUBSTRING_INDEX(table_name, '_', 1) as username,
    COUNT(*) as sales_count
FROM information_schema.tables t
JOIN information_schema.table_constraints tc ON t.table_name = tc.table_name
WHERE t.table_schema = 'u193708811_operations' 
AND t.table_name LIKE '%_sales'
GROUP BY username;

-- ===================================================================
-- أوامر تنظيف وصيانة دورية
-- ===================================================================

-- حذف سجلات النشاطات القديمة (أكثر من 90 يوم)
USE u193708811_system_main;
DELETE FROM activity_log 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- حذف جلسات المستخدمين المنتهية الصلاحية
-- (إذا كان هناك جدول sessions)

-- تحديث إحصائيات الجداول
USE u193708811_operations;
ANALYZE TABLE testuser_customers;
ANALYZE TABLE testuser_products;
ANALYZE TABLE testuser_sales;
ANALYZE TABLE testuser_purchases;

-- ===================================================================
-- ملاحظات مهمة للصيانة
-- ===================================================================

/*
1. النسخ الاحتياطي:
   - يُنصح بعمل نسخ احتياطي يومي
   - احتفظ بنسخ احتياطية لمدة 30 يوم على الأقل
   - اختبر استعادة النسخ الاحتياطية دورياً

2. الصيانة:
   - شغل OPTIMIZE TABLE شهرياً
   - راقب حجم قواعد البيانات
   - احذف السجلات القديمة دورياً

3. الأمان:
   - غير كلمات المرور الافتراضية
   - استخدم اتصالات SSL
   - راقب سجلات النشاطات

4. الأداء:
   - راقب الاستعلامات البطيئة
   - أضف فهارس حسب الحاجة
   - راقب استخدام الذاكرة والمعالج

5. النسخ الاحتياطي التلقائي:
   - استخدم cron jobs للنسخ الاحتياطي التلقائي
   - مثال: 0 2 * * * /path/to/backup_script.sh
*/
