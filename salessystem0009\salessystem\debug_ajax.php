<?php
/**
 * ملف لتشخيص مشاكل AJAX
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

// بدء الجلسة
session_start();

// تعيين نوع المحتوى
header('Content-Type: application/json');

// تسجيل جميع البيانات المرسلة
$debug_info = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'post_data' => $_POST,
    'session_data' => $_SESSION,
    'user_logged_in' => isset($_SESSION['user_id']),
    'files_exist' => [
        'config/init.php' => file_exists(__DIR__ . '/config/init.php'),
        'includes/functions.php' => file_exists(__DIR__ . '/includes/functions.php')
    ]
];

// محاولة تحميل الملفات المطلوبة
try {
    require_once __DIR__ . '/config/init.php';
    $debug_info['init_loaded'] = true;
} catch (Exception $e) {
    $debug_info['init_error'] = $e->getMessage();
    echo json_encode(['success' => false, 'debug' => $debug_info]);
    exit();
}

try {
    require_once __DIR__ . '/includes/functions.php';
    $debug_info['functions_loaded'] = true;
} catch (Exception $e) {
    $debug_info['functions_error'] = $e->getMessage();
    echo json_encode(['success' => false, 'debug' => $debug_info]);
    exit();
}

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    $debug_info['error'] = 'User not logged in';
    echo json_encode(['success' => false, 'message' => 'غير مسموح - يجب تسجيل الدخول', 'debug' => $debug_info]);
    exit();
}

// محاولة الاتصال بقاعدة البيانات
try {
    $db = getCurrentUserDB();
    if ($db === null) {
        $debug_info['db_error'] = 'Database connection is null';
        echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات', 'debug' => $debug_info]);
        exit();
    }
    
    if ($db->connect_error) {
        $debug_info['db_error'] = $db->connect_error;
        echo json_encode(['success' => false, 'message' => 'خطأ في الاتصال بقاعدة البيانات', 'debug' => $debug_info]);
        exit();
    }
    
    $debug_info['db_connected'] = true;
} catch (Exception $e) {
    $debug_info['db_exception'] = $e->getMessage();
    echo json_encode(['success' => false, 'message' => 'استثناء في قاعدة البيانات', 'debug' => $debug_info]);
    exit();
}

// التحقق من وجود جدول العملاء
try {
    $check_table = $db->query("SHOW TABLES LIKE 'customers'");
    $debug_info['customers_table_exists'] = ($check_table && $check_table->num_rows > 0);
    
    if ($debug_info['customers_table_exists']) {
        // فحص بنية الجدول
        $structure = $db->query("DESCRIBE customers");
        $columns = [];
        while ($row = $structure->fetch_assoc()) {
            $columns[] = $row['Field'];
        }
        $debug_info['table_columns'] = $columns;
    }
} catch (Exception $e) {
    $debug_info['table_check_error'] = $e->getMessage();
}

// التحقق من نوع الطلب والإجراء
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $debug_info['error'] = 'Invalid request method';
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة', 'debug' => $debug_info]);
    exit();
}

if (!isset($_POST['action'])) {
    $debug_info['error'] = 'No action specified';
    echo json_encode(['success' => false, 'message' => 'الإجراء المطلوب غير محدد', 'debug' => $debug_info]);
    exit();
}

$action = $_POST['action'];
$debug_info['action'] = $action;

// معالجة إضافة العميل
if ($action === 'add_customer') {
    try {
        // التحقق من البيانات المطلوبة
        if (empty($_POST['name'])) {
            $debug_info['error'] = 'Name is required';
            echo json_encode(['success' => false, 'message' => 'اسم العميل مطلوب', 'debug' => $debug_info]);
            exit();
        }
        
        $name = trim($_POST['name']);
        $phone = trim($_POST['phone'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $address = trim($_POST['address'] ?? '');
        
        $debug_info['customer_data'] = [
            'name' => $name,
            'phone' => $phone,
            'email' => $email,
            'address' => $address
        ];
        
        // التحقق من صحة البريد الإلكتروني
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $debug_info['error'] = 'Invalid email format';
            echo json_encode(['success' => false, 'message' => 'البريد الإلكتروني غير صحيح', 'debug' => $debug_info]);
            exit();
        }
        
        // التحقق من عدم وجود عميل بنفس الاسم
        $check_stmt = $db->prepare("SELECT id FROM customers WHERE name = ?");
        if (!$check_stmt) {
            $debug_info['error'] = 'Failed to prepare check statement: ' . $db->error;
            echo json_encode(['success' => false, 'message' => 'خطأ في إعداد الاستعلام', 'debug' => $debug_info]);
            exit();
        }
        
        $check_stmt->bind_param("s", $name);
        $check_stmt->execute();
        $result = $check_stmt->get_result();
        
        if ($result->num_rows > 0) {
            $debug_info['error'] = 'Customer name already exists';
            echo json_encode(['success' => false, 'message' => 'يوجد عميل بنفس الاسم بالفعل', 'debug' => $debug_info]);
            exit();
        }
        
        // إدراج العميل الجديد
        $stmt = $db->prepare("INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)");
        if (!$stmt) {
            $debug_info['error'] = 'Failed to prepare insert statement: ' . $db->error;
            echo json_encode(['success' => false, 'message' => 'خطأ في إعداد استعلام الإدراج', 'debug' => $debug_info]);
            exit();
        }
        
        $stmt->bind_param("ssss", $name, $phone, $email, $address);
        
        if ($stmt->execute()) {
            $customer_id = $stmt->insert_id;
            $debug_info['success'] = true;
            $debug_info['customer_id'] = $customer_id;
            
            echo json_encode([
                'success' => true,
                'customer_id' => $customer_id,
                'message' => 'تم إضافة العميل بنجاح',
                'debug' => $debug_info
            ]);
        } else {
            $debug_info['error'] = 'Execute failed: ' . $stmt->error;
            echo json_encode([
                'success' => false,
                'message' => 'حدث خطأ أثناء إضافة العميل: ' . $stmt->error,
                'debug' => $debug_info
            ]);
        }
        
        $stmt->close();
        
    } catch (Exception $e) {
        $debug_info['exception'] = $e->getMessage();
        $debug_info['trace'] = $e->getTraceAsString();
        echo json_encode([
            'success' => false,
            'message' => 'حدث استثناء: ' . $e->getMessage(),
            'debug' => $debug_info
        ]);
    }
} else {
    $debug_info['error'] = 'Unknown action: ' . $action;
    echo json_encode(['success' => false, 'message' => 'إجراء غير معروف', 'debug' => $debug_info]);
}
?>
