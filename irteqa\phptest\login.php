
    <style>
        .bdyf{
            padding:0;
            font-family: sans-sarif;
            background:rgb(222 225 230);
            background-size:cover;
            margin: 5px;
        }
        form{
            box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
            position:absolute;
            top:50%;
            left:50%;
            transform:translate(-50%,-50%);
            width: 350px;
            height: 420px;
            background:rgba(0,0,0,.5);
            border-radius:15px;
            box-sizing:border-box;
            margin: 5px;
        }
        img[alt="user"]{
                position:absolute;
                top:-60px;
                left:100px;
            }
            input[type="text"],[type="password"]{
                font-size: 20px;
                position:absolute;
                outline:none;
                border:none;
                width:250px;
                height:35px;
                border-bottom:2px solid #ffff;
                background:none;
                padding:5px;
                color:#fff;
                text-align:right;

                
            }
            input[type="text"]{
                  top:140px;
                  left:50px
             
            
            }
            input[type="password"]{
                top:210px;
                left:50px;
            }
            ::placeholder{
                color:#fff;

            }
            button[type="submit"]{
                position:absolute;
                top:305px;
                left:110px;
                width:130px;
                height:35px;
                background:#ffffff;
                border:none;
                outline: none;
                border-radius:15px;
                font-size:20px;

            }
            button[type="submit"]:hover{
                background:#fff;
                color:#0077ff;
            }

    </style>
<div class="bdyf">
    <form method="POST">
        <img src="img/offbtn.png" alt="user"style="width:150px;">
        <input name="uname" type="text" placeholder="اسم المستخدم" style="color:#cbcbcb" required>
        <input name="pword" type="password" placeholder="كلمة المرور" required>
        <button name="login" type="submit">الدخول</button>
    </form>
    <?php 
    include('lgcon.php');
    if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST['uname'];
    $password = $_POST['pword'];

    if ($conn->connect_error) {
        die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    $query = "SELECT * FROM users WHERE username = '$username'";
    $result = $conn->query($query);

    if ($result->num_rows == 1) {
        $row = $result->fetch_assoc();

        if (password_verify($password, $row["pword"])) {
            // تم تسجيل الدخول بنجاح
            $_SESSION["uname"] = $username; // تخزين اسم المستخدم في الجلسة
            header("Location: home.php");
            exit; // توقف تنفيذ السكريبت بعد التوجيه
        } else {
            // كلمة المرور غير صحيحة
            echo "كلمة المرور غير صحيحة.";
        }
    } else {
        // اسم المستخدم غير موجود
        echo "اسم المستخدم غير موجود.";
    }

    $conn->close();




  /* 
    $sql = "INSERT INTO userst(uname,pword) VALUES ('$username','$password')";
if (isset($_POST['login']));
{

if(empty($username)){
  echo '';

}
elseif(mysqli_query($conn, $sql)){ 

    }else{
      echo 'Error: ' . mysqli_error($conn);    }
    
}*/

}
   /* if(isset($_POST['login'])){
        $getusername=$_POST['username'];
        $getpassword=$_POST['password'];
        if($username === $getusername && $password === $getpassword){
            echo "تم تسجيل الدخول";

        }else{
            echo "كلمة مرور او اسم المستخدم غير صحيح";
        }
    }*/
    ?>
</div>