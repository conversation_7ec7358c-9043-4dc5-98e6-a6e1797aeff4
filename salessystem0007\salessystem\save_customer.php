<?php
/**
 * حفظ وتعديل العملاء والموردين
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من تسجيل الدخول
redirectIfNotLoggedIn();

// التحقق من طريقة الإرسال
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "طريقة إرسال غير صحيحة";
    header("Location: customers.php");
    exit();
}

// الحصول على اتصال قاعدة البيانات
$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات";
    header("Location: customers.php");
    exit();
}

try {
    // استلام البيانات
    $customer_id = !empty($_POST['customer_id']) ? intval($_POST['customer_id']) : null;
    $name = trim($_POST['name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $tax_number = trim($_POST['tax_number'] ?? '');
    $address = trim($_POST['address'] ?? '');
    $customer_type = $_POST['customer_type'] ?? 'customer';

    // التحقق من البيانات الأساسية
    if (empty($name)) {
        throw new Exception("الاسم مطلوب");
    }

    if (!in_array($customer_type, ['customer', 'supplier'])) {
        throw new Exception("نوع العميل غير صحيح");
    }

    // التحقق من صحة البريد الإلكتروني
    if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception("البريد الإلكتروني غير صحيح");
    }

    // التحقق من عدم تكرار الاسم
    if ($customer_id) {
        // في حالة التعديل
        $check_stmt = $db->prepare("SELECT id FROM customers WHERE name = ? AND customer_type = ? AND id != ?");
        $check_stmt->bind_param("ssi", $name, $customer_type, $customer_id);
    } else {
        // في حالة الإضافة
        $check_stmt = $db->prepare("SELECT id FROM customers WHERE name = ? AND customer_type = ?");
        $check_stmt->bind_param("ss", $name, $customer_type);
    }

    $check_stmt->execute();
    $existing_customer = $check_stmt->get_result()->fetch_assoc();
    $check_stmt->close();

    if ($existing_customer) {
        $type_label = $customer_type === 'supplier' ? 'مورد' : 'عميل';
        throw new Exception("يوجد $type_label آخر بنفس الاسم");
    }

    // التحقق من عدم تكرار البريد الإلكتروني
    if (!empty($email)) {
        if ($customer_id) {
            $check_email_stmt = $db->prepare("SELECT id FROM customers WHERE email = ? AND id != ?");
            $check_email_stmt->bind_param("si", $email, $customer_id);
        } else {
            $check_email_stmt = $db->prepare("SELECT id FROM customers WHERE email = ?");
            $check_email_stmt->bind_param("s", $email);
        }

        $check_email_stmt->execute();
        $existing_email = $check_email_stmt->get_result()->fetch_assoc();
        $check_email_stmt->close();

        if ($existing_email) {
            throw new Exception("البريد الإلكتروني مستخدم من قبل");
        }
    }

    if ($customer_id) {
        // تعديل عميل/مورد موجود
        $stmt = $db->prepare("UPDATE customers SET name = ?, phone = ?, email = ?, tax_number = ?, address = ?, customer_type = ?, updated_at = NOW() WHERE id = ?");
        $stmt->bind_param("ssssssi", $name, $phone, $email, $tax_number, $address, $customer_type, $customer_id);
        
        if ($stmt->execute()) {
            $type_label = $customer_type === 'supplier' ? 'المورد' : 'العميل';
            logActivity('customer_update', 'customers', $customer_id, null, [
                'name' => $name,
                'customer_type' => $customer_type
            ], "تعديل $type_label");
            
            $_SESSION['success'] = "تم تعديل $type_label بنجاح";
        } else {
            throw new Exception("حدث خطأ أثناء تعديل البيانات");
        }
    } else {
        // إضافة عميل/مورد جديد
        $stmt = $db->prepare("INSERT INTO customers (name, phone, email, tax_number, address, customer_type, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())");
        $stmt->bind_param("ssssss", $name, $phone, $email, $tax_number, $address, $customer_type);
        
        if ($stmt->execute()) {
            $new_customer_id = $db->insert_id;
            $type_label = $customer_type === 'supplier' ? 'مورد' : 'عميل';
            
            logActivity('customer_create', 'customers', $new_customer_id, null, [
                'name' => $name,
                'customer_type' => $customer_type
            ], "إضافة $type_label جديد");
            
            $_SESSION['success'] = "تم إضافة $type_label بنجاح";
        } else {
            throw new Exception("حدث خطأ أثناء إضافة البيانات");
        }
    }

    $stmt->close();

} catch (Exception $e) {
    $_SESSION['error'] = "خطأ في حفظ البيانات: " . $e->getMessage();
} finally {
    if (isset($db)) {
        $db->close();
    }
}

// إعادة التوجيه مع الحفاظ على نوع العميل
$redirect_type = $_POST['customer_type'] ?? 'customer';
header("Location: customers.php?type=$redirect_type");
exit();
?>
