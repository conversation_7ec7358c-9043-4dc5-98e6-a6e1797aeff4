<?php
// 1. تضمين ملف init.php أولاً
require_once __DIR__.'/config/init.php';

// 2. تضمين ملف functions.php الذي يحتوي على displayMessages()
require_once __DIR__.'/includes/functions.php';

// فحص تسجيل الدخول قبل إرسال أي محتوى
if (isLoggedIn()) {
    header("Location: index.php");
    exit();
}

// 3. ثم تضمين header.php
require_once __DIR__.'/includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">تسجيل مستخدم جديد</div>
            <div class="card-body">
                <form method="POST" action="includes/auth.php">
                    <div class="mb-3">
                        <label for="full_name" class="form-label">الاسم الكامل</label>
                        <input type="text" class="form-control" id="full_name" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <button type="submit" name="register" class="btn btn-primary">تسجيل</button>
                </form>
                <div class="mt-3">
                    لديك حساب بالفعل؟ <a href="login.php">سجل الدخول</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>