<?php
// ملف مساعد لإدارة قاعدة البيانات مع نظام البادئات

// دالة لتحديث الاستعلامات لتستخدم أسماء الجداول مع البادئة
function updateQueryWithUserPrefix($query, $username = null) {
    if (!$username && isset($_SESSION['username'])) {
        $username = $_SESSION['username'];
    }
    
    if (!$username) {
        return $query;
    }
    
    $prefix = getUserTablePrefix($username);
    if (!$prefix) {
        return $query;
    }
    
    // قائمة الجداول التي تحتاج بادئة
    $tables_to_prefix = [
        'customers',
        'products', 
        'sales',
        'purchases',
        'sale_items',
        'purchase_items'
    ];
    
    // استبدال أسماء الجداول بالأسماء مع البادئة
    foreach ($tables_to_prefix as $table) {
        // استبدال اسم الجدول في الاستعلام
        $query = preg_replace('/\b' . $table . '\b/', $prefix . $table, $query);
    }
    
    return $query;
}

// دالة لتنفيذ استعلام مع البادئة التلقائية
function executeUserQuery($db, $query, $username = null) {
    $updated_query = updateQueryWithUserPrefix($query, $username);
    return $db->query($updated_query);
}

// دالة لتحضير استعلام مع البادئة التلقائية
function prepareUserQuery($db, $query, $username = null) {
    $updated_query = updateQueryWithUserPrefix($query, $username);
    return $db->prepare($updated_query);
}

// دالة للحصول على عدد السجلات في جدول المستخدم
function getUserTableCount($table_name, $username = null) {
    $db = getCurrentUserDB();
    if (!$db) return 0;
    
    $table_with_prefix = getUserTableName($table_name, $username);
    if (!$table_with_prefix) return 0;
    
    $query = "SELECT COUNT(*) as count FROM `$table_with_prefix`";
    $result = $db->query($query);
    
    if ($result && $row = $result->fetch_assoc()) {
        return (int)$row['count'];
    }
    
    return 0;
}

// دالة للتحقق من وجود جدول المستخدم (محدثة للنظام الموحد)
function userTableExists($table_name, $username = null) {
    // في النظام الموحد، نتحقق من وجود الجدول مباشرة
    $db = getUnifiedDB();
    if (!$db) return false;

    // في النظام الموحد، أسماء الجداول مباشرة بدون بادئة
    $query = "SHOW TABLES LIKE '$table_name'";
    $result = $db->query($query);

    return $result && $result->num_rows > 0;
}

// دالة لإنشاء جدول واحد للمستخدم
function createSingleUserTable($table_name, $username = null) {
    if (!$username && isset($_SESSION['username'])) {
        $username = $_SESSION['username'];
    }
    
    if (!$username) return false;
    
    $db = getOperationsDB();
    if (!$db) return false;
    
    $prefix = getUserTablePrefix($username);
    if (!$prefix) return false;
    
    // تعريف هياكل الجداول
    $table_structures = [
        'customers' => "CREATE TABLE IF NOT EXISTS `{$prefix}customers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `email` varchar(255) DEFAULT NULL,
            `tax_number` varchar(50) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `customer_type` enum('customer','supplier') NOT NULL DEFAULT 'customer',
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_email` (`email`),
            KEY `idx_phone` (`phone`),
            KEY `idx_customer_type` (`customer_type`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'products' => "CREATE TABLE IF NOT EXISTS `{$prefix}products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `description` text DEFAULT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
            `category` varchar(100) DEFAULT NULL,
            `stock_quantity` decimal(10,2) DEFAULT 0.00,
            `unit` varchar(50) DEFAULT 'قطعة',
            `barcode` varchar(100) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_category` (`category`),
            KEY `idx_barcode` (`barcode`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'sales' => "CREATE TABLE IF NOT EXISTS `{$prefix}sales` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_date` (`date`),
            KEY `idx_payment_status` (`payment_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'purchases' => "CREATE TABLE IF NOT EXISTS `{$prefix}purchases` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `supplier_name` varchar(255) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_date` (`date`),
            KEY `idx_payment_status` (`payment_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'sale_items' => "CREATE TABLE IF NOT EXISTS `{$prefix}sale_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `sale_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `product_name` varchar(255) NOT NULL DEFAULT '',
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_sale_id` (`sale_id`),
            KEY `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",

        'purchase_items' => "CREATE TABLE IF NOT EXISTS `{$prefix}purchase_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `purchase_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `product_name` varchar(255) NOT NULL DEFAULT '',
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_purchase_id` (`purchase_id`),
            KEY `idx_product_id` (`product_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci"
    ];

    if (!isset($table_structures[$table_name])) {
        return false;
    }

    return $db->query($table_structures[$table_name]);
}

// دالة لحذف جداول المستخدم (للاختبار أو الصيانة)
function dropUserTables($username) {
    $db = getOperationsDB();
    if (!$db || !$username) return false;
    
    $prefix = getUserTablePrefix($username);
    if (!$prefix) return false;
    
    $tables = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
    
    foreach ($tables as $table) {
        $table_with_prefix = $prefix . $table;
        $db->query("DROP TABLE IF EXISTS `$table_with_prefix`");
    }
    
    return true;
}

// دالة للحصول على قائمة جداول المستخدم
function getUserTables($username = null) {
    if (!$username && isset($_SESSION['username'])) {
        $username = $_SESSION['username'];
    }
    
    if (!$username) return [];
    
    $db = getOperationsDB();
    if (!$db) return [];
    
    $prefix = getUserTablePrefix($username);
    if (!$prefix) return [];
    
    $query = "SHOW TABLES LIKE '{$prefix}%'";
    $result = $db->query($query);
    
    $tables = [];
    if ($result) {
        while ($row = $result->fetch_array()) {
            $tables[] = $row[0];
        }
    }
    
    return $tables;
}
?>
