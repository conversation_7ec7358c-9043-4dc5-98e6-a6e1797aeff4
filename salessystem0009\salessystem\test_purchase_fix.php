<?php
/**
 * ملف لاختبار إصلاح مشكلة إضافة المشتريات
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

$test_results = [];

// اختبار 1: التحقق من وجود جدول المشتريات
$check_purchases = $db->query("SHOW TABLES LIKE 'purchases'");
$test_results['purchases_table_exists'] = ($check_purchases && $check_purchases->num_rows > 0);

if ($test_results['purchases_table_exists']) {
    // اختبار 2: التحقق من بنية الجدول
    $structure = $db->query("DESCRIBE purchases");
    $columns = [];
    while ($row = $structure->fetch_assoc()) {
        $columns[$row['Field']] = $row['Type'];
    }
    $test_results['table_structure'] = $columns;
    
    // اختبار 3: محاولة إدراج بيانات تجريبية
    try {
        // التحقق من وجود عميل تجريبي
        $customer_check = $db->query("SELECT id FROM customers LIMIT 1");
        if ($customer_check && $customer_check->num_rows > 0) {
            $customer = $customer_check->fetch_assoc();
            $customer_id = $customer['id'];
        } else {
            // إنشاء عميل تجريبي
            $stmt = $db->prepare("INSERT INTO customers (name, phone, email, address) VALUES (?, ?, ?, ?)");
            $name = "عميل تجريبي";
            $phone = "0501234567";
            $email = "<EMAIL>";
            $address = "عنوان تجريبي";
            $stmt->bind_param("ssss", $name, $phone, $email, $address);
            $stmt->execute();
            $customer_id = $stmt->insert_id;
        }
        
        // إنشاء فاتورة مشتريات تجريبية
        $invoice_number = 'TEST-' . date('YmdHis');
        $date = date('Y-m-d');
        $subtotal = 100.00;
        $tax_amount = 15.00;
        $total_amount = 115.00;
        $notes = 'فاتورة اختبار إصلاح المشكلة';
        
        $stmt = $db->prepare("INSERT INTO purchases (customer_id, invoice_number, date, subtotal, tax_amount, total_amount, notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("issddds", $customer_id, $invoice_number, $date, $subtotal, $tax_amount, $total_amount, $notes);
        
        if ($stmt->execute()) {
            $test_results['insert_test'] = [
                'success' => true,
                'purchase_id' => $stmt->insert_id,
                'invoice_number' => $invoice_number
            ];
            
            // اختبار 4: قراءة البيانات المدرجة
            $read_stmt = $db->prepare("SELECT * FROM purchases WHERE id = ?");
            $read_stmt->bind_param("i", $test_results['insert_test']['purchase_id']);
            $read_stmt->execute();
            $result = $read_stmt->get_result();
            
            if ($result && $result->num_rows > 0) {
                $test_results['read_test'] = [
                    'success' => true,
                    'data' => $result->fetch_assoc()
                ];
            } else {
                $test_results['read_test'] = [
                    'success' => false,
                    'error' => 'فشل في قراءة البيانات المدرجة'
                ];
            }
            
        } else {
            $test_results['insert_test'] = [
                'success' => false,
                'error' => $stmt->error
            ];
        }
        
    } catch (Exception $e) {
        $test_results['insert_test'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
    
    // اختبار 5: اختبار الاستعلامات المستخدمة في الصفحة الرئيسية
    try {
        $today = date('Y-m-d');
        $stmt = $db->prepare("SELECT SUM(total_amount) FROM purchases WHERE date = ?");
        $stmt->bind_param("s", $today);
        $stmt->execute();
        $stmt->bind_result($today_purchases);
        $stmt->fetch();
        $stmt->close();
        
        $test_results['dashboard_query_test'] = [
            'success' => true,
            'today_purchases' => $today_purchases ?? 0
        ];
    } catch (Exception $e) {
        $test_results['dashboard_query_test'] = [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

displayMessages();
?>

<div class="container mt-4">
    <h2>اختبار إصلاح مشكلة المشتريات</h2>
    
    <div class="row">
        <!-- نتائج الاختبار -->
        <div class="col-md-8">
            <!-- اختبار وجود الجدول -->
            <div class="card mb-3">
                <div class="card-header <?php echo $test_results['purchases_table_exists'] ? 'bg-success text-white' : 'bg-danger text-white'; ?>">
                    <h5 class="mb-0">
                        <i class="fas <?php echo $test_results['purchases_table_exists'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        اختبار وجود جدول المشتريات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($test_results['purchases_table_exists']): ?>
                        <p class="text-success">✅ جدول المشتريات موجود</p>
                    <?php else: ?>
                        <p class="text-danger">❌ جدول المشتريات غير موجود</p>
                        <a href="check_tables.php" class="btn btn-primary">إنشاء الجداول</a>
                    <?php endif; ?>
                </div>
            </div>
            
            <?php if ($test_results['purchases_table_exists']): ?>
            <!-- اختبار بنية الجدول -->
            <div class="card mb-3">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-table"></i>
                        بنية جدول المشتريات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>اسم العمود</th>
                                    <th>نوع البيانات</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $required_columns = [
                                    'id' => 'int',
                                    'customer_id' => 'int',
                                    'invoice_number' => 'varchar',
                                    'date' => 'date',
                                    'subtotal' => 'decimal',
                                    'tax_amount' => 'decimal',
                                    'total_amount' => 'decimal',
                                    'notes' => 'text'
                                ];
                                
                                foreach ($required_columns as $column => $expected_type):
                                    $exists = isset($test_results['table_structure'][$column]);
                                ?>
                                <tr>
                                    <td><?php echo $column; ?></td>
                                    <td><?php echo $exists ? $test_results['table_structure'][$column] : 'غير موجود'; ?></td>
                                    <td>
                                        <?php if ($exists): ?>
                                            <span class="text-success"><i class="fas fa-check"></i> موجود</span>
                                        <?php else: ?>
                                            <span class="text-danger"><i class="fas fa-times"></i> مفقود</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- اختبار الإدراج -->
            <div class="card mb-3">
                <div class="card-header <?php echo isset($test_results['insert_test']) && $test_results['insert_test']['success'] ? 'bg-success text-white' : 'bg-danger text-white'; ?>">
                    <h5 class="mb-0">
                        <i class="fas <?php echo isset($test_results['insert_test']) && $test_results['insert_test']['success'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        اختبار إدراج البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($test_results['insert_test'])): ?>
                        <?php if ($test_results['insert_test']['success']): ?>
                            <p class="text-success">✅ تم إدراج فاتورة اختبار بنجاح</p>
                            <ul>
                                <li><strong>رقم الفاتورة:</strong> <?php echo $test_results['insert_test']['invoice_number']; ?></li>
                                <li><strong>معرف الفاتورة:</strong> <?php echo $test_results['insert_test']['purchase_id']; ?></li>
                            </ul>
                        <?php else: ?>
                            <p class="text-danger">❌ فشل في إدراج البيانات</p>
                            <p><strong>الخطأ:</strong> <?php echo $test_results['insert_test']['error']; ?></p>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- اختبار القراءة -->
            <?php if (isset($test_results['read_test'])): ?>
            <div class="card mb-3">
                <div class="card-header <?php echo $test_results['read_test']['success'] ? 'bg-success text-white' : 'bg-danger text-white'; ?>">
                    <h5 class="mb-0">
                        <i class="fas <?php echo $test_results['read_test']['success'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        اختبار قراءة البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($test_results['read_test']['success']): ?>
                        <p class="text-success">✅ تم قراءة البيانات بنجاح</p>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <?php foreach ($test_results['read_test']['data'] as $key => $value): ?>
                                <tr>
                                    <th><?php echo $key; ?></th>
                                    <td><?php echo htmlspecialchars($value); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </table>
                        </div>
                    <?php else: ?>
                        <p class="text-danger">❌ فشل في قراءة البيانات</p>
                        <p><strong>الخطأ:</strong> <?php echo $test_results['read_test']['error']; ?></p>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- اختبار استعلام الصفحة الرئيسية -->
            <?php if (isset($test_results['dashboard_query_test'])): ?>
            <div class="card mb-3">
                <div class="card-header <?php echo $test_results['dashboard_query_test']['success'] ? 'bg-success text-white' : 'bg-danger text-white'; ?>">
                    <h5 class="mb-0">
                        <i class="fas <?php echo $test_results['dashboard_query_test']['success'] ? 'fa-check-circle' : 'fa-times-circle'; ?>"></i>
                        اختبار استعلام الصفحة الرئيسية
                    </h5>
                </div>
                <div class="card-body">
                    <?php if ($test_results['dashboard_query_test']['success']): ?>
                        <p class="text-success">✅ استعلام المشتريات اليومية يعمل بنجاح</p>
                        <p><strong>إجمالي مشتريات اليوم:</strong> <?php echo number_format($test_results['dashboard_query_test']['today_purchases'], 2); ?> ر.س</p>
                    <?php else: ?>
                        <p class="text-danger">❌ فشل في استعلام المشتريات اليومية</p>
                        <p><strong>الخطأ:</strong> <?php echo $test_results['dashboard_query_test']['error']; ?></p>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
        
        <!-- الإجراءات -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">الإجراءات</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                        <a href="add_purchase.php" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة مشتريات
                        </a>
                        <a href="purchases.php" class="btn btn-info">
                            <i class="fas fa-list"></i> عرض المشتريات
                        </a>
                        <a href="reports.php" class="btn btn-warning">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                        <a href="test_purchase_fix.php" class="btn btn-outline-primary">
                            <i class="fas fa-redo"></i> إعادة الاختبار
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
