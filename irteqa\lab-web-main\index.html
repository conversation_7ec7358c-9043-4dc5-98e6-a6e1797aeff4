<!doctype html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <link rel="icon" href="./favicon.ico" />
    <meta name="viewport"
        content="width=device-width,initial-scale=.5,maximum-scale=5,minimum-scale=.25,user-scalable=yes" />
    <link rel="canonical" href="https://jsonldresume.org/" />
    <meta name="theme-color" content="#546e7a" />
    <meta name="description"
        content="A one-of-a-kind resume builder that's not out to get your data. Completely secure, customizable, portable, open-source and free forever. Try it out today!" />
    <link rel="apple-touch-icon" href="./logo192.png" />
    <link rel="manifest" href="./manifest.json" />
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="Json-ld Resume">
    <meta property="og:description"
        content="A one-of-a-kind resume builder that's not out to get your data. Completely secure, customizable, portable, open-source and free forever. Try it out today!">
    <meta property="og:image"
        content="https://raw.githubusercontent.com/Jsonldresume/lab-web/main/public/images/jsonldresume.png">
    <meta property="og:url" content="https://jsonldresume.org/">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@jsonldresume">
    <title>Json-ld Resume</title>
    <link href="./static/css/2.1781c263.chunk.css" rel="stylesheet">
    <link href="./static/css/main.16a1f148.chunk.css" rel="stylesheet"><!--Start of Tawk.to Script-->
    <script type="text/javascript">
        var Tawk_API = Tawk_API || {}, Tawk_LoadStart = new Date();
        (function () {
            var s1 = document.createElement("script"), s0 = document.getElementsByTagName("script")[0];
            s1.async = true;
            s1.src = 'https://embed.tawk.to/60231ad2918aa261273d5e8d/1eu4i1hu4';
            s1.charset = 'UTF-8';
            s1.setAttribute('crossorigin', '*');
            s0.parentNode.insertBefore(s1, s0);
        })();
    </script>
    <!--End of Tawk.to Script-->
</head>

<body><noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script>!function (e) { function r(r) { for (var n, l, f = r[0], i = r[1], a = r[2], c = 0, s = []; c < f.length; c++)l = f[c], Object.prototype.hasOwnProperty.call(o, l) && o[l] && s.push(o[l][0]), o[l] = 0; for (n in i) Object.prototype.hasOwnProperty.call(i, n) && (e[n] = i[n]); for (p && p(r); s.length;)s.shift()(); return u.push.apply(u, a || []), t() } function t() { for (var e, r = 0; r < u.length; r++) { for (var t = u[r], n = !0, f = 1; f < t.length; f++) { var i = t[f]; 0 !== o[i] && (n = !1) } n && (u.splice(r--, 1), e = l(l.s = t[0])) } return e } var n = {}, o = { 1: 0 }, u = []; function l(r) { if (n[r]) return n[r].exports; var t = n[r] = { i: r, l: !1, exports: {} }; return e[r].call(t.exports, t, t.exports, l), t.l = !0, t.exports } l.m = e, l.c = n, l.d = function (e, r, t) { l.o(e, r) || Object.defineProperty(e, r, { enumerable: !0, get: t }) }, l.r = function (e) { "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(e, "__esModule", { value: !0 }) }, l.t = function (e, r) { if (1 & r && (e = l(e)), 8 & r) return e; if (4 & r && "object" == typeof e && e && e.__esModule) return e; var t = Object.create(null); if (l.r(t), Object.defineProperty(t, "default", { enumerable: !0, value: e }), 2 & r && "string" != typeof e) for (var n in e) l.d(t, n, function (r) { return e[r] }.bind(null, n)); return t }, l.n = function (e) { var r = e && e.__esModule ? function () { return e.default } : function () { return e }; return l.d(r, "a", r), r }, l.o = function (e, r) { return Object.prototype.hasOwnProperty.call(e, r) }, l.p = "./"; var f = this.webpackJsonpjsonldresume = this.webpackJsonpjsonldresume || [], i = f.push.bind(f); f.push = r, f = f.slice(); for (var a = 0; a < f.length; a++)r(f[a]); var p = i; t() }([])</script>
    <script src="./static/js/2.275ab9cd.chunk.js"></script>
    <script src="./static/js/main.f6ad7922.chunk.js"></script>
</body>

</html>