<?php
// اختبار الدوال للتأكد من عدم وجود تضارب
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الدوال - salessystem_v2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .test-section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-chart-bar"></i> تقرير الدوال - salessystem_v2
                </h1>
                
                <!-- اختبار تحميل الملفات -->
                <div class="test-section">
                    <h3><i class="fas fa-file-code"></i> اختبار تحميل الملفات</h3>
                    
                    <?php
                    echo "<h5>1. تحميل ملفات التكوين:</h5>";
                    
                    try {
                        require_once 'config/init.php';
                        echo "<p class='success'><i class='fas fa-check'></i> تم تحميل config/init.php بنجاح</p>";
                    } catch (Exception $e) {
                        echo "<p class='error'><i class='fas fa-times'></i> خطأ في تحميل config/init.php: " . $e->getMessage() . "</p>";
                    }
                    ?>
                </div>

                <!-- اختبار الدوال الأساسية -->
                <div class="test-section">
                    <h3><i class="fas fa-cogs"></i> اختبار الدوال الأساسية</h3>
                    
                    <?php
                    echo "<h5>2. اختبار دوال قاعدة البيانات:</h5>";
                    
                    // اختبار دالة getOperationsDB
                    if (function_exists('getOperationsDB')) {
                        echo "<p class='success'><i class='fas fa-check'></i> دالة getOperationsDB موجودة</p>";
                        
                        try {
                            $ops_db = getOperationsDB();
                            if ($ops_db && !$ops_db->connect_error) {
                                echo "<p class='success'><i class='fas fa-check'></i> دالة getOperationsDB تعمل بنجاح</p>";
                            } else {
                                echo "<p class='warning'><i class='fas fa-exclamation-triangle'></i> دالة getOperationsDB تعمل لكن هناك مشكلة في الاتصال</p>";
                            }
                        } catch (Exception $e) {
                            echo "<p class='error'><i class='fas fa-times'></i> خطأ في دالة getOperationsDB: " . $e->getMessage() . "</p>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> دالة getOperationsDB غير موجودة</p>";
                    }
                    
                    // اختبار دالة getCurrentUserDB
                    if (function_exists('getCurrentUserDB')) {
                        echo "<p class='success'><i class='fas fa-check'></i> دالة getCurrentUserDB موجودة</p>";
                        
                        try {
                            $current_db = getCurrentUserDB();
                            if ($current_db) {
                                echo "<p class='success'><i class='fas fa-check'></i> دالة getCurrentUserDB تعمل بنجاح</p>";
                            } else {
                                echo "<p class='info'><i class='fas fa-info-circle'></i> دالة getCurrentUserDB تعمل (لكن لا يوجد مستخدم مسجل دخول)</p>";
                            }
                        } catch (Exception $e) {
                            echo "<p class='error'><i class='fas fa-times'></i> خطأ في دالة getCurrentUserDB: " . $e->getMessage() . "</p>";
                        }
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> دالة getCurrentUserDB غير موجودة</p>";
                    }
                    
                    // اختبار دالة getUserDBConnection
                    if (function_exists('getUserDBConnection')) {
                        echo "<p class='success'><i class='fas fa-check'></i> دالة getUserDBConnection موجودة (للتوافق)</p>";
                    } else {
                        echo "<p class='error'><i class='fas fa-times'></i> دالة getUserDBConnection غير موجودة</p>";
                    }
                    ?>
                </div>

                <!-- اختبار دوال البادئة -->
                <div class="test-section">
                    <h3><i class="fas fa-tag"></i> اختبار دوال البادئة</h3>
                    
                    <?php
                    echo "<h5>3. اختبار دوال البادئة:</h5>";
                    
                    $test_functions = [
                        'getUserTablePrefix',
                        'getUserTableName', 
                        'updateQueryWithUserPrefix',
                        'createUserTables',
                        'userTableExists',
                        'getUserTableCount',
                        'getUserTables'
                    ];
                    
                    foreach ($test_functions as $function_name) {
                        if (function_exists($function_name)) {
                            echo "<p class='success'><i class='fas fa-check'></i> دالة $function_name موجودة</p>";
                        } else {
                            echo "<p class='error'><i class='fas fa-times'></i> دالة $function_name غير موجودة</p>";
                        }
                    }
                    
                    // اختبار عملي لدوال البادئة
                    if (function_exists('getUserTablePrefix') && function_exists('getUserTableName')) {
                        echo "<h6>اختبار عملي:</h6>";
                        
                        $test_username = 'test_user';
                        $prefix = getUserTablePrefix($test_username);
                        $table_name = getUserTableName('customers', $test_username);
                        
                        echo "<ul>";
                        echo "<li>المستخدم: <code>$test_username</code></li>";
                        echo "<li>البادئة: <code>$prefix</code></li>";
                        echo "<li>اسم جدول العملاء: <code>$table_name</code></li>";
                        echo "</ul>";
                    }
                    ?>
                </div>

                <!-- اختبار دوال الترحيل -->
                <div class="test-section">
                    <h3><i class="fas fa-exchange-alt"></i> اختبار دوال الترحيل</h3>
                    
                    <?php
                    echo "<h5>4. اختبار دوال الترحيل:</h5>";
                    
                    $migration_functions = [
                        'migrateUserData',
                        'migrateAllUsers',
                        'verifyMigration',
                        'createBackupBeforeMigration',
                        'cleanupOldDatabases',
                        'generateMigrationReport',
                        'testNewSystem'
                    ];
                    
                    foreach ($migration_functions as $function_name) {
                        if (function_exists($function_name)) {
                            echo "<p class='success'><i class='fas fa-check'></i> دالة $function_name موجودة</p>";
                        } else {
                            echo "<p class='warning'><i class='fas fa-exclamation-triangle'></i> دالة $function_name غير موجودة</p>";
                        }
                    }
                    ?>
                </div>

                <!-- اختبار دوال النظام -->
                <div class="test-section">
                    <h3><i class="fas fa-system"></i> اختبار دوال النظام</h3>
                    
                    <?php
                    echo "<h5>5. اختبار دوال النظام الأساسية:</h5>";
                    
                    $system_functions = [
                        'isLoggedIn',
                        'isAdminLoggedIn',
                        'hasAdminPermission',
                        'logActivity',
                        'redirectIfNotLoggedIn',
                        'ensureMainDatabase',
                        'ensureAdminTables'
                    ];
                    
                    foreach ($system_functions as $function_name) {
                        if (function_exists($function_name)) {
                            echo "<p class='success'><i class='fas fa-check'></i> دالة $function_name موجودة</p>";
                        } else {
                            echo "<p class='error'><i class='fas fa-times'></i> دالة $function_name غير موجودة</p>";
                        }
                    }
                    
                    // اختبار حالة تسجيل الدخول
                    echo "<h6>حالة النظام:</h6>";
                    echo "<ul>";
                    echo "<li>تسجيل الدخول: " . (isLoggedIn() ? "<span class='success'>مسجل</span>" : "<span class='info'>غير مسجل</span>") . "</li>";
                    echo "<li>تسجيل دخول المدير: " . (isAdminLoggedIn() ? "<span class='success'>مسجل</span>" : "<span class='info'>غير مسجل</span>") . "</li>";
                    echo "<li>حالة الجلسة: " . (session_status() === PHP_SESSION_ACTIVE ? "<span class='success'>نشطة</span>" : "<span class='warning'>غير نشطة</span>") . "</li>";
                    echo "</ul>";
                    ?>
                </div>

                <!-- اختبار المتغيرات العامة -->
                <div class="test-section">
                    <h3><i class="fas fa-database"></i> اختبار المتغيرات العامة</h3>
                    
                    <?php
                    echo "<h5>6. اختبار متغيرات قاعدة البيانات:</h5>";
                    
                    $db_constants = [
                        'MAIN_DB_HOST',
                        'MAIN_DB_USER', 
                        'MAIN_DB_PASS',
                        'MAIN_DB_NAME',
                        'OPERATIONS_DB_HOST',
                        'OPERATIONS_DB_USER',
                        'OPERATIONS_DB_PASS',
                        'OPERATIONS_DB_NAME'
                    ];
                    
                    foreach ($db_constants as $constant) {
                        if (defined($constant)) {
                            $value = constant($constant);
                            // إخفاء كلمة المرور
                            if (strpos($constant, 'PASS') !== false) {
                                $value = str_repeat('*', strlen($value));
                            }
                            echo "<p class='success'><i class='fas fa-check'></i> $constant = <code>$value</code></p>";
                        } else {
                            echo "<p class='error'><i class='fas fa-times'></i> $constant غير معرف</p>";
                        }
                    }
                    
                    // اختبار المتغيرات العامة
                    echo "<h6>متغيرات الاتصال:</h6>";
                    echo "<ul>";
                    if (isset($main_db)) {
                        echo "<li>\$main_db: " . ($main_db->connect_error ? "<span class='error'>خطأ في الاتصال</span>" : "<span class='success'>متصل</span>") . "</li>";
                    } else {
                        echo "<li>\$main_db: <span class='error'>غير موجود</span></li>";
                    }
                    
                    if (isset($operations_db)) {
                        echo "<li>\$operations_db: " . ($operations_db->connect_error ? "<span class='error'>خطأ في الاتصال</span>" : "<span class='success'>متصل</span>") . "</li>";
                    } else {
                        echo "<li>\$operations_db: <span class='warning'>غير موجود (سيتم إنشاؤه عند الحاجة)</span></li>";
                    }
                    echo "</ul>";
                    ?>
                </div>

                <!-- ملخص النتائج -->
                <div class="test-section">
                    <h3><i class="fas fa-clipboard-check"></i> ملخص النتائج</h3>
                    
                    <?php
                    $total_tests = 0;
                    $passed_tests = 0;
                    
                    // عد الدوال الموجودة
                    $all_functions = array_merge($test_functions, $migration_functions, $system_functions);
                    foreach ($all_functions as $func) {
                        $total_tests++;
                        if (function_exists($func)) {
                            $passed_tests++;
                        }
                    }
                    
                    // عد الثوابت الموجودة
                    foreach ($db_constants as $const) {
                        $total_tests++;
                        if (defined($const)) {
                            $passed_tests++;
                        }
                    }
                    
                    $success_rate = round(($passed_tests / $total_tests) * 100, 1);
                    
                    if ($success_rate >= 90) {
                        echo "<div class='alert alert-success'>";
                        echo "<h5><i class='fas fa-check-circle'></i> النظام يعمل بشكل ممتاز!</h5>";
                    } elseif ($success_rate >= 70) {
                        echo "<div class='alert alert-warning'>";
                        echo "<h5><i class='fas fa-exclamation-triangle'></i> النظام يعمل مع بعض التحذيرات</h5>";
                    } else {
                        echo "<div class='alert alert-danger'>";
                        echo "<h5><i class='fas fa-times-circle'></i> يوجد مشاكل في النظام</h5>";
                    }
                    
                    echo "<p>معدل النجاح: <strong>$success_rate%</strong> ($passed_tests من $total_tests)</p>";
                    echo "</div>";
                    ?>
                </div>

                <!-- أزرار التحكم -->
                <div class="test-section text-center">
                    <h3><i class="fas fa-tools"></i> أدوات أخرى</h3>
                    
                    <div class="btn-group" role="group">
                        <a href="setup_databases.php" class="btn btn-primary">
                            <i class="fas fa-database"></i> إعداد قواعد البيانات
                        </a>
                        <a href="test_system.php" class="btn btn-info">
                            <i class="fas fa-chart-line"></i> تقرير النظام
                        </a>
                        <a href="update_database.php" class="btn btn-success">
                            <i class="fas fa-sync"></i> تحديث قاعدة البيانات
                        </a>
                        <a href="index_safe.php" class="btn btn-secondary">
                            <i class="fas fa-home"></i> الصفحة الرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
