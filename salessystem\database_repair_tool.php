<?php
/**
 * أداة إصلاح وتشخيص قاعدة البيانات الشاملة
 * تقوم بفحص وإصلاح جميع مشاكل قاعدة البيانات تلقائياً
 */

// منع انتهاء وقت التنفيذ
set_time_limit(300);
ini_set('memory_limit', '256M');

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التحقق من صلاحيات المستخدم
redirectIfNotLoggedIn();

// بدء معالجة الإصلاح
$repair_log = [];
$errors = [];
$success_count = 0;
$error_count = 0;

function addLog($message, $type = 'info') {
    global $repair_log;
    $repair_log[] = [
        'time' => date('H:i:s'),
        'type' => $type,
        'message' => $message
    ];
    
    if ($type === 'error') {
        global $error_count;
        $error_count++;
    } elseif ($type === 'success') {
        global $success_count;
        $success_count++;
    }
}

// 1. فحص الاتصال بقاعدة البيانات الرئيسية
addLog("بدء فحص الاتصال بقاعدة البيانات الرئيسية...", 'info');

try {
    global $main_db;
    if ($main_db->connect_error) {
        addLog("فشل الاتصال بقاعدة البيانات الرئيسية: " . $main_db->connect_error, 'error');
        $errors[] = "مشكلة في الاتصال بقاعدة البيانات الرئيسية";
    } else {
        addLog("تم الاتصال بقاعدة البيانات الرئيسية بنجاح", 'success');
        
        // فحص وجود قاعدة البيانات الرئيسية
        $db_check = $main_db->query("SELECT SCHEMA_NAME FROM INFORMATION_SCHEMA.SCHEMATA WHERE SCHEMA_NAME = '" . MAIN_DB_NAME . "'");
        if (!$db_check || $db_check->num_rows == 0) {
            addLog("قاعدة البيانات الرئيسية غير موجودة، جاري إنشاؤها...", 'warning');
            
            if ($main_db->query("CREATE DATABASE IF NOT EXISTS `" . MAIN_DB_NAME . "` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci")) {
                addLog("تم إنشاء قاعدة البيانات الرئيسية بنجاح", 'success');
            } else {
                addLog("فشل في إنشاء قاعدة البيانات الرئيسية: " . $main_db->error, 'error');
            }
        } else {
            addLog("قاعدة البيانات الرئيسية موجودة", 'success');
        }
    }
} catch (Exception $e) {
    addLog("استثناء في فحص قاعدة البيانات الرئيسية: " . $e->getMessage(), 'error');
}

// 2. فحص قاعدة بيانات المستخدم الحالي
addLog("فحص قاعدة بيانات المستخدم الحالي...", 'info');

$user_db_name = "sales_system_user_" . $_SESSION['user_id'];
$user_db = null;

try {
    // محاولة الاتصال بقاعدة بيانات المستخدم
    $user_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, $user_db_name);
    $user_db->set_charset("utf8mb4");
    
    if ($user_db->connect_error) {
        addLog("قاعدة بيانات المستخدم غير موجودة، جاري إنشاؤها...", 'warning');
        
        // إنشاء قاعدة بيانات المستخدم
        $temp_conn = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS);
        if ($temp_conn->query("CREATE DATABASE IF NOT EXISTS `$user_db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci")) {
            addLog("تم إنشاء قاعدة بيانات المستخدم بنجاح", 'success');
            
            // إعادة الاتصال
            $user_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, $user_db_name);
            $user_db->set_charset("utf8mb4");
        } else {
            addLog("فشل في إنشاء قاعدة بيانات المستخدم: " . $temp_conn->error, 'error');
        }
        $temp_conn->close();
    } else {
        addLog("تم الاتصال بقاعدة بيانات المستخدم بنجاح", 'success');
    }
} catch (Exception $e) {
    addLog("استثناء في فحص قاعدة بيانات المستخدم: " . $e->getMessage(), 'error');
}

// 3. إنشاء/فحص الجداول المطلوبة
if ($user_db && !$user_db->connect_error) {
    addLog("بدء فحص وإنشاء الجداول المطلوبة...", 'info');
    
    // تعريف الجداول المطلوبة
    $required_tables = [
        'customers' => "CREATE TABLE IF NOT EXISTS `customers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `email` varchar(255) DEFAULT NULL,
            `tax_number` varchar(50) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_email` (`email`),
            KEY `idx_phone` (`phone`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
        
        'products' => "CREATE TABLE IF NOT EXISTS `products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `stock_quantity` decimal(10,2) DEFAULT 0.00,
            `unit` varchar(50) DEFAULT 'قطعة',
            `barcode` varchar(100) DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`),
            KEY `idx_barcode` (`barcode`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
        
        'sales' => "CREATE TABLE IF NOT EXISTS `sales` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`),
            KEY `idx_customer_id` (`customer_id`),
            KEY `idx_date` (`date`),
            KEY `idx_payment_status` (`payment_status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci"
    ];
    
    // إضافة باقي الجداول
    $required_tables['purchases'] = "CREATE TABLE IF NOT EXISTS `purchases` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `invoice_number` varchar(50) NOT NULL,
        `date` date NOT NULL,
        `supplier_name` varchar(255) DEFAULT NULL,
        `customer_id` int(11) DEFAULT NULL,
        `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `payment_status` enum('paid','unpaid','partial') DEFAULT 'unpaid',
        `notes` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `idx_invoice_number` (`invoice_number`),
        KEY `idx_customer_id` (`customer_id`),
        KEY `idx_date` (`date`),
        KEY `idx_payment_status` (`payment_status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    $required_tables['sale_items'] = "CREATE TABLE IF NOT EXISTS `sale_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `sale_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `idx_sale_id` (`sale_id`),
        KEY `idx_product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    $required_tables['purchase_items'] = "CREATE TABLE IF NOT EXISTS `purchase_items` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `purchase_id` int(11) NOT NULL,
        `product_id` int(11) NOT NULL,
        `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
        `price` decimal(10,2) NOT NULL DEFAULT 0.00,
        `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
        `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
        `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
        PRIMARY KEY (`id`),
        KEY `idx_purchase_id` (`purchase_id`),
        KEY `idx_product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    $required_tables['settings'] = "CREATE TABLE IF NOT EXISTS `settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text DEFAULT NULL,
        `description` text DEFAULT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `idx_setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

    foreach ($required_tables as $table_name => $create_sql) {
        try {
            if ($user_db->query($create_sql)) {
                addLog("تم فحص/إنشاء جدول $table_name بنجاح", 'success');
            } else {
                addLog("فشل في إنشاء جدول $table_name: " . $user_db->error, 'error');
            }
        } catch (Exception $e) {
            addLog("استثناء في إنشاء جدول $table_name: " . $e->getMessage(), 'error');
        }
    }

    // 4. إضافة البيانات الافتراضية للإعدادات
    addLog("فحص وإضافة البيانات الافتراضية...", 'info');

    $default_settings = [
        ['company_name', 'شركتي', 'اسم الشركة'],
        ['company_address', '', 'عنوان الشركة'],
        ['company_phone', '', 'هاتف الشركة'],
        ['company_email', '', 'بريد الشركة الإلكتروني'],
        ['tax_number', '', 'الرقم الضريبي للشركة'],
        ['default_tax_rate', '15.00', 'معدل الضريبة الافتراضي'],
        ['currency_symbol', 'ر.س', 'رمز العملة'],
        ['date_format', 'Y-m-d', 'تنسيق التاريخ'],
        ['invoice_prefix', 'INV', 'بادئة رقم الفاتورة'],
        ['backup_enabled', '1', 'تفعيل النسخ الاحتياطي التلقائي']
    ];

    foreach ($default_settings as $setting) {
        try {
            $check_setting = $user_db->prepare("SELECT id FROM settings WHERE setting_key = ?");
            $check_setting->bind_param("s", $setting[0]);
            $check_setting->execute();
            $result = $check_setting->get_result();

            if ($result->num_rows == 0) {
                $insert_setting = $user_db->prepare("INSERT INTO settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
                $insert_setting->bind_param("sss", $setting[0], $setting[1], $setting[2]);

                if ($insert_setting->execute()) {
                    addLog("تم إضافة إعداد: " . $setting[0], 'success');
                } else {
                    addLog("فشل في إضافة إعداد " . $setting[0] . ": " . $user_db->error, 'error');
                }
                $insert_setting->close();
            }
            $check_setting->close();
        } catch (Exception $e) {
            addLog("استثناء في إضافة الإعدادات: " . $e->getMessage(), 'error');
        }
    }

    // 5. فحص سلامة البيانات
    addLog("فحص سلامة البيانات...", 'info');

    // فحص البيانات اليتيمة
    try {
        $orphaned_sales = $user_db->query("
            SELECT COUNT(*) as count
            FROM sales s
            LEFT JOIN customers c ON s.customer_id = c.id
            WHERE s.customer_id IS NOT NULL AND c.id IS NULL
        ");

        if ($orphaned_sales) {
            $orphaned_count = $orphaned_sales->fetch_assoc()['count'];
            if ($orphaned_count > 0) {
                addLog("تم العثور على $orphaned_count فاتورة مبيعات يتيمة", 'warning');

                // إصلاح البيانات اليتيمة
                $fix_orphaned = $user_db->query("UPDATE sales SET customer_id = NULL WHERE customer_id NOT IN (SELECT id FROM customers)");
                if ($fix_orphaned) {
                    addLog("تم إصلاح البيانات اليتيمة في جدول المبيعات", 'success');
                }
            }
        }
    } catch (Exception $e) {
        addLog("استثناء في فحص البيانات اليتيمة: " . $e->getMessage(), 'error');
    }

    $user_db->close();
} else {
    addLog("فشل في الاتصال بقاعدة بيانات المستخدم", 'error');
}

// إنهاء عملية الإصلاح
addLog("انتهت عملية إصلاح قاعدة البيانات", 'info');
addLog("النتائج: $success_count نجح، $error_count فشل", 'info');

// حفظ تقرير الإصلاح
$repair_report = [
    'timestamp' => date('Y-m-d H:i:s'),
    'user_id' => $_SESSION['user_id'],
    'success_count' => $success_count,
    'error_count' => $error_count,
    'log' => $repair_log
];

// عرض الواجهة
require_once __DIR__ . '/includes/header.php';
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header <?php echo $error_count > 0 ? 'bg-warning' : 'bg-success'; ?> text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-tools"></i>
                        أداة إصلاح قاعدة البيانات الشاملة
                    </h4>
                </div>
                <div class="card-body">
                    <!-- ملخص النتائج -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body text-center">
                                    <h2><?php echo $success_count; ?></h2>
                                    <p class="mb-0">عمليات نجحت</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-danger text-white">
                                <div class="card-body text-center">
                                    <h2><?php echo $error_count; ?></h2>
                                    <p class="mb-0">عمليات فشلت</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h2><?php echo count($repair_log); ?></h2>
                                    <p class="mb-0">إجمالي العمليات</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- سجل الإصلاح -->
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-list"></i>
                                سجل عمليات الإصلاح
                            </h5>
                        </div>
                        <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>الوقت</th>
                                            <th>النوع</th>
                                            <th>الرسالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($repair_log as $log_entry): ?>
                                        <tr>
                                            <td><?php echo $log_entry['time']; ?></td>
                                            <td>
                                                <?php
                                                $badge_class = 'secondary';
                                                $icon = 'info-circle';

                                                switch ($log_entry['type']) {
                                                    case 'success':
                                                        $badge_class = 'success';
                                                        $icon = 'check-circle';
                                                        break;
                                                    case 'error':
                                                        $badge_class = 'danger';
                                                        $icon = 'times-circle';
                                                        break;
                                                    case 'warning':
                                                        $badge_class = 'warning';
                                                        $icon = 'exclamation-triangle';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge bg-<?php echo $badge_class; ?>">
                                                    <i class="fas fa-<?php echo $icon; ?>"></i>
                                                    <?php echo ucfirst($log_entry['type']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($log_entry['message']); ?></td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="mt-4 text-center">
                        <a href="index.php" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            العودة للصفحة الرئيسية
                        </a>
                        <a href="database_repair_tool.php" class="btn btn-secondary ms-2">
                            <i class="fas fa-redo"></i>
                            إعادة تشغيل الأداة
                        </a>
                        <a href="database_health_check.php" class="btn btn-info ms-2">
                            <i class="fas fa-heartbeat"></i>
                            فحص صحة قاعدة البيانات
                        </a>
                        <a href="check_tables.php" class="btn btn-success ms-2">
                            <i class="fas fa-table"></i>
                            فحص الجداول
                        </a>
                    </div>

                    <?php if ($error_count > 0): ?>
                    <div class="alert alert-warning mt-4">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير</h6>
                        <p>تم العثور على <?php echo $error_count; ?> خطأ أثناء عملية الإصلاح. يُرجى مراجعة السجل أعلاه والتواصل مع المطور إذا استمرت المشاكل.</p>
                    </div>
                    <?php else: ?>
                    <div class="alert alert-success mt-4">
                        <h6><i class="fas fa-check-circle"></i> تم بنجاح!</h6>
                        <p>تم إصلاح جميع مشاكل قاعدة البيانات بنجاح. يمكنك الآن استخدام النظام بشكل طبيعي.</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
