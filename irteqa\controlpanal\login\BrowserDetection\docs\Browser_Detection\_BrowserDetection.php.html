<html>
<head>
<title>Docs for page BrowserDetection.php</title>
<link rel="stylesheet" type="text/css" href="../media/style.css">
</head>
<body>

<table border="0" cellspacing="0" cellpadding="0" height="48" width="100%">
  <tr>
    <td class="header_top">Browser_Detection</td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
  <tr>
    <td class="header_menu">
        
                                    
                              		  [ <a href="../classtrees_Browser_Detection.html" class="menu">class tree: Browser_Detection</a> ]
		  [ <a href="../elementindex_Browser_Detection.html" class="menu">index: Browser_Detection</a> ]
		  	    [ <a href="../elementindex.html" class="menu">all elements</a> ]
    </td>
  </tr>
  <tr><td class="header_line"><img src="../media/empty.png" width="1" height="1" border="0" alt=""  /></td></tr>
</table>

<table width="100%" border="0" cellpadding="0" cellspacing="0">
  <tr valign="top">
    <td width="200" class="menu">
      <b>Packages:</b><br />
              <a href="../li_Browser_Detection.html">Browser_Detection</a><br />
            <br /><br />
                        <b>Files:</b><br />
      	  <div class="package">
			<a href="../Browser_Detection/_BrowserDetection.php.html">		BrowserDetection.php
		</a><br>
	  </div><br />

      
      
            <b>Classes:</b><br />
        <div class="package">
		    		<a href="../Browser_Detection/BrowserDetection.html">BrowserDetection</a><br />
	  </div>

                </td>
    <td>
      <table cellpadding="10" cellspacing="0" width="100%" border="0"><tr><td valign="top">

<h1>Procedural File: BrowserDetection.php</h1>
Source Location: /BrowserDetection.php<br /><br />

<br>
<br>

<div class="contents">
<h2>Classes:</h2>
<dt><a href="../Browser_Detection/BrowserDetection.html">BrowserDetection</a></dt>
	<dd>The BrowserDetection class facilitates the identification of the user's environment such as Web browser, version,  platform and device type.</dd>
</div><br /><br />

<h2>Page Details:</h2>
Browser detection class file.<br /><br /><p>This file contains everything required to use the BrowserDetection class. Tested with PHP 5.3.29 - 7.4.0.</p><p>This program is free software; you can redistribute it and/or modify it under the terms of EITHER the MIT License  (SPDX short identifier: MIT) OR the GNU Lesser General Public License Version 3 (SPDX short identifier:  LGPL-3.0-only). License details can be found in the LICENSE-*.md files of this project or on the below URLs.</p><br /><br /><br />
<h4>Tags:</h4>
<div class="tags">
<table border="0" cellspacing="0" cellpadding="0">
  <tr>
    <td><b>author:</b>&nbsp;&nbsp;</td><td>Alexandre Valiquette</td>
  </tr>
  <tr>
    <td><b>version:</b>&nbsp;&nbsp;</td><td>2.9.7</td>
  </tr>
  <tr>
    <td><b>copyright:</b>&nbsp;&nbsp;</td><td>Copyright (c) 2022, Wolfcast</td>
  </tr>
  <tr>
    <td><b>link:</b>&nbsp;&nbsp;</td><td><a href="https://wolfcast.com/">https://wolfcast.com/</a></td>
  </tr>
  <tr>
    <td><b>last-modified:</b>&nbsp;&nbsp;</td><td>September 20, 2022</td>
  </tr>
  <tr>
    <td><b>filesource:</b>&nbsp;&nbsp;</td><td><a href="../__filesource/fsource_Browser_Detection__BrowserDetection.php.html">Source Code for this file</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="https://spdx.org/licenses/GPL-3.0-only.html">https://spdx.org/licenses/GPL-3.0-only.html</a></td>
  </tr>
  <tr>
    <td><b>license:</b>&nbsp;&nbsp;</td><td><a href="https://spdx.org/licenses/MIT.html">https://spdx.org/licenses/MIT.html</a></td>
  </tr>
</table>
</div>
<br /><br />
<br /><br />
<br /><br />
<br />

        <div class="credit">
		    <hr />
		    Documentation generated on Tue, 20 Sep 2022 23:35:13 -0400 by <a href="http://www.phpdoc.org">phpDocumentor 1.4.3</a>
	      </div>
      </td></tr></table>
    </td>
  </tr>
</table>

</body>
</html>
