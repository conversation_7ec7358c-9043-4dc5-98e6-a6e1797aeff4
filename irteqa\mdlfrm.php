
	<title>Edit</title>

<script src="js/popper.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/fontawesome-all.min.js"></script>
<script src="js/form-jquery.js" type="text/javascript"></script>	
<script src="js/main-js.js"></script>
<style>
	div#edit {
		border-radius: 12px;
		width: 99%;
		border: 1px solid #dcdcdc;
    height: 180px;
    direction: rtl;
    margin-top: 100px;}
	div hr {
    margin: 5px 20px;
}
	.form-select2 {
    display: block;
    width: 100%;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    -moz-padding-start: calc(0.75rem - 3px);
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-image: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e);
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}
input[type=submit]:hover {
background-color: #62a874;
}
button.close {
    color: black;
    background: none;
}
 .form-select2 select {
    word-wrap: normal;
}
	select.form-select2 {
    margin: 10px;
    width: 8%;
    padding: 4px;
}
.inputd {
    display: flex;
	direction: rtl;
}
.seldn.style\= {
    display: inline-flex;
	float: right;
    width: 100%;
}
button[type=submit] {
    color: white;
    float: right;
    width: 10%;
    margin: 8px;
    height: 40px;
    border: none;
    border-radius: 20px;
    background-color: #04aa30;
    box-shadow: 0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%);
    
}
button[type=button] {
    color: white;
    float: right;
    width: 10%;
    margin: 8px;
    height: 40px;
    border: none;
    border-radius: 20px;
    background-color: #04aa30;
    box-shadow: 0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%);
    
}
</style>

	<?php
	
	if(isset($_GET['user_edit'])){
				
		$id = $_GET['user_edit'];
					$sql = mysqli_query($conn , " select * from orderss where id = '$id' ");
					$row= mysqli_fetch_assoc($sql);
		
		echo '
<div class="modal fade" id="edit" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        
        <button style="width: 40px; background:#c04848;" type="button" class="close" onclick="goBack()" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
	  <hr>
		<form class="contact-form "  action="edit.php" method="post" id="" enctype="multipart/form-data">
						
						
					  <div class="inputd">
						<label for="colFormLabel" >الخدمة</label>
						
						  <input type="text"  name="serv" value="'.$row['serv'].'" >
						  <input type="hidden"  name="id" value="'.$row['id'].'" >
						
					 
					  
					  
						<label for="colFormLabel" >الإسم</label>
						
						  <input type="text" class="form-control" name="fname" value="'.$row['fname'].'" >
						
					  
					  
					  
						<label for="colFormLabel" >الهاتف</label>
						
						  <input type="text" class="form-control" name="calll" value="'.$row['calll'].'" >
						
					 
					  
					  
						<label  " for="colFormLabel" >الصفة</label>
						
						 
              <select class="form-select" aria-label="Default select example" style="text-align: center;font-size:20px;" value="'.$row['who'].'" name="who">
              <option >منفذ</option>
              <option >وسيط</option>
              <option >عميل</option>
					  
					  
						<label for="colFormLabel" >المبلغ</label>
						
						  <input type="text" name="sell"  value="'.$row['sell'].'" >
						</div>
					  <div class="seldn style="width:100%">
					  
<select name="injaz" value="'.$row['injaz'].'"   class="form-select2" aria-label="Default select example" style="text-align: center;background-size: 9px 6px;font-size:20px;">
  <option >جاري الإنجاز</option>
  <option >تم الإنجاز</option>
  <option >ملغي</optio>
</select  >
<select Disabled class="form-select2" aria-label="Default select example">
<option selected>العنوان</option>
<option value="1">One</option>
<option value="2">Two</option>
<option value="3">Three</option>
</select>

		   <div id="product_ar_edit_result" class="text-center col-md-12" style="margin:10px 0;"></div>
        <button style="background:#c04848;" type="button" onclick="goBack()" class="btn btn-danger" data-dismiss="modal">رجوع</button>
		 
        <button type="submit" name="menu_ar_edit" class="btn btn-success">تعديل</button>
		<button style="background:black" type="submit" name="delete" class="btn btn-dark">حذف</button>
		  </div></form>

      </div>
    </div>
  </div>
</div>				
		';
	}
	
?>
</body>


