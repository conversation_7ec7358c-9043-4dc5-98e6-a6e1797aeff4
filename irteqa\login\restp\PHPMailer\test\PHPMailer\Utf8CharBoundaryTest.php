<?php

/**
 * PHP<PERSON>ailer - PHP email transport unit tests.
 * PHP version 5.5.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR>
 * @copyright 2012 - 2020 <PERSON>
 * @copyright 2004 - 2009 <PERSON>
 * @license   http://www.gnu.org/copyleft/lesser.html GNU Lesser General Public License
 */

namespace PHPMailer\Test\PHPMailer;

use PHPMailer\Test\TestCase;

/**
 * Test UTF8 character boundary functionality.
 *
 * @covers \PHPMailer\PHPMailer\PHPMailer::utf8CharBoundary
 *
 * @todo Add more testcases to properly cover all paths in the method!
 */
final class Utf8CharBoundaryTest extends TestCase
{
    /**
     * Verify that the utf8CharBoundary() returns the correct last character boundary for encoded text.
     *
     * @dataProvider dataUtf8CharBoundary
     *
     * @param string $encodedText UTF-8 QP text to use as input string.
     * @param int    $maxLength   Max length to pass to the function.
     * @param int    $expected    Expected function output.
     */
    public function testUtf8CharBoundary($encodedText, $maxLength, $expected)
    {
        $this->assertSame($expected, $this->Mail->utf8CharBoundary($encodedText, $maxLength));
    }

    /**
     * Data provider.
     *
     * @return array
     */
    public function dataUtf8CharBoundary()
    {
        return [
            'Encoded word with multibyte char first byte' => [
                'encodedText' => 'H=E4tten',
                'maxLength'   => 3,
                'expected'    => 1,
            ],
            'Encoded single byte char' => [
                'encodedText' => '=0C',
                'maxLength'   => 3,
                'expected'    => 3,
            ],
            'Encoded word with multi byte char middle byte' => [
                'encodedText' => 'L=C3=B6rem',
                'maxLength'   => 6,
                'expected'    => 1,
            ],
        ];
    }
}
