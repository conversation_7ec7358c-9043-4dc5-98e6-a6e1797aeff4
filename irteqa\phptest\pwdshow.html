<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اظهار كلمة المرور</title>
    <script>
        // تغيير نوع الإدخال بين كلمة المرور ونص عند الضغط على الزر
        function togglePasswordVisibility() {
            var passwordField = document.getElementById("password");
            var passwordToggle = document.getElementById("password-toggle");

            if (passwordField.type === "password") {
                passwordField.type = "text";
                passwordToggle.textContent = "إخفاء كلمة المرور";
            } else {
                passwordField.type = "password";
                passwordToggle.textContent = "اظهار كلمة المرور";
            }
        }
    </script>
</head>
<body>
    <h1>اظهار كلمة المرور</h1>
    <label for="password">كلمة المرور:</label>
    <input type="password" id="password" name="password" required>
    <br>
    <button type="button" id="password-toggle" onclick="togglePasswordVisibility()">اظهار كلمة المرور</button><br>
    <h1>اظهار/إخفاء كلمة المرور</h1>
    <label for="password">كلمة المرور:</label>
    <input type="text" id="password" name="password" required>
    <button type="button" onclick="togglePasswordVisibility()">اظهار/إخفاء كلمة المرور</button>

    <script>
        var passwordField = document.getElementById("password");
        var passwordVisible = false;

        function togglePasswordVisibility() {
            passwordVisible = !passwordVisible;

            if (passwordVisible) {
                passwordField.type = "text";
            } else {
                passwordField.type = "password";
            }
        }
    </script>
</body>
</html>
في هذا المثال، تم إنشاء وظيفة JavaScript تسمى togglePasswordVisibility() التي تقوم بتغيير نوع الإدخال (input type) بين "password" و "text" عند الضغط على الزر. الزر يعرض نص "اظهار كلمة المرور" عندما يكون نوع الإدخال "password" ويعرض "إخفاء كلمة المرور" عندما يكون نوع الإدخال "text". بهذه الطريقة، يمكن للمستخدمين التبديل بين عرض وإخفاء كلمة المرور.





