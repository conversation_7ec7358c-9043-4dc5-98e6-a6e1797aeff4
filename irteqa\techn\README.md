# Web-based Ordering and Sales Management System with Customer Transaction Behavioral Analysis for TECHNOHOLICS

> This project aims to help TECHNOHOLICS in monitoring transactions and forecasting sales of the business to come up with better marketing strategies based on the customers’ purchase data. Using the Apriori algorithm, the system generated the frequent item set of each customer and from it, association rules were produced to provide suggestions or recommendations about what the customer might purchase.

## Built With

* [AJAX](https://developer.mozilla.org/en-US/docs/Web/Guide/AJAX) - Used to update data in the website asynchronously
* [CodeIgniter 3.1.5](https://codeigniter.com/) - PHP web framework used
* [JavaScript](https://developer.mozilla.org/en-US/docs/Web/JavaScript)
* [MySQL](https://www.mysql.com/) - SQL Database
* [PHP 7.0.30](https://www.php.net/)

## Setup

### Instructions

1. Install [XAMPP v3.2.2](https://www.apachefriends.org/)
1. <PERSON>lone or download this repository
1. Move the repository folder to the `/xampp/htdocs/` folder on your local hard drive
1. Open the XAMPP application and start the Apache and MySQL modules (under Actions)
1. Navigate to http://localhost/phpmyadmin
1. Create `itemdb` database and import the development database: `Project/itemdb.sql`
1. In a browser, navigate to http://localhost/project

## Authors

* [Chris John Agarap](https://github.com/seeej) - Data Analyst & Scrum master
* Veo Thadeus Calimlim - Back End Web developer
* Andew Cyle Leona - Project Manager
* Michaela Mallari - Front End Web developer
