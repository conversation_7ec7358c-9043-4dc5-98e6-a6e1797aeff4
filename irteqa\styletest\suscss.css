html.s-js-no .page-search section .content ul,html.s-js-yes .page-search section .content ul:not(.app-grid-loaded) {
    grid-template-columns: repeat(auto-fill, minmax(136px, 1fr));
    grid-gap: 0 16px;
    display: grid
}

html.s-js-no .page-search section .content ul li:nth-child(1),html.s-js-yes .page-search section .content ul:not(.app-grid-loaded) li:nth-child(1) {
    grid-row: span 4
}

html.s-js-no .page-search section .content ul li,html.s-js-yes .page-search section .content ul:not(.app-grid-loaded) li {
    margin-bottom: 16px
}

html.s-js-no .page-search section .content ul li,html.s-js-yes .page-search section .content ul:not(.app-grid-loaded) li {
    grid-row: span 3;
    position: relative;
    padding-top: 125%
}

html.s-js-no .page-search section .content ul li>*,html.s-js-yes .page-search section .content ul:not(.app-grid-loaded) li>* {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

@media screen and (min-width: 472px) {
    html.s-js-no .page-search section .content ul li:nth-child(3),html.s-js-yes .page-search section .content ul:not(.app-grid-loaded) li:nth-child(3) {
        grid-row:span 4
    }
}

@media screen and (min-width: 550px) {
    html.s-js-no .page-search section .content ul,html.s-js-yes .page-search section .content ul:not(.app-grid-loaded) {
        grid-template-columns:repeat(auto-fill, minmax(150px, 1fr))
    }
}

@media screen and (min-width: 800px) {
    html.s-js-no .page-search section .content ul,html.s-js-yes .page-search section .content ul:not(.app-grid-loaded) {
        grid-template-columns:repeat(auto-fill, minmax(170px, 1fr))
    }
}

body>aside.popups div.popup.page-search {
    max-width: 1000px;
    background: #fff
}

body>aside.popups div.popup.page-search section {
    padding: 40px 24px 72px
}

@media screen and (min-width: 1048px) {
    body>aside.popups div.popup.page-search section {
        padding:40px 48px 72px
    }
}

body>aside.popups div.popup.page-search section nav.pagination.vertical {
    position: absolute;
    right: 0;
    bottom: 48px;
    left: 0;
    padding-top: 0
}

body>aside.popups div.popup.page-search section nav.pagination.vertical>div {
    display: none
}

main.search section {
    padding: 0 16px 72px
}

main.search section header {
    padding: 8px 0 0
}

.page-search section {
    position: relative;
    margin: 0 auto;
    max-width: 800px
}

.page-search section::before {
    height: 30px;
    background-image: url('/dist/component/loading/loading-theme.svg?h=183ca65db9');
    position: absolute;
    top: 50%;
    left: 50%;
    content: '';
    display: block;
    width: 30px;
    transform: translate(-50%, -50%) rotate(0deg);
    transform-origin: center;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0
}

.page-search section::before {
    will-change: transform;
    opacity: 1;
    animation: spinner 0.6s linear infinite
}

@keyframes spinner {
    to {
        transform: translate(-50%, -50%) rotate(360deg)
    }
}

.page-search section::before {
    display: none;
    position: fixed
}

.page-search section.state-loading::before {
    display: block
}

.page-search section header {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    margin-bottom: 16px
}

.page-search section header h2 {
    display: inline;
    margin-top: 8px;
    color: #1a1a1a;
    font-size: 24px;
    font-weight: bold
}

.page-search section header a {
    flex-shrink: 0;
    margin-bottom: 2px
}

.page-search section header a span {
    background: #fe6771 linear-gradient(to bottom right, #fd7e3f, #ff4da0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    color: #fe6771;
    position: relative;
    cursor: pointer;
    display: inline-block;
    margin: -10px;
    padding: 10px;
    font-weight: bold
}

html:not(.s-touchevents-yes) .page-search section header a span:hover::after {
    width: calc(100% - 20px)
}

.page-search section header a span:after {
    background: #fe6771 linear-gradient(to bottom right, #fd7e3f, #ff4da0);
    content: '';
    display: block;
    position: absolute;
    bottom: 10px;
    width: 0;
    height: 1px;
    transition: width .3s
}

html[dir=ltr] .page-search section header a span:after {
    left: 10px
}

html[dir=rtl] .page-search section header a span:after {
    right: 10px
}

.page-search section div.empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    box-sizing: border-box;
    width: 100%;
    max-width: 500px;
    padding: 24px 12px 96px;
    text-align: center;
    height: 600px;
    padding-right: 12px;
    padding-left: 12px
}

.page-search section div.empty::before {
    content: '';
    display: block;
    flex: 1 0 50%;
    width: 100%;
    max-width: 444px;
    min-height: 152px;
    background-position: bottom;
    background-size: contain;
    background-repeat: no-repeat
}

.page-search section div.empty.type-boost-active::before {
    background-image: url('/dist/component/empty-view/boost-active.svg?h=3c0939a3e2')
}

.page-search section div.empty.type-boost-inactive::before {
    background-image: url('/dist/component/empty-view/boost-inactive.svg?h=3e3a20d911')
}

.page-search section div.empty.type-boost-empty::before {
    background-image: url('/dist/component/empty-view/boost-empty.svg?h=5c1912fef6')
}

.page-search section div.empty:not([class*=type-boost])::before {
    background-image: url('/dist/page/feature/search/empty-content-search.svg?h=c85e789a3a')
}

.page-search section div.empty div {
    flex: 1 0 50%;
    box-sizing: border-box;
    padding-top: 12px
}

.page-search section div.empty[class*=type-boost] div>a,.page-search section div.empty[class*=type-boost] div>button {
    position: relative;
    z-index: 0;
    transition: color .3s,box-shadow .3s,border-color .3s;
    color: #fff;
    background: linear-gradient(to bottom right, #61a5ff, #c547ff)
}

.page-search section div.empty[class*=type-boost] div>a.loading,.page-search section div.empty[class*=type-boost] div>button.loading {
    position: relative
}

@keyframes hide-content {
    0% {
        transform: scale(1)
    }

    30% {
        transform: scale(1.2)
    }

    100% {
        transform: scale(0)
    }
}

.page-search section div.empty[class*=type-boost] div>a.loading span,.page-search section div.empty[class*=type-boost] div>button.loading span {
    animation: hide-content 0.5s;
    animation-fill-mode: forwards
}

.page-search section div.empty[class*=type-boost] div>a.loading::after,.page-search section div.empty[class*=type-boost] div>button.loading::after {
    height: 30px;
    background-image: url('/dist/component/loading/loading-light.svg?h=fbae7afa3c');
    position: absolute;
    top: 50%;
    left: 50%;
    content: '';
    display: block;
    width: 30px;
    transform: translate(-50%, -50%) rotate(0deg);
    transform-origin: center;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0
}

.page-search section div.empty[class*=type-boost] div>a.loading::after,.page-search section div.empty[class*=type-boost] div>button.loading::after {
    transition: opacity .3s .3s;
    will-change: transform;
    opacity: 1;
    animation: spinner 0.6s linear infinite
}

@keyframes spinner {
    to {
        transform: translate(-50%, -50%) rotate(360deg)
    }
}

.page-search section div.empty[class*=type-boost] div>a span,.page-search section div.empty[class*=type-boost] div>button span {
    display: block
}

html:not(.s-touchevents-yes) .page-search section div.empty[class*=type-boost] div>a:not(.disabled):not(.disabled):hover:not(.loading)::after,html:not(.s-touchevents-yes) .page-search section div.empty[class*=type-boost] div>button:not(.disabled):not(.disabled):hover:not(.loading)::after {
    opacity: 0.55
}

html:not(.s-touchevents-yes) .page-search section div.empty[class*=type-boost] div>a:not(.disabled):not(.disabled):active:not(.loading)::after,html:not(.s-touchevents-yes) .page-search section div.empty[class*=type-boost] div>button:not(.disabled):not(.disabled):active:not(.loading)::after,html.s-touchevents-yes .page-search section div.empty[class*=type-boost] div>a:not(.disabled):not(.disabled):active:not(.loading)::after,html.s-touchevents-yes .page-search section div.empty[class*=type-boost] div>button:not(.disabled):not(.disabled):active:not(.loading)::after {
    opacity: 1
}

.page-search section div.empty[class*=type-boost] div>a:not(.loading)::after,.page-search section div.empty[class*=type-boost] div>button:not(.loading)::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    will-change: opacity;
    content: '';
    z-index: -1;
    background: linear-gradient(to bottom right, #548ed9, #a73ad9);
    opacity: 0;
    transition: opacity .3s
}

.page-search section div.empty[class*=type-boost] div>a:disabled,.page-search section div.empty[class*=type-boost] div>a.disabled,.page-search section div.empty[class*=type-boost] div>button:disabled,.page-search section div.empty[class*=type-boost] div>button.disabled {
    background: linear-gradient(to bottom right, #61a5ff, #c547ff) !important
}

.page-search section div.empty:not([class*=type-boost]) div>a,.page-search section div.empty:not([class*=type-boost]) div>button {
    position: relative;
    z-index: 0;
    transition: color .3s,box-shadow .3s,border-color .3s;
    color: #fff;
    background: #fe6771 linear-gradient(to bottom right, #fd7e3f, #ff4da0)
}

html:not(.s-touchevents-yes) .page-search section div.empty:not([class*=type-boost]) div>a:not(.disabled):not(.disabled):hover:not(.loading)::after,html:not(.s-touchevents-yes) .page-search section div.empty:not([class*=type-boost]) div>button:not(.disabled):not(.disabled):hover:not(.loading)::after {
    opacity: 0.55
}

html:not(.s-touchevents-yes) .page-search section div.empty:not([class*=type-boost]) div>a:not(.disabled):not(.disabled):active:not(.loading)::after,html:not(.s-touchevents-yes) .page-search section div.empty:not([class*=type-boost]) div>button:not(.disabled):not(.disabled):active:not(.loading)::after,html.s-touchevents-yes .page-search section div.empty:not([class*=type-boost]) div>a:not(.disabled):not(.disabled):active:not(.loading)::after,html.s-touchevents-yes .page-search section div.empty:not([class*=type-boost]) div>button:not(.disabled):not(.disabled):active:not(.loading)::after {
    opacity: 1
}

.page-search section div.empty:not([class*=type-boost]) div>a:not(.loading)::after,.page-search section div.empty:not([class*=type-boost]) div>button:not(.loading)::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    will-change: opacity;
    content: '';
    z-index: -1;
    background: #cb525a linear-gradient(to bottom right, #cb6634, #cc3e80);
    opacity: 0;
    transition: opacity .3s
}

.page-search section div.empty:not([class*=type-boost]) div>a:disabled,.page-search section div.empty:not([class*=type-boost]) div>a.disabled,.page-search section div.empty:not([class*=type-boost]) div>button:disabled,.page-search section div.empty:not([class*=type-boost]) div>button.disabled {
    background: #fe6771 linear-gradient(to bottom right, #fd7e3f, #ff4da0) !important
}

.page-search section div.empty div h2 {
    font-size: 18px;
    font-weight: bold
}

.page-search section div.empty div p {
    margin-top: 12px;
    color: #8c8c8c
}

.page-search section div.empty div>a,.page-search section div.empty div>button {
    overflow: hidden;
    line-height: 1.5;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    box-sizing: border-box;
    box-shadow: 0 2px 8px 0 rgba(77,77,77,0.2);
    cursor: pointer;
    font-weight: bold;
    text-align: center;
    padding: 0 24px;
    border-radius: 48px;
    line-height: 48px;
    margin-top: 24px;
    width: 188px
}

html:not(.s-touchevents-yes) .page-search section div.empty div>a:not(.disabled):not(.disabled):active,html:not(.s-touchevents-yes) .page-search section div.empty div>button:not(.disabled):not(.disabled):active,html.s-touchevents-yes .page-search section div.empty div>a:not(.disabled):not(.disabled):active,html.s-touchevents-yes .page-search section div.empty div>button:not(.disabled):not(.disabled):active {
    box-shadow: none
}

.page-search section div.empty div>a:disabled,.page-search section div.empty div>a.disabled,.page-search section div.empty div>button:disabled,.page-search section div.empty div>button.disabled {
    opacity: .6;
    cursor: default
}

.page-search section:not(.state-empty) div.empty {
    display: none
}

.page-search section.state-empty .content {
    display: none
}

html.s-js-yes .page-search section .content ul {
    will-change: height;
    position: relative
}

html.s-js-yes .page-search section .content ul.animate {
    transition: height .2s
}

html.s-js-yes .page-search section .content ul>* {
    will-change: opacity, transform, width, height;
    z-index: 1
}

html.s-js-yes .page-search section .content ul>*.hide {
    z-index: 0;
    opacity: 0;
    transition: opacity .2s ease-out
}

html.s-js-yes .page-search section .content ul>*.animate {
    transition: transform .2s,width .2s,height .2s;
    transition-timing-function: ease-out
}

.page-search section .content ul li {
    transition: opacity .3s
}

.page-search section.state-loading .content ul li {
    opacity: 0;
    visibility: hidden;
    transition: opacity .3s,visibility 0s .3s
}

.page-search section .content ul li .profile-card-container {
    height: 100%
}

.page-search section .content ul li .profile-card-container>.profile-card {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
    transition-duration: 0.7s
}

.page-search section .content ul li .profile-card-container>.profile-card::before {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    content: '';
    z-index: 1;
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.5s 0.2s ease-in-out
}

.page-search section .content ul li .profile-card-container>.profile-card[class*=relation-]::before,.page-search section .content ul li .profile-card-container>.profile-card.block-tx::before {
    opacity: 0.5
}

.page-search section .content ul li .profile-card-container>.profile-card.relation-reject-tx::before {
    background: #b3b3b3 linear-gradient(to bottom, #c7c7c7, #9e9e9e)
}

.page-search section .content ul li .profile-card-container>.profile-card.relation-interest-tx::before {
    background: #f34f80 linear-gradient(to bottom right, #ff80b5, #fb2355)
}

.page-search section .content ul li .profile-card-container>.profile-card.relation-interest-mutual::before {
    background: #fe6771 linear-gradient(to bottom right, #fd7e3f, #ff4da0)
}

.page-search section .content ul li .profile-card-container>.profile-card.relation-message-mutual::before {
    background: #fe6771 linear-gradient(to bottom right, #fd7e3f, #ff4da0)
}

.page-search section .content ul li .profile-card-container>.profile-card.relation-message-tx::before {
    background: #2dc5d2 linear-gradient(to bottom right, #79ecc6, #0da6f2)
}

.page-search section .content ul li .profile-card-container>.profile-card.relation-unmatch::before {
    background: #b3b3b3 linear-gradient(to bottom, #c7c7c7, #9e9e9e)
}

.page-search section .content ul li .profile-card-container>.profile-card.block-tx::before {
    background: #b3b3b3 linear-gradient(to bottom, #c7c7c7, #9e9e9e)
}

.page-search section .content ul li .profile-card-container>.profile-card .app-icon {
    width: 24px;
    height: 24px;
    fill: #fff;
    display: block;
    position: absolute;
    top: 8px;
    z-index: 2
}

@keyframes fade-in {
    to {
        opacity: 1
    }
}

html[dir=ltr] .page-search section .content ul li .profile-card-container>.profile-card .app-icon {
    left: 8px
}

html[dir=rtl] .page-search section .content ul li .profile-card-container>.profile-card .app-icon {
    right: 8px
}

.page-search section .content ul li .profile-card-container>.profile-card .app-icon.animate-in {
    opacity: 0;
    animation: fade-in 0.5s ease-in-out 0.2s 1 forwards
}

.page-search section .content ul li .profile-card-container>.profile-card .app-icon.block-action ~ .relation-action {
    display: none
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container {
    width: 100%;
    height: 100%;
    position: relative
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container.online .online {
    position: absolute;
    z-index: 0;
    top: 8px
}

html[dir=ltr] .page-search section .content ul li .profile-card-container>.profile-card .portrait-container.online .online {
    right: 8px
}

html[dir=rtl] .page-search section .content ul li .profile-card-container>.profile-card .portrait-container.online .online {
    left: 8px
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container.online .online span {
    display: inline-block;
    transition: border-color .3s
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container.online .online span.online-now {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(to bottom right, #aae462, #89da25)
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container.online .online span.online-recently {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 3px solid #99df43
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container .portrait {
    width: 100%;
    height: 100%;
    display: block;
    position: relative;
    box-sizing: border-box;
    border-radius: 12px
}

html.s-touchevents-no .page-search section .content ul li .profile-card-container>.profile-card .portrait-container .portrait {
    overflow: hidden;
    transform: translateZ(0)
}

html.s-touchevents-no .page-search section .content ul li .profile-card-container>.profile-card .portrait-container .portrait img {
    transition: transform 0.7s
}

html.s-touchevents-no .page-search section .content ul li .profile-card-container>.profile-card .portrait-container .portrait:hover img:first-of-type {
    transform: scale(1.03)
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container .portrait img {
    -webkit-user-select: none;
    user-select: none;
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
    object-fit: cover
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container .portrait img:not(:first-of-type) {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0
}

.page-search section .content ul li .profile-card-container>.profile-card:not(.empty) .portrait-container .portrait::after {
    background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.1) 30%, rgba(0,0,0,0.3));
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    content: '';
    border-radius: 12px
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container .portrait {
    width: 100%;
    height: 100%;
    box-shadow: 0 6px 24px 0 rgba(0,0,0,0.15)
}

.page-search section .content ul li .profile-card-container>.profile-card .portrait-container.online .online {
    z-index: 4
}

.page-search section .content ul li .profile-card-container>.profile-card .match-score {
    box-shadow: 0 6px 24px 0 rgba(0,0,0,0.15),inset 0 0 0 24px rgba(0,0,0,0.15);
    background: rgba(192,192,192,0.5);
    color: #fff;
    box-shadow: 0 6px 24px 0 rgba(0,0,0,0.15);
    display: inline-block;
    vertical-align: baseline;
    border-radius: 100px;
    font-size: 15px;
    font-weight: bold;
    position: absolute;
    top: 8px
}

.page-search section .content ul li .profile-card-container>.profile-card .match-score.small {
    padding: 8px
}

.page-search section .content ul li .profile-card-container>.profile-card .match-score.large {
    padding: 8px 10px
}

html[dir=ltr] .page-search section .content ul li .profile-card-container>.profile-card .match-score {
    right: 8px
}

html[dir=rtl] .page-search section .content ul li .profile-card-container>.profile-card .match-score {
    left: 8px
}

.page-search section .content ul li .profile-card-container>.profile-card .details {
    position: absolute;
    top: auto;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 3;
    padding: 8px;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.5)
}

.page-search section .content ul li .profile-card-container>.profile-card .details .boost-icon {
    width: 20px;
    height: 20px;
    display: block;
    margin-bottom: 3px;
    background: url('/dist/component/profile/boost-badge.svg?h=3d4184d3c7') center/contain no-repeat
}

.page-search section .content ul li .profile-card-container>.profile-card .details h3 {
    display: flex;
    align-items: baseline;
    font-size: 18px;
    font-weight: bold
}

.page-search section .content ul li .profile-card-container>.profile-card .details h3 bdi,.page-search section .content ul li .profile-card-container>.profile-card .details h3 span {
    vertical-align: middle
}

.page-search section .content ul li .profile-card-container>.profile-card .details h3 bdi {
    overflow: hidden;
    line-height: 1.5;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: calc(100% - 37px)
}

html[dir=ltr] .page-search section .content ul li .profile-card-container>.profile-card .details h3 bdi {
    margin-right: 4px
}

html[dir=rtl] .page-search section .content ul li .profile-card-container>.profile-card .details h3 bdi {
    margin-left: 4px
}

html[dir=ltr] .page-search section .content ul li .profile-card-container>.profile-card .details h3 span {
    text-align: right
}

html[dir=rtl] .page-search section .content ul li .profile-card-container>.profile-card .details h3 span {
    text-align: left
}

.page-search section .content ul li .profile-card-container>.profile-card .details h3 span::before {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    display: inline-block;
    vertical-align: middle;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.5)
}

html[dir=ltr] .page-search section .content ul li .profile-card-container>.profile-card .details h3 span::before {
    margin-right: 4px
}

html[dir=rtl] .page-search section .content ul li .profile-card-container>.profile-card .details h3 span::before {
    margin-left: 4px
}

.page-search section .content ul li .profile-card-container>.profile-card .details p {
    overflow: hidden;
    line-height: 1.5;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    margin-top: -5px
}

.page-search section .content ul li .profile-card-container>.profile-card .details p .online {
    vertical-align: middle
}

.page-search section .content ul li .profile-card-container>.profile-card .details p .online span {
    display: inline-block;
    transition: border-color .3s
}

.page-search section .content ul li .profile-card-container>.profile-card .details p .online span.online-now {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: linear-gradient(to bottom right, #aae462, #89da25)
}

.page-search section .content ul li .profile-card-container>.profile-card .details p .online span.online-recently {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    border: 3px solid #99df43
}

html[dir=ltr] .page-search section .content ul li .profile-card-container>.profile-card .details p .online span {
    margin-right: 6px
}

html[dir=rtl] .page-search section .content ul li .profile-card-container>.profile-card .details p .online span {
    margin-left: 6px
}

.page-search section .content div.few {
    margin: 16px auto 0;
    max-width: 600px;
    text-align: center
}

.page-search section:not(.state-few) .content div.few {
    display: none
}

.page-search section .content div.few h3 {
    margin-bottom: 6px;
    font-size: 18px;
    font-weight: bold
}

.page-search section .content div.few p {
    color: #8c8c8c
}

.page-search section .content div.few a {
    overflow: hidden;
    line-height: 1.5;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    box-sizing: border-box;
    box-shadow: 0 2px 8px 0 rgba(77,77,77,0.2);
    cursor: pointer;
    font-weight: bold;
    text-align: center;
    padding: 0 24px;
    border-radius: 48px;
    line-height: 48px;
    position: relative;
    z-index: 0;
    transition: color .3s,box-shadow .3s,border-color .3s;
    color: #fff;
    background: #fe6771 linear-gradient(to bottom right, #fd7e3f, #ff4da0);
    margin-top: 24px
}

html:not(.s-touchevents-yes) .page-search section .content div.few a:not(.disabled):not(.disabled):active,html.s-touchevents-yes .page-search section .content div.few a:not(.disabled):not(.disabled):active {
    box-shadow: none
}

.page-search section .content div.few a:disabled,.page-search section .content div.few a.disabled {
    opacity: .6;
    cursor: default
}

html:not(.s-touchevents-yes) .page-search section .content div.few a:not(.disabled):not(.disabled):hover:not(.loading)::after {
    opacity: 0.55
}

html:not(.s-touchevents-yes) .page-search section .content div.few a:not(.disabled):not(.disabled):active:not(.loading)::after,html.s-touchevents-yes .page-search section .content div.few a:not(.disabled):not(.disabled):active:not(.loading)::after {
    opacity: 1
}

.page-search section .content div.few a:not(.loading)::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    will-change: opacity;
    content: '';
    z-index: -1;
    background: #cb525a linear-gradient(to bottom right, #cb6634, #cc3e80);
    opacity: 0;
    transition: opacity .3s
}

.page-search section .content div.few a:disabled,.page-search section .content div.few a.disabled {
    background: #fe6771 linear-gradient(to bottom right, #fd7e3f, #ff4da0) !important
}

.page-search section .content nav.pagination {
    margin-top: 24px
}
