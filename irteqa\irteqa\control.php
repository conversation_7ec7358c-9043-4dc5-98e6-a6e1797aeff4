<!DOCTYPE html>
<?php include ('conn2.php');?>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="style/foundation.min.css">
<link rel="stylesheet" href="style/jquery.dataTables.min.css">
<link rel="stylesheet" href="style/responsive.dataTables.min.css">
    <title>لوحة المعلومات</title>
    <style>
      body {
  display:block;
  width:100%;
    text-align: center;
    font-weight: bold;
}
    div.aa{
    direction: rtl;
    padding: 3px;
    border: 1px solid #ccc;
    width: 97%;
    height: 330px;
    margin: auto;
    margin-right: 20px;
    display: inline-table;
    border-radius:3px;
  }
  .card{
    background-color: #fff;
    width: 350px;
    /* box-shadow: 3px 5px 3px 0 rgb(85 85 85 / 20%), 0 6px 20px 0 rgb(125 125 125 / 19%); */
    border-radius: 30px;
    padding: 3px;
    height: 120px;
    /* margin: auto; */
    transition: 00.4s;
    text-align: right;
    margin: 10px 5px 5px 5px;
    height: 150px;
    box-shadow:0 1px 10px rgb(138 194 173);


}
.card h1{
  margin:4px;
  font-size: 30px;
    font-weight: bold;

}
.card .h1h1{
   ;font-size: 60px 
}
h2{
  margin-left:25px;
}
h4{
    font-size: 20px;
    text-align: center;
}
.cntrlg{
  float:right;
  margin:2px;
}
h3{font-size: 25px;
    font-weight: bolder;
    color: #1a73e8;
}
.hnum{
  text-align:center;font-size: 60px;color: #04AA6D;font-weight: bold;
}
.numdv{
  display: inline-flex;
}
@media screen and (max-width: 39.9375em) {
body{
	display: inline-table;
	zoom:0.50;
}
.card{
    float: inherit;
}
.card h1 {
    margin:2px;
    font-size: 30px;
    font-weight: bold;
}
.card .h1h1{
    font-size: 50px;
    
}
}
@media screen and (max-width:420px) {
    body{
    	display: inline-table;
	zoom:0.40;
.card h1 {
    margin:2px;
    font-size: 15px;
    font-weight: bold;
}
.card .h1h1{
    font-size: 30px;
    
}
}
    </style>
</head>

<body>
<?php include('head.php');?>
    <section>
        <div class="aa" >

                <div class="cntrlg">
            <div class="card">
              <h1>العملاء</h1><h1 class="h1h1" style="text-align:center;color: #04AA6D;font-weight: bold;">
  <?php
  // u364709247_shoping 5dN53znd 127.0.0.1
include('conn2.php');
            $sql = $database->prepare("SELECT * FROM orderss where who='عميل'");
            $sql->execute();
            echo $sql->rowcount();
            ?></h1>
            </div>
            <div class="card"><h1>إجمالي مبلغ الخدمات</h1><h1 class="h1h1" style="text-align:center;color: #04AA6D;font-weight: bold;">

   <?php
include('conn2.php');
$conn = mysqli_connect('localhost','u364709247_shoping','5dN53znd','u364709247_shoping');
 $sql = $database->prepare("SELECT * FROM orderss");
   //sql sum all culm itm query
   $sql = "SELECT  SUM(sell) from orderss where who='عميل'";
   $result = $conn->query($sql);
   //display data on web page
   while($row = mysqli_fetch_array($result)){
       echo $row['SUM(sell)'];
       
      }
            ?><h2 style="text-align:left;
            margin-top: -42px;">ريال</h2></h1>
            </div>
            </div>
            <div class="cntrlg">
            <div class="card" style="height: 305px;background: oldlace;"><h1>جاري الإنجاز</h1><h3>العملاء:</h3><div class="numdv"><h1 class="hnum" style="margin-right:20px">
  <?php
       include('conn2.php');
            $sql = $database->prepare("SELECT * FROM orderss where who='عميل'AND injaz='جاري الإنجاز'");
            $sql->execute();
            echo $sql->rowcount();
            ?></h1><h1 style="margin-right:100px" class="hnum">
<?php
include('conn2.php');

$sql = $database->prepare("SELECT * FROM orderss");
//sql sum all culm itm query
$sql = "SELECT  SUM(sell) from orderss where who='عميل' AND injaz='جاري الإنجاز'";
$result = $conn->query($sql);
//display data on web page
while($row = mysqli_fetch_array($result)){
    echo $row['SUM(sell)'];
   }
         ?><h4>ريال</h4></div><h3>الوسطاء:</h3><div class="numdv"><h1 class="hnum" style="margin-right:20px;    color: firebrick;">
         <?php
             include('conn2.php');
                   $sql = $database->prepare("SELECT * FROM orderss where ((who='وسيط' or who='منفذ') and injaz='جاري الإنجاز')");
                   $sql->execute();
                   echo $sql->rowcount();
                   ?></h1><h1 style="margin-right:100px;    color: firebrick;" class="hnum">
       <?php
       include('conn2.php');
       
       $sql = $database->prepare("SELECT * FROM orderss");
       //sql sum all culm itm query
       $sql = "SELECT  SUM(sell) from orderss where ((who='وسيط' or who='منفذ') and injaz='جاري الإنجاز')";
       $result = $conn->query($sql);
       //display data on web page
       while($row = mysqli_fetch_array($result)){
           echo $row['SUM(sell)'];
          }
                ?><h4>ريال</h4></div></div>
    </div>
    <div class="cntrlg">
            <div class="card" style="height: 305px;background: #e1efea;"><h1>تم اللإنجاز</h1><h3>العملاء:</h3><div class="numdv"><h1 class="hnum" style="margin-right:20px">
  <?php
       include('conn2.php');
            $sql = $database->prepare("SELECT * FROM orderss where ((who='عميل') and injaz='تم الإنجاز')");
            $sql->execute();
            echo $sql->rowcount();
            ?></h1><h1 style="margin-right:100px" class="hnum">
<?php
include('conn2.php');

$sql = $database->prepare("SELECT * FROM orderss");
//sql sum all culm itm query
$sql = "SELECT  SUM(sell) from orderss where who='عميل' and injaz='تم الإنجاز'";
$result = $conn->query($sql);
//display data on web page
while($row = mysqli_fetch_array($result)){
    echo $row['SUM(sell)'];
   }
         ?><h4>ريال</h4></div><h3>الوسطاء:</h3><div class="numdv"><h1 class="hnum" style="margin-right:20px;    color: firebrick;">
         <?php
              include('conn2.php');
                   $sql = $database->prepare("SELECT * FROM orderss where ((who='وسيط'or who='منفذ') AND injaz='تم الإنجاز')");
                   $sql->execute();
                   echo $sql->rowcount();
                   ?></h1><h1 style="margin-right:100px;    color: firebrick;" class="hnum">
       <?php
       include('conn2.php');
       
       $sql = $database->prepare("SELECT * FROM orderss");
       //sql sum all culm itm query
       $sql = "SELECT  SUM(sell) from orderss where ((who='وسيط'or who='منفذ') and injaz='تم الإنجاز')";
       $result = $conn->query($sql);
       //display data on web page
       while($row = mysqli_fetch_array($result)){
           echo $row['SUM(sell)'];
          }
                ?><h4>ريال</h4></div></div>
    </div>
            <div class="cntrlg">
            <div class="card"><h1>الوسطاء</h1><h1 class="h1h1" style="text-align:center; color: firebrick;font-weight: bold;">
  <?php
include('conn2.php');
            $sql = $database->prepare("SELECT * FROM orderss ً WHERE who='وسيط' or who='منفذ'");
            $sql->execute();
            echo $sql->rowcount();
            ?></h1>
            </div><div class="card" ><h1>إجمالي مبالغ الوسطاء</h1><h1 class="h1h1" style="text-align:center;color: firebrick;font-weight: bold;">

<?php
include('conn2.php');
$sql = $database->prepare("SELECT * FROM orderss");
   //sql sum all culm itm query
   $sql = "SELECT  SUM(sell) from orderss where who='وسيط' or who='منفذ'";
   $result = $conn->query($sql);
   //display data on web page
   while($row = mysqli_fetch_array($result)){
       echo $row['SUM(sell)'];
       
      }
         ?><h2 style="text-align:left;
         margin-top: -42px;">ريال</h2>
         <?php ?></h1>
            </div>
          </div>

        
        </div>
    </section>
</body>
</html>