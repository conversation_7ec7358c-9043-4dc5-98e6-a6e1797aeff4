<?php
/**
 * صفحة طباعة التقرير
 *
 * تستخدم هذه الصفحة لعرض التقرير بتنسيق مناسب للطباعة
 */

// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// التأكد من ضبط ترميز UTF-8 قبل أي إخراج
header('Content-Type: text/html; charset=utf-8');

redirectIfNotLoggedIn();

// تحديد الفترة الزمنية
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$report_type = isset($_GET['report_type']) ? $_GET['report_type'] : 'summary';
$customer_id = isset($_GET['customer_id']) ? intval($_GET['customer_id']) : 0;
$report_title = isset($_GET['title']) ? $_GET['title'] : __('reports');

// تحديد اتجاه اللغة
$lang_dir = getCurrentLang() === 'ar' ? 'rtl' : 'ltr';
$lang_code = getCurrentLang();
?>
<!DOCTYPE html>
<html lang="<?php echo $lang_code; ?>" dir="<?php echo $lang_dir; ?>">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo urldecode($report_title); ?></title>
    <?php if ($lang_dir === 'rtl'): ?>
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    <?php else: ?>
    <!-- Bootstrap LTR -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <?php endif; ?>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            body {
                padding: 20px;
            }
            .card {
                border: 1px solid #ddd !important;
                margin-bottom: 20px !important;
            }
            .card-header {
                background-color: #f8f9fa !important;
                color: #000 !important;
                border-bottom: 1px solid #ddd !important;
            }
            .table {
                width: 100% !important;
                border-collapse: collapse !important;
            }
            .table th, .table td {
                border: 1px solid #ddd !important;
                padding: 8px !important;
            }
            .table-striped tbody tr:nth-of-type(odd) {
                background-color: rgba(0, 0, 0, 0.05) !important;
            }
        }
        .print-controls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="print-controls no-print">
        <button class="btn btn-primary" onclick="window.print();">
            <i class="fas fa-print"></i> <?php echo __('print'); ?>
        </button>
        <button class="btn btn-secondary" onclick="window.close();">
            <i class="fas fa-times"></i> <?php echo __('close'); ?>
        </button>
    </div>

    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h1><?php
                // استخدام عنوان التقرير من ملف اللغة بدلاً من النص المرسل
                switch ($report_type) {
                    case 'sales_details':
                        echo __('sales_details');
                        break;
                    case 'purchases_details':
                        echo __('purchases_details');
                        break;
                    case 'top_products':
                        echo __('top_products');
                        break;
                    case 'top_customers':
                        echo __('top_customers');
                        break;
                    default:
                        echo __('reports');
                        break;
                }
                ?></h1>
                <p><?php echo __('period'); ?>: <?php echo date('d/m/Y', strtotime($start_date)) . ' - ' . date('d/m/Y', strtotime($end_date)); ?></p>
                <hr>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <?php
                // استيراد محتوى التقرير من الجلسة
                if (isset($_SESSION['report_content'])) {
                    echo $_SESSION['report_content'];
                    // حذف المحتوى من الجلسة بعد عرضه
                    unset($_SESSION['report_content']);
                } else {
                    echo '<div class="alert alert-warning">' . __('no_report_data') . '</div>';
                }
                ?>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12 text-center">
                <p><?php echo __('app_name'); ?> &copy; <?php echo date('Y'); ?></p>
            </div>
        </div>
    </div>

    <script>
        // طباعة التقرير تلقائيًا عند تحميل الصفحة
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html>
