{"data": {"profile": {"heading": "Profile", "photo": "https://i.imgur.com/Icr472Z.jpg", "firstName": "Nancy", "lastName": "<PERSON>", "subtitle": "Customer Sales Representative", "address": {"line1": "3879 Gateway Avenue", "line2": "Bakersfield,", "line3": "California, USA"}, "phone": "******-808-4188", "website": "nancyontheweb.com", "email": "<EMAIL>"}, "objective": {"enable": true, "heading": "Professional Objective", "body": "To obtain a job within my chosen field that will challenge me and allow me to use my education, skills and past experiences in a way that is mutually beneficial to both myself and my employer and allow for future growth and advancement."}, "work": {"enable": true, "heading": "Work Experience", "items": [{"id": "a208ec03-76e3-4428-ac5b-e17c3de4ac18", "title": "On Point Electronics, NYC, NY", "role": "Customer Service Representative", "start": "Jan 2013", "end": "July 2018", "description": "- Organized customer information and account data for business planning and customer service purposes.\n- Created excel spreadsheets to track customer data and perform intense reconciliation process.\n- Received 97% positive customer survey results.\n- Speed on calls was 10% above team average.  \n**Key Achievement:** Designed and executed an automatized system for following up with customers, increasing customer retention by 22%.", "enabled": true, "enable": true}, {"id": "bd8649f2-42d1-4424-acaf-a02c08c3322c", "title": "Excelsior Communications, NYC, NY", "role": "Customer Service Representative", "start": "Oct 2009", "end": "Dec 2012", "description": "- Worked as a full time customer service rep in a high volume call center.\n- Received \"Associate of the Month\" award six times.\n- <PERSON><PERSON> as an example for other associates in trainings.  \n**Key Achievement:** Received Customer Appreciation bonus in three of four years.", "enabled": true, "enable": true}, {"id": "dde47711-a7a6-424f-9751-73483a0ef4ed", "title": "Pizza Hut, Newark, NJ", "role": "Waitress", "start": "Aug 2005", "end": "Sep 2009", "description": "- Worked passionately in customer service in a high volume restaurant.\n- Completed the FAST customer service training class.\n- Maintained a high tip average thanks to consistent customer satisfaction.", "enabled": true, "enable": true}]}, "education": {"enable": true, "heading": "Education", "items": [{"id": "624f32ab-2d78-4052-86ad-1354fd41d754", "name": "The City College of New York, NYC, NY", "major": "MS in Computer Science", "start": "Sep 2001", "end": "Aug 2002", "grade": "7.2 CGPA", "description": "", "enabled": true, "enable": true}, {"id": "71a9852f-ed14-4281-bff2-4db9a2275978", "name": "University of California, Berkeley, CA", "major": "BS in Computer Science", "start": "Sep 1997", "end": "Aug 2001", "grade": "8.4 CGPA", "description": "", "enabled": true, "enable": true}]}, "awards": {"enable": true, "heading": "Honors & Awards", "items": [{"id": "121f0976-18cb-4e46-921d-0e156b6bf7fb", "title": "Cast Member of a Musical - Oklahoma", "subtitle": "Winter, 2007", "description": "", "enable": true}, {"id": "e5f27346-72ad-4d4f-bab3-726a111e4932", "title": "Class Representative to ASB", "subtitle": "Fall, 2008", "description": "", "enable": true}, {"id": "f71ba9bc-8c14-46b5-99dd-e1333e9aceb9", "title": "Most Improved - Varsity Soccer", "subtitle": "Fall, 2007", "description": "", "enable": true}]}, "certifications": {"enable": true, "heading": "Certifications", "items": [{"id": "e5170d99-b21d-4131-a7dc-26a4670037f5", "title": "CCNP", "subtitle": "Cisco Systems", "description": "", "enable": true}, {"id": "788e4042-9ecb-40c5-849d-7688b4e23888", "title": "VCP6-DCV", "subtitle": "VMWare", "description": "", "enable": true}, {"id": "97a1a8d9-3c03-47fb-93ab-e84f864ffe17", "title": "DCUCI 642-999", "subtitle": "Cisco Systems", "description": "", "enable": true}]}, "skills": {"enable": true, "heading": "Skills", "items": [{"id": "2562d78a-3459-4370-8604-c81b00738db1", "skill": "Customer Service Expertise"}, {"id": "58c31587-9770-4522-a34c-f5ad92fe33e5", "skill": "High-Volume Call Center"}, {"id": "7aa9a4b1-a2bb-4bcd-8711-b66c0d246971", "skill": "Team Leader/Problem <PERSON>"}, {"id": "e7fd33e8-5d77-462d-8115-5be57f52832e", "skill": "Call Center Management"}, {"id": "7bad2af1-c24d-4e01-b68b-be01cfa784ce", "skill": "Teambuilding & Training"}, {"id": "64fe1710-c2d1-4f53-922e-a5d751eee967", "skill": "Continuous Improvement"}]}, "Memberships": {"enable": true, "heading": "Memberships", "items": [{"id": "dd2efad7-e900-4384-bdc0-b2ab5f62bb71", "hobby": "Poetry"}, {"id": "96023eb7-8c93-4b1d-b581-b8fc4107351a", "hobby": "Travelling"}, {"id": "7e5a6168-9cbe-4fe6-b9b9-43a47d8bb15a", "hobby": "Beatboxing"}, {"id": "dd7f4ffd-9c16-4dbf-8968-1165b9e30db8", "hobby": "Sketching"}]}, "languages": {"enable": true, "heading": "Languages", "items": [{"id": "9d34cfcb-c9f0-4d25-ab27-cf81652dd1d0", "key": "English (US)", "value": 5, "enable": true, "level": "", "rating": 5}, {"id": "3511a86b-7ea9-44ac-8144-6acc7f3bd54f", "key": "Spanish", "value": 4, "enable": true, "rating": 4}, {"id": "d1e17542-f7cc-473a-aa0e-978765907454", "key": "Japanese", "value": 4, "enable": true, "level": "N4", "rating": 2}, {"id": "b1e8442a-7059-4c6f-8a9c-415383133b0e", "key": "German", "value": 3, "enable": true, "level": "B1", "rating": 0}]}, "references": {"enable": true, "heading": "References", "items": [{"id": "ba3662e6-29cb-4a03-9766-b3618d1621f3", "name": "<PERSON>", "position": "Head of HR, Carson Logistics", "phone": "******-808-4188", "email": "<EMAIL>", "description": "", "enable": true}, {"id": "62fd3293-0e93-4242-882b-ae19b7865fef", "name": "<PERSON>", "position": "Assistant Manager, <PERSON><PERSON>", "phone": "******-808-4188", "email": "<EMAIL>", "description": "", "enable": true}, {"id": "eaab2e32-8591-497c-8676-d122cf3a4798", "name": "<PERSON>", "position": "CEO , DownToPlay", "phone": "******-808-4188", "email": "<EMAIL>", "description": "", "enable": true}]}, "extras": {"enable": true, "heading": "Additional Information", "items": [{"id": "3834a270-2c01-4105-b670-80863c955347", "key": "Skype", "value": "@NancyJack5436", "enable": true}, {"id": "b0c4fd85-cfda-421e-bd31-008b9aad1dfe", "key": "Hometown", "value": "New Jersey, NY", "enable": true}, {"id": "7f0a4971-9770-4ca7-b135-2b0ccd867879", "key": "Memberships", "value": "Playing Soccer & Guitar", "enable": true}, {"id": "e17552a2-e7e9-4605-8145-795e2b62c30e", "key": "Valid Work Visas", "value": "US, UK, EU", "enable": true}]}}, "theme": {"layout": "celebi2", "font": {"family": "Montserrat"}, "colors": {"background": "#ffffff", "primary": "#212121", "accent": "#f44336"}}}