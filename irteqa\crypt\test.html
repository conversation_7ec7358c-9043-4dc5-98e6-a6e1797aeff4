<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translate</title>
</head>
<style>
  #translated-text{
    color: rgb(42, 165, 58);
  }
  #source-text{
    font-size:x-large;
    height: 30px;
    width: 200px;
    text-align:center;
  }
  .translatebtn{
    height: 30px;
    width: 80px;
    font-size: large;
    margin: 5px;
  }
  #display-label{
    font-size: x-large;
  }
  #translated-text{
    font-size: x-large;
  }
  #target-language{
    font-size:larger;
    height: 30px;
    width: 180px;
  }
  table {
            width: 300px;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
            
        }
        #translatediv{
            text-align: center;
            width: 400px;
            display: inline-grid;
    justify-items: center;
        }
</style>
<body>
<div id="translatediv">
    <h1>ترجمة فورية</h1>
    <div><input type="submit" value="ترجمة" class="translatebtn" onclick="translateText()"><input id="source-text" rows="4" cols="50">
    </div>
    <select id="target-language">
        <option value="en">من العربية الى الإنجليزية</option>
        <option value="ar">من الإنجليزية الى العربية</option>
    </select><br>

    <table>
        <thead>
            <tr>
                <th> النص الأصلي</th>
                <th>الترجمة</th>
            </tr>
        </thead>
        <tbody>
            
            <tr>
                <td id="display-label"></td>
                <td id="translated-text"></td>
            </tr>
        </tbody>
    </table>
</div>

    <script>
        function translateText() {
            const sourceText = document.getElementById('source-text').value;
const targetLanguage = document.getElementById('target-language').value;
const userInput = document.getElementById('source-text').value;
                document.getElementById('display-label').innerText = userInput;
            const apiUrl = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=auto&tl=${targetLanguage}&dt=t&q=${encodeURIComponent(sourceText)}`;
            fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                const translatedText = data[0][0][0];
                document.getElementById('translated-text').innerText = translatedText;
            })
            .catch(error => console.error('Error:', error));
        }
        
    </script>

</body>
</html>