<?php

namespace PhpOffice\PhpSpreadsheet\Shared;

class Escher
{
    /**
     * Drawing Group Container.
     */
    private ?<PERSON><PERSON>\DggContainer $dggContainer = null;

    /**
     * Drawing Container.
     */
    private ?<PERSON><PERSON>\DgContainer $dgContainer = null;

    /**
     * Get Drawing Group Container.
     */
    public function getDggContainer(): ?Escher\DggContainer
    {
        return $this->dggContainer;
    }

    /**
     * Set Drawing Group Container.
     */
    public function setDggContainer(<PERSON><PERSON>\DggContainer $dggContainer): <PERSON><PERSON>\DggContainer
    {
        return $this->dggContainer = $dggContainer;
    }

    /**
     * Get Drawing Container.
     */
    public function getDgContainer(): ?Escher\DgContainer
    {
        return $this->dgContainer;
    }

    /**
     * Set Drawing Container.
     */
    public function setDgContainer(Escher\DgContainer $dgContainer): <PERSON><PERSON>\DgContainer
    {
        return $this->dgContainer = $dgContainer;
    }
}
