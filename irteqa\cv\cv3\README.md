# Web Resume

![Web Resume](https://raw.githubusercontent.com/SumatM/web_resume/main/public/Screenshot%20(747).png)

## Table of Contents

- [Introduction](#introduction)
- [Demo](#demo)
- [Features](#features)
- [Technologies Used](#technologies-used)
- [Installation](#installation)
- [Usage](#usage)
- [Deployment](#deployment)
- [Contributing](#contributing)
- [Contact](#contact)

## Introduction

Web Resume is a personal portfolio website showcasing my skills, experience, and projects. This website is designed to provide a clean and professional interface for potential employers or clients to learn more about me and my work.

## Demo

You can see a live demo of the project [here](https://web-resume-six.vercel.app/resume).

## Features

- A visually appealing and responsive design suitable for different devices.
- Displaying essential contact information, skills, and professional experience.
- Showcasing a list of technical skills and soft skills.
- Providing details about my educational background.
- A project section to showcase my latest work or personal projects.
- Ability to toggle between colorful themes for a better user experience.

## Technologies Used

- HTML5
- CSS3
- JavaScript (ES6+)
- React.js
- Next.js
- Redux 
- React Icons
- Tailwind CSS 

## Installation

1. Clone the repository: `git clone https://github.com/SumatM/web_resume.git`
2. Navigate to the project directory: `cd web_resume`
3. Install dependencies: `npm install`

## Usage

1. Start the development server: `npm run dev`
2. Open your browser and visit: `http://localhost:3000`

## Deployment

To deploy the project to a live server, follow these steps:

1. Build the production-ready code: `npm run build`
2. Start the production server: `npm run start`

You can now access your deployed project on the specified port.

## Like this project?

If you find this project helpful or interesting, consider giving it a ⭐️ on GitHub to show your appreciation and support.

## Fork this project

Want to build your own resume website or personalize this one? You can fork this repository to get started with your own version. Click the "Fork" button at the top right of this repository to create your own copy.

## Contributing

Contributions to this project are welcome. If you find any bugs or want to add new features, feel free to submit a pull request.

## Contact

If you have any questions or want to connect, feel free to reach out to me:

- Email: <EMAIL>
- LinkedIn: [Sumat Mallick](https://www.linkedin.com/in/sumat-mallick-65b966227/)
- GitHub: [SumatM](https://github.com/SumatM)
