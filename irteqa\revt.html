<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<style>

    body{
        font-family: 'Droid Arabic Naskh', 'Monda', sans-serif;
        direction: rtl;
    }
    .trev{
        margin: 10%;
        margin-top: 100px;
    float: right;
    }
    .reslt{
        border: dashed 2px;
    padding: 10px;
    margin: 20px;
    border-radius: 7px;
    }
    h3{
        margin: 0;
    }
   .sub{
        font-size: x-large;
    border: outset;
    }
</style>
<body>
    <div class="trev">
    <p>ADD TEXT.</p>
  <input class="" id="numb" style="width: 250px;
  height: 25px;">
  <button id="btnsubmit" type="button">Change</button>
  <div class="reslt">
  <h3 id="demo"></h3>
</div></div>
</body>
<script>
   function reverseText() {
    var forward = document.getElementById("numb").value;
    var backward = forward.split("").reverse().join("");
    document.getElementById("demo").innerHTML = backward;
}

document.getElementById("btnsubmit").addEventListener("click", reverseText);
</script>
</html>