<style>

.favret {
     
      height: 780px;
      border-radius: 10px;
      border: 1px solid #dbdbdb;
      margin: 5px;
      padding: 5px;
      background: #f4f4f4;
      position: absolute;
      left: 0;

    }

    .favret h1 {
      padding: 5px;
    }

    .favretlist::-webkit-scrollbar {
      width: 10px;
    }

    .favretlist::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    .favretlist::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 5px;
    }

    .favret::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    .favretlist li a {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      text-decoration: none;
    }

    .favretlist li {
      display: inline-flex;
      flex-wrap: wrap;
    }

    .favretlist {
      width: 435px;
      /* تغيير العرض كما تريد */
      height: 670px;
      /* تغيير الارتفاع كما تريد */
      overflow-y: auto;
      /* جعل الشريط العمودي يظهر عند الحاجة */
      padding: 5px;
      border-radius: 10px;
      direction: ltr;
      background: white;
    }

</style>

<section class="favret">
    <div>
        <h1> المفضلة</h1>
    </div>
    <div class="section-toggle">
  <button class="btnsctn" onclick="toggleSection(1)">الجديد</button>
  <button class="btnsctn" onclick="toggleSection(2)">التقييم</button>
  <button class="btnsctn" onclick="toggleSection(3)">المفضلة</button>
</div>
<div class="section active" id="section1">
<ul class="favretlist">
    <li><a href="#"><div></div></a></li>
    <li><a href="#"><div></div></a></li>
  </ul>
</div>
<div class="section" id="section2">
<ul class="favretlist">
    <li><a href="#"><div></div></a></li></ul>
</div>
<div class="section" id="section3">
<?php include_once "inport/cards.php";?>
</section>
<script>
  function toggleSection(sectionNumber) {
    // Hide all sections
    const sections = document.querySelectorAll('.section');
    sections.forEach(section => {
      section.classList.remove('active');
    });

    // Show the selected section
    const selectedSection = document.getElementById(`section${sectionNumber}`);
    selectedSection.classList.add('active');
  }
</script>