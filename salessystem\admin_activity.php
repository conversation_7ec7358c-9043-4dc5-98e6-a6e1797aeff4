<?php
/**
 * صفحة سجل العمليات والأنشطة
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// التحقق من الصلاحيات
if (!hasAdminPermission('view_all_data')) {
    $_SESSION['error'] = 'ليس لديك صلاحية للوصول لهذه الصفحة';
    header("Location: admin_dashboard.php");
    exit();
}

global $main_db;

// معاملات البحث والفلترة
$search = $_GET['search'] ?? '';
$user_type_filter = $_GET['user_type'] ?? '';
$action_filter = $_GET['action'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 50;
$offset = ($page - 1) * $per_page;

// بناء شروط البحث
$where_conditions = [];
$params = [];
$param_types = '';

if (!empty($search)) {
    $where_conditions[] = "(al.action LIKE ? OR al.description LIKE ? OR u.full_name LIKE ? OR a.full_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
    $param_types .= 'ssss';
}

if (!empty($user_type_filter)) {
    $where_conditions[] = "al.user_type = ?";
    $params[] = $user_type_filter;
    $param_types .= 's';
}

if (!empty($action_filter)) {
    $where_conditions[] = "al.action LIKE ?";
    $params[] = "%$action_filter%";
    $param_types .= 's';
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(al.created_at) >= ?";
    $params[] = $date_from;
    $param_types .= 's';
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(al.created_at) <= ?";
    $params[] = $date_to;
    $param_types .= 's';
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// استعلام العد الإجمالي
$count_query = "SELECT COUNT(*) as total FROM activity_log al 
                LEFT JOIN users u ON al.user_id = u.id AND al.user_type = 'user'
                LEFT JOIN admins a ON al.user_id = a.id AND al.user_type = 'admin'
                $where_clause";

if (!empty($params)) {
    $count_stmt = $main_db->prepare($count_query);
    $count_stmt->bind_param($param_types, ...$params);
    $count_stmt->execute();
    $total_records = $count_stmt->get_result()->fetch_assoc()['total'];
    $count_stmt->close();
} else {
    $total_records = $main_db->query($count_query)->fetch_assoc()['total'];
}

$total_pages = ceil($total_records / $per_page);

// استعلام البيانات الرئيسي
$query = "SELECT al.*, 
          CASE 
              WHEN al.user_type = 'admin' THEN a.full_name 
              ELSE u.full_name 
          END as user_name,
          CASE 
              WHEN al.user_type = 'admin' THEN a.username 
              ELSE u.username 
          END as username
          FROM activity_log al
          LEFT JOIN users u ON al.user_id = u.id AND al.user_type = 'user'
          LEFT JOIN admins a ON al.user_id = a.id AND al.user_type = 'admin'
          $where_clause
          ORDER BY al.created_at DESC 
          LIMIT $per_page OFFSET $offset";

if (!empty($params)) {
    $stmt = $main_db->prepare($query);
    $stmt->bind_param($param_types, ...$params);
    $stmt->execute();
    $activities_result = $stmt->get_result();
} else {
    $activities_result = $main_db->query($query);
}

// جلب إحصائيات سريعة
$stats = $main_db->query("SELECT 
    COUNT(*) as total_activities,
    COUNT(CASE WHEN user_type = 'user' THEN 1 END) as user_activities,
    COUNT(CASE WHEN user_type = 'admin' THEN 1 END) as admin_activities,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_activities
    FROM activity_log")->fetch_assoc();

require_once __DIR__ . '/includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_users.php">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active text-white" href="admin_activity.php">
                            <i class="fas fa-history me-2"></i>سجل العمليات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الشاملة
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">سجل العمليات والأنشطة</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportData('excel', 'admin_export.php?type=activity')">
                        <i class="fas fa-download me-1"></i>تصدير Excel
                    </button>
                </div>
            </div>

            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-primary shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي العمليات
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['total_activities']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-list fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-success shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        عمليات المستخدمين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['user_activities']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-warning shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        عمليات المديرين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['admin_activities']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card border-left-info shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        عمليات اليوم
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        <?php echo number_format($stats['today_activities']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="search" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="<?php echo htmlspecialchars($search); ?>" 
                                   placeholder="العملية، الوصف، أو اسم المستخدم">
                        </div>
                        <div class="col-md-2">
                            <label for="user_type" class="form-label">نوع المستخدم</label>
                            <select class="form-select" id="user_type" name="user_type">
                                <option value="">الكل</option>
                                <option value="user" <?php echo $user_type_filter === 'user' ? 'selected' : ''; ?>>مستخدم</option>
                                <option value="admin" <?php echo $user_type_filter === 'admin' ? 'selected' : ''; ?>>مدير</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="action" class="form-label">نوع العملية</label>
                            <input type="text" class="form-control" id="action" name="action" 
                                   value="<?php echo htmlspecialchars($action_filter); ?>" 
                                   placeholder="مثل: login, create">
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" 
                                   value="<?php echo htmlspecialchars($date_from); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" 
                                   value="<?php echo htmlspecialchars($date_to); ?>">
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- جدول العمليات -->
            <div class="card shadow">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">سجل العمليات</h6>
                    <span class="text-muted">إجمالي: <?php echo number_format($total_records); ?> عملية</span>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>المستخدم</th>
                                    <th>النوع</th>
                                    <th>العملية</th>
                                    <th>الجدول</th>
                                    <th>الوصف</th>
                                    <th>IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($activity = $activities_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo date('Y-m-d H:i:s', strtotime($activity['created_at'])); ?></td>
                                    <td>
                                        <?php echo htmlspecialchars($activity['user_name'] ?? 'غير محدد'); ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($activity['username'] ?? ''); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge <?php echo $activity['user_type'] === 'admin' ? 'bg-danger' : 'bg-primary'; ?>">
                                            <?php echo $activity['user_type'] === 'admin' ? 'مدير' : 'مستخدم'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <code><?php echo htmlspecialchars($activity['action']); ?></code>
                                    </td>
                                    <td>
                                        <?php if ($activity['table_name']): ?>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($activity['table_name']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($activity['description'] ?? ''); ?></td>
                                    <td><?php echo htmlspecialchars($activity['ip_address'] ?? ''); ?></td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- التنقل بين الصفحات -->
                    <?php if ($total_pages > 1): ?>
                    <nav aria-label="تنقل الصفحات">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page-1; ?>&<?php echo http_build_query($_GET); ?>">السابق</a>
                            </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page-2); $i <= min($total_pages, $page+2); $i++): ?>
                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query($_GET); ?>"><?php echo $i; ?></a>
                            </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                            <li class="page-item">
                                <a class="page-link" href="?page=<?php echo $page+1; ?>&<?php echo http_build_query($_GET); ?>">التالي</a>
                            </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>
</div>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
