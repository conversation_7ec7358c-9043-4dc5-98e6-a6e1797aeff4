/*!
 * Font Awesome Free 6.1.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2022 Fonticons, Inc.
 */
@import "_variables.less";

:root, :host {
  --@{fa-css-prefix}-font-regular: normal 400 1em/1 "@{fa-style-family}";
}

@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 400;
  font-display: @fa-font-display;
  src: url('@{fa-font-path}/fa-regular-400.woff2') format('woff2'),
    url('@{fa-font-path}/fa-regular-400.ttf') format('truetype');
}

.far,
.@{fa-css-prefix}-regular {
  font-family: 'Font Awesome 6 Free';
  font-weight: 400;
}
