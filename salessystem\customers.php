<?php
// يجب أن تكون هذه السطور الأولى في الملف بالضبط
require_once __DIR__ . '/config/init.php';  // هذا الملف يحتوي على تعريف isLoggedIn()
require_once __DIR__ . '/includes/functions.php';

// فحص تسجيل الدخول قبل إرسال أي محتوى
redirectIfNotLoggedIn();

require_once __DIR__ . '/includes/header.php';

$db = getCurrentUserDB();
if ($db === null || $db->connect_error) {
    $_SESSION['error'] = __('database_error') . ": " . ($db ? $db->connect_error : __('connection_error'));
    header("Location: index.php");
    exit();
}

// تنظيف أي نتائج متبقية
$db = resetDBConnection($db);

displayMessages(); // عرض أي رسائل خطأ أو نجاح
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><?php echo __('manage_customers'); ?></h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="add_customer.php" class="btn btn-success">
            <i class="fas fa-plus"></i> <?php echo __('add_customer'); ?>
        </a>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-striped table-hover">
        <thead>
            <tr>
                <th>#</th>
                <th><?php echo __('customer_name'); ?></th>
                <th><?php echo __('customer_phone'); ?></th>
                <th><?php echo __('tax_number'); ?></th>
                <th><?php echo __('customer_address'); ?></th>
                <th><?php echo __('actions'); ?></th>
            </tr>
        </thead>
        <tbody>
            <?php
            try {
                $query = "SELECT * FROM customers ORDER BY id DESC";
                $result = $db->query($query);

                if (!$result) {
                    throw new Exception($db->error);
                }

                if ($result->num_rows > 0) {
                    while ($row = $result->fetch_assoc()):
                    ?>
                    <tr>
                        <td><?php echo $row['id']; ?></td>
                        <td><?php echo htmlspecialchars($row['name']); ?></td>
                        <td><?php echo htmlspecialchars($row['phone']); ?></td>
                        <td><?php echo htmlspecialchars($row['tax_number']); ?></td>
                        <td><?php echo htmlspecialchars($row['address']); ?></td>
                        <td>
                            <a href="edit_customer.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary" title="<?php echo __('edit'); ?>">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="delete_customer.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('<?php echo __('confirm_delete_customer'); ?>');" title="<?php echo __('delete'); ?>">
                                <i class="fas fa-trash"></i>
                            </a>
                        </td>
                    </tr>
                    <?php
                    endwhile;
                } else {
                    echo '<tr><td colspan="6" class="text-center">' . __('no_data') . '</td></tr>';
                }
            } catch (Exception $e) {
                error_log("Error displaying customers: " . $e->getMessage());
                echo '<tr><td colspan="6" class="text-center">' . __('error') . '</td></tr>';
            }
            ?>
        </tbody>
    </table>
</div>

<?php require_once 'includes/footer.php'; ?>