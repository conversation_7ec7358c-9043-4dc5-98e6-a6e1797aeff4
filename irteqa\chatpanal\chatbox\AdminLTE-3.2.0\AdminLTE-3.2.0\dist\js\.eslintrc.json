{"root": true, "parserOptions": {"ecmaVersion": 5, "sourceType": "script"}, "env": {"jquery": true}, "extends": ["plugin:unicorn/recommended", "xo", "xo/browser"], "rules": {"capitalized-comments": "off", "comma-dangle": ["error", "never"], "indent": ["error", 2, {"MemberExpression": "off", "SwitchCase": 1}], "multiline-ternary": ["error", "always-multiline"], "no-var": "off", "object-curly-spacing": ["error", "always"], "object-shorthand": "off", "prefer-arrow-callback": "off", "semi": ["error", "never"], "strict": "error", "unicorn/no-array-for-each": "off", "unicorn/no-for-loop": "off", "unicorn/no-null": "off", "unicorn/numeric-separators-style": "off", "unicorn/prefer-dataset": "off", "unicorn/prefer-includes": "off", "unicorn/prefer-module": "off", "unicorn/prefer-node-append": "off", "unicorn/prefer-query-selector": "off", "unicorn/prefer-spread": "off", "unicorn/prevent-abbreviations": "off"}}