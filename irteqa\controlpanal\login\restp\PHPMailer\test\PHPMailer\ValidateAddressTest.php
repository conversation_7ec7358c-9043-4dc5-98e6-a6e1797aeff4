<?php

/**
 * PHPMailer - PHP email transport unit tests.
 * PHP version 5.5.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR>
 * @copyright 2012 - 2020 <PERSON>
 * @copyright 2004 - 2009 <PERSON>
 * @license   http://www.gnu.org/copyleft/lesser.html GNU Lesser General Public License
 */

namespace PHPMailer\Test\PHPMailer;

use PHPMailer\PHPMailer\PHPMailer;
use Yoast\PHPUnitPolyfills\TestCases\TestCase;

/**
 * Test email address validation.
 *
 * Test addresses obtained from {@link http://isemail.info}.
 *
 * @todo Recommendation JRF: Rework the tests to actually test all test cases
 *       against each type of build-in pattern.
 *       As things stand, only the PHP validation is tested (while it shouldn't be as that's
 *       the responsibility of PHP Core), while the PCRE and HTML5 regexes are untested, while
 *       those are maintained within this repo.
 *       There should also be a test to make sure that `auto` and `noregex` correctly
 *       fall through to the default validation.
 *
 * @covers \PHPMailer\PHPMailer\PHPMailer::validateAddress
 */
final class ValidateAddressTest extends TestCase
{
    /**
     * Run before this test class.
     */
    public static function set_up_before_class()
    {
        // Make sure that validator property starts off with its default value.
        PHPMailer::$validator = 'php';
    }

    /**
     * Run after this test class.
     */
    public static function tear_down_after_class()
    {
        self::set_up_before_class();
    }

    /**
     * Testing against the pre-defined patterns with a valid address (for coverage).
     *
     * @dataProvider dataPatterns
     *
     * @param string $pattern Validation pattern.
     */
    public function testPatternsValidAddress($pattern)
    {
        self::assertTrue(
            PHPMailer::validateAddress('<EMAIL>', $pattern),
            'Good address that failed validation against pattern ' . $pattern
        );
    }

    /**
     * Testing against the pre-defined patterns with an invalid address (for coverage).
     *
     * @dataProvider dataPatterns
     *
     * @param string $pattern Validation pattern.
     */
    public function testPatternsInvalidAddress($pattern)
    {
        self::assertFalse(
            PHPMailer::validateAddress('<EMAIL>.', $pattern),
            'Bad address that passed validation against pattern ' . $pattern
        );
    }

    /**
     * Data provider.
     *
     * @return array
     */
    public function dataPatterns()
    {
        $patterns = [
            'auto',
            'pcre',
            'pcre8',
            'html5',
            'php',
            'noregex',
        ];

        return $this->arrayToNamedDataProvider($patterns);
    }

    /**
     * Verify that valid addresses are recognized as such.
     *
     * @dataProvider dataValidAddresses
     * @dataProvider dataAsciiAddresses
     * @dataProvider dataValidIPv6
     *
     * @param string $emailAddress The address to test.
     */
    public function testValidAddresses($emailAddress)
    {
        self::assertTrue(PHPMailer::validateAddress($emailAddress), 'Good address that failed validation');
    }

    /**
     * Data provider for valid addresses.
     *
     * @return array
     */
    public function dataValidAddresses()
    {
        $validaddresses = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '"first\"last"@example.org',
            '"first@last"@example.org',
            '"first\last"@example.org',
            'first.last@[***********]',
            '<EMAIL>',
            '<EMAIL>',
            '"first\last"@example.org',
            '"Abc\@def"@example.org',
            '"Fred\ Bloggs"@example.org',
            '"Joe.\Blow"@example.org',
            '"Abc@def"@example.org',
            '<EMAIL>',
            'customer/department=<EMAIL>',
            '$<EMAIL>',
            '!def!<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            't*<EMAIL>',
            '+1~<EMAIL>',
            '{_test_}@example.org',
            '<EMAIL>',
            '"test.test"@example.org',
            'test."test"@example.org',
            '"test@test"@example.org',
            'test@123.123.123.x123',
            'test@[***************]',
            '<EMAIL>',
            '<EMAIL>',
            '"test\test"@example.org',
            '"test\blah"@example.org',
            '"test\blah"@example.org',
            '"test\"blah"@example.org',
            'customer/<EMAIL>',
            '<EMAIL>',
            '~@example.org',
            '"Austin@Powers"@example.org',
            '<EMAIL>',
            '"Ima.Fool"@example.org',
            '"first"."last"@example.org',
            '"first".middle."last"@example.org',
            '"first".<EMAIL>',
            'first."last"@example.org',
            '"first"."middle"."last"@example.org',
            '"first.middle"."last"@example.org',
            '"first.middle.last"@example.org',
            '"first..last"@example.org',
            '"first\"last"@example.org',
            'first."mid\dle"."last"@example.org',
            '<EMAIL>',
            '<EMAIL>',
            'aaa@[***************]',
            '<EMAIL>',
            '+@b.c',
            '+@b.com',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '"Joe\Blow"@example.org',
            'user%uucp!<EMAIL>',
            'cdburgess+!#$%&\'*-/=?+_{}|~<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
        ];

        return $this->arrayToNamedDataProvider($validaddresses, 'Valid: ');
    }

    /**
     * Data provider for IDNs in ASCII form.
     *
     * @return array
     */
    public function dataAsciiAddresses()
    {
        $asciiaddresses = [
            '<EMAIL>',
            '<EMAIL>--p1ai',
            '<EMAIL>',
        ];

        return $this->arrayToNamedDataProvider($asciiaddresses, 'Valid ascii: ');
    }

    /**
     * Data provider for valid explicit IPv6 numeric addresses.
     *
     * @todo Fix the failing (commented out) tests.
     *
     * @return array
     */
    public function dataValidIPv6()
    {
        $validipv6 = [
            //'first.last@[IPv6:::a2:a3:a4:b1:b2:b3:b4]',
            //'first.last@[IPv6:a1:a2:a3:a4:b1:b2:b3::]',
            'first.last@[IPv6:::]',
            'first.last@[IPv6:::b4]',
            'first.last@[IPv6:::b3:b4]',
            'first.last@[IPv6:a1::b4]',
            'first.last@[IPv6:a1::]',
            'first.last@[IPv6:a1:a2::]',
            'first.last@[IPv6:0123:4567:89ab:cdef::]',
            'first.last@[IPv6:0123:4567:89ab:CDEF::]',
            'first.last@[IPv6:::a3:a4:b1:ffff:***********]',
            //'first.last@[IPv6:::a2:a3:a4:b1:ffff:***********]',
            'first.last@[IPv6:a1:a2:a3:a4::***********]',
            //'first.last@[IPv6:a1:a2:a3:a4:b1::***********]',
            'first.last@[IPv6:a1::***********]',
            'first.last@[IPv6:a1:a2::***********]',
            'first.last@[IPv6:0123:4567:89ab:cdef::***********]',
            'first.last@[IPv6:0123:4567:89ab:CDEF::***********]',
            'first.last@[IPv6:a1::b2:***********]',
            'first.last@[IPv6:::***********]',
            'first.last@[IPv6:1111:2222:3333::4444:***********]',
            'first.last@[IPv6:1111:2222:3333:4444:5555:6666:***********]',
            'first.last@[IPv6:::1111:2222:3333:4444:5555:6666]',
            'first.last@[IPv6:1111:2222:3333::4444:5555:6666]',
            'first.last@[IPv6:1111:2222:3333:4444:5555:6666::]',
            'first.last@[IPv6:1111:2222:3333:4444:5555:6666:7777:8888]',
            //'first.last@[IPv6:1111:2222:3333::4444:5555:***********]',
            //'first.last@[IPv6:1111:2222:3333::4444:5555:6666:7777]',
        ];

        return $this->arrayToNamedDataProvider($validipv6, 'Valid IPv6: ');
    }

    /**
     * Verify that invalid addresses are recognized as such.
     *
     * @dataProvider dataInvalidAddresses
     * @dataProvider dataUnicodeAddresses
     * @dataProvider dataInvalidPHPPattern
     *
     * @param string $emailAddress The address to test.
     */
    public function testInvalidAddresses($emailAddress)
    {
        self::assertFalse(PHPMailer::validateAddress($emailAddress), 'Bad address that passed validation');
    }

    /**
     * Data provider for invalid addresses.
     *
     * Some failing cases commented out that are apparently up for debate!
     *
     * @return array
     */
    public function dataInvalidAddresses()
    {
        $invalidaddresses = [
            '<EMAIL>,com',
            'first\@<EMAIL>',
            '123456789012345678901234567890123456789012345678901234567890' .
            '@12345678901234567890123456789012345678901234 [...]',
            'first.last',
            '<EMAIL>',
            '.<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '"first"last"@iana.org',
            '"""@iana.org',
            '"\"@iana.org',
            //'""@iana.org',
            'first\@<EMAIL>',
            'first.last@',
            'x@x23456789.x23456789.x23456789.x23456789.x23456789.x23456789.x23456789.' .
            'x23456789.x23456789.x23456789.x23 [...]',
            'first.last@[.***********]',
            'first.last@[***********9]',
            'first.last@[::***********]',
            'first.last@[IPv5:::***********]',
            'first.last@[IPv6:1111:2222:3333:4444:5555:***********]',
            'first.last@[IPv6:1111:2222:3333:4444:5555:6666:7777:***********]',
            'first.last@[IPv6:1111:2222:3333:4444:5555:6666:7777]',
            'first.last@[IPv6:1111:2222:3333:4444:5555:6666:7777:8888:9999]',
            'first.last@[IPv6:1111:2222::3333::4444:5555:6666]',
            'first.last@[IPv6:1111:2222:333x::4444:5555]',
            'first.last@[IPv6:1111:2222:33333::4444:5555]',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            'abc\@<EMAIL>',
            'abc\@iana.org',
            'Doug\ \"Ace\"\ <EMAIL>',
            'abc@<EMAIL>',
            'abc\@<EMAIL>',
            'abc\@iana.org',
            '@iana.org',
            'doug@',
            '"<EMAIL>',
            'ote"@iana.org',
            '.<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '"Doug "Ace" L."@iana.org',
            'Doug\ \"Ace\"\ L\.@iana.org',
            'hello <EMAIL>',
            //'helloworld@iana .org',
            '<EMAIL>.l.d.',
            'test.iana.org',
            '<EMAIL>',
            '<EMAIL>',
            '.<EMAIL>',
            'test@<EMAIL>',
            'test@@iana.org',
            '-- test --@iana.org',
            '[test]@iana.org',
            '"test"test"@iana.org',
            '()[]\;:,><@iana.org',
            'test@.',
            'test@example.',
            'test@.org',
            'test@12345678901234567890123456789012345678901234567890123456789012345678901234567890' .
            '12345678901234567890 [...]',
            'test@[***************',
            'test@***************]',
            'NotAnEmail',
            '@NotAnEmail',
            '"test"blah"@iana.org',
            '.<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '.@iana.org',
            'Ima <EMAIL>',
            'phil.h\@\@<EMAIL>',
            'foo@[\*******]',
            //'first."".<EMAIL>',
            'first\<EMAIL>',
            'Abc\@<EMAIL>',
            'Fred\ <EMAIL>',
            'Joe.\<EMAIL>',
            'first.last@[IPv6:1111:2222:3333:4444:5555:6666:12.34.567.89]',
            '{^c\@**Dog^}@cartoon.com',
            //'"foo"(yay)@(hoopla)[*******]',
            'cal(foo(bar)@iamcal.com',
            'cal(foo)bar)@iamcal.com',
            'cal(foo\)@iamcal.com',
            'first(12345678901234567890123456789012345678901234567890)last@(1234567890123456789' .
            '01234567890123456789012 [...]',
            'first(middle)<EMAIL>',
            'first(abc("def".ghi).mno)middle(abc("def".ghi).mno).last@(abc("def".ghi).mno)example' .
            '(abc("def".ghi).mno). [...]',
            'a(a(b(c)d(e(f))g)(h(i)j)@iana.org',
            '.@',
            '@bar.com',
            '@@bar.com',
            'aaa.com',
            'aaa@.com',
            'aaa@.123',
            'aaa@[***************]a',
            'aaa@[123.123.123.333]',
            '<EMAIL>.',
            '<EMAIL>',
            '<EMAIL>',
            '-@..com',
            '-@a..com',
            '<EMAIL>-',
            '<EMAIL>',
            '"Unicode NULL' . chr(0) . '"@char.com',
            'Unicode NULL' . chr(0) . '@char.com',
            'first.last@[IPv6::]',
            'first.last@[IPv6::::]',
            'first.last@[IPv6::b4]',
            'first.last@[IPv6::::b4]',
            'first.last@[IPv6::b3:b4]',
            'first.last@[IPv6::::b3:b4]',
            'first.last@[IPv6:a1:::b4]',
            'first.last@[IPv6:a1:]',
            'first.last@[IPv6:a1:::]',
            'first.last@[IPv6:a1:a2:]',
            'first.last@[IPv6:a1:a2:::]',
            'first.last@[IPv6::***********]',
            'first.last@[IPv6::::***********]',
            'first.last@[IPv6:a1:***********]',
            'first.last@[IPv6:a1:::***********]',
            'first.last@[IPv6:a1:a2:::***********]',
            'first.last@[IPv6:0123:4567:89ab:cdef::11.22.33.xx]',
            'first.last@[IPv6:0123:4567:89ab:CDEFF::***********]',
            'first.last@[IPv6:a1::a4:b1::b4:***********]',
            'first.last@[IPv6:a1::11.22.33]',
            'first.last@[IPv6:a1::***********.55]',
            'first.last@[IPv6:a1::b2***********]',
            'first.last@[IPv6:a1::b2::***********]',
            'first.last@[IPv6:a1::b3:]',
            'first.last@[IPv6::a2::b4]',
            'first.last@[IPv6:a1:a2:a3:a4:b1:b2:b3:]',
            'first.last@[IPv6::a2:a3:a4:b1:b2:b3:b4]',
            'first.last@[IPv6:a1:a2:a3:a4::b1:b2:b3:b4]',
            //This is a valid RFC5322 address, but we don't want to allow it for obvious reasons!
            "(\r\n RCPT TO:<EMAIL>\r\n DATA \\\nSubject: spam10\\\n\r\n Hello," .
            "\r\n this is a spam mail.\\\n.\r\n QUIT\r\n ) <EMAIL>",
        ];

        return $this->arrayToNamedDataProvider($invalidaddresses, 'Invalid: ');
    }

    /**
     * Data provider for IDNs in Unicode form.
     *
     * @return array
     */
    public function dataUnicodeAddresses()
    {
        $unicodeaddresses = [
            'first.last@bücher.ch',
            'first.last@кто.рф',
            'first.last@phplíst.com',
        ];

        return $this->arrayToNamedDataProvider($unicodeaddresses, 'Invalid unicode: ');
    }

    /**
     * Data provider.
     *
     * These are invalid according to PHP's filter_var() email filter,
     * which doesn't allow dotless domains, numeric TLDs or unbracketed IPv4 literals.
     *
     * @return array
     */
    public function dataInvalidPHPPattern()
    {
        $invalidphp = [
            'a@b',
            'a@bar',
            'first.last@com',
            'test@***************',
            'foobar@***********',
            'first.last@example.123',
        ];

        return $this->arrayToNamedDataProvider($invalidphp, 'Invalid PHP: ');
    }

    /**
     * Create a dataprovider array from a single-dimensional array.
     *
     * Each item will have it's value as the test case name for easier debugging.
     *
     * @param array  $items  Single dimensional array.
     * @param string $prefix Optional. Prefix to add to the data set name.
     *
     * @return array
     */
    protected function arrayToNamedDataProvider($items, $prefix = '')
    {
        $provider = [];
        foreach ($items as $item) {
            $provider[$prefix . $item] = [$item];
        }

        return $provider;
    }
}
