<?php

/**
 * P<PERSON>Mailer - PHP email transport unit tests.
 * PHP version 5.5.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR>
 * @copyright 2012 - 2020 <PERSON>
 * @copyright 2004 - 2009 <PERSON>
 * @license   http://www.gnu.org/copyleft/lesser.html GNU Lesser General Public License
 */

namespace PHPMailer\Test\PHPMailer;

use PHPMailer\PHPMailer\PHPMailer;
use Yoast\PHPUnitPolyfills\TestCases\TestCase;

/**
 * Test mime type mapping functionality.
 *
 * @covers \PHPMailer\PHPMailer\PHPMailer::_mime_types
 */
final class MimeTypesTest extends TestCase
{
    /**
     * Test mime type mapping.
     *
     * @dataProvider dataMime_Types
     *
     * @param string $input     Input text string.
     * @param string $expected  Expected function output.
     */
    public function testMime_Types($input, $expected)
    {
        $result = PHPMailer::_mime_types($input);
        self::assertSame($expected, $result, 'MIME TYPE lookup failed');
    }

    /**
     * Data provider.
     *
     * @return array
     */
    public function dataMime_Types()
    {
        return [
            'Extension: pdf (lowercase)' => [
                'input'    => 'pdf',
                'expected' => 'application/pdf',
            ],
            'Extension: PHP (uppercase)' => [
                'input'    => 'PHP',
                'expected' => 'application/x-httpd-php',
            ],
            'Extension: Doc (mixed case)' => [
                'input'    => 'Doc',
                'expected' => 'application/msword',
            ],
            'Extension which is not in the list' => [
                'input'    => 'md',
                'expected' => 'application/octet-stream',
            ],
            'Empty string' => [
                'input'    => '',
                'expected' => 'application/octet-stream',
            ],
        ];
    }
}
