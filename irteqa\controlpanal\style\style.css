
*{font-family: sans-serif;}
  body {
  display:block;
  width:100%;
    text-align: center;
    font-weight: bold;
}
  div.aa{
    padding:5px ;
  box-shadow: 3px 5px 3px 0 rgba(85, 85, 85, 0.2), 0 6px 20px 0 rgba(125, 125, 125, 0.19);
  background-color: rgb(244, 244, 244);
  border: 1px solid #ccc;
  border-radius: 10px 0px 10px 10px;
  width: 97%;
  height: 150px;
  align-items: center;
  float:right;
  margin-right: 20px;
  }
  li a.activ {
    color: white;
    background-color: #04aa30;}
  input[type=text] {
  text-align: right;
  float:right;
  width: 20%;
  padding: 12px 20px;
  margin: 5px;
  box-sizing: border-box;
  border-radius: 10px;
  display:block;
  box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%);
}
input[type=submit]{
  color:white;
  float:right;
width: 10%;
margin:8px;
height: 50px;
border:none;
border-radius: 20px;
background-color:#04aa30;
box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%); 
}
input[type=submit]:hover {
background-color: #62a874;
}
 h1{
 font-size:  2.5em;
margin: 0px 0px 0px 30px;
 }
 .box{
  display: flex;
    height: 99px;
    height: 100px;
    direction: rtl;
 }

.form-select{
  display:block;
border: 0px;
text-align:center;
font-size: 20px;
width: 90px;
padding: 5px 20px;
margin: 10px;
border-radius: 10px;
float: right;
box-shadow:0 12px 20px -10px rgb(76 175 80 / 28%), 0 4px 20px 0 rgb(0 0 0 / 12%), 0 7px 8px -5px rgb(76 175 80 / 20%);
}
.box select{
border:none;
margin:5px;
}

body {
  display:block;
  width:100%;
    text-align: center;
    font-weight: bold;
}

#fantasyTable_filter{
  border-radius:10px;
  direction: rtl;
}
#myform{
  float:right;
  display: inline-flex;
}
table {
  border-radius:6px;
    width: 100%;
	border-collapse: collapse;
  counter-reset: row-num-1;
  zoom:1.5;
}
#fantasyTable tr .header, #fantasyTable tr:hover {
  background-color: #f1f1f1;
}
/*table tr {
  counter-increment: row-num;
}
table tr td:last-child::after {
    content: counter(row-num) "";
}*/
#fantasyTable {
	width: 100%;
	border-collapse: collapse;
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
  float:right;
  font: icon;
}
#tabh{
  margin:10px;
}
#fantasyTable th {
	background: #D6D6D6;
	font-size: 1rem;
	font-weight: 700;
	color: #474443;
	border-bottom: solid thin #C1C1C1;
	border-right: solid thin #C1C1C1;
	border-top: solid thin #D6D6D6;
	border-left: solid thin #D6D6D6;
}
#fantasyTable td {
  text-align-last: center;
	padding: 2px;
	font-size: .75rem;
	font-weight: 400;
	color: #474443;
	border-bottom: solid thin #C1C1C1;
	border-right: solid thin #C1C1C1;
	border-top: solid thin #D6D6D6;
	border-left: solid thin #D6D6D6;
}

/*controls odd rows*/
#fantasyTable tr:nth-child(odd) {
	background: #FFFFFF;
	border-bottom: solid thin #C1C1C1;
	border-right: solid thin #C1C1C1;
	border-top: solid thin #D6D6D6;
	border-left: solid thin #D6D6D6;
}
/*controls even rows*/
#fantasyTable tr:nth-child(even) {
	background: #FFFFFF;
	border-bottom: solid thin #C1C1C1;
	border-right: solid thin #C1C1C1;
	border-top: solid thin #D6D6D6;
	border-left: solid thin #D6D6D6;
}
#fantasyTable td:nth-child(2) {
	color: #04aa30;
	text-align:center;
	font-weight: 700;
  font-size: 1.2rem;
  width: 40px;
}
/*controls column 1*/
#fantasyTable td:nth-child(1) {
	background: #FFFFFF;
	color: #0082D6;
	font-size: 1rem;
	font-weight: 700;
	text-align: center;
}
/*controls column 1 header*/
#fantasyTable th:nth-child(1) {
	text-align: center;
	border-left: solid 1px #FFFFFF;
}
/*controls column 2*/
#fantasyTable td:nth-child(3) {
	text-transform: uppercase;
  font-weight: 700;
  font-size: 1.5rem;
  color: #c82b2b;
  text-align: center;
}
#fantasyTable td:nth-child(4) {
	text-transform: uppercase;
  font-weight: 700;
  font-size: 1rem;
  text-align:center;
}
#fantasyTable td:nth-child(5) {
	text-transform: uppercase;
  font-weight: 700;
  font-size: 1rem;
}
/*controls column 5*/
#fantasyTable td:nth-child(6) {
	background: #ffffff;
	color: #000000;
	font-weight: 700;
  font-size: 1rem;
}
#fantasyTable td:nth-child(7) {
	background: #ffffff;
	color: #000000;
	font-weight: 700;
  font-size: 1rem;
}
/*controls column head 5*/
#fantasyTable th:nth-child(6) {
	background: #D6D6D6;
}
/*controls column 6*/
#fantasyTable td:nth-child(8) {
	background: #EDEDED;
	color: #0082D6;
	text-align:center;
	font-weight: 700;
  font-size: 1.5rem;
}
/*controls column head 6*/
#fantasyTable th:nth-child(8) {
	background: #D6D6D6;
	text-align: right;
}
 
#fantasyTable th:nth-child(9) {
	background: #D6D6D6;
}
#fantasyTable td:nth-child(9) {
	align-items:center;
}
.pointLeader img {
	float: left;
	padding-right: 20px;
	font-weight: 900;
}
.pointLeader p {
	font-size: .75rem;
	text-align: left
}
.pointLeader h4 {
	font-weight: 900;
	text-align: left
}
.align-center {
	text-align: center
}
.learnMore {
	padding: 0 0 0 10px
}
.md-label {
	background: #BABABA
}
.md-label-orange {
	background: #f78d2c;
	padding: 0.3rem 0.5rem;
	border-radius: 0;
	color: white
}
.md-caps {
	text-transform: uppercase
}
.card {
	border: solid thin #F5F5F5;
	padding: 2rem 1rem;
}
.callout {
	border: solid thin #F5F5F5;
	background-color: #D6D6D6;
	border-radius: 5px;
	padding: .5rem;
	width: auto;
}
.md-black {
	font-weight: 900;
}
.linethrough {
	text-align: center;
	position: relative;
	z-index: 1;
}
.linethrough:before {
	border-top: 2px solid #dfdfdf;
	content: "";
	margin: 0 auto; /* this centers the line to the full width specified */
	position: absolute; /* positioning must be absolute here, and relative positioning must be applied to the parent */
	top: 15px;
	left: 0;
	right: 0;
	bottom: 0;
	width: 95%;
	z-index: -1;
}
.linethrough span {
	/* to hide the lines from behind the text, you have to set the background color the same as the container */ 
	background: #FFFFFF;
	padding: 0 15px;
}
.max-width-50 {
	max-width: 50%
}
/*controls responsive button for opening hidden columns*/
table.dataTable.dtr-inline.collapsed>tbody>tr>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr>th:first-child:before {
	background-color: #0d82fb;
	border: none;
  font-size:12px
}
/* give space between search bar and table */
.dataTables_filter {
	margin-bottom: 20px
}
.dataTables_filter input {
    text-align: center;
    font-size: 1rem;
}
label {
    font-size: 1rem;
}
@media screen and (max-width: 39.9375em) {
/* give space between search bar and table */
.dataTables_wrapper .dataTables_length {
    display: none;
	
}
body{
	display: inline-table;
	zoom:0.58;
}
} 

@media screen and (max-width:1200px) {
    body{
        zoom:.8;
        display: inline-table;
    }
	.dataTables_wrapper {
		position: relative;
		zoom: 1.1;
	}
}

@media screen and (max-width:800px) {
    body{
        zoom:.6;
        display: inline-table;
    }
	.dataTables_wrapper {
		position: relative;
		zoom: 1.5;
	}
}
@media screen and (max-width:420px) {
    body{
        zoom:.6;
        display: inline-table;
    }
h1 {
    margin:10px;
    font-size: 20px;
    font-weight: bold;
}
label{
    font-size: 16px;
       text-align: right;
}
.dataTables_wrapper {
    position: relative;
    zoom: .8;
}
.dataTables_filter input {
    width:50%;
    margin-left: 13.5em;
    zoom:1;
    font-size: 16px;

    padding:5px;
}
}
