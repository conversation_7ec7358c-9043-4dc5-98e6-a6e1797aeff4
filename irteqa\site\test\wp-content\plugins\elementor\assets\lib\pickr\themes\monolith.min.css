/*! Pickr 1.5.0 MIT | https://github.com/Simonwep/pickr */.pickr{position:relative;height:27px;width:100%;padding:3px;border-radius:3px;border:1px solid #d5dadf;-webkit-transform:translateY(0);transform:translateY(0)}.pickr *{outline:none;border:none;-webkit-appearance:none}.pcr-button{background-image:linear-gradient(45deg,#ddd 25%,transparent 0,transparent 75%,#ddd 0,#ddd),linear-gradient(45deg,#ddd 25%,transparent 0,transparent 75%,#ddd 0,#ddd);background-size:13px 13px;background-position:0 0,6.5px 6.5px;display:block;position:relative;height:100%;width:100%;cursor:pointer}.pcr-button:after{position:absolute;content:"";top:0;left:0;height:100%;width:100%;-webkit-transition:background .3s;transition:background .3s;background:currentColor}.pcr-button.disabled{cursor:not-allowed}.pcr-app *,.pickr *{box-sizing:border-box;outline:none;border:none;-webkit-appearance:none}.pcr-palette,.pcr-slider{-webkit-transition:box-shadow .3s;transition:box-shadow .3s}.pcr-app{position:fixed;display:-webkit-box;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;flex-direction:column;z-index:10000;border-radius:3px;background:#fff;opacity:0;visibility:hidden;-webkit-transition:opacity .3s,visibility 0s .3s;transition:opacity .3s,visibility 0s .3s;font-family:Roboto;box-shadow:0 .15em 1.5em 0 rgba(0,0,0,.1),0 0 1em 0 rgba(0,0,0,.03);left:0;top:0;-webkit-transform:translateX(10px);transform:translateX(10px)}.pcr-app.visible{-webkit-transition:opacity .3s;transition:opacity .3s;visibility:visible;opacity:1}.pcr-swatches{margin:0 -3.5px}.pcr-swatches>*{width:20px;display:-webkit-inline-box;display:inline-flex;border-radius:3px;cursor:pointer;margin:0 3.1px 5px}.pcr-swatches>:after{content:"";width:100%;padding-bottom:100%}.pcr-swatch:hover{-webkit-filter:brightness(1.05);filter:brightness(1.05)}.pcr-swatch:after{background:currentColor;border-radius:3px;box-shadow:inset 0 0 0 1px rgba(0,0,0,.1)}.pcr-swatch.pcr-active:after{border-radius:2px;box-shadow:0 0 0 1px #71d7f7,inset 0 0 0 1px #fff}.pcr-interaction{display:-webkit-box;display:flex;margin-top:10px;-webkit-box-ordinal-group:3;order:2;direction:ltr}.pcr-interaction input{-webkit-transition:all .15s;transition:all .15s}.pcr-interaction input:hover{-webkit-filter:brightness(.975);filter:brightness(.975)}.pcr-result{font-size:10px;padding:6.5px 8px;color:#495157;border-radius:3px 0 0 3px;border:1px solid #d5dadf;border-right-width:0}.pcr-type.active{color:#fff}.pcr-save,.pcr-type.active{background:#4285f4}.pcr-clear{width:auto;font-size:9px;padding:0 12.5px;text-transform:uppercase;border-radius:0 3px 3px 0;background-color:#d5dadf;color:#6d7882;font-family:Roboto;cursor:pointer}.pcr-picker{position:absolute;height:15px;width:15px;border-radius:50%;box-shadow:inset 0 0 0 2px #fff,1px 1px 6px rgba(0,0,0,.4),inset 0 0 3px 1px rgba(0,0,0,.3)}.pcr-color-chooser,.pcr-color-opacity,.pcr-color-palette,.pcr-picker{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.pcr-color-chooser,.pcr-color-opacity,.pcr-color-palette{position:relative;display:-webkit-box;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;flex-direction:column;cursor:-webkit-grab;cursor:grab}.pcr-color-chooser:active,.pcr-color-opacity:active,.pcr-color-palette:active{cursor:-webkit-grabbing;cursor:grabbing}.pcr-app[data-theme=monolith]{width:270px;padding:20px}.pcr-app[data-theme=monolith] .pcr-selection{display:-webkit-box;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;flex-direction:column;-webkit-box-pack:justify;justify-content:space-between;-webkit-box-flex:1;flex-grow:1;-webkit-box-ordinal-group:2;order:1}.pcr-app[data-theme=monolith] .pcr-color-palette{background-image:linear-gradient(45deg,#ddd 25%,transparent 0,transparent 75%,#ddd 0,#ddd),linear-gradient(45deg,#ddd 25%,transparent 0,transparent 75%,#ddd 0,#ddd);background-size:16px 16px;background-position:0 0,8px 8px;height:170px;margin-top:15px;overflow:hidden}.pcr-app[data-theme=monolith] .pcr-color-palette .pcr-picker{height:13px;width:13px;box-shadow:inset 0 0 0 2px #fff,1px 1px 6px rgba(0,0,0,.4)}.pcr-app[data-theme=monolith] .pcr-color-palette .pcr-palette{height:100%}.pcr-app[data-theme=monolith] .pcr-color-chooser,.pcr-app[data-theme=monolith] .pcr-color-opacity{height:10px;margin-top:.75em;border-radius:4px}.pcr-app[data-theme=monolith] .pcr-color-chooser .pcr-picker,.pcr-app[data-theme=monolith] .pcr-color-opacity .pcr-picker{top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%)}.pcr-app[data-theme=monolith] .pcr-color-chooser .pcr-slider,.pcr-app[data-theme=monolith] .pcr-color-opacity .pcr-slider{-webkit-box-flex:1;flex-grow:1;border-radius:5px;box-shadow:inset 0 0 0 1px rgba(0,0,0,.2)}.pcr-app[data-theme=monolith] .pcr-color-chooser .pcr-slider{background:-webkit-gradient(linear,left top,right top,from(red),color-stop(#ff0),color-stop(#0f0),color-stop(#0ff),color-stop(#00f),color-stop(#f0f),to(red));background:linear-gradient(90deg,red,#ff0,#0f0,#0ff,#00f,#f0f,red)}.pcr-app[data-theme=monolith] .pcr-color-opacity{background-image:linear-gradient(45deg,#ddd 25%,transparent 0,transparent 75%,#ddd 0,#ddd),linear-gradient(45deg,#ddd 25%,transparent 0,transparent 75%,#ddd 0,#ddd);background-size:8px 8px;background-position:0 0,4px 4px}.pcr-app[data-theme=monolith] .pcr-color-opacity .pcr-slider{background-image:-webkit-gradient(linear,right top,left top,from(#000),to(transparent));background-image:linear-gradient(270deg,#000,transparent)}