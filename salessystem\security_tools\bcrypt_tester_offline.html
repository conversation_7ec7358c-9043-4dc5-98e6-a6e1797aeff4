<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة اختبار bcrypt - نسخة مباشرة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bcryptjs/2.4.3/bcrypt.min.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
            margin-bottom: 20px;
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(45deg, #667eea, #764ba2);
            padding: 20px;
            color: white;
            text-align: center;
        }
        .progress {
            height: 25px;
            border-radius: 15px;
        }
        .progress-bar {
            border-radius: 15px;
            transition: width 0.3s ease;
        }
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
            padding: 12px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .badge {
            font-size: 0.8em;
            margin: 2px;
        }
        .example-password {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 5px 10px;
            font-family: monospace;
            margin: 2px;
            display: inline-block;
            cursor: pointer;
        }
        .example-password:hover {
            background: #e9ecef;
        }
        .stats-card {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin: 5px 0;
        }
        .method-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #667eea;
        }
        .offline-badge {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- شارة العمل المباشر -->
    <div class="offline-badge">
        <span class="badge bg-success fs-6">
            <i class="fas fa-wifi"></i> يعمل بدون إنترنت
        </span>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">

                <!-- العنوان الرئيسي -->
                <div class="card">
                    <div class="card-header">
                        <h1><i class="fas fa-shield-alt"></i> أداة اختبار أمان bcrypt</h1>
                        <p class="mb-0">نسخة مباشرة - تعمل في المتصفح بدون سيرفر</p>
                    </div>
                </div>

                <!-- نموذج الاختبار -->
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(45deg, #28a745, #20c997);">
                        <h3><i class="fas fa-play-circle"></i> بدء الاختبار</h3>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-key"></i> bcrypt Hash:</label>
                                        <textarea class="form-control" id="bcrypt_hash" rows="3"
                                                placeholder="$2y$10$example..." required></textarea>
                                        <small class="text-muted">أدخل bcrypt hash المراد اختبار قوته</small>

                                        <!-- أمثلة سريعة -->
                                        <div class="mt-2">
                                            <small class="text-muted">أمثلة سريعة:</small>
                                            <div class="mt-1">
                                                <span class="example-password" onclick="generateTestHash('123456')">123456</span>
                                                <span class="example-password" onclick="generateTestHash('password')">password</span>
                                                <span class="example-password" onclick="generateTestHash('admin123')">admin123</span>
                                                <span class="example-password" onclick="generateTestHash('test2023')">test2023</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-cogs"></i> طريقة الاختبار:</label>
                                        <select class="form-select" id="test_method" onchange="updateMethodInfo()">
                                            <option value="common">كلمات المرور الشائعة</option>
                                            <option value="pattern">الأنماط الشائعة</option>
                                            <option value="dictionary">قاموس كلمات المرور</option>
                                            <option value="brute_simple">القوة الغاشمة البسيطة</option>
                                            <option value="wordlist_extended">قاموس موسع</option>
                                            <option value="rule_based">قائم على القواعد</option>
                                            <option value="hybrid">هجين (مختلط)</option>
                                            <option value="comprehensive">شامل (جميع الطرق)</option>
                                        </select>
                                        <div id="methodInfo" class="method-info mt-2"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-hashtag"></i> عدد المحاولات الأقصى:</label>
                                        <select class="form-select" id="max_attempts">
                                            <option value="1000">1,000 (سريع)</option>
                                            <option value="10000">10,000 (عادي)</option>
                                            <option value="100000" selected>100,000 (متوسط)</option>
                                            <option value="1000000">1,000,000 (طويل)</option>
                                            <option value="10000000">10,000,000 (طويل جداً)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- خيارات متقدمة -->
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse"
                                            data-bs-target="#advancedOptions">
                                        <i class="fas fa-cog"></i> خيارات متقدمة
                                    </button>
                                </div>
                            </div>

                            <div class="collapse mt-3" id="advancedOptions">
                                <div class="card" style="background: #f8f9fa;">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">نوع الأحرف:</label>
                                                <select class="form-select form-select-sm" id="charset_type">
                                                    <option value="basic">أساسي (a-z, 0-9)</option>
                                                    <option value="extended">موسع (+A-Z)</option>
                                                    <option value="special">خاص (+رموز)</option>
                                                    <option value="unicode">يونيكود (+عربي)</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">الطول الأدنى:</label>
                                                <input type="number" class="form-control form-control-sm" id="min_length"
                                                       value="1" min="1" max="20">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">الطول الأقصى:</label>
                                                <input type="number" class="form-control form-control-sm" id="max_length"
                                                       value="8" min="1" max="20">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-play"></i> بدء الاختبار
                                </button>
                                <button type="button" class="btn btn-danger btn-lg" id="stopTest"
                                        style="display: none;" onclick="stopTest()">
                                    <i class="fas fa-stop"></i> إيقاف الاختبار
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- شريط التقدم -->
                <div class="card" id="progressContainer" style="display: none;">
                    <div class="card-header" style="background: linear-gradient(45deg, #ffc107, #ff8f00);">
                        <h4><i class="fas fa-hourglass-half"></i> جاري الاختبار...</h4>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 id="progressBar" style="width: 0%"></div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="text-center">
                                        <h5 id="progressText">0%</h5>
                                        <small>التقدم</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="text-center">
                                        <h5 id="totalAttempts">0</h5>
                                        <small>المحاولات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="text-center">
                                        <h5 id="attemptsPerSecond">0</h5>
                                        <small>محاولة/ثانية</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="text-center">
                                        <h5 id="elapsedTime">00:00</h5>
                                        <small>الوقت المنقضي</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <p id="currentAttempt" class="mb-0">المحاولة 0 من 0</p>
                            <small id="currentPassword" class="text-muted">كلمة المرور الحالية: -</small>
                        </div>
                    </div>
                </div>

                <!-- النتائج -->
                <div class="card" id="resultsContainer" style="display: none;">
                    <div class="card-header" id="resultsHeader">
                        <h4><i class="fas fa-chart-line"></i> نتائج الاختبار</h4>
                    </div>
                    <div class="card-body" id="resultsBody">
                        <!-- سيتم ملء النتائج هنا -->
                    </div>
                </div>

                <!-- محلل كلمة المرور -->
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(45deg, #1976d2, #1565c0);">
                        <h4><i class="fas fa-microscope"></i> محلل كلمات المرور الذكي</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <input type="text" class="form-control" id="test_password"
                                       placeholder="أدخل كلمة مرور للتحليل...">
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-primary w-100" onclick="analyzePassword()">
                                    <i class="fas fa-search"></i> تحليل
                                </button>
                            </div>
                        </div>
                        <div id="analysisResults" class="mt-3" style="display: none;"></div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // متغيرات عامة
        let testRunning = false;
        let startTime = 0;
        let totalAttempts = 0;
        let timerInterval;
        let testWorker;
        let currentMethod = '';
        let maxAttempts = 0;

        // بيانات كلمات المرور والأنماط
        const commonPasswords = [
            '123456', 'password', '123456789', '12345678', '12345',
            '1234567', '1234567890', 'qwerty', 'abc123', '111111',
            'password1', 'admin', 'welcome', 'monkey', 'login',
            'princess', 'qwertyuiop', 'solo', 'passw0rd', 'starwars',
            'hello', 'dragon', 'master', 'freedom', 'whatever',
            'qazwsx', 'trustno1', 'jordan', 'harley', 'robert',
            'matthew', 'daniel', 'andrew', 'joshua', 'hunter',
            'target', 'charlie', 'michael', 'sunshine', 'computer',
            'michelle', 'jessica', 'pepper', 'zxcvbnm', 'ashley',
            'nicole', 'chelsea', 'biteme', 'summer', 'sophie',
            'football', 'jesus', 'ninja', 'mustang', 'mercedes',
            'samsung', 'cookie', 'maverick', 'tigger', 'puppy',
            'flower', 'baseball', 'shadow', 'lovely', 'buster',
            'basketball', 'soccer', 'purple', 'matrix', 'secret',
            'orange', 'yankees', 'austin', 'william', 'golfer',
            'heather', 'hammer', 'maggie', 'enter', 'thunder',
            'cowboy', 'silver', 'richard', 'merlin', 'corvette',
            'bigdog', 'cheese', 'patrick', 'martin', 'ginger',
            'blondie', 'rock', 'angel', 'much', 'rebel', 'xxx',
            'qwerty123', 'password123', 'admin123', '123123',
            'test', 'guest', 'info', 'adm', 'mysql', 'user',
            'administrator', 'oracle', 'ftp', 'pi', 'puppet',
            'ansible', 'ec2-user', 'vagrant', 'azureuser'
        ];

        const dictionaryWords = [
            'welcome', 'hello', 'world', 'computer', 'internet',
            'security', 'system', 'network', 'server', 'database',
            'application', 'software', 'hardware', 'technology',
            'information', 'digital', 'online', 'website', 'email',
            'mobile', 'phone', 'device', 'machine', 'program',
            'code', 'data', 'file', 'folder', 'document', 'text',
            'image', 'video', 'audio', 'music', 'game', 'play',
            'work', 'office', 'home', 'family', 'friend', 'love',
            'life', 'time', 'money', 'business', 'company', 'job',
            'project', 'team', 'group', 'member', 'account',
            'profile', 'setting', 'option', 'feature', 'service',
            'support', 'help', 'guide', 'tutorial', 'example'
        ];

        const patterns = [
            // أنماط التاريخ
            '2023', '2022', '2021', '2020', '2019', '2018',
            '********', '********', '********', '********',
            // أنماط الأرقام
            '0000', '1111', '2222', '3333', '4444', '5555',
            '6666', '7777', '8888', '9999', '1010', '2020',
            // أنماط لوحة المفاتيح
            'qwerty', 'asdf', 'zxcv', 'qaz', 'wsx', 'edc',
            'qwe', 'asd', 'zxc', '147', '258', '369',
            // أنماط الكلمات + أرقام
            'test123', 'user123', 'pass123', 'admin123',
            'root123', 'demo123', 'temp123', 'guest123'
        ];

        // تحديث معلومات الطريقة
        function updateMethodInfo() {
            const method = document.getElementById('test_method').value;
            const infoDiv = document.getElementById('methodInfo');

            const methodInfos = {
                'common': {
                    text: 'اختبار +100 كلمة مرور شائعة جداً',
                    time: 'ثواني إلى دقائق',
                    attempts: '100+'
                },
                'pattern': {
                    text: 'اختبار الأنماط الشائعة والتسلسلات',
                    time: 'دقائق',
                    attempts: '1,000+'
                },
                'dictionary': {
                    text: 'كلمات عادية مع تنويعات',
                    time: 'دقائق إلى ساعات',
                    attempts: '10,000+'
                },
                'brute_simple': {
                    text: 'تجربة منهجية للأحرف البسيطة',
                    time: 'دقائق إلى ساعات',
                    attempts: 'متغير'
                },
                'wordlist_extended': {
                    text: 'قاموس موسع مع تركيبات متقدمة',
                    time: 'ساعات',
                    attempts: '100,000+'
                },
                'rule_based': {
                    text: 'تحويلات ذكية للكلمات',
                    time: 'ساعات',
                    attempts: '50,000+'
                },
                'hybrid': {
                    text: 'مزيج من عدة طرق',
                    time: 'دقائق إلى ساعات',
                    attempts: 'متغير'
                },
                'comprehensive': {
                    text: 'جميع الطرق معاً',
                    time: 'ساعات إلى أيام',
                    attempts: '1,000,000+'
                }
            };

            const info = methodInfos[method];
            infoDiv.innerHTML = `
                <strong>الوصف:</strong> ${info.text}<br>
                <strong>الوقت المتوقع:</strong> ${info.time}<br>
                <strong>عدد المحاولات:</strong> ${info.attempts}
            `;
        }

        // توليد bcrypt hash للاختبار
        function generateTestHash(password) {
            const hash = bcrypt.hashSync(password, 10);
            document.getElementById('bcrypt_hash').value = hash;
            document.getElementById('test_password').value = password;

            // تحليل كلمة المرور تلقائياً
            analyzePassword();

            alert(`تم توليد hash لكلمة المرور: ${password}`);
        }

        // بدء الاختبار
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            startTest();
        });

        function startTest() {
            const hash = document.getElementById('bcrypt_hash').value.trim();
            const method = document.getElementById('test_method').value;
            maxAttempts = parseInt(document.getElementById('max_attempts').value);

            if (!hash) {
                alert('يرجى إدخال bcrypt hash');
                return;
            }

            if (!hash.match(/^\$2[ayb]\$.{56}$/)) {
                alert('Hash غير صحيح - يجب أن يكون bcrypt hash صالح');
                return;
            }

            testRunning = true;
            currentMethod = method;
            startTime = Date.now();
            totalAttempts = 0;

            // إظهار عناصر التقدم
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('stopTest').style.display = 'inline-block';
            document.querySelector('button[type="submit"]').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'none';

            // بدء المؤقت
            timerInterval = setInterval(updateTimer, 1000);

            // بدء الاختبار
            runTest(hash, method, maxAttempts);
        }

        function stopTest() {
            testRunning = false;
            clearInterval(timerInterval);

            if (testWorker) {
                testWorker.terminate();
                testWorker = null;
            }

            document.getElementById('stopTest').style.display = 'none';
            document.querySelector('button[type="submit"]').style.display = 'inline-block';

            showResults({
                found: false,
                password: '',
                attempts: totalAttempts,
                time_taken: (Date.now() - startTime) / 1000,
                method: currentMethod,
                stopped: true
            });
        }

        function updateTimer() {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('elapsedTime').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // تشغيل الاختبار
        async function runTest(hash, method, maxAttempts) {
            let result = { found: false, password: '', attempts: 0 };

            try {
                switch (method) {
                    case 'common':
                        result = await testCommonPasswords(hash, maxAttempts);
                        break;
                    case 'pattern':
                        result = await testPatterns(hash, maxAttempts);
                        break;
                    case 'dictionary':
                        result = await testDictionary(hash, maxAttempts);
                        break;
                    case 'brute_simple':
                        result = await testBruteForce(hash, maxAttempts);
                        break;
                    case 'wordlist_extended':
                        result = await testExtendedWordlist(hash, maxAttempts);
                        break;
                    case 'rule_based':
                        result = await testRuleBased(hash, maxAttempts);
                        break;
                    case 'hybrid':
                        result = await testHybrid(hash, maxAttempts);
                        break;
                    case 'comprehensive':
                        result = await testComprehensive(hash, maxAttempts);
                        break;
                }
            } catch (error) {
                console.error('Test error:', error);
                result.error = error.message;
            }

            if (testRunning) {
                testRunning = false;
                clearInterval(timerInterval);
                result.time_taken = (Date.now() - startTime) / 1000;
                result.method = method;
                showResults(result);
            }
        }

        // اختبار كلمات المرور الشائعة
        async function testCommonPasswords(hash, maxAttempts) {
            let attempts = 0;

            for (let password of commonPasswords) {
                if (!testRunning || attempts >= maxAttempts) break;

                attempts++;
                updateProgress(attempts, maxAttempts, password);

                if (bcrypt.compareSync(password, hash)) {
                    return { found: true, password: password, attempts: attempts };
                }

                // تأخير صغير لتحديث الواجهة
                if (attempts % 10 === 0) {
                    await sleep(1);
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار الأنماط
        async function testPatterns(hash, maxAttempts) {
            let attempts = 0;

            // إضافة أنماط إضافية
            const extendedPatterns = [...patterns];

            // أرقام متكررة
            for (let digit = 0; digit <= 9; digit++) {
                for (let length = 3; length <= 8; length++) {
                    extendedPatterns.push(digit.toString().repeat(length));
                }
            }

            // تسلسلات
            const sequences = ['123456789', '987654321', 'abcdefgh', 'zyxwvuts'];
            for (let seq of sequences) {
                for (let length = 3; length <= seq.length; length++) {
                    extendedPatterns.push(seq.substring(0, length));
                }
            }

            for (let pattern of extendedPatterns) {
                if (!testRunning || attempts >= maxAttempts) break;

                attempts++;
                updateProgress(attempts, maxAttempts, pattern);

                if (bcrypt.compareSync(pattern, hash)) {
                    return { found: true, password: pattern, attempts: attempts };
                }

                if (attempts % 50 === 0) {
                    await sleep(1);
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار القاموس
        async function testDictionary(hash, maxAttempts) {
            let attempts = 0;
            const variations = ['', '1', '12', '123', '1234', '12345', '!', '@', '#', '$'];

            for (let word of dictionaryWords) {
                if (!testRunning || attempts >= maxAttempts) break;

                for (let suffix of variations) {
                    if (!testRunning || attempts >= maxAttempts) break;

                    const password = word + suffix;
                    attempts++;
                    updateProgress(attempts, maxAttempts, password);

                    if (bcrypt.compareSync(password, hash)) {
                        return { found: true, password: password, attempts: attempts };
                    }

                    // اختبار مع أول حرف كبير
                    const passwordCap = word.charAt(0).toUpperCase() + word.slice(1) + suffix;
                    attempts++;
                    updateProgress(attempts, maxAttempts, passwordCap);

                    if (bcrypt.compareSync(passwordCap, hash)) {
                        return { found: true, password: passwordCap, attempts: attempts };
                    }

                    if (attempts % 20 === 0) {
                        await sleep(1);
                    }
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار القوة الغاشمة البسيطة
        async function testBruteForce(hash, maxAttempts) {
            let attempts = 0;
            const charset = getCharset();
            const minLength = parseInt(document.getElementById('min_length').value);
            const maxLength = Math.min(parseInt(document.getElementById('max_length').value), 6);

            for (let length = minLength; length <= maxLength; length++) {
                if (!testRunning || attempts >= maxAttempts) break;

                const result = await generatePasswords(charset, length, hash, maxAttempts, attempts);
                attempts = result.attempts;

                if (result.found) {
                    return result;
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // توليد كلمات المرور للقوة الغاشمة
        async function generatePasswords(charset, length, hash, maxAttempts, startAttempts) {
            let attempts = startAttempts;
            const total = Math.pow(charset.length, length);

            for (let i = 0; i < total && testRunning && attempts < maxAttempts; i++) {
                let password = '';
                let temp = i;

                for (let j = 0; j < length; j++) {
                    password = charset[temp % charset.length] + password;
                    temp = Math.floor(temp / charset.length);
                }

                attempts++;
                updateProgress(attempts, maxAttempts, password);

                if (bcrypt.compareSync(password, hash)) {
                    return { found: true, password: password, attempts: attempts };
                }

                if (attempts % 100 === 0) {
                    await sleep(1);
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // الحصول على مجموعة الأحرف
        function getCharset() {
            const type = document.getElementById('charset_type').value;

            switch (type) {
                case 'basic':
                    return 'abcdefghijklmnopqrstuvwxyz0123456789';
                case 'extended':
                    return 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                case 'special':
                    return 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
                case 'unicode':
                    return 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?أبتثجحخدذرزسشصضطظعغفقكلمنهوي';
                default:
                    return 'abcdefghijklmnopqrstuvwxyz0123456789';
            }
        }

        // اختبار قاموس موسع
        async function testExtendedWordlist(hash, maxAttempts) {
            let attempts = 0;
            const extendedWords = [
                ...commonPasswords, ...dictionaryWords,
                'ahmed', 'mohamed', 'ali', 'omar', 'sara', 'fatima',
                'john', 'mike', 'david', 'sarah', 'mary', 'lisa',
                'server', 'client', 'mysql', 'php', 'html', 'css'
            ];

            const variations = [];
            for (let i = 0; i <= 999; i++) {
                variations.push(i.toString());
            }

            for (let word of extendedWords) {
                if (!testRunning || attempts >= maxAttempts) break;

                for (let variation of variations) {
                    if (!testRunning || attempts >= maxAttempts) break;

                    const combinations = [
                        word + variation,
                        variation + word,
                        word.charAt(0).toUpperCase() + word.slice(1) + variation,
                        word.toUpperCase() + variation,
                        word + variation + '!',
                        word + variation + '@'
                    ];

                    for (let password of combinations) {
                        if (!testRunning || attempts >= maxAttempts) break;

                        attempts++;
                        updateProgress(attempts, maxAttempts, password);

                        if (bcrypt.compareSync(password, hash)) {
                            return { found: true, password: password, attempts: attempts };
                        }

                        if (attempts % 100 === 0) {
                            await sleep(1);
                        }
                    }
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار قائم على القواعد
        async function testRuleBased(hash, maxAttempts) {
            let attempts = 0;
            const baseWords = ['password', 'admin', 'user', 'test', 'login', 'welcome'];

            // قواعد الاستبدال
            const substitutions = [
                { 'a': '@', 'e': '3', 'i': '1', 'o': '0', 's': '$' },
                { 'a': '4', 'e': '3', 'i': '!', 'o': '0', 's': '5' }
            ];

            for (let word of baseWords) {
                if (!testRunning || attempts >= maxAttempts) break;

                const variations = [word];

                // تطبيق الاستبدالات
                for (let sub of substitutions) {
                    let newWord = word;
                    for (let [from, to] of Object.entries(sub)) {
                        newWord = newWord.replace(new RegExp(from, 'g'), to);
                    }
                    variations.push(newWord);
                }

                // إضافة أرقام
                for (let variation of variations) {
                    for (let i = 0; i <= 999; i++) {
                        if (!testRunning || attempts >= maxAttempts) break;

                        const passwords = [
                            variation + i,
                            i + variation,
                            variation.charAt(0).toUpperCase() + variation.slice(1) + i,
                            variation.toUpperCase() + i
                        ];

                        for (let password of passwords) {
                            if (!testRunning || attempts >= maxAttempts) break;

                            attempts++;
                            updateProgress(attempts, maxAttempts, password);

                            if (bcrypt.compareSync(password, hash)) {
                                return { found: true, password: password, attempts: attempts };
                            }

                            if (attempts % 50 === 0) {
                                await sleep(1);
                            }
                        }
                    }
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار هجين
        async function testHybrid(hash, maxAttempts) {
            const methods = ['common', 'pattern', 'dictionary'];
            let totalAttempts = 0;

            for (let method of methods) {
                if (!testRunning || totalAttempts >= maxAttempts) break;

                const methodAttempts = Math.floor(maxAttempts / methods.length);
                let result;

                switch (method) {
                    case 'common':
                        result = await testCommonPasswords(hash, methodAttempts);
                        break;
                    case 'pattern':
                        result = await testPatterns(hash, methodAttempts);
                        break;
                    case 'dictionary':
                        result = await testDictionary(hash, methodAttempts);
                        break;
                }

                totalAttempts += result.attempts;

                if (result.found) {
                    result.attempts = totalAttempts;
                    return result;
                }
            }

            return { found: false, password: '', attempts: totalAttempts };
        }

        // اختبار شامل
        async function testComprehensive(hash, maxAttempts) {
            const methods = ['common', 'pattern', 'dictionary', 'wordlist_extended', 'rule_based'];
            let totalAttempts = 0;

            for (let method of methods) {
                if (!testRunning || totalAttempts >= maxAttempts) break;

                const methodAttempts = Math.floor(maxAttempts / methods.length);
                let result;

                switch (method) {
                    case 'common':
                        result = await testCommonPasswords(hash, methodAttempts);
                        break;
                    case 'pattern':
                        result = await testPatterns(hash, methodAttempts);
                        break;
                    case 'dictionary':
                        result = await testDictionary(hash, methodAttempts);
                        break;
                    case 'wordlist_extended':
                        result = await testExtendedWordlist(hash, methodAttempts);
                        break;
                    case 'rule_based':
                        result = await testRuleBased(hash, methodAttempts);
                        break;
                }

                totalAttempts += result.attempts;

                if (result.found) {
                    result.attempts = totalAttempts;
                    return result;
                }
            }

            return { found: false, password: '', attempts: totalAttempts };
        }

        // تحديث التقدم
        function updateProgress(attempts, maxAttempts, currentPassword = '') {
            totalAttempts = attempts;
            const percentage = Math.min((attempts / maxAttempts) * 100, 100);

            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressText').textContent = percentage.toFixed(1) + '%';
            document.getElementById('totalAttempts').textContent = attempts.toLocaleString();
            document.getElementById('currentAttempt').textContent =
                `المحاولة ${attempts.toLocaleString()} من ${maxAttempts.toLocaleString()}`;

            if (currentPassword) {
                document.getElementById('currentPassword').textContent =
                    `كلمة المرور الحالية: ${currentPassword}`;
            }

            // حساب السرعة
            const elapsed = (Date.now() - startTime) / 1000;
            if (elapsed > 0) {
                const speed = Math.round(attempts / elapsed);
                document.getElementById('attemptsPerSecond').textContent = speed.toLocaleString();
            }
        }

        // دالة النوم
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // عرض النتائج
        function showResults(data) {
            const container = document.getElementById('resultsContainer');
            const header = document.getElementById('resultsHeader');
            const body = document.getElementById('resultsBody');

            container.style.display = 'block';
            document.getElementById('stopTest').style.display = 'none';
            document.querySelector('button[type="submit"]').style.display = 'inline-block';

            let resultHtml = '';

            if (data.found) {
                header.innerHTML = '<h4><i class="fas fa-check-circle text-success"></i> تم العثور على كلمة المرور!</h4>';
                header.className = 'card-header bg-success text-white';

                resultHtml = `
                    <div class="alert alert-success">
                        <h5><i class="fas fa-key"></i> كلمة المرور المكتشفة:</h5>
                        <div class="bg-white text-dark p-3 rounded" style="font-family: monospace; font-size: 1.2em; font-weight: bold;">
                            ${data.password}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6>عدد المحاولات</h6>
                                <h4>${data.attempts.toLocaleString()}</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6>الوقت المستغرق</h6>
                                <h4>${data.time_taken.toFixed(2)}s</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6>طريقة الاختبار</h6>
                                <h4>${getMethodName(data.method)}</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6>السرعة</h6>
                                <h4>${Math.round(data.attempts / data.time_taken).toLocaleString()}/s</h4>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-exclamation-triangle"></i> تحذير أمني:</h6>
                        <p>كلمة المرور ضعيفة ويمكن كسرها بسهولة. يُنصح بتغييرها إلى كلمة مرور أقوى.</p>
                    </div>
                `;
            } else {
                header.innerHTML = '<h4><i class="fas fa-times-circle text-danger"></i> لم يتم العثور على كلمة المرور</h4>';
                header.className = 'card-header bg-danger text-white';

                const statusText = data.stopped ? 'تم إيقاف الاختبار' : 'اكتمل الاختبار';

                resultHtml = `
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> ${statusText}</h5>
                        <p>لم يتم العثور على كلمة المرور بعد ${data.attempts.toLocaleString()} محاولة.</p>
                        <p><strong>الوقت المستغرق:</strong> ${data.time_taken.toFixed(2)}s</p>
                        <hr>
                        <p class="mb-0">كلمة المرور تبدو قوية ومقاومة للهجمات الأساسية.</p>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stats-card">
                                <h6>عدد المحاولات</h6>
                                <h4>${data.attempts.toLocaleString()}</h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-card">
                                <h6>الوقت المستغرق</h6>
                                <h4>${data.time_taken.toFixed(2)}s</h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-card">
                                <h6>السرعة المتوسطة</h6>
                                <h4>${Math.round(data.attempts / data.time_taken).toLocaleString()}/s</h4>
                            </div>
                        </div>
                    </div>
                `;
            }

            body.innerHTML = resultHtml;
        }

        // الحصول على اسم الطريقة
        function getMethodName(method) {
            const methods = {
                'common': 'كلمات شائعة',
                'pattern': 'أنماط شائعة',
                'dictionary': 'قاموس',
                'brute_simple': 'قوة غاشمة بسيطة',
                'wordlist_extended': 'قاموس موسع',
                'rule_based': 'قائم على قواعد',
                'hybrid': 'هجين',
                'comprehensive': 'شامل'
            };
            return methods[method] || method;
        }

        // تحليل كلمة المرور
        function analyzePassword() {
            const password = document.getElementById('test_password').value.trim();

            if (!password) {
                alert('يرجى إدخال كلمة مرور للتحليل');
                return;
            }

            const analysis = analyzePasswordStrength(password);
            displayAnalysisResults(analysis);
        }

        // تحليل قوة كلمة المرور
        function analyzePasswordStrength(password) {
            const length = password.length;
            const hasLower = /[a-z]/.test(password);
            const hasUpper = /[A-Z]/.test(password);
            const hasDigits = /[0-9]/.test(password);
            const hasSpecial = /[^a-zA-Z0-9]/.test(password);
            const hasArabic = /[\u0600-\u06FF]/.test(password);

            // حساب حجم مجموعة الأحرف
            let charsetSize = 0;
            if (hasLower) charsetSize += 26;
            if (hasUpper) charsetSize += 26;
            if (hasDigits) charsetSize += 10;
            if (hasSpecial) charsetSize += 32;
            if (hasArabic) charsetSize += 28;

            // حساب الإنتروبيا
            const entropy = charsetSize > 0 ? length * Math.log2(charsetSize) : 0;

            // كشف الأنماط
            const patterns = [];
            if (commonPasswords.includes(password.toLowerCase())) patterns.push('common_password');
            if (/^(.)\1+$/.test(password)) patterns.push('repeating');
            if (/^(012|123|234|345|456|567|678|789|890|987|876|765|654|543|432|321|210)/.test(password)) patterns.push('sequential');
            if (/^(qwerty|asdf|zxcv|qaz|wsx|edc)/.test(password.toLowerCase())) patterns.push('keyboard_pattern');
            if (/^\d{4}$|^\d{2}\/\d{2}\/\d{4}$|^\d{8}$/.test(password)) patterns.push('date_pattern');
            if (/^\d+$/.test(password)) patterns.push('numbers_only');
            if (/^[a-zA-Z]+$/.test(password)) patterns.push('letters_only');

            // تحديد مستوى القوة
            let score = 0;
            if (patterns.includes('common_password')) score -= 50;
            if (patterns.includes('repeating')) score -= 40;
            if (patterns.includes('sequential')) score -= 30;
            if (patterns.includes('keyboard_pattern')) score -= 25;
            if (patterns.includes('date_pattern')) score -= 20;
            if (patterns.includes('numbers_only')) score -= 20;
            if (patterns.includes('letters_only')) score -= 15;

            if (length >= 8) score += 10;
            if (length >= 12) score += 20;
            if (hasLower) score += 5;
            if (hasUpper) score += 10;
            if (hasDigits) score += 10;
            if (hasSpecial) score += 15;
            if (entropy > 40) score += 20;
            if (entropy > 60) score += 30;

            let level;
            if (score < -30) level = 'very_weak';
            else if (score < -10) level = 'weak';
            else if (score < 20) level = 'medium';
            else if (score < 40) level = 'strong';
            else level = 'very_strong';

            // توصيات الاختبار
            const recommendations = getTestRecommendations(level, patterns, length);

            return {
                password: password,
                length: length,
                charsetSize: charsetSize,
                entropy: entropy,
                patterns: patterns,
                level: level,
                score: score,
                characteristics: {
                    hasLower, hasUpper, hasDigits, hasSpecial, hasArabic
                },
                recommendations: recommendations
            };
        }

        // الحصول على توصيات الاختبار
        function getTestRecommendations(level, patterns, length) {
            const recommendations = [];

            if (patterns.includes('common_password')) {
                recommendations.push({ method: 'common', priority: 1, reason: 'كلمة مرور شائعة' });
            }

            if (patterns.includes('sequential') || patterns.includes('keyboard_pattern') || patterns.includes('date_pattern')) {
                recommendations.push({ method: 'pattern', priority: 2, reason: 'تحتوي على أنماط شائعة' });
            }

            if (patterns.includes('numbers_only')) {
                recommendations.push({ method: 'brute_simple', priority: 1, reason: 'أرقام فقط - قوة غاشمة بسيطة' });
            }

            switch (level) {
                case 'very_weak':
                    recommendations.push({ method: 'common', priority: 1, reason: 'ضعيفة جداً - ابدأ بالشائعة' });
                    recommendations.push({ method: 'pattern', priority: 2, reason: 'فحص الأنماط البسيطة' });
                    break;
                case 'weak':
                    recommendations.push({ method: 'hybrid', priority: 1, reason: 'ضعيفة - اختبار هجين' });
                    recommendations.push({ method: 'rule_based', priority: 2, reason: 'قواعد التحويل' });
                    break;
                case 'medium':
                    recommendations.push({ method: 'wordlist_extended', priority: 1, reason: 'متوسطة - قاموس موسع' });
                    recommendations.push({ method: 'rule_based', priority: 2, reason: 'قواعد متقدمة' });
                    break;
                case 'strong':
                    recommendations.push({ method: 'comprehensive', priority: 1, reason: 'قوية - اختبار شامل' });
                    break;
                case 'very_strong':
                    recommendations.push({ method: 'comprehensive', priority: 1, reason: 'قوية جداً - اختبار شامل طويل المدى' });
                    break;
            }

            // إزالة التكرارات وترتيب حسب الأولوية
            const uniqueRecommendations = [];
            const seen = new Set();

            for (let rec of recommendations) {
                if (!seen.has(rec.method)) {
                    seen.add(rec.method);
                    uniqueRecommendations.push(rec);
                }
            }

            return uniqueRecommendations.sort((a, b) => a.priority - b.priority);
        }

        // عرض نتائج التحليل
        function displayAnalysisResults(analysis) {
            const container = document.getElementById('analysisResults');
            container.style.display = 'block';

            const levelClass = getWeaknessLevelClass(analysis.level);
            const levelText = getWeaknessLevelText(analysis.level);

            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle"></i> معلومات أساسية:</h6>
                        <ul class="list-unstyled">
                            <li><strong>الطول:</strong> ${analysis.length} حرف</li>
                            <li><strong>حجم مجموعة الأحرف:</strong> ${analysis.charsetSize}</li>
                            <li><strong>الإنتروبيا:</strong> ${analysis.entropy.toFixed(2)} بت</li>
                            <li><strong>نقاط الأمان:</strong> ${analysis.score}</li>
                        </ul>

                        <h6><i class="fas fa-shield-alt"></i> مستوى القوة:</h6>
                        <span class="badge ${levelClass} fs-6">${levelText}</span>
                    </div>

                    <div class="col-md-6">
                        <h6><i class="fas fa-search"></i> الأنماط المكتشفة:</h6>
                        <div class="mb-3">
                            ${analysis.patterns.length > 0 ?
                                analysis.patterns.map(pattern =>
                                    `<span class="badge bg-warning text-dark me-1">${getPatternText(pattern)}</span>`
                                ).join('') :
                                '<span class="badge bg-success">لا توجد أنماط ضعيفة</span>'
                            }
                        </div>

                        <h6><i class="fas fa-check-circle"></i> خصائص الأحرف:</h6>
                        <div>
                            ${analysis.characteristics.hasLower ? '<span class="badge bg-success me-1">أحرف صغيرة</span>' : '<span class="badge bg-secondary me-1">لا توجد أحرف صغيرة</span>'}
                            ${analysis.characteristics.hasUpper ? '<span class="badge bg-success me-1">أحرف كبيرة</span>' : '<span class="badge bg-secondary me-1">لا توجد أحرف كبيرة</span>'}
                            ${analysis.characteristics.hasDigits ? '<span class="badge bg-success me-1">أرقام</span>' : '<span class="badge bg-secondary me-1">لا توجد أرقام</span>'}
                            ${analysis.characteristics.hasSpecial ? '<span class="badge bg-success me-1">رموز خاصة</span>' : '<span class="badge bg-secondary me-1">لا توجد رموز</span>'}
                            ${analysis.characteristics.hasArabic ? '<span class="badge bg-info me-1">أحرف عربية</span>' : ''}
                        </div>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-12">
                        <h6><i class="fas fa-bullseye"></i> الاختبارات المقترحة:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الأولوية</th>
                                        <th>نوع الاختبار</th>
                                        <th>السبب</th>
                                        <th>إجراء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${analysis.recommendations.map((test, index) => `
                                        <tr>
                                            <td><span class="badge bg-primary">${test.priority}</span></td>
                                            <td>${getMethodName(test.method)}</td>
                                            <td>${test.reason}</td>
                                            <td>
                                                <button class="btn btn-sm btn-success"
                                                        onclick="applyRecommendation('${test.method}', ${analysis.length})">
                                                    <i class="fas fa-play"></i> تطبيق
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // تطبيق التوصية
        function applyRecommendation(method, passwordLength) {
            document.getElementById('test_method').value = method;

            // تطبيق الإعدادات المناسبة
            if (passwordLength <= 4) {
                document.getElementById('max_length').value = 4;
                document.getElementById('max_attempts').value = 100000;
            } else if (passwordLength <= 6) {
                document.getElementById('max_length').value = 6;
                document.getElementById('max_attempts').value = 1000000;
            } else if (passwordLength <= 8) {
                document.getElementById('max_length').value = 8;
                document.getElementById('max_attempts').value = 10000000;
            } else {
                document.getElementById('max_length').value = 10;
                document.getElementById('max_attempts').value = 10000000;
            }

            updateMethodInfo();
            alert('تم تطبيق الإعدادات المقترحة. يمكنك الآن بدء الاختبار.');
        }

        // دوال مساعدة للعرض
        function getWeaknessLevelClass(level) {
            const classes = {
                'very_weak': 'bg-danger',
                'weak': 'bg-warning',
                'medium': 'bg-info',
                'strong': 'bg-success',
                'very_strong': 'bg-primary'
            };
            return classes[level] || 'bg-secondary';
        }

        function getWeaknessLevelText(level) {
            const texts = {
                'very_weak': 'ضعيفة جداً',
                'weak': 'ضعيفة',
                'medium': 'متوسطة',
                'strong': 'قوية',
                'very_strong': 'قوية جداً'
            };
            return texts[level] || 'غير محدد';
        }

        function getPatternText(pattern) {
            const texts = {
                'common_password': 'كلمة مرور شائعة',
                'sequential': 'تسلسل',
                'repeating': 'تكرار',
                'keyboard_pattern': 'نمط لوحة مفاتيح',
                'date_pattern': 'نمط تاريخ',
                'numbers_only': 'أرقام فقط',
                'letters_only': 'أحرف فقط'
            };
            return texts[pattern] || pattern;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateMethodInfo();

            // إضافة مستمع لتحليل كلمة المرور عند الكتابة
            document.getElementById('test_password').addEventListener('input', function() {
                const password = this.value.trim();
                if (password.length > 0) {
                    setTimeout(() => {
                        if (this.value.trim() === password) {
                            analyzePassword();
                        }
                    }, 500);
                }
            });
        });
    </script>
</body>
</html>