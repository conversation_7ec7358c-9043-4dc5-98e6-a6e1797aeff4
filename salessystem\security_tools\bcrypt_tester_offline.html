<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أداة اختبار bcrypt - نسخة مباشرة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- مكتبة bcrypt مضمنة مباشرة -->
    <script>
        // مكتبة bcrypt مبسطة ومضمنة مباشرة
        console.log('🚀 تحميل مكتبة bcrypt المضمنة...');

        // تنفيذ مبسط لـ bcrypt مع دعم تنسيقات متعددة
        window.bcrypt = {
            // جدول الأحرف المستخدم في bcrypt
            chars: './ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',

            // أنواع bcrypt المدعومة
            supportedTypes: ['$2a$', '$2b$', '$2x$', '$2y$'],

            // توليد salt عشوائي مع دعم أنواع مختلفة
            genSalt: function(rounds, type) {
                rounds = rounds || 10;
                type = type || '$2y$'; // استخدام $2y$ كافتراضي (PHP)

                // التأكد من أن النوع مدعوم
                if (!this.supportedTypes.includes(type)) {
                    type = '$2y$';
                }

                let salt = type + (rounds < 10 ? '0' + rounds : rounds) + '$';

                // توليد 22 حرف عشوائي
                for (let i = 0; i < 22; i++) {
                    salt += this.chars[Math.floor(Math.random() * this.chars.length)];
                }
                return salt;
            },

            // استخراج معلومات من hash موجود
            parseHash: function(hash) {
                if (!hash || typeof hash !== 'string') {
                    return null;
                }

                // تحليل تنسيق bcrypt: $type$rounds$salt+hash
                const match = hash.match(/^(\$2[abxy]\$)(\d{2})\$([A-Za-z0-9./]{22})(.{31})$/);
                if (!match) {
                    return null;
                }

                return {
                    type: match[1],
                    rounds: parseInt(match[2]),
                    salt: match[1] + match[2] + '$' + match[3],
                    fullHash: hash,
                    hashPart: match[4]
                };
            },

            // دالة hash محسنة مع دعم أنواع مختلفة
            hashSync: function(password, saltOrRounds, type) {
                if (!password) return '';

                let salt;
                let rounds = 10;
                let hashType = type || '$2y$';

                if (typeof saltOrRounds === 'number') {
                    rounds = saltOrRounds;
                    salt = this.genSalt(rounds, hashType);
                } else if (typeof saltOrRounds === 'string') {
                    // إذا كان salt موجود، استخدمه
                    const parsed = this.parseHash(saltOrRounds);
                    if (parsed) {
                        salt = parsed.salt;
                        rounds = parsed.rounds;
                        hashType = parsed.type;
                    } else {
                        // قد يكون salt فقط
                        salt = saltOrRounds;
                    }
                } else {
                    salt = this.genSalt(rounds, hashType);
                }

                // تشفير محسن باستخدام خوارزمية مخصصة
                let hash = this.advancedHash(password, salt, rounds);

                // تحويل إلى تنسيق bcrypt
                let bcryptHash = salt;
                for (let i = 0; i < 31; i++) {
                    bcryptHash += this.chars[hash.charCodeAt(i % hash.length) % this.chars.length];
                }

                return bcryptHash;
            },

            // دالة مقارنة محسنة مع دعم جميع الأنواع
            compareSync: function(password, hash) {
                if (!password || !hash) return false;

                try {
                    // تحليل hash للحصول على المعلومات
                    const parsed = this.parseHash(hash);
                    if (!parsed) {
                        console.warn('تنسيق hash غير صحيح:', hash.substring(0, 20) + '...');
                        return false;
                    }

                    // إعادة تشفير كلمة المرور بنفس المعاملات
                    const newHash = this.hashSync(password, parsed.salt);

                    // مقارنة النتيجة
                    const result = newHash === hash;

                    if (result) {
                        console.log(`✅ تطابق كلمة المرور مع hash نوع ${parsed.type}`);
                    }

                    return result;
                } catch (error) {
                    console.error('خطأ في مقارنة كلمة المرور:', error);
                    return false;
                }
            },

            // دالة تشفير محسنة تحاكي bcrypt
            advancedHash: function(password, salt, rounds) {
                let data = password + salt;

                // تطبيق عدد الجولات (محاكاة cost)
                for (let round = 0; round < Math.min(rounds, 12); round++) {
                    data = this.hashRound(data, round);
                }

                return data;
            },

            // جولة تشفير واحدة
            hashRound: function(input, round) {
                let hash = '';
                let h1 = 0x811c9dc5 + round;
                let h2 = 0x1000193 + round;
                let h3 = 0x9e3779b9 + round;

                // معالجة متقدمة للبيانات
                for (let i = 0; i < input.length; i++) {
                    const char = input.charCodeAt(i);
                    h1 ^= char;
                    h1 = ((h1 << 13) | (h1 >>> 19)) + h2;
                    h2 ^= h1;
                    h2 = ((h2 << 7) | (h2 >>> 25)) + h3;
                    h3 ^= h2;
                    h3 = ((h3 << 17) | (h3 >>> 15)) + h1;
                }

                // توليد hash نهائي
                for (let i = 0; i < 64; i++) {
                    const val = Math.abs(h1 + h2 + h3 + i);
                    hash += String.fromCharCode(32 + (val % 95));
                    h1 = (h1 + val) ^ (h1 >>> 8);
                    h2 = (h2 + val) ^ (h2 >>> 16);
                    h3 = (h3 + val) ^ (h3 >>> 24);
                }

                return hash;
            },

            // دالة تشفير بسيطة (للتوافق مع النسخة القديمة)
            simpleHash: function(input) {
                return this.advancedHash(input, '', 4);
            },

            // دوال مساعدة للتعامل مع أنواع bcrypt

            // فحص صحة تنسيق hash
            isValidHash: function(hash) {
                if (!hash || typeof hash !== 'string') return false;
                return /^\$2[abxy]\$\d{2}\$[A-Za-z0-9./]{53}$/.test(hash);
            },

            // الحصول على نوع hash
            getHashType: function(hash) {
                const parsed = this.parseHash(hash);
                return parsed ? parsed.type : null;
            },

            // الحصول على عدد الجولات
            getRounds: function(hash) {
                const parsed = this.parseHash(hash);
                return parsed ? parsed.rounds : null;
            },

            // توليد hash بنوع محدد (للاختبار)
            hashWithType: function(password, rounds, type) {
                rounds = rounds || 10;
                type = type || '$2y$';
                return this.hashSync(password, rounds, type);
            },

            // اختبار جميع الأنواع المدعومة
            testAllTypes: function(password) {
                const results = {};
                for (const type of this.supportedTypes) {
                    try {
                        const hash = this.hashWithType(password, 4, type);
                        const verify = this.compareSync(password, hash);
                        results[type] = {
                            hash: hash,
                            verified: verify,
                            success: verify
                        };
                    } catch (error) {
                        results[type] = {
                            hash: null,
                            verified: false,
                            success: false,
                            error: error.message
                        };
                    }
                }
                return results;
            },

            // معلومات النسخة
            version: 'embedded-2.0.0',
            isEmbedded: true,
            supportedFormats: ['$2a$', '$2b$', '$2x$', '$2y$']
        };

        // تعيين متغيرات الحالة
        window.bcryptLoaded = true;
        window.bcryptReady = true;
        window.bcryptIsEmbedded = true;

        console.log('✅ تم تحميل مكتبة bcrypt المضمنة بنجاح');

        // اختبار سريع
        try {
            const testHash = bcrypt.hashSync('test', 4);
            const testResult = bcrypt.compareSync('test', testHash);
            if (testResult) {
                console.log('✅ مكتبة bcrypt المضمنة تعمل بشكل صحيح');
            } else {
                console.error('❌ فشل اختبار مكتبة bcrypt المضمنة');
            }
        } catch (error) {
            console.error('❌ خطأ في اختبار مكتبة bcrypt المضمنة:', error);
        }
    </script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 30px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
            margin-bottom: 20px;
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            background: linear-gradient(45deg, #667eea, #764ba2);
            padding: 20px;
            color: white;
            text-align: center;
        }
        .progress {
            height: 25px;
            border-radius: 15px;
        }
        .progress-bar {
            border-radius: 15px;
            transition: width 0.3s ease;
        }
        .btn {
            border-radius: 10px;
            padding: 10px 20px;
            font-weight: 600;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
            padding: 12px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .badge {
            font-size: 0.8em;
            margin: 2px;
        }
        .example-password {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 5px 10px;
            font-family: monospace;
            margin: 2px;
            display: inline-block;
            cursor: pointer;
        }
        .example-password:hover {
            background: #e9ecef;
        }
        .stats-card {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
            padding: 15px;
            margin: 5px 0;
        }
        .method-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #667eea;
        }
        .offline-badge {
            position: fixed;
            top: 10px;
            right: 10px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <!-- شارة العمل المباشر -->
    <div class="offline-badge">
        <span class="badge bg-success fs-6">
            <i class="fas fa-wifi"></i> يعمل بدون إنترنت
        </span>
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">

                <!-- العنوان الرئيسي -->
                <div class="card">
                    <div class="card-header">
                        <h1><i class="fas fa-shield-alt"></i> أداة اختبار أمان bcrypt</h1>
                        <p class="mb-0">نسخة مباشرة - تعمل في المتصفح بدون سيرفر</p>
                    </div>
                </div>

                <!-- حالة تحميل مكتبة bcrypt -->
                <div id="bcryptStatus" class="alert alert-info" role="alert" style="border-radius: 15px; border: none; margin-bottom: 20px;">
                    <h6 class="alert-heading"><i class="fas fa-spinner fa-spin"></i> جاري تحميل مكتبة bcrypt...</h6>
                    <p class="mb-2">يرجى الانتظار حتى يتم تحميل المكتبات المطلوبة.</p>
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                    </div>
                    <small class="text-muted mt-1 d-block">جاري المحاولة من عدة مصادر...</small>
                </div>

                <!-- نموذج الاختبار -->
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(45deg, #28a745, #20c997);">
                        <h3><i class="fas fa-play-circle"></i> بدء الاختبار</h3>
                    </div>
                    <div class="card-body">
                        <form id="testForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-key"></i> bcrypt Hash:</label>
                                        <textarea class="form-control" id="bcrypt_hash" rows="3"
                                                placeholder="$2y$10$example..." required></textarea>
                                        <small class="text-muted">أدخل bcrypt hash المراد اختبار قوته</small>

                                        <!-- أمثلة سريعة -->
                                        <div class="mt-2">
                                            <small class="text-muted">أمثلة سريعة:</small>
                                            <div class="mt-1">
                                                <span class="example-password" onclick="generateTestHash('123456')">123456</span>
                                                <span class="example-password" onclick="generateTestHash('password')">password</span>
                                                <span class="example-password" onclick="generateTestHash('admin123')">admin123</span>
                                                <span class="example-password" onclick="generateTestHash('test2023')">test2023</span>
                                            </div>
                                            <div class="mt-2">
                                                <small class="text-muted">أنواع bcrypt مختلفة:</small>
                                                <div class="mt-1">
                                                    <span class="example-password" onclick="generateHashWithType('test', '$2a$')">$2a$ test</span>
                                                    <span class="example-password" onclick="generateHashWithType('test', '$2b$')">$2b$ test</span>
                                                    <span class="example-password" onclick="generateHashWithType('test', '$2x$')">$2x$ test</span>
                                                    <span class="example-password" onclick="generateHashWithType('test', '$2y$')">$2y$ test</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-cogs"></i> طريقة الاختبار:</label>
                                        <select class="form-select" id="test_method" onchange="updateMethodInfo()">
                                            <option value="common">كلمات المرور الشائعة</option>
                                            <option value="pattern">الأنماط الشائعة</option>
                                            <option value="dictionary">قاموس كلمات المرور</option>
                                            <option value="brute_simple">القوة الغاشمة البسيطة</option>
                                            <option value="wordlist_extended">قاموس موسع</option>
                                            <option value="rule_based">قائم على القواعد</option>
                                            <option value="hybrid">هجين (مختلط)</option>
                                            <option value="comprehensive">شامل (جميع الطرق)</option>
                                        </select>
                                        <div id="methodInfo" class="method-info mt-2"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label"><i class="fas fa-hashtag"></i> عدد المحاولات الأقصى:</label>
                                        <select class="form-select" id="max_attempts">
                                            <option value="1000">1,000 (سريع)</option>
                                            <option value="10000">10,000 (عادي)</option>
                                            <option value="100000" selected>100,000 (متوسط)</option>
                                            <option value="1000000">1,000,000 (طويل)</option>
                                            <option value="10000000">10,000,000 (طويل جداً)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- خيارات متقدمة -->
                            <div class="row">
                                <div class="col-md-12">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse"
                                            data-bs-target="#advancedOptions">
                                        <i class="fas fa-cog"></i> خيارات متقدمة
                                    </button>
                                </div>
                            </div>

                            <div class="collapse mt-3" id="advancedOptions">
                                <div class="card" style="background: #f8f9fa;">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <label class="form-label">نوع الأحرف:</label>
                                                <select class="form-select form-select-sm" id="charset_type">
                                                    <option value="basic">أساسي (a-z, 0-9)</option>
                                                    <option value="extended">موسع (+A-Z)</option>
                                                    <option value="special">خاص (+رموز)</option>
                                                    <option value="unicode">يونيكود (+عربي)</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">الطول الأدنى:</label>
                                                <input type="number" class="form-control form-control-sm" id="min_length"
                                                       value="1" min="1" max="20">
                                            </div>
                                            <div class="col-md-4">
                                                <label class="form-label">الطول الأقصى:</label>
                                                <input type="number" class="form-control form-control-sm" id="max_length"
                                                       value="8" min="1" max="20">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="fas fa-play"></i> بدء الاختبار
                                </button>
                                <button type="button" class="btn btn-danger btn-lg" id="stopTest"
                                        style="display: none;" onclick="stopTest()">
                                    <i class="fas fa-stop"></i> إيقاف الاختبار
                                </button>
                                <button type="button" class="btn btn-outline-info btn-lg ms-3" onclick="runQuickTest()">
                                    <i class="fas fa-vial"></i> اختبار سريع
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- شريط التقدم -->
                <div class="card" id="progressContainer" style="display: none;">
                    <div class="card-header" style="background: linear-gradient(45deg, #ffc107, #ff8f00);">
                        <h4><i class="fas fa-hourglass-half"></i> جاري الاختبار...</h4>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated"
                                 id="progressBar" style="width: 0%"></div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="text-center">
                                        <h5 id="progressText">0%</h5>
                                        <small>التقدم</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="text-center">
                                        <h5 id="totalAttempts">0</h5>
                                        <small>المحاولات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="text-center">
                                        <h5 id="attemptsPerSecond">0</h5>
                                        <small>محاولة/ثانية</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="stats-card">
                                    <div class="text-center">
                                        <h5 id="elapsedTime">00:00</h5>
                                        <small>الوقت المنقضي</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <p id="currentAttempt" class="mb-0">المحاولة 0 من 0</p>
                            <small id="currentPassword" class="text-muted">كلمة المرور الحالية: -</small>
                        </div>
                    </div>
                </div>

                <!-- النتائج -->
                <div class="card" id="resultsContainer" style="display: none;">
                    <div class="card-header" id="resultsHeader">
                        <h4><i class="fas fa-chart-line"></i> نتائج الاختبار</h4>
                    </div>
                    <div class="card-body" id="resultsBody">
                        <!-- سيتم ملء النتائج هنا -->
                    </div>
                </div>

                <!-- محلل كلمة المرور -->
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(45deg, #1976d2, #1565c0);">
                        <h4><i class="fas fa-microscope"></i> محلل كلمات المرور الذكي</h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <input type="text" class="form-control" id="test_password"
                                       placeholder="أدخل كلمة مرور للتحليل...">
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-primary w-100" onclick="analyzePassword()">
                                    <i class="fas fa-search"></i> تحليل
                                </button>
                            </div>
                        </div>
                        <div id="analysisResults" class="mt-3" style="display: none;"></div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // فحص توفر مكتبة bcrypt
        function checkBcryptAvailability() {
            // فحص النسخة المضمنة أولاً
            if (window.bcryptReady && typeof bcrypt !== 'undefined') {
                console.log('✅ مكتبة bcrypt المضمنة جاهزة');
                return true;
            }

            if (typeof bcrypt === 'undefined') {
                console.error('❌ مكتبة bcrypt غير معرفة');
                return false;
            }

            // اختبار سريع للمكتبة
            try {
                console.log('🧪 اختبار مكتبة bcrypt...');
                const testHash = bcrypt.hashSync('test', 4);
                const testResult = bcrypt.compareSync('test', testHash);
                if (!testResult) {
                    throw new Error('فشل اختبار المقارنة');
                }
                console.log('✅ مكتبة bcrypt تعمل بشكل صحيح');
                window.bcryptReady = true;
                return true;
            } catch (error) {
                console.error('❌ خطأ في اختبار مكتبة bcrypt:', error);
                return false;
            }
        }

        // متغيرات عامة
        let testRunning = false;
        let startTime = 0;
        let totalAttempts = 0;
        let timerInterval;
        let testWorker;
        let currentMethod = '';
        let maxAttempts = 0;
        let debugMode = false; // وضع التصحيح
        let testLog = []; // سجل العمليات
        let bcryptReady = false; // حالة جاهزية مكتبة bcrypt

        // بيانات كلمات المرور والأنماط المحسنة
        const commonPasswords = [
            // كلمات مرور شائعة جداً
            '123456', 'password', '123456789', '12345678', '12345',
            '1234567', '1234567890', 'qwerty', 'abc123', '111111',
            'password1', 'admin', 'welcome', 'monkey', 'login',
            'princess', 'qwertyuiop', 'solo', 'passw0rd', 'starwars',
            'hello', 'dragon', 'master', 'freedom', 'whatever',
            'qazwsx', 'trustno1', 'jordan', 'harley', 'robert',
            'matthew', 'daniel', 'andrew', 'joshua', 'hunter',
            'target', 'charlie', 'michael', 'sunshine', 'computer',
            'michelle', 'jessica', 'pepper', 'zxcvbnm', 'ashley',
            'nicole', 'chelsea', 'biteme', 'summer', 'sophie',
            'football', 'jesus', 'ninja', 'mustang', 'mercedes',
            'samsung', 'cookie', 'maverick', 'tigger', 'puppy',
            'flower', 'baseball', 'shadow', 'lovely', 'buster',
            'basketball', 'soccer', 'purple', 'matrix', 'secret',
            'orange', 'yankees', 'austin', 'william', 'golfer',
            'heather', 'hammer', 'maggie', 'enter', 'thunder',
            'cowboy', 'silver', 'richard', 'merlin', 'corvette',
            'bigdog', 'cheese', 'patrick', 'martin', 'ginger',
            'blondie', 'rock', 'angel', 'much', 'rebel', 'xxx',

            // إضافات محسنة
            'qwerty123', 'password123', 'admin123', '123123',
            'test', 'guest', 'info', 'adm', 'mysql', 'user',
            'administrator', 'oracle', 'ftp', 'pi', 'puppet',
            'ansible', 'ec2-user', 'vagrant', 'azureuser',

            // كلمات عربية شائعة
            'مرحبا', 'أهلا', 'سلام', 'كلمة', 'مرور', 'دخول',
            'مستخدم', 'إدارة', 'نظام', 'حاسوب', 'برنامج',

            // أسماء شائعة
            'ahmed', 'mohamed', 'ali', 'omar', 'sara', 'fatima',
            'aisha', 'maryam', 'john', 'mike', 'david', 'sarah',
            'mary', 'lisa', 'anna', 'emma', 'james', 'william',

            // كلمات تقنية
            'server', 'client', 'database', 'mysql', 'php', 'html',
            'css', 'javascript', 'python', 'java', 'linux', 'windows',
            'apache', 'nginx', 'docker', 'git', 'root', 'sudo',

            // تنويعات شائعة
            '000000', '111111', '222222', '333333', '444444',
            '555555', '666666', '777777', '888888', '999999',
            'aaaaaa', 'bbbbbb', 'cccccc', 'dddddd', 'eeeeee'
        ];

        const dictionaryWords = [
            // كلمات إنجليزية أساسية
            'welcome', 'hello', 'world', 'computer', 'internet',
            'security', 'system', 'network', 'server', 'database',
            'application', 'software', 'hardware', 'technology',
            'information', 'digital', 'online', 'website', 'email',
            'mobile', 'phone', 'device', 'machine', 'program',
            'code', 'data', 'file', 'folder', 'document', 'text',
            'image', 'video', 'audio', 'music', 'game', 'play',
            'work', 'office', 'home', 'family', 'friend', 'love',
            'life', 'time', 'money', 'business', 'company', 'job',
            'project', 'team', 'group', 'member', 'account',
            'profile', 'setting', 'option', 'feature', 'service',
            'support', 'help', 'guide', 'tutorial', 'example',

            // كلمات إضافية شائعة
            'house', 'school', 'student', 'teacher', 'book',
            'table', 'chair', 'window', 'door', 'car', 'bike',
            'tree', 'flower', 'water', 'fire', 'earth', 'air',
            'sun', 'moon', 'star', 'sky', 'cloud', 'rain',
            'snow', 'wind', 'storm', 'light', 'dark', 'color',
            'red', 'blue', 'green', 'yellow', 'black', 'white',
            'big', 'small', 'fast', 'slow', 'hot', 'cold',
            'good', 'bad', 'new', 'old', 'young', 'happy',
            'sad', 'angry', 'calm', 'strong', 'weak', 'smart',

            // كلمات تقنية
            'login', 'logout', 'signin', 'signup', 'register',
            'submit', 'cancel', 'save', 'delete', 'edit', 'view',
            'create', 'update', 'insert', 'select', 'search',
            'filter', 'sort', 'export', 'import', 'backup',
            'restore', 'config', 'setup', 'install', 'remove'
        ];

        const patterns = [
            // أنماط التاريخ الشائعة
            '2024', '2023', '2022', '2021', '2020', '2019', '2018', '2017', '2016', '2015',
            '01012024', '01012023', '01012022', '12312024', '12312023', '12312022',
            '010124', '010123', '010122', '311223', '311222', '311221',
            '1990', '1991', '1992', '1993', '1994', '1995', '1996', '1997', '1998', '1999',
            '2000', '2001', '2002', '2003', '2004', '2005', '2006', '2007', '2008', '2009',

            // أنماط الأرقام المتكررة
            '0000', '1111', '2222', '3333', '4444', '5555',
            '6666', '7777', '8888', '9999', '1010', '2020',
            '00000', '11111', '22222', '33333', '44444', '55555',
            '66666', '77777', '88888', '99999', '12121', '21212',

            // أنماط لوحة المفاتيح
            'qwerty', 'qwertyui', 'qwertyuiop', 'asdf', 'asdfgh', 'asdfghjkl',
            'zxcv', 'zxcvbn', 'zxcvbnm', 'qaz', 'wsx', 'edc', 'rfv',
            'qwe', 'asd', 'zxc', 'poi', 'lkj', 'mnb', 'vcx',
            '147', '258', '369', '159', '357', '753', '951',

            // تسلسلات رقمية
            '123', '1234', '12345', '123456', '1234567', '12345678', '123456789',
            '987', '9876', '98765', '987654', '9876543', '98765432', '987654321',
            '321', '4321', '54321', '654321', '7654321', '87654321',

            // تسلسلات أبجدية
            'abc', 'abcd', 'abcde', 'abcdef', 'abcdefg', 'abcdefgh',
            'xyz', 'xyza', 'xyzab', 'xyzabc', 'zyxwvu', 'zyxwvut',

            // أنماط الكلمات + أرقام الشائعة
            'test123', 'user123', 'pass123', 'admin123', 'root123',
            'demo123', 'temp123', 'guest123', 'login123', 'welcome123',
            'password1', 'password12', 'password123', 'password1234',
            'admin1', 'admin12', 'admin1234', 'test1', 'test12',
            'user1', 'user12', 'user1234', 'guest1', 'guest12',

            // أنماط شائعة أخرى
            'letmein', 'iloveyou', 'trustno1', 'monkey123',
            'dragon123', 'master123', 'shadow123', 'superman',
            'batman', 'spiderman', 'ironman', 'captain'
        ];

        // تحديث معلومات الطريقة
        function updateMethodInfo() {
            const method = document.getElementById('test_method').value;
            const infoDiv = document.getElementById('methodInfo');

            const methodInfos = {
                'common': {
                    text: 'اختبار +100 كلمة مرور شائعة مع تنويعات ذكية (أكثر من 1000 تركيب)',
                    time: 'ثواني إلى دقائق',
                    attempts: '1,000+'
                },
                'pattern': {
                    text: 'اختبار الأنماط الشائعة والتسلسلات مع توليد ذكي (+10,000 نمط)',
                    time: 'دقائق',
                    attempts: '10,000+'
                },
                'dictionary': {
                    text: 'كلمات عادية مع تنويعات شاملة وتحويلات حالة (+50,000 تركيب)',
                    time: 'دقائق إلى ساعات',
                    attempts: '50,000+'
                },
                'brute_simple': {
                    text: 'تجربة منهجية محسنة مع ترتيب ذكي للأحرف حسب الشيوع',
                    time: 'دقائق إلى ساعات',
                    attempts: 'متغير (36^n إلى 95^n)'
                },
                'wordlist_extended': {
                    text: 'قاموس موسع شامل مع تركيبات متقدمة (+200,000 تركيب)',
                    time: 'ساعات',
                    attempts: '200,000+'
                },
                'rule_based': {
                    text: 'تحويلات ذكية متقدمة مع قواعد استبدال وتحويل شاملة',
                    time: 'ساعات',
                    attempts: '100,000+'
                },
                'hybrid': {
                    text: 'مزيج محسن من أفضل 3 طرق بترتيب ذكي',
                    time: 'دقائق إلى ساعات',
                    attempts: 'متغير'
                },
                'comprehensive': {
                    text: 'جميع الطرق المحسنة معاً بترتيب أمثل للفعالية',
                    time: 'ساعات إلى أيام',
                    attempts: '500,000+'
                }
            };

            const info = methodInfos[method];
            infoDiv.innerHTML = `
                <strong>الوصف:</strong> ${info.text}<br>
                <strong>الوقت المتوقع:</strong> ${info.time}<br>
                <strong>عدد المحاولات:</strong> ${info.attempts}
            `;
        }

        // توليد bcrypt hash للاختبار
        function generateTestHash(password) {
            // فحص توفر مكتبة bcrypt أولاً
            if (!bcryptReady || typeof bcrypt === 'undefined') {
                alert('خطأ: مكتبة bcrypt غير متوفرة. يرجى إعادة تحميل الصفحة أو استخدام نسخة السيرفر.');
                return;
            }

            try {
                const hash = bcrypt.hashSync(password, 10);
                document.getElementById('bcrypt_hash').value = hash;
                document.getElementById('test_password').value = password;

                // تحليل كلمة المرور تلقائياً
                analyzePassword();

                // اختبار فوري للتأكد من صحة العملية
                const testResult = safeBcryptCompare(password, hash);
                if (testResult) {
                    alert(`تم توليد hash لكلمة المرور: ${password}\nHash: ${hash}`);
                } else {
                    alert('خطأ في توليد hash - يرجى المحاولة مرة أخرى');
                }
            } catch (error) {
                console.error('Error generating hash:', error);
                alert('خطأ في توليد hash: ' + error.message);
            }
        }

        // توليد hash بنوع محدد
        function generateHashWithType(password, type) {
            // فحص توفر مكتبة bcrypt أولاً
            if (!bcryptReady || typeof bcrypt === 'undefined') {
                alert('خطأ: مكتبة bcrypt غير متوفرة. يرجى إعادة تحميل الصفحة أو استخدام نسخة السيرفر.');
                return;
            }

            try {
                let hash;
                if (bcrypt.hashWithType) {
                    hash = bcrypt.hashWithType(password, 10, type);
                } else {
                    // استخدام الطريقة العادية مع تحديد النوع
                    hash = bcrypt.hashSync(password, 10, type);
                }

                document.getElementById('bcrypt_hash').value = hash;
                document.getElementById('test_password').value = password;

                // تحليل كلمة المرور تلقائياً
                analyzePassword();

                // اختبار فوري للتأكد من صحة العملية
                const testResult = safeBcryptCompare(password, hash);
                if (testResult) {
                    alert(`تم توليد hash نوع ${type} لكلمة المرور: ${password}\nHash: ${hash}`);
                } else {
                    alert('خطأ في توليد hash - يرجى المحاولة مرة أخرى');
                }
            } catch (error) {
                console.error('Error generating hash with type:', error);
                alert('خطأ في توليد hash: ' + error.message);
            }
        }

        // بدء الاختبار
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            startTest();
        });

        function startTest() {
            // فحص توفر مكتبة bcrypt أولاً
            if (!bcryptReady || typeof bcrypt === 'undefined') {
                alert('خطأ: مكتبة bcrypt غير متوفرة. يرجى إعادة تحميل الصفحة أو استخدام نسخة السيرفر.');
                return;
            }

            const hash = document.getElementById('bcrypt_hash').value.trim();
            const method = document.getElementById('test_method').value;
            maxAttempts = parseInt(document.getElementById('max_attempts').value);

            // مسح السجل السابق
            testLog = [];

            logDebug('Starting test', {
                method: method,
                maxAttempts: maxAttempts,
                hashPrefix: hash.substring(0, 20) + '...'
            });

            if (!hash) {
                alert('يرجى إدخال bcrypt hash');
                logDebug('Test failed: No hash provided');
                return;
            }

            if (!isValidBcryptHash(hash)) {
                alert('Hash غير صحيح - يجب أن يكون bcrypt hash صالح');
                logDebug('Test failed: Invalid hash format', { hash: hash });
                return;
            }

            // اختبار سريع للتأكد من أن bcrypt يعمل
            try {
                const testPassword = 'test123';
                const testHash = bcrypt.hashSync(testPassword, 10);
                const testResult = bcrypt.compareSync(testPassword, testHash);

                if (!testResult) {
                    throw new Error('bcrypt self-test failed');
                }

                logDebug('bcrypt self-test passed');
            } catch (error) {
                alert('خطأ في مكتبة bcrypt: ' + error.message);
                logDebug('bcrypt self-test failed', { error: error.message });
                return;
            }

            testRunning = true;
            currentMethod = method;
            startTime = Date.now();
            totalAttempts = 0;

            logDebug('Test initialized successfully');

            // إظهار عناصر التقدم
            document.getElementById('progressContainer').style.display = 'block';
            document.getElementById('stopTest').style.display = 'inline-block';
            document.querySelector('button[type="submit"]').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'none';

            // بدء المؤقت
            timerInterval = setInterval(updateTimer, 1000);

            // بدء الاختبار
            runTest(hash, method, maxAttempts);
        }

        function stopTest() {
            testRunning = false;
            clearInterval(timerInterval);

            if (testWorker) {
                testWorker.terminate();
                testWorker = null;
            }

            document.getElementById('stopTest').style.display = 'none';
            document.querySelector('button[type="submit"]').style.display = 'inline-block';

            showResults({
                found: false,
                password: '',
                attempts: totalAttempts,
                time_taken: (Date.now() - startTime) / 1000,
                method: currentMethod,
                stopped: true
            });
        }

        function updateTimer() {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('elapsedTime').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // تشغيل الاختبار المحسن
        async function runTest(hash, method, maxAttempts) {
            let result = { found: false, password: '', attempts: 0 };

            // التحقق من صحة hash
            if (!isValidBcryptHash(hash)) {
                result.error = 'Hash غير صحيح - يجب أن يكون bcrypt hash صالح';
                showResults(result);
                return;
            }

            try {
                // إعداد معلومات الطريقة
                updateProgress(0, maxAttempts, `بدء اختبار: ${getMethodName(method)}`);

                switch (method) {
                    case 'common':
                        result = await testCommonPasswords(hash, maxAttempts);
                        break;
                    case 'pattern':
                        result = await testPatterns(hash, maxAttempts);
                        break;
                    case 'dictionary':
                        result = await testDictionary(hash, maxAttempts);
                        break;
                    case 'brute_simple':
                        result = await testBruteForce(hash, maxAttempts);
                        break;
                    case 'wordlist_extended':
                        result = await testExtendedWordlist(hash, maxAttempts);
                        break;
                    case 'rule_based':
                        result = await testRuleBased(hash, maxAttempts);
                        break;
                    case 'hybrid':
                        result = await testHybrid(hash, maxAttempts);
                        break;
                    case 'comprehensive':
                        result = await testComprehensive(hash, maxAttempts);
                        break;
                    default:
                        throw new Error('طريقة اختبار غير مدعومة: ' + method);
                }
            } catch (error) {
                console.error('Test error:', error);
                result.error = error.message;
                result.attempts = totalAttempts;
            }

            if (testRunning) {
                testRunning = false;
                clearInterval(timerInterval);
                result.time_taken = (Date.now() - startTime) / 1000;
                result.method = method;
                showResults(result);
            }
        }

        // التحقق من صحة bcrypt hash مع دعم جميع الأنواع
        function isValidBcryptHash(hash) {
            if (!hash || typeof hash !== 'string') return false;

            // استخدام دالة المكتبة المضمنة إذا كانت متوفرة
            if (typeof bcrypt !== 'undefined' && bcrypt.isValidHash) {
                return bcrypt.isValidHash(hash);
            }

            // فحص يدوي للأنواع المدعومة: $2a$, $2b$, $2x$, $2y$
            const bcryptRegex = /^\$2[abxy]\$[0-9]{2}\$[A-Za-z0-9./]{53}$/;
            const isValid = bcryptRegex.test(hash);

            if (isValid) {
                const type = hash.substring(0, 4);
                console.log(`✅ Hash صحيح من نوع ${type}`);
            } else {
                console.warn('❌ تنسيق Hash غير صحيح:', hash.substring(0, 20) + '...');
            }

            return isValid;
        }

        // دالة آمنة للمقارنة مع bcrypt
        function safeBcryptCompare(password, hash) {
            try {
                // التحقق من صحة المدخلات
                if (!password || !hash) {
                    logDebug('Invalid input', { password: !!password, hash: !!hash });
                    return false;
                }

                // التحقق من صحة hash
                if (!isValidBcryptHash(hash)) {
                    logDebug('Invalid bcrypt hash format', { hash: hash });
                    return false;
                }

                // المقارنة الآمنة
                const result = bcrypt.compareSync(password, hash);

                // تسجيل النتيجة الإيجابية فقط لتجنب الإفراط في التسجيل
                if (result) {
                    logDebug('Password match found!', { password: password, hash: hash.substring(0, 20) + '...' });
                }

                return result;
            } catch (error) {
                logDebug('bcrypt compare error', {
                    error: error.message,
                    password: password,
                    hash: hash.substring(0, 20) + '...'
                });
                return false;
            }
        }

        // دالة التسجيل المفصل
        function logDebug(message, data = {}) {
            const logEntry = {
                timestamp: new Date().toISOString(),
                message: message,
                data: data
            };

            testLog.push(logEntry);

            // الاحتفاظ بآخر 1000 إدخال فقط
            if (testLog.length > 1000) {
                testLog = testLog.slice(-1000);
            }

            if (debugMode) {
                console.log('[DEBUG]', message, data);
            }
        }

        // تفعيل/إلغاء وضع التصحيح
        function toggleDebugMode() {
            debugMode = !debugMode;
            console.log('Debug mode:', debugMode ? 'ON' : 'OFF');

            if (debugMode) {
                console.log('Test log entries:', testLog.length);
            }
        }

        // عرض سجل العمليات
        function showTestLog() {
            console.log('=== Test Log ===');
            testLog.forEach((entry, index) => {
                console.log(`${index + 1}. [${entry.timestamp}] ${entry.message}`, entry.data);
            });
        }

        // اختبار كلمات المرور الشائعة المحسن
        async function testCommonPasswords(hash, maxAttempts) {
            logDebug('Starting common passwords test', {
                basePasswords: commonPasswords.length,
                maxAttempts: maxAttempts
            });

            let attempts = 0;
            const allPasswords = [...commonPasswords];

            // إضافة تنويعات شائعة لكل كلمة مرور
            const variations = [];
            for (let password of commonPasswords) {
                variations.push(password);

                // تنويعات بسيطة
                if (password.length <= 10) {
                    variations.push(password + '1');
                    variations.push(password + '12');
                    variations.push(password + '123');
                    variations.push(password + '!');
                    variations.push(password + '@');
                    variations.push('1' + password);

                    // أول حرف كبير
                    if (password.length > 0) {
                        const capitalized = password.charAt(0).toUpperCase() + password.slice(1);
                        variations.push(capitalized);
                        variations.push(capitalized + '1');
                        variations.push(capitalized + '123');
                        variations.push(capitalized + '!');
                    }

                    // كل الأحرف كبيرة
                    variations.push(password.toUpperCase());
                }
            }

            // إزالة التكرارات
            const uniquePasswords = [...new Set(variations)];

            logDebug('Generated password variations', {
                originalCount: commonPasswords.length,
                variationsCount: variations.length,
                uniqueCount: uniquePasswords.length
            });

            for (let password of uniquePasswords) {
                if (!testRunning || attempts >= maxAttempts) break;

                attempts++;
                updateProgress(attempts, Math.min(maxAttempts, uniquePasswords.length), password);

                if (safeBcryptCompare(password, hash)) {
                    return { found: true, password: password, attempts: attempts };
                }

                // تأخير أقل للسرعة
                if (attempts % 50 === 0) {
                    await sleep(1);
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار الأنماط المحسن
        async function testPatterns(hash, maxAttempts) {
            let attempts = 0;
            const allPatterns = [...patterns];

            // توليد أنماط إضافية ذكية
            const generatedPatterns = [];

            // أرقام متكررة (محسنة)
            for (let digit = 0; digit <= 9; digit++) {
                for (let length = 3; length <= 10; length++) {
                    generatedPatterns.push(digit.toString().repeat(length));
                }
            }

            // تسلسلات رقمية متقدمة
            for (let start = 0; start <= 6; start++) {
                for (let length = 3; length <= 8; length++) {
                    let sequence = '';
                    for (let i = 0; i < length; i++) {
                        sequence += ((start + i) % 10).toString();
                    }
                    generatedPatterns.push(sequence);
                }
            }

            // تسلسلات عكسية
            for (let start = 9; start >= 3; start--) {
                for (let length = 3; length <= 8; length++) {
                    let sequence = '';
                    for (let i = 0; i < length; i++) {
                        sequence += ((start - i + 10) % 10).toString();
                    }
                    generatedPatterns.push(sequence);
                }
            }

            // أنماط لوحة مفاتيح متقدمة
            const keyboardRows = [
                'qwertyuiop',
                'asdfghjkl',
                'zxcvbnm',
                '1234567890'
            ];

            for (let row of keyboardRows) {
                for (let start = 0; start < row.length - 2; start++) {
                    for (let length = 3; length <= Math.min(8, row.length - start); length++) {
                        generatedPatterns.push(row.substring(start, start + length));
                        // عكسي
                        generatedPatterns.push(row.substring(start, start + length).split('').reverse().join(''));
                    }
                }
            }

            // أنماط التاريخ المتقدمة
            const currentYear = new Date().getFullYear();
            for (let year = currentYear - 50; year <= currentYear + 5; year++) {
                generatedPatterns.push(year.toString());
                generatedPatterns.push(year.toString().substring(2)); // آخر رقمين

                // أشهر
                for (let month = 1; month <= 12; month++) {
                    const monthStr = month.toString().padStart(2, '0');
                    generatedPatterns.push(monthStr + year.toString().substring(2));
                    generatedPatterns.push(year.toString().substring(2) + monthStr);

                    // أيام شائعة
                    const commonDays = ['01', '15', '31'];
                    for (let day of commonDays) {
                        generatedPatterns.push(day + monthStr + year.toString().substring(2));
                        generatedPatterns.push(year.toString().substring(2) + monthStr + day);
                    }
                }
            }

            // دمج جميع الأنماط وإزالة التكرارات
            const uniquePatterns = [...new Set([...allPatterns, ...generatedPatterns])];

            // ترتيب حسب الاحتمالية (الأقصر أولاً)
            uniquePatterns.sort((a, b) => a.length - b.length);

            for (let pattern of uniquePatterns) {
                if (!testRunning || attempts >= maxAttempts) break;

                attempts++;
                updateProgress(attempts, Math.min(maxAttempts, uniquePatterns.length), pattern);

                if (safeBcryptCompare(pattern, hash)) {
                    return { found: true, password: pattern, attempts: attempts };
                }

                // تأخير أقل للسرعة
                if (attempts % 100 === 0) {
                    await sleep(1);
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار القاموس المحسن
        async function testDictionary(hash, maxAttempts) {
            let attempts = 0;

            // تنويعات أكثر شمولية
            const suffixes = [
                '', '1', '12', '123', '1234', '12345', '123456',
                '!', '@', '#', '$', '%', '&', '*',
                '01', '02', '03', '99', '00',
                '2023', '2024', '2022', '2021', '2020',
                '!@', '!!', '@@', '##', '$$'
            ];

            const prefixes = ['', '1', '12', '123', '@', '#', '$'];

            // تحويلات الحالة
            const caseTransforms = [
                (word) => word, // كما هو
                (word) => word.charAt(0).toUpperCase() + word.slice(1), // أول حرف كبير
                (word) => word.toUpperCase(), // كل الأحرف كبيرة
                (word) => word.toLowerCase() // كل الأحرف صغيرة
            ];

            // توليد جميع التركيبات
            const allCombinations = [];

            for (let word of dictionaryWords) {
                for (let transform of caseTransforms) {
                    const transformedWord = transform(word);

                    for (let prefix of prefixes) {
                        for (let suffix of suffixes) {
                            const combination = prefix + transformedWord + suffix;
                            if (combination.length >= 3 && combination.length <= 20) {
                                allCombinations.push(combination);
                            }
                        }
                    }
                }
            }

            // إزالة التكرارات وترتيب حسب الطول
            const uniqueCombinations = [...new Set(allCombinations)];
            uniqueCombinations.sort((a, b) => a.length - b.length);

            for (let password of uniqueCombinations) {
                if (!testRunning || attempts >= maxAttempts) break;

                attempts++;
                updateProgress(attempts, Math.min(maxAttempts, uniqueCombinations.length), password);

                if (safeBcryptCompare(password, hash)) {
                    return { found: true, password: password, attempts: attempts };
                }

                // تأخير أقل للسرعة
                if (attempts % 100 === 0) {
                    await sleep(1);
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار القوة الغاشمة المحسن
        async function testBruteForce(hash, maxAttempts) {
            let attempts = 0;
            const charset = getCharset();
            const minLength = parseInt(document.getElementById('min_length').value);
            const maxLength = Math.min(parseInt(document.getElementById('max_length').value), 7); // زيادة الحد الأقصى

            // ترتيب الأحرف حسب الشيوع
            const orderedCharset = orderCharsetByFrequency(charset);

            for (let length = minLength; length <= maxLength; length++) {
                if (!testRunning || attempts >= maxAttempts) break;

                const result = await generatePasswordsOptimized(orderedCharset, length, hash, maxAttempts, attempts);
                attempts = result.attempts;

                if (result.found) {
                    return result;
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // ترتيب الأحرف حسب الشيوع
        function orderCharsetByFrequency(charset) {
            // ترتيب الأحرف حسب الشيوع في كلمات المرور
            const frequency = {
                // أرقام شائعة
                '1': 10, '2': 9, '3': 8, '0': 7, '4': 6, '5': 5,
                '6': 4, '7': 3, '8': 2, '9': 1,

                // أحرف شائعة
                'a': 10, 'e': 9, 'i': 8, 'o': 7, 'u': 6, 's': 5,
                't': 4, 'r': 3, 'n': 2, 'l': 1,

                // أحرف كبيرة شائعة
                'A': 5, 'S': 4, 'P': 3, 'M': 2, 'T': 1,

                // رموز شائعة
                '!': 10, '@': 9, '#': 8, '$': 7, '%': 6, '&': 5,
                '*': 4, '(': 3, ')': 2, '_': 1
            };

            return charset.split('').sort((a, b) => {
                const freqA = frequency[a] || 0;
                const freqB = frequency[b] || 0;
                return freqB - freqA; // ترتيب تنازلي
            }).join('');
        }

        // توليد كلمات المرور المحسن
        async function generatePasswordsOptimized(charset, length, hash, maxAttempts, startAttempts) {
            let attempts = startAttempts;
            const total = Math.pow(charset.length, length);

            // استراتيجية ذكية: البدء بالأنماط الأكثر شيوعاً
            const commonStarts = ['1', 'a', 'p', 's', 't', 'A', 'P', 'S'];
            const commonEnds = ['1', '2', '3', '!', '@', '#', 'e', 's', 't'];

            // اختبار الأنماط الشائعة أولاً
            for (let start of commonStarts) {
                if (!testRunning || attempts >= maxAttempts) break;
                if (!charset.includes(start)) continue;

                for (let end of commonEnds) {
                    if (!testRunning || attempts >= maxAttempts) break;
                    if (!charset.includes(end) || length < 2) continue;

                    // توليد كلمة مرور بنمط شائع
                    let password = start;

                    // ملء الوسط
                    for (let i = 1; i < length - 1; i++) {
                        password += charset[i % charset.length];
                    }

                    if (length > 1) {
                        password += end;
                    }

                    attempts++;
                    updateProgress(attempts, maxAttempts, password);

                    if (safeBcryptCompare(password, hash)) {
                        return { found: true, password: password, attempts: attempts };
                    }
                }
            }

            // الطريقة التقليدية مع تحسينات
            const batchSize = 1000; // معالجة دفعية
            let batchCount = 0;

            for (let i = 0; i < total && testRunning && attempts < maxAttempts; i++) {
                let password = '';
                let temp = i;

                for (let j = 0; j < length; j++) {
                    password = charset[temp % charset.length] + password;
                    temp = Math.floor(temp / charset.length);
                }

                attempts++;
                batchCount++;

                // تحديث التقدم كل دفعة
                if (batchCount >= batchSize) {
                    updateProgress(attempts, maxAttempts, password);
                    batchCount = 0;
                    await sleep(1); // تأخير قصير
                }

                if (safeBcryptCompare(password, hash)) {
                    return { found: true, password: password, attempts: attempts };
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // الحصول على مجموعة الأحرف
        function getCharset() {
            const type = document.getElementById('charset_type').value;

            switch (type) {
                case 'basic':
                    return 'abcdefghijklmnopqrstuvwxyz0123456789';
                case 'extended':
                    return 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                case 'special':
                    return 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?';
                case 'unicode':
                    return 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?أبتثجحخدذرزسشصضطظعغفقكلمنهوي';
                default:
                    return 'abcdefghijklmnopqrstuvwxyz0123456789';
            }
        }

        // اختبار قاموس موسع محسن
        async function testExtendedWordlist(hash, maxAttempts) {
            let attempts = 0;

            // قاموس موسع شامل
            const extendedWords = [
                ...commonPasswords, ...dictionaryWords,

                // أسماء شائعة عربية وإنجليزية
                'ahmed', 'mohamed', 'ali', 'omar', 'sara', 'fatima',
                'aisha', 'maryam', 'hassan', 'hussein', 'nour', 'layla',
                'john', 'mike', 'david', 'sarah', 'mary', 'lisa',
                'anna', 'emma', 'james', 'william', 'robert', 'michael',

                // كلمات تقنية
                'server', 'client', 'mysql', 'php', 'html', 'css',
                'javascript', 'python', 'java', 'linux', 'windows',
                'apache', 'nginx', 'docker', 'git', 'github',

                // كلمات شائعة إضافية
                'house', 'school', 'work', 'office', 'home', 'family',
                'friend', 'love', 'life', 'time', 'money', 'business',
                'company', 'team', 'project', 'system', 'network',

                // كلمات عربية
                'مرحبا', 'أهلا', 'سلام', 'بيت', 'مدرسة', 'عمل',
                'مكتب', 'عائلة', 'صديق', 'حب', 'حياة', 'وقت'
            ];

            // تنويعات أكثر ذكاءً
            const smartVariations = [];

            // أرقام شائعة
            const commonNumbers = [
                '1', '12', '123', '1234', '12345',
                '2023', '2024', '2022', '2021', '2020',
                '01', '02', '03', '10', '11', '99', '00'
            ];

            // رموز شائعة
            const commonSymbols = ['!', '@', '#', '$', '!@', '!!', '@@'];

            // توليد تركيبات ذكية
            const allCombinations = [];

            for (let word of extendedWords) {
                if (word.length > 15) continue; // تجنب الكلمات الطويلة جداً

                // الكلمة كما هي
                allCombinations.push(word);

                // تحويلات الحالة
                const variations = [
                    word.toLowerCase(),
                    word.toUpperCase(),
                    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
                ];

                for (let variant of variations) {
                    // مع أرقام
                    for (let num of commonNumbers) {
                        allCombinations.push(variant + num);
                        allCombinations.push(num + variant);

                        // مع رموز
                        for (let symbol of commonSymbols) {
                            allCombinations.push(variant + num + symbol);
                            allCombinations.push(variant + symbol + num);
                            allCombinations.push(symbol + variant + num);
                        }
                    }

                    // مع رموز فقط
                    for (let symbol of commonSymbols) {
                        allCombinations.push(variant + symbol);
                        allCombinations.push(symbol + variant);
                    }
                }
            }

            // إزالة التكرارات وترتيب حسب الطول والشيوع
            const uniqueCombinations = [...new Set(allCombinations)];
            uniqueCombinations.sort((a, b) => {
                // ترتيب حسب الطول أولاً، ثم أبجدياً
                if (a.length !== b.length) {
                    return a.length - b.length;
                }
                return a.localeCompare(b);
            });

            // تحديد الحد الأقصى للمعالجة
            const maxCombinations = Math.min(uniqueCombinations.length, maxAttempts);

            for (let i = 0; i < maxCombinations; i++) {
                if (!testRunning || attempts >= maxAttempts) break;

                const password = uniqueCombinations[i];
                attempts++;
                updateProgress(attempts, maxCombinations, password);

                if (safeBcryptCompare(password, hash)) {
                    return { found: true, password: password, attempts: attempts };
                }

                // تأخير أقل للسرعة
                if (attempts % 200 === 0) {
                    await sleep(1);
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار قائم على القواعد المحسن
        async function testRuleBased(hash, maxAttempts) {
            let attempts = 0;

            // كلمات أساسية موسعة
            const baseWords = [
                'password', 'admin', 'user', 'test', 'login', 'welcome',
                'guest', 'root', 'demo', 'temp', 'system', 'server',
                'client', 'database', 'mysql', 'oracle', 'windows',
                'linux', 'ubuntu', 'centos', 'apache', 'nginx',
                'home', 'work', 'office', 'school', 'house', 'family'
            ];

            // قواعد استبدال متقدمة
            const substitutionRules = [
                { 'a': '@', 'e': '3', 'i': '1', 'o': '0', 's': '$' },
                { 'a': '4', 'e': '3', 'i': '!', 'o': '0', 's': '5' },
                { 'a': '@', 'e': '€', 'i': '!', 'o': '°', 's': '$' },
                { 'l': '1', 't': '7', 'g': '9', 'b': '6', 'z': '2' },
                { 'A': '@', 'E': '3', 'I': '1', 'O': '0', 'S': '$' }
            ];

            // قواعد التحويل
            const transformRules = [
                (word) => word, // كما هو
                (word) => word.charAt(0).toUpperCase() + word.slice(1), // أول حرف كبير
                (word) => word.toUpperCase(), // كل الأحرف كبيرة
                (word) => word.toLowerCase(), // كل الأحرف صغيرة
                (word) => word.split('').reverse().join(''), // عكسي
                (word) => word.charAt(0).toUpperCase() + word.slice(1).split('').reverse().join('') // أول حرف كبير + عكسي
            ];

            // أرقام وسنوات شائعة
            const commonAppends = [
                '1', '12', '123', '1234', '12345',
                '2024', '2023', '2022', '2021', '2020', '2019',
                '01', '02', '03', '10', '11', '99', '00',
                '!', '@', '#', '$', '!@', '!!', '@@', '##'
            ];

            // توليد جميع التركيبات
            const allCombinations = [];

            for (let word of baseWords) {
                // تطبيق قواعد التحويل
                for (let transform of transformRules) {
                    const transformedWord = transform(word);

                    // الكلمة المحولة كما هي
                    allCombinations.push(transformedWord);

                    // تطبيق قواعد الاستبدال
                    for (let rule of substitutionRules) {
                        let substitutedWord = transformedWord;
                        for (let [from, to] of Object.entries(rule)) {
                            substitutedWord = substitutedWord.replace(new RegExp(from, 'g'), to);
                        }

                        allCombinations.push(substitutedWord);

                        // إضافة أرقام ورموز
                        for (let append of commonAppends) {
                            allCombinations.push(substitutedWord + append);
                            allCombinations.push(append + substitutedWord);

                            // تركيبات مزدوجة
                            if (append.length <= 4) {
                                allCombinations.push(substitutedWord + append + '!');
                                allCombinations.push(substitutedWord + append + '@');
                                allCombinations.push('!' + substitutedWord + append);
                                allCombinations.push('@' + substitutedWord + append);
                            }
                        }
                    }

                    // الكلمة المحولة مع إضافات مباشرة
                    for (let append of commonAppends) {
                        allCombinations.push(transformedWord + append);
                        allCombinations.push(append + transformedWord);
                    }
                }
            }

            // إزالة التكرارات وترتيب
            const uniqueCombinations = [...new Set(allCombinations)];
            uniqueCombinations.sort((a, b) => a.length - b.length);

            // تحديد الحد الأقصى
            const maxCombinations = Math.min(uniqueCombinations.length, maxAttempts);

            for (let i = 0; i < maxCombinations; i++) {
                if (!testRunning || attempts >= maxAttempts) break;

                const password = uniqueCombinations[i];
                attempts++;
                updateProgress(attempts, maxCombinations, password);

                try {
                    if (bcrypt.compareSync(password, hash)) {
                        return { found: true, password: password, attempts: attempts };
                    }
                } catch (error) {
                    console.error('bcrypt error:', error);
                    continue;
                }

                // تأخير أقل للسرعة
                if (attempts % 150 === 0) {
                    await sleep(1);
                }
            }

            return { found: false, password: '', attempts: attempts };
        }

        // اختبار هجين
        async function testHybrid(hash, maxAttempts) {
            const methods = ['common', 'pattern', 'dictionary'];
            let totalAttempts = 0;

            for (let method of methods) {
                if (!testRunning || totalAttempts >= maxAttempts) break;

                const methodAttempts = Math.floor(maxAttempts / methods.length);
                let result;

                switch (method) {
                    case 'common':
                        result = await testCommonPasswords(hash, methodAttempts);
                        break;
                    case 'pattern':
                        result = await testPatterns(hash, methodAttempts);
                        break;
                    case 'dictionary':
                        result = await testDictionary(hash, methodAttempts);
                        break;
                }

                totalAttempts += result.attempts;

                if (result.found) {
                    result.attempts = totalAttempts;
                    return result;
                }
            }

            return { found: false, password: '', attempts: totalAttempts };
        }

        // اختبار شامل
        async function testComprehensive(hash, maxAttempts) {
            const methods = ['common', 'pattern', 'dictionary', 'wordlist_extended', 'rule_based'];
            let totalAttempts = 0;

            for (let method of methods) {
                if (!testRunning || totalAttempts >= maxAttempts) break;

                const methodAttempts = Math.floor(maxAttempts / methods.length);
                let result;

                switch (method) {
                    case 'common':
                        result = await testCommonPasswords(hash, methodAttempts);
                        break;
                    case 'pattern':
                        result = await testPatterns(hash, methodAttempts);
                        break;
                    case 'dictionary':
                        result = await testDictionary(hash, methodAttempts);
                        break;
                    case 'wordlist_extended':
                        result = await testExtendedWordlist(hash, methodAttempts);
                        break;
                    case 'rule_based':
                        result = await testRuleBased(hash, methodAttempts);
                        break;
                }

                totalAttempts += result.attempts;

                if (result.found) {
                    result.attempts = totalAttempts;
                    return result;
                }
            }

            return { found: false, password: '', attempts: totalAttempts };
        }

        // تحديث التقدم
        function updateProgress(attempts, maxAttempts, currentPassword = '') {
            totalAttempts = attempts;
            const percentage = Math.min((attempts / maxAttempts) * 100, 100);

            // تحديث شريط التقدم مع ألوان مختلفة
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';

            // تغيير لون الشريط حسب التقدم
            if (percentage < 25) {
                progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
            } else if (percentage < 50) {
                progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-warning';
            } else if (percentage < 75) {
                progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-primary';
            } else {
                progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-danger';
            }

            document.getElementById('progressText').textContent = percentage.toFixed(1) + '%';
            document.getElementById('totalAttempts').textContent = attempts.toLocaleString();
            document.getElementById('currentAttempt').textContent =
                `المحاولة ${attempts.toLocaleString()} من ${maxAttempts.toLocaleString()}`;

            if (currentPassword) {
                // إخفاء كلمة المرور إذا كانت طويلة
                const displayPassword = currentPassword.length > 20 ?
                    currentPassword.substring(0, 20) + '...' : currentPassword;
                document.getElementById('currentPassword').textContent =
                    `كلمة المرور الحالية: ${displayPassword}`;
            }

            // حساب السرعة والوقت المتبقي
            const elapsed = (Date.now() - startTime) / 1000;
            if (elapsed > 0) {
                const speed = Math.round(attempts / elapsed);
                document.getElementById('attemptsPerSecond').textContent = speed.toLocaleString();

                // تقدير الوقت المتبقي
                if (speed > 0 && maxAttempts > attempts) {
                    const remainingAttempts = maxAttempts - attempts;
                    const estimatedSeconds = remainingAttempts / speed;
                    const estimatedTime = formatEstimatedTime(estimatedSeconds);

                    // إضافة عنصر الوقت المتبقي إذا لم يكن موجود
                    let etaElement = document.getElementById('estimatedTime');
                    if (!etaElement) {
                        const statsContainer = document.querySelector('.row');
                        const newCol = document.createElement('div');
                        newCol.className = 'col-md-3';
                        newCol.innerHTML = `
                            <div class="stats-card">
                                <div class="text-center">
                                    <h5 id="estimatedTime">--</h5>
                                    <small>الوقت المتبقي</small>
                                </div>
                            </div>
                        `;
                        statsContainer.appendChild(newCol);
                        etaElement = document.getElementById('estimatedTime');
                    }

                    if (etaElement) {
                        etaElement.textContent = estimatedTime;
                    }
                }
            }
        }

        // تنسيق الوقت المقدر
        function formatEstimatedTime(seconds) {
            if (seconds < 60) {
                return Math.round(seconds) + 's';
            } else if (seconds < 3600) {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = Math.round(seconds % 60);
                return `${minutes}m ${remainingSeconds}s`;
            } else if (seconds < 86400) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                return `${hours}h ${minutes}m`;
            } else {
                const days = Math.floor(seconds / 86400);
                const hours = Math.floor((seconds % 86400) / 3600);
                return `${days}d ${hours}h`;
            }
        }

        // دالة النوم
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // عرض النتائج
        function showResults(data) {
            const container = document.getElementById('resultsContainer');
            const header = document.getElementById('resultsHeader');
            const body = document.getElementById('resultsBody');

            container.style.display = 'block';
            document.getElementById('stopTest').style.display = 'none';
            document.querySelector('button[type="submit"]').style.display = 'inline-block';

            let resultHtml = '';

            if (data.found) {
                header.innerHTML = '<h4><i class="fas fa-check-circle text-success"></i> تم العثور على كلمة المرور!</h4>';
                header.className = 'card-header bg-success text-white';

                // تحليل قوة كلمة المرور المكتشفة
                const passwordAnalysis = analyzePasswordStrength(data.password);
                const strengthClass = getWeaknessLevelClass(passwordAnalysis.level);
                const strengthText = getWeaknessLevelText(passwordAnalysis.level);

                resultHtml = `
                    <div class="alert alert-success">
                        <h5><i class="fas fa-key"></i> كلمة المرور المكتشفة:</h5>
                        <div class="bg-white text-dark p-3 rounded" style="font-family: monospace; font-size: 1.2em; font-weight: bold;">
                            ${data.password}
                        </div>
                        <div class="mt-2">
                            <span class="badge ${strengthClass} fs-6">قوة كلمة المرور: ${strengthText}</span>
                            <span class="badge bg-info ms-2">الطول: ${data.password.length} حرف</span>
                            <span class="badge bg-secondary ms-2">الإنتروبيا: ${passwordAnalysis.entropy.toFixed(1)} بت</span>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6>عدد المحاولات</h6>
                                <h4>${data.attempts.toLocaleString()}</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6>الوقت المستغرق</h6>
                                <h4>${data.time_taken.toFixed(2)}s</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6>طريقة الاختبار</h6>
                                <h4>${getMethodName(data.method)}</h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stats-card">
                                <h6>السرعة</h6>
                                <h4>${Math.round(data.attempts / data.time_taken).toLocaleString()}/s</h4>
                            </div>
                        </div>
                    </div>

                    ${passwordAnalysis.patterns.length > 0 ? `
                    <div class="alert alert-warning mt-3">
                        <h6><i class="fas fa-exclamation-triangle"></i> أنماط ضعيفة مكتشفة:</h6>
                        <div>
                            ${passwordAnalysis.patterns.map(pattern =>
                                `<span class="badge bg-warning text-dark me-1">${getPatternText(pattern)}</span>`
                            ).join('')}
                        </div>
                    </div>
                    ` : ''}

                    <div class="alert alert-${passwordAnalysis.level === 'very_weak' || passwordAnalysis.level === 'weak' ? 'danger' : 'warning'} mt-3">
                        <h6><i class="fas fa-shield-alt"></i> توصيات الأمان:</h6>
                        ${getSecurityRecommendations(passwordAnalysis)}
                    </div>
                `;
            } else {
                header.innerHTML = '<h4><i class="fas fa-times-circle text-danger"></i> لم يتم العثور على كلمة المرور</h4>';
                header.className = 'card-header bg-danger text-white';

                const statusText = data.stopped ? 'تم إيقاف الاختبار' : 'اكتمل الاختبار';

                resultHtml = `
                    <div class="alert alert-info">
                        <h5><i class="fas fa-info-circle"></i> ${statusText}</h5>
                        <p>لم يتم العثور على كلمة المرور بعد ${data.attempts.toLocaleString()} محاولة.</p>
                        <p><strong>الوقت المستغرق:</strong> ${data.time_taken.toFixed(2)}s</p>
                        ${data.error ? `<p><strong>خطأ:</strong> ${data.error}</p>` : ''}
                        <hr>
                        <p class="mb-0">كلمة المرور تبدو قوية ومقاومة للهجمات الأساسية.</p>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="stats-card">
                                <h6>عدد المحاولات</h6>
                                <h4>${data.attempts.toLocaleString()}</h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-card">
                                <h6>الوقت المستغرق</h6>
                                <h4>${data.time_taken.toFixed(2)}s</h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="stats-card">
                                <h6>السرعة المتوسطة</h6>
                                <h4>${Math.round(data.attempts / data.time_taken).toLocaleString()}/s</h4>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات التصحيح -->
                    <div class="mt-3">
                        <button class="btn btn-outline-secondary btn-sm" onclick="toggleDebugMode()">
                            <i class="fas fa-bug"></i> ${debugMode ? 'إخفاء' : 'إظهار'} معلومات التصحيح
                        </button>
                        <button class="btn btn-outline-info btn-sm ms-2" onclick="showTestLog()">
                            <i class="fas fa-list"></i> عرض سجل العمليات
                        </button>
                        <button class="btn btn-outline-success btn-sm ms-2" onclick="downloadTestLog()">
                            <i class="fas fa-download"></i> تحميل السجل
                        </button>
                    </div>
                `;
            }

            body.innerHTML = resultHtml;
        }

        // الحصول على اسم الطريقة
        function getMethodName(method) {
            const methods = {
                'common': 'كلمات شائعة',
                'pattern': 'أنماط شائعة',
                'dictionary': 'قاموس',
                'brute_simple': 'قوة غاشمة بسيطة',
                'wordlist_extended': 'قاموس موسع',
                'rule_based': 'قائم على قواعد',
                'hybrid': 'هجين',
                'comprehensive': 'شامل'
            };
            return methods[method] || method;
        }

        // تحليل كلمة المرور
        function analyzePassword() {
            const password = document.getElementById('test_password').value.trim();

            if (!password) {
                alert('يرجى إدخال كلمة مرور للتحليل');
                return;
            }

            const analysis = analyzePasswordStrength(password);
            displayAnalysisResults(analysis);
        }

        // تحليل قوة كلمة المرور
        function analyzePasswordStrength(password) {
            const length = password.length;
            const hasLower = /[a-z]/.test(password);
            const hasUpper = /[A-Z]/.test(password);
            const hasDigits = /[0-9]/.test(password);
            const hasSpecial = /[^a-zA-Z0-9]/.test(password);
            const hasArabic = /[\u0600-\u06FF]/.test(password);

            // حساب حجم مجموعة الأحرف
            let charsetSize = 0;
            if (hasLower) charsetSize += 26;
            if (hasUpper) charsetSize += 26;
            if (hasDigits) charsetSize += 10;
            if (hasSpecial) charsetSize += 32;
            if (hasArabic) charsetSize += 28;

            // حساب الإنتروبيا
            const entropy = charsetSize > 0 ? length * Math.log2(charsetSize) : 0;

            // كشف الأنماط
            const patterns = [];
            if (commonPasswords.includes(password.toLowerCase())) patterns.push('common_password');
            if (/^(.)\1+$/.test(password)) patterns.push('repeating');
            if (/^(012|123|234|345|456|567|678|789|890|987|876|765|654|543|432|321|210)/.test(password)) patterns.push('sequential');
            if (/^(qwerty|asdf|zxcv|qaz|wsx|edc)/.test(password.toLowerCase())) patterns.push('keyboard_pattern');
            if (/^\d{4}$|^\d{2}\/\d{2}\/\d{4}$|^\d{8}$/.test(password)) patterns.push('date_pattern');
            if (/^\d+$/.test(password)) patterns.push('numbers_only');
            if (/^[a-zA-Z]+$/.test(password)) patterns.push('letters_only');

            // تحديد مستوى القوة
            let score = 0;
            if (patterns.includes('common_password')) score -= 50;
            if (patterns.includes('repeating')) score -= 40;
            if (patterns.includes('sequential')) score -= 30;
            if (patterns.includes('keyboard_pattern')) score -= 25;
            if (patterns.includes('date_pattern')) score -= 20;
            if (patterns.includes('numbers_only')) score -= 20;
            if (patterns.includes('letters_only')) score -= 15;

            if (length >= 8) score += 10;
            if (length >= 12) score += 20;
            if (hasLower) score += 5;
            if (hasUpper) score += 10;
            if (hasDigits) score += 10;
            if (hasSpecial) score += 15;
            if (entropy > 40) score += 20;
            if (entropy > 60) score += 30;

            let level;
            if (score < -30) level = 'very_weak';
            else if (score < -10) level = 'weak';
            else if (score < 20) level = 'medium';
            else if (score < 40) level = 'strong';
            else level = 'very_strong';

            // توصيات الاختبار
            const recommendations = getTestRecommendations(level, patterns, length);

            return {
                password: password,
                length: length,
                charsetSize: charsetSize,
                entropy: entropy,
                patterns: patterns,
                level: level,
                score: score,
                characteristics: {
                    hasLower, hasUpper, hasDigits, hasSpecial, hasArabic
                },
                recommendations: recommendations
            };
        }

        // الحصول على توصيات الاختبار
        function getTestRecommendations(level, patterns, length) {
            const recommendations = [];

            if (patterns.includes('common_password')) {
                recommendations.push({ method: 'common', priority: 1, reason: 'كلمة مرور شائعة' });
            }

            if (patterns.includes('sequential') || patterns.includes('keyboard_pattern') || patterns.includes('date_pattern')) {
                recommendations.push({ method: 'pattern', priority: 2, reason: 'تحتوي على أنماط شائعة' });
            }

            if (patterns.includes('numbers_only')) {
                recommendations.push({ method: 'brute_simple', priority: 1, reason: 'أرقام فقط - قوة غاشمة بسيطة' });
            }

            switch (level) {
                case 'very_weak':
                    recommendations.push({ method: 'common', priority: 1, reason: 'ضعيفة جداً - ابدأ بالشائعة' });
                    recommendations.push({ method: 'pattern', priority: 2, reason: 'فحص الأنماط البسيطة' });
                    break;
                case 'weak':
                    recommendations.push({ method: 'hybrid', priority: 1, reason: 'ضعيفة - اختبار هجين' });
                    recommendations.push({ method: 'rule_based', priority: 2, reason: 'قواعد التحويل' });
                    break;
                case 'medium':
                    recommendations.push({ method: 'wordlist_extended', priority: 1, reason: 'متوسطة - قاموس موسع' });
                    recommendations.push({ method: 'rule_based', priority: 2, reason: 'قواعد متقدمة' });
                    break;
                case 'strong':
                    recommendations.push({ method: 'comprehensive', priority: 1, reason: 'قوية - اختبار شامل' });
                    break;
                case 'very_strong':
                    recommendations.push({ method: 'comprehensive', priority: 1, reason: 'قوية جداً - اختبار شامل طويل المدى' });
                    break;
            }

            // إزالة التكرارات وترتيب حسب الأولوية
            const uniqueRecommendations = [];
            const seen = new Set();

            for (let rec of recommendations) {
                if (!seen.has(rec.method)) {
                    seen.add(rec.method);
                    uniqueRecommendations.push(rec);
                }
            }

            return uniqueRecommendations.sort((a, b) => a.priority - b.priority);
        }

        // عرض نتائج التحليل
        function displayAnalysisResults(analysis) {
            const container = document.getElementById('analysisResults');
            container.style.display = 'block';

            const levelClass = getWeaknessLevelClass(analysis.level);
            const levelText = getWeaknessLevelText(analysis.level);

            let html = `
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-info-circle"></i> معلومات أساسية:</h6>
                        <ul class="list-unstyled">
                            <li><strong>الطول:</strong> ${analysis.length} حرف</li>
                            <li><strong>حجم مجموعة الأحرف:</strong> ${analysis.charsetSize}</li>
                            <li><strong>الإنتروبيا:</strong> ${analysis.entropy.toFixed(2)} بت</li>
                            <li><strong>نقاط الأمان:</strong> ${analysis.score}</li>
                        </ul>

                        <h6><i class="fas fa-shield-alt"></i> مستوى القوة:</h6>
                        <span class="badge ${levelClass} fs-6">${levelText}</span>
                    </div>

                    <div class="col-md-6">
                        <h6><i class="fas fa-search"></i> الأنماط المكتشفة:</h6>
                        <div class="mb-3">
                            ${analysis.patterns.length > 0 ?
                                analysis.patterns.map(pattern =>
                                    `<span class="badge bg-warning text-dark me-1">${getPatternText(pattern)}</span>`
                                ).join('') :
                                '<span class="badge bg-success">لا توجد أنماط ضعيفة</span>'
                            }
                        </div>

                        <h6><i class="fas fa-check-circle"></i> خصائص الأحرف:</h6>
                        <div>
                            ${analysis.characteristics.hasLower ? '<span class="badge bg-success me-1">أحرف صغيرة</span>' : '<span class="badge bg-secondary me-1">لا توجد أحرف صغيرة</span>'}
                            ${analysis.characteristics.hasUpper ? '<span class="badge bg-success me-1">أحرف كبيرة</span>' : '<span class="badge bg-secondary me-1">لا توجد أحرف كبيرة</span>'}
                            ${analysis.characteristics.hasDigits ? '<span class="badge bg-success me-1">أرقام</span>' : '<span class="badge bg-secondary me-1">لا توجد أرقام</span>'}
                            ${analysis.characteristics.hasSpecial ? '<span class="badge bg-success me-1">رموز خاصة</span>' : '<span class="badge bg-secondary me-1">لا توجد رموز</span>'}
                            ${analysis.characteristics.hasArabic ? '<span class="badge bg-info me-1">أحرف عربية</span>' : ''}
                        </div>
                    </div>
                </div>

                <hr>

                <div class="row">
                    <div class="col-md-12">
                        <h6><i class="fas fa-bullseye"></i> الاختبارات المقترحة:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>الأولوية</th>
                                        <th>نوع الاختبار</th>
                                        <th>السبب</th>
                                        <th>إجراء</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${analysis.recommendations.map((test, index) => `
                                        <tr>
                                            <td><span class="badge bg-primary">${test.priority}</span></td>
                                            <td>${getMethodName(test.method)}</td>
                                            <td>${test.reason}</td>
                                            <td>
                                                <button class="btn btn-sm btn-success"
                                                        onclick="applyRecommendation('${test.method}', ${analysis.length})">
                                                    <i class="fas fa-play"></i> تطبيق
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            container.innerHTML = html;
        }

        // تطبيق التوصية
        function applyRecommendation(method, passwordLength) {
            document.getElementById('test_method').value = method;

            // تطبيق الإعدادات المناسبة
            if (passwordLength <= 4) {
                document.getElementById('max_length').value = 4;
                document.getElementById('max_attempts').value = 100000;
            } else if (passwordLength <= 6) {
                document.getElementById('max_length').value = 6;
                document.getElementById('max_attempts').value = 1000000;
            } else if (passwordLength <= 8) {
                document.getElementById('max_length').value = 8;
                document.getElementById('max_attempts').value = 10000000;
            } else {
                document.getElementById('max_length').value = 10;
                document.getElementById('max_attempts').value = 10000000;
            }

            updateMethodInfo();
            alert('تم تطبيق الإعدادات المقترحة. يمكنك الآن بدء الاختبار.');
        }

        // دوال مساعدة للعرض
        function getWeaknessLevelClass(level) {
            const classes = {
                'very_weak': 'bg-danger',
                'weak': 'bg-warning',
                'medium': 'bg-info',
                'strong': 'bg-success',
                'very_strong': 'bg-primary'
            };
            return classes[level] || 'bg-secondary';
        }

        function getWeaknessLevelText(level) {
            const texts = {
                'very_weak': 'ضعيفة جداً',
                'weak': 'ضعيفة',
                'medium': 'متوسطة',
                'strong': 'قوية',
                'very_strong': 'قوية جداً'
            };
            return texts[level] || 'غير محدد';
        }

        function getPatternText(pattern) {
            const texts = {
                'common_password': 'كلمة مرور شائعة',
                'sequential': 'تسلسل',
                'repeating': 'تكرار',
                'keyboard_pattern': 'نمط لوحة مفاتيح',
                'date_pattern': 'نمط تاريخ',
                'numbers_only': 'أرقام فقط',
                'letters_only': 'أحرف فقط'
            };
            return texts[pattern] || pattern;
        }

        // الحصول على التوصيات الأمنية
        function getSecurityRecommendations(analysis) {
            let recommendations = [];

            if (analysis.level === 'very_weak') {
                recommendations.push('كلمة المرور ضعيفة جداً ويمكن كسرها في ثوانٍ');
                recommendations.push('استخدم كلمة مرور أطول (12+ حرف)');
                recommendations.push('امزج أحرف كبيرة وصغيرة وأرقام ورموز');
                recommendations.push('تجنب الكلمات الشائعة والأنماط المتوقعة');
            } else if (analysis.level === 'weak') {
                recommendations.push('كلمة المرور ضعيفة ويمكن كسرها في دقائق');
                recommendations.push('أضف المزيد من الأحرف والرموز');
                recommendations.push('تجنب الاستبدالات البسيطة (مثل @ بدلاً من a)');
                recommendations.push('استخدم عبارة مرور طويلة بدلاً من كلمة واحدة');
            } else if (analysis.level === 'medium') {
                recommendations.push('كلمة المرور متوسطة القوة');
                recommendations.push('يمكن تحسينها بإضافة المزيد من الرموز الخاصة');
                recommendations.push('تأكد من عدم استخدامها في أماكن متعددة');
            } else if (analysis.level === 'strong') {
                recommendations.push('كلمة المرور قوية ومقاومة للهجمات الأساسية');
                recommendations.push('تأكد من تغييرها بانتظام');
            } else {
                recommendations.push('كلمة المرور قوية جداً ومقاومة للهجمات المتقدمة');
                recommendations.push('ممتازة للاستخدام في التطبيقات الحساسة');
            }

            return '<ul><li>' + recommendations.join('</li><li>') + '</li></ul>';
        }

        // تحميل سجل العمليات
        function downloadTestLog() {
            const logData = {
                timestamp: new Date().toISOString(),
                testInfo: {
                    method: currentMethod,
                    maxAttempts: maxAttempts,
                    totalAttempts: totalAttempts,
                    testDuration: testRunning ? 0 : (Date.now() - startTime) / 1000
                },
                log: testLog
            };

            const logText = JSON.stringify(logData, null, 2);
            const blob = new Blob([logText], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `bcrypt_test_log_${new Date().toISOString().slice(0,19).replace(/:/g,'-')}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        // اختبار سريع للتحقق من صحة العمل
        async function runQuickTest() {
            // فحص توفر مكتبة bcrypt أولاً
            if (!bcryptReady || typeof bcrypt === 'undefined') {
                alert('خطأ: مكتبة bcrypt غير متوفرة. يرجى إعادة تحميل الصفحة أو استخدام نسخة السيرفر.');
                return [];
            }

            const testPasswords = ['123456', 'password', 'admin', 'test123'];
            const results = [];

            console.log('=== بدء الاختبار السريع ===');
            console.log('🔍 اختبار دعم أنواع bcrypt المختلفة...');

            // اختبار دعم الأنواع المختلفة
            if (bcrypt.testAllTypes) {
                const typeTest = bcrypt.testAllTypes('test');
                console.log('📊 نتائج اختبار الأنواع:', typeTest);

                for (const [type, result] of Object.entries(typeTest)) {
                    if (result.success) {
                        console.log(`✅ ${type} يعمل بشكل صحيح`);
                    } else {
                        console.warn(`❌ ${type} لا يعمل:`, result.error || 'خطأ غير معروف');
                    }
                }
            }

            for (let password of testPasswords) {
                try {
                    // توليد hash
                    const hash = bcrypt.hashSync(password, 10);
                    console.log(`Hash for "${password}": ${hash}`);

                    // اختبار المقارنة المباشرة
                    const directTest = bcrypt.compareSync(password, hash);
                    const safeTest = safeBcryptCompare(password, hash);

                    // اختبار كلمة مرور خاطئة
                    const wrongTest = safeBcryptCompare(password + 'wrong', hash);

                    const result = {
                        password: password,
                        hash: hash,
                        directTest: directTest,
                        safeTest: safeTest,
                        wrongTest: wrongTest,
                        success: directTest && safeTest && !wrongTest
                    };

                    results.push(result);
                    console.log(`Test for "${password}":`, result);

                } catch (error) {
                    console.error(`Error testing "${password}":`, error);
                    results.push({
                        password: password,
                        error: error.message,
                        success: false
                    });
                }
            }

            // عرض النتائج
            const allSuccess = results.every(r => r.success);
            const message = allSuccess ?
                '✅ جميع الاختبارات نجحت! البرنامج يعمل بشكل صحيح.' :
                '❌ بعض الاختبارات فشلت. يرجى فحص وحدة التحكم للتفاصيل.';

            alert(message);
            console.log('=== نتائج الاختبار السريع ===');
            console.log('النجاح الإجمالي:', allSuccess);
            console.log('النتائج التفصيلية:', results);

            return results;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 تم تحميل الصفحة، فحص مكتبة bcrypt المضمنة...');

            // فحص فوري للمكتبة المضمنة
            if (window.bcryptReady && typeof bcrypt !== 'undefined') {
                console.log('✅ مكتبة bcrypt المضمنة جاهزة فوراً');

                bcryptReady = true;
                showBcryptSuccess();
                updateMethodInfo();

                // إضافة مستمع لتحليل كلمة المرور عند الكتابة
                document.getElementById('test_password').addEventListener('input', function() {
                    const password = this.value.trim();
                    if (password.length > 0) {
                        setTimeout(() => {
                            if (this.value.trim() === password) {
                                analyzePassword();
                            }
                        }, 500);
                    }
                });
            } else {
                // في حالة عدم توفر المكتبة المضمنة (لا يجب أن يحدث)
                console.error('❌ مكتبة bcrypt المضمنة غير متوفرة');
                bcryptReady = false;
                showBcryptError();
            }
        });

        // عرض خطأ مكتبة bcrypt
        function showBcryptError() {
            const statusDiv = document.getElementById('bcryptStatus');
            if (statusDiv) {
                statusDiv.className = 'alert alert-danger';
                statusDiv.innerHTML = `
                    <h5><i class="fas fa-exclamation-triangle"></i> فشل في تحميل مكتبة bcrypt</h5>
                    <p>تم محاولة تحميل المكتبة من عدة مصادر ولكن فشلت جميع المحاولات.</p>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6><i class="fas fa-tools"></i> الحلول المقترحة:</h6>
                            <ul class="small">
                                <li><strong>تحقق من الاتصال بالإنترنت</strong> - المكتبة تحتاج اتصال للتحميل</li>
                                <li><strong>أعد تحميل الصفحة</strong> - قد تكون مشكلة مؤقتة</li>
                                <li><strong>جرب متصفح آخر</strong> - قد يكون هناك حجب للمحتوى</li>
                                <li><strong>تعطيل مانع الإعلانات</strong> - قد يحجب تحميل المكتبات</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-lightbulb"></i> البدائل المتاحة:</h6>
                            <div class="d-grid gap-2">
                                <a href="bcrypt_security_tester.php" class="btn btn-primary">
                                    <i class="fas fa-server"></i> استخدم نسخة السيرفر
                                </a>
                                <button class="btn btn-warning" onclick="location.reload()">
                                    <i class="fas fa-refresh"></i> إعادة تحميل الصفحة
                                </button>
                                <button class="btn btn-info" onclick="showTechnicalDetails()">
                                    <i class="fas fa-info-circle"></i> تفاصيل تقنية
                                </button>
                            </div>
                        </div>
                    </div>

                    <div id="technicalDetails" class="mt-3" style="display: none;">
                        <hr>
                        <h6><i class="fas fa-code"></i> معلومات تقنية:</h6>
                        <div class="bg-light p-2 rounded small">
                            <strong>المصادر المجربة:</strong><br>
                            • ملف محلي: bcrypt.min.js<br>
                            • CDN 1: cdnjs.cloudflare.com<br>
                            • CDN 2: unpkg.com<br>
                            • CDN 3: cdn.jsdelivr.net<br>
                            • CDN 4: cdn.skypack.dev<br><br>
                            <strong>نوع المتصفح:</strong> ${navigator.userAgent}<br>
                            <strong>حالة الاتصال:</strong> ${navigator.onLine ? 'متصل' : 'غير متصل'}
                        </div>
                    </div>
                `;
            }
        }

        // عرض التفاصيل التقنية
        function showTechnicalDetails() {
            const details = document.getElementById('technicalDetails');
            if (details) {
                details.style.display = details.style.display === 'none' ? 'block' : 'none';
            }
        }

        // عرض نجاح تحميل مكتبة bcrypt
        function showBcryptSuccess() {
            const statusDiv = document.getElementById('bcryptStatus');
            if (statusDiv) {
                const isEmbedded = window.bcryptIsEmbedded || (typeof bcrypt !== 'undefined' && bcrypt.isEmbedded);

                if (isEmbedded) {
                    // الحصول على الأنواع المدعومة
                    const supportedTypes = (typeof bcrypt !== 'undefined' && bcrypt.supportedFormats)
                        ? bcrypt.supportedFormats.join(', ')
                        : '$2a$, $2b$, $2x$, $2y$';

                    statusDiv.className = 'alert alert-success';
                    statusDiv.innerHTML = `
                        <h6 class="alert-heading"><i class="fas fa-check-circle"></i> مكتبة bcrypt المضمنة جاهزة!</h6>
                        <p class="mb-2">تم تحميل مكتبة bcrypt المضمنة بنجاح. الأداة جاهزة للاستخدام!</p>
                        <div class="row">
                            <div class="col-md-8">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle"></i>
                                    نسخة مضمنة محسنة تعمل بدون اتصال إنترنت<br>
                                    <i class="fas fa-shield-alt"></i>
                                    الأنواع المدعومة: <strong>${supportedTypes}</strong>
                                </small>
                            </div>
                            <div class="col-md-4 text-end">
                                <a href="bcrypt_security_tester.php" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-server"></i> نسخة السيرفر
                                </a>
                            </div>
                        </div>
                    `;

                    // إخفاء الإشعار بعد 5 ثوانٍ
                    setTimeout(() => {
                        statusDiv.style.display = 'none';
                    }, 5000);
                } else {
                    statusDiv.className = 'alert alert-success';
                    statusDiv.innerHTML = `
                        <h6 class="alert-heading"><i class="fas fa-check-circle"></i> مكتبة bcrypt جاهزة!</h6>
                        <p class="mb-0">تم تحميل مكتبة bcrypt بنجاح. يمكنك الآن استخدام جميع ميزات الأداة.</p>
                    `;

                    // إخفاء الإشعار بعد 3 ثوانٍ
                    setTimeout(() => {
                        statusDiv.style.display = 'none';
                    }, 3000);
                }
            }
        }
    </script>
</body>
</html>