<?php
/**
 * صفحة فحص حالة قاعدة البيانات
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';

// فحص تسجيل الدخول قبل إرسال أي محتوى
redirectIfNotLoggedIn();

require_once __DIR__ . '/includes/header.php';

// فحص حالة قاعدة البيانات
$db = getCurrentUserDB();
$validation = validateDatabase($db);

$tables_info = [];
if ($db && !$db->connect_error) {
    $required_tables = ['customers', 'products', 'sales', 'purchases', 'sale_items', 'purchase_items'];
    
    foreach ($required_tables as $table) {
        $table_info = [
            'name' => $table,
            'exists' => false,
            'count' => 0,
            'size' => 'غير محدد'
        ];
        
        // فحص وجود الجدول
        $result = $db->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            $table_info['exists'] = true;
            
            // عدد السجلات
            $count_result = $db->query("SELECT COUNT(*) as count FROM $table");
            if ($count_result) {
                $table_info['count'] = $count_result->fetch_assoc()['count'];
            }
            
            // حجم الجدول
            $size_result = $db->query("SELECT 
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb'
                FROM information_schema.TABLES 
                WHERE table_schema = DATABASE() AND table_name = '$table'");
            if ($size_result) {
                $size_data = $size_result->fetch_assoc();
                $table_info['size'] = $size_data['size_mb'] . ' MB';
            }
        }
        
        $tables_info[] = $table_info;
    }
}
?>

<div class="container mt-4">
    <div class="card">
        <div class="card-header <?php echo $validation['status'] ? 'bg-success' : 'bg-warning'; ?> text-white">
            <h4 class="mb-0">
                <i class="fas fa-database"></i>
                حالة قاعدة البيانات
            </h4>
        </div>
        <div class="card-body">
            <!-- حالة عامة -->
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="alert <?php echo $validation['status'] ? 'alert-success' : 'alert-warning'; ?>">
                        <h5>
                            <i class="fas <?php echo $validation['status'] ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?>"></i>
                            <?php echo $validation['message']; ?>
                        </h5>
                    </div>
                </div>
            </div>

            <!-- معلومات الاتصال -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">معلومات الاتصال</h6>
                        </div>
                        <div class="card-body">
                            <p><strong>حالة الاتصال:</strong> 
                                <?php if ($db && !$db->connect_error): ?>
                                    <span class="badge bg-success">متصل</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">غير متصل</span>
                                <?php endif; ?>
                            </p>
                            <p><strong>معرف المستخدم:</strong> <?php echo $_SESSION['user_id']; ?></p>
                            <p><strong>اسم المستخدم:</strong> <?php echo $_SESSION['username']; ?></p>
                            <p><strong>قاعدة البيانات:</strong> sales_system_user_<?php echo $_SESSION['user_id']; ?></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">إحصائيات سريعة</h6>
                        </div>
                        <div class="card-body">
                            <?php
                            $total_records = 0;
                            $existing_tables = 0;
                            foreach ($tables_info as $table) {
                                if ($table['exists']) {
                                    $existing_tables++;
                                    $total_records += $table['count'];
                                }
                            }
                            ?>
                            <p><strong>الجداول الموجودة:</strong> <?php echo $existing_tables; ?> من <?php echo count($tables_info); ?></p>
                            <p><strong>إجمالي السجلات:</strong> <?php echo number_format($total_records); ?></p>
                            <p><strong>آخر فحص:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الجداول -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">تفاصيل الجداول</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>اسم الجدول</th>
                                    <th>الحالة</th>
                                    <th>عدد السجلات</th>
                                    <th>الحجم</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($tables_info as $table): ?>
                                <tr>
                                    <td><code><?php echo $table['name']; ?></code></td>
                                    <td>
                                        <?php if ($table['exists']): ?>
                                            <span class="badge bg-success">موجود</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">مفقود</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo number_format($table['count']); ?></td>
                                    <td><?php echo $table['size']; ?></td>
                                    <td>
                                        <?php
                                        $descriptions = [
                                            'customers' => 'بيانات العملاء',
                                            'products' => 'بيانات المنتجات',
                                            'sales' => 'فواتير المبيعات',
                                            'purchases' => 'فواتير المشتريات',
                                            'sale_items' => 'عناصر فواتير المبيعات',
                                            'purchase_items' => 'عناصر فواتير المشتريات'
                                        ];
                                        echo $descriptions[$table['name']] ?? 'غير محدد';
                                        ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="text-center mt-4">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    العودة للصفحة الرئيسية
                </a>
                <a href="database_status.php" class="btn btn-secondary ms-2">
                    <i class="fas fa-sync"></i>
                    تحديث الحالة
                </a>
                <?php if (!$validation['status']): ?>
                <button class="btn btn-warning ms-2" onclick="location.reload()">
                    <i class="fas fa-tools"></i>
                    إعادة المحاولة
                </button>
                <?php endif; ?>
            </div>

            <!-- معلومات إضافية -->
            <div class="mt-4">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> معلومات مهمة</h6>
                    <ul class="mb-0">
                        <li>يتم إنشاء الجداول تلقائياً عند أول استخدام للنظام</li>
                        <li>كل مستخدم له قاعدة بيانات منفصلة لضمان الخصوصية</li>
                        <li>يتم فحص سلامة الجداول تلقائياً مع كل اتصال</li>
                        <li>في حالة وجود مشاكل، يتم إعادة إنشاء الجداول المفقودة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
