<?php
/**
 * فهرس الأدوات الأمنية
 * صفحة رئيسية للوصول إلى جميع أدوات الأمان
 */

// فهرس الأدوات الأمنية - متاح للجميع بدون تسجيل دخول
// تم إزالة متطلب تسجيل الدخول لسهولة الوصول
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الأدوات الأمنية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.4);
        }
        .card-header {
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
            color: white;
            text-align: center;
        }
        .tool-card .card-header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        .generator-card .card-header {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        .main-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 40px;
        }
        .btn-tool {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: bold;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            width: 100%;
        }
        .btn-tool:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            color: white;
        }
        .btn-generator {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-weight: bold;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            width: 100%;
        }
        .btn-generator:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            color: white;
        }
        .warning-box {
            background: linear-gradient(45deg, #ffa726, #ff7043);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list i {
            color: #28a745;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                
                <!-- العنوان الرئيسي -->
                <div class="main-header">
                    <h1><i class="fas fa-shield-alt"></i> الأدوات الأمنية</h1>
                    <p class="mb-0">مجموعة من الأدوات لاختبار وتحسين أمان النظام</p>
                </div>

                <!-- إشعار الوصول المفتوح -->
                <div class="alert alert-success" role="alert" style="border-radius: 15px; border: none; margin-bottom: 20px;">
                    <h5 class="alert-heading"><i class="fas fa-unlock text-success"></i> وصول مفتوح للجميع!</h5>
                    <p class="mb-0">جميع الأدوات الأمنية متاحة الآن بدون تسجيل دخول للاستخدام التعليمي والأمني.</p>
                </div>

                <!-- تحذير أمني -->
                <div class="warning-box">
                    <h4><i class="fas fa-exclamation-triangle"></i> تحذير أمني مهم</h4>
                    <p class="mb-0">هذه الأدوات مخصصة للأغراض التعليمية واختبار الأمان فقط. استخدمها بمسؤولية وفقط على الأنظمة التي تملكها أو لديك إذن صريح لاختبارها.</p>
                </div>

                <div class="row">
                    
                    <!-- أداة اختبار bcrypt -->
                    <div class="col-md-6 mb-4">
                        <div class="card tool-card">
                            <div class="card-header">
                                <h4><i class="fas fa-search"></i> أداة اختبار bcrypt</h4>
                            </div>
                            <div class="card-body">
                                <p>أداة متقدمة لاختبار قوة كلمات المرور المشفرة بـ bcrypt باستخدام عدة طرق:</p>
                                <ul class="feature-list">
                                    <li><i class="fas fa-check"></i> اختبار كلمات المرور الشائعة</li>
                                    <li><i class="fas fa-check"></i> اختبار قاموس الكلمات</li>
                                    <li><i class="fas fa-check"></i> القوة الغاشمة البسيطة</li>
                                    <li><i class="fas fa-check"></i> اختبار الأنماط الشائعة</li>
                                    <li><i class="fas fa-check"></i> إحصائيات مفصلة</li>
                                    <li><i class="fas fa-check"></i> واجهة تفاعلية</li>
                                </ul>
                                <div class="mt-3">
                                    <a href="bcrypt_security_tester.php" class="btn-tool">
                                        <i class="fas fa-play"></i> بدء الاختبار
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- مولد bcrypt -->
                    <div class="col-md-6 mb-4">
                        <div class="card generator-card">
                            <div class="card-header">
                                <h4><i class="fas fa-key"></i> مولد bcrypt Hash</h4>
                            </div>
                            <div class="card-body">
                                <p>أداة لإنشاء bcrypt hashes لاختبار الأداة الرئيسية:</p>
                                <ul class="feature-list">
                                    <li><i class="fas fa-check"></i> إنشاء hashes بمستويات تعقيد مختلفة</li>
                                    <li><i class="fas fa-check"></i> كلمات مرور جاهزة للاختبار</li>
                                    <li><i class="fas fa-check"></i> نسخ سريع للحافظة</li>
                                    <li><i class="fas fa-check"></i> معلومات حول مستويات الأمان</li>
                                    <li><i class="fas fa-check"></i> أمثلة ضعيفة وقوية</li>
                                    <li><i class="fas fa-check"></i> ربط مباشر مع أداة الاختبار</li>
                                </ul>
                                <div class="mt-3">
                                    <a href="bcrypt_hash_generator.php" class="btn-generator">
                                        <i class="fas fa-magic"></i> إنشاء Hash
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- معلومات تعليمية -->
                <div class="card mt-4">
                    <div class="card-header" style="background: linear-gradient(45deg, #3498db, #2980b9);">
                        <h5><i class="fas fa-graduation-cap"></i> معلومات تعليمية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-info-circle text-primary"></i> ما هو bcrypt؟</h6>
                                <p>bcrypt هو دالة تشفير كلمات المرور مصممة لتكون بطيئة ومقاومة للهجمات. يستخدم salt عشوائي ومستوى تعقيد قابل للتحكم.</p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-shield-alt text-success"></i> لماذا bcrypt آمن؟</h6>
                                <p>bcrypt مقاوم للهجمات بالقوة الغاشمة لأنه بطيء بالتصميم. كل زيادة في مستوى التعقيد تضاعف الوقت المطلوب.</p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-lightbulb text-warning"></i> نصائح الأمان</h6>
                                <p>استخدم كلمات مرور طويلة ومعقدة، تجنب الكلمات الشائعة، واستخدم مستوى تعقيد مناسب (10 أو أكثر).</p>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-times-circle text-danger"></i> كلمات مرور ضعيفة:</h6>
                                <ul>
                                    <li>كلمات قاموس عادية (password, admin)</li>
                                    <li>أرقام متسلسلة (123456, 111111)</li>
                                    <li>أنماط لوحة المفاتيح (qwerty, asdf)</li>
                                    <li>معلومات شخصية (اسم، تاريخ ميلاد)</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-check-circle text-success"></i> كلمات مرور قوية:</h6>
                                <ul>
                                    <li>مزيج من أحرف كبيرة وصغيرة</li>
                                    <li>أرقام ورموز خاصة</li>
                                    <li>طول 12 حرف أو أكثر</li>
                                    <li>عدم استخدام كلمات قاموس</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أدوات إضافية -->
                <div class="card mt-4">
                    <div class="card-header" style="background: linear-gradient(45deg, #9b59b6, #8e44ad);">
                        <h5><i class="fas fa-tools"></i> أدوات إضافية</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>النسخة المباشرة:</h6>
                                <a href="bcrypt_tester_offline.html" class="btn btn-outline-warning">
                                    <i class="fas fa-rocket"></i> النسخة المباشرة
                                </a>
                            </div>
                            <div class="col-md-4">
                                <h6>دليل الاختبارات:</h6>
                                <a href="password_testing_guide.php" class="btn btn-outline-info">
                                    <i class="fas fa-book"></i> دليل الاختبارات
                                </a>
                            </div>
                            <div class="col-md-4">
                                <h6>دليل النسخة المباشرة:</h6>
                                <a href="offline_guide.html" class="btn btn-outline-secondary">
                                    <i class="fas fa-question-circle"></i> دليل النسخة المباشرة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
