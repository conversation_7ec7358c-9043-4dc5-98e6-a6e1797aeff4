<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد قواعد البيانات - salessystem_v2</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .setup-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin: 50px auto;
            max-width: 800px;
            overflow: hidden;
        }
        .setup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .setup-content {
            padding: 40px;
        }
        .database-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .database-card:hover {
            border-color: #007bff;
            box-shadow: 0 5px 15px rgba(0,123,255,0.1);
        }
        .status-badge {
            font-size: 14px;
            padding: 5px 15px;
            border-radius: 20px;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .sql-code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
        }
        .step-number {
            background: #007bff;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="setup-container">
            <div class="setup-header">
                <h1><i class="fas fa-database"></i> إعداد قواعد البيانات</h1>
                <p class="mb-0">نظام المبيعات المحسن - الإصدار 2.0</p>
            </div>
            
            <div class="setup-content">
                <!-- تنبيه عام -->
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> تنبيه مهم</h5>
                    <p class="mb-0">يجب إنشاء قواعد البيانات التالية يدوياً قبل استخدام النظام. تم إزالة الإنشاء التلقائي لأسباب أمنية.</p>
                </div>

                <!-- قاعدة البيانات الرئيسية -->
                <div class="database-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4><i class="fas fa-server"></i> قاعدة البيانات الرئيسية</h4>
                        <?php
                        // فحص قاعدة البيانات الرئيسية
                        $main_status = false;
                        try {
                            $test_main = new mysqli('localhost', 'sales01', 'dNz35nd5@', 'u193708811_system_main');
                            if (!$test_main->connect_error) {
                                $main_status = true;
                                $test_main->close();
                            }
                        } catch (Exception $e) {
                            $main_status = false;
                        }
                        
                        if ($main_status) {
                            echo '<span class="status-badge status-success"><i class="fas fa-check"></i> متصلة</span>';
                        } else {
                            echo '<span class="status-badge status-error"><i class="fas fa-times"></i> غير موجودة</span>';
                        }
                        ?>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم قاعدة البيانات:</strong> <code>u193708811_system_main</code></p>
                            <p><strong>المستخدم:</strong> <code>sales01</code></p>
                            <p><strong>كلمة المرور:</strong> <code>dNz35nd5@</code></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الغرض:</strong> تخزين بيانات المستخدمين والمديرين</p>
                            <p><strong>الجداول:</strong> users, admins, activity_log</p>
                        </div>
                    </div>
                    
                    <?php if (!$main_status): ?>
                    <div class="mt-3">
                        <h6><i class="fas fa-code"></i> أمر SQL للإنشاء:</h6>
                        <div class="sql-code">
                            CREATE DATABASE IF NOT EXISTS `u193708811_system_main`<br>
                            CHARACTER SET utf8mb4<br>
                            COLLATE utf8mb4_general_ci;
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- قاعدة بيانات العمليات -->
                <div class="database-card">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4><i class="fas fa-cogs"></i> قاعدة بيانات العمليات</h4>
                        <?php
                        // فحص قاعدة بيانات العمليات
                        $ops_status = false;
                        try {
                            $test_ops = new mysqli('localhost', 'sales02', 'dNz35nd5@', 'u193708811_operations');
                            if (!$test_ops->connect_error) {
                                $ops_status = true;
                                $test_ops->close();
                            }
                        } catch (Exception $e) {
                            $ops_status = false;
                        }
                        
                        if ($ops_status) {
                            echo '<span class="status-badge status-success"><i class="fas fa-check"></i> متصلة</span>';
                        } else {
                            echo '<span class="status-badge status-error"><i class="fas fa-times"></i> غير موجودة</span>';
                        }
                        ?>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>اسم قاعدة البيانات:</strong> <code>u193708811_operations</code></p>
                            <p><strong>المستخدم:</strong> <code>sales02</code></p>
                            <p><strong>كلمة المرور:</strong> <code>dNz35nd5@</code></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>الغرض:</strong> تخزين بيانات العمليات مع البادئات</p>
                            <p><strong>الجداول:</strong> {username}_customers, {username}_products, etc.</p>
                        </div>
                    </div>
                    
                    <?php if (!$ops_status): ?>
                    <div class="mt-3">
                        <h6><i class="fas fa-code"></i> أمر SQL للإنشاء:</h6>
                        <div class="sql-code">
                            CREATE DATABASE IF NOT EXISTS `u193708811_operations`<br>
                            CHARACTER SET utf8mb4<br>
                            COLLATE utf8mb4_general_ci;
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- طرق الإنشاء -->
                <div class="mt-4">
                    <h4><i class="fas fa-tools"></i> طرق إنشاء قواعد البيانات</h4>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="step-number">1</div>
                                    <h5 class="card-title">phpMyAdmin</h5>
                                    <p class="card-text">استخدم واجهة phpMyAdmin لإنشاء قواعد البيانات بسهولة</p>
                                    <a href="http://localhost/phpmyadmin" target="_blank" class="btn btn-primary">
                                        <i class="fas fa-external-link-alt"></i> فتح phpMyAdmin
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="step-number">2</div>
                                    <h5 class="card-title">أوامر SQL</h5>
                                    <p class="card-text">استخدم أوامر SQL المباشرة من خلال سطر الأوامر</p>
                                    <button class="btn btn-info" onclick="showSQLCommands()">
                                        <i class="fas fa-code"></i> عرض الأوامر
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <div class="step-number">3</div>
                                    <h5 class="card-title">لوحة التحكم</h5>
                                    <p class="card-text">استخدم لوحة تحكم الاستضافة (cPanel) لإنشاء قواعد البيانات</p>
                                    <button class="btn btn-success" onclick="showCPanelSteps()">
                                        <i class="fas fa-server"></i> خطوات cPanel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أوامر SQL (مخفية) -->
                <div id="sqlCommands" class="mt-4" style="display: none;">
                    <div class="alert alert-info">
                        <h5><i class="fas fa-terminal"></i> أوامر SQL</h5>
                        <p>نفذ الأوامر التالية في سطر أوامر MySQL:</p>
                        
                        <h6>للقاعدة الرئيسية:</h6>
                        <div class="sql-code">
                            mysql -u sales01 -p<br>
                            CREATE DATABASE IF NOT EXISTS `u193708811_system_main` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
                        </div>
                        
                        <h6>لقاعدة العمليات:</h6>
                        <div class="sql-code">
                            mysql -u sales02 -p<br>
                            CREATE DATABASE IF NOT EXISTS `u193708811_operations` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
                        </div>
                    </div>
                </div>

                <!-- خطوات cPanel (مخفية) -->
                <div id="cpanelSteps" class="mt-4" style="display: none;">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-server"></i> خطوات cPanel</h5>
                        <ol>
                            <li>اذهب إلى لوحة تحكم الاستضافة (cPanel)</li>
                            <li>ابحث عن قسم "قواعد البيانات" (Databases)</li>
                            <li>انقر على "MySQL Databases"</li>
                            <li>في حقل "Create New Database" أدخل:
                                <ul>
                                    <li><code>system_main</code> (ستصبح u193708811_system_main)</li>
                                    <li><code>operations</code> (ستصبح u193708811_operations)</li>
                                </ul>
                            </li>
                            <li>اربط المستخدمين المناسبين بقواعد البيانات</li>
                            <li>امنح جميع الصلاحيات (ALL PRIVILEGES)</li>
                        </ol>
                    </div>
                </div>

                <!-- حالة النظام -->
                <div class="mt-4">
                    <?php
                    if ($main_status && $ops_status) {
                        echo '<div class="alert alert-success">';
                        echo '<h5><i class="fas fa-check-circle"></i> النظام جاهز!</h5>';
                        echo '<p class="mb-0">جميع قواعد البيانات متصلة بنجاح. يمكنك الآن استخدام النظام.</p>';
                        echo '</div>';
                        
                        echo '<div class="text-center">';
                        echo '<a href="index.php" class="btn btn-success btn-lg me-3">';
                        echo '<i class="fas fa-home"></i> الذهاب للنظام';
                        echo '</a>';
                        echo '<a href="test_system.php" class="btn btn-info btn-lg">';
                        echo '<i class="fas fa-test-tube"></i> اختبار النظام';
                        echo '</a>';
                        echo '</div>';
                    } else {
                        echo '<div class="alert alert-warning">';
                        echo '<h5><i class="fas fa-exclamation-triangle"></i> يجب إنشاء قواعد البيانات</h5>';
                        echo '<p class="mb-0">أنشئ قواعد البيانات المطلوبة باستخدام إحدى الطرق المذكورة أعلاه، ثم أعد تحميل هذه الصفحة.</p>';
                        echo '</div>';
                        
                        echo '<div class="text-center">';
                        echo '<button onclick="location.reload()" class="btn btn-primary btn-lg me-3">';
                        echo '<i class="fas fa-refresh"></i> إعادة فحص';
                        echo '</button>';
                        echo '<a href="DATABASE_SETUP.md" class="btn btn-info btn-lg me-3">';
                        echo '<i class="fas fa-book"></i> دليل الإعداد';
                        echo '</a>';
                        echo '<a href="test_system.php" class="btn btn-success btn-lg">';
                        echo '<i class="fas fa-chart-bar"></i> تقارير النظام';
                        echo '</a>';
                        echo '</div>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showSQLCommands() {
            const element = document.getElementById('sqlCommands');
            element.style.display = element.style.display === 'none' ? 'block' : 'none';
        }
        
        function showCPanelSteps() {
            const element = document.getElementById('cpanelSteps');
            element.style.display = element.style.display === 'none' ? 'block' : 'none';
        }
    </script>
</body>
</html>
