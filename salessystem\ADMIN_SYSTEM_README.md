# نظام المدير - دليل الاستخدام الشامل

## 🛡️ نظرة عامة

تم إنشاء نظام مدير منفصل بصلاحيات شاملة لإدارة جميع المستخدمين ومراقبة جميع العمليات في نظام إدارة المبيعات.

## 🚀 الوصول للنظام

### رابط تسجيل الدخول
```
http://localhost/salessystem/admin_login.php
```

### بيانات المدير الافتراضي
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **البريد الإلكتروني:** `<EMAIL>`

## 📋 الصفحات والوظائف

### 1. لوحة التحكم الرئيسية (`admin_dashboard.php`)
- **الإحصائيات الفورية:**
  - إجمالي المستخدمين
  - المستخدمين النشطين
  - نشاط اليوم
  - المستخدمين الحديثين (آخر 30 يوم)
- **أحدث العمليات:** عرض آخر 10 عمليات في النظام
- **روابط سريعة:** للوصول لجميع أقسام الإدارة

### 2. إدارة المستخدمين (`admin_users.php`)
- **عرض جميع المستخدمين** مع معلوماتهم الكاملة
- **البحث والفلترة:**
  - البحث بالاسم أو البريد الإلكتروني
  - فلترة حسب الحالة (نشط/غير نشط)
- **الإجراءات المتاحة:**
  - عرض تفاصيل المستخدم
  - تفعيل/إلغاء تفعيل الحساب
  - حذف المستخدم وقاعدة بياناته نهائياً
- **تصدير البيانات:** إمكانية تصدير قائمة المستخدمين

### 3. سجل العمليات (`admin_activity.php`)
- **مراقبة شاملة** لجميع الأنشطة في النظام
- **الإحصائيات:**
  - إجمالي العمليات
  - عمليات المستخدمين
  - عمليات المديرين
  - عمليات اليوم
- **فلترة متقدمة:**
  - البحث في الوصف والعمليات
  - فلترة حسب نوع المستخدم (مستخدم/مدير)
  - فلترة حسب نوع العملية
  - فلترة حسب التاريخ (من - إلى)
- **التنقل بين الصفحات:** عرض 50 عملية في كل صفحة

## 🔐 نظام الصلاحيات

### الصلاحيات المتاحة:
- `manage_users`: إدارة حسابات المستخدمين
- `view_all_data`: عرض جميع البيانات والتقارير
- `manage_system`: إدارة إعدادات النظام
- `view_reports`: عرض التقارير الشاملة
- `manage_admins`: إدارة حسابات المديرين الآخرين

### المدير العام (Super Admin):
- صلاحيات كاملة على جميع أجزاء النظام
- إمكانية إدارة المديرين الآخرين
- الوصول لجميع البيانات بدون قيود

## 📊 العمليات المسجلة

### عمليات المستخدمين:
- `user_login`: تسجيل دخول المستخدم
- `user_register`: تسجيل مستخدم جديد
- `user_logout`: تسجيل خروج المستخدم
- `sale_create`: إنشاء فاتورة مبيعات
- `purchase_create`: إنشاء فاتورة مشتريات
- `customer_create`: إضافة عميل جديد
- `data_update`: تحديث البيانات
- `data_delete`: حذف البيانات

### عمليات المديرين:
- `admin_login`: تسجيل دخول المدير
- `admin_logout`: تسجيل خروج المدير
- `user_status_changed`: تغيير حالة المستخدم
- `user_deleted`: حذف مستخدم
- `system_setup`: إعداد النظام

## 🗄️ قاعدة البيانات

### الجداول الجديدة:

#### جدول المديرين (`admins`)
```sql
- id: معرف المدير
- username: اسم المستخدم (فريد)
- password: كلمة المرور المشفرة
- full_name: الاسم الكامل
- email: البريد الإلكتروني (فريد)
- phone: رقم الهاتف
- permissions: الصلاحيات (JSON)
- is_super_admin: مدير عام (0/1)
- is_active: نشط (0/1)
- last_login: آخر تسجيل دخول
- created_at: تاريخ الإنشاء
- updated_at: تاريخ آخر تحديث
```

#### جدول سجل العمليات (`activity_log`)
```sql
- id: معرف العملية
- user_id: معرف المستخدم/المدير
- user_type: نوع المستخدم (user/admin)
- action: نوع العملية
- table_name: اسم الجدول المتأثر
- record_id: معرف السجل المتأثر
- old_data: البيانات القديمة (JSON)
- new_data: البيانات الجديدة (JSON)
- description: وصف العملية
- ip_address: عنوان IP
- user_agent: معلومات المتصفح
- created_at: وقت العملية
```

## 🔧 الدوال المساعدة

### دوال التحقق من الصلاحيات:
- `isAdminLoggedIn()`: التحقق من تسجيل دخول المدير
- `hasAdminPermission($permission)`: التحقق من صلاحية معينة
- `logActivity()`: تسجيل العمليات في سجل النشاطات

## 🎨 التصميم والواجهة

### الألوان المميزة:
- **اللون الأساسي:** أحمر (`#dc3545`) للتمييز عن النظام العادي
- **الشريط الجانبي:** رمادي داكن (`#343a40`)
- **البطاقات:** تدرج أحمر مع ظلال

### الميزات التفاعلية:
- **إشعارات فورية:** للعمليات الناجحة والأخطاء
- **تأكيد العمليات:** للعمليات الحساسة مثل الحذف
- **تحديث تلقائي:** للإحصائيات كل 30 ثانية
- **بحث سريع:** في جميع أقسام النظام

## 🔗 الروابط السريعة

### من النظام العادي:
- رابط في footer الصفحة الرئيسية
- رابط مباشر: `admin_login.php`

### داخل نظام المدير:
- لوحة التحكم: `admin_dashboard.php`
- إدارة المستخدمين: `admin_users.php`
- سجل العمليات: `admin_activity.php`
- تسجيل الخروج: `admin_logout.php`

## 🛡️ الأمان والحماية

### ميزات الأمان:
- **تشفير كلمات المرور:** باستخدام `password_hash()`
- **فحص الصلاحيات:** مع كل طلب
- **تسجيل جميع الأنشطة:** للمراجعة والتدقيق
- **حماية من الوصول غير المصرح:** إعادة توجيه للمتطفلين
- **جلسات منفصلة:** عن المستخدمين العاديين

### معلومات إضافية:
- **عنوان IP:** يتم تسجيله مع كل عملية
- **معلومات المتصفح:** للتتبع الأمني
- **الوقت الدقيق:** لكل عملية

## 📈 الإحصائيات والتقارير

### الإحصائيات المتاحة:
- إجمالي المستخدمين (نشط/غير نشط)
- المستخدمين الحديثين (آخر 30 يوم)
- عدد العمليات اليومية
- توزيع العمليات حسب النوع
- نشاط المستخدمين والمديرين

### إمكانيات التصدير:
- تصدير قائمة المستخدمين (Excel)
- تصدير سجل العمليات (Excel)
- تقارير مخصصة حسب التاريخ

## 🚀 التطوير المستقبلي

### الميزات المخططة:
- إدارة المديرين الآخرين
- تقارير مرئية بالرسوم البيانية
- إشعارات فورية للأنشطة المهمة
- نظام النسخ الاحتياطي التلقائي
- إعدادات النظام المتقدمة

---

**ملاحظة:** هذا النظام يوفر مراقبة شاملة وإدارة كاملة لجميع جوانب نظام إدارة المبيعات مع الحفاظ على أعلى معايير الأمان والخصوصية.
