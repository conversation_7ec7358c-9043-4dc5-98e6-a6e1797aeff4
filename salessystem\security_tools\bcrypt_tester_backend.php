<?php
/**
 * معالج خلفي لأداة اختبار أمان bcrypt
 * هذا الملف يتعامل مع طلبات AJAX لاختبار كلمات المرور
 */

session_start();

// التحقق من الصلاحيات
if (!isset($_SESSION['admin_id'])) {
    http_response_code(403);
    echo json_encode(['error' => 'غير مسموح']);
    exit;
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة غير مسموحة']);
    exit;
}

// التحقق من وجود البيانات المطلوبة
if (!isset($_POST['action']) || $_POST['action'] !== 'test_bcrypt') {
    http_response_code(400);
    echo json_encode(['error' => 'طلب غير صحيح']);
    exit;
}

$hash = $_POST['hash'] ?? '';
$method = $_POST['method'] ?? 'common';
$maxAttempts = intval($_POST['max_attempts'] ?? 1000);

// التحقق من صحة البيانات
if (empty($hash)) {
    echo json_encode(['error' => 'Hash مطلوب']);
    exit;
}

// التحقق من صحة bcrypt hash
if (!preg_match('/^\$2[ayb]\$.{56}$/', $hash)) {
    echo json_encode(['error' => 'Hash غير صحيح - يجب أن يكون bcrypt hash صالح']);
    exit;
}

// تحديد الحد الأقصى للمحاولات لحماية الخادم
$maxAttempts = min($maxAttempts, 10000);

$startTime = microtime(true);
$attempts = 0;
$passwordFound = false;
$foundPassword = '';

try {
    switch ($method) {
        case 'common':
            $result = testCommonPasswords($hash, $maxAttempts);
            break;
        case 'dictionary':
            $result = testDictionaryPasswords($hash, $maxAttempts);
            break;
        case 'brute_simple':
            $result = testBruteForceSimple($hash, $maxAttempts);
            break;
        case 'pattern':
            $result = testPatternPasswords($hash, $maxAttempts);
            break;
        default:
            throw new Exception('طريقة اختبار غير صحيحة');
    }

    $endTime = microtime(true);
    $timeTaken = round($endTime - $startTime, 2);

    echo json_encode([
        'success' => true,
        'password_found' => $result['found'],
        'password' => $result['password'],
        'attempts' => $result['attempts'],
        'time_taken' => $timeTaken,
        'method' => $method
    ]);

} catch (Exception $e) {
    echo json_encode([
        'error' => $e->getMessage()
    ]);
}

/**
 * اختبار كلمات المرور الشائعة
 */
function testCommonPasswords($hash, $maxAttempts) {
    $commonPasswords = [
        '123456', 'password', '123456789', '12345678', '12345',
        '1234567', '1234567890', 'qwerty', 'abc123', '111111',
        'password1', 'admin', 'welcome', 'monkey', 'login',
        'princess', 'qwertyuiop', 'solo', 'passw0rd', 'starwars',
        'hello', 'dragon', 'master', 'freedom', 'whatever',
        'qazwsx', 'trustno1', 'jordan', 'harley', 'robert',
        'matthew', 'daniel', 'andrew', 'joshua', 'hunter',
        'target', 'charlie', 'michael', 'sunshine', 'computer',
        'michelle', 'jessica', 'pepper', 'zxcvbnm', 'ashley',
        'nicole', 'chelsea', 'biteme', 'summer', 'sophie',
        'football', 'jesus', 'michael', 'ninja', 'mustang',
        'mercedes', 'samsung', 'cookie', 'maverick', 'tigger',
        'puppy', 'flower', 'baseball', 'shadow', 'lovely',
        'buster', 'basketball', 'soccer', 'purple', 'matrix',
        'secret', 'summer', 'orange', 'jordan', 'taylor',
        'yankees', 'austin', 'william', 'daniel', 'golfer',
        'summer', 'heather', 'hammer', 'yankees', 'joshua',
        'maggie', 'biteme', 'enter', 'ashley', 'thunder',
        'cowboy', 'silver', 'richard', 'orange', 'merlin',
        'michelle', 'corvette', 'bigdog', 'cheese', 'matthew',
        'patrick', 'martin', 'freedom', 'ginger', 'blondie',
        'cookie', 'rock', 'angel', 'much', 'rebel', 'xxx',
        'qwerty123', 'password123', 'admin123', '123123',
        'test', 'guest', 'info', 'adm', 'mysql', 'user',
        'administrator', 'oracle', 'ftp', 'pi', 'puppet',
        'ansible', 'ec2-user', 'vagrant', 'azureuser'
    ];
    
    $attempts = 0;
    foreach ($commonPasswords as $password) {
        if ($attempts >= $maxAttempts) break;

        $attempts++;
        if (password_verify($password, $hash)) {
            return [
                'found' => true,
                'password' => $password,
                'attempts' => $attempts
            ];
        }

        // إضافة تأخير صغير لمنع الحمل الزائد
        if ($attempts % 100 == 0) {
            usleep(10000); // 10ms
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار قاموس كلمات المرور
 */
function testDictionaryPasswords($hash, $maxAttempts) {
    $dictionaryWords = [
        'welcome', 'hello', 'world', 'computer', 'internet',
        'security', 'system', 'network', 'server', 'database',
        'application', 'software', 'hardware', 'technology',
        'information', 'digital', 'online', 'website', 'email',
        'mobile', 'phone', 'device', 'machine', 'program',
        'code', 'data', 'file', 'folder', 'document', 'text',
        'image', 'video', 'audio', 'music', 'game', 'play',
        'work', 'office', 'home', 'family', 'friend', 'love',
        'life', 'time', 'money', 'business', 'company', 'job',
        'project', 'team', 'group', 'member', 'user', 'account',
        'profile', 'setting', 'option', 'feature', 'service',
        'support', 'help', 'guide', 'tutorial', 'example'
    ];
    
    $variations = ['', '1', '12', '123', '1234', '!', '@', '#'];
    $attempts = 0;

    foreach ($dictionaryWords as $word) {
        foreach ($variations as $suffix) {
            if ($attempts >= $maxAttempts) break 2;

            $password = $word . $suffix;
            $attempts++;

            if (password_verify($password, $hash)) {
                return [
                    'found' => true,
                    'password' => $password,
                    'attempts' => $attempts
                ];
            }

            // اختبار مع أول حرف كبير
            $passwordCap = ucfirst($word) . $suffix;
            $attempts++;

            if (password_verify($passwordCap, $hash)) {
                return [
                    'found' => true,
                    'password' => $passwordCap,
                    'attempts' => $attempts
                ];
            }

            if ($attempts % 50 == 0) {
                usleep(5000); // 5ms
            }
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار القوة الغاشمة البسيطة
 */
function testBruteForceSimple($hash, $maxAttempts) {
    $chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    $attempts = 0;

    // اختبار كلمات مرور من 1 إلى 4 أحرف
    for ($length = 1; $length <= 4; $length++) {
        $result = generatePasswords($chars, $length, $hash, $maxAttempts, $attempts);
        if ($result['found'] || $attempts >= $maxAttempts) {
            return $result;
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * توليد كلمات المرور للقوة الغاشمة
 */
function generatePasswords($chars, $length, $hash, $maxAttempts, &$attempts) {
    $charsLength = strlen($chars);
    $total = pow($charsLength, $length);

    for ($i = 0; $i < $total && $attempts < $maxAttempts; $i++) {
        $password = '';
        $temp = $i;

        for ($j = 0; $j < $length; $j++) {
            $password = $chars[$temp % $charsLength] . $password;
            $temp = intval($temp / $charsLength);
        }

        $attempts++;
        if (password_verify($password, $hash)) {
            return [
                'found' => true,
                'password' => $password,
                'attempts' => $attempts
            ];
        }

        if ($attempts % 100 == 0) {
            usleep(1000); // 1ms
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * اختبار الأنماط الشائعة
 */
function testPatternPasswords($hash, $maxAttempts) {
    $patterns = [
        // أنماط التاريخ
        '2023', '2022', '2021', '2020', '2019', '2018',
        '01012023', '01012022', '12312023', '12312022',
        
        // أنماط الأرقام
        '0000', '1111', '2222', '3333', '4444', '5555',
        '6666', '7777', '8888', '9999', '1010', '2020',
        
        // أنماط لوحة المفاتيح
        'qwerty', 'asdf', 'zxcv', 'qaz', 'wsx', 'edc',
        'qwe', 'asd', 'zxc', '147', '258', '369',
        
        // أنماط الكلمات + أرقام
        'test123', 'user123', 'pass123', 'admin123',
        'root123', 'demo123', 'temp123', 'guest123'
    ];
    
    $attempts = 0;
    foreach ($patterns as $pattern) {
        if ($attempts >= $maxAttempts) break;

        $attempts++;
        if (password_verify($pattern, $hash)) {
            return [
                'found' => true,
                'password' => $pattern,
                'attempts' => $attempts
            ];
        }

        if ($attempts % 50 == 0) {
            usleep(5000); // 5ms
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts
    ];
}

/**
 * توليد كلمات مرور إضافية للاختبار اللانهائي
 */
function generateMorePasswords() {
    $additional = [];

    // كلمات مرور بأرقام متسلسلة
    for ($i = 0; $i <= 9999; $i++) {
        $additional[] = str_pad($i, 4, '0', STR_PAD_LEFT);
    }

    // كلمات مرور بأنماط مختلفة
    $patterns = ['test', 'user', 'pass', 'demo', 'temp', 'guest', 'root', 'admin'];
    for ($i = 0; $i <= 999; $i++) {
        foreach ($patterns as $pattern) {
            $additional[] = $pattern . $i;
            $additional[] = $pattern . str_pad($i, 3, '0', STR_PAD_LEFT);
        }
    }

    // كلمات مرور بتواريخ
    for ($year = 1990; $year <= 2030; $year++) {
        $additional[] = (string)$year;
        $additional[] = '01012' . substr($year, -2);
        $additional[] = '31122' . substr($year, -2);
    }

    return $additional;
}

/**
 * متابعة الاختبار اللانهائي
 */
function continueInfiniteTest($hash, $startAttempts, $params, $method) {
    $maxTime = $params['max_time'];
    $startTime = $params['start_time'];
    $delayMs = $params['delay_ms'];
    $hasTimeLimit = ($maxTime !== -1);

    $attempts = $startAttempts;
    $chars = 'abcdefghijklmnopqrstuvwxyz0123456789';

    // بدء من كلمات مرور بطول 1 حرف وزيادة تدريجية
    for ($length = 1; $length <= 8; $length++) {
        $total = pow(strlen($chars), $length);

        for ($i = 0; $i < $total; $i++) {
            // فحص الحد الزمني
            if ($hasTimeLimit && (microtime(true) - $startTime) > $maxTime) {
                return [
                    'found' => false,
                    'password' => '',
                    'attempts' => $attempts,
                    'stopped_reason' => 'time_limit'
                ];
            }

            $password = '';
            $temp = $i;

            for ($j = 0; $j < $length; $j++) {
                $password = $chars[$temp % strlen($chars)] . $password;
                $temp = intval($temp / strlen($chars));
            }

            $attempts++;
            if (password_verify($password, $hash)) {
                return [
                    'found' => true,
                    'password' => $password,
                    'attempts' => $attempts,
                    'stopped_reason' => 'found'
                ];
            }

            // إضافة تأخير
            if ($delayMs > 0 && $attempts % 100 == 0) {
                usleep($delayMs * 1000);
            }

            // فحص دوري للذاكرة والوقت
            if ($attempts % 10000 == 0) {
                // فحص استهلاك الذاكرة
                if (memory_get_usage() > 128 * 1024 * 1024) { // 128MB
                    return [
                        'found' => false,
                        'password' => '',
                        'attempts' => $attempts,
                        'stopped_reason' => 'memory_limit'
                    ];
                }
            }
        }
    }

    return [
        'found' => false,
        'password' => '',
        'attempts' => $attempts,
        'stopped_reason' => 'exhausted'
    ];
}
?>
