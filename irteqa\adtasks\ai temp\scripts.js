document.addEventListener('DOMContentLoaded', function () {
    const progressBars = document.querySelectorAll('.progress');

    progressBars.forEach(progress => {
        const percentage = progress.dataset.percentage;
        const progressBar = progress.querySelector('.progress-bar');
        progressBar.style.transform = `rotate(${(percentage / 100) * 180}deg)`;
    });

    // Example chart data using Chart.js
    const ctx1 = document.getElementById('visitorChart').getContext('2d');
    const visitorChart = new Chart(ctx1, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر'],
            datasets: [
                {
                    label: 'الزوار الجدد',
                    data: [15, 20, 18, 25, 22, 30, 28, 35, 25, 30, 20],
                    borderColor: 'rgba(52, 152, 219, 1)',
                    fill: false
                },
                {
                    label: 'الزوار العائدون',
                    data: [10, 15, 12, 20, 18, 25, 22, 30, 20, 25, 15],
                    borderColor: 'rgba(231, 76, 60, 1)',
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    const ctx2 = document.getElementById('salesChart').getContext('2d');
    const salesChart = new Chart(ctx2, {
        type: 'line',
        data: {
            labels: ['الأسبوع الأول', 'الأسبوع الثاني', 'الأسبوع الثالث', 'الأسبوع الرابع'],
            datasets: [
                {
                    label: 'المبيعات في يناير',
                    data: [200, 300, 250, 400],
                    borderColor: 'rgba(46, 204, 113, 1)',
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
});
