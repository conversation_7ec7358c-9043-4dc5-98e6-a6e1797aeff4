<?php
/**
 * أداة المراجعة الأمنية الشاملة للمشروع
 */

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/header.php';
redirectIfNotLoggedIn();

// فحص المشاكل الأمنية
$security_issues = [];
$security_score = 100;

// 1. فحص SQL Injection
$sql_injection_issues = [];
$files_to_check = [
    'add_sale.php', 'add_purchase.php', 'add_customer.php', 'edit_sale.php', 
    'edit_purchase.php', 'edit_customer.php', 'delete_sale.php', 'delete_purchase.php', 
    'delete_customer.php', 'ajax_handler.php', 'customers.php', 'sales.php', 'purchases.php'
];

foreach ($files_to_check as $file) {
    $filepath = __DIR__ . '/' . $file;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        
        // فحص استخدام prepared statements
        if (preg_match('/\$db->query\s*\(\s*["\'].*\$/', $content)) {
            $sql_injection_issues[] = "$file: استخدام query() مع متغيرات مباشرة";
            $security_score -= 5;
        }
        
        // فحص استخدام mysqli_query مع متغيرات
        if (preg_match('/mysqli_query\s*\(.*\$/', $content)) {
            $sql_injection_issues[] = "$file: استخدام mysqli_query مع متغيرات مباشرة";
            $security_score -= 5;
        }
        
        // فحص عدم استخدام htmlspecialchars
        if (preg_match('/echo\s+\$_/', $content) && !preg_match('/htmlspecialchars/', $content)) {
            $sql_injection_issues[] = "$file: عرض متغيرات بدون تنظيف";
            $security_score -= 3;
        }
    }
}

// 2. فحص XSS
$xss_issues = [];
foreach ($files_to_check as $file) {
    $filepath = __DIR__ . '/' . $file;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        
        // فحص عرض البيانات بدون تنظيف
        if (preg_match('/echo\s+\$_(GET|POST|REQUEST)/', $content)) {
            $xss_issues[] = "$file: عرض بيانات المستخدم بدون تنظيف";
            $security_score -= 8;
        }
        
        // فحص استخدام innerHTML مع بيانات غير منظفة
        if (preg_match('/innerHTML\s*=.*\$/', $content)) {
            $xss_issues[] = "$file: استخدام innerHTML مع بيانات غير منظفة";
            $security_score -= 6;
        }
    }
}

// 3. فحص CSRF Protection
$csrf_issues = [];
$csrf_protected_files = 0;
$csrf_total_files = 0;

foreach ($files_to_check as $file) {
    $filepath = __DIR__ . '/' . $file;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        
        // فحص وجود نماذج POST
        if (preg_match('/method=["\']post["\']/', $content)) {
            $csrf_total_files++;
            
            // فحص وجود CSRF token
            if (preg_match('/csrf_token|_token/', $content)) {
                $csrf_protected_files++;
            } else {
                $csrf_issues[] = "$file: نموذج POST بدون حماية CSRF";
                $security_score -= 10;
            }
        }
    }
}

// 4. فحص Session Security
$session_issues = [];
$session_config = [
    'session.cookie_httponly' => ini_get('session.cookie_httponly'),
    'session.cookie_secure' => ini_get('session.cookie_secure'),
    'session.use_strict_mode' => ini_get('session.use_strict_mode'),
    'session.cookie_samesite' => ini_get('session.cookie_samesite')
];

if (!$session_config['session.cookie_httponly']) {
    $session_issues[] = "session.cookie_httponly غير مفعل";
    $security_score -= 8;
}

if (!$session_config['session.cookie_secure']) {
    $session_issues[] = "session.cookie_secure غير مفعل";
    $security_score -= 6;
}

if (!$session_config['session.use_strict_mode']) {
    $session_issues[] = "session.use_strict_mode غير مفعل";
    $security_score -= 5;
}

// 5. فحص File Upload Security
$upload_issues = [];
$upload_files = glob(__DIR__ . '/*upload*.php');
foreach ($upload_files as $file) {
    $content = file_get_contents($file);
    
    if (!preg_match('/PATHINFO_EXTENSION/', $content)) {
        $upload_issues[] = basename($file) . ": عدم فحص امتداد الملف";
        $security_score -= 10;
    }
    
    if (!preg_match('/mime_content_type|finfo_file/', $content)) {
        $upload_issues[] = basename($file) . ": عدم فحص نوع الملف";
        $security_score -= 8;
    }
}

// 6. فحص Input Validation
$validation_issues = [];
foreach ($files_to_check as $file) {
    $filepath = __DIR__ . '/' . $file;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        
        // فحص استخدام filter_var
        if (preg_match('/\$_(GET|POST|REQUEST)/', $content) && !preg_match('/filter_var|trim|strip_tags/', $content)) {
            $validation_issues[] = "$file: عدم تنظيف البيانات المدخلة";
            $security_score -= 4;
        }
    }
}

// 7. فحص Error Handling
$error_issues = [];
foreach ($files_to_check as $file) {
    $filepath = __DIR__ . '/' . $file;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        
        // فحص عرض أخطاء قاعدة البيانات
        if (preg_match('/echo.*\$db->error|\$mysqli->error/', $content)) {
            $error_issues[] = "$file: عرض أخطاء قاعدة البيانات للمستخدم";
            $security_score -= 6;
        }
        
        // فحص عرض معلومات النظام
        if (preg_match('/phpinfo|var_dump|print_r/', $content)) {
            $error_issues[] = "$file: عرض معلومات النظام";
            $security_score -= 8;
        }
    }
}

// 8. فحص Authentication & Authorization
$auth_issues = [];
foreach ($files_to_check as $file) {
    $filepath = __DIR__ . '/' . $file;
    if (file_exists($filepath)) {
        $content = file_get_contents($filepath);
        
        // فحص وجود فحص تسجيل الدخول
        if (!preg_match('/isLoggedIn|redirectIfNotLoggedIn|session.*user/', $content)) {
            $auth_issues[] = "$file: عدم فحص تسجيل الدخول";
            $security_score -= 7;
        }
    }
}

// 9. فحص Password Security
$password_issues = [];
$auth_file = __DIR__ . '/includes/auth.php';
if (file_exists($auth_file)) {
    $content = file_get_contents($auth_file);
    
    if (!preg_match('/password_hash/', $content)) {
        $password_issues[] = "عدم استخدام password_hash لتشفير كلمات المرور";
        $security_score -= 15;
    }
    
    if (!preg_match('/password_verify/', $content)) {
        $password_issues[] = "عدم استخدام password_verify للتحقق من كلمات المرور";
        $security_score -= 10;
    }
}

// 10. فحص Headers Security
$headers_issues = [];
$security_headers = [
    'X-Content-Type-Options' => false,
    'X-Frame-Options' => false,
    'X-XSS-Protection' => false,
    'Strict-Transport-Security' => false,
    'Content-Security-Policy' => false
];

// فحص الهيدر
$header_file = __DIR__ . '/includes/header.php';
if (file_exists($header_file)) {
    $content = file_get_contents($header_file);
    
    foreach ($security_headers as $header => $found) {
        if (preg_match('/' . preg_quote($header, '/') . '/', $content)) {
            $security_headers[$header] = true;
        }
    }
}

foreach ($security_headers as $header => $found) {
    if (!$found) {
        $headers_issues[] = "هيدر الأمان $header مفقود";
        $security_score -= 3;
    }
}

// تحديد مستوى الأمان
$security_level = 'خطير';
$security_color = 'danger';

if ($security_score >= 90) {
    $security_level = 'ممتاز';
    $security_color = 'success';
} elseif ($security_score >= 75) {
    $security_level = 'جيد';
    $security_color = 'primary';
} elseif ($security_score >= 60) {
    $security_level = 'متوسط';
    $security_color = 'warning';
} elseif ($security_score >= 40) {
    $security_level = 'ضعيف';
    $security_color = 'danger';
}

displayMessages();
?>

<link rel="stylesheet" href="assets/css/system-tools.css">

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card system-tools-card">
                <div class="card-header bg-<?php echo $security_color; ?> text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt"></i>
                        المراجعة الأمنية الشاملة للمشروع
                    </h4>
                </div>
                <div class="card-body">
                    <!-- النتيجة العامة -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-<?php echo $security_color; ?> text-white">
                                <div class="card-body text-center">
                                    <h2><?php echo $security_score; ?>%</h2>
                                    <p class="mb-0">النتيجة الأمنية</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-info text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo $security_level; ?></h3>
                                    <p class="mb-0">مستوى الأمان</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-secondary text-white">
                                <div class="card-body text-center">
                                    <h3><?php echo count($files_to_check); ?></h3>
                                    <p class="mb-0">ملفات تم فحصها</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- التبويبات -->
                    <ul class="nav nav-tabs" id="securityTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                                <i class="fas fa-chart-pie"></i> نظرة عامة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="sql-tab" data-bs-toggle="tab" data-bs-target="#sql" type="button" role="tab">
                                <i class="fas fa-database"></i> SQL Injection
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="xss-tab" data-bs-toggle="tab" data-bs-target="#xss" type="button" role="tab">
                                <i class="fas fa-code"></i> XSS
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="csrf-tab" data-bs-toggle="tab" data-bs-target="#csrf" type="button" role="tab">
                                <i class="fas fa-shield-alt"></i> CSRF
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="session-tab" data-bs-toggle="tab" data-bs-target="#session" type="button" role="tab">
                                <i class="fas fa-user-lock"></i> Session
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="fixes-tab" data-bs-toggle="tab" data-bs-target="#fixes" type="button" role="tab">
                                <i class="fas fa-tools"></i> الإصلاحات
                            </button>
                        </li>
                    </ul>

                    <div class="tab-content mt-4" id="securityTabContent">
                        <!-- تبويب النظرة العامة -->
                        <div class="tab-pane fade show active" id="overview" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-chart-pie text-primary"></i>
                                تحليل الوضع الأمني العام
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-danger text-white">
                                            <h6 class="mb-0">المشاكل الأمنية المكتشفة</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>SQL Injection:</span>
                                                <span class="badge bg-danger"><?php echo count($sql_injection_issues); ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>XSS:</span>
                                                <span class="badge bg-danger"><?php echo count($xss_issues); ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>CSRF:</span>
                                                <span class="badge bg-warning"><?php echo count($csrf_issues); ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Session:</span>
                                                <span class="badge bg-warning"><?php echo count($session_issues); ?></span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Headers:</span>
                                                <span class="badge bg-secondary"><?php echo count($headers_issues); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">الحماية المطبقة</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Prepared Statements:</span>
                                                <span class="badge bg-success">مطبق جزئياً</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Password Hashing:</span>
                                                <span class="badge bg-success">مطبق</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Authentication:</span>
                                                <span class="badge bg-success">مطبق</span>
                                            </div>
                                            <div class="d-flex justify-content-between mb-2">
                                                <span>Input Validation:</span>
                                                <span class="badge bg-warning">مطبق جزئياً</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <?php if ($security_score < 70): ?>
                            <div class="alert alert-danger mt-4">
                                <h6><i class="fas fa-exclamation-triangle"></i> تحذير أمني</h6>
                                <p>النظام يحتوي على مشاكل أمنية خطيرة تحتاج إلى إصلاح فوري. يُنصح بشدة بتطبيق الإصلاحات المقترحة.</p>
                            </div>
                            <?php elseif ($security_score < 85): ?>
                            <div class="alert alert-warning mt-4">
                                <h6><i class="fas fa-exclamation-triangle"></i> تحسينات مطلوبة</h6>
                                <p>النظام يحتاج إلى بعض التحسينات الأمنية لضمان الحماية الكاملة.</p>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-success mt-4">
                                <h6><i class="fas fa-check-circle"></i> أمان جيد</h6>
                                <p>النظام يتمتع بمستوى أمان جيد مع بعض التحسينات البسيطة المطلوبة.</p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب SQL Injection -->
                        <div class="tab-pane fade" id="sql" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-database text-danger"></i>
                                فحص SQL Injection
                            </h5>

                            <?php if (empty($sql_injection_issues)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                لم يتم العثور على مشاكل SQL Injection واضحة
                            </div>
                            <?php else: ?>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle"></i> مشاكل SQL Injection مكتشفة</h6>
                                <ul class="mb-0">
                                    <?php foreach ($sql_injection_issues as $issue): ?>
                                    <li><?php echo htmlspecialchars($issue); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">التوصيات لمنع SQL Injection</h6>
                                </div>
                                <div class="card-body">
                                    <ul>
                                        <li><strong>استخدم Prepared Statements دائماً:</strong> <code>$stmt = $db->prepare("SELECT * FROM users WHERE id = ?");</code></li>
                                        <li><strong>تجنب استخدام:</strong> <code>$db->query("SELECT * FROM users WHERE id = " . $_GET['id']);</code></li>
                                        <li><strong>استخدم bind_param:</strong> <code>$stmt->bind_param("i", $user_id);</code></li>
                                        <li><strong>تحقق من نوع البيانات:</strong> استخدم <code>intval()</code> للأرقام</li>
                                        <li><strong>استخدم whitelist للقيم المسموحة</strong></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب XSS -->
                        <div class="tab-pane fade" id="xss" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-code text-warning"></i>
                                فحص Cross-Site Scripting (XSS)
                            </h5>

                            <?php if (empty($xss_issues)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                لم يتم العثور على مشاكل XSS واضحة
                            </div>
                            <?php else: ?>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle"></i> مشاكل XSS مكتشفة</h6>
                                <ul class="mb-0">
                                    <?php foreach ($xss_issues as $issue): ?>
                                    <li><?php echo htmlspecialchars($issue); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <div class="card">
                                <div class="card-header bg-warning text-dark">
                                    <h6 class="mb-0">التوصيات لمنع XSS</h6>
                                </div>
                                <div class="card-body">
                                    <ul>
                                        <li><strong>استخدم htmlspecialchars دائماً:</strong> <code>echo htmlspecialchars($_POST['name'], ENT_QUOTES, 'UTF-8');</code></li>
                                        <li><strong>تجنب استخدام:</strong> <code>echo $_GET['data'];</code></li>
                                        <li><strong>استخدم Content Security Policy (CSP)</strong></li>
                                        <li><strong>تحقق من البيانات المدخلة:</strong> استخدم <code>filter_var()</code></li>
                                        <li><strong>استخدم template engines آمنة</strong></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب CSRF -->
                        <div class="tab-pane fade" id="csrf" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-shield-alt text-primary"></i>
                                فحص Cross-Site Request Forgery (CSRF)
                            </h5>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>نماذج محمية</h6>
                                            <h3 class="text-success"><?php echo $csrf_protected_files; ?></h3>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>نماذج غير محمية</h6>
                                            <h3 class="text-danger"><?php echo ($csrf_total_files - $csrf_protected_files); ?></h3>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <?php if (empty($csrf_issues)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                جميع النماذج محمية ضد CSRF
                            </div>
                            <?php else: ?>
                            <div class="alert alert-danger">
                                <h6><i class="fas fa-exclamation-triangle"></i> نماذج غير محمية ضد CSRF</h6>
                                <ul class="mb-0">
                                    <?php foreach ($csrf_issues as $issue): ?>
                                    <li><?php echo htmlspecialchars($issue); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">التوصيات لمنع CSRF</h6>
                                </div>
                                <div class="card-body">
                                    <ul>
                                        <li><strong>أضف CSRF token لكل نموذج:</strong> <code>&lt;input type="hidden" name="csrf_token" value="&lt;?php echo $_SESSION['csrf_token']; ?&gt;"&gt;</code></li>
                                        <li><strong>تحقق من التوكن:</strong> <code>if ($_POST['csrf_token'] !== $_SESSION['csrf_token']) { die('CSRF detected'); }</code></li>
                                        <li><strong>استخدم SameSite cookies</strong></li>
                                        <li><strong>تحقق من Referer header</strong></li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب Session -->
                        <div class="tab-pane fade" id="session" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-user-lock text-info"></i>
                                فحص أمان الجلسات
                            </h5>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>الإعداد</th>
                                            <th>القيمة الحالية</th>
                                            <th>الحالة</th>
                                            <th>التوصية</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>session.cookie_httponly</td>
                                            <td><?php echo $session_config['session.cookie_httponly'] ? 'مفعل' : 'غير مفعل'; ?></td>
                                            <td>
                                                <?php if ($session_config['session.cookie_httponly']): ?>
                                                    <span class="badge bg-success">آمن</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">غير آمن</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>يجب تفعيله</td>
                                        </tr>
                                        <tr>
                                            <td>session.cookie_secure</td>
                                            <td><?php echo $session_config['session.cookie_secure'] ? 'مفعل' : 'غير مفعل'; ?></td>
                                            <td>
                                                <?php if ($session_config['session.cookie_secure']): ?>
                                                    <span class="badge bg-success">آمن</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">تحذير</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>يجب تفعيله مع HTTPS</td>
                                        </tr>
                                        <tr>
                                            <td>session.use_strict_mode</td>
                                            <td><?php echo $session_config['session.use_strict_mode'] ? 'مفعل' : 'غير مفعل'; ?></td>
                                            <td>
                                                <?php if ($session_config['session.use_strict_mode']): ?>
                                                    <span class="badge bg-success">آمن</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning">تحذير</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>يجب تفعيله</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <?php if (!empty($session_issues)): ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> مشاكل أمان الجلسات</h6>
                                <ul class="mb-0">
                                    <?php foreach ($session_issues as $issue): ?>
                                    <li><?php echo htmlspecialchars($issue); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- تبويب الإصلاحات -->
                        <div class="tab-pane fade" id="fixes" role="tabpanel">
                            <h5 class="mb-3">
                                <i class="fas fa-tools text-success"></i>
                                تطبيق الإصلاحات الأمنية
                            </h5>

                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> إصلاحات متاحة</h6>
                                <p>يمكنك تطبيق الإصلاحات الأمنية التلقائية باستخدام الأدوات أدناه.</p>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <h6 class="mb-0">إصلاح أمان الجلسات</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>تطبيق إعدادات أمان الجلسات الموصى بها</p>
                                            <a href="fix_session_security.php" class="btn btn-primary" target="_blank">
                                                <i class="fas fa-shield-alt"></i> إصلاح الجلسات
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">إضافة CSRF Protection</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>إضافة حماية CSRF لجميع النماذج</p>
                                            <a href="add_csrf_protection.php" class="btn btn-success" target="_blank">
                                                <i class="fas fa-shield-alt"></i> إضافة CSRF
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-warning text-dark">
                                            <h6 class="mb-0">إضافة Security Headers</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>إضافة هيدرز الأمان المطلوبة</p>
                                            <a href="add_security_headers.php" class="btn btn-warning" target="_blank">
                                                <i class="fas fa-code"></i> إضافة Headers
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-header bg-info text-white">
                                            <h6 class="mb-0">تحسين Input Validation</h6>
                                        </div>
                                        <div class="card-body">
                                            <p>تحسين تنظيف وتحقق البيانات المدخلة</p>
                                            <a href="improve_input_validation.php" class="btn btn-info" target="_blank">
                                                <i class="fas fa-filter"></i> تحسين التحقق
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <a href="apply_all_security_fixes.php" class="btn btn-danger btn-lg" target="_blank">
                                    <i class="fas fa-shield-alt"></i> تطبيق جميع الإصلاحات الأمنية
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
