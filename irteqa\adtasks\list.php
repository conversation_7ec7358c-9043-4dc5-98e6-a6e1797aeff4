<style>
  .menu {
      list-style: none;
      padding: 0;
      margin: 0;
      margin-top: 20px;
    }

    .menu li {
      display: block;
      margin-bottom: 10px;
    }

    a div {
      margin: 5px;
    }

    .menu li a {
      display: flex;
      padding: 5px;
      background-color: #f4f4f4;
      color: #333;
      text-decoration: none;
      border-radius: 5px;
      margin-top: 5px;
      margin-left: 465px;
      font-size: 2em;
      flex-wrap: wrap;
      justify-content: center;
      border: 1px solid #dbdbdb;
    }

    .submenu {
      display: none;
      margin-left: 456px;
    }

    .menu li.active .submenu {
      display: block;
    }
    .list {
      margin-right: 205px;
      margin-left: 5px;
      position: absolute;
      left: 0px;
      right: 0px;

    }

    ul {
      padding: 0;
    }
    
</style>
<section class="list">
  <h1>قوائم الإعلانات</h1>
<ul class="menu">
  <li>
    <a href="#">Menu Item 1</a>
    <ul class="submenu">
    <li><?php include_once "list/cards.php"; ?></li>
  </ul>
  </li>
  <li>
    <a href="#">Menu Item 2</a>
    <ul class="submenu">
    <li><a href="#"><div></div><div></div><div></div><div></div></a></li>
    </ul>
  </li>
  <li>
    <a href="#">Menu Item 3</a>
    <ul class="submenu">
    <li><a href="#"><div></div><div></div><div></div><div></div></a></li>
    </ul>
  </li>
  <li>
    <a href="#">Menu Item 3</a>
    <ul class="submenu">
    <li><a href="#"><div></div><div></div><div></div><div></div></a></li>
    </ul>
  </li>
</ul>

<script>
  document.addEventListener("DOMContentLoaded", function() {
    const menuItems = document.querySelectorAll('.menu li');

    menuItems.forEach(function(item) {
      item.addEventListener('click', function() {
        const isActive = this.classList.contains('active');

        menuItems.forEach(function(menuItem) {
          menuItem.classList.remove('active');
        });

        if (!isActive) {
          this.classList.add('active');
        }
      });
    });
  });
</script>  
</section>