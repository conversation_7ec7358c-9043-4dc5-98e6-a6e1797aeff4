@page {
  size: auto;
  margin: 0mm;
}

* {
  box-sizing: border-box;
}

:root {
  --page-width: 8.5in;
  --page-height: 11in;
  --main-width: 4.25in;
  --container-font-h4: 15pt;
  --container-font-h5: 13pt;
  --container-font-h6: 11pt;
  --primary-bulletin-font: 11pt;
  --normal-font-size: 9pt;
  --decorator-horizontal-margin: 0.2in;
  --sidebar-horizontal-padding: 0.2in;
  /* XXX: using px for very good precision control */
  --decorator-outer-offset-top: 10px;
  --decorator-outer-offset-left: -5.5px;
  --decorator-border-width: 1px;
  --decorator-outer-dim: 9px;
  --decorator-border: 1px solid #ccc;
  --row-blocks-padding-top: 5pt;
  --date-block-width: 0.6in;
}

html {
  overflow: auto;
}

body {
  letter-spacing: 0.2pt;
  -webkit-print-color-adjust: exact;
  width: var(--page-width);
  height: var(--page-height);
  margin: 0;
  font-family: "Inter var", "Inter", sans-serif !important;
  font-weight: 300;
  /* overflow:hidden; */
  line-height: 1.3;
  color: #444;
  hyphens: auto;
}

.name-description-contact {
  width: var(--page-width);
  background: #edf2f8;
  height: 1.6in;
  float: left;
  padding: 0.15in 0.25in 0 0.25in;
}

.name-description-contact.as-page-header {
  height: 1in;
  margin-top: 0.36in;
}

h1, h2, h3, h4, h5 {
  margin: 0;
  color: #000;
}

.main {
  float: left;
  width: var(--main-width);
  padding: 0in 0.25in 0 0.25in;
}

#title {
  position: relative;
  line-height: 1.2;
  height: 1.2in;
  float: left;
  width: 50%;
}

#title.unheight {
  height: auto !important;
}

#title h1 {
  font-weight: 300;
  font-size: 18pt;
  line-height: 1.5;
}

.subtitle {
  font-size: 12pt;
}

.aboutme {
  font-size: 10pt;
}

/* containers */

.contact-details {
  width: 50%;
  float: left;
  font-size: 10pt;
  line-height: 1.8;
  font-family: 'Source Code Pro', monospace;
  height: 1.2in;
}

.contact-details.unheight {
  height: auto !important;
}

.contact-details ul li {
  list-style-type: none;
}

.container {
  font-size: var(--normal-font-size);
}

.tech-skill ul {
  font-size: 11pt;
  padding-left: 0;
}

.tech-skill ul li {
  letter-spacing: 1.5pt;
  font-weight: 600;
  list-style-type: none;
  position: relative;
  line-height: 2;
  font-size: var(--primary-bulletin-font);
}

.tech-skill ul li ul li {
  letter-spacing: normal;
  font-weight: normal;
  list-style-type: none;
  position: relative;
  line-height: 1.5;
  font-size: var(--normal-font-size);
}

.tech-skill ul li ul {
  padding-left: 15pt;
}

.tech-skill ul li ul li:before {
  content: '\2022';
  /* The unicode for • character */
  position: absolute;
  left: -0.8em;
  top: -0.3em;
  font-size: 15pt;
}

.container h3, .container h4, .container h5 {
  font-weight: 300;
  line-height: 1.5;
}

.container h3 {
  font-size: var(--container-font-h3);
}

.container h4 {
  font-size: var(--container-font-h4);
  line-height: 2;
  margin-top: 0.3in;
}

.container h5 {
  font-size: var(--container-font-h5);
}

.container button {
  background: #538cc6;
  border: 0px;
  padding: 4pt 6pt;
  margin: 0pt 2pt;
  border-radius: 3pt;
  color: white;
}

/* common code  */

.text-right {
  text-align: right;
}

ul.no-margin {
  margin: 0 !important;
}

/* copy psate */

.main-block {
  margin-top: 0.1in;
}

.main-block:not(.concise) .details div {
  margin: 0.18in 0 0.1in 0;
}

.main-block:not(.concise) .blocks:last-child .details div {
  margin-bottom: 0;
}

.main-block.concise .details div:not(.concise) {
  /* use padding to work around the fact that margin doesn't affect floated
     neighboring elements */
  padding: 0.05in 0 0.07in 0;
}

.blocks {
  display: flex;
  flex-flow: row nowrap;
}

.date {
  word-spacing: 0pt;
  padding-left: 0.07in;
  padding-right: 0.2in;
  font-size: 7pt;
  text-align: right;
  line-height: 1;
}

.date span {
  display: block;
}

.date span:nth-child(2)::before {
  position: relative;
  top: 1pt;
  right: 5.5pt;
  display: block;
  height: 10pt;
  content: '|';
}

.decorator {
  flex: 0 0 0;
  position: relative;
  width: 2pt;
  min-height: 100%;
  border-left: var(--decorator-border);
}

.decorator::before {
  position: absolute;
  top: var(--decorator-outer-offset-top);
  left: var(--decorator-outer-offset-left);
  content: ' ';
  display: block;
  width: var(--decorator-outer-dim);
  height: var(--decorator-outer-dim);
  border-radius: calc(var(--decorator-outer-dim) / 2);
  background-color: #fff;
}

.decorator::after {
  position: absolute;
  top: calc(var(--decorator-outer-offset-top) + var(--decorator-border-width));
  left: calc(var(--decorator-outer-offset-left) + var(--decorator-border-width));
  content: ' ';
  display: block;
  width: calc(var(--decorator-outer-dim) - (var(--decorator-border-width) * 2));
  height: calc(var(--decorator-outer-dim) - (var(--decorator-border-width) * 2));
  border-radius: calc((var(--decorator-outer-dim) - (var(--decorator-border-width) * 2)) / 2);
  background-color: #555;
}

.blocks:last-child .decorator {
  /* slightly shortens it */
  margin-bottom: 0.25in;
}

.details {
  margin-bottom: 0.2in;
  flex: 1 0 0;
  padding-left: var(--decorator-horizontal-margin);
  padding-top: calc(var(--row-blocks-padding-top) - 0.5pt) !important;
  /* not sure why but this is needed for better alignment */
}

.details header {
  color: #000;
}

.details h3 {
  letter-spacing: 1.5pt;
  font-weight: 600;
  margin-bottom: 0.1in;
}

.main-block:not(.concise) .details div {
  margin: 0.18in 0 0.1in 0;
}

.main-block:not(.concise) .blocks:last-child .details div {
  margin-bottom: 0;
}

.main-block.concise .details div:not(.concise) {
  /* use padding to work around the fact that margin doesn't affect floated
     neighboring elements */
  padding: 0.05in 0 0.07in 0;
}

.details .place {
  color: #538cc6;
  float: left;
}

.details .location {
  color: #538cc6;
  float: right;
}

.details div {
  clear: both;
}

.details .location::before {
  display: inline-block;
  position: relative;
  right: 3pt;
  top: 0.25pt;
  font-family: FontAwesome;
  font-weight: normal;
  font-style: normal;
  text-decoration: inherit;
  content: "\f041";
}

.details ul {
  margin-top: 0.3in;
  margin-bottom: 0;
}

.details ul li {
  line-height: 1.5;
}

footer {
  bottom: -213pt;
  text-align: right;
  position: absolute;
  right: 0;
  font-size: 8pt;
}