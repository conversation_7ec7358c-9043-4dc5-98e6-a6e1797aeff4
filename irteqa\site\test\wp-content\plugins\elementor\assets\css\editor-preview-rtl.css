/*! elementor - v3.11.5 - 14-03-2023 */
.e-con .elementor-empty-view {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: var(--min-height, 100px);
}
.e-con .elementor-empty-view .elementor-first-add {
  width: auto;
  height: auto;
  inset: 0px;
}
.e-con .elementor-widget-placeholder {
  --size: 10px;
  --margin-start: calc( -1 * var( --size ) );
  --margin-end: calc( -2 * var( --size ) );
  flex-shrink: 0;
  align-self: stretch;
  z-index: 1;
  pointer-events: none;
}
.e-con.e-con--row > .elementor-widget-placeholder, .e-con.e-con--row > .e-con-inner > .elementor-widget-placeholder {
  position: relative;
  z-index: 9999;
  width: var(--size);
  -webkit-margin-start: var(--margin-start);
          margin-inline-start: var(--margin-start);
  height: auto;
  min-height: 100%;
  animation-name: dnd-placeholder-widget-vertical;
  animation-fill-mode: both;
}
.e-con.e-con--row[data-nesting-level]:not([data-nesting-level="0"]) > .elementor-widget-placeholder, .e-con.e-con--row[data-nesting-level]:not([data-nesting-level="0"]) > .e-con-inner > .elementor-widget-placeholder {
  -webkit-margin-end: 0;
          margin-inline-end: 0;
  -webkit-margin-start: calc(var(--margin-start) + var(--margin-end));
          margin-inline-start: calc(var(--margin-start) + var(--margin-end));
}
.e-con.e-con--row[data-nesting-level]:not([data-nesting-level="0"]) > :not(.elementor-element) + .elementor-widget-placeholder, .e-con.e-con--row[data-nesting-level]:not([data-nesting-level="0"]) > .e-con-inner > :not(.elementor-element) + .elementor-widget-placeholder {
  -webkit-margin-end: var(--margin-end);
          margin-inline-end: var(--margin-end);
  -webkit-margin-start: var(--margin-start);
          margin-inline-start: var(--margin-start);
}
.e-con.e-con--column > .elementor-widget-placeholder, .e-con.e-con--column > .e-con-inner > .elementor-widget-placeholder {
  height: var(--size);
  -webkit-margin-before: var(--margin-start);
          margin-block-start: var(--margin-start);
  -webkit-margin-after: var(--margin-end);
          margin-block-end: var(--margin-end);
  animation-name: dnd-placeholder-widget-horizontal;
}
.e-con.e-con--column > .elementor-widget-placeholder:nth-last-child(2) {
  -webkit-margin-before: calc(2 * var(--margin-start));
          margin-block-start: calc(2 * var(--margin-start));
  --margin-end: 0;
}
.e-con.e-con--column > .e-con-inner > .elementor-widget-placeholder:last-child {
  --margin-end: 0;
}
.e-con .elementor-sortable-helper {
  height: 84px;
  width: 125px;
  z-index: -1;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting {
  position: relative;
  background-color: #556068;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting:hover, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting:hover, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting:hover {
  background-color: #495157;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-add, .e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-edit + .elementor-editor-element-remove, .e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting:first-child::before, .e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting:last-child::after, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-add, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-edit + .elementor-editor-element-remove, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting:first-child::before, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting:last-child::after, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-add, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting.elementor-editor-element-edit + .elementor-editor-element-remove, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting:first-child::before, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting:last-child::after {
  display: none;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-setting:not(.elementor-editor-element-edit), .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-setting:not(.elementor-editor-element-edit), .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-setting:not(.elementor-editor-element-edit) {
  -webkit-margin-start: -25px;
          margin-inline-start: -25px;
  z-index: -1;
  transition: 0.3s all;
  will-change: margin-inline-start;
}
.e-con > .e-con > .elementor-element-overlay > .elementor-editor-element-settings:hover > :is(.elementor-editor-element-duplicate, .elementor-editor-element-remove), .e-con-inner > .e-con > .elementor-element-overlay > .elementor-editor-element-settings:hover > :is(.elementor-editor-element-duplicate, .elementor-editor-element-remove), .elementor-widget .e-con > .elementor-element-overlay > .elementor-editor-element-settings:hover > :is(.elementor-editor-element-duplicate, .elementor-editor-element-remove) {
  -webkit-margin-start: 0;
          margin-inline-start: 0;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-settings, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-settings, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-settings {
  inset: initial;
  transform: none;
  inset-inline-start: 0;
  top: 0;
  border-radius: 0;
  border-end-end-radius: 3px;
  height: auto;
  background-color: #556068;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-settings:hover, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-settings:hover, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-settings:hover {
  background-color: #495157;
}
.e-con > .e-con > .elementor-element-overlay .elementor-editor-element-settings i.eicon-handle::before, .e-con-inner > .e-con > .elementor-element-overlay .elementor-editor-element-settings i.eicon-handle::before, .elementor-widget .e-con > .elementor-element-overlay .elementor-editor-element-settings i.eicon-handle::before {
  content: "\e9b4";
  font-size: 20px;
  display: block;
  padding: 2px;
}

@keyframes dnd-placeholder-widget-vertical {
  0% {
    transform-origin: 0 50%;
    transform: translateX(50%) scaleX(0);
    opacity: 0;
  }
  100% {
    transform-origin: 0 50%;
    transform: translateX(50%) scaleX(1);
    opacity: 0.9;
  }
}
@keyframes dnd-placeholder-widget-horizontal {
  0% {
    transform-origin: 50% 0;
    transform: scaleY(0);
    opacity: 0;
  }
  100% {
    transform-origin: 50% 0;
    transform: scaleY(1);
    opacity: 0.9;
  }
}
.elementor-control-unit-1 {
  width: 27px;
}

.elementor-control-unit-2 {
  width: 54px;
}

.elementor-control-unit-3 {
  width: 81px;
}

.elementor-control-unit-4 {
  width: 108px;
}

.elementor-control-unit-5 {
  max-width: 400px;
  width: 52%;
}

.elementor-tags-list {
  display: none;
  position: absolute;
  width: 260px;
  max-height: 300px;
  overflow: auto;
  padding-bottom: 5px;
  background-color: #fff;
  border: 1px solid #a4afb7;
  border-radius: 3px;
  z-index: 10000;
}
.elementor-tags-list__group-title {
  color: #495157;
  font-weight: bold;
  font-size: 12px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
}
.elementor-tags-list__group-title .eicon-info-circle {
  padding-left: 5px;
  color: #71d7f7;
  font-size: 14px;
}
.elementor-tags-list__item {
  font-size: 10px;
  padding: 6px 15px;
  cursor: pointer;
}
.elementor-tags-list__item:before {
  content: ">";
  font-size: 8px;
  padding-left: 5px;
}
.elementor-tags-list__item:hover {
  background-color: #e6e9ec;
}
.elementor-tags-list__teaser {
  border-top: 2px solid #d5dadf;
  padding-top: 4px;
  margin-top: 4px;
}
.elementor-tags-list__teaser-title {
  color: #6d7882;
}
.elementor-tags-list__teaser-text {
  padding: 2px 15px 8px;
  line-height: 1.5;
  font-size: 12px;
}
.elementor-tags-list__teaser-link {
  color: #93003c;
  text-decoration: underline;
  font-style: italic;
  font-weight: bold;
}

.elementor-dynamic-cover {
  display: flex;
  align-items: center;
  width: 100%;
  height: 27px;
  box-sizing: border-box;
}
.elementor-dynamic-cover__title {
  padding: 0 8px;
  flex-grow: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.elementor-dynamic-cover__settings, .elementor-dynamic-cover__remove {
  color: #c2cbd2;
  cursor: pointer;
  transition: all 0.3s;
}
.elementor-dynamic-cover__settings:hover, .elementor-dynamic-cover__remove:hover {
  color: #a4afb7;
}
.elementor-control-type-wysiwyg .elementor-dynamic-cover {
  margin-top: 10px;
}

.elementor-tag-settings-popup {
  position: absolute;
  width: 260px;
  background-color: #e6e9ec;
  box-shadow: 1px 1px 10px rgba(0, 0, 0, 0.3);
  z-index: 1;
}
.elementor-tag-settings-popup:before {
  content: "";
  position: absolute;
  top: -20px;
  right: 5px;
  border: 10px solid transparent;
  border-bottom-color: #fff;
}
.elementor-tag-settings-popup .elementor-control-type-section:first-child {
  margin: 0;
}

.elementor-tag-controls-stack-empty {
  background-color: #fff;
  padding: 10px;
  font-size: 13px;
  text-align: center;
}

.elementor-control-dynamic-switcher {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  height: 27px;
  cursor: pointer;
  border: 1px solid #d5dadf;
  border-radius: 3px;
  background: #fff;
}
.elementor-control-dynamic-switcher.e-control-tool {
  height: 20px;
  border: 0;
}
.elementor-control-dynamic-switcher-wrapper {
  display: flex;
}
.elementor-control-dynamic-switcher .eicon-database {
  font-size: 12px;
}
.elementor-control-dynamic-value .elementor-control-tag-area,
.elementor-control-dynamic-value .elementor-control-dynamic-switcher,
.elementor-control-dynamic-value .e-global__popover-toggle {
  display: none;
}

.elementor-panel-box {
  margin-top: 10px;
  background-color: #fff;
}

.elementor-panel-box-content {
  padding: 20px 20px 10px;
}

.elementor-button {
  display: inline-block;
}

.elementor-panel-alert {
  background-color: #fcfcfc;
  padding: 15px;
  border-right: 3px solid transparent;
  position: relative;
  font-size: 12px;
  font-weight: 300;
  font-style: italic;
  line-height: 1.5;
  text-align: right;
  border-radius: 3px 0 0 3px;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.07);
}
.elementor-panel-alert a {
  color: inherit;
}
.elementor-panel-alert.elementor-panel-alert-info {
  border-color: #71d7f7;
  background-color: #f3fcff;
}
.elementor-panel-alert.elementor-panel-alert-success {
  border-color: #39b54a;
}
.elementor-panel-alert.elementor-panel-alert-warning {
  border-color: #fcb92c;
}
.elementor-panel-alert.elementor-panel-alert-danger {
  border-color: #d72b3f;
}

.elementor-label {
  display: inline;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
  text-transform: capitalize;
}
.elementor-label.elementor-label-default {
  background-color: #a4afb7;
}
.elementor-label.elementor-label-info {
  background-color: #5bc0de;
}
.elementor-label.elementor-label-success {
  background-color: #5cb85c;
}
.elementor-label.elementor-label-warning {
  background-color: #f0ad4e;
}
.elementor-label.elementor-label-danger {
  background-color: #d9534f;
}

.elementor-descriptor, .elementor-control-field-description {
  font-size: 11px;
  font-style: italic;
  line-height: 1.4;
  color: #a4afb7;
}

.elementor-controls-popover {
  display: none;
  position: absolute;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
  border-radius: 3px;
  left: 0;
  right: 0;
  margin: -4px auto 5px;
  padding-top: 15px;
  width: 90%;
  z-index: 10000;
  background-color: #fff;
}
.elementor-controls-popover:before {
  content: "";
  position: absolute;
  top: -16px;
  left: 22px;
  border: 8px solid transparent;
  border-bottom-color: #fff;
}
.elementor-controls-popover div.elementor-control {
  background-color: transparent;
}
.elementor-controls-popover div.elementor-control:before {
  content: none;
}

#elementor-controls .pojo-widget-button-collapse {
  display: none;
}

#elementor-panel-global .elementor-nerd-box .elementor-nerd-box-icon {
  margin-top: 20px;
}

.elementor-control {
  --control-title-size: 12px;
  background-color: #fff;
  position: relative;
  padding: 0 20px 15px;
}
.elementor-control a {
  font-weight: 500;
  text-decoration: none;
  border-bottom: 1px dotted transparent;
  transition: all ease-in-out 0.3s;
}
.elementor-control a:hover {
  border-bottom-color: inherit;
}
.elementor-control a.elementor-responsive-switcher {
  border-bottom: 0;
}
.elementor-control .elementor-control-content {
  display: flex;
  flex-direction: column;
}
.elementor-control .elementor-control-title {
  font-size: var(--control-title-size);
  line-height: 1;
  margin-left: 5px;
}
.elementor-control .elementor-control-spinner {
  display: flex;
  align-items: center;
}
.elementor-control.elementor-control-type-divider {
  padding: 0;
  background-color: transparent;
}
.elementor-control.elementor-control-type-divider .elementor-control-content {
  border-width: 0;
  border-color: #e6e9ec;
  border-style: solid;
  border-top-width: 1px;
  background-color: #ffffff;
  height: 15px;
}
.elementor-control.elementor-control-separator-default:not(.elementor-control-type-divider).elementor-control-wp {
  margin-top: 15px;
}
.elementor-control.elementor-control-separator-default:not(.elementor-control-type-divider).elementor-control-wp:before {
  content: "";
  height: 1px;
  display: block;
  margin-bottom: 15px;
  background-color: transparent;
}
.elementor-control.elementor-control-separator-before {
  padding-top: 15px;
}
.elementor-control.elementor-control-separator-before:before {
  content: "";
  position: absolute;
  height: 1px;
  width: 100%;
  top: 0;
  left: 0;
  background-color: #e6e9ec;
}
.elementor-control.elementor-control-separator-after:after {
  content: "";
  position: absolute;
  height: 1px;
  width: 100%;
  bottom: 0;
  left: 0;
  background-color: #e6e9ec;
}
.elementor-control.elementor-control-separator-after + .elementor-control-type-tabs + .elementor-control-separator-default, .elementor-control.elementor-control-separator-after:not(.elementor-hidden-control) + .elementor-control-separator-default {
  padding-top: 15px;
}
.elementor-control.elementor-control-deprecated {
  color: #b01b1b;
}
.elementor-control.elementor-control-deprecated .elementor-control-field-description {
  color: #b01b1b;
}
.elementor-control.elementor-control-hidden-label > * > .elementor-control-title, .elementor-control.elementor-control-hidden-label > * > * > .elementor-control-title {
  display: none;
}
.elementor-control.elementor-hidden-control {
  display: none;
}
.elementor-control.elementor-control-type-heading .elementor-control-title {
  font-weight: bold;
  margin: 0;
}
body:not(.elementor-device-widescreen) .elementor-control.elementor-control-responsive-widescreen {
  display: none;
}
body:not(.elementor-device-desktop) .elementor-control.elementor-control-responsive-desktop {
  display: none;
}
body:not(.elementor-device-laptop) .elementor-control.elementor-control-responsive-laptop {
  display: none;
}
body:not(.elementor-device-tablet_extra) .elementor-control.elementor-control-responsive-tablet_extra {
  display: none;
}
body:not(.elementor-device-tablet) .elementor-control.elementor-control-responsive-tablet {
  display: none;
}
body:not(.elementor-device-mobile_extra) .elementor-control.elementor-control-responsive-mobile_extra {
  display: none;
}
body:not(.elementor-device-mobile) .elementor-control.elementor-control-responsive-mobile {
  display: none;
}
.elementor-control-custom_css_pro .elementor-nerd-box-message, .elementor-control-custom_attributes_pro .elementor-nerd-box-message {
  margin-top: 5px;
}
.elementor-control.elementor-control-custom_css_title {
  font-size: 12px;
}

.elementor-control.elementor-open .elementor-panel-heading-toggle .eicon:before,
.elementor-panel-scheme-item.elementor-open .elementor-panel-heading-toggle .eicon:before {
  content: "\e92a";
}
.elementor-control:not(.elementor-open) .elementor-panel-heading-toggle .eicon:before,
.elementor-panel-scheme-item:not(.elementor-open) .elementor-panel-heading-toggle .eicon:before {
  content: "\e909";
}

.elementor-panel-heading {
  display: table;
  table-layout: fixed;
  height: 40px;
  padding: 0 20px;
  width: 100%;
  border-bottom: 1px solid #e6e9ec;
  cursor: pointer;
}
.elementor-panel-heading > * {
  display: table-cell;
  vertical-align: middle;
}
.elementor-panel-heading-toggle {
  width: 20px;
  color: #495157;
}
.elementor-panel-heading-title {
  color: #495157;
  font-weight: bold;
}

.elementor-control-wp {
  line-height: 1.5;
}
.elementor-control-wp p {
  margin: 15px 0;
}

.elementor-control-field {
  display: flex;
  align-items: center;
}

.elementor-label-block > .elementor-control-content > .elementor-control-field {
  flex-wrap: wrap;
}
.elementor-label-block > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  width: 100%;
  max-width: 100%;
  margin-top: 10px;
}
.elementor-label-block > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper > .elementor-choices label {
  width: auto;
  flex: 1 1 27px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.elementor-label-block.elementor-control-hidden-label:not(.elementor-control-dynamic) > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-top: 0;
}
.elementor-label-block.elementor-control-hidden-label.elementor-label-block > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-top: 0;
}

.elementor-label-inline > .elementor-control-content > .elementor-control-field > .elementor-control-title {
  flex-shrink: 0;
  max-width: 60%;
}
.elementor-label-inline > .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-right: auto;
}

.elementor-control-field-description {
  margin-top: 10px;
}

.elementor-group-control-attachment_alert .elementor-control-field-description {
  margin-top: 0;
}

.elementor-required {
  color: #b01b1b;
}

.elementor-control-start-end .eicon-h-align-left, .elementor-control-start-end .eicon-h-align-right {
  transform: rotate(180deg);
}

.elementor-update-preview {
  margin: 15px 15px 0;
  display: flex;
  align-items: center;
}

.elementor-update-preview-button-wrapper {
  flex-grow: 1;
  text-align: left;
}

.elementor-update-preview-button {
  padding: 8px 15px;
  text-transform: uppercase;
}

.elementor-control-direction-ltr input,
.elementor-control-direction-ltr textarea {
  direction: ltr;
}
.elementor-control-direction-rtl input,
.elementor-control-direction-rtl textarea {
  direction: rtl;
}

.elementor-control-responsive-switchers {
  --selected-option: 0;
  --pointer-position: var(--selected-option);
  position: relative;
  width: 2.5em;
  height: 2.5em;
  margin: calc( -2.5em + var(--control-title-size)) 0;
  margin-left: 5px;
}
.elementor-control-responsive-switchers__holder {
  position: absolute;
  width: 100%;
  top: 0;
  background-color: #ffffff;
  border-radius: 3px;
  transition: 0.15s;
  border: 1px solid transparent;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open {
  z-index: 11000;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-responsive-switcher:hover {
  color: #71d7f7;
}
.elementor-control-responsive-switchers.elementor-responsive-switchers-open .elementor-control-responsive-switchers__holder {
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}

.elementor-responsive-switcher {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  cursor: pointer;
  height: 0;
  transform: scale(0);
  opacity: 0;
  transition: 0.15s;
  font-size: 12px;
}

a.elementor-responsive-switcher {
  color: #a4afb7;
}
a.elementor-responsive-switcher:hover {
  color: #71d7f7;
}

.elementor-device-widescreen .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-widescreen .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-widescreen {
  color: #71d7f7;
}

.elementor-device-desktop .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-desktop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-desktop {
  color: #71d7f7;
}

.elementor-device-laptop .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-laptop .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-laptop {
  color: #71d7f7;
}

.elementor-device-tablet_extra .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet_extra {
  color: #71d7f7;
}

.elementor-device-tablet .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-tablet .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-tablet {
  color: #71d7f7;
}

.elementor-device-mobile_extra .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile_extra .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile_extra {
  color: #71d7f7;
}

.elementor-device-mobile .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  height: 2.5em;
  transform: scale(1);
  opacity: 1;
}
.elementor-device-mobile .elementor-responsive-switchers-open:not(:hover) .elementor-responsive-switcher.elementor-responsive-switcher-mobile {
  color: #71d7f7;
}

.e-units-wrapper {
  position: relative;
  margin-right: auto;
}
.e-units-wrapper .e-units-switcher {
  color: #6d7882;
  cursor: pointer;
  text-transform: uppercase;
  font-size: 9px;
  padding: 0.5em;
  margin: -0.5em 0;
  transition: all ease-in-out 0.15s;
}
.e-units-wrapper .e-units-switcher:hover {
  color: #71d7f7;
  background-color: #f1f3f5;
  border-radius: 3px;
}
.e-units-wrapper .e-units-switcher:not([data-selected=custom]) i.eicon-edit {
  display: none;
}
.e-units-wrapper .e-units-switcher[data-selected=custom] span {
  display: none;
}
.e-units-wrapper .e-units-switcher i.eicon-angle-right {
  transform: rotate(90deg);
}

.e-units-choices input {
  display: none;
}
.e-units-choices input:checked + label {
  color: #71d7f7;
}
.e-units-choices label {
  cursor: pointer;
  display: block;
}
.e-units-choices {
  display: none;
  overflow: hidden;
  max-height: 0;
  position: absolute;
  top: -0.8em;
  right: -0.5em;
  width: 2.5em;
  text-align: center;
  background-color: #ffffff;
  border-radius: 3px;
  border: 1px solid transparent;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  animation-timing-function: linear;
  animation-delay: 0s;
  animation-fill-mode: forwards;
  z-index: 11000;
}
.e-units-choices .elementor-units-choices-label {
  display: flex;
  align-items: center;
  height: 3em;
  justify-content: center;
  text-transform: uppercase;
  font-size: 9px;
  transition: 0.15s;
}
.e-units-choices .elementor-units-choices-label:hover {
  color: #71d7f7;
}
.e-units-choices .elementor-units-choices-label:not([data-choose=custom]) i {
  display: none;
}
.e-units-choices .elementor-units-choices-label[data-choose=custom] span {
  display: none;
}
.e-units-choices.e-units-choices-open {
  display: block;
  animation-duration: 1s;
  animation-name: e-units-choices-open;
}

.e-units-custom input {
  font-family: monospace;
  font-size: 0.85em;
}

@keyframes e-units-choices-open {
  from {
    max-height: 0;
  }
  to {
    max-height: 100vh;
  }
}
.elementor-control-type-button .elementor-control-input-wrapper {
  text-align: left;
}
.elementor-control-type-button .elementor-button {
  width: auto;
  height: 26px;
}
.elementor-control-type-button .elementor-button.elementor-button-center {
  display: block;
  margin: 0 auto;
}

.elementor-control-type-choose.elementor-label-block .elementor-choices {
  width: 100%;
}

.elementor-choices {
  display: flex;
  height: 27px;
  line-height: 27px;
  text-align: center;
  border-spacing: 1px;
  border-radius: 3px;
  overflow: hidden;
}
.elementor-choices .elementor-choices-label {
  border-top: 1px solid #d5dadf;
  border-bottom: 1px solid #d5dadf;
  border-right: 1px solid #d5dadf;
  border-left: none;
  font-size: 12px;
  transition: all 0.5s;
  cursor: pointer;
  overflow: hidden;
}
.elementor-choices .elementor-choices-label:nth-child(2) {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}
.elementor-choices .elementor-choices-label:last-child {
  border-left: 1px solid #d5dadf;
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.elementor-choices input {
  display: none;
}
.elementor-choices input.e-choose-placeholder + .elementor-choices-label {
  color: #ffffff;
  background-color: #c2cbd2;
  border-color: #c2cbd2;
}
.elementor-choices input:checked + .elementor-choices-label {
  color: #fff;
  background-color: #a4afb7;
  border-color: #a4afb7;
}

.elementor-label-inline .elementor-choices {
  justify-content: flex-end;
}

.rtl .elementor-control-type-choose[class*=elementor-control-text_align] .elementor-choices, .rtl .elementor-control-type-choose[class*=elementor-control-align] .elementor-choices, .rtl .elementor-control-type-choose[class*=elementor-control-position] .elementor-choices {
  flex-direction: row-reverse;
}
.rtl .elementor-control-type-choose[class*=elementor-control-text_align] .elementor-choices .elementor-choices-label:nth-child(2), .rtl .elementor-control-type-choose[class*=elementor-control-align] .elementor-choices .elementor-choices-label:nth-child(2), .rtl .elementor-control-type-choose[class*=elementor-control-position] .elementor-choices .elementor-choices-label:nth-child(2) {
  border-left: 1px solid #d5dadf;
  border-radius: 3px 0 0 3px;
}
.rtl .elementor-control-type-choose[class*=elementor-control-text_align] .elementor-choices .elementor-choices-label:last-child, .rtl .elementor-control-type-choose[class*=elementor-control-align] .elementor-choices .elementor-choices-label:last-child, .rtl .elementor-control-type-choose[class*=elementor-control-position] .elementor-choices .elementor-choices-label:last-child {
  border-left: none;
  border-radius: 0 3px 3px 0;
}

.elementor-control-type-color.e-control-global .e-global__popover-toggle ~ .pickr {
  border-radius: 3px 0 0 3px;
  flex-shrink: 0;
}
.elementor-control-type-color .elementor-control-title {
  flex-grow: 1;
}
.elementor-control-type-color .elementor-control-input-wrapper {
  display: flex;
  justify-content: flex-end;
}
.elementor-control-type-color .elementor-control-dynamic-switcher {
  border-right-width: 0;
  border-radius: 3px 0 0 3px;
}
.elementor-control-type-color.elementor-control-dynamic .pickr {
  border-radius: 0 3px 3px 0;
}

.elementor-group-control-css-filter .elementor-slider {
  height: 6px;
  box-shadow: 0 0 1px 1px inset rgba(0, 0, 0, 0.2);
}
.elementor-group-control-css-filter .elementor-control-content > .elementor-control-field > .elementor-control-input-wrapper {
  margin-top: 0;
  margin-bottom: 5px;
}

.elementor-group-control-blur .elementor-slider {
  background: url("../images/blur.png");
  background-size: cover;
  background-position: center;
}

.elementor-group-control-contrast .elementor-slider {
  background: url("../images/contrast.png");
  background-size: 100% 100%;
}

.elementor-group-control-hue .elementor-slider {
  background-image: linear-gradient(to right, red, orange, yellow, greenyellow, limegreen, deepskyblue, blue, darkviolet 95%);
}

.elementor-group-control-saturate .elementor-slider {
  background-image: linear-gradient(to right, gray, red);
}

.elementor-group-control-brightness .elementor-slider {
  background-image: linear-gradient(to right, black, white);
}

.elementor-control-type-dimensions .elementor-control-dimensions {
  display: flex;
}
.elementor-control-type-dimensions li {
  flex: 1;
  transition: flex-grow 0.3s ease-in-out;
}
.elementor-control-type-dimensions li input,
.elementor-control-type-dimensions li .elementor-link-dimensions {
  display: block;
  text-align: center;
  width: 100%;
  height: 27px;
}
.elementor-control-type-dimensions li input {
  border-right: none;
  border-radius: 0;
  padding: 3px;
}
.elementor-control-type-dimensions li input:focus {
  border-right: 1px solid #a4afb7;
  margin-right: -1px;
  width: calc(100% + 1px);
}
.elementor-control-type-dimensions li input:focus + .elementor-control-dimension-label {
  color: #a4afb7;
}
.elementor-control-type-dimensions li .elementor-link-dimensions {
  border: 1px solid #d5dadf;
  border-right: none;
  background-color: #fff;
  padding: 0;
  outline: none;
  border-radius: 3px 0 0 3px;
  cursor: pointer;
}
.elementor-control-type-dimensions li:first-child input {
  border-right: 1px solid #d5dadf;
  border-radius: 0 3px 3px 0;
}
.elementor-control-type-dimensions li:first-child input:focus {
  border-color: #a4afb7;
  margin-right: 0;
  width: 100%;
}
.elementor-control-type-dimensions li:last-child {
  max-width: 27px;
}
.elementor-control-type-dimensions.e-units-custom li.elementor-control-dimension:focus-within {
  flex: 2.5;
}
.elementor-control-type-dimensions .elementor-control-dimension-label {
  display: block;
  text-align: center;
  color: #d5dadf;
  font-size: 9px;
  text-transform: uppercase;
  padding-top: 5px;
}
.elementor-control-type-dimensions .elementor-link-dimensions.unlinked {
  background-color: #fff;
}
.elementor-control-type-dimensions .elementor-link-dimensions.unlinked .elementor-linked {
  display: none;
}
.elementor-control-type-dimensions .elementor-link-dimensions:not(.unlinked) {
  background-color: #a4afb7;
  border-color: #a4afb7;
}
.elementor-control-type-dimensions .elementor-link-dimensions:not(.unlinked) .elementor-unlinked {
  display: none;
}
.elementor-control-type-dimensions .elementor-link-dimensions .elementor-linked {
  color: #fff;
}

.elementor-control-type-icons .elementor-control-media__preview > * {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.elementor-control-type-icons .elementor-control-media__preview i {
  font-size: 70px;
  color: #6d7882;
}
.elementor-control-type-icons .elementor-control-media__preview svg {
  height: 75%;
}
.elementor-control-type-icons .elementor-control-icons--inline__svg i.eicon-upload {
  font-size: 15px;
}

.elementor-control-type-gallery .elementor-control-media__content {
  border: 1px solid #d5dadf;
  border-radius: 3px;
}
.elementor-control-type-gallery .elementor-control-gallery-status {
  font-size: 12px;
  height: 27px;
  padding-right: 10px;
  border-bottom: 1px solid #d5dadf;
  display: flex;
}
.elementor-control-type-gallery .elementor-control-gallery-status > * {
  display: flex;
  align-items: center;
}
.elementor-control-type-gallery .elementor-control-gallery-status-title {
  flex-grow: 1;
}
.elementor-control-type-gallery .elementor-control-gallery-content {
  position: relative;
  overflow: hidden;
}
.elementor-control-type-gallery .elementor-control-gallery-content:not(:hover) .elementor-control-gallery-edit {
  opacity: 0;
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnails {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(48px, 1fr));
  grid-gap: 10px;
  cursor: pointer;
  padding: 10px;
}
.elementor-control-type-gallery .elementor-control-gallery-thumbnail {
  width: 48px;
  height: 48px;
  background-size: cover;
  background-position: 50% 50%;
}
.elementor-control-type-gallery .elementor-control-gallery-edit {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(109, 120, 130, 0.3);
  padding: 10px;
  font-size: 11px;
  transition: all 0.3s ease-in-out;
  pointer-events: none;
  cursor: pointer;
  opacity: 1;
}
.elementor-control-type-gallery .elementor-control-gallery-edit span {
  position: absolute;
  top: 10px;
  left: 10px;
  width: 21px;
  height: 21px;
  background-color: rgba(255, 255, 255, 0.9);
  color: #a4afb7;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
.elementor-control-type-gallery .elementor-control-gallery-edit span i {
  font-size: 11px;
  padding: 5px;
}
.elementor-control-type-gallery .elementor-control-gallery-add {
  width: 48px;
  height: 48px;
  color: #fff;
  background-color: #d5dadf;
  font-size: 14px;
  border-radius: 0;
}
.elementor-control-type-gallery .elementor-control-gallery-add:hover {
  background-color: #c2cbd2;
}
.elementor-control-type-gallery .elementor-control-gallery-add i {
  margin: 0;
  color: #a4afb7;
}
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-clear,
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-thumbnails,
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-edit {
  display: none;
}
.elementor-control-type-gallery.elementor-gallery-empty .elementor-control-gallery-content {
  padding: 10px;
}
.elementor-control-type-gallery.elementor-gallery-has-images .elementor-control-gallery-add {
  display: none;
}
.elementor-control-type-gallery.elementor-control-dynamic .elementor-control-gallery-clear {
  border-right: 1px solid #d5dadf;
}
.elementor-control-type-gallery .elementor-control-gallery-clear {
  cursor: pointer;
  justify-content: center;
}
.elementor-control-type-gallery .elementor-control-gallery-clear:hover {
  color: #b01b1b;
}
.elementor-control-type-gallery .elementor-control-dynamic-switcher {
  border: none;
  border-bottom: 1px solid #d5dadf;
  border-right: 1px solid #d5dadf;
  border-radius: 3px 0 0 0;
}

.e-global__popover {
  width: 288px;
  z-index: 1;
  font-size: 12px;
  padding-right: 10px;
}
.e-global__popover-toggle {
  border: 1px solid #d5dadf;
  border-left: 0;
  border-radius: 0 3px 3px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.e-global__popover-toggle--active i {
  color: #71d7f7;
}
.e-global__popover-container {
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.3);
  border-radius: 3px;
  background-color: #fff;
  padding-bottom: 5px;
}
.e-global__popover-title {
  padding: 15px 20px;
  font-weight: 500;
  color: #6d7882;
  border-bottom: 1px solid #e6e9ec;
  display: flex;
}
.e-global__popover-title > i {
  margin-left: 5px;
  color: #d5dadf;
}
.e-global__popover-title-text {
  flex-grow: 1;
}
.e-global__popover-info {
  margin-left: 5px;
  display: inline-block;
}
.e-global__popover-info-tooltip {
  width: 270px;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.9);
  color: #fff;
  padding: 20px;
  border-radius: 3px;
}
.e-global__popover-info-tooltip:after {
  content: "";
  position: absolute;
  bottom: -17px;
  right: 16px;
  border: 10px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
}
.e-global__popover-info i {
  color: #d5dadf;
  font-size: 13px;
}
.e-global__preview-items-container {
  max-height: 260px;
  overflow-y: auto;
  margin-top: 5px;
}
.e-global__preview-items-container::-webkit-scrollbar {
  width: 7px;
}
.e-global__preview-items-container::-webkit-scrollbar-thumb {
  background-color: #c2cbd2;
  border-radius: 10px;
}
.e-global__manage-button {
  font-weight: 500;
  cursor: pointer;
}
.e-global__manage-button:hover i {
  color: #4ab7f4;
}
.e-global__manage-button i {
  color: #6d7882;
}
.e-global__typography {
  padding: 5px 35px 5px 10px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.e-global__typography.e-global__preview-item--selected:before {
  font-family: "eicons";
  font-size: 13px;
  content: "\e90e";
  position: absolute;
  transform: translateY(-50%);
  top: 50%;
  left: 12px;
}
.e-global__color {
  margin: 5px auto;
  padding: 5px 20px;
  display: flex;
  align-items: center;
}
.e-global__color:first-child {
  margin-top: 5px;
}
.e-global__color:last-child {
  margin-bottom: 10px;
}
.e-global__color-preview-container {
  height: 20px;
  width: 20px;
  border-radius: 3px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-left: 10px;
  flex-shrink: 0;
  position: relative;
}
.e-global__color-preview-color, .e-global__color-preview-transparent-bg {
  border-radius: 3px;
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.e-global__color-preview-transparent-bg {
  background-image: linear-gradient(45deg, #ddd 25%, transparent 0, transparent 75%, #ddd 0, #ddd), linear-gradient(45deg, #ddd 25%, transparent 0, transparent 75%, #ddd 0, #ddd);
  background-size: 12px 12px;
  background-position: 0 0, calc( 12px / 2 ) calc( 12px / 2 );
}
.e-global__color-title {
  font-weight: 500;
  color: #6d7882;
  flex-grow: 1;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  padding-left: 10px;
}
.e-global__color-hex {
  font-size: 10px;
  font-weight: 500;
  color: #c2cbd2;
}
.e-global__color .pcr-button {
  background-color: #fff;
}
.e-global__color.e-global__preview-item--selected .e-global__color-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #ffffff;
}
.e-global__color.e-global__preview-item--selected .e-global__color-preview-container:before {
  font-family: "eicons";
  font-size: 13px;
  content: "\e90e";
  text-shadow: 0px 0px 1px #000;
  z-index: 1;
}
.e-global__preview-item {
  cursor: pointer;
  position: relative;
}
.e-global__preview-item:hover {
  background-color: #f1f3f5;
}
.e-global__preview-item:hover .e-global__color-hex {
  color: #a4afb7;
}
.e-global__confirm-add .dialog-buttons-wrapper > .dialog-button.dialog-confirm-ok {
  color: #39b54a;
}
.e-global__confirm-delete i {
  color: #b01b1b;
}
.e-global__confirm-message-text i {
  color: #fcb92c;
}
.e-global__confirm-input-wrapper {
  display: flex;
  align-items: center;
  border: 1px solid #a4afb7;
  border-radius: 3px;
  margin-top: 10px;
  padding: 2px;
}
.e-global__confirm-input-wrapper input {
  font-family: Roboto, Arial, sans-serif;
  font-weight: 500;
  font-size: 12px;
  padding: 2px;
  border: 0;
}

.e-control-global .elementor-control-input-wrapper {
  display: flex;
  justify-content: flex-end;
  max-width: 135px;
  width: 100%;
}
.e-control-global.elementor-control .elementor-control-input-wrapper {
  direction: rtl;
}
.e-control-global .elementor-control-spinner {
  margin-left: 4px;
}

.elementor-control-type-hidden {
  display: none !important;
}

.elementor-control-type-icon .select2-selection__rendered .eicon {
  margin-right: 3px;
}

.elementor-control-type-image_dimensions .elementor-control-field-description {
  margin: 0 0 15px;
  line-height: 1.4;
}
.elementor-control-type-image_dimensions .elementor-control-input-wrapper {
  overflow: hidden;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-field {
  width: 65px;
  float: right;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-field input:focus + .elementor-image-dimensions-field-description {
  color: #a4afb7;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-separator {
  width: 20px;
  text-align: center;
  float: right;
  padding-top: 4px;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-field-description {
  display: block;
  text-transform: uppercase;
  margin-top: 5px;
  color: #d5dadf;
  font-size: 9px;
  text-align: center;
}
.elementor-control-type-image_dimensions .elementor-image-dimensions-apply-button {
  float: left;
  height: 27px;
  width: 65px;
}

.elementor-control-media.e-media-empty .elementor-control-media-area .elementor-control-media__remove, .elementor-control-media.e-media-empty .elementor-control-media-area .elementor-control-media__content__remove {
  display: none;
}
.elementor-control-media.e-media-empty-placeholder .e-control-image-size {
  display: none;
}
.elementor-control-media:not(.e-media-empty) .elementor-control-media__content__upload-button {
  display: none;
}
.elementor-control-media .eicon-plus-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #ffffff;
  font-size: 20px;
}
.elementor-control-media__content__upload-button {
  background-color: #d5dadf;
}
.elementor-control-media__preview {
  height: 100%;
  background-size: cover;
  background-position: center;
  padding-bottom: 42.8571%;
}
.elementor-control-media-area {
  background-image: linear-gradient(45deg, #ddd 25%, transparent 0, transparent 75%, #ddd 0, #ddd), linear-gradient(45deg, #ddd 25%, transparent 0, transparent 75%, #ddd 0, #ddd);
  background-size: 16px 16px;
  background-position: 0 0, calc( 16px / 2 ) calc( 16px / 2 );
  background-color: #f6f6f6;
  aspect-ratio: 21/9;
}
.elementor-control-media-area:not(:hover) .elementor-control-media__remove {
  display: none;
}
.elementor-control-media-area .eicon-video-camera {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7);
  font-size: 21px;
}
.elementor-control-media .elementor-control-media__content {
  aspect-ratio: 21/9;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}
.elementor-control-media .elementor-control-media__content:hover:after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.2);
  pointer-events: none;
}
.elementor-control-media .elementor-control-media__content:not(:hover) .elementor-control-media__tools {
  bottom: -30px;
}
.elementor-control-media__content {
  transition: all 0.2s ease-in-out;
}
.elementor-control-media__tools {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 27px;
  transition: all 0.2s ease-in-out;
}
.elementor-control-media__tools > *:not(:first-child) {
  margin-right: 1px;
}
.elementor-control-media__tool {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
  color: #ffffff;
  background-color: rgba(109, 120, 130, 0.85);
  font-size: 11px;
  transition: background 0.3s;
}
.elementor-control-media__tool:hover {
  background-color: rgba(109, 120, 130, 0.95);
}
.elementor-control-media__remove {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
}
.elementor-control-media__content__remove {
  position: absolute;
  z-index: 1;
  top: 10px;
  left: 10px;
  width: 20px;
  height: 20px;
  font-size: 11px;
  color: #ffffff;
  background-color: rgba(109, 120, 130, 0.85);
  border-radius: 3px;
  box-shadow: 0 0 7px 0 rgba(0, 0, 0, 0.1);
  transition: background 0.3s;
}
.elementor-control-media__content__remove:hover {
  background-color: rgba(109, 120, 130, 0.95);
  box-shadow: 0 0 5px 0 rgba(0, 0, 0, 0.4);
}
.elementor-control-media.e-media-empty .elementor-control-file-area {
  display: none;
}
.elementor-control-media__file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 10px;
  border: 1px solid #d5dadf;
}
.elementor-control-media__file__content {
  padding-right: 5px;
  font-size: 12px;
}
.elementor-control-media__file__content__label {
  color: #a4afb7;
}
.elementor-control-media__file__content__info {
  display: flex;
  align-items: center;
  font-weight: 500;
}
.elementor-control-media__file__content__info__icon {
  margin-left: 5px;
}
.elementor-control-media__file__controls {
  display: flex;
  border-right: 1px solid #d5dadf;
}
.elementor-control-media__file__controls__upload-button, .elementor-control-media__file__controls__remove {
  width: 27px;
  height: 27px;
  cursor: pointer;
  align-items: center;
}
.elementor-control-media__file__controls__upload-button {
  display: flex;
  justify-content: center;
}
.elementor-control-media__file__controls__remove {
  border-left: 1px solid #d5dadf;
}
.elementor-control-media:not(.e-media-empty) .elementor-control-media__file__content__label {
  display: none;
}
.elementor-control-media.e-media-empty .elementor-control-media__file__content__info {
  display: none;
}
.elementor-control-media.e-media-empty .elementor-control-media__file__controls__remove {
  display: none;
}
.elementor-control-media .elementor-control-dynamic-switcher {
  border: none;
  border-radius: 0;
  background-color: rgba(109, 120, 130, 0.85);
  color: #ffffff;
}
.elementor-control-media .elementor-control-dynamic-switcher:hover {
  background-color: rgba(109, 120, 130, 0.95);
}
.elementor-control-media .e-control-image-size {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-top: 20px;
}
.elementor-control-media .e-control-image-size .elementor-control-input-wrapper {
  margin-right: auto;
}

.elementor-control-type-media.elementor-control-dynamic-value .elementor-control-input-wrapper {
  border: none;
}

.elementor-control:not(.elementor-control-type-icons) .elementor-control-media__preview {
  background-color: #d5dadf;
}

.elementor-control-type-number.elementor-control-dynamic input {
  border-radius: 0 3px 3px 0;
  border-left: none;
}
.elementor-control-type-number.elementor-control-dynamic .elementor-control-dynamic-switcher {
  border-radius: 3px 0 0 3px;
}

.elementor-control-type-order .elementor-control-oreder-wrapper {
  display: flex;
}
.elementor-control-type-order input {
  display: none;
}
.elementor-control-type-order input:checked + .elementor-control-order-label {
  transform: scale(1, -1);
}
.elementor-control-type-order select {
  border-radius: 3px 0 0 3px;
}
.elementor-control-type-order select:not(:focus) ~ .elementor-control-order-label {
  border-color: #d5dadf;
}
.elementor-control-type-order select:focus ~ .elementor-control-order-label {
  border-color: #a4afb7;
}
.elementor-control-type-order .elementor-control-order-label {
  position: relative;
  padding: 0;
  width: 40px;
  border: 1px solid;
  font-size: 10px;
  border-radius: 0 3px 3px 0;
  margin-left: -3px;
  background-color: #fff;
  cursor: pointer;
}
.elementor-control-type-order .elementor-control-order-label i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.elementor-control-type-popover_toggle input {
  display: none;
}
.elementor-control-type-popover_toggle label {
  cursor: pointer;
}
.elementor-control-type-popover_toggle .elementor-control-input-wrapper {
  direction: ltr;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle:checked + .elementor-control-popover-toggle-toggle-label {
  color: #71d7f7;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle:not(:checked) ~ .elementor-control-popover-toggle-reset-label {
  display: none;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-reset-label {
  color: #c2cbd2;
  margin-left: 5px;
}
.elementor-control-type-popover_toggle .elementor-control-popover-toggle-toggle-label {
  height: 27px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  border: 1px solid #d5dadf;
}

.elementor-controls-popover.e-controls-popover--typography {
  padding-top: 0;
}

.e-control-global .elementor-control-popover-toggle-toggle-label {
  border-radius: 3px 0 0 3px;
  flex-shrink: 0;
}

.elementor-control-type-repeater .elementor-control:not(.elementor-control-type-tab) {
  padding-right: 10px;
  padding-left: 10px;
  padding-bottom: 10px;
}
.elementor-control-type-repeater.elementor-repeater-has-minimum-rows .elementor-repeater-tool-remove {
  display: none;
}
.elementor-control-type-repeater .elementor-repeater-fields {
  margin: 10px 0;
}
.elementor-control-type-repeater .elementor-repeater-row-controls {
  border: 1px solid #d5dadf;
  border-top-width: 0;
  padding-top: 15px;
}
.elementor-control-type-repeater .elementor-repeater-row-controls:not(.editable) {
  display: none;
}
.elementor-control-type-repeater .elementor-repeater-row-tools {
  display: table;
  table-layout: fixed;
  width: 100%;
  color: #556068;
  background-color: #d5dadf;
  height: 40px;
  border-spacing: 1px;
  transition: all 0.5s;
}
.elementor-control-type-repeater .elementor-repeater-row-tools:hover {
  background-color: #a4afb7;
}
.elementor-control-type-repeater .elementor-repeater-row-tools > div {
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
}
.elementor-control-type-repeater .elementor-repeater-row-tools > div:not(.elementor-repeater-row-handle-sortable) {
  background-color: #fff;
}
.elementor-control-type-repeater .elementor-repeater-row-tools > div:hover {
  opacity: 0.95;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title {
  text-align: right;
  padding: 0 10px;
  font-size: 12px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title .eicon,
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title i,
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title svg {
  margin-left: 5px;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-item-title img[src$=svg] {
  width: 1em;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-tool {
  width: 40px;
}
.elementor-control-type-repeater .elementor-repeater-row-tools .elementor-repeater-row-handle-sortable {
  cursor: move;
  width: 10px;
  color: #fff;
}
.elementor-control-type-repeater .elementor-button-wrapper {
  text-align: center;
  padding-top: 5px;
}

.elementor-control-type-section {
  margin-top: 10px;
  padding: 0;
}
.elementor-control-type-section.elementor-open {
  padding-bottom: 15px;
}
.elementor-control-type-section + .elementor-control:not(.elementor-control-type-section):before {
  display: none;
}

.elementor-control-type-select .elementor-control-input-wrapper {
  position: relative;
}
.elementor-control-type-select .elementor-control-input-wrapper select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  font-size: 12px;
  font-family: inherit;
  font-weight: inherit;
  font-style: inherit;
  text-transform: inherit;
  letter-spacing: inherit;
  line-height: inherit;
  flex-basis: 100%;
  padding-right: 5px;
  padding-left: 20px;
  cursor: pointer;
}
.elementor-control-type-select .elementor-control-input-wrapper select.e-select-placeholder {
  color: #a4afb7;
}
.elementor-control-type-select .elementor-control-input-wrapper option.e-option-placeholder {
  display: none;
}
.elementor-control-type-select .elementor-control-input-wrapper:after {
  font-family: eicons;
  content: "\e8ad";
  font-size: 12px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 5px;
  pointer-events: none;
}

.elementor-shadow-box .elementor-shadow-slider {
  margin-top: 10px;
}
.elementor-shadow-box .elementor-color-picker-wrapper .elementor-control-title {
  flex-grow: 1;
}

.elementor-control-type-slider.elementor-control-dynamic input {
  border-radius: 0 3px 3px 0;
}
.elementor-control-type-slider .elementor-control-unit-2 {
  width: 21%;
}
.elementor-control-type-slider.elementor-control-type-slider--multiple .elementor-control-input-wrapper {
  display: block;
}
.elementor-control-type-slider--multiple {
  padding-bottom: 40px;
}
.elementor-control-type-slider--multiple .elementor-slider {
  margin-top: 12px;
  width: 98%;
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle {
  border-radius: 0;
  width: 10px;
  transform: translateY(calc(50% - 14px)) translateX(-4px);
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle:after {
  content: "";
  position: absolute;
  top: 2px;
  height: 12px;
  width: 11px;
  transform: rotate(45deg);
  background-color: #fff;
  border-radius: 3px;
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle-lower:after {
  left: 5px;
  box-shadow: 2px -2px 3px 0px rgba(0, 0, 0, 0.1);
}
.elementor-control-type-slider--handles-range .elementor-slider .noUi-handle-upper:after {
  right: 5px;
  box-shadow: -2px 2px 3px 0px rgba(0, 0, 0, 0.1);
}
.elementor-control-type-slider .elementor-control-dynamic-switcher {
  border-right-width: 0;
  border-radius: 3px 0 0 3px;
}
.elementor-control-type-slider .elementor-control-input-wrapper {
  display: flex;
  align-items: center;
}
.elementor-control-type-slider .elementor-dynamic-cover {
  margin-top: 10px;
}
.elementor-control-type-slider.e-units-custom .elementor-slider {
  display: none;
}
.elementor-control-type-slider.e-units-custom .elementor-slider-input {
  width: 100%;
  margin: 0;
  transition: none;
}

.elementor-slider {
  flex-grow: 1;
  height: 4px;
  background-color: #d5dadf;
  border-radius: 5px;
  position: relative;
  cursor: pointer;
}
.elementor-slider-input {
  width: 21%;
  min-width: 54px;
  margin-right: 12px;
  transition: width 0.3s ease-in-out;
}
.elementor-slider__extra {
  position: relative;
}
.elementor-slider__labels {
  display: flex;
  justify-content: space-between;
}
.elementor-slider__label {
  font-size: 9px;
  color: #c2cbd2;
}
.elementor-slider__scales {
  position: absolute;
  display: flex;
  justify-content: space-evenly;
  width: 100%;
  margin-top: 4px;
}
.elementor-slider__scale {
  width: 1px;
  height: 21px;
  background-color: #a4afb7;
}
.elementor-slider .noUi-handle {
  height: 16px;
  width: 16px;
  background-color: #fff;
  left: 0;
  transform: translateY(calc(50% - 14px)) translateX(-8px);
  position: absolute;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  border-radius: 50%;
}
.elementor-slider .noUi-connects {
  position: absolute;
  width: 100%;
  height: 4px;
}
.elementor-slider .noUi-connect {
  position: absolute;
  z-index: 0;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  will-change: transform;
  transform-origin: 0 0;
  background-color: #a4afb7;
}
.elementor-slider .noUi-tooltip {
  position: absolute;
  top: calc(100% + 5px);
  left: calc(50% - 4px);
  transform: translateX(-50%);
  font-size: 10px;
}

.elementor-control-type-structure .elementor-control-field {
  display: initial;
}
.elementor-control-type-structure .elementor-control-structure-preset {
  padding: 3px;
  border-radius: 3px;
  display: inline-block;
  cursor: pointer;
  height: 50px;
}
.elementor-control-type-structure .elementor-control-structure-preset svg {
  height: 100%;
}
.elementor-control-type-structure .elementor-control-structure-preset path {
  fill: #e6e9ec;
}
.elementor-control-type-structure .elementor-control-structure-reset {
  padding: 15px 20px 0;
  font-size: 11px;
  cursor: pointer;
  color: #a4afb7;
  border-top: 1px solid #e6e9ec;
  margin: 0 -20px;
}
.elementor-control-type-structure .elementor-control-structure-title {
  margin: 10px -20px 0;
}
.elementor-control-type-structure .elementor-control-structure-title:before {
  height: 10px;
  box-shadow: inset 0 2px 4px rgba(127, 127, 127, 0.1);
}
.elementor-control-type-structure .elementor-control-structure-presets {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.elementor-control-type-structure .elementor-control-structure-presets input {
  display: none;
}
.elementor-control-type-structure .elementor-control-structure-presets input:checked + .elementor-control-structure-preset path {
  fill: #a4afb7;
}
.elementor-control-type-structure .elementor-control-structure-preset-wrapper {
  margin-bottom: 15px;
}
.elementor-control-type-structure .elementor-control-structure-preset-title {
  text-align: center;
  padding-top: 5px;
  font-style: italic;
  font-size: 11px;
  color: #a4afb7;
}

.elementor-control-type-switcher .elementor-control-input-wrapper {
  text-align: left;
}
.elementor-control-type-switcher .elementor-switch {
  position: relative;
  display: inline-block;
  vertical-align: top;
  height: 20px;
  background-color: white;
  border-radius: 18px;
  cursor: pointer;
}
.elementor-control-type-switcher .elementor-switch-input {
  display: none;
}
.elementor-control-type-switcher .elementor-switch-label {
  position: relative;
  display: block;
  height: inherit;
  font-size: 7px;
  text-transform: uppercase;
  background: #eceeef;
  border-radius: inherit;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.12), inset 0 0 2px rgba(0, 0, 0, 0.15);
  transition: 0.15s ease-out;
  transition-property: opacity, background;
}
.elementor-control-type-switcher .elementor-switch-label:before, .elementor-control-type-switcher .elementor-switch-label:after {
  position: absolute;
  top: 0;
  width: 50%;
  text-align: center;
  line-height: 20px;
  transition: inherit;
}
.elementor-control-type-switcher .elementor-switch-label:before {
  content: attr(data-off);
  right: 3px;
  color: #a4afb7;
  text-shadow: 0 1px rgba(255, 255, 255, 0.5);
}
.elementor-control-type-switcher .elementor-switch-label:after {
  content: attr(data-on);
  left: 3px;
  color: white;
  text-shadow: 0 1px rgba(0, 0, 0, 0.2);
  opacity: 0;
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-label {
  background: #71d7f7;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15), inset 0 0 3px rgba(0, 0, 0, 0.2);
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-label:before {
  opacity: 0;
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-label:after {
  opacity: 1;
}
.elementor-control-type-switcher .elementor-switch-handle {
  position: absolute;
  top: 1px;
  left: 1px;
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 10px;
  transition: left 0.15s ease-out;
}
.elementor-control-type-switcher .elementor-switch-input:checked ~ .elementor-switch-handle {
  left: initial;
  right: 1px;
  box-shadow: -1px 1px 5px rgba(0, 0, 0, 0.2);
}

.elementor-control-type-tabs {
  font-size: 0.8em;
  text-transform: uppercase;
  display: flex;
}

.elementor-control-type-tab {
  text-align: center;
  width: 100%;
  padding: 0;
  line-height: 25px;
  border-top: 1px solid #d5dadf;
  border-bottom: 1px solid #d5dadf;
  border-left: 1px solid #d5dadf;
  cursor: pointer;
}
.elementor-control-type-tab:first-child {
  border-right: 1px solid #d5dadf;
  border-top-right-radius: 3px;
  border-bottom-right-radius: 3px;
}
.elementor-control-type-tab:last-child {
  border-top-left-radius: 3px;
  border-bottom-left-radius: 3px;
}
.elementor-control-type-tab.elementor-tab-active {
  background-color: #a4afb7;
  border-color: #a4afb7;
  color: #ffffff;
}

.elementor-tab-close {
  display: none !important;
}

.elementor-control-type-textarea.elementor-control-dynamic-value .elementor-control-dynamic-switcher,
.elementor-control-type-code.elementor-control-dynamic-value .elementor-control-dynamic-switcher {
  border-radius: 3px 0 0 3px;
  border-right-width: 0;
}
.elementor-control-type-textarea .elementor-control-dynamic-switcher,
.elementor-control-type-code .elementor-control-dynamic-switcher {
  border-radius: 3px 0;
}
.elementor-control-type-textarea:not(.elementor-control-dynamic-value) .elementor-control-dynamic-switcher,
.elementor-control-type-code:not(.elementor-control-dynamic-value) .elementor-control-dynamic-switcher {
  position: absolute;
  top: 0;
  z-index: 1;
  left: 0;
}
.elementor-control-type-textarea .elementor-control-input-wrapper,
.elementor-control-type-code .elementor-control-input-wrapper {
  position: relative;
}
.elementor-control-type-textarea textarea,
.elementor-control-type-code textarea {
  display: block;
  font-family: inherit;
}
.elementor-control-type-textarea textarea:focus + .elementor-control-dynamic-switcher,
.elementor-control-type-code textarea:focus + .elementor-control-dynamic-switcher {
  display: none;
}
.elementor-control-type-textarea pre:focus-within + .elementor-control-dynamic-switcher,
.elementor-control-type-code pre:focus-within + .elementor-control-dynamic-switcher {
  display: none;
}

.elementor-control-type-code .elementor-control-dynamic-switcher {
  border-radius: 0 3px;
}
.elementor-control-type-code:not(.elementor-control-dynamic-value) .elementor-control-dynamic-switcher {
  right: 0;
}

.elementor-control-type-url.elementor-control-dynamic .elementor-input {
  border-radius: 3px 0 0 3px;
}
.elementor-control-type-url .elementor-control-field {
  position: relative;
}
.elementor-control-type-url:not(.elementor-control-dynamic) .elementor-control-url-more, .elementor-control-type-url.elementor-control-dynamic-value .elementor-control-url-more {
  border-radius: 0 3px 3px 0;
}
.elementor-control-type-url .elementor-control-input-wrapper {
  display: flex;
  flex-direction: row-reverse;
}
.elementor-control-type-url .elementor-control-url-more {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 1px solid #d5dadf;
  border-left: none;
  cursor: pointer;
}
.elementor-control-type-url .elementor-control-url-more i {
  font-size: 12px;
}
.elementor-control-type-url .elementor-control-url-more-options {
  display: none;
  padding-top: 10px;
}
.elementor-control-type-url .elementor-control-url-more-options .elementor-control-field-description {
  margin-top: 10px;
}
.elementor-control-type-url .elementor-control-url-option {
  display: flex;
  align-items: center;
}
.elementor-control-type-url .elementor-control-url-option:not(:last-child) {
  padding-bottom: 10px;
}
.elementor-control-type-url .elementor-control-url-option input,
.elementor-control-type-url .elementor-control-url-option label {
  cursor: pointer;
}
.elementor-control-type-url .elementor-control-url-option-input {
  margin: 0;
  padding: 0;
  border-radius: 2px;
  margin-left: 5px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  outline: none;
  content: none;
  height: 15px;
  width: 15px;
  border: 1px solid #d5dadf;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.elementor-control-type-url .elementor-control-url-option-input:checked {
  background: #71d7f7;
  border: none;
}
.elementor-control-type-url .elementor-control-url-option-input:checked:before {
  display: block;
  content: "";
  width: 4px;
  height: 7px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
.elementor-control-type-url .elementor-control-url-option label {
  font-size: 12px;
}
.elementor-control-type-url .elementor-control-url-external-hide .elementor-control-url-more {
  display: none;
}
.elementor-control-type-url .elementor-control-url-external-show .elementor-input,
.elementor-control-type-url .elementor-control-url-external-show .elementor-dynamic-cover {
  border-radius: 3px 0 0 3px;
}
.elementor-control-type-url .elementor-control-url-autocomplete-spinner {
  display: none;
  position: absolute;
  top: 5px;
  right: 0px;
  width: 10px;
  height: 10px;
  font-size: 10px;
  color: #D4D4D4;
}
.elementor-control-type-url .elementor-control-url__custom-attributes {
  margin-top: 5px;
}
.elementor-control-type-url .elementor-control-url__custom-attributes label {
  font-size: 12px;
}
.elementor-control-type-url .elementor-control-url__custom-attributes input {
  width: 100%;
  margin-top: 10px;
}
.elementor-control-type-url .elementor-input {
  direction: ltr;
}
.elementor-control-type-url .elementor-input:not(:focus) + .elementor-control-url-more {
  border-color: #d5dadf;
}
.elementor-control-type-url .elementor-input:focus ~ div {
  border-color: #a4afb7;
}
.elementor-control-type-url .elementor-control-dynamic-switcher {
  border-left: none;
  border-radius: 0 3px 3px 0;
}

.elementor-autocomplete-menu {
  position: absolute;
  background: #fff;
  color: #495157;
  border: 1px solid #a4afb7;
  margin: 0;
  list-style: none;
  padding: 4px 0;
  height: auto;
  width: 100%;
  min-width: 260px;
  max-width: 300px;
  max-height: 200px;
  overflow-y: auto;
  border-radius: 3px;
  transition: all 0.3s ease-in-out;
  cursor: default;
  z-index: 1;
}
.elementor-autocomplete-menu .ui-menu-item {
  display: flex;
  justify-content: space-between;
  align-self: baseline;
  padding: 5px 8px;
  font-size: 12px;
  width: 100%;
  line-height: 1.2;
  cursor: pointer;
}
.elementor-autocomplete-menu .ui-menu-item.ui-state-hover, .elementor-autocomplete-menu .ui-menu-item.ui-state-active, .elementor-autocomplete-menu .ui-menu-item.ui-state-focus {
  background: #e6e9ec;
}
.elementor-autocomplete-menu .elementor-autocomplete-item-info {
  font-size: 10px;
  padding-top: 2px;
}

.elementor-control-type-wp_widget .widget-inside {
  display: block;
}
.elementor-control-type-wp_widget .quicktags-toolbar input {
  width: auto;
}

.elementor-control-type-wysiwyg * {
  box-sizing: content-box;
}
.elementor-control-type-wysiwyg .wp-editor-container {
  border: 1px solid #e6e9ec;
}
.elementor-control-type-wysiwyg .wp-editor-tabs {
  border: 1px solid #d5dadf;
  border-bottom: none;
  border-radius: 3px 3px 0 0;
}
.elementor-control-type-wysiwyg #insert-media-button {
  height: initial;
  line-height: 24px;
  font-size: 10px;
  color: #6d7882;
  border-color: #d5dadf;
  background-color: #e6e9ec;
  min-height: initial;
}
.elementor-control-type-wysiwyg .ed_button {
  height: 22px;
  width: initial;
}
.elementor-control-type-wysiwyg .wp-media-buttons-icon {
  height: 14px;
  margin: 0;
}
.elementor-control-type-wysiwyg .wp-media-buttons-icon:before {
  font-size: 14px;
}
.elementor-control-type-wysiwyg .wp-switch-editor {
  position: static;
  border: none;
  margin: 0;
  color: #6d7882;
  font-size: 10px;
  padding: 3px 9px 4px;
}
.elementor-control-type-wysiwyg .switch-html {
  border: solid #d5dadf;
  border-width: 0 1px;
}
.elementor-control-type-wysiwyg .html-active .switch-tmce {
  background-color: transparent;
}
.elementor-control-type-wysiwyg .html-active .switch-html {
  background-color: #e6e9ec;
}
.elementor-control-type-wysiwyg .tmce-active .switch-tmce {
  background-color: #e6e9ec;
}
.elementor-control-type-wysiwyg .tmce-active .switch-html {
  background-color: transparent;
}
.elementor-control-type-wysiwyg .mce-toolbar-grp, .elementor-control-type-wysiwyg .quicktags-toolbar {
  background-color: #e6e9ec;
}
.elementor-control-type-wysiwyg .mce-toolbar-grp > div {
  padding: 0 3px;
}
.elementor-control-type-wysiwyg .elementor-wp-editor {
  box-sizing: border-box;
}
.elementor-control-type-wysiwyg .mce-ico {
  color: #6d7882;
  font-size: 16px;
}
.elementor-control-type-wysiwyg .mce-btn {
  margin-right: 0;
  margin-left: 0;
}
.elementor-control-type-wysiwyg .mce-btn:hover, .elementor-control-type-wysiwyg .mce-btn:active, .elementor-control-type-wysiwyg .mce-btn.mce-active {
  border-color: #d5dadf;
}
.elementor-control-type-wysiwyg .mce-path {
  padding: 5px 10px;
}
.elementor-control-type-wysiwyg .mce-path-item {
  font-size: 12px;
  color: #6d7882;
}
.elementor-control-type-wysiwyg .elementor-control-dynamic-switcher {
  border: none;
}

.elementor-control-type-text .elementor-control-dynamic-switcher {
  border-right-width: 0;
  border-radius: 3px 0 0 3px;
}
.elementor-control-type-text.elementor-control-dynamic input {
  border-radius: 0 3px 3px 0;
}

.elementor-control-type-date_time .elementor-control-dynamic-switcher {
  border-inline-start-width: 0;
  border-start-start-radius: 0;
  border-start-end-radius: 3px;
  border-end-end-radius: 3px;
  border-end-start-radius: 0;
}
.elementor-control-type-date_time.elementor-control-dynamic input {
  border-start-start-radius: 3px;
  border-start-end-radius: 0;
  border-end-end-radius: 0;
  border-end-start-radius: 3px;
}

.ui-resizable-n {
  top: -5px;
}
.ui-resizable-e {
  right: -5px;
}
.ui-resizable-s {
  bottom: -5px;
}
.ui-resizable-w {
  left: -5px;
}
.ui-resizable-ne {
  top: -5px;
  right: -5px;
}
.ui-resizable-nw {
  top: -5px;
  left: -5px;
}
.ui-resizable-se {
  bottom: -5px;
  right: -5px;
}
.ui-resizable-sw {
  bottom: -5px;
  left: -5px;
}
.ui-resizable-n, .ui-resizable-s {
  left: 0;
  height: 10px;
  width: 100%;
  cursor: ns-resize;
}
.ui-resizable-e, .ui-resizable-w {
  top: 0;
  height: 100%;
  width: 10px;
  cursor: ew-resize;
}
.ui-resizable-ne, .ui-resizable-nw, .ui-resizable-se, .ui-resizable-sw {
  height: 15px;
  width: 15px;
}
.ui-resizable-nw, .ui-resizable-se {
  cursor: nwse-resize;
}
.ui-resizable-ne, .ui-resizable-sw {
  cursor: nesw-resize;
}
.ui-resizable-handle {
  position: absolute;
}
.ui-resizable-resizing {
  pointer-events: none;
}

@keyframes placeholder-section {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    height: 40px;
    opacity: 0.9;
  }
}
@keyframes placeholder-widget {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    height: 10px;
    opacity: 0.9;
  }
}
@keyframes section-outline {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
body.elementor-editor-active .elementor.elementor-edit-mode.layout-section .entry-content {
  overflow: visible;
}
body.elementor-editor-active .elementor.elementor-edit-mode .article {
  overflow: visible;
}
body.elementor-editor-active .elementor.elementor-edit-mode .elementor-element-overlay .elementor-editor-element-settings {
  clear: unset;
}
body.elementor-editor-active .elementor.elementor-edit-mode .elementor-element.elementor-section {
  overflow: visible;
}

.elementor-edit-area .animated {
  animation-fill-mode: none !important;
}
.elementor-edit-area ul.elementor-editor-element-settings {
  word-break: normal;
  padding: 0;
}
.elementor-edit-area .gallery {
  opacity: 1;
}

.pen {
  position: relative;
  outline: none;
}
.pen:not([data-elementor-inline-editing-toolbar=advanced]) {
  white-space: pre-wrap;
}
.pen-menu {
  box-shadow: 1px 2px 3px -2px #222;
  background-color: #6d7882;
  position: fixed;
  overflow: hidden;
  border-radius: 3px;
  z-index: 9999;
}
.pen-menu:after {
  top: 100%;
  content: "";
  position: absolute;
  border: 6px solid transparent;
  border-top-color: #6d7882;
  left: 50%;
  transform: translateX(-50%);
}
.pen-menu-below:after {
  top: 0;
  transform: translateX(-50%) translateY(-100%) rotate(180deg);
}
.pen-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35px;
  padding: 0 10px;
  font-size: 21px;
  color: #d5dadf;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.pen-icon:hover {
  background: #556068;
}
.pen-icon[data-group], .pen-icon[data-action=closeGroup] {
  display: none;
}
.pen-icon[data-action=close]:before {
  content: "";
  position: absolute;
  right: 0;
  height: 60%;
  width: 1px;
  background-color: #556068;
}
.pen-icon.active {
  background-color: #556068;
  box-shadow: inset 2px 2px 4px #556068;
}
.pen-group-icon:after {
  font-family: eicons;
  font-size: 12px;
  content: "\e92a";
  padding-left: 4px;
  color: #71d7f7;
}
.pen-input-wrapper {
  align-items: center;
}
.pen-input-wrapper .pen-url-input {
  font-size: 12px;
  line-height: 1.5;
  padding: 0;
  padding-right: 10px;
  padding-left: 10px;
  width: 250px;
  direction: ltr;
}
.pen-input-wrapper .pen-url-input, .pen-input-wrapper .pen-url-input:focus {
  background-color: transparent;
  border: none;
  outline: none;
  box-shadow: none;
  color: #fff;
}
.pen-input-wrapper .pen-url-input::-moz-placeholder {
  color: #d5dadf;
}
.pen-input-wrapper .pen-url-input::placeholder {
  color: #d5dadf;
}
.pen-input-label {
  margin: 0;
  margin-left: -1px;
}
.pen-placeholder:before {
  content: attr(data-pen-placeholder);
  position: absolute;
  font-weight: normal;
  color: #757575;
  opacity: 0.6;
}
.pen-external-url-checkbox {
  display: none;
}
.pen-external-url-checkbox:checked + i {
  color: #fff;
}

.elementor-inline-editing i:not([class]) {
  font-style: italic;
}
.elementor-inline-editing b {
  font-weight: bold;
}
.elementor-inline-editing u {
  text-decoration: underline;
}

.e-element-color-picker {
  --primary-color: #71d7f7;
  --swatch-size: 25px;
  cursor: default;
  display: flex;
  position: absolute;
  width: calc((var(--count) + 1) * var(--swatch-size));
  height: var(--swatch-size);
  top: var(--top);
  left: var(--left);
  right: var(--right, unset);
  border-radius: 3px;
  opacity: 0;
  pointer-events: none;
  z-index: 9998;
  background-color: var(--primary-color);
  padding: 1px;
  box-sizing: content-box;
  transition: opacity 0.3s, width 0.3s;
}
.e-element-color-picker.e-picker-hidden {
  opacity: 0;
  pointer-events: none;
}
.e-element-color-picker::before {
  content: "";
  flex: 0 1 var(--swatch-size);
  max-width: 100%;
  height: 100%;
  box-sizing: border-box;
  text-align: center;
}
.e-element-color-picker::after {
  content: "\e91e";
  font-family: "eicons";
  color: #FFF;
  font-size: 1rem;
  line-height: var(--swatch-size);
  position: absolute;
  left: 0.3rem;
  z-index: -1;
}
.e-element-color-picker__swatch {
  flex: 1 0 var(--swatch-size);
  max-width: 100%;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
  border-radius: inherit;
  /* Hack to fix transparent `--color` on hover */
  background: linear-gradient(var(--color), var(--color)), linear-gradient(var(--primary-color), var(--primary-color));
}
.e-element-color-picker__swatch:not(:first-child) {
  border-left: 1px solid var(--primary-color);
}
.e-element-color-picker__swatch::before {
  content: attr(data-text);
  position: absolute;
  left: 50%;
  top: 50%;
  opacity: 0;
  color: var(--color);
  font-size: 10px;
  font-weight: 300;
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  transform: translate(-50%, -50%);
  filter: hue-rotate(180deg) grayscale(1) contrast(999) invert(1);
  transition: inherit;
}
.e-element-color-picker__swatch:hover {
  flex-basis: calc(2 * var(--swatch-size));
  flex-shrink: 0;
}
.e-element-color-picker__swatch:hover::before {
  opacity: 1;
}

.e-ui-state--elements-color-picker-color-picking__on *:not(.e-element-color-picker__swatch) {
  cursor: url("../images/eyedropper.svg") 0 20, pointer;
}
.e-ui-state--elements-color-picker-color-picking__on .e-element-color-picker:not(.e-picker-hidden):hover,
.e-ui-state--elements-color-picker-color-picking__on .elementor-element:hover > .e-element-color-picker:not(.e-picker-hidden),
.e-ui-state--elements-color-picker-color-picking__on .elementor-widget-container:hover + .e-element-color-picker:not(.e-picker-hidden) {
  opacity: 1;
  pointer-events: all;
}
.e-ui-state--elements-color-picker-color-picking__on .elementor-section:hover {
  outline: 1px solid #71d7f7;
}

[class^=eicon-flex], [class*=" eicon-flex"] {
  transition: 0.3s all;
  --is-ltr: 1;
  --is-rtl: 0;
  --rotation-direction: calc(var(--is-ltr) - var(--is-rtl));
  --is-ltr: 0;
  --is-rtl: 1;
}
[class^=eicon-flex].eicon-inline, [class*=" eicon-flex"].eicon-inline {
  max-height: 1em;
  max-width: 1em;
}
[class^=eicon-flex]:is(.eicon-justify-start-h, .eicon-justify-end-h), [class*=" eicon-flex"]:is(.eicon-justify-start-h, .eicon-justify-end-h) {
  --rotation-direction: calc(var(--is-ltr) + var(--is-rtl));
}
:is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow), :is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow) {
  transform: rotate(calc(var(--rotation-direction) * 90deg));
}
:is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-align-start-v, .eicon-align-end-v), :is(.e-ui-state--document-direction-mode__column, .e-ui-state--document-direction-mode__column-reverse) [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-align-start-v, .eicon-align-end-v) {
  transform: rotate(calc(var(--rotation-direction) * -90deg));
}
.e-ui-state--document-direction-mode__column-reverse [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end), .e-ui-state--document-direction-mode__column-reverse [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end) {
  transform: rotate(calc(var(--rotation-direction) * -90deg));
}
.e-ui-state--document-direction-mode__row [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end), .e-ui-state--document-direction-mode__row [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end) {
  transform: rotate(calc(var(--is-rtl) * 180deg));
}
.e-ui-state--document-direction-mode__row-reverse [class^=eicon-flex]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end), .e-ui-state--document-direction-mode__row-reverse [class*=" eicon-flex"]:not(.eicon-wrap):not(.eicon-nowrap):not(.eicon-grow):is(.eicon-justify-start-h, .eicon-justify-end-h, .eicon-order-start, .eicon-order-end) {
  transform: rotate(calc(var(--is-ltr) * 180deg));
}

html.elementor-html {
  margin-top: 0 !important;
}

.elementor-edit-area {
  position: relative;
}

.elementor.loading {
  opacity: 0.5;
}

.elementor-edit-area-active {
  --primary-color: #71d7f7;
  --secondary-color: #10bcf2;
  --outline-color: var( --primary-color );
}
.elementor-edit-area-active .e-con {
  --primary-color: #0098c7;
  --secondary-color: #007194;
  --outline-color: var( --primary-color );
}
.elementor-edit-area-active .e-con.e-dragging-over, .elementor-edit-area-active .e-con.elementor-dragging-on-child, .elementor-edit-area-active .e-con.e-con-boxed.elementor-html5dnd-current-element {
  outline: 1px solid var(--outline-color);
}
.elementor-edit-area-active .elementor-inner-section:first-child {
  margin-top: 15px;
}
.elementor-edit-area-active .elementor-widget-wrap.elementor-element-empty {
  min-height: 30px;
}
@media (min-width: 768px) {
  .elementor-edit-area-active .elementor-widget-wrap.elementor-element-empty {
    margin: 10px;
  }
}
.elementor-edit-area-active .elementor-column {
  min-width: 25px;
}
.elementor-edit-area-active .elementor-widget.elementor-loading {
  opacity: 0.3;
}
.elementor-edit-area-active .elementor-widget.elementor-element-editable, .elementor-edit-area-active .elementor-widget.elementor-element-edit-mode:hover {
  box-shadow: 0 0 0 1px #71d7f7;
}
.elementor-edit-area-active .elementor-widget:not(:hover) .elementor-editor-element-settings {
  display: none;
}
.elementor-edit-area-active .elementor-widget.ui-draggable-dragging {
  pointer-events: none;
}
.elementor-edit-area-active .elementor-editor-element-setting {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  font-size: 11px;
  color: #fff;
  width: 25px;
  transition: margin 0.3s, width 0.3s, font 0.3s;
}
.elementor-edit-area-active .elementor-inline-editing {
  min-height: 15px;
}
.elementor-edit-area-active .elementor-edit-hidden {
  display: none;
}

.elementor-section-wrap:empty {
  min-height: 25px;
}
.elementor-section-wrap > :first-child > .elementor-element-overlay {
  z-index: 9999;
}

.elementor-element > .elementor-element-overlay {
  position: absolute;
  top: 0;
  left: 0;
  letter-spacing: 0;
  z-index: 9998;
  pointer-events: none;
}
.elementor-element-empty .elementor-sortable-placeholder {
  display: none;
}
.elementor-element.elementor-widget-empty {
  background-color: rgba(213, 218, 223, 0.8);
}
.elementor-element.elementor-widget-empty .elementor-widget-empty-icon {
  color: #c2cbd2;
  font-size: 22px;
  display: block;
  text-align: center;
  padding: 10px 0;
}
.elementor-element:not(:hover):not(.elementor-element-editable) > .elementor-element-overlay .elementor-editor-element-settings {
  display: none;
}
.elementor-element--toggle-edit-tools > .elementor-element-overlay .elementor-editor-element-edit:not(.elementor-active) ~ * {
  width: 0;
  font-size: 0;
}
.elementor-element[data-side=top]:before, .elementor-element[data-side=bottom] + .elementor-element:before {
  content: "";
  background-color: #71d7f7;
  transition-timing-function: ease-out;
  opacity: 0.9;
  height: 10px;
  animation: placeholder-widget 500ms;
  display: block;
}
.elementor-element[data-side=bottom]:last-child:after {
  content: "";
  background-color: #71d7f7;
  transition-timing-function: ease-out;
  opacity: 0.9;
  height: 10px;
  animation: placeholder-widget 500ms;
  display: block;
}
.elementor-element.elementor-absolute, .elementor-element.elementor-fixed {
  cursor: grab;
}
.elementor-element.elementor-absolute:active, .elementor-element.elementor-fixed:active {
  cursor: grabbing;
}
.elementor-element.elementor-absolute .eicon-edit:before, .elementor-element.elementor-fixed .eicon-edit:before {
  content: "\e902";
}

.elementor-column > .elementor-element-overlay {
  right: 0;
  bottom: 0;
}
.elementor-column > .elementor-element-overlay:after {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  outline: 1px dashed #6d7882;
}
.elementor-column:hover > .elementor-element-overlay, .elementor-column.elementor-element-editable > .elementor-element-overlay {
  border: 1px solid #ffffff;
}
.elementor-column:hover > .elementor-element-overlay:after, .elementor-column.elementor-element-editable > .elementor-element-overlay:after {
  content: "";
}
.elementor-column.elementor-dragging-on-child > .elementor-element-overlay {
  border: 1px solid #71d7f7;
}
.elementor-column.elementor-dragging-on-child > .elementor-element-overlay:after {
  display: none;
}
.elementor-column > .ui-resizable-e, .elementor-column > .ui-resizable-w {
  cursor: col-resize;
  width: 7px;
  position: absolute;
  left: -5px;
  top: 0;
  height: 100%;
}
.elementor-column:last-of-type > .ui-resizable-e, .elementor-column:last-of-type > .ui-resizable-w {
  display: none !important;
}
@media (max-width: 1024px) {
  .elementor-column > .ui-resizable-e, .elementor-column > .ui-resizable-w {
    display: none !important;
  }
}

.elementor-editor-element-settings {
  position: absolute;
  display: flex;
  height: 26px;
  list-style: none;
  margin: 0;
  padding: 0;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 13px;
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  z-index: 1;
  pointer-events: all;
}

@media (min-width: 1025px) {
  .elementor-editor-element-edit {
    cursor: move;
  }
}

.elementor-editor-section-settings,
.elementor-editor-container-settings {
  height: 24px;
  top: 1px;
  left: 50%;
  transform: translateX(-50%) translateY(-100%);
  background-color: var(--primary-color);
  border-radius: 5px 5px 0 0;
  box-shadow: 0px -2px 8px rgba(0, 0, 0, 0.05);
}
.elementor-editor-section-settings i.eicon-handle,
.elementor-editor-container-settings i.eicon-handle {
  font-size: 16px;
}
.elementor-editor-section-settings .elementor-editor-element-setting:hover,
.elementor-editor-container-settings .elementor-editor-element-setting:hover {
  background-color: var(--secondary-color);
}
.elementor-editor-section-settings .elementor-editor-element-setting:first-child,
.elementor-editor-container-settings .elementor-editor-element-setting:first-child {
  border-radius: 0 5px 0 0;
}
.elementor-editor-section-settings .elementor-editor-element-setting:first-child:before,
.elementor-editor-container-settings .elementor-editor-element-setting:first-child:before {
  content: "";
  position: absolute;
  border: solid transparent;
  top: 2px;
  border-left-color: var(--primary-color);
  border-width: 22px 0 0 12px;
  left: calc(100% - 1px);
}
.elementor-editor-section-settings .elementor-editor-element-setting:first-child:hover:before,
.elementor-editor-container-settings .elementor-editor-element-setting:first-child:hover:before {
  border-left-color: var(--secondary-color);
}
.elementor-editor-section-settings .elementor-editor-element-setting:last-child,
.elementor-editor-container-settings .elementor-editor-element-setting:last-child {
  border-radius: 5px 0 0 0;
}
.elementor-editor-section-settings .elementor-editor-element-setting:last-child:after,
.elementor-editor-container-settings .elementor-editor-element-setting:last-child:after {
  content: "";
  position: absolute;
  border: solid transparent;
  top: 2px;
  border-right-color: var(--primary-color);
  border-width: 22px 12px 0 0;
  right: calc(100% - 1px);
}
.elementor-editor-section-settings .elementor-editor-element-setting:last-child:hover:after,
.elementor-editor-container-settings .elementor-editor-element-setting:last-child:hover:after {
  border-right-color: var(--secondary-color);
}

.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings,
.elementor-section--handles-inside > .elementor-element-overlay > .elementor-editor-element-settings {
  transform: translateX(-50%);
  border-radius: 0 0 5px 5px;
}
.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:first-child,
.elementor-section--handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:first-child {
  border-radius: 0 0 5px 0;
}
.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:first-child:before,
.elementor-section--handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:first-child:before {
  top: 0;
  border-width: 0 0 22px 12px;
}
.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:last-child,
.elementor-section--handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:last-child {
  border-radius: 0 0 0 5px;
}
.e-handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:last-child:after,
.elementor-section--handles-inside > .elementor-element-overlay > .elementor-editor-element-settings .elementor-editor-element-setting:last-child:after {
  top: 0;
  border-width: 0 12px 22px 0;
}

.elementor-editor-column-settings {
  top: -1px;
  right: -1px;
  border-radius: 0 0 0 3px;
  overflow: hidden;
}
.elementor-editor-column-settings .elementor-editor-element-setting {
  background-color: #495157;
}
.elementor-editor-column-settings .elementor-editor-element-setting:not(:hover) {
  background-image: linear-gradient(to bottom, #6d7882, #556068);
}

.elementor-editor-widget-settings {
  z-index: 2;
  top: -1px;
  left: -1px;
  flex-direction: row-reverse;
  border-radius: 0 0 3px 0;
  overflow: hidden;
}
.elementor-editor-widget-settings .elementor-editor-element-setting {
  background-color: #10bcf2;
}
.elementor-editor-widget-settings .elementor-editor-element-setting:not(:hover) {
  background-image: linear-gradient(to top, #41c9f4, #71d7f7);
}

.elementor-empty-view {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
}

.elementor-first-add {
  text-align: center;
  border: 1px dashed #d5dadf;
  display: flex;
  height: 100%;
  width: 100%;
  position: absolute;
  align-items: center;
  justify-content: center;
}
.elementor-first-add .elementor-icon {
  font-size: 19px;
  color: #a4afb7;
  cursor: pointer;
}

.elementor-sortable-helper {
  position: absolute;
  border-radius: 3px;
  background-color: #fff;
  text-align: center;
  color: #556068;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  cursor: move;
}
.elementor-sortable-helper .icon {
  font-size: 28px;
  padding-top: 15px;
  line-height: 1;
}
.elementor-sortable-helper .elementor-element-title-wrapper {
  display: table;
  width: 100%;
}
.elementor-sortable-helper .title {
  font-size: 11px;
  font-family: Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  display: table-cell;
  vertical-align: middle;
  height: 40px;
}

.elementor-editor-content-only .elementor-first-add {
  display: none;
}
.elementor-editor-content-only .elementor-section > .elementor-element-overlay {
  display: none;
}
.elementor-editor-content-only .elementor-section > .elementor-element-overlay:after {
  display: none;
}
.elementor-editor-content-only .elementor-section:hover > .elementor-element-overlay:after, .elementor-editor-content-only .elementor-section.elementor-element-editable > .elementor-element-overlay:after {
  display: none;
}
.elementor-editor-content-only .elementor-column > .elementor-element-overlay {
  display: none;
}
.elementor-editor-content-only .elementor-column > .elementor-element-overlay:after {
  display: none;
}
.elementor-editor-content-only .elementor-column:hover > .elementor-element-overlay:after, .elementor-editor-content-only .elementor-column.elementor-element-editable > .elementor-element-overlay:after {
  display: none;
}
@media (min-width: 1025px) {
  .elementor-editor-content-only .elementor-editor-element-edit {
    cursor: pointer;
  }
}

.elementor-sortable-placeholder:not(.elementor-column-placeholder) {
  background-color: #71d7f7;
  animation-duration: 250ms;
  opacity: 0.9;
  width: 100%;
  align-self: stretch;
}
.e-con .elementor-first-add .elementor-sortable-placeholder:not(.elementor-column-placeholder) {
  align-self: center;
}
.e-swappable--active > .elementor-sortable-placeholder:not(.elementor-column-placeholder) {
  display: none;
}

.elementor-section-placeholder {
  height: 40px;
  animation-name: placeholder-section;
}

.elementor-column-placeholder {
  position: relative;
}
.elementor-column-placeholder:before, .elementor-column-placeholder:after {
  content: "";
  position: absolute;
  top: 10px;
  bottom: 10px;
  right: 10px;
  left: 10px;
}
.elementor-column-placeholder:before {
  border: 1px solid #6d7882;
}
.elementor-column-placeholder:after {
  border: 1px dashed #fff;
}

.elementor-widget-placeholder {
  height: 10px;
  animation-name: placeholder-widget;
}

.elementor-draggable-over:not([data-dragged-element=section]):not([data-dragged-is-inner=true]) > .elementor-empty-view > .elementor-first-add:after,
.elementor-first-add.elementor-html5dnd-current-element:after {
  content: "";
  background-color: #71d7f7;
  transition-timing-function: ease-out;
  opacity: 0.9;
  height: 10px;
  animation: placeholder-widget 500ms;
  width: 100%;
}
.e-con .elementor-draggable-over:not([data-dragged-element=section]):not([data-dragged-is-inner=true]) > .elementor-empty-view > .elementor-first-add:after,
.e-con .elementor-first-add.elementor-html5dnd-current-element:after {
  display: none;
}
.elementor-draggable-over:not([data-dragged-element=section]):not([data-dragged-is-inner=true]) > .elementor-empty-view > .elementor-first-add .elementor-icon,
.elementor-first-add.elementor-html5dnd-current-element .elementor-icon {
  display: none;
}

.elementor-draggable-over[data-dragged-element=section][data-dragged-is-inner=true] .elementor-inner-column .elementor-sortable-placeholder {
  display: none;
}

.elementor-add-section {
  all: initial;
  display: flex;
  max-width: 1160px;
  position: relative;
  margin-inline: auto;
}
.elementor-add-section:not(.elementor-dragging-on-child) .elementor-add-section-inner {
  border: 2px dashed #d5dadf;
  background-color: rgba(255, 255, 255, 0.5);
}
.elementor-add-section.elementor-dragging-on-child .elementor-add-section-inner {
  border: 3px dashed #71d7f7;
}
.elementor-add-section[data-view=choose-action] .e-view:not(.elementor-add-new-section) {
  display: none;
}
.elementor-add-section[data-view=select-preset] .e-view:not(.elementor-select-preset) {
  display: none;
}
.elementor-add-section[data-view=select-container-preset] .e-view:not(.e-con-select-preset) {
  display: none;
}

.elementor-add-section-inner {
  text-align: center;
  margin: 20px;
  padding: 40px 0;
  flex-grow: 1;
}

.elementor-add-new-section {
  display: inline-block;
}
.elementor-add-new-section .elementor-add-section-area-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 40px;
  color: #fff;
  font-size: 16px;
  border-radius: 50%;
  transition: all 0.3s;
  cursor: pointer;
}
.elementor-add-new-section .elementor-add-section-area-button:hover {
  opacity: 0.85;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.12), 0 2px 2px rgba(0, 0, 0, 0.2);
}
.elementor-add-new-section .elementor-add-section-area-button:active {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.1);
}
.elementor-add-new-section .elementor-add-section-button {
  background-color: #93003c;
  -webkit-margin-start: 5px;
          margin-inline-start: 5px;
}
.elementor-add-new-section .elementor-add-template-button {
  background-color: #6d7882;
  margin-right: 5px;
}

.elementor-add-section-drag-title {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 13px;
  font-weight: normal;
  font-style: italic;
  line-height: 1;
  color: #556068;
  margin-top: 15px;
}

.elementor-add-section-close {
  position: absolute;
  right: 40px;
  top: 40px;
  font-size: 20px;
  cursor: pointer;
  line-height: 1;
  color: #d5dadf;
}
.elementor-add-section-close:hover {
  color: #6d7882;
}

.elementor-select-preset-title {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 13px;
  font-weight: 500;
  font-style: normal;
  text-transform: uppercase;
  color: #556068;
}

.elementor-select-preset-list {
  list-style: none;
  padding: 0 25px;
  margin: 20px auto 0;
  overflow: hidden;
  max-width: 700px;
}
.elementor-select-preset-list .elementor-preset {
  cursor: pointer;
  margin: 0;
  padding: 10px;
  float: right;
  max-width: 33.333%;
}
.elementor-select-preset-list .elementor-preset:hover path,
.elementor-select-preset-list .elementor-preset:hover rect {
  fill: #6d7882;
}
.elementor-select-preset-list .elementor-preset:not(:hover) path,
.elementor-select-preset-list .elementor-preset:not(:hover) rect {
  fill: #d5dadf;
}
.elementor-select-preset-list .elementor-preset .e-preset--container {
  position: relative;
  display: flex;
}
.elementor-select-preset-list .elementor-preset .e-preset--container::before {
  content: var(--text);
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  font-size: 13px;
  font-weight: 500;
  position: absolute;
  left: 50%;
  top: 50%;
  color: #ffffff;
  transform: translate(-50%, -50%);
}
.elementor-select-preset-list .elementor-preset svg {
  height: 50px;
  width: 100%;
  transform: rotate(180deg);
}

#elementor-add-new-section {
  margin: 60px auto;
}
#elementor-add-new-section[data-view=choose-action] .elementor-add-section-close {
  display: none;
}

.elementor-add-section-inline {
  margin: 10px auto;
  width: 100%;
}

.elementor-column-percents-tooltip {
  position: absolute;
  display: none;
  pointer-events: none;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
  font-size: 10px;
  background-color: #556068;
  width: 40px;
  padding: 3.5px 0;
  text-align: center;
  z-index: 1;
  line-height: 1;
}
.elementor-column-percents-tooltip:after {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 8.5px solid transparent;
  top: 0;
}
.elementor-column-percents-tooltip[data-side=left] {
  border-radius: 3px 0 0 3px;
  right: 15px;
}
.elementor-column-percents-tooltip[data-side=left]:after {
  left: 100%;
  border-left-color: #556068;
  border-right-width: 0;
}
.elementor-column-percents-tooltip[data-side=right] {
  border-radius: 0 3px 3px 0;
  left: 15px;
}
.elementor-column-percents-tooltip[data-side=right]:after {
  right: 100%;
  border-right-color: #556068;
  border-left-width: 0;
}

.elementor-editor-preview .elementor-element-overlay,
.elementor-editor-preview .elementor-empty,
.elementor-editor-preview .elementor-add-section,
.elementor-editor-preview .elementor-add-section-inline,
.elementor-editor-preview .elementor-empty-view,
.elementor-editor-preview .elementor-widget-empty {
  display: none;
}

.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile {
  display: inherit;
  background: repeating-linear-gradient(125deg, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 1px, transparent 2px, transparent 9px);
  border: 1px solid rgba(0, 0, 0, 0.02);
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.elementor-section > .elementor-element-overlay, .e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.e-con > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.elementor-section > .elementor-element-overlay,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.e-con > .elementor-element-overlay {
  background-color: #d5dadf;
  mix-blend-mode: color;
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.elementor-section:before, .e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.e-con:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.elementor-section:before,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.e-con:before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.6);
  z-index: 9997;
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.e-con,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.e-con {
  display: var(--display);
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.elementor-inner-section .elementor-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.elementor-inner-section .elementor-container {
  width: 100%;
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen > .elementor-widget-container, .e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra > .elementor-widget-wrap,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile > .elementor-widget-container,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile > .elementor-widget-wrap {
  filter: opacity(0.4) saturate(0);
}
.e-preview--show-hidden-elements[data-elementor-device-mode=widescreen] .elementor-edit-area-active .elementor-hidden-widescreen.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=desktop] .elementor-edit-area-active .elementor-hidden-desktop.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=laptop] .elementor-edit-area-active .elementor-hidden-laptop.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet_extra] .elementor-edit-area-active .elementor-hidden-tablet_extra.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=tablet] .elementor-edit-area-active .elementor-hidden-tablet.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile_extra] .elementor-edit-area-active .elementor-hidden-mobile_extra.elementor-edit-hidden,
.e-preview--show-hidden-elements[data-elementor-device-mode=mobile] .elementor-edit-area-active .elementor-hidden-mobile.elementor-edit-hidden {
  display: none;
}

.elementor-section > .elementor-element-overlay,
.e-con > .elementor-element-overlay {
  right: 0;
  bottom: 0;
}
.elementor-section > .elementor-element-overlay:after,
.e-con > .elementor-element-overlay:after {
  position: absolute;
  left: 2px;
  right: 2px;
  top: 2px;
  bottom: 2px;
  outline: 2px solid var(--outline-color);
  animation: section-outline 0.75s;
}
.elementor-section:hover > .elementor-element-overlay:after, .elementor-section.elementor-element-editable > .elementor-element-overlay:after,
.e-con:hover > .elementor-element-overlay:after,
.e-con.elementor-element-editable > .elementor-element-overlay:after {
  content: "";
}
.elementor-section-filled .elementor-sortable-placeholder.elementor-column,
.e-con-filled .elementor-sortable-placeholder.elementor-column {
  display: none;
}
.elementor-section-filled .elementor-row.elementor-draggable-over,
.e-con-filled .elementor-row.elementor-draggable-over {
  border: 1px solid #d72b3f;
}

.e-con-select-preset {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 25px;
}
.e-con-select-preset[data-view=choose-preset] > *:not(.e-con-select-preset__inner) {
  display: none;
}
.e-con-select-preset[data-view=drop-area] > *:not(.elementor-first-add) {
  display: none;
}
.e-con-select-preset__title {
  font-family: Roboto, Arial, Helvetica, Verdana, sans-serif;
  font-size: 13px;
  font-weight: 500;
  text-transform: uppercase;
  color: #556068;
}
.e-con-select-preset__list {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  max-width: 650px;
}
.e-con-select-preset__list .e-con-preset {
  cursor: pointer;
  flex-basis: 90px;
}
.e-con-select-preset__list .e-con-preset svg {
  width: 100%;
  height: auto;
}
.e-con-select-preset__list .e-con-preset path {
  fill: #ffffff;
}
.e-con-select-preset__list .e-con-preset rect {
  fill: #d5dadf;
  transition: 0.3s all;
}
.e-con-select-preset__list .e-con-preset:hover rect {
  fill: #6d7882;
}
.e-con-select-preset__list .e-con-preset[data-preset=r100] {
  transform: scaleX(-1);
}
.e-con.e-con--column .ui-resizable-e {
  right: 0;
}
.e-con.e-con--column .ui-resizable-w {
  left: 0;
}
/*# sourceMappingURL=editor-preview-rtl.css.map */