# تحديث قاعدة البيانات وإضافة التصنيفات في الأوامر الأساسية

## 🔧 التحديثات المطبقة على الأوامر الأساسية

### **تحديث جدول المنتجات في `config/init.php`:**

#### **الهيكل الجديد:**
```sql
CREATE TABLE IF NOT EXISTS `products` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `description` text DEFAULT NULL,                    -- جديد
    `price` decimal(10,2) NOT NULL DEFAULT 0.00,
    `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
    `category` varchar(100) DEFAULT NULL,               -- جديد
    `stock_quantity` decimal(10,2) DEFAULT 0.00,
    `unit` varchar(50) DEFAULT 'قطعة',
    `barcode` varchar(100) DEFAULT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,  -- جديد
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`),
    KEY `idx_category` (`category`),                    -- جديد
    KEY `idx_barcode` (`barcode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
```

#### **الحقول المضافة:**
1. **`description`** - text DEFAULT NULL
2. **`category`** - varchar(100) DEFAULT NULL  
3. **`updated_at`** - timestamp with auto-update
4. **فهرس على التصنيف** - KEY `idx_category` (`category`)

### **دالة التحديث التلقائي المحسنة:**

#### **إضافة جدول المنتجات للتحديث:**
```php
$tables_to_update = [
    'products' => [
        'description' => "ALTER TABLE `products` ADD COLUMN `description` text DEFAULT NULL AFTER `name`",
        'category' => "ALTER TABLE `products` ADD COLUMN `category` varchar(100) DEFAULT NULL AFTER `tax_rate`",
        'updated_at' => "ALTER TABLE `products` ADD COLUMN `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `created_at`"
    ],
    'purchase_items' => [
        'product_name' => "ALTER TABLE `purchase_items` ADD COLUMN `product_name` varchar(255) NOT NULL DEFAULT '' AFTER `product_id`",
        'unit_price' => "ALTER TABLE `purchase_items` ADD COLUMN `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `quantity`"
    ],
    'sale_items' => [
        'product_name' => "ALTER TABLE `sale_items` ADD COLUMN `product_name` varchar(255) NOT NULL DEFAULT '' AFTER `product_id`",
        'unit_price' => "ALTER TABLE `sale_items` ADD COLUMN `unit_price` decimal(10,2) NOT NULL DEFAULT 0.00 AFTER `quantity`"
    ]
];
```

#### **دالة إضافة الفهارس المفقودة:**
```php
function addMissingIndexes($db) {
    $indexes_to_add = [
        'products' => [
            'idx_category' => "ALTER TABLE `products` ADD INDEX `idx_category` (`category`)"
        ]
    ];
    
    foreach ($indexes_to_add as $table_name => $indexes) {
        // التحقق من وجود الجدول أولاً
        $check_table = $db->query("SHOW TABLES LIKE '$table_name'");
        if ($check_table && $check_table->num_rows > 0) {
            // جلب الفهارس الموجودة
            $existing_indexes = [];
            $indexes_result = $db->query("SHOW INDEX FROM `$table_name`");
            if ($indexes_result) {
                while ($index = $indexes_result->fetch_assoc()) {
                    $existing_indexes[] = $index['Key_name'];
                }
            }
            
            // إضافة الفهارس المفقودة
            foreach ($indexes as $index_name => $alter_sql) {
                if (!in_array($index_name, $existing_indexes)) {
                    $db->query($alter_sql);
                }
            }
        }
    }
}
```

## 🔄 تحديثات معالجة الفواتير

### **تحديث `process_quick_invoice.php`:**

#### **جلب بيانات المنتج مع التصنيف:**
```php
// جلب بيانات المنتج من قاعدة البيانات
$product_stmt = $db->prepare("SELECT name, category FROM products WHERE id = ?");
$product_stmt->bind_param("i", $product_id);
$product_stmt->execute();
$product_result = $product_stmt->get_result();

if ($product_result->num_rows === 0) {
    continue; // تجاهل المنتجات غير الموجودة
}

$product_data = $product_result->fetch_assoc();
$product_name = $product_data['name'];
$product_category = $product_data['category'];
$product_stmt->close();
```

#### **حفظ بيانات العناصر مع التصنيف:**
```php
$valid_items[] = [
    'product_id' => $product_id,
    'product_name' => $product_name,
    'product_category' => $product_category,    // جديد
    'quantity' => $quantity,
    'price' => $price,
    'tax_rate' => $tax_rate,
    'total' => $item_total
];
```

## 📋 تحديثات واجهة الفاتورة السريعة

### **تحميل المنتجات مع التصنيفات:**

#### **استعلام محسن في `index.php`:**
```php
$products_result = $db->query("SELECT id, name, price, tax_rate, category FROM products ORDER BY name");
if ($products_result && $products_result->num_rows > 0) {
    while ($product = $products_result->fetch_assoc()) {
        $category = $product['category'] ? addslashes($product['category']) : '';
        echo "{id: {$product['id']}, name: '" . addslashes($product['name']) . "', price: {$product['price']}, tax_rate: {$product['tax_rate']}, category: '$category'},";
    }
}
```

#### **عرض المنتجات مع التصنيف:**
```javascript
quickProducts.forEach(p => {
    const option = document.createElement('option');
    option.value = p.id;
    // عرض اسم المنتج مع التصنيف إذا كان متوفراً
    const displayName = p.category ? `${p.name} (${p.category})` : p.name;
    option.textContent = displayName;
    option.dataset.price = p.price;
    option.dataset.taxRate = p.tax_rate;
    option.dataset.category = p.category || '';

    if (product && product.id == p.id) {
        option.selected = true;
    }

    productSelect.appendChild(option);
});
```

## 🎯 المميزات الجديدة

### **في قاعدة البيانات:**
- ✅ **عمود التصنيف** في جدول المنتجات
- ✅ **عمود الوصف** للتفاصيل الإضافية
- ✅ **عمود updated_at** لتتبع التعديلات
- ✅ **فهرس على التصنيف** لتحسين الأداء

### **في الأوامر الأساسية:**
- ✅ **تحديث تلقائي** للجداول الموجودة
- ✅ **إضافة الأعمدة المفقودة** تلقائياً
- ✅ **إضافة الفهارس المفقودة** تلقائياً
- ✅ **حماية من الأخطاء** أثناء التحديث

### **في معالجة الفواتير:**
- ✅ **جلب التصنيف** مع بيانات المنتج
- ✅ **حفظ التصنيف** في بيانات العناصر
- ✅ **تتبع المنتجات** بالتصنيف

### **في واجهة الفاتورة السريعة:**
- ✅ **عرض التصنيف** مع اسم المنتج
- ✅ **تحميل التصنيفات** من قاعدة البيانات
- ✅ **تخزين التصنيف** في dataset
- ✅ **عرض منظم** للمنتجات

## 🔍 آلية التحديث التلقائي

### **خطوات التحديث:**

#### **1. فحص الجداول الموجودة:**
```php
$check_table = $db->query("SHOW TABLES LIKE '$table_name'");
if ($check_table && $check_table->num_rows > 0) {
    // الجدول موجود، فحص الأعمدة
}
```

#### **2. فحص الأعمدة المفقودة:**
```php
foreach ($columns as $column_name => $alter_sql) {
    $check_column = $db->query("SHOW COLUMNS FROM `$table_name` LIKE '$column_name'");
    if ($check_column && $check_column->num_rows == 0) {
        // العمود غير موجود، أضفه
        $db->query($alter_sql);
    }
}
```

#### **3. فحص الفهارس المفقودة:**
```php
$existing_indexes = [];
$indexes_result = $db->query("SHOW INDEX FROM `$table_name`");
if ($indexes_result) {
    while ($index = $indexes_result->fetch_assoc()) {
        $existing_indexes[] = $index['Key_name'];
    }
}

foreach ($indexes as $index_name => $alter_sql) {
    if (!in_array($index_name, $existing_indexes)) {
        $db->query($alter_sql);
    }
}
```

## 📊 الفوائد المحققة

### **تحسين الأداء:**
- **فهرس على التصنيف** لبحث أسرع
- **استعلامات محسنة** مع JOIN على المنتجات
- **تحميل مُحسن** للبيانات في الواجهة

### **تحسين البيانات:**
- **تصنيف المنتجات** بشكل منطقي
- **وصف تفصيلي** للمنتجات
- **تتبع التعديلات** بالتوقيت
- **ربط التصنيفات** بالفواتير

### **سهولة الصيانة:**
- **تحديث تلقائي** للهيكل
- **حماية من فقدان البيانات**
- **إضافة آمنة** للحقول الجديدة
- **فحص شامل** للتوافق

### **تحسين تجربة المستخدم:**
- **عرض منظم** للمنتجات مع التصنيفات
- **بحث محسن** حسب التصنيف
- **فلترة متقدمة** في صفحة المنتجات
- **واجهة بديهية** للإدارة

## ✅ الخلاصة

تم تحديث النظام بنجاح ليشمل:

### **الإنجازات:**
1. **إضافة التصنيفات** في الأوامر الأساسية
2. **تحديث تلقائي** للجداول الموجودة
3. **تكامل التصنيفات** مع معالجة الفواتير
4. **تحسين واجهة** الفاتورة السريعة

### **النتائج:**
- ✅ **قاعدة بيانات محسنة** مع التصنيفات
- ✅ **تحديث آمن** للجداول الموجودة
- ✅ **تكامل شامل** مع جميع الوظائف
- ✅ **أداء محسن** مع الفهارس الجديدة

**النتيجة: نظام متكامل مع تصنيفات المنتجات في جميع مستويات النظام!** 🎉
