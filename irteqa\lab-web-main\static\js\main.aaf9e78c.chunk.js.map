{"version": 3, "sources": ["templates/onyx/preview.png", "templates/pikachu/preview.png", "templates/gengar/preview.png", "templates/castform/preview.png", "templates/glalie/preview.png", "templates/celebi/preview.png", "assets/panzoom.mp4", "i18n/locales/af/app/index.js", "i18n/locales/af/leftSidebar/index.js", "i18n/locales/af/rightSidebar/index.js", "i18n/locales/af/index.js", "i18n/locales/ar/app/index.js", "i18n/locales/ar/leftSidebar/index.js", "i18n/locales/ar/rightSidebar/index.js", "i18n/locales/ar/index.js", "i18n/locales/as/app/index.js", "i18n/locales/as/leftSidebar/index.js", "i18n/locales/as/rightSidebar/index.js", "i18n/locales/as/index.js", "i18n/locales/ca/app/index.js", "i18n/locales/ca/leftSidebar/index.js", "i18n/locales/ca/rightSidebar/index.js", "i18n/locales/ca/index.js", "i18n/locales/cs/app/index.js", "i18n/locales/cs/leftSidebar/index.js", "i18n/locales/cs/rightSidebar/index.js", "i18n/locales/cs/index.js", "i18n/locales/da/app/index.js", "i18n/locales/da/leftSidebar/index.js", "i18n/locales/da/rightSidebar/index.js", "i18n/locales/da/index.js", "i18n/locales/de/app/index.js", "i18n/locales/de/leftSidebar/index.js", "i18n/locales/de/rightSidebar/index.js", "i18n/locales/de/index.js", "i18n/locales/el/app/index.js", "i18n/locales/el/leftSidebar/index.js", "i18n/locales/el/rightSidebar/index.js", "i18n/locales/el/index.js", "i18n/locales/en/app/index.js", "i18n/locales/en/leftSidebar/index.js", "i18n/locales/en/rightSidebar/index.js", "i18n/locales/en/index.js", "i18n/locales/es/app/index.js", "i18n/locales/es/leftSidebar/index.js", "i18n/locales/es/rightSidebar/index.js", "i18n/locales/es/index.js", "i18n/locales/fi/app/index.js", "i18n/locales/fi/leftSidebar/index.js", "i18n/locales/fi/rightSidebar/index.js", "i18n/locales/fi/index.js", "i18n/locales/fr/app/index.js", "i18n/locales/fr/leftSidebar/index.js", "i18n/locales/fr/rightSidebar/index.js", "i18n/locales/fr/index.js", "i18n/locales/he/app/index.js", "i18n/locales/he/leftSidebar/index.js", "i18n/locales/he/rightSidebar/index.js", "i18n/locales/he/index.js", "i18n/locales/hi/app/index.js", "i18n/locales/hi/leftSidebar/index.js", "i18n/locales/hi/rightSidebar/index.js", "i18n/locales/hi/index.js", "i18n/locales/hu/app/index.js", "i18n/locales/hu/leftSidebar/index.js", "i18n/locales/hu/rightSidebar/index.js", "i18n/locales/hu/index.js", "i18n/locales/it/app/index.js", "i18n/locales/it/leftSidebar/index.js", "i18n/locales/it/rightSidebar/index.js", "i18n/locales/it/index.js", "i18n/locales/ja/app/index.js", "i18n/locales/ja/leftSidebar/index.js", "i18n/locales/ja/rightSidebar/index.js", "i18n/locales/ja/index.js", "i18n/locales/kn/app/index.js", "i18n/locales/kn/leftSidebar/index.js", "i18n/locales/kn/rightSidebar/index.js", "i18n/locales/kn/index.js", "i18n/locales/ko/app/index.js", "i18n/locales/ko/leftSidebar/index.js", "i18n/locales/ko/rightSidebar/index.js", "i18n/locales/ko/index.js", "i18n/locales/ml/app/index.js", "i18n/locales/ml/leftSidebar/index.js", "i18n/locales/ml/rightSidebar/index.js", "i18n/locales/ml/index.js", "i18n/locales/mr/app/index.js", "i18n/locales/mr/leftSidebar/index.js", "i18n/locales/mr/rightSidebar/index.js", "i18n/locales/mr/index.js", "i18n/locales/nl/app/index.js", "i18n/locales/nl/leftSidebar/index.js", "i18n/locales/nl/rightSidebar/index.js", "i18n/locales/nl/index.js", "i18n/locales/no/app/index.js", "i18n/locales/no/leftSidebar/index.js", "i18n/locales/no/rightSidebar/index.js", "i18n/locales/no/index.js", "i18n/locales/pa/app/index.js", "i18n/locales/pa/leftSidebar/index.js", "i18n/locales/pa/rightSidebar/index.js", "i18n/locales/pa/index.js", "i18n/locales/pl/app/index.js", "i18n/locales/index.js", "i18n/locales/pl/index.js", "i18n/locales/pl/leftSidebar/index.js", "i18n/locales/pl/rightSidebar/index.js", "i18n/locales/pt/index.js", "i18n/locales/pt/leftSidebar/index.js", "i18n/locales/pt/rightSidebar/index.js", "i18n/locales/ro/index.js", "i18n/locales/ro/leftSidebar/index.js", "i18n/locales/ro/rightSidebar/index.js", "i18n/locales/ru/index.js", "i18n/locales/ru/leftSidebar/index.js", "i18n/locales/ru/rightSidebar/index.js", "i18n/locales/sv/index.js", "i18n/locales/sv/leftSidebar/index.js", "i18n/locales/sv/rightSidebar/index.js", "i18n/locales/ta/index.js", "i18n/locales/ta/leftSidebar/index.js", "i18n/locales/ta/rightSidebar/index.js", "i18n/locales/tr/index.js", "i18n/locales/tr/leftSidebar/index.js", "i18n/locales/tr/rightSidebar/index.js", "i18n/locales/uk/index.js", "i18n/locales/uk/leftSidebar/index.js", "i18n/locales/uk/rightSidebar/index.js", "i18n/locales/vi/index.js", "i18n/locales/vi/leftSidebar/index.js", "i18n/locales/vi/rightSidebar/index.js", "i18n/locales/zh/index.js", "i18n/locales/zh/leftSidebar/index.js", "i18n/locales/zh/rightSidebar/index.js", "i18n/index.js", "serviceWorker.js", "utils/index.js", "context/AppContext.js", "context/PageContext.js", "shared/Dropdown.js", "shared/TabBar.js", "shared/TextField.js", "components/LeftSidebar/tabs/Profile.js", "shared/Checkbox.js", "shared/ItemActions.js", "shared/AddItemButton.js", "shared/ItemHeading.js", "components/LeftSidebar/tabs/Address.js", "components/LeftSidebar/tabs/Contacts.js", "shared/MarkdownHelpText.js", "shared/TextArea.js", "components/LeftSidebar/tabs/Objective.js", "components/LeftSidebar/tabs/Work.js", "components/LeftSidebar/tabs/Education.js", "components/LeftSidebar/tabs/Awards.js", "components/LeftSidebar/tabs/Extras.js", "components/LeftSidebar/tabs/Languages.js", "components/LeftSidebar/tabs/References.js", "components/LeftSidebar/tabs/Memberships.js", "components/LeftSidebar/LeftSidebar.js", "templates/onyx/Onyx.js", "templates/onyx/index.js", "templates/pikachu/Pikachu.js", "templates/pikachu/index.js", "templates/gengar/Gengar.js", "templates/gengar/index.js", "templates/castform/Castform.js", "templates/castform/index.js", "templates/glalie/Glalie.js", "templates/glalie/index.js", "templates/celebi/Celebi.js", "templates/index.js", "templates/celebi/index.js", "components/RightSidebar/tabs/Templates.js", "components/RightSidebar/tabs/Colors.js", "components/RightSidebar/tabs/Fonts.js", "components/RightSidebar/tabs/Actions.js", "components/RightSidebar/tabs/About.js", "components/RightSidebar/tabs/Settings.js", "components/RightSidebar/RightSidebar.js", "shared/PageController.js", "shared/PrintDialog.js", "shared/PanZoomAnimation.js", "components/App/App.js", "index.js"], "names": ["module", "exports", "app", "profile", "objective", "work", "education", "awards", "certifications", "languages", "references", "extras", "templates", "colors", "fonts", "actions", "settings", "about", "leftSidebar", "rightSidebar", "af", "ar", "as", "ca", "cs", "da", "de", "el", "en", "es", "fi", "fr", "he", "hi", "hu", "it", "ja", "kn", "ko", "ml", "mr", "nl", "no", "pa", "pl", "pt", "ro", "ru", "sv", "ta", "tr", "uk", "vi", "zh", "code", "name", "i18n", "use", "detector", "backend", "initReactI18next", "init", "resources", "lng", "fallbackLng", "ns", "defaultNS", "isLocalhost", "Boolean", "window", "location", "hostname", "match", "registerValidSW", "swUrl", "config", "navigator", "serviceWorker", "register", "then", "registration", "onupdatefound", "installingWorker", "installing", "onstatechange", "state", "controller", "console", "log", "onUpdate", "onSuccess", "catch", "error", "move", "array", "element", "delta", "index", "findIndex", "item", "id", "newIndex", "length", "indexes", "sort", "a", "b", "splice", "hexToRgb", "hex", "replace", "m", "r", "g", "result", "exec", "parseInt", "saveData", "dispatch", "type", "addItem", "key", "value", "payload", "saveAsPdfTimer", "saveAsPdf", "pageRef", "panZoomRef", "quality", "Promise", "resolve", "current", "autoCenter", "reset", "setTimeout", "html2canvas", "scale", "useCORS", "<PERSON><PERSON><PERSON><PERSON>", "canvas", "image", "toDataURL", "doc", "jsPDF", "orientation", "unit", "format", "width", "height", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "widthRatio", "heightRatio", "ratio", "canvasWidth", "canvasHeight", "marginX", "marginY", "addImage", "save", "Date", "now", "saveAsMultiPagePdfTimer", "saveAsMultiPagePdf", "marginTop", "heightLeft", "addPage", "initialState", "data", "j<PERSON>ld", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ame", "address", "heading", "photo", "firstName", "lastName", "subtitle", "line1", "line2", "line3", "phone", "website", "email", "contacts", "enable", "body", "items", "skills", "memberships", "theme", "layout", "font", "family", "background", "primary", "accent", "language", "reducer", "newState", "JSON", "parse", "stringify", "set", "get", "push", "remove", "x", "localStorage", "setItem", "Object", "keys", "section", "demoJsonldData", "AppContext", "createContext", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "useReducer", "Consumer", "PageContext", "React", "<PERSON><PERSON><PERSON><PERSON>", "useState", "setPageRef", "setPanZoomRef", "isPrintDialogOpen", "setPrintDialogOpen", "Dropdown", "className", "label", "onChange", "options", "optionItem", "style", "display", "e", "target", "map", "TabBar", "tabs", "currentTab", "setCurrentTab", "changeBy", "tab", "onClick", "placeholder", "v", "availableLanguages", "TextField", "editingLanguage", "handleMultiTextChange", "allValues", "props", "Array", "isArray", "_", "initAllValues", "lang", "currrentValueIndex", "newLang", "handleLanguageChange", "setState", "handleTextChange", "MultiItem", "disabled", "this", "event", "for<PERSON>ach", "c", "MakeSelectOptions", "Component", "ProfileTab", "t", "useTranslation", "setValue", "path", "field", "val", "personUrl", "AddItem", "Checkbox", "checked", "icon", "size", "ItemActions", "first", "identifier", "last", "enableAction", "deleteItem", "moveItemUp", "moveItemDown", "AddItemButton", "onSubmit", "ItemHeading", "title", "isOpen", "<PERSON><PERSON><PERSON>", "Form", "streetAddress", "addressLocality", "addressRegion", "addressCountry", "postalCode", "sameAs", "uuidv4", "hoursAvailable", "contactType", "ItemActionEnable", "validThrough", "<PERSON><PERSON>", "AddressTab", "useContext", "telephone", "description", "ContactsTab", "contactPoint", "filter", "MarkdownHelpText", "Trans", "i18nKey", "rel", "href", "TextArea", "rows", "Availablity", "availableAtOrFrom", "addAvailability", "availabilityStarts", "availabilityEnds", "deliveryLeadTime", "substring", "ObjectiveTab", "seeks", "fullPath", "emptyItem", "hasOccupation", "responsibilities", "subjectOf", "organizer", "<PERSON><PERSON><PERSON>", "startDate", "endDate", "WorkTab", "educationalLevel", "EducationTab", "altidentifier", "AwardsTab", "ExtrasTab", "LanguagesTab", "useEffect", "ReferencesTab", "MembershipsTab", "LeftSidebar", "context", "Contacts", "renderTabs", "Onyx", "Photo", "src", "alt", "Profile", "color", "ContactItem", "link", "Heading", "Objective", "source", "WorkItem", "role", "start", "end", "Work", "EducationItem", "major", "grade", "Education", "AwardItem", "Awards", "CertificationItem", "Certifications", "HobbyItem", "backgroundColor", "hobby", "Memberships", "SkillItem", "skill", "Skills", "LanguageItem", "level", "rating", "from", "i", "Languages", "ReferenceItem", "position", "References", "ExtraItem", "Extras", "fontFamily", "Image", "<PERSON><PERSON><PERSON>", "Header", "borderColor", "hobbies", "Gengar", "FullName", "Castform", "borderWidth", "PersonalInformation", "light", "Address", "ContactInformation", "G<PERSON><PERSON>", "Subtitle", "top", "minHeight", "styles", "header", "left", "right", "zIndex", "flexDirection", "justifyContent", "alignItems", "paddingLeft", "marginLeft", "<PERSON><PERSON><PERSON>", "Subnames", "elem", "familynameIndex", "join", "Names", "fontSize", "AddressItem", "Contact", "SectionSkillsItem", "SectionSkills", "WorkResponsibilityItem", "WorkResponsibility", "endsWith", "workSkills", "flatten", "awardSkills", "educationSkills", "coursesSkills", "educationProjectSkills", "interactionTeachSkills", "interactionAssessSkills", "userSkills", "component", "preview", "OnyxPreview", "PikachuPreview", "GengarPreview", "CastformPreview", "GlaliePreview", "TemplatesTab", "toLowerCase", "colorOptions", "ColorsTab", "copyColorToClipboard", "text", "textArea", "document", "createElement", "padding", "border", "outline", "boxShadow", "append<PERSON><PERSON><PERSON>", "focus", "select", "successful", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "copyToClipboard", "toast", "bodyClassName", "fontOptions", "FontsTab", "ActionsTab", "fileInputRef", "useRef", "ref", "FileReader", "addEventListener", "importedObject", "readAsText", "files", "importJson", "click", "dataclone", "javascript_part1", "javascript", "zip", "JSZip", "file", "generateAsync", "content", "saveAs", "AboutTab", "SettingsTab", "RightSidebar", "Templates", "Colors", "Fonts", "Actions", "Settings", "About", "PageController", "pageContext", "zoomIn", "zoomOut", "print", "PrintDialog", "printTypes", "setQuality", "setType", "stopPropagation", "preventDefault", "min", "max", "step", "PanZoomAnimation", "animationVisible", "setAnimationVisible", "animation", "autoPlay", "muted", "loop", "App", "changeLanguage", "storedState", "getItem", "fallback", "minZoom", "autoCenterZoomLevel", "enableBoundingBox", "boundaryRatioVertical", "boundaryRatioHorizontal", "find", "configure", "autoClose", "closeButton", "hideProgressBar", "POSITION", "BOTTOM_RIGHT", "ReactDOM", "render", "StrictMode", "getElementById", "URL", "process", "origin", "fetch", "headers", "response", "contentType", "status", "indexOf", "ready", "unregister", "reload", "checkValidServiceWorker"], "mappings": "wk5RAAAA,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,mBCA3CD,EAAOC,QAAU,IAA0B,qC,kZCE5BC,I,wECQA,GACbC,UACAC,YACAC,OACAC,YACAC,SACAC,iBACAC,YACAC,aACAC,U,gDCZa,GACbC,YACAC,SACAC,QACAC,UACAC,WACAC,SCTa,GACbf,MACAgB,cACAC,gB,QCLajB,I,iFCQA,GACbC,UACAC,YACAC,OACAC,YACAC,SACAC,iBACAC,YACAC,aACAC,U,sDCZa,GACbC,YACAC,SACAC,QACAC,UACAC,WACAC,SCTa,GACbf,MACAgB,cACAC,gB,SCLajB,I,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,MACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,MCiCA,IACbkB,KACAC,KACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,GCxDa,CACb1C,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SHgDA4B,GIzDa,CACb3C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SNiDA6B,GO1Da,CACb5C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,STkDA8B,GU3Da,CACb7C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SZmDA+B,Ga5Da,CACb9C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SfoDAgC,GgB7Da,CACb/C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SlBqDAiC,GmB9Da,CACbhD,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SrBsDAkC,GsB/Da,CACbjD,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SxBuDAmC,GyBhEa,CACblD,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,S3BwDAoC,G4BjEa,CACbnD,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,UCNIR,GAAY,CAChB,CACE6C,KAAM,KACNC,KAAM,qCAER,CACED,KAAM,KACNC,KAAM,0BAER,CACED,KAAM,KACNC,KAAM,kBAER,CACED,KAAM,KACNC,KAAM,sBAER,CACED,KAAM,KACNC,KAAM,gBAER,CACED,KAAM,KACNC,KAAM,wBAER,CACED,KAAM,KACNC,KAAM,qBAER,CACED,KAAM,KACNC,KAAM,2CAER,CACED,KAAM,KACNC,KAAM,gDAER,CACED,KAAM,KACNC,KAAM,sBAER,CACED,KAAM,KACNC,KAAM,4CAER,CACED,KAAM,KACNC,KAAM,oBAER,CACED,KAAM,KACNC,KAAM,6BAER,CACED,KAAM,KACNC,KAAM,wDAER,CACED,KAAM,KACNC,KAAM,wBAER,CACED,KAAM,KACNC,KAAM,0CAER,CACED,KAAM,KACNC,KAAM,sCAIVC,IACGC,IAAIC,KACJD,IAAIE,KACJF,IAAIG,KACJC,KAAK,CACJC,aACAC,IAAK,KACLC,YAAa,KACbC,GAAI,CAAC,MAAO,cAAe,gBAC3BC,UAAW,QAKAV,EAAf,E,cAAeA,IC9ETW,GAAcC,QACW,cAA7BC,OAAOC,SAASC,UAEe,UAA7BF,OAAOC,SAASC,UAEhBF,OAAOC,SAASC,SAASC,MAAM,2DAqCnC,SAASC,GAAgBC,EAAOC,GAC9BC,UAAUC,cACPC,SAASJ,GACTK,MAAK,SAAAC,GACJA,EAAaC,cAAgB,WAC3B,IAAMC,EAAmBF,EAAaG,WACd,MAApBD,IAGJA,EAAiBE,cAAgB,WACA,cAA3BF,EAAiBG,QACfT,UAAUC,cAAcS,YAI1BC,QAAQC,IACN,iHAKEb,GAAUA,EAAOc,UACnBd,EAAOc,SAAST,KAMlBO,QAAQC,IAAI,sCAGRb,GAAUA,EAAOe,WACnBf,EAAOe,UAAUV,WAO5BW,OAAM,SAAAC,GACLL,QAAQK,MAAM,4CAA6CA,M,uHC5F3DC,GAAO,SAACC,EAAOC,EAASC,GAC5B,IAAMC,EAAQH,EAAMI,WAAU,SAAAC,GAAI,OAAIA,EAAKC,KAAOL,EAAQK,MACpDC,EAAWJ,EAAQD,EACzB,KAAIK,EAAW,GAAKA,IAAaP,EAAMQ,QAAvC,CACA,IAAMC,EAAU,CAACN,EAAOI,GAAUG,MAAK,SAACC,EAAGC,GAAJ,OAAUD,EAAIC,KACrDZ,EAAMa,OAAOJ,EAAQ,GAAI,EAAGT,EAAMS,EAAQ,IAAKT,EAAMS,EAAQ,OAGzDK,GAAW,SAAAC,GAEfA,EAAMA,EAAIC,QADa,oCACW,SAACC,EAAGC,EAAGC,EAAGP,GAAV,OAAgBM,EAAIA,EAAIC,EAAIA,EAAIP,EAAIA,KACtE,IAAMQ,EAAS,4CAA4CC,KAAKN,GAChE,OAAOK,EACH,CACEF,EAAGI,SAASF,EAAO,GAAI,IACvBD,EAAGG,SAASF,EAAO,GAAI,IACvBR,EAAGU,SAASF,EAAO,GAAI,KAEzB,MAwBAG,GAAW,SAAAC,GAAQ,OAAIA,EAAS,CAAEC,KAAM,eAExCC,GAAU,SAACF,EAAUG,EAAKC,GAC9BJ,EAAS,CACPC,KAAM,WACNI,QAAS,CACPF,MACAC,WAIJL,GAASC,IAiDPM,GAAiB,KACfC,GAAY,SAACC,EAASC,EAAYC,EAAST,GAC/C,IAAGK,GAGH,OAAO,IAAIK,SAAQ,SAAAC,GACjBH,EAAWI,QAAQC,WAAW,GAC9BL,EAAWI,QAAQE,QAEnBT,GAAiBU,YAAW,WAC1BC,KAAYT,EAAQK,QAAS,CAC3BK,MAAO,EACPC,SAAS,EACTC,YAAY,IACX3D,MAAK,SAAA4D,GACN,IAAMC,EAAQD,EAAOE,UAAU,aAAcb,EAAU,KACjDc,EAAM,IAAIC,GAAM,CACpBC,YAAa,WACbC,KAAM,KACNC,OAAiB,kBAAT3B,EAA2B,CAACoB,EAAOQ,MAAOR,EAAOS,QAAU,OAG/DC,EAAYP,EAAIQ,SAASC,SAASC,WAClCC,EAAaX,EAAIQ,SAASC,SAASG,YAEnCC,EAAaN,EAAYV,EAAOQ,MAChCS,EAAcH,EAAad,EAAOS,OAClCS,EAAQF,EAAaC,EAAcA,EAAcD,EAEjDG,EAAcnB,EAAOQ,MAAQU,EAC7BE,EAAepB,EAAOS,OAASS,EAEjCG,EAAU,EACVC,EAAU,EAED,kBAAT1C,IACFyC,GAAWX,EAAYS,GAAe,EACtCG,GAAWR,EAAaM,GAAgB,GAG1CjB,EAAIoB,SAAStB,EAAO,OAAQoB,EAASC,EAASH,EAAaC,EAAc,KAAM,QAC/EjB,EAAIqB,KAAJ,mBAAqBC,KAAKC,MAA1B,SACAzC,GAAiB,KACjBM,SAED,SAIHoC,GAA0B,KACxBC,GAAqB,SAACzC,EAASC,EAAYC,GAC/C,IAAGsC,GAGH,OAAO,IAAIrC,SAAQ,SAAAC,GACjBH,EAAWI,QAAQC,WAAW,GAC9BL,EAAWI,QAAQE,QAEnBiC,GAA0BhC,YAAW,WACnCC,KAAYT,EAAQK,QAAS,CAC3BK,MAAO,EACPC,SAAS,EACTC,YAAY,IACX3D,MAAK,SAAA4D,GACN,IAAMC,EAAQD,EAAOE,UAAU,aAAcb,EAAU,KACjDc,EAAM,IAAIC,GAAM,CACpBC,YAAa,WACbC,KAAM,KACNC,OAAQ,OAGJO,EAAaX,EAAIQ,SAASC,SAASG,YACnCI,EAAchB,EAAIQ,SAASC,SAASC,WACpCO,EAAgBpB,EAAOS,OAASU,EAAenB,EAAOQ,MACxDqB,EAAY,EACZC,EAAaV,EAKjB,IAHAjB,EAAIoB,SAAStB,EAAO,OAAQ,EAAG4B,EAAWV,EAAaC,GACvDU,GAAchB,EAEPgB,GAAc,GACnBD,EAAYC,EAAaV,EACzBjB,EAAI4B,UACJ5B,EAAIoB,SAAStB,EAAO,OAAQ,EAAG4B,EAAWV,EAAaC,GACvDU,GAAchB,EAGhBX,EAAIqB,KAAJ,mBAAqBC,KAAKC,MAA1B,SACAC,GAA0B,KAC1BpC,SAED,SC7LDyC,GAAe,CACnBC,KAAM,CACJC,OAAO,CACL,WAAY,CAChB,oDACA,CACC,OAAU,CACT,MAAO,gBACP,QAAS,UAEV,qBAAsB,CACrB,MAAO,qBACP,QAAS,UAEV,0BAA2B,CAC1B,MAAO,0BACP,QAAS,UAEV,SAAY,CACX,MAAO,kBACP,QAAS,UAEV,UAAa,CACZ,MAAO,mBACP,QAAS,YAIP,SAAU,CACR,CACE,QAAS,gBAEX,CACE,QAAS,SACTC,UAAU,CAAC,CAAC,YAAa,KAAM,SAAS,KACxCC,WAAY,CAAC,CAAC,YAAa,KAAM,SAAS,KAC1CC,QAAS,MAIf7K,QAAS,CACP8K,QAAS,UACTC,MAAO,GACPC,UAAW,GACXC,SAAU,GACVC,SAAU,GACVL,QAAS,CACPM,MAAO,GACPC,MAAO,GACPC,MAAO,IAETC,MAAO,GACPC,QAAS,GACTC,MAAO,IAETC,SAAU,CACR,QAAU,EACVX,QAAS,YAEXD,QAAS,CACP,QAAU,EACVC,QAAS,WAEX7K,UAAW,CACTyL,QAAQ,EACRZ,QAAS,YACTa,KAAM,IAERzL,KAAM,CACJwL,QAAQ,EACRZ,QAAS,kBACTc,MAAO,IAETzL,UAAW,CACTuL,QAAQ,EACRZ,QAAS,YACTc,MAAO,IAETxL,OAAQ,CACNsL,QAAQ,EACRZ,QAAS,kBACTc,MAAO,IAETvL,eAAgB,CACdqL,QAAQ,EACRZ,QAAS,iBACTc,MAAO,IAETC,OAAQ,CACNH,QAAQ,EACRZ,QAAS,SACTc,MAAO,IAETE,YAAa,CACXJ,QAAQ,EACRZ,QAAS,cACTc,MAAO,IAETtL,UAAW,CACToL,QAAQ,EACRZ,QAAS,YACTc,MAAO,IAETrL,WAAY,CACVmL,QAAQ,EACRZ,QAAS,aACTc,MAAO,IAETpL,OAAQ,CACNkL,QAAQ,EACRZ,QAAS,uBACTc,MAAO,KAGXG,MAAO,CACLC,OAAQ,OACRC,KAAM,CACJC,OAAQ,IAEVxL,OAAQ,CACNyL,WAAY,UACZC,QAAS,UACTC,OAAQ,YAGZxL,SAAU,CACRyL,SAAU,OAIRC,GAAU,SAACrH,EAAD,GAA+B,IACzC0G,EADoBxE,EAAoB,EAApBA,KAAMI,EAAc,EAAdA,QAExBgF,EAAWC,KAAKC,MAAMD,KAAKE,UAAUzH,IAE3C,OAAQkC,GACN,IAAK,kBACH,OAAOwF,KAAI,gBAAKJ,GAAN,eAA0BhF,EAAQF,KAAOE,EAAQD,OAC7D,IAAK,WAGH,OAFAqE,EAAQiB,KAAI,gBAAKL,GAAN,UAAqBhF,EAAQF,KAAO,KACzCwF,KAAKtF,EAAQD,OACZqF,KAAI,gBAAKJ,GAAN,UAAqBhF,EAAQF,KAAOsE,GAChD,IAAK,cAGH,OAFAA,EAAQiB,KAAI,gBAAKL,GAAN,UAAqBhF,EAAQF,KAAO,IAC/CyF,KAAOnB,GAAO,SAAAoB,GAAC,OAAIA,EAAE/G,KAAOuB,EAAQD,MAAMtB,MACnC2G,KAAI,gBAAKJ,GAAN,UAAqBhF,EAAQF,KAAOsE,GAChD,IAAK,eAGH,OAFAA,EAAQiB,KAAI,gBAAKL,GAAN,UAAqBhF,EAAQF,KAAO,IAC/C5B,GAAKkG,EAAOpE,EAAQD,OAAQ,GACrBqF,KAAI,gBAAKJ,GAAN,UAAqBhF,EAAQF,KAAOsE,GAChD,IAAK,iBAGH,OAFAA,EAAQiB,KAAI,gBAAKL,GAAN,UAAqBhF,EAAQF,KAAO,IAC/C5B,GAAKkG,EAAOpE,EAAQD,MAAO,GACpBqF,KAAI,gBAAKJ,GAAN,UAAqBhF,EAAQF,KAAOsE,GAChD,IAAK,WACH,OAAOgB,KAAI,gBAAKJ,GAAYhF,EAAQF,IAAKE,EAAQD,OACnD,IAAK,YAEH,OADA0F,aAAaC,QAAQ,QAAST,KAAKE,UAAUH,IACtCA,EACT,IAAK,cACH,GAAgB,OAAZhF,EAAkB,OAAOgD,GAE7B,cAAsB2C,OAAOC,KAAK5C,GAAaC,MAA/C,eAAsD,CAAjD,IAAM4C,EAAO,KACVA,KAAW7F,EAAQiD,OACvBjD,EAAQiD,KAAK4C,GAAW7C,GAAaC,KAAK4C,IAI9C,OAAO,gBACFb,EADL,GAEKhF,GAEP,IAAK,iBACH,OAAO,gBACFgF,EADL,GAEKc,IAEP,IAAK,QACH,OAAO9C,GACT,QACE,OAAOgC,IAIPe,GAAaC,wBAAchD,IACzBiD,GAAaF,GAAbE,SAOKC,GALS,SAAC,GAAkB,IAAhBC,EAAe,EAAfA,SAAe,EACZC,qBAAWrB,GAAS/B,IADR,oBAC/BtF,EAD+B,KACxBiC,EADwB,KAEtC,OAAO,kBAACsG,GAAD,CAAUlG,MAAO,CAAErC,QAAOiC,aAAawG,IAMjCJ,IAFYA,GAAWM,SAEvBN,ICxMTO,GAAcC,IAAMP,cAAc,MAChCC,GAAaK,GAAbL,SAuBKO,GArBS,SAAC,GAAkB,IAAhBL,EAAe,EAAfA,SAAe,EACRM,mBAAS,MADD,oBAC/BtG,EAD+B,KACtBuG,EADsB,OAEFD,mBAAS,MAFP,oBAE/BrG,EAF+B,KAEnBuG,EAFmB,OAGUF,oBAAS,GAHnB,oBAG/BG,EAH+B,KAGZC,EAHY,KAKtC,OACE,kBAAC,GAAD,CACE9G,MAAO,CACLI,UACAuG,aACAtG,aACAuG,gBACAC,oBACAC,uBAGDV,IAQQG,IAFaA,GAAYD,SAEzBC,I,4BCLAQ,GAtBE,SAAC,GAAD,IAAGC,EAAH,EAAGA,UAAWC,EAAd,EAAcA,MAAOjH,EAArB,EAAqBA,MAAOkH,EAA5B,EAA4BA,SAAUC,EAAtC,EAAsCA,QAASC,EAA/C,EAA+CA,WAA/C,OACf,yBAAKJ,UAAW,sBAAuBA,EAAWK,MAAO,CAACC,QAAQ,aAC/DL,GACC,2BAAOD,UAAU,oEACdC,GAGL,yBAAKD,UAAU,4GACb,4BACEA,UAAU,4EACVhH,MAAOA,EACPkH,SAAU,SAAAK,GAAC,OAAIL,EAASK,EAAEC,OAAOxH,SAEhCmH,EAAQM,IAAIL,IAEf,yBAAKJ,UAAU,oGACb,uBAAGA,UAAU,kBAAb,mBCkCOU,GAjDA,SAAC,GAAyC,IAAvCC,EAAsC,EAAtCA,KAAMC,EAAgC,EAAhCA,WAAYC,EAAoB,EAApBA,cAE5BC,EAAW,SAACrC,GAChB,IAAMlH,EAAQoJ,EAAKnJ,WAAU,SAACuJ,GAAD,OAASA,EAAIhI,MAAQ6H,KAE9CnC,EAAI,GAAKlH,EAAQ,GACnBsJ,EAAcF,EAAKpJ,EAAQ,GAAGwB,KAG5B0F,EAAI,GAAKlH,EAAQoJ,EAAK/I,OAAS,GACjCiJ,EAAcF,EAAKpJ,EAAQ,GAAGwB,MAUlC,OACE,yBAAKiH,UAAU,+BACb,yBACEA,UAAU,yEACVgB,QAAS,kBAAMF,GAAU,KAEzB,uBAAGd,UAAU,kBAAb,iBAGA,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAM,GACNgB,YAAY,GACZjI,MAAO4H,EACPV,SAAU,SAAAgB,GAAML,EAAcK,IAC9Bf,QAAWQ,EACXP,WAvBU,SAACW,EAAKxJ,GACtB,OACE,4BAAQwB,IAAKgI,EAAIhI,IAAKC,MAAO+H,EAAIhI,KAC9BgI,EAAIlM,MAAQ,UAuBf,yBACEmL,UAAU,yEACVgB,QAAS,kBAAMF,EAAS,KAExB,uBAAGd,UAAU,kBAAb,oB,wCC3CFmB,GAAqB,CAAC,KAAM,KAAM,KAAM,KAAM,MAU/BC,G,+MACjBzK,MAAQ,CACN0K,gBAAiB,M,EAGnBC,sBAAwB,SAACtI,EAAOzB,GAC9B,IAAIgK,EAAY,EAAKC,MAAMxI,MAK3B,IAJI,EAAKwI,MAAMxI,OAAUyI,MAAMC,QAAQ,EAAKF,MAAMxI,SAC9CuI,EAAY,IAGVI,QAAOJ,IAAYhK,GACvBgK,EAAUhD,KAAK,IAEjBgD,EAAUhK,GAASyB,EAEnB,EAAKwI,MAAMtB,SAASqB,I,EAGtBK,cAAgB,SAACC,GACf,IAAIN,EAAY,EAAKC,MAAMxI,MACvB,EAAKwI,MAAMxI,OAAUyI,MAAMC,QAAQ,EAAKF,MAAMxI,SAChDuI,EAAY,CACV,CACE,YAAaM,EACb,SAAU,MAKhB,IAAIC,EAAqBP,EAAU/J,WAAU,SAAAiH,GAAC,OAAIA,EAAE,eAAiBoD,KACrE,GAAGC,EAAqB,EAAE,CACxB,IAAIC,EAAU,CACZ,YAAaF,EACb,SAAU,IAEZN,EAAUhD,KAAKwD,GACf,EAAKP,MAAMtB,SAASqB,GAGtB,OADAO,EAAqBP,EAAU/J,WAAU,SAAAiH,GAAC,OAAIA,EAAE,eAAiBoD,M,EAGnEG,qBAAuB,SAACH,GACtB,EAAKD,cAAcC,GAEnB,EAAKI,SAAS,CACZZ,gBAAiBQ,K,EAGrBK,iBAAmB,SAACL,EAAM7I,GACxB,IAAI8I,EAAqB,EAAKF,cAAcC,GACxCN,EAAY,EAAKC,MAAMxI,MAE3BuI,EAAUO,GAAoB,UAAY9I,EAC1C,EAAKwI,MAAMtB,SAASqB,I,EAGtBY,UAAY,SAAC1D,EAAGlH,GAAJ,OACV,yBAAKwB,IAAK,UAAUxB,EAAO8I,MAAO,CAACC,QAAS,SAC1C,2BACEN,UAAU,wKACVnH,KAAM,EAAK2I,MAAM3I,KACjBuJ,SAAU,EAAKZ,MAAMY,SACrBpJ,MAAO,EAAKwI,MAAMxI,MAAMzB,GACxB2I,SAAU,SAAAK,GAAC,OAAI,EAAKe,sBAAsBf,EAAEC,OAAOxH,MAAOzB,IAC1D0J,YAAa,EAAKO,MAAMP,YACxBlI,IAAK,SAASxB,IAEdoK,QAAO,EAAKH,MAAMxI,QAAQ,EAAM,GAClC,4BACEH,KAAK,SACLmI,QAAS,WAAKW,UAAS,EAAKH,MAAMxI,MAAOzB,GAAO,EAAKiK,MAAMtB,SAAS,EAAKsB,MAAMxI,QAC/EgH,UAAU,iFACVjH,IAAK,UAAUxB,GAEf,yBAAKyI,UAAU,oBAAoBjH,IAAK,gBAAgBxB,GACtD,uBAAGyI,UAAU,qCAAqCjH,IAAK,UAAUxB,GAAjE,c,wDAME,IAAD,OACP,OACJ,yBAAKyI,UAAS,+BAA0BqC,KAAKb,MAAMxB,YAC1CqC,KAAKb,MAAMvB,OACV,2BAAOD,UAAU,oEACdqC,KAAKb,MAAMvB,OAGK,cAAlBoC,KAAKb,MAAM3I,KACZ,yBAAKwH,MAAO,CAACC,QAAS,SACpB,4BAAQtH,MAAOqJ,KAAK1L,MAAM0K,gBAAiBnB,SAAU,SAACoC,GAAD,OAAW,EAAKN,qBAAqBM,EAAM9B,OAAOxH,SApG3F,WACxB,IAAImH,EAAU,GAId,OAHAgB,GAAmBoB,SAAQ,SAACC,GAC1BrC,EAAQ5B,KAAK,4BAAQxF,IAAKyJ,EAAGxJ,MAAOwJ,GAAIA,OAEnCrC,EAgGUsC,IAEL,2BACEzC,UAAU,wKACVnH,KAAMwJ,KAAKb,MAAM3I,KACjBuJ,SAAUC,KAAKb,MAAMY,SACrBpJ,MAAOqJ,KAAKb,MAAMxI,MAAQqJ,KAAKb,MAAMxI,MAAMxB,WAAU,SAAAiH,GAAC,OAAIA,EAAE,eAAiB,EAAK9H,MAAM0K,oBAAkB,EAAIgB,KAAKb,MAAMxI,MAAMxB,WAAU,SAAAiH,GAAC,OAAIA,EAAE,eAAiB,EAAK9H,MAAM0K,mBAAoB,GAAI,UACpMnB,SAAU,SAAAK,GAAC,OAAI,EAAK2B,iBAAiB,EAAKvL,MAAM0K,gBAAiBd,EAAEC,OAAOxH,QAC1EiI,YAAaoB,KAAKb,MAAMP,eAIT,cAAlBoB,KAAKb,MAAM3I,KACV,6BACGwJ,KAAKb,MAAMxI,MAAMyH,IAAI4B,KAAKF,WAC3B,yBAAKpJ,IAAI,cAAcsH,MAAO,CAACC,QAAS,SACtC,4BACEzH,KAAK,SACLmI,QAAS,WAAK,EAAKQ,MAAMxI,MAAMuF,KAAK,IAAI,EAAKiD,MAAMtB,SAAS,EAAKsB,MAAMxI,QACvEgH,UAAU,iFACVjH,IAAI,eAEJ,yBAAKiH,UAAU,oBAAoBjH,IAAI,kBACrC,uBAAGiH,UAAU,qCAAqCjH,IAAI,YAAtD,WAMR,2BACEiH,UAAU,wKACVnH,KAAMwJ,KAAKb,MAAM3I,KACjBuJ,SAAUC,KAAKb,MAAMY,SACrBpJ,MAAOqJ,KAAKb,MAAMxI,MAClBkH,SAAU,SAAAK,GAAC,OAAI,EAAKiB,MAAMtB,SAASK,EAAEC,OAAOxH,QAC5CiI,YAAaoB,KAAKb,MAAMP,mB,GAhIDzB,IAAMkD,WCiF9BC,GAxFI,SAAC,GAAwB,IAAtBzG,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAClB0C,EAAMC,aAAe,eAArBD,EAEFE,EAAW,SAACC,EAAMC,EAAO9B,GAA2B,IAAxBrI,EAAuB,uDAAlB,KAAMnB,EAAY,uDAAT,KAC1CuL,EAAMtB,OAAMzF,EAAM6G,EAAK,IAAIC,EAAO,MAC3B,OAARC,IACgB,kBAAP/B,GAAiC,kBAAPA,EAClCS,OAAMzF,EAAM6G,EAAK,IAAIC,EAAO,IACP,kBAAP9B,IACTO,MAAMC,QAAQR,GACfS,OAAMzF,EAAM6G,EAAK,IAAIC,EAAO,IAE5BrB,OAAMzF,EAAM6G,EAAK,IAAIC,EAAO,MAKpC9C,EAAS,QAAQ6C,EAAK,IAAIC,EAAO9B,GAC9BxJ,GACDwI,EAAS,QAAQ6C,EAAK,UAAWrL,GAEhCmB,GACDqH,EAAS,QAAQ6C,EAAK,YAAalK,IAGvC,OACE,6BACE,kBAAC,GAAD,CACEmH,UAAU,OACViB,YAAY,UACZjI,MAAOkD,EAAKzK,QAAQ8K,QACpB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,uBAAwBgB,MAGlD,wBAAIlB,UAAU,SAEd,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,0BACT3B,YAAY,0BACZjI,MAAO2I,OAAMzF,EAAM,uCAAwC,IAC3DgE,SAAU,SAAAgB,GAAM4B,EAAS,4BAA6B,aAAc5B,EAAG,cAAegC,eAGxF,yBAAKlD,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,2BACT3B,YAAY,OACZjI,MAAO2I,OAAMzF,EAAK,gCAAiC,IACnDgE,SAAU,SAAAgB,GAAC,OAAI4B,EAAS,sBAAuB,YAAa5B,IAC5DrI,KAAK,cAGP,kBAAC,GAAD,CACEmH,UAAU,OACVC,MAAO2C,EAAE,0BACT3B,YAAY,MACZjI,MAAO2I,OAAMzF,EAAK,iCAAkC,IACpDgE,SAAU,SAAAgB,GAAC,OAAI4B,EAAS,sBAAuB,aAAc5B,IAC7DrI,KAAK,eAIT,kBAAC,GAAD,CACEmH,UAAU,OACVC,MAAO2C,EAAE,0BACT3B,YAAY,2BACZjI,MAAO2I,OAAMzF,EAAM,kCAAmC,IACtDgE,SAAU,SAAAgB,GAAM4B,EAAS,sBAAuB,cAAe5B,MAGjE,wBAAIlB,UAAU,SAEd,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,yBACT3B,YAAY,aACZjI,MAAO2I,OAAMzF,EAAK,6BAA8B,IAChDgE,SAAU,SAAAgB,GAAC,OAAI4B,EAAS,sBAAuB,SAAU5B,IACzDiC,QAAS,aACTtK,KAAK,gB,UC7DEuK,GAxBE,SAAC,GAA0D,IAAxDC,EAAuD,EAAvDA,QAASnD,EAA8C,EAA9CA,SAA8C,IAApCoD,YAAoC,MAA7B,QAA6B,MAApBC,YAAoB,MAAb,OAAa,EACzE,OACE,yBACEvD,UAAU,2KACVK,MAAO,CAAE5F,MAAO8I,EAAM7I,OAAQ6I,IAE9B,2BACE1K,KAAK,WACLwH,MAAO,CAAE5F,MAAO8I,EAAM7I,OAAQ6I,GAC9BvD,UAAU,yCACVqD,QAASA,EACTnD,SAAU,SAAAK,GAAC,OAAIL,EAASK,EAAEC,OAAO6C,YAEnC,uBACErD,UAAS,kCACPqD,EAAU,cAAgB,YADnB,2BAIRC,KC2CME,GAzDK,SAAC,GAA+E,IAA7E5K,EAA4E,EAA5EA,SAAU6K,EAAkE,EAAlEA,MAAOC,EAA2D,EAA3DA,WAAYjM,EAA+C,EAA/CA,KAAMkM,EAAyC,EAAzCA,KAAMzD,EAAmC,EAAnCA,SAAUrH,EAAyB,EAAzBA,KAAM+K,EAAmB,EAAnBA,aACtEhB,EAAMC,eAAND,EAER,OACE,yBAAK5C,UAAU,wBACb,yBAAKA,UAAU,qBACd4D,EAAeA,EAAaF,EAAYjM,EAAMyI,GAC7C,kBAAC,GAAD,CACEqD,KAAK,UACLF,QAAS5L,EAAK0F,OACd+C,SAAU,SAAAgB,GACRhB,EAAS,GAAD,OAAIwD,EAAJ,UAAwBxC,MAKpC,4BACErI,KAAK,SACLmI,QAAS,kBRoCA,SAACpI,EAAUG,EAAKC,GACjCJ,EAAS,CACPC,KAAM,cACNI,QAAS,CACPF,MACAC,WAIJL,GAASC,GQ7CciL,CAAWjL,EAAUC,EAAMpB,IAC1CuI,UAAU,qFAEV,yBAAKA,UAAU,qBACb,uBAAGA,UAAU,2CAAb,UACA,0BAAMA,UAAU,WAAW4C,EAAE,4BAKnC,yBAAK5C,UAAU,SACXyD,GACA,4BACE5K,KAAK,SACLmI,QAAS,kBRkCF,SAACpI,EAAUG,EAAKC,GACjCJ,EAAS,CACPC,KAAM,eACNI,QAAS,CACPF,MACAC,WAIJL,GAASC,GQ3CgBkL,CAAWlL,EAAUC,EAAMpB,IAC1CuI,UAAU,uFAEV,yBAAKA,UAAU,qBACb,uBAAGA,UAAU,sCAAb,mBAKJ2D,GACA,4BACE9K,KAAK,SACLmI,QAAS,kBRkCA,SAACpI,EAAUG,EAAKC,GACnCJ,EAAS,CACPC,KAAM,iBACNI,QAAS,CACPF,MACAC,WAIJL,GAASC,GQ3CgBmL,CAAanL,EAAUC,EAAMpB,IAC5CuI,UAAU,kFAEV,yBAAKA,UAAU,qBACb,uBAAGA,UAAU,sCAAb,uBChCCgE,GAnBO,SAAC,GAAkB,IAAhBC,EAAe,EAAfA,SACfrB,EAAMC,eAAND,EAER,OACE,6BACE,4BACE/J,KAAK,SACLmI,QAASiD,EACTjE,UAAU,kFAEV,yBAAKA,UAAU,qBACb,uBAAGA,UAAU,2CAAb,OACA,0BAAMA,UAAU,WAAW4C,EAAE,0BCIxBsB,GAhBK,SAAC,GAAyC,IAAvCC,EAAsC,EAAtCA,MAAO5H,EAA+B,EAA/BA,QAAS6H,EAAsB,EAAtBA,OAAQC,EAAc,EAAdA,QACrCzB,EAAMC,eAAND,EAER,OACE,yBACE5C,UAAU,mDACVgB,QAAS,kBAAMqD,GAASD,KAExB,wBAAIpE,UAAU,uBACQ,qBAAZzD,EAA0B4H,EAAQvB,EAAE,WAAY,CAAErG,aAE5D,uBAAGyD,UAAU,kBAAkBoE,EAAS,cAAgB,iBCqCxDE,GAAO,SAAC,GAAyC,IAAvC7M,EAAsC,EAAtCA,KAAMyI,EAAgC,EAAhCA,SAAgC,IAAtBwD,kBAAsB,MAAT,GAAS,EAC5Cd,EAAMC,aAAe,CAAC,cAAe,QAArCD,EAER,OACE,6BACE,kBAAC,GAAD,CACE5C,UAAU,OACVC,MAAO2C,EAAE,+BACT3B,YAAY,eACZjI,MAAOvB,EAAK8M,cACZrE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,iBAA+BxC,MAGxD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,iCACT3B,YAAY,UACZjI,MAAOvB,EAAK+M,gBACZtE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,mBAAiCxC,MAG1D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,+BACT3B,YAAY,KACZjI,MAAOvB,EAAKgN,cACZvE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,iBAA+BxC,MAGxD,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,gCACT3B,YAAY,SACZjI,MAAOvB,EAAKiN,eACZxE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,kBAAgCxC,MAGzD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,4BACT3B,YAAY,UACZjI,MAAOvB,EAAKkN,WACZzE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,cAA4BxC,OAIvD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,wBACT3B,YAAY,4BACZjI,MAAOvB,EAAKmN,OACZ1E,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,UAAwBxC,QAMjDiC,GAAU,SAAC,GAA2B,IAAzB5G,EAAwB,EAAxBA,QAAS3D,EAAe,EAAfA,SACtBlB,EAAK,KAAKmN,eAD2B,EAEfnF,oBAAS,GAFM,oBAElC0E,EAFkC,KAE1BC,EAF0B,OAGjB3E,mBAAS,CAC/B,MAAOhI,EACP,QAAS,gBACToN,eAAe,CACb,MAAOpN,EAAG,kBACV,QAAS,4BACT,aAAgB,cAElBgN,eAAgB,GAChBH,cAAe,GACfE,cAAe,GACfD,gBAAiB,GACjBG,WAAY,GACZI,YAAa,GACbH,OAAQ,KAjB+B,oBAGlCnN,EAHkC,KAG5BkH,EAH4B,KA+CzC,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAhCP,SAACnH,EAAKC,GAAN,OAAgB2F,EAAQN,KAAI,gBAAK5G,GAAQsB,EAAKC,OAkCzD,kBAAC,GAAD,CAAeiL,SAjCJ,WACf,IAAIvM,EAAK,KAAKmN,eACe,KAAxBpN,EAAKiN,iBAEV5L,GAAQF,EAAU,mCAAoCnB,GAEtDkH,EAAQ,CACN,MAAOjH,EACP,QAAS,gBACToN,eAAe,CACb,MAAOpN,EAAG,kBACV,QAAS,4BACT,aAAgB,cAElBgN,eAAgB,GAChBH,cAAe,GACfE,cAAe,GACfD,gBAAiB,GACjBG,WAAY,GACZI,YAAa,GACbH,OAAQ,KAGVP,GAAQ,UAgBNW,GAAmB,SAACtB,EAAYjM,EAAMyI,GAE1C,OACE,kBAAC,GAAD,CACEqD,KAAK,UACLF,QAAU5L,GAAQA,EAAKqN,gBAAkBrN,EAAKqN,eAAeG,cAAiBvJ,KAAKyC,MAAM1G,EAAKqN,eAAeG,cAAgBvJ,KAAKyC,MAAM,IAAIzC,MAAS,EACrJwE,SAAU,SAAAgB,GACR,IAAI+D,EAAe,aAChB/D,IAAG+D,EAAe,cACrB/E,EAAS,GAAD,OAAIwD,EAAJ,+BAA6CuB,OAMvDC,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,2CAAuCnM,EAAvC,MAEhB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAO1M,EAAK8M,eAAiB9M,EAAKiN,eAAgBL,QAASA,EAASD,OAAQA,IAEzF,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,IAElD,kBAAC,GAAD,CACE9K,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,mCACL+K,aAAcoB,QAOTG,GArMI,SAAC,GAAwB,IAAtBjJ,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAElBtH,EADQwM,qBAAWpG,IACnBpG,SAER,OACE,oCACE,yBAAKoH,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CAAUqD,QAASnH,EAAKI,QAAQa,OAAQ+C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,sBAAuBgB,OAEzF,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAOkD,EAAKI,QAAQC,QACpB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,uBAAwBgB,QAKtD,wBAAIlB,UAAU,SAEb9D,EAAKC,OAAO,UAAU,GAAGG,SAAWJ,EAAKC,OAAO,UAAU,GAAGG,QAAQmE,KAAI,SAAChC,EAAGlH,GAAJ,OACxE,kBAAC,GAAD,CACEqB,SAAUA,EACV6K,MAAiB,IAAVlM,EACPA,MAAOA,EACPE,KAAMgH,EACN1F,IAAK0F,EAAE,OACPkF,KAAMpM,IAAU2E,EAAKC,OAAO,UAAU,GAAGG,QAAQ1E,OAAS,EAC1DsI,SAAUA,OAId,kBAAC,GAAD,CAAS3D,QAASL,EAAKI,QAAQC,QAAS3D,SAAUA,MCMlD0L,GAAO,SAAC,GAAyC,IAAvC7M,EAAsC,EAAtCA,KAAMyI,EAAgC,EAAhCA,SAAgC,IAAtBwD,kBAAsB,MAAT,GAAS,EAC5Cd,EAAMC,aAAe,CAAC,cAAe,QAArCD,EAQR,OACE,6BACE,kBAAC,GAAD,CACE5C,UAAU,OACVC,MAAO2C,EAAE,uBACT3B,YAAY,mBACZjI,MAAOvB,EAAK4N,UACZnF,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,aAA2BxC,MAGpD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,uBACT3B,YAAY,wBACZjI,MAAOvB,EAAKwF,MACZiD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,SAAuBxC,MAGhD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,6BACT3B,YAAY,oCACZjI,MAAOvB,EAAKsN,YACZ7E,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,eAA6BxC,IACpDf,QAAW,CAAC,YAAa,YAAa,SACtCC,WAhCoB,SAAC3B,EAAGlH,GAC5B,OACE,4BAAQwB,IAAK0F,EAAGzF,MAAOyF,GACpBA,MAgCH,kBAAC,GAAD,CACEuB,UAAU,OACVC,MAAO2C,EAAE,gCACT3B,YAAY,cACZjI,MAAOvB,EAAK6N,YACZpF,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,eAA6BxC,QAMtDiC,GAAU,SAAC,GAA2B,IAAzB5G,EAAwB,EAAxBA,QAAS3D,EAAe,EAAfA,SACtBlB,EAAK,KAAKmN,eAD2B,EAEfnF,oBAAS,GAFM,oBAElC0E,EAFkC,KAE1BC,EAF0B,OAGjB3E,mBAAS,CAC/B,MAAOhI,EACP,QAAS,eACT4N,YAAa,GACbP,YAAa,YACb9H,MAAO,GACPoI,UAAW,KAT4B,oBAGlC5N,EAHkC,KAG5BkH,EAH4B,KA+BzC,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAxBP,SAACnH,EAAKC,GAAN,OAAgB2F,EAAQN,KAAI,gBAAK5G,GAAQsB,EAAKC,OA0BzD,kBAAC,GAAD,CAAeiL,SAzBJ,WACf,IAAIvM,EAAK,KAAKmN,eACY,KAArBpN,EAAKsN,cAEVjM,GAAQF,EAAU,wCAAyCnB,GAE3DkH,EAAQ,CACN,MAAOjH,EACP,QAAS,eACT4N,YAAa,GACbP,YAAa,YACb9H,MAAO,GACPoI,UAAW,KAGbhB,GAAQ,UAgBNW,GAAmB,SAACtB,EAAYjM,EAAMyI,GAE1C,OACE,sCAIEgF,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,gDAA4CnM,EAA5C,MAEhB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAO1M,EAAKsN,aAAetN,EAAK4N,UAAWhB,QAASA,EAASD,OAAQA,IAElF,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,IAElD,kBAAC,GAAD,CACE9K,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,wCACL+K,aAAcoB,QAOTO,GAnKK,SAAC,GAAwB,IAAtBrJ,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAEnBtH,EADQwM,qBAAWpG,IACnBpG,SAER,OACE,oCACE,yBAAKoH,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CAAUqD,QAASnH,EAAKgB,SAASC,OAAQ+C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,uBAAwBgB,OAE3F,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAOkD,EAAKgB,SAASX,QACrB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,wBAAyBgB,QAKvD,wBAAIlB,UAAU,SAEb9D,EAAKC,OAAO,UAAU,GAAGqJ,cAAgBtJ,EAAKC,OAAO,UAAU,GAAGqJ,aAAaC,QAAO,SAAAhH,GAAC,MAAmB,cAAhBA,EAAEsG,eAA4BtE,KAAI,SAAChC,EAAGlH,GAAJ,OAC3H,kBAAC,GAAD,CACEqB,SAAUA,EACV6K,MAAiB,IAAVlM,EACPA,MAAOA,EACPE,KAAMgH,EACN1F,IAAK0F,EAAE,OACPkF,KAAMpM,IAAU2E,EAAKC,OAAO,UAAU,GAAGqJ,aAAa5N,OAAS,EAC/DsI,SAAUA,OAId,kBAAC,GAAD,CAAS3D,QAASL,EAAKgB,SAASX,QAAS3D,SAAUA,M,UCvB1C8M,GArBU,SAAC,GAAmB,IAAjB1F,EAAgB,EAAhBA,UAC1B,OACE,yBAAKA,UAAWA,GACd,uBAAGA,UAAU,yBACX,kBAAC2F,GAAA,EAAD,CAAOC,QAAQ,oBAAf,cAEE,uBACE5F,UAAU,gCACVQ,OAAO,SACPqF,IAAI,sBACJC,KAAK,oEAJP,4BAFF,qCCaOC,GAjBE,SAAC,GAAD,IAAG9F,EAAH,EAAGA,MAAOgB,EAAV,EAAUA,YAAajI,EAAvB,EAAuBA,MAAOkH,EAA9B,EAA8BA,SAAUF,EAAxC,EAAwCA,UAAxC,IAAmDgG,YAAnD,MAA0D,EAA1D,SACf,yBAAKhG,UAAS,+BAA0BA,IACtC,2BAAOA,UAAU,oEACdC,GAEH,8BACED,UAAU,oKACVgG,KAAMA,EACNhN,MAAOA,EACPkH,SAAU,SAAAK,GAAC,OAAIL,EAASK,EAAEC,OAAOxH,QACjCiI,YAAaA,IAGf,kBAAC,GAAD,CAAkBjB,UAAU,WCsC1BiG,GAAc,SAAC,GAAsB,IAArBxO,EAAoB,EAApBA,KAAMyI,EAAc,EAAdA,SACtBzI,IACFA,EAAO,IAF+B,IAIhCmL,EAAMC,aAAe,CAAC,cAAe,QAArCD,EACR,OACE,6BACE,kBAAC,GAAD,CACE5C,UAAU,OACVC,MAAO2C,EAAE,iCACT3B,YAAY,UACZjI,MAAO2I,OAAMlK,EAAK,kBAAmB,IACrCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,kBAAmBgB,MAG7C,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,+BACT3B,YAAY,KACZjI,MAAO2I,OAAMlK,EAAK,gBAAgB,IAClCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,gBAAiBgB,MAG3C,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,gCACT3B,YAAY,SACZjI,MAAO2I,OAAMlK,EAAK,iBAAiB,IACnCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,iBAAkBgB,SAO9CoD,GAAO,SAAC,GAAkD,IAAhD7M,EAA+C,EAA/CA,KAAMyI,EAAyC,EAAzCA,SAAyC,IAA/BwD,kBAA+B,MAAlB,GAAkB,MAAdnM,aAAc,MAAR,EAAQ,EAErDqL,EAAMC,aAAe,CAAC,cAAe,QAArCD,EAmBR,OACE,6BACY,IAARrL,EAAc,kBAAC,GAAD,CACdyO,KAAK,KACLhG,UAAU,OACVC,MAAO2C,EAAE,6BACT5J,MAAO2I,OAAMlK,EAAK,cAAe,IACjCwJ,YAAY,4OACZf,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,eAA6BxC,MAC/C,qCAEP,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,sCACT3B,YAAY,aACZjI,MAAO2I,OAAMlK,EAAK,qBAAsB,IACxCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,sBAAoCxC,MAG7D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,oCACT3B,YAAY,aACZxJ,KAAMkK,OAAMlK,EAAM,mBAAoB,IACtCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,oBAAkCxC,MAG3D,kBAAC,GAAD,CACEzJ,KAAMkK,OAAMlK,EAAM,4BAA6B,IAC/CyI,SAAU,SAACnH,EAAKC,GAAN,OA/CQ,SAACA,EAAOD,EAAKmN,GACnC,GAAGlN,GAASD,EAAI,CACd,IAAIuD,EAAUqF,OAAMuE,EAAkB,UAAW,IAC7C5J,EAAQ,WACVA,EAAQ,SAAW,gBACnBA,EAAQ,OAAS7E,EAAK,OAAO,8BAE/B6E,EAAQvD,GAAOC,EAEf2I,OAAMuE,EAAmB,UAAW5J,GAChC4J,EAAkB,WACpBA,EAAkB,SAAW,QAC7BA,EAAkB,OAASzO,EAAK,OAAO,sBAEzCyI,EAAS,GAAD,OAAIwD,EAAJ,qBAAmCwC,IAiCfC,CAAgBnN,EAAOD,EAAKtB,EAAKyO,wBAO7D/C,GAAU,SAAC,GAAiC,IAA/B5G,EAA8B,EAA9BA,QAAS3D,EAAqB,EAArBA,SAAU2K,EAAW,EAAXA,KAEhC7L,EAAK,KAAKmN,eAFiC,EAGrBnF,oBAAS,GAHY,oBAGxC0E,EAHwC,KAGhCC,EAHgC,OAIvB3E,mBAAS,CAC/B,MAAOhI,EACP,QAAS,SACT4N,YAAa,GACbc,mBAAoB,GACpBC,iBAAkB,GAClBH,kBAAmB,GACnBI,iBAAkB,KAX2B,oBAIxC7O,EAJwC,KAIlCkH,EAJkC,KAkC/C,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAzBP,SAACnH,EAAKC,GAAN,OAAgB2F,EAAQN,KAAI,gBAAK5G,GAAQsB,EAAKC,KAyBnBzB,MAAOgM,IAE7C,kBAAC,GAAD,CAAeU,SA1BJ,WACf,IAAIvM,EAAK,KAAKmN,eACY,KAArBpN,EAAK6N,aAAsB7N,EAAKyO,oBAAsB,KAE3DpN,GAAQF,EAAU,iCAAkCnB,GAEpDkH,EAAQ,CACN,MAAOjH,EACP,QAAS,SACT4N,YAAa,GACbc,mBAAoB,GACpBC,iBAAkB,GAClBH,kBAAmB,GACnBI,iBAAkB,KAGpBjC,GAAQ,UAgBNW,GAAmB,SAACtB,EAAYjM,EAAMyI,GAExC,OACA,kBAAC,GAAD,CACEqD,KAAK,UACLF,QAAU5L,GAAQA,EAAK4O,kBAAqB3K,KAAKyC,MAAM1G,EAAK4O,kBAAoB3K,KAAKyC,MAAM,IAAIzC,MAAS,EACxGwE,SAAU,SAAAgB,GACR,IAAImF,EAAmB,aACpBnF,IAAGmF,EAAmB,cACzBnG,EAAS,GAAD,OAAIwD,EAAJ,oBAAkC2C,OAM5CnB,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,yCAAqCnM,EAArC,MAEhB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAO1M,EAAKyO,kBAAkB5J,QAAQoI,gBAAmBjN,EAAK6N,YAAYiB,UAAU,EAAG,IAAI,MAAQlC,QAASA,EAASD,OAAQA,IAE1I,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,EAAYnM,MAAOA,IAErE,kBAAC,GAAD,CACEqB,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,iCACL+K,aAAcoB,QAOTwB,GA3NM,SAAC,GAAwB,IAAtBtK,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAEpBtH,EADQwM,qBAAWpG,IACnBpG,SAER,OACE,oCACE,yBAAKoH,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CAAUqD,QAASnH,EAAKxK,UAAUyL,OAAQ+C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,wBAAyBgB,OAE7F,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAOkD,EAAKxK,UAAU6K,QACtB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,yBAA0BgB,QAKxD,wBAAIlB,UAAU,SAEb9D,EAAKC,OAAO,UAAU,GAAGsK,OAASvK,EAAKC,OAAO,UAAU,GAAGsK,MAAMhG,KAAI,SAAChC,EAAGlH,GAAJ,OACpE,kBAAC,GAAD,CACEqB,SAAUA,EACV6K,MAAiB,IAAVlM,EACPA,MAAOA,EACPE,KAAMgH,EACN1F,IAAK0F,EAAE,OACPkF,KAAMpM,IAAU2E,EAAKC,OAAO,UAAU,GAAGsK,MAAM7O,OAAS,EACxDsI,SAAUA,OAId,kBAAC,GAAD,CAAS3D,QAASL,EAAKxK,UAAU6K,QAAS3D,SAAUA,EAAU2K,KAAM5B,QAAOzF,EAAKC,OAAO,UAAU,GAAGsK,WCKpGnC,GAAO,SAAC,GAAyC,IAAvC7M,EAAsC,EAAtCA,KAAMyI,EAAgC,EAAhCA,SAAgC,IAAtBwD,kBAAsB,MAAT,GAAS,EAC5Cd,EAAMC,aAAe,CAAC,cAAe,QAArCD,EACFE,EAAW,SAACC,EAAMC,EAAO9B,GAA2B,IAAxBrI,EAAuB,uDAAlB,KAAMnB,EAAY,uDAAT,KAC1CgP,EAAW3D,EACZC,IACD0D,EAAWA,EAAS,IAAI1D,GAE1B,IAAIC,EAAMtB,OAAMlK,EAAMiP,EAAU,MACrB,OAARzD,IACgB,kBAAP/B,GAAiC,kBAAPA,EAClCS,OAAMlK,EAAMiP,EAAU,IACD,kBAAPxF,IACTO,MAAMC,QAAQR,GACfS,OAAMlK,EAAMiP,EAAU,IAEtB/E,OAAMlK,EAAMiP,EAAU,MAK9BxG,EAASwD,EAAWgD,EAAUxF,GAC3BxJ,GACDwI,EAAS,UAAGwD,GAAaX,EAAK,UAAWrL,GAExCmB,GACDqH,EAAS,UAAGwD,GAAaX,EAAK,YAAalK,IAI/C,OACE,6BACE,kBAAC,GAAD,CACEmH,UAAU,OACVC,MAAO2C,EAAE,mBACT3B,YAAY,SACZjI,MAAO2I,OAAMlK,EAAM,2BAA4B,IAC/CyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,4BAA0CxC,MAGnE,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,mBACT3B,YAAY,2BACZjI,MAAO2I,OAAMlK,EAAM,WAAY,IAC/ByI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,YAA0BxC,MAGnD,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,4BACT3B,YAAY,aACZjI,MAAO2I,OAAMlK,EAAM,YAAa,IAChCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,aAA2BxC,MAGpD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,0BACT3B,YAAY,aACZjI,MAAO2I,OAAMlK,EAAM,UAAW,IAC9ByI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,WAAyBxC,OAIpD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,+BACT3B,YAAY,0BACZjI,MAAO2I,OAAMlK,EAAK,iCAAkC,IACpDyI,SAAU,SAAAgB,GAAC,OAAI4B,EAAS,gBAAiB,mBAAoB5B,IAC7DiC,QAAS,aACTtK,KAAK,cAGP,kBAAC,GAAD,CACEmH,UAAU,OACVC,MAAO2C,EAAE,qBACT3B,YAAY,qBACZjI,MAAO2I,OAAMlK,EAAK,uBAAwB,IAC1CyI,SAAU,SAAAgB,GAAC,OAAI4B,EAAS,gBAAiB,SAAU5B,IACnDiC,QAAS,aACTtK,KAAK,cAGP,kBAAC,GAAD,CACEmN,KAAK,IACLhG,UAAU,OACVC,MAAO2C,EAAE,8BACT5J,MAAO2I,OAAMlK,EAAM,cAAe,IAClCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,eAA6BxC,QAMtDyF,GAAY,WAChB,IAAIjP,EAAKmN,eACT,MAAQ,CACN,QAAS,eACT,MAAO,KAAKnN,EAAG,UACfkP,cAAe,CACb,MAAO,KAAKlP,EAAG,iBACf,QAAS,aACT7C,KAAM,GACNyI,OAAQ,GACRuJ,iBAAkB,IAEpBC,UAAW,CACT,QAAS,gBACTpP,GAAI,KAAKA,EAAG,aACZqP,UAAW,CACT,QAAS,eACTrP,GAAI,KAAKA,EAAG,uBACZ7C,KAAM,KAGVmS,SAAU,GACVC,UAAW,GACXC,QAAS,GACT5B,YAAa,KAGXnC,GAAU,SAAC,GAA2B,IAAzB5G,EAAwB,EAAxBA,QAAS3D,EAAe,EAAfA,SAAe,EACf8G,oBAAS,GADM,oBAClC0E,EADkC,KAC1BC,EAD0B,OAEjB3E,mBAASiH,MAFQ,oBAElClP,EAFkC,KAE5BkH,EAF4B,KAezC,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAhBP,SAACnH,EAAKC,GAAN,OAAgB2F,EAAQN,KAAI,gBAAK5G,GAAQsB,EAAKC,OAkBzD,kBAAC,GAAD,CAAeiL,SAhBJ,WACO,KAAlBxM,EAAKuP,WACTlO,GAAQF,EAAU,yCAA0CnB,GAE5DkH,EAAQgI,MAERtC,GAAQ,UAeNW,GAAmB,SAACtB,EAAYjM,EAAMyI,GAE1C,OACE,sCAIEgF,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,iDAA6CnM,EAA7C,MAEhB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAO1M,EAAKuP,SAAS,IAAIvP,EAAKqP,UAAUC,UAAUlS,KAAMwP,QAASA,EAASD,OAAQA,IAE/F,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,IAElD,kBAAC,GAAD,CACE9K,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,yCACL+K,aAAcoB,QAOTmC,GA7NC,SAAC,GAAwB,IAAtBjL,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAEftH,EADQwM,qBAAWpG,IACnBpG,SAER,OACE,oCACE,yBAAKoH,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CAAUqD,QAASnH,EAAKvK,KAAKwL,OAAQ+C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,mBAAoBgB,OAEnF,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAOkD,EAAKvK,KAAK4K,QACjB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,oBAAqBgB,QAKnD,wBAAIlB,UAAU,SAEb2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,IAAIsE,KAAI,SAAChC,EAAGlH,GAAJ,OACxD,kBAAC,GAAD,CACEqB,SAAUA,EACV6K,MAAiB,IAAVlM,EACPA,MAAOA,EACPE,KAAMgH,EACN1F,IAAK4I,OAAMlD,EAAG,MAAO,QACrBkF,KAAMpM,IAAUoK,QAAOzF,EAAKC,OAAO,UAAU,GAAGyK,eAAiB,EACjE1G,SAAUA,OAId,kBAAC,GAAD,CAAS3D,QAASL,EAAKvK,KAAK4K,QAAS3D,SAAUA,MCS/C0L,GAAO,SAAC,GAAyC,IAAvC7M,EAAsC,EAAtCA,KAAMyI,EAAgC,EAAhCA,SAAgC,IAAtBwD,kBAAsB,MAAT,GAAS,EAC5Cd,EAAMC,aAAe,CAAC,cAAe,QAArCD,EAoCR,OACE,6BACE,kBAAC,GAAD,CACE5C,UAAU,OACVC,MAAO2C,EAAE,wBACT3B,YAAY,qBACZjI,MAAO2I,OAAMlK,EAAM,sBAAuB,IAC1CyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,uBAAqCxC,MAE9D,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,wBACT3B,YAAY,mBACZjI,MAAO2I,OAAMlK,EAAM,qBAAsB,IACzCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,sBAAoCxC,IAC3Df,QAAW,CAAC,SAAU,cAAe,SACrCC,WApDoB,SAAC3B,EAAGlH,GAC9B,OACE,4BAAQwB,IAAK0F,EAAGzF,MAAOyF,GACpBA,MAoDD,kBAAC,GAAD,CACEuB,UAAU,OACVC,MAAO2C,EAAE,0BACT3B,YAAY,qBACZjI,MAAO2I,OAAMlK,EAAM,mBAAoB,IACvCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,oBAAkCxC,OAI7D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,yBACT3B,YAAY,mBACZjI,MAAO2I,OAAMlK,EAAM,qCAAsC,IACzDyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,sCAAoDxC,MAG7E,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,yBACT3B,YAAY,MACZjI,MAAO2I,OAAMlK,EAAM,8BAA+B,IAClDyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,+BAA6CxC,MAGtE,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,4BACT3B,YAAY,IACZjI,MAAO2I,OAAMlK,EAAM,6BAA8B,IACjDyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,8BAA4CxC,OAIvE,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,4BACT3B,YAAY,aACZjI,MAAO2I,OAAMlK,EAAM,kBAAmB,IACtCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,mBAAiCxC,MAG1D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,0BACT3B,YAAY,aACZjI,MAAO2I,OAAMlK,EAAM,gBAAiB,IACpCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,iBAA+BxC,OAI1D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,qBACT3B,YAAY,qBACZjI,MAAO2I,OAAMlK,EAAK,UAAW,IAC7ByI,SAAU,SAAAgB,GAAC,OAzGA,SAAC6B,EAAMC,EAAO9B,GAA2B,IAAxBrI,EAAuB,uDAAlB,KAAMnB,EAAY,uDAAT,KAC1CgP,EAAW3D,EACZC,IACD0D,EAAWA,EAAS,IAAI1D,GAE1B,IAAIC,EAAMtB,OAAMlK,EAAMiP,EAAU,MACrB,OAARzD,IACgB,kBAAP/B,GAAiC,kBAAPA,EAClCS,OAAMlK,EAAMiP,EAAU,IACD,kBAAPxF,IACTO,MAAMC,QAAQR,GACfS,OAAMlK,EAAMiP,EAAU,IAEtB/E,OAAMlK,EAAMiP,EAAU,MAK9BxG,EAASwD,EAAWgD,EAAUxF,GAC3BxJ,GACDwI,EAAS,UAAGwD,GAAaX,EAAK,UAAWrL,GAExCmB,GACDqH,EAAS,UAAGwD,GAAaX,EAAK,YAAalK,GAkF1BiK,CAAS,UAAW,GAAI5B,IACvCiC,QAAS,aACTtK,KAAK,cAGP,kBAAC,GAAD,CACEmN,KAAK,IACLhG,UAAU,OACVC,MAAO2C,EAAE,8BACT5J,MAAO2I,OAAMlK,EAAM,WAAY,IAC/ByI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,YAA0BxC,QAMnDyF,GAAY,WAChB,IAAIjP,EAAKmN,eACT,MAAQ,CACN,QAAS,oCACT,MAAO,KAAKnN,EAAG,UACf,gBAAmB,CACjB,MAAO,KAAKA,EAAG,mBACf,QAAS,kBACT,WAAc,GACd,YAAe,GACf,KAAQ,MACR,aAAgB,CACd,MAAO,KAAKA,EAAG,YAGnB,mBAAsB,SACtB,iBAAoB,GACvB,SAAY,GACT,QAAW,GACX,MAAS,CACP,MAAO,KAAKA,EAAG,SACf,QAAS,iCACT,6BAAgC,GAChC,UAAa,GACb,QAAW,GACX,SAAY,CAChB,MAAO,KAAKA,EAAG,kBACf,QAAS,sBACT,KAAQ,OAMJyL,GAAU,SAAC,GAA2B,IAAzB5G,EAAwB,EAAxBA,QAAS3D,EAAe,EAAfA,SAAe,EACf8G,oBAAS,GADM,oBAClC0E,EADkC,KAC1BC,EAD0B,OAEjB3E,mBAASiH,MAFQ,oBAElClP,EAFkC,KAE5BkH,EAF4B,KAgBzC,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAjBP,SAACnH,EAAKC,GAAN,OAAgB2F,EAAQN,KAAI,gBAAK5G,GAAQsB,EAAKC,OAkBzD,kBAAC,GAAD,CAAeiL,SAhBJ,WACe,KAA1BxM,EAAK2P,mBAETtO,GAAQF,EAAU,yCAA0CnB,GAE5DkH,EAAQgI,MAERtC,GAAQ,UAeNW,GAAmB,SAACtB,EAAYjM,EAAMyI,GAE1C,OACE,sCAIEgF,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,iDAA6CnM,EAA7C,MAEhB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAOxC,OAAMlK,EAAM,mBAAoB,IAAK4M,QAASA,EAASD,OAAQA,IAEnF,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,IAElD,kBAAC,GAAD,CACE9K,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,yCACL+K,aAAcoB,QAOTqC,GA3QM,SAAC,GAAwB,IAAtBnL,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAEpBtH,EADQwM,qBAAWpG,IACnBpG,SAER,OACE,oCACE,yBAAKoH,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEqD,QAASnH,EAAKtK,UAAUuL,OACxB+C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,wBAAyBgB,OAGrD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAOkD,EAAKtK,UAAU2K,QACtB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,yBAA0BgB,QAKxD,wBAAIlB,UAAU,SAEb2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,IAAIsE,KAAI,SAAChC,EAAGlH,GAAJ,OACxD,kBAAC,GAAD,CACEE,KAAMgH,EACN1F,IAAK4I,OAAMlD,EAAG,MAAO,QACrBlH,MAAOA,EACP2I,SAAUA,EACVtH,SAAUA,EACV6K,MAAiB,IAAVlM,EACPoM,KAAMpM,IAAUoK,QAAOA,OAAMzF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,KAAO,OAInF,kBAAC,GAAD,CAASI,QAASL,EAAKtK,UAAU2K,QAAS3D,SAAUA,MCIpD0L,GAAO,SAAC,GAA2D,IAAzD7M,EAAwD,EAAxDA,KAAMyI,EAAkD,EAAlDA,SAAkD,IAAxCwD,kBAAwC,MAA3B,GAA2B,MAAvB4D,qBAAuB,MAAT,GAAS,EAC9D1E,EAAMC,aAAe,CAAC,cAAe,QAArCD,EAER,OACE,6BACE,kBAAC,GAAD,CACE5C,UAAU,OACVC,MAAO2C,EAAE,sBACT3B,YAAY,0BACZjI,MAAO2I,OAAMlK,EAAK,cAAe,IACjCyI,SAAU,SAAAgB,GAAMhB,EAAS,GAAD,OAAIwD,EAAJ,mBAAiCxC,GAAsB,KAAhBoG,GAAoBpH,EAAS,GAAD,OAAIoH,GAAiBpG,MAGlH,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,yBACT3B,YAAY,8BACZjI,MAAO2I,OAAMlK,EAAM,oBAAqB,IACxCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,yBAAuCxC,MAGhE,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,8BACT5J,MAAOvB,EAAK6N,YACZpF,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,eAA6BxC,QAMtDyF,GAAY,WAEhB,MAAQ,CACN,QAAS,cACT,MAAO,KAHA9B,eAGQ,UACf,cAAe,GAClB,oBAAqB,GAClBS,YAAa,KAIXnC,GAAU,SAAC,GAA2B,IAAzB5G,EAAwB,EAAxBA,QAAS3D,EAAe,EAAfA,SAAe,EACf8G,oBAAS,GADM,oBAClC0E,EADkC,KAC1BC,EAD0B,OAEjB3E,mBAASiH,MAFQ,oBAElClP,EAFkC,KAE5BkH,EAF4B,KAiBzC,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAlBP,SAACnH,EAAKC,GAAN,OAAgB2F,EAAQN,KAAI,gBAAK5G,GAAQsB,EAAKC,OAoBzD,kBAAC,GAAD,CAAeiL,SAlBJ,WACwB,KAAnCtC,OAAMlK,EAAM,cAAe,MAE/BqB,GAAQF,EAAU,oCAAqCnB,GACvDqB,GAAQF,EAAU,oCAAqC+I,OAAMlK,EAAM,cAAe,KAElFkH,EAAQgI,MAERtC,GAAQ,UAgBNW,GAAmB,SAACtB,EAAYjM,EAAMyI,GAE1C,OACE,sCAIEgF,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,yCAAqCnM,EAArC,MACV+P,EAAa,yCAAqC/P,EAArC,KAEnB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAO1M,EAAK,eAAgB4M,QAASA,EAASD,OAAQA,IAEnE,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,EAAY4D,cAAeA,IAE7E,kBAAC,GAAD,CACE1O,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,iCACL+K,aAAcoB,QAOTuC,GAnJG,SAAC,GAAwB,IAAtBrL,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAEjBtH,EADQwM,qBAAWpG,IACnBpG,SAER,OACE,oCACE,yBAAKoH,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEqD,QAASnH,EAAKrK,OAAOsL,OACrB+C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,qBAAsBgB,OAGlD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAOkD,EAAKrK,OAAO0K,QACnB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,sBAAuBgB,QAKrD,wBAAIlB,UAAU,SAEb2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,QAAS,IAAIsE,KAAI,SAAChC,EAAGlH,GAAJ,OAChD,kBAAC,GAAD,CACEE,KAAMgH,EACN1F,IAAK4I,OAAMlD,EAAG,MAAO,QACrBlH,MAAOA,EACP2I,SAAUA,EACVtH,SAAUA,EACV6K,MAAiB,IAAVlM,EACPoM,KAAMpM,IAAUoK,QAAOA,OAAMzF,EAAKC,OAAO,UAAU,GAAI,QAAS,KAAO,OAI3E,kBAAC,GAAD,CAASI,QAASL,EAAKrK,OAAO0K,QAAS3D,SAAUA,MCIjD0L,GAAO,SAAC,GAAyC,IAAvC7M,EAAsC,EAAtCA,KAAMyI,EAAgC,EAAhCA,SAAgC,IAAtBwD,kBAAsB,MAAT,GAAS,EAC5Cd,EAAMC,aAAe,eAArBD,EAER,OACE,6BACE,kBAAC,GAAD,CACE5C,UAAU,OACVC,MAAO2C,EAAE,oBACT3B,YAAY,gBACZjI,MAAO2I,OAAMlK,EAAM,aAAc,IACjCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,cAA4BxC,MAGrD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,sBACT3B,YAAY,kBACZjI,MAAO2I,OAAMlK,EAAM,QAAS,IAC5ByI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,SAAuBxC,QAMhDyF,GAAY,WAEhB,MAAQ,CACN,QAAS,gBACT,MAAO,YAHA9B,eAIP,WAAc,GACd,MAAS,KAIP1B,GAAU,SAAC,GAA2B,IAAzB5G,EAAwB,EAAxBA,QAAS3D,EAAe,EAAfA,SAAe,EACf8G,oBAAS,GADM,oBAClC0E,EADkC,KAC1BC,EAD0B,OAEjB3E,mBAASiH,MAFQ,oBAElClP,EAFkC,KAE5BkH,EAF4B,KAgBzC,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAjBP,SAACnH,EAAKC,GAAN,OAAgB2F,GAAQ,SAAAtB,GAAK,OAAIgB,KAAI,gBAAKhB,GAAStE,EAAKC,SAmBnE,kBAAC,GAAD,CAAeiL,SAjBJ,WACuB,KAAlCtC,OAAMlK,EAAM,aAAc,KAA2C,KAA7BkK,OAAMlK,EAAM,QAAS,MAEjEqB,GAAQF,EAAU,sCAAuCnB,GAEzDkH,EAAQgI,MAERtC,GAAQ,UAgBNa,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,8CAA0CnM,EAA1C,MAEhB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAOxC,OAAMlK,EAAM,aAAc,IAAK4M,QAASA,EAASD,OAAQA,IAE7E,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,IAElD,kBAAC,GAAD,CACE9K,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,2CAOA2O,GAjIG,SAAC,GAAwB,IAAtBtL,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAEjBtH,EADQwM,qBAAWpG,IACnBpG,SAER,OACE,oCACE,yBAAKoH,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEqD,QAASnH,EAAKjK,OAAOkL,OACrB+C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,qBAAsBgB,OAGlD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAOkD,EAAKjK,OAAOsK,QACnB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,sBAAuBgB,QAKrD,wBAAIlB,UAAU,SAEb2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,aAAc,IAAIsE,KAAI,SAAChC,EAAGlH,GAAJ,OACrD,kBAAC,GAAD,CACEE,KAAMgH,EACN1F,IAAK4I,OAAMlD,EAAG,MAAO,QACrBlH,MAAOA,EACP2I,SAAUA,EACVtH,SAAUA,EACV6K,MAAiB,IAAVlM,EACPoM,KAAMpM,IAAUoK,OAAMzF,EAAKC,OAAO,UAAU,GAAI,aAAc,IAAIvE,OAAS,OAI/E,kBAAC,GAAD,CAAS2E,QAASL,EAAKjK,OAAOsK,QAAS3D,SAAUA,MCwBjD0L,GAAO,SAAC,GAAyC,IAAvC7M,EAAsC,EAAtCA,KAAMyI,EAAgC,EAAhCA,SAAgC,IAAtBwD,kBAAsB,MAAT,GAAS,EAC5Cd,EAAMC,aAAe,eAArBD,EAER,OACE,6BACE,kBAAC,GAAD,CACE5C,UAAU,OACVC,MAAO2C,EAAE,uBACT3B,YAAY,UACZjI,MAAO2I,OAAMlK,EAAK,OAAQ,IAC1ByI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,QAAsBxC,QAM/CyF,GAAY,WAEhB,MAAQ,CACN,QAAS,WACT,MAAO,KAHA9B,eAIP,KAAQ,KAGN1B,GAAU,SAAC,GAA2B,IAAzB5G,EAAwB,EAAxBA,QAAS3D,EAAe,EAAfA,SAAe,EACf8G,oBAAS,GADM,oBAClC0E,EADkC,KAC1BC,EAD0B,OAGjB3E,mBAASiH,MAHQ,oBAGlClP,EAHkC,KAG5BkH,EAH4B,KAiBzC,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAjBP,SAACnH,EAAKC,GAAN,OAAgB2F,GAAQ,SAAAtB,GAAK,OAAIgB,KAAI,gBAAKhB,GAAStE,EAAKC,SAmBnE,kBAAC,GAAD,CAAeiL,SAjBJ,WACiB,KAA5BtC,OAAMlK,EAAM,OAAQ,MAExBqB,GAAQF,EAAU,yCAA0CnB,GAE5DkH,EAAQgI,MAERtC,GAAQ,UAgBNa,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,iDAA6CnM,EAA7C,MAEhB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAOxC,OAAMlK,EAAM,OAAQ,IAAK4M,QAASA,EAASD,OAAQA,IAEvE,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,IAElD,kBAAC,GAAD,CACE9K,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,8CAOA4O,GA3IM,SAAC,GAAwB,IAAtBvL,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAEpBtH,EADQwM,qBAAWpG,IACnBpG,SAmBR,OAjBA8O,qBAAU,WACF,cAAexL,IACnBtD,EAAS,CACPC,KAAM,kBACNI,QAAS,CACPF,IAAK,YACLC,MAAO,CACLmE,QAAQ,EACRZ,QAAS,gBAKf3D,EAAS,CAAEC,KAAM,iBAElB,CAACqD,EAAMtD,IAGR,cAAesD,GACb,oCACE,yBAAK8D,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEqD,QAASnH,EAAKnK,UAAUoL,OACxB+C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,wBAAyBgB,OAGrD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAOkD,EAAKnK,UAAUwK,QACtB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,yBAA0BgB,QAKxD,wBAAIlB,UAAU,SAEb2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,IAAIsE,KAAI,SAAChC,EAAGlH,GAAJ,OACxD,kBAAC,GAAD,CACEE,KAAMgH,EACN1F,IAAK4I,OAAMlD,EAAE,MAAO,QACpBlH,MAAOA,EACP2I,SAAUA,EACVtH,SAAUA,EACV6K,MAAiB,IAAVlM,EACPoM,KAAMpM,IAAUoK,QAAOA,OAAMzF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,KAAO,OAInF,kBAAC,GAAD,CAASI,QAASL,EAAKnK,UAAUwK,QAAS3D,SAAUA,MCOtD0L,GAAO,SAAC,GAAyC,IAAvC7M,EAAsC,EAAtCA,KAAMyI,EAAgC,EAAhCA,SAAgC,IAAtBwD,kBAAsB,MAAT,GAAS,EAC5Cd,EAAMC,aAAe,CAAC,cAAe,QAArCD,EAER,OACE,6BACE,yBAAK5C,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAO2C,EAAE,yBACT3B,YAAY,oBACZjI,MAAO2I,OAAMlK,EAAM,wCAAyC,IAC5DyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,yCAAuDxC,MAGhF,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,+BACT3B,YAAY,oBACZjI,MAAO2I,OAAMlK,EAAM,yCAA0C,IAC7DyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,0CAAwDxC,OAInF,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,6BACT3B,YAAY,kBACZjI,MAAO2I,OAAMlK,EAAM,uCAAwC,IAC3DyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,wCAAsDxC,MAG/E,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,0BACT3B,YAAY,kBACZjI,MAAO2I,OAAMlK,EAAM,wCAAyC,IAC5DyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,yCAAuDxC,MAGhF,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAO2C,EAAE,0BACT3B,YAAY,wBACZjI,MAAO2I,OAAMlK,EAAM,oCAAqC,IACxDyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,qCAAmDxC,MAG5E,kBAAC,GAAD,CACE8E,KAAK,IACLhG,UAAU,OACVC,MAAO2C,EAAE,8BACT5J,MAAO2I,OAAMlK,EAAM,2CAA4C,IAC/DyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,4CAA0DxC,QAKnFyF,GAAY,WAChB,IAAIjP,EAAKmN,eACT,MACE,CACE,MAAO,eAAenN,EACtB,QAAS,qBACT,0BAA6B,YAC7B,gBAAmB,CACjB,MAAO,eAAeA,EAAG,mBACzB,QAAS,eACT,YAAe,CACb,MAAO,eAAeA,EAAG,+BACzB,QAAS,UAEX,OAAU,CACR,CACE,MAAO,eAAeA,EAAG,UACzB,QAAS,SACT,aAAgB,GAGhB,aAAgB,GAGhB,aAAgB,CACd,MAAO,eAAeA,EAAG,uBACzB,QAAS,SACT,YAAe,IACf,WAAc,IACd,kBAAqB,SAQ7ByL,GAAU,SAAC,GAA2B,IAAzB5G,EAAwB,EAAxBA,QAAS3D,EAAe,EAAfA,SAAe,EACf8G,oBAAS,GADM,oBAClC0E,EADkC,KAC1BC,EAD0B,OAEjB3E,mBAASiH,MAFQ,oBAElClP,EAFkC,KAE5BkH,EAF4B,KAgBzC,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAjBP,SAACnH,EAAKC,GAAN,OAAgB2F,EAAQN,KAAI,gBAAK5G,GAAQsB,EAAKC,OAmBzD,kBAAC,GAAD,CAAeiL,SAjBJ,WACkD,KAA7DtC,OAAMlK,EAAM,wCAAyC,MAEzDqB,GAAQF,EAAU,gDAAiDnB,GAEnEkH,EAAQgI,MAERtC,GAAQ,UAgBNa,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,wDAAoDnM,EAApD,MAEhB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAOxC,OAAMlK,EAAM,wCAAyC,IAAI,IAAIkK,OAAMlK,EAAM,yCAA0C,IAAK4M,QAASA,EAASD,OAAQA,IAEtK,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,IAElD,kBAAC,GAAD,CACE9K,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,qDAOA8O,GAhNO,SAAC,GAAwB,IAAtBzL,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAErBtH,EADQwM,qBAAWpG,IACnBpG,SAmBR,OAjBA8O,qBAAU,WACF,eAAgBxL,IACpBtD,EAAS,CACPC,KAAM,kBACNI,QAAS,CACPF,IAAK,aACLC,MAAO,CACLmE,QAAQ,EACRZ,QAAS,iBAKf3D,EAAS,CAAEC,KAAM,iBAElB,CAACqD,EAAMtD,IAGR,eAAgBsD,GACd,oCACE,yBAAK8D,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEqD,QAASnH,EAAKlK,WAAWmL,OACzB+C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,yBAA0BgB,OAGtD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAOkD,EAAKlK,WAAWuK,QACvB2D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,0BAA2BgB,QAKzD,wBAAIlB,UAAU,SAEb2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,uBAAwB,IAAIsJ,QAAO,SAAAhH,GAAC,MAAiD,cAA7CkD,OAAMlD,EAAG,4BAA6B,OAAoBgC,KAAI,SAAChC,EAAGlH,GAAJ,OACrI,kBAAC,GAAD,CACEE,KAAMgH,EACN1F,IAAK4I,OAAMlD,EAAE,MAAO,QACpBlH,MAAOA,EACP2I,SAAUA,EACVtH,SAAUA,EACV6K,MAAiB,IAAVlM,EACPoM,KAAMpM,IAAUoK,OAAMzF,EAAKC,OAAO,UAAU,GAAI,uBAAwB,IAAIsJ,QAAO,SAAAhH,GAAC,MAAiD,cAA7CkD,OAAMlD,EAAG,4BAA6B,OAAoB7G,OAAS,OAI/J,kBAAC,GAAD,CAAS2E,QAASL,EAAKlK,WAAWuK,QAAS3D,SAAUA,MCdvD0L,GAAO,SAAC,GAAyC,IAAvC7M,EAAsC,EAAtCA,KAAMyI,EAAgC,EAAhCA,SAAgC,IAAtBwD,kBAAsB,MAAT,GAAS,EAC5Cd,EAAMC,aAAe,CAAC,cAAe,QAArCD,EAER,OACE,6BACE,kBAAC,GAAD,CACI5C,UAAU,OACVC,MAAO2C,EAAE,gCACT3B,YAAY,oBACZjI,MAAO2I,OAAMlK,EAAM,uBAAwB,IAC3CyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,wBAAsCxC,MAGjE,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACIA,UAAU,OACVC,MAAO2C,EAAE,8BACT3B,YAAY,aACZjI,MAAO2I,OAAMlK,EAAM,YAAa,IAChCyI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,aAA2BxC,MAGtD,kBAAC,GAAD,CACIlB,UAAU,OACVC,MAAO2C,EAAE,4BACT3B,YAAY,aACZjI,MAAO2I,OAAMlK,EAAM,UAAW,IAC9ByI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,WAAyBxC,OAGpD,kBAAC,GAAD,CACIlB,UAAU,OACVC,MAAO2C,EAAE,6BACT3B,YAAc,aACdjI,MAAO2I,OAAMlK,EAAM,WAAY,IAC/ByI,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIwD,EAAJ,YAA0BxC,QAMvDyF,GAAY,WAChB,IAAIjP,EAAKmN,eACT,MACE,CACE,MAAO,KAAKnN,EAAG,UACf,QAAS,OACT,UAAa,GACb,QAAW,GACX,SAAY,SACZ,SAAY,CACV,MAAO,KAAKA,EAAG,YACf,QAAS,oBACT,IAAO,GACP,YAAe,GACf,YAAe,MAMjByL,GAAU,SAAC,GAA2B,IAAzB5G,EAAwB,EAAxBA,QAAS3D,EAAe,EAAfA,SAAe,EACf8G,oBAAS,GADM,oBAClC0E,EADkC,KAC1BC,EAD0B,OAEjB3E,mBAASiH,MAFQ,oBAElClP,EAFkC,KAE5BkH,EAF4B,KAezC,OACE,yBAAKqB,UAAU,2CACb,kBAAC,GAAD,CAAazD,QAASA,EAAS8H,QAASA,EAASD,OAAQA,IAEzD,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAhBP,SAACnH,EAAKC,GAAN,OAAgB2F,EAAQN,KAAI,gBAAK5G,GAAQsB,EAAKC,OAiBzD,kBAAC,GAAD,CAAeiL,SAhBJ,WACqB,KAAhCtC,OAAMlK,EAAM,WAAY,MAE5BqB,GAAQF,EAAU,oCAAqCnB,GAEvDkH,EAAQgI,MAERtC,GAAQ,UAeNW,GAAmB,SAACtB,EAAYjM,EAAMyI,GAE1C,OACE,sCAIEgF,GAAO,SAAC,GAAsD,IAApDzN,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO2I,EAAsC,EAAtCA,SAAUtH,EAA4B,EAA5BA,SAAU6K,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvCjE,oBAAS,GAD8B,oBAC1D0E,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,4CAAwCnM,EAAxC,MAEhB,OACE,yBAAKyI,UAAU,2CACb,kBAAC,GAAD,CAAamE,MAAOxC,OAAMlK,EAAM,uBAAwB,IAAK4M,QAASA,EAASD,OAAQA,IAEvF,yBAAKpE,UAAS,eAAUoE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAM3M,KAAMA,EAAMyI,SAAUA,EAAUwD,WAAYA,IAElD,kBAAC,GAAD,CACE9K,SAAUA,EACV6K,MAAOA,EACPC,WAAYA,EACZjM,KAAMA,EACNkM,KAAMA,EACNzD,SAAUA,EACVrH,KAAK,oCACL+K,aAAcoB,QAOT4C,GAlKQ,SAAC,GAAwB,IAAtB1L,EAAqB,EAArBA,KAAMgE,EAAe,EAAfA,SAEtBtH,EADQwM,qBAAWpG,IACnBpG,SAER,OACE,oCACE,yBAAKoH,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEqD,QAAS1B,OAAMzF,EAAK,sBAAsB,GAC1CgE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,0BAA2BgB,OAGvD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZjI,MAAO2I,OAAMzF,EAAM,sBAAuB,IAC1CgE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,2BAA4BgB,QAK1D,wBAAIlB,UAAU,SAEb2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,WAAY,IAAIsE,KAAI,SAAChC,EAAGlH,GAAJ,OACnD,kBAAC,GAAD,CACEE,KAAMgH,EACN1F,IAAK4I,OAAMlD,EAAG,MAAO,QACrBlH,MAAOA,EACP2I,SAAUA,EACVtH,SAAUA,EACV6K,MAAiB,IAAVlM,EACPoM,KAAMpM,IAAUoK,QAAOA,OAAMzF,EAAKC,OAAO,UAAU,GAAI,WAAY,KAAO,OAI9E,kBAAC,GAAD,CAASI,QAASoF,OAAMzF,EAAM,sBAAuB,IAAKtD,SAAUA,MCqC3DiP,GAvEK,WAClB,IAAMC,EAAU1C,qBAAWpG,IACnBrI,EAAoBmR,EAApBnR,MAAOiC,EAAakP,EAAblP,SACPsD,EAASvF,EAATuF,KAEFyE,EAAO,CACX,CAAE5H,IAAK,UAAWlE,KAAM8M,OAAMzF,EAAM,kBAAmB,YACvD,CAAEnD,IAAK,UAAWlE,KAAM8M,OAAMzF,EAAM,iBAAkB,YACtD,CAAEnD,IAAK,WAAYlE,KAAM8M,OAAMzF,EAAM,mBAAoB,aACzD,CAAEnD,IAAK,YAAalE,KAAM8M,OAAMzF,EAAM,oBAAqB,cAC3D,CAAEnD,IAAK,OAAQlE,KAAM8M,OAAMzF,EAAM,eAAgB,SACjD,CAAEnD,IAAK,YAAalE,KAAM8M,OAAMzF,EAAM,oBAAqB,cAC3D,CAAEnD,IAAK,SAAUlE,KAAM8M,OAAMzF,EAAM,iBAAkB,WACrD,CAAEnD,IAAK,cAAelE,KAAM8M,OAAMzF,EAAM,sBAAuB,gBAC/D,CAAEnD,IAAK,YAAalE,KAAM8M,OAAMzF,EAAM,oBAAqB,cAC3D,CAAEnD,IAAK,aAAclE,KAAM8M,OAAMzF,EAAM,qBAAsB,eAC7D,CAAEnD,IAAK,SAAUlE,KAAM8M,OAAMzF,EAAM,iBAAkB,YAhB/B,EAkBYwD,mBAASiB,EAAK,GAAG5H,KAlB7B,oBAkBjB6H,EAlBiB,KAkBLC,EAlBK,KAmBlBX,EAAW,SAACnH,EAAKC,GACrBJ,EAAS,CACPC,KAAM,WACNI,QAAS,CACPF,MACAC,WAIJJ,EAAS,CAAEC,KAAM,eAgCnB,OACE,yBACEnB,GAAG,cACHsI,UAAU,4FAEV,kBAAC,GAAD,CAAQW,KAAMA,EAAMC,WAAYA,EAAYC,cAAeA,IAC3D,yBAAKb,UAAU,QAnCA,WACjB,OAAQY,GACN,IAAK,UACH,OAAO,kBAAC,GAAD,CAAY1E,KAAMA,EAAMgE,SAAUA,IAC3C,IAAK,UACH,OAAO,kBAAC,GAAD,CAAYhE,KAAMA,EAAMgE,SAAUA,IAC3C,IAAK,WACH,OAAO,kBAAC6H,GAAD,CAAa7L,KAAMA,EAAMgE,SAAUA,IAC5C,IAAK,YACH,OAAO,kBAAC,GAAD,CAAchE,KAAMA,EAAMgE,SAAUA,IAC7C,IAAK,OACH,OAAO,kBAAC,GAAD,CAAShE,KAAMA,EAAMgE,SAAUA,IACxC,IAAK,YACH,OAAO,kBAAC,GAAD,CAAchE,KAAMA,EAAMgE,SAAUA,IAC7C,IAAK,SACH,OAAO,kBAAC,GAAD,CAAWhE,KAAMA,EAAMgE,SAAUA,IAC1C,IAAK,cACH,OAAO,kBAAC,GAAD,CAAgBhE,KAAMA,EAAMgE,SAAUA,IAC/C,IAAK,YACH,OAAO,kBAAC,GAAD,CAAchE,KAAMA,EAAMgE,SAAUA,IAC7C,IAAK,aACH,OAAO,kBAAC,GAAD,CAAehE,KAAMA,EAAMgE,SAAUA,IAC9C,IAAK,SACH,OAAO,kBAAC,GAAD,CAAWhE,KAAMA,EAAMgE,SAAUA,IAC1C,QACE,OAAO,MAUc8H,M,mBCuOdC,GArTF,WACX,IACQtR,EADQyO,qBAAWpG,IACnBrI,MACAuF,EAAgBvF,EAAhBuF,KAAMsB,EAAU7G,EAAV6G,MAER0K,EAAQ,kBACZhM,EAAKzK,QAAQ+K,OACX,yBACEwD,UAAU,4BACVmI,IAAKjM,EAAKzK,QAAQ+K,MAClB4L,IAAI,oBACJ/H,MAAO,CAAE5F,MAAO,QAASC,OAAQ,YAIjC2N,EAAU,kBACd,6BACE,wBAAIrI,UAAU,qBAAqBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAC7D5B,EAAKzK,QAAQgL,UADhB,IAC4BP,EAAKzK,QAAQiL,UAEzC,wBAAIsD,UAAU,uBAAuB9D,EAAKzK,QAAQkL,UAElD,yBAAKqD,UAAU,8BACb,8BAAO9D,EAAKzK,QAAQ6K,QAAQM,OAC5B,8BAAOV,EAAKzK,QAAQ6K,QAAQO,OAC5B,8BAAOX,EAAKzK,QAAQ6K,QAAQQ,UAK5ByL,EAAc,SAAC,GAAD,IAAGjF,EAAH,EAAGA,KAAMtK,EAAT,EAASA,MAAT,IAAgBwP,YAAhB,MAAuB,IAAvB,SAClBxP,GACE,yBAAKgH,UAAU,0BACb,0BAAMA,UAAU,8BAA8BK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SACxEwF,GAEH,uBAAGwC,KAAM0C,GACP,0BAAMxI,UAAU,yBAAyBhH,MAK3CyP,EAAU,SAAC,GAAD,IAAGtE,EAAH,EAAGA,MAAH,OACd,wBAAInE,UAAU,wCAAwCK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAChFqG,IAICuE,EAAY,kBAChBxM,EAAKxK,WACLwK,EAAKxK,UAAUyL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKxK,UAAU6K,UAC/B,kBAAC,KAAD,CAAeyD,UAAU,UAAU2I,OAAQzM,EAAKxK,UAAU0L,SAI1DwL,EAAW,SAAAnK,GAAC,OAChB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,yBAAKA,UAAU,wBACb,6BACE,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAEoK,OAE5B,0BAAM7I,UAAU,uBAAhB,IACIvB,EAAEqK,MADN,MACgBrK,EAAEsK,IADlB,MAIF,kBAAC,KAAD,CAAe/I,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD0D,EAAO,kBACX9M,EAAKvK,MACLuK,EAAKvK,KAAKwL,QACR,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKvK,KAAK4K,UACzBL,EAAKvK,KAAK0L,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAImI,KAI3CK,EAAgB,SAAAxK,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,yBAAKA,UAAU,wBACb,6BACE,wBAAIA,UAAU,iBAAiBvB,EAAE5J,MACjC,uBAAGmL,UAAU,WAAWvB,EAAEyK,QAE5B,yBAAKlJ,UAAU,2BACb,0BAAMA,UAAU,qBAAqBvB,EAAE0K,OACvC,0BAAMnJ,UAAU,uBAAhB,IACIvB,EAAEqK,MADN,MACgBrK,EAAEsK,IADlB,OAKJ,kBAAC,KAAD,CAAe/I,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD8D,EAAY,kBAChBlN,EAAKtK,WACLsK,EAAKtK,UAAUuL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKtK,UAAU2K,UAC9BL,EAAKtK,UAAUyL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIwI,KAIhDI,EAAY,SAAA5K,GAAC,OACjB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDgE,EAAS,kBACbpN,EAAKrK,QACLqK,EAAKrK,OAAOsL,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKrK,OAAO0K,UAC3BL,EAAKrK,OAAOwL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI4I,KAI7CE,EAAoB,SAAA9K,GAAC,OACzB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkE,EAAiB,kBACrBtN,EAAKpK,gBACLoK,EAAKpK,eAAeqL,QAClB,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKpK,eAAeyK,UACnCL,EAAKpK,eAAeuL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI8I,KAIrDE,EAAY,SAAAhL,GAAC,OACjB,0BACE1F,IAAK0F,EAAE/G,GACPsI,UAAU,uDACVK,MAAO,CACLqJ,gBAAiBlM,EAAMrL,OAAO0L,QAC9ByK,MAAO9K,EAAMrL,OAAOyL,aAGrBa,EAAEkL,QAIDC,EAAc,kBAClB1N,EAAKqB,aACLrB,EAAKqB,YAAYJ,QACf,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKqB,YAAYhB,UACjC,yBAAKyD,UAAU,uBAAuB9D,EAAKqB,YAAYF,MAAMoD,IAAIgJ,MAIjEI,EAAY,SAAApL,GAAC,OACjB,0BACE1F,IAAK0F,EAAE/G,GACPsI,UAAU,uDACVK,MAAO,CACLqJ,gBAAiBlM,EAAMrL,OAAO0L,QAC9ByK,MAAO9K,EAAMrL,OAAOyL,aAGrBa,EAAEqL,QAIDC,EAAS,kBACb7N,EAAKoB,QACLpB,EAAKoB,OAAOH,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKoB,OAAOf,UAC5B,yBAAKyD,UAAU,uBAAuB9D,EAAKoB,OAAOD,MAAMoD,IAAIoJ,MAI5DG,EAAe,SAAAvL,GAAC,OACpB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,sCACxB,wBAAIA,UAAU,uBAAuBvB,EAAE1F,KACvC,yBAAKiH,UAAU,QACZvB,EAAEwL,OAAS,yBAAKjK,UAAU,0BAA0BvB,EAAEwL,OACzC,IAAbxL,EAAEyL,QACD,yBAAKlK,UAAU,QACZyB,MAAM0I,KAAK1I,MAAMhD,EAAEyL,SAASzJ,KAAI,SAACkB,EAAGyI,GAAJ,OAC/B,uBAAGrR,IAAKqR,EAAGpK,UAAU,yBAAyBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAA3E,eAUNuM,EAAY,kBAChBnO,EAAKnK,WACLmK,EAAKnK,UAAUoL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKnK,UAAUwK,UAC/B,yBAAKyD,UAAU,SAAS9D,EAAKnK,UAAUsL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIuJ,MAIvEM,EAAgB,SAAA7L,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,iBACxB,wBAAIA,UAAU,uBAAuBvB,EAAE5J,MACvC,0BAAMmL,UAAU,WAAWvB,EAAE8L,UAC7B,0BAAMvK,UAAU,WAAWvB,EAAE1B,OAC7B,0BAAMiD,UAAU,WAAWvB,EAAExB,OAC7B,kBAAC,KAAD,CAAe+C,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkF,EAAa,kBACjBtO,EAAKlK,YACLkK,EAAKlK,WAAWmL,QACd,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKlK,WAAWuK,UAChC,yBAAKyD,UAAU,0BACZ9D,EAAKlK,WAAWqL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI6J,MAKnDG,EAAY,SAAAhM,GAAC,OACjB,wBAAI1F,IAAK0F,EAAE/G,IACT,wBAAIsI,UAAU,wCAAwCvB,EAAE1F,KACxD,wBAAIiH,UAAU,4BAA4BvB,EAAEzF,SAI1C0R,EAAS,kBACbxO,EAAKjK,QACLiK,EAAKjK,OAAOkL,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKjK,OAAOsK,UAC5B,2BAAOyD,UAAU,cACf,+BAAQ9D,EAAKjK,OAAOoL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIgK,OAK5D,OACE,yBACEzK,UAAU,OACVK,MAAO,CACLsK,WAAYnN,EAAME,KAAKC,OACvB+L,gBAAiBlM,EAAMrL,OAAOyL,WAC9B0K,MAAO9K,EAAMrL,OAAO0L,UAGtB,yBAAKmC,UAAU,iCACb,yBAAKA,UAAU,gCACb,kBAACkI,EAAD,MACA,kBAACG,EAAD,OAGF,yBAAKrI,UAAU,sBACb,kBAACuI,EAAD,CAAajF,KAAK,QAAQtK,MAAOkD,EAAKzK,QAAQsL,MAAOyL,KAAI,cAAStM,EAAKzK,QAAQsL,SAC/E,kBAACwL,EAAD,CACEjF,KAAK,WACLtK,MAAOkD,EAAKzK,QAAQuL,QACpBwL,KAAI,iBAAYtM,EAAKzK,QAAQuL,WAE/B,kBAACuL,EAAD,CACEjF,KAAK,QACLtK,MAAOkD,EAAKzK,QAAQwL,MACpBuL,KAAI,iBAAYtM,EAAKzK,QAAQwL,SAE/B,kBAACsL,EAAD,CAAajF,KAAK,cAActK,MAAOkD,EAAKzK,QAAQ6K,QAAQQ,UAIhE,wBAAIkD,UAAU,SAEd,kBAAC0I,EAAD,MACA,kBAACM,EAAD,MACA,kBAACI,EAAD,MAEA,yBAAKpJ,UAAU,0BACb,kBAACsJ,EAAD,MACA,kBAACE,EAAD,OAGF,yBAAKxJ,UAAU,0BACb,kBAAC+J,EAAD,MACA,kBAACH,EAAD,OAGF,kBAACY,EAAD,MAEA,yBAAKxK,UAAU,0BACb,kBAAC0K,EAAD,MACA,kBAACL,EAAD,S,UCjTKO,G,QAAQ1Q,EACN+N,MCmTA4C,GAlTC,WACd,IACQlU,EADQyO,qBAAWpG,IACnBrI,MACAuF,EAAgBvF,EAAhBuF,KAAMsB,EAAU7G,EAAV6G,MAER0K,EAAQ,iBACW,KAAvBhM,EAAKzK,QAAQ+K,OACX,yBAAKwD,UAAU,0BACb,yBACEA,UAAU,8CACVmI,IAAKjM,EAAKzK,QAAQ+K,MAClB4L,IAAI,OAKN0C,EAAS,kBACb,yBACE9K,UAAU,4CACVK,MAAO,CAAEqJ,gBAAiBlM,EAAMrL,OAAO2L,OAAQwK,MAAO9K,EAAMrL,OAAOyL,aAEnE,yBAAKoC,UAAU,0CACb,wBAAIA,UAAU,oCACX9D,EAAKzK,QAAQgL,UADhB,IAC4BP,EAAKzK,QAAQiL,UAEzC,yBAAKsD,UAAU,qCAAqC9D,EAAKzK,QAAQkL,UAEjE,wBAAIqD,UAAU,oBAEd,kBAAC,KAAD,CAAeA,UAAU,UAAU2I,OAAQzM,EAAKxK,UAAU0L,UAK1DmL,EAAc,SAAC,GAAD,IAAGjF,EAAH,EAAGA,KAAMtK,EAAT,EAASA,MAAT,IAAgBwP,YAAhB,MAAuB,IAAvB,SAClBxP,GACE,yBAAKgH,UAAU,0BACb,0BAAMA,UAAU,8BAA8BK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SACxEwF,GAEH,uBAAGwC,KAAM0C,GACP,0BAAMxI,UAAU,yBAAyBhH,MAK3CyP,EAAU,SAAC,GAAD,IAAGtE,EAAH,EAAGA,MAAH,OACd,yBACEnE,UAAU,iEACVK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,OAAQiN,YAAavN,EAAMrL,OAAO2L,SAE9DqG,IAIC0F,EAAY,SAAApL,GAAC,OACjB,0BACE1F,IAAK0F,EAAE/G,GACPsI,UAAU,0EAETvB,EAAEqL,QAIDC,EAAS,kBACb7N,EAAKoB,QACLpB,EAAKoB,OAAOH,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKoB,OAAOf,UAC5B,yBAAKyD,UAAU,sBAAsB9D,EAAKoB,OAAOD,MAAMoD,IAAIoJ,MAI3DJ,EAAY,SAAAhL,GAAC,OACjB,0BACE1F,IAAK0F,EAAE/G,GACPsI,UAAU,0EAETvB,EAAEkL,QAIDC,EAAc,kBAClB1N,EAAK8O,SACL9O,EAAK8O,QAAQ7N,QACX,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAK8O,QAAQzO,UAC7B,yBAAKyD,UAAU,sBAAsB9D,EAAK8O,QAAQ3N,MAAMoD,IAAIgJ,MAI5Da,EAAgB,SAAA7L,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,iBACxB,wBAAIA,UAAU,uBAAuBvB,EAAE5J,MACvC,0BAAMmL,UAAU,WAAWvB,EAAE8L,UAC7B,0BAAMvK,UAAU,WAAWvB,EAAE1B,OAC7B,0BAAMiD,UAAU,WAAWvB,EAAExB,OAC7B,kBAAC,KAAD,CAAe+C,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkF,EAAa,kBACjBtO,EAAKlK,YACLkK,EAAKlK,WAAWmL,QACd,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKlK,WAAWuK,UAChC,yBAAKyD,UAAU,+BACZ9D,EAAKlK,WAAWqL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI6J,MAKnDN,EAAe,SAAAvL,GAAC,OACpB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,sCACxB,wBAAIA,UAAU,uBAAuBvB,EAAE1F,KACvC,yBAAKiH,UAAU,QACZvB,EAAEwL,OAAS,yBAAKjK,UAAU,0BAA0BvB,EAAEwL,OACzC,IAAbxL,EAAEyL,QACD,yBAAKlK,UAAU,QACZyB,MAAM0I,KAAK1I,MAAMhD,EAAEyL,SAASzJ,KAAI,SAACkB,EAAGyI,GAAJ,OAC/B,uBAAGrR,IAAKqR,EAAGpK,UAAU,yBAAyBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAA3E,eAUNuM,EAAY,kBAChBnO,EAAKnK,WACLmK,EAAKnK,UAAUoL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKnK,UAAUwK,UAC/B,yBAAKyD,UAAU,QAAQ9D,EAAKnK,UAAUsL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIuJ,MAItES,EAAY,SAAAhM,GAAC,OACjB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,gBACxB,wBAAIA,UAAU,qBAAqBvB,EAAE1F,KACrC,wBAAIiH,UAAU,IAAIvB,EAAEzF,SAIlB0R,EAAS,kBACbxO,EAAKjK,QACLiK,EAAKjK,OAAOkL,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKjK,OAAOsK,UAC5B,yBAAKyD,UAAU,oBACZ9D,EAAKjK,OAAOoL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIgK,MAK/C7B,EAAW,SAAAnK,GAAC,OAChB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,yBAAKA,UAAU,qCACb,6BACE,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAEoK,OAE5B,0BAAM7I,UAAU,uBAAhB,IACIvB,EAAEqK,MADN,MACgBrK,EAAEsK,IADlB,MAIF,kBAAC,KAAD,CAAe/I,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD0D,EAAO,kBACX9M,EAAKvK,MACLuK,EAAKvK,KAAKwL,QACR,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKvK,KAAK4K,UAC1B,yBAAKyD,UAAU,sBACZ9D,EAAKvK,KAAK0L,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAImI,MAK7CK,EAAgB,SAAAxK,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,yBAAKA,UAAU,qCACb,6BACE,wBAAIA,UAAU,iBAAiBvB,EAAE5J,MACjC,uBAAGmL,UAAU,WAAWvB,EAAEyK,QAE5B,yBAAKlJ,UAAU,sCACb,0BAAMA,UAAU,oBAAoBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAC9DW,EAAE0K,OAEL,0BAAMnJ,UAAU,uBAAhB,IACIvB,EAAEqK,MADN,MACgBrK,EAAEsK,IADlB,OAKJ,kBAAC,KAAD,CAAe/I,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD8D,EAAY,kBAChBlN,EAAKtK,WACLsK,EAAKtK,UAAUuL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKtK,UAAU2K,UAC/B,yBAAKyD,UAAU,sBACZ9D,EAAKtK,UAAUyL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIwI,MAKlDI,EAAY,SAAA5K,GAAC,OACjB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDgE,EAAS,kBACbpN,EAAKrK,QACLqK,EAAKrK,OAAOsL,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKrK,OAAO0K,UAC5B,yBAAKyD,UAAU,sBACZ9D,EAAKrK,OAAOwL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI4I,MAK/CE,EAAoB,SAAA9K,GAAC,OACzB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkE,EAAiB,kBACrBtN,EAAKpK,gBACLoK,EAAKpK,eAAeqL,QAClB,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKpK,eAAeyK,UACpC,yBAAKyD,UAAU,sBACZ9D,EAAKpK,eAAeuL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI8I,MAK7D,OACE,yBACEvJ,UAAU,OACVK,MAAO,CACLsK,WAAYnN,EAAME,KAAKC,OACvB+L,gBAAiBlM,EAAMrL,OAAOyL,WAC9B0K,MAAO9K,EAAMrL,OAAO0L,UAGtB,yBAAKmC,UAAU,yCACb,kBAACkI,EAAD,MAEA,yBAAKlI,UAAS,UAA4B,KAAvB9D,EAAKzK,QAAQ+K,MAAe,aAAe,gBAC5D,kBAACsO,EAAD,OAGF,yBAAK9K,UAAU,8BACb,yBAAKA,UAAU,gBACb,kBAACuI,EAAD,CACEjF,KAAK,QACLtK,MAAOkD,EAAKzK,QAAQsL,MACpByL,KAAI,cAAStM,EAAKzK,QAAQsL,SAE5B,kBAACwL,EAAD,CACEjF,KAAK,WACLtK,MAAOkD,EAAKzK,QAAQuL,QACpBwL,KAAI,iBAAYtM,EAAKzK,QAAQuL,WAE/B,kBAACuL,EAAD,CACEjF,KAAK,QACLtK,MAAOkD,EAAKzK,QAAQwL,MACpBuL,KAAI,iBAAYtM,EAAKzK,QAAQwL,SAE/B,kBAACsL,EAAD,CAAajF,KAAK,cAActK,MAAOkD,EAAKzK,QAAQ6K,QAAQQ,SAG9D,kBAACiN,EAAD,MACA,kBAACH,EAAD,MACA,kBAACS,EAAD,MACA,kBAACb,EAAD,OAGF,yBAAKxJ,UAAU,cACb,kBAACgJ,EAAD,MACA,kBAACI,EAAD,MACA,kBAACE,EAAD,MACA,kBAACkB,EAAD,MACA,kBAACE,EAAD,U,UC7SGE,G,QAAQ1Q,EACN2Q,MC0TAI,GAxTA,WACb,IACQtU,EADQyO,qBAAWpG,IACnBrI,MACAuF,EAAgBvF,EAAhBuF,KAAMsB,EAAU7G,EAAV6G,MAHK,EAKCtF,GAASsF,EAAMrL,OAAO2L,SAAW,GAA7CxF,EALW,EAKXA,EAAGC,EALQ,EAKRA,EAAGP,EALK,EAKLA,EAERkQ,EAAQ,iBACW,KAAvBhM,EAAKzK,QAAQ+K,OACX,yBACEwD,UAAU,oDACVK,MAAO,CACL0K,YAAavN,EAAMrL,OAAOyL,YAE5BuK,IAAKjM,EAAKzK,QAAQ+K,MAClB4L,IAAI,uBAIJ8C,EAAW,kBACf,6BACE,wBAAIlL,UAAU,oCAAoC9D,EAAKzK,QAAQgL,WAC/D,wBAAIuD,UAAU,oCAAoC9D,EAAKzK,QAAQiL,UAC/D,yBAAKsD,UAAU,4BAA4B9D,EAAKzK,QAAQkL,YAItD4L,EAAc,SAAC,GAAD,IAAGjF,EAAH,EAAGA,KAAMtK,EAAT,EAASA,MAAT,IAAgBwP,YAAhB,MAAuB,IAAvB,SAClBxP,GACE,yBAAKgH,UAAU,0BACb,yBACEA,UAAU,6DACVK,MAAO,CAAEqJ,gBAAiBlM,EAAMrL,OAAOyL,aAEvC,uBACEoC,UAAU,0DACVK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAE5BwF,IAGL,uBAAGwC,KAAM0C,GACP,0BAAMxI,UAAU,iCAAiChH,MAKnDyP,EAAU,SAAC,GAAD,IAAGtE,EAAH,EAAGA,MAAH,OACd,wBAAInE,UAAU,kDAAkDmE,IAG5DuE,EAAY,kBAChBxM,EAAKxK,WACLwK,EAAKxK,UAAUyL,QACb,yBAAK6C,UAAU,iDACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKxK,UAAU6K,UAC/B,kBAAC,KAAD,CAAeyD,UAAU,UAAU2I,OAAQzM,EAAKxK,UAAU0L,SAI1DyM,EAAY,SAAApL,GAAC,OACjB,wBAAI1F,IAAK0F,EAAE/G,GAAIsI,UAAU,gBACtBvB,EAAEqL,QAIDC,EAAS,kBACb7N,EAAKoB,QACLpB,EAAKoB,OAAOH,QACV,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKoB,OAAOf,UAC5B,4BAAKL,EAAKoB,OAAOD,MAAMoD,IAAIoJ,MAI3BJ,EAAY,SAAAhL,GAAC,OACjB,wBAAI1F,IAAK0F,EAAE/G,GAAIsI,UAAU,gBACtBvB,EAAEkL,QAIDC,EAAc,kBAClB1N,EAAK8O,SACL9O,EAAK8O,QAAQ7N,QACX,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAK8O,QAAQzO,UAC7B,4BAAKL,EAAK8O,QAAQ3N,MAAMoD,IAAIgJ,MAI5BR,EAAgB,SAAAxK,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,yBAAKA,UAAU,qCACb,6BACE,wBAAIA,UAAU,iBACXvB,EAAE5J,KACH,2BAAOmL,UAAU,QACF,KAAZvB,EAAEqK,OAA0B,KAAVrK,EAAEsK,KACnB,0BAAM/I,UAAU,uBAAhB,IACIvB,EAAEqK,MADN,MACgBrK,EAAEsK,IADlB,OAMN,uBAAG/I,UAAU,WAAWvB,EAAEyK,QAE5B,yBAAKlJ,UAAU,sCACb,0BAAMA,UAAU,oBAAoBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAC9DW,EAAE0K,SAIT,kBAAC,KAAD,CAAenJ,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD8D,EAAY,kBAChBlN,EAAKtK,WACLsK,EAAKtK,UAAUuL,QACb,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKtK,UAAU2K,UAC9BL,EAAKtK,UAAUyL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIwI,KAIhDM,EAAoB,SAAA9K,GAAC,OACzB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkE,EAAiB,kBACrBtN,EAAKpK,gBACLoK,EAAKpK,eAAeqL,QAClB,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKpK,eAAeyK,UACnCL,EAAKpK,eAAeuL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI8I,KAIrDF,EAAY,SAAA5K,GAAC,OACjB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDgE,EAAS,kBACbpN,EAAKrK,QACLqK,EAAKrK,OAAOsL,QACV,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKrK,OAAO0K,UAC3BL,EAAKrK,OAAOwL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI4I,KAI7CiB,EAAgB,SAAA7L,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,iBACxB,wBAAIA,UAAU,uBAAuBvB,EAAE5J,MACvC,0BAAMmL,UAAU,WAAWvB,EAAE8L,UAC7B,0BAAMvK,UAAU,WAAWvB,EAAE1B,OAC7B,0BAAMiD,UAAU,WAAWvB,EAAExB,OAC7B,kBAAC,KAAD,CAAe+C,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkF,EAAa,kBACjBtO,EAAKlK,YACLkK,EAAKlK,WAAWmL,QACd,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKlK,WAAWuK,UAChC,yBAAKyD,UAAU,0BACZ9D,EAAKlK,WAAWqL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI6J,MAKnD1B,EAAW,SAAAnK,GAAC,OAChB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,yBAAKA,UAAU,qCACb,6BACE,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAEoK,OAE5B,0BAAM7I,UAAU,uBAAhB,IACIvB,EAAEqK,MADN,MACgBrK,EAAEsK,IADlB,MAIF,kBAAC,KAAD,CAAe/I,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD0D,EAAO,kBACX9M,EAAKvK,MACLuK,EAAKvK,KAAKwL,QACR,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKvK,KAAK4K,UACzBL,EAAKvK,KAAK0L,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAImI,KAI3CoB,EAAe,SAAAvL,GAAC,OACpB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,sCACxB,wBAAIA,UAAU,uBAAuBvB,EAAE1F,KACvC,yBAAKiH,UAAU,QACZvB,EAAEwL,OAAS,yBAAKjK,UAAU,0BAA0BvB,EAAEwL,OACzC,IAAbxL,EAAEyL,QACD,yBAAKlK,UAAU,QACZyB,MAAM0I,KAAK1I,MAAMhD,EAAEyL,SAASzJ,KAAI,SAACkB,EAAGyI,GAAJ,OAC/B,uBAAGrR,IAAKqR,EAAGpK,UAAU,yBAAyBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAA3E,eAUNuM,EAAY,kBAChBnO,EAAKnK,WACLmK,EAAKnK,UAAUoL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKnK,UAAUwK,UAC/B,yBAAKyD,UAAU,QAAQ9D,EAAKnK,UAAUsL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIuJ,MAItES,EAAY,SAAAhM,GAAC,OACjB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,gBACxB,wBAAIA,UAAU,qBAAqBvB,EAAE1F,KACrC,4BAAK0F,EAAEzF,SAIL0R,EAAS,kBACbxO,EAAKjK,QACLiK,EAAKjK,OAAOkL,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKjK,OAAOsK,UAC5B,yBAAKyD,UAAU,oBACZ9D,EAAKjK,OAAOoL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIgK,MAKrD,OACE,yBACEpK,MAAO,CACLsK,WAAYnN,EAAME,KAAKC,OACvB+L,gBAAiBlM,EAAMrL,OAAOyL,WAC9B0K,MAAO9K,EAAMrL,OAAO0L,UAGtB,yBAAKmC,UAAU,qBACb,yBACEA,UAAU,uBACVK,MAAO,CAAEqJ,gBAAiBlM,EAAMrL,OAAO2L,OAAQwK,MAAO9K,EAAMrL,OAAOyL,aAEnE,yBAAKoC,UAAU,qBACb,kBAACkI,EAAD,MACA,kBAACgD,EAAD,OAGF,wBAAIlL,UAAU,0BAEd,kBAACuI,EAAD,CAAajF,KAAK,QAAQtK,MAAOkD,EAAKzK,QAAQsL,MAAOyL,KAAI,cAAStM,EAAKzK,QAAQsL,SAC/E,kBAACwL,EAAD,CACEjF,KAAK,QACLtK,MAAOkD,EAAKzK,QAAQwL,MACpBuL,KAAI,iBAAYtM,EAAKzK,QAAQwL,SAE/B,kBAACsL,EAAD,CACEjF,KAAK,WACLtK,MAAOkD,EAAKzK,QAAQuL,QACpBwL,KAAI,iBAAYtM,EAAKzK,QAAQuL,WAE/B,kBAACuL,EAAD,CAAajF,KAAK,cAActK,MAAOkD,EAAKzK,QAAQ6K,QAAQQ,SAG9D,yBACEkD,UAAU,uBACVK,MAAO,CAAEqJ,gBAAgB,QAAD,OAAUpR,EAAV,aAAgBC,EAAhB,aAAsBP,EAAtB,YAExB,kBAAC0Q,EAAD,MACA,kBAACgC,EAAD,OAGF,yBACE1K,UAAU,uBACVK,MAAO,CAAEqJ,gBAAgB,QAAD,OAAUpR,EAAV,aAAgBC,EAAhB,aAAsBP,EAAtB,YAExB,kBAAC+R,EAAD,MACA,kBAACH,EAAD,MACA,kBAACS,EAAD,MACA,kBAACjB,EAAD,MACA,kBAACI,EAAD,OAGF,yBAAKxJ,UAAU,wBACb,kBAACgJ,EAAD,MACA,kBAACM,EAAD,MACA,kBAACkB,EAAD,U,UCpTGI,G,QAAQ1Q,EACN+Q,MCkTAE,GAjTE,WACf,IACQxU,EADQyO,qBAAWpG,IACnBrI,MACAuF,EAAgBvF,EAAhBuF,KAAMsB,EAAU7G,EAAV6G,MAER0K,EAAQ,iBACW,KAAvBhM,EAAKzK,QAAQ+K,OACX,yBAAKwD,UAAU,aACb,yBACEA,UAAU,yBACVK,MAAO,CACL+K,YAAa,EACbL,YAAavN,EAAMrL,OAAOyL,YAE5BuK,IAAKjM,EAAKzK,QAAQ+K,MAClB4L,IAAI,yBAKNiD,EAAsB,kBAC1B,yBAAKrL,UAAU,aACb,wBAAIA,UAAU,sBACX9D,EAAKzK,QAAQgL,UADhB,IAC4BP,EAAKzK,QAAQiL,UAEzC,4BAAKR,EAAKzK,QAAQkL,YAIhB8L,EAAU,SAAC,GAAD,IAAGtE,EAAH,EAAGA,MAAH,IAAUmH,aAAV,gBACd,yBACEtL,UAAS,oBAAesL,EAAQ,yCAA2C,IAC3EjL,MAAO,CAAEqJ,gBAAiB4B,EAAQ,GAAK,wBAEvC,wBAAItL,UAAS,UAAKsL,EAAQ,GAAK,OAAlB,mBAA2CnH,KAItDoH,EAAU,kBACd,yBAAKvL,UAAU,aACb,wBAAIA,UAAU,qBAAd,WACA,yBAAKA,UAAU,WAAW9D,EAAKzK,QAAQ6K,QAAQM,OAC/C,yBAAKoD,UAAU,WAAW9D,EAAKzK,QAAQ6K,QAAQO,OAC/C,yBAAKmD,UAAU,WAAW9D,EAAKzK,QAAQ6K,QAAQQ,SAI7CyL,EAAc,SAAC,GAAD,IAAGpE,EAAH,EAAGA,MAAOnL,EAAV,EAAUA,MAAV,IAAiBwP,YAAjB,MAAwB,IAAxB,SAClBxP,GACE,yBAAKgH,UAAU,aACb,wBAAIA,UAAU,qBAAqBmE,GACnC,uBAAG2B,KAAM0C,GACP,yBAAKxI,UAAU,WAAWhH,MAK5BwS,EAAqB,kBACzB,6BACE,kBAAC/C,EAAD,CAAStE,MAAOjI,EAAKzK,QAAQ8K,UAC7B,kBAACgP,EAAD,MACA,kBAAChD,EAAD,CAAapE,MAAM,QAAQnL,MAAOkD,EAAKzK,QAAQsL,MAAOyL,KAAI,cAAStM,EAAKzK,QAAQsL,SAChF,kBAACwL,EAAD,CACEpE,MAAM,gBACNnL,MAAOkD,EAAKzK,QAAQwL,MACpBuL,KAAI,iBAAYtM,EAAKzK,QAAQwL,SAE/B,kBAACsL,EAAD,CACEpE,MAAM,UACNnL,MAAOkD,EAAKzK,QAAQuL,QACpBwL,KAAI,iBAAYtM,EAAKzK,QAAQuL,aAK7B6M,EAAY,SAAApL,GAAC,OACjB,wBAAI1F,IAAK0F,EAAE/G,GAAIsI,UAAU,gBACtBvB,EAAEqL,QAIDC,EAAS,kBACb7N,EAAKoB,QACLpB,EAAKoB,OAAOH,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKoB,OAAOf,UAC5B,wBAAIyD,UAAU,kBAAkB9D,EAAKoB,OAAOD,MAAMoD,IAAIoJ,MAItDJ,EAAY,SAAAhL,GAAC,OACjB,wBAAI1F,IAAK0F,EAAE/G,GAAIsI,UAAU,gBACtBvB,EAAEkL,QAIDC,EAAc,kBAClB1N,EAAKqB,aACLrB,EAAKqB,YAAYJ,QACf,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKqB,YAAYhB,UACjC,wBAAIyD,UAAU,kBAAkB9D,EAAKqB,YAAYF,MAAMoD,IAAIgJ,MAI3Df,EAAY,kBAChBxM,EAAKxK,WAAawK,EAAKxK,UAAUyL,QAAU,kBAAC,KAAD,CAAe6C,UAAU,cAAc2I,OAAQzM,EAAKxK,UAAU0L,QAErGwL,EAAW,SAAAnK,GAAC,OAChB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,aACxB,yBAAKA,UAAU,wBACb,6BACE,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAEoK,OAE5B,0BAAM7I,UAAU,uBAAhB,IACIvB,EAAEqK,MADN,MACgBrK,EAAEsK,IADlB,MAIF,kBAAC,KAAD,CAAe/I,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD0D,EAAO,kBACX9M,EAAKvK,MACLuK,EAAKvK,KAAKwL,QACR,6BACE,kBAACsL,EAAD,CAAS6C,OAAK,EAACnH,MAAOjI,EAAKvK,KAAK4K,UAC/BL,EAAKvK,KAAK0L,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAImI,KAI3C0B,EAAgB,SAAA7L,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,iBACxB,wBAAIA,UAAU,uBAAuBvB,EAAE5J,MACvC,0BAAMmL,UAAU,WAAWvB,EAAE8L,UAC7B,0BAAMvK,UAAU,WAAWvB,EAAE1B,OAC7B,0BAAMiD,UAAU,WAAWvB,EAAExB,OAC7B,kBAAC,KAAD,CAAe+C,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkF,EAAa,kBACjBtO,EAAKlK,YACLkK,EAAKlK,WAAWmL,QACd,6BACE,kBAACsL,EAAD,CAAS6C,OAAK,EAACnH,MAAOjI,EAAKlK,WAAWuK,UACtC,yBAAKyD,UAAU,+BACZ9D,EAAKlK,WAAWqL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI6J,MAKnDN,EAAe,SAAAvL,GAAC,OACpB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,sBACxB,yBAAKA,UAAU,qCACb,wBAAIA,UAAU,4BAA4BvB,EAAE1F,KAC/B,KAAZ0F,EAAEwL,OAAgB,yBAAKjK,UAAU,qBAAqBvB,EAAEwL,QAG7C,IAAbxL,EAAEyL,QACD,yBAAKlK,UAAU,gBACb,yBACEA,UAAU,wBACVK,MAAO,CACLqJ,gBAAiB,yBAGrB,yBACE1J,UAAU,gCACVK,MAAO,CACL5F,MAAM,GAAD,OAAgB,GAAXgE,EAAEyL,OAAP,KACLR,gBAAiB,2BAQvBW,EAAY,kBAChBnO,EAAKnK,WACLmK,EAAKnK,UAAUoL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKnK,UAAUwK,UAC/B,yBAAKyD,UAAU,aACZ9D,EAAKnK,UAAUsL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIuJ,MAKlDf,EAAgB,SAAAxK,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,aACxB,yBAAKA,UAAU,wBACb,6BACE,wBAAIA,UAAU,iBAAiBvB,EAAE5J,MACjC,uBAAGmL,UAAU,WAAWvB,EAAEyK,QAE5B,yBAAKlJ,UAAU,2BACb,0BAAMA,UAAU,qBAAqBvB,EAAE0K,OACvC,0BAAMnJ,UAAU,uBAAhB,IACIvB,EAAEqK,MADN,MACgBrK,EAAEsK,IADlB,OAKJ,kBAAC,KAAD,CAAe/I,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD8D,EAAY,kBAChBlN,EAAKtK,WACLsK,EAAKtK,UAAUuL,QACb,6BACE,kBAACsL,EAAD,CAAS6C,OAAK,EAACnH,MAAOjI,EAAKtK,UAAU2K,UACpCL,EAAKtK,UAAUyL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIwI,KAIhDI,EAAY,SAAA5K,GAAC,OACjB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,aACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDgE,EAAS,kBACbpN,EAAKrK,QACLqK,EAAKrK,OAAOsL,QACV,6BACE,kBAACsL,EAAD,CAAS6C,OAAK,EAACnH,MAAOjI,EAAKrK,OAAO0K,UACjCL,EAAKrK,OAAOwL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI4I,KAI7CE,EAAoB,SAAA9K,GAAC,OACzB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,aACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkE,EAAiB,kBACrBtN,EAAKpK,gBACLoK,EAAKpK,eAAeqL,QAClB,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKpK,eAAeyK,UACnCL,EAAKpK,eAAeuL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI8I,KAIrDkB,EAAY,SAAAhM,GAAC,OACjB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,aACxB,wBAAIA,UAAU,qBAAqBvB,EAAE1F,KACrC,yBAAKiH,UAAU,WAAWvB,EAAEzF,SAI1B0R,EAAS,kBACbxO,EAAKjK,QACLiK,EAAKjK,OAAOkL,QACV,6BACE,kBAACsL,EAAD,CAAS6C,OAAK,EAACnH,MAAOjI,EAAKjK,OAAOsK,UACjCL,EAAKjK,OAAOoL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIgK,KAInD,OACE,yBACEpK,MAAO,CACLsK,WAAYnN,EAAME,KAAKC,OACvB+L,gBAAiBlM,EAAMrL,OAAOyL,WAC9B0K,MAAO9K,EAAMrL,OAAO0L,UAGtB,yBAAKmC,UAAU,qBACb,yBACEA,UAAU,aACVK,MAAO,CACLiI,MAAO9K,EAAMrL,OAAOyL,WACpB8L,gBAAiBlM,EAAMrL,OAAO2L,SAGhC,kBAACoK,EAAD,MACA,kBAACmD,EAAD,MACA,kBAACG,EAAD,MACA,kBAACzB,EAAD,MACA,kBAACH,EAAD,MACA,kBAACS,EAAD,MACA,kBAACb,EAAD,OAEF,yBAAKxJ,UAAU,cACb,kBAAC0I,EAAD,MACA,kBAACM,EAAD,MACA,kBAACI,EAAD,MACA,kBAACE,EAAD,MACA,kBAACkB,EAAD,MACA,kBAACE,EAAD,U,UC5SGE,G,QAAQ1Q,EACNiR,MCkTAM,GAhTA,WACb,IACQ9U,EADQyO,qBAAWpG,IACnBrI,MACAuF,EAAgBvF,EAAhBuF,KAAMsB,EAAU7G,EAAV6G,MAHK,EAKCtF,GAASsF,EAAMrL,OAAO2L,SAAW,GAA7CxF,EALW,EAKXA,EAAGC,EALQ,EAKRA,EAAGP,EALK,EAKLA,EAERkQ,EAAQ,iBACW,KAAvBhM,EAAKzK,QAAQ+K,OACX,yBACEwD,UAAU,iCACVmI,IAAKjM,EAAKzK,QAAQ+K,MAClB4L,IAAI,uBAIJ8C,EAAW,kBACf,yBAAKlL,UAAU,mCACb,4BAAK9D,EAAKzK,QAAQgL,WAClB,4BAAKP,EAAKzK,QAAQiL,YAIhBgP,EAAW,kBACf,yBAAK1L,UAAU,+CAA+C9D,EAAKzK,QAAQkL,WAGvE4L,EAAc,SAAC,GAAD,IAAGpE,EAAH,EAAGA,MAAOnL,EAAV,EAAUA,MAAV,OAClBA,GACE,yBAAKgH,UAAU,iBACb,wBAAIA,UAAU,oBAAoBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAC5DqG,GAEH,uBAAGnE,UAAU,WAAWhH,KAIxBwS,EAAqB,kBACzB,yBACExL,UAAU,iCACVK,MAAO,CACL0K,YAAavN,EAAMrL,OAAO2L,SAG5B,yBACEkC,UAAU,6BACVK,MAAO,CAAEsL,IAAK,SAAUrD,MAAO9K,EAAMrL,OAAO2L,SAE5C,wBAAIkC,UAAU,QACZ,uBAAGA,UAAU,kBAAb,WAIJ,yBAAKA,UAAU,8BACb,kBAACuI,EAAD,CAAapE,MAAM,eAAenL,MAAOkD,EAAKzK,QAAQsL,QACtD,kBAACwL,EAAD,CAAapE,MAAM,gBAAgBnL,MAAOkD,EAAKzK,QAAQwL,QACvD,kBAACsL,EAAD,CAAapE,MAAM,UAAUnL,MAAOkD,EAAKzK,QAAQuL,UAEjD,yBAAKgD,UAAU,iBACb,uBAAGA,UAAU,yBAAyBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAAnE,QAGA,uBAAGkC,UAAU,WAAW9D,EAAKzK,QAAQ6K,QAAQM,OAC7C,uBAAGoD,UAAU,WAAW9D,EAAKzK,QAAQ6K,QAAQO,OAC7C,uBAAGmD,UAAU,WAAW9D,EAAKzK,QAAQ6K,QAAQQ,WAM/C2L,EAAU,SAAC,GAAD,IAAGtE,EAAH,EAAGA,MAAH,OACd,wBACEnE,UAAU,qDACVK,MAAO,CAAE0K,YAAavN,EAAMrL,OAAO2L,OAAQwK,MAAO9K,EAAMrL,OAAO2L,SAE9DqG,IAICuE,EAAY,kBAChBxM,EAAKxK,UAAUyL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKxK,UAAU6K,UAC/B,kBAAC,KAAD,CAAeyD,UAAU,uBAAuB2I,OAAQzM,EAAKxK,UAAU0L,SAIvEwL,EAAW,SAAAnK,GAAC,OAChB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,yBAAKA,UAAU,wBACb,6BACE,wBAAIA,UAAU,yBAAyBvB,EAAE0F,OACzC,uBAAGnE,UAAU,kCACVvB,EAAEoK,KADL,MACcpK,EAAEqK,MADhB,MAC0BrK,EAAEsK,OAIhC,kBAAC,KAAD,CAAe/I,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD0D,EAAO,kBACX9M,EAAKvK,MACLuK,EAAKvK,KAAKwL,QACR,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKvK,KAAK4K,UACzBL,EAAKvK,KAAK0L,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAImI,KAI3CK,EAAgB,SAAAxK,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,QACxB,6BACE,wBAAIA,UAAU,yBAAyBvB,EAAE5J,MACzC,uBAAGmL,UAAU,sBAAsBvB,EAAEyK,OACrC,uBAAGlJ,UAAU,sBACVvB,EAAEqK,MADL,MACerK,EAAEsK,MAGnB,kBAAC,KAAD,CAAe/I,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhD8D,EAAY,kBAChBlN,EAAKtK,WACLsK,EAAKtK,UAAUuL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKtK,UAAU2K,UAC/B,yBAAKyD,UAAU,0BACZ9D,EAAKtK,UAAUyL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIwI,MAKlDI,EAAY,SAAA5K,GAAC,OACjB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,kBACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDgE,EAAS,kBACbpN,EAAKrK,QACLqK,EAAKrK,OAAOsL,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKrK,OAAO0K,UAC3BL,EAAKrK,OAAOwL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI4I,KAI7CE,EAAoB,SAAA9K,GAAC,OACzB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,kBACxB,wBAAIA,UAAU,iBAAiBvB,EAAE0F,OACjC,uBAAGnE,UAAU,WAAWvB,EAAE9B,UAC1B,kBAAC,KAAD,CAAeqD,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkE,EAAiB,kBACrBtN,EAAKpK,gBACLoK,EAAKpK,eAAeqL,QAClB,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKpK,eAAeyK,UACnCL,EAAKpK,eAAeuL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI8I,KAIrDM,EAAY,SAAApL,GAAC,OACjB,wBAAI1F,IAAK0F,EAAE/G,GAAIsI,UAAU,uBACtBvB,EAAEqL,QAIDC,EAAS,kBACb7N,EAAKoB,QACLpB,EAAKoB,OAAOH,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKoB,OAAOf,UAC5B,wBAAIyD,UAAU,+BAA+B9D,EAAKoB,OAAOD,MAAMoD,IAAIoJ,MAInEJ,EAAY,SAAAhL,GAAC,OACjB,wBAAI1F,IAAK0F,EAAE/G,GAAIsI,UAAU,uBACtBvB,EAAEkL,QAIDC,EAAc,kBAClB1N,EAAK8O,SACL9O,EAAK8O,QAAQ7N,QACX,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAK8O,QAAQzO,UAC7B,wBAAIyD,UAAU,6CACX9D,EAAK8O,QAAQ3N,MAAMoD,IAAIgJ,MAK1BO,EAAe,SAAAvL,GAAC,OACpB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,sCACxB,wBAAIA,UAAU,iCAAiCvB,EAAE1F,KACjD,yBAAKiH,UAAU,QACZvB,EAAEwL,OAAS,yBAAKjK,UAAU,0BAA0BvB,EAAEwL,OACzC,IAAbxL,EAAEyL,QACD,yBAAKlK,UAAU,QACZyB,MAAM0I,KAAK1I,MAAMhD,EAAEyL,SAASzJ,KAAI,SAACkB,EAAGyI,GAAJ,OAC/B,uBAAGrR,IAAKqR,EAAGpK,UAAU,yBAAyBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAA3E,eAUNuM,EAAY,kBAChBnO,EAAKnK,WACLmK,EAAKnK,UAAUoL,QACb,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKnK,UAAUwK,UAC/B,yBAAKyD,UAAU,SAAS9D,EAAKnK,UAAUsL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIuJ,MAIvEM,EAAgB,SAAA7L,GAAC,OACrB,yBAAK1F,IAAK0F,EAAE/G,GAAIsI,UAAU,iBACxB,wBAAIA,UAAU,uBAAuBvB,EAAE5J,MACvC,0BAAMmL,UAAU,WAAWvB,EAAE8L,UAC7B,0BAAMvK,UAAU,WAAWvB,EAAE1B,OAC7B,0BAAMiD,UAAU,WAAWvB,EAAExB,OAC7B,kBAAC,KAAD,CAAe+C,UAAU,eAAe2I,OAAQlK,EAAE6G,gBAIhDkF,EAAa,kBACjBtO,EAAKlK,YACLkK,EAAKlK,WAAWmL,QACd,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKlK,WAAWuK,UAChC,yBAAKyD,UAAU,0BACZ9D,EAAKlK,WAAWqL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAI6J,MAKnDG,EAAY,SAAAhM,GAAC,OACjB,wBAAI1F,IAAK0F,EAAE/G,IACT,wBAAIsI,UAAU,wCAAwCvB,EAAE1F,KACxD,wBAAIiH,UAAU,4BAA4BvB,EAAEzF,SAI1C0R,EAAS,kBACbxO,EAAKjK,QACLiK,EAAKjK,OAAOkL,QACV,6BACE,kBAACsL,EAAD,CAAStE,MAAOjI,EAAKjK,OAAOsK,UAC5B,2BAAOyD,UAAU,yBACf,+BAAQ9D,EAAKjK,OAAOoL,MAAMoI,QAAO,SAAAhH,GAAC,OAAIA,EAAEtB,UAAQsD,IAAIgK,OAK5D,OACE,yBACEpK,MAAO,CACLsK,WAAYnN,EAAME,KAAKC,OACvB+L,gBAAiBlM,EAAMrL,OAAOyL,WAC9B0K,MAAO9K,EAAMrL,OAAO0L,UAGtB,yBAAKmC,UAAU,qBACb,yBACEA,UAAU,+DACVK,MAAO,CAAEqJ,gBAAgB,QAAD,OAAUpR,EAAV,aAAgBC,EAAhB,aAAsBP,EAAtB,UAAiC4T,UAAW,WAEpE,yBAAK5L,UAAU,0BACb,kBAACkI,EAAD,MACA,kBAACgD,EAAD,MACA,kBAACQ,EAAD,OAEF,kBAACF,EAAD,MACA,kBAAC9C,EAAD,MACA,kBAACkB,EAAD,MACA,kBAACS,EAAD,MACA,kBAACb,EAAD,OAGF,yBAAKxJ,UAAU,6CACb,kBAACgJ,EAAD,MACA,kBAACI,EAAD,MACA,kBAACW,EAAD,MACA,kBAACT,EAAD,MACA,kBAACkB,EAAD,MACA,kBAACE,EAAD,U,UC5SGE,G,QAAQ1Q,EACNuR,M,SCMTI,GAAS,CACbC,OAAQ,CACNvB,SAAU,WACVwB,KAAM,EACNC,MAAO,EACPC,OAAQ,EACR3L,QAAS,OACT4L,cAAe,SACfC,eAAgB,SAChBC,WAAY,QACZ9D,MAAO,QACPoB,gBAAiB,OACjBhP,OAAQ,QACR2R,YAAa,SAEfvN,QAAS,CACPhD,UAAW,QACXwQ,WAAY,SAwYDC,GApYA,WACb,IACQ5V,EADQyO,qBAAWpG,IACnBrI,MACAuF,EAAgBvF,EAAhBuF,KAAMsB,EAAU7G,EAAV6G,MAHK,EAKCtF,GAASsF,EAAMrL,OAAO2L,SAAW,GAA7CxF,EALW,EAKXA,EAAGC,EALQ,EAKRA,EAAGP,EALK,EAKLA,EAERyQ,EAAU,SAAC,GAAD,IAAGtE,EAAH,EAAGA,MAAOnE,EAAV,EAAUA,UAAV,OACd,wBACEA,UAAS,8FAAyFA,IAEjGmE,IAIC+D,EAAQ,iBACiD,KAA5DvG,OAAMzF,EAAM,uCAAwC,KACnD,yBAAK8D,UAAU,iBACb,yBACEA,UAAU,oCACVmI,IAAKxG,OAAMzF,EAAM,uCAAwC,IACzDkM,IAAI,oBACJ/H,MAAO,CACL3F,OAAQ,aAKd,yBAAKsF,UAAU,iBACb,yBAAKK,MAAO,CACR3F,OAAQ,aAMZ8R,EAAW,kBACf,wBAAIxM,UAAU,oCAAsC9D,EAAKC,OAAO,UAAU,GAAGC,UAAU,GAAO,KAAKF,EAAKC,OAAO,UAAU,GAAGC,UAAUqE,KAAI,SAASgM,EAAKlV,GAC9I,GAAGA,EAAQ,GAAKkV,EAAK,UAAU,CAC7B,IAAI5X,EAAO4X,EAAK,UACZC,EAAkBxQ,EAAKC,OAAO,UAAU,GAAGE,WAAW7E,WAAU,SAAAiH,GAAC,OAAEA,EAAE,eAAegO,EAAK,gBAM7F,OALGC,GAAmB,GACjBxQ,EAAKC,OAAO,UAAU,GAAGE,WAAWqQ,IAAoBxQ,EAAKC,OAAO,UAAU,GAAGE,WAAWqQ,GAAiB,YAC9G7X,GAAQ,IAAIqH,EAAKC,OAAO,UAAU,GAAGE,WAAWqQ,GAAiB,WAG9D7X,EAEP,OAAO,QAEpB4Q,QAAO,SAAUxS,GAClB,OAAa,MAANA,KACN0Z,KAAK,MAAM,IAAQ,KAGdC,EAAQ,kBACZ,wBAAI5M,UAAU,wCAAwCK,MAAO,CAAEwM,SAAU,WACjEpL,MAAMC,QAAQxF,EAAKC,OAAO,UAAU,GAAGC,WAAeF,EAAKC,OAAO,UAAU,GAAGC,UAAU,GAAG,UAAcF,EAAKC,OAAO,UAAU,GAAGC,UAD3I,IACyJqF,MAAMC,QAAQxF,EAAKC,OAAO,UAAU,GAAGE,YAAgBH,EAAKC,OAAO,UAAU,GAAGE,WAAW,GAAG,UAAcH,EAAKC,OAAO,UAAU,GAAGE,aAI1RyO,EAAS,kBACb,4BAAQzK,MAAOwL,GAAOC,QACpB,yBAAK9L,UAAU,QACb,kBAAC4M,EAAD,MACA,kBAACJ,EAAD,MACA,wBAAIxM,UAAU,oCAAoC2B,OAAMzF,EAAM,kCAAmC,QAKjGwM,EAAY,kBAChBxM,EAAKxK,WACLwK,EAAKxK,UAAUyL,QACb,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKxK,UAAU6K,UAC5BoF,OAAMzF,EAAM,4BAA4B,IAAIuE,KAAI,SAAChC,EAAGlH,GAAJ,OAC7C,kBAAC,KAAD,CAAewB,IAAK,YAAYxB,EAAOyI,UAAU,gBAAgB2I,OAAQlK,EAAE6G,iBAE9E3D,OAAMzF,EAAM,4BAA4B,IAAIuE,KAAI,SAAChC,EAAGlH,GAAJ,OAC/C,yBAAKwB,IAAK,UAAUxB,GAClB,uBAAGyI,UAAU,wBAAwBjH,IAAK,KAAKxB,IAC3CoK,OAAMlD,EAAE,2CAA4C,OAASkD,OAAMlD,EAAE,0CAA2C,OAASkD,OAAMlD,EAAE,4CAA6C,MAAS,KACvLkD,OAAMlD,EAAE,4CAA6C,MAASkD,OAAMlD,EAAE,4CAA6C,IAAI,IAAO,KAC9HkD,OAAMlD,EAAE,0CAA2C,MAASkD,OAAMlD,EAAE,0CAA2C,IAAI,IAAO,KAC1HkD,OAAMlD,EAAE,2CAA4C,MAASkD,OAAMlD,EAAE,2CAA4C,IAAO,IAJ5H,MAIqIkD,OAAMlD,EAAG,qBAAsB,MAAUkD,OAAMlD,EAAG,qBAAsB,IAAO,GAJpN,IAIyNkD,OAAMlD,EAAG,mBAAoB,MAAS,KAAMkD,OAAMlD,EAAE,mBAAmB,IAAM,UAO5S8J,EAAc,SAAC,GAAD,IAAGtI,EAAH,EAAGA,MAAOjH,EAAV,EAAUA,MAAV,OAClBA,GACE,yBAAKgH,UAAU,QACb,wBAAIA,UAAU,qBAAqBC,GACnC,uBAAGD,UAAU,WAAWhH,KAIxBuS,EAAU,kBAEZrP,EAAKC,OAAO,UAAU,GAAGG,SAAWJ,EAAKC,OAAO,UAAU,GAAGG,QAAQ1E,OAAO,GAC5EsE,EAAKI,QAAQa,QACX,yBAAK6C,UAAU,QACZ9D,EAAKC,OAAO,UAAU,GAAGG,QAAQmJ,QAAO,SAAAhH,GAAC,OAAK/C,KAAKyC,MAAMM,EAAEqG,eAAeG,cAAgBvJ,KAAKyC,MAAM,IAAIzC,MAAS,KAAG+E,IAAIqM,KAG1H,IAGFA,EAAc,SAACrO,EAAGlH,GAAJ,OAEd,yBAAKyI,UAAU,OAAOjH,IAAK4I,OAAMlD,EAAE,MAAO,SAC/B,IAARlH,EAAU,wBAAIyI,UAAU,qBAAqB9D,EAAKzK,QAAQ6K,QAAQC,SAAW,WAAgB,GAC9F,uBAAGyD,UAAU,WAAWvB,EAAE8F,eAC1B,uBAAGvE,UAAU,WAAWvB,EAAE+F,gBAA1B,IAA4C/F,EAAEgG,eAC9C,uBAAGzE,UAAU,WAAWvB,EAAEiG,eAA1B,IAA2CjG,EAAEkG,cAI/CoI,EAAU,kBACd7Q,EAAKgB,SAASC,QACZ,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAM,UAAUnE,UAAU,uBACnC,kBAACuL,EAAD,MACA,kBAAChD,EAAD,CAAatI,MAAM,QAAQjH,MAAO2I,OAAMA,QAAOzF,EAAKC,OAAO,UAAU,GAAGqJ,aAAa,CAACT,YAAY,cAAe,YAAa,MAC9H,kBAACwD,EAAD,CAAatI,MAAM,gBAAgBjH,MAAO2I,OAAMA,QAAOzF,EAAKC,OAAO,UAAU,GAAGqJ,aAAa,CAACT,YAAY,cAAe,QAAS,MAClI,kBAACwD,EAAD,CAAatI,MAAM,UAAUjH,MAAO2I,OAAMzF,EAAK,gCAAgC,QAK/E8Q,EAAoB,SAAAvO,GAAC,OACzBA,GACI,uBAAG1F,IAAK8L,gBAAR,KAAqBpG,EAArB,MAIAwO,EAAgB,SAAC,GAAD,IAAE3P,EAAF,EAAEA,OAAF,OACpBA,GAAWA,EAAO1F,OAAO,GACvB,yBAAKoI,UAAU,8BAEb1C,EAAOmI,QAAO,SAAAhH,GAAC,MAAW,KAANA,KAAWgC,IAAIuM,KAMnCE,EAAyB,SAAAzO,GAAC,OAC9BA,GACI,wBAAIuB,UAAU,eAAejH,IAAK8L,gBAAWpG,IAI7C0O,EAAqB,SAAC,GAAD,IAAEtG,EAAF,EAAEA,iBAAF,OACzBA,GAAqBA,EAAiBjP,OAAO,GAC3C,4BAEEiP,EAAiBpB,QAAO,SAAAhH,GAAC,MAAW,KAANA,KAAWgC,IAAIyM,KAM7CtE,EAAW,SAAAnK,GAAC,OAChB,yBAAK1F,IAAK4I,OAAMlD,EAAE,MAAO,QAASuB,UAAU,cAC1C,6BACE,wBAAIA,UAAU,iBAAiB2B,OAAMlD,EAAE,2BAA2B,KAClE,uBAAGuB,UAAU,yBACV2B,OAAMlD,EAAE,WAAY,IADvB,MAC+BkD,OAAMlD,EAAE,YAAY,IADnD,MAC2DkD,OAAMlD,EAAE,UAAU,MAG/E,kBAAC,KAAD,CAAeuB,UAAU,eAAe2I,OAAQhH,OAAMlD,EAAE,cAAc,MACtE,kBAAC0O,EAAD,CAAoBtG,iBAAkBlF,OAAMlD,EAAG,iCAAkC,MACjF,kBAACwO,EAAD,CAAe3P,OAAQqE,OAAMlD,EAAG,uBAAwB,QAItDuK,EAAO,kBACXrH,OAAMzF,EAAM,oCAAqC,IAAItE,QAAUsE,EAAKvK,KAAKwL,QACvE,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKvK,KAAK4K,UACzBoF,OAAMzF,EAAM,oCAAqC,IAAIuJ,QAAO,SAAAhH,GAAC,OAAKkD,OAAMlD,EAAG,MAAO,IAAI2O,SAAS,cAAY3M,IAAImI,KAIhHK,EAAgB,SAAAxK,GAAC,OACrB,yBAAK1F,IAAK4I,OAAMlD,EAAE,MAAO,QAASuB,UAAU,cAC1C,wBAAIA,UAAU,iBAAiB2B,OAAMlD,EAAG,sBAAuB,KAC/D,uBAAGuB,UAAU,WAAW2B,OAAMlD,EAAG,mBAAoB,IAArD,IAA2DkD,OAAMlD,EAAG,qCAAsC,KAC1G,yBAAKuB,UAAU,WACZ2B,OAAMlD,EAAG,kBAAmB,IAD/B,MACuCkD,OAAMlD,EAAG,gBAAiB,KAEjE,kBAAC,KAAD,CAAeuB,UAAU,eAAe2I,OAAQhH,OAAMlD,EAAG,WAAY,MACrE,kBAACwO,EAAD,CAAe3P,OAAQqE,OAAMlD,EAAG,UAAW,QAIzC2K,EAAY,kBACfzH,OAAMzF,EAAM,oCAAqC,IAAItE,OAAO,GAC7DsE,EAAKtK,UAAUuL,QACb,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKtK,UAAU2K,UAC9BoF,OAAMzF,EAAM,oCAAqC,IAAIuJ,QAAO,SAAAhH,GAAC,OAAMkD,OAAMlD,EAAG,MAAO,IAAI2O,SAAS,YAAmD,WAArCzL,OAAMlD,EAAG,qBAAsB,OAAiBgC,IAAIwI,KAqBnKc,EAAS,kBACb7N,EAAKoB,OAAOH,QACV,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAM,SAASnE,UAAU,kBAClC,wBAAIA,UAAU,qBArBD,WACjB,IAAIqN,EAAa1L,SAAQA,OAAMzF,EAAM,oCAAqC,KAAKuE,IAAI,UAAU6M,UAEzFC,EAAc5L,SAAQA,OAAMzF,EAAM,4BAA6B,KAAKuE,IAAI,kBAAkB6M,UAE1FE,EAAkB7L,SAAQA,OAAMzF,EAAM,oCAAqC,KAAKuE,IAAI,WAAW6M,UAE/FG,EAAgB9L,SAAQA,OAAMzF,EAAM,oCAAqC,KAAKuE,IAAI,SAAS6M,UAAU7M,IAAI,aAAa6M,UAAU7M,IAAI,WAAW6M,UAE/II,EAAyB/L,SAAQA,OAAMzF,EAAM,oCAAqC,KAAKuE,IAAI,SAASA,IAAI,eAAe6M,UAAU7M,IAAI,WAAW6M,UAAU7M,IAAI,WAAW6M,UAEzKK,EAAyBhM,SAAQA,OAAMzF,EAAM,2CAA4C,KAAKuE,IAAI,UAAU6M,UAAU7M,IAAI,WAAW6M,UAErIM,EAA0BjM,SAAQA,OAAMzF,EAAM,2CAA4C,KAAKuE,IAAI,UAAU6M,UAAU7M,IAAI,YAAY6M,UAE3I,MAAM,GAAN,oBAAWD,GAAX,aAA0BE,GAA1B,aAA0CC,GAA1C,aAA8DC,GAA9D,aAAgFC,GAAhF,aAA2GC,GAA3G,aAAsIC,IAO/HC,GAAapN,KAAI,SAAAhC,GAAC,OACjB,wBAAI1F,IAAK8L,eAAU7E,UAAU,QAC1BvB,SAOPmL,EAAc,kBAClB1N,EAAKqB,YAAYJ,QACf,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAM,cAAcnE,UAAU,kBACvC,wBAAIA,UAAU,qBACX2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,WAAY,IAAIsE,KAAI,SAAAhC,GAAC,OACpD,wBAAI1F,IAAK4I,OAAMlD,EAAE,MAAO,IAAKuB,UAAU,QACpC2B,OAAMlD,EAAG,uBAAwB,WAOxC6L,EAAgB,SAAA7L,GAAC,OACrB,yBAAK1F,IAAK4I,OAAMlD,EAAG,MAAO,QAASuB,UAAU,iBAC3C,wBAAIA,UAAU,yBAAyB2B,OAAMlD,EAAG,wCAAyC,IAAzF,IAA+FkD,OAAMlD,EAAG,yCAA0C,KAClJ,0BAAMuB,UAAU,WAAW2B,OAAMlD,EAAG,uCAAwC,KAC5E,0BAAMuB,UAAU,WAAW2B,OAAMlD,EAAG,wCAAyC,KAC7E,0BAAMuB,UAAU,WAAW2B,OAAMlD,EAAG,oCAAqC,KACzE,kBAAC,KAAD,CAAeuB,UAAU,eAAe2I,OAAQhH,OAAMlD,EAAG,2CAA4C,QAInG+L,EAAa,kBACjBtO,EAAKlK,YACLkK,EAAKlK,WAAWmL,QACd,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKlK,WAAWuK,UAChC,yBAAKyD,UAAU,wCACZ2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,uBAAwB,IAAIsJ,QAAO,SAAAhH,GAAC,MAAiD,cAA7CkD,OAAMlD,EAAG,4BAA6B,OAAoBgC,IAAI6J,MAKzIN,EAAe,SAAAvL,GAAC,OACpB,yBAAK1F,IAAK4I,OAAMlD,EAAG,MAAO,IAAKuB,UAAU,sCACvC,wBAAIA,UAAU,iCAAiC2B,OAAMlD,EAAG,OAAQ,KAChE,yBAAKuB,UAAU,QACZvB,EAAEwL,OAAS,yBAAKjK,UAAU,0BAA0BvB,EAAEwL,OACzC,IAAbxL,EAAEyL,QACD,yBAAKlK,UAAU,QACZyB,MAAM0I,KAAK1I,MAAMhD,EAAEyL,SAASzJ,KAAI,SAACkB,EAAGyI,GAAJ,OAC/B,uBAAGrR,IAAKqR,EAAGpK,UAAU,yBAAyBK,MAAO,CAAEiI,MAAO9K,EAAMrL,OAAO2L,SAA3E,eAUNuM,EAAY,kBAChBnO,EAAKnK,WACLmK,EAAKnK,UAAUoL,QAAWwE,OAAMzF,EAAM,oCAAoC,IAAItE,OAAS,GACrF,yBAAKoI,UAAU,sBACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKnK,UAAUwK,UAC/B,6BAAMoF,OAAMzF,EAAM,oCAAqC,IAAIuJ,QAAO,SAAAhH,GAAC,MAA6B,KAAzBkD,OAAMlD,EAAG,OAAQ,OAAYgC,IAAIuJ,MAIxGX,EAAY,SAAA5K,GAAC,OACjB,yBAAK1F,IAAK4I,OAAMlD,EAAE,MAAO,QAASuB,UAAU,QAC1C,wBAAIA,UAAU,iBAAiB2B,OAAMlD,EAAG,cAAe,KACvD,uBAAGuB,UAAU,WAAW2B,OAAMlD,EAAG,oBAAqB,KACtD,kBAAC,KAAD,CAAeuB,UAAU,eAAe2I,OAAQhH,OAAMlD,EAAG,cAAe,QAItE6K,EAAS,kBACbpN,EAAKrK,QACLqK,EAAKrK,OAAOsL,QACV,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAS6C,OAAK,EAACnH,MAAOjI,EAAKrK,OAAO0K,UACjCoF,OAAMzF,EAAKC,OAAO,UAAU,GAAI,QAAS,IAAIsJ,QAAO,SAAAhH,GAAC,MAAuB,KAAnBA,EAAE,kBAAqBgC,IAAI4I,KAIrFE,EAAoB,SAAA9K,GAAC,OACzB,yBAAK1F,IAAK4I,OAAMlD,EAAE,MAAM,QAASuB,UAAU,QACzC,wBAAIA,UAAU,iBAAiB2B,OAAMlD,EAAG,mBAAoB,KAC5D,uBAAGuB,UAAU,WAAW2B,OAAMlD,EAAG,qCAAsC,KACvE,kBAAC,KAAD,CAAeuB,UAAU,eAAe2I,OAAQhH,OAAMlD,EAAG,WAAY,QAInE+K,EAAiB,kBACrBtN,EAAKpK,gBACLoK,EAAKpK,eAAeqL,QAClB,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKpK,eAAeyK,QAASyD,UAAU,kBACtD2B,OAAMzF,EAAM,oCAAqC,IAAIuJ,QAAO,SAAAhH,GAAC,OAAMkD,OAAMlD,EAAG,MAAO,IAAI2O,SAAS,YAAmD,WAArCzL,OAAMlD,EAAG,qBAAsB,OAAiBgC,IAAI8I,KAInKkB,EAAY,SAAAhM,GAAC,OACjB,yBAAK1F,IAAK4I,OAAMlD,EAAG,MAAO,QAASuB,UAAU,QAC3C,wBAAIA,UAAU,qBAAqB2B,OAAMlD,EAAG,aAAc,KAC1D,yBAAKuB,UAAU,WAAW2B,OAAMlD,EAAG,QAAS,OAI1CiM,EAAS,kBACbxO,EAAKjK,QACLiK,EAAKjK,OAAOkL,QACV,yBAAK6C,UAAU,QACb,kBAACyI,EAAD,CAAStE,MAAOjI,EAAKjK,OAAOsK,QAASyD,UAAU,kBAC9C2B,OAAMzF,EAAKC,OAAO,UAAU,GAAI,aAAc,IAAIsJ,QAAO,SAAAhH,GAAC,MAAmB,KAAfA,EAAC,SAAkBgC,IAAIgK,KAI5F,OACE,yBACEpK,MAAO,CACLsK,WAAYnN,EAAME,KAAKC,OACvB+L,gBAAiBlM,EAAMrL,OAAOyL,WAC9B0K,MAAO9K,EAAMrL,OAAO0L,UAGtB,yBAAKmC,UAAU,qBACb,yBACEA,UAAU,gDACVK,MAAO,CAAEqJ,gBAAgB,QAAD,OAAUpR,EAAV,aAAgBC,EAAhB,aAAsBP,EAAtB,YAExB,kBAACkQ,EAAD,MACA,kBAAC6E,EAAD,MACA,kBAAChD,EAAD,MACA,kBAACH,EAAD,MACA,kBAACS,EAAD,MACA,kBAACb,EAAD,MACA,kBAACkB,EAAD,OAEF,yBAAK1K,UAAU,cACb,kBAAC8K,EAAD,MAEA,6BAAS9K,UAAU,OAAOK,MAAOwL,GAAO/M,SACtC,kBAAC4J,EAAD,MACA,kBAACM,EAAD,MACA,kBAACI,EAAD,MACA,kBAACE,EAAD,MACA,kBAACkB,EAAD,W,UCpZG,IACb,CACEzR,IAAK,OACLlE,KAAM,OACNiZ,UAAW7F,GACX8F,QAASC,IAEX,CACEjV,IAAK,UACLlE,KAAM,UACNiZ,UAAWjD,GACXkD,QAASE,IAEX,CACElV,IAAK,SACLlE,KAAM,SACNiZ,UAAW7C,GACX8C,QAASG,IAEX,CACEnV,IAAK,WACLlE,KAAM,WACNiZ,UAAW3C,GACX4C,QAASI,IAEX,CACEpV,IAAK,SACLlE,KAAM,SACNiZ,UAAWrC,GACXsC,QAASK,IAEX,CACErV,IAAK,SACLlE,KAAM,SACNiZ,UCrCWvB,GDsCXwB,Q,QCvCiB7T,ICsBNmU,GArBM,SAAC,GAAyB,IAAvB7Q,EAAsB,EAAtBA,MAAO0C,EAAe,EAAfA,SAC7B,OACE,yBAAKF,UAAU,0BACZ9N,GAAUuO,KAAI,SAAAhC,GAAC,OACd,yBAAK1F,IAAK0F,EAAE1F,IAAKiH,UAAU,cAAcgB,QAAS,kBAAMd,EAAS,eAAgBzB,EAAE1F,OACjF,yBACEiH,UAAS,4EACPxC,EAAMC,OAAO6Q,gBAAkB7P,EAAE1F,IAC7B,wCACA,sBAHG,yCAKToP,IAAK1J,EAAEsP,QACP3F,IAAK3J,EAAE5J,OAET,uBAAGmL,UAAU,4BAA4BvB,EAAE5J,YCX/C0Z,GAAe,CACnB,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAiEaC,GA9DG,SAAC,GAAyB,IAAvBhR,EAAsB,EAAtBA,MAAO0C,EAAe,EAAfA,SAClB0C,EAAMC,aAAe,gBAArBD,EAEF6L,EAAuB,SAAAnG,ItCVP,SAAAoG,GACtB,IAAMC,EAAWC,SAASC,cAAc,YACxCF,EAAStO,MAAMkK,SAAW,QAC1BoE,EAAStO,MAAMsL,IAAM,EACrBgD,EAAStO,MAAM0L,KAAO,EACtB4C,EAAStO,MAAM5F,MAAQ,MACvBkU,EAAStO,MAAM3F,OAAS,MACxBiU,EAAStO,MAAMyO,QAAU,EACzBH,EAAStO,MAAM0O,OAAS,OACxBJ,EAAStO,MAAM2O,QAAU,OACzBL,EAAStO,MAAM4O,UAAY,OAC3BN,EAAStO,MAAMzC,WAAa,cAC5B+Q,EAAS3V,MAAQ0V,EACjBE,SAASxR,KAAK8R,YAAYP,GAC1BA,EAASQ,QACTR,EAASS,SACT,IAAMC,EAAaT,SAASU,YAAY,QACxCV,SAASxR,KAAKmS,YAAYZ,GsCNxBa,CAAgBlH,GAChBmH,YAAM7M,EAAE,6BAA8B,CAAE0F,UAAU,CAChDoH,cAAe,mCAEjBxP,EAAS,sBAAuBoI,IAGlC,OACE,6BACE,yBAAKtI,UAAU,oEACZ4C,EAAE,wBAEL,yBAAK5C,UAAU,6CACZuO,GAAa9N,KAAI,SAAA6H,GAAK,OACrB,yBACEvP,IAAKuP,EACLtI,UAAU,8EACVK,MAAO,CAAEqJ,gBAAiBpB,GAC1BtH,QAAS,kBAAMyN,EAAqBnG,UAK1C,wBAAItI,UAAU,SAEd,yBAAKA,UAAU,mCACb,yBACEA,UAAU,qCACVK,MAAO,CAAEqJ,gBAAiBlM,EAAMrL,OAAO0L,WAEzC,yBAAKmC,UAAU,cACb,kBAAC,GAAD,CACEC,MAAO2C,EAAE,uBACT3B,YAAY,UACZjI,MAAOwE,EAAMrL,OAAO0L,QACpBqC,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,uBAAwBgB,QAKtD,yBAAKlB,UAAU,mCACb,yBACEA,UAAU,qCACVK,MAAO,CAAEqJ,gBAAiBlM,EAAMrL,OAAO2L,UAEzC,yBAAKkC,UAAU,cACb,kBAAC,GAAD,CACEC,MAAO2C,EAAE,sBACT3B,YAAY,UACZjI,MAAOwE,EAAMrL,OAAO2L,OACpBoC,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,sBAAuBgB,UCjFrDyO,GAAc,CAClB,OACA,aACA,SACA,YACA,UACA,QACA,kBACA,gBACA,UAoCaC,GAjCE,SAAC,GAAyB,IAAvBpS,EAAsB,EAAtBA,MAAO0C,EAAe,EAAfA,SACjB0C,EAAMC,aAAe,gBAArBD,EAER,OACE,yBAAK5C,UAAU,0BACZ2P,GAAYlP,KAAI,SAAAhC,GAAC,OAChB,yBACE1F,IAAK0F,EACL4B,MAAO,CAAEsK,WAAYlM,GACrBuC,QAAS,kBAAMd,EAAS,oBAAqBzB,IAC7CuB,UAAS,gEACPxC,EAAME,KAAKC,SAAWc,EAAI,kBAAoB,qBADvC,0CAIRA,MAIL,6BACE,kBAAC,GAAD,CACEuB,UAAU,OACVC,MAAO2C,EAAE,0BACT3B,YAAY,cACZjI,MAAOwE,EAAME,KAAKC,OAClBuC,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,oBAAqBgB,MAG/C,uBAAGlB,UAAU,yBAAyB4C,EAAE,iC,oBCyGjCiN,GAvII,SAAC,GAA+B,IAA7B3T,EAA4B,EAA5BA,KAAatD,GAAe,EAAtB4E,MAAsB,EAAf5E,UAEzBkH,EADYsF,qBAAW7F,IACvBO,mBACA8C,EAAMC,aAAe,gBAArBD,EACFkN,EAAeC,iBAAO,MA4B5B,OACE,6BACE,yBAAK/P,UAAU,kCAAkC4C,EAAE,uBAEnD,wBAAI5C,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0B4C,EAAE,iCAE1C,uBAAG5C,UAAU,WAAW4C,EAAE,8BAE1B,2BACEoN,IAAKF,EACLjX,KAAK,OACLmH,UAAU,SACVE,SAAU,SAACK,GAAD,OxCmCD,SAAC+B,EAAO1J,GACzB,IAAMvF,EAAK,IAAI4c,WACf5c,EAAG6c,iBAAiB,QAAQ,WAC1B,IAAMC,EAAiBjS,KAAKC,MAAM9K,EAAGmF,QACrCI,EAAS,CAAEC,KAAM,cAAeI,QAASkX,IACzCvX,EAAS,CAAEC,KAAM,iBAEnBxF,EAAG+c,WAAW9N,EAAM9B,OAAO6P,MAAM,IwC1CRC,CAAW/P,EAAG3H,MAEjC,uBAAGlB,GAAG,iBAAiBsI,UAAU,WAEjC,yBAAKA,UAAU,mCACb,4BACEnH,KAAK,SACLmI,QAAS,kBAAM8O,EAAarW,QAAQ8W,SACpCvQ,UAAU,kFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,WACA,0BAAMA,UAAU,WAAW4C,EAAE,0CAIjC,4BACE/J,KAAK,SACLmI,QA3Da,WACrB,IAAIwP,EAAY7O,aAAYzF,EAAKC,QAC7BsU,EAAmB,sCAAsCvS,KAAKE,UAAUoS,GAAW,aACvF7O,OAAM6O,EAAU,UAAU,GAAI,WAAY,sBAC1C,IAEIE,EAAaD,GAFM,sCAAsCvS,KAAKE,UAAUoS,EAAU,UAAU,IAAI,cAGhGG,EAAM,IAAIC,GACdD,EAAIE,KAAK,YAAaH,GACtBC,EAAIE,KAAK,cAAe3S,KAAKE,UAAUlC,IACvCyU,EAAIG,cAAc,CAACjY,KAAK,SACvBxC,MAAK,SAAS0a,GACXC,kBAAOD,EAAS,wBAgDZ/Q,UAAU,kFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,WACA,0BAAMA,UAAU,WAAW4C,EAAE,4CAMrC,wBAAI5C,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0B4C,EAAE,mCAC1C,yBAAK5C,UAAU,WAAW4C,EAAE,gCAE5B,4BACE/J,KAAK,SACLmI,QAAS,kBAAMlB,GAAmB,IAClCE,UAAU,uFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,QACA,0BAAMA,UAAU,WAAW4C,EAAE,gDAKnC,wBAAI5C,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0B4C,EAAE,iCAE1C,yBAAK5C,UAAU,WAAW4C,EAAE,8BAE5B,4BACE/J,KAAK,SACLmI,QAjFa,WACnBpI,EAAS,CAAEC,KAAM,mBACjBD,EAAS,CAAEC,KAAM,eAgFXmH,UAAU,yFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,kBACA,0BAAMA,UAAU,WAAW4C,EAAE,6CAKnC,wBAAI5C,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0B4C,EAAE,0BAE1C,yBAAK5C,UAAU,WAAW4C,EAAE,uBAE5B,4BACE/J,KAAK,SACLmI,QA/FgB,WACtBpI,EAAS,CAAEC,KAAM,UACjBD,EAAS,CAAEC,KAAM,eA8FXmH,UAAU,qFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,WACA,0BAAMA,UAAU,WAAW4C,EAAE,qCCrB1BqO,GArHE,WAAO,IACdrO,EAAMC,aAAe,gBAArBD,EAER,OACE,6BACE,yBAAK5C,UAAU,0BACb,wBAAIA,UAAU,0BAA0B4C,EAAE,gCAE1C,yBAAK5C,UAAU,WAAW4C,EAAE,6BAE5B,uBACEpC,OAAO,SACPqF,IAAI,sBACJC,KAAK,iCACL9F,UAAU,wHAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,eACA,0BAAMA,UAAU,WAAW4C,EAAE,iDAKnC,wBAAI5C,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0B4C,EAAE,sCAE1C,yBAAK5C,UAAU,WAAW4C,EAAE,mCAE5B,yBAAK5C,UAAU,oBACb,uBACEQ,OAAO,SACPqF,IAAI,sBACJC,KAAK,6DACL9F,UAAU,qFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,cACA,0BAAMA,UAAU,WAAW4C,EAAE,mDAIjC,uBACEpC,OAAO,SACPqF,IAAI,sBACJC,KAAK,0FACL9F,UAAU,uFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,SACA,0BAAMA,UAAU,WAAW4C,EAAE,oDAMrC,wBAAI5C,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0B4C,EAAE,6BAE1C,yBAAK5C,UAAU,WAAW4C,EAAE,0BAE5B,uBACEpC,OAAO,SACPqF,IAAI,sBACJC,KAAK,kDACL9F,UAAU,0HAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,QACA,0BAAMA,UAAU,WAAW4C,EAAE,2CAKnC,wBAAI5C,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0B4C,EAAE,0BAE1C,yBAAK5C,UAAU,WAAW4C,EAAE,uBAE5B,uBACEpC,OAAO,SACPqF,IAAI,sBACJC,KAAK,sEACL9F,UAAU,wHAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,SACA,0BAAMA,UAAU,WAAW4C,EAAE,wCAKnC,yBAAK5C,UAAU,QACb,uBAAGA,UAAU,qCACX,kBAAC2F,GAAA,EAAD,CAAO/C,EAAGA,EAAGgD,QAAQ,uBAArB,oBAEE,uBACE5F,UAAU,4BACV8F,KAAK,gCACLD,IAAI,sBACJrF,OAAO,UAJT,mBAUJ,uBAAGR,UAAU,qCAAqC4C,EAAE,2BCzE7CsO,GAnCK,SAAC,GAA4B,IAA1B5e,EAAyB,EAAzBA,SAAU4N,EAAe,EAAfA,SACvB0C,EAAMC,aAAe,gBAArBD,EAER,OACE,6BACE,kBAAC,GAAD,CACE3C,MAAO2C,EAAE,2BACT5J,MAAO1G,EAASyL,SAChBmC,SAAU,SAAAzB,GAAC,OAAIyB,EAAS,oBAAqBzB,IAC7C0B,QAASpO,GACTqO,WAAY,SAAA3B,GAAC,OACX,4BAAQ1F,IAAK0F,EAAE7J,KAAMoE,MAAOyF,EAAE7J,MAC3B6J,EAAE5J,SAKT,uBAAGmL,UAAU,yBACX,kBAAC2F,GAAA,EAAD,CAAO/C,EAAGA,EAAGgD,QAAQ,8BAArB,uFAEE,uBACE5F,UAAU,gCACVQ,OAAO,SACPqF,IAAI,sBACJC,KAAK,uCAJP,6BAFF,QCiEOqL,GA7EM,WAAO,IAClBvO,EAAMC,aAAe,gBAArBD,EAEFkF,EAAU1C,qBAAWpG,IACnBrI,EAAoBmR,EAApBnR,MAAOiC,EAAakP,EAAblP,SACPsD,EAA0BvF,EAA1BuF,KAAMsB,EAAoB7G,EAApB6G,MAAOlL,EAAaqE,EAAbrE,SAEfqO,EAAO,CACX,CACE5H,IAAK,YACLlE,KAAM+N,EAAE,oBAEV,CACE7J,IAAK,SACLlE,KAAM+N,EAAE,iBAEV,CACE7J,IAAK,QACLlE,KAAM+N,EAAE,gBAEV,CACE7J,IAAK,UACLlE,KAAM+N,EAAE,kBAEV,CACE7J,IAAK,WACLlE,KAAM+N,EAAE,mBAEV,CACE7J,IAAK,QACLlE,KAAM+N,EAAE,iBA9Ba,EAiCWlD,mBAASiB,EAAK,GAAG5H,KAjC5B,oBAiClB6H,EAjCkB,KAiCNC,EAjCM,KAmCnBX,EAAW,SAACnH,EAAKC,GACrBJ,EAAS,CACPC,KAAM,WACNI,QAAS,CACPF,MACAC,WAIJJ,EAAS,CAAEC,KAAM,eAsBnB,OACE,yBACEnB,GAAG,eACHsI,UAAU,6FAEV,kBAAC,GAAD,CAAQW,KAAMA,EAAMC,WAAYA,EAAYC,cAAeA,IAC3D,yBAAKb,UAAU,QAzBA,WACjB,OAAQY,GACN,KAAKD,EAAK,GAAG5H,IACX,OAAO,kBAACqY,GAAD,CAAc5T,MAAOA,EAAO0C,SAAUA,IAC/C,KAAKS,EAAK,GAAG5H,IACX,OAAO,kBAACsY,GAAD,CAAW7T,MAAOA,EAAO0C,SAAUA,IAC5C,KAAKS,EAAK,GAAG5H,IACX,OAAO,kBAACuY,GAAD,CAAU9T,MAAOA,EAAO0C,SAAUA,IAC3C,KAAKS,EAAK,GAAG5H,IACX,OAAO,kBAACwY,GAAD,CAAYrV,KAAMA,EAAMsB,MAAOA,EAAO5E,SAAUA,IACzD,KAAK+H,EAAK,GAAG5H,IACX,OAAO,kBAACyY,GAAD,CAAalf,SAAUA,EAAU4N,SAAUA,IACpD,KAAKS,EAAK,GAAG5H,IACX,OAAO,kBAAC0Y,GAAD,MACT,QACE,OAAO,MAUczJ,MCvBd0J,GAzDQ,WACrB,IAAMC,EAAcvM,qBAAW7F,IACvBlG,EAAmCsY,EAAnCtY,WAAYyG,EAAuB6R,EAAvB7R,mBASpB,OACE,yBACEpI,GAAG,iBACHsI,UAAU,0EAEV,yBAAKA,UAAU,wHACb,yBAAKA,UAAU,4CAA4CgB,QAblD,kBAAM3H,EAAWI,QAAQmY,OAAO,KAcvC,uBAAG5R,UAAU,kBAAb,YAGF,yBAAKA,UAAU,4CAA4CgB,QAhBjD,kBAAM3H,EAAWI,QAAQoY,QAAQ,KAiBzC,uBAAG7R,UAAU,kBAAb,aAGF,yBAAKA,UAAU,4CAA4CgB,QAnB7C,WAClB3H,EAAWI,QAAQC,WAAW,GAC9BL,EAAWI,QAAQE,MAAM,KAkBnB,uBAAGqG,UAAU,kBAAb,wBAGF,yBAAKA,UAAU,qBAAf,KAEA,yBAAKA,UAAU,4CAA4CgB,QAAS,kBAAMrL,OAAOmc,UAC/E,uBAAG9R,UAAU,kBAAb,UAGF,yBACEA,UAAU,4CACVgB,QAAS,kBAAMlB,GAAmB,KAElC,uBAAGE,UAAU,kBAAb,SAGF,yBAAKA,UAAU,qBAAf,KAEA,uBACEA,UAAU,4CACV8F,KAAK,gCACLtF,OAAO,SACPqF,IAAI,uBAEJ,uBAAG7F,UAAU,kBAAb,oB,8BCmDK+R,GAlGK,WAAO,IACjBnP,EAAMC,eAAND,EACF+O,EAAcvM,qBAAW7F,IACvBnG,EAA+DuY,EAA/DvY,QAASC,EAAsDsY,EAAtDtY,WAAYwG,EAA0C8R,EAA1C9R,kBAAmBC,EAAuB6R,EAAvB7R,mBAE1CkS,EAAa,CACjB,CAAEjZ,IAAK,gBAAiBC,MAAM,GAAD,OAAK4J,EAAE,+CACpC,CAAE7J,IAAK,UAAWC,MAAM,GAAD,OAAK4J,EAAE,yCAC9B,CAAE7J,IAAK,cAAeC,MAAM,GAAD,OAAK4J,EAAE,8CARZ,EAWMlD,mBAAS,IAXf,oBAWjBpG,EAXiB,KAWR2Y,EAXQ,OAYAvS,mBAASsS,EAAW,GAAGjZ,KAZvB,oBAYjBF,EAZiB,KAYXqZ,EAZW,KAcxB,OACE,yBACElS,UAAS,mEACPH,EAAoB,mBAAqB,iBAE3CQ,MAAO,CAAEqJ,gBAAiB,uBAC1B1I,QAAS,WACPlB,GAAmB,KAGrB,yBACEE,UAAU,iEACVgB,QAAS,SAAAT,GACPA,EAAE4R,kBACF5R,EAAE6R,mBAGJ,wBAAIpS,UAAU,0BAA0B4C,EAAE,wBAE1C,wBAAI5C,UAAU,4BAA4B4C,EAAE,8BAC5C,yBAAK5C,UAAU,qBACb,2BACEnH,KAAK,QACLmH,UAAU,8FACVhH,MAAOM,EACP4G,SAAU,SAAAK,GAAC,OAAI0R,EAAW1R,EAAEC,OAAOxH,QACnCqZ,IAAI,KACJC,IAAI,MACJC,KAAK,MAGP,wBAAIvS,UAAU,oBAAoB1G,EAAlC,MAGF,wBAAI0G,UAAU,iCAAiC4C,EAAE,gCACjD,kBAAC,GAAD,CACE5J,MAAOH,EACPsH,QAAS6R,EACT9R,SAAUgS,EACV9R,WAAY,SAAA3B,GAAC,OACX,4BAAQ1F,IAAK0F,EAAE1F,IAAKC,MAAOyF,EAAE1F,KAC1B0F,EAAEzF,UAKT,uBAAGgH,UAAU,8BAA8B4C,EAAE,2BAC7C,uBAAG5C,UAAU,8BAA8B4C,EAAE,2BAE7C,yBAAK5C,UAAU,wBACb,4BACEnH,KAAK,SACLmI,QAAS,WACPlB,GAAmB,IAErBE,UAAU,mHAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,SACA,0BAAMA,UAAU,WAAW4C,EAAE,iCAIjC,4BACE/J,KAAK,SACLmI,QAAO,wBAAE,uBAAAjJ,EAAA,sEACS,gBAATc,EACHgD,GAAmBzC,EAASC,EAAYC,GACxCH,GAAUC,EAASC,EAAYC,EAAST,GAHrC,OAIPiH,GAAmB,GAJZ,2CAMTE,UAAU,sHAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,QACA,0BAAMA,UAAU,WAAW4C,EAAE,wC,qBClE5B4P,GAzBU,WAAO,IACtB5P,EAAMC,eAAND,EADqB,EAEmBlD,oBAAS,GAF5B,oBAEtB+S,EAFsB,KAEJC,EAFI,KAS7B,OALAhL,qBAAU,WACR9N,YAAW,kBAAM8Y,GAAoB,KAAO,KAC5C9Y,YAAW,kBAAM8Y,GAAoB,KAAQ,OAC5C,IAGD,yBACE1S,UAAS,yFACPyS,EAAmB,mBAAqB,kBAG1C,yBAAKzS,UAAU,wCACb,2BAAOmI,IAAKwK,KAAWC,UAAQ,EAACC,OAAK,EAACC,MAAI,IAC1C,uBAAG9S,UAAU,2DACV4C,EAAE,iCCkDEmQ,GAzDH,WACV,IAAM3Z,EAAU2W,iBAAO,MACjB1W,EAAa0W,iBAAO,MAClBjb,EAAS+N,eAAT/N,KAEFgT,EAAU1C,qBAAWpG,IACnBrI,EAAoBmR,EAApBnR,MAAOiC,EAAakP,EAAblP,SACP4E,EAAoB7G,EAApB6G,MAAOlL,EAAaqE,EAAbrE,SAETqf,EAAcvM,qBAAW7F,IACvBI,EAA8BgS,EAA9BhS,WAAYC,EAAkB+R,EAAlB/R,cAUpB,OARA8H,qBAAU,WACR/H,EAAWvG,GACXwG,EAAcvG,GACdvE,EAAKke,eAAe1gB,EAASyL,UAC7B,IAAMkV,EAAc/U,KAAKC,MAAMO,aAAawU,QAAQ,UACpDta,EAAS,CAAEC,KAAM,cAAeI,QAASga,MACxC,CAACra,EAAU+G,EAAYC,EAAe9K,EAAMxC,EAASyL,WAGtD,kBAAC,WAAD,CAAUoV,SAAS,cACjB,yBAAKnT,UAAU,0CACb,kBAAC,GAAD,MAEA,yBAAKA,UAAU,sFACb,kBAAC,WAAD,CACEgQ,IAAK3W,EACL+Z,QAAQ,MACR1Z,YAAU,EACV2Z,oBAAqB,GACrBC,mBAAiB,EACjBC,sBAAuB,GACvBC,wBAAyB,GACzBnT,MAAO,CAAE2O,QAAS,SAElB,yBAAKtX,GAAG,OAAOsY,IAAK5W,EAAS4G,UAAU,0BACpC9N,GAAUuhB,MAAK,SAAAhV,GAAC,OAAIjB,EAAMC,OAAO6Q,gBAAkB7P,EAAE1F,OAAK+U,cAI/D,kBAAC,GAAD,OAGF,yBAAKpW,GAAG,YAAYsI,UAAU,eAC3B9N,GAAUuhB,MAAK,SAAAhV,GAAC,OAAIjB,EAAMC,OAAO6Q,gBAAkB7P,EAAE1F,OAAK+U,aAG7D,kBAAC,GAAD,MAEA,kBAAC,GAAD,MACA,kBAAC,GAAD,SCrDR2B,IAAMiE,UAAU,CACdC,UAAW,IACXC,aAAa,EACbC,iBAAiB,EACjBtJ,SAAUkF,IAAMqE,SAASC,eAG3BC,IAASC,OACP,kBAAC,IAAMC,WAAP,KACE,kBAAC/U,GAAD,KACE,kBAACM,GAAD,KACE,kBAAC,GAAD,SAINmP,SAASuF,eAAe,SjDPnB,SAAkBle,GACvB,GAA6C,kBAAmBC,UAAW,CAGzE,GADkB,IAAIke,IAAIC,IAAwB1e,OAAOC,SAASkQ,MACpDwO,SAAW3e,OAAOC,SAAS0e,OAIvC,OAGF3e,OAAOua,iBAAiB,QAAQ,WAC9B,IAAMla,EAAK,UAAMqe,IAAN,sBAEP5e,KAgEV,SAAiCO,EAAOC,GAEtCse,MAAMve,EAAO,CACXwe,QAAS,CAAE,iBAAkB,YAE5Bne,MAAK,SAAAoe,GAEJ,IAAMC,EAAcD,EAASD,QAAQlW,IAAI,gBAEnB,MAApBmW,EAASE,QACO,MAAfD,IAA8D,IAAvCA,EAAYE,QAAQ,cAG5C1e,UAAUC,cAAc0e,MAAMxe,MAAK,SAAAC,GACjCA,EAAawe,aAAaze,MAAK,WAC7BV,OAAOC,SAASmf,eAKpBhf,GAAgBC,EAAOC,MAG1BgB,OAAM,WACLJ,QAAQC,IAAI,oEAtFVke,CAAwBhf,EAAOC,GAI/BC,UAAUC,cAAc0e,MAAMxe,MAAK,WACjCQ,QAAQC,IACN,iHAMJf,GAAgBC,EAAOC,OiDlB/BE,M", "file": "static/js/main.aaf9e78c.chunk.js", "sourcesContent": ["module.exports = __webpack_public_path__ + \"static/media/preview.a5fc2f27.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.f1f46a82.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.27f7a093.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.115df124.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.93d6587b.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.3186944c.png\";", "module.exports = __webpack_public_path__ + \"static/media/panzoom.e912bae3.mp4\";", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import af from './af';\r\nimport ar from './ar';\r\nimport as from './as';\r\nimport ca from './ca';\r\nimport cs from './cs';\r\nimport da from './da';\r\nimport de from './de';\r\nimport el from './el';\r\nimport en from './en';\r\nimport es from './es';\r\nimport fi from './fi';\r\nimport fr from './fr';\r\nimport he from './he';\r\nimport hi from './hi';\r\nimport hu from './hu';\r\nimport it from './it';\r\nimport ja from './ja';\r\nimport kn from './kn';\r\nimport ko from './ko';\r\nimport ml from './ml';\r\nimport mr from './mr';\r\nimport nl from './nl';\r\nimport no from './no';\r\nimport pa from './pa';\r\nimport pl from './pl';\r\nimport pt from './pt';\r\nimport ro from './ro';\r\nimport ru from './ru';\r\nimport sv from './sv';\r\nimport ta from './ta';\r\nimport tr from './tr';\r\nimport uk from './uk';\r\nimport vi from './vi';\r\nimport zh from './zh';\r\n\r\nexport default {\r\n  af,\r\n  ar,\r\n  as,\r\n  ca,\r\n  cs,\r\n  da,\r\n  de,\r\n  el,\r\n  en,\r\n  es,\r\n  fi,\r\n  fr,\r\n  he,\r\n  hi,\r\n  hu,\r\n  it,\r\n  ja,\r\n  kn,\r\n  ko,\r\n  ml,\r\n  mr,\r\n  nl,\r\n  no,\r\n  pa,\r\n  pl,\r\n  pt,\r\n  ro,\r\n  ru,\r\n  sv,\r\n  ta,\r\n  tr,\r\n  uk,\r\n  vi,\r\n  zh,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import i18n from 'i18next';\r\nimport backend from 'i18next-http-backend';\r\nimport { initReactI18next } from 'react-i18next';\r\nimport detector from 'i18next-browser-languagedetector';\r\n\r\nimport resources from './locales';\r\n\r\nconst languages = [\r\n  {\r\n    code: 'ar',\r\n    name: 'Arabic (عربى)',\r\n  },\r\n  {\r\n    code: 'zh',\r\n    name: 'Chinese (中文)',\r\n  },\r\n  {\r\n    code: 'da',\r\n    name: 'Danish (Dansk)',\r\n  },\r\n  {\r\n    code: 'nl',\r\n    name: 'Dutch (Nederlands)',\r\n  },\r\n  {\r\n    code: 'en',\r\n    name: 'English (US)',\r\n  },\r\n  {\r\n    code: 'fr',\r\n    name: 'French (Français)',\r\n  },\r\n  {\r\n    code: 'de',\r\n    name: 'German (Deutsche)',\r\n  },\r\n  {\r\n    code: 'he',\r\n    name: 'Hebrew (עברית)',\r\n  },\r\n  {\r\n    code: 'hi',\r\n    name: 'Hindi (हिन्दी)',\r\n  },\r\n  {\r\n    code: 'it',\r\n    name: 'Italian (Italiano)',\r\n  },\r\n  {\r\n    code: 'kn',\r\n    name: 'Kannada (ಕನ್ನಡ)',\r\n  },\r\n  {\r\n    code: 'pl',\r\n    name: 'Polish (Polskie)',\r\n  },\r\n  {\r\n    code: 'pt',\r\n    name: 'Portuguese (Português)',\r\n  },\r\n  {\r\n    code: 'ru',\r\n    name: 'Russian (русский)',\r\n  },\r\n  {\r\n    code: 'es',\r\n    name: 'Spanish (Español)',\r\n  },\r\n  {\r\n    code: 'ta',\r\n    name: 'Tamil (தமிழ்)',\r\n  },\r\n  {\r\n    code: 'vi',\r\n    name: 'Vietnamese (Tiếng Việt)',\r\n  },\r\n];\r\n\r\ni18n\r\n  .use(detector)\r\n  .use(backend)\r\n  .use(initReactI18next)\r\n  .init({\r\n    resources,\r\n    lng: 'en',\r\n    fallbackLng: 'en',\r\n    ns: ['app', 'leftSidebar', 'rightSidebar'],\r\n    defaultNS: 'app',\r\n  });\r\n\r\nexport { languages };\r\n\r\nexport default i18n;\r\n", "/* eslint-disable no-console */\r\n/* eslint-disable no-use-before-define */\r\n// This optional code is used to register a service worker.\r\n// register() is not called by default.\r\n\r\n// This lets the app load faster on subsequent visits in production, and gives\r\n// it offline capabilities. However, it also means that developers (and users)\r\n// will only see deployed updates on subsequent visits to a page, after all the\r\n// existing tabs open on the page have been closed, since previously cached\r\n// resources are updated in the background.\r\n\r\n// To learn more about the benefits of this model and instructions on how to\r\n// opt-in, read https://bit.ly/CRA-PWA\r\n\r\nconst isLocalhost = Boolean(\r\n  window.location.hostname === 'localhost' ||\r\n    // [::1] is the IPv6 localhost address.\r\n    window.location.hostname === '[::1]' ||\r\n    // *********/8 are considered localhost for IPv4.\r\n    window.location.hostname.match(/^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/),\r\n);\r\n\r\nexport function register(config) {\r\n  if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {\r\n    // The URL constructor is available in all browsers that support SW.\r\n    const publicUrl = new URL(process.env.PUBLIC_URL, window.location.href);\r\n    if (publicUrl.origin !== window.location.origin) {\r\n      // Our service worker won't work if PUBLIC_URL is on a different origin\r\n      // from what our page is served on. This might happen if a CDN is used to\r\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\r\n      return;\r\n    }\r\n\r\n    window.addEventListener('load', () => {\r\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\r\n\r\n      if (isLocalhost) {\r\n        // This is running on localhost. Let's check if a service worker still exists or not.\r\n        checkValidServiceWorker(swUrl, config);\r\n\r\n        // Add some additional logging to localhost, pointing developers to the\r\n        // service worker/PWA documentation.\r\n        navigator.serviceWorker.ready.then(() => {\r\n          console.log(\r\n            'This web app is being served cache-first by a service ' +\r\n              'worker. To learn more, visit https://bit.ly/CRA-PWA',\r\n          );\r\n        });\r\n      } else {\r\n        // Is not localhost. Just register service worker\r\n        registerValidSW(swUrl, config);\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nfunction registerValidSW(swUrl, config) {\r\n  navigator.serviceWorker\r\n    .register(swUrl)\r\n    .then(registration => {\r\n      registration.onupdatefound = () => {\r\n        const installingWorker = registration.installing;\r\n        if (installingWorker == null) {\r\n          return;\r\n        }\r\n        installingWorker.onstatechange = () => {\r\n          if (installingWorker.state === 'installed') {\r\n            if (navigator.serviceWorker.controller) {\r\n              // At this point, the updated precached content has been fetched,\r\n              // but the previous service worker will still serve the older\r\n              // content until all client tabs are closed.\r\n              console.log(\r\n                'New content is available and will be used when all ' +\r\n                  'tabs for this page are closed. See https://bit.ly/CRA-PWA.',\r\n              );\r\n\r\n              // Execute callback\r\n              if (config && config.onUpdate) {\r\n                config.onUpdate(registration);\r\n              }\r\n            } else {\r\n              // At this point, everything has been precached.\r\n              // It's the perfect time to display a\r\n              // \"Content is cached for offline use.\" message.\r\n              console.log('Content is cached for offline use.');\r\n\r\n              // Execute callback\r\n              if (config && config.onSuccess) {\r\n                config.onSuccess(registration);\r\n              }\r\n            }\r\n          }\r\n        };\r\n      };\r\n    })\r\n    .catch(error => {\r\n      console.error('Error during service worker registration:', error);\r\n    });\r\n}\r\n\r\nfunction checkValidServiceWorker(swUrl, config) {\r\n  // Check if the service worker can be found. If it can't reload the page.\r\n  fetch(swUrl, {\r\n    headers: { 'Service-Worker': 'script' },\r\n  })\r\n    .then(response => {\r\n      // Ensure service worker exists, and that we really are getting a JS file.\r\n      const contentType = response.headers.get('content-type');\r\n      if (\r\n        response.status === 404 ||\r\n        (contentType != null && contentType.indexOf('javascript') === -1)\r\n      ) {\r\n        // No service worker found. Probably a different app. Reload the page.\r\n        navigator.serviceWorker.ready.then(registration => {\r\n          registration.unregister().then(() => {\r\n            window.location.reload();\r\n          });\r\n        });\r\n      } else {\r\n        // Service worker found. Proceed as normal.\r\n        registerValidSW(swUrl, config);\r\n      }\r\n    })\r\n    .catch(() => {\r\n      console.log('No internet connection found. App is running in offline mode.');\r\n    });\r\n}\r\n\r\nexport function unregister() {\r\n  if ('serviceWorker' in navigator) {\r\n    navigator.serviceWorker.ready\r\n      .then(registration => {\r\n        registration.unregister();\r\n      })\r\n      .catch(error => {\r\n        console.error(error.message);\r\n      });\r\n  }\r\n}\r\n", "/* eslint-disable new-cap */\r\nimport html2canvas from 'html2canvas';\r\nimport * as jsPDF from 'jspdf';\r\n\r\nconst move = (array, element, delta) => {\r\n  const index = array.findIndex(item => item.id === element.id);\r\n  const newIndex = index + delta;\r\n  if (newIndex < 0 || newIndex === array.length) return;\r\n  const indexes = [index, newIndex].sort((a, b) => a - b);\r\n  array.splice(indexes[0], 2, array[indexes[1]], array[indexes[0]]);\r\n};\r\n\r\nconst hexToRgb = hex => {\r\n  const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\r\n  hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);\r\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\r\n  return result\r\n    ? {\r\n        r: parseInt(result[1], 16),\r\n        g: parseInt(result[2], 16),\r\n        b: parseInt(result[3], 16),\r\n      }\r\n    : null;\r\n};\r\n\r\nconst copyToClipboard = text => {\r\n  const textArea = document.createElement('textarea');\r\n  textArea.style.position = 'fixed';\r\n  textArea.style.top = 0;\r\n  textArea.style.left = 0;\r\n  textArea.style.width = '2em';\r\n  textArea.style.height = '2em';\r\n  textArea.style.padding = 0;\r\n  textArea.style.border = 'none';\r\n  textArea.style.outline = 'none';\r\n  textArea.style.boxShadow = 'none';\r\n  textArea.style.background = 'transparent';\r\n  textArea.value = text;\r\n  document.body.appendChild(textArea);\r\n  textArea.focus();\r\n  textArea.select();\r\n  const successful = document.execCommand('copy');\r\n  document.body.removeChild(textArea);\r\n  return successful;\r\n};\r\n\r\nconst saveData = dispatch => dispatch({ type: 'save_data' });\r\n\r\nconst addItem = (dispatch, key, value) => {\r\n  dispatch({\r\n    type: 'add_item',\r\n    payload: {\r\n      key,\r\n      value,\r\n    },\r\n  });\r\n\r\n  saveData(dispatch);\r\n};\r\n\r\nconst deleteItem = (dispatch, key, value) => {\r\n  dispatch({\r\n    type: 'delete_item',\r\n    payload: {\r\n      key,\r\n      value,\r\n    },\r\n  });\r\n\r\n  saveData(dispatch);\r\n};\r\n\r\nconst moveItemUp = (dispatch, key, value) => {\r\n  dispatch({\r\n    type: 'move_item_up',\r\n    payload: {\r\n      key,\r\n      value,\r\n    },\r\n  });\r\n\r\n  saveData(dispatch);\r\n};\r\n\r\nconst moveItemDown = (dispatch, key, value) => {\r\n  dispatch({\r\n    type: 'move_item_down',\r\n    payload: {\r\n      key,\r\n      value,\r\n    },\r\n  });\r\n\r\n  saveData(dispatch);\r\n};\r\n\r\nconst importJson = (event, dispatch) => {\r\n  const fr = new FileReader();\r\n  fr.addEventListener('load', () => {\r\n    const importedObject = JSON.parse(fr.result);\r\n    dispatch({ type: 'import_data', payload: importedObject });\r\n    dispatch({ type: 'save_data' });\r\n  });\r\n  fr.readAsText(event.target.files[0]);\r\n};\r\n\r\nlet saveAsPdfTimer = null;\r\nconst saveAsPdf = (pageRef, panZoomRef, quality, type) => {\r\n  if(saveAsPdfTimer){\r\n      return;\r\n  }\r\n  return new Promise(resolve => {\r\n    panZoomRef.current.autoCenter(1);\r\n    panZoomRef.current.reset();\r\n\r\n    saveAsPdfTimer = setTimeout(() => {\r\n      html2canvas(pageRef.current, {\r\n        scale: 5,\r\n        useCORS: true,\r\n        allowTaint: true,\r\n      }).then(canvas => {\r\n        const image = canvas.toDataURL('image/jpeg', quality / 100);\r\n        const doc = new jsPDF({\r\n          orientation: 'portrait',\r\n          unit: 'px',\r\n          format: type === 'unconstrained' ? [canvas.width, canvas.height] : 'a4',\r\n        });\r\n\r\n        const pageWidth = doc.internal.pageSize.getWidth();\r\n        const pageHeight = doc.internal.pageSize.getHeight();\r\n\r\n        const widthRatio = pageWidth / canvas.width;\r\n        const heightRatio = pageHeight / canvas.height;\r\n        const ratio = widthRatio > heightRatio ? heightRatio : widthRatio;\r\n\r\n        const canvasWidth = canvas.width * ratio;\r\n        const canvasHeight = canvas.height * ratio;\r\n\r\n        let marginX = 0;\r\n        let marginY = 0;\r\n\r\n        if (type !== 'unconstrained') {\r\n          marginX = (pageWidth - canvasWidth) / 2;\r\n          marginY = (pageHeight - canvasHeight) / 2;\r\n        }\r\n\r\n        doc.addImage(image, 'JPEG', marginX, marginY, canvasWidth, canvasHeight, null, 'SLOW');\r\n        doc.save(`RxResume_${Date.now()}.pdf`);\r\n        saveAsPdfTimer = null;\r\n        resolve();\r\n      });\r\n    }, 250);\r\n  });\r\n}\r\n  \r\nlet saveAsMultiPagePdfTimer = null;\r\nconst saveAsMultiPagePdf = (pageRef, panZoomRef, quality) => {\r\n  if(saveAsMultiPagePdfTimer){\r\n      return;\r\n  }\r\n  return new Promise(resolve => {\r\n    panZoomRef.current.autoCenter(1);\r\n    panZoomRef.current.reset();\r\n\r\n    saveAsMultiPagePdfTimer = setTimeout(() => {\r\n      html2canvas(pageRef.current, {\r\n        scale: 5,\r\n        useCORS: true,\r\n        allowTaint: true,\r\n      }).then(canvas => {\r\n        const image = canvas.toDataURL('image/jpeg', quality / 100);\r\n        const doc = new jsPDF({\r\n          orientation: 'portrait',\r\n          unit: 'px',\r\n          format: 'a4',\r\n        });\r\n\r\n        const pageHeight = doc.internal.pageSize.getHeight();\r\n        const canvasWidth = doc.internal.pageSize.getWidth();\r\n        const canvasHeight = (canvas.height * canvasWidth) / canvas.width;\r\n        let marginTop = 0;\r\n        let heightLeft = canvasHeight;\r\n\r\n        doc.addImage(image, 'JPEG', 0, marginTop, canvasWidth, canvasHeight);\r\n        heightLeft -= pageHeight;\r\n\r\n        while (heightLeft >= 0) {\r\n          marginTop = heightLeft - canvasHeight;\r\n          doc.addPage();\r\n          doc.addImage(image, 'JPEG', 0, marginTop, canvasWidth, canvasHeight);\r\n          heightLeft -= pageHeight;\r\n        }\r\n\r\n        doc.save(`RxResume_${Date.now()}.pdf`);\r\n        saveAsMultiPagePdfTimer = null;\r\n        resolve();\r\n      });\r\n    }, 250);\r\n  });\r\n}\r\n\r\nexport {\r\n  move,\r\n  hexToRgb,\r\n  copyToClipboard,\r\n  saveData,\r\n  addItem,\r\n  deleteItem,\r\n  moveItemUp,\r\n  moveItemDown,\r\n  importJson,\r\n  saveAsPdf,\r\n  saveAsMultiPagePdf,\r\n};\r\n", "import React, { createContext, useReducer } from 'react';\r\nimport get from 'lodash/get';\r\nimport set from 'lodash/set';\r\nimport remove from 'lodash/remove';\r\n\r\nimport demoJsonldData from '../assets/demo/jsonlddata.json';\r\nimport { move } from '../utils';\r\n\r\nconst initialState = {\r\n  data: {\r\n    jsonld:{\r\n      \"@context\": [\r\n\t\t\"https://jsonldresume.github.io/skill/context.json\",\r\n\t\t{\r\n\t\t\t\"gender\": {\r\n\t\t\t\t\"@id\": \"schema:gender\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t},\r\n\t\t\t\"skill:classOfAward\": {\r\n\t\t\t\t\"@id\": \"skill:classOfAward\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t},\r\n\t\t\t\"skill:securityClearance\": {\r\n\t\t\t\t\"@id\": \"skill:securityClearance\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t},\r\n\t\t\t\"category\": {\r\n\t\t\t\t\"@id\": \"schema:category\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t},\r\n\t\t\t\"dayOfWeek\": {\r\n\t\t\t\t\"@id\": \"schema:dayOfWeek\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t}\r\n\t\t}\r\n      ],\r\n      '@graph': [\r\n        {\r\n          \"@type\": \"skill:Resume\",\r\n        },\r\n        {\r\n          \"@type\": \"Person\",\r\n          givenName:[{'@language': 'en', '@value':''}],\r\n          familyName: [{'@language': 'en', '@value':''}],\r\n          address: []\r\n        }\r\n      ]\r\n    },\r\n    profile: {\r\n      heading: 'Profile',\r\n      photo: '',\r\n      firstName: '',\r\n      lastName: '',\r\n      subtitle: '',\r\n      address: {\r\n        line1: '',\r\n        line2: '',\r\n        line3: '',\r\n      },\r\n      phone: '',\r\n      website: '',\r\n      email: '',\r\n    },\r\n    contacts: {\r\n      \"enable\": true,\r\n      heading: \"Contacts\"\r\n    },\r\n    address: {\r\n      \"enable\": true,\r\n      heading: 'Address'\r\n    },\r\n    objective: {\r\n      enable: true,\r\n      heading: 'Objective',\r\n      body: '',\r\n    },\r\n    work: {\r\n      enable: true,\r\n      heading: 'Work Experience',\r\n      items: [],\r\n    },\r\n    education: {\r\n      enable: true,\r\n      heading: 'Education',\r\n      items: [],\r\n    },\r\n    awards: {\r\n      enable: true,\r\n      heading: 'Honors & Awards',\r\n      items: [],\r\n    },\r\n    certifications: {\r\n      enable: true,\r\n      heading: 'Certifications',\r\n      items: [],\r\n    },\r\n    skills: {\r\n      enable: true,\r\n      heading: 'Skills',\r\n      items: [],\r\n    },\r\n    memberships: {\r\n      enable: true,\r\n      heading: 'Memberships',\r\n      items: [],\r\n    },\r\n    languages: {\r\n      enable: true,\r\n      heading: 'Languages',\r\n      items: [],\r\n    },\r\n    references: {\r\n      enable: true,\r\n      heading: 'References',\r\n      items: [],\r\n    },\r\n    extras: {\r\n      enable: true,\r\n      heading: 'Personal Information',\r\n      items: [],\r\n    },\r\n  },\r\n  theme: {\r\n    layout: 'Onyx',\r\n    font: {\r\n      family: '',\r\n    },\r\n    colors: {\r\n      background: '#ffffff',\r\n      primary: '#212121',\r\n      accent: '#f44336',\r\n    },\r\n  },\r\n  settings: {\r\n    language: 'en',\r\n  },\r\n};\r\n\r\nconst reducer = (state, { type, payload }) => {\r\n  let items;\r\n  const newState = JSON.parse(JSON.stringify(state));\r\n\r\n  switch (type) {\r\n    case 'migrate_section':\r\n      return set({ ...newState }, `data.${payload.key}`, payload.value);\r\n    case 'add_item':\r\n      items = get({ ...newState }, `${payload.key}`, []);\r\n      items.push(payload.value);\r\n      return set({ ...newState }, `${payload.key}`, items);\r\n    case 'delete_item':\r\n      items = get({ ...newState }, `${payload.key}`, []);\r\n      remove(items, x => x.id === payload.value.id);\r\n      return set({ ...newState }, `${payload.key}`, items);\r\n    case 'move_item_up':\r\n      items = get({ ...newState }, `${payload.key}`, []);\r\n      move(items, payload.value, -1);\r\n      return set({ ...newState }, `${payload.key}`, items);\r\n    case 'move_item_down':\r\n      items = get({ ...newState }, `${payload.key}`, []);\r\n      move(items, payload.value, 1);\r\n      return set({ ...newState }, `${payload.key}`, items);\r\n    case 'on_input':\r\n      return set({ ...newState }, payload.key, payload.value);\r\n    case 'save_data':\r\n      localStorage.setItem('state', JSON.stringify(newState));\r\n      return newState;\r\n    case 'import_data':\r\n      if (payload === null) return initialState;\r\n\r\n      for (const section of Object.keys(initialState.data)) {\r\n        if (!(section in payload.data)) {\r\n          payload.data[section] = initialState.data[section];\r\n        }\r\n      }\r\n\r\n      return {\r\n        ...newState,\r\n        ...payload,\r\n      };\r\n    case 'load_demo_data':\r\n      return {\r\n        ...newState,\r\n        ...demoJsonldData,\r\n      };\r\n    case 'reset':\r\n      return initialState;\r\n    default:\r\n      return newState;\r\n  }\r\n};\r\n\r\nconst AppContext = createContext(initialState);\r\nconst { Provider } = AppContext;\r\n\r\nconst StateProvider = ({ children }) => {\r\n  const [state, dispatch] = useReducer(reducer, initialState);\r\n  return <Provider value={{ state, dispatch }}>{children}</Provider>;\r\n};\r\n\r\nexport const AppProvider = StateProvider;\r\nexport const AppConsumer = AppContext.Consumer;\r\n\r\nexport default AppContext;\r\n", "import React, { useState } from 'react';\r\n\r\nconst PageContext = React.createContext(null);\r\nconst { Provider } = PageContext;\r\n\r\nconst StateProvider = ({ children }) => {\r\n  const [pageRef, setPageRef] = useState(null);\r\n  const [panZoomRef, setPanZoomRef] = useState(null);\r\n  const [isPrintDialogOpen, setPrintDialogOpen] = useState(false);\r\n\r\n  return (\r\n    <Provider\r\n      value={{\r\n        pageRef,\r\n        setPageRef,\r\n        panZoomRef,\r\n        setPanZoomRef,\r\n        isPrintDialogOpen,\r\n        setPrintDialogOpen,\r\n      }}\r\n    >\r\n      {children}\r\n    </Provider>\r\n  );\r\n};\r\n\r\nexport const PageProvider = StateProvider;\r\nexport const PageConsumer = PageContext.Consumer;\r\n\r\nexport default PageContext;\r\n", "import React from 'react';\r\n\r\nconst Dropdown = ({ className, label, value, onChange, options, optionItem }) => (\r\n  <div className={\"flex flex-col mb-2 \"+ className} style={{display:'contents'}}>\r\n    {label && (\r\n      <label className=\"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2\">\r\n        {label}\r\n      </label>\r\n    )}\r\n    <div className=\"inline-flex relative w-full bg-gray-200 text-gray-800 rounded py-3 px-4 leading-tight focus:outline-none\">\r\n      <select\r\n        className=\"block appearance-none w-full bg-gray-200 text-gray-800 focus:outline-none\"\r\n        value={value}\r\n        onChange={e => onChange(e.target.value)}\r\n      >\r\n        {options.map(optionItem)}\r\n      </select>\r\n      <div className=\"pointer-events-none absolute inset-y-0 right-0 flex justify-center items-center px-2 bg-gray-200\">\r\n        <i className=\"material-icons\">expand_more</i>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport default Dropdown;\r\n", "import React from 'react';\r\nimport Dropdown from './Dropdown';\r\n\r\nconst TabBar = ({ tabs, currentTab, setCurrentTab }) => {\r\n\r\n  const changeBy = (x) => {\r\n    const index = tabs.findIndex((tab) => tab.key === currentTab);\r\n\r\n    if (x < 0 && index > 0) {\r\n      setCurrentTab(tabs[index - 1].key);\r\n    }\r\n\r\n    if (x > 0 && index < tabs.length - 1) {\r\n      setCurrentTab(tabs[index + 1].key);\r\n    }\r\n  };\r\n  const TabOption = (tab, index) => {\r\n    return (\r\n      <option key={tab.key} value={tab.key}>\r\n        {tab.name || 'Tab'}\r\n      </option>\r\n    );\r\n  };\r\n  return (\r\n    <div className=\"mx-4 mb-6 flex items-center\">\r\n      <div\r\n        className=\"flex mr-1 cursor-pointer select-none text-gray-600 hover:text-gray-800\"\r\n        onClick={() => changeBy(-1)}\r\n      >\r\n        <i className=\"material-icons\">chevron_left</i>\r\n      </div>\r\n      \r\n        <Dropdown\r\n          className=\"mb-6\"\r\n          label=''\r\n          placeholder=\"\"\r\n          value={currentTab}\r\n          onChange={v => {setCurrentTab(v);}}\r\n          options = {tabs}\r\n          optionItem = {TabOption}\r\n        />\r\n\r\n      <div\r\n        className=\"flex ml-1 cursor-pointer select-none text-gray-600 hover:text-gray-800\"\r\n        onClick={() => changeBy(1)}\r\n      >\r\n        <i className=\"material-icons\">chevron_right</i>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TabBar;\r\n", "import React from 'react';\r\nimport * as _  from 'lodash';\r\n\r\nconst availableLanguages = ['en', 'fr', 'it', 'de', 'ar'];\r\n\r\nconst MakeSelectOptions = function() {\r\n  let options = [];\r\n  availableLanguages.forEach((c) => {\r\n    options.push(<option key={c} value={c}>{c}</option>);\r\n  });\r\n  return options;\r\n};\r\n\r\nexport default class TextField extends React.Component {\r\n    state = {\r\n      editingLanguage: 'en'\r\n\t}\r\n\r\n    handleMultiTextChange = (value, index) => {\r\n      let allValues = this.props.value;\r\n      if(!this.props.value || !Array.isArray(this.props.value)){\r\n          allValues = [];\r\n      }\r\n      \r\n      while(_.size(allValues)<=index){\r\n        allValues.push(\"\");\r\n      }\r\n      allValues[index] = value;\r\n      \r\n      this.props.onChange(allValues);\r\n    }\r\n    \r\n    initAllValues = (lang) => {\r\n      let allValues = this.props.value;\r\n      if(!this.props.value || !Array.isArray(this.props.value)){\r\n        allValues = [\r\n          {\r\n            \"@language\": lang,\r\n            \"@value\": \"\"\r\n          }\r\n        ];\r\n      }\r\n      \r\n      let currrentValueIndex = allValues.findIndex(x => x[\"@language\"] === lang);\r\n      if(currrentValueIndex < 0){\r\n        let newLang = {\r\n          \"@language\": lang,\r\n          \"@value\": \"\"\r\n        };\r\n        allValues.push(newLang);\r\n        this.props.onChange(allValues);\r\n      }\r\n      currrentValueIndex = allValues.findIndex(x => x[\"@language\"] === lang);\r\n      return currrentValueIndex;\r\n    }\r\n    handleLanguageChange = (lang) => {\r\n      this.initAllValues(lang);\r\n      \r\n      this.setState({\r\n        editingLanguage: lang\r\n      });\r\n    }\r\n    handleTextChange = (lang, value) => {\r\n      let currrentValueIndex = this.initAllValues(lang);\r\n      let allValues = this.props.value;\r\n      \r\n      allValues[currrentValueIndex][\"@value\"] = value;\r\n      this.props.onChange(allValues);\r\n    }\r\n    \r\n    MultiItem = (x, index) => (\r\n      <div key={\"holder_\"+index} style={{display: \"flex\"}}>\r\n        <input\r\n          className=\"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500\"\r\n          type={this.props.type}\r\n          disabled={this.props.disabled}\r\n          value={this.props.value[index]}\r\n          onChange={e => this.handleMultiTextChange(e.target.value, index)}\r\n          placeholder={this.props.placeholder}\r\n          key={\"input_\"+index}\r\n        />\r\n        {(_.size(this.props.value)<=1) ? (\"\") : (\r\n        <button\r\n          type=\"button\"\r\n          onClick={()=>{_.pullAt(this.props.value, index);this.props.onChange(this.props.value);}}\r\n          className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded\"\r\n          key={\"button_\"+index}\r\n        >\r\n          <div className=\"flex items-center\" key={\"removeHolder_\"+index}>\r\n            <i className=\"material-icons font-bold text-base\" key={\"remove_\"+index}>remove</i>\r\n          </div>\r\n        </button>)\r\n        }\r\n      </div>\r\n    );\r\n    render() {\r\n      return (\r\n\t\t<div className={`w-full flex flex-col ${this.props.className}`}>\r\n          {this.props.label && (\r\n            <label className=\"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2\">\r\n              {this.props.label}\r\n            </label>\r\n          )}\r\n          { (this.props.type===\"multilang\") ? (\r\n            <div style={{display: \"flex\"}}>\r\n              <select value={this.state.editingLanguage} onChange={(event) => this.handleLanguageChange(event.target.value)}>\r\n                  {MakeSelectOptions()}\r\n              </select>\r\n              <input\r\n                className=\"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500\"\r\n                type={this.props.type}\r\n                disabled={this.props.disabled}\r\n                value={this.props.value[((this.props.value.findIndex(x => x[\"@language\"] === this.state.editingLanguage)>=0)?(this.props.value.findIndex(x => x[\"@language\"] === this.state.editingLanguage)) : 0)][\"@value\"]}\r\n                onChange={e => this.handleTextChange(this.state.editingLanguage, e.target.value)}\r\n                placeholder={this.props.placeholder}\r\n              />\r\n            </div>\r\n          ) : ( \r\n            (this.props.type===\"multitext\") ? (\r\n              <div>\r\n                {this.props.value.map(this.MultiItem)}\r\n                <div key=\"holder_main\" style={{display: \"flex\"}}>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={()=>{this.props.value.push(\"\");this.props.onChange(this.props.value);}}\r\n                    className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded\"\r\n                    key=\"button_main\"\r\n                  >\r\n                    <div className=\"flex items-center\" key=\"addHolder_main\">\r\n                      <i className=\"material-icons font-bold text-base\" key=\"add_main\">add</i>\r\n                    </div>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <input\r\n                className=\"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500\"\r\n                type={this.props.type}\r\n                disabled={this.props.disabled}\r\n                value={this.props.value}\r\n                onChange={e => this.props.onChange(e.target.value)}\r\n                placeholder={this.props.placeholder}\r\n              />\r\n            )\r\n          )\r\n          }\r\n        </div>\r\n      );\r\n\t}\r\n};\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport * as _  from 'lodash';\r\n\r\nimport TextField from '../../../shared/TextField';\r\n\r\nconst ProfileTab = ({ data, onChange }) => {\r\n  const { t } = useTranslation('leftSidebar');\r\n  let personUrl = \"_:\";\r\n  const setValue = (path, field, v, type=null, id=null) => {\r\n    let val = _.get(data, path+\".\"+field, null);\r\n    if(val === null){\r\n      if(typeof(v) === \"string\" || typeof(v) === \"number\"){\r\n        _.set(data, path+\".\"+field, \"\");\r\n      }else if(typeof(v) === \"object\"){\r\n          if(Array.isArray(v)){\r\n            _.set(data, path+\".\"+field, []);\r\n          }else{\r\n            _.set(data, path+\".\"+field, {});\r\n          }\r\n      }\r\n    }\r\n  \r\n    onChange(\"data.\"+path+'.'+field, v);\r\n    if(id){\r\n      onChange(\"data.\"+path+'[\"@id\"]', id);\r\n    }\r\n    if(type){\r\n      onChange(\"data.\"+path+'[\"@type\"]', type);\r\n    }\r\n  };\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        placeholder=\"Heading\"\r\n        value={data.profile.heading}\r\n        onChange={v => onChange('data.profile.heading', v)}\r\n      />\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.photoUrl.label')}\r\n        placeholder=\"https://i.imgur.com/...\"\r\n        value={_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\")}\r\n        onChange={v => {setValue('jsonld[\"@graph\"][1].image', \"contentUrl\", v, \"ImageObject\", personUrl+\"#image\");}}\r\n      />\r\n\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('profile.firstName.label')}\r\n          placeholder=\"Jane\"\r\n          value={_.get(data,\"jsonld['@graph'][1].givenName\", \"\")}\r\n          onChange={v => setValue('jsonld[\"@graph\"][1]', \"givenName\", v)}\r\n          type=\"multilang\"\r\n        />\r\n\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('profile.lastName.label')}\r\n          placeholder=\"Doe\"\r\n          value={_.get(data,\"jsonld['@graph'][1].familyName\", \"\")}\r\n          onChange={v => setValue('jsonld[\"@graph\"][1]', \"familyName\", v)}\r\n          type=\"multilang\"\r\n        />\r\n      </div>\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.subtitle.label')}\r\n        placeholder=\"Full-Stack Web Developer\"\r\n        value={_.get(data, 'jsonld[\"@graph\"][1].description', \"\")}\r\n        onChange={v => {setValue('jsonld[\"@graph\"][1]', \"description\", v);}}\r\n      />\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.website.label')}\r\n        placeholder=\"janedoe.me\"\r\n        value={_.get(data,'jsonld[\"@graph\"][1].sameAs', [])}\r\n        onChange={v => setValue('jsonld[\"@graph\"][1]', \"sameAs\", v)}\r\n        AddItem={()=>{}}\r\n        type=\"multitext\"\r\n      />\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileTab;\r\n", "import React from 'react';\r\n\r\nconst Checkbox = ({ checked, onChange, icon = 'check', size = '2rem' }) => {\r\n  return (\r\n    <div\r\n      className=\"relative bg-white border-2 rounded border-gray-400 hover:border-gray-500 flex flex-shrink-0 justify-center items-center mr-2 focus-within:border-blue-500 cursor-pointer\"\r\n      style={{ width: size, height: size }}\r\n    >\r\n      <input\r\n        type=\"checkbox\"\r\n        style={{ width: size, height: size }}\r\n        className=\"opacity-0 absolute cursor-pointer z-20\"\r\n        checked={checked}\r\n        onChange={e => onChange(e.target.checked)}\r\n      />\r\n      <i\r\n        className={`absolute material-icons ${\r\n          checked ? 'opacity-100' : 'opacity-0'\r\n        } text-sm text-gray-800`}\r\n      >\r\n        {icon}\r\n      </i>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Checkbox;\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport Checkbox from './Checkbox';\r\nimport { deleteItem, moveItemUp, moveItemDown } from '../utils';\r\n\r\nconst ItemActions = ({ dispatch, first, identifier, item, last, onChange, type, enableAction }) => {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <div className=\"flex justify-between\">\r\n      <div className=\"flex items-center\">\r\n      {enableAction ? enableAction(identifier, item, onChange) : (\r\n        <Checkbox\r\n          size=\"2.25rem\"\r\n          checked={item.enable}\r\n          onChange={v => {\r\n            onChange(`${identifier}enable`, v);\r\n          }}\r\n        />\r\n      )}\r\n\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => deleteItem(dispatch, type, item)}\r\n          className=\"ml-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">delete</i>\r\n            <span className=\"text-sm\">{t('buttons.delete.label')}</span>\r\n          </div>\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"flex\">\r\n        {!first && (\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => moveItemUp(dispatch, type, item)}\r\n            className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded mr-2\"\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <i className=\"material-icons font-bold text-base\">arrow_upward</i>\r\n            </div>\r\n          </button>\r\n        )}\r\n\r\n        {!last && (\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => moveItemDown(dispatch, type, item)}\r\n            className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded\"\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <i className=\"material-icons font-bold text-base\">arrow_downward</i>\r\n            </div>\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ItemActions;\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst AddItemButton = ({ onSubmit }) => {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <div>\r\n      <button\r\n        type=\"button\"\r\n        onClick={onSubmit}\r\n        className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n      >\r\n        <div className=\"flex items-center\">\r\n          <i className=\"material-icons mr-2 font-bold text-base\">add</i>\r\n          <span className=\"text-sm\">{t('buttons.add.label')}</span>\r\n        </div>\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AddItemButton;\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ItemHeading = ({ title, heading, isOpen, setOpen }) => {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <div\r\n      className=\"flex justify-between items-center cursor-pointer\"\r\n      onClick={() => setOpen(!isOpen)}\r\n    >\r\n      <h6 className=\"text-sm font-medium\">\r\n        {typeof heading === 'undefined' ? title : t('item.add', { heading })}\r\n      </h6>\r\n      <i className=\"material-icons\">{isOpen ? 'expand_less' : 'expand_more'}</i>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ItemHeading;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nconst AddressTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox checked={data.address.enable} onChange={v => onChange('data.address.enable', v)} />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.address.heading}\r\n            onChange={v => onChange('data.address.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {data.jsonld[\"@graph\"][1].address && data.jsonld[\"@graph\"][1].address.map((x, index) => (\r\n        <Item\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          index={index}\r\n          item={x}\r\n          key={x[\"@id\"]}\r\n          last={index === data.jsonld[\"@graph\"][1].address.length - 1}\r\n          onChange={onChange}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.address.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.streetAddress.label')}\r\n        placeholder=\"20 Malvin Dr\"\r\n        value={item.streetAddress}\r\n        onChange={v => onChange(`${identifier}streetAddress`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.addressLocality.label')}\r\n        placeholder=\"Toronto\"\r\n        value={item.addressLocality}\r\n        onChange={v => onChange(`${identifier}addressLocality`, v)}\r\n      />\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.addressRegion.label')}\r\n        placeholder=\"ON\"\r\n        value={item.addressRegion}\r\n        onChange={v => onChange(`${identifier}addressRegion`, v)}\r\n      />\r\n\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('address.addressCountry.label')}\r\n          placeholder=\"Canada\"\r\n          value={item.addressCountry}\r\n          onChange={v => onChange(`${identifier}addressCountry`, v)}\r\n        />\r\n\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('address.postalCode.label')}\r\n          placeholder=\"H1H 0H0\"\r\n          value={item.postalCode}\r\n          onChange={v => onChange(`${identifier}postalCode`, v)}\r\n        />\r\n      </div>\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.sameAs.label')}\r\n        placeholder=\"Google Map Url of address\"\r\n        value={item.sameAs}\r\n        onChange={v => onChange(`${identifier}sameAs`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  let id = \"_:\"+uuidv4();\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState({\r\n    \"@id\": id,\r\n    \"@type\": \"PostalAddress\",\r\n    hoursAvailable:{\r\n      \"@id\": id+\"#hoursAvailable\",\r\n      \"@type\": \"OpeningHoursSpecification\",\r\n      \"validThrough\": \"2099-01-01\"\r\n    },\r\n    addressCountry: '',\r\n    streetAddress: '',\r\n    addressRegion: '',\r\n    addressLocality: '',\r\n    postalCode: '',\r\n    contactType: '',\r\n    sameAs: ''\r\n  });\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n  const onSubmit = () => {\r\n    let id = \"_:\"+uuidv4();\r\n    if ( item.addressCountry === '' ) return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].address', item);\r\n\r\n    setItem({\r\n      \"@id\": id,\r\n      \"@type\": \"PostalAddress\",\r\n      hoursAvailable:{\r\n        \"@id\": id+\"#hoursAvailable\",\r\n        \"@type\": \"OpeningHoursSpecification\",\r\n        \"validThrough\": \"2099-01-01\"\r\n      },\r\n      addressCountry: '',\r\n      streetAddress: '',\r\n      addressRegion: '',\r\n      addressLocality: '',\r\n      postalCode: '',\r\n      contactType: '',\r\n      sameAs: ''\r\n    });\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <Checkbox\r\n      size=\"2.25rem\"\r\n      checked={(item && item.hoursAvailable && item.hoursAvailable.validThrough && (Date.parse(item.hoursAvailable.validThrough) - Date.parse(new Date()))>0) }\r\n      onChange={v => {\r\n        let validThrough = \"1900-01-01\";\r\n        if(v){validThrough = \"2099-01-01\";}\r\n        onChange(`${identifier}hoursAvailable.validThrough`, validThrough);\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].address[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item.streetAddress || item.addressCountry} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].address\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AddressTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport Dropdown from '../../../shared/Dropdown';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nconst ContactsTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox checked={data.contacts.enable} onChange={v => onChange('data.contacts.enable', v)} />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.contacts.heading}\r\n            onChange={v => onChange('data.contacts.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {data.jsonld[\"@graph\"][1].contactPoint && data.jsonld[\"@graph\"][1].contactPoint.filter(x=>(x.contactType===\"Preferred\")).map((x, index) => (\r\n        <Item\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          index={index}\r\n          item={x}\r\n          key={x[\"@id\"]}\r\n          last={index === data.jsonld[\"@graph\"][1].contactPoint.length - 1}\r\n          onChange={onChange}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.contacts.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  const ContactTypeOption = (x, index) => {\r\n    return (\r\n      <option key={x} value={x}>\r\n        {x}\r\n      </option>\r\n    );\r\n  };\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.phone.label')}\r\n        placeholder=\"+1 (999)999-9999\"\r\n        value={item.telephone}\r\n        onChange={v => onChange(`${identifier}telephone`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.email.label')}\r\n        placeholder=\"<EMAIL>\"\r\n        value={item.email}\r\n        onChange={v => onChange(`${identifier}email`, v)}\r\n      />\r\n      \r\n      <Dropdown\r\n        className=\"mb-6\"\r\n        label={t('profile.contactType.label')}\r\n        placeholder=\"Only preferred is shown on resume\"\r\n        value={item.contactType}\r\n        onChange={v => onChange(`${identifier}contactType`, v)}\r\n        options = {[\"Preferred\", \"Emergency\", \"Other\"]}\r\n        optionItem = {ContactTypeOption}\r\n      />\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.contacts.description')}\r\n        placeholder=\"Description\"\r\n        value={item.description}\r\n        onChange={v => onChange(`${identifier}description`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  let id = \"_:\"+uuidv4();\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState({\r\n    \"@id\": id,\r\n    \"@type\": \"ContactPoint\",\r\n    description: '',\r\n    contactType: 'Preferred',\r\n    email: '',\r\n    telephone: ''\r\n  });\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n  const onSubmit = () => {\r\n    let id = \"_:\"+uuidv4();\r\n    if ( item.contactType === '' ) return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].contactPoint', item);\r\n\r\n    setItem({\r\n      \"@id\": id,\r\n      \"@type\": \"ContactPoint\",\r\n      description: '',\r\n      contactType: 'Preferred',\r\n      email: '',\r\n      telephone: ''\r\n    });\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].contactPoint[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item.contactType || item.telephone} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].contactPoint\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContactsTab;\r\n", "import React from 'react';\r\nimport { Trans } from 'react-i18next';\r\n\r\nconst MarkdownHelpText = ({ className }) => {\r\n  return (\r\n    <div className={className}>\r\n      <p className=\"text-gray-800 text-xs\">\r\n        <Trans i18nKey=\"markdownHelpText\">\r\n          You can use\r\n          <a\r\n            className=\"text-blue-600 hover:underline\"\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            href=\"https://github.com/adam-p/markdown-here/wiki/Markdown-Cheatsheet\"\r\n          >\r\n            GitHub Flavored Markdown\r\n          </a>\r\n          to style this section of text.\r\n        </Trans>\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MarkdownHelpText;\r\n", "import React from 'react';\r\nimport MarkdownHelpText from './MarkdownHelpText';\r\n\r\nconst TextArea = ({ label, placeholder, value, onChange, className, rows = 5 }) => (\r\n  <div className={`w-full flex flex-col ${className}`}>\r\n    <label className=\"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2\">\r\n      {label}\r\n    </label>\r\n    <textarea\r\n      className=\"appearance-none block leading-7 w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 focus:outline-none focus:bg-white focus:border-gray-500\"\r\n      rows={rows}\r\n      value={value}\r\n      onChange={e => onChange(e.target.value)}\r\n      placeholder={placeholder}\r\n    />\r\n\r\n    <MarkdownHelpText className=\"mt-2\" />\r\n  </div>\r\n);\r\n\r\nexport default TextArea;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst ObjectiveTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox checked={data.objective.enable} onChange={v => onChange('data.objective.enable', v)} />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.objective.heading}\r\n            onChange={v => onChange('data.objective.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {data.jsonld[\"@graph\"][1].seeks && data.jsonld[\"@graph\"][1].seeks.map((x, index) => (\r\n        <Item\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          index={index}\r\n          item={x}\r\n          key={x[\"@id\"]}\r\n          last={index === data.jsonld[\"@graph\"][1].seeks.length - 1}\r\n          onChange={onChange}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.objective.heading} dispatch={dispatch} size={_.size(data.jsonld[\"@graph\"][1].seeks)} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Availablity = ({item, onChange}) => {\r\n  if(!item){\r\n    item = {};\r\n  }\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.addressLocality.label')}\r\n        placeholder=\"Toronto\"\r\n        value={_.get(item,'addressLocality', '')}\r\n        onChange={v => onChange('addressLocality', v)}\r\n      />\r\n      \r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('address.addressRegion.label')}\r\n          placeholder=\"ON\"\r\n          value={_.get(item,'addressRegion','')}\r\n          onChange={v => onChange('addressRegion', v)}\r\n        />\r\n\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('address.addressCountry.label')}\r\n          placeholder=\"Canada\"\r\n          value={_.get(item,'addressCountry','')}\r\n          onChange={v => onChange('addressCountry', v)}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nconst Form = ({ item, onChange, identifier = '', index=0 }) => {\r\n    \r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  const addAvailability = (value, key, availableAtOrFrom) => {\r\n    if(value && key){\r\n      let address = _.get(availableAtOrFrom,'address', {});\r\n      if(!address['@type']){\r\n        address['@type'] = \"PostalAddress\";\r\n        address['@id'] = item['@id']+\"_availableAtOrFrom_address\";\r\n      }\r\n      address[key] = value;\r\n      \r\n      _.set(availableAtOrFrom, 'address', address);\r\n      if(!availableAtOrFrom['@type']){\r\n        availableAtOrFrom['@type'] = \"Place\";\r\n        availableAtOrFrom['@id'] = item['@id']+\"_availableAtOrFrom\";\r\n      }\r\n      onChange(`${identifier}availableAtOrFrom`, availableAtOrFrom);\r\n    }\r\n  }\r\n  \r\n  return (\r\n    <div>\r\n      {(index===0) ? (<TextArea\r\n        rows=\"15\"\r\n        className=\"mb-4\"\r\n        label={t('objective.objective.label')}\r\n        value={_.get(item,'description', '')}\r\n        placeholder=\"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector.\"\r\n        onChange={v => onChange(`${identifier}description`, v)}\r\n      />) : (<></>)}\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('objective.availabilityStarts.label')}\r\n        placeholder=\"2022-01-01\"\r\n        value={_.get(item,'availabilityStarts', '')}\r\n        onChange={v => onChange(`${identifier}availabilityStarts`, v)}\r\n      />\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('objective.availabilityEnds.label')}\r\n        placeholder=\"2022-12-01\"\r\n        item={_.get(item, 'availabilityEnds', '')}\r\n        onChange={v => onChange(`${identifier}availabilityEnds`, v)}\r\n      />\r\n      \r\n      <Availablity\r\n        item={_.get(item, 'availableAtOrFrom.address', {})}\r\n        onChange={(key, value) => addAvailability(value, key, item.availableAtOrFrom)}\r\n      />\r\n      \r\n    </div>\r\n  );\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch, size }) => {\r\n\r\n  let id = \"_:\"+uuidv4();\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState({\r\n    \"@id\": id,\r\n    \"@type\": \"Demand\",\r\n    description: '',\r\n    availabilityStarts: '',\r\n    availabilityEnds: '',\r\n    availableAtOrFrom: {},\r\n    deliveryLeadTime: {}\r\n  });\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n  const onSubmit = () => {\r\n    let id = \"_:\"+uuidv4();\r\n    if ( item.description === '' && item.availableAtOrFrom === [] ) return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].seeks', item);\r\n\r\n    setItem({\r\n      \"@id\": id,\r\n      \"@type\": \"Demand\",\r\n      description: '',\r\n      availabilityStarts: '',\r\n      availabilityEnds: '',\r\n      availableAtOrFrom: {},\r\n      deliveryLeadTime: {}\r\n    });\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} index={size} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n    return (\r\n    <Checkbox\r\n      size=\"2.25rem\"\r\n      checked={(item && item.availabilityEnds && (Date.parse(item.availabilityEnds) - Date.parse(new Date()))>0) }\r\n      onChange={v => {\r\n        let availabilityEnds = \"1900-01-01\";\r\n        if(v){availabilityEnds = \"2099-01-01\";}\r\n        onChange(`${identifier}availabilityEnds`, availabilityEnds);\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].seeks[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item.availableAtOrFrom.address.addressCountry || (item.description.substring(0, 10)+\"...\")} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} index={index} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].seeks\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ObjectiveTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst WorkTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox checked={data.work.enable} onChange={v => onChange('data.work.enable', v)} />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.work.heading}\r\n            onChange={v => onChange('data.work.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][1], 'hasOccupation', []).map((x, index) => (\r\n        <Item\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          index={index}\r\n          item={x}\r\n          key={_.get(x, '@id', 'item')}\r\n          last={index === _.size(data.jsonld[\"@graph\"][1].hasOccupation) - 1}\r\n          onChange={onChange}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.work.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  const setValue = (path, field, v, type=null, id=null) => {\r\n    let fullPath = path;\r\n    if(field){\r\n      fullPath = fullPath+\".\"+field;\r\n    }\r\n    let val = _.get(item, fullPath, null);\r\n    if(val === null){\r\n      if(typeof(v) === \"string\" || typeof(v) === \"number\"){\r\n        _.set(item, fullPath, \"\");\r\n      }else if(typeof(v) === \"object\"){\r\n          if(Array.isArray(v)){\r\n            _.set(item, fullPath, []);\r\n          }else{\r\n            _.set(item, fullPath, {});\r\n          }\r\n      }\r\n    }\r\n  \r\n    onChange(identifier+fullPath, v);\r\n    if(id){\r\n      onChange(`${identifier}`+path+'[\"@id\"]', id);\r\n    }\r\n    if(type){\r\n      onChange(`${identifier}`+path+'[\"@type\"]', type);\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.name.label')}\r\n        placeholder=\"Amazon\"\r\n        value={_.get(item, 'subjectOf.organizer.name', '')}\r\n        onChange={v => onChange(`${identifier}subjectOf.organizer.name`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.role.label')}\r\n        placeholder=\"Full-Stack Web Developer\"\r\n        value={_.get(item, 'roleName', '')}\r\n        onChange={v => onChange(`${identifier}roleName`, v)}\r\n      />\r\n\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('app:item.startDate.label')}\r\n          placeholder=\"2019-01-01\"\r\n          value={_.get(item, 'startDate', '')}\r\n          onChange={v => onChange(`${identifier}startDate`, v)}\r\n        />\r\n\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('app:item.endDate.label')}\r\n          placeholder=\"2020-01-01\"\r\n          value={_.get(item, 'endDate', '')}\r\n          onChange={v => onChange(`${identifier}endDate`, v)}\r\n        />\r\n      </div>\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.responsibilities.label')}\r\n        placeholder=\"Preparing project plans\"\r\n        value={_.get(item,'hasOccupation.responsibilities', [])}\r\n        onChange={v => setValue('hasOccupation', \"responsibilities\", v)}\r\n        AddItem={()=>{}}\r\n        type=\"multitext\"\r\n      />\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.skills.label')}\r\n        placeholder=\"Project Management\"\r\n        value={_.get(item,'hasOccupation.skills', [])}\r\n        onChange={v => setValue('hasOccupation', \"skills\", v)}\r\n        AddItem={()=>{}}\r\n        type=\"multitext\"\r\n      />\r\n\r\n      <TextArea\r\n        rows=\"5\"\r\n        className=\"mb-6\"\r\n        label={t('app:item.description.label')}\r\n        value={_.get(item, 'description', '')}\r\n        onChange={v => onChange(`${identifier}description`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"EmployeeRole\",\r\n    \"@id\": \"_:\"+id+\"#enable\",\r\n    hasOccupation: {\r\n      \"@id\": \"_:\"+id+\"#hasOccupation\",\r\n      \"@type\": \"Occupation\",\r\n      name: \"\",\r\n      skills: [],\r\n      responsibilities: []\r\n    },\r\n    subjectOf: {\r\n      \"@type\": \"BusinessEvent\",\r\n      id: \"_:\"+id+\"#subjectOf\",\r\n      organizer: {\r\n        \"@type\": \"Organization\",\r\n        id: \"_:\"+id+\"#subjectOf#organizer\",\r\n        name: ''\r\n      }\r\n    },\r\n    roleName: '',\r\n    startDate: '',\r\n    endDate: '',\r\n    description: ''\r\n  });\r\n};\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (item.roleName === '') return;\r\n    addItem(dispatch, \"data.jsonld['@graph'][1].hasOccupation\", item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].hasOccupation[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item.roleName+\" \"+item.subjectOf.organizer.name} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].hasOccupation\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WorkTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport Dropdown from '../../../shared/Dropdown';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst EducationTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n  \r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox\r\n            checked={data.education.enable}\r\n            onChange={v => onChange('data.education.enable', v)}\r\n          />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.education.heading}\r\n            onChange={v => onChange('data.education.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][1], 'hasCredential', []).map((x, index) => (\r\n        <Item\r\n          item={x}\r\n          key={_.get(x, '@id', 'item')}\r\n          index={index}\r\n          onChange={onChange}\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          last={index === _.size(_.get(data.jsonld[\"@graph\"][1], 'hasCredential', [])) - 1}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.education.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  const EducationTypeOption = (x, index) => {\r\n    return (\r\n      <option key={x} value={x}>\r\n        {x}\r\n      </option>\r\n    );\r\n  };\r\n  \r\n  const setValue = (path, field, v, type=null, id=null) => {\r\n    let fullPath = path;\r\n    if(field){\r\n      fullPath = fullPath+\".\"+field;\r\n    }\r\n    let val = _.get(item, fullPath, null);\r\n    if(val === null){\r\n      if(typeof(v) === \"string\" || typeof(v) === \"number\"){\r\n        _.set(item, fullPath, \"\");\r\n      }else if(typeof(v) === \"object\"){\r\n          if(Array.isArray(v)){\r\n            _.set(item, fullPath, []);\r\n          }else{\r\n            _.set(item, fullPath, {});\r\n          }\r\n      }\r\n    }\r\n  \r\n    onChange(identifier+fullPath, v);\r\n    if(id){\r\n      onChange(`${identifier}`+path+'[\"@id\"]', id);\r\n    }\r\n    if(type){\r\n      onChange(`${identifier}`+path+'[\"@type\"]', type);\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('education.name.label')}\r\n        placeholder=\"Harvard University\"\r\n        value={_.get(item, \"about.provider.name\", \"\")}\r\n        onChange={v => onChange(`${identifier}about.provider.name`, v)}\r\n      />\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <Dropdown\r\n          className=\"mb-6\"\r\n          label={t('education.type.label')}\r\n          placeholder=\"Certificate type\"\r\n          value={_.get(item, \"credentialCategory\", \"\")}\r\n          onChange={v => onChange(`${identifier}credentialCategory`, v)}\r\n          options = {[\"Degree\", \"Certificate\", \"Badge\"]}\r\n          optionItem = {EducationTypeOption}\r\n        />\r\n        \r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('education.major.degree')}\r\n          placeholder=\"Masters of Science\"\r\n          value={_.get(item, \"educationalLevel\", \"\")}\r\n          onChange={v => onChange(`${identifier}educationalLevel`, v)}\r\n        />\r\n      </div>\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('education.major.label')}\r\n        placeholder=\"Computer Science\"\r\n        value={_.get(item, \"about.educationalCredentialAwarded\", \"\")}\r\n        onChange={v => onChange(`${identifier}about.educationalCredentialAwarded`, v)}\r\n      />\r\n      \r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('education.grade.label')}\r\n          placeholder=\"3.7\"\r\n          value={_.get(item, \"aggregateRating.ratingValue\", \"\")}\r\n          onChange={v => onChange(`${identifier}aggregateRating.ratingValue`, v)}\r\n        />\r\n        \r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('education.maxGrade.label')}\r\n          placeholder=\"4\"\r\n          value={_.get(item, \"aggregateRating.bestRating\", \"\")}\r\n          onChange={v => onChange(`${identifier}aggregateRating.bestRating`, v)}\r\n        />\r\n      </div>\r\n      \r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('app:item.startDate.label')}\r\n          placeholder=\"2018-01-01\"\r\n          value={_.get(item, \"about.startDate\", \"\")}\r\n          onChange={v => onChange(`${identifier}about.startDate`, v)}\r\n        />\r\n      \r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('app:item.endDate.label')}\r\n          placeholder=\"2020-01-01\"\r\n          value={_.get(item, \"about.endDate\", \"\")}\r\n          onChange={v => onChange(`${identifier}about.endDate`, v)}\r\n        />\r\n      </div>\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.skills.label')}\r\n        placeholder=\"Project Management\"\r\n        value={_.get(item,'teaches', [])}\r\n        onChange={v => setValue('teaches', '', v)}\r\n        AddItem={()=>{}}\r\n        type=\"multitext\"\r\n      />\r\n\r\n      <TextArea\r\n        rows=\"5\"\r\n        className=\"mb-6\"\r\n        label={t('app:item.description.label')}\r\n        value={_.get(item, 'abstract', '')}\r\n        onChange={v => onChange(`${identifier}abstract`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"EducationalOccupationalCredential\",\r\n    \"@id\": \"_:\"+id+\"#enable\",\r\n    \"aggregateRating\": {\r\n      \"@id\": \"_:\"+id+\"#aggregateRating\",\r\n      \"@type\": \"aggregateRating\",\r\n      \"bestRating\": \"\",\r\n      \"ratingValue\": \"\",\r\n      \"name\": \"GPA\",\r\n      \"itemReviewed\": {\r\n        \"@id\": \"_:\"+id+\"#enable\"\r\n      }\r\n    },\r\n    \"credentialCategory\": \"degree\",\r\n    \"educationalLevel\": \"\",\r\n\t\"abstract\": \"\",\r\n    \"teaches\": [],\r\n    \"about\": {\r\n      \"@id\": \"_:\"+id+\"#about\",\r\n      \"@type\": \"EducationalOccupationalProgram\",\r\n      \"educationalCredentialAwarded\": \"\",\r\n      \"startDate\": \"\",\r\n      \"endDate\": \"\",\r\n      \"provider\": {\r\n\t\t\"@id\": \"_:\"+id+\"#about#provider\",\r\n\t\t\"@type\": \"CollegeOrUniversity\",\r\n\t\t\"name\": \"\"\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (item.educationalLevel === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].hasCredential', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld['@graph'][1].hasCredential[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, \"educationalLevel\", \"\")} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].hasCredential\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EducationTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst AwardsTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox\r\n            checked={data.awards.enable}\r\n            onChange={v => onChange('data.awards.enable', v)}\r\n          />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.awards.heading}\r\n            onChange={v => onChange('data.awards.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][0], 'award', []).map((x, index) => (\r\n        <Item\r\n          item={x}\r\n          key={_.get(x, '@id', 'main')}\r\n          index={index}\r\n          onChange={onChange}\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          last={index === _.size(_.get(data.jsonld[\"@graph\"][0], 'award', [])) - 1}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.awards.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '', altidentifier='' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('awards.title.label')}\r\n        placeholder=\"Code For Good Hackathon\"\r\n        value={_.get(item,'skill:title', \"\")}\r\n        onChange={v => {onChange(`${identifier}['skill:title']`, v);if(altidentifier!==''){onChange(`${altidentifier}`, v);} }}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('awards.subtitle.label')}\r\n        placeholder=\"First Place, National Level\"\r\n        value={_.get(item, 'skill:nativeLabel', '')}\r\n        onChange={v => onChange(`${identifier}['skill:nativeLabel']`, v)}\r\n      />\r\n\r\n      <TextArea\r\n        className=\"mb-6\"\r\n        label={t('app:item.description.label')}\r\n        value={item.description}\r\n        onChange={v => onChange(`${identifier}description`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"skill:Award\",\r\n    \"@id\": \"_:\"+id+\"#enable\",\r\n    \"skill:title\": \"\",\r\n\t\"skill:nativeLabel\": \"\",\r\n    description: ''\r\n  });\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (_.get(item, \"skill:title\", '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][0][\"award\"]', item);\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1][\"award\"]', _.get(item, \"skill:title\", ''));\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][0].award[${index}].`;\r\n  const altidentifier = `data.jsonld[\"@graph\"][1].award[${index}]`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item[\"skill:title\"]} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} altidentifier={altidentifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][0].award\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AwardsTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst ExtrasTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox\r\n            checked={data.extras.enable}\r\n            onChange={v => onChange('data.extras.enable', v)}\r\n          />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.extras.heading}\r\n            onChange={v => onChange('data.extras.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][1], 'identifier', []).map((x, index) => (\r\n        <Item\r\n          item={x}\r\n          key={_.get(x, '@id', 'main')}\r\n          index={index}\r\n          onChange={onChange}\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          last={index === _.get(data.jsonld[\"@graph\"][1], 'identifier', []).length - 1}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.extras.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation('leftSidebar');\r\n\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('extras.key.label')}\r\n        placeholder=\"Date of Birth\"\r\n        value={_.get(item, 'propertyID', '')}\r\n        onChange={v => onChange(`${identifier}propertyID`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('extras.value.label')}\r\n        placeholder=\"6th August 1995\"\r\n        value={_.get(item, 'value', '')}\r\n        onChange={v => onChange(`${identifier}value`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"PropertyValue\",\r\n    \"@id\": \"_:Extras_\"+id,\r\n    \"propertyID\": \"\",\r\n    \"value\": \"\"\r\n  });\r\n}\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(items => set({ ...items }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (_.get(item, 'propertyID', '') === '' || _.get(item, 'value', '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].identifier', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].identifier[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, 'propertyID', '')} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].identifier\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExtrasTab;\r\n", "import set from 'lodash/set';\r\nimport React, { useContext, useEffect, useState } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nimport AppContext from '../../../context/AppContext';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\nimport TextField from '../../../shared/TextField';\r\nimport { addItem } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst LanguagesTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  useEffect(() => {\r\n    if (!('languages' in data)) {\r\n      dispatch({\r\n        type: 'migrate_section',\r\n        payload: {\r\n          key: 'languages',\r\n          value: {\r\n            enable: false,\r\n            heading: 'Languages'\r\n          },\r\n        },\r\n      });\r\n\r\n      dispatch({ type: 'save_data' });\r\n    }\r\n  }, [data, dispatch]);\r\n\r\n  return (\r\n    'languages' in data && (\r\n      <>\r\n        <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n          <div className=\"col-span-1\">\r\n            <Checkbox\r\n              checked={data.languages.enable}\r\n              onChange={v => onChange('data.languages.enable', v)}\r\n            />\r\n          </div>\r\n          <div className=\"col-span-5\">\r\n            <TextField\r\n              placeholder=\"Heading\"\r\n              value={data.languages.heading}\r\n              onChange={v => onChange('data.languages.heading', v)}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <hr className=\"my-6\" />\r\n\r\n        {_.get(data.jsonld[\"@graph\"][1], 'knowsLanguage', []).map((x, index) => (\r\n          <Item\r\n            item={x}\r\n            key={_.get(x,'@id', 'item')}\r\n            index={index}\r\n            onChange={onChange}\r\n            dispatch={dispatch}\r\n            first={index === 0}\r\n            last={index === _.size(_.get(data.jsonld[\"@graph\"][1], 'knowsLanguage', [])) - 1}\r\n          />\r\n        ))}\r\n\r\n        <AddItem heading={data.languages.heading} dispatch={dispatch} />\r\n      </>\r\n    )\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation('leftSidebar');\r\n\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('languages.key.label')}\r\n        placeholder=\"English\"\r\n        value={_.get(item,'name', '')}\r\n        onChange={v => onChange(`${identifier}name`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"Language\",\r\n    \"@id\": \"_:\"+id,\r\n    \"name\": \"\"\r\n  });\r\n}\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  \r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(items => set({ ...items }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (_.get(item, 'name', '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].knowsLanguage', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].knowsLanguage[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, 'name', '')} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].knowsLanguage\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LanguagesTab;\r\n", "import React, { useState, useEffect, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst ReferencesTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  useEffect(() => {\r\n    if (!('references' in data)) {\r\n      dispatch({\r\n        type: 'migrate_section',\r\n        payload: {\r\n          key: 'references',\r\n          value: {\r\n            enable: false,\r\n            heading: 'References'\r\n          },\r\n        },\r\n      });\r\n\r\n      dispatch({ type: 'save_data' });\r\n    }\r\n  }, [data, dispatch]);\r\n  \r\n  return (\r\n    'references' in data && (\r\n      <>\r\n        <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n          <div className=\"col-span-1\">\r\n            <Checkbox\r\n              checked={data.references.enable}\r\n              onChange={v => onChange('data.references.enable', v)}\r\n            />\r\n          </div>\r\n          <div className=\"col-span-5\">\r\n            <TextField\r\n              placeholder=\"Heading\"\r\n              value={data.references.heading}\r\n              onChange={v => onChange('data.references.heading', v)}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <hr className=\"my-6\" />\r\n\r\n        {_.get(data.jsonld[\"@graph\"][1], 'interactionStatistic', []).filter(x => _.get(x, 'disambiguatingDescription', '')=== 'Reference').map((x, index) => (\r\n          <Item\r\n            item={x}\r\n            key={_.get(x,'@id', 'main')}\r\n            index={index}\r\n            onChange={onChange}\r\n            dispatch={dispatch}\r\n            first={index === 0}\r\n            last={index === _.get(data.jsonld[\"@graph\"][1], 'interactionStatistic', []).filter(x => _.get(x, 'disambiguatingDescription', '')=== 'Reference').length - 1}\r\n          />\r\n        ))}\r\n\r\n        <AddItem heading={data.references.heading} dispatch={dispatch} />\r\n      </>\r\n    )\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('references.name.label')}\r\n          placeholder=\"Richard Hendricks\"\r\n          value={_.get(item, 'interactionType.participant.givenName', '')}\r\n          onChange={v => onChange(`${identifier}interactionType.participant.givenName`, v)}\r\n        />\r\n        \r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('references.familyName.label')}\r\n          placeholder=\"Richard Hendricks\"\r\n          value={_.get(item, 'interactionType.participant.familyName', '')}\r\n          onChange={v => onChange(`${identifier}interactionType.participant.familyName`, v)}\r\n        />\r\n      </div>\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('references.position.label')}\r\n        placeholder=\"CEO, Pied Piper\"\r\n        value={_.get(item, 'interactionType.participant.jobTitle', '')}\r\n        onChange={v => onChange(`${identifier}interactionType.participant.jobTitle`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('references.phone.label')}\r\n        placeholder=\"****** 754 3010\"\r\n        value={_.get(item, 'interactionType.participant.telephone', '')}\r\n        onChange={v => onChange(`${identifier}interactionType.participant.telephone`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('references.email.label')}\r\n        placeholder=\"<EMAIL>\"\r\n        value={_.get(item, 'interactionType.participant.email', '')}\r\n        onChange={v => onChange(`${identifier}interactionType.participant.email`, v)}\r\n      />\r\n\r\n      <TextArea\r\n        rows=\"5\"\r\n        className=\"mb-6\"\r\n        label={t('app:item.description.label')}\r\n        value={_.get(item, 'result[0].reviewRating.ratingExplanation', '')}\r\n        onChange={v => onChange(`${identifier}result[0].reviewRating.ratingExplanation`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return (\r\n    {\r\n      \"@id\": \"_:Reference#\"+id,\r\n      \"@type\": \"InteractionCounter\",\r\n      \"disambiguatingDescription\": \"Reference\",\r\n      \"interactionType\": {\r\n        \"@id\": \"_:Reference#\"+id+\"#interactionType\",\r\n        \"@type\": \"AssessAction\",\r\n        \"participant\": {\r\n          \"@id\": \"_:Reference#\"+id+\"#interactionType#participant\",\r\n          \"@type\": \"Person\"\r\n        },\r\n        \"result\": [\r\n          {\r\n            \"@id\": \"_:Reference#\"+id+\"#result\",\r\n            \"@type\": \"Review\",\r\n            \"itemReviewed\": {\r\n            \r\n            },\r\n            \"reviewAspect\": [\r\n              \r\n            ],\r\n            \"reviewRating\": {\r\n              \"@id\": \"_:Reference#\"+id+\"#result#reviewRating\",\r\n              \"@type\": \"Rating\",\r\n              \"ratingValue\": \"5\",\r\n              \"bestRating\": \"5\",\r\n              \"ratingExplanation\": \"\"\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  );\r\n}\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (_.get(item, 'interactionType.participant.givenName', '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].interactionStatistic', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].interactionStatistic[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, 'interactionType.participant.givenName', '')+\" \"+_.get(item, 'interactionType.participant.familyName', '')} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].interactionStatistic\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReferencesTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst MembershipsTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"my-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox\r\n            checked={_.get(data,'Memberships.enable', true)}\r\n            onChange={v => onChange('data.Memberships.enable', v)}\r\n          />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={_.get(data, 'Memberships.heading', '')}\r\n            onChange={v => onChange('data.Memberships.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][1], 'memberOf', []).map((x, index) => (\r\n        <Item\r\n          item={x}\r\n          key={_.get(x, '@id', 'item')}\r\n          index={index}\r\n          onChange={onChange}\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          last={index === _.size(_.get(data.jsonld[\"@graph\"][1], 'memberOf', [])) - 1}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={_.get(data, 'Memberships.heading', '')} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  \r\n  return (\r\n    <div>\r\n      <TextField\r\n          className=\"mb-6\"\r\n          label={t('membership.programName.label')}\r\n          placeholder=\"Salsa Dance Class\"\r\n          value={_.get(item, \"memberOf.programName\", \"\")}\r\n          onChange={v => onChange(`${identifier}memberOf.programName`, v)}\r\n      />\r\n      \r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n            className=\"mb-6\"\r\n            label={t('membership.startDate.label')}\r\n            placeholder=\"2019-01-01\"\r\n            value={_.get(item, \"startDate\", \"\")}\r\n            onChange={v => onChange(`${identifier}startDate`, v)}\r\n        />\r\n      \r\n        <TextField\r\n            className=\"mb-6\"\r\n            label={t('membership.endDate.label')}\r\n            placeholder=\"2020-01-01\"\r\n            value={_.get(item, \"endDate\", \"\")}\r\n            onChange={v => onChange(`${identifier}endDate`, v)}\r\n        />\r\n      </div>\r\n        <TextField\r\n            className=\"mb-6\"\r\n            label={t('membership.roleName.label')}\r\n            placeholder = \"VIP member\"\r\n            value={_.get(item, \"roleName\", \"\")}\r\n            onChange={v => onChange(`${identifier}roleName`, v)}\r\n        />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return (\r\n    {\r\n      \"@id\": \"_:\"+id+\"#enable\",\r\n      \"@type\": \"Role\",\r\n      \"startDate\": \"\",\r\n      \"endDate\": \"\",\r\n      \"roleName\": \"member\",\r\n      \"memberOf\": {\r\n        \"@id\": \"_:\"+id+\"#memberOf\",\r\n        \"@type\": \"ProgramMembership\",\r\n        \"url\": \"\",\r\n        \"programName\": \"\",\r\n        \"description\": \"\"\r\n      }\r\n    }\r\n  );\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n  \r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n  const onSubmit = () => {\r\n    if (_.get(item, 'roleName', '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].memberOf', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n  \r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld['@graph'][1].memberOf[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, \"memberOf.programName\", \"\")} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].memberOf\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MembershipsTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport * as _  from 'lodash';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport TabBar from '../../shared/TabBar';\r\nimport ProfileTab from './tabs/Profile';\r\nimport AddressTab from './tabs/Address';\r\nimport ContactsTab from './tabs/Contacts';\r\nimport ObjectiveTab from './tabs/Objective';\r\nimport WorkTab from './tabs/Work';\r\nimport EducationTab from './tabs/Education';\r\nimport AwardsTab from './tabs/Awards';\r\nimport ExtrasTab from './tabs/Extras';\r\nimport LanguagesTab from './tabs/Languages';\r\nimport ReferencesTab from './tabs/References';\r\nimport MembershipsTab from './tabs/Memberships';\r\n\r\nconst LeftSidebar = () => {\r\n  const context = useContext(AppContext);\r\n  const { state, dispatch } = context;\r\n  const { data } = state;\r\n\r\n  const tabs = [\r\n    { key: 'profile', name: _.get(data, \"profile.heading\", \"Profile\") },\r\n    { key: 'address', name: _.get(data, \"address.headin\", \"Address\") },\r\n    { key: 'contacts', name: _.get(data, \"contacts.heading\", \"Contacts\") },\r\n    { key: 'objective', name: _.get(data, \"objective.heading\", \"Objective\") },\r\n    { key: 'work', name: _.get(data, \"work.heading\", \"Work\") },\r\n    { key: 'education', name: _.get(data, \"education.heading\", \"Education\") },\r\n    { key: 'awards', name: _.get(data, \"awards.heading\", \"Awards\")  },\r\n    { key: 'memberships', name: _.get(data, \"memberships.heading\", \"Memberships\") },\r\n    { key: 'languages', name: _.get(data, \"languages.heading\", \"Languages\") },\r\n    { key: 'references', name: _.get(data, \"references.heading\", \"References\") },\r\n    { key: 'extras', name: _.get(data, \"extras.heading\", \"Extras\") },\r\n  ];\r\n  const [currentTab, setCurrentTab] = useState(tabs[0].key);\r\n  const onChange = (key, value) => {\r\n    dispatch({\r\n      type: 'on_input',\r\n      payload: {\r\n        key,\r\n        value,\r\n      },\r\n    });\r\n\r\n    dispatch({ type: 'save_data' });\r\n  };\r\n\r\n  const renderTabs = () => {\r\n    switch (currentTab) {\r\n      case 'profile':\r\n        return <ProfileTab data={data} onChange={onChange} />;\r\n      case 'address':\r\n        return <AddressTab data={data} onChange={onChange} />;\r\n      case 'contacts':\r\n        return <ContactsTab data={data} onChange={onChange} />;\r\n      case 'objective':\r\n        return <ObjectiveTab data={data} onChange={onChange} />;\r\n      case 'work':\r\n        return <WorkTab data={data} onChange={onChange} />;\r\n      case 'education':\r\n        return <EducationTab data={data} onChange={onChange} />;\r\n      case 'awards':\r\n        return <AwardsTab data={data} onChange={onChange} />;\r\n      case 'memberships':\r\n        return <MembershipsTab data={data} onChange={onChange} />;\r\n      case 'languages':\r\n        return <LanguagesTab data={data} onChange={onChange} />;\r\n      case 'references':\r\n        return <ReferencesTab data={data} onChange={onChange} />;\r\n      case 'extras':\r\n        return <ExtrasTab data={data} onChange={onChange} />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      id=\"leftSidebar\"\r\n      className=\"animated slideInLeft z-10 py-6 h-screen bg-white col-span-1 shadow-2xl overflow-y-scroll\"\r\n    >\r\n      <TabBar tabs={tabs} currentTab={currentTab} setCurrentTab={setCurrentTab} />\r\n      <div className=\"px-6\">{renderTabs()}</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LeftSidebar;\r\n", "import React, { useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\n\r\nimport AppContext from '../../context/AppContext';\r\n\r\nconst Onyx = () => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  const Photo = () =>\r\n    data.profile.photo && (\r\n      <img\r\n        className=\"rounded object-cover mr-4\"\r\n        src={data.profile.photo}\r\n        alt=\"Resume Photograph\"\r\n        style={{ width: '120px', height: '120px' }}\r\n      />\r\n    );\r\n\r\n  const Profile = () => (\r\n    <div>\r\n      <h1 className=\"font-bold text-4xl\" style={{ color: theme.colors.accent }}>\r\n        {data.profile.firstName} {data.profile.lastName}\r\n      </h1>\r\n      <h6 className=\"font-medium text-sm\">{data.profile.subtitle}</h6>\r\n\r\n      <div className=\"flex flex-col mt-4 text-xs\">\r\n        <span>{data.profile.address.line1}</span>\r\n        <span>{data.profile.address.line2}</span>\r\n        <span>{data.profile.address.line3}</span>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const ContactItem = ({ icon, value, link = '#' }) =>\r\n    value && (\r\n      <div className=\"flex items-center my-3\">\r\n        <span className=\"material-icons text-lg mr-2\" style={{ color: theme.colors.accent }}>\r\n          {icon}\r\n        </span>\r\n        <a href={link}>\r\n          <span className=\"font-medium break-all\">{value}</span>\r\n        </a>\r\n      </div>\r\n    );\r\n\r\n  const Heading = ({ title }) => (\r\n    <h6 className=\"text-xs font-bold uppercase mt-6 mb-2\" style={{ color: theme.colors.accent }}>\r\n      {title}\r\n    </h6>\r\n  );\r\n\r\n  const Objective = () =>\r\n    data.objective &&\r\n    data.objective.enable && (\r\n      <div>\r\n        <Heading title={data.objective.heading} />\r\n        <ReactMarkdown className=\"text-sm\" source={data.objective.body} />\r\n      </div>\r\n    );\r\n\r\n  const WorkItem = x => (\r\n    <div key={x.id} className=\"mt-3\">\r\n      <div className=\"flex justify-between\">\r\n        <div>\r\n          <h6 className=\"font-semibold\">{x.title}</h6>\r\n          <p className=\"text-xs\">{x.role}</p>\r\n        </div>\r\n        <span className=\"text-xs font-medium\">\r\n          ({x.start} - {x.end})\r\n        </span>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Work = () =>\r\n    data.work &&\r\n    data.work.enable && (\r\n      <div>\r\n        <Heading title={data.work.heading} />\r\n        {data.work.items.filter(x => x.enable).map(WorkItem)}\r\n      </div>\r\n    );\r\n\r\n  const EducationItem = x => (\r\n    <div key={x.id} className=\"mt-3\">\r\n      <div className=\"flex justify-between\">\r\n        <div>\r\n          <h6 className=\"font-semibold\">{x.name}</h6>\r\n          <p className=\"text-xs\">{x.major}</p>\r\n        </div>\r\n        <div className=\"flex flex-col items-end\">\r\n          <span className=\"text-sm font-bold\">{x.grade}</span>\r\n          <span className=\"text-xs font-medium\">\r\n            ({x.start} - {x.end})\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Education = () =>\r\n    data.education &&\r\n    data.education.enable && (\r\n      <div>\r\n        <Heading title={data.education.heading} />\r\n        {data.education.items.filter(x => x.enable).map(EducationItem)}\r\n      </div>\r\n    );\r\n\r\n  const AwardItem = x => (\r\n    <div key={x.id} className=\"mt-3\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Awards = () =>\r\n    data.awards &&\r\n    data.awards.enable && (\r\n      <div>\r\n        <Heading title={data.awards.heading} />\r\n        {data.awards.items.filter(x => x.enable).map(AwardItem)}\r\n      </div>\r\n    );\r\n\r\n  const CertificationItem = x => (\r\n    <div key={x.id} className=\"mt-3\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Certifications = () =>\r\n    data.certifications &&\r\n    data.certifications.enable && (\r\n      <div>\r\n        <Heading title={data.certifications.heading} />\r\n        {data.certifications.items.filter(x => x.enable).map(CertificationItem)}\r\n      </div>\r\n    );\r\n\r\n  const HobbyItem = x => (\r\n    <span\r\n      key={x.id}\r\n      className=\"text-xs rounded-full px-3 py-1 font-medium my-2 mr-2\"\r\n      style={{\r\n        backgroundColor: theme.colors.primary,\r\n        color: theme.colors.background,\r\n      }}\r\n    >\r\n      {x.hobby}\r\n    </span>\r\n  );\r\n\r\n  const Memberships = () =>\r\n    data.memberships &&\r\n    data.memberships.enable && (\r\n      <div>\r\n        <Heading title={data.memberships.heading} />\r\n        <div className=\"mt-1 flex flex-wrap\">{data.memberships.items.map(HobbyItem)}</div>\r\n      </div>\r\n    );\r\n\r\n  const SkillItem = x => (\r\n    <span\r\n      key={x.id}\r\n      className=\"text-xs rounded-full px-3 py-1 font-medium my-2 mr-2\"\r\n      style={{\r\n        backgroundColor: theme.colors.primary,\r\n        color: theme.colors.background,\r\n      }}\r\n    >\r\n      {x.skill}\r\n    </span>\r\n  );\r\n\r\n  const Skills = () =>\r\n    data.skills &&\r\n    data.skills.enable && (\r\n      <div>\r\n        <Heading title={data.skills.heading} />\r\n        <div className=\"mt-1 flex flex-wrap\">{data.skills.items.map(SkillItem)}</div>\r\n      </div>\r\n    );\r\n\r\n  const LanguageItem = x => (\r\n    <div key={x.id} className=\"grid grid-cols-2 items-center py-2\">\r\n      <h6 className=\"text-sm font-medium\">{x.key}</h6>\r\n      <div className=\"flex\">\r\n        {x.level && <div className=\"font-bold text-sm mr-2\">{x.level}</div>}\r\n        {x.rating !== 0 && (\r\n          <div className=\"flex\">\r\n            {Array.from(Array(x.rating)).map((_, i) => (\r\n              <i key={i} className=\"material-icons text-lg\" style={{ color: theme.colors.accent }}>\r\n                star\r\n              </i>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const Languages = () =>\r\n    data.languages &&\r\n    data.languages.enable && (\r\n      <div>\r\n        <Heading title={data.languages.heading} />\r\n        <div className=\"w-3/4\">{data.languages.items.filter(x => x.enable).map(LanguageItem)}</div>\r\n      </div>\r\n    );\r\n\r\n  const ReferenceItem = x => (\r\n    <div key={x.id} className=\"flex flex-col\">\r\n      <h6 className=\"text-sm font-medium\">{x.name}</h6>\r\n      <span className=\"text-xs\">{x.position}</span>\r\n      <span className=\"text-xs\">{x.phone}</span>\r\n      <span className=\"text-xs\">{x.email}</span>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const References = () =>\r\n    data.references &&\r\n    data.references.enable && (\r\n      <div>\r\n        <Heading title={data.references.heading} />\r\n        <div className=\"grid grid-cols-3 gap-6\">\r\n          {data.references.items.filter(x => x.enable).map(ReferenceItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const ExtraItem = x => (\r\n    <tr key={x.id}>\r\n      <td className=\"border font-medium px-4 py-2 text-sm\">{x.key}</td>\r\n      <td className=\"border px-4 py-2 text-sm\">{x.value}</td>\r\n    </tr>\r\n  );\r\n\r\n  const Extras = () =>\r\n    data.extras &&\r\n    data.extras.enable && (\r\n      <div>\r\n        <Heading title={data.extras.heading} />\r\n        <table className=\"table-auto\">\r\n          <tbody>{data.extras.items.filter(x => x.enable).map(ExtraItem)}</tbody>\r\n        </table>\r\n      </div>\r\n    );\r\n\r\n  return (\r\n    <div\r\n      className=\"p-10\"\r\n      style={{\r\n        fontFamily: theme.font.family,\r\n        backgroundColor: theme.colors.background,\r\n        color: theme.colors.primary,\r\n      }}\r\n    >\r\n      <div className=\"grid grid-cols-4 items-center\">\r\n        <div className=\"col-span-3 flex items-center\">\r\n          <Photo />\r\n          <Profile />\r\n        </div>\r\n\r\n        <div className=\"col-span-1 text-xs\">\r\n          <ContactItem icon=\"phone\" value={data.profile.phone} link={`tel:${data.profile.phone}`} />\r\n          <ContactItem\r\n            icon=\"language\"\r\n            value={data.profile.website}\r\n            link={`http://${data.profile.website}`}\r\n          />\r\n          <ContactItem\r\n            icon=\"email\"\r\n            value={data.profile.email}\r\n            link={`mailto:${data.profile.email}`}\r\n          />\r\n          <ContactItem icon=\"location_on\" value={data.profile.address.line3} />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <Objective />\r\n      <Work />\r\n      <Education />\r\n\r\n      <div className=\"grid grid-cols-2 gap-6\">\r\n        <Awards />\r\n        <Certifications />\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-2 gap-6\">\r\n        <Skills />\r\n        <Memberships />\r\n      </div>\r\n\r\n      <References />\r\n\r\n      <div className=\"grid grid-cols-2 gap-6\">\r\n        <Extras />\r\n        <Languages />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Onyx;\r\n", "import Onyx from './Onyx';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Onyx;\r\n", "import React, { useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\n\r\nimport AppContext from '../../context/AppContext';\r\n\r\nconst Pikachu = () => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  const Photo = () =>\r\n    data.profile.photo !== '' && (\r\n      <div className=\"self-center col-span-4\">\r\n        <img\r\n          className=\"w-48 h-48 rounded-full mx-auto object-cover\"\r\n          src={data.profile.photo}\r\n          alt=\"\"\r\n        />\r\n      </div>\r\n    );\r\n\r\n  const Header = () => (\r\n    <div\r\n      className=\"h-48 rounded flex flex-col justify-center\"\r\n      style={{ backgroundColor: theme.colors.accent, color: theme.colors.background }}\r\n    >\r\n      <div className=\"flex flex-col justify-center mx-8 my-6\">\r\n        <h1 className=\"text-3xl font-bold leading-tight\">\r\n          {data.profile.firstName} {data.profile.lastName}\r\n        </h1>\r\n        <div className=\"text-sm font-medium tracking-wide\">{data.profile.subtitle}</div>\r\n\r\n        <hr className=\"my-4 opacity-50\" />\r\n\r\n        <ReactMarkdown className=\"text-sm\" source={data.objective.body} />\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const ContactItem = ({ icon, value, link = '#' }) =>\r\n    value && (\r\n      <div className=\"flex items-center my-3\">\r\n        <span className=\"material-icons text-lg mr-2\" style={{ color: theme.colors.accent }}>\r\n          {icon}\r\n        </span>\r\n        <a href={link}>\r\n          <span className=\"font-medium break-all\">{value}</span>\r\n        </a>\r\n      </div>\r\n    );\r\n\r\n  const Heading = ({ title }) => (\r\n    <div\r\n      className=\"mb-2 border-b-2 pb-1 font-bold uppercase tracking-wide text-sm\"\r\n      style={{ color: theme.colors.accent, borderColor: theme.colors.accent }}\r\n    >\r\n      {title}\r\n    </div>\r\n  );\r\n\r\n  const SkillItem = x => (\r\n    <span\r\n      key={x.id}\r\n      className=\"leading-none rounded-lg text-sm font-medium bg-gray-300 py-3 my-1 px-4\"\r\n    >\r\n      {x.skill}\r\n    </span>\r\n  );\r\n\r\n  const Skills = () =>\r\n    data.skills &&\r\n    data.skills.enable && (\r\n      <div>\r\n        <Heading title={data.skills.heading} />\r\n        <div className=\"flex flex-col mb-6\">{data.skills.items.map(SkillItem)}</div>\r\n      </div>\r\n    );\r\n\r\n  const HobbyItem = x => (\r\n    <span\r\n      key={x.id}\r\n      className=\"leading-none rounded-lg text-sm font-medium bg-gray-300 py-3 my-1 px-4\"\r\n    >\r\n      {x.hobby}\r\n    </span>\r\n  );\r\n\r\n  const Memberships = () =>\r\n    data.hobbies &&\r\n    data.hobbies.enable && (\r\n      <div>\r\n        <Heading title={data.hobbies.heading} />\r\n        <div className=\"flex flex-col mb-6\">{data.hobbies.items.map(HobbyItem)}</div>\r\n      </div>\r\n    );\r\n\r\n  const ReferenceItem = x => (\r\n    <div key={x.id} className=\"flex flex-col\">\r\n      <h6 className=\"text-sm font-medium\">{x.name}</h6>\r\n      <span className=\"text-xs\">{x.position}</span>\r\n      <span className=\"text-xs\">{x.phone}</span>\r\n      <span className=\"text-xs\">{x.email}</span>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const References = () =>\r\n    data.references &&\r\n    data.references.enable && (\r\n      <div>\r\n        <Heading title={data.references.heading} />\r\n        <div className=\"grid grid-cols-2 gap-2 mb-6\">\r\n          {data.references.items.filter(x => x.enable).map(ReferenceItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const LanguageItem = x => (\r\n    <div key={x.id} className=\"grid grid-cols-2 items-center py-2\">\r\n      <h6 className=\"text-sm font-medium\">{x.key}</h6>\r\n      <div className=\"flex\">\r\n        {x.level && <div className=\"font-bold text-sm mr-2\">{x.level}</div>}\r\n        {x.rating !== 0 && (\r\n          <div className=\"flex\">\r\n            {Array.from(Array(x.rating)).map((_, i) => (\r\n              <i key={i} className=\"material-icons text-lg\" style={{ color: theme.colors.accent }}>\r\n                star\r\n              </i>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const Languages = () =>\r\n    data.languages &&\r\n    data.languages.enable && (\r\n      <div>\r\n        <Heading title={data.languages.heading} />\r\n        <div className=\"mb-6\">{data.languages.items.filter(x => x.enable).map(LanguageItem)}</div>\r\n      </div>\r\n    );\r\n\r\n  const ExtraItem = x => (\r\n    <div key={x.id} className=\"text-sm my-1\">\r\n      <h6 className=\"text-xs font-bold\">{x.key}</h6>\r\n      <h6 className=\"\">{x.value}</h6>\r\n    </div>\r\n  );\r\n\r\n  const Extras = () =>\r\n    data.extras &&\r\n    data.extras.enable && (\r\n      <div>\r\n        <Heading title={data.extras.heading} />\r\n        <div className=\"grid grid-cols-2\">\r\n          {data.extras.items.filter(x => x.enable).map(ExtraItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const WorkItem = x => (\r\n    <div key={x.id} className=\"mb-3\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h6 className=\"font-semibold\">{x.title}</h6>\r\n          <p className=\"text-xs\">{x.role}</p>\r\n        </div>\r\n        <span className=\"text-xs font-medium\">\r\n          ({x.start} - {x.end})\r\n        </span>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Work = () =>\r\n    data.work &&\r\n    data.work.enable && (\r\n      <div>\r\n        <Heading title={data.work.heading} />\r\n        <div className=\"flex flex-col mb-4\">\r\n          {data.work.items.filter(x => x.enable).map(WorkItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const EducationItem = x => (\r\n    <div key={x.id} className=\"mb-2\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h6 className=\"font-semibold\">{x.name}</h6>\r\n          <p className=\"text-xs\">{x.major}</p>\r\n        </div>\r\n        <div className=\"flex flex-col text-right items-end\">\r\n          <span className=\"text-sm font-bold\" style={{ color: theme.colors.accent }}>\r\n            {x.grade}\r\n          </span>\r\n          <span className=\"text-xs font-medium\">\r\n            ({x.start} - {x.end})\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Education = () =>\r\n    data.education &&\r\n    data.education.enable && (\r\n      <div>\r\n        <Heading title={data.education.heading} />\r\n        <div className=\"flex flex-col mb-4\">\r\n          {data.education.items.filter(x => x.enable).map(EducationItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const AwardItem = x => (\r\n    <div key={x.id} className=\"mb-2\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Awards = () =>\r\n    data.awards &&\r\n    data.awards.enable && (\r\n      <div>\r\n        <Heading title={data.awards.heading} />\r\n        <div className=\"flex flex-col mb-2\">\r\n          {data.awards.items.filter(x => x.enable).map(AwardItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const CertificationItem = x => (\r\n    <div key={x.id} className=\"mb-3\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Certifications = () =>\r\n    data.certifications &&\r\n    data.certifications.enable && (\r\n      <div>\r\n        <Heading title={data.certifications.heading} />\r\n        <div className=\"flex flex-col mb-2\">\r\n          {data.certifications.items.filter(x => x.enable).map(CertificationItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  return (\r\n    <div\r\n      className=\"p-10\"\r\n      style={{\r\n        fontFamily: theme.font.family,\r\n        backgroundColor: theme.colors.background,\r\n        color: theme.colors.primary,\r\n      }}\r\n    >\r\n      <div className=\"grid grid-cols-12 col-gap-6 row-gap-8\">\r\n        <Photo />\r\n\r\n        <div className={`${data.profile.photo !== '' ? 'col-span-8' : 'col-span-12'}`}>\r\n          <Header />\r\n        </div>\r\n\r\n        <div className=\"col-span-4 overflow-hidden\">\r\n          <div className=\"text-sm mb-6\">\r\n            <ContactItem\r\n              icon=\"phone\"\r\n              value={data.profile.phone}\r\n              link={`tel:${data.profile.phone}`}\r\n            />\r\n            <ContactItem\r\n              icon=\"language\"\r\n              value={data.profile.website}\r\n              link={`http://${data.profile.website}`}\r\n            />\r\n            <ContactItem\r\n              icon=\"email\"\r\n              value={data.profile.email}\r\n              link={`mailto:${data.profile.email}`}\r\n            />\r\n            <ContactItem icon=\"location_on\" value={data.profile.address.line3} />\r\n          </div>\r\n\r\n          <Skills />\r\n          <Memberships />\r\n          <Languages />\r\n          <Certifications />\r\n        </div>\r\n\r\n        <div className=\"col-span-8\">\r\n          <Work />\r\n          <Education />\r\n          <Awards />\r\n          <References />\r\n          <Extras />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Pikachu;\r\n", "import Pikachu from './Pikachu';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Pikachu;\r\n", "import React, { useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport { hexToRgb } from '../../utils';\r\n\r\nconst Gengar = () => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  const { r, g, b } = hexToRgb(theme.colors.accent) || {};\r\n\r\n  const Photo = () =>\r\n    data.profile.photo !== '' && (\r\n      <img\r\n        className=\"w-24 h-24 rounded-full mr-4 object-cover border-4\"\r\n        style={{\r\n          borderColor: theme.colors.background,\r\n        }}\r\n        src={data.profile.photo}\r\n        alt=\"Resume Photograph\"\r\n      />\r\n    );\r\n\r\n  const FullName = () => (\r\n    <div>\r\n      <h1 className=\"text-2xl font-bold leading-tight\">{data.profile.firstName}</h1>\r\n      <h1 className=\"text-2xl font-bold leading-tight\">{data.profile.lastName}</h1>\r\n      <div className=\"text-xs font-medium mt-2\">{data.profile.subtitle}</div>\r\n    </div>\r\n  );\r\n\r\n  const ContactItem = ({ icon, value, link = '#' }) =>\r\n    value && (\r\n      <div className=\"flex items-center mb-3\">\r\n        <div\r\n          className=\"w-5 h-5 rounded-full flex justify-center items-center mr-2\"\r\n          style={{ backgroundColor: theme.colors.background }}\r\n        >\r\n          <i\r\n            className=\"flex justify-center items-center material-icons text-xs\"\r\n            style={{ color: theme.colors.accent }}\r\n          >\r\n            {icon}\r\n          </i>\r\n        </div>\r\n        <a href={link}>\r\n          <span className=\"text-sm font-medium break-all\">{value}</span>\r\n        </a>\r\n      </div>\r\n    );\r\n\r\n  const Heading = ({ title }) => (\r\n    <h6 className=\"font-bold text-xs uppercase tracking-wide mb-2\">{title}</h6>\r\n  );\r\n\r\n  const Objective = () =>\r\n    data.objective &&\r\n    data.objective.enable && (\r\n      <div className=\"flex flex-col justify-center items-start mb-6\">\r\n        <Heading title={data.objective.heading} />\r\n        <ReactMarkdown className=\"text-sm\" source={data.objective.body} />\r\n      </div>\r\n    );\r\n\r\n  const SkillItem = x => (\r\n    <li key={x.id} className=\"text-sm py-1\">\r\n      {x.skill}\r\n    </li>\r\n  );\r\n\r\n  const Skills = () =>\r\n    data.skills &&\r\n    data.skills.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.skills.heading} />\r\n        <ul>{data.skills.items.map(SkillItem)}</ul>\r\n      </div>\r\n    );\r\n\r\n  const HobbyItem = x => (\r\n    <li key={x.id} className=\"text-sm py-1\">\r\n      {x.hobby}\r\n    </li>\r\n  );\r\n\r\n  const Memberships = () =>\r\n    data.hobbies &&\r\n    data.hobbies.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.hobbies.heading} />\r\n        <ul>{data.hobbies.items.map(HobbyItem)}</ul>\r\n      </div>\r\n    );\r\n\r\n  const EducationItem = x => (\r\n    <div key={x.id} className=\"mb-3\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h6 className=\"font-semibold\">\r\n            {x.name}\r\n            <small className=\"ml-2\">\r\n              {x.start !== '' && x.end !== '' && (\r\n                <span className=\"text-xs font-medium\">\r\n                  ({x.start} - {x.end})\r\n                </span>\r\n              )}\r\n            </small>\r\n          </h6>\r\n          <p className=\"text-xs\">{x.major}</p>\r\n        </div>\r\n        <div className=\"flex flex-col text-right items-end\">\r\n          <span className=\"text-sm font-bold\" style={{ color: theme.colors.accent }}>\r\n            {x.grade}\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Education = () =>\r\n    data.education &&\r\n    data.education.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.education.heading} />\r\n        {data.education.items.filter(x => x.enable).map(EducationItem)}\r\n      </div>\r\n    );\r\n\r\n  const CertificationItem = x => (\r\n    <div key={x.id} className=\"mb-3\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Certifications = () =>\r\n    data.certifications &&\r\n    data.certifications.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.certifications.heading} />\r\n        {data.certifications.items.filter(x => x.enable).map(CertificationItem)}\r\n      </div>\r\n    );\r\n\r\n  const AwardItem = x => (\r\n    <div key={x.id} className=\"mb-3\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Awards = () =>\r\n    data.awards &&\r\n    data.awards.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.awards.heading} />\r\n        {data.awards.items.filter(x => x.enable).map(AwardItem)}\r\n      </div>\r\n    );\r\n\r\n  const ReferenceItem = x => (\r\n    <div key={x.id} className=\"flex flex-col\">\r\n      <h6 className=\"text-sm font-medium\">{x.name}</h6>\r\n      <span className=\"text-xs\">{x.position}</span>\r\n      <span className=\"text-xs\">{x.phone}</span>\r\n      <span className=\"text-xs\">{x.email}</span>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const References = () =>\r\n    data.references &&\r\n    data.references.enable && (\r\n      <div>\r\n        <Heading title={data.references.heading} />\r\n        <div className=\"grid grid-cols-2 gap-6\">\r\n          {data.references.items.filter(x => x.enable).map(ReferenceItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const WorkItem = x => (\r\n    <div key={x.id} className=\"mb-3\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div>\r\n          <h6 className=\"font-semibold\">{x.title}</h6>\r\n          <p className=\"text-xs\">{x.role}</p>\r\n        </div>\r\n        <span className=\"text-xs font-medium\">\r\n          ({x.start} - {x.end})\r\n        </span>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Work = () =>\r\n    data.work &&\r\n    data.work.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.work.heading} />\r\n        {data.work.items.filter(x => x.enable).map(WorkItem)}\r\n      </div>\r\n    );\r\n\r\n  const LanguageItem = x => (\r\n    <div key={x.id} className=\"grid grid-cols-2 items-center py-2\">\r\n      <h6 className=\"text-sm font-medium\">{x.key}</h6>\r\n      <div className=\"flex\">\r\n        {x.level && <div className=\"font-bold text-sm mr-2\">{x.level}</div>}\r\n        {x.rating !== 0 && (\r\n          <div className=\"flex\">\r\n            {Array.from(Array(x.rating)).map((_, i) => (\r\n              <i key={i} className=\"material-icons text-lg\" style={{ color: theme.colors.accent }}>\r\n                star\r\n              </i>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const Languages = () =>\r\n    data.languages &&\r\n    data.languages.enable && (\r\n      <div>\r\n        <Heading title={data.languages.heading} />\r\n        <div className=\"mb-6\">{data.languages.items.filter(x => x.enable).map(LanguageItem)}</div>\r\n      </div>\r\n    );\r\n\r\n  const ExtraItem = x => (\r\n    <div key={x.id} className=\"text-sm my-1\">\r\n      <h6 className=\"text-xs font-bold\">{x.key}</h6>\r\n      <h6>{x.value}</h6>\r\n    </div>\r\n  );\r\n\r\n  const Extras = () =>\r\n    data.extras &&\r\n    data.extras.enable && (\r\n      <div>\r\n        <Heading title={data.extras.heading} />\r\n        <div className=\"grid grid-cols-2\">\r\n          {data.extras.items.filter(x => x.enable).map(ExtraItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        fontFamily: theme.font.family,\r\n        backgroundColor: theme.colors.background,\r\n        color: theme.colors.primary,\r\n      }}\r\n    >\r\n      <div className=\"grid grid-cols-12\">\r\n        <div\r\n          className=\"col-span-4 px-6 py-8\"\r\n          style={{ backgroundColor: theme.colors.accent, color: theme.colors.background }}\r\n        >\r\n          <div className=\"flex items-center\">\r\n            <Photo />\r\n            <FullName />\r\n          </div>\r\n\r\n          <hr className=\"w-1/4 my-5 opacity-50\" />\r\n\r\n          <ContactItem icon=\"phone\" value={data.profile.phone} link={`tel:${data.profile.phone}`} />\r\n          <ContactItem\r\n            icon=\"email\"\r\n            value={data.profile.email}\r\n            link={`mailto:${data.profile.email}`}\r\n          />\r\n          <ContactItem\r\n            icon=\"language\"\r\n            value={data.profile.website}\r\n            link={`http://${data.profile.website}`}\r\n          />\r\n          <ContactItem icon=\"location_on\" value={data.profile.address.line3} />\r\n        </div>\r\n\r\n        <div\r\n          className=\"col-span-8 px-6 py-8\"\r\n          style={{ backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)` }}\r\n        >\r\n          <Objective />\r\n          <Extras />\r\n        </div>\r\n\r\n        <div\r\n          className=\"col-span-4 px-6 py-8\"\r\n          style={{ backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)` }}\r\n        >\r\n          <Skills />\r\n          <Memberships />\r\n          <Languages />\r\n          <Education />\r\n          <Certifications />\r\n        </div>\r\n\r\n        <div className=\"col-span-8 px-6 py-8\">\r\n          <Work />\r\n          <Awards />\r\n          <References />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Gengar;\r\n", "import Gengar from './Gengar';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Gengar;\r\n", "import React, { useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\n\r\nimport AppContext from '../../context/AppContext';\r\n\r\nconst Castform = () => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  const Photo = () =>\r\n    data.profile.photo !== '' && (\r\n      <div className=\"mt-5 ml-5\">\r\n        <img\r\n          className=\"w-32 h-32 rounded-full\"\r\n          style={{\r\n            borderWidth: 6,\r\n            borderColor: theme.colors.background,\r\n          }}\r\n          src={data.profile.photo}\r\n          alt=\"Profile Photograph\"\r\n        />\r\n      </div>\r\n    );\r\n\r\n  const PersonalInformation = () => (\r\n    <div className=\"pt-5 px-5\">\r\n      <h1 className=\"text-2xl font-bold\">\r\n        {data.profile.firstName} {data.profile.lastName}\r\n      </h1>\r\n      <h5>{data.profile.subtitle}</h5>\r\n    </div>\r\n  );\r\n\r\n  const Heading = ({ title, light = false }) => (\r\n    <div\r\n      className={`py-2 my-4 ${light ? 'mx-5 border-t border-b border-gray-400' : ''}`}\r\n      style={{ backgroundColor: light ? '' : 'rgba(0, 0, 0, 0.25)' }}\r\n    >\r\n      <h6 className={`${light ? '' : 'pl-5'} font-semibold`}>{title}</h6>\r\n    </div>\r\n  );\r\n\r\n  const Address = () => (\r\n    <div className=\"px-5 my-2\">\r\n      <h6 className=\"text-xs font-bold\">Address</h6>\r\n      <div className=\"text-sm\">{data.profile.address.line1}</div>\r\n      <div className=\"text-sm\">{data.profile.address.line2}</div>\r\n      <div className=\"text-sm\">{data.profile.address.line3}</div>\r\n    </div>\r\n  );\r\n\r\n  const ContactItem = ({ title, value, link = '#' }) =>\r\n    value && (\r\n      <div className=\"px-5 my-2\">\r\n        <h6 className=\"text-xs font-bold\">{title}</h6>\r\n        <a href={link}>\r\n          <div className=\"text-sm\">{value}</div>\r\n        </a>\r\n      </div>\r\n    );\r\n\r\n  const ContactInformation = () => (\r\n    <div>\r\n      <Heading title={data.profile.heading} />\r\n      <Address />\r\n      <ContactItem title=\"Phone\" value={data.profile.phone} link={`tel:${data.profile.phone}`} />\r\n      <ContactItem\r\n        title=\"Email Address\"\r\n        value={data.profile.email}\r\n        link={`mailto:${data.profile.email}`}\r\n      />\r\n      <ContactItem\r\n        title=\"Website\"\r\n        value={data.profile.website}\r\n        link={`http://${data.profile.website}`}\r\n      />\r\n    </div>\r\n  );\r\n\r\n  const SkillItem = x => (\r\n    <li key={x.id} className=\"text-sm my-2\">\r\n      {x.skill}\r\n    </li>\r\n  );\r\n\r\n  const Skills = () =>\r\n    data.skills &&\r\n    data.skills.enable && (\r\n      <div>\r\n        <Heading title={data.skills.heading} />\r\n        <ul className=\"list-none px-5\">{data.skills.items.map(SkillItem)}</ul>\r\n      </div>\r\n    );\r\n\r\n  const HobbyItem = x => (\r\n    <li key={x.id} className=\"text-sm my-2\">\r\n      {x.hobby}\r\n    </li>\r\n  );\r\n\r\n  const Memberships = () =>\r\n    data.memberships &&\r\n    data.memberships.enable && (\r\n      <div>\r\n        <Heading title={data.memberships.heading} />\r\n        <ul className=\"list-none px-5\">{data.memberships.items.map(HobbyItem)}</ul>\r\n      </div>\r\n    );\r\n\r\n  const Objective = () =>\r\n    data.objective && data.objective.enable && <ReactMarkdown className=\"m-5 text-sm\" source={data.objective.body} />;\r\n\r\n  const WorkItem = x => (\r\n    <div key={x.id} className=\"my-3 px-5\">\r\n      <div className=\"flex justify-between\">\r\n        <div>\r\n          <h6 className=\"font-semibold\">{x.title}</h6>\r\n          <p className=\"text-xs\">{x.role}</p>\r\n        </div>\r\n        <span className=\"text-xs font-medium\">\r\n          ({x.start} - {x.end})\r\n        </span>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Work = () =>\r\n    data.work &&\r\n    data.work.enable && (\r\n      <div>\r\n        <Heading light title={data.work.heading} />\r\n        {data.work.items.filter(x => x.enable).map(WorkItem)}\r\n      </div>\r\n    );\r\n\r\n  const ReferenceItem = x => (\r\n    <div key={x.id} className=\"flex flex-col\">\r\n      <h6 className=\"text-sm font-medium\">{x.name}</h6>\r\n      <span className=\"text-xs\">{x.position}</span>\r\n      <span className=\"text-xs\">{x.phone}</span>\r\n      <span className=\"text-xs\">{x.email}</span>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const References = () =>\r\n    data.references &&\r\n    data.references.enable && (\r\n      <div>\r\n        <Heading light title={data.references.heading} />\r\n        <div className=\"grid grid-cols-2 gap-6 px-5\">\r\n          {data.references.items.filter(x => x.enable).map(ReferenceItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const LanguageItem = x => (\r\n    <div key={x.id} className=\"flex flex-col my-2\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h6 className=\"text-sm font-medium mb-1\">{x.key}</h6>\r\n        {x.level !== '' && <div className=\"font-bold text-sm\">{x.level}</div>}\r\n      </div>\r\n\r\n      {x.rating !== 0 && (\r\n        <div className=\"relative h-5\">\r\n          <div\r\n            className=\"absolute mb-1 inset-0\"\r\n            style={{\r\n              backgroundColor: 'rgba(0, 0, 0, 0.25)',\r\n            }}\r\n          />\r\n          <div\r\n            className=\"absolute mb-1 inset-0 rounded\"\r\n            style={{\r\n              width: `${x.rating * 20}%`,\r\n              backgroundColor: 'rgba(0, 0, 0, 0.3)',\r\n            }}\r\n          />\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  const Languages = () =>\r\n    data.languages &&\r\n    data.languages.enable && (\r\n      <div>\r\n        <Heading title={data.languages.heading} />\r\n        <div className=\"px-5 mb-6\">\r\n          {data.languages.items.filter(x => x.enable).map(LanguageItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const EducationItem = x => (\r\n    <div key={x.id} className=\"my-3 px-5\">\r\n      <div className=\"flex justify-between\">\r\n        <div>\r\n          <h6 className=\"font-semibold\">{x.name}</h6>\r\n          <p className=\"text-xs\">{x.major}</p>\r\n        </div>\r\n        <div className=\"flex flex-col items-end\">\r\n          <span className=\"text-sm font-bold\">{x.grade}</span>\r\n          <span className=\"text-xs font-medium\">\r\n            ({x.start} - {x.end})\r\n          </span>\r\n        </div>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Education = () =>\r\n    data.education &&\r\n    data.education.enable && (\r\n      <div>\r\n        <Heading light title={data.education.heading} />\r\n        {data.education.items.filter(x => x.enable).map(EducationItem)}\r\n      </div>\r\n    );\r\n\r\n  const AwardItem = x => (\r\n    <div key={x.id} className=\"my-3 px-5\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Awards = () =>\r\n    data.awards &&\r\n    data.awards.enable && (\r\n      <div>\r\n        <Heading light title={data.awards.heading} />\r\n        {data.awards.items.filter(x => x.enable).map(AwardItem)}\r\n      </div>\r\n    );\r\n\r\n  const CertificationItem = x => (\r\n    <div key={x.id} className=\"my-3 px-5\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Certifications = () =>\r\n    data.certifications &&\r\n    data.certifications.enable && (\r\n      <div>\r\n        <Heading title={data.certifications.heading} />\r\n        {data.certifications.items.filter(x => x.enable).map(CertificationItem)}\r\n      </div>\r\n    );\r\n\r\n  const ExtraItem = x => (\r\n    <div key={x.id} className=\"px-5 my-2\">\r\n      <h6 className=\"text-xs font-bold\">{x.key}</h6>\r\n      <div className=\"text-sm\">{x.value}</div>\r\n    </div>\r\n  );\r\n\r\n  const Extras = () =>\r\n    data.extras &&\r\n    data.extras.enable && (\r\n      <div>\r\n        <Heading light title={data.extras.heading} />\r\n        {data.extras.items.filter(x => x.enable).map(ExtraItem)}\r\n      </div>\r\n    );\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        fontFamily: theme.font.family,\r\n        backgroundColor: theme.colors.background,\r\n        color: theme.colors.primary,\r\n      }}\r\n    >\r\n      <div className=\"grid grid-cols-12\">\r\n        <div\r\n          className=\"col-span-4\"\r\n          style={{\r\n            color: theme.colors.background,\r\n            backgroundColor: theme.colors.accent,\r\n          }}\r\n        >\r\n          <Photo />\r\n          <PersonalInformation />\r\n          <ContactInformation />\r\n          <Skills />\r\n          <Memberships />\r\n          <Languages />\r\n          <Certifications />\r\n        </div>\r\n        <div className=\"col-span-8\">\r\n          <Objective />\r\n          <Work />\r\n          <Education />\r\n          <Awards />\r\n          <References />\r\n          <Extras />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Castform;\r\n", "import Castform from './Castform';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Castform;\r\n", "import React, { useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport { hexToRgb } from '../../utils';\r\n\r\nconst Glalie = () => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  const { r, g, b } = hexToRgb(theme.colors.accent) || {};\r\n\r\n  const Photo = () =>\r\n    data.profile.photo !== '' && (\r\n      <img\r\n        className=\"w-40 h-40 rounded-full mx-auto\"\r\n        src={data.profile.photo}\r\n        alt=\"Resume Photograph\"\r\n      />\r\n    );\r\n\r\n  const FullName = () => (\r\n    <div className=\"text-4xl font-bold leading-none\">\r\n      <h1>{data.profile.firstName}</h1>\r\n      <h1>{data.profile.lastName}</h1>\r\n    </div>\r\n  );\r\n\r\n  const Subtitle = () => (\r\n    <div className=\"tracking-wide text-xs uppercase font-medium\">{data.profile.subtitle}</div>\r\n  );\r\n\r\n  const ContactItem = ({ title, value }) =>\r\n    value && (\r\n      <div className=\"flex flex-col\">\r\n        <h6 className=\"text-xs font-bold\" style={{ color: theme.colors.accent }}>\r\n          {title}\r\n        </h6>\r\n        <p className=\"text-sm\">{value}</p>\r\n      </div>\r\n    );\r\n\r\n  const ContactInformation = () => (\r\n    <div\r\n      className=\"w-full border-2 pl-4 pr-4 mb-6\"\r\n      style={{\r\n        borderColor: theme.colors.accent,\r\n      }}\r\n    >\r\n      <div\r\n        className=\"inline-block relative px-4\"\r\n        style={{ top: '-.75em', color: theme.colors.accent }}\r\n      >\r\n        <h2 className=\"flex\">\r\n          <i className=\"material-icons\">flare</i>\r\n        </h2>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 row-gap-4\">\r\n        <ContactItem title=\"Phone Number\" value={data.profile.phone} />\r\n        <ContactItem title=\"Email Address\" value={data.profile.email} />\r\n        <ContactItem title=\"Website\" value={data.profile.website} />\r\n\r\n        <div className=\"flex flex-col\">\r\n          <i className=\"material-icons text-lg\" style={{ color: theme.colors.accent }}>\r\n            home\r\n          </i>\r\n          <p className=\"text-sm\">{data.profile.address.line1}</p>\r\n          <p className=\"text-sm\">{data.profile.address.line2}</p>\r\n          <p className=\"text-sm\">{data.profile.address.line3}</p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const Heading = ({ title }) => (\r\n    <h6\r\n      className=\"text-sm font-semibold uppercase pb-1 mb-2 border-b\"\r\n      style={{ borderColor: theme.colors.accent, color: theme.colors.accent }}\r\n    >\r\n      {title}\r\n    </h6>\r\n  );\r\n\r\n  const Objective = () =>\r\n    data.objective.enable && (\r\n      <div>\r\n        <Heading title={data.objective.heading} />\r\n        <ReactMarkdown className=\"text-sm text-justify\" source={data.objective.body} />\r\n      </div>\r\n    );\r\n\r\n  const WorkItem = x => (\r\n    <div key={x.id} className=\"mt-3\">\r\n      <div className=\"flex justify-between\">\r\n        <div>\r\n          <h6 className=\"font-semibold text-sm\">{x.title}</h6>\r\n          <p className=\"text-xs opacity-75 font-medium\">\r\n            {x.role} / {x.start} - {x.end}\r\n          </p>\r\n        </div>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Work = () =>\r\n    data.work &&\r\n    data.work.enable && (\r\n      <div>\r\n        <Heading title={data.work.heading} />\r\n        {data.work.items.filter(x => x.enable).map(WorkItem)}\r\n      </div>\r\n    );\r\n\r\n  const EducationItem = x => (\r\n    <div key={x.id} className=\"mt-3\">\r\n      <div>\r\n        <h6 className=\"font-semibold text-xs\">{x.name}</h6>\r\n        <p className=\"text-xs opacity-75\">{x.major}</p>\r\n        <p className=\"text-xs opacity-75\">\r\n          {x.start} - {x.end}\r\n        </p>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Education = () =>\r\n    data.education &&\r\n    data.education.enable && (\r\n      <div>\r\n        <Heading title={data.education.heading} />\r\n        <div className=\"grid grid-cols-2 gap-4\">\r\n          {data.education.items.filter(x => x.enable).map(EducationItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const AwardItem = x => (\r\n    <div key={x.id} className=\"mt-3 text-left\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Awards = () =>\r\n    data.awards &&\r\n    data.awards.enable && (\r\n      <div>\r\n        <Heading title={data.awards.heading} />\r\n        {data.awards.items.filter(x => x.enable).map(AwardItem)}\r\n      </div>\r\n    );\r\n\r\n  const CertificationItem = x => (\r\n    <div key={x.id} className=\"mt-3 text-left\">\r\n      <h6 className=\"font-semibold\">{x.title}</h6>\r\n      <p className=\"text-xs\">{x.subtitle}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const Certifications = () =>\r\n    data.certifications &&\r\n    data.certifications.enable && (\r\n      <div>\r\n        <Heading title={data.certifications.heading} />\r\n        {data.certifications.items.filter(x => x.enable).map(CertificationItem)}\r\n      </div>\r\n    );\r\n\r\n  const SkillItem = x => (\r\n    <li key={x.id} className=\"text-xs font-medium\">\r\n      {x.skill}\r\n    </li>\r\n  );\r\n\r\n  const Skills = () =>\r\n    data.skills &&\r\n    data.skills.enable && (\r\n      <div>\r\n        <Heading title={data.skills.heading} />\r\n        <ul className=\"pt-2 grid grid-cols-2 gap-3\">{data.skills.items.map(SkillItem)}</ul>\r\n      </div>\r\n    );\r\n\r\n  const HobbyItem = x => (\r\n    <li key={x.id} className=\"text-xs font-medium\">\r\n      {x.hobby}\r\n    </li>\r\n  );\r\n\r\n  const Memberships = () =>\r\n    data.hobbies &&\r\n    data.hobbies.enable && (\r\n      <div>\r\n        <Heading title={data.hobbies.heading} />\r\n        <ul className=\"pt-2 grid grid-cols-2 row-gap-3 text-left\">\r\n          {data.hobbies.items.map(HobbyItem)}\r\n        </ul>\r\n      </div>\r\n    );\r\n\r\n  const LanguageItem = x => (\r\n    <div key={x.id} className=\"grid grid-cols-2 items-center py-2\">\r\n      <h6 className=\"text-xs font-medium text-left\">{x.key}</h6>\r\n      <div className=\"flex\">\r\n        {x.level && <div className=\"font-bold text-sm mr-2\">{x.level}</div>}\r\n        {x.rating !== 0 && (\r\n          <div className=\"flex\">\r\n            {Array.from(Array(x.rating)).map((_, i) => (\r\n              <i key={i} className=\"material-icons text-lg\" style={{ color: theme.colors.accent }}>\r\n                star\r\n              </i>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const Languages = () =>\r\n    data.languages &&\r\n    data.languages.enable && (\r\n      <div>\r\n        <Heading title={data.languages.heading} />\r\n        <div className=\"w-3/4\">{data.languages.items.filter(x => x.enable).map(LanguageItem)}</div>\r\n      </div>\r\n    );\r\n\r\n  const ReferenceItem = x => (\r\n    <div key={x.id} className=\"flex flex-col\">\r\n      <h6 className=\"text-sm font-medium\">{x.name}</h6>\r\n      <span className=\"text-xs\">{x.position}</span>\r\n      <span className=\"text-xs\">{x.phone}</span>\r\n      <span className=\"text-xs\">{x.email}</span>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={x.description} />\r\n    </div>\r\n  );\r\n\r\n  const References = () =>\r\n    data.references &&\r\n    data.references.enable && (\r\n      <div>\r\n        <Heading title={data.references.heading} />\r\n        <div className=\"grid grid-cols-3 gap-8\">\r\n          {data.references.items.filter(x => x.enable).map(ReferenceItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const ExtraItem = x => (\r\n    <tr key={x.id}>\r\n      <td className=\"border font-medium px-4 py-2 text-xs\">{x.key}</td>\r\n      <td className=\"border px-4 py-2 text-xs\">{x.value}</td>\r\n    </tr>\r\n  );\r\n\r\n  const Extras = () =>\r\n    data.extras &&\r\n    data.extras.enable && (\r\n      <div>\r\n        <Heading title={data.extras.heading} />\r\n        <table className=\"mt-4 w-2/3 table-auto\">\r\n          <tbody>{data.extras.items.filter(x => x.enable).map(ExtraItem)}</tbody>\r\n        </table>\r\n      </div>\r\n    );\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        fontFamily: theme.font.family,\r\n        backgroundColor: theme.colors.background,\r\n        color: theme.colors.primary,\r\n      }}\r\n    >\r\n      <div className=\"grid grid-cols-12\">\r\n        <div\r\n          className=\"h-full col-span-4 p-8 grid grid-cols-1 row-gap-4 text-center\"\r\n          style={{ backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)`, minHeight: '29.7cm' }}\r\n        >\r\n          <div className=\"grid grid-cols-1 gap-2\">\r\n            <Photo />\r\n            <FullName />\r\n            <Subtitle />\r\n          </div>\r\n          <ContactInformation />\r\n          <Objective />\r\n          <Memberships />\r\n          <Languages />\r\n          <Certifications />\r\n        </div>\r\n\r\n        <div className=\"col-span-8 p-8 grid grid-cols-1 row-gap-4\">\r\n          <Work />\r\n          <Education />\r\n          <Skills />\r\n          <Awards />\r\n          <References />\r\n          <Extras />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Glalie;\r\n", "import Glalie from './Glalie';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Glalie;\r\n", "import React, { useContext } from 'react';\r\n\r\nimport ReactMarkdown from 'react-markdown';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport { hexToRgb } from '../../utils';\r\n\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport * as _  from 'lodash';\r\n\r\nconst styles = {\r\n  header: {\r\n    position: 'absolute',\r\n    left: 0,\r\n    right: 0,\r\n    zIndex: 0,\r\n    display: 'flex',\r\n    flexDirection: 'column',\r\n    justifyContent: 'center',\r\n    alignItems: 'start',\r\n    color: 'white',\r\n    backgroundColor: '#222',\r\n    height: '160px',\r\n    paddingLeft: '270px',\r\n  },\r\n  section: {\r\n    marginTop: '167px',\r\n    marginLeft: '20px',\r\n  },\r\n};\r\n\r\nconst Celebi = () => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  const { r, g, b } = hexToRgb(theme.colors.accent) || {};\r\n\r\n  const Heading = ({ title, className }) => (\r\n    <h5\r\n      className={`my-2 text-md uppercase font-semibold tracking-wider pb-1 border-b-2 border-gray-800 ${className}`}\r\n    >\r\n      {title}\r\n    </h5>\r\n  );\r\n\r\n  const Photo = () =>\r\n    (_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\") !== '' && (\r\n      <div className=\"relative z-40\">\r\n        <img\r\n          className=\"w-full object-cover object-center\"\r\n          src={_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\")}\r\n          alt=\"Person Photograph\"\r\n          style={{\r\n            height: '160px',\r\n          }}\r\n        />\r\n      </div>\r\n    )) || (\r\n      <div className=\"relative z-40\">\r\n        <div style={{\r\n            height: '160px',\r\n          }}>\r\n        </div>\r\n      </div>\r\n    );\r\n  \r\n  const Subnames = () => (\r\n    <h6 className=\"text-lg tracking-wider uppercase\">{((data.jsonld['@graph'][1].givenName[1]) ? (\" (\"+data.jsonld['@graph'][1].givenName.map(function(elem,index){\r\n              if(index > 0 && elem['@value']){\r\n                let name = elem['@value'];\r\n                let familynameIndex = data.jsonld['@graph'][1].familyName.findIndex(x=>x['@language']===elem['@language']);\r\n                if(familynameIndex >= 0){\r\n                  if(data.jsonld['@graph'][1].familyName[familynameIndex] && data.jsonld['@graph'][1].familyName[familynameIndex]['@value']){\r\n                    name += \" \"+data.jsonld['@graph'][1].familyName[familynameIndex]['@value'];\r\n                  }\r\n                }\r\n                return name;\r\n              }else{\r\n                return null;\r\n              }\r\n}).filter(function (el) {\r\n  return el != null;\r\n}).join(\", \")+\")\") : (\"\"))}\r\n</h6>\r\n  );\r\n  const Names = () => (\r\n    <h1 className=\"tracking-wide uppercase font-semibold\" style={{ fontSize: '2.75em' }}>\r\n          {(Array.isArray(data.jsonld['@graph'][1].givenName)) ? (data.jsonld['@graph'][1].givenName[0]['@value']) : (data.jsonld['@graph'][1].givenName)} {(Array.isArray(data.jsonld['@graph'][1].familyName)) ? (data.jsonld['@graph'][1].familyName[0]['@value']) : (data.jsonld['@graph'][1].familyName)}\r\n        </h1>\r\n  );\r\n  \r\n  const Header = () => (\r\n    <header style={styles.header}>\r\n      <div className=\"ml-6\">\r\n        <Names />\r\n        <Subnames />\r\n        <h6 className=\"text-lg tracking-wider uppercase\">{_.get(data, 'jsonld[\"@graph\"][1].description', \"\")}</h6>\r\n      </div>\r\n    </header>\r\n  );\r\n  \r\n  const Objective = () =>\r\n    data.objective &&\r\n    data.objective.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.objective.heading} />\r\n          {_.get(data, 'jsonld[\"@graph\"][1].seeks',[]).map((x, index) => (\r\n              <ReactMarkdown key={\"objetive_\"+index} className=\"mr-10 text-sm\" source={x.description} />\r\n          ))}\r\n          {_.get(data, 'jsonld[\"@graph\"][1].seeks',[]).map((x, index) => (\r\n            <div key={\"holder_\"+index}>\r\n              <p className=\"text-xs text-gray-800\" key={\"p_\"+index}>\r\n                {(_.get(x,'availableAtOrFrom.address.addressCountry', null) || _.get(x,'availableAtOrFrom.address.addressRegion', null) || _.get(x,'availableAtOrFrom.address.addressLocality', null)) ? \"\" : \"\"}\r\n                {(_.get(x,'availableAtOrFrom.address.addressLocality', null) ? (_.get(x,'availableAtOrFrom.address.addressLocality', '')+' ') : '')\r\n                +(_.get(x,'availableAtOrFrom.address.addressRegion', null) ? (_.get(x,'availableAtOrFrom.address.addressRegion', '')+' ') : '')\r\n                +(_.get(x,'availableAtOrFrom.address.addressCountry', null) ? (_.get(x,'availableAtOrFrom.address.addressCountry', '')) : '')} | {(_.get(x, 'availabilityStarts', null)) ? (_.get(x, 'availabilityStarts', '')) : \"\"} {_.get(x, 'availabilityEnds', null) ? (\"- \" +_.get(x,'availabilityEnds','')): \"\"}\r\n              </p>\r\n            </div>\r\n          ))}\r\n      </div>\r\n    );\r\n\r\n  const ContactItem = ({ label, value }) =>\r\n    value && (\r\n      <div className=\"mb-3\">\r\n        <h6 className=\"text-xs font-bold\">{label}</h6>\r\n        <p className=\"text-sm\">{value}</p>\r\n      </div>\r\n    );\r\n  \r\n  const Address = () => (\r\n    (\r\n      data.jsonld[\"@graph\"][1].address && data.jsonld[\"@graph\"][1].address.length>0 &&\r\n      data.address.enable && (\r\n        <div className=\"mb-6\">\r\n          {data.jsonld[\"@graph\"][1].address.filter(x => (Date.parse(x.hoursAvailable.validThrough) - Date.parse(new Date()))>0).map(AddressItem)}\r\n        </div>\r\n      )\r\n    ) || (\"\")\r\n  );\r\n  \r\n  const AddressItem = (x, index) => (\r\n    (\r\n        <div className=\"mb-3\" key={_.get(x,'@id', 'main')}>\r\n          {index===0?<h6 className=\"text-xs font-bold\">{data.profile.address.heading || \"Address\"}</h6>:\"\"}\r\n          <p className=\"text-sm\">{x.streetAddress}</p>\r\n          <p className=\"text-sm\">{x.addressLocality} {x.addressRegion}</p>\r\n          <p className=\"text-sm\">{x.addressCountry} {x.postalCode}</p>\r\n        </div>\r\n    )\r\n  );\r\n  const Contact = () => (\r\n    data.contacts.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title=\"Contact\" className=\"mt-8 w-3/4 mx-auto\" />\r\n        <Address />\r\n        <ContactItem label=\"Phone\" value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'telephone', \"\")} />\r\n        <ContactItem label=\"Email Address\" value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'email', \"\")} />\r\n        <ContactItem label=\"Website\" value={_.get(data,'jsonld[\"@graph\"][1].sameAs[0]',\"\")} />\r\n      </div>\r\n    )\r\n  );\r\n  \r\n  const SectionSkillsItem = x => (\r\n    x && (\r\n        <p key={uuidv4()}>| {x} </p>\r\n    )\r\n  )\r\n  \r\n  const SectionSkills = ({skills}) => (\r\n    skills && (skills.length>0) && (\r\n      <div className=\"text-xs text-gray-800 flex\">\r\n      {\r\n        skills.filter(x => (x !== '')).map(SectionSkillsItem)\r\n      }\r\n      </div>\r\n    )\r\n  )\r\n  \r\n  const WorkResponsibilityItem = x => (\r\n    x && (\r\n        <li className=\"mt-2 text-sm\" key={uuidv4()}>{x}</li>\r\n    )\r\n  )\r\n    \r\n  const WorkResponsibility = ({responsibilities}) => (\r\n    responsibilities && (responsibilities.length>0) && (\r\n      <ul>\r\n      {\r\n        responsibilities.filter(x => (x !== '')).map(WorkResponsibilityItem)\r\n      }\r\n      </ul>\r\n    )\r\n  )\r\n  \r\n  const WorkItem = x => (\r\n    <div key={_.get(x,'@id', 'main')} className=\"my-3 mr-10\">\r\n      <div>\r\n        <h6 className=\"font-semibold\">{_.get(x,'subjectOf.organizer.name','')}</h6>\r\n        <p className=\"text-xs text-gray-800\">\r\n          {_.get(x,'roleName', '')} | {_.get(x,'startDate','')} - {_.get(x,'endDate','')}\r\n        </p>\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={_.get(x,'description','')} />\r\n      <WorkResponsibility responsibilities={_.get(x, \"hasOccupation.responsibilities\", [])} />\r\n      <SectionSkills skills={_.get(x, \"hasOccupation.skills\", [])} />\r\n    </div>\r\n  );\r\n\r\n  const Work = () =>\r\n    _.get(data, \"jsonld['@graph'][1].hasOccupation\", []).length && data.work.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.work.heading} />\r\n        {_.get(data, \"jsonld['@graph'][1].hasOccupation\", []).filter(x => !_.get(x, '@id', '').endsWith(\"disable\")).map(WorkItem)}\r\n      </div>\r\n    );\r\n\r\n  const EducationItem = x => (\r\n    <div key={_.get(x,'@id', 'main')} className=\"my-3 mr-10\">\r\n      <h6 className=\"font-semibold\">{_.get(x, \"about.provider.name\", \"\")}</h6>\r\n      <p className=\"text-xs\">{_.get(x, \"educationalLevel\", \"\")} {_.get(x, \"about.educationalCredentialAwarded\", \"\")}</p>\r\n      <div className=\"text-xs\">\r\n        {_.get(x, \"about.startDate\", \"\")} - {_.get(x, \"about.endDate\", \"\")}\r\n      </div>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={_.get(x, 'abstract', '')} />\r\n      <SectionSkills skills={_.get(x, \"teaches\", [])} />\r\n    </div>\r\n  );\r\n\r\n  const Education = () =>\r\n    (_.get(data, \"jsonld['@graph'][1].hasCredential\", []).length>0) &&\r\n    data.education.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.education.heading} />\r\n        {_.get(data, \"jsonld['@graph'][1].hasCredential\", []).filter(x => (!_.get(x, '@id', '').endsWith(\"disable\") && _.get(x, 'credentialCategory', '')===\"degree\")).map(EducationItem)}\r\n      </div>\r\n    );\r\n  \r\n  const userSkills = () => {\r\n    let workSkills = _.chain(_.get(data, \"jsonld['@graph'][1].hasOccupation\", [])).map('skills').flatten();\r\n    \r\n    let awardSkills = _.chain(_.get(data, \"jsonld['@graph'][0].award\", [])).map('skill:assesses').flatten();\r\n    \r\n    let educationSkills = _.chain(_.get(data, \"jsonld['@graph'][1].hasCredential\", [])).map('teaches').flatten();\r\n    \r\n    let coursesSkills = _.chain(_.get(data, \"jsonld['@graph'][1].hasCredential\", [])).map('about').flatten().map('hasCourse').flatten().map('teaches').flatten();\r\n    \r\n    let educationProjectSkills = _.chain(_.get(data, \"jsonld['@graph'][1].hasCredential\", [])).map('about').map('workExample').flatten().map('hasPart').flatten().map('teaches').flatten();\r\n    \r\n    let interactionTeachSkills = _.chain(_.get(data, \"jsonld['@graph'][1].interactionStatistic\", [])).map('result').flatten().map('teaches').flatten();\r\n    \r\n    let interactionAssessSkills = _.chain(_.get(data, \"jsonld['@graph'][1].interactionStatistic\", [])).map('result').flatten().map('assesses').flatten();\r\n    \r\n    return [...workSkills, ...awardSkills, ...educationSkills, ...coursesSkills, ...educationProjectSkills, ...interactionTeachSkills, ...interactionAssessSkills];\r\n  }\r\n  const Skills = () =>\r\n    data.skills.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title=\"Skills\" className=\"w-3/4 mx-auto\" />\r\n        <ul className=\"list-none text-sm\">\r\n          {userSkills().map(x => (\r\n            <li key={uuidv4()} className=\"my-2\">\r\n              {x}\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </div>\r\n    );\r\n\r\n  const Memberships = () =>\r\n    data.memberships.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title=\"Memberships\" className=\"w-3/4 mx-auto\" />\r\n        <ul className=\"list-none text-sm\">\r\n          {_.get(data.jsonld[\"@graph\"][1], 'memberOf', []).map(x => (\r\n            <li key={_.get(x,'@id', '')} className=\"my-2\">\r\n              {_.get(x, \"memberOf.programName\", \"\")}\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      </div>\r\n    );\r\n\r\n  const ReferenceItem = x => (\r\n    <div key={_.get(x, '@id', 'main')} className=\"flex flex-col\">\r\n      <h6 className=\"text-sm font-semibold\">{_.get(x, 'interactionType.participant.givenName', '')} {_.get(x, 'interactionType.participant.familyName', '')}</h6>\r\n      <span className=\"text-sm\">{_.get(x, 'interactionType.participant.jobTitle', '')}</span>\r\n      <span className=\"text-sm\">{_.get(x, 'interactionType.participant.telephone', '')}</span>\r\n      <span className=\"text-sm\">{_.get(x, 'interactionType.participant.email', '')}</span>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={_.get(x, 'result[0].reviewRating.ratingExplanation', '')} />\r\n    </div>\r\n  );\r\n\r\n  const References = () =>\r\n    data.references &&\r\n    data.references.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.references.heading} />\r\n        <div className=\"grid grid-cols-2 col-gap-4 row-gap-2\">\r\n          {_.get(data.jsonld[\"@graph\"][1], 'interactionStatistic', []).filter(x => _.get(x, 'disambiguatingDescription', '')=== 'Reference').map(ReferenceItem)}\r\n        </div>\r\n      </div>\r\n    );\r\n\r\n  const LanguageItem = x => (\r\n    <div key={_.get(x, '@id', '')} className=\"grid grid-cols-2 items-center py-2\">\r\n      <h6 className=\"text-xs font-medium text-left\">{_.get(x, 'name', '')}</h6>\r\n      <div className=\"flex\">\r\n        {x.level && <div className=\"font-bold text-sm mr-2\">{x.level}</div>}\r\n        {x.rating !== 0 && (\r\n          <div className=\"flex\">\r\n            {Array.from(Array(x.rating)).map((_, i) => (\r\n              <i key={i} className=\"material-icons text-lg\" style={{ color: theme.colors.accent }}>\r\n                star\r\n              </i>\r\n            ))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  const Languages = () =>\r\n    data.languages &&\r\n    data.languages.enable && (_.get(data, 'jsonld[\"@graph\"][1].knowsLanguage',[]).length > 0) && (\r\n      <div className=\"w-3/4 mx-auto mb-6\">\r\n        <Heading title={data.languages.heading} />\r\n        <div>{_.get(data, 'jsonld[\"@graph\"][1].knowsLanguage', []).filter(x => _.get(x, 'name', '') !== '').map(LanguageItem)}</div>\r\n      </div>\r\n    );\r\n\r\n  const AwardItem = x => (\r\n    <div key={_.get(x,'@id', 'main')} className=\"my-2\">\r\n      <h6 className=\"font-semibold\">{_.get(x, \"skill:title\", \"\")}</h6>\r\n      <p className=\"text-xs\">{_.get(x, \"skill:nativeLabel\", \"\")}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={_.get(x, \"description\", \"\")} />\r\n    </div>\r\n  );\r\n\r\n  const Awards = () =>\r\n    data.awards &&\r\n    data.awards.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading light title={data.awards.heading} />\r\n        {_.get(data.jsonld[\"@graph\"][0], 'award', []).filter(x => x[\"skill:title\"]!==\"\").map(AwardItem)}\r\n      </div>\r\n    );\r\n\r\n  const CertificationItem = x => (\r\n    <div key={_.get(x,'@id','main')} className=\"my-2\">\r\n      <h6 className=\"font-semibold\">{_.get(x, 'educationalLevel', '')}</h6>\r\n      <p className=\"text-xs\">{_.get(x, 'about.educationalCredentialAwarded', '')}</p>\r\n      <ReactMarkdown className=\"mt-2 text-sm\" source={_.get(x, 'abstract', '')} />\r\n    </div>\r\n  );\r\n\r\n  const Certifications = () =>\r\n    data.certifications &&\r\n    data.certifications.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.certifications.heading} className=\"w-3/4 mx-auto\" />\r\n        {_.get(data, \"jsonld['@graph'][1].hasCredential\", []).filter(x => (!_.get(x, '@id', '').endsWith(\"disable\") && _.get(x, 'credentialCategory', '')!==\"Degree\")).map(CertificationItem)}\r\n      </div>\r\n    );\r\n\r\n  const ExtraItem = x => (\r\n    <div key={_.get(x, '@id', 'main')} className=\"my-3\">\r\n      <h6 className=\"text-xs font-bold\">{_.get(x, 'propertyID', '')}</h6>\r\n      <div className=\"text-sm\">{_.get(x, 'value', '')}</div>\r\n    </div>\r\n  );\r\n\r\n  const Extras = () =>\r\n    data.extras &&\r\n    data.extras.enable && (\r\n      <div className=\"mb-6\">\r\n        <Heading title={data.extras.heading} className=\"w-3/4 mx-auto\" />\r\n        {_.get(data.jsonld[\"@graph\"][1], 'identifier', []).filter(x => x['value'] !== '').map(ExtraItem)}\r\n      </div>\r\n    );\r\n\r\n  return (\r\n    <div\r\n      style={{\r\n        fontFamily: theme.font.family,\r\n        backgroundColor: theme.colors.background,\r\n        color: theme.colors.primary,\r\n      }}\r\n    >\r\n      <div className=\"grid grid-cols-12\">\r\n        <div\r\n          className=\"sidebar col-span-4 pb-8 ml-8 z-10 text-center\"\r\n          style={{ backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)` }}\r\n        >\r\n          <Photo />\r\n          <Contact />\r\n          <Skills />\r\n          <Memberships />\r\n          <Languages />\r\n          <Certifications />\r\n          <Extras />\r\n        </div>\r\n        <div className=\"col-span-8\">\r\n          <Header />\r\n\r\n          <section className=\"py-4\" style={styles.section}>\r\n            <Objective />\r\n            <Work />\r\n            <Education />\r\n            <Awards />\r\n            <References />\r\n          </section>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Celebi;\r\n", "import Onyx, { Image as OnyxPreview } from './onyx';\r\nimport Pikachu, { Image as PikachuPreview } from './pikachu';\r\nimport Gengar, { Image as GengarPreview } from './gengar';\r\nimport Castform, { Image as CastformPreview } from './castform';\r\nimport Glalie, { Image as GlaliePreview } from './glalie';\r\nimport Celebi, { Image as CelebiPreview } from './celebi';\r\n\r\nexport default [\r\n  {\r\n    key: 'onyx',\r\n    name: 'Onyx',\r\n    component: Onyx,\r\n    preview: OnyxPreview,\r\n  },\r\n  {\r\n    key: 'pikachu',\r\n    name: '<PERSON><PERSON><PERSON>',\r\n    component: <PERSON><PERSON>chu,\r\n    preview: PikachuPreview,\r\n  },\r\n  {\r\n    key: 'gengar',\r\n    name: 'Gengar',\r\n    component: Gengar,\r\n    preview: GengarPreview,\r\n  },\r\n  {\r\n    key: 'castform',\r\n    name: 'Castform',\r\n    component: Castform,\r\n    preview: CastformPreview,\r\n  },\r\n  {\r\n    key: 'glalie',\r\n    name: 'Glal<PERSON>',\r\n    component: Glalie,\r\n    preview: GlaliePreview,\r\n  },\r\n  {\r\n    key: 'celebi',\r\n    name: '<PERSON><PERSON><PERSON>',\r\n    component: <PERSON><PERSON><PERSON>,\r\n    preview: CelebiPreview,\r\n  },\r\n];\r\n", "import Celebi from './<PERSON><PERSON><PERSON>';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Celebi;\r\n", "import React from 'react';\r\n\r\nimport templates from '../../../templates';\r\n\r\nconst TemplatesTab = ({ theme, onChange }) => {\r\n  return (\r\n    <div className=\"grid grid-cols-2 gap-6\">\r\n      {templates.map(x => (\r\n        <div key={x.key} className=\"text-center\" onClick={() => onChange('theme.layout', x.key)}>\r\n          <img\r\n            className={`rounded cursor-pointer object-cover border shadow hover:shadow-md ${\r\n              theme.layout.toLowerCase() === x.key\r\n                ? 'border-gray-600 hover:border-gray-600'\r\n                : 'border-transparent '\r\n            } hover:border-gray-500 cursor-pointer`}\r\n            src={x.preview}\r\n            alt={x.name}\r\n          />\r\n          <p className=\"mt-1 text-sm font-medium\">{x.name}</p>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TemplatesTab;\r\n", "import React from 'react';\r\nimport { toast } from 'react-toastify';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport { copyToClipboard } from '../../../utils';\r\n\r\nconst colorOptions = [\r\n  '#f44336',\r\n  '#E91E63',\r\n  '#9C27B0',\r\n  '#673AB7',\r\n  '#3F51B5',\r\n  '#2196F3',\r\n  '#03A9F4',\r\n  '#00BCD4',\r\n  '#009688',\r\n  '#4CAF50',\r\n  '#8BC34A',\r\n  '#CDDC39',\r\n  '#FFEB3B',\r\n  '#FFC107',\r\n  '#FF9800',\r\n  '#FF5722',\r\n  '#795548',\r\n  '#9E9E9E',\r\n  '#607D8B',\r\n  '#FAFAFA',\r\n  '#212121',\r\n  '#263238',\r\n];\r\n\r\nconst ColorsTab = ({ theme, onChange }) => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  const copyColorToClipboard = color => {\r\n    copyToClipboard(color);\r\n    toast(t('colors.clipboardCopyAction', { color }), {\r\n      bodyClassName: 'text-center text-gray-800 py-2',\r\n    });\r\n    onChange('theme.colors.accent', color);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-4\">\r\n        {t('colors.colorOptions')}\r\n      </div>\r\n      <div className=\"mb-6 grid grid-cols-8 col-gap-2 row-gap-3\">\r\n        {colorOptions.map(color => (\r\n          <div\r\n            key={color}\r\n            className=\"cursor-pointer rounded-full border border-gray-200 h-6 w-6 hover:opacity-75\"\r\n            style={{ backgroundColor: color }}\r\n            onClick={() => copyColorToClipboard(color)}\r\n          />\r\n        ))}\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"my-6 grid grid-cols-6 items-end\">\r\n        <div\r\n          className=\"rounded-full w-8 h-8 mb-2 border-2\"\r\n          style={{ backgroundColor: theme.colors.primary }}\r\n        />\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            label={t('colors.primaryColor')}\r\n            placeholder=\"#FFFFFF\"\r\n            value={theme.colors.primary}\r\n            onChange={v => onChange('theme.colors.primary', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"my-6 grid grid-cols-6 items-end\">\r\n        <div\r\n          className=\"rounded-full w-8 h-8 mb-2 border-2\"\r\n          style={{ backgroundColor: theme.colors.accent }}\r\n        />\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            label={t('colors.accentColor')}\r\n            placeholder=\"#FFFFFF\"\r\n            value={theme.colors.accent}\r\n            onChange={v => onChange('theme.colors.accent', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ColorsTab;\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport TextField from '../../../shared/TextField';\r\n\r\nconst fontOptions = [\r\n  'Lato',\r\n  'Montserrat',\r\n  'Nunito',\r\n  'Open Sans',\r\n  'Raleway',\r\n  'Rubik',\r\n  'Source Sans Pro',\r\n  'Titillium Web',\r\n  'Ubuntu',\r\n];\r\n\r\nconst FontsTab = ({ theme, onChange }) => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 gap-6\">\r\n      {fontOptions.map(x => (\r\n        <div\r\n          key={x}\r\n          style={{ fontFamily: x }}\r\n          onClick={() => onChange('theme.font.family', x)}\r\n          className={`w-full rounded border py-4 shadow text-xl text-center ${\r\n            theme.font.family === x ? 'border-gray-500' : 'border-transparent'\r\n          } hover:border-gray-400 cursor-pointer`}\r\n        >\r\n          {x}\r\n        </div>\r\n      ))}\r\n\r\n      <div>\r\n        <TextField\r\n          className=\"mb-3\"\r\n          label={t('fonts.fontFamily.label')}\r\n          placeholder=\"Avenir Next\"\r\n          value={theme.font.family}\r\n          onChange={v => onChange('theme.font.family', v)}\r\n        />\r\n\r\n        <p className=\"text-gray-800 text-xs\">{t('fonts.fontFamily.helpText')}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FontsTab;\r\n", "/* eslint-disable new-cap */\r\n/* eslint-disable jsx-a11y/anchor-has-content */\r\n/* eslint-disable jsx-a11y/anchor-is-valid */\r\n\r\nimport React, { useRef, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport PageContext from '../../../context/PageContext';\r\nimport { importJson } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport * as JSZip from 'jszip';\r\nimport { saveAs } from 'file-saver';\r\n\r\nconst ActionsTab = ({ data, theme, dispatch }) => {\r\n  const pageContext = useContext(PageContext);\r\n  const { setPrintDialogOpen } = pageContext;\r\n  const { t } = useTranslation('rightSidebar');\r\n  const fileInputRef = useRef(null);\r\n  \r\n  const exportToJsonld = () => {\r\n    let dataclone = _.cloneDeep(data.jsonld);\r\n    let javascript_part1 = '<script type=\"application/ld+json\">'+JSON.stringify(dataclone)+\"</script>\";\r\n    _.set(dataclone['@graph'][1], \"@context\", \"http://schema.org/\");\r\n    let javascript_part2 = '<script type=\"application/ld+json\">'+JSON.stringify(dataclone['@graph'][1])+\"</script>\";\r\n    \r\n    let javascript = javascript_part1 + javascript_part2;\r\n    var zip = new JSZip();\r\n    zip.file(\"script.js\", javascript);\r\n    zip.file(\"resume.json\", JSON.stringify(data));\r\n    zip.generateAsync({type:\"blob\"})\r\n    .then(function(content) {\r\n        saveAs(content, \"jsonldresume.zip\");\r\n    });\r\n  };\r\n\r\n  const loadDemoData = () => {\r\n    dispatch({ type: 'load_demo_data' });\r\n    dispatch({ type: 'save_data' });\r\n  };\r\n\r\n  const resetEverything = () => {\r\n    dispatch({ type: 'reset' });\r\n    dispatch({ type: 'save_data' });\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"shadow text-center text-sm p-5\">{t('actions.disclaimer')}</div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('actions.importExport.heading')}</h6>\r\n\r\n        <p className=\"text-sm\">{t('actions.importExport.body')}</p>\r\n\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          className=\"hidden\"\r\n          onChange={(e) => importJson(e, dispatch)}\r\n        />\r\n        <a id=\"downloadAnchor\" className=\"hidden\" />\r\n\r\n        <div className=\"mt-4 grid grid-cols-2 col-gap-6\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => fileInputRef.current.click()}\r\n            className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">publish</i>\r\n              <span className=\"text-sm\">{t('actions.importExport.buttons.import')}</span>\r\n            </div>\r\n          </button>\r\n\r\n          <button\r\n            type=\"button\"\r\n            onClick={exportToJsonld}\r\n            className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">get_app</i>\r\n              <span className=\"text-sm\">{t('actions.importExport.buttons.export')}</span>\r\n            </div>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('actions.downloadResume.heading')}</h6>\r\n        <div className=\"text-sm\">{t('actions.downloadResume.body')}</div>\r\n\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => setPrintDialogOpen(true)}\r\n          className=\"mt-4 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">save</i>\r\n            <span className=\"text-sm\">{t('actions.downloadResume.buttons.saveAsPdf')}</span>\r\n          </div>\r\n        </button>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('actions.loadDemoData.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('actions.loadDemoData.body')}</div>\r\n\r\n        <button\r\n          type=\"button\"\r\n          onClick={loadDemoData}\r\n          className=\"mt-4 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">flight_takeoff</i>\r\n            <span className=\"text-sm\">{t('actions.loadDemoData.buttons.loadData')}</span>\r\n          </div>\r\n        </button>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('actions.reset.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('actions.reset.body')}</div>\r\n\r\n        <button\r\n          type=\"button\"\r\n          onClick={resetEverything}\r\n          className=\"mt-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">refresh</i>\r\n            <span className=\"text-sm\">{t('actions.reset.buttons.reset')}</span>\r\n          </div>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ActionsTab;\r\n", "import React from 'react';\r\nimport { Trans, useTranslation } from 'react-i18next';\r\n\r\nconst AboutTab = () => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('about.documentation.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('about.documentation.body')}</div>\r\n\r\n        <a\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          href=\"https://docs.jsonldresume.org/\"\r\n          className=\"flex justify-center items-center mt-4 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">description</i>\r\n            <span className=\"text-sm\">{t('about.documentation.buttons.documentation')}</span>\r\n          </div>\r\n        </a>\r\n      </div>\r\n\r\n      <hr className=\"my-5\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('about.bugOrFeatureRequest.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('about.bugOrFeatureRequest.body')}</div>\r\n\r\n        <div className=\"grid grid-cols-1\">\r\n          <a\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            href=\"https://github.com/AmruthPillai/Reactive-Resume/issues/new\"\r\n            className=\"mt-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">bug_report</i>\r\n              <span className=\"text-sm\">{t('about.bugOrFeatureRequest.buttons.raiseIssue')}</span>\r\n            </div>\r\n          </a>\r\n\r\n          <a\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            href=\"mailto:<EMAIL>?subject=Feature Request/Reporting a Bug in Reactive Resume: \"\r\n            className=\"mt-4 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">email</i>\r\n              <span className=\"text-sm\">{t('about.bugOrFeatureRequest.buttons.sendEmail')}</span>\r\n            </div>\r\n          </a>\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-5\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('about.sourceCode.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('about.sourceCode.body')}</div>\r\n\r\n        <a\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          href=\"https://github.com/AmruthPillai/Reactive-Resume\"\r\n          className=\"flex justify-center items-center mt-4 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">code</i>\r\n            <span className=\"text-sm\">{t('about.sourceCode.buttons.githubRepo')}</span>\r\n          </div>\r\n        </a>\r\n      </div>\r\n\r\n      <hr className=\"my-5\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('about.license.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('about.license.body')}</div>\r\n\r\n        <a\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          href=\"https://github.com/AmruthPillai/Reactive-Resume/blob/master/LICENSE\"\r\n          className=\"flex justify-center items-center mt-4 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">gavel</i>\r\n            <span className=\"text-sm\">{t('about.license.buttons.mitLicense')}</span>\r\n          </div>\r\n        </a>\r\n      </div>\r\n\r\n      <div className=\"mt-5\">\r\n        <p className=\"text-xs font-gray-600 text-center\">\r\n          <Trans t={t} i18nKey=\"about.footer.credit\">\r\n            Made with Love by\r\n            <a\r\n              className=\"font-bold hover:underline\"\r\n              href=\"https://www.amruthpillai.com/\"\r\n              rel=\"noopener noreferrer\"\r\n              target=\"_blank\"\r\n            >\r\n              Amruth Pillai\r\n            </a>\r\n          </Trans>\r\n        </p>\r\n        <p className=\"text-xs font-gray-600 text-center\">{t('about.footer.thanks')}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AboutTab;\r\n", "import React from 'react';\r\nimport { useTranslation, Trans } from 'react-i18next';\r\n\r\nimport { languages } from '../../../i18n';\r\nimport Dropdown from '../../../shared/Dropdown';\r\n\r\nconst SettingsTab = ({ settings, onChange }) => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  return (\r\n    <div>\r\n      <Dropdown\r\n        label={t('settings.language.label')}\r\n        value={settings.language}\r\n        onChange={x => onChange('settings.language', x)}\r\n        options={languages}\r\n        optionItem={x => (\r\n          <option key={x.code} value={x.code}>\r\n            {x.name}\r\n          </option>\r\n        )}\r\n      />\r\n\r\n      <p className=\"text-gray-800 text-xs\">\r\n        <Trans t={t} i18nKey=\"settings.language.helpText\">\r\n          If you would like to help translate the app into your own language, please refer the\r\n          <a\r\n            className=\"text-blue-600 hover:underline\"\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            href=\"https://docs.rxresu.me/translation/\"\r\n          >\r\n            Translation Documentation\r\n          </a>\r\n          .\r\n        </Trans>\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport TabBar from '../../shared/TabBar';\r\nimport TemplatesTab from './tabs/Templates';\r\nimport ColorsTab from './tabs/Colors';\r\nimport FontsTab from './tabs/Fonts';\r\nimport ActionsTab from './tabs/Actions';\r\nimport AboutTab from './tabs/About';\r\nimport SettingsTab from './tabs/Settings';\r\n\r\nconst RightSidebar = () => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  const context = useContext(AppContext);\r\n  const { state, dispatch } = context;\r\n  const { data, theme, settings } = state;\r\n\r\n  const tabs = [\r\n    {\r\n      key: 'templates',\r\n      name: t('templates.title'),\r\n    },\r\n    {\r\n      key: 'colors',\r\n      name: t('colors.title'),\r\n    },\r\n    {\r\n      key: 'fonts',\r\n      name: t('fonts.title'),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      name: t('actions.title'),\r\n    },\r\n    {\r\n      key: 'settings',\r\n      name: t('settings.title'),\r\n    },\r\n    {\r\n      key: 'about',\r\n      name: t('about.title'),\r\n    },\r\n  ];\r\n  const [currentTab, setCurrentTab] = useState(tabs[0].key);\r\n\r\n  const onChange = (key, value) => {\r\n    dispatch({\r\n      type: 'on_input',\r\n      payload: {\r\n        key,\r\n        value,\r\n      },\r\n    });\r\n\r\n    dispatch({ type: 'save_data' });\r\n  };\r\n\r\n  const renderTabs = () => {\r\n    switch (currentTab) {\r\n      case tabs[0].key:\r\n        return <TemplatesTab theme={theme} onChange={onChange} />;\r\n      case tabs[1].key:\r\n        return <ColorsTab theme={theme} onChange={onChange} />;\r\n      case tabs[2].key:\r\n        return <FontsTab theme={theme} onChange={onChange} />;\r\n      case tabs[3].key:\r\n        return <ActionsTab data={data} theme={theme} dispatch={dispatch} />;\r\n      case tabs[4].key:\r\n        return <SettingsTab settings={settings} onChange={onChange} />;\r\n      case tabs[5].key:\r\n        return <AboutTab />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      id=\"rightSidebar\"\r\n      className=\"animated slideInRight z-10 py-6 h-screen bg-white col-span-1 shadow-2xl overflow-y-scroll\"\r\n    >\r\n      <TabBar tabs={tabs} currentTab={currentTab} setCurrentTab={setCurrentTab} />\r\n      <div className=\"px-6\">{renderTabs()}</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RightSidebar;\r\n", "import React, { useContext } from 'react';\r\n\r\nimport PageContext from '../context/PageContext';\r\n\r\nconst PageController = () => {\r\n  const pageContext = useContext(PageContext);\r\n  const { panZoomRef, setPrintDialogOpen } = pageContext;\r\n\r\n  const zoomIn = () => panZoomRef.current.zoomIn(2);\r\n  const zoomOut = () => panZoomRef.current.zoomOut(2);\r\n  const centerReset = () => {\r\n    panZoomRef.current.autoCenter(1);\r\n    panZoomRef.current.reset(1);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      id=\"pageController\"\r\n      className=\"absolute z-20 opacity-75 hover:opacity-100 transition-all duration-150\"\r\n    >\r\n      <div className=\"text-2xl px-8 border border-gray-200 rounded-full bg-white flex justify-center items-center leading-none select-none\">\r\n        <div className=\"p-3 hover:bg-gray-200 cursor-pointer flex\" onClick={zoomIn}>\r\n          <i className=\"material-icons\">zoom_in</i>\r\n        </div>\r\n\r\n        <div className=\"p-3 hover:bg-gray-200 cursor-pointer flex\" onClick={zoomOut}>\r\n          <i className=\"material-icons\">zoom_out</i>\r\n        </div>\r\n\r\n        <div className=\"p-3 hover:bg-gray-200 cursor-pointer flex\" onClick={centerReset}>\r\n          <i className=\"material-icons\">center_focus_strong</i>\r\n        </div>\r\n\r\n        <div className=\"text-gray-400 p-3\">|</div>\r\n\r\n        <div className=\"p-3 hover:bg-gray-200 cursor-pointer flex\" onClick={() => window.print()}>\r\n          <i className=\"material-icons\">print</i>\r\n        </div>\r\n\r\n        <div\r\n          className=\"p-3 hover:bg-gray-200 cursor-pointer flex\"\r\n          onClick={() => setPrintDialogOpen(true)}\r\n        >\r\n          <i className=\"material-icons\">save</i>\r\n        </div>\r\n\r\n        <div className=\"text-gray-400 p-3\">|</div>\r\n\r\n        <a\r\n          className=\"p-3 hover:bg-gray-200 cursor-pointer flex\"\r\n          href=\"https://doc.jsonldresume.org/\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n        >\r\n          <i className=\"material-icons\">help_outline</i>\r\n        </a>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageController;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport PageContext from '../context/PageContext';\r\nimport Dropdown from './Dropdown';\r\nimport { saveAsPdf, saveAsMultiPagePdf } from '../utils';\r\n\r\nconst PrintDialog = () => {\r\n  const { t } = useTranslation();\r\n  const pageContext = useContext(PageContext);\r\n  const { pageRef, panZoomRef, isPrintDialogOpen, setPrintDialogOpen } = pageContext;\r\n\r\n  const printTypes = [\r\n    { key: 'unconstrained', value: `${t('printDialog.printType.types.unconstrained')}` },\r\n    { key: 'fitInA4', value: `${t('printDialog.printType.types.fitInA4')}` },\r\n    { key: 'multiPageA4', value: `${t('printDialog.printType.types.multiPageA4')}` },\r\n  ];\r\n\r\n  const [quality, setQuality] = useState(80);\r\n  const [type, setType] = useState(printTypes[0].key);\r\n\r\n  return (\r\n    <div\r\n      className={`absolute inset-0 transition-all duration-200 ease-in-out ${\r\n        isPrintDialogOpen ? 'opacity-100 z-20' : 'opacity-0 z-0'\r\n      }`}\r\n      style={{ backgroundColor: 'rgba(0, 0, 0, 0.25)' }}\r\n      onClick={() => {\r\n        setPrintDialogOpen(false);\r\n      }}\r\n    >\r\n      <div\r\n        className=\"centered py-8 px-12 bg-white shadow-xl rounded w-full md:w-1/3\"\r\n        onClick={e => {\r\n          e.stopPropagation();\r\n          e.preventDefault();\r\n        }}\r\n      >\r\n        <h5 className=\"mb-6 text-lg font-bold\">{t('printDialog.heading')}</h5>\r\n\r\n        <h6 className=\"mb-1 text-sm font-medium\">{t('printDialog.quality.label')}</h6>\r\n        <div className=\"flex items-center\">\r\n          <input\r\n            type=\"range\"\r\n            className=\"w-full h-4 my-2 rounded-full overflow-hidden appearance-none focus:outline-none bg-gray-400\"\r\n            value={quality}\r\n            onChange={e => setQuality(e.target.value)}\r\n            min=\"40\"\r\n            max=\"100\"\r\n            step=\"5\"\r\n          />\r\n\r\n          <h6 className=\"font-medium pl-5\">{quality}%</h6>\r\n        </div>\r\n\r\n        <h6 className=\"mt-4 mb-2 text-sm font-medium\">{t('printDialog.printType.label')}</h6>\r\n        <Dropdown\r\n          value={type}\r\n          options={printTypes}\r\n          onChange={setType}\r\n          optionItem={x => (\r\n            <option key={x.key} value={x.key}>\r\n              {x.value}\r\n            </option>\r\n          )}\r\n        />\r\n\r\n        <p className=\"my-3 text-xs text-gray-600\">{t('printDialog.helpText.0')}</p>\r\n        <p className=\"my-3 text-xs text-gray-600\">{t('printDialog.helpText.1')}</p>\r\n\r\n        <div className=\"flex justify-between\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => {\r\n              setPrintDialogOpen(false);\r\n            }}\r\n            className=\"mt-6 border border-red-600 text-red-600 hover:bg-red-600 hover:text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">close</i>\r\n              <span className=\"text-sm\">{t('printDialog.buttons.cancel')}</span>\r\n            </div>\r\n          </button>\r\n\r\n          <button\r\n            type=\"button\"\r\n            onClick={async () => {\r\n              await (type === 'multiPageA4'\r\n                ? saveAsMultiPagePdf(pageRef, panZoomRef, quality)\r\n                : saveAsPdf(pageRef, panZoomRef, quality, type));\r\n              setPrintDialogOpen(false);\r\n            }}\r\n            className=\"mt-6 border border-gray-700 text-gray-700 hover:bg-gray-700 hover:text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">save</i>\r\n              <span className=\"text-sm\">{t('printDialog.buttons.saveAsPdf')}</span>\r\n            </div>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PrintDialog;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport animation from '../assets/panzoom.mp4';\r\n\r\nconst PanZoomAnimation = () => {\r\n  const { t } = useTranslation();\r\n  const [animationVisible, setAnimationVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setTimeout(() => setAnimationVisible(true), 500);\r\n    setTimeout(() => setAnimationVisible(false), 3000);\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      className={`centered absolute inset-0 w-1/4 mt-24 transition-all duration-1000 ease-in-out ${\r\n        animationVisible ? 'opacity-100 z-20' : 'opacity-0 z-0'\r\n      }`}\r\n    >\r\n      <div className=\"px-12 rounded-lg shadow-2xl bg-white\">\r\n        <video src={animation} autoPlay muted loop />\r\n        <p className=\"px-6 pb-6 text-sm text-gray-800 font-medium text-center\">\r\n          {t('panZoomAnimation.helpText')}\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PanZoomAnimation;\r\n", "/* eslint-disable jsx-a11y/media-has-caption */\r\nimport React, { useRef, useEffect, useContext, Suspense } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { PanZoom } from 'react-easy-panzoom';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport PageContext from '../../context/PageContext';\r\n\r\nimport LeftSidebar from '../LeftSidebar/LeftSidebar';\r\nimport RightSidebar from '../RightSidebar/RightSidebar';\r\n\r\nimport templates from '../../templates';\r\nimport PageController from '../../shared/PageController';\r\nimport PrintDialog from '../../shared/PrintDialog';\r\nimport PanZoomAnimation from '../../shared/PanZoomAnimation';\r\n\r\nconst App = () => {\r\n  const pageRef = useRef(null);\r\n  const panZoomRef = useRef(null);\r\n  const { i18n } = useTranslation();\r\n\r\n  const context = useContext(AppContext);\r\n  const { state, dispatch } = context;\r\n  const { theme, settings } = state;\r\n\r\n  const pageContext = useContext(PageContext);\r\n  const { setPageRef, setPanZoomRef } = pageContext;\r\n\r\n  useEffect(() => {\r\n    setPageRef(pageRef);\r\n    setPanZoomRef(panZoomRef);\r\n    i18n.changeLanguage(settings.language);\r\n    const storedState = JSON.parse(localStorage.getItem('state'));\r\n    dispatch({ type: 'import_data', payload: storedState });\r\n  }, [dispatch, setPageRef, setPanZoomRef, i18n, settings.language]);\r\n\r\n  return (\r\n    <Suspense fallback=\"Loading...\">\r\n      <div className=\"h-screen grid grid-cols-5 items-center\">\r\n        <LeftSidebar />\r\n\r\n        <div className=\"relative z-10 h-screen overflow-hidden col-span-3 flex justify-center items-center\">\r\n          <PanZoom\r\n            ref={panZoomRef}\r\n            minZoom=\"0.4\"\r\n            autoCenter\r\n            autoCenterZoomLevel={0.7}\r\n            enableBoundingBox\r\n            boundaryRatioVertical={0.8}\r\n            boundaryRatioHorizontal={0.8}\r\n            style={{ outline: 'none' }}\r\n          >\r\n            <div id=\"page\" ref={pageRef} className=\"shadow-2xl break-words\">\r\n              {templates.find(x => theme.layout.toLowerCase() === x.key).component()}\r\n            </div>\r\n          </PanZoom>\r\n\r\n          <PageController />\r\n        </div>\r\n\r\n        <div id=\"printPage\" className=\"break-words\">\r\n          {templates.find(x => theme.layout.toLowerCase() === x.key).component()}\r\n        </div>\r\n\r\n        <RightSidebar />\r\n\r\n        <PanZoomAnimation />\r\n        <PrintDialog />\r\n      </div>\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nexport default App;\r\n", "import React from 'react';\r\nimport ReactDOM from 'react-dom';\r\nimport { toast } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\n\r\nimport './i18n';\r\nimport './assets/tailwind/tailwind.css';\r\nimport './index.css';\r\n\r\nimport * as serviceWorker from './serviceWorker';\r\nimport { AppProvider } from './context/AppContext';\r\nimport { PageProvider } from './context/PageContext';\r\nimport App from './components/App/App';\r\n\r\ntoast.configure({\r\n  autoClose: 3000,\r\n  closeButton: false,\r\n  hideProgressBar: true,\r\n  position: toast.POSITION.BOTTOM_RIGHT,\r\n});\r\n\r\nReactDOM.render(\r\n  <React.StrictMode>\r\n    <AppProvider>\r\n      <PageProvider>\r\n        <App />\r\n      </PageProvider>\r\n    </AppProvider>\r\n  </React.StrictMode>,\r\n  document.getElementById('root'),\r\n);\r\n\r\nserviceWorker.register();\r\n"], "sourceRoot": ""}