{"version": 3, "sources": ["templates/onyx/preview.png", "templates/pikachu/preview.png", "templates/gengar/preview.png", "templates/castform/preview.png", "templates/glalie/preview.png", "templates/celebi/preview.png", "assets/panzoom.mp4", "i18n/locales/af/app/index.js", "i18n/locales/af/leftSidebar/index.js", "i18n/locales/af/rightSidebar/index.js", "i18n/locales/af/index.js", "i18n/locales/ar/app/index.js", "i18n/locales/ar/leftSidebar/index.js", "i18n/locales/ar/rightSidebar/index.js", "i18n/locales/ar/index.js", "i18n/locales/as/app/index.js", "i18n/locales/as/leftSidebar/index.js", "i18n/locales/as/rightSidebar/index.js", "i18n/locales/as/index.js", "i18n/locales/ca/app/index.js", "i18n/locales/ca/leftSidebar/index.js", "i18n/locales/ca/rightSidebar/index.js", "i18n/locales/ca/index.js", "i18n/locales/cs/app/index.js", "i18n/locales/cs/leftSidebar/index.js", "i18n/locales/cs/rightSidebar/index.js", "i18n/locales/cs/index.js", "i18n/locales/da/app/index.js", "i18n/locales/da/leftSidebar/index.js", "i18n/locales/da/rightSidebar/index.js", "i18n/locales/da/index.js", "i18n/locales/de/app/index.js", "i18n/locales/de/leftSidebar/index.js", "i18n/locales/de/rightSidebar/index.js", "i18n/locales/de/index.js", "i18n/locales/el/app/index.js", "i18n/locales/el/leftSidebar/index.js", "i18n/locales/el/rightSidebar/index.js", "i18n/locales/el/index.js", "i18n/locales/en/app/index.js", "i18n/locales/en/leftSidebar/index.js", "i18n/locales/en/rightSidebar/index.js", "i18n/locales/en/index.js", "i18n/locales/es/app/index.js", "i18n/locales/es/leftSidebar/index.js", "i18n/locales/es/rightSidebar/index.js", "i18n/locales/es/index.js", "i18n/locales/fi/app/index.js", "i18n/locales/fi/leftSidebar/index.js", "i18n/locales/fi/rightSidebar/index.js", "i18n/locales/fi/index.js", "i18n/locales/fr/app/index.js", "i18n/locales/fr/leftSidebar/index.js", "i18n/locales/fr/rightSidebar/index.js", "i18n/locales/fr/index.js", "i18n/locales/he/app/index.js", "i18n/locales/he/leftSidebar/index.js", "i18n/locales/he/rightSidebar/index.js", "i18n/locales/he/index.js", "i18n/locales/hi/app/index.js", "i18n/locales/hi/leftSidebar/index.js", "i18n/locales/hi/rightSidebar/index.js", "i18n/locales/hi/index.js", "i18n/locales/hu/app/index.js", "i18n/locales/hu/leftSidebar/index.js", "i18n/locales/hu/rightSidebar/index.js", "i18n/locales/hu/index.js", "i18n/locales/it/app/index.js", "i18n/locales/it/leftSidebar/index.js", "i18n/locales/it/rightSidebar/index.js", "i18n/locales/it/index.js", "i18n/locales/ja/app/index.js", "i18n/locales/ja/leftSidebar/index.js", "i18n/locales/ja/rightSidebar/index.js", "i18n/locales/ja/index.js", "i18n/locales/kn/app/index.js", "i18n/locales/kn/leftSidebar/index.js", "i18n/locales/kn/rightSidebar/index.js", "i18n/locales/kn/index.js", "i18n/locales/ko/app/index.js", "i18n/locales/ko/leftSidebar/index.js", "i18n/locales/ko/rightSidebar/index.js", "i18n/locales/ko/index.js", "i18n/locales/ml/app/index.js", "i18n/locales/ml/leftSidebar/index.js", "i18n/locales/ml/rightSidebar/index.js", "i18n/locales/ml/index.js", "i18n/locales/mr/app/index.js", "i18n/locales/mr/leftSidebar/index.js", "i18n/locales/mr/rightSidebar/index.js", "i18n/locales/mr/index.js", "i18n/locales/nl/app/index.js", "i18n/locales/nl/leftSidebar/index.js", "i18n/locales/nl/rightSidebar/index.js", "i18n/locales/nl/index.js", "i18n/locales/no/app/index.js", "i18n/locales/no/leftSidebar/index.js", "i18n/locales/no/rightSidebar/index.js", "i18n/locales/no/index.js", "i18n/locales/pa/app/index.js", "i18n/locales/pa/leftSidebar/index.js", "i18n/locales/pa/rightSidebar/index.js", "i18n/locales/pa/index.js", "i18n/locales/pl/app/index.js", "i18n/locales/index.js", "i18n/locales/pl/index.js", "i18n/locales/pl/leftSidebar/index.js", "i18n/locales/pl/rightSidebar/index.js", "i18n/locales/pt/index.js", "i18n/locales/pt/leftSidebar/index.js", "i18n/locales/pt/rightSidebar/index.js", "i18n/locales/ro/index.js", "i18n/locales/ro/leftSidebar/index.js", "i18n/locales/ro/rightSidebar/index.js", "i18n/locales/ru/index.js", "i18n/locales/ru/leftSidebar/index.js", "i18n/locales/ru/rightSidebar/index.js", "i18n/locales/sv/index.js", "i18n/locales/sv/leftSidebar/index.js", "i18n/locales/sv/rightSidebar/index.js", "i18n/locales/ta/index.js", "i18n/locales/ta/leftSidebar/index.js", "i18n/locales/ta/rightSidebar/index.js", "i18n/locales/tr/index.js", "i18n/locales/tr/leftSidebar/index.js", "i18n/locales/tr/rightSidebar/index.js", "i18n/locales/uk/index.js", "i18n/locales/uk/leftSidebar/index.js", "i18n/locales/uk/rightSidebar/index.js", "i18n/locales/vi/index.js", "i18n/locales/vi/leftSidebar/index.js", "i18n/locales/vi/rightSidebar/index.js", "i18n/locales/zh/index.js", "i18n/locales/zh/leftSidebar/index.js", "i18n/locales/zh/rightSidebar/index.js", "i18n/index.js", "serviceWorker.js", "utils/index.js", "context/AppContext.js", "context/PageContext.js", "shared/Dropdown.js", "shared/TabBar.js", "shared/TextField.js", "components/LeftSidebar/tabs/Profile.js", "shared/Checkbox.js", "shared/ItemActions.js", "shared/AddItemButton.js", "shared/ItemHeading.js", "components/LeftSidebar/tabs/Address.js", "components/LeftSidebar/tabs/Contacts.js", "shared/MarkdownHelpText.js", "shared/TextArea.js", "components/LeftSidebar/tabs/Objective.js", "components/LeftSidebar/tabs/Work.js", "components/LeftSidebar/tabs/Education.js", "components/LeftSidebar/tabs/Awards.js", "components/LeftSidebar/tabs/Extras.js", "components/LeftSidebar/tabs/Languages.js", "components/LeftSidebar/tabs/References.js", "components/LeftSidebar/tabs/Memberships.js", "components/LeftSidebar/LeftSidebar.js", "templates/blocks/Awards/AwardsA.js", "templates/blocks/Certifications/CertificationsA.js", "templates/blocks/Icons.js", "templates/blocks/BirthDate/BirthDateB.js", "templates/blocks/Contact/ContactA.js", "templates/blocks/SectionSkills/SectionSkillsA.js", "templates/blocks/Education/EducationA.js", "templates/blocks/Heading/HeadingA.js", "templates/blocks/Hobbies/HobbiesA.js", "templates/blocks/Languages/LanguagesA.js", "templates/blocks/Objective/ObjectiveA.js", "templates/blocks/Projects/ProjectsA.js", "templates/blocks/References/ReferencesA.js", "templates/blocks/Skills/SkillsA.js", "templates/blocks/Work/WorkA.js", "templates/blocks/Address/AddressA.js", "templates/blocks/Names/NamesA.js", "templates/blocks/Names/SubNamesA.js", "templates/onyx/Onyx.js", "templates/onyx/index.js", "templates/blocks/Heading/HeadingB.js", "templates/blocks/Objective/ObjectiveB.js", "templates/pikachu/Pikachu.js", "templates/pikachu/index.js", "templates/blocks/BirthDate/BirthDateC.js", "templates/blocks/Contact/ContactB.js", "templates/blocks/Heading/HeadingC.js", "templates/blocks/References/ReferencesB.js", "templates/blocks/Names/NamesB.js", "templates/gengar/Gengar.js", "templates/gengar/index.js", "templates/blocks/BirthDate/BirthDateA.js", "templates/blocks/Contact/ContactC.js", "templates/blocks/Heading/HeadingD.js", "templates/castform/Castform.js", "templates/castform/index.js", "templates/blocks/Contact/ContactD.js", "templates/glalie/Glalie.js", "templates/glalie/index.js", "templates/blocks/Heading/HeadingE.js", "templates/blocks/Languages/LanguagesB.js", "templates/celebi/Celebi.js", "templates/index.js", "templates/celebi/index.js", "components/RightSidebar/tabs/Templates.js", "components/RightSidebar/tabs/Colors.js", "components/RightSidebar/tabs/Fonts.js", "components/RightSidebar/tabs/Actions.js", "components/RightSidebar/tabs/About.js", "components/RightSidebar/tabs/Settings.js", "components/RightSidebar/RightSidebar.js", "shared/PageController.js", "shared/PrintDialog.js", "shared/PanZoomAnimation.js", "components/App/App.js", "index.js"], "names": ["module", "exports", "app", "profile", "objective", "work", "education", "awards", "certifications", "languages", "references", "extras", "templates", "colors", "fonts", "actions", "settings", "about", "leftSidebar", "rightSidebar", "af", "ar", "as", "ca", "cs", "da", "de", "el", "en", "es", "fi", "fr", "he", "hi", "hu", "it", "ja", "kn", "ko", "ml", "mr", "nl", "no", "pa", "pl", "pt", "ro", "ru", "sv", "ta", "tr", "uk", "vi", "zh", "code", "name", "i18n", "use", "detector", "backend", "initReactI18next", "init", "resources", "lng", "fallbackLng", "ns", "defaultNS", "isLocalhost", "Boolean", "window", "location", "hostname", "match", "registerValidSW", "swUrl", "config", "navigator", "serviceWorker", "register", "then", "registration", "onupdatefound", "installingWorker", "installing", "onstatechange", "state", "controller", "console", "log", "onUpdate", "onSuccess", "catch", "error", "move", "array", "element", "delta", "index", "findIndex", "item", "id", "newIndex", "length", "indexes", "sort", "a", "b", "splice", "hexToRgb", "hex", "replace", "m", "r", "g", "result", "exec", "parseInt", "saveData", "dispatch", "type", "addItem", "key", "value", "payload", "saveAsPdfTimer", "saveAsPdf", "pageRef", "panZoomRef", "quality", "Promise", "resolve", "current", "autoCenter", "reset", "setTimeout", "html2canvas", "scale", "useCORS", "<PERSON><PERSON><PERSON><PERSON>", "canvas", "image", "toDataURL", "doc", "jsPDF", "orientation", "unit", "format", "width", "height", "pageWidth", "internal", "pageSize", "getWidth", "pageHeight", "getHeight", "widthRatio", "heightRatio", "ratio", "canvasWidth", "canvasHeight", "marginX", "marginY", "addImage", "save", "Date", "now", "saveAsMultiPagePdfTimer", "saveAsMultiPagePdf", "marginTop", "heightLeft", "addPage", "formatDate", "date", "language", "includeDay", "template", "dayjs", "locale", "substr", "formatDateRange", "t", "startDate", "endDate", "start", "end", "<PERSON><PERSON><PERSON><PERSON>", "safetyCheck", "section", "enable", "initialState", "data", "j<PERSON>ld", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ame", "address", "heading", "photo", "firstName", "lastName", "subtitle", "line1", "line2", "line3", "phone", "website", "email", "contacts", "body", "items", "skills", "memberships", "theme", "layout", "font", "family", "background", "primary", "accent", "reducer", "newState", "JSON", "parse", "stringify", "set", "get", "push", "remove", "x", "localStorage", "setItem", "Object", "keys", "demoJsonldData", "AppContext", "createContext", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "useReducer", "Consumer", "PageContext", "React", "<PERSON><PERSON><PERSON><PERSON>", "useState", "setPageRef", "setPanZoomRef", "isPrintDialogOpen", "setPrintDialogOpen", "Dropdown", "className", "label", "onChange", "options", "optionItem", "style", "display", "e", "target", "map", "TabBar", "tabs", "currentTab", "setCurrentTab", "changeBy", "tab", "onClick", "placeholder", "v", "availableLanguages", "TextField", "editingLanguage", "handleMultiTextChange", "allValues", "props", "Array", "isArray", "_", "initAllValues", "lang", "currrentValueIndex", "newLang", "handleLanguageChange", "setState", "handleTextChange", "MultiItem", "disabled", "this", "event", "for<PERSON>ach", "c", "MakeSelectOptions", "Component", "ProfileTab", "useTranslation", "setValue", "path", "field", "val", "personUrl", "AddItem", "Checkbox", "checked", "icon", "size", "ItemActions", "first", "identifier", "last", "enableAction", "deleteItem", "moveItemUp", "moveItemDown", "AddItemButton", "onSubmit", "ItemHeading", "title", "isOpen", "<PERSON><PERSON><PERSON>", "Form", "streetAddress", "addressLocality", "addressRegion", "addressCountry", "postalCode", "sameAs", "uuidv4", "hoursAvailable", "contactType", "ItemActionEnable", "validThrough", "<PERSON><PERSON>", "AddressTab", "useContext", "telephone", "description", "ContactsTab", "contactPoint", "filter", "MarkdownHelpText", "Trans", "i18nKey", "rel", "href", "TextArea", "rows", "Availablity", "availableAtOrFrom", "addAvailability", "availabilityStarts", "availabilityEnds", "deliveryLeadTime", "substring", "ObjectiveTab", "seeks", "fullPath", "emptyItem", "hasOccupation", "responsibilities", "subjectOf", "organizer", "<PERSON><PERSON><PERSON>", "WorkTab", "educationalLevel", "EducationTab", "altidentifier", "AwardsTab", "ExtrasTab", "LanguagesTab", "useEffect", "ReferencesTab", "MembershipsTab", "LeftSidebar", "context", "Address", "Contacts", "Objective", "Work", "Education", "Awards", "Memberships", "Languages", "References", "Extras", "renderTabs", "AwardItem", "source", "memo", "Heading", "CertificationItem", "endsWith", "Icons", "MdPhone", "FaGlobeAmericas", "MdEmail", "facebook", "FaFacebookF", "twitter", "FaTwitter", "linkedin", "FaLinkedinIn", "github", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dribbble", "FaDribbble", "instagram", "FaInstagram", "stackoverflow", "FaStackOverflow", "behance", "FaBehance", "gitlab", "FaGitlab", "birthday", "FaBirthdayCake", "telegram", "FaTelegram", "skype", "FaSkype", "Icon", "color", "ContactItem", "link", "toLowerCase", "FaCaretRight", "social", "username", "network", "url", "SectionSkillsItem", "EducationItem", "summary", "HobbyA", "hobbies", "LanguageItem", "fluency", "ProjectItem", "projects", "metadata", "ReferenceItem", "SkillItem", "level", "undefined", "workSkills", "flatten", "awardSkills", "educationSkills", "coursesSkills", "educationProjectSkills", "interactionTeachSkills", "interactionAssessSkills", "allSkills", "skillsObject", "i", "userSkills", "WorkResponsibilityItem", "WorkResponsibility", "WorkItem", "AddressItem", "subclassName", "mainclassName", "hclassName", "fontSize", "elem", "familynameIndex", "join", "Blocks", "ObjectiveA", "WorkA", "EducationA", "ProjectsA", "AwardsA", "CertificationsA", "SkillsA", "HobbiesA", "LanguagesA", "ReferencesA", "Onyx", "HeadingA", "fontFamily", "backgroundColor", "src", "alt", "SubNamesA", "borderColor", "Image", "<PERSON><PERSON><PERSON>", "HeadingB", "ReferencesB", "Gengar", "Photo", "Profile", "HeadingC", "birthDate", "marginLeft", "Castform", "borderWidth", "HeadingD", "top", "left", "G<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "styles", "header", "position", "right", "flexDirection", "justifyContent", "paddingLeft", "leftSection", "rightSection", "HeadingE", "text", "component", "preview", "OnyxPreview", "PikachuPreview", "GengarPreview", "CastformPreview", "GlaliePreview", "TemplatesTab", "disable", "alert", "colorOptions", "ColorsTab", "copyColorToClipboard", "textArea", "document", "createElement", "padding", "border", "outline", "boxShadow", "append<PERSON><PERSON><PERSON>", "focus", "select", "successful", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "copyToClipboard", "toast", "bodyClassName", "fontOptions", "FontsTab", "ActionsTab", "fileInputRef", "useRef", "ref", "FileReader", "addEventListener", "importedObject", "readAsText", "files", "importJson", "click", "backup<PERSON><PERSON><PERSON>", "dataclone", "javascript_part1", "javascript", "zip", "JSZip", "file", "generateAsync", "content", "saveAs", "AboutTab", "SettingsTab", "RightSidebar", "Templates", "Colors", "Fonts", "Actions", "Settings", "About", "PageController", "pageContext", "zoomIn", "zoomOut", "print", "PrintDialog", "printTypes", "setQuality", "setType", "stopPropagation", "preventDefault", "min", "max", "step", "PanZoomAnimation", "animationVisible", "setAnimationVisible", "animation", "autoPlay", "muted", "loop", "App", "changeLanguage", "storedState", "getItem", "fallback", "minZoom", "autoCenterZoomLevel", "enableBoundingBox", "boundaryRatioVertical", "boundaryRatioHorizontal", "find", "configure", "autoClose", "closeButton", "hideProgressBar", "POSITION", "BOTTOM_RIGHT", "ReactDOM", "render", "StrictMode", "getElementById", "URL", "process", "origin", "fetch", "headers", "response", "contentType", "status", "indexOf", "ready", "unregister", "reload", "checkValidServiceWorker"], "mappings": "4+6RAAAA,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,gBCA3CD,EAAOC,QAAU,IAA0B,qC,mBCA3CD,EAAOC,QAAU,IAA0B,qC,kZCE5BC,I,wECQA,GACbC,UACAC,YACAC,OACAC,YACAC,SACAC,iBACAC,YACAC,aACAC,U,mDCZa,GACbC,YACAC,SACAC,QACAC,UACAC,WACAC,SCTa,GACbf,MACAgB,cACAC,gB,SCLajB,I,iFCQA,GACbC,UACAC,YACAC,OACAC,YACAC,SACAC,iBACAC,YACAC,aACAC,U,sDCZa,GACbC,YACAC,SACAC,QACAC,UACAC,WACAC,SCTa,GACbf,MACAgB,cACAC,gB,SCLajB,I,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,MACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,M,0FCQA,IACbC,WACAC,aACAC,QACAC,aACAC,UACAC,kBACAC,aACAC,cACAC,W,4DCZa,IACbC,aACAC,UACAC,SACAC,WACAC,YACAC,UCTa,IACbf,OACAgB,eACAC,iB,UCLajB,MCiCA,IACbkB,KACAC,KACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,MACAC,GCxDa,CACb1C,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SHgDA4B,GIzDa,CACb3C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SNiDA6B,GO1Da,CACb5C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,STkDA8B,GU3Da,CACb7C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SZmDA+B,Ga5Da,CACb9C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SfoDAgC,GgB7Da,CACb/C,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SlBqDAiC,GmB9Da,CACbhD,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SrBsDAkC,GsB/Da,CACbjD,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,SxBuDAmC,GyBhEa,CACblD,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,S3BwDAoC,G4BjEa,CACbnD,I,OACAgB,YCIa,CACbf,Q,OACAC,U,OACAC,K,OACAC,U,OACAC,O,OACAC,e,OACAC,U,OACAC,W,OACAC,O,QDZAQ,aEAa,CACbP,U,OACAC,O,OACAC,M,OACAC,Q,OACAC,S,OACAC,M,UCNIR,GAAY,CAChB,CACE6C,KAAM,KACNC,KAAM,qCAER,CACED,KAAM,KACNC,KAAM,0BAER,CACED,KAAM,KACNC,KAAM,kBAER,CACED,KAAM,KACNC,KAAM,sBAER,CACED,KAAM,KACNC,KAAM,gBAER,CACED,KAAM,KACNC,KAAM,wBAER,CACED,KAAM,KACNC,KAAM,qBAER,CACED,KAAM,KACNC,KAAM,2CAER,CACED,KAAM,KACNC,KAAM,gDAER,CACED,KAAM,KACNC,KAAM,sBAER,CACED,KAAM,KACNC,KAAM,4CAER,CACED,KAAM,KACNC,KAAM,oBAER,CACED,KAAM,KACNC,KAAM,6BAER,CACED,KAAM,KACNC,KAAM,wDAER,CACED,KAAM,KACNC,KAAM,wBAER,CACED,KAAM,KACNC,KAAM,0CAER,CACED,KAAM,KACNC,KAAM,sCAIVC,IACGC,IAAIC,KACJD,IAAIE,KACJF,IAAIG,KACJC,KAAK,CACJC,aACAC,IAAK,KACLC,YAAa,KACbC,GAAI,CAAC,MAAO,cAAe,gBAC3BC,UAAW,QAKAV,EAAf,E,cAAeA,IC9ETW,GAAcC,QACW,cAA7BC,OAAOC,SAASC,UAEe,UAA7BF,OAAOC,SAASC,UAEhBF,OAAOC,SAASC,SAASC,MAAM,2DAqCnC,SAASC,GAAgBC,EAAOC,GAC9BC,UAAUC,cACPC,SAASJ,GACTK,MAAK,SAAAC,GACJA,EAAaC,cAAgB,WAC3B,IAAMC,EAAmBF,EAAaG,WACd,MAApBD,IAGJA,EAAiBE,cAAgB,WACA,cAA3BF,EAAiBG,QACfT,UAAUC,cAAcS,YAI1BC,QAAQC,IACN,iHAKEb,GAAUA,EAAOc,UACnBd,EAAOc,SAAST,KAMlBO,QAAQC,IAAI,sCAGRb,GAAUA,EAAOe,WACnBf,EAAOe,UAAUV,WAO5BW,OAAM,SAAAC,GACLL,QAAQK,MAAM,4CAA6CA,M,mJC1F3DC,GAAO,SAACC,EAAOC,EAASC,GAC5B,IAAMC,EAAQH,EAAMI,WAAU,SAAAC,GAAI,OAAIA,EAAKC,KAAOL,EAAQK,MACpDC,EAAWJ,EAAQD,EACzB,KAAIK,EAAW,GAAKA,IAAaP,EAAMQ,QAAvC,CACA,IAAMC,EAAU,CAACN,EAAOI,GAAUG,MAAK,SAACC,EAAGC,GAAJ,OAAUD,EAAIC,KACrDZ,EAAMa,OAAOJ,EAAQ,GAAI,EAAGT,EAAMS,EAAQ,IAAKT,EAAMS,EAAQ,OAGzDK,GAAW,SAAAC,GAEfA,EAAMA,EAAIC,QADa,oCACW,SAACC,EAAGC,EAAGC,EAAGP,GAAV,OAAgBM,EAAIA,EAAIC,EAAIA,EAAIP,EAAIA,KACtE,IAAMQ,EAAS,4CAA4CC,KAAKN,GAChE,OAAOK,EACH,CACEF,EAAGI,SAASF,EAAO,GAAI,IACvBD,EAAGG,SAASF,EAAO,GAAI,IACvBR,EAAGU,SAASF,EAAO,GAAI,KAEzB,MAwBAG,GAAW,SAAAC,GAAQ,OAAIA,EAAS,CAAEC,KAAM,eAExCC,GAAU,SAACF,EAAUG,EAAKC,GAC9BJ,EAAS,CACPC,KAAM,WACNI,QAAS,CACPF,MACAC,WAIJL,GAASC,IAiDPM,GAAiB,KACfC,GAAY,SAACC,EAASC,EAAYC,EAAST,GAC/C,IAAGK,GAGH,OAAO,IAAIK,SAAQ,SAAAC,GACjBH,EAAWI,QAAQC,WAAW,GAC9BL,EAAWI,QAAQE,QAEnBT,GAAiBU,YAAW,WAC1BC,KAAYT,EAAQK,QAAS,CAC3BK,MAAO,EACPC,SAAS,EACTC,YAAY,IACX3D,MAAK,SAAA4D,GACN,IAAMC,EAAQD,EAAOE,UAAU,aAAcb,EAAU,KACjDc,EAAM,IAAIC,GAAM,CACpBC,YAAa,WACbC,KAAM,KACNC,OAAiB,kBAAT3B,EAA2B,CAACoB,EAAOQ,MAAOR,EAAOS,QAAU,OAG/DC,EAAYP,EAAIQ,SAASC,SAASC,WAClCC,EAAaX,EAAIQ,SAASC,SAASG,YAEnCC,EAAaN,EAAYV,EAAOQ,MAChCS,EAAcH,EAAad,EAAOS,OAClCS,EAAQF,EAAaC,EAAcA,EAAcD,EAEjDG,EAAcnB,EAAOQ,MAAQU,EAC7BE,EAAepB,EAAOS,OAASS,EAEjCG,EAAU,EACVC,EAAU,EAED,kBAAT1C,IACFyC,GAAWX,EAAYS,GAAe,EACtCG,GAAWR,EAAaM,GAAgB,GAG1CjB,EAAIoB,SAAStB,EAAO,OAAQoB,EAASC,EAASH,EAAaC,EAAc,KAAM,QAC/EjB,EAAIqB,KAAJ,mBAAqBC,KAAKC,MAA1B,SACAzC,GAAiB,KACjBM,SAED,SAIHoC,GAA0B,KACxBC,GAAqB,SAACzC,EAASC,EAAYC,GAC/C,IAAGsC,GAGH,OAAO,IAAIrC,SAAQ,SAAAC,GACjBH,EAAWI,QAAQC,WAAW,GAC9BL,EAAWI,QAAQE,QAEnBiC,GAA0BhC,YAAW,WACnCC,KAAYT,EAAQK,QAAS,CAC3BK,MAAO,EACPC,SAAS,EACTC,YAAY,IACX3D,MAAK,SAAA4D,GACN,IAAMC,EAAQD,EAAOE,UAAU,aAAcb,EAAU,KACjDc,EAAM,IAAIC,GAAM,CACpBC,YAAa,WACbC,KAAM,KACNC,OAAQ,OAGJO,EAAaX,EAAIQ,SAASC,SAASG,YACnCI,EAAchB,EAAIQ,SAASC,SAASC,WACpCO,EAAgBpB,EAAOS,OAASU,EAAenB,EAAOQ,MACxDqB,EAAY,EACZC,EAAaV,EAKjB,IAHAjB,EAAIoB,SAAStB,EAAO,OAAQ,EAAG4B,EAAWV,EAAaC,GACvDU,GAAchB,EAEPgB,GAAc,GACnBD,EAAYC,EAAaV,EACzBjB,EAAI4B,UACJ5B,EAAIoB,SAAStB,EAAO,OAAQ,EAAG4B,EAAWV,EAAaC,GACvDU,GAAchB,EAGhBX,EAAIqB,KAAJ,mBAAqBC,KAAKC,MAA1B,SACAC,GAA0B,KAC1BpC,SAED,SAIDyC,GAAa,SAAC,GAAmD,IAAjDC,EAAgD,EAAhDA,KAAgD,IAA1CC,gBAA0C,MAA/B,KAA+B,MAAzBC,WACrCC,OAD8D,SACtC,eAAiB,YAE/C,OAAOC,KAAMJ,GAAMK,OAAOJ,EAASK,OAAO,EAAG,IAAIhC,OAAO6B,IAGpDI,GAAkB,SAAC,EAAyCC,GAAO,IAA9CC,EAA6C,EAA7CA,UAAWC,EAAkC,EAAlCA,QAAkC,IAAzBT,gBAAyB,MAAd,KAAc,EAChEU,EAAK,UAAMP,KAAMK,GACpBJ,OAAOJ,EAASK,OAAO,EAAG,IAC1BhC,OAAO,cAEJsC,EAAMR,KAAMM,GAASG,UAAf,UACLT,KAAMM,GAASL,OAAOJ,EAASK,OAAO,EAAG,IAAIhC,OAAO,cACvDkC,EAAE,wBAEN,MAAM,GAAN,OAAUG,EAAV,cAAqBC,IAMjBE,GAAc,SAACC,GAAD,iEACfA,IAA8B,IAAnBA,EAAQC,SCzNlBC,GAAe,CACnBC,KAAM,CACJC,OAAO,CACL,WAAY,CAChB,oDACA,CACC,OAAU,CACT,MAAO,gBACP,QAAS,UAEV,qBAAsB,CACrB,MAAO,qBACP,QAAS,UAEV,0BAA2B,CAC1B,MAAO,0BACP,QAAS,UAEV,SAAY,CACX,MAAO,kBACP,QAAS,UAEV,UAAa,CACZ,MAAO,mBACP,QAAS,YAIP,SAAU,CACR,CACE,QAAS,gBAEX,CACE,QAAS,SACTC,UAAU,CAAC,CAAC,YAAa,KAAM,SAAS,KACxCC,WAAY,CAAC,CAAC,YAAa,KAAM,SAAS,KAC1CC,QAAS,MAIf/L,QAAS,CACPgM,QAAS,UACTC,MAAO,GACPC,UAAW,GACXC,SAAU,GACVC,SAAU,GACVL,QAAS,CACPM,MAAO,GACPC,MAAO,GACPC,MAAO,IAETC,MAAO,GACPC,QAAS,GACTC,MAAO,IAETC,SAAU,CACR,QAAU,EACVX,QAAS,YAEXD,QAAS,CACP,QAAU,EACVC,QAAS,WAEX/L,UAAW,CACTwL,QAAQ,EACRO,QAAS,YACTY,KAAM,IAER1M,KAAM,CACJuL,QAAQ,EACRO,QAAS,kBACTa,MAAO,IAET1M,UAAW,CACTsL,QAAQ,EACRO,QAAS,YACTa,MAAO,IAETzM,OAAQ,CACNqL,QAAQ,EACRO,QAAS,kBACTa,MAAO,IAETxM,eAAgB,CACdoL,QAAQ,EACRO,QAAS,iBACTa,MAAO,IAETC,OAAQ,CACNrB,QAAQ,EACRO,QAAS,SACTa,MAAO,IAETE,YAAa,CACXtB,QAAQ,EACRO,QAAS,cACTa,MAAO,IAETvM,UAAW,CACTmL,QAAQ,EACRO,QAAS,YACTa,MAAO,IAETtM,WAAY,CACVkL,QAAQ,EACRO,QAAS,aACTa,MAAO,IAETrM,OAAQ,CACNiL,QAAQ,EACRO,QAAS,uBACTa,MAAO,KAGXG,MAAO,CACLC,OAAQ,OACRC,KAAM,CACJC,OAAQ,IAEVzM,OAAQ,CACN0M,WAAY,UACZC,QAAS,UACTC,OAAQ,WAEZ,aAAgB,CACf,KAAQ,CACP,CACC,YACA,OACA,YACA,YAED,CACC,UACA,YACA,SACA,kBAED,CACC,SACA,eAGF,QAAW,CACV,CACC,SACA,YACA,UACA,SACA,kBAED,CACC,OACA,YACA,WACA,eAGF,OAAU,CACT,CACC,YACA,UAED,CACC,SACA,iBACA,YACA,aACA,WAED,CACC,OACA,YACA,aAGF,SAAY,CACX,CACC,SACA,iBACA,YACA,WAED,CACC,YACA,OACA,YACA,SACA,WACA,eAGF,OAAU,CACT,CACC,SACA,iBACA,WAED,CACC,YACA,OACA,YACA,SACA,WACA,YACA,eAGF,OAAU,CACT,CACC,SACA,iBACA,YACA,WAED,CACC,YACA,OACA,YACA,SACA,WACA,iBAKHzM,SAAU,CACR6J,SAAU,OAIR6C,GAAU,SAACrI,EAAD,GAA+B,IACzC2H,EADoBzF,EAAoB,EAApBA,KAAMI,EAAc,EAAdA,QAExBgG,EAAWC,KAAKC,MAAMD,KAAKE,UAAUzI,IAE3C,OAAQkC,GACN,IAAK,kBACH,OAAOwG,KAAI,gBAAKJ,GAAN,eAA0BhG,EAAQF,KAAOE,EAAQD,OAC7D,IAAK,WAGH,OAFAsF,EAAQgB,KAAI,gBAAKL,GAAN,UAAqBhG,EAAQF,KAAO,KACzCwG,KAAKtG,EAAQD,OACZqG,KAAI,gBAAKJ,GAAN,UAAqBhG,EAAQF,KAAOuF,GAChD,IAAK,cAGH,OAFAA,EAAQgB,KAAI,gBAAKL,GAAN,UAAqBhG,EAAQF,KAAO,IAC/CyG,KAAOlB,GAAO,SAAAmB,GAAC,OAAIA,EAAE/H,KAAOuB,EAAQD,MAAMtB,MACnC2H,KAAI,gBAAKJ,GAAN,UAAqBhG,EAAQF,KAAOuF,GAChD,IAAK,eAGH,OAFAA,EAAQgB,KAAI,gBAAKL,GAAN,UAAqBhG,EAAQF,KAAO,IAC/C5B,GAAKmH,EAAOrF,EAAQD,OAAQ,GACrBqG,KAAI,gBAAKJ,GAAN,UAAqBhG,EAAQF,KAAOuF,GAChD,IAAK,iBAGH,OAFAA,EAAQgB,KAAI,gBAAKL,GAAN,UAAqBhG,EAAQF,KAAO,IAC/C5B,GAAKmH,EAAOrF,EAAQD,MAAO,GACpBqG,KAAI,gBAAKJ,GAAN,UAAqBhG,EAAQF,KAAOuF,GAChD,IAAK,WACH,OAAOe,KAAI,gBAAKJ,GAAYhG,EAAQF,IAAKE,EAAQD,OACnD,IAAK,YAEH,OADA0G,aAAaC,QAAQ,QAAST,KAAKE,UAAUH,IACtCA,EACT,IAAK,cACH,GAAgB,OAAZhG,EAAkB,OAAOkE,GAE7B,cAAsByC,OAAOC,KAAK1C,GAAaC,MAA/C,eAAsD,CAAjD,IAAMH,EAAO,KACVA,KAAWhE,EAAQmE,OACvBnE,EAAQmE,KAAKH,GAAWE,GAAaC,KAAKH,IAI9C,OAAO,gBACFgC,EADL,GAEKhG,GAEP,IAAK,iBACH,OAAO,gBACFgG,EADL,GAEKa,IAEP,IAAK,QACH,OAAO3C,GACT,QACE,OAAO8B,IAIPc,GAAaC,wBAAc7C,IACzB8C,GAAaF,GAAbE,SAOKC,GALS,SAAC,GAAkB,IAAhBC,EAAe,EAAfA,SAAe,EACZC,qBAAWpB,GAAS7B,IADR,oBAC/BxG,EAD+B,KACxBiC,EADwB,KAEtC,OAAO,kBAACqH,GAAD,CAAUjH,MAAO,CAAErC,QAAOiC,aAAauH,IAMjCJ,IAFYA,GAAWM,SAEvBN,IC7STO,GAAcC,IAAMP,cAAc,MAChCC,GAAaK,GAAbL,SAuBKO,GArBS,SAAC,GAAkB,IAAhBL,EAAe,EAAfA,SAAe,EACRM,mBAAS,MADD,oBAC/BrH,EAD+B,KACtBsH,EADsB,OAEFD,mBAAS,MAFP,oBAE/BpH,EAF+B,KAEnBsH,EAFmB,OAGUF,oBAAS,GAHnB,oBAG/BG,EAH+B,KAGZC,EAHY,KAKtC,OACE,kBAAC,GAAD,CACE7H,MAAO,CACLI,UACAsH,aACArH,aACAsH,gBACAC,oBACAC,uBAGDV,IAQQG,IAFaA,GAAYD,SAEzBC,I,oBCLAQ,GAtBE,SAAC,GAAD,IAAGC,EAAH,EAAGA,UAAWC,EAAd,EAAcA,MAAOhI,EAArB,EAAqBA,MAAOiI,EAA5B,EAA4BA,SAAUC,EAAtC,EAAsCA,QAASC,EAA/C,EAA+CA,WAA/C,OACf,yBAAKJ,UAAW,sBAAuBA,EAAWK,MAAO,CAACC,QAAQ,aAC/DL,GACC,2BAAOD,UAAU,oEACdC,GAGL,yBAAKD,UAAU,4GACb,4BACEA,UAAU,4EACV/H,MAAOA,EACPiI,SAAU,SAAAK,GAAC,OAAIL,EAASK,EAAEC,OAAOvI,SAEhCkI,EAAQM,IAAIL,IAEf,yBAAKJ,UAAU,oGACb,uBAAGA,UAAU,kBAAb,mBCkCOU,GAjDA,SAAC,GAAyC,IAAvCC,EAAsC,EAAtCA,KAAMC,EAAgC,EAAhCA,WAAYC,EAAoB,EAApBA,cAE5BC,EAAW,SAACpC,GAChB,IAAMlI,EAAQmK,EAAKlK,WAAU,SAACsK,GAAD,OAASA,EAAI/I,MAAQ4I,KAE9ClC,EAAI,GAAKlI,EAAQ,GACnBqK,EAAcF,EAAKnK,EAAQ,GAAGwB,KAG5B0G,EAAI,GAAKlI,EAAQmK,EAAK9J,OAAS,GACjCgK,EAAcF,EAAKnK,EAAQ,GAAGwB,MAUlC,OACE,yBAAKgI,UAAU,+BACb,yBACEA,UAAU,yEACVgB,QAAS,kBAAMF,GAAU,KAEzB,uBAAGd,UAAU,kBAAb,iBAGA,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAM,GACNgB,YAAY,GACZhJ,MAAO2I,EACPV,SAAU,SAAAgB,GAAML,EAAcK,IAC9Bf,QAAWQ,EACXP,WAvBU,SAACW,EAAKvK,GACtB,OACE,4BAAQwB,IAAK+I,EAAI/I,IAAKC,MAAO8I,EAAI/I,KAC9B+I,EAAIjN,MAAQ,UAuBf,yBACEkM,UAAU,yEACVgB,QAAS,kBAAMF,EAAS,KAExB,uBAAGd,UAAU,kBAAb,oB,wCC3CFmB,GAAqB,CAAC,KAAM,KAAM,KAAM,KAAM,MAU/BC,G,+MACjBxL,MAAQ,CACNyL,gBAAiB,M,EAGnBC,sBAAwB,SAACrJ,EAAOzB,GAC9B,IAAI+K,EAAY,EAAKC,MAAMvJ,MAK3B,IAJI,EAAKuJ,MAAMvJ,OAAUwJ,MAAMC,QAAQ,EAAKF,MAAMvJ,SAC9CsJ,EAAY,IAGVI,QAAOJ,IAAY/K,GACvB+K,EAAU/C,KAAK,IAEjB+C,EAAU/K,GAASyB,EAEnB,EAAKuJ,MAAMtB,SAASqB,I,EAGtBK,cAAgB,SAACC,GACf,IAAIN,EAAY,EAAKC,MAAMvJ,MACvB,EAAKuJ,MAAMvJ,OAAUwJ,MAAMC,QAAQ,EAAKF,MAAMvJ,SAChDsJ,EAAY,CACV,CACE,YAAaM,EACb,SAAU,MAKhB,IAAIC,EAAqBP,EAAU9K,WAAU,SAAAiI,GAAC,OAAIA,EAAE,eAAiBmD,KACrE,GAAGC,EAAqB,EAAE,CACxB,IAAIC,EAAU,CACZ,YAAaF,EACb,SAAU,IAEZN,EAAU/C,KAAKuD,GACf,EAAKP,MAAMtB,SAASqB,GAGtB,OADAO,EAAqBP,EAAU9K,WAAU,SAAAiI,GAAC,OAAIA,EAAE,eAAiBmD,M,EAGnEG,qBAAuB,SAACH,GACtB,EAAKD,cAAcC,GAEnB,EAAKI,SAAS,CACZZ,gBAAiBQ,K,EAGrBK,iBAAmB,SAACL,EAAM5J,GACxB,IAAI6J,EAAqB,EAAKF,cAAcC,GACxCN,EAAY,EAAKC,MAAMvJ,MAE3BsJ,EAAUO,GAAoB,UAAY7J,EAC1C,EAAKuJ,MAAMtB,SAASqB,I,EAGtBY,UAAY,SAACzD,EAAGlI,GAAJ,OACV,yBAAKwB,IAAK,UAAUxB,EAAO6J,MAAO,CAACC,QAAS,SAC1C,2BACEN,UAAU,wKACVlI,KAAM,EAAK0J,MAAM1J,KACjBsK,SAAU,EAAKZ,MAAMY,SACrBnK,MAAO,EAAKuJ,MAAMvJ,MAAMzB,GACxB0J,SAAU,SAAAK,GAAC,OAAI,EAAKe,sBAAsBf,EAAEC,OAAOvI,MAAOzB,IAC1DyK,YAAa,EAAKO,MAAMP,YACxBjJ,IAAK,SAASxB,IAEdmL,QAAO,EAAKH,MAAMvJ,QAAQ,EAAM,GAClC,4BACEH,KAAK,SACLkJ,QAAS,WAAKW,UAAS,EAAKH,MAAMvJ,MAAOzB,GAAO,EAAKgL,MAAMtB,SAAS,EAAKsB,MAAMvJ,QAC/E+H,UAAU,iFACVhI,IAAK,UAAUxB,GAEf,yBAAKwJ,UAAU,oBAAoBhI,IAAK,gBAAgBxB,GACtD,uBAAGwJ,UAAU,qCAAqChI,IAAK,UAAUxB,GAAjE,c,wDAME,IAAD,OACP,OACJ,yBAAKwJ,UAAS,+BAA0BqC,KAAKb,MAAMxB,YAC1CqC,KAAKb,MAAMvB,OACV,2BAAOD,UAAU,oEACdqC,KAAKb,MAAMvB,OAGK,cAAlBoC,KAAKb,MAAM1J,KACZ,yBAAKuI,MAAO,CAACC,QAAS,SACpB,4BAAQrI,MAAOoK,KAAKzM,MAAMyL,gBAAiBnB,SAAU,SAACoC,GAAD,OAAW,EAAKN,qBAAqBM,EAAM9B,OAAOvI,SApG3F,WACxB,IAAIkI,EAAU,GAId,OAHAgB,GAAmBoB,SAAQ,SAACC,GAC1BrC,EAAQ3B,KAAK,4BAAQxG,IAAKwK,EAAGvK,MAAOuK,GAAIA,OAEnCrC,EAgGUsC,IAEL,2BACEzC,UAAU,wKACVlI,KAAMuK,KAAKb,MAAM1J,KACjBsK,SAAUC,KAAKb,MAAMY,SACrBnK,MAAOoK,KAAKb,MAAMvJ,MAAQoK,KAAKb,MAAMvJ,MAAMxB,WAAU,SAAAiI,GAAC,OAAIA,EAAE,eAAiB,EAAK9I,MAAMyL,oBAAkB,EAAIgB,KAAKb,MAAMvJ,MAAMxB,WAAU,SAAAiI,GAAC,OAAIA,EAAE,eAAiB,EAAK9I,MAAMyL,mBAAoB,GAAI,UACpMnB,SAAU,SAAAK,GAAC,OAAI,EAAK2B,iBAAiB,EAAKtM,MAAMyL,gBAAiBd,EAAEC,OAAOvI,QAC1EgJ,YAAaoB,KAAKb,MAAMP,eAIT,cAAlBoB,KAAKb,MAAM1J,KACV,6BACGuK,KAAKb,MAAMvJ,MAAMwI,IAAI4B,KAAKF,WAC3B,yBAAKnK,IAAI,cAAcqI,MAAO,CAACC,QAAS,SACtC,4BACExI,KAAK,SACLkJ,QAAS,WAAK,EAAKQ,MAAMvJ,MAAMuG,KAAK,IAAI,EAAKgD,MAAMtB,SAAS,EAAKsB,MAAMvJ,QACvE+H,UAAU,iFACVhI,IAAI,eAEJ,yBAAKgI,UAAU,oBAAoBhI,IAAI,kBACrC,uBAAGgI,UAAU,qCAAqChI,IAAI,YAAtD,WAMR,2BACEgI,UAAU,wKACVlI,KAAMuK,KAAKb,MAAM1J,KACjBsK,SAAUC,KAAKb,MAAMY,SACrBnK,MAAOoK,KAAKb,MAAMvJ,MAClBiI,SAAU,SAAAK,GAAC,OAAI,EAAKiB,MAAMtB,SAASK,EAAEC,OAAOvI,QAC5CgJ,YAAaoB,KAAKb,MAAMP,mB,GAhIDzB,IAAMkD,WCiF9BC,GAxFI,SAAC,GAAwB,IAAtBtG,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAClBvE,EAAMiH,aAAe,eAArBjH,EAEFkH,EAAW,SAACC,EAAMC,EAAO7B,GAA2B,IAAxBpJ,EAAuB,uDAAlB,KAAMnB,EAAY,uDAAT,KAC1CqM,EAAMrB,OAAMtF,EAAMyG,EAAK,IAAIC,EAAO,MAC3B,OAARC,IACgB,kBAAP9B,GAAiC,kBAAPA,EAClCS,OAAMtF,EAAMyG,EAAK,IAAIC,EAAO,IACP,kBAAP7B,IACTO,MAAMC,QAAQR,GACfS,OAAMtF,EAAMyG,EAAK,IAAIC,EAAO,IAE5BpB,OAAMtF,EAAMyG,EAAK,IAAIC,EAAO,MAKpC7C,EAAS,QAAQ4C,EAAK,IAAIC,EAAO7B,GAC9BvK,GACDuJ,EAAS,QAAQ4C,EAAK,UAAWnM,GAEhCmB,GACDoI,EAAS,QAAQ4C,EAAK,YAAahL,IAGvC,OACE,6BACE,kBAAC,GAAD,CACEkI,UAAU,OACViB,YAAY,UACZhJ,MAAOoE,EAAK3L,QAAQgM,QACpBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,uBAAwBgB,MAGlD,wBAAIlB,UAAU,SAEd,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,0BACTsF,YAAY,0BACZhJ,MAAO0J,OAAMtF,EAAM,uCAAwC,IAC3D6D,SAAU,SAAAgB,GAAM2B,EAAS,4BAA6B,aAAc3B,EAAG,cAAe+B,eAGxF,yBAAKjD,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,2BACTsF,YAAY,OACZhJ,MAAO0J,OAAMtF,EAAK,gCAAiC,IACnD6D,SAAU,SAAAgB,GAAC,OAAI2B,EAAS,sBAAuB,YAAa3B,IAC5DpJ,KAAK,cAGP,kBAAC,GAAD,CACEkI,UAAU,OACVC,MAAOtE,EAAE,0BACTsF,YAAY,MACZhJ,MAAO0J,OAAMtF,EAAK,iCAAkC,IACpD6D,SAAU,SAAAgB,GAAC,OAAI2B,EAAS,sBAAuB,aAAc3B,IAC7DpJ,KAAK,eAIT,kBAAC,GAAD,CACEkI,UAAU,OACVC,MAAOtE,EAAE,0BACTsF,YAAY,2BACZhJ,MAAO0J,OAAMtF,EAAM,kCAAmC,IACtD6D,SAAU,SAAAgB,GAAM2B,EAAS,sBAAuB,cAAe3B,MAGjE,wBAAIlB,UAAU,SAEd,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,yBACTsF,YAAY,aACZhJ,MAAO0J,OAAMtF,EAAK,6BAA8B,IAChD6D,SAAU,SAAAgB,GAAC,OAAI2B,EAAS,sBAAuB,SAAU3B,IACzDgC,QAAS,aACTpL,KAAK,gB,UC7DEqL,GAxBE,SAAC,GAA0D,IAAxDC,EAAuD,EAAvDA,QAASlD,EAA8C,EAA9CA,SAA8C,IAApCmD,YAAoC,MAA7B,QAA6B,MAApBC,YAAoB,MAAb,OAAa,EACzE,OACE,yBACEtD,UAAU,2KACVK,MAAO,CAAE3G,MAAO4J,EAAM3J,OAAQ2J,IAE9B,2BACExL,KAAK,WACLuI,MAAO,CAAE3G,MAAO4J,EAAM3J,OAAQ2J,GAC9BtD,UAAU,yCACVoD,QAASA,EACTlD,SAAU,SAAAK,GAAC,OAAIL,EAASK,EAAEC,OAAO4C,YAEnC,uBACEpD,UAAS,kCACPoD,EAAU,cAAgB,YADnB,2BAIRC,KC2CME,GAzDK,SAAC,GAA+E,IAA7E1L,EAA4E,EAA5EA,SAAU2L,EAAkE,EAAlEA,MAAOC,EAA2D,EAA3DA,WAAY/M,EAA+C,EAA/CA,KAAMgN,EAAyC,EAAzCA,KAAMxD,EAAmC,EAAnCA,SAAUpI,EAAyB,EAAzBA,KAAM6L,EAAmB,EAAnBA,aACtEhI,EAAMiH,eAANjH,EAER,OACE,yBAAKqE,UAAU,wBACb,yBAAKA,UAAU,qBACd2D,EAAeA,EAAaF,EAAY/M,EAAMwJ,GAC7C,kBAAC,GAAD,CACEoD,KAAK,UACLF,QAAS1M,EAAKyF,OACd+D,SAAU,SAAAgB,GACRhB,EAAS,GAAD,OAAIuD,EAAJ,UAAwBvC,MAKpC,4BACEpJ,KAAK,SACLkJ,QAAS,kBRsCA,SAACnJ,EAAUG,EAAKC,GACjCJ,EAAS,CACPC,KAAM,cACNI,QAAS,CACPF,MACAC,WAIJL,GAASC,GQ/Cc+L,CAAW/L,EAAUC,EAAMpB,IAC1CsJ,UAAU,qFAEV,yBAAKA,UAAU,qBACb,uBAAGA,UAAU,2CAAb,UACA,0BAAMA,UAAU,WAAWrE,EAAE,4BAKnC,yBAAKqE,UAAU,SACXwD,GACA,4BACE1L,KAAK,SACLkJ,QAAS,kBRoCF,SAACnJ,EAAUG,EAAKC,GACjCJ,EAAS,CACPC,KAAM,eACNI,QAAS,CACPF,MACAC,WAIJL,GAASC,GQ7CgBgM,CAAWhM,EAAUC,EAAMpB,IAC1CsJ,UAAU,uFAEV,yBAAKA,UAAU,qBACb,uBAAGA,UAAU,sCAAb,mBAKJ0D,GACA,4BACE5L,KAAK,SACLkJ,QAAS,kBRoCA,SAACnJ,EAAUG,EAAKC,GACnCJ,EAAS,CACPC,KAAM,iBACNI,QAAS,CACPF,MACAC,WAIJL,GAASC,GQ7CgBiM,CAAajM,EAAUC,EAAMpB,IAC5CsJ,UAAU,kFAEV,yBAAKA,UAAU,qBACb,uBAAGA,UAAU,sCAAb,uBChCC+D,GAnBO,SAAC,GAAkB,IAAhBC,EAAe,EAAfA,SACfrI,EAAMiH,eAANjH,EAER,OACE,6BACE,4BACE7D,KAAK,SACLkJ,QAASgD,EACThE,UAAU,kFAEV,yBAAKA,UAAU,qBACb,uBAAGA,UAAU,2CAAb,OACA,0BAAMA,UAAU,WAAWrE,EAAE,0BCIxBsI,GAhBK,SAAC,GAAyC,IAAvCC,EAAsC,EAAtCA,MAAOxH,EAA+B,EAA/BA,QAASyH,EAAsB,EAAtBA,OAAQC,EAAc,EAAdA,QACrCzI,EAAMiH,eAANjH,EAER,OACE,yBACEqE,UAAU,mDACVgB,QAAS,kBAAMoD,GAASD,KAExB,wBAAInE,UAAU,uBACQ,qBAAZtD,EAA0BwH,EAAQvI,EAAE,WAAY,CAAEe,aAE5D,uBAAGsD,UAAU,kBAAkBmE,EAAS,cAAgB,iBCqCxDE,GAAO,SAAC,GAAyC,IAAvC3N,EAAsC,EAAtCA,KAAMwJ,EAAgC,EAAhCA,SAAgC,IAAtBuD,kBAAsB,MAAT,GAAS,EAC5C9H,EAAMiH,aAAe,CAAC,cAAe,QAArCjH,EAER,OACE,6BACE,kBAAC,GAAD,CACEqE,UAAU,OACVC,MAAOtE,EAAE,+BACTsF,YAAY,eACZhJ,MAAOvB,EAAK4N,cACZpE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,iBAA+BvC,MAGxD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,iCACTsF,YAAY,UACZhJ,MAAOvB,EAAK6N,gBACZrE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,mBAAiCvC,MAG1D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,+BACTsF,YAAY,KACZhJ,MAAOvB,EAAK8N,cACZtE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,iBAA+BvC,MAGxD,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,gCACTsF,YAAY,SACZhJ,MAAOvB,EAAK+N,eACZvE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,kBAAgCvC,MAGzD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,4BACTsF,YAAY,UACZhJ,MAAOvB,EAAKgO,WACZxE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,cAA4BvC,OAIvD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,wBACTsF,YAAY,4BACZhJ,MAAOvB,EAAKiO,OACZzE,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,UAAwBvC,QAMjDgC,GAAU,SAAC,GAA2B,IAAzBxG,EAAwB,EAAxBA,QAAS7E,EAAe,EAAfA,SACtBlB,EAAK,KAAKiO,eAD2B,EAEflF,oBAAS,GAFM,oBAElCyE,EAFkC,KAE1BC,EAF0B,OAGjB1E,mBAAS,CAC/B,MAAO/I,EACP,QAAS,gBACTkO,eAAe,CACb,MAAOlO,EAAG,kBACV,QAAS,4BACT,aAAgB,cAElB8N,eAAgB,GAChBH,cAAe,GACfE,cAAe,GACfD,gBAAiB,GACjBG,WAAY,GACZI,YAAa,GACbH,OAAQ,KAjB+B,oBAGlCjO,EAHkC,KAG5BkI,EAH4B,KA+CzC,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAhCP,SAAClI,EAAKC,GAAN,OAAgB2G,EAAQN,KAAI,gBAAK5H,GAAQsB,EAAKC,OAkCzD,kBAAC,GAAD,CAAe+L,SAjCJ,WACf,IAAIrN,EAAK,KAAKiO,eACe,KAAxBlO,EAAK+N,iBAEV1M,GAAQF,EAAU,mCAAoCnB,GAEtDkI,EAAQ,CACN,MAAOjI,EACP,QAAS,gBACTkO,eAAe,CACb,MAAOlO,EAAG,kBACV,QAAS,4BACT,aAAgB,cAElB8N,eAAgB,GAChBH,cAAe,GACfE,cAAe,GACfD,gBAAiB,GACjBG,WAAY,GACZI,YAAa,GACbH,OAAQ,KAGVP,GAAQ,UAgBNW,GAAmB,SAACtB,EAAY/M,EAAMwJ,GAE1C,OACE,kBAAC,GAAD,CACEoD,KAAK,UACLF,QAAU1M,GAAQA,EAAKmO,gBAAkBnO,EAAKmO,eAAeG,cAAiBrK,KAAKyD,MAAM1H,EAAKmO,eAAeG,cAAgBrK,KAAKyD,MAAM,IAAIzD,MAAS,EACrJuF,SAAU,SAAAgB,GACR,IAAI8D,EAAe,aAChB9D,IAAG8D,EAAe,cACrB9E,EAAS,GAAD,OAAIuD,EAAJ,+BAA6CuB,OAMvDC,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,2CAAuCjN,EAAvC,MAEhB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOxN,EAAK4N,eAAiB5N,EAAK+N,eAAgBL,QAASA,EAASD,OAAQA,IAEzF,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,IAElD,kBAAC,GAAD,CACE5L,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,mCACL6L,aAAcoB,QAOTG,GArMI,SAAC,GAAwB,IAAtB7I,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAElBrI,EADQsN,qBAAWnG,IACnBnH,SAER,OACE,oCACE,yBAAKmI,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CAAUoD,QAAS/G,EAAKI,QAAQN,OAAQ+D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,sBAAuBgB,OAEzF,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAOoE,EAAKI,QAAQC,QACpBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,uBAAwBgB,QAKtD,wBAAIlB,UAAU,SAEb3D,EAAKC,OAAO,UAAU,GAAGG,SAAWJ,EAAKC,OAAO,UAAU,GAAGG,QAAQgE,KAAI,SAAC/B,EAAGlI,GAAJ,OACxE,kBAAC,GAAD,CACEqB,SAAUA,EACV2L,MAAiB,IAAVhN,EACPA,MAAOA,EACPE,KAAMgI,EACN1G,IAAK0G,EAAE,OACPgF,KAAMlN,IAAU6F,EAAKC,OAAO,UAAU,GAAGG,QAAQ5F,OAAS,EAC1DqJ,SAAUA,OAId,kBAAC,GAAD,CAASxD,QAASL,EAAKI,QAAQC,QAAS7E,SAAUA,MCMlDwM,GAAO,SAAC,GAAyC,IAAvC3N,EAAsC,EAAtCA,KAAMwJ,EAAgC,EAAhCA,SAAgC,IAAtBuD,kBAAsB,MAAT,GAAS,EAC5C9H,EAAMiH,aAAe,CAAC,cAAe,QAArCjH,EAQR,OACE,6BACE,kBAAC,GAAD,CACEqE,UAAU,OACVC,MAAOtE,EAAE,uBACTsF,YAAY,mBACZhJ,MAAOvB,EAAK0O,UACZlF,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,aAA2BvC,MAGpD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,uBACTsF,YAAY,wBACZhJ,MAAOvB,EAAK0G,MACZ8C,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,SAAuBvC,MAGhD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,6BACTsF,YAAY,oCACZhJ,MAAOvB,EAAKoO,YACZ5E,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,eAA6BvC,IACpDf,QAAW,CAAC,YAAa,YAAa,SACtCC,WAhCoB,SAAC1B,EAAGlI,GAC5B,OACE,4BAAQwB,IAAK0G,EAAGzG,MAAOyG,GACpBA,MAgCH,kBAAC,GAAD,CACEsB,UAAU,OACVC,MAAOtE,EAAE,gCACTsF,YAAY,cACZhJ,MAAOvB,EAAK2O,YACZnF,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,eAA6BvC,QAMtDgC,GAAU,SAAC,GAA2B,IAAzBxG,EAAwB,EAAxBA,QAAS7E,EAAe,EAAfA,SACtBlB,EAAK,KAAKiO,eAD2B,EAEflF,oBAAS,GAFM,oBAElCyE,EAFkC,KAE1BC,EAF0B,OAGjB1E,mBAAS,CAC/B,MAAO/I,EACP,QAAS,eACT0O,YAAa,GACbP,YAAa,YACb1H,MAAO,GACPgI,UAAW,KAT4B,oBAGlC1O,EAHkC,KAG5BkI,EAH4B,KA+BzC,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAxBP,SAAClI,EAAKC,GAAN,OAAgB2G,EAAQN,KAAI,gBAAK5H,GAAQsB,EAAKC,OA0BzD,kBAAC,GAAD,CAAe+L,SAzBJ,WACf,IAAIrN,EAAK,KAAKiO,eACY,KAArBlO,EAAKoO,cAEV/M,GAAQF,EAAU,wCAAyCnB,GAE3DkI,EAAQ,CACN,MAAOjI,EACP,QAAS,eACT0O,YAAa,GACbP,YAAa,YACb1H,MAAO,GACPgI,UAAW,KAGbhB,GAAQ,UAgBNW,GAAmB,SAACtB,EAAY/M,EAAMwJ,GAE1C,OACE,sCAIE+E,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,gDAA4CjN,EAA5C,MAEhB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOxN,EAAKoO,aAAepO,EAAK0O,UAAWhB,QAASA,EAASD,OAAQA,IAElF,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,IAElD,kBAAC,GAAD,CACE5L,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,wCACL6L,aAAcoB,QAOTO,GAnKK,SAAC,GAAwB,IAAtBjJ,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAEnBrI,EADQsN,qBAAWnG,IACnBnH,SAER,OACE,oCACE,yBAAKmI,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CAAUoD,QAAS/G,EAAKgB,SAASlB,OAAQ+D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,uBAAwBgB,OAE3F,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAOoE,EAAKgB,SAASX,QACrBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,wBAAyBgB,QAKvD,wBAAIlB,UAAU,SAEb3D,EAAKC,OAAO,UAAU,GAAGiJ,cAAgBlJ,EAAKC,OAAO,UAAU,GAAGiJ,aAAaC,QAAO,SAAA9G,GAAC,MAAmB,cAAhBA,EAAEoG,eAA4BrE,KAAI,SAAC/B,EAAGlI,GAAJ,OAC3H,kBAAC,GAAD,CACEqB,SAAUA,EACV2L,MAAiB,IAAVhN,EACPA,MAAOA,EACPE,KAAMgI,EACN1G,IAAK0G,EAAE,OACPgF,KAAMlN,IAAU6F,EAAKC,OAAO,UAAU,GAAGiJ,aAAa1O,OAAS,EAC/DqJ,SAAUA,OAId,kBAAC,GAAD,CAASxD,QAASL,EAAKgB,SAASX,QAAS7E,SAAUA,M,UCvB1C4N,GArBU,SAAC,GAAmB,IAAjBzF,EAAgB,EAAhBA,UAC1B,OACE,yBAAKA,UAAWA,GACd,uBAAGA,UAAU,yBACX,kBAAC0F,GAAA,EAAD,CAAOC,QAAQ,oBAAf,cAEE,uBACE3F,UAAU,gCACVQ,OAAO,SACPoF,IAAI,sBACJC,KAAK,oEAJP,4BAFF,qCCaOC,GAjBE,SAAC,GAAD,IAAG7F,EAAH,EAAGA,MAAOgB,EAAV,EAAUA,YAAahJ,EAAvB,EAAuBA,MAAOiI,EAA9B,EAA8BA,SAAUF,EAAxC,EAAwCA,UAAxC,IAAmD+F,YAAnD,MAA0D,EAA1D,SACf,yBAAK/F,UAAS,+BAA0BA,IACtC,2BAAOA,UAAU,oEACdC,GAEH,8BACED,UAAU,oKACV+F,KAAMA,EACN9N,MAAOA,EACPiI,SAAU,SAAAK,GAAC,OAAIL,EAASK,EAAEC,OAAOvI,QACjCgJ,YAAaA,IAGf,kBAAC,GAAD,CAAkBjB,UAAU,WCsC1BgG,GAAc,SAAC,GAAsB,IAArBtP,EAAoB,EAApBA,KAAMwJ,EAAc,EAAdA,SACtBxJ,IACFA,EAAO,IAF+B,IAIhCiF,EAAMiH,aAAe,CAAC,cAAe,QAArCjH,EACR,OACE,6BACE,kBAAC,GAAD,CACEqE,UAAU,OACVC,MAAOtE,EAAE,iCACTsF,YAAY,UACZhJ,MAAO0J,OAAMjL,EAAK,kBAAmB,IACrCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,kBAAmBgB,MAG7C,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,+BACTsF,YAAY,KACZhJ,MAAO0J,OAAMjL,EAAK,gBAAgB,IAClCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,gBAAiBgB,MAG3C,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,gCACTsF,YAAY,SACZhJ,MAAO0J,OAAMjL,EAAK,iBAAiB,IACnCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,iBAAkBgB,SAO9CmD,GAAO,SAAC,GAAkD,IAAhD3N,EAA+C,EAA/CA,KAAMwJ,EAAyC,EAAzCA,SAAyC,IAA/BuD,kBAA+B,MAAlB,GAAkB,MAAdjN,aAAc,MAAR,EAAQ,EAErDmF,EAAMiH,aAAe,CAAC,cAAe,QAArCjH,EAmBR,OACE,6BACY,IAARnF,EAAc,kBAAC,GAAD,CACduP,KAAK,KACL/F,UAAU,OACVC,MAAOtE,EAAE,6BACT1D,MAAO0J,OAAMjL,EAAK,cAAe,IACjCuK,YAAY,4OACZf,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,eAA6BvC,MAC/C,qCAEP,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,sCACTsF,YAAY,aACZhJ,MAAO0J,OAAMjL,EAAK,qBAAsB,IACxCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,sBAAoCvC,MAG7D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,oCACTsF,YAAY,aACZvK,KAAMiL,OAAMjL,EAAM,mBAAoB,IACtCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,oBAAkCvC,MAG3D,kBAAC,GAAD,CACExK,KAAMiL,OAAMjL,EAAM,4BAA6B,IAC/CwJ,SAAU,SAAClI,EAAKC,GAAN,OA/CQ,SAACA,EAAOD,EAAKiO,GACnC,GAAGhO,GAASD,EAAI,CACd,IAAIyE,EAAUkF,OAAMsE,EAAkB,UAAW,IAC7CxJ,EAAQ,WACVA,EAAQ,SAAW,gBACnBA,EAAQ,OAAS/F,EAAK,OAAO,8BAE/B+F,EAAQzE,GAAOC,EAEf0J,OAAMsE,EAAmB,UAAWxJ,GAChCwJ,EAAkB,WACpBA,EAAkB,SAAW,QAC7BA,EAAkB,OAASvP,EAAK,OAAO,sBAEzCwJ,EAAS,GAAD,OAAIuD,EAAJ,qBAAmCwC,IAiCfC,CAAgBjO,EAAOD,EAAKtB,EAAKuP,wBAO7D/C,GAAU,SAAC,GAAiC,IAA/BxG,EAA8B,EAA9BA,QAAS7E,EAAqB,EAArBA,SAAUyL,EAAW,EAAXA,KAEhC3M,EAAK,KAAKiO,eAFiC,EAGrBlF,oBAAS,GAHY,oBAGxCyE,EAHwC,KAGhCC,EAHgC,OAIvB1E,mBAAS,CAC/B,MAAO/I,EACP,QAAS,SACT0O,YAAa,GACbc,mBAAoB,GACpBC,iBAAkB,GAClBH,kBAAmB,GACnBI,iBAAkB,KAX2B,oBAIxC3P,EAJwC,KAIlCkI,EAJkC,KAkC/C,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAzBP,SAAClI,EAAKC,GAAN,OAAgB2G,EAAQN,KAAI,gBAAK5H,GAAQsB,EAAKC,KAyBnBzB,MAAO8M,IAE7C,kBAAC,GAAD,CAAeU,SA1BJ,WACf,IAAIrN,EAAK,KAAKiO,eACY,KAArBlO,EAAK2O,aAAsB3O,EAAKuP,oBAAsB,KAE3DlO,GAAQF,EAAU,iCAAkCnB,GAEpDkI,EAAQ,CACN,MAAOjI,EACP,QAAS,SACT0O,YAAa,GACbc,mBAAoB,GACpBC,iBAAkB,GAClBH,kBAAmB,GACnBI,iBAAkB,KAGpBjC,GAAQ,UAgBNW,GAAmB,SAACtB,EAAY/M,EAAMwJ,GAExC,OACA,kBAAC,GAAD,CACEoD,KAAK,UACLF,QAAU1M,GAAQA,EAAK0P,kBAAqBzL,KAAKyD,MAAM1H,EAAK0P,kBAAoBzL,KAAKyD,MAAM,IAAIzD,MAAS,EACxGuF,SAAU,SAAAgB,GACR,IAAIkF,EAAmB,aACpBlF,IAAGkF,EAAmB,cACzBlG,EAAS,GAAD,OAAIuD,EAAJ,oBAAkC2C,OAM5CnB,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,yCAAqCjN,EAArC,MAEhB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOxN,EAAKuP,kBAAkBxJ,QAAQgI,gBAAmB/N,EAAK2O,YAAYiB,UAAU,EAAG,IAAI,MAAQlC,QAASA,EAASD,OAAQA,IAE1I,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,EAAYjN,MAAOA,IAErE,kBAAC,GAAD,CACEqB,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,iCACL6L,aAAcoB,QAOTwB,GA3NM,SAAC,GAAwB,IAAtBlK,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAEpBrI,EADQsN,qBAAWnG,IACnBnH,SAER,OACE,oCACE,yBAAKmI,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CAAUoD,QAAS/G,EAAK1L,UAAUwL,OAAQ+D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,wBAAyBgB,OAE7F,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAOoE,EAAK1L,UAAU+L,QACtBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,yBAA0BgB,QAKxD,wBAAIlB,UAAU,SAEb3D,EAAKC,OAAO,UAAU,GAAGkK,OAASnK,EAAKC,OAAO,UAAU,GAAGkK,MAAM/F,KAAI,SAAC/B,EAAGlI,GAAJ,OACpE,kBAAC,GAAD,CACEqB,SAAUA,EACV2L,MAAiB,IAAVhN,EACPA,MAAOA,EACPE,KAAMgI,EACN1G,IAAK0G,EAAE,OACPgF,KAAMlN,IAAU6F,EAAKC,OAAO,UAAU,GAAGkK,MAAM3P,OAAS,EACxDqJ,SAAUA,OAId,kBAAC,GAAD,CAASxD,QAASL,EAAK1L,UAAU+L,QAAS7E,SAAUA,EAAUyL,KAAM3B,QAAOtF,EAAKC,OAAO,UAAU,GAAGkK,WCKpGnC,GAAO,SAAC,GAAyC,IAAvC3N,EAAsC,EAAtCA,KAAMwJ,EAAgC,EAAhCA,SAAgC,IAAtBuD,kBAAsB,MAAT,GAAS,EAC5C9H,EAAMiH,aAAe,CAAC,cAAe,QAArCjH,EACFkH,EAAW,SAACC,EAAMC,EAAO7B,GAA2B,IAAxBpJ,EAAuB,uDAAlB,KAAMnB,EAAY,uDAAT,KAC1C8P,EAAW3D,EACZC,IACD0D,EAAWA,EAAS,IAAI1D,GAE1B,IAAIC,EAAMrB,OAAMjL,EAAM+P,EAAU,MACrB,OAARzD,IACgB,kBAAP9B,GAAiC,kBAAPA,EAClCS,OAAMjL,EAAM+P,EAAU,IACD,kBAAPvF,IACTO,MAAMC,QAAQR,GACfS,OAAMjL,EAAM+P,EAAU,IAEtB9E,OAAMjL,EAAM+P,EAAU,MAK9BvG,EAASuD,EAAWgD,EAAUvF,GAC3BvK,GACDuJ,EAAS,UAAGuD,GAAaX,EAAK,UAAWnM,GAExCmB,GACDoI,EAAS,UAAGuD,GAAaX,EAAK,YAAahL,IAI/C,OACE,6BACE,kBAAC,GAAD,CACEkI,UAAU,OACVC,MAAOtE,EAAE,mBACTsF,YAAY,SACZhJ,MAAO0J,OAAMjL,EAAM,2BAA4B,IAC/CwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,4BAA0CvC,MAGnE,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,mBACTsF,YAAY,2BACZhJ,MAAO0J,OAAMjL,EAAM,WAAY,IAC/BwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,YAA0BvC,MAGnD,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,4BACTsF,YAAY,aACZhJ,MAAO0J,OAAMjL,EAAM,YAAa,IAChCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,aAA2BvC,MAGpD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,0BACTsF,YAAY,aACZhJ,MAAO0J,OAAMjL,EAAM,UAAW,IAC9BwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,WAAyBvC,OAIpD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,+BACTsF,YAAY,0BACZhJ,MAAO0J,OAAMjL,EAAK,iCAAkC,IACpDwJ,SAAU,SAAAgB,GAAC,OAAI2B,EAAS,gBAAiB,mBAAoB3B,IAC7DgC,QAAS,aACTpL,KAAK,cAGP,kBAAC,GAAD,CACEkI,UAAU,OACVC,MAAOtE,EAAE,qBACTsF,YAAY,qBACZhJ,MAAO0J,OAAMjL,EAAK,uBAAwB,IAC1CwJ,SAAU,SAAAgB,GAAC,OAAI2B,EAAS,gBAAiB,SAAU3B,IACnDgC,QAAS,aACTpL,KAAK,cAGP,kBAAC,GAAD,CACEiO,KAAK,IACL/F,UAAU,OACVC,MAAOtE,EAAE,8BACT1D,MAAO0J,OAAMjL,EAAM,cAAe,IAClCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,eAA6BvC,QAMtDwF,GAAY,WAChB,IAAI/P,EAAKiO,eACT,MAAQ,CACN,QAAS,eACT,MAAO,KAAKjO,EAAG,UACfgQ,cAAe,CACb,MAAO,KAAKhQ,EAAG,iBACf,QAAS,aACT7C,KAAM,GACN0J,OAAQ,GACRoJ,iBAAkB,IAEpBC,UAAW,CACT,QAAS,gBACTlQ,GAAI,KAAKA,EAAG,aACZmQ,UAAW,CACT,QAAS,eACTnQ,GAAI,KAAKA,EAAG,uBACZ7C,KAAM,KAGViT,SAAU,GACVnL,UAAW,GACXC,QAAS,GACTwJ,YAAa,KAGXnC,GAAU,SAAC,GAA2B,IAAzBxG,EAAwB,EAAxBA,QAAS7E,EAAe,EAAfA,SAAe,EACf6H,oBAAS,GADM,oBAClCyE,EADkC,KAC1BC,EAD0B,OAEjB1E,mBAASgH,MAFQ,oBAElChQ,EAFkC,KAE5BkI,EAF4B,KAezC,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAhBP,SAAClI,EAAKC,GAAN,OAAgB2G,EAAQN,KAAI,gBAAK5H,GAAQsB,EAAKC,OAkBzD,kBAAC,GAAD,CAAe+L,SAhBJ,WACO,KAAlBtN,EAAKqQ,WACThP,GAAQF,EAAU,yCAA0CnB,GAE5DkI,EAAQ8H,MAERtC,GAAQ,UAeNW,GAAmB,SAACtB,EAAY/M,EAAMwJ,GAE1C,OACE,sCAIE+E,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,iDAA6CjN,EAA7C,MAEhB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOxN,EAAKqQ,SAAS,IAAIrQ,EAAKmQ,UAAUC,UAAUhT,KAAMsQ,QAASA,EAASD,OAAQA,IAE/F,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,IAElD,kBAAC,GAAD,CACE5L,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,yCACL6L,aAAcoB,QAOTiC,GA7NC,SAAC,GAAwB,IAAtB3K,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAEfrI,EADQsN,qBAAWnG,IACnBnH,SAER,OACE,oCACE,yBAAKmI,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CAAUoD,QAAS/G,EAAKzL,KAAKuL,OAAQ+D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,mBAAoBgB,OAEnF,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAOoE,EAAKzL,KAAK8L,QACjBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,oBAAqBgB,QAKnD,wBAAIlB,UAAU,SAEb2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,IAAImE,KAAI,SAAC/B,EAAGlI,GAAJ,OACxD,kBAAC,GAAD,CACEqB,SAAUA,EACV2L,MAAiB,IAAVhN,EACPA,MAAOA,EACPE,KAAMgI,EACN1G,IAAK2J,OAAMjD,EAAG,MAAO,QACrBgF,KAAMlN,IAAUmL,QAAOtF,EAAKC,OAAO,UAAU,GAAGqK,eAAiB,EACjEzG,SAAUA,OAId,kBAAC,GAAD,CAASxD,QAASL,EAAKzL,KAAK8L,QAAS7E,SAAUA,MCS/CwM,GAAO,SAAC,GAAyC,IAAvC3N,EAAsC,EAAtCA,KAAMwJ,EAAgC,EAAhCA,SAAgC,IAAtBuD,kBAAsB,MAAT,GAAS,EAC5C9H,EAAMiH,aAAe,CAAC,cAAe,QAArCjH,EAoCR,OACE,6BACE,kBAAC,GAAD,CACEqE,UAAU,OACVC,MAAOtE,EAAE,wBACTsF,YAAY,qBACZhJ,MAAO0J,OAAMjL,EAAM,sBAAuB,IAC1CwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,uBAAqCvC,MAE9D,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,wBACTsF,YAAY,mBACZhJ,MAAO0J,OAAMjL,EAAM,qBAAsB,IACzCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,sBAAoCvC,IAC3Df,QAAW,CAAC,SAAU,cAAe,SACrCC,WApDoB,SAAC1B,EAAGlI,GAC9B,OACE,4BAAQwB,IAAK0G,EAAGzG,MAAOyG,GACpBA,MAoDD,kBAAC,GAAD,CACEsB,UAAU,OACVC,MAAOtE,EAAE,0BACTsF,YAAY,qBACZhJ,MAAO0J,OAAMjL,EAAM,mBAAoB,IACvCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,oBAAkCvC,OAI7D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,yBACTsF,YAAY,mBACZhJ,MAAO0J,OAAMjL,EAAM,qCAAsC,IACzDwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,sCAAoDvC,MAG7E,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,yBACTsF,YAAY,MACZhJ,MAAO0J,OAAMjL,EAAM,8BAA+B,IAClDwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,+BAA6CvC,MAGtE,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,4BACTsF,YAAY,IACZhJ,MAAO0J,OAAMjL,EAAM,6BAA8B,IACjDwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,8BAA4CvC,OAIvE,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,4BACTsF,YAAY,aACZhJ,MAAO0J,OAAMjL,EAAM,kBAAmB,IACtCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,mBAAiCvC,MAG1D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,0BACTsF,YAAY,aACZhJ,MAAO0J,OAAMjL,EAAM,gBAAiB,IACpCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,iBAA+BvC,OAI1D,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,qBACTsF,YAAY,qBACZhJ,MAAO0J,OAAMjL,EAAK,UAAW,IAC7BwJ,SAAU,SAAAgB,GAAC,OAzGA,SAAC4B,EAAMC,EAAO7B,GAA2B,IAAxBpJ,EAAuB,uDAAlB,KAAMnB,EAAY,uDAAT,KAC1C8P,EAAW3D,EACZC,IACD0D,EAAWA,EAAS,IAAI1D,GAE1B,IAAIC,EAAMrB,OAAMjL,EAAM+P,EAAU,MACrB,OAARzD,IACgB,kBAAP9B,GAAiC,kBAAPA,EAClCS,OAAMjL,EAAM+P,EAAU,IACD,kBAAPvF,IACTO,MAAMC,QAAQR,GACfS,OAAMjL,EAAM+P,EAAU,IAEtB9E,OAAMjL,EAAM+P,EAAU,MAK9BvG,EAASuD,EAAWgD,EAAUvF,GAC3BvK,GACDuJ,EAAS,UAAGuD,GAAaX,EAAK,UAAWnM,GAExCmB,GACDoI,EAAS,UAAGuD,GAAaX,EAAK,YAAahL,GAkF1B+K,CAAS,UAAW,GAAI3B,IACvCgC,QAAS,aACTpL,KAAK,cAGP,kBAAC,GAAD,CACEiO,KAAK,IACL/F,UAAU,OACVC,MAAOtE,EAAE,8BACT1D,MAAO0J,OAAMjL,EAAM,WAAY,IAC/BwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,YAA0BvC,QAMnDwF,GAAY,WAChB,IAAI/P,EAAKiO,eACT,MAAQ,CACN,QAAS,oCACT,MAAO,KAAKjO,EAAG,UACf,gBAAmB,CACjB,MAAO,KAAKA,EAAG,mBACf,QAAS,kBACT,WAAc,GACd,YAAe,GACf,KAAQ,MACR,aAAgB,CACd,MAAO,KAAKA,EAAG,YAGnB,mBAAsB,SACtB,iBAAoB,GACvB,SAAY,GACT,QAAW,GACX,MAAS,CACP,MAAO,KAAKA,EAAG,SACf,QAAS,iCACT,6BAAgC,GAChC,UAAa,GACb,QAAW,GACX,SAAY,CAChB,MAAO,KAAKA,EAAG,kBACf,QAAS,sBACT,KAAQ,OAMJuM,GAAU,SAAC,GAA2B,IAAzBxG,EAAwB,EAAxBA,QAAS7E,EAAe,EAAfA,SAAe,EACf6H,oBAAS,GADM,oBAClCyE,EADkC,KAC1BC,EAD0B,OAEjB1E,mBAASgH,MAFQ,oBAElChQ,EAFkC,KAE5BkI,EAF4B,KAgBzC,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAjBP,SAAClI,EAAKC,GAAN,OAAgB2G,EAAQN,KAAI,gBAAK5H,GAAQsB,EAAKC,OAkBzD,kBAAC,GAAD,CAAe+L,SAhBJ,WACe,KAA1BtN,EAAKuQ,mBAETlP,GAAQF,EAAU,yCAA0CnB,GAE5DkI,EAAQ8H,MAERtC,GAAQ,UAeNW,GAAmB,SAACtB,EAAY/M,EAAMwJ,GAE1C,OACE,sCAIE+E,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,iDAA6CjN,EAA7C,MAEhB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOvC,OAAMjL,EAAM,mBAAoB,IAAK0N,QAASA,EAASD,OAAQA,IAEnF,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,IAElD,kBAAC,GAAD,CACE5L,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,yCACL6L,aAAcoB,QAOTmC,GA3QM,SAAC,GAAwB,IAAtB7K,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAEpBrI,EADQsN,qBAAWnG,IACnBnH,SAER,OACE,oCACE,yBAAKmI,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEoD,QAAS/G,EAAKxL,UAAUsL,OACxB+D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,wBAAyBgB,OAGrD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAOoE,EAAKxL,UAAU6L,QACtBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,yBAA0BgB,QAKxD,wBAAIlB,UAAU,SAEb2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,IAAImE,KAAI,SAAC/B,EAAGlI,GAAJ,OACxD,kBAAC,GAAD,CACEE,KAAMgI,EACN1G,IAAK2J,OAAMjD,EAAG,MAAO,QACrBlI,MAAOA,EACP0J,SAAUA,EACVrI,SAAUA,EACV2L,MAAiB,IAAVhN,EACPkN,KAAMlN,IAAUmL,QAAOA,OAAMtF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,KAAO,OAInF,kBAAC,GAAD,CAASI,QAASL,EAAKxL,UAAU6L,QAAS7E,SAAUA,MCIpDwM,GAAO,SAAC,GAA2D,IAAzD3N,EAAwD,EAAxDA,KAAMwJ,EAAkD,EAAlDA,SAAkD,IAAxCuD,kBAAwC,MAA3B,GAA2B,MAAvB0D,qBAAuB,MAAT,GAAS,EAC9DxL,EAAMiH,aAAe,CAAC,cAAe,QAArCjH,EAER,OACE,6BACE,kBAAC,GAAD,CACEqE,UAAU,OACVC,MAAOtE,EAAE,sBACTsF,YAAY,0BACZhJ,MAAO0J,OAAMjL,EAAK,cAAe,IACjCwJ,SAAU,SAAAgB,GAAMhB,EAAS,GAAD,OAAIuD,EAAJ,mBAAiCvC,GAAsB,KAAhBiG,GAAoBjH,EAAS,GAAD,OAAIiH,GAAiBjG,MAGlH,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,yBACTsF,YAAY,SACZhJ,MAAO0J,OAAMjL,EAAM,oBAAqB,IACxCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,yBAAuCvC,MAGhE,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,8BACT1D,MAAOvB,EAAK2O,YACZnF,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,eAA6BvC,QAMtDwF,GAAY,WAEhB,MAAQ,CACN,QAAS,cACT,MAAO,KAHA9B,eAGQ,UACf,cAAe,GAClB,oBAAqB,GAClBS,YAAa,KAIXnC,GAAU,SAAC,GAA2B,IAAzBxG,EAAwB,EAAxBA,QAAS7E,EAAe,EAAfA,SAAe,EACf6H,oBAAS,GADM,oBAClCyE,EADkC,KAC1BC,EAD0B,OAEjB1E,mBAASgH,MAFQ,oBAElChQ,EAFkC,KAE5BkI,EAF4B,KAiBzC,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAlBP,SAAClI,EAAKC,GAAN,OAAgB2G,EAAQN,KAAI,gBAAK5H,GAAQsB,EAAKC,OAoBzD,kBAAC,GAAD,CAAe+L,SAlBJ,WACwB,KAAnCrC,OAAMjL,EAAM,cAAe,MAE/BqB,GAAQF,EAAU,oCAAqCnB,GACvDqB,GAAQF,EAAU,oCAAqC8J,OAAMjL,EAAM,cAAe,KAElFkI,EAAQ8H,MAERtC,GAAQ,UAgBNW,GAAmB,SAACtB,EAAY/M,EAAMwJ,GAE1C,OACE,sCAIE+E,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,yCAAqCjN,EAArC,MACV2Q,EAAa,yCAAqC3Q,EAArC,KAEnB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOxN,EAAK,eAAgB0N,QAASA,EAASD,OAAQA,IAEnE,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,EAAY0D,cAAeA,IAE7E,kBAAC,GAAD,CACEtP,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,iCACL6L,aAAcoB,QAOTqC,GAnJG,SAAC,GAAwB,IAAtB/K,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAEjBrI,EADQsN,qBAAWnG,IACnBnH,SAER,OACE,oCACE,yBAAKmI,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEoD,QAAS/G,EAAKvL,OAAOqL,OACrB+D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,qBAAsBgB,OAGlD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAOoE,EAAKvL,OAAO4L,QACnBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,sBAAuBgB,QAKrD,wBAAIlB,UAAU,SAEb2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,QAAS,IAAImE,KAAI,SAAC/B,EAAGlI,GAAJ,OAChD,kBAAC,GAAD,CACEE,KAAMgI,EACN1G,IAAK2J,OAAMjD,EAAG,MAAO,QACrBlI,MAAOA,EACP0J,SAAUA,EACVrI,SAAUA,EACV2L,MAAiB,IAAVhN,EACPkN,KAAMlN,IAAUmL,QAAOA,OAAMtF,EAAKC,OAAO,UAAU,GAAI,QAAS,KAAO,OAI3E,kBAAC,GAAD,CAASI,QAASL,EAAKvL,OAAO4L,QAAS7E,SAAUA,MCIjDwM,GAAO,SAAC,GAAyC,IAAvC3N,EAAsC,EAAtCA,KAAMwJ,EAAgC,EAAhCA,SAAgC,IAAtBuD,kBAAsB,MAAT,GAAS,EAC5C9H,EAAMiH,aAAe,eAArBjH,EAER,OACE,6BACE,kBAAC,GAAD,CACEqE,UAAU,OACVC,MAAOtE,EAAE,oBACTsF,YAAY,gBACZhJ,MAAO0J,OAAMjL,EAAM,aAAc,IACjCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,cAA4BvC,MAGrD,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,sBACTsF,YAAY,kBACZhJ,MAAO0J,OAAMjL,EAAM,QAAS,IAC5BwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,SAAuBvC,QAMhDwF,GAAY,WAEhB,MAAQ,CACN,QAAS,gBACT,MAAO,YAHA9B,eAIP,WAAc,GACd,MAAS,KAIP1B,GAAU,SAAC,GAA2B,IAAzBxG,EAAwB,EAAxBA,QAAS7E,EAAe,EAAfA,SAAe,EACf6H,oBAAS,GADM,oBAClCyE,EADkC,KAC1BC,EAD0B,OAEjB1E,mBAASgH,MAFQ,oBAElChQ,EAFkC,KAE5BkI,EAF4B,KAgBzC,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAjBP,SAAClI,EAAKC,GAAN,OAAgB2G,GAAQ,SAAArB,GAAK,OAAIe,KAAI,gBAAKf,GAASvF,EAAKC,SAmBnE,kBAAC,GAAD,CAAe+L,SAjBJ,WACuB,KAAlCrC,OAAMjL,EAAM,aAAc,KAA2C,KAA7BiL,OAAMjL,EAAM,QAAS,MAEjEqB,GAAQF,EAAU,sCAAuCnB,GAEzDkI,EAAQ8H,MAERtC,GAAQ,UAgBNa,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,8CAA0CjN,EAA1C,MAEhB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOvC,OAAMjL,EAAM,aAAc,IAAK0N,QAASA,EAASD,OAAQA,IAE7E,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,IAElD,kBAAC,GAAD,CACE5L,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,2CAOAuP,GAjIG,SAAC,GAAwB,IAAtBhL,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAEjBrI,EADQsN,qBAAWnG,IACnBnH,SAER,OACE,oCACE,yBAAKmI,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEoD,QAAS/G,EAAKnL,OAAOiL,OACrB+D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,qBAAsBgB,OAGlD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAOoE,EAAKnL,OAAOwL,QACnBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,sBAAuBgB,QAKrD,wBAAIlB,UAAU,SAEb2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,aAAc,IAAImE,KAAI,SAAC/B,EAAGlI,GAAJ,OACrD,kBAAC,GAAD,CACEE,KAAMgI,EACN1G,IAAK2J,OAAMjD,EAAG,MAAO,QACrBlI,MAAOA,EACP0J,SAAUA,EACVrI,SAAUA,EACV2L,MAAiB,IAAVhN,EACPkN,KAAMlN,IAAUmL,OAAMtF,EAAKC,OAAO,UAAU,GAAI,aAAc,IAAIzF,OAAS,OAI/E,kBAAC,GAAD,CAAS6F,QAASL,EAAKnL,OAAOwL,QAAS7E,SAAUA,MCwBjDwM,GAAO,SAAC,GAAyC,IAAvC3N,EAAsC,EAAtCA,KAAMwJ,EAAgC,EAAhCA,SAAgC,IAAtBuD,kBAAsB,MAAT,GAAS,EAC5C9H,EAAMiH,aAAe,eAArBjH,EAER,OACE,6BACE,kBAAC,GAAD,CACEqE,UAAU,OACVC,MAAOtE,EAAE,uBACTsF,YAAY,UACZhJ,MAAO0J,OAAMjL,EAAK,OAAQ,IAC1BwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,QAAsBvC,QAM/CwF,GAAY,WAEhB,MAAQ,CACN,QAAS,WACT,MAAO,KAHA9B,eAIP,KAAQ,KAGN1B,GAAU,SAAC,GAA2B,IAAzBxG,EAAwB,EAAxBA,QAAS7E,EAAe,EAAfA,SAAe,EACf6H,oBAAS,GADM,oBAClCyE,EADkC,KAC1BC,EAD0B,OAGjB1E,mBAASgH,MAHQ,oBAGlChQ,EAHkC,KAG5BkI,EAH4B,KAiBzC,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAjBP,SAAClI,EAAKC,GAAN,OAAgB2G,GAAQ,SAAArB,GAAK,OAAIe,KAAI,gBAAKf,GAASvF,EAAKC,SAmBnE,kBAAC,GAAD,CAAe+L,SAjBJ,WACiB,KAA5BrC,OAAMjL,EAAM,OAAQ,MAExBqB,GAAQF,EAAU,yCAA0CnB,GAE5DkI,EAAQ8H,MAERtC,GAAQ,UAgBNa,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,iDAA6CjN,EAA7C,MAEhB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOvC,OAAMjL,EAAM,OAAQ,IAAK0N,QAASA,EAASD,OAAQA,IAEvE,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,IAElD,kBAAC,GAAD,CACE5L,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,8CAOAwP,GA3IM,SAAC,GAAwB,IAAtBjL,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAEpBrI,EADQsN,qBAAWnG,IACnBnH,SAmBR,OAjBA0P,qBAAU,WACF,cAAelL,IACnBxE,EAAS,CACPC,KAAM,kBACNI,QAAS,CACPF,IAAK,YACLC,MAAO,CACLkE,QAAQ,EACRO,QAAS,gBAKf7E,EAAS,CAAEC,KAAM,iBAElB,CAACuE,EAAMxE,IAGR,cAAewE,GACb,oCACE,yBAAK2D,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEoD,QAAS/G,EAAKrL,UAAUmL,OACxB+D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,wBAAyBgB,OAGrD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAOoE,EAAKrL,UAAU0L,QACtBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,yBAA0BgB,QAKxD,wBAAIlB,UAAU,SAEb2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,IAAImE,KAAI,SAAC/B,EAAGlI,GAAJ,OACxD,kBAAC,GAAD,CACEE,KAAMgI,EACN1G,IAAK2J,OAAMjD,EAAE,MAAO,QACpBlI,MAAOA,EACP0J,SAAUA,EACVrI,SAAUA,EACV2L,MAAiB,IAAVhN,EACPkN,KAAMlN,IAAUmL,QAAOA,OAAMtF,EAAKC,OAAO,UAAU,GAAI,gBAAiB,KAAO,OAInF,kBAAC,GAAD,CAASI,QAASL,EAAKrL,UAAU0L,QAAS7E,SAAUA,MCOtDwM,GAAO,SAAC,GAAyC,IAAvC3N,EAAsC,EAAtCA,KAAMwJ,EAAgC,EAAhCA,SAAgC,IAAtBuD,kBAAsB,MAAT,GAAS,EAC5C9H,EAAMiH,aAAe,CAAC,cAAe,QAArCjH,EAER,OACE,6BACE,yBAAKqE,UAAU,8BACb,kBAAC,GAAD,CACEA,UAAU,OACVC,MAAOtE,EAAE,yBACTsF,YAAY,oBACZhJ,MAAO0J,OAAMjL,EAAM,wCAAyC,IAC5DwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,yCAAuDvC,MAGhF,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,+BACTsF,YAAY,oBACZhJ,MAAO0J,OAAMjL,EAAM,yCAA0C,IAC7DwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,0CAAwDvC,OAInF,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,6BACTsF,YAAY,kBACZhJ,MAAO0J,OAAMjL,EAAM,uCAAwC,IAC3DwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,wCAAsDvC,MAG/E,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,0BACTsF,YAAY,kBACZhJ,MAAO0J,OAAMjL,EAAM,wCAAyC,IAC5DwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,yCAAuDvC,MAGhF,kBAAC,GAAD,CACElB,UAAU,OACVC,MAAOtE,EAAE,0BACTsF,YAAY,wBACZhJ,MAAO0J,OAAMjL,EAAM,oCAAqC,IACxDwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,qCAAmDvC,MAG5E,kBAAC,GAAD,CACE6E,KAAK,IACL/F,UAAU,OACVC,MAAOtE,EAAE,8BACT1D,MAAO0J,OAAMjL,EAAM,2CAA4C,IAC/DwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,4CAA0DvC,QAKnFwF,GAAY,WAChB,IAAI/P,EAAKiO,eACT,MACE,CACE,MAAO,eAAejO,EACtB,QAAS,qBACT,0BAA6B,YAC7B,gBAAmB,CACjB,MAAO,eAAeA,EAAG,mBACzB,QAAS,eACT,YAAe,CACb,MAAO,eAAeA,EAAG,+BACzB,QAAS,UAEX,OAAU,CACR,CACE,MAAO,eAAeA,EAAG,UACzB,QAAS,SACT,aAAgB,GAGhB,aAAgB,GAGhB,aAAgB,CACd,MAAO,eAAeA,EAAG,uBACzB,QAAS,SACT,YAAe,IACf,WAAc,IACd,kBAAqB,SAQ7BuM,GAAU,SAAC,GAA2B,IAAzBxG,EAAwB,EAAxBA,QAAS7E,EAAe,EAAfA,SAAe,EACf6H,oBAAS,GADM,oBAClCyE,EADkC,KAC1BC,EAD0B,OAEjB1E,mBAASgH,MAFQ,oBAElChQ,EAFkC,KAE5BkI,EAF4B,KAgBzC,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAjBP,SAAClI,EAAKC,GAAN,OAAgB2G,EAAQN,KAAI,gBAAK5H,GAAQsB,EAAKC,OAmBzD,kBAAC,GAAD,CAAe+L,SAjBJ,WACkD,KAA7DrC,OAAMjL,EAAM,wCAAyC,MAEzDqB,GAAQF,EAAU,gDAAiDnB,GAEnEkI,EAAQ8H,MAERtC,GAAQ,UAgBNa,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,wDAAoDjN,EAApD,MAEhB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOvC,OAAMjL,EAAM,wCAAyC,IAAI,IAAIiL,OAAMjL,EAAM,yCAA0C,IAAK0N,QAASA,EAASD,OAAQA,IAEtK,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,IAElD,kBAAC,GAAD,CACE5L,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,qDAOA0P,GAhNO,SAAC,GAAwB,IAAtBnL,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAErBrI,EADQsN,qBAAWnG,IACnBnH,SAmBR,OAjBA0P,qBAAU,WACF,eAAgBlL,IACpBxE,EAAS,CACPC,KAAM,kBACNI,QAAS,CACPF,IAAK,aACLC,MAAO,CACLkE,QAAQ,EACRO,QAAS,iBAKf7E,EAAS,CAAEC,KAAM,iBAElB,CAACuE,EAAMxE,IAGR,eAAgBwE,GACd,oCACE,yBAAK2D,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEoD,QAAS/G,EAAKpL,WAAWkL,OACzB+D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,yBAA0BgB,OAGtD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAOoE,EAAKpL,WAAWyL,QACvBwD,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,0BAA2BgB,QAKzD,wBAAIlB,UAAU,SAEb2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,uBAAwB,IAAIkJ,QAAO,SAAA9G,GAAC,MAAiD,cAA7CiD,OAAMjD,EAAG,4BAA6B,OAAoB+B,KAAI,SAAC/B,EAAGlI,GAAJ,OACrI,kBAAC,GAAD,CACEE,KAAMgI,EACN1G,IAAK2J,OAAMjD,EAAE,MAAO,QACpBlI,MAAOA,EACP0J,SAAUA,EACVrI,SAAUA,EACV2L,MAAiB,IAAVhN,EACPkN,KAAMlN,IAAUmL,OAAMtF,EAAKC,OAAO,UAAU,GAAI,uBAAwB,IAAIkJ,QAAO,SAAA9G,GAAC,MAAiD,cAA7CiD,OAAMjD,EAAG,4BAA6B,OAAoB7H,OAAS,OAI/J,kBAAC,GAAD,CAAS6F,QAASL,EAAKpL,WAAWyL,QAAS7E,SAAUA,MCdvDwM,GAAO,SAAC,GAAyC,IAAvC3N,EAAsC,EAAtCA,KAAMwJ,EAAgC,EAAhCA,SAAgC,IAAtBuD,kBAAsB,MAAT,GAAS,EAC5C9H,EAAMiH,aAAe,CAAC,cAAe,QAArCjH,EAER,OACE,6BACE,kBAAC,GAAD,CACIqE,UAAU,OACVC,MAAOtE,EAAE,gCACTsF,YAAY,oBACZhJ,MAAO0J,OAAMjL,EAAM,uBAAwB,IAC3CwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,wBAAsCvC,MAGjE,yBAAKlB,UAAU,8BACb,kBAAC,GAAD,CACIA,UAAU,OACVC,MAAOtE,EAAE,8BACTsF,YAAY,aACZhJ,MAAO0J,OAAMjL,EAAM,YAAa,IAChCwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,aAA2BvC,MAGtD,kBAAC,GAAD,CACIlB,UAAU,OACVC,MAAOtE,EAAE,4BACTsF,YAAY,aACZhJ,MAAO0J,OAAMjL,EAAM,UAAW,IAC9BwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,WAAyBvC,OAGpD,kBAAC,GAAD,CACIlB,UAAU,OACVC,MAAOtE,EAAE,6BACTsF,YAAc,aACdhJ,MAAO0J,OAAMjL,EAAM,WAAY,IAC/BwJ,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,GAAD,OAAIuD,EAAJ,YAA0BvC,QAMvDwF,GAAY,WAChB,IAAI/P,EAAKiO,eACT,MACE,CACE,MAAO,KAAKjO,EAAG,UACf,QAAS,OACT,UAAa,GACb,QAAW,GACX,SAAY,SACZ,SAAY,CACV,MAAO,KAAKA,EAAG,YACf,QAAS,oBACT,IAAO,GACP,YAAe,GACf,YAAe,MAMjBuM,GAAU,SAAC,GAA2B,IAAzBxG,EAAwB,EAAxBA,QAAS7E,EAAe,EAAfA,SAAe,EACf6H,oBAAS,GADM,oBAClCyE,EADkC,KAC1BC,EAD0B,OAEjB1E,mBAASgH,MAFQ,oBAElChQ,EAFkC,KAE5BkI,EAF4B,KAezC,OACE,yBAAKoB,UAAU,2CACb,kBAAC,GAAD,CAAatD,QAASA,EAAS0H,QAASA,EAASD,OAAQA,IAEzD,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAhBP,SAAClI,EAAKC,GAAN,OAAgB2G,EAAQN,KAAI,gBAAK5H,GAAQsB,EAAKC,OAiBzD,kBAAC,GAAD,CAAe+L,SAhBJ,WACqB,KAAhCrC,OAAMjL,EAAM,WAAY,MAE5BqB,GAAQF,EAAU,oCAAqCnB,GAEvDkI,EAAQ8H,MAERtC,GAAQ,UAeNW,GAAmB,SAACtB,EAAY/M,EAAMwJ,GAE1C,OACE,sCAIE+E,GAAO,SAAC,GAAsD,IAApDvO,EAAmD,EAAnDA,KAAMF,EAA6C,EAA7CA,MAAO0J,EAAsC,EAAtCA,SAAUrI,EAA4B,EAA5BA,SAAU2L,EAAkB,EAAlBA,MAAOE,EAAW,EAAXA,KAAW,EACvChE,oBAAS,GAD8B,oBAC1DyE,EAD0D,KAClDC,EADkD,KAE3DX,EAAU,4CAAwCjN,EAAxC,MAEhB,OACE,yBAAKwJ,UAAU,2CACb,kBAAC,GAAD,CAAakE,MAAOvC,OAAMjL,EAAM,uBAAwB,IAAK0N,QAASA,EAASD,OAAQA,IAEvF,yBAAKnE,UAAS,eAAUmE,EAAS,QAAU,WACzC,kBAAC,GAAD,CAAMzN,KAAMA,EAAMwJ,SAAUA,EAAUuD,WAAYA,IAElD,kBAAC,GAAD,CACE5L,SAAUA,EACV2L,MAAOA,EACPC,WAAYA,EACZ/M,KAAMA,EACNgN,KAAMA,EACNxD,SAAUA,EACVpI,KAAK,oCACL6L,aAAcoB,QAOT0C,GAlKQ,SAAC,GAAwB,IAAtBpL,EAAqB,EAArBA,KAAM6D,EAAe,EAAfA,SAEtBrI,EADQsN,qBAAWnG,IACnBnH,SAER,OACE,oCACE,yBAAKmI,UAAU,sCACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,CACEoD,QAASzB,OAAMtF,EAAK,sBAAsB,GAC1C6D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,0BAA2BgB,OAGvD,yBAAKlB,UAAU,cACb,kBAAC,GAAD,CACEiB,YAAY,UACZhJ,MAAO0J,OAAMtF,EAAM,sBAAuB,IAC1C6D,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,2BAA4BgB,QAK1D,wBAAIlB,UAAU,SAEb2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,WAAY,IAAImE,KAAI,SAAC/B,EAAGlI,GAAJ,OACnD,kBAAC,GAAD,CACEE,KAAMgI,EACN1G,IAAK2J,OAAMjD,EAAG,MAAO,QACrBlI,MAAOA,EACP0J,SAAUA,EACVrI,SAAUA,EACV2L,MAAiB,IAAVhN,EACPkN,KAAMlN,IAAUmL,QAAOA,OAAMtF,EAAKC,OAAO,UAAU,GAAI,WAAY,KAAO,OAI9E,kBAAC,GAAD,CAASI,QAASiF,OAAMtF,EAAM,sBAAuB,IAAKxE,SAAUA,MCqC3D6P,GAvEK,WAClB,IAAMC,EAAUxC,qBAAWnG,IACnBpJ,EAAoB+R,EAApB/R,MAAOiC,EAAa8P,EAAb9P,SACPwE,EAASzG,EAATyG,KAEFsE,EAAO,CACX,CAAE3I,IAAK,UAAWlE,KAAM6N,OAAMtF,EAAM,kBAAmB,YACvD,CAAErE,IAAK,UAAWlE,KAAM6N,OAAMtF,EAAM,iBAAkB,YACtD,CAAErE,IAAK,WAAYlE,KAAM6N,OAAMtF,EAAM,mBAAoB,aACzD,CAAErE,IAAK,YAAalE,KAAM6N,OAAMtF,EAAM,oBAAqB,cAC3D,CAAErE,IAAK,OAAQlE,KAAM6N,OAAMtF,EAAM,eAAgB,SACjD,CAAErE,IAAK,YAAalE,KAAM6N,OAAMtF,EAAM,oBAAqB,cAC3D,CAAErE,IAAK,SAAUlE,KAAM6N,OAAMtF,EAAM,iBAAkB,WACrD,CAAErE,IAAK,cAAelE,KAAM6N,OAAMtF,EAAM,sBAAuB,gBAC/D,CAAErE,IAAK,YAAalE,KAAM6N,OAAMtF,EAAM,oBAAqB,cAC3D,CAAErE,IAAK,aAAclE,KAAM6N,OAAMtF,EAAM,qBAAsB,eAC7D,CAAErE,IAAK,SAAUlE,KAAM6N,OAAMtF,EAAM,iBAAkB,YAhB/B,EAkBYqD,mBAASiB,EAAK,GAAG3I,KAlB7B,oBAkBjB4I,EAlBiB,KAkBLC,EAlBK,KAmBlBX,EAAW,SAAClI,EAAKC,GACrBJ,EAAS,CACPC,KAAM,WACNI,QAAS,CACPF,MACAC,WAIJJ,EAAS,CAAEC,KAAM,eAgCnB,OACE,yBACEnB,GAAG,cACHqJ,UAAU,4FAEV,kBAAC,GAAD,CAAQW,KAAMA,EAAMC,WAAYA,EAAYC,cAAeA,IAC3D,yBAAKb,UAAU,QAnCA,WACjB,OAAQY,GACN,IAAK,UACH,OAAO,kBAAC,GAAD,CAAYvE,KAAMA,EAAM6D,SAAUA,IAC3C,IAAK,UACH,OAAO,kBAAC0H,GAAD,CAAYvL,KAAMA,EAAM6D,SAAUA,IAC3C,IAAK,WACH,OAAO,kBAAC2H,GAAD,CAAaxL,KAAMA,EAAM6D,SAAUA,IAC5C,IAAK,YACH,OAAO,kBAAC4H,GAAD,CAAczL,KAAMA,EAAM6D,SAAUA,IAC7C,IAAK,OACH,OAAO,kBAAC6H,GAAD,CAAS1L,KAAMA,EAAM6D,SAAUA,IACxC,IAAK,YACH,OAAO,kBAAC8H,GAAD,CAAc3L,KAAMA,EAAM6D,SAAUA,IAC7C,IAAK,SACH,OAAO,kBAAC+H,GAAD,CAAW5L,KAAMA,EAAM6D,SAAUA,IAC1C,IAAK,cACH,OAAO,kBAACgI,GAAD,CAAgB7L,KAAMA,EAAM6D,SAAUA,IAC/C,IAAK,YACH,OAAO,kBAACiI,GAAD,CAAc9L,KAAMA,EAAM6D,SAAUA,IAC7C,IAAK,aACH,OAAO,kBAACkI,GAAD,CAAe/L,KAAMA,EAAM6D,SAAUA,IAC9C,IAAK,SACH,OAAO,kBAACmI,GAAD,CAAWhM,KAAMA,EAAM6D,SAAUA,IAC1C,QACE,OAAO,MAUcoI,M,mBC3EvBC,GAAY,SAAC,GAAD,IAAG7R,EAAH,EAAGA,KAAM0E,EAAT,EAASA,SAAT,OAChB,6BACE,yBAAK4E,UAAU,qCACb,yBAAKA,UAAU,gCACb,wBAAIA,UAAU,yBAAyB2B,OAAMjL,EAAM,kBAAmB,KACtE,0BAAMsJ,UAAU,WAAW2B,OAAMjL,EAAM,6BAA8B,MAEzB,KAA7CiL,OAAMjL,EAAM,wBAAyB,KACpC,wBAAIsJ,UAAU,kCACX9E,GAAW,CAAEC,KAAMwG,OAAMjL,EAAM,wBAAyB,IAAK0E,eAIhC,KAAnCuG,OAAMjL,EAAM,cAAe,KAC1B,kBAAC,KAAD,CAAesJ,UAAU,wBAAwBwI,OAAQ7G,OAAMjL,EAAM,cAAe,QAqB3E+R,mBAhBC,WAAO,IAAD,EACetD,qBAAW5F,IAAtClD,EADY,EACZA,KAAeqM,EADH,EACNhM,QAEd,OAAQL,EAAKvL,QACXuL,EAAKvL,OAAOqL,OACZ,6BACE,kBAACuM,EAAD,KAAUrM,EAAKvL,OAAO4L,SACtB,yBAAKsD,UAAU,cACZ2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,QAAS,IAAIkJ,QAAO,SAAA9G,GAAC,MAAuB,KAAnBA,EAAE,kBAAqB+B,KAAI,SAAC/B,GAAD,OACnF,kBAAC,GAAD,CAAW1G,IAAK2J,OAAMjD,EAAE,MAAOkG,gBAAWlO,KAAMgI,EAAGtD,SAAUiB,EAAKjB,UAAY,YAIlF,QChCAuN,GAAoB,SAAC,GAAD,IAAGjS,EAAH,EAAGA,KAAM0E,EAAT,EAASA,SAAT,OACxB,6BACE,yBAAK4E,UAAU,qCACb,yBAAKA,UAAU,gCACb,wBAAIA,UAAU,yBAAyB2B,OAAMjL,EAAM,mBAAoB,IAAvE,IAA6EiL,OAAMjL,EAAM,qCAAsC,KAC/H,0BAAMsJ,UAAU,WAAW2B,OAAMjL,EAAM,sBAAuB,MAEzB,KAArCiL,OAAMjL,EAAM,gBAAiB,KAC7B,wBAAIsJ,UAAU,kCACX9E,GAAW,CAAEC,KAAMwG,OAAMjL,EAAM,gBAAiB,IAAK0E,eAI3B,KAAhCuG,OAAMjL,EAAM,WAAY,KACvB,kBAAC,KAAD,CAAesJ,UAAU,wBAAwBwI,OAAQ7G,OAAMjL,EAAM,WAAY,QAuBxE+R,mBAlBS,WAAO,IAAD,EACOtD,qBAAW5F,IAAtClD,EADoB,EACpBA,KAAeqM,EADK,EACdhM,QAEd,OAAQL,EAAKtL,gBACXsL,EAAKtL,eAAeoL,OACpB,6BACE,kBAACuM,EAAD,KAAUrM,EAAKtL,eAAe2L,SAC9B,yBAAKsD,UAAU,cACZ2B,OAAMtF,EAAM,oCAAqC,IAAImJ,QAAO,SAAA9G,GAAC,OAAMiD,OAAMjD,EAAG,MAAO,IAAIkK,SAAS,YAA8D,WAAhDjH,WAAUA,OAAMjD,EAAG,qBAAsB,QAAkB+B,KAAI,SAAA/B,GAAC,OAAI,kBAAC,GAAD,CAC/K1G,IAAK2J,OAAMjD,EAAE,MAAOkG,gBACpBlO,KAAMgI,EACNtD,SAAUiB,EAAKjB,gBAIrB,Q,iBCPSyN,GAlBD,CACZ3L,MAAO4L,KACP3L,QAAS4L,KACT3L,MAAO4L,KACPC,SAAUC,KACVC,QAASC,KACTC,SAAUC,KACVC,OAAQC,KACRC,SAAUC,KACVC,UAAWC,KACXC,cAAeC,KACfC,QAASC,KACTC,OAAQC,KACRC,SAAUC,KACVC,SAAUC,KACVC,MAAOC,MCIM/B,mBA5BI,WACjB,IACQ7S,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MACR+M,EAAOlM,eAAIsK,GAAO,YAExB,OAAIlH,OAAMtF,EAAK,gCAAgC,IAE3C,yBAAK2D,UAAU,6BACb,kBAACyK,EAAD,CACEnH,KAAK,OACLtD,UAAU,OACVK,MAAO,CAAEqK,MAAOhN,EAAMtM,OAAO2M,WAE/B,0BAAMiC,UAAU,yBACb9E,GAAW,CACVC,KAAMwG,OAAMtF,EAAK,gCAAgC,IACjDjB,SAAUiB,EAAKjB,UAAY,KAC3BC,YAAY,MAOf,QCvBHsP,GAAc,SAAC,GAA2B,IAAzB1S,EAAwB,EAAxBA,MAAOoL,EAAiB,EAAjBA,KAAMuH,EAAW,EAAXA,KAC5BH,EAAOlM,eAAIsK,GAAOxF,GAAQA,EAAKwH,cAAeC,MAE5ClV,EADQuP,qBAAWnG,IACnBpJ,MACM8H,GAAU9H,EAAhByG,KAAgBzG,EAAV8H,OAEd,OAAOzF,EACL,yBAAK+H,UAAU,qBACb,kBAACyK,EAAD,CACEnH,KAAK,OACLtD,UAAU,OACVK,MAAO,CAAEqK,MAAOhN,EAAMtM,OAAO2M,WAE9B6M,EACC,uBAAG/E,KAAM+E,EAAMpK,OAAO,SAASoF,IAAI,uBACjC,0BAAM5F,UAAU,yBAAyB/H,IAG3C,0BAAM+H,UAAU,yBAAyB/H,IAG3C,MA6CSwQ,mBA1CE,WAAO,IACd9M,EAAMiH,eAANjH,EAEA/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAgBzG,EAAV8H,MAEd,OACE,yBAAKsC,UAAU,sBACb,kBAAC,GAAD,CACEC,MAAO0B,OAAMtF,EAAK,wBAAyBV,EAAE,UAC7C1D,MAAO0J,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,YAAa,IACnGzB,KAAK,QACLuH,KAAI,cAASjJ,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,YAAa,OAE3G,kBAAC,GAAD,CACE7E,MAAO0B,OAAMtF,EAAK,0BAA2BV,EAAE,YAC/C1D,MAAO0J,OAAMtF,EAAK,gCAAgC,IAClDgH,KAAK,UACLuH,KAAMjJ,OAAMtF,EAAK,gCAAgC,MAEnD,kBAAC,GAAD,CACE4D,MAAO0B,OAAMtF,EAAK,wBAAyBV,EAAE,UAC7C1D,MAAO0J,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,QAAS,IAC/FzB,KAAK,QACLuH,KAAI,iBAAYjJ,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,QAAS,OAG1G,kBAAC,GAAD,MAEC7I,GAAYI,EAAK0O,SAChB1O,EAAK0O,OAAOxN,MAAMkD,KAAI,SAAC/B,GAAD,OACpB,kBAAC,GAAD,CACE1G,IAAK0G,EAAE/H,GACPsB,MAAOyG,EAAEsM,SACT3H,KAAM3E,EAAEuM,QACRL,KAAMlM,EAAEwM,aC9DdC,GAAoB,SAAAzM,GAAC,OACvBA,GACI,uBAAG1G,IAAK4M,gBAAR,SAA2BlG,IAcpB+J,mBAVQ,SAAC,GAAD,IAAEjL,EAAF,EAAEA,OAAF,OACnBA,GAAWA,EAAO3G,OAAO,GACvB,yBAAKmJ,UAAU,8BAEbxC,EAAOgI,QAAO,SAAA9G,GAAC,MAAW,KAANA,KAAW+B,IAAI0K,QCPrCC,GAAgB,SAAC,GAAwB,IAAtB1U,EAAqB,EAArBA,KAAM0E,EAAe,EAAfA,SACrBO,EAAMiH,eAANjH,EACR,OACE,6BACE,yBAAKqE,UAAU,qCACb,yBAAKA,UAAU,gCACb,wBAAIA,UAAU,yBAAyB2B,OAAMjL,EAAM,sBAAuB,KAC1E,0BAAMsJ,UAAU,WACd,gCAAS2B,OAAMjL,EAAM,mBAAoB,KAD3C,IAC0DiL,OAAMjL,EAAM,qCAAsC,MAG9G,yBAAKsJ,UAAU,sCAC2B,KAAvC2B,OAAMjL,EAAM,kBAAmB,KAC9B,wBAAIsJ,UAAU,4BAAd,IAEGtE,GACC,CACEE,UAAW+F,OAAMjL,EAAM,kBAAmB,IAC1CmF,QAAS8F,OAAMjL,EAAM,gBAAiB,IACtC0E,YAEFO,GARJ,KAaF,0BAAMqE,UAAU,uBAAuB2B,OAAMjL,EAAM,8BAA+B,IAAlF,IAAwFiL,OAAMjL,EAAM,6BAA8B,OAGrIA,EAAK2U,SACJ,kBAAC,KAAD,CACErL,UAAU,wBACVwI,OAAQ7G,OAAMjL,EAAM,WAAY,MAGpC,kBAAC,GAAD,CAAgB8G,OAAQmE,OAAMjL,EAAM,UAAW,QAwBtC+R,mBAnBI,WAAO,IAAD,EACYtD,qBAAW5F,IAAtClD,EADe,EACfA,KAAeqM,EADA,EACThM,QAEd,OAAQiF,OAAMtF,EAAM,oCAAqC,IAAIxF,OAAO,GAClEwF,EAAKxL,UAAUsL,OACf,6BACE,kBAACuM,EAAD,KAAUrM,EAAKxL,UAAU6L,SACzB,yBAAKsD,UAAU,cACV2B,OAAMtF,EAAM,oCAAqC,IAAImJ,QAAO,SAAA9G,GAAC,OAAMiD,OAAMjD,EAAG,MAAO,IAAIkK,SAAS,YAAmD,WAArCjH,OAAMjD,EAAG,qBAAsB,OAAiB+B,KAAI,SAAC/B,GAAD,OAAQ,kBAAC,GAAD,CACzK1G,IAAK2J,OAAMjD,EAAG,MAAOkG,gBACrBlO,KAAMgI,EACNtD,SAAUiB,EAAKjB,UAAY,YAKjC,QChDSqN,mBAfE,SAAC,GAAkB,IAAhBrJ,EAAe,EAAfA,SAEVxJ,EADQuP,qBAAWnG,IACnBpJ,MACM8H,GAAU9H,EAAhByG,KAAgBzG,EAAV8H,OAEd,OACE,wBACEsC,UAAU,mCACVK,MAAO,CAAEqK,MAAOhN,EAAMtM,OAAO2M,UAE5BqB,MCTDkM,GAAS,SAAC5M,GAAD,OACb,yBAAK1G,IAAK0G,EAAE/H,IACV,wBAAIqJ,UAAU,yBAAyBtB,EAAE5K,QAe9B2U,mBAXE,WAAO,IAAD,EACctD,qBAAW5F,IAAtClD,EADa,EACbA,KAAeqM,EADF,EACPhM,QAEd,OAAOT,GAAYI,EAAKkP,SACtB,6BACE,kBAAC7C,EAAD,KAAUrM,EAAKkP,QAAQ7O,SACvB,yBAAKsD,UAAU,cAAc3D,EAAKkP,QAAQhO,MAAMkD,IAAI6K,MAEpD,QCXAE,GAAe,SAAC9M,GAAD,OACnB,yBAAK1G,IAAK2J,OAAMjD,EAAE,MAAOkG,gBAAW5E,UAAU,iBAC5C,wBAAIA,UAAU,yBAAyB2B,OAAMjD,EAAG,OAAQ,KACxD,0BAAMsB,UAAU,WAAWtB,EAAE+M,WAkBlBhD,mBAdI,WAAO,IAAD,EACYtD,qBAAW5F,IAAtClD,EADe,EACfA,KAAeqM,EADA,EACThM,QAEd,OAAQL,EAAKrL,WACXqL,EAAKrL,UAAUmL,QAAWwF,OAAMtF,EAAM,oCAAoC,IAAIxF,OAAS,EACvF,6BACE,kBAAC6R,EAAD,KAAUrM,EAAKrL,UAAU0L,SACzB,yBAAKsD,UAAU,0BACZ2B,OAAMtF,EAAM,oCAAqC,IAAImJ,QAAO,SAAA9G,GAAC,MAA6B,KAAzBiD,OAAMjD,EAAG,OAAQ,OAAY+B,IAAI+K,MAGrG,QCLS/C,mBAfI,WAAO,IAAD,EACYtD,qBAAW5F,IAAtClD,EADe,EACfA,KAAeqM,EADA,EACThM,QAEd,OACEiF,QAAOA,OAAMtF,EAAM,4BAA4B,KAAK,GAClD,6BACE,kBAACqM,EAAD,KAAUrM,EAAK1L,UAAU+L,SACxBiF,OAAMtF,EAAM,4BAA4B,IAAIoE,KAAI,SAAC/B,EAAGlI,GAAJ,OAC3C,kBAAC,KAAD,CAAewB,IAAK,YAAYxB,EAAOwJ,UAAU,gBAAgBwI,OAAQ9J,EAAE2G,qBCPnFqG,GAAc,SAAC,GAAwB,IAAtBhV,EAAqB,EAArBA,KAAM0E,EAAe,EAAfA,SACnBO,EAAMiH,eAANjH,EACR,OACE,6BACE,yBAAKqE,UAAU,qCACb,yBAAKA,UAAU,gCACb,wBAAIA,UAAU,yBAAyBtJ,EAAKwN,OAC3CxN,EAAKkU,MACJ,uBAAG/E,KAAMnP,EAAKkU,KAAM5K,UAAU,WAC3BtJ,EAAKkU,OAIXlU,EAAKyE,MACJ,wBAAI6E,UAAU,kCAAd,IAEGtE,GACC,CACEE,UAAWlF,EAAKyE,KAChBU,QAASnF,EAAKmF,QACdT,YAEFO,GARJ,MAcHjF,EAAK2U,SACJ,kBAAC,KAAD,CACErL,UAAU,wBACVwI,OAAQ9R,EAAK2U,YAsBR5C,mBAfG,WAAO,IAAD,EACatD,qBAAW5F,IAAtClD,EADc,EACdA,KAAeqM,EADD,EACRhM,QAEd,OAAOT,GAAYI,EAAKsP,UACtB,6BACE,kBAACjD,EAAD,KAAUrM,EAAKsP,SAASjP,SACxB,yBAAKsD,UAAU,cACZ3D,EAAKsP,SAASpO,MAAMkD,KAAI,SAAC/B,GAAD,OACvB,kBAAC,GAAD,CAAa1G,IAAK0G,EAAE/H,GAAID,KAAMgI,EAAGtD,SAAUiB,EAAKuP,SAASxQ,gBAI7D,QChDAyQ,GAAgB,SAACnN,GAAD,OACpB,yBAAK1G,IAAK2J,OAAMjD,EAAG,MAAOkG,gBAAW5E,UAAU,iBAC7C,wBAAIA,UAAU,yBAAyB2B,OAAMjD,EAAG,wCAAyC,IAAzF,IAA+FiD,OAAMjD,EAAG,yCAA0C,KAClJ,0BAAMsB,UAAU,WAAW2B,OAAMjD,EAAG,uCAAwC,KAC5E,0BAAMsB,UAAU,WAAW2B,OAAMjD,EAAG,wCAAyC,KAC7E,0BAAMsB,UAAU,WAAW2B,OAAMjD,EAAG,oCAAqC,KACX,KAA7DiD,OAAMjD,EAAG,2CAA4C,KACpD,kBAAC,KAAD,CAAesB,UAAU,wBAAwBwI,OAAQ7G,OAAMjD,EAAG,2CAA4C,QAkBrG+J,mBAbK,WAAO,IAAD,EACWtD,qBAAW5F,IAAtClD,EADgB,EAChBA,KAAeqM,EADC,EACVhM,QAEd,OAAOT,GAAYI,EAAKpL,YACtB,6BACE,kBAACyX,EAAD,KAAUrM,EAAKpL,WAAWyL,SAC1B,yBAAKsD,UAAU,0BACZ2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,uBAAwB,IAAIkJ,QAAO,SAAA9G,GAAC,MAAiD,cAA7CiD,OAAMjD,EAAG,4BAA6B,OAAoB+B,IAAIoL,MAGzI,Q,SCMAC,GAAY,SAACpN,EAAGqN,GAAJ,YACVC,IAANtN,GAAyB,cAANA,GAA2B,KAANA,GACxC,yBAAK1G,IAAK4M,eAAU5E,UAAU,iBAC5B,wBAAIA,UAAU,yBAAyBtB,GACvC,0BAAMsB,UAAU,WAAY+L,EAAQ,EAAK,WAAcA,GAAQ,GAAKA,GAAO,EAAK,eAAgB,cAmBrFtD,mBAdC,WAAO,IAAD,EACetD,qBAAW5F,IAAtClD,EADY,EACZA,KAAeqM,EADH,EACNhM,QACRc,EAxCW,SAACnB,GAkBhB,IAjBA,IAAI4P,EAAatK,SAAQA,OAAMtF,EAAM,oCAAqC,KAAKoE,IAAI,UAAUyL,UAEzFC,EAAcxK,SAAQA,OAAMtF,EAAM,4BAA6B,KAAKoE,IAAI,kBAAkByL,UAE1FE,EAAkBzK,SAAQA,OAAMtF,EAAM,oCAAqC,KAAKoE,IAAI,WAAWyL,UAE/FG,EAAgB1K,SAAQA,OAAMtF,EAAM,oCAAqC,KAAKoE,IAAI,SAASyL,UAAUzL,IAAI,aAAayL,UAAUzL,IAAI,WAAWyL,UAE/II,EAAyB3K,SAAQA,OAAMtF,EAAM,oCAAqC,KAAKoE,IAAI,SAASA,IAAI,eAAeyL,UAAUzL,IAAI,WAAWyL,UAAUzL,IAAI,WAAWyL,UAEzKK,EAAyB5K,SAAQA,OAAMtF,EAAM,2CAA4C,KAAKoE,IAAI,UAAUyL,UAAUzL,IAAI,WAAWyL,UAErIM,EAA0B7K,SAAQA,OAAMtF,EAAM,2CAA4C,KAAKoE,IAAI,UAAUyL,UAAUzL,IAAI,YAAYyL,UAEvIO,EAAS,uBAAOR,GAAP,aAAsBE,GAAtB,aAAsCC,GAAtC,aAA0DC,GAA1D,aAA4EC,GAA5E,aAAuGC,GAAvG,aAAkIC,IAE3IE,EAAe,GACXC,EAAE,EAAGA,EAAEF,EAAU5V,OAAQ8V,IAC5BD,EAAaD,EAAUE,IACxBD,EAAaD,EAAUE,IAAMD,EAAaD,EAAUE,IAAM,EAE1DD,EAAaD,EAAUE,IAAM,EAIjC,OAAOD,EAcME,CAAWvQ,GAE1B,OAAOsF,QAAOnE,GAAU,EACtB,6BACE,kBAACkL,EAAD,KAAUrM,EAAKmB,OAAOd,SACtB,yBAAKsD,UAAU,oCACZnB,OAAOC,KAAKtB,GAAQiD,IAAIqL,MAG3B,QC9CAe,GAAyB,SAAAnO,GAAC,OAC5BA,GACI,wBAAIsB,UAAU,eAAehI,IAAK4M,gBAAWlG,IAI/CoO,GAAqB,SAAC,GAAD,IAAElG,EAAF,EAAEA,iBAAF,OACvBA,GAAqBA,EAAiB/P,OAAO,GAC3C,4BAEE+P,EAAiBpB,QAAO,SAAA9G,GAAC,MAAW,KAANA,KAAW+B,IAAIoM,MAM/CE,GAAW,SAAC,GAA6B,IAA3BrW,EAA0B,EAA1BA,KAA0B,IAApB0E,gBAAoB,MAAX,KAAW,EACpCO,EAAMiH,eAANjH,EACR,OACE,6BACE,yBAAKqE,UAAU,qCACb,yBAAKA,UAAU,gCACb,wBAAIA,UAAU,yBAAyB2B,OAAMjL,EAAK,2BAA2B,KAC7E,0BAAMsJ,UAAU,WAAW2B,OAAMjL,EAAK,WAAY,MAEnB,KAA/BiL,OAAMjL,EAAK,YAAY,KACvB,wBAAIsJ,UAAU,kCAAd,IAEGtE,GACC,CACEE,UAAW+F,OAAMjL,EAAK,YAAY,IAClCmF,QAAS8F,OAAMjL,EAAK,UAAU,IAC9B0E,YAEFO,GARJ,MAc8B,KAAjCgG,OAAMjL,EAAK,cAAc,KACxB,kBAAC,KAAD,CACEsJ,UAAU,wBACVwI,OAAQ7G,OAAMjL,EAAK,cAAc,MAGrC,kBAAC,GAAD,CAAoBkQ,iBAAkBjF,OAAMjL,EAAM,iCAAkC,MACpF,kBAAC,GAAD,CAAgB8G,OAAQmE,OAAMjL,EAAM,uBAAwB,QAoBnD+R,mBAfD,WAAO,IAAD,EACiBtD,qBAAW5F,IAAtClD,EADU,EACVA,KAAeqM,EADL,EACJhM,QAEd,OAAQiF,OAAMtF,EAAM,oCAAqC,IAAIxF,OAAS,GAAKwF,EAAKzL,KAAKuL,OACnF,6BACE,kBAACuM,EAAD,KAAUrM,EAAKzL,KAAK8L,SACpB,yBAAKsD,UAAU,cACZ2B,OAAMtF,EAAM,oCAAqC,IAAImJ,QAAO,SAAA9G,GAAC,OAAKiD,OAAMjD,EAAG,MAAO,IAAIkK,SAAS,cAAYnI,KAAI,SAAC/B,GAAD,OAC9G,kBAAC,GAAD,CAAU1G,IAAK2J,OAAMjD,EAAE,MAAOkG,gBAAWlO,KAAMgI,EAAGtD,SAAUiB,EAAKjB,UAAY,YAIjF,QCnEA4R,GAAc,SAAC,GAAD,IAAEtO,EAAF,EAAEA,EAAUuO,GAAZ,EAAKzW,MAAL,EAAYyW,cAAZ,OAChBvO,GACE,yBAAKsB,UAAWiN,GACd,8BAAOvO,EAAE4F,eACT,qCAAa5F,EAAE6F,gBAAf,IAAiC7F,EAAE8F,eACnC,qCAAa9F,EAAE+F,eAAf,IAAgC/F,EAAEgG,cAkB3B+D,mBAbE,SAAC,GAAD,IAAEpM,EAAF,EAAEA,KAAF,IAAQ6Q,qBAAR,MAAsB,GAAtB,MAA0BC,kBAA1B,MAAqC,2BAArC,EAAkEF,EAAlE,uDAA+E,wBAA/E,OAEX5Q,EAAKC,OAAO,UAAU,GAAGG,SAAWJ,EAAKC,OAAO,UAAU,GAAGG,QAAQ5F,OAAO,GAC5EwF,EAAKI,QAAQN,QACX,yBAAK6D,UAAWkN,GACd,wBAAIlN,UAAWmN,GAAaxL,OAAMtF,EAAM,0BAA2B,YAElEA,EAAKC,OAAO,UAAU,GAAGG,QAAQ+I,QAAO,SAAA9G,GAAC,OAAK/D,KAAKyD,MAAMM,EAAEmG,eAAeG,cAAgBrK,KAAKyD,MAAM,IAAIzD,MAAS,KAAG8F,KAAI,SAAC/B,EAAGlI,GAAJ,OAAe,kBAAC,GAAD,CAAakI,EAAGA,EAAGlI,MAAOA,EAAOyW,aAAcA,EAAcjV,IAAKxB,SAG3M,MCdKiS,mBANA,SAAC,GAAD,IAAEpM,EAAF,EAAEA,KAAF,IAAQ2D,iBAAR,MAAkB,oCAAlB,EAAwDK,EAAxD,uDAA8D,CAAE+M,SAAU,UAA1E,OACX,wBAAIpN,UAAWA,EAAWK,MAAOA,GACzBoB,MAAMC,QAAQrF,EAAKC,OAAO,UAAU,GAAGC,WAAeoF,OAAMtF,EAAK,6CAA6C,IAAQA,EAAKC,OAAO,UAAU,GAAGC,UADvJ,IACqKkF,MAAMC,QAAQrF,EAAKC,OAAO,UAAU,GAAGE,YAAgBmF,OAAMtF,EAAK,8CAA+C,IAAQA,EAAKC,OAAO,UAAU,GAAGE,eCsB5SiM,mBAxBG,SAAC,GAAD,IAAEpM,EAAF,EAAEA,KAAF,OACd,wBAAI2D,UAAU,oCACb2B,OAAMtF,EAAK,mCAAoC,IAAQ,KAAKsF,OAAMtF,EAAK,gCAAiC,IAAIoE,KAAI,SAAS4M,EAAK7W,GACrH,GAAGA,EAAQ,GAAK6W,EAAK,UAAU,CAC7B,IAAIvZ,EAAOuZ,EAAK,UACZC,EAAkB3L,OAAMtF,EAAK,iCAAiC,IAAI5F,WAAU,SAAAiI,GAAC,OAAEA,EAAE,eAAe2O,EAAK,gBAMzG,OALGC,GAAmB,GACjBjR,EAAKC,OAAO,UAAU,GAAGE,WAAW8Q,IAAoBjR,EAAKC,OAAO,UAAU,GAAGE,WAAW8Q,GAAiB,YAC9GxZ,GAAQ,IAAIuI,EAAKC,OAAO,UAAU,GAAGE,WAAW8Q,GAAiB,WAG9DxZ,EAEP,OAAO,QAER0R,QAAO,SAAUtT,GAClB,OAAa,MAANA,KACNqb,KAAK,MAAM,IAEb,OCFPC,GAAS,CACb7c,UAAW8c,GACX7c,KAAM8c,GACN7c,UAAW8c,GACXhC,SAAUiC,GACV9c,OAAQ+c,GACR9c,eAAgB+c,GAChBtQ,OAAQuQ,GACRxC,QAASyC,GACThd,UAAWid,GACXhd,WAAYid,IA2ECC,GAxEF,WACGvL,eAANjH,EADU,IAGV/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MACRC,EAASgE,OAAMjE,EAAM,oBAAqB,IAEhD,OACE,kBAAC,GAAYwB,SAAb,CAAsBjH,MAAO,CAAEoE,OAAMK,QAAS0R,KAC5C,yBACEzX,GAAG,OACHqJ,UAAU,cACVK,MAAO,CACLgO,WAAY3Q,EAAME,KAAKC,OACvB6M,MAAOhN,EAAMtM,OAAO2M,QACpBuQ,gBAAiB5Q,EAAMtM,OAAO0M,aAGhC,yBAAKkC,UAAU,iCACb,yBAAKA,UAAU,gCACgD,KAA5D2B,OAAMtF,EAAM,uCAAwC,KACnD,yBACE2D,UAAU,4BACVuO,IAAK5M,OAAMtF,EAAM,uCAAwC,IACzDmS,IAAI,oBACJnO,MAAO,CAAE3G,MAAO,QAASC,OAAQ,WAIrC,6BACI,kBAAC,GAAD,CAAQ0C,KAAMA,EAAM2D,UAAU,qBAAqBK,MAAO,CAAEqK,MAAOhN,EAAMtM,OAAO2M,WAChF,kBAAC0Q,GAAD,CAAWpS,KAAMA,IACnB,wBAAI2D,UAAU,uBAAuB2B,OAAMtF,EAAM,kCAAmC,KAEpF,kBAAC,GAAD,CAAUA,KAAMA,EAAM6Q,cAAc,6BAA6BC,WAAW,iDAAiDF,aAAa,OAI9I,kBAAC,GAAD,OAGF,wBACEjN,UAAU,kBACVK,MAAO,CAAEqO,YAAahR,EAAMtM,OAAO2M,WAGrC,yBAAKiC,UAAU,cACZrC,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,OAGxC,yBAAKsB,UAAU,0BACZrC,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,QAIzCf,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,W,UClGrCiQ,G,QAAQxV,EACNgV,MCiBA1F,mBAlBE,SAAC,GAAkB,IAAhBrJ,EAAe,EAAfA,SAEVxJ,EADQuP,qBAAWnG,IACnBpJ,MACM8H,GAAU9H,EAAhByG,KAAgBzG,EAAV8H,OAEd,OACE,wBACEsC,UAAU,iEACVK,MAAO,CACLqK,MAAOhN,EAAMtM,OAAO2M,QACpB2Q,YAAahR,EAAMtM,OAAO2M,UAG3BqB,MCQQqJ,mBAnBI,WACjB,IACQ7S,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MACd,OACEiE,QAAOA,OAAMtF,EAAM,4BAA4B,KAAK,GAClD,6BACE,wBACE2D,UAAU,kBACVK,MAAO,CAAEqO,YAAahR,EAAMtM,OAAO0M,cAEpC6D,OAAMtF,EAAM,4BAA4B,IAAIoE,KAAI,SAAC/B,EAAGlI,GAAJ,OAC3C,kBAAC,KAAD,CAAewB,IAAK,YAAYxB,EAAOwJ,UAAU,UAAUwI,OAAQ9J,EAAE2G,qBCM7EmI,GAAS,CACb5c,KAAM8c,GACN7c,UAAW8c,GACXhC,SAAUiC,GACV9c,OAAQ+c,GACR9c,eAAgB+c,GAChBtQ,OAAQuQ,GACRxC,QAASyC,GACThd,UAAWid,GACXhd,WAAYid,IAmFCU,GAhFC,WACAhM,eAANjH,EADa,IAGb/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MACRC,EAASgE,OAAMjE,EAAM,uBAAwB,IAEnD,OACE,kBAAC,GAAYwB,SAAb,CAAsBjH,MAAO,CAAEoE,OAAMK,QAASmS,KAC5C,yBACElY,GAAG,OACHqJ,UAAU,cACVK,MAAO,CACLgO,WAAY3Q,EAAME,KAAKC,OACvB6M,MAAOhN,EAAMtM,OAAO2M,QACpBuQ,gBAAiB5Q,EAAMtM,OAAO0M,aAGhC,yBAAKkC,UAAU,2BACgD,KAA5D2B,OAAMtF,EAAM,uCAAwC,KACnD,yBAAK2D,UAAU,0BACb,yBACEA,UAAU,8CACVuO,IAAK5M,OAAMtF,EAAM,uCAAwC,IACzDmS,IAAI,uBAKV,yBACExO,UAAS,UACqD,KAA5D2B,OAAMtF,EAAM,uCAAwC,IAAa,aAAe,gBAGlF,yBACE2D,UAAU,4CACVK,MAAO,CACLiO,gBAAiB5Q,EAAMtM,OAAO2M,QAC9B2M,MAAOhN,EAAMtM,OAAO0M,aAGtB,yBAAKkC,UAAU,0CACb,kBAAC,GAAD,CAAQ3D,KAAMA,EAAM2D,UAAU,qCAC9B,kBAACyO,GAAD,CAAWpS,KAAMA,IACjB,yBAAK2D,UAAU,qCACZ2B,OAAMtF,EAAM,kCAAmC,KAGlD,kBAAC,GAAD,CAAYA,KAAMA,OAKxB,yBAAK2D,UAAU,cACb,yBAAKA,UAAU,cACb,kBAAC,GAAD,MAECrC,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,SAK5C,yBAAKsB,UAAU,cACb,yBAAKA,UAAU,cACZrC,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,a,UCtGzCiQ,G,QAAQxV,EACNyV,MCgCAnG,mBA5BI,WACjB,IACQ7S,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MACR+M,EAAOlM,eAAIsK,GAAO,YAExB,OAAIlH,OAAMtF,EAAK,gCAAgC,IAE3C,yBAAK2D,UAAU,6BACb,kBAACyK,EAAD,CACEnH,KAAK,OACLtD,UAAU,OACVK,MAAO,CAAEqK,MAAOhN,EAAMtM,OAAO0M,cAE/B,0BAAMkC,UAAU,yBACb9E,GAAW,CACVC,KAAMwG,OAAMtF,EAAK,gCAAgC,IACjDjB,SAAUiB,EAAKjB,UAAY,KAC3BC,YAAY,MAOf,QCvBHsP,GAAc,SAAC,GAA2B,IAAzB1S,EAAwB,EAAxBA,MAAOoL,EAAiB,EAAjBA,KAAMuH,EAAW,EAAXA,KAE1BhV,EADQuP,qBAAWnG,IACnBpJ,MACM8H,GAAU9H,EAAhByG,KAAgBzG,EAAV8H,OACR+M,EAAOlM,eAAIsK,GAAOxF,GAAQA,EAAKwH,cAAeC,MAEpD,OAAO7S,EACL,yBAAK+H,UAAU,qBACb,kBAACyK,EAAD,CACEnH,KAAK,OACLtD,UAAU,OACVK,MAAO,CAAEqK,MAAOhN,EAAMtM,OAAO0M,cAE9B8M,EACC,uBAAG/E,KAAM+E,EAAMpK,OAAO,SAASoF,IAAI,uBACjC,0BAAM5F,UAAU,yBAAyB/H,IAG3C,0BAAM+H,UAAU,yBAAyB/H,IAG3C,MA6CSwQ,mBA1CE,WAAO,IACd9M,EAAMiH,eAANjH,EAEA/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAgBzG,EAAV8H,MAEd,OACE,yBAAKsC,UAAU,sBACb,kBAAC,GAAD,CACEC,MAAO0B,OAAMtF,EAAK,wBAAyBV,EAAE,UAC7C1D,MAAO0J,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,YAAa,IACnGzB,KAAK,QACLuH,KAAI,cAASjJ,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,YAAa,OAE3G,kBAAC,GAAD,CACE7E,MAAO0B,OAAMtF,EAAK,0BAA2BV,EAAE,YAC/C1D,MAAO0J,OAAMtF,EAAK,gCAAgC,IAClDgH,KAAK,UACLuH,KAAMjJ,OAAMtF,EAAK,gCAAgC,MAEnD,kBAAC,GAAD,CACE4D,MAAO0B,OAAMtF,EAAK,wBAAyBV,EAAE,UAC7C1D,MAAO0J,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,QAAS,IAC/FzB,KAAK,QACLuH,KAAI,iBAAYjJ,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,QAAS,OAG1G,kBAAC,GAAD,MAEC7I,GAAYI,EAAK0O,SAChB1O,EAAK0O,OAAOxN,MAAMkD,KAAI,SAAC/B,GAAD,OACpB,kBAAC,GAAD,CACE1G,IAAK0G,EAAE/H,GACPsB,MAAOyG,EAAEsM,SACT3H,KAAM3E,EAAEuM,QACRL,KAAMlM,EAAEwM,aC/DLzC,mBAJE,SAAC,GAAD,IAAGrJ,EAAH,EAAGA,SAAH,OACf,wBAAIY,UAAU,kDAAkDZ,MCK5DyM,GAAgB,SAACnN,GAAD,OACpB,yBAAK1G,IAAK2J,OAAMjD,EAAG,MAAOkG,gBAAW5E,UAAU,iBAC7C,wBAAIA,UAAU,yBAAyB2B,OAAMjD,EAAG,wCAAyC,IAAzF,IAA+FiD,OAAMjD,EAAG,yCAA0C,KAClJ,0BAAMsB,UAAU,WAAW2B,OAAMjD,EAAG,uCAAwC,KAC5E,0BAAMsB,UAAU,WAAW2B,OAAMjD,EAAG,wCAAyC,KAC7E,0BAAMsB,UAAU,WAAW2B,OAAMjD,EAAG,oCAAqC,KACb,KAA3DiD,OAAMjD,EAAG,2CAA4C,KACpD,kBAAC,KAAD,CAAesB,UAAU,wBAAwBwI,OAAQ7G,OAAMjD,EAAG,2CAA4C,QAkBrG+J,mBAbK,WAAO,IAAD,EACWtD,qBAAW5F,IAAtClD,EADgB,EAChBA,KAAeqM,EADC,EACVhM,QAEd,OAAOT,GAAYI,EAAKpL,YACtB,6BACE,kBAACyX,EAAD,KAAUrM,EAAKpL,WAAWyL,SAC1B,yBAAKsD,UAAU,cACZ2B,OAAMtF,EAAKC,OAAO,UAAU,GAAI,uBAAwB,IAAIkJ,QAAO,SAAA9G,GAAC,MAAiD,cAA7CiD,OAAMjD,EAAG,4BAA6B,OAAoB+B,IAAIoL,MAGzI,QCZSpD,mBAXA,SAAC,GAAD,IAAEpM,EAAF,EAAEA,KAAF,IAAQ2D,iBAAR,MAAkB,mCAAlB,EAAuDK,EAAvD,uDAA6D,GAA7D,OACd,oCACC,wBAAIL,UAAWA,EAAWK,MAAOA,GAC5BoB,MAAMC,QAAQrF,EAAKC,OAAO,UAAU,GAAGC,WAAeoF,OAAMtF,EAAK,6CAA6C,IAAQA,EAAKC,OAAO,UAAU,GAAGC,WAEpJ,wBAAIyD,UAAWA,EAAWK,MAAOA,GAC5BoB,MAAMC,QAAQrF,EAAKC,OAAO,UAAU,GAAGE,YAAgBmF,OAAMtF,EAAK,8CAA+C,IAAQA,EAAKC,OAAO,UAAU,GAAGE,gBCWnJgR,GAAS,CACb7c,UAAW8c,GACX7c,KAAM8c,GACN7c,UAAW8c,GACXhC,SAAUiC,GACV9c,OAAQ+c,GACR9c,eAAgB+c,GAChBtQ,OAAQuQ,GACRxC,QAASyC,GACThd,UAAWid,GACXhd,WAAY6d,IA8GCC,GA3GA,WACCnM,eAANjH,EADY,IAGZ/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MACRC,EAASgE,OAAMjE,EAAM,sBAAuB,IAL/B,EAOCvG,GAASuG,EAAMtM,OAAO2M,UAAY,GAA9CxG,EAPW,EAOXA,EAAGC,EAPQ,EAORA,EAAGP,EAPK,EAOLA,EAER+X,EAAQ,iBACgD,KAA5DrN,OAAMtF,EAAM,uCAAwC,KAClD,yBACE2D,UAAU,oDACVK,MAAO,CACLqO,YAAahR,EAAMtM,OAAO0M,YAE5ByQ,IAAK5M,OAAMtF,EAAM,uCAAwC,IACzDmS,IAAI,uBAIJS,EAAU,kBACd,6BACE,kBAAC,GAAD,CAAQ5S,KAAMA,EAAM2D,UAAU,qCAC9B,kBAACyO,GAAD,CAAWpS,KAAMA,IACjB,yBAAK2D,UAAU,4BAA4B2B,OAAMtF,EAAM,kCAAmC,OAI9F,OACE,kBAAC,GAAY6C,SAAb,CAAsBjH,MAAO,CAAEoE,OAAMK,QAASwS,KAC5C,yBACEvY,GAAG,OACHqJ,UAAU,UACVK,MAAO,CACLgO,WAAY3Q,EAAME,KAAKC,OACvB6M,MAAOhN,EAAMtM,OAAO2M,QACpBuQ,gBAAiB5Q,EAAMtM,OAAO0M,aAGhC,yBAAKkC,UAAU,qBACb,yBACEA,UAAU,uBACVK,MAAO,CACLiO,gBAAiB5Q,EAAMtM,OAAO2M,QAC9B2M,MAAOhN,EAAMtM,OAAO0M,aAGtB,yBAAKkC,UAAU,qBACb,kBAACgP,EAAD,MACA,kBAACC,EAAD,OAGF,kBAAC,GAAD,CAAU5S,KAAMA,EAAM6Q,cAAc,6BAA6BC,WAAW,iDAAiDF,aAAa,KAE1I,wBACEjN,UAAU,wBACVK,MAAO,CAAEqO,YAAahR,EAAMtM,OAAO0M,cAGrC,wBAAIkC,UAAU,kDACX3D,EAAKgB,SAASX,SAAW,WAE5B,kBAAC,GAAD,OAGF,yBACEsD,UAAU,uBACVK,MAAO,CAAEiO,gBAAgB,QAAD,OAAU/W,EAAV,aAAgBC,EAAhB,aAAsBP,EAAtB,YAExB,yBAAK+I,UAAU,2BACZrC,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,SAK5C,yBACEsB,UAAU,uBACVK,MAAO,CAAEiO,gBAAgB,QAAD,OAAU/W,EAAV,aAAgBC,EAAhB,aAAsBP,EAAtB,YAExB,yBAAK+I,UAAU,cACZrC,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,SAK5C,yBAAKsB,UAAU,wBACb,yBAAKA,UAAU,cACZrC,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,a,UCnIzCiQ,G,QAAQxV,EACN4V,MC+BAtG,mBA5BI,WACH7F,eAANjH,EADgB,IAGhB/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAgBzG,EAAV8H,MAEd,OAAIiE,OAAMtF,EAAK,gCAAgC,IAE3C,yBAAK2D,UAAU,WACb,wBAAIA,UAAU,4BACX3D,EAAK3L,QAAQye,UAAUzS,SAAW,cAErC,6BACE,8BACGxB,GAAW,CACVC,KAAMwG,OAAMtF,EAAK,gCAAgC,IACjDjB,SAAUiB,EAAKjB,UAAY,KAC3BC,YAAY,OAQjB,QCxBHsP,GAAc,SAAC,GAAD,IAAG1S,EAAH,EAAGA,MAAOgI,EAAV,EAAUA,MAAO2K,EAAjB,EAAiBA,KAAjB,OAClB3S,EACE,yBAAK+H,UAAU,iBACb,wBAAIA,UAAU,4BAA4BC,GACzC2K,EACC,uBAAG/E,KAAM+E,EAAMpK,OAAO,SAASoF,IAAI,uBACjC,0BAAM5F,UAAU,yBAAyB/H,IAG3C,0BAAM+H,UAAU,yBAAyB/H,IAG3C,MA2CSwQ,mBAzCE,WAAO,IACd9M,EAAMiH,eAANjH,EAEA/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAgBzG,EAAV8H,MAEd,OACE,yBAAKsC,UAAU,sBACX,kBAAC,GAAD,CAAU3D,KAAMA,IAElB,kBAAC,GAAD,CACE4D,MAAO0B,OAAMtF,EAAK,wBAAyBV,EAAE,UAC7C1D,MAAO0J,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,YAAa,IACnG8F,KAAI,cAASjJ,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,YAAa,OAE3G,kBAAC,GAAD,CACE7E,MAAO0B,OAAMtF,EAAK,0BAA2BV,EAAE,YAC/C1D,MAAO0J,OAAMtF,EAAK,gCAAgC,IAClDuO,KAAMjJ,OAAMtF,EAAK,gCAAgC,MAEnD,kBAAC,GAAD,CACE4D,MAAO0B,OAAMtF,EAAK,wBAAyBV,EAAE,UAC7C1D,MAAO0J,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,QAAS,IAC/F8F,KAAI,iBAAYjJ,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,QAAS,OAG1G,kBAAC,GAAD,MAEC7I,GAAYI,EAAK0O,SAChB1O,EAAK0O,OAAOxN,MAAMkD,KAAI,SAAC/B,GAAD,OACpB,kBAAC,GAAD,CACE1G,IAAK0G,EAAE/H,GACPsB,MAAOyG,EAAEsM,SACT/K,MAAOvB,EAAEuM,QACTL,KAAMlM,EAAEwM,aChCLzC,mBApBE,SAAC,GAAkB,IAAhBrJ,EAAe,EAAfA,SAEVxJ,EADQuP,qBAAWnG,IACnBpJ,MACM8H,GAAU9H,EAAhByG,KAAgBzG,EAAV8H,OAHmB,EAIbvG,GAASuG,EAAMtM,OAAO2M,UAAY,GAA9CxG,EAJyB,EAIzBA,EAAGC,EAJsB,EAItBA,EAAGP,EAJmB,EAInBA,EAEd,OACE,wBACE+I,UAAU,mFACVK,MAAO,CACL+O,WAAY,QACZ1E,MAAOhN,EAAMtM,OAAO0M,WACpBwQ,gBAAgB,QAAD,OAAU/W,EAAI,GAAd,aAAqBC,EAAI,GAAzB,aAAgCP,EAAI,GAApC,YAGhBmI,MCGDoO,GAAS,CACb7c,UAAW8c,GACX7c,KAAM8c,GACN7c,UAAW8c,GACXhC,SAAUiC,GACV9c,OAAQ+c,GACR9c,eAAgB+c,GAChBtQ,OAAQuQ,GACRxC,QAASyC,GACThd,UAAWid,GACXhd,WAAYid,IAiFCmB,GA9EE,WACDzM,eAANjH,EADc,IAGd/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MACRC,EAASgE,OAAMjE,EAAM,wBAAyB,IAE9CsR,EAAQ,iBACgD,KAA5DrN,OAAMtF,EAAM,uCAAwC,KAClD,yBACE2D,UAAU,yBACVK,MAAO,CACLiP,YAAa,EACbZ,YAAahR,EAAMtM,OAAO0M,YAE5ByQ,IAAK5M,OAAMtF,EAAM,uCAAwC,IACzDmS,IAAI,uBAIJS,EAAU,kBACd,6BACE,kBAAC,GAAD,CAAQ5S,KAAMA,EAAM2D,UAAU,uBAC9B,kBAACyO,GAAD,CAAWpS,KAAMA,IACjB,4BAAKsF,OAAMtF,EAAM,kCAAmC,OAIxD,OACE,kBAAC,GAAY6C,SAAb,CAAsBjH,MAAO,CAAEoE,OAAMK,QAAS6S,KAC5C,yBACE5Y,GAAG,OACHqJ,UAAU,UACVK,MAAO,CACLgO,WAAY3Q,EAAME,KAAKC,OACvB6M,MAAOhN,EAAMtM,OAAO2M,QACpBuQ,gBAAiB5Q,EAAMtM,OAAO0M,aAGhC,yBAAKkC,UAAU,qBACb,yBACEA,UAAU,4BACVK,MAAO,CACLqK,MAAOhN,EAAMtM,OAAO0M,WACpBwQ,gBAAiB5Q,EAAMtM,OAAO2M,UAGhC,yBAAKiC,UAAU,cACb,kBAACgP,EAAD,MACA,kBAACC,EAAD,MAEA,6BACE,kBAAC,GAAD,KAAW5S,EAAK3L,QAAQgM,SACxB,kBAAC,GAAD,OAGDiB,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,SAI5C,yBAAKsB,UAAU,6BACb,yBAAKA,UAAU,cACZrC,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,a,UCpGzCiQ,G,QAAQxV,EACNkW,MCKT1E,GAAc,SAAC,GAAD,IAAG1S,EAAH,EAAGA,MAAOgI,EAAV,EAAUA,MAAO2K,EAAjB,EAAiBA,KAAjB,OAClB3S,EACE,yBAAK+H,UAAU,iBACb,wBAAIA,UAAU,4BAA4BC,GACzC2K,EACC,uBAAG/E,KAAM+E,EAAMpK,OAAO,SAASoF,IAAI,uBACjC,0BAAM5F,UAAU,yBAAyB/H,IAG3C,0BAAM+H,UAAU,yBAAyB/H,IAG3C,MA4DSwQ,mBA1DE,WAAO,IACd9M,EAAMiH,eAANjH,EAEA/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MAEd,OACE,yBACEsC,UAAU,oEACVK,MAAO,CACLqO,YAAahR,EAAMtM,OAAO2M,UAG5B,yBACEiC,UAAU,uBACVK,MAAO,CACLmP,IAAK,QACLC,KAAM,MACNL,WAAY,QACZ1E,MAAOhN,EAAMtM,OAAO2M,UAGtB,kBAAC,KAAD,CAASuF,KAAK,UAGd,kBAAC,GAAD,CAAUjH,KAAMA,IAElB,kBAAC,GAAD,CACE4D,MAAO0B,OAAMtF,EAAK,wBAAyBV,EAAE,UAC7C1D,MAAO0J,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,YAAa,IACnG8F,KAAI,cAASjJ,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,YAAa,OAE3G,kBAAC,GAAD,CACE7E,MAAO0B,OAAMtF,EAAK,0BAA2BV,EAAE,YAC/C1D,MAAO0J,OAAMtF,EAAK,gCAAgC,IAClDuO,KAAMjJ,OAAMtF,EAAK,gCAAgC,MAEnD,kBAAC,GAAD,CACE4D,MAAO0B,OAAMtF,EAAK,wBAAyBV,EAAE,UAC7C1D,MAAO0J,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,QAAS,IAC/F8F,KAAI,iBAAYjJ,OAAMA,QAAOtF,EAAKC,OAAO,UAAU,GAAGiJ,aAAa,CAACT,YAAY,cAAe,QAAS,OAG1G,kBAAC,GAAD,MAEC7I,GAAYI,EAAK0O,SAChB1O,EAAK0O,OAAOxN,MAAMkD,KAAI,SAAC/B,GAAD,OACpB,kBAAC,GAAD,CACE1G,IAAK0G,EAAE/H,GACPsB,MAAOyG,EAAEsM,SACT/K,MAAOvB,EAAEuM,QACTL,KAAMlM,EAAEwM,aCnDdsC,GAAS,CACb7c,UAAW8c,GACX7c,KAAM8c,GACN7c,UAAW8c,GACXhC,SAAUiC,GACV9c,OAAQ+c,GACR9c,eAAgB+c,GAChBtQ,OAAQuQ,GACRxC,QAASyC,GACThd,UAAWid,GACXhd,WAAYid,IA4ECwB,GAzEA,WACC9M,eAANjH,EADY,IAGZ/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MACRC,EAASgE,OAAMjE,EAAM,sBAAuB,IAL/B,EAOCvG,GAASuG,EAAMtM,OAAO2M,UAAY,GAA9CxG,EAPW,EAOXA,EAAGC,EAPQ,EAORA,EAAGP,EAPK,EAOLA,EAERgY,EAAU,kBACd,yBAAKjP,UAAU,0BACgD,KAA5D2B,OAAMtF,EAAM,uCAAwC,KACnD,yBACE2D,UAAU,iCACVuO,IAAK5M,OAAMtF,EAAM,uCAAwC,IACzDmS,IAAI,sBAGR,yBAAKxO,UAAU,mCACb,kBAAC,GAAD,CAAQ3D,KAAMA,EAAM2D,UAAU,KAC9B,kBAACyO,GAAD,CAAWpS,KAAMA,KAEnB,yBAAK2D,UAAU,+CACZ2B,OAAMtF,EAAM,kCAAmC,OAKtD,OACE,kBAAC,GAAY6C,SAAb,CAAsBjH,MAAO,CAAEoE,OAAMK,QAASmS,KAC5C,yBACElY,GAAG,OACHqJ,UAAU,UACVK,MAAO,CACLgO,WAAY3Q,EAAME,KAAKC,OACvB6M,MAAOhN,EAAMtM,OAAO2M,QACpBuQ,gBAAiB5Q,EAAMtM,OAAO0M,aAGhC,yBAAKkC,UAAU,qBACb,yBACEA,UAAU,aACVK,MAAO,CACLiO,gBAAgB,QAAD,OAAU/W,EAAV,aAAgBC,EAAhB,aAAsBP,EAAtB,YAGjB,yBAAK+I,UAAU,8BACb,kBAACiP,EAAD,MACA,kBAAC,GAAD,MAECtR,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,SAK5C,yBAAKsB,UAAU,cACb,yBAAKA,UAAU,kBACZrC,EAAO,IACNA,EAAO,GAAG8C,KAAI,SAAC/B,GACb,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,a,UChGzCiQ,G,QAAQxV,EACNuW,MCIAjH,mBANE,SAAC,GAAD,IAAGrJ,EAAH,EAAGA,SAAH,OACf,wBAAIY,UAAU,uFACXZ,MCGCoM,GAAe,SAAC9M,GAAD,OACnB,yBAAK1G,IAAK2J,OAAMjD,EAAE,MAAOkG,gBAAW5E,UAAU,iBAC5C,wBAAIA,UAAU,yBAAyB2B,OAAMjD,EAAG,OAAQ,KACxD,0BAAMsB,UAAU,WAAWtB,EAAE+M,WCa3B+B,GAAS,CACb7c,UAAW8c,GACX7c,KAAM8c,GACN7c,UAAW8c,GACXhC,SAAUiC,GACV9c,OAAQ+c,GACR9c,eAAgB+c,GAChBtQ,OAAQuQ,GACRxC,QAASyC,GACThd,UDNayX,gBAZI,WAAO,IAAD,EACYtD,qBAAW5F,IAAtClD,EADe,EACfA,KAAeqM,EADA,EACThM,QAEd,OAAQL,EAAKrL,WACXqL,EAAKrL,UAAUmL,QAAWwF,OAAMtF,EAAM,oCAAoC,IAAIxF,OAAS,EACvF,6BACE,kBAAC6R,EAAD,KAAUrM,EAAKrL,UAAU0L,SACzB,yBAAKsD,UAAU,cAAc2B,OAAMtF,EAAM,oCAAqC,IAAImJ,QAAO,SAAA9G,GAAC,MAA6B,KAAzBiD,OAAMjD,EAAG,OAAQ,OAAY+B,IAAI+K,MAE/H,QCUJva,WAAYid,IAkHCyB,GA/GA,WACC/M,eAANjH,EADY,IAGZ/F,EADQuP,qBAAWnG,IACnBpJ,MACAyG,EAAgBzG,EAAhByG,KAAMqB,EAAU9H,EAAV8H,MACRC,EAASgE,OAAMjE,EAAM,sBAAuB,IAL/B,EAOCvG,GAASuG,EAAMtM,OAAO4M,SAAW,GAA7CzG,EAPW,EAOXA,EAAGC,EAPQ,EAORA,EAAGP,EAPK,EAOLA,EAGR2Y,EAAS,CACbC,OAAQ,CACNC,SAAU,WACVL,KAAM,EACNM,MAAO,EACPzP,QAAS,OACT0P,cAAe,SACfC,eAAgB,SAChBvF,MAAO,QACP4D,gBAAiB5Q,EAAMtM,OAAO2M,QAC9BpE,OAAQ,QACRuW,YAAa,SAEfC,YAAa,CACX7B,gBAAgB,QAAD,OAAU/W,EAAV,aAAgBC,EAAhB,aAAsBP,EAAtB,WAEjBmZ,aAAc,CACZrV,UAAW,UAITiU,EAAQ,iBACiD,KAA5DrN,OAAMtF,EAAM,uCAAwC,KACnD,yBAAK2D,UAAU,iBACb,yBACEA,UAAU,oCACVuO,IAAK5M,OAAMtF,EAAM,uCAAwC,IACzDmS,IAAI,oBACJnO,MAAO,CACL1G,OAAQ,aAKd,yBAAKqG,UAAU,iBACb,yBAAKK,MAAO,CACR1G,OAAQ,aAMZsV,EAAU,kBACd,yBAAK5O,MAAOuP,EAAOC,QACf,kBAAC,GAAD,CAAQxT,KAAMA,IACd,kBAACoS,GAAD,CAAWpS,KAAMA,IACnB,wBAAI2D,UAAU,oCACX2B,OAAMtF,EAAM,kCAAmC,OAKtD,OACE,kBAAC,GAAY6C,SAAb,CAAsBjH,MAAO,CAAEoE,OAAMK,QAAS2T,KAC5C,yBACE1Z,GAAG,OACHqJ,UAAU,mBACVK,MAAO,CACLgO,WAAY3Q,EAAME,KAAKC,OACvB6M,MAAOhN,EAAMtM,OAAOkf,KACpBhC,gBAAiB5Q,EAAMtM,OAAO0M,aAGhC,yBAAKkC,UAAU,2BACb,yBAAKA,UAAU,kBAAkBK,MAAOuP,EAAOO,aAC7C,kBAACnB,EAAD,MAEA,yBAAKhP,UAAU,yCACb,6BACE,kBAACqQ,GAAD,KAAWhU,EAAK3L,QAAQgM,SACxB,yBAAKsD,UAAU,sCACb,kBAAC,GAAD,QAIH2B,OAAMhE,EAAO,MAAO,KACnBgE,OAAMhE,EAAO,MAAO,IAAI8C,KAAI,SAAC/B,GAC3B,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,SAI5C,yBAAKsB,UAAU,cACb,kBAACiP,EAAD,MAEA,yBAAKjP,UAAU,WAAWK,MAAOuP,EAAOQ,cACtC,yBAAKpQ,UAAU,6BACZ2B,OAAMhE,EAAO,MAAO,KACnBgE,OAAMhE,EAAO,MAAO,IAAI8C,KAAI,SAAC/B,GAC3B,IAAMgE,EAAY8K,GAAO9O,GACzB,OAAOgE,GAAa,kBAACA,EAAD,CAAW1K,IAAK0G,c,UCjIzC,IACb,CACE1G,IAAK,OACLlE,KAAM,OACNyc,UAAWpC,GACXqC,QAASC,IAEX,CACEzY,IAAK,UACLlE,KAAM,UACNyc,UAAW3B,GACX4B,QAASE,IAEX,CACE1Y,IAAK,SACLlE,KAAM,SACNyc,UAAWxB,GACXyB,QAASG,IAEX,CACE3Y,IAAK,WACLlE,KAAM,WACNyc,UAAWlB,GACXmB,QAASI,IAEX,CACE5Y,IAAK,SACLlE,KAAM,SACNyc,UAAWb,GACXc,QAASK,IAEX,CACE7Y,IAAK,SACLlE,KAAM,SACNyc,UCrCWZ,GDsCXa,Q,QCvCiBrX,ICsBN2X,GArBM,SAAC,GAAyB,IAAvBpT,EAAsB,EAAtBA,MAAOwC,EAAe,EAAfA,SAC7B,OACE,yBAAKF,UAAU,0BACZ7O,GAAUsP,KAAI,SAAA/B,GAAC,OACd,yBAAK1G,IAAK0G,EAAE1G,IAAKgI,UAAU,cAAcgB,QAAS,WAAWtC,EAAEqS,QAA+CC,MAAM,qCAA5C9Q,EAAS,eAAgBxB,EAAE1G,OACjG,yBACEgI,UAAS,4EACPtC,EAAMC,OAAOkN,gBAAkBnM,EAAE1G,IAC7B,wCACA,sBAHG,yCAKTuW,IAAK7P,EAAE8R,QACPhC,IAAK9P,EAAE5K,OAET,uBAAGkM,UAAU,4BAA4BtB,EAAE5K,YCX/Cmd,GAAe,CACnB,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAiEaC,GA9DG,SAAC,GAAyB,IAAvBxT,EAAsB,EAAtBA,MAAOwC,EAAe,EAAfA,SAClBvE,EAAMiH,aAAe,gBAArBjH,EAEFwV,EAAuB,SAAAzG,IrERP,SAAA4F,GACtB,IAAMc,EAAWC,SAASC,cAAc,YACxCF,EAAS/Q,MAAMyP,SAAW,QAC1BsB,EAAS/Q,MAAMmP,IAAM,EACrB4B,EAAS/Q,MAAMoP,KAAO,EACtB2B,EAAS/Q,MAAM3G,MAAQ,MACvB0X,EAAS/Q,MAAM1G,OAAS,MACxByX,EAAS/Q,MAAMkR,QAAU,EACzBH,EAAS/Q,MAAMmR,OAAS,OACxBJ,EAAS/Q,MAAMoR,QAAU,OACzBL,EAAS/Q,MAAMqR,UAAY,OAC3BN,EAAS/Q,MAAMvC,WAAa,cAC5BsT,EAASnZ,MAAQqY,EACjBe,SAAS/T,KAAKqU,YAAYP,GAC1BA,EAASQ,QACTR,EAASS,SACT,IAAMC,EAAaT,SAASU,YAAY,QACxCV,SAAS/T,KAAK0U,YAAYZ,GqERxBa,CAAgBvH,GAChBwH,YAAMvW,EAAE,6BAA8B,CAAE+O,UAAU,CAChDyH,cAAe,mCAEjBjS,EAAS,sBAAuBwK,IAGlC,OACE,6BACE,yBAAK1K,UAAU,oEACZrE,EAAE,wBAEL,yBAAKqE,UAAU,6CACZiR,GAAaxQ,KAAI,SAAAiK,GAAK,OACrB,yBACE1S,IAAK0S,EACL1K,UAAU,8EACVK,MAAO,CAAEiO,gBAAiB5D,GAC1B1J,QAAS,kBAAMmQ,EAAqBzG,UAK1C,wBAAI1K,UAAU,SAEd,yBAAKA,UAAU,mCACb,yBACEA,UAAU,qCACVK,MAAO,CAAEiO,gBAAiB5Q,EAAMtM,OAAO2M,WAEzC,yBAAKiC,UAAU,cACb,kBAAC,GAAD,CACEC,MAAOtE,EAAE,uBACTsF,YAAY,UACZhJ,MAAOyF,EAAMtM,OAAO2M,QACpBmC,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,uBAAwBgB,QAKtD,yBAAKlB,UAAU,mCACb,yBACEA,UAAU,qCACVK,MAAO,CAAEiO,gBAAiB5Q,EAAMtM,OAAO4M,UAEzC,yBAAKgC,UAAU,cACb,kBAAC,GAAD,CACEC,MAAOtE,EAAE,sBACTsF,YAAY,UACZhJ,MAAOyF,EAAMtM,OAAO4M,OACpBkC,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,sBAAuBgB,UCjFrDkR,GAAc,CAClB,OACA,aACA,SACA,YACA,UACA,QACA,kBACA,gBACA,UAoCaC,GAjCE,SAAC,GAAyB,IAAvB3U,EAAsB,EAAtBA,MAAOwC,EAAe,EAAfA,SACjBvE,EAAMiH,aAAe,gBAArBjH,EAER,OACE,yBAAKqE,UAAU,0BACZoS,GAAY3R,KAAI,SAAA/B,GAAC,OAChB,yBACE1G,IAAK0G,EACL2B,MAAO,CAAEgO,WAAY3P,GACrBsC,QAAS,kBAAMd,EAAS,oBAAqBxB,IAC7CsB,UAAS,gEACPtC,EAAME,KAAKC,SAAWa,EAAI,kBAAoB,qBADvC,0CAIRA,MAIL,6BACE,kBAAC,GAAD,CACEsB,UAAU,OACVC,MAAOtE,EAAE,0BACTsF,YAAY,cACZhJ,MAAOyF,EAAME,KAAKC,OAClBqC,SAAU,SAAAgB,GAAC,OAAIhB,EAAS,oBAAqBgB,MAG/C,uBAAGlB,UAAU,yBAAyBrE,EAAE,iC,oBC0GjC2W,GAxII,SAAC,GAA+B,IAA7BjW,EAA4B,EAA5BA,KAAMqB,EAAsB,EAAtBA,MAAO7F,EAAe,EAAfA,SAEzBiI,EADYqF,qBAAW5F,IACvBO,mBACAnE,EAAMiH,aAAe,gBAArBjH,EACF4W,EAAeC,iBAAO,MA6B5B,OACE,6BACE,yBAAKxS,UAAU,kCAAkCrE,EAAE,uBAEnD,wBAAIqE,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0BrE,EAAE,iCAE1C,uBAAGqE,UAAU,WAAWrE,EAAE,8BAE1B,2BACE8W,IAAKF,EACLza,KAAK,OACLkI,UAAU,SACVE,SAAU,SAACK,GAAD,OvEoCD,SAAC+B,EAAOzK,GACzB,IAAMvF,EAAK,IAAIogB,WACfpgB,EAAGqgB,iBAAiB,QAAQ,WAC1B,IAAMC,EAAiBzU,KAAKC,MAAM9L,EAAGmF,QACrCI,EAAS,CAAEC,KAAM,cAAeI,QAAS0a,IACzC/a,EAAS,CAAEC,KAAM,iBAEnBxF,EAAGugB,WAAWvQ,EAAM9B,OAAOsS,MAAM,IuE3CRC,CAAWxS,EAAG1I,MAEjC,uBAAGlB,GAAG,iBAAiBqJ,UAAU,WAEjC,yBAAKA,UAAU,mCACb,4BACElI,KAAK,SACLkJ,QAAS,kBAAMuR,EAAa7Z,QAAQsa,SACpChT,UAAU,kFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,WACA,0BAAMA,UAAU,WAAWrE,EAAE,0CAIjC,4BACE7D,KAAK,SACLkJ,QA5Da,WACrB,IAAMiS,EAAY,CAAE5W,OAAMqB,SACtBwV,EAAYvR,aAAYtF,EAAKC,QAC7B6W,EAAmB,sCAAsChV,KAAKE,UAAU6U,GAAW,aACvFvR,OAAMuR,EAAU,UAAU,GAAI,WAAY,sBAC1C,IAEIE,EAAaD,GAFM,sCAAsChV,KAAKE,UAAU6U,EAAU,UAAU,IAAI,cAGhGG,EAAM,IAAIC,GACdD,EAAIE,KAAK,YAAaH,GACtBC,EAAIE,KAAK,cAAepV,KAAKE,UAAU4U,IACvCI,EAAIG,cAAc,CAAC1b,KAAK,SACvBxC,MAAK,SAASme,GACXC,kBAAOD,EAAS,wBAgDZzT,UAAU,kFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,WACA,0BAAMA,UAAU,WAAWrE,EAAE,4CAMrC,wBAAIqE,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0BrE,EAAE,mCAC1C,yBAAKqE,UAAU,WAAWrE,EAAE,gCAE5B,4BACE7D,KAAK,SACLkJ,QAAS,kBAAMlB,GAAmB,IAClCE,UAAU,uFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,QACA,0BAAMA,UAAU,WAAWrE,EAAE,gDAKnC,wBAAIqE,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0BrE,EAAE,iCAE1C,yBAAKqE,UAAU,WAAWrE,EAAE,8BAE5B,4BACE7D,KAAK,SACLkJ,QAjFa,WACnBnJ,EAAS,CAAEC,KAAM,mBACjBD,EAAS,CAAEC,KAAM,eAgFXkI,UAAU,yFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,kBACA,0BAAMA,UAAU,WAAWrE,EAAE,6CAKnC,wBAAIqE,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0BrE,EAAE,0BAE1C,yBAAKqE,UAAU,WAAWrE,EAAE,uBAE5B,4BACE7D,KAAK,SACLkJ,QA/FgB,WACtBnJ,EAAS,CAAEC,KAAM,UACjBD,EAAS,CAAEC,KAAM,eA8FXkI,UAAU,qFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,WACA,0BAAMA,UAAU,WAAWrE,EAAE,qCCtB1BgY,GArHE,WAAO,IACdhY,EAAMiH,aAAe,gBAArBjH,EAER,OACE,6BACE,yBAAKqE,UAAU,0BACb,wBAAIA,UAAU,0BAA0BrE,EAAE,gCAE1C,yBAAKqE,UAAU,WAAWrE,EAAE,6BAE5B,uBACE6E,OAAO,SACPoF,IAAI,sBACJC,KAAK,iCACL7F,UAAU,wHAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,eACA,0BAAMA,UAAU,WAAWrE,EAAE,iDAKnC,wBAAIqE,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0BrE,EAAE,sCAE1C,yBAAKqE,UAAU,WAAWrE,EAAE,mCAE5B,yBAAKqE,UAAU,oBACb,uBACEQ,OAAO,SACPoF,IAAI,sBACJC,KAAK,6DACL7F,UAAU,qFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,cACA,0BAAMA,UAAU,WAAWrE,EAAE,mDAIjC,uBACE6E,OAAO,SACPoF,IAAI,sBACJC,KAAK,0FACL7F,UAAU,uFAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,SACA,0BAAMA,UAAU,WAAWrE,EAAE,oDAMrC,wBAAIqE,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0BrE,EAAE,6BAE1C,yBAAKqE,UAAU,WAAWrE,EAAE,0BAE5B,uBACE6E,OAAO,SACPoF,IAAI,sBACJC,KAAK,kDACL7F,UAAU,0HAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,QACA,0BAAMA,UAAU,WAAWrE,EAAE,2CAKnC,wBAAIqE,UAAU,SAEd,yBAAKA,UAAU,0BACb,wBAAIA,UAAU,0BAA0BrE,EAAE,0BAE1C,yBAAKqE,UAAU,WAAWrE,EAAE,uBAE5B,uBACE6E,OAAO,SACPoF,IAAI,sBACJC,KAAK,sEACL7F,UAAU,wHAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,SACA,0BAAMA,UAAU,WAAWrE,EAAE,wCAKnC,yBAAKqE,UAAU,QACb,uBAAGA,UAAU,qCACX,kBAAC0F,GAAA,EAAD,CAAO/J,EAAGA,EAAGgK,QAAQ,uBAArB,oBAEE,uBACE3F,UAAU,4BACV6F,KAAK,gCACLD,IAAI,sBACJpF,OAAO,UAJT,mBAUJ,uBAAGR,UAAU,qCAAqCrE,EAAE,2BCzE7CiY,GAnCK,SAAC,GAA4B,IAA1BriB,EAAyB,EAAzBA,SAAU2O,EAAe,EAAfA,SACvBvE,EAAMiH,aAAe,gBAArBjH,EAER,OACE,6BACE,kBAAC,GAAD,CACEsE,MAAOtE,EAAE,2BACT1D,MAAO1G,EAAS6J,SAChB8E,SAAU,SAAAxB,GAAC,OAAIwB,EAAS,oBAAqBxB,IAC7CyB,QAASnP,GACToP,WAAY,SAAA1B,GAAC,OACX,4BAAQ1G,IAAK0G,EAAE7K,KAAMoE,MAAOyG,EAAE7K,MAC3B6K,EAAE5K,SAKT,uBAAGkM,UAAU,yBACX,kBAAC0F,GAAA,EAAD,CAAO/J,EAAGA,EAAGgK,QAAQ,8BAArB,uFAEE,uBACE3F,UAAU,gCACVQ,OAAO,SACPoF,IAAI,sBACJC,KAAK,uCAJP,6BAFF,QCiEOgO,GA7EM,WAAO,IAClBlY,EAAMiH,aAAe,gBAArBjH,EAEFgM,EAAUxC,qBAAWnG,IACnBpJ,EAAoB+R,EAApB/R,MAAOiC,EAAa8P,EAAb9P,SACPwE,EAA0BzG,EAA1ByG,KAAMqB,EAAoB9H,EAApB8H,MAAOnM,EAAaqE,EAAbrE,SAEfoP,EAAO,CACX,CACE3I,IAAK,YACLlE,KAAM6H,EAAE,oBAEV,CACE3D,IAAK,SACLlE,KAAM6H,EAAE,iBAEV,CACE3D,IAAK,QACLlE,KAAM6H,EAAE,gBAEV,CACE3D,IAAK,UACLlE,KAAM6H,EAAE,kBAEV,CACE3D,IAAK,WACLlE,KAAM6H,EAAE,mBAEV,CACE3D,IAAK,QACLlE,KAAM6H,EAAE,iBA9Ba,EAiCW+D,mBAASiB,EAAK,GAAG3I,KAjC5B,oBAiClB4I,EAjCkB,KAiCNC,EAjCM,KAmCnBX,EAAW,SAAClI,EAAKC,GACrBJ,EAAS,CACPC,KAAM,WACNI,QAAS,CACPF,MACAC,WAIJJ,EAAS,CAAEC,KAAM,eAsBnB,OACE,yBACEnB,GAAG,eACHqJ,UAAU,6FAEV,kBAAC,GAAD,CAAQW,KAAMA,EAAMC,WAAYA,EAAYC,cAAeA,IAC3D,yBAAKb,UAAU,QAzBA,WACjB,OAAQY,GACN,KAAKD,EAAK,GAAG3I,IACX,OAAO,kBAAC8b,GAAD,CAAcpW,MAAOA,EAAOwC,SAAUA,IAC/C,KAAKS,EAAK,GAAG3I,IACX,OAAO,kBAAC+b,GAAD,CAAWrW,MAAOA,EAAOwC,SAAUA,IAC5C,KAAKS,EAAK,GAAG3I,IACX,OAAO,kBAACgc,GAAD,CAAUtW,MAAOA,EAAOwC,SAAUA,IAC3C,KAAKS,EAAK,GAAG3I,IACX,OAAO,kBAACic,GAAD,CAAY5X,KAAMA,EAAMqB,MAAOA,EAAO7F,SAAUA,IACzD,KAAK8I,EAAK,GAAG3I,IACX,OAAO,kBAACkc,GAAD,CAAa3iB,SAAUA,EAAU2O,SAAUA,IACpD,KAAKS,EAAK,GAAG3I,IACX,OAAO,kBAACmc,GAAD,MACT,QACE,OAAO,MAUc7L,MCvBd8L,GAzDQ,WACrB,IAAMC,EAAclP,qBAAW5F,IACvBjH,EAAmC+b,EAAnC/b,WAAYwH,EAAuBuU,EAAvBvU,mBASpB,OACE,yBACEnJ,GAAG,iBACHqJ,UAAU,0EAEV,yBAAKA,UAAU,wHACb,yBAAKA,UAAU,4CAA4CgB,QAblD,kBAAM1I,EAAWI,QAAQ4b,OAAO,KAcvC,uBAAGtU,UAAU,kBAAb,YAGF,yBAAKA,UAAU,4CAA4CgB,QAhBjD,kBAAM1I,EAAWI,QAAQ6b,QAAQ,KAiBzC,uBAAGvU,UAAU,kBAAb,aAGF,yBAAKA,UAAU,4CAA4CgB,QAnB7C,WAClB1I,EAAWI,QAAQC,WAAW,GAC9BL,EAAWI,QAAQE,MAAM,KAkBnB,uBAAGoH,UAAU,kBAAb,wBAGF,yBAAKA,UAAU,qBAAf,KAEA,yBAAKA,UAAU,4CAA4CgB,QAAS,kBAAMpM,OAAO4f,UAC/E,uBAAGxU,UAAU,kBAAb,UAGF,yBACEA,UAAU,4CACVgB,QAAS,kBAAMlB,GAAmB,KAElC,uBAAGE,UAAU,kBAAb,SAGF,yBAAKA,UAAU,qBAAf,KAEA,uBACEA,UAAU,4CACV6F,KAAK,gCACLrF,OAAO,SACPoF,IAAI,uBAEJ,uBAAG5F,UAAU,kBAAb,oB,8BCmDKyU,GAlGK,WAAO,IACjB9Y,EAAMiH,eAANjH,EACF0Y,EAAclP,qBAAW5F,IACvBlH,EAA+Dgc,EAA/Dhc,QAASC,EAAsD+b,EAAtD/b,WAAYuH,EAA0CwU,EAA1CxU,kBAAmBC,EAAuBuU,EAAvBvU,mBAE1C4U,EAAa,CACjB,CAAE1c,IAAK,gBAAiBC,MAAM,GAAD,OAAK0D,EAAE,+CACpC,CAAE3D,IAAK,UAAWC,MAAM,GAAD,OAAK0D,EAAE,yCAC9B,CAAE3D,IAAK,cAAeC,MAAM,GAAD,OAAK0D,EAAE,8CARZ,EAWM+D,mBAAS,IAXf,oBAWjBnH,EAXiB,KAWRoc,EAXQ,OAYAjV,mBAASgV,EAAW,GAAG1c,KAZvB,oBAYjBF,EAZiB,KAYX8c,EAZW,KAcxB,OACE,yBACE5U,UAAS,mEACPH,EAAoB,mBAAqB,iBAE3CQ,MAAO,CAAEiO,gBAAiB,uBAC1BtN,QAAS,WACPlB,GAAmB,KAGrB,yBACEE,UAAU,iEACVgB,QAAS,SAAAT,GACPA,EAAEsU,kBACFtU,EAAEuU,mBAGJ,wBAAI9U,UAAU,0BAA0BrE,EAAE,wBAE1C,wBAAIqE,UAAU,4BAA4BrE,EAAE,8BAC5C,yBAAKqE,UAAU,qBACb,2BACElI,KAAK,QACLkI,UAAU,8FACV/H,MAAOM,EACP2H,SAAU,SAAAK,GAAC,OAAIoU,EAAWpU,EAAEC,OAAOvI,QACnC8c,IAAI,KACJC,IAAI,MACJC,KAAK,MAGP,wBAAIjV,UAAU,oBAAoBzH,EAAlC,MAGF,wBAAIyH,UAAU,iCAAiCrE,EAAE,gCACjD,kBAAC,GAAD,CACE1D,MAAOH,EACPqI,QAASuU,EACTxU,SAAU0U,EACVxU,WAAY,SAAA1B,GAAC,OACX,4BAAQ1G,IAAK0G,EAAE1G,IAAKC,MAAOyG,EAAE1G,KAC1B0G,EAAEzG,UAKT,uBAAG+H,UAAU,8BAA8BrE,EAAE,2BAC7C,uBAAGqE,UAAU,8BAA8BrE,EAAE,2BAE7C,yBAAKqE,UAAU,wBACb,4BACElI,KAAK,SACLkJ,QAAS,WACPlB,GAAmB,IAErBE,UAAU,mHAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,SACA,0BAAMA,UAAU,WAAWrE,EAAE,iCAIjC,4BACE7D,KAAK,SACLkJ,QAAO,wBAAE,uBAAAhK,EAAA,sEACS,gBAATc,EACHgD,GAAmBzC,EAASC,EAAYC,GACxCH,GAAUC,EAASC,EAAYC,EAAST,GAHrC,OAIPgI,GAAmB,GAJZ,2CAMTE,UAAU,sHAEV,yBAAKA,UAAU,oCACb,uBAAGA,UAAU,2CAAb,QACA,0BAAMA,UAAU,WAAWrE,EAAE,wC,qBClE5BuZ,GAzBU,WAAO,IACtBvZ,EAAMiH,eAANjH,EADqB,EAEmB+D,oBAAS,GAF5B,oBAEtByV,EAFsB,KAEJC,EAFI,KAS7B,OALA7N,qBAAU,WACR1O,YAAW,kBAAMuc,GAAoB,KAAO,KAC5Cvc,YAAW,kBAAMuc,GAAoB,KAAQ,OAC5C,IAGD,yBACEpV,UAAS,yFACPmV,EAAmB,mBAAqB,kBAG1C,yBAAKnV,UAAU,wCACb,2BAAOuO,IAAK8G,KAAWC,UAAQ,EAACC,OAAK,EAACC,MAAI,IAC1C,uBAAGxV,UAAU,2DACVrE,EAAE,iCCkDE8Z,GAzDH,WACV,IAAMpd,EAAUma,iBAAO,MACjBla,EAAaka,iBAAO,MAClBze,EAAS6O,eAAT7O,KAEF4T,EAAUxC,qBAAWnG,IACnBpJ,EAAoB+R,EAApB/R,MAAOiC,EAAa8P,EAAb9P,SACP6F,EAAoB9H,EAApB8H,MAAOnM,EAAaqE,EAAbrE,SAET8iB,EAAclP,qBAAW5F,IACvBI,EAA8B0U,EAA9B1U,WAAYC,EAAkByU,EAAlBzU,cAUpB,OARA2H,qBAAU,WACR5H,EAAWtH,GACXuH,EAActH,GACdvE,EAAK2hB,eAAenkB,EAAS6J,UAC7B,IAAMua,EAAcxX,KAAKC,MAAMO,aAAaiX,QAAQ,UACpD/d,EAAS,CAAEC,KAAM,cAAeI,QAASyd,MACxC,CAAC9d,EAAU8H,EAAYC,EAAe7L,EAAMxC,EAAS6J,WAGtD,kBAAC,WAAD,CAAUya,SAAS,cACjB,yBAAK7V,UAAU,0CACb,kBAAC,GAAD,MAEA,yBAAKA,UAAU,sFACb,kBAAC,WAAD,CACEyS,IAAKna,EACLwd,QAAQ,MACRnd,YAAU,EACVod,oBAAqB,GACrBC,mBAAiB,EACjBC,sBAAuB,GACvBC,wBAAyB,GACzB7V,MAAO,CAAEoR,QAAS,SAElB,yBAAK9a,GAAG,OAAO8b,IAAKpa,EAAS2H,UAAU,0BACpC7O,GAAUglB,MAAK,SAAAzX,GAAC,OAAIhB,EAAMC,OAAOkN,gBAAkBnM,EAAE1G,OAAKuY,cAI/D,kBAAC,GAAD,OAGF,yBAAK5Z,GAAG,YAAYqJ,UAAU,eAC3B7O,GAAUglB,MAAK,SAAAzX,GAAC,OAAIhB,EAAMC,OAAOkN,gBAAkBnM,EAAE1G,OAAKuY,aAG7D,kBAAC,GAAD,MAEA,kBAAC,GAAD,MACA,kBAAC,GAAD,SCrDR2B,IAAMkE,UAAU,CACdC,UAAW,IACXC,aAAa,EACbC,iBAAiB,EACjBzG,SAAUoC,IAAMsE,SAASC,eAG3BC,IAASC,OACP,kBAAC,IAAMC,WAAP,KACE,kBAACzX,GAAD,KACE,kBAACM,GAAD,KACE,kBAAC,GAAD,SAIN4R,SAASwF,eAAe,ShFPnB,SAAkB3hB,GACvB,GAA6C,kBAAmBC,UAAW,CAGzE,GADkB,IAAI2hB,IAAIC,IAAwBniB,OAAOC,SAASgR,MACpDmR,SAAWpiB,OAAOC,SAASmiB,OAIvC,OAGFpiB,OAAO+d,iBAAiB,QAAQ,WAC9B,IAAM1d,EAAK,UAAM8hB,IAAN,sBAEPriB,KAgEV,SAAiCO,EAAOC,GAEtC+hB,MAAMhiB,EAAO,CACXiiB,QAAS,CAAE,iBAAkB,YAE5B5hB,MAAK,SAAA6hB,GAEJ,IAAMC,EAAcD,EAASD,QAAQ3Y,IAAI,gBAEnB,MAApB4Y,EAASE,QACO,MAAfD,IAA8D,IAAvCA,EAAYE,QAAQ,cAG5CniB,UAAUC,cAAcmiB,MAAMjiB,MAAK,SAAAC,GACjCA,EAAaiiB,aAAaliB,MAAK,WAC7BV,OAAOC,SAAS4iB,eAKpBziB,GAAgBC,EAAOC,MAG1BgB,OAAM,WACLJ,QAAQC,IAAI,oEAtFV2hB,CAAwBziB,EAAOC,GAI/BC,UAAUC,cAAcmiB,MAAMjiB,MAAK,WACjCQ,QAAQC,IACN,iHAMJf,GAAgBC,EAAOC,OgFlB/BE,M", "file": "static/js/main.f6ad7922.chunk.js", "sourcesContent": ["module.exports = __webpack_public_path__ + \"static/media/preview.a5fc2f27.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.f1f46a82.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.27f7a093.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.115df124.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.93d6587b.png\";", "module.exports = __webpack_public_path__ + \"static/media/preview.3186944c.png\";", "module.exports = __webpack_public_path__ + \"static/media/panzoom.e912bae3.mp4\";", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import app from './app.json';\r\n\r\nexport default app;\r\n", "import af from './af';\r\nimport ar from './ar';\r\nimport as from './as';\r\nimport ca from './ca';\r\nimport cs from './cs';\r\nimport da from './da';\r\nimport de from './de';\r\nimport el from './el';\r\nimport en from './en';\r\nimport es from './es';\r\nimport fi from './fi';\r\nimport fr from './fr';\r\nimport he from './he';\r\nimport hi from './hi';\r\nimport hu from './hu';\r\nimport it from './it';\r\nimport ja from './ja';\r\nimport kn from './kn';\r\nimport ko from './ko';\r\nimport ml from './ml';\r\nimport mr from './mr';\r\nimport nl from './nl';\r\nimport no from './no';\r\nimport pa from './pa';\r\nimport pl from './pl';\r\nimport pt from './pt';\r\nimport ro from './ro';\r\nimport ru from './ru';\r\nimport sv from './sv';\r\nimport ta from './ta';\r\nimport tr from './tr';\r\nimport uk from './uk';\r\nimport vi from './vi';\r\nimport zh from './zh';\r\n\r\nexport default {\r\n  af,\r\n  ar,\r\n  as,\r\n  ca,\r\n  cs,\r\n  da,\r\n  de,\r\n  el,\r\n  en,\r\n  es,\r\n  fi,\r\n  fr,\r\n  he,\r\n  hi,\r\n  hu,\r\n  it,\r\n  ja,\r\n  kn,\r\n  ko,\r\n  ml,\r\n  mr,\r\n  nl,\r\n  no,\r\n  pa,\r\n  pl,\r\n  pt,\r\n  ro,\r\n  ru,\r\n  sv,\r\n  ta,\r\n  tr,\r\n  uk,\r\n  vi,\r\n  zh,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import app from './app';\r\nimport leftSidebar from './leftSidebar';\r\nimport rightSidebar from './rightSidebar';\r\n\r\nexport default {\r\n  app,\r\n  leftSidebar,\r\n  rightSidebar,\r\n};\r\n", "import profile from './profile.json';\r\nimport objective from './objective.json';\r\nimport work from './work.json';\r\nimport education from './education.json';\r\nimport awards from './awards.json';\r\nimport certifications from './certifications.json';\r\nimport languages from './languages.json';\r\nimport references from './references.json';\r\nimport extras from './extras.json';\r\n\r\nexport default {\r\n  profile,\r\n  objective,\r\n  work,\r\n  education,\r\n  awards,\r\n  certifications,\r\n  languages,\r\n  references,\r\n  extras,\r\n};\r\n", "import templates from './templates.json';\r\nimport colors from './colors.json';\r\nimport fonts from './fonts.json';\r\nimport actions from './actions.json';\r\nimport settings from './settings.json';\r\nimport about from './about.json';\r\n\r\nexport default {\r\n  templates,\r\n  colors,\r\n  fonts,\r\n  actions,\r\n  settings,\r\n  about,\r\n};\r\n", "import i18n from 'i18next';\r\nimport backend from 'i18next-http-backend';\r\nimport { initReactI18next } from 'react-i18next';\r\nimport detector from 'i18next-browser-languagedetector';\r\n\r\nimport resources from './locales';\r\n\r\nconst languages = [\r\n  {\r\n    code: 'ar',\r\n    name: 'Arabic (عربى)',\r\n  },\r\n  {\r\n    code: 'zh',\r\n    name: 'Chinese (中文)',\r\n  },\r\n  {\r\n    code: 'da',\r\n    name: 'Danish (Dansk)',\r\n  },\r\n  {\r\n    code: 'nl',\r\n    name: 'Dutch (Nederlands)',\r\n  },\r\n  {\r\n    code: 'en',\r\n    name: 'English (US)',\r\n  },\r\n  {\r\n    code: 'fr',\r\n    name: 'French (Français)',\r\n  },\r\n  {\r\n    code: 'de',\r\n    name: 'German (Deutsche)',\r\n  },\r\n  {\r\n    code: 'he',\r\n    name: 'Hebrew (עברית)',\r\n  },\r\n  {\r\n    code: 'hi',\r\n    name: 'Hindi (हिन्दी)',\r\n  },\r\n  {\r\n    code: 'it',\r\n    name: 'Italian (Italiano)',\r\n  },\r\n  {\r\n    code: 'kn',\r\n    name: 'Kannada (ಕನ್ನಡ)',\r\n  },\r\n  {\r\n    code: 'pl',\r\n    name: 'Polish (Polskie)',\r\n  },\r\n  {\r\n    code: 'pt',\r\n    name: 'Portuguese (Português)',\r\n  },\r\n  {\r\n    code: 'ru',\r\n    name: 'Russian (русский)',\r\n  },\r\n  {\r\n    code: 'es',\r\n    name: 'Spanish (Español)',\r\n  },\r\n  {\r\n    code: 'ta',\r\n    name: 'Tamil (தமிழ்)',\r\n  },\r\n  {\r\n    code: 'vi',\r\n    name: 'Vietnamese (Tiếng Việt)',\r\n  },\r\n];\r\n\r\ni18n\r\n  .use(detector)\r\n  .use(backend)\r\n  .use(initReactI18next)\r\n  .init({\r\n    resources,\r\n    lng: 'en',\r\n    fallbackLng: 'en',\r\n    ns: ['app', 'leftSidebar', 'rightSidebar'],\r\n    defaultNS: 'app',\r\n  });\r\n\r\nexport { languages };\r\n\r\nexport default i18n;\r\n", "/* eslint-disable no-console */\r\n/* eslint-disable no-use-before-define */\r\n// This optional code is used to register a service worker.\r\n// register() is not called by default.\r\n\r\n// This lets the app load faster on subsequent visits in production, and gives\r\n// it offline capabilities. However, it also means that developers (and users)\r\n// will only see deployed updates on subsequent visits to a page, after all the\r\n// existing tabs open on the page have been closed, since previously cached\r\n// resources are updated in the background.\r\n\r\n// To learn more about the benefits of this model and instructions on how to\r\n// opt-in, read https://bit.ly/CRA-PWA\r\n\r\nconst isLocalhost = Boolean(\r\n  window.location.hostname === 'localhost' ||\r\n    // [::1] is the IPv6 localhost address.\r\n    window.location.hostname === '[::1]' ||\r\n    // *********/8 are considered localhost for IPv4.\r\n    window.location.hostname.match(/^127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}$/),\r\n);\r\n\r\nexport function register(config) {\r\n  if (process.env.NODE_ENV === 'production' && 'serviceWorker' in navigator) {\r\n    // The URL constructor is available in all browsers that support SW.\r\n    const publicUrl = new URL(process.env.PUBLIC_URL, window.location.href);\r\n    if (publicUrl.origin !== window.location.origin) {\r\n      // Our service worker won't work if PUBLIC_URL is on a different origin\r\n      // from what our page is served on. This might happen if a CDN is used to\r\n      // serve assets; see https://github.com/facebook/create-react-app/issues/2374\r\n      return;\r\n    }\r\n\r\n    window.addEventListener('load', () => {\r\n      const swUrl = `${process.env.PUBLIC_URL}/service-worker.js`;\r\n\r\n      if (isLocalhost) {\r\n        // This is running on localhost. Let's check if a service worker still exists or not.\r\n        checkValidServiceWorker(swUrl, config);\r\n\r\n        // Add some additional logging to localhost, pointing developers to the\r\n        // service worker/PWA documentation.\r\n        navigator.serviceWorker.ready.then(() => {\r\n          console.log(\r\n            'This web app is being served cache-first by a service ' +\r\n              'worker. To learn more, visit https://bit.ly/CRA-PWA',\r\n          );\r\n        });\r\n      } else {\r\n        // Is not localhost. Just register service worker\r\n        registerValidSW(swUrl, config);\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\nfunction registerValidSW(swUrl, config) {\r\n  navigator.serviceWorker\r\n    .register(swUrl)\r\n    .then(registration => {\r\n      registration.onupdatefound = () => {\r\n        const installingWorker = registration.installing;\r\n        if (installingWorker == null) {\r\n          return;\r\n        }\r\n        installingWorker.onstatechange = () => {\r\n          if (installingWorker.state === 'installed') {\r\n            if (navigator.serviceWorker.controller) {\r\n              // At this point, the updated precached content has been fetched,\r\n              // but the previous service worker will still serve the older\r\n              // content until all client tabs are closed.\r\n              console.log(\r\n                'New content is available and will be used when all ' +\r\n                  'tabs for this page are closed. See https://bit.ly/CRA-PWA.',\r\n              );\r\n\r\n              // Execute callback\r\n              if (config && config.onUpdate) {\r\n                config.onUpdate(registration);\r\n              }\r\n            } else {\r\n              // At this point, everything has been precached.\r\n              // It's the perfect time to display a\r\n              // \"Content is cached for offline use.\" message.\r\n              console.log('Content is cached for offline use.');\r\n\r\n              // Execute callback\r\n              if (config && config.onSuccess) {\r\n                config.onSuccess(registration);\r\n              }\r\n            }\r\n          }\r\n        };\r\n      };\r\n    })\r\n    .catch(error => {\r\n      console.error('Error during service worker registration:', error);\r\n    });\r\n}\r\n\r\nfunction checkValidServiceWorker(swUrl, config) {\r\n  // Check if the service worker can be found. If it can't reload the page.\r\n  fetch(swUrl, {\r\n    headers: { 'Service-Worker': 'script' },\r\n  })\r\n    .then(response => {\r\n      // Ensure service worker exists, and that we really are getting a JS file.\r\n      const contentType = response.headers.get('content-type');\r\n      if (\r\n        response.status === 404 ||\r\n        (contentType != null && contentType.indexOf('javascript') === -1)\r\n      ) {\r\n        // No service worker found. Probably a different app. Reload the page.\r\n        navigator.serviceWorker.ready.then(registration => {\r\n          registration.unregister().then(() => {\r\n            window.location.reload();\r\n          });\r\n        });\r\n      } else {\r\n        // Service worker found. Proceed as normal.\r\n        registerValidSW(swUrl, config);\r\n      }\r\n    })\r\n    .catch(() => {\r\n      console.log('No internet connection found. App is running in offline mode.');\r\n    });\r\n}\r\n\r\nexport function unregister() {\r\n  if ('serviceWorker' in navigator) {\r\n    navigator.serviceWorker.ready\r\n      .then(registration => {\r\n        registration.unregister();\r\n      })\r\n      .catch(error => {\r\n        console.error(error.message);\r\n      });\r\n  }\r\n}\r\n", "/* eslint-disable new-cap */\r\nimport { get, isEmpty } from 'lodash';\r\nimport html2canvas from 'html2canvas';\r\nimport * as jsPDF from 'jspdf';\r\nimport dayjs from 'dayjs';\r\n\r\nconst move = (array, element, delta) => {\r\n  const index = array.findIndex(item => item.id === element.id);\r\n  const newIndex = index + delta;\r\n  if (newIndex < 0 || newIndex === array.length) return;\r\n  const indexes = [index, newIndex].sort((a, b) => a - b);\r\n  array.splice(indexes[0], 2, array[indexes[1]], array[indexes[0]]);\r\n};\r\n\r\nconst hexToRgb = hex => {\r\n  const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\r\n  hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);\r\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex);\r\n  return result\r\n    ? {\r\n        r: parseInt(result[1], 16),\r\n        g: parseInt(result[2], 16),\r\n        b: parseInt(result[3], 16),\r\n      }\r\n    : null;\r\n};\r\n\r\nconst copyToClipboard = text => {\r\n  const textArea = document.createElement('textarea');\r\n  textArea.style.position = 'fixed';\r\n  textArea.style.top = 0;\r\n  textArea.style.left = 0;\r\n  textArea.style.width = '2em';\r\n  textArea.style.height = '2em';\r\n  textArea.style.padding = 0;\r\n  textArea.style.border = 'none';\r\n  textArea.style.outline = 'none';\r\n  textArea.style.boxShadow = 'none';\r\n  textArea.style.background = 'transparent';\r\n  textArea.value = text;\r\n  document.body.appendChild(textArea);\r\n  textArea.focus();\r\n  textArea.select();\r\n  const successful = document.execCommand('copy');\r\n  document.body.removeChild(textArea);\r\n  return successful;\r\n};\r\n\r\nconst saveData = dispatch => dispatch({ type: 'save_data' });\r\n\r\nconst addItem = (dispatch, key, value) => {\r\n  dispatch({\r\n    type: 'add_item',\r\n    payload: {\r\n      key,\r\n      value,\r\n    },\r\n  });\r\n\r\n  saveData(dispatch);\r\n};\r\n\r\nconst deleteItem = (dispatch, key, value) => {\r\n  dispatch({\r\n    type: 'delete_item',\r\n    payload: {\r\n      key,\r\n      value,\r\n    },\r\n  });\r\n\r\n  saveData(dispatch);\r\n};\r\n\r\nconst moveItemUp = (dispatch, key, value) => {\r\n  dispatch({\r\n    type: 'move_item_up',\r\n    payload: {\r\n      key,\r\n      value,\r\n    },\r\n  });\r\n\r\n  saveData(dispatch);\r\n};\r\n\r\nconst moveItemDown = (dispatch, key, value) => {\r\n  dispatch({\r\n    type: 'move_item_down',\r\n    payload: {\r\n      key,\r\n      value,\r\n    },\r\n  });\r\n\r\n  saveData(dispatch);\r\n};\r\n\r\nconst importJson = (event, dispatch) => {\r\n  const fr = new FileReader();\r\n  fr.addEventListener('load', () => {\r\n    const importedObject = JSON.parse(fr.result);\r\n    dispatch({ type: 'import_data', payload: importedObject });\r\n    dispatch({ type: 'save_data' });\r\n  });\r\n  fr.readAsText(event.target.files[0]);\r\n};\r\n\r\nlet saveAsPdfTimer = null;\r\nconst saveAsPdf = (pageRef, panZoomRef, quality, type) => {\r\n  if(saveAsPdfTimer){\r\n      return;\r\n  }\r\n  return new Promise(resolve => {\r\n    panZoomRef.current.autoCenter(1);\r\n    panZoomRef.current.reset();\r\n\r\n    saveAsPdfTimer = setTimeout(() => {\r\n      html2canvas(pageRef.current, {\r\n        scale: 5,\r\n        useCORS: true,\r\n        allowTaint: true,\r\n      }).then(canvas => {\r\n        const image = canvas.toDataURL('image/jpeg', quality / 100);\r\n        const doc = new jsPDF({\r\n          orientation: 'portrait',\r\n          unit: 'px',\r\n          format: type === 'unconstrained' ? [canvas.width, canvas.height] : 'a4',\r\n        });\r\n\r\n        const pageWidth = doc.internal.pageSize.getWidth();\r\n        const pageHeight = doc.internal.pageSize.getHeight();\r\n\r\n        const widthRatio = pageWidth / canvas.width;\r\n        const heightRatio = pageHeight / canvas.height;\r\n        const ratio = widthRatio > heightRatio ? heightRatio : widthRatio;\r\n\r\n        const canvasWidth = canvas.width * ratio;\r\n        const canvasHeight = canvas.height * ratio;\r\n\r\n        let marginX = 0;\r\n        let marginY = 0;\r\n\r\n        if (type !== 'unconstrained') {\r\n          marginX = (pageWidth - canvasWidth) / 2;\r\n          marginY = (pageHeight - canvasHeight) / 2;\r\n        }\r\n\r\n        doc.addImage(image, 'JPEG', marginX, marginY, canvasWidth, canvasHeight, null, 'SLOW');\r\n        doc.save(`RxResume_${Date.now()}.pdf`);\r\n        saveAsPdfTimer = null;\r\n        resolve();\r\n      });\r\n    }, 250);\r\n  });\r\n}\r\n  \r\nlet saveAsMultiPagePdfTimer = null;\r\nconst saveAsMultiPagePdf = (pageRef, panZoomRef, quality) => {\r\n  if(saveAsMultiPagePdfTimer){\r\n      return;\r\n  }\r\n  return new Promise(resolve => {\r\n    panZoomRef.current.autoCenter(1);\r\n    panZoomRef.current.reset();\r\n\r\n    saveAsMultiPagePdfTimer = setTimeout(() => {\r\n      html2canvas(pageRef.current, {\r\n        scale: 5,\r\n        useCORS: true,\r\n        allowTaint: true,\r\n      }).then(canvas => {\r\n        const image = canvas.toDataURL('image/jpeg', quality / 100);\r\n        const doc = new jsPDF({\r\n          orientation: 'portrait',\r\n          unit: 'px',\r\n          format: 'a4',\r\n        });\r\n\r\n        const pageHeight = doc.internal.pageSize.getHeight();\r\n        const canvasWidth = doc.internal.pageSize.getWidth();\r\n        const canvasHeight = (canvas.height * canvasWidth) / canvas.width;\r\n        let marginTop = 0;\r\n        let heightLeft = canvasHeight;\r\n\r\n        doc.addImage(image, 'JPEG', 0, marginTop, canvasWidth, canvasHeight);\r\n        heightLeft -= pageHeight;\r\n\r\n        while (heightLeft >= 0) {\r\n          marginTop = heightLeft - canvasHeight;\r\n          doc.addPage();\r\n          doc.addImage(image, 'JPEG', 0, marginTop, canvasWidth, canvasHeight);\r\n          heightLeft -= pageHeight;\r\n        }\r\n\r\n        doc.save(`RxResume_${Date.now()}.pdf`);\r\n        saveAsMultiPagePdfTimer = null;\r\n        resolve();\r\n      });\r\n    }, 250);\r\n  });\r\n}\r\n\r\nconst formatDate = ({ date, language = 'en', includeDay = false }) => {\r\n  const template = includeDay ? 'DD MMMM YYYY' : 'MMMM YYYY';\r\n\r\n  return dayjs(date).locale(language.substr(0, 2)).format(template);\r\n};\r\n\r\nconst formatDateRange = ({ startDate, endDate, language = 'en' }, t) => {\r\n  const start = `${dayjs(startDate)\r\n    .locale(language.substr(0, 2))\r\n    .format('MMMM YYYY')}`;\r\n\r\n  const end = dayjs(endDate).isValid()\r\n    ? `${dayjs(endDate).locale(language.substr(0, 2)).format('MMMM YYYY')}`\r\n    : t('shared.forms.present');\r\n\r\n  return `${start} - ${end}`;\r\n};\r\n\r\nconst hasAddress = (address) =>\r\n  !!address.line1 || !!address.line2 || !!address.city || !!address.pincode;\r\n  \r\nconst safetyCheck = (section, path = 'items') =>\r\n  !!(section && section.enable === true);\r\n\r\nexport {\r\n  move,\r\n  hexToRgb,\r\n  copyToClipboard,\r\n  saveData,\r\n  addItem,\r\n  deleteItem,\r\n  moveItemUp,\r\n  moveItemDown,\r\n  importJson,\r\n  saveAsPdf,\r\n  saveAsMultiPagePdf,\r\n  formatDate,\r\n  formatDateRange,\r\n  hasAddress,\r\n  safetyCheck\r\n};\r\n", "import React, { createContext, useReducer } from 'react';\r\nimport get from 'lodash/get';\r\nimport set from 'lodash/set';\r\nimport remove from 'lodash/remove';\r\n\r\nimport demoJsonldData from '../assets/demo/jsonlddata.json';\r\nimport { move } from '../utils';\r\n\r\nconst initialState = {\r\n  data: {\r\n    jsonld:{\r\n      \"@context\": [\r\n\t\t\"https://jsonldresume.github.io/skill/context.json\",\r\n\t\t{\r\n\t\t\t\"gender\": {\r\n\t\t\t\t\"@id\": \"schema:gender\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t},\r\n\t\t\t\"skill:classOfAward\": {\r\n\t\t\t\t\"@id\": \"skill:classOfAward\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t},\r\n\t\t\t\"skill:securityClearance\": {\r\n\t\t\t\t\"@id\": \"skill:securityClearance\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t},\r\n\t\t\t\"category\": {\r\n\t\t\t\t\"@id\": \"schema:category\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t},\r\n\t\t\t\"dayOfWeek\": {\r\n\t\t\t\t\"@id\": \"schema:dayOfWeek\",\r\n\t\t\t\t\"@type\": \"@vocab\"\r\n\t\t\t}\r\n\t\t}\r\n      ],\r\n      '@graph': [\r\n        {\r\n          \"@type\": \"skill:Resume\",\r\n        },\r\n        {\r\n          \"@type\": \"Person\",\r\n          givenName:[{'@language': 'en', '@value':''}],\r\n          familyName: [{'@language': 'en', '@value':''}],\r\n          address: []\r\n        }\r\n      ]\r\n    },\r\n    profile: {\r\n      heading: 'Profile',\r\n      photo: '',\r\n      firstName: '',\r\n      lastName: '',\r\n      subtitle: '',\r\n      address: {\r\n        line1: '',\r\n        line2: '',\r\n        line3: '',\r\n      },\r\n      phone: '',\r\n      website: '',\r\n      email: '',\r\n    },\r\n    contacts: {\r\n      \"enable\": true,\r\n      heading: \"Contacts\"\r\n    },\r\n    address: {\r\n      \"enable\": true,\r\n      heading: 'Address'\r\n    },\r\n    objective: {\r\n      enable: true,\r\n      heading: 'Objective',\r\n      body: '',\r\n    },\r\n    work: {\r\n      enable: true,\r\n      heading: 'Work Experience',\r\n      items: [],\r\n    },\r\n    education: {\r\n      enable: true,\r\n      heading: 'Education',\r\n      items: [],\r\n    },\r\n    awards: {\r\n      enable: true,\r\n      heading: 'Honors & Awards',\r\n      items: [],\r\n    },\r\n    certifications: {\r\n      enable: true,\r\n      heading: 'Certifications',\r\n      items: [],\r\n    },\r\n    skills: {\r\n      enable: true,\r\n      heading: 'Skills',\r\n      items: [],\r\n    },\r\n    memberships: {\r\n      enable: true,\r\n      heading: 'Memberships',\r\n      items: [],\r\n    },\r\n    languages: {\r\n      enable: true,\r\n      heading: 'Languages',\r\n      items: [],\r\n    },\r\n    references: {\r\n      enable: true,\r\n      heading: 'References',\r\n      items: [],\r\n    },\r\n    extras: {\r\n      enable: true,\r\n      heading: 'Personal Information',\r\n      items: [],\r\n    },\r\n  },\r\n  theme: {\r\n    layout: 'Onyx',\r\n    font: {\r\n      family: '',\r\n    },\r\n    colors: {\r\n      background: '#ffffff',\r\n      primary: '#212121',\r\n      accent: '#f44336',\r\n    },\r\n\t\t\"layoutblocks\": {\r\n\t\t\t\"onyx\": [\r\n\t\t\t\t[\r\n\t\t\t\t\t\"objective\",\r\n\t\t\t\t\t\"work\",\r\n\t\t\t\t\t\"education\",\r\n\t\t\t\t\t\"projects\"\r\n\t\t\t\t],\r\n\t\t\t\t[\r\n\t\t\t\t\t\"hobbies\",\r\n\t\t\t\t\t\"languages\",\r\n\t\t\t\t\t\"awards\",\r\n\t\t\t\t\t\"certifications\"\r\n\t\t\t\t],\r\n\t\t\t\t[\r\n\t\t\t\t\t\"skills\",\r\n\t\t\t\t\t\"references\"\r\n\t\t\t\t]\r\n\t\t\t],\r\n\t\t\t\"pikachu\": [\r\n\t\t\t\t[\r\n\t\t\t\t\t\"skills\",\r\n\t\t\t\t\t\"languages\",\r\n\t\t\t\t\t\"hobbies\",\r\n\t\t\t\t\t\"awards\",\r\n\t\t\t\t\t\"certifications\"\r\n\t\t\t\t],\r\n\t\t\t\t[\r\n\t\t\t\t\t\"work\",\r\n\t\t\t\t\t\"education\",\r\n\t\t\t\t\t\"projects\",\r\n\t\t\t\t\t\"references\"\r\n\t\t\t\t]\r\n\t\t\t],\r\n\t\t\t\"gengar\": [\r\n\t\t\t\t[\r\n\t\t\t\t\t\"objective\",\r\n\t\t\t\t\t\"skills\"\r\n\t\t\t\t],\r\n\t\t\t\t[\r\n\t\t\t\t\t\"awards\",\r\n\t\t\t\t\t\"certifications\",\r\n\t\t\t\t\t\"languages\",\r\n\t\t\t\t\t\"references\",\r\n\t\t\t\t\t\"hobbies\"\r\n\t\t\t\t],\r\n\t\t\t\t[\r\n\t\t\t\t\t\"work\",\r\n\t\t\t\t\t\"education\",\r\n\t\t\t\t\t\"projects\"\r\n\t\t\t\t]\r\n\t\t\t],\r\n\t\t\t\"castform\": [\r\n\t\t\t\t[\r\n\t\t\t\t\t\"awards\",\r\n\t\t\t\t\t\"certifications\",\r\n\t\t\t\t\t\"languages\",\r\n\t\t\t\t\t\"hobbies\"\r\n\t\t\t\t],\r\n\t\t\t\t[\r\n\t\t\t\t\t\"objective\",\r\n\t\t\t\t\t\"work\",\r\n\t\t\t\t\t\"education\",\r\n\t\t\t\t\t\"skills\",\r\n\t\t\t\t\t\"projects\",\r\n\t\t\t\t\t\"references\"\r\n\t\t\t\t]\r\n\t\t\t],\r\n\t\t\t\"glalie\": [\r\n\t\t\t\t[\r\n\t\t\t\t\t\"awards\",\r\n\t\t\t\t\t\"certifications\",\r\n\t\t\t\t\t\"hobbies\"\r\n\t\t\t\t],\r\n\t\t\t\t[\r\n\t\t\t\t\t\"objective\",\r\n\t\t\t\t\t\"work\",\r\n\t\t\t\t\t\"education\",\r\n\t\t\t\t\t\"skills\",\r\n\t\t\t\t\t\"projects\",\r\n\t\t\t\t\t\"languages\",\r\n\t\t\t\t\t\"references\"\r\n\t\t\t\t]\r\n\t\t\t],\r\n\t\t\t\"celebi\": [\r\n\t\t\t\t[\r\n\t\t\t\t\t\"awards\",\r\n\t\t\t\t\t\"certifications\",\r\n\t\t\t\t\t\"languages\",\r\n\t\t\t\t\t\"hobbies\"\r\n\t\t\t\t],\r\n\t\t\t\t[\r\n\t\t\t\t\t\"objective\",\r\n\t\t\t\t\t\"work\",\r\n\t\t\t\t\t\"education\",\r\n\t\t\t\t\t\"skills\",\r\n\t\t\t\t\t\"projects\",\r\n\t\t\t\t\t\"references\"\r\n\t\t\t\t]\r\n\t\t\t]\r\n\t\t}\r\n  },\r\n  settings: {\r\n    language: 'en',\r\n  },\r\n};\r\n\r\nconst reducer = (state, { type, payload }) => {\r\n  let items;\r\n  const newState = JSON.parse(JSON.stringify(state));\r\n\r\n  switch (type) {\r\n    case 'migrate_section':\r\n      return set({ ...newState }, `data.${payload.key}`, payload.value);\r\n    case 'add_item':\r\n      items = get({ ...newState }, `${payload.key}`, []);\r\n      items.push(payload.value);\r\n      return set({ ...newState }, `${payload.key}`, items);\r\n    case 'delete_item':\r\n      items = get({ ...newState }, `${payload.key}`, []);\r\n      remove(items, x => x.id === payload.value.id);\r\n      return set({ ...newState }, `${payload.key}`, items);\r\n    case 'move_item_up':\r\n      items = get({ ...newState }, `${payload.key}`, []);\r\n      move(items, payload.value, -1);\r\n      return set({ ...newState }, `${payload.key}`, items);\r\n    case 'move_item_down':\r\n      items = get({ ...newState }, `${payload.key}`, []);\r\n      move(items, payload.value, 1);\r\n      return set({ ...newState }, `${payload.key}`, items);\r\n    case 'on_input':\r\n      return set({ ...newState }, payload.key, payload.value);\r\n    case 'save_data':\r\n      localStorage.setItem('state', JSON.stringify(newState));\r\n      return newState;\r\n    case 'import_data':\r\n      if (payload === null) return initialState;\r\n\r\n      for (const section of Object.keys(initialState.data)) {\r\n        if (!(section in payload.data)) {\r\n          payload.data[section] = initialState.data[section];\r\n        }\r\n      }\r\n\r\n      return {\r\n        ...newState,\r\n        ...payload,\r\n      };\r\n    case 'load_demo_data':\r\n      return {\r\n        ...newState,\r\n        ...demoJsonldData,\r\n      };\r\n    case 'reset':\r\n      return initialState;\r\n    default:\r\n      return newState;\r\n  }\r\n};\r\n\r\nconst AppContext = createContext(initialState);\r\nconst { Provider } = AppContext;\r\n\r\nconst StateProvider = ({ children }) => {\r\n  const [state, dispatch] = useReducer(reducer, initialState);\r\n  return <Provider value={{ state, dispatch }}>{children}</Provider>;\r\n};\r\n\r\nexport const AppProvider = StateProvider;\r\nexport const AppConsumer = AppContext.Consumer;\r\n\r\nexport default AppContext;\r\n", "import React, { useState } from 'react';\r\n\r\nconst PageContext = React.createContext(null);\r\nconst { Provider } = PageContext;\r\n\r\nconst StateProvider = ({ children }) => {\r\n  const [pageRef, setPageRef] = useState(null);\r\n  const [panZoomRef, setPanZoomRef] = useState(null);\r\n  const [isPrintDialogOpen, setPrintDialogOpen] = useState(false);\r\n\r\n  return (\r\n    <Provider\r\n      value={{\r\n        pageRef,\r\n        setPageRef,\r\n        panZoomRef,\r\n        setPanZoomRef,\r\n        isPrintDialogOpen,\r\n        setPrintDialogOpen,\r\n      }}\r\n    >\r\n      {children}\r\n    </Provider>\r\n  );\r\n};\r\n\r\nexport const PageProvider = StateProvider;\r\nexport const PageConsumer = PageContext.Consumer;\r\n\r\nexport default PageContext;\r\n", "import React from 'react';\r\n\r\nconst Dropdown = ({ className, label, value, onChange, options, optionItem }) => (\r\n  <div className={\"flex flex-col mb-2 \"+ className} style={{display:'contents'}}>\r\n    {label && (\r\n      <label className=\"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2\">\r\n        {label}\r\n      </label>\r\n    )}\r\n    <div className=\"inline-flex relative w-full bg-gray-200 text-gray-800 rounded py-3 px-4 leading-tight focus:outline-none\">\r\n      <select\r\n        className=\"block appearance-none w-full bg-gray-200 text-gray-800 focus:outline-none\"\r\n        value={value}\r\n        onChange={e => onChange(e.target.value)}\r\n      >\r\n        {options.map(optionItem)}\r\n      </select>\r\n      <div className=\"pointer-events-none absolute inset-y-0 right-0 flex justify-center items-center px-2 bg-gray-200\">\r\n        <i className=\"material-icons\">expand_more</i>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport default Dropdown;\r\n", "import React from 'react';\r\nimport Dropdown from './Dropdown';\r\n\r\nconst TabBar = ({ tabs, currentTab, setCurrentTab }) => {\r\n\r\n  const changeBy = (x) => {\r\n    const index = tabs.findIndex((tab) => tab.key === currentTab);\r\n\r\n    if (x < 0 && index > 0) {\r\n      setCurrentTab(tabs[index - 1].key);\r\n    }\r\n\r\n    if (x > 0 && index < tabs.length - 1) {\r\n      setCurrentTab(tabs[index + 1].key);\r\n    }\r\n  };\r\n  const TabOption = (tab, index) => {\r\n    return (\r\n      <option key={tab.key} value={tab.key}>\r\n        {tab.name || 'Tab'}\r\n      </option>\r\n    );\r\n  };\r\n  return (\r\n    <div className=\"mx-4 mb-6 flex items-center\">\r\n      <div\r\n        className=\"flex mr-1 cursor-pointer select-none text-gray-600 hover:text-gray-800\"\r\n        onClick={() => changeBy(-1)}\r\n      >\r\n        <i className=\"material-icons\">chevron_left</i>\r\n      </div>\r\n      \r\n        <Dropdown\r\n          className=\"mb-6\"\r\n          label=''\r\n          placeholder=\"\"\r\n          value={currentTab}\r\n          onChange={v => {setCurrentTab(v);}}\r\n          options = {tabs}\r\n          optionItem = {TabOption}\r\n        />\r\n\r\n      <div\r\n        className=\"flex ml-1 cursor-pointer select-none text-gray-600 hover:text-gray-800\"\r\n        onClick={() => changeBy(1)}\r\n      >\r\n        <i className=\"material-icons\">chevron_right</i>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TabBar;\r\n", "import React from 'react';\r\nimport * as _  from 'lodash';\r\n\r\nconst availableLanguages = ['en', 'fr', 'it', 'de', 'ar'];\r\n\r\nconst MakeSelectOptions = function() {\r\n  let options = [];\r\n  availableLanguages.forEach((c) => {\r\n    options.push(<option key={c} value={c}>{c}</option>);\r\n  });\r\n  return options;\r\n};\r\n\r\nexport default class TextField extends React.Component {\r\n    state = {\r\n      editingLanguage: 'en'\r\n\t}\r\n\r\n    handleMultiTextChange = (value, index) => {\r\n      let allValues = this.props.value;\r\n      if(!this.props.value || !Array.isArray(this.props.value)){\r\n          allValues = [];\r\n      }\r\n      \r\n      while(_.size(allValues)<=index){\r\n        allValues.push(\"\");\r\n      }\r\n      allValues[index] = value;\r\n      \r\n      this.props.onChange(allValues);\r\n    }\r\n    \r\n    initAllValues = (lang) => {\r\n      let allValues = this.props.value;\r\n      if(!this.props.value || !Array.isArray(this.props.value)){\r\n        allValues = [\r\n          {\r\n            \"@language\": lang,\r\n            \"@value\": \"\"\r\n          }\r\n        ];\r\n      }\r\n      \r\n      let currrentValueIndex = allValues.findIndex(x => x[\"@language\"] === lang);\r\n      if(currrentValueIndex < 0){\r\n        let newLang = {\r\n          \"@language\": lang,\r\n          \"@value\": \"\"\r\n        };\r\n        allValues.push(newLang);\r\n        this.props.onChange(allValues);\r\n      }\r\n      currrentValueIndex = allValues.findIndex(x => x[\"@language\"] === lang);\r\n      return currrentValueIndex;\r\n    }\r\n    handleLanguageChange = (lang) => {\r\n      this.initAllValues(lang);\r\n      \r\n      this.setState({\r\n        editingLanguage: lang\r\n      });\r\n    }\r\n    handleTextChange = (lang, value) => {\r\n      let currrentValueIndex = this.initAllValues(lang);\r\n      let allValues = this.props.value;\r\n      \r\n      allValues[currrentValueIndex][\"@value\"] = value;\r\n      this.props.onChange(allValues);\r\n    }\r\n    \r\n    MultiItem = (x, index) => (\r\n      <div key={\"holder_\"+index} style={{display: \"flex\"}}>\r\n        <input\r\n          className=\"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500\"\r\n          type={this.props.type}\r\n          disabled={this.props.disabled}\r\n          value={this.props.value[index]}\r\n          onChange={e => this.handleMultiTextChange(e.target.value, index)}\r\n          placeholder={this.props.placeholder}\r\n          key={\"input_\"+index}\r\n        />\r\n        {(_.size(this.props.value)<=1) ? (\"\") : (\r\n        <button\r\n          type=\"button\"\r\n          onClick={()=>{_.pullAt(this.props.value, index);this.props.onChange(this.props.value);}}\r\n          className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded\"\r\n          key={\"button_\"+index}\r\n        >\r\n          <div className=\"flex items-center\" key={\"removeHolder_\"+index}>\r\n            <i className=\"material-icons font-bold text-base\" key={\"remove_\"+index}>remove</i>\r\n          </div>\r\n        </button>)\r\n        }\r\n      </div>\r\n    );\r\n    render() {\r\n      return (\r\n\t\t<div className={`w-full flex flex-col ${this.props.className}`}>\r\n          {this.props.label && (\r\n            <label className=\"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2\">\r\n              {this.props.label}\r\n            </label>\r\n          )}\r\n          { (this.props.type===\"multilang\") ? (\r\n            <div style={{display: \"flex\"}}>\r\n              <select value={this.state.editingLanguage} onChange={(event) => this.handleLanguageChange(event.target.value)}>\r\n                  {MakeSelectOptions()}\r\n              </select>\r\n              <input\r\n                className=\"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500\"\r\n                type={this.props.type}\r\n                disabled={this.props.disabled}\r\n                value={this.props.value[((this.props.value.findIndex(x => x[\"@language\"] === this.state.editingLanguage)>=0)?(this.props.value.findIndex(x => x[\"@language\"] === this.state.editingLanguage)) : 0)][\"@value\"]}\r\n                onChange={e => this.handleTextChange(this.state.editingLanguage, e.target.value)}\r\n                placeholder={this.props.placeholder}\r\n              />\r\n            </div>\r\n          ) : ( \r\n            (this.props.type===\"multitext\") ? (\r\n              <div>\r\n                {this.props.value.map(this.MultiItem)}\r\n                <div key=\"holder_main\" style={{display: \"flex\"}}>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={()=>{this.props.value.push(\"\");this.props.onChange(this.props.value);}}\r\n                    className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded\"\r\n                    key=\"button_main\"\r\n                  >\r\n                    <div className=\"flex items-center\" key=\"addHolder_main\">\r\n                      <i className=\"material-icons font-bold text-base\" key=\"add_main\">add</i>\r\n                    </div>\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <input\r\n                className=\"appearance-none block w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 leading-tight focus:outline-none focus:bg-white focus:border-gray-500\"\r\n                type={this.props.type}\r\n                disabled={this.props.disabled}\r\n                value={this.props.value}\r\n                onChange={e => this.props.onChange(e.target.value)}\r\n                placeholder={this.props.placeholder}\r\n              />\r\n            )\r\n          )\r\n          }\r\n        </div>\r\n      );\r\n\t}\r\n};\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport * as _  from 'lodash';\r\n\r\nimport TextField from '../../../shared/TextField';\r\n\r\nconst ProfileTab = ({ data, onChange }) => {\r\n  const { t } = useTranslation('leftSidebar');\r\n  let personUrl = \"_:\";\r\n  const setValue = (path, field, v, type=null, id=null) => {\r\n    let val = _.get(data, path+\".\"+field, null);\r\n    if(val === null){\r\n      if(typeof(v) === \"string\" || typeof(v) === \"number\"){\r\n        _.set(data, path+\".\"+field, \"\");\r\n      }else if(typeof(v) === \"object\"){\r\n          if(Array.isArray(v)){\r\n            _.set(data, path+\".\"+field, []);\r\n          }else{\r\n            _.set(data, path+\".\"+field, {});\r\n          }\r\n      }\r\n    }\r\n  \r\n    onChange(\"data.\"+path+'.'+field, v);\r\n    if(id){\r\n      onChange(\"data.\"+path+'[\"@id\"]', id);\r\n    }\r\n    if(type){\r\n      onChange(\"data.\"+path+'[\"@type\"]', type);\r\n    }\r\n  };\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        placeholder=\"Heading\"\r\n        value={data.profile.heading}\r\n        onChange={v => onChange('data.profile.heading', v)}\r\n      />\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.photoUrl.label')}\r\n        placeholder=\"https://i.imgur.com/...\"\r\n        value={_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\")}\r\n        onChange={v => {setValue('jsonld[\"@graph\"][1].image', \"contentUrl\", v, \"ImageObject\", personUrl+\"#image\");}}\r\n      />\r\n\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('profile.firstName.label')}\r\n          placeholder=\"Jane\"\r\n          value={_.get(data,\"jsonld['@graph'][1].givenName\", \"\")}\r\n          onChange={v => setValue('jsonld[\"@graph\"][1]', \"givenName\", v)}\r\n          type=\"multilang\"\r\n        />\r\n\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('profile.lastName.label')}\r\n          placeholder=\"Doe\"\r\n          value={_.get(data,\"jsonld['@graph'][1].familyName\", \"\")}\r\n          onChange={v => setValue('jsonld[\"@graph\"][1]', \"familyName\", v)}\r\n          type=\"multilang\"\r\n        />\r\n      </div>\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.subtitle.label')}\r\n        placeholder=\"Full-Stack Web Developer\"\r\n        value={_.get(data, 'jsonld[\"@graph\"][1].description', \"\")}\r\n        onChange={v => {setValue('jsonld[\"@graph\"][1]', \"description\", v);}}\r\n      />\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.website.label')}\r\n        placeholder=\"janedoe.me\"\r\n        value={_.get(data,'jsonld[\"@graph\"][1].sameAs', [])}\r\n        onChange={v => setValue('jsonld[\"@graph\"][1]', \"sameAs\", v)}\r\n        AddItem={()=>{}}\r\n        type=\"multitext\"\r\n      />\r\n\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProfileTab;\r\n", "import React from 'react';\r\n\r\nconst Checkbox = ({ checked, onChange, icon = 'check', size = '2rem' }) => {\r\n  return (\r\n    <div\r\n      className=\"relative bg-white border-2 rounded border-gray-400 hover:border-gray-500 flex flex-shrink-0 justify-center items-center mr-2 focus-within:border-blue-500 cursor-pointer\"\r\n      style={{ width: size, height: size }}\r\n    >\r\n      <input\r\n        type=\"checkbox\"\r\n        style={{ width: size, height: size }}\r\n        className=\"opacity-0 absolute cursor-pointer z-20\"\r\n        checked={checked}\r\n        onChange={e => onChange(e.target.checked)}\r\n      />\r\n      <i\r\n        className={`absolute material-icons ${\r\n          checked ? 'opacity-100' : 'opacity-0'\r\n        } text-sm text-gray-800`}\r\n      >\r\n        {icon}\r\n      </i>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Checkbox;\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport Checkbox from './Checkbox';\r\nimport { deleteItem, moveItemUp, moveItemDown } from '../utils';\r\n\r\nconst ItemActions = ({ dispatch, first, identifier, item, last, onChange, type, enableAction }) => {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <div className=\"flex justify-between\">\r\n      <div className=\"flex items-center\">\r\n      {enableAction ? enableAction(identifier, item, onChange) : (\r\n        <Checkbox\r\n          size=\"2.25rem\"\r\n          checked={item.enable}\r\n          onChange={v => {\r\n            onChange(`${identifier}enable`, v);\r\n          }}\r\n        />\r\n      )}\r\n\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => deleteItem(dispatch, type, item)}\r\n          className=\"ml-2 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">delete</i>\r\n            <span className=\"text-sm\">{t('buttons.delete.label')}</span>\r\n          </div>\r\n        </button>\r\n      </div>\r\n\r\n      <div className=\"flex\">\r\n        {!first && (\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => moveItemUp(dispatch, type, item)}\r\n            className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded mr-2\"\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <i className=\"material-icons font-bold text-base\">arrow_upward</i>\r\n            </div>\r\n          </button>\r\n        )}\r\n\r\n        {!last && (\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => moveItemDown(dispatch, type, item)}\r\n            className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-4 rounded\"\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <i className=\"material-icons font-bold text-base\">arrow_downward</i>\r\n            </div>\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ItemActions;\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst AddItemButton = ({ onSubmit }) => {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <div>\r\n      <button\r\n        type=\"button\"\r\n        onClick={onSubmit}\r\n        className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n      >\r\n        <div className=\"flex items-center\">\r\n          <i className=\"material-icons mr-2 font-bold text-base\">add</i>\r\n          <span className=\"text-sm\">{t('buttons.add.label')}</span>\r\n        </div>\r\n      </button>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AddItemButton;\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nconst ItemHeading = ({ title, heading, isOpen, setOpen }) => {\r\n  const { t } = useTranslation();\r\n\r\n  return (\r\n    <div\r\n      className=\"flex justify-between items-center cursor-pointer\"\r\n      onClick={() => setOpen(!isOpen)}\r\n    >\r\n      <h6 className=\"text-sm font-medium\">\r\n        {typeof heading === 'undefined' ? title : t('item.add', { heading })}\r\n      </h6>\r\n      <i className=\"material-icons\">{isOpen ? 'expand_less' : 'expand_more'}</i>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ItemHeading;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nconst AddressTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox checked={data.address.enable} onChange={v => onChange('data.address.enable', v)} />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.address.heading}\r\n            onChange={v => onChange('data.address.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {data.jsonld[\"@graph\"][1].address && data.jsonld[\"@graph\"][1].address.map((x, index) => (\r\n        <Item\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          index={index}\r\n          item={x}\r\n          key={x[\"@id\"]}\r\n          last={index === data.jsonld[\"@graph\"][1].address.length - 1}\r\n          onChange={onChange}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.address.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.streetAddress.label')}\r\n        placeholder=\"20 Malvin Dr\"\r\n        value={item.streetAddress}\r\n        onChange={v => onChange(`${identifier}streetAddress`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.addressLocality.label')}\r\n        placeholder=\"Toronto\"\r\n        value={item.addressLocality}\r\n        onChange={v => onChange(`${identifier}addressLocality`, v)}\r\n      />\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.addressRegion.label')}\r\n        placeholder=\"ON\"\r\n        value={item.addressRegion}\r\n        onChange={v => onChange(`${identifier}addressRegion`, v)}\r\n      />\r\n\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('address.addressCountry.label')}\r\n          placeholder=\"Canada\"\r\n          value={item.addressCountry}\r\n          onChange={v => onChange(`${identifier}addressCountry`, v)}\r\n        />\r\n\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('address.postalCode.label')}\r\n          placeholder=\"H1H 0H0\"\r\n          value={item.postalCode}\r\n          onChange={v => onChange(`${identifier}postalCode`, v)}\r\n        />\r\n      </div>\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.sameAs.label')}\r\n        placeholder=\"Google Map Url of address\"\r\n        value={item.sameAs}\r\n        onChange={v => onChange(`${identifier}sameAs`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  let id = \"_:\"+uuidv4();\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState({\r\n    \"@id\": id,\r\n    \"@type\": \"PostalAddress\",\r\n    hoursAvailable:{\r\n      \"@id\": id+\"#hoursAvailable\",\r\n      \"@type\": \"OpeningHoursSpecification\",\r\n      \"validThrough\": \"2099-01-01\"\r\n    },\r\n    addressCountry: '',\r\n    streetAddress: '',\r\n    addressRegion: '',\r\n    addressLocality: '',\r\n    postalCode: '',\r\n    contactType: '',\r\n    sameAs: ''\r\n  });\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n  const onSubmit = () => {\r\n    let id = \"_:\"+uuidv4();\r\n    if ( item.addressCountry === '' ) return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].address', item);\r\n\r\n    setItem({\r\n      \"@id\": id,\r\n      \"@type\": \"PostalAddress\",\r\n      hoursAvailable:{\r\n        \"@id\": id+\"#hoursAvailable\",\r\n        \"@type\": \"OpeningHoursSpecification\",\r\n        \"validThrough\": \"2099-01-01\"\r\n      },\r\n      addressCountry: '',\r\n      streetAddress: '',\r\n      addressRegion: '',\r\n      addressLocality: '',\r\n      postalCode: '',\r\n      contactType: '',\r\n      sameAs: ''\r\n    });\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <Checkbox\r\n      size=\"2.25rem\"\r\n      checked={(item && item.hoursAvailable && item.hoursAvailable.validThrough && (Date.parse(item.hoursAvailable.validThrough) - Date.parse(new Date()))>0) }\r\n      onChange={v => {\r\n        let validThrough = \"1900-01-01\";\r\n        if(v){validThrough = \"2099-01-01\";}\r\n        onChange(`${identifier}hoursAvailable.validThrough`, validThrough);\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].address[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item.streetAddress || item.addressCountry} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].address\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AddressTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport Dropdown from '../../../shared/Dropdown';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nconst ContactsTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox checked={data.contacts.enable} onChange={v => onChange('data.contacts.enable', v)} />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.contacts.heading}\r\n            onChange={v => onChange('data.contacts.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {data.jsonld[\"@graph\"][1].contactPoint && data.jsonld[\"@graph\"][1].contactPoint.filter(x=>(x.contactType===\"Preferred\")).map((x, index) => (\r\n        <Item\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          index={index}\r\n          item={x}\r\n          key={x[\"@id\"]}\r\n          last={index === data.jsonld[\"@graph\"][1].contactPoint.length - 1}\r\n          onChange={onChange}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.contacts.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  const ContactTypeOption = (x, index) => {\r\n    return (\r\n      <option key={x} value={x}>\r\n        {x}\r\n      </option>\r\n    );\r\n  };\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.phone.label')}\r\n        placeholder=\"+1 (999)999-9999\"\r\n        value={item.telephone}\r\n        onChange={v => onChange(`${identifier}telephone`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.email.label')}\r\n        placeholder=\"<EMAIL>\"\r\n        value={item.email}\r\n        onChange={v => onChange(`${identifier}email`, v)}\r\n      />\r\n      \r\n      <Dropdown\r\n        className=\"mb-6\"\r\n        label={t('profile.contactType.label')}\r\n        placeholder=\"Only preferred is shown on resume\"\r\n        value={item.contactType}\r\n        onChange={v => onChange(`${identifier}contactType`, v)}\r\n        options = {[\"Preferred\", \"Emergency\", \"Other\"]}\r\n        optionItem = {ContactTypeOption}\r\n      />\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('profile.contacts.description')}\r\n        placeholder=\"Description\"\r\n        value={item.description}\r\n        onChange={v => onChange(`${identifier}description`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  let id = \"_:\"+uuidv4();\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState({\r\n    \"@id\": id,\r\n    \"@type\": \"ContactPoint\",\r\n    description: '',\r\n    contactType: 'Preferred',\r\n    email: '',\r\n    telephone: ''\r\n  });\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n  const onSubmit = () => {\r\n    let id = \"_:\"+uuidv4();\r\n    if ( item.contactType === '' ) return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].contactPoint', item);\r\n\r\n    setItem({\r\n      \"@id\": id,\r\n      \"@type\": \"ContactPoint\",\r\n      description: '',\r\n      contactType: 'Preferred',\r\n      email: '',\r\n      telephone: ''\r\n    });\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].contactPoint[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item.contactType || item.telephone} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].contactPoint\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContactsTab;\r\n", "import React from 'react';\r\nimport { Trans } from 'react-i18next';\r\n\r\nconst MarkdownHelpText = ({ className }) => {\r\n  return (\r\n    <div className={className}>\r\n      <p className=\"text-gray-800 text-xs\">\r\n        <Trans i18nKey=\"markdownHelpText\">\r\n          You can use\r\n          <a\r\n            className=\"text-blue-600 hover:underline\"\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            href=\"https://github.com/adam-p/markdown-here/wiki/Markdown-Cheatsheet\"\r\n          >\r\n            GitHub Flavored Markdown\r\n          </a>\r\n          to style this section of text.\r\n        </Trans>\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MarkdownHelpText;\r\n", "import React from 'react';\r\nimport MarkdownHelpText from './MarkdownHelpText';\r\n\r\nconst TextArea = ({ label, placeholder, value, onChange, className, rows = 5 }) => (\r\n  <div className={`w-full flex flex-col ${className}`}>\r\n    <label className=\"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-2\">\r\n      {label}\r\n    </label>\r\n    <textarea\r\n      className=\"appearance-none block leading-7 w-full bg-gray-200 text-gray-800 border border-gray-200 rounded py-3 px-4 focus:outline-none focus:bg-white focus:border-gray-500\"\r\n      rows={rows}\r\n      value={value}\r\n      onChange={e => onChange(e.target.value)}\r\n      placeholder={placeholder}\r\n    />\r\n\r\n    <MarkdownHelpText className=\"mt-2\" />\r\n  </div>\r\n);\r\n\r\nexport default TextArea;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst ObjectiveTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox checked={data.objective.enable} onChange={v => onChange('data.objective.enable', v)} />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.objective.heading}\r\n            onChange={v => onChange('data.objective.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {data.jsonld[\"@graph\"][1].seeks && data.jsonld[\"@graph\"][1].seeks.map((x, index) => (\r\n        <Item\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          index={index}\r\n          item={x}\r\n          key={x[\"@id\"]}\r\n          last={index === data.jsonld[\"@graph\"][1].seeks.length - 1}\r\n          onChange={onChange}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.objective.heading} dispatch={dispatch} size={_.size(data.jsonld[\"@graph\"][1].seeks)} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Availablity = ({item, onChange}) => {\r\n  if(!item){\r\n    item = {};\r\n  }\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('address.addressLocality.label')}\r\n        placeholder=\"Toronto\"\r\n        value={_.get(item,'addressLocality', '')}\r\n        onChange={v => onChange('addressLocality', v)}\r\n      />\r\n      \r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('address.addressRegion.label')}\r\n          placeholder=\"ON\"\r\n          value={_.get(item,'addressRegion','')}\r\n          onChange={v => onChange('addressRegion', v)}\r\n        />\r\n\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('address.addressCountry.label')}\r\n          placeholder=\"Canada\"\r\n          value={_.get(item,'addressCountry','')}\r\n          onChange={v => onChange('addressCountry', v)}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nconst Form = ({ item, onChange, identifier = '', index=0 }) => {\r\n    \r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  const addAvailability = (value, key, availableAtOrFrom) => {\r\n    if(value && key){\r\n      let address = _.get(availableAtOrFrom,'address', {});\r\n      if(!address['@type']){\r\n        address['@type'] = \"PostalAddress\";\r\n        address['@id'] = item['@id']+\"_availableAtOrFrom_address\";\r\n      }\r\n      address[key] = value;\r\n      \r\n      _.set(availableAtOrFrom, 'address', address);\r\n      if(!availableAtOrFrom['@type']){\r\n        availableAtOrFrom['@type'] = \"Place\";\r\n        availableAtOrFrom['@id'] = item['@id']+\"_availableAtOrFrom\";\r\n      }\r\n      onChange(`${identifier}availableAtOrFrom`, availableAtOrFrom);\r\n    }\r\n  }\r\n  \r\n  return (\r\n    <div>\r\n      {(index===0) ? (<TextArea\r\n        rows=\"15\"\r\n        className=\"mb-4\"\r\n        label={t('objective.objective.label')}\r\n        value={_.get(item,'description', '')}\r\n        placeholder=\"Looking for a challenging role in a reputable organization to utilize my technical, database, and management skills for the growth of the organization as well as to enhance my knowledge about new and emerging trends in the IT sector.\"\r\n        onChange={v => onChange(`${identifier}description`, v)}\r\n      />) : (<></>)}\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('objective.availabilityStarts.label')}\r\n        placeholder=\"2022-01-01\"\r\n        value={_.get(item,'availabilityStarts', '')}\r\n        onChange={v => onChange(`${identifier}availabilityStarts`, v)}\r\n      />\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('objective.availabilityEnds.label')}\r\n        placeholder=\"2022-12-01\"\r\n        item={_.get(item, 'availabilityEnds', '')}\r\n        onChange={v => onChange(`${identifier}availabilityEnds`, v)}\r\n      />\r\n      \r\n      <Availablity\r\n        item={_.get(item, 'availableAtOrFrom.address', {})}\r\n        onChange={(key, value) => addAvailability(value, key, item.availableAtOrFrom)}\r\n      />\r\n      \r\n    </div>\r\n  );\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch, size }) => {\r\n\r\n  let id = \"_:\"+uuidv4();\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState({\r\n    \"@id\": id,\r\n    \"@type\": \"Demand\",\r\n    description: '',\r\n    availabilityStarts: '',\r\n    availabilityEnds: '',\r\n    availableAtOrFrom: {},\r\n    deliveryLeadTime: {}\r\n  });\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n  const onSubmit = () => {\r\n    let id = \"_:\"+uuidv4();\r\n    if ( item.description === '' && item.availableAtOrFrom === [] ) return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].seeks', item);\r\n\r\n    setItem({\r\n      \"@id\": id,\r\n      \"@type\": \"Demand\",\r\n      description: '',\r\n      availabilityStarts: '',\r\n      availabilityEnds: '',\r\n      availableAtOrFrom: {},\r\n      deliveryLeadTime: {}\r\n    });\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} index={size} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n    return (\r\n    <Checkbox\r\n      size=\"2.25rem\"\r\n      checked={(item && item.availabilityEnds && (Date.parse(item.availabilityEnds) - Date.parse(new Date()))>0) }\r\n      onChange={v => {\r\n        let availabilityEnds = \"1900-01-01\";\r\n        if(v){availabilityEnds = \"2099-01-01\";}\r\n        onChange(`${identifier}availabilityEnds`, availabilityEnds);\r\n      }}\r\n    />\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].seeks[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item.availableAtOrFrom.address.addressCountry || (item.description.substring(0, 10)+\"...\")} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} index={index} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].seeks\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ObjectiveTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst WorkTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox checked={data.work.enable} onChange={v => onChange('data.work.enable', v)} />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.work.heading}\r\n            onChange={v => onChange('data.work.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][1], 'hasOccupation', []).map((x, index) => (\r\n        <Item\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          index={index}\r\n          item={x}\r\n          key={_.get(x, '@id', 'item')}\r\n          last={index === _.size(data.jsonld[\"@graph\"][1].hasOccupation) - 1}\r\n          onChange={onChange}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.work.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  const setValue = (path, field, v, type=null, id=null) => {\r\n    let fullPath = path;\r\n    if(field){\r\n      fullPath = fullPath+\".\"+field;\r\n    }\r\n    let val = _.get(item, fullPath, null);\r\n    if(val === null){\r\n      if(typeof(v) === \"string\" || typeof(v) === \"number\"){\r\n        _.set(item, fullPath, \"\");\r\n      }else if(typeof(v) === \"object\"){\r\n          if(Array.isArray(v)){\r\n            _.set(item, fullPath, []);\r\n          }else{\r\n            _.set(item, fullPath, {});\r\n          }\r\n      }\r\n    }\r\n  \r\n    onChange(identifier+fullPath, v);\r\n    if(id){\r\n      onChange(`${identifier}`+path+'[\"@id\"]', id);\r\n    }\r\n    if(type){\r\n      onChange(`${identifier}`+path+'[\"@type\"]', type);\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.name.label')}\r\n        placeholder=\"Amazon\"\r\n        value={_.get(item, 'subjectOf.organizer.name', '')}\r\n        onChange={v => onChange(`${identifier}subjectOf.organizer.name`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.role.label')}\r\n        placeholder=\"Full-Stack Web Developer\"\r\n        value={_.get(item, 'roleName', '')}\r\n        onChange={v => onChange(`${identifier}roleName`, v)}\r\n      />\r\n\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('app:item.startDate.label')}\r\n          placeholder=\"2019-01-01\"\r\n          value={_.get(item, 'startDate', '')}\r\n          onChange={v => onChange(`${identifier}startDate`, v)}\r\n        />\r\n\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('app:item.endDate.label')}\r\n          placeholder=\"2020-01-01\"\r\n          value={_.get(item, 'endDate', '')}\r\n          onChange={v => onChange(`${identifier}endDate`, v)}\r\n        />\r\n      </div>\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.responsibilities.label')}\r\n        placeholder=\"Preparing project plans\"\r\n        value={_.get(item,'hasOccupation.responsibilities', [])}\r\n        onChange={v => setValue('hasOccupation', \"responsibilities\", v)}\r\n        AddItem={()=>{}}\r\n        type=\"multitext\"\r\n      />\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.skills.label')}\r\n        placeholder=\"Project Management\"\r\n        value={_.get(item,'hasOccupation.skills', [])}\r\n        onChange={v => setValue('hasOccupation', \"skills\", v)}\r\n        AddItem={()=>{}}\r\n        type=\"multitext\"\r\n      />\r\n\r\n      <TextArea\r\n        rows=\"5\"\r\n        className=\"mb-6\"\r\n        label={t('app:item.description.label')}\r\n        value={_.get(item, 'description', '')}\r\n        onChange={v => onChange(`${identifier}description`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"EmployeeRole\",\r\n    \"@id\": \"_:\"+id+\"#enable\",\r\n    hasOccupation: {\r\n      \"@id\": \"_:\"+id+\"#hasOccupation\",\r\n      \"@type\": \"Occupation\",\r\n      name: \"\",\r\n      skills: [],\r\n      responsibilities: []\r\n    },\r\n    subjectOf: {\r\n      \"@type\": \"BusinessEvent\",\r\n      id: \"_:\"+id+\"#subjectOf\",\r\n      organizer: {\r\n        \"@type\": \"Organization\",\r\n        id: \"_:\"+id+\"#subjectOf#organizer\",\r\n        name: ''\r\n      }\r\n    },\r\n    roleName: '',\r\n    startDate: '',\r\n    endDate: '',\r\n    description: ''\r\n  });\r\n};\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (item.roleName === '') return;\r\n    addItem(dispatch, \"data.jsonld['@graph'][1].hasOccupation\", item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].hasOccupation[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item.roleName+\" \"+item.subjectOf.organizer.name} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].hasOccupation\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WorkTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport Dropdown from '../../../shared/Dropdown';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst EducationTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n  \r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox\r\n            checked={data.education.enable}\r\n            onChange={v => onChange('data.education.enable', v)}\r\n          />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.education.heading}\r\n            onChange={v => onChange('data.education.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][1], 'hasCredential', []).map((x, index) => (\r\n        <Item\r\n          item={x}\r\n          key={_.get(x, '@id', 'item')}\r\n          index={index}\r\n          onChange={onChange}\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          last={index === _.size(_.get(data.jsonld[\"@graph\"][1], 'hasCredential', [])) - 1}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.education.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  const EducationTypeOption = (x, index) => {\r\n    return (\r\n      <option key={x} value={x}>\r\n        {x}\r\n      </option>\r\n    );\r\n  };\r\n  \r\n  const setValue = (path, field, v, type=null, id=null) => {\r\n    let fullPath = path;\r\n    if(field){\r\n      fullPath = fullPath+\".\"+field;\r\n    }\r\n    let val = _.get(item, fullPath, null);\r\n    if(val === null){\r\n      if(typeof(v) === \"string\" || typeof(v) === \"number\"){\r\n        _.set(item, fullPath, \"\");\r\n      }else if(typeof(v) === \"object\"){\r\n          if(Array.isArray(v)){\r\n            _.set(item, fullPath, []);\r\n          }else{\r\n            _.set(item, fullPath, {});\r\n          }\r\n      }\r\n    }\r\n  \r\n    onChange(identifier+fullPath, v);\r\n    if(id){\r\n      onChange(`${identifier}`+path+'[\"@id\"]', id);\r\n    }\r\n    if(type){\r\n      onChange(`${identifier}`+path+'[\"@type\"]', type);\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('education.name.label')}\r\n        placeholder=\"Harvard University\"\r\n        value={_.get(item, \"about.provider.name\", \"\")}\r\n        onChange={v => onChange(`${identifier}about.provider.name`, v)}\r\n      />\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <Dropdown\r\n          className=\"mb-6\"\r\n          label={t('education.type.label')}\r\n          placeholder=\"Certificate type\"\r\n          value={_.get(item, \"credentialCategory\", \"\")}\r\n          onChange={v => onChange(`${identifier}credentialCategory`, v)}\r\n          options = {[\"Degree\", \"Certificate\", \"Badge\"]}\r\n          optionItem = {EducationTypeOption}\r\n        />\r\n        \r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('education.major.degree')}\r\n          placeholder=\"Masters of Science\"\r\n          value={_.get(item, \"educationalLevel\", \"\")}\r\n          onChange={v => onChange(`${identifier}educationalLevel`, v)}\r\n        />\r\n      </div>\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('education.major.label')}\r\n        placeholder=\"Computer Science\"\r\n        value={_.get(item, \"about.educationalCredentialAwarded\", \"\")}\r\n        onChange={v => onChange(`${identifier}about.educationalCredentialAwarded`, v)}\r\n      />\r\n      \r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('education.grade.label')}\r\n          placeholder=\"3.7\"\r\n          value={_.get(item, \"aggregateRating.ratingValue\", \"\")}\r\n          onChange={v => onChange(`${identifier}aggregateRating.ratingValue`, v)}\r\n        />\r\n        \r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('education.maxGrade.label')}\r\n          placeholder=\"4\"\r\n          value={_.get(item, \"aggregateRating.bestRating\", \"\")}\r\n          onChange={v => onChange(`${identifier}aggregateRating.bestRating`, v)}\r\n        />\r\n      </div>\r\n      \r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('app:item.startDate.label')}\r\n          placeholder=\"2018-01-01\"\r\n          value={_.get(item, \"about.startDate\", \"\")}\r\n          onChange={v => onChange(`${identifier}about.startDate`, v)}\r\n        />\r\n      \r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('app:item.endDate.label')}\r\n          placeholder=\"2020-01-01\"\r\n          value={_.get(item, \"about.endDate\", \"\")}\r\n          onChange={v => onChange(`${identifier}about.endDate`, v)}\r\n        />\r\n      </div>\r\n      \r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('work.skills.label')}\r\n        placeholder=\"Project Management\"\r\n        value={_.get(item,'teaches', [])}\r\n        onChange={v => setValue('teaches', '', v)}\r\n        AddItem={()=>{}}\r\n        type=\"multitext\"\r\n      />\r\n\r\n      <TextArea\r\n        rows=\"5\"\r\n        className=\"mb-6\"\r\n        label={t('app:item.description.label')}\r\n        value={_.get(item, 'abstract', '')}\r\n        onChange={v => onChange(`${identifier}abstract`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"EducationalOccupationalCredential\",\r\n    \"@id\": \"_:\"+id+\"#enable\",\r\n    \"aggregateRating\": {\r\n      \"@id\": \"_:\"+id+\"#aggregateRating\",\r\n      \"@type\": \"aggregateRating\",\r\n      \"bestRating\": \"\",\r\n      \"ratingValue\": \"\",\r\n      \"name\": \"GPA\",\r\n      \"itemReviewed\": {\r\n        \"@id\": \"_:\"+id+\"#enable\"\r\n      }\r\n    },\r\n    \"credentialCategory\": \"degree\",\r\n    \"educationalLevel\": \"\",\r\n\t\"abstract\": \"\",\r\n    \"teaches\": [],\r\n    \"about\": {\r\n      \"@id\": \"_:\"+id+\"#about\",\r\n      \"@type\": \"EducationalOccupationalProgram\",\r\n      \"educationalCredentialAwarded\": \"\",\r\n      \"startDate\": \"\",\r\n      \"endDate\": \"\",\r\n      \"provider\": {\r\n\t\t\"@id\": \"_:\"+id+\"#about#provider\",\r\n\t\t\"@type\": \"CollegeOrUniversity\",\r\n\t\t\"name\": \"\"\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (item.educationalLevel === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].hasCredential', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld['@graph'][1].hasCredential[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, \"educationalLevel\", \"\")} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].hasCredential\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default EducationTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst AwardsTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox\r\n            checked={data.awards.enable}\r\n            onChange={v => onChange('data.awards.enable', v)}\r\n          />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.awards.heading}\r\n            onChange={v => onChange('data.awards.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][0], 'award', []).map((x, index) => (\r\n        <Item\r\n          item={x}\r\n          key={_.get(x, '@id', 'main')}\r\n          index={index}\r\n          onChange={onChange}\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          last={index === _.size(_.get(data.jsonld[\"@graph\"][0], 'award', [])) - 1}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.awards.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '', altidentifier='' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('awards.title.label')}\r\n        placeholder=\"Code For Good Hackathon\"\r\n        value={_.get(item,'skill:title', \"\")}\r\n        onChange={v => {onChange(`${identifier}['skill:title']`, v);if(altidentifier!==''){onChange(`${altidentifier}`, v);} }}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('awards.subtitle.label')}\r\n        placeholder=\"Google\"\r\n        value={_.get(item, 'skill:conferredBy', '')}\r\n        onChange={v => onChange(`${identifier}['skill:conferredBy']`, v)}\r\n      />\r\n\r\n      <TextArea\r\n        className=\"mb-6\"\r\n        label={t('app:item.description.label')}\r\n        value={item.description}\r\n        onChange={v => onChange(`${identifier}description`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"skill:Award\",\r\n    \"@id\": \"_:\"+id+\"#enable\",\r\n    \"skill:title\": \"\",\r\n\t\"skill:nativeLabel\": \"\",\r\n    description: ''\r\n  });\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (_.get(item, \"skill:title\", '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][0][\"award\"]', item);\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1][\"award\"]', _.get(item, \"skill:title\", ''));\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][0].award[${index}].`;\r\n  const altidentifier = `data.jsonld[\"@graph\"][1].award[${index}]`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={item[\"skill:title\"]} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} altidentifier={altidentifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][0].award\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AwardsTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst ExtrasTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox\r\n            checked={data.extras.enable}\r\n            onChange={v => onChange('data.extras.enable', v)}\r\n          />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={data.extras.heading}\r\n            onChange={v => onChange('data.extras.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][1], 'identifier', []).map((x, index) => (\r\n        <Item\r\n          item={x}\r\n          key={_.get(x, '@id', 'main')}\r\n          index={index}\r\n          onChange={onChange}\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          last={index === _.get(data.jsonld[\"@graph\"][1], 'identifier', []).length - 1}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={data.extras.heading} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation('leftSidebar');\r\n\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('extras.key.label')}\r\n        placeholder=\"Date of Birth\"\r\n        value={_.get(item, 'propertyID', '')}\r\n        onChange={v => onChange(`${identifier}propertyID`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('extras.value.label')}\r\n        placeholder=\"6th August 1995\"\r\n        value={_.get(item, 'value', '')}\r\n        onChange={v => onChange(`${identifier}value`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"PropertyValue\",\r\n    \"@id\": \"_:Extras_\"+id,\r\n    \"propertyID\": \"\",\r\n    \"value\": \"\"\r\n  });\r\n}\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(items => set({ ...items }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (_.get(item, 'propertyID', '') === '' || _.get(item, 'value', '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].identifier', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].identifier[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, 'propertyID', '')} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].identifier\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ExtrasTab;\r\n", "import set from 'lodash/set';\r\nimport React, { useContext, useEffect, useState } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nimport AppContext from '../../../context/AppContext';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\nimport TextField from '../../../shared/TextField';\r\nimport { addItem } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst LanguagesTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  useEffect(() => {\r\n    if (!('languages' in data)) {\r\n      dispatch({\r\n        type: 'migrate_section',\r\n        payload: {\r\n          key: 'languages',\r\n          value: {\r\n            enable: false,\r\n            heading: 'Languages'\r\n          },\r\n        },\r\n      });\r\n\r\n      dispatch({ type: 'save_data' });\r\n    }\r\n  }, [data, dispatch]);\r\n\r\n  return (\r\n    'languages' in data && (\r\n      <>\r\n        <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n          <div className=\"col-span-1\">\r\n            <Checkbox\r\n              checked={data.languages.enable}\r\n              onChange={v => onChange('data.languages.enable', v)}\r\n            />\r\n          </div>\r\n          <div className=\"col-span-5\">\r\n            <TextField\r\n              placeholder=\"Heading\"\r\n              value={data.languages.heading}\r\n              onChange={v => onChange('data.languages.heading', v)}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <hr className=\"my-6\" />\r\n\r\n        {_.get(data.jsonld[\"@graph\"][1], 'knowsLanguage', []).map((x, index) => (\r\n          <Item\r\n            item={x}\r\n            key={_.get(x,'@id', 'item')}\r\n            index={index}\r\n            onChange={onChange}\r\n            dispatch={dispatch}\r\n            first={index === 0}\r\n            last={index === _.size(_.get(data.jsonld[\"@graph\"][1], 'knowsLanguage', [])) - 1}\r\n          />\r\n        ))}\r\n\r\n        <AddItem heading={data.languages.heading} dispatch={dispatch} />\r\n      </>\r\n    )\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation('leftSidebar');\r\n\r\n  return (\r\n    <div>\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('languages.key.label')}\r\n        placeholder=\"English\"\r\n        value={_.get(item,'name', '')}\r\n        onChange={v => onChange(`${identifier}name`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return ({\r\n    \"@type\": \"Language\",\r\n    \"@id\": \"_:\"+id,\r\n    \"name\": \"\"\r\n  });\r\n}\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  \r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(items => set({ ...items }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (_.get(item, 'name', '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].knowsLanguage', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].knowsLanguage[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, 'name', '')} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].knowsLanguage\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LanguagesTab;\r\n", "import React, { useState, useEffect, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport TextArea from '../../../shared/TextArea';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst ReferencesTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  useEffect(() => {\r\n    if (!('references' in data)) {\r\n      dispatch({\r\n        type: 'migrate_section',\r\n        payload: {\r\n          key: 'references',\r\n          value: {\r\n            enable: false,\r\n            heading: 'References'\r\n          },\r\n        },\r\n      });\r\n\r\n      dispatch({ type: 'save_data' });\r\n    }\r\n  }, [data, dispatch]);\r\n  \r\n  return (\r\n    'references' in data && (\r\n      <>\r\n        <div className=\"mb-6 grid grid-cols-6 items-center\">\r\n          <div className=\"col-span-1\">\r\n            <Checkbox\r\n              checked={data.references.enable}\r\n              onChange={v => onChange('data.references.enable', v)}\r\n            />\r\n          </div>\r\n          <div className=\"col-span-5\">\r\n            <TextField\r\n              placeholder=\"Heading\"\r\n              value={data.references.heading}\r\n              onChange={v => onChange('data.references.heading', v)}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <hr className=\"my-6\" />\r\n\r\n        {_.get(data.jsonld[\"@graph\"][1], 'interactionStatistic', []).filter(x => _.get(x, 'disambiguatingDescription', '')=== 'Reference').map((x, index) => (\r\n          <Item\r\n            item={x}\r\n            key={_.get(x,'@id', 'main')}\r\n            index={index}\r\n            onChange={onChange}\r\n            dispatch={dispatch}\r\n            first={index === 0}\r\n            last={index === _.get(data.jsonld[\"@graph\"][1], 'interactionStatistic', []).filter(x => _.get(x, 'disambiguatingDescription', '')=== 'Reference').length - 1}\r\n          />\r\n        ))}\r\n\r\n        <AddItem heading={data.references.heading} dispatch={dispatch} />\r\n      </>\r\n    )\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('references.name.label')}\r\n          placeholder=\"Richard Hendricks\"\r\n          value={_.get(item, 'interactionType.participant.givenName', '')}\r\n          onChange={v => onChange(`${identifier}interactionType.participant.givenName`, v)}\r\n        />\r\n        \r\n        <TextField\r\n          className=\"mb-6\"\r\n          label={t('references.familyName.label')}\r\n          placeholder=\"Richard Hendricks\"\r\n          value={_.get(item, 'interactionType.participant.familyName', '')}\r\n          onChange={v => onChange(`${identifier}interactionType.participant.familyName`, v)}\r\n        />\r\n      </div>\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('references.position.label')}\r\n        placeholder=\"CEO, Pied Piper\"\r\n        value={_.get(item, 'interactionType.participant.jobTitle', '')}\r\n        onChange={v => onChange(`${identifier}interactionType.participant.jobTitle`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('references.phone.label')}\r\n        placeholder=\"****** 754 3010\"\r\n        value={_.get(item, 'interactionType.participant.telephone', '')}\r\n        onChange={v => onChange(`${identifier}interactionType.participant.telephone`, v)}\r\n      />\r\n\r\n      <TextField\r\n        className=\"mb-6\"\r\n        label={t('references.email.label')}\r\n        placeholder=\"<EMAIL>\"\r\n        value={_.get(item, 'interactionType.participant.email', '')}\r\n        onChange={v => onChange(`${identifier}interactionType.participant.email`, v)}\r\n      />\r\n\r\n      <TextArea\r\n        rows=\"5\"\r\n        className=\"mb-6\"\r\n        label={t('app:item.description.label')}\r\n        value={_.get(item, 'result[0].reviewRating.ratingExplanation', '')}\r\n        onChange={v => onChange(`${identifier}result[0].reviewRating.ratingExplanation`, v)}\r\n      />\r\n    </div>\r\n  );\r\n};\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return (\r\n    {\r\n      \"@id\": \"_:Reference#\"+id,\r\n      \"@type\": \"InteractionCounter\",\r\n      \"disambiguatingDescription\": \"Reference\",\r\n      \"interactionType\": {\r\n        \"@id\": \"_:Reference#\"+id+\"#interactionType\",\r\n        \"@type\": \"AssessAction\",\r\n        \"participant\": {\r\n          \"@id\": \"_:Reference#\"+id+\"#interactionType#participant\",\r\n          \"@type\": \"Person\"\r\n        },\r\n        \"result\": [\r\n          {\r\n            \"@id\": \"_:Reference#\"+id+\"#result\",\r\n            \"@type\": \"Review\",\r\n            \"itemReviewed\": {\r\n            \r\n            },\r\n            \"reviewAspect\": [\r\n              \r\n            ],\r\n            \"reviewRating\": {\r\n              \"@id\": \"_:Reference#\"+id+\"#result#reviewRating\",\r\n              \"@type\": \"Rating\",\r\n              \"ratingValue\": \"5\",\r\n              \"bestRating\": \"5\",\r\n              \"ratingExplanation\": \"\"\r\n            }\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  );\r\n}\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n\r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n\r\n  const onSubmit = () => {\r\n    if (_.get(item, 'interactionType.participant.givenName', '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].interactionStatistic', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld[\"@graph\"][1].interactionStatistic[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, 'interactionType.participant.givenName', '')+\" \"+_.get(item, 'interactionType.participant.familyName', '')} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].interactionStatistic\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ReferencesTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport set from 'lodash/set';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Checkbox from '../../../shared/Checkbox';\r\nimport { addItem } from '../../../utils';\r\nimport ItemActions from '../../../shared/ItemActions';\r\nimport AddItemButton from '../../../shared/AddItemButton';\r\nimport ItemHeading from '../../../shared/ItemHeading';\r\n\r\nimport * as _  from 'lodash';\r\n\r\nconst MembershipsTab = ({ data, onChange }) => {\r\n  const context = useContext(AppContext);\r\n  const { dispatch } = context;\r\n\r\n  return (\r\n    <>\r\n      <div className=\"my-6 grid grid-cols-6 items-center\">\r\n        <div className=\"col-span-1\">\r\n          <Checkbox\r\n            checked={_.get(data,'Memberships.enable', true)}\r\n            onChange={v => onChange('data.Memberships.enable', v)}\r\n          />\r\n        </div>\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            placeholder=\"Heading\"\r\n            value={_.get(data, 'Memberships.heading', '')}\r\n            onChange={v => onChange('data.Memberships.heading', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      {_.get(data.jsonld[\"@graph\"][1], 'memberOf', []).map((x, index) => (\r\n        <Item\r\n          item={x}\r\n          key={_.get(x, '@id', 'item')}\r\n          index={index}\r\n          onChange={onChange}\r\n          dispatch={dispatch}\r\n          first={index === 0}\r\n          last={index === _.size(_.get(data.jsonld[\"@graph\"][1], 'memberOf', [])) - 1}\r\n        />\r\n      ))}\r\n\r\n      <AddItem heading={_.get(data, 'Memberships.heading', '')} dispatch={dispatch} />\r\n    </>\r\n  );\r\n};\r\n\r\nconst Form = ({ item, onChange, identifier = '' }) => {\r\n  const { t } = useTranslation(['leftSidebar', 'app']);\r\n  \r\n  return (\r\n    <div>\r\n      <TextField\r\n          className=\"mb-6\"\r\n          label={t('membership.programName.label')}\r\n          placeholder=\"Salsa Dance Class\"\r\n          value={_.get(item, \"memberOf.programName\", \"\")}\r\n          onChange={v => onChange(`${identifier}memberOf.programName`, v)}\r\n      />\r\n      \r\n      <div className=\"grid grid-cols-2 col-gap-4\">\r\n        <TextField\r\n            className=\"mb-6\"\r\n            label={t('membership.startDate.label')}\r\n            placeholder=\"2019-01-01\"\r\n            value={_.get(item, \"startDate\", \"\")}\r\n            onChange={v => onChange(`${identifier}startDate`, v)}\r\n        />\r\n      \r\n        <TextField\r\n            className=\"mb-6\"\r\n            label={t('membership.endDate.label')}\r\n            placeholder=\"2020-01-01\"\r\n            value={_.get(item, \"endDate\", \"\")}\r\n            onChange={v => onChange(`${identifier}endDate`, v)}\r\n        />\r\n      </div>\r\n        <TextField\r\n            className=\"mb-6\"\r\n            label={t('membership.roleName.label')}\r\n            placeholder = \"VIP member\"\r\n            value={_.get(item, \"roleName\", \"\")}\r\n            onChange={v => onChange(`${identifier}roleName`, v)}\r\n        />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst emptyItem = () => {\r\n  let id = uuidv4();\r\n  return (\r\n    {\r\n      \"@id\": \"_:\"+id+\"#enable\",\r\n      \"@type\": \"Role\",\r\n      \"startDate\": \"\",\r\n      \"endDate\": \"\",\r\n      \"roleName\": \"member\",\r\n      \"memberOf\": {\r\n        \"@id\": \"_:\"+id+\"#memberOf\",\r\n        \"@type\": \"ProgramMembership\",\r\n        \"url\": \"\",\r\n        \"programName\": \"\",\r\n        \"description\": \"\"\r\n      }\r\n    }\r\n  );\r\n};\r\n\r\nconst AddItem = ({ heading, dispatch }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const [item, setItem] = useState(emptyItem());\r\n  \r\n  const onChange = (key, value) => setItem(set({ ...item }, key, value));\r\n  const onSubmit = () => {\r\n    if (_.get(item, 'roleName', '') === '') return;\r\n\r\n    addItem(dispatch, 'data.jsonld[\"@graph\"][1].memberOf', item);\r\n\r\n    setItem(emptyItem());\r\n\r\n    setOpen(false);\r\n  };\r\n  \r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading heading={heading} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} />\r\n        <AddItemButton onSubmit={onSubmit} />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ItemActionEnable = (identifier, item, onChange) => {\r\n  \r\n  return (\r\n    <></>\r\n  )\r\n}\r\n\r\nconst Item = ({ item, index, onChange, dispatch, first, last }) => {\r\n  const [isOpen, setOpen] = useState(false);\r\n  const identifier = `data.jsonld['@graph'][1].memberOf[${index}].`;\r\n\r\n  return (\r\n    <div className=\"my-4 border border-gray-200 rounded p-5\">\r\n      <ItemHeading title={_.get(item, \"memberOf.programName\", \"\")} setOpen={setOpen} isOpen={isOpen} />\r\n\r\n      <div className={`mt-6 ${isOpen ? 'block' : 'hidden'}`}>\r\n        <Form item={item} onChange={onChange} identifier={identifier} />\r\n\r\n        <ItemActions\r\n          dispatch={dispatch}\r\n          first={first}\r\n          identifier={identifier}\r\n          item={item}\r\n          last={last}\r\n          onChange={onChange}\r\n          type=\"data.jsonld['@graph'][1].memberOf\"\r\n          enableAction={ItemActionEnable}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MembershipsTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport * as _  from 'lodash';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport TabBar from '../../shared/TabBar';\r\nimport ProfileTab from './tabs/Profile';\r\nimport AddressTab from './tabs/Address';\r\nimport ContactsTab from './tabs/Contacts';\r\nimport ObjectiveTab from './tabs/Objective';\r\nimport WorkTab from './tabs/Work';\r\nimport EducationTab from './tabs/Education';\r\nimport AwardsTab from './tabs/Awards';\r\nimport ExtrasTab from './tabs/Extras';\r\nimport LanguagesTab from './tabs/Languages';\r\nimport ReferencesTab from './tabs/References';\r\nimport MembershipsTab from './tabs/Memberships';\r\n\r\nconst LeftSidebar = () => {\r\n  const context = useContext(AppContext);\r\n  const { state, dispatch } = context;\r\n  const { data } = state;\r\n\r\n  const tabs = [\r\n    { key: 'profile', name: _.get(data, \"profile.heading\", \"Profile\") },\r\n    { key: 'address', name: _.get(data, \"address.headin\", \"Address\") },\r\n    { key: 'contacts', name: _.get(data, \"contacts.heading\", \"Contacts\") },\r\n    { key: 'objective', name: _.get(data, \"objective.heading\", \"Objective\") },\r\n    { key: 'work', name: _.get(data, \"work.heading\", \"Work\") },\r\n    { key: 'education', name: _.get(data, \"education.heading\", \"Education\") },\r\n    { key: 'awards', name: _.get(data, \"awards.heading\", \"Awards\")  },\r\n    { key: 'memberships', name: _.get(data, \"memberships.heading\", \"Memberships\") },\r\n    { key: 'languages', name: _.get(data, \"languages.heading\", \"Languages\") },\r\n    { key: 'references', name: _.get(data, \"references.heading\", \"References\") },\r\n    { key: 'extras', name: _.get(data, \"extras.heading\", \"Extras\") },\r\n  ];\r\n  const [currentTab, setCurrentTab] = useState(tabs[0].key);\r\n  const onChange = (key, value) => {\r\n    dispatch({\r\n      type: 'on_input',\r\n      payload: {\r\n        key,\r\n        value,\r\n      },\r\n    });\r\n\r\n    dispatch({ type: 'save_data' });\r\n  };\r\n\r\n  const renderTabs = () => {\r\n    switch (currentTab) {\r\n      case 'profile':\r\n        return <ProfileTab data={data} onChange={onChange} />;\r\n      case 'address':\r\n        return <AddressTab data={data} onChange={onChange} />;\r\n      case 'contacts':\r\n        return <ContactsTab data={data} onChange={onChange} />;\r\n      case 'objective':\r\n        return <ObjectiveTab data={data} onChange={onChange} />;\r\n      case 'work':\r\n        return <WorkTab data={data} onChange={onChange} />;\r\n      case 'education':\r\n        return <EducationTab data={data} onChange={onChange} />;\r\n      case 'awards':\r\n        return <AwardsTab data={data} onChange={onChange} />;\r\n      case 'memberships':\r\n        return <MembershipsTab data={data} onChange={onChange} />;\r\n      case 'languages':\r\n        return <LanguagesTab data={data} onChange={onChange} />;\r\n      case 'references':\r\n        return <ReferencesTab data={data} onChange={onChange} />;\r\n      case 'extras':\r\n        return <ExtrasTab data={data} onChange={onChange} />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      id=\"leftSidebar\"\r\n      className=\"animated slideInLeft z-10 py-6 h-screen bg-white col-span-1 shadow-2xl overflow-y-scroll\"\r\n    >\r\n      <TabBar tabs={tabs} currentTab={currentTab} setCurrentTab={setCurrentTab} />\r\n      <div className=\"px-6\">{renderTabs()}</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LeftSidebar;\r\n", "import React, { memo, useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { formatDate, safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst AwardItem = ({ item, language }) => (\r\n  <div>\r\n    <div className=\"flex justify-between items-center\">\r\n      <div className=\"flex flex-col text-left mr-2\">\r\n        <h6 className=\"font-semibold text-sm\">{_.get(item, \"['skill:title']\", \"\")}</h6>\r\n        <span className=\"text-xs\">{_.get(item, \"['skill:conferredBy'].name\", \"\")}</span>\r\n      </div>\r\n      {_.get(item, \"['skill:awardedDate']\", \"\") !== '' && (\r\n        <h6 className=\"text-xs font-medium text-right\">\r\n          {formatDate({ date: _.get(item, \"['skill:awardedDate']\", \"\"), language })}\r\n        </h6>\r\n      )}\r\n    </div>\r\n    {_.get(item, \"description\", \"\") !== '' && (\r\n      <ReactMarkdown className=\"markdown mt-2 text-sm\" source={_.get(item, \"description\", \"\")} />\r\n    )}\r\n  </div>\r\n);\r\n\r\nconst AwardsA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return (data.awards &&\r\n    data.awards.enable) ? (\r\n    <div>\r\n      <Heading>{data.awards.heading}</Heading>\r\n      <div className=\"grid gap-4\">\r\n        {_.get(data.jsonld[\"@graph\"][0], 'award', []).filter(x => x[\"skill:title\"]!==\"\").map((x) => (\r\n          <AwardItem key={_.get(x,'@id', uuidv4())} item={x} language={data.language || 'en'} />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(AwardsA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { formatDate, safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst CertificationItem = ({ item, language }) => (\r\n  <div>\r\n    <div className=\"flex justify-between items-center\">\r\n      <div className=\"flex flex-col text-left mr-2\">\r\n        <h6 className=\"font-semibold text-sm\">{_.get(item, 'educationalLevel', '')} {_.get(item, 'about.educationalCredentialAwarded', '')}</h6>\r\n        <span className=\"text-xs\">{_.get(item, \"about.provider.name\", \"\")}</span>\r\n      </div>\r\n      { _.get(item, \"about.endDate\", \"\") !== '' && (\r\n        <h6 className=\"text-xs font-medium text-right\">\r\n          {formatDate({ date: _.get(item, \"about.endDate\", \"\"), language })}\r\n        </h6>\r\n      )}\r\n    </div>\r\n    {_.get(item, 'abstract', '') !== '' && (\r\n      <ReactMarkdown className=\"markdown mt-2 text-sm\" source={_.get(item, 'abstract', '')} />\r\n    )}\r\n  </div>\r\n);\r\n\r\nconst CertificationsA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return (data.certifications &&\r\n    data.certifications.enable) ? (\r\n    <div>\r\n      <Heading>{data.certifications.heading}</Heading>\r\n      <div className=\"grid gap-4\">\r\n        {_.get(data, \"jsonld['@graph'][1].hasCredential\", []).filter(x => (!_.get(x, '@id', '').endsWith(\"disable\") && _.toLower(_.get(x, 'credentialCategory', ''))!==\"degree\")).map(x=> (<CertificationItem\r\n            key={_.get(x,'@id', uuidv4())}\r\n            item={x}\r\n            language={data.language}\r\n          />))}\r\n      </div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(CertificationsA);\r\n", "import {\r\n  FaGlobeAmericas,\r\n  FaFacebookF,\r\n  FaTwitter,\r\n  FaLinkedinIn,\r\n  FaGithub,\r\n  FaDribbble,\r\n  FaInstagram,\r\n  FaStackOverflow,\r\n  FaBehance,\r\n  FaGitlab,\r\n  FaBirthdayCake,\r\n  FaTelegram,\r\n  FaSkype,\r\n} from 'react-icons/fa';\r\nimport { MdPhone, MdEmail } from 'react-icons/md';\r\n\r\nconst Icons = {\r\n  phone: MdPhone,\r\n  website: FaGlobeAmericas,\r\n  email: MdEmail,\r\n  facebook: FaFacebookF,\r\n  twitter: FaTwitter,\r\n  linkedin: FaLinkedinIn,\r\n  github: FaGithub,\r\n  dribbble: FaDribbble,\r\n  instagram: FaInstagram,\r\n  stackoverflow: FaStackOverflow,\r\n  behance: FaBehance,\r\n  gitlab: FaGitlab,\r\n  birthday: FaBirthdayCake,\r\n  telegram: FaTelegram,\r\n  skype: FaSkype,\r\n};\r\n\r\nexport default Icons;\r\n", "import React, { memo, useContext } from 'react';\r\nimport { get } from 'lodash';\r\nimport PageContext from '../../../context/PageContext';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Icons from '../Icons';\r\nimport { formatDate } from '../../../utils';\r\nimport * as _  from 'lodash';\r\n\r\nconst BirthDateB = () => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const Icon = get(Icons, 'birthday');\r\n\r\n  if (_.get(data,\"jsonld['@graph'][1].birthDate\",\"\")) {\r\n    return (\r\n      <div className=\"text-xs flex items-center\">\r\n        <Icon\r\n          size=\"10px\"\r\n          className=\"mr-2\"\r\n          style={{ color: theme.colors.primary }}\r\n        />\r\n        <span className=\"font-medium break-all\">\r\n          {formatDate({\r\n            date: _.get(data,\"jsonld['@graph'][1].birthDate\",\"\"),\r\n            language: data.language || 'en',\r\n            includeDay: true,\r\n          })}\r\n        </span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport default memo(BirthDateB);\r\n", "import { get } from 'lodash';\r\nimport React, { memo, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { FaCaretRight } from 'react-icons/fa';\r\nimport AppContext from '../../../context/AppContext';\r\nimport { safetyCheck } from '../../../utils';\r\nimport BirthDateB from '../BirthDate/BirthDateB';\r\nimport Icons from '../Icons';\r\nimport * as _  from 'lodash';\r\n\r\nconst ContactItem = ({ value, icon, link }) => {\r\n  const Icon = get(Icons, icon && icon.toLowerCase(), FaCaretRight);\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  return value ? (\r\n    <div className=\"flex items-center\">\r\n      <Icon\r\n        size=\"10px\"\r\n        className=\"mr-2\"\r\n        style={{ color: theme.colors.primary }}\r\n      />\r\n      {link ? (\r\n        <a href={link} target=\"_blank\" rel=\"noopener noreferrer\">\r\n          <span className=\"font-medium break-all\">{value}</span>\r\n        </a>\r\n      ) : (\r\n        <span className=\"font-medium break-all\">{value}</span>\r\n      )}\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nconst ContactA = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  return (\r\n    <div className=\"text-xs grid gap-2\">\r\n      <ContactItem\r\n        label={_.get(data,'profile.phone.heading', t(\"Phone\"))}\r\n        value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'telephone', \"\")}\r\n        icon=\"phone\"\r\n        link={`tel:${_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'telephone', \"\")}`}\r\n      />\r\n      <ContactItem\r\n        label={_.get(data,'profile.website.heading', t(\"Website\"))}\r\n        value={_.get(data,'jsonld[\"@graph\"][1].sameAs[0]',\"\")}\r\n        icon=\"website\"\r\n        link={_.get(data,'jsonld[\"@graph\"][1].sameAs[0]',\"\")}\r\n      />\r\n      <ContactItem\r\n        label={_.get(data,'profile.email.heading' ,t(\"Email\"))}\r\n        value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'email', \"\")}\r\n        icon=\"email\"\r\n        link={`mailto:${_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'email', \"\")}`}\r\n      />\r\n\r\n      <BirthDateB />\r\n\r\n      {safetyCheck(data.social) &&\r\n        data.social.items.map((x) => (\r\n          <ContactItem\r\n            key={x.id}\r\n            value={x.username}\r\n            icon={x.network}\r\n            link={x.url}\r\n          />\r\n        ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(ContactA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst SectionSkillsItem = x => (\r\n    x && (\r\n        <p key={uuidv4()}>&nbsp;| {x}</p>\r\n    )\r\n)\r\n  \r\nconst SectionSkillsA = ({skills}) => (\r\n    skills && (skills.length>0) && (\r\n      <div className=\"text-xs text-gray-800 flex\">\r\n      {\r\n        skills.filter(x => (x !== '')).map(SectionSkillsItem)\r\n      }\r\n      </div>\r\n    )\r\n)\r\n\r\nexport default memo(SectionSkillsA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { formatDateRange, safetyCheck } from '../../../utils';\r\nimport SectionSkillsA from '../SectionSkills/SectionSkillsA';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst EducationItem = ({ item, language }) => {\r\n  const { t } = useTranslation();\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between items-center\">\r\n        <div className=\"flex flex-col text-left mr-2\">\r\n          <h6 className=\"font-semibold text-sm\">{_.get(item, \"about.provider.name\", \"\")}</h6>\r\n          <span className=\"text-xs\">\r\n            <strong>{_.get(item, \"educationalLevel\", \"\")}</strong> {_.get(item, \"about.educationalCredentialAwarded\", \"\")}\r\n          </span>\r\n        </div>\r\n        <div className=\"flex flex-col items-end text-right\">\r\n          {_.get(item, \"about.startDate\", \"\") !== '' && (\r\n            <h6 className=\"text-xs font-medium mb-1\">\r\n              (\r\n              {formatDateRange(\r\n                {\r\n                  startDate: _.get(item, \"about.startDate\", \"\"),\r\n                  endDate: _.get(item, \"about.endDate\", \"\"),\r\n                  language,\r\n                },\r\n                t,\r\n              )}\r\n              )\r\n            </h6>\r\n          )}\r\n          <span className=\"text-sm font-medium\">{_.get(item, \"aggregateRating.ratingValue\", \"\")}/{_.get(item, \"aggregateRating.bestRating\", \"\")}</span>\r\n        </div>\r\n      </div>\r\n      {item.summary && (\r\n        <ReactMarkdown\r\n          className=\"markdown mt-2 text-sm\"\r\n          source={_.get(item, 'abstract', '')}\r\n        />\r\n      )}\r\n      <SectionSkillsA skills={_.get(item, \"teaches\", [])} />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst EducationA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return (_.get(data, \"jsonld['@graph'][1].hasCredential\", []).length>0 &&\r\n    data.education.enable) ? (\r\n    <div>\r\n      <Heading>{data.education.heading}</Heading>\r\n      <div className=\"grid gap-4\">\r\n          {_.get(data, \"jsonld['@graph'][1].hasCredential\", []).filter(x => (!_.get(x, '@id', '').endsWith(\"disable\") && _.get(x, 'credentialCategory', '')===\"degree\")).map((x) => (<EducationItem\r\n            key={_.get(x, '@id', uuidv4())}\r\n            item={x}\r\n            language={data.language || 'en'}\r\n          />\r\n          ))}\r\n      </div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(EducationA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport AppContext from '../../../context/AppContext';\r\n\r\nconst HeadingA = ({ children }) => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  return (\r\n    <h6\r\n      className=\"text-xs font-bold uppercase mb-1\"\r\n      style={{ color: theme.colors.primary }}\r\n    >\r\n      {children}\r\n    </h6>\r\n  );\r\n};\r\n\r\nexport default memo(HeadingA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nconst HobbyA = (x) => (\r\n  <div key={x.id}>\r\n    <h6 className=\"font-semibold text-sm\">{x.name}</h6>\r\n  </div>\r\n);\r\n\r\nconst HobbiesA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return safetyCheck(data.hobbies) ? (\r\n    <div>\r\n      <Heading>{data.hobbies.heading}</Heading>\r\n      <div className=\"grid gap-2\">{data.hobbies.items.map(HobbyA)}</div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(HobbiesA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst LanguageItem = (x) => (\r\n  <div key={_.get(x,'@id', uuidv4())} className=\"flex flex-col\">\r\n    <h6 className=\"font-semibold text-sm\">{_.get(x, 'name', '')}</h6>\r\n    <span className=\"text-xs\">{x.fluency}</span>\r\n  </div>\r\n);\r\n\r\nconst LanguagesA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return (data.languages &&\r\n    data.languages.enable && (_.get(data, 'jsonld[\"@graph\"][1].knowsLanguage',[]).length > 0)) ? (\r\n    <div>\r\n      <Heading>{data.languages.heading}</Heading>\r\n      <div className=\"grid grid-cols-2 gap-2\">\r\n        {_.get(data, 'jsonld[\"@graph\"][1].knowsLanguage', []).filter(x => _.get(x, 'name', '') !== '').map(LanguageItem)}\r\n      </div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(LanguagesA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport PageContext from '../../../context/PageContext';\r\nimport * as _  from 'lodash';\r\n\r\nconst ObjectiveA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return (\r\n    _.size(_.get(data, 'jsonld[\"@graph\"][1].seeks',[]))>0 && (\r\n      <div>\r\n        <Heading>{data.objective.heading}</Heading>\r\n        {_.get(data, 'jsonld[\"@graph\"][1].seeks',[]).map((x, index) => (\r\n              <ReactMarkdown key={\"objetive_\"+index} className=\"mr-10 text-sm\" source={x.description} />\r\n          ))}\r\n      </div>\r\n    )\r\n  );\r\n};\r\n\r\nexport default memo(ObjectiveA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { formatDateRange, safetyCheck } from '../../../utils';\r\n\r\nconst ProjectItem = ({ item, language }) => {\r\n  const { t } = useTranslation();\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between items-center\">\r\n        <div className=\"flex flex-col text-left mr-2\">\r\n          <h6 className=\"font-semibold text-sm\">{item.title}</h6>\r\n          {item.link && (\r\n            <a href={item.link} className=\"text-xs\">\r\n              {item.link}\r\n            </a>\r\n          )}\r\n        </div>\r\n        {item.date && (\r\n          <h6 className=\"text-xs font-medium text-right\">\r\n            (\r\n            {formatDateRange(\r\n              {\r\n                startDate: item.date,\r\n                endDate: item.endDate,\r\n                language,\r\n              },\r\n              t,\r\n            )}\r\n            )\r\n          </h6>\r\n        )}\r\n      </div>\r\n      {item.summary && (\r\n        <ReactMarkdown\r\n          className=\"markdown mt-2 text-sm\"\r\n          source={item.summary}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ProjectsA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return safetyCheck(data.projects) ? (\r\n    <div>\r\n      <Heading>{data.projects.heading}</Heading>\r\n      <div className=\"grid gap-4\">\r\n        {data.projects.items.map((x) => (\r\n          <ProjectItem key={x.id} item={x} language={data.metadata.language} />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(ProjectsA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst ReferenceItem = (x) => (\r\n  <div key={_.get(x, '@id', uuidv4())} className=\"flex flex-col\">\r\n    <h6 className=\"font-semibold text-sm\">{_.get(x, 'interactionType.participant.givenName', '')} {_.get(x, 'interactionType.participant.familyName', '')}</h6>\r\n    <span className=\"text-xs\">{_.get(x, 'interactionType.participant.jobTitle', '')}</span>\r\n    <span className=\"text-xs\">{_.get(x, 'interactionType.participant.telephone', '')}</span>\r\n    <span className=\"text-xs\">{_.get(x, 'interactionType.participant.email', '')}</span>\r\n    {_.get(x, 'result[0].reviewRating.ratingExplanation', '') !== '' && (\r\n      <ReactMarkdown className=\"markdown mt-2 text-sm\" source={_.get(x, 'result[0].reviewRating.ratingExplanation', '')} />\r\n    )}\r\n  </div>\r\n);\r\n\r\nconst ReferencesA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return safetyCheck(data.references) ? (\r\n    <div>\r\n      <Heading>{data.references.heading}</Heading>\r\n      <div className=\"grid grid-cols-3 gap-4\">\r\n        {_.get(data.jsonld[\"@graph\"][1], 'interactionStatistic', []).filter(x => _.get(x, 'disambiguatingDescription', '')=== 'Reference').map(ReferenceItem)}\r\n      </div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(ReferencesA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst userSkills = (data) => {\r\n    let workSkills = _.chain(_.get(data, \"jsonld['@graph'][1].hasOccupation\", [])).map('skills').flatten();\r\n    \r\n    let awardSkills = _.chain(_.get(data, \"jsonld['@graph'][0].award\", [])).map('skill:assesses').flatten();\r\n    \r\n    let educationSkills = _.chain(_.get(data, \"jsonld['@graph'][1].hasCredential\", [])).map('teaches').flatten();\r\n    \r\n    let coursesSkills = _.chain(_.get(data, \"jsonld['@graph'][1].hasCredential\", [])).map('about').flatten().map('hasCourse').flatten().map('teaches').flatten();\r\n    \r\n    let educationProjectSkills = _.chain(_.get(data, \"jsonld['@graph'][1].hasCredential\", [])).map('about').map('workExample').flatten().map('hasPart').flatten().map('teaches').flatten();\r\n    \r\n    let interactionTeachSkills = _.chain(_.get(data, \"jsonld['@graph'][1].interactionStatistic\", [])).map('result').flatten().map('teaches').flatten();\r\n    \r\n    let interactionAssessSkills = _.chain(_.get(data, \"jsonld['@graph'][1].interactionStatistic\", [])).map('result').flatten().map('assesses').flatten();\r\n    \r\n    let allSkills = [...workSkills, ...awardSkills, ...educationSkills, ...coursesSkills, ...educationProjectSkills, ...interactionTeachSkills, ...interactionAssessSkills];\r\n    \r\n    let skillsObject = {};\r\n    for(let i=0; i<allSkills.length; i++){\r\n      if(skillsObject[allSkills[i]]){\r\n        skillsObject[allSkills[i]] = skillsObject[allSkills[i]] + 1;\r\n      }else{\r\n        skillsObject[allSkills[i]] = 1;\r\n      }\r\n    }\r\n    \r\n    return skillsObject;\r\n};\r\n\r\nconst SkillItem = (x, level) => (\r\n  x !== undefined && x !== 'undefined' && x !== '' && (\r\n  <div key={uuidv4()} className=\"flex flex-col\">\r\n    <h6 className=\"font-semibold text-sm\">{x}</h6>\r\n    <span className=\"text-xs\">{(level < 2) ? 'Beginner': ((level <=4 && level>=2) ? 'Intermediate': 'Advanced')}</span>\r\n  </div>\r\n  )\r\n);\r\n\r\nconst SkillsA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n  const skills = userSkills(data);\r\n  \r\n  return _.size(skills) > 0 ? (\r\n    <div>\r\n      <Heading>{data.skills.heading}</Heading>\r\n      <div className=\"grid grid-cols-2 gap-y-2 gap-x-4\">\r\n        {Object.keys(skills).map(SkillItem)}\r\n      </div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(SkillsA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { formatDateRange, safetyCheck } from '../../../utils';\r\nimport SectionSkillsA from '../SectionSkills/SectionSkillsA';\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\n  \r\nconst WorkResponsibilityItem = x => (\r\n    x && (\r\n        <li className=\"mt-2 text-sm\" key={uuidv4()}>{x}</li>\r\n    )\r\n)\r\n    \r\nconst WorkResponsibility = ({responsibilities}) => (\r\n    responsibilities && (responsibilities.length>0) && (\r\n      <ul>\r\n      {\r\n        responsibilities.filter(x => (x !== '')).map(WorkResponsibilityItem)\r\n      }\r\n      </ul>\r\n    )\r\n)\r\n\r\nconst WorkItem = ({ item, language='en' }) => {\r\n  const { t } = useTranslation();\r\n  return (\r\n    <div>\r\n      <div className=\"flex justify-between items-center\">\r\n        <div className=\"flex flex-col text-left mr-2\">\r\n          <h6 className=\"font-semibold text-sm\">{_.get(item,'subjectOf.organizer.name','')}</h6>\r\n          <span className=\"text-xs\">{_.get(item,'roleName', '')}</span>\r\n        </div>\r\n        { _.get(item,'startDate','') !== '' && (\r\n          <h6 className=\"text-xs font-medium text-right\">\r\n            (\r\n            {formatDateRange(\r\n              {\r\n                startDate: _.get(item,'startDate',''),\r\n                endDate: _.get(item,'endDate',''),\r\n                language,\r\n              },\r\n              t,\r\n            )}\r\n            )\r\n          </h6>\r\n        )}\r\n      </div>\r\n      {_.get(item,'description','') !== '' && (\r\n        <ReactMarkdown\r\n          className=\"markdown mt-2 text-sm\"\r\n          source={_.get(item,'description','')}\r\n        />\r\n      )}\r\n      <WorkResponsibility responsibilities={_.get(item, \"hasOccupation.responsibilities\", [])} />\r\n      <SectionSkillsA skills={_.get(item, \"hasOccupation.skills\", [])} />\r\n    </div>\r\n  );\r\n};\r\n\r\nconst WorkA = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return (_.get(data, \"jsonld['@graph'][1].hasOccupation\", []).length > 0 && data.work.enable) ? (\r\n    <div>\r\n      <Heading>{data.work.heading}</Heading>\r\n      <div className=\"grid gap-4\">\r\n        {_.get(data, \"jsonld['@graph'][1].hasOccupation\", []).filter(x => !_.get(x, '@id', '').endsWith(\"disable\")).map((x) => (\r\n          <WorkItem key={_.get(x,'@id', uuidv4())} item={x} language={data.language || 'en'} />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(WorkA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst AddressItem = ({x, index, subclassName}) => (\r\n    x && (\r\n      <div className={subclassName}>\r\n        <span>{x.streetAddress}</span>\r\n        <span>&nbsp;{x.addressLocality} {x.addressRegion}</span>\r\n        <span>&nbsp;{x.addressCountry} {x.postalCode}</span>\r\n      </div>\r\n    )\r\n);\r\n  \r\nconst AddressA = ({data, mainclassName=\"\", hclassName=\"capitalize font-semibold\"}, subclassName=\"flex flex-col text-xs\") => (\r\n    (\r\n      data.jsonld[\"@graph\"][1].address && data.jsonld[\"@graph\"][1].address.length>0 &&\r\n      data.address.enable && (\r\n        <div className={mainclassName}>\r\n          <h6 className={hclassName}>{_.get(data, 'profile.address.heading', \"Address\")}</h6>\r\n          \r\n          {data.jsonld[\"@graph\"][1].address.filter(x => (Date.parse(x.hoursAvailable.validThrough) - Date.parse(new Date()))>0).map((x, index) => (<AddressItem x={x} index={index} subclassName={subclassName} key={index} />))}\r\n        </div>\r\n      )\r\n    ) || (\"\")\r\n);\r\n\r\nexport default memo(AddressA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n  \r\nconst NamesA = ({data, className=\"tracking-wide uppercase font-bold\"}, style={ fontSize: '2.75em' }) => (\r\n    <h1 className={className} style={style}>\r\n          {(Array.isArray(data.jsonld['@graph'][1].givenName)) ? (_.get(data,\"jsonld['@graph'][1].givenName[0]['@value']\",\"\")) : (data.jsonld['@graph'][1].givenName)} {(Array.isArray(data.jsonld['@graph'][1].familyName)) ? (_.get(data,\"jsonld['@graph'][1].familyName[0]['@value']\", \"\")) : (data.jsonld['@graph'][1].familyName)}\r\n        </h1>\r\n  );\r\n\r\nexport default memo(NamesA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n  \r\nconst SubnamesA = ({data}) => (\r\n    <h6 className=\"text-lg tracking-wider uppercase\">{(\r\n    (_.get(data,\"jsonld['@graph'][1].givenName[1]\", \"\")) ? (\" (\"+_.get(data,\"jsonld['@graph'][1].givenName\", []).map(function(elem,index){\r\n              if(index > 0 && elem['@value']){\r\n                let name = elem['@value'];\r\n                let familynameIndex = _.get(data,\"jsonld['@graph'][1].familyName\",[]).findIndex(x=>x['@language']===elem['@language']);\r\n                if(familynameIndex >= 0){\r\n                  if(data.jsonld['@graph'][1].familyName[familynameIndex] && data.jsonld['@graph'][1].familyName[familynameIndex]['@value']){\r\n                    name += \" \"+data.jsonld['@graph'][1].familyName[familynameIndex]['@value'];\r\n                  }\r\n                }\r\n                return name;\r\n              }else{\r\n                return null;\r\n              }\r\n            }).filter(function (el) {\r\n              return el != null;\r\n            }).join(\", \")+\")\") \r\n            : \r\n            (\"\")\r\n            )}\r\n            </h6>\r\n  );\r\n\r\nexport default memo(SubnamesA);\r\n", "import React, { useContext, memo } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport AwardsA from '../blocks/Awards/AwardsA';\r\nimport CertificationsA from '../blocks/Certifications/CertificationsA';\r\nimport Contact from '../blocks/Contact/ContactA';\r\nimport EducationA from '../blocks/Education/EducationA';\r\nimport HeadingA from '../blocks/Heading/HeadingA';\r\nimport HobbiesA from '../blocks/Hobbies/HobbiesA';\r\nimport LanguagesA from '../blocks/Languages/LanguagesA';\r\nimport ObjectiveA from '../blocks/Objective/ObjectiveA';\r\nimport ProjectsA from '../blocks/Projects/ProjectsA';\r\nimport ReferencesA from '../blocks/References/ReferencesA';\r\nimport SkillsA from '../blocks/Skills/SkillsA';\r\nimport WorkA from '../blocks/Work/WorkA';\r\nimport AddressA from '../blocks/Address/AddressA';\r\n\r\nimport PageContext from '../../context/PageContext';\r\nimport { hasAddress } from '../../utils';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport NamesA from '../blocks/Names/NamesA';\r\nimport SubNamesA from '../blocks/Names/SubNamesA';\r\nimport * as _  from 'lodash';\r\n\r\nconst Blocks = {\r\n  objective: ObjectiveA,\r\n  work: WorkA,\r\n  education: EducationA,\r\n  projects: ProjectsA,\r\n  awards: AwardsA,\r\n  certifications: CertificationsA,\r\n  skills: SkillsA,\r\n  hobbies: HobbiesA,\r\n  languages: LanguagesA,\r\n  references: ReferencesA,\r\n};\r\n\r\nconst Onyx = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const layout = _.get(theme,'layoutblocks.onyx', []);\r\n\r\n  return (\r\n    <PageContext.Provider value={{ data, heading: HeadingA }}>\r\n      <div\r\n        id=\"page\"\r\n        className=\"p-8 rounded\"\r\n        style={{\r\n          fontFamily: theme.font.family,\r\n          color: theme.colors.primary,\r\n          backgroundColor: theme.colors.background,\r\n        }}\r\n      >\r\n        <div className=\"grid grid-cols-4 items-center\">\r\n          <div className=\"col-span-3 flex items-center\">\r\n            {_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\") !== '' && (\r\n              <img\r\n                className=\"rounded object-cover mr-4\"\r\n                src={_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\")}\r\n                alt=\"Resume Photograph\"\r\n                style={{ width: '120px', height: '120px' }}\r\n              />\r\n            )}\r\n\r\n            <div>\r\n                <NamesA data={data} className=\"font-bold text-4xl\" style={{ color: theme.colors.primary }} />\r\n                <SubNamesA data={data} />\r\n              <h6 className=\"font-medium text-sm\">{_.get(data, 'jsonld[\"@graph\"][1].description', \"\")}</h6>\r\n\r\n              <AddressA data={data} mainclassName=\"flex flex-col mt-4 text-xs\" hclassName=\"font-bold text-xs uppercase tracking-wide mb-1\" subclassName=\"\" />\r\n            </div>\r\n          </div>\r\n\r\n          <Contact />\r\n        </div>\r\n\r\n        <hr\r\n          className=\"my-5 opacity-25\"\r\n          style={{ borderColor: theme.colors.primary }}\r\n        />\r\n\r\n        <div className=\"grid gap-4\">\r\n          {layout[0] &&\r\n            layout[0].map((x) => {\r\n              const Component = Blocks[x];\r\n              return Component && <Component key={x} />;\r\n            })}\r\n\r\n          <div className=\"grid grid-cols-2 gap-4\">\r\n            {layout[1] &&\r\n              layout[1].map((x) => {\r\n                const Component = Blocks[x];\r\n                return Component && <Component key={x} />;\r\n              })}\r\n          </div>\r\n\r\n          {layout[2] &&\r\n            layout[2].map((x) => {\r\n              const Component = Blocks[x];\r\n              return Component && <Component key={x} />;\r\n            })}\r\n        </div>\r\n      </div>\r\n    </PageContext.Provider>\r\n  );\r\n};\r\n\r\nexport default Onyx;\r\n", "import Onyx from './Onyx';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Onyx;\r\n", "import React, { memo, useContext } from 'react';\r\nimport AppContext from '../../../context/AppContext';\r\n\r\nconst HeadingB = ({ children }) => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  return (\r\n    <h6\r\n      className=\"mb-2 border-b-2 pb-1 font-bold uppercase tracking-wide text-sm\"\r\n      style={{\r\n        color: theme.colors.primary,\r\n        borderColor: theme.colors.primary,\r\n      }}\r\n    >\r\n      {children}\r\n    </h6>\r\n  );\r\n};\r\n\r\nexport default memo(HeadingB);\r\n", "import React, { memo, useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport AppContext from '../../../context/AppContext';\r\nimport * as _  from 'lodash';\r\n\r\nconst ObjectiveB = () => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  return (\r\n    _.size(_.get(data, 'jsonld[\"@graph\"][1].seeks',[]))>0 && (\r\n      <div>\r\n        <hr\r\n          className=\"my-5 opacity-25\"\r\n          style={{ borderColor: theme.colors.background }}\r\n        />\r\n        {_.get(data, 'jsonld[\"@graph\"][1].seeks',[]).map((x, index) => (\r\n              <ReactMarkdown key={\"objetive_\"+index} className=\"text-sm\" source={x.description} />\r\n          ))}\r\n      </div>\r\n    )\r\n  );\r\n};\r\n\r\nexport default memo(ObjectiveB);\r\n", "import React, { useContext, memo } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport ContactA from '../blocks/Contact/ContactA';\r\nimport HeadingB from '../blocks/Heading/HeadingB';\r\nimport AwardsA from '../blocks/Awards/AwardsA';\r\nimport CertificationsA from '../blocks/Certifications/CertificationsA';\r\nimport EducationA from '../blocks/Education/EducationA';\r\nimport HobbiesA from '../blocks/Hobbies/HobbiesA';\r\nimport LanguagesA from '../blocks/Languages/LanguagesA';\r\nimport ProjectsA from '../blocks/Projects/ProjectsA';\r\nimport ReferencesA from '../blocks/References/ReferencesA';\r\nimport SkillsA from '../blocks/Skills/SkillsA';\r\nimport WorkA from '../blocks/Work/WorkA';\r\nimport ObjectiveB from '../blocks/Objective/ObjectiveB';\r\n\r\nimport PageContext from '../../context/PageContext';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport NamesA from '../blocks/Names/NamesA';\r\nimport SubNamesA from '../blocks/Names/SubNamesA';\r\nimport * as _  from 'lodash';\r\n\r\nconst Blocks = {\r\n  work: WorkA,\r\n  education: EducationA,\r\n  projects: ProjectsA,\r\n  awards: AwardsA,\r\n  certifications: CertificationsA,\r\n  skills: SkillsA,\r\n  hobbies: HobbiesA,\r\n  languages: LanguagesA,\r\n  references: ReferencesA,\r\n};\r\n\r\nconst Pikachu = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const layout = _.get(theme,'layoutblocks.pikachu', []);\r\n\r\n  return (\r\n    <PageContext.Provider value={{ data, heading: HeadingB }}>\r\n      <div\r\n        id=\"page\"\r\n        className=\"p-8 rounded\"\r\n        style={{\r\n          fontFamily: theme.font.family,\r\n          color: theme.colors.primary,\r\n          backgroundColor: theme.colors.background,\r\n        }}\r\n      >\r\n        <div className=\"grid grid-cols-12 gap-8\">\r\n          {_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\") !== '' && (\r\n            <div className=\"self-center col-span-4\">\r\n              <img\r\n                className=\"w-48 h-48 rounded-full mx-auto object-cover\"\r\n                src={_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\")}\r\n                alt=\"Resume Photograph\"\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <div\r\n            className={`${\r\n              _.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\") !== '' ? 'col-span-8' : 'col-span-12'\r\n            }`}\r\n          >\r\n            <div\r\n              className=\"h-48 rounded flex flex-col justify-center\"\r\n              style={{\r\n                backgroundColor: theme.colors.primary,\r\n                color: theme.colors.background,\r\n              }}\r\n            >\r\n              <div className=\"flex flex-col justify-center mx-8 my-6\">\r\n                <NamesA data={data} className=\"text-3xl font-bold leading-tight\"/>\r\n                <SubNamesA data={data} />\r\n                <div className=\"text-sm font-medium tracking-wide\">\r\n                  {_.get(data, 'jsonld[\"@graph\"][1].description', \"\")}\r\n                </div>\r\n\r\n                <ObjectiveB data={data} />\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-span-4\">\r\n            <div className=\"grid gap-4\">\r\n              <ContactA />\r\n\r\n              {layout[0] &&\r\n                layout[0].map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-span-8\">\r\n            <div className=\"grid gap-4\">\r\n              {layout[1] &&\r\n                layout[1].map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </PageContext.Provider>\r\n  );\r\n};\r\n\r\nexport default Pikachu;\r\n", "import Pikachu from './Pikachu';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Pikachu;\r\n", "import React, { memo, useContext } from 'react';\r\nimport { get } from 'lodash';\r\nimport PageContext from '../../../context/PageContext';\r\nimport AppContext from '../../../context/AppContext';\r\nimport Icons from '../Icons';\r\nimport { formatDate } from '../../../utils';\r\nimport * as _  from 'lodash';\r\n\r\nconst BirthDateC = () => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const Icon = get(Icons, 'birthday');\r\n\r\n  if (_.get(data,\"jsonld['@graph'][1].birthDate\",\"\")) {\r\n    return (\r\n      <div className=\"text-xs flex items-center\">\r\n        <Icon\r\n          size=\"10px\"\r\n          className=\"mr-2\"\r\n          style={{ color: theme.colors.background }}\r\n        />\r\n        <span className=\"font-medium break-all\">\r\n          {formatDate({\r\n            date: _.get(data,\"jsonld['@graph'][1].birthDate\",\"\"),\r\n            language: data.language || 'en',\r\n            includeDay: true,\r\n          })}\r\n        </span>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport default memo(BirthDateC);\r\n", "import { get } from 'lodash';\r\nimport React, { memo, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { FaCaretRight } from 'react-icons/fa';\r\nimport AppContext from '../../../context/AppContext';\r\nimport { safetyCheck } from '../../../utils';\r\nimport BirthDateC from '../BirthDate/BirthDateC';\r\nimport Icons from '../Icons';\r\nimport * as _  from 'lodash';\r\n\r\nconst ContactItem = ({ value, icon, link }) => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const Icon = get(Icons, icon && icon.toLowerCase(), FaCaretRight);\r\n\r\n  return value ? (\r\n    <div className=\"flex items-center\">\r\n      <Icon\r\n        size=\"10px\"\r\n        className=\"mr-2\"\r\n        style={{ color: theme.colors.background }}\r\n      />\r\n      {link ? (\r\n        <a href={link} target=\"_blank\" rel=\"noopener noreferrer\">\r\n          <span className=\"font-medium break-all\">{value}</span>\r\n        </a>\r\n      ) : (\r\n        <span className=\"font-medium break-all\">{value}</span>\r\n      )}\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nconst ContactB = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  return (\r\n    <div className=\"text-xs grid gap-2\">\r\n      <ContactItem\r\n        label={_.get(data,'profile.phone.heading', t(\"Phone\"))}\r\n        value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'telephone', \"\")}\r\n        icon=\"phone\"\r\n        link={`tel:${_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'telephone', \"\")}`}\r\n      />\r\n      <ContactItem\r\n        label={_.get(data,'profile.website.heading', t(\"Website\"))}\r\n        value={_.get(data,'jsonld[\"@graph\"][1].sameAs[0]',\"\")}\r\n        icon=\"website\"\r\n        link={_.get(data,'jsonld[\"@graph\"][1].sameAs[0]',\"\")}\r\n      />\r\n      <ContactItem\r\n        label={_.get(data,'profile.email.heading' ,t(\"Email\"))}\r\n        value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'email', \"\")}\r\n        icon=\"email\"\r\n        link={`mailto:${_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'email', \"\")}`}\r\n      />\r\n\r\n      <BirthDateC />\r\n\r\n      {safetyCheck(data.social) &&\r\n        data.social.items.map((x) => (\r\n          <ContactItem\r\n            key={x.id}\r\n            value={x.username}\r\n            icon={x.network}\r\n            link={x.url}\r\n          />\r\n        ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(ContactB);\r\n", "import React, { memo } from 'react';\r\n\r\nconst HeadingC = ({ children }) => (\r\n  <h6 className=\"font-bold text-xs uppercase tracking-wide mb-1\">{children}</h6>\r\n);\r\n\r\nexport default memo(HeadingC);\r\n", "import React, { memo, useContext } from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst ReferenceItem = (x) => (\r\n  <div key={_.get(x, '@id', uuidv4())} className=\"flex flex-col\">\r\n    <h6 className=\"font-semibold text-sm\">{_.get(x, 'interactionType.participant.givenName', '')} {_.get(x, 'interactionType.participant.familyName', '')}</h6>\r\n    <span className=\"text-xs\">{_.get(x, 'interactionType.participant.jobTitle', '')}</span>\r\n    <span className=\"text-xs\">{_.get(x, 'interactionType.participant.telephone', '')}</span>\r\n    <span className=\"text-xs\">{_.get(x, 'interactionType.participant.email', '')}</span>\r\n    {_.get(x, 'result[0].reviewRating.ratingExplanation', '')!=='' && (\r\n      <ReactMarkdown className=\"markdown mt-2 text-sm\" source={_.get(x, 'result[0].reviewRating.ratingExplanation', '')} />\r\n    )}\r\n  </div>\r\n);\r\n\r\nconst ReferencesB = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return safetyCheck(data.references) ? (\r\n    <div>\r\n      <Heading>{data.references.heading}</Heading>\r\n      <div className=\"grid gap-4\">\r\n        {_.get(data.jsonld[\"@graph\"][1], 'interactionStatistic', []).filter(x => _.get(x, 'disambiguatingDescription', '')=== 'Reference').map(ReferenceItem)}\r\n      </div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(ReferencesB);\r\n", "import React, { memo, useContext } from 'react';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n  \r\nconst NamesB = ({data, className=\"text-2xl font-bold leading-tight\"}, style={}) => (\r\n\t<>\r\n\t\t<h1 className={className} style={style}>\r\n\t\t\t  {(Array.isArray(data.jsonld['@graph'][1].givenName)) ? (_.get(data,\"jsonld['@graph'][1].givenName[0]['@value']\",\"\")) : (data.jsonld['@graph'][1].givenName)} \r\n\t\t</h1>\r\n\t\t<h1 className={className} style={style}>\r\n\t\t\t  {(Array.isArray(data.jsonld['@graph'][1].familyName)) ? (_.get(data,\"jsonld['@graph'][1].familyName[0]['@value']\", \"\")) : (data.jsonld['@graph'][1].familyName)}\r\n\t\t</h1>\r\n\t</>\r\n  );\r\n\r\nexport default memo(NamesB);\r\n", "import React, { useContext, memo } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { hasAddress, hexToRgb } from '../../utils';\r\nimport AwardsA from '../blocks/Awards/AwardsA';\r\nimport CertificationsA from '../blocks/Certifications/CertificationsA';\r\nimport ContactB from '../blocks/Contact/ContactB';\r\nimport EducationA from '../blocks/Education/EducationA';\r\nimport HeadingC from '../blocks/Heading/HeadingC';\r\nimport HobbiesA from '../blocks/Hobbies/HobbiesA';\r\nimport LanguagesA from '../blocks/Languages/LanguagesA';\r\nimport ObjectiveA from '../blocks/Objective/ObjectiveA';\r\nimport ProjectsA from '../blocks/Projects/ProjectsA';\r\nimport ReferencesB from '../blocks/References/ReferencesB';\r\nimport SkillsA from '../blocks/Skills/SkillsA';\r\nimport WorkA from '../blocks/Work/WorkA';\r\n\r\nimport PageContext from '../../context/PageContext';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport NamesB from '../blocks/Names/NamesB';\r\nimport SubNamesA from '../blocks/Names/SubNamesA';\r\nimport AddressA from '../blocks/Address/AddressA';\r\nimport * as _  from 'lodash';\r\n\r\nconst Blocks = {\r\n  objective: ObjectiveA,\r\n  work: WorkA,\r\n  education: EducationA,\r\n  projects: ProjectsA,\r\n  awards: AwardsA,\r\n  certifications: CertificationsA,\r\n  skills: SkillsA,\r\n  hobbies: HobbiesA,\r\n  languages: LanguagesA,\r\n  references: ReferencesB,\r\n};\r\n\r\nconst Gengar = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const layout = _.get(theme,'layoutblocks.gengar', []);\r\n  \r\n  const { r, g, b } = hexToRgb(theme.colors.primary) || {};\r\n\r\n  const Photo = () =>\r\n    _.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\") !== '' && (\r\n      <img\r\n        className=\"w-24 h-24 rounded-full mr-4 object-cover border-4\"\r\n        style={{\r\n          borderColor: theme.colors.background,\r\n        }}\r\n        src={_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\")}\r\n        alt=\"Resume Photograph\"\r\n      />\r\n    );\r\n\r\n  const Profile = () => (\r\n    <div>\r\n      <NamesB data={data} className=\"text-2xl font-bold leading-tight\"/>\r\n      <SubNamesA data={data} />\r\n      <div className=\"text-xs font-medium mt-2\">{_.get(data, 'jsonld[\"@graph\"][1].description', \"\")}</div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <PageContext.Provider value={{ data, heading: HeadingC }}>\r\n      <div\r\n        id=\"page\"\r\n        className=\"rounded\"\r\n        style={{\r\n          fontFamily: theme.font.family,\r\n          color: theme.colors.primary,\r\n          backgroundColor: theme.colors.background,\r\n        }}\r\n      >\r\n        <div className=\"grid grid-cols-12\">\r\n          <div\r\n            className=\"col-span-4 px-6 py-8\"\r\n            style={{\r\n              backgroundColor: theme.colors.primary,\r\n              color: theme.colors.background,\r\n            }}\r\n          >\r\n            <div className=\"flex items-center\">\r\n              <Photo />\r\n              <Profile />\r\n            </div>\r\n            \r\n            <AddressA data={data} mainclassName=\"flex flex-col mt-4 text-xs\" hclassName=\"font-bold text-xs uppercase tracking-wide mb-1\" subclassName=\"\" />\r\n\r\n            <hr\r\n              className=\"w-1/4 my-5 opacity-25\"\r\n              style={{ borderColor: theme.colors.background }}\r\n            />\r\n\r\n            <h6 className=\"font-bold text-xs uppercase tracking-wide mb-2\">\r\n              {data.contacts.heading || \"Contact\"}\r\n            </h6>\r\n            <ContactB />\r\n          </div>\r\n\r\n          <div\r\n            className=\"col-span-8 px-6 py-8\"\r\n            style={{ backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)` }}\r\n          >\r\n            <div className=\"grid gap-6 items-center\">\r\n              {layout[0] &&\r\n                layout[0].map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            className=\"col-span-4 px-6 py-8\"\r\n            style={{ backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)` }}\r\n          >\r\n            <div className=\"grid gap-6\">\r\n              {layout[1] &&\r\n                layout[1].map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-span-8 px-6 py-8\">\r\n            <div className=\"grid gap-6\">\r\n              {layout[2] &&\r\n                layout[2].map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </PageContext.Provider>\r\n  );\r\n};\r\n\r\nexport default Gengar;\r\n", "import Gengar from './Gengar';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Gengar;\r\n", "import React, { memo, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport PageContext from '../../../context/PageContext';\r\nimport AppContext from '../../../context/AppContext';\r\nimport { formatDate } from '../../../utils';\r\nimport * as _  from 'lodash';\r\n\r\nconst BirthDateA = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  if (_.get(data,\"jsonld['@graph'][1].birthDate\",\"\")) {\r\n    return (\r\n      <div className=\"text-xs\">\r\n        <h6 className=\"capitalize font-semibold\">\r\n          {data.profile.birthDate.heading || \"Birth Date\"}\r\n        </h6>\r\n        <div>\r\n          <span>\r\n            {formatDate({\r\n              date: _.get(data,\"jsonld['@graph'][1].birthDate\",\"\"),\r\n              language: data.language || 'en',\r\n              includeDay: true,\r\n            })}\r\n          </span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport default memo(BirthDateA);\r\n", "import React, { memo, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport AppContext from '../../../context/AppContext';\r\nimport { hasAddress, safetyCheck } from '../../../utils';\r\nimport BirthDateA from '../BirthDate/BirthDateA';\r\nimport AddressA from '../Address/AddressA';\r\nimport * as _  from 'lodash';\r\n\r\nconst ContactItem = ({ value, label, link }) =>\r\n  value ? (\r\n    <div className=\"flex flex-col\">\r\n      <h6 className=\"capitalize font-semibold\">{label}</h6>\r\n      {link ? (\r\n        <a href={link} target=\"_blank\" rel=\"noopener noreferrer\">\r\n          <span className=\"font-medium break-all\">{value}</span>\r\n        </a>\r\n      ) : (\r\n        <span className=\"font-medium break-all\">{value}</span>\r\n      )}\r\n    </div>\r\n  ) : null;\r\n  \r\nconst ContactC = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  return (\r\n    <div className=\"text-xs grid gap-2\">\r\n        <AddressA data={data} />\r\n\r\n      <ContactItem\r\n        label={_.get(data,'profile.phone.heading', t(\"Phone\"))}\r\n        value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'telephone', \"\")}\r\n        link={`tel:${_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'telephone', \"\")}`}\r\n      />\r\n      <ContactItem\r\n        label={_.get(data,'profile.website.heading', t(\"Website\"))}\r\n        value={_.get(data,'jsonld[\"@graph\"][1].sameAs[0]',\"\")}\r\n        link={_.get(data,'jsonld[\"@graph\"][1].sameAs[0]',\"\")}\r\n      />\r\n      <ContactItem\r\n        label={_.get(data,'profile.email.heading' ,t(\"Email\"))}\r\n        value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'email', \"\")}\r\n        link={`mailto:${_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'email', \"\")}`}\r\n      />\r\n\r\n      <BirthDateA />\r\n\r\n      {safetyCheck(data.social) &&\r\n        data.social.items.map((x) => (\r\n          <ContactItem\r\n            key={x.id}\r\n            value={x.username}\r\n            label={x.network}\r\n            link={x.url}\r\n          />\r\n        ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(ContactC);\r\n", "import React, { memo, useContext } from 'react';\r\nimport AppContext from '../../../context/AppContext';\r\nimport { hexToRgb } from '../../../utils';\r\n\r\nconst HeadingD = ({ children }) => {\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const { r, g, b } = hexToRgb(theme.colors.primary) || {};\r\n\r\n  return (\r\n    <h6\r\n      className=\"py-1 px-4 rounded-r leading-loose font-bold text-xs uppercase tracking-wide mb-3\"\r\n      style={{\r\n        marginLeft: '-15px',\r\n        color: theme.colors.background,\r\n        backgroundColor: `rgba(${r - 40}, ${g - 40}, ${b - 40}, 0.8)`,\r\n      }}\r\n    >\r\n      {children}\r\n    </h6>\r\n  );\r\n};\r\n\r\nexport default memo(HeadingD);\r\n", "import React, { useContext, memo } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport AwardsA from '../blocks/Awards/AwardsA';\r\nimport CertificationsA from '../blocks/Certifications/CertificationsA';\r\nimport ContactC from '../blocks/Contact/ContactC';\r\nimport EducationA from '../blocks/Education/EducationA';\r\nimport HeadingD from '../blocks/Heading/HeadingD';\r\nimport HobbiesA from '../blocks/Hobbies/HobbiesA';\r\nimport LanguagesA from '../blocks/Languages/LanguagesA';\r\nimport ObjectiveA from '../blocks/Objective/ObjectiveA';\r\nimport ProjectsA from '../blocks/Projects/ProjectsA';\r\nimport ReferencesA from '../blocks/References/ReferencesA';\r\nimport SkillsA from '../blocks/Skills/SkillsA';\r\nimport WorkA from '../blocks/Work/WorkA';\r\n\r\nimport PageContext from '../../context/PageContext';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport NamesA from '../blocks/Names/NamesA';\r\nimport SubNamesA from '../blocks/Names/SubNamesA';\r\nimport * as _  from 'lodash';\r\n\r\nconst Blocks = {\r\n  objective: ObjectiveA,\r\n  work: WorkA,\r\n  education: EducationA,\r\n  projects: ProjectsA,\r\n  awards: AwardsA,\r\n  certifications: CertificationsA,\r\n  skills: SkillsA,\r\n  hobbies: HobbiesA,\r\n  languages: LanguagesA,\r\n  references: ReferencesA,\r\n};\r\n\r\nconst Castform = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const layout = _.get(theme,'layoutblocks.castform', []);\r\n\r\n  const Photo = () =>\r\n    _.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\") !== '' && (\r\n      <img\r\n        className=\"w-32 h-32 rounded-full\"\r\n        style={{\r\n          borderWidth: 6,\r\n          borderColor: theme.colors.background,\r\n        }}\r\n        src={_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\")}\r\n        alt=\"Resume Photograph\"\r\n      />\r\n    );\r\n\r\n  const Profile = () => (\r\n    <div>\r\n      <NamesA data={data} className=\"text-2xl font-bold\"/>\r\n      <SubNamesA data={data} />\r\n      <h5>{_.get(data, 'jsonld[\"@graph\"][1].description', \"\")}</h5>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <PageContext.Provider value={{ data, heading: HeadingD }}>\r\n      <div\r\n        id=\"page\"\r\n        className=\"rounded\"\r\n        style={{\r\n          fontFamily: theme.font.family,\r\n          color: theme.colors.primary,\r\n          backgroundColor: theme.colors.background,\r\n        }}\r\n      >\r\n        <div className=\"grid grid-cols-12\">\r\n          <div\r\n            className=\"col-span-4 py-8 pr-8 pl-5\"\r\n            style={{\r\n              color: theme.colors.background,\r\n              backgroundColor: theme.colors.primary,\r\n            }}\r\n          >\r\n            <div className=\"grid gap-4\">\r\n              <Photo />\r\n              <Profile />\r\n\r\n              <div>\r\n                <HeadingD>{data.profile.heading}</HeadingD>\r\n                <ContactC />\r\n              </div>\r\n\r\n              {layout[0] &&\r\n                layout[0].map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n          <div className=\"col-span-8 py-8 pr-8 pl-5\">\r\n            <div className=\"grid gap-4\">\r\n              {layout[1] &&\r\n                layout[1].map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </PageContext.Provider>\r\n  );\r\n};\r\n\r\nexport default Castform;\r\n", "import Castform from './Castform';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Castform;\r\n", "import React, { memo, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { MdFlare } from 'react-icons/md';\r\nimport AppContext from '../../../context/AppContext';\r\nimport { hasAddress, safetyCheck } from '../../../utils';\r\nimport BirthDateA from '../BirthDate/BirthDateA';\r\nimport AddressA from '../Address/AddressA';\r\nimport * as _  from 'lodash';\r\n\r\nconst ContactItem = ({ value, label, link }) =>\r\n  value ? (\r\n    <div className=\"flex flex-col\">\r\n      <h6 className=\"capitalize font-semibold\">{label}</h6>\r\n      {link ? (\r\n        <a href={link} target=\"_blank\" rel=\"noopener noreferrer\">\r\n          <span className=\"font-medium break-all\">{value}</span>\r\n        </a>\r\n      ) : (\r\n        <span className=\"font-medium break-all\">{value}</span>\r\n      )}\r\n    </div>\r\n  ) : null;\r\n\r\nconst ContactD = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n\r\n  return (\r\n    <div\r\n      className=\"my-4 relative w-full border-2 grid gap-2 text-center text-xs py-5\"\r\n      style={{\r\n        borderColor: theme.colors.primary,\r\n      }}\r\n    >\r\n      <div\r\n        className=\"absolute text-center\"\r\n        style={{\r\n          top: '-11px',\r\n          left: '50%',\r\n          marginLeft: '-10px',\r\n          color: theme.colors.primary,\r\n        }}\r\n      >\r\n        <MdFlare size=\"20px\" />\r\n      </div>\r\n\r\n        <AddressA data={data}  />\r\n\r\n      <ContactItem\r\n        label={_.get(data,'profile.phone.heading', t(\"Phone\"))}\r\n        value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'telephone', \"\")}\r\n        link={`tel:${_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'telephone', \"\")}`}\r\n      />\r\n      <ContactItem\r\n        label={_.get(data,'profile.website.heading', t(\"Website\"))}\r\n        value={_.get(data,'jsonld[\"@graph\"][1].sameAs[0]',\"\")}\r\n        link={_.get(data,'jsonld[\"@graph\"][1].sameAs[0]',\"\")}\r\n      />\r\n      <ContactItem\r\n        label={_.get(data,'profile.email.heading' ,t(\"Email\"))}\r\n        value={_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'email', \"\")}\r\n        link={`mailto:${_.get(_.find(data.jsonld[\"@graph\"][1].contactPoint,{contactType:\"Preferred\"}), 'email', \"\")}`}\r\n      />\r\n\r\n      <BirthDateA />\r\n\r\n      {safetyCheck(data.social) &&\r\n        data.social.items.map((x) => (\r\n          <ContactItem\r\n            key={x.id}\r\n            value={x.username}\r\n            label={x.network}\r\n            link={x.url}\r\n          />\r\n        ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default memo(ContactD);\r\n", "import React, { useContext, memo } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { hexToRgb } from '../../utils';\r\nimport AwardsA from '../blocks/Awards/AwardsA';\r\nimport CertificationsA from '../blocks/Certifications/CertificationsA';\r\nimport ContactD from '../blocks/Contact/ContactD';\r\nimport EducationA from '../blocks/Education/EducationA';\r\nimport HeadingB from '../blocks/Heading/HeadingB';\r\nimport HobbiesA from '../blocks/Hobbies/HobbiesA';\r\nimport LanguagesA from '../blocks/Languages/LanguagesA';\r\nimport ObjectiveA from '../blocks/Objective/ObjectiveA';\r\nimport ProjectsA from '../blocks/Projects/ProjectsA';\r\nimport ReferencesA from '../blocks/References/ReferencesA';\r\nimport SkillsA from '../blocks/Skills/SkillsA';\r\nimport WorkA from '../blocks/Work/WorkA';\r\n\r\nimport PageContext from '../../context/PageContext';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport NamesB from '../blocks/Names/NamesB';\r\nimport SubNamesA from '../blocks/Names/SubNamesA';\r\nimport * as _  from 'lodash';\r\n\r\nconst Blocks = {\r\n  objective: ObjectiveA,\r\n  work: WorkA,\r\n  education: EducationA,\r\n  projects: ProjectsA,\r\n  awards: AwardsA,\r\n  certifications: CertificationsA,\r\n  skills: SkillsA,\r\n  hobbies: HobbiesA,\r\n  languages: LanguagesA,\r\n  references: ReferencesA,\r\n};\r\n\r\nconst Glalie = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const layout = _.get(theme,'layoutblocks.glalie', []);\r\n  \r\n  const { r, g, b } = hexToRgb(theme.colors.primary) || {};\r\n\r\n  const Profile = () => (\r\n    <div className=\"grid gap-2 text-center\">\r\n      {_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\") !== '' && (\r\n        <img\r\n          className=\"w-40 h-40 rounded-full mx-auto\"\r\n          src={_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\")}\r\n          alt=\"Resume Photograph\"\r\n        />\r\n      )}\r\n      <div className=\"text-4xl font-bold leading-none\">\r\n        <NamesB data={data} className=\"\"/>\r\n        <SubNamesA data={data} />\r\n      </div>\r\n      <div className=\"tracking-wide text-xs uppercase font-medium\">\r\n        {_.get(data, 'jsonld[\"@graph\"][1].description', \"\")}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <PageContext.Provider value={{ data, heading: HeadingB }}>\r\n      <div\r\n        id=\"page\"\r\n        className=\"rounded\"\r\n        style={{\r\n          fontFamily: theme.font.family,\r\n          color: theme.colors.primary,\r\n          backgroundColor: theme.colors.background,\r\n        }}\r\n      >\r\n        <div className=\"grid grid-cols-12\">\r\n          <div\r\n            className=\"col-span-4\"\r\n            style={{\r\n              backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)`,\r\n            }}\r\n          >\r\n            <div className=\"grid gap-6 text-center p-8\">\r\n              <Profile />\r\n              <ContactD />\r\n\r\n              {layout[0] &&\r\n                layout[0].map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"col-span-8\">\r\n            <div className=\"grid gap-4 p-8\">\r\n              {layout[1] &&\r\n                layout[1].map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </PageContext.Provider>\r\n  );\r\n};\r\n\r\nexport default Glalie;\r\n", "import Glalie from './Glalie';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Glalie;\r\n", "import React, { memo } from 'react';\r\n\r\nconst HeadingC = ({ children }) => (\r\n  <h6 className=\"my-2 text-md uppercase font-semibold tracking-wider pb-1 border-b-2 border-gray-800\">\r\n    {children}\r\n  </h6>\r\n);\r\n\r\nexport default memo(HeadingC);\r\n", "import React, { memo, useContext } from 'react';\r\nimport PageContext from '../../../context/PageContext';\r\nimport { safetyCheck } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nconst LanguageItem = (x) => (\r\n  <div key={_.get(x,'@id', uuidv4())} className=\"flex flex-col\">\r\n    <h6 className=\"font-semibold text-sm\">{_.get(x, 'name', '')}</h6>\r\n    <span className=\"text-xs\">{x.fluency}</span>\r\n  </div>\r\n);\r\n\r\nconst LanguagesB = () => {\r\n  const { data, heading: Heading } = useContext(PageContext);\r\n\r\n  return (data.languages &&\r\n    data.languages.enable && (_.get(data, 'jsonld[\"@graph\"][1].knowsLanguage',[]).length > 0)) ? (\r\n    <div>\r\n      <Heading>{data.languages.heading}</Heading>\r\n      <div className=\"grid gap-2\">{_.get(data, 'jsonld[\"@graph\"][1].knowsLanguage', []).filter(x => _.get(x, 'name', '') !== '').map(LanguageItem)}</div>\r\n    </div>\r\n  ) : null;\r\n};\r\n\r\nexport default memo(LanguagesB);\r\n", "import React, { useContext, memo } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { hexToRgb } from '../../utils';\r\nimport AwardsA from '../blocks/Awards/AwardsA';\r\nimport CertificationsA from '../blocks/Certifications/CertificationsA';\r\nimport ContactC from '../blocks/Contact/ContactC';\r\nimport EducationA from '../blocks/Education/EducationA';\r\nimport HeadingE from '../blocks/Heading/HeadingE';\r\nimport HobbiesA from '../blocks/Hobbies/HobbiesA';\r\nimport LanguagesB from '../blocks/Languages/LanguagesB';\r\nimport ObjectiveA from '../blocks/Objective/ObjectiveA';\r\nimport ProjectsA from '../blocks/Projects/ProjectsA';\r\nimport ReferencesA from '../blocks/References/ReferencesA';\r\nimport SkillsA from '../blocks/Skills/SkillsA';\r\nimport WorkA from '../blocks/Work/WorkA';\r\n\r\nimport PageContext from '../../context/PageContext';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport NamesA from '../blocks/Names/NamesA';\r\nimport SubNamesA from '../blocks/Names/SubNamesA';\r\nimport * as _  from 'lodash';\r\n\r\nconst Blocks = {\r\n  objective: ObjectiveA,\r\n  work: WorkA,\r\n  education: EducationA,\r\n  projects: ProjectsA,\r\n  awards: AwardsA,\r\n  certifications: CertificationsA,\r\n  skills: SkillsA,\r\n  hobbies: HobbiesA,\r\n  languages: LanguagesB,\r\n  references: ReferencesA,\r\n};\r\n\r\nconst Celebi = () => {\r\n  const { t } = useTranslation();\r\n  const context = useContext(AppContext);\r\n  const { state } = context;\r\n  const { data, theme } = state;\r\n  const layout = _.get(theme,'layoutblocks.celebi', []);\r\n  \r\n  const { r, g, b } = hexToRgb(theme.colors.accent) || {};\r\n\r\n\r\n  const styles = {\r\n    header: {\r\n      position: 'absolute',\r\n      left: 0,\r\n      right: 0,\r\n      display: 'flex',\r\n      flexDirection: 'column',\r\n      justifyContent: 'center',\r\n      color: 'white',\r\n      backgroundColor: theme.colors.primary,\r\n      height: '160px',\r\n      paddingLeft: '275px',\r\n    },\r\n    leftSection: {\r\n      backgroundColor: `rgba(${r}, ${g}, ${b}, 0.1)`,\r\n    },\r\n    rightSection: {\r\n      marginTop: '160px',\r\n    },\r\n  };\r\n\r\n  const Photo = () =>\r\n    (_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\") !== '' && (\r\n      <div className=\"relative z-40\">\r\n        <img\r\n          className=\"w-full object-cover object-center\"\r\n          src={_.get(data, 'jsonld[\"@graph\"][1].image.contentUrl', \"\")}\r\n          alt=\"Person Photograph\"\r\n          style={{\r\n            height: '160px',\r\n          }}\r\n        />\r\n      </div>\r\n    )) || (\r\n      <div className=\"relative z-40\">\r\n        <div style={{\r\n            height: '160px',\r\n          }}>\r\n        </div>\r\n      </div>\r\n    );\r\n  \r\n  const Profile = () => (\r\n    <div style={styles.header}>\r\n        <NamesA data={data} />\r\n        <SubNamesA data={data} />\r\n      <h6 className=\"text-lg tracking-wider uppercase\">\r\n        {_.get(data, 'jsonld[\"@graph\"][1].description', \"\")}\r\n      </h6>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <PageContext.Provider value={{ data, heading: HeadingE }}>\r\n      <div\r\n        id=\"page\"\r\n        className=\"relative rounded\"\r\n        style={{\r\n          fontFamily: theme.font.family,\r\n          color: theme.colors.text,\r\n          backgroundColor: theme.colors.background,\r\n        }}\r\n      >\r\n        <div className=\"grid grid-cols-12 gap-8\">\r\n          <div className=\"col-span-4 ml-8\" style={styles.leftSection}>\r\n            <Photo />\r\n\r\n            <div className=\"text-center grid gap-4 mt-4 mb-8 mx-6\">\r\n              <div>\r\n                <HeadingE>{data.profile.heading}</HeadingE>\r\n                <div className=\"relative w-full grid gap-4 text-xs\">\r\n                  <ContactC />\r\n                </div>\r\n              </div>\r\n\r\n              {_.get(layout,\"[0]\", []) &&\r\n                _.get(layout,\"[0]\", []).map((x) => {\r\n                  const Component = Blocks[x];\r\n                  return Component && <Component key={x} />;\r\n                })}\r\n            </div>\r\n          </div>\r\n          <div className=\"col-span-8\">\r\n            <Profile />\r\n\r\n            <div className=\"relative\" style={styles.rightSection}>\r\n              <div className=\"grid gap-4 mt-4 mb-8 mr-8\">\r\n                {_.get(layout,\"[1]\", []) &&\r\n                  _.get(layout,\"[1]\", []).map((x) => {\r\n                    const Component = Blocks[x];\r\n                    return Component && <Component key={x} />;\r\n                  })}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </PageContext.Provider>\r\n  );\r\n};\r\n\r\nexport default Celebi;\r\n", "import Onyx, { Image as OnyxPreview } from './onyx';\r\nimport Pikachu, { Image as PikachuPreview } from './pikachu';\r\nimport Gengar, { Image as GengarPreview } from './gengar';\r\nimport Castform, { Image as CastformPreview } from './castform';\r\nimport Glalie, { Image as GlaliePreview } from './glalie';\r\nimport Celebi, { Image as CelebiPreview } from './celebi';\r\n\r\nexport default [\r\n  {\r\n    key: 'onyx',\r\n    name: 'Onyx',\r\n    component: Onyx,\r\n    preview: OnyxPreview,\r\n  },\r\n  {\r\n    key: 'pikachu',\r\n    name: '<PERSON><PERSON><PERSON>',\r\n    component: <PERSON><PERSON>chu,\r\n    preview: PikachuPreview,\r\n  },\r\n  {\r\n    key: 'gengar',\r\n    name: 'Gengar',\r\n    component: Gengar,\r\n    preview: GengarPreview,\r\n  },\r\n  {\r\n    key: 'castform',\r\n    name: 'Castform',\r\n    component: Castform,\r\n    preview: CastformPreview,\r\n  },\r\n  {\r\n    key: 'glalie',\r\n    name: 'Glal<PERSON>',\r\n    component: Glalie,\r\n    preview: GlaliePreview,\r\n  },\r\n  {\r\n    key: 'celebi',\r\n    name: '<PERSON><PERSON><PERSON>',\r\n    component: <PERSON><PERSON><PERSON>,\r\n    preview: CelebiPreview,\r\n  },\r\n];\r\n", "import Celebi from './<PERSON><PERSON><PERSON>';\r\nimport image from './preview.png';\r\n\r\nexport const Image = image;\r\nexport default Celebi;\r\n", "import React from 'react';\r\n\r\nimport templates from '../../../templates';\r\n\r\nconst TemplatesTab = ({ theme, onChange }) => {\r\n  return (\r\n    <div className=\"grid grid-cols-2 gap-6\">\r\n      {templates.map(x => (\r\n        <div key={x.key} className=\"text-center\" onClick={() => {if(!x.disable){onChange('theme.layout', x.key);}else{alert(\"This template is under develoment\");}}}>\r\n          <img\r\n            className={`rounded cursor-pointer object-cover border shadow hover:shadow-md ${\r\n              theme.layout.toLowerCase() === x.key\r\n                ? 'border-gray-600 hover:border-gray-600'\r\n                : 'border-transparent '\r\n            } hover:border-gray-500 cursor-pointer`}\r\n            src={x.preview}\r\n            alt={x.name}\r\n          />\r\n          <p className=\"mt-1 text-sm font-medium\">{x.name}</p>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TemplatesTab;\r\n", "import React from 'react';\r\nimport { toast } from 'react-toastify';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport TextField from '../../../shared/TextField';\r\nimport { copyToClipboard } from '../../../utils';\r\n\r\nconst colorOptions = [\r\n  '#f44336',\r\n  '#E91E63',\r\n  '#9C27B0',\r\n  '#673AB7',\r\n  '#3F51B5',\r\n  '#2196F3',\r\n  '#03A9F4',\r\n  '#00BCD4',\r\n  '#009688',\r\n  '#4CAF50',\r\n  '#8BC34A',\r\n  '#CDDC39',\r\n  '#FFEB3B',\r\n  '#FFC107',\r\n  '#FF9800',\r\n  '#FF5722',\r\n  '#795548',\r\n  '#9E9E9E',\r\n  '#607D8B',\r\n  '#FAFAFA',\r\n  '#212121',\r\n  '#263238',\r\n];\r\n\r\nconst ColorsTab = ({ theme, onChange }) => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  const copyColorToClipboard = color => {\r\n    copyToClipboard(color);\r\n    toast(t('colors.clipboardCopyAction', { color }), {\r\n      bodyClassName: 'text-center text-gray-800 py-2',\r\n    });\r\n    onChange('theme.colors.accent', color);\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"uppercase tracking-wide text-gray-600 text-xs font-semibold mb-4\">\r\n        {t('colors.colorOptions')}\r\n      </div>\r\n      <div className=\"mb-6 grid grid-cols-8 col-gap-2 row-gap-3\">\r\n        {colorOptions.map(color => (\r\n          <div\r\n            key={color}\r\n            className=\"cursor-pointer rounded-full border border-gray-200 h-6 w-6 hover:opacity-75\"\r\n            style={{ backgroundColor: color }}\r\n            onClick={() => copyColorToClipboard(color)}\r\n          />\r\n        ))}\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"my-6 grid grid-cols-6 items-end\">\r\n        <div\r\n          className=\"rounded-full w-8 h-8 mb-2 border-2\"\r\n          style={{ backgroundColor: theme.colors.primary }}\r\n        />\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            label={t('colors.primaryColor')}\r\n            placeholder=\"#FFFFFF\"\r\n            value={theme.colors.primary}\r\n            onChange={v => onChange('theme.colors.primary', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"my-6 grid grid-cols-6 items-end\">\r\n        <div\r\n          className=\"rounded-full w-8 h-8 mb-2 border-2\"\r\n          style={{ backgroundColor: theme.colors.accent }}\r\n        />\r\n        <div className=\"col-span-5\">\r\n          <TextField\r\n            label={t('colors.accentColor')}\r\n            placeholder=\"#FFFFFF\"\r\n            value={theme.colors.accent}\r\n            onChange={v => onChange('theme.colors.accent', v)}\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ColorsTab;\r\n", "import React from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport TextField from '../../../shared/TextField';\r\n\r\nconst fontOptions = [\r\n  'Lato',\r\n  'Montserrat',\r\n  'Nunito',\r\n  'Open Sans',\r\n  'Raleway',\r\n  'Rubik',\r\n  'Source Sans Pro',\r\n  'Titillium Web',\r\n  'Ubuntu',\r\n];\r\n\r\nconst FontsTab = ({ theme, onChange }) => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 gap-6\">\r\n      {fontOptions.map(x => (\r\n        <div\r\n          key={x}\r\n          style={{ fontFamily: x }}\r\n          onClick={() => onChange('theme.font.family', x)}\r\n          className={`w-full rounded border py-4 shadow text-xl text-center ${\r\n            theme.font.family === x ? 'border-gray-500' : 'border-transparent'\r\n          } hover:border-gray-400 cursor-pointer`}\r\n        >\r\n          {x}\r\n        </div>\r\n      ))}\r\n\r\n      <div>\r\n        <TextField\r\n          className=\"mb-3\"\r\n          label={t('fonts.fontFamily.label')}\r\n          placeholder=\"Avenir Next\"\r\n          value={theme.font.family}\r\n          onChange={v => onChange('theme.font.family', v)}\r\n        />\r\n\r\n        <p className=\"text-gray-800 text-xs\">{t('fonts.fontFamily.helpText')}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FontsTab;\r\n", "/* eslint-disable new-cap */\r\n/* eslint-disable jsx-a11y/anchor-has-content */\r\n/* eslint-disable jsx-a11y/anchor-is-valid */\r\n\r\nimport React, { useRef, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport PageContext from '../../../context/PageContext';\r\nimport { importJson } from '../../../utils';\r\n\r\nimport * as _  from 'lodash';\r\nimport * as JSZip from 'jszip';\r\nimport { saveAs } from 'file-saver';\r\n\r\nconst ActionsTab = ({ data, theme, dispatch }) => {\r\n  const pageContext = useContext(PageContext);\r\n  const { setPrintDialogOpen } = pageContext;\r\n  const { t } = useTranslation('rightSidebar');\r\n  const fileInputRef = useRef(null);\r\n  \r\n  const exportToJsonld = () => {\r\n    const backupObj = { data, theme };\r\n    let dataclone = _.cloneDeep(data.jsonld);\r\n    let javascript_part1 = '<script type=\"application/ld+json\">'+JSON.stringify(dataclone)+\"</script>\";\r\n    _.set(dataclone['@graph'][1], \"@context\", \"http://schema.org/\");\r\n    let javascript_part2 = '<script type=\"application/ld+json\">'+JSON.stringify(dataclone['@graph'][1])+\"</script>\";\r\n    \r\n    let javascript = javascript_part1 + javascript_part2;\r\n    var zip = new JSZip();\r\n    zip.file(\"script.js\", javascript);\r\n    zip.file(\"resume.json\", JSON.stringify(backupObj));\r\n    zip.generateAsync({type:\"blob\"})\r\n    .then(function(content) {\r\n        saveAs(content, \"jsonldresume.zip\");\r\n    });\r\n  };\r\n\r\n  const loadDemoData = () => {\r\n    dispatch({ type: 'load_demo_data' });\r\n    dispatch({ type: 'save_data' });\r\n  };\r\n\r\n  const resetEverything = () => {\r\n    dispatch({ type: 'reset' });\r\n    dispatch({ type: 'save_data' });\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"shadow text-center text-sm p-5\">{t('actions.disclaimer')}</div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('actions.importExport.heading')}</h6>\r\n\r\n        <p className=\"text-sm\">{t('actions.importExport.body')}</p>\r\n\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          className=\"hidden\"\r\n          onChange={(e) => importJson(e, dispatch)}\r\n        />\r\n        <a id=\"downloadAnchor\" className=\"hidden\" />\r\n\r\n        <div className=\"mt-4 grid grid-cols-2 col-gap-6\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => fileInputRef.current.click()}\r\n            className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">publish</i>\r\n              <span className=\"text-sm\">{t('actions.importExport.buttons.import')}</span>\r\n            </div>\r\n          </button>\r\n\r\n          <button\r\n            type=\"button\"\r\n            onClick={exportToJsonld}\r\n            className=\"bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">get_app</i>\r\n              <span className=\"text-sm\">{t('actions.importExport.buttons.export')}</span>\r\n            </div>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('actions.downloadResume.heading')}</h6>\r\n        <div className=\"text-sm\">{t('actions.downloadResume.body')}</div>\r\n\r\n        <button\r\n          type=\"button\"\r\n          onClick={() => setPrintDialogOpen(true)}\r\n          className=\"mt-4 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">save</i>\r\n            <span className=\"text-sm\">{t('actions.downloadResume.buttons.saveAsPdf')}</span>\r\n          </div>\r\n        </button>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('actions.loadDemoData.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('actions.loadDemoData.body')}</div>\r\n\r\n        <button\r\n          type=\"button\"\r\n          onClick={loadDemoData}\r\n          className=\"mt-4 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">flight_takeoff</i>\r\n            <span className=\"text-sm\">{t('actions.loadDemoData.buttons.loadData')}</span>\r\n          </div>\r\n        </button>\r\n      </div>\r\n\r\n      <hr className=\"my-6\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('actions.reset.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('actions.reset.body')}</div>\r\n\r\n        <button\r\n          type=\"button\"\r\n          onClick={resetEverything}\r\n          className=\"mt-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">refresh</i>\r\n            <span className=\"text-sm\">{t('actions.reset.buttons.reset')}</span>\r\n          </div>\r\n        </button>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ActionsTab;\r\n", "import React from 'react';\r\nimport { Trans, useTranslation } from 'react-i18next';\r\n\r\nconst AboutTab = () => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  return (\r\n    <div>\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('about.documentation.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('about.documentation.body')}</div>\r\n\r\n        <a\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          href=\"https://docs.jsonldresume.org/\"\r\n          className=\"flex justify-center items-center mt-4 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">description</i>\r\n            <span className=\"text-sm\">{t('about.documentation.buttons.documentation')}</span>\r\n          </div>\r\n        </a>\r\n      </div>\r\n\r\n      <hr className=\"my-5\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('about.bugOrFeatureRequest.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('about.bugOrFeatureRequest.body')}</div>\r\n\r\n        <div className=\"grid grid-cols-1\">\r\n          <a\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            href=\"https://github.com/AmruthPillai/Reactive-Resume/issues/new\"\r\n            className=\"mt-4 bg-red-600 hover:bg-red-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">bug_report</i>\r\n              <span className=\"text-sm\">{t('about.bugOrFeatureRequest.buttons.raiseIssue')}</span>\r\n            </div>\r\n          </a>\r\n\r\n          <a\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            href=\"mailto:<EMAIL>?subject=Feature Request/Reporting a Bug in Reactive Resume: \"\r\n            className=\"mt-4 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">email</i>\r\n              <span className=\"text-sm\">{t('about.bugOrFeatureRequest.buttons.sendEmail')}</span>\r\n            </div>\r\n          </a>\r\n        </div>\r\n      </div>\r\n\r\n      <hr className=\"my-5\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('about.sourceCode.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('about.sourceCode.body')}</div>\r\n\r\n        <a\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          href=\"https://github.com/AmruthPillai/Reactive-Resume\"\r\n          className=\"flex justify-center items-center mt-4 bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">code</i>\r\n            <span className=\"text-sm\">{t('about.sourceCode.buttons.githubRepo')}</span>\r\n          </div>\r\n        </a>\r\n      </div>\r\n\r\n      <hr className=\"my-5\" />\r\n\r\n      <div className=\"shadow text-center p-5\">\r\n        <h6 className=\"font-bold text-sm mb-2\">{t('about.license.heading')}</h6>\r\n\r\n        <div className=\"text-sm\">{t('about.license.body')}</div>\r\n\r\n        <a\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n          href=\"https://github.com/AmruthPillai/Reactive-Resume/blob/master/LICENSE\"\r\n          className=\"flex justify-center items-center mt-4 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium py-2 px-5 rounded\"\r\n        >\r\n          <div className=\"flex justify-center items-center\">\r\n            <i className=\"material-icons mr-2 font-bold text-base\">gavel</i>\r\n            <span className=\"text-sm\">{t('about.license.buttons.mitLicense')}</span>\r\n          </div>\r\n        </a>\r\n      </div>\r\n\r\n      <div className=\"mt-5\">\r\n        <p className=\"text-xs font-gray-600 text-center\">\r\n          <Trans t={t} i18nKey=\"about.footer.credit\">\r\n            Made with Love by\r\n            <a\r\n              className=\"font-bold hover:underline\"\r\n              href=\"https://www.amruthpillai.com/\"\r\n              rel=\"noopener noreferrer\"\r\n              target=\"_blank\"\r\n            >\r\n              Amruth Pillai\r\n            </a>\r\n          </Trans>\r\n        </p>\r\n        <p className=\"text-xs font-gray-600 text-center\">{t('about.footer.thanks')}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AboutTab;\r\n", "import React from 'react';\r\nimport { useTranslation, Trans } from 'react-i18next';\r\n\r\nimport { languages } from '../../../i18n';\r\nimport Dropdown from '../../../shared/Dropdown';\r\n\r\nconst SettingsTab = ({ settings, onChange }) => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  return (\r\n    <div>\r\n      <Dropdown\r\n        label={t('settings.language.label')}\r\n        value={settings.language}\r\n        onChange={x => onChange('settings.language', x)}\r\n        options={languages}\r\n        optionItem={x => (\r\n          <option key={x.code} value={x.code}>\r\n            {x.name}\r\n          </option>\r\n        )}\r\n      />\r\n\r\n      <p className=\"text-gray-800 text-xs\">\r\n        <Trans t={t} i18nKey=\"settings.language.helpText\">\r\n          If you would like to help translate the app into your own language, please refer the\r\n          <a\r\n            className=\"text-blue-600 hover:underline\"\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            href=\"https://docs.rxresu.me/translation/\"\r\n          >\r\n            Translation Documentation\r\n          </a>\r\n          .\r\n        </Trans>\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SettingsTab;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport TabBar from '../../shared/TabBar';\r\nimport TemplatesTab from './tabs/Templates';\r\nimport ColorsTab from './tabs/Colors';\r\nimport FontsTab from './tabs/Fonts';\r\nimport ActionsTab from './tabs/Actions';\r\nimport AboutTab from './tabs/About';\r\nimport SettingsTab from './tabs/Settings';\r\n\r\nconst RightSidebar = () => {\r\n  const { t } = useTranslation('rightSidebar');\r\n\r\n  const context = useContext(AppContext);\r\n  const { state, dispatch } = context;\r\n  const { data, theme, settings } = state;\r\n\r\n  const tabs = [\r\n    {\r\n      key: 'templates',\r\n      name: t('templates.title'),\r\n    },\r\n    {\r\n      key: 'colors',\r\n      name: t('colors.title'),\r\n    },\r\n    {\r\n      key: 'fonts',\r\n      name: t('fonts.title'),\r\n    },\r\n    {\r\n      key: 'actions',\r\n      name: t('actions.title'),\r\n    },\r\n    {\r\n      key: 'settings',\r\n      name: t('settings.title'),\r\n    },\r\n    {\r\n      key: 'about',\r\n      name: t('about.title'),\r\n    },\r\n  ];\r\n  const [currentTab, setCurrentTab] = useState(tabs[0].key);\r\n\r\n  const onChange = (key, value) => {\r\n    dispatch({\r\n      type: 'on_input',\r\n      payload: {\r\n        key,\r\n        value,\r\n      },\r\n    });\r\n\r\n    dispatch({ type: 'save_data' });\r\n  };\r\n\r\n  const renderTabs = () => {\r\n    switch (currentTab) {\r\n      case tabs[0].key:\r\n        return <TemplatesTab theme={theme} onChange={onChange} />;\r\n      case tabs[1].key:\r\n        return <ColorsTab theme={theme} onChange={onChange} />;\r\n      case tabs[2].key:\r\n        return <FontsTab theme={theme} onChange={onChange} />;\r\n      case tabs[3].key:\r\n        return <ActionsTab data={data} theme={theme} dispatch={dispatch} />;\r\n      case tabs[4].key:\r\n        return <SettingsTab settings={settings} onChange={onChange} />;\r\n      case tabs[5].key:\r\n        return <AboutTab />;\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      id=\"rightSidebar\"\r\n      className=\"animated slideInRight z-10 py-6 h-screen bg-white col-span-1 shadow-2xl overflow-y-scroll\"\r\n    >\r\n      <TabBar tabs={tabs} currentTab={currentTab} setCurrentTab={setCurrentTab} />\r\n      <div className=\"px-6\">{renderTabs()}</div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RightSidebar;\r\n", "import React, { useContext } from 'react';\r\n\r\nimport PageContext from '../context/PageContext';\r\n\r\nconst PageController = () => {\r\n  const pageContext = useContext(PageContext);\r\n  const { panZoomRef, setPrintDialogOpen } = pageContext;\r\n\r\n  const zoomIn = () => panZoomRef.current.zoomIn(2);\r\n  const zoomOut = () => panZoomRef.current.zoomOut(2);\r\n  const centerReset = () => {\r\n    panZoomRef.current.autoCenter(1);\r\n    panZoomRef.current.reset(1);\r\n  };\r\n\r\n  return (\r\n    <div\r\n      id=\"pageController\"\r\n      className=\"absolute z-20 opacity-75 hover:opacity-100 transition-all duration-150\"\r\n    >\r\n      <div className=\"text-2xl px-8 border border-gray-200 rounded-full bg-white flex justify-center items-center leading-none select-none\">\r\n        <div className=\"p-3 hover:bg-gray-200 cursor-pointer flex\" onClick={zoomIn}>\r\n          <i className=\"material-icons\">zoom_in</i>\r\n        </div>\r\n\r\n        <div className=\"p-3 hover:bg-gray-200 cursor-pointer flex\" onClick={zoomOut}>\r\n          <i className=\"material-icons\">zoom_out</i>\r\n        </div>\r\n\r\n        <div className=\"p-3 hover:bg-gray-200 cursor-pointer flex\" onClick={centerReset}>\r\n          <i className=\"material-icons\">center_focus_strong</i>\r\n        </div>\r\n\r\n        <div className=\"text-gray-400 p-3\">|</div>\r\n\r\n        <div className=\"p-3 hover:bg-gray-200 cursor-pointer flex\" onClick={() => window.print()}>\r\n          <i className=\"material-icons\">print</i>\r\n        </div>\r\n\r\n        <div\r\n          className=\"p-3 hover:bg-gray-200 cursor-pointer flex\"\r\n          onClick={() => setPrintDialogOpen(true)}\r\n        >\r\n          <i className=\"material-icons\">save</i>\r\n        </div>\r\n\r\n        <div className=\"text-gray-400 p-3\">|</div>\r\n\r\n        <a\r\n          className=\"p-3 hover:bg-gray-200 cursor-pointer flex\"\r\n          href=\"https://doc.jsonldresume.org/\"\r\n          target=\"_blank\"\r\n          rel=\"noopener noreferrer\"\r\n        >\r\n          <i className=\"material-icons\">help_outline</i>\r\n        </a>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PageController;\r\n", "import React, { useState, useContext } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport PageContext from '../context/PageContext';\r\nimport Dropdown from './Dropdown';\r\nimport { saveAsPdf, saveAsMultiPagePdf } from '../utils';\r\n\r\nconst PrintDialog = () => {\r\n  const { t } = useTranslation();\r\n  const pageContext = useContext(PageContext);\r\n  const { pageRef, panZoomRef, isPrintDialogOpen, setPrintDialogOpen } = pageContext;\r\n\r\n  const printTypes = [\r\n    { key: 'unconstrained', value: `${t('printDialog.printType.types.unconstrained')}` },\r\n    { key: 'fitInA4', value: `${t('printDialog.printType.types.fitInA4')}` },\r\n    { key: 'multiPageA4', value: `${t('printDialog.printType.types.multiPageA4')}` },\r\n  ];\r\n\r\n  const [quality, setQuality] = useState(80);\r\n  const [type, setType] = useState(printTypes[0].key);\r\n\r\n  return (\r\n    <div\r\n      className={`absolute inset-0 transition-all duration-200 ease-in-out ${\r\n        isPrintDialogOpen ? 'opacity-100 z-20' : 'opacity-0 z-0'\r\n      }`}\r\n      style={{ backgroundColor: 'rgba(0, 0, 0, 0.25)' }}\r\n      onClick={() => {\r\n        setPrintDialogOpen(false);\r\n      }}\r\n    >\r\n      <div\r\n        className=\"centered py-8 px-12 bg-white shadow-xl rounded w-full md:w-1/3\"\r\n        onClick={e => {\r\n          e.stopPropagation();\r\n          e.preventDefault();\r\n        }}\r\n      >\r\n        <h5 className=\"mb-6 text-lg font-bold\">{t('printDialog.heading')}</h5>\r\n\r\n        <h6 className=\"mb-1 text-sm font-medium\">{t('printDialog.quality.label')}</h6>\r\n        <div className=\"flex items-center\">\r\n          <input\r\n            type=\"range\"\r\n            className=\"w-full h-4 my-2 rounded-full overflow-hidden appearance-none focus:outline-none bg-gray-400\"\r\n            value={quality}\r\n            onChange={e => setQuality(e.target.value)}\r\n            min=\"40\"\r\n            max=\"100\"\r\n            step=\"5\"\r\n          />\r\n\r\n          <h6 className=\"font-medium pl-5\">{quality}%</h6>\r\n        </div>\r\n\r\n        <h6 className=\"mt-4 mb-2 text-sm font-medium\">{t('printDialog.printType.label')}</h6>\r\n        <Dropdown\r\n          value={type}\r\n          options={printTypes}\r\n          onChange={setType}\r\n          optionItem={x => (\r\n            <option key={x.key} value={x.key}>\r\n              {x.value}\r\n            </option>\r\n          )}\r\n        />\r\n\r\n        <p className=\"my-3 text-xs text-gray-600\">{t('printDialog.helpText.0')}</p>\r\n        <p className=\"my-3 text-xs text-gray-600\">{t('printDialog.helpText.1')}</p>\r\n\r\n        <div className=\"flex justify-between\">\r\n          <button\r\n            type=\"button\"\r\n            onClick={() => {\r\n              setPrintDialogOpen(false);\r\n            }}\r\n            className=\"mt-6 border border-red-600 text-red-600 hover:bg-red-600 hover:text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">close</i>\r\n              <span className=\"text-sm\">{t('printDialog.buttons.cancel')}</span>\r\n            </div>\r\n          </button>\r\n\r\n          <button\r\n            type=\"button\"\r\n            onClick={async () => {\r\n              await (type === 'multiPageA4'\r\n                ? saveAsMultiPagePdf(pageRef, panZoomRef, quality)\r\n                : saveAsPdf(pageRef, panZoomRef, quality, type));\r\n              setPrintDialogOpen(false);\r\n            }}\r\n            className=\"mt-6 border border-gray-700 text-gray-700 hover:bg-gray-700 hover:text-white text-sm font-medium py-2 px-5 rounded\"\r\n          >\r\n            <div className=\"flex justify-center items-center\">\r\n              <i className=\"material-icons mr-2 font-bold text-base\">save</i>\r\n              <span className=\"text-sm\">{t('printDialog.buttons.saveAsPdf')}</span>\r\n            </div>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PrintDialog;\r\n", "import React, { useState, useEffect } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\n\r\nimport animation from '../assets/panzoom.mp4';\r\n\r\nconst PanZoomAnimation = () => {\r\n  const { t } = useTranslation();\r\n  const [animationVisible, setAnimationVisible] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setTimeout(() => setAnimationVisible(true), 500);\r\n    setTimeout(() => setAnimationVisible(false), 3000);\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      className={`centered absolute inset-0 w-1/4 mt-24 transition-all duration-1000 ease-in-out ${\r\n        animationVisible ? 'opacity-100 z-20' : 'opacity-0 z-0'\r\n      }`}\r\n    >\r\n      <div className=\"px-12 rounded-lg shadow-2xl bg-white\">\r\n        <video src={animation} autoPlay muted loop />\r\n        <p className=\"px-6 pb-6 text-sm text-gray-800 font-medium text-center\">\r\n          {t('panZoomAnimation.helpText')}\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PanZoomAnimation;\r\n", "/* eslint-disable jsx-a11y/media-has-caption */\r\nimport React, { useRef, useEffect, useContext, Suspense } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { PanZoom } from 'react-easy-panzoom';\r\n\r\nimport AppContext from '../../context/AppContext';\r\nimport PageContext from '../../context/PageContext';\r\n\r\nimport LeftSidebar from '../LeftSidebar/LeftSidebar';\r\nimport RightSidebar from '../RightSidebar/RightSidebar';\r\n\r\nimport templates from '../../templates';\r\nimport PageController from '../../shared/PageController';\r\nimport PrintDialog from '../../shared/PrintDialog';\r\nimport PanZoomAnimation from '../../shared/PanZoomAnimation';\r\n\r\nconst App = () => {\r\n  const pageRef = useRef(null);\r\n  const panZoomRef = useRef(null);\r\n  const { i18n } = useTranslation();\r\n\r\n  const context = useContext(AppContext);\r\n  const { state, dispatch } = context;\r\n  const { theme, settings } = state;\r\n\r\n  const pageContext = useContext(PageContext);\r\n  const { setPageRef, setPanZoomRef } = pageContext;\r\n\r\n  useEffect(() => {\r\n    setPageRef(pageRef);\r\n    setPanZoomRef(panZoomRef);\r\n    i18n.changeLanguage(settings.language);\r\n    const storedState = JSON.parse(localStorage.getItem('state'));\r\n    dispatch({ type: 'import_data', payload: storedState });\r\n  }, [dispatch, setPageRef, setPanZoomRef, i18n, settings.language]);\r\n\r\n  return (\r\n    <Suspense fallback=\"Loading...\">\r\n      <div className=\"h-screen grid grid-cols-5 items-center\">\r\n        <LeftSidebar />\r\n\r\n        <div className=\"relative z-10 h-screen overflow-hidden col-span-3 flex justify-center items-center\">\r\n          <PanZoom\r\n            ref={panZoomRef}\r\n            minZoom=\"0.4\"\r\n            autoCenter\r\n            autoCenterZoomLevel={0.7}\r\n            enableBoundingBox\r\n            boundaryRatioVertical={0.8}\r\n            boundaryRatioHorizontal={0.8}\r\n            style={{ outline: 'none' }}\r\n          >\r\n            <div id=\"page\" ref={pageRef} className=\"shadow-2xl break-words\">\r\n              {templates.find(x => theme.layout.toLowerCase() === x.key).component()}\r\n            </div>\r\n          </PanZoom>\r\n\r\n          <PageController />\r\n        </div>\r\n\r\n        <div id=\"printPage\" className=\"break-words\">\r\n          {templates.find(x => theme.layout.toLowerCase() === x.key).component()}\r\n        </div>\r\n\r\n        <RightSidebar />\r\n\r\n        <PanZoomAnimation />\r\n        <PrintDialog />\r\n      </div>\r\n    </Suspense>\r\n  );\r\n};\r\n\r\nexport default App;\r\n", "import React from 'react';\r\nimport ReactDOM from 'react-dom';\r\nimport { toast } from 'react-toastify';\r\nimport 'react-toastify/dist/ReactToastify.css';\r\n\r\nimport './i18n';\r\nimport './assets/tailwind/tailwind.css';\r\nimport './index.css';\r\n\r\nimport * as serviceWorker from './serviceWorker';\r\nimport { AppProvider } from './context/AppContext';\r\nimport { PageProvider } from './context/PageContext';\r\nimport App from './components/App/App';\r\n\r\ntoast.configure({\r\n  autoClose: 3000,\r\n  closeButton: false,\r\n  hideProgressBar: true,\r\n  position: toast.POSITION.BOTTOM_RIGHT,\r\n});\r\n\r\nReactDOM.render(\r\n  <React.StrictMode>\r\n    <AppProvider>\r\n      <PageProvider>\r\n        <App />\r\n      </PageProvider>\r\n    </AppProvider>\r\n  </React.StrictMode>,\r\n  document.getElementById('root'),\r\n);\r\n\r\nserviceWorker.register();\r\n"], "sourceRoot": ""}