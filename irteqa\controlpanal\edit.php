<?php
session_start();
$con = mysqli_connect("127.0.0.1:3306","u364709247_shoping","5dN53znd","u364709247_shoping");

if(isset($_POST['menu_ar_edit']))
{
  $id = $_POST['id'];
  $name = $_POST['serv'];
  $uname = $_POST['fname'];
  $address = $_POST['calll'];
  $who = $_POST['who'];
  $mobile_number = $_POST['sell'];
  $injaz = $_POST['injaz'];
    $query = "UPDATE orderss SET serv='$name', fname='$uname', calll='$address', who='$who', sell='$mobile_number', injaz='$injaz' WHERE id=$id";
    $query_run = mysqli_query($con, $query);

    if($query_run)
    {
        $_SESSION['orderss'] = "Data Updated Successfully";
        header("Location: index.php");
    }
    else
    {
        $_SESSION['users'] = "Not Updated";
        header("Location: index.php");
    }
  
}

if (isset($_POST['delete'])) {
  $sql = "DELETE FROM orderss WHERE id='".$_POST['id']."' ";

    if ($con->query($sql) === TRUE) {
       header("Location: index.php");
    } else {
        echo "Error deleting record: " . $conn->error;
    }

    $conn->close();
}
/*if (isset($_POST['menu_ar_edit'])) {
  $name = $_POST['full_name'];
  $uname = $_POST['user_name'];
  $address = $_POST['address'];
  $email = $_POST['email'];
  $mobile_number = $_POST['phone'];
 $sql = "INSERT INTO data (full_name,user_name,email,phone,address,) VALUES ('$name','$uname','$email','$mobile_number','$address')";
 if (mysqli_query($conn, $sql)) { 
   $_SESSION['message'] = "Data Saved Successfully";
    header("Location: index.php");
   } else {
    mysqli_close($conn);
   }
   
}*/
/*session_start();
include_once ('include/config.php')
  $name = $_POST['full_name'];
  $uname = $_POST['user_name'];
  $address = $_POST['address'];
  $email = $_POST['email'];
  $mobile_number = $_POST['phone'];
if (isset($_POST['menu_ar_edit'])) {
  mysqli_query($conn, "UPDATE data SET full_name='$name', user_name='$uname', address='$address',email='$email', phone='$mobile_number' WHERE id=$id");
if (mysqli_query($conn, $sql)) { 
  $_SESSION['message'] = "Data Saved Successfully";
   header("Location: index.php");
  } else {
   mysqli_close($conn);
  }
}*/
?>