<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل حساب مستخدم جديد</title>
</head>
<body>
    <h1>تسجيل حساب مستخدم جديد</h1>
    <form action="process_registration.php" method="post">
        <label for="username">اسم المستخدم:</label>
        <input type="text" id="username" name="username" value="<?php echo isset($_POST['username']) ? $_POST['username'] : ''; ?>" required>
        <br>
        
        <label for="password">كلمة المرور:</label>
        <input type="password" id="password" name="password" required>
        <br>
        
        <label for="confirm_password">تأكيد كلمة المرور:</label>
        <input type="password" id="confirm_password" name="confirm_password" required>
        <br>
        
        <input type="submit" value="تسجيل">
    </form>
    <?php
// استدعاء ملف تأسيس الاتصال بقاعدة البيانات إذا كان ضرورياً
// قم بتعديل معلومات قاعدة البيانات واستدعاء الاتصال هنا

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = $_POST["username"];
    $password = $_POST["password"];
    $confirm_password = $_POST["confirm_password"];

    // التحقق من تطابق كلمتي المرور
    if ($password !== $confirm_password) {
        echo "كلمة المرور وتأكيد كلمة المرور غير متطابقين.";
    } else {
        // التحقق من عدم تكرار اسم المستخدم في قاعدة البيانات
        // يجب تعديل المعلومات الخاصة بالاتصال بقاعدة البيانات هنا
        $db_host = "localhost";
        $db_user = "root";
        $db_pass = "";
        $db_name = "signupai";

        $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

        if ($conn->connect_error) {
            die("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
        }

        $check_username_query = "SELECT * FROM users WHERE username = '$username'";
        $result = $conn->query($check_username_query);

        if ($result->num_rows > 0) {
            echo "اسم المستخدم موجود بالفعل، يرجى اختيار اسم مستخدم آخر.";
        } else {
            // إذا لم يتم العثور على اسم المستخدم في قاعدة البيانات، قم بإضافة المستخدم الجديد إلى الجدول
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $insert_user_query = "INSERT INTO users (username, password) VALUES ('$username', '$hashed_password')";

            if ($conn->query($insert_user_query) === TRUE) {
                echo "تم تسجيل المستخدم بنجاح!";
            } else {
                echo "حدث خطأ أثناء تسجيل المستخدم: " . $conn->error;
            }
        }

        $conn->close();
    }
}
?>

</body>
</html>