<?php
/**
 * لوحة تحكم المدير الرئيسية
 */

require_once __DIR__ . '/config/init.php';

// التحقق من تسجيل دخول المدير
if (!isAdminLoggedIn()) {
    header("Location: admin_login.php");
    exit();
}

// جلب إحصائيات النظام
global $main_db;

// إحصائيات المستخدمين
$users_stats = $main_db->query("SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_users,
    COUNT(CASE WHEN last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as recent_users
    FROM users")->fetch_assoc();

// إحصائيات العمليات اليومية
$today_activity = $main_db->query("SELECT 
    COUNT(*) as total_activities,
    COUNT(CASE WHEN user_type = 'user' THEN 1 END) as user_activities,
    COUNT(CASE WHEN user_type = 'admin' THEN 1 END) as admin_activities
    FROM activity_log 
    WHERE DATE(created_at) = CURDATE()")->fetch_assoc();

// أحدث العمليات
$recent_activities = $main_db->query("SELECT 
    al.*, 
    CASE 
        WHEN al.user_type = 'admin' THEN a.full_name 
        ELSE u.full_name 
    END as user_name
    FROM activity_log al
    LEFT JOIN admins a ON al.user_id = a.id AND al.user_type = 'admin'
    LEFT JOIN users u ON al.user_id = u.id AND al.user_type = 'user'
    ORDER BY al.created_at DESC 
    LIMIT 10");

require_once __DIR__ . '/includes/admin_header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- الشريط الجانبي -->
        <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active text-white" href="admin_dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_users.php">
                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_activity.php">
                            <i class="fas fa-history me-2"></i>سجل العمليات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_reports.php">
                            <i class="fas fa-chart-bar me-2"></i>التقارير الشاملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_financial.php">
                            <i class="fas fa-file-invoice-dollar me-2"></i>التقارير المالية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_system.php">
                            <i class="fas fa-cogs me-2"></i>إعدادات النظام
                        </a>
                    </li>
                    <?php if (hasAdminPermission('manage_admins')): ?>
                    <li class="nav-item">
                        <a class="nav-link text-light" href="admin_manage_admins.php">
                            <i class="fas fa-user-shield me-2"></i>إدارة المديرين
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                <div>
                    <h1 class="h3 mb-1 fw-bold text-primary">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة تحكم المدير
                    </h1>
                    <p class="text-muted mb-0 small">مرحباً بك في نظام إدارة المبيعات المتقدم</p>
                </div>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-1"></i>إضافة جديد
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-download me-1"></i>تصدير
                        </button>
                        <button type="button" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                    </div>
                </div>
            </div>

            <!-- البطاقات الإحصائية المحسنة -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-primary h-100">
                        <div class="card-body py-3">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                        إجمالي المستخدمين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">
                                        <?php echo number_format($users_stats['total_users']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-users fa-lg text-primary"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-success h-100">
                        <div class="card-body py-3">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                        المستخدمين النشطين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">
                                        <?php echo number_format($users_stats['active_users']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-check fa-lg text-success"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-info h-100">
                        <div class="card-body py-3">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                        نشاط اليوم
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">
                                        <?php echo number_format($today_activity['total_activities']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-calendar fa-lg text-info"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-3">
                    <div class="card border-left-warning h-100">
                        <div class="card-body py-3">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                        مستخدمين حديثين
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold">
                                        <?php echo number_format($users_stats['recent_users']); ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-user-plus fa-lg text-warning"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أحدث العمليات -->
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-history me-2"></i>أحدث العمليات
                    </h6>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>الوقت</th>
                                    <th>المستخدم</th>
                                    <th>النوع</th>
                                    <th>العملية</th>
                                    <th>الوصف</th>
                                    <th>IP</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($activity = $recent_activities->fetch_assoc()): ?>
                                <tr>
                                    <td class="small"><?php echo date('H:i:s', strtotime($activity['created_at'])); ?></td>
                                    <td class="small"><?php echo htmlspecialchars($activity['user_name'] ?? 'غير محدد'); ?></td>
                                    <td>
                                        <span class="badge <?php echo $activity['user_type'] === 'admin' ? 'bg-danger' : 'bg-primary'; ?>">
                                            <?php echo $activity['user_type'] === 'admin' ? 'مدير' : 'مستخدم'; ?>
                                        </span>
                                    </td>
                                    <td class="small"><code><?php echo htmlspecialchars($activity['action']); ?></code></td>
                                    <td class="small"><?php echo htmlspecialchars($activity['description'] ?? ''); ?></td>
                                    <td class="small text-muted"><?php echo htmlspecialchars($activity['ip_address'] ?? ''); ?></td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<style>
.text-xs {
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.h5 {
    font-size: 18px;
    font-weight: 700;
}

code {
    background: rgba(52, 152, 219, 0.1);
    color: var(--admin-blue-accent);
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
}

.small {
    font-size: 12px;
}
</style>

<?php require_once __DIR__ . '/includes/admin_footer.php'; ?>
