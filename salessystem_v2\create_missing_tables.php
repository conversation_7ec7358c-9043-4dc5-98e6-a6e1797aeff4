<?php
/**
 * إنشاء الجداول المفقودة تلقائياً
 */

require_once __DIR__ . '/config/init.php';
redirectIfNotLoggedIn();

// التحقق من وجود المستخدم
if (!isset($_SESSION['username']) || !isset($_SESSION['user_id'])) {
    $_SESSION['error'] = "يجب تسجيل الدخول أولاً";
    header("Location: login.php");
    exit();
}

$username = $_SESSION['username'];
$user_id = $_SESSION['user_id'];

// الحصول على اتصال قاعدة البيانات
$db = getCurrentUserDB();
if (!$db || $db->connect_error) {
    $_SESSION['error'] = "فشل الاتصال بقاعدة البيانات";
    header("Location: index.php");
    exit();
}

$created_tables = [];
$errors = [];

try {
    // قائمة الجداول المطلوبة مع هياكلها
    $required_tables = [
        'customers' => "
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `name` varchar(255) NOT NULL,
                `phone` varchar(20) DEFAULT NULL,
                `email` varchar(255) DEFAULT NULL,
                `tax_number` varchar(50) DEFAULT NULL,
                `address` text DEFAULT NULL,
                `customer_type` enum('customer','supplier') DEFAULT 'customer',
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `customer_type` (`customer_type`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'products' => "
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `name` varchar(255) NOT NULL,
                `description` text DEFAULT NULL,
                `price` decimal(10,2) NOT NULL DEFAULT 0.00,
                `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
                `category` varchar(100) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `category` (`category`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'sales' => "
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `customer_id` int(11) DEFAULT NULL,
                `invoice_number` varchar(50) NOT NULL,
                `date` date NOT NULL,
                `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
                `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `payment_status` enum('pending','paid','partial') DEFAULT 'pending',
                `notes` text DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `customer_id` (`customer_id`),
                KEY `invoice_number` (`invoice_number`),
                KEY `date` (`date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'sale_items' => "
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `sale_id` int(11) NOT NULL,
                `product_id` int(11) NOT NULL,
                `quantity` int(11) NOT NULL,
                `unit_price` decimal(10,2) NOT NULL,
                `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
                `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `sale_id` (`sale_id`),
                KEY `product_id` (`product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'purchases' => "
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `customer_id` int(11) DEFAULT NULL,
                `invoice_number` varchar(50) NOT NULL,
                `date` date NOT NULL,
                `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
                `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `payment_status` enum('pending','paid','partial') DEFAULT 'pending',
                `notes` text DEFAULT NULL,
                `supplier_name` varchar(255) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `customer_id` (`customer_id`),
                KEY `invoice_number` (`invoice_number`),
                KEY `date` (`date`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ",
        
        'purchase_items' => "
            CREATE TABLE IF NOT EXISTS `{table_name}` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `purchase_id` int(11) NOT NULL,
                `product_id` int(11) NOT NULL,
                `product_name` varchar(255) DEFAULT NULL,
                `quantity` int(11) NOT NULL,
                `unit_price` decimal(10,2) NOT NULL,
                `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
                `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
                `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                KEY `user_id` (`user_id`),
                KEY `purchase_id` (`purchase_id`),
                KEY `product_id` (`product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        "
    ];

    // إنشاء الجداول
    foreach ($required_tables as $table_type => $sql_template) {
        $table_name = getUserTableName($table_type, $username);
        
        // استبدال placeholder باسم الجدول الفعلي
        $sql = str_replace('{table_name}', $table_name, $sql_template);
        
        // التحقق من وجود الجدول
        $check_table = $db->query("SHOW TABLES LIKE '$table_name'");
        
        if (!$check_table || $check_table->num_rows == 0) {
            // إنشاء الجدول
            if ($db->query($sql)) {
                $created_tables[] = $table_name;
            } else {
                $errors[] = "فشل في إنشاء جدول $table_name: " . $db->error;
            }
        }
    }

    // رسالة النجاح
    if (!empty($created_tables)) {
        $_SESSION['success'] = "تم إنشاء الجداول التالية بنجاح: " . implode(', ', $created_tables);
    } else {
        $_SESSION['info'] = "جميع الجداول المطلوبة موجودة بالفعل";
    }

    // رسائل الخطأ
    if (!empty($errors)) {
        $_SESSION['error'] = "حدثت أخطاء: " . implode(', ', $errors);
    }

} catch (Exception $e) {
    error_log("خطأ في إنشاء الجداول: " . $e->getMessage());
    $_SESSION['error'] = "حدث خطأ أثناء إنشاء الجداول: " . $e->getMessage();
}

// إعادة التوجيه
$redirect_to = $_GET['redirect'] ?? 'index.php';
header("Location: $redirect_to");
exit();
?>
