<?php
/**
 * مولد bcrypt hashes للاختبار
 * أداة مساعدة لإنشاء hashes لاختبار الأداة الرئيسية
 */

// مولد bcrypt hashes - متاح للجميع بدون تسجيل دخول
// تم إزالة متطلب تسجيل الدخول لسهولة الوصول

$generated_hash = '';
$input_password = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['generate'])) {
    $input_password = $_POST['password'] ?? '';
    $cost = intval($_POST['cost'] ?? 10);
    
    if (!empty($input_password)) {
        $cost = max(4, min(15, $cost)); // تحديد النطاق الآمن
        $generated_hash = password_hash($input_password, PASSWORD_BCRYPT, ['cost' => $cost]);
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد bcrypt Hash</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding-top: 50px;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .card-header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 20px;
        }
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: bold;
        }
        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: bold;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }
        .form-control:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        .hash-output {
            background: #f8f9fa;
            border: 2px dashed #28a745;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            min-height: 100px;
        }
        .test-passwords {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        .password-item {
            background: white;
            border-radius: 8px;
            padding: 10px;
            margin: 5px 0;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                
                <div class="card">
                    <div class="card-header text-center">
                        <h3><i class="fas fa-key"></i> مولد bcrypt Hash</h3>
                        <p class="mb-0">أداة لإنشاء bcrypt hashes للاختبار</p>
                    </div>
                    <div class="card-body">
                        
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="password" class="form-label">
                                            <i class="fas fa-lock"></i> كلمة المرور
                                        </label>
                                        <input type="text" class="form-control" id="password" name="password" 
                                               value="<?php echo htmlspecialchars($input_password); ?>" 
                                               placeholder="أدخل كلمة المرور المراد تشفيرها" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="cost" class="form-label">
                                            <i class="fas fa-cogs"></i> مستوى التعقيد
                                        </label>
                                        <select class="form-control" id="cost" name="cost">
                                            <option value="4">4 (سريع جداً)</option>
                                            <option value="6">6 (سريع)</option>
                                            <option value="8">8 (متوسط)</option>
                                            <option value="10" selected>10 (افتراضي)</option>
                                            <option value="12">12 (بطيء)</option>
                                            <option value="14">14 (بطيء جداً)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" name="generate" class="btn btn-success">
                                    <i class="fas fa-magic"></i> إنشاء Hash
                                </button>
                            </div>
                        </form>

                        <?php if (!empty($generated_hash)): ?>
                        <div class="mt-4">
                            <h5><i class="fas fa-check-circle text-success"></i> Hash المُنشأ:</h5>
                            <div class="hash-output">
                                <strong>كلمة المرور:</strong> <?php echo htmlspecialchars($input_password); ?><br>
                                <strong>Hash:</strong><br>
                                <?php echo htmlspecialchars($generated_hash); ?>
                            </div>
                            
                            <div class="mt-3 text-center">
                                <button class="btn btn-primary" onclick="copyToClipboard('<?php echo addslashes($generated_hash); ?>')">
                                    <i class="fas fa-copy"></i> نسخ Hash
                                </button>
                                <a href="bcrypt_security_tester.php" class="btn btn-primary">
                                    <i class="fas fa-shield-alt"></i> اختبار الأمان
                                </a>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- كلمات مرور للاختبار -->
                        <div class="test-passwords">
                            <h5><i class="fas fa-list"></i> كلمات مرور للاختبار:</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger">كلمات مرور ضعيفة:</h6>
                                    <div class="password-item">
                                        <strong>123456</strong> - رقم بسيط
                                        <button class="btn btn-sm btn-outline-success float-end" 
                                                onclick="generateHash('123456', 10)">إنشاء</button>
                                    </div>
                                    <div class="password-item">
                                        <strong>password</strong> - كلمة شائعة
                                        <button class="btn btn-sm btn-outline-success float-end" 
                                                onclick="generateHash('password', 10)">إنشاء</button>
                                    </div>
                                    <div class="password-item">
                                        <strong>admin</strong> - كلمة إدارية
                                        <button class="btn btn-sm btn-outline-success float-end" 
                                                onclick="generateHash('admin', 10)">إنشاء</button>
                                    </div>
                                    <div class="password-item">
                                        <strong>qwerty</strong> - نمط لوحة مفاتيح
                                        <button class="btn btn-sm btn-outline-success float-end" 
                                                onclick="generateHash('qwerty', 10)">إنشاء</button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success">كلمات مرور قوية:</h6>
                                    <div class="password-item">
                                        <strong>MyStr0ng!P@ss</strong> - معقدة
                                        <button class="btn btn-sm btn-outline-success float-end" 
                                                onclick="generateHash('MyStr0ng!P@ss', 10)">إنشاء</button>
                                    </div>
                                    <div class="password-item">
                                        <strong>Tr!cky#2023$</strong> - رموز وأرقام
                                        <button class="btn btn-sm btn-outline-success float-end" 
                                                onclick="generateHash('Tr!cky#2023$', 10)">إنشاء</button>
                                    </div>
                                    <div class="password-item">
                                        <strong>C0mpl3x&S3cur3</strong> - مختلطة
                                        <button class="btn btn-sm btn-outline-success float-end" 
                                                onclick="generateHash('C0mpl3x&S3cur3', 10)">إنشاء</button>
                                    </div>
                                    <div class="password-item">
                                        <strong>UnGu3ss@bl3!2023</strong> - طويلة ومعقدة
                                        <button class="btn btn-sm btn-outline-success float-end" 
                                                onclick="generateHash('UnGu3ss@bl3!2023', 10)">إنشاء</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات حول مستويات التعقيد -->
                        <div class="mt-4">
                            <h5><i class="fas fa-info-circle"></i> معلومات حول مستويات التعقيد:</h5>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>المستوى</th>
                                            <th>الوقت التقريبي</th>
                                            <th>الاستخدام</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>4</td>
                                            <td>~1ms</td>
                                            <td>اختبار سريع</td>
                                        </tr>
                                        <tr>
                                            <td>6</td>
                                            <td>~4ms</td>
                                            <td>تطبيقات بسيطة</td>
                                        </tr>
                                        <tr>
                                            <td>8</td>
                                            <td>~16ms</td>
                                            <td>تطبيقات عادية</td>
                                        </tr>
                                        <tr class="table-success">
                                            <td>10</td>
                                            <td>~70ms</td>
                                            <td>الافتراضي الموصى به</td>
                                        </tr>
                                        <tr>
                                            <td>12</td>
                                            <td>~280ms</td>
                                            <td>أمان عالي</td>
                                        </tr>
                                        <tr>
                                            <td>14</td>
                                            <td>~1.1s</td>
                                            <td>أمان فائق</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('تم نسخ Hash إلى الحافظة!');
            }, function(err) {
                console.error('فشل في النسخ: ', err);
                // طريقة بديلة للنسخ
                const textArea = document.createElement("textarea");
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    document.execCommand('copy');
                    alert('تم نسخ Hash إلى الحافظة!');
                } catch (err) {
                    alert('فشل في النسخ. يرجى النسخ يدوياً.');
                }
                document.body.removeChild(textArea);
            });
        }

        function generateHash(password, cost) {
            document.getElementById('password').value = password;
            document.getElementById('cost').value = cost;
            document.querySelector('form').submit();
        }
    </script>
</body>
</html>
