
body{ 
		background-color:rgba(236.23359739780426, 241.01965337991714, 245.43749392032623, 1);
		margin: 0;
		width: 100%;
}
.e47_258 { 
	right: 0px;
	width:100%;
	height:1150px;
	position:absolute;
	overflow:hidden;
}
.e40_208 { 
	box-shadow:4px 0px 4px rgba(0, 0, 0, 0.25);
	width:480px;
	height:1000px;
	position:absolute;
	right: 10px;
	top:89px;
	border-radius: 0 20px 0 0 ;

}
.e40_206 { 
	background-color:rgba(255, 255, 255, 1);
	width:505px;
	height:1000px;
	position:absolute;
	left:-105px;
	top:0px;
}
.e40_207 { 
	background-color:rgba(192.31249898672104, 192.31249898672104, 192.31249898672104, 1);
	width:80px;
	height:1000px;
	position:absolute;
	left:400px;
	top:0px;
	border-radius:0 20px 0 0;
}
.ei40_207_36_176 { 
	background-color:rgba(140.25000303983688, 140.25000303983688, 140.25000303983688, 1);
	width:79px;
	height:69px;
	position:absolute;
	left:0px;
	top:0px;
	border-radius:0 20px 0 0 ;
}
.ei40_207_36_178 { 
	width:51px;
	height:39.659183502197266px;
	position:absolute;
	left:14px;
	top:17px;
}
.ei40_207_36_179 { 
	background-color:rgba(63.75, 63.75, 63.75, 1);
	width:51px;
	height:39.659183502197266px;
	position:absolute;
	left:0px;
	top:0px;
}
.ei40_207_36_205 { 
	box-shadow:0px 4px 4px rgba(0, 0, 0, 0.25);
	border-radius:500px;
	background-color:rgba(255, 255, 255, 1);
	width:46px;
	height:46px;
	position:absolute;
	left:17px;
	top:908px;
}
.ei40_207_36_188 { 
	width:35px;
	height:35.564544677734375px;
	position:absolute;
	left:22px;
	top:913px;
}
.ei40_207_36_189 { 
	background-color:rgba(223.125, 73.44530895352364, 73.44530895352364, 1);
	width:35px;
	height:35.564544677734375px;
	position:absolute;
	left:0px;
	top:0px;
}
.ei40_207_36_204 { 
	box-shadow:0px 4px 4px rgba(0, 0, 0, 0.25);
	border-radius:500px;
	background-color:rgba(255, 255, 255, 1);
	width:49px;
	height:49px;
	position:absolute;
	left:14px;
	top:834px;
}
.ei40_207_36_195 { 
	width:22px;
	height:32.008567810058594px;
	position:absolute;
	left:28px;
	top:843px;
}
.ei40_207_36_196 { 
	background-color:rgba(0, 0, 0, 1);
	width:22px;
	height:32.008567810058594px;
	position:absolute;
	left:0px;
	top:0px;
}
.ei40_207_36_203 { 
	width:75px;
	height:374px;
	position:absolute;
	left:0px;
	top:199px;
}
.ei40_207_36_174 { 
	background-color:rgba(140.25000303983688, 140.25000303983688, 140.25000303983688, 1);
	width:74px;
	height:374px;
	position:absolute;
	left:0px;
	top:0px;
	border-radius:0 20px 20px 0;
}
.ei40_207_36_175 { 
	box-shadow:4px 4px 4px rgba(0, 0, 0, 0.25);
	background-color:rgba(255, 255, 255, 1);
	width:75px;
	height:66px;
	position:absolute;
	left:0px;
	top:0px;
	border-radius:0 20px 20px 0;
}
.ei40_207_36_180 { 
	width:51px;
	height:53.2750244140625px;
	position:absolute;
	left:14px;
	top:314px;
}
.ei40_207_36_181 { 
	background-color:rgba(64.81249898672104, 64.81249898672104, 64.81249898672104, 1);
	width:51px;
	height:53.2750244140625px;
	position:absolute;
	left:0px;
	top:0px;
}
.ei40_207_36_182 { 
	width:51px;
	height:35.69999694824219px;
	position:absolute;
	left:14px;
	top:216px;
}
.ei40_207_36_183 { 
	background-color:rgba(75.43749645352364, 75.43749645352364, 75.43749645352364, 1);
	width:51px;
	height:35.69999694824219px;
	position:absolute;
	left:0px;
	top:0px;
}
.ei40_207_36_184 { 
	width:51px;
	height:39.66190719604492px;
	position:absolute;
	left:14px;
	top:13.3433837890625px;
}
.ei40_207_36_185 { 
	background-color:rgba(64.81249898672104, 64.81249898672104, 64.81249898672104, 1);
	width:51px;
	height:39.66190719604492px;
	position:absolute;
	left:0px;
	top:0px;
}
.ei40_207_36_186 { 
	width:51px;
	height:38.25px;
	position:absolute;
	left:14px;
	top:115.3699951171875px;
}
.ei40_207_36_187 { 
	background-color:rgba(61.62499822676182, 61.62499822676182, 61.62499822676182, 1);
	width:51px;
	height:38.25px;
	position:absolute;
	left:0px;
	top:0px;
}
.e47_259 { 
	background-color:rgba(255, 255, 255, 1);
	width:862px;
	height:1000px;
	position:absolute;
	left:-967px;
	top:0px;
	border-radius:20px 0 0 0;
}
.e47_260 { 
	box-shadow:0px 4px 4px rgba(0, 0, 0, 0.25);
	background-color:rgba(255, 255, 255, 1);
	width:346px;
	height:670px;
	position:absolute;
	left:31px;
	top:240px;
	border-top-left-radius:20px;
	border-top-right-radius:20px;
	border-bottom-left-radius:20px;
	border-bottom-right-radius:20px;
}
.e47_260 { 
	border:1px solid rgba(193.37499797344208, 193.37499797344208, 193.37499797344208, 1);
}
.e47_261 { 
	background-color:rgba(78.62500101327896, 78.62500101327896, 78.62500101327896, 1);
	width:100%;
	height:61px;
	position:absolute;
	left:0px;
	top:1089px;
}
.e47_269 { 
	width:100%;
	height:73px;
	position:absolute;
	left:0px;
	top:0px;
	box-shadow:0px 4px 4px rgba(0, 0, 0, 0.25);
	background-color:rgba(255, 255, 255, 1);
}

.e47_270 { 
	width:248.29444885253906px;
	height:57px;
	position:absolute;
	left:31.16666603088379px;
	top:8px;
}
.e47_264 { 
	box-shadow:0px 4px 4px rgba(0, 0, 0, 0.25);
	border-radius:500px;
	background-color:rgba(255, 255, 255, 1);
	width:59.21666717529297px;
	height:57px;
	position:absolute;
	left:0px;
	top:0px;
}
.e47_265 { 
	border-radius:500px;
	background-color:rgba(217.0000022649765, 217.0000022649765, 217.0000022649765, 1);
	width:55.06111145019531px;
	height:53px;
	position:absolute;
	left:2.0778183937072754px;
	top:2px;
}
.e47_266 { 
	color:rgba(0, 0, 0, 1);
	width:60.25555419921875px;
	height:24px;
	position:absolute;
	left:188.03892517089844px;
	top:11px;
	font-family:Inter;
	text-align:left;
	font-size:20px;
	letter-spacing:0;
}
.e47_267 { 
	color:rgba(160.43749898672104, 52.14218907058239, 52.14218907058239, 1);
	width:112.19999694824219px;
	height:28px;
	position:absolute;
	left:71.6833724975586px;
	top:11px;
	font-family:Inter;
	text-align:left;
	font-size:20px;
	letter-spacing:0;
}
.e47_273 { 
	width:44px;
	height:52.360294342041016px;
	position:absolute;
	right: 20;
	top:7px;
}
.e47_272 { 
	background-color:rgba(65.99083095788956, 142.37500101327896, 39.153125658631325, 1);
	width:44px;
	height:52.360294342041016px;
	position:absolute;
	left:0px;
	top:0px;
}
