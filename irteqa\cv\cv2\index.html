<!DOCTYPE html>
<html>

<head>
   <meta charset="utf-8" />
   <link rel="stylesheet" type="text/css" href="dep/normalize.css/normalize.css" />
   <link rel="stylesheet" type="text/css" href="dep/Font-Awesome/css/font-awesome.css" />
   <link rel="stylesheet" type="text/css" href="style_sivashanmugam.css" />
   <link href="https://fonts.googleapis.com/css?family=Source+Code+Pro" rel="stylesheet">
   <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600,700" rel="stylesheet">
</head>

<body lang="en">
   <section class="name-description-contact">
      <header id="title">
         <h1>Sivashanmugam Kannan</h1>
         <span class="subtitle">Fullstack Software Developer</span>
         <p class="aboutme">Software Developer with <strong>6 years</strong> of work experience with a Bachelor of
            Technology degree in
            Computer Science and Engineering primarily working as Web Developer</p>
      </header>
      <div class="contact-details text-right">
         <ul class="no-margin">
            <li><EMAIL> <i class="fa fa-envelope"></i> </li>
            <li> +91 8508874861 <i class="fa fa-phone"></i></li>
            <li>github.com/shivashanmugam <i class="fa fa-github"></i> </li>
            <li>stackoverflow.com/users/2264606 <i class="fa fa-stack-overflow"></i></li>
            <li>https://medium.com/@_siva_kannan <i class="fa fa-rss"></i></li>
         </ul>
      </div>
   </section>
   <section class="main">
      <div class="container tech-skill">
         <h4><i class="fa fa-code"></i> Programming Experience</h4>
         <ul class="no-margin">
            <li>
               Strong knowledge in Core Concepts of
               <ul>
                  <li>Object oriented JavaScript, ReactJS, NodeJS</li>
                  <li>Test Driven Development</li>
                  <li>Writing Scalable effitient code</li>
                  <li>Web optimizations</li>
               </ul>
            </li>
            <li>
               Good Experience in
               <ul>
                  <li>Deployment configurations
                  </li>
                  <li>Writing Web Build configurations</li>
                  <li>Tools and libraries familier with: Webpack, Storybook, express, socket.io, mongoose, antd, grunt
                  </li>
                  <li>Server development with NodeJS, mongoDB</li>
                  <li>Developing Responsive User Interfaces</li>
               </ul>
            </li>
            <li></li>
         </ul>
      </div>

      <div class="container">
         <h4><i class="fa fa-graduation-cap"></i> Education</h4>
         <section class="project">
            <section class="blocks">
               <div class="date">
                  <span>2011</span><span>2015</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>Bachelor of Technology in Computer Science And Engineering</h3>
                     <span class="place"><i class="fa fa-university"></i> &nbsp;SASTRA University</span>
                     <span class="location">Thanjavur</span>
                  </header>
                  <div>
                     <ul>
                        <li>Passed with <strong>6.1 CGPA</strong></li>
                        <li>Created a IoT Project for indoor location monitoring with low powered devices using IPv6 and
                           submitted the project in various forums and publications</li>
                     </ul>
                  </div>
               </div>
            </section>
            <section class="blocks">
               <div class="date">
                  <span>2009</span><span>2011</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>Higher Secondary Schooling</h3>
                     <span class="place"><i class="fa fa-university"></i> &nbsp;Laurel Higher Secondary School</span>
                  </header>
                  <div>
                     <ul>
                        <li>Passed with Percentage of <strong>87%</strong></li>
                     </ul>
                  </div>
               </div>
            </section>
            <section class="blocks">
               <div class="date">
                  <span>2007</span><span>2009</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>SSLC</h3>
                     <span class="place"><i class="fa fa-university"></i> &nbsp;Government Boys Higher Secondary
                        School</span>
                  </header>
                  <div>
                     <ul>
                        <li>Passed with the percentage of <strong>90%</strong></li>
                     </ul>
                  </div>
               </div>
            </section>
         </section>
      </div>
   </section>
   <section class="main">
      <div class="container">
         <h4><i class="fa fa-wrench"></i> Skills</h4>
         <button>Javascript</button>
         <button>ReactJS</button>
         <button>NodeJS</button>
         <button>Typescript</button>
      </div>
      <div class="container">
         <h4><i class="fa fa-suitcase"></i> Work Experience</h4>
         <section class="project">
            <section class="blocks">
               <div class="date">
                  <span>2017<br>Aug</span><span>Now</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>Embibe ( Funtoot Merged )</h3>
                     <span class="place"><i class="fa fa-briefcase"></i> &nbsp;SDE-II</span>
                     <span class="location">Bengaluru</span>
                  </header>
                  <div>
                     <ul>
                        <li>Developing multiple interactive question UI components for students in TV, Web, mobile
                        </li>
                        <li>Part UI/UX design discussions</li>
                        <li>Responsible for configuring development and production platforms</li>
                     </ul>
                  </div>
               </div>
            </section>
            <section class="blocks">
               <div class="date">
                  <span>2015<br>Aug&nbsp;</span><span>2016<br>May</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>Indo Sakura Software Private Limited</h3>
                     <span class="place"><i class="fa fa-briefcase"></i> &nbsp;Software Developer</span>
                     <span class="location">Bengaluru</span>
                  </header>
                  <div>
                     <ul>
                        <li>As a part of team of 5 we delivered successfully multiple projects for Japanese clients with
                           highly constrained timeline with efficiency</li>
                        <li>Responsible for Design and development of multiple front end applications</li>
                     </ul>
                  </div>
               </div>
            </section>
            <section class="blocks">
               <div class="date">
                  <span>2016<br>Jun&nbsp;</span><span>2017<br>Jul</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>Higher One Financial Technology Private Limited</h3>
                     <span class="place"><i class="fa fa-briefcase"></i> &nbsp;Technology Trainee</span>
                     <span class="location">Chennai</span>
                  </header>
                  <div>
                     <ul>
                        <li>As Trainee and being a part of team of 7, helped the team on new product features and
                           functionality in our finance platform</li>
                        <li>Helped the team on datbase migration from SQL to mongoDB</li>
                        <li>Created a internal communication tools With NodeJS and socket.io</li>
                     </ul>
                  </div>
               </div>
            </section>
         </section>
      </div>
   </section>

   <section class="name-description-contact as-page-header">
      <header id="title" class="unheight">
         <h1>Sivashanmugam Kannan</h1>
      </header>
      <div class="contact-details unheight text-right">
         <ul class="no-margin">
            <li><EMAIL> <i class="fa fa-envelope"></i> </li>
            <!-- <li>shivashanmugam <i class="fa fa-github"></i> </li> -->
            <li> +91 8508874861 <i class="fa fa-phone"></i></li>
            <!-- <li> mywebsite.com <i class="fa fa-globe"></i></li> -->
         </ul>
      </div>
   </section>
   <section class="main">
      <div class="container">
         <h4><i class="fa fa-folder-open"></i> Selected Work Projects</h4>
         <section class="project">
            <section class="blocks">
               <div class="date">
                  <span>2017</span><span>2018</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>Educational Content Management System</h3>
                     <span class="place"><i class="fa fa-briefcase"></i> &nbsp;EDreams (Merged into Embibe) </span>
                     <span class="location">Bengaluru</span>
                  </header>
                  <div>
                     <ul>
                        <li>Developed scalable angular applications and configurable and extendable angular directives
                           for our product's CMS platform</li>
                        <li>Developed javascript highlevel plugins to seamlessly integrate external libraries</li>
                        <li>Added SASS support for important modules</li>
                        <li>Written grunt configuration for express, SASS compilation and packaging</li>
                        <li>Used NodeJS, mongoDB with Google auth in the web server</li>
                        <li>For access control, defined roles and permissions for each user</li>
                        <li>Designed schema for all the collection in mongoDB</li>
                     </ul>
                  </div>
               </div>
            </section>
            <section class="blocks">
               <div class="date">
                  <span>2019</span><span>2021</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>Online learning platform</h3>
                     <span class="place"><i class="fa fa-briefcase"></i> &nbsp;Embibe</span>
                     <span class="location">Bangalore</span>
                  </header>
                  <div>
                     <ul>
                        <li>Created a online learning platform for the students of India
                        </li>
                     </ul>
                  </div>
               </div>
            </section>
            <section class="blocks">
               <div class="date">
                  <span>2017</span><span>2018</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>Educational plugins with translation support</h3>
                     <span class="place"><i class="fa fa-briefcase"></i> &nbsp;EDreams</span>
                     <span class="location">Bengaluru</span>
                  </header>
                  <div>
                     <ul>
                        <li>Using javaScript created educational plugins for web and mobile</li>
                        <li>Written automations to enable translation of content plugins in various Indian languages and
                           it has been widely adapted to different state government school books successfully</li>
                     </ul>
                  </div>
               </div>
            </section>
         </section>
      </div>
   </section>
   <!-- Education -->
   <section class="main" style="position: relative; height:7in">
      <div class="container">
         <h4><i class="fa fa-folder-open"></i> Selected Personal Projects</h4>
         <section class="project">
            <section class="blocks">
               <div class="date">
                  <span>2017</span><span>2018</span>
               </div>
               <div class="decorator"></div>
               <div class="details">
                  <header>
                     <h3>Custom AngularJS directive for table generator</h3>
                  </header>
                  <div>
                     <ul class="no-margin">
                        <li>Created a angularJS custom directive for generating high configurable HTML table</li>
                        <li>Implemented sort, column search, table search, filter, pagination, selecting rows,row
                           action, table actions, custom coloring for selected rows and specifc columns</li>
                        <li>Written extensive documentation for configuration, examples and featured on github</li>
                     </ul>
                  </div>
               </div>
            </section>

            <section class="project">
               <section class="blocks">
                  <div class="date">
                     <span>2015</span><span>2016</span>
                  </div>
                  <div class="decorator"></div>
                  <div class="details">
                     <header>
                        <h3>Webcrawling wikipedia to Identifying missing data</h3>
                     </header>
                     <div>
                        <ul class="no-margin">
                           <li>Created Webcrawler using NodeJS which gets the details of people pages</li>
                           <li>Extracted data through DOM parsing and regular expressions and stored the details in
                              mongoDB</li>
                           <li>Analysed data and fixed the missed data in wikimedia API</li>
                           <li>Server handles 200 concurrent http request and details retrived from more than 30 lack
                              web pages</li>
                        </ul>
                     </div>
                  </div>
               </section>
               <section class="blocks">
                  <div class="date">
                     <span>2016</span><span>2017</span>
                  </div>
                  <div class="decorator"></div>
                  <div class="details">
                     <header>
                        <h3>Image Processing Web App</h3>
                     </header>
                     <div>
                        <ul class="no-margin">
                           <li>Created web application which authorizes users through facebook</li>
                           <li>Process authorized images with existing templates using imagemagick nodeJS library</li>
                           <li>Deployed server on amazon EC2, Integrated amazon S3 storage, Google analytics API and
                              Facebook advertisement</li>
                        </ul>
                     </div>
                  </div>
               </section>
            </section>
      </div>
   </section>
</body>

</html>