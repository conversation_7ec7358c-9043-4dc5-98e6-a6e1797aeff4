<?php
require 'vendor/autoload.php';

use UAParser\Parser;

// عين المستخدم الذي ترغب في الكشف عن معلوماته
$user_agent_string = $_SERVER['HTTP_USER_AGENT'];

// إنشاء مثيل من مكتبة UserAgentParser
$parser = Parser::create();

// كشف معلومات المتصفح ونظام التشغيل
$result = $parser->parse($user_agent_string);

$browser = $result->ua->family;
$browser_version = $result->ua->toString();
$os = $result->os->family;
$os_version = $result->os->toString();

// عرض معلومات المتصفح ونظام التشغيل
echo "المتصفح: $browser $browser_version<br>";
echo "نظام التشغيل: $os $os_version<br>";