<?php
/**
 * إصلاح سريع لمشاكل قاعدة البيانات
 * يقوم بتشغيل جميع أدوات الإصلاح تلقائياً
 */

// منع انتهاء وقت التنفيذ
set_time_limit(0);
ini_set('memory_limit', '512M');

require_once __DIR__ . '/config/init.php';
require_once __DIR__ . '/includes/functions.php';
redirectIfNotLoggedIn();

$start_time = microtime(true);
$operations = [];
$total_success = 0;
$total_errors = 0;

function logOperation($message, $status = 'info', $details = '') {
    global $operations, $total_success, $total_errors;
    
    $operations[] = [
        'time' => date('H:i:s'),
        'message' => $message,
        'status' => $status,
        'details' => $details
    ];
    
    if ($status === 'success') {
        $total_success++;
    } elseif ($status === 'error') {
        $total_errors++;
    }
}

// بدء العمليات
logOperation("بدء الإصلاح السريع لقاعدة البيانات", 'info');

// 1. فحص الاتصال الأساسي
logOperation("فحص الاتصال بقاعدة البيانات...", 'info');

try {
    global $main_db;
    if ($main_db && !$main_db->connect_error) {
        logOperation("الاتصال بقاعدة البيانات الرئيسية سليم", 'success');
    } else {
        logOperation("مشكلة في الاتصال بقاعدة البيانات الرئيسية", 'error', $main_db ? $main_db->connect_error : 'اتصال فارغ');
    }
} catch (Exception $e) {
    logOperation("استثناء في فحص قاعدة البيانات الرئيسية", 'error', $e->getMessage());
}

// 2. إنشاء/فحص قاعدة بيانات المستخدم
logOperation("فحص قاعدة بيانات المستخدم...", 'info');

$user_db_name = "sales_system_user_" . $_SESSION['user_id'];

try {
    // محاولة إنشاء قاعدة البيانات إذا لم تكن موجودة
    $create_db_query = "CREATE DATABASE IF NOT EXISTS `$user_db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
    if ($main_db->query($create_db_query)) {
        logOperation("تم التأكد من وجود قاعدة بيانات المستخدم", 'success');
    } else {
        logOperation("فشل في إنشاء قاعدة بيانات المستخدم", 'error', $main_db->error);
    }
} catch (Exception $e) {
    logOperation("استثناء في إنشاء قاعدة بيانات المستخدم", 'error', $e->getMessage());
}

// 3. الحصول على اتصال بقاعدة بيانات المستخدم
$user_db = null;
try {
    $user_db = new mysqli(MAIN_DB_HOST, MAIN_DB_USER, MAIN_DB_PASS, $user_db_name);
    $user_db->set_charset("utf8mb4");
    
    if (!$user_db->connect_error) {
        logOperation("تم الاتصال بقاعدة بيانات المستخدم بنجاح", 'success');
    } else {
        logOperation("فشل الاتصال بقاعدة بيانات المستخدم", 'error', $user_db->connect_error);
    }
} catch (Exception $e) {
    logOperation("استثناء في الاتصال بقاعدة بيانات المستخدم", 'error', $e->getMessage());
}

// 4. إنشاء الجداول الأساسية
if ($user_db && !$user_db->connect_error) {
    logOperation("بدء إنشاء الجداول الأساسية...", 'info');
    
    $tables = [
        'customers' => "CREATE TABLE IF NOT EXISTS `customers` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `phone` varchar(20) DEFAULT NULL,
            `email` varchar(255) DEFAULT NULL,
            `tax_number` varchar(50) DEFAULT NULL,
            `address` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_name` (`name`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
        
        'products' => "CREATE TABLE IF NOT EXISTS `products` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 15.00,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
        
        'sales' => "CREATE TABLE IF NOT EXISTS `sales` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
        
        'purchases' => "CREATE TABLE IF NOT EXISTS `purchases` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `invoice_number` varchar(50) NOT NULL,
            `date` date NOT NULL,
            `customer_id` int(11) DEFAULT NULL,
            `subtotal` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `notes` text DEFAULT NULL,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_invoice_number` (`invoice_number`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
        
        'sale_items' => "CREATE TABLE IF NOT EXISTS `sale_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `sale_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_sale_id` (`sale_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci",
        
        'purchase_items' => "CREATE TABLE IF NOT EXISTS `purchase_items` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `purchase_id` int(11) NOT NULL,
            `product_id` int(11) NOT NULL,
            `quantity` decimal(10,2) NOT NULL DEFAULT 0.00,
            `price` decimal(10,2) NOT NULL DEFAULT 0.00,
            `tax_rate` decimal(5,2) NOT NULL DEFAULT 0.00,
            `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
            `total_price` decimal(10,2) NOT NULL DEFAULT 0.00,
            PRIMARY KEY (`id`),
            KEY `idx_purchase_id` (`purchase_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci"
    ];
    
    foreach ($tables as $table_name => $create_sql) {
        try {
            if ($user_db->query($create_sql)) {
                logOperation("تم إنشاء/فحص جدول $table_name", 'success');
            } else {
                logOperation("فشل في إنشاء جدول $table_name", 'error', $user_db->error);
            }
        } catch (Exception $e) {
            logOperation("استثناء في إنشاء جدول $table_name", 'error', $e->getMessage());
        }
    }
    
    // 5. إضافة بيانات تجريبية إذا كانت الجداول فارغة
    logOperation("فحص وإضافة البيانات التجريبية...", 'info');
    
    // فحص جدول العملاء
    $customers_count = $user_db->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc()['count'];
    if ($customers_count == 0) {
        $sample_customers = [
            ['عميل تجريبي 1', '0501234567', '<EMAIL>', '1234567890', 'الرياض، المملكة العربية السعودية'],
            ['عميل تجريبي 2', '0509876543', '<EMAIL>', '0987654321', 'جدة، المملكة العربية السعودية']
        ];
        
        foreach ($sample_customers as $customer) {
            $stmt = $user_db->prepare("INSERT INTO customers (name, phone, email, tax_number, address) VALUES (?, ?, ?, ?, ?)");
            $stmt->bind_param("sssss", $customer[0], $customer[1], $customer[2], $customer[3], $customer[4]);
            if ($stmt->execute()) {
                logOperation("تم إضافة عميل تجريبي: " . $customer[0], 'success');
            }
            $stmt->close();
        }
    }
    
    // فحص جدول المنتجات
    $products_count = $user_db->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    if ($products_count == 0) {
        $sample_products = [
            ['منتج تجريبي 1', 100.00, 15.00],
            ['منتج تجريبي 2', 250.50, 15.00],
            ['منتج تجريبي 3', 75.25, 15.00]
        ];
        
        foreach ($sample_products as $product) {
            $stmt = $user_db->prepare("INSERT INTO products (name, price, tax_rate) VALUES (?, ?, ?)");
            $stmt->bind_param("sdd", $product[0], $product[1], $product[2]);
            if ($stmt->execute()) {
                logOperation("تم إضافة منتج تجريبي: " . $product[0], 'success');
            }
            $stmt->close();
        }
    }
    
    $user_db->close();
} else {
    logOperation("لا يمكن إنشاء الجداول بسبب مشكلة في الاتصال", 'error');
}

// حساب الوقت المستغرق
$end_time = microtime(true);
$execution_time = round($end_time - $start_time, 2);

logOperation("انتهى الإصلاح السريع في $execution_time ثانية", 'info');

require_once __DIR__ . '/includes/header.php';
?>

<div class="container mt-4">
    <div class="card">
        <div class="card-header <?php echo $total_errors > 0 ? 'bg-warning' : 'bg-success'; ?> text-white">
            <h4 class="mb-0">
                <i class="fas fa-bolt"></i>
                الإصلاح السريع لقاعدة البيانات
            </h4>
        </div>
        <div class="card-body">
            <!-- ملخص النتائج -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <h3><?php echo $total_success; ?></h3>
                            <p class="mb-0">نجح</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body text-center">
                            <h3><?php echo $total_errors; ?></h3>
                            <p class="mb-0">فشل</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <h3><?php echo count($operations); ?></h3>
                            <p class="mb-0">إجمالي العمليات</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body text-center">
                            <h3><?php echo $execution_time; ?>s</h3>
                            <p class="mb-0">وقت التنفيذ</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- سجل العمليات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">سجل العمليات</h5>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    <?php foreach ($operations as $op): ?>
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-<?php 
                            echo $op['status'] === 'success' ? 'success' : 
                                ($op['status'] === 'error' ? 'danger' : 'info'); 
                        ?> me-2">
                            <?php echo $op['time']; ?>
                        </span>
                        <span class="flex-grow-1"><?php echo $op['message']; ?></span>
                        <?php if (!empty($op['details'])): ?>
                        <small class="text-muted"><?php echo $op['details']; ?></small>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- أزرار الإجراءات -->
            <div class="mt-4 text-center">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    العودة للصفحة الرئيسية
                </a>
                <a href="database_repair_tool.php" class="btn btn-info ms-2">
                    <i class="fas fa-tools"></i>
                    أداة الإصلاح الشاملة
                </a>
                <a href="database_health_check.php" class="btn btn-success ms-2">
                    <i class="fas fa-heartbeat"></i>
                    فحص صحة قاعدة البيانات
                </a>
            </div>

            <?php if ($total_errors == 0): ?>
            <div class="alert alert-success mt-4">
                <h6><i class="fas fa-check-circle"></i> تم بنجاح!</h6>
                <p>تم إصلاح جميع مشاكل قاعدة البيانات. يمكنك الآن استخدام النظام بشكل طبيعي.</p>
            </div>
            <?php else: ?>
            <div class="alert alert-warning mt-4">
                <h6><i class="fas fa-exclamation-triangle"></i> تحذير</h6>
                <p>تم العثور على بعض المشاكل. يُرجى استخدام أداة الإصلاح الشاملة لحل المشاكل المتبقية.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/includes/footer.php'; ?>
