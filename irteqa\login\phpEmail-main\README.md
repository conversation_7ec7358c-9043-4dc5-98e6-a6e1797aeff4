# [phpEmail](https://github.com/allencasul/phpEmail) ![Github](https://img.shields.io/github/license/allencasul/phpEmail?logo=Github)

Send Electronic Mail using PHPMailer with [Lonica CSS Framework](https://github.com/allencasul/Lonica)

### phpEmail Guide

## What local server should I use?

You can try using local servers such as WAMP and make sure to put the phpEmail folder inside the directory wamp64 --> www folder.
For XAMPP the phpEmail folder should be inside the htdocs folder


## I don't have the option "App Password" on my google account, was it removed?

Make sure to enable 2 step verification first.


## Error: Form submission failed and no error message returned from: email.php?

Make sure to clone the source code using git, turn on local server, and run localhost/phpEmail in the search bar.


## Uncaught PHPMailer\PHPMailer\Exception: SMTP Error: Could not authenticate?

You need to create a google app password and make sure that you have provided the correct information such as the generated app password/your email address.
